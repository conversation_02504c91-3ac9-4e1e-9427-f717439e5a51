// https://umijs.org/config/
import { defineConfig } from 'umi';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
import versionPlugin from './versionPlugin';

const { REACT_APP_ENV } = process.env;
const publicPath = '/front/';
const isProd = process.env.NODE_ENV === 'production';

const babelPlugins = [];
if (isProd) {
    babelPlugins.push('transform-remove-debugger');
    babelPlugins.push('transform-remove-console');
}

const buildProductTime = new Date().getTime();

module.exports = defineConfig({
    mountElementId: 'xdt-front',
    base: publicPath,
    publicPath: publicPath,
    history: { type: 'browser' },
    hash: true,
    antd: {},
    dva: {
        hmr: true,
    },
    define: {
        PUBLIC_PATH: publicPath,
        APP_VERSION: `v${buildProductTime}`, //发布版本时间戳编号
    },
    layout: {
        // https://umijs.org/zh-CN/plugins/plugin-layout
        siderWidth: 208,
        ...defaultSettings,
        locale: false,
    },
    // https://umijs.org/zh-CN/plugins/plugin-locale
    locale: {
        default: 'zh-CN',
        antd: false,
        title: false,
        baseNavigator: true,
        baseSeparator: '-',
    },
    dynamicImport: {
        loading: '@ant-design/pro-layout/es/PageLoading',
    },
    targets: {
        ie: 11,
    },
    // umi routes: https://umijs.org/docs/routing
    routes,
    // Theme for antd: https://ant.design/docs/react/customize-theme-cn
    theme: {
        'primary-color': defaultSettings.primaryColor,
    },

    title: false,
    ignoreMomentLocale: true,
    proxy: proxy[REACT_APP_ENV || 'dev'],
    manifest: {
        basePath: '/',
    },
    // Fast Refresh 热更新
    fastRefresh: {},
    // openAPI: [
    //     {
    //         requestLibPath: "import { request } from 'umi'",
    //         // 或者使用在线的版本
    //         // schemaPath: "https://gw.alipayobjects.com/os/antfincdn/M%24jrzTTYJN/oneapi.json"
    //         schemaPath: join(__dirname, 'oneapi.json'),
    //         mock: false,
    //     },
    //     {
    //         requestLibPath: "import { request } from 'umi'",
    //         schemaPath: 'https://gw.alipayobjects.com/os/antfincdn/CA1dOm%2631B/openapi.json',
    //         projectName: 'swagger',
    //     },
    // ],
    nodeModulesTransform: {
        type: 'none',
    },
    // mfsu: {},
    webpack5: {},
    // esbuild is father build tools
    // https://umijs.org/plugins/plugin-esbuild
    esbuild: {},

    // 并行编译
    workerLoader: {
        workers: 10, // 根据 CPU 核心数调整
        workerParallelJobs: 50,
    },

    externals: {
        AMap: 'window.AMap',
    },
    extraBabelPlugins: babelPlugins,
    // 完全关闭 SourceMap
    devtool: false,
    // chunks: ['vendor', 'echarts', 'antdesigns', 'commons', 'umi'],
    chainWebpack: (config, { webpack }) => {
        config.when(process.env.NODE_ENV !== 'development', (config) => {
            config.plugin('version').use(
                new versionPlugin({
                    publicDir: publicPath,
                    version: `v${buildProductTime}`,
                }),
            );
        });

        // 排除非必要依赖 (减少 30-50% 编译量)
        config.module.rule('js').exclude.add(/node_modules\/(?!react|antd|@ant-design)/);

        // 启用持久化缓存 (Umi 3.5+)
        config.cache({
            type: 'filesystem',
            buildDependencies: { config: [__filename] },
        });

        config.when(process.env.NODE_ENV === 'development', (config) => {});

        // config.merge({
        //     optimization: {
        //         sideEffects: true,
        //         splitChunks: {
        //             chunks: 'all',
        //             minSize: 30000,
        //             minChunks: 2,
        //             automaticNameDelimiter: '.',
        //             cacheGroups: {
        //                 vendor: {
        //                     name: 'vendor',
        //                     test: /[\\/]node_modules[\\/]/,
        //                     chunks: 'all',
        //                     priority: 98,
        //                 },
        //                 echarts: {
        //                     name: 'echarts',
        //                     test: /[\\/]node_modules[\\/](echarts.*)[\\/]/,
        //                     chunks: 'all',
        //                     priority: 100,
        //                 },
        //                 lfpantdesigns: {
        //                     name: 'antdesigns',
        //                     chunks: 'all',
        //                     test: /[\\/]node_modules[\\/](@antv|antd|@ant-design)/,
        //                     priority: 100,
        //                 },

        //                 lfpcommons: {
        //                     name: 'commons',
        //                     // 其余同步加载包
        //                     chunks: 'all',
        //                     minChunks: 2,
        //                     priority: 1,
        //                     // 这里需要注意下，webpack5会有问题， 需加上这个 enforce: true，
        //                     // refer: https://github.com/webpack-contrib/mini-css-extract-plugin/issues/257#issuecomment-432594711
        //                     enforce: true,
        //                 },
        //             },
        //         },
        //     },
        // });
    },
    qiankun: {
        slave: {},
    },
});
