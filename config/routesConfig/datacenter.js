module.exports = [
    {
        path: '/datacenter',
        name: '数据中心',
        title: '数据中心',
        icon: 'gold',
        component: '../components/Layouts/FatherLayout',
        routes: [
            /*************** 数据中心 ***************/
            {
                path: '/datacenter',
                name: '数据中心',
                title: '数据中心',
                hideInMenu: true,
                redirect: '/datacenter/overview',
            },

            /*************** 大屏 ***************/

            {
                path: '/datacenter/screen',
                name: '大屏',
                title: '大屏',
                icon: 'FundProjectionScreenOutlined',
                layout: false,
                redirect: '/screen/xdt',
            },

            /*************** 运营概况 ***************/

            {
                path: '/datacenter/newscreen',
                name: '新大屏',
                title: '新大屏',
                icon: 'FundProjectionScreenOutlined',
                layout: false,
                redirect: '/newscreen/xdt',
            },
            {
                path: '/datacenter/largescreen',
                name: '展厅大屏',
                title: '展厅大屏',
                icon: 'FundProjectionScreenOutlined',
                layout: false,
                redirect: '/largescreen/xdt',
            },
            {
                path: '/datacenter/overview',
                name: '运营概况',
                title: '运营概况',
                component: './DataCenter/Overview/OverviewPage',
            },

            /*************** SOC管控 ***************/

            {
                path: '/datacenter/soc',
                name: 'SOC管控',
                title: 'SOC管控',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/datacenter/soc',
                        redirect: '/datacenter/soc/manage',
                    },
                    {
                        path: '/datacenter/soc/manage',
                        name: '异常数据',
                        title: '异常数据',
                        component: './DataCenter/SocManage/SocListPage',
                    },
                    {
                        path: '/datacenter/soc/support',
                        name: '阈值维护',
                        title: '阈值维护',
                        component: './DataCenter/SocManage/SupportPage',
                    },
                ],
            },
            {
                path: '/datacenter/warnManage',
                name: '告警管理',
                title: '告警管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/datacenter/warnManage',
                        redirect: '/datacenter/warnManage/manage',
                    },
                    {
                        path: '/datacenter/warnManage/manage',
                        name: '设备异常',
                        title: '设备异常',
                        component: './DataCenter/warnManage/EquipABListPage',
                    },
                ],
            },
        ],
    },
];
