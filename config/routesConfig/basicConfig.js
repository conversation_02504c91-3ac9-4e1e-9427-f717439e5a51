module.exports = [
    {
        path: '/basic',
        name: '基础管理',
        title: '基础管理',
        icon: 'SettingOutlined',
        component: '../components/Layouts/FatherLayout',
        routes: [
            {
                path: '/basic/protocol',
                title: '协议管理',
                name: '协议管理',
                exact: true,
                component: './BasicManagement/Protocol/List',
            },
            {
                path: '/basic/protocol/add',
                title: '新增协议',
                name: '新增协议',
                hideInMenu: true,
                component: './BasicManagement/Protocol/Add',
                parentKeys: ['/basic/protocol'],
            },
            {
                path: '/basic/protocol/edit/:id',
                title: '编辑协议',
                name: '编辑协议',
                hideInMenu: true,
                component: './BasicManagement/Protocol/Edit',
                parentKeys: ['/basic/protocol'],
            },
            {
                path: '/basic/protocol/detail/:id',
                title: '查看协议',
                name: '查看协议',
                hideInMenu: true,
                component: './BasicManagement/Protocol/Detail',
                parentKeys: ['/basic/protocol'],
            },
        ],
    },
];
