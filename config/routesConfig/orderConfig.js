module.exports = [
    {
        name: '订单中心',
        title: '订单中心',
        path: '/orderCenter',
        icon: 'fileSearch',
        routes: [
            {
                path: '/orderCenter',
                redirect: '/orderCenter/charge-order',
            },
            {
                path: '/orderCenter/serviceMarketOrder',
                title: '服务市场订单',
                name: '服务市场订单',
                component: './OperationTools/ServMktOrd',
            },
            {
                path: '/orderCenter/serviceMarketOrder/detail/:orderId',
                title: '结清明细',
                name: '结清明细',
                component: './OperationTools/ServMktOrd/Detail',
                hideInMenu: true,
                parentKeys: ['/orderCenter/serviceMarketOrder'],
            },
            {
                path: '/orderCenter/channel-message-push',
                title: '渠道订单补推报文',
                name: '渠道订单补推报文',
                component: './OperationTools/channelMessagePush/index',
            },
            {
                path: '/orderCenter/charge-order',
                title: '充电订单',
                name: '充电订单',
                component: './OperationTools/OrderList/ChargeOrderListPage',
            },
            {
                path: '/orderCenter/chargeOrder',
                title: '充电订单【新】',
                name: '充电订单【新】',
                component: './OperationTools/OrderList/ChargeOrderListPage',
            },
            {
                path: '/orderCenter/external-order',
                title: '外部带货订单',
                name: '外部带货订单',
                component: './OperationTools/ExternalOrder/List',
            },
            {
                path: '/orderCenter/inTransit-order',
                title: '在途订单',
                name: '在途订单',
                component: './OperationTools/OrderList/InTransitOrderListPage',
            },

            {
                path: '/orderCenter/charge-order/detail',
                title: '充电订单',
                name: '充电订单',
                component: './OperationTools/OrderList/ChargeOrderDetailPage',
                hideInMenu: true,
                parentKeys: ['/orderCenter/charge-order'],
            },
            {
                path: '/orderCenter/debt',
                title: '订单欠款',
                name: '订单欠款',
                component: './OperationTools/DebtManage/DebtListPage',
            },
            {
                path: '/orderCenter/order',
                title: '订单归属',
                name: '订单归属',
                component: './OperationTools/OrderList/OrderListPage',
            },
            {
                path: '/orderCenter/order-refund/list',
                title: '订单退款',
                name: '订单退款',
                component: './OperationTools/OrderList/RefundOrder/ListPage',
            },
            {
                path: '/orderCenter/order-refund/list/add',
                title: '新增订单退款',
                name: '新增订单退款',
                hideInMenu: true,

                component: './OperationTools/OrderList/RefundOrder/UpdatePage',
            },
            {
                path: '/orderCenter/order-refund/list/detail',
                title: '订单退款',
                name: '订单退款',
                hideInMenu: true,

                component: './OperationTools/OrderList/RefundOrder/DetailPage',
            },
            {
                path: '/orderCenter/safe-charge/list',
                title: '安心充订单列表',
                name: '安心充订单列表',
                hideInMenu: true,

                component: './OperationTools/OrderList/SafeCharge',
            },

            /************** 充电全流程管理 **************/
            {
                path: '/orderCenter/charge-process',
                redirect: '/orderCenter/charge-process/list',
            },
            {
                path: '/orderCenter/charge-process/list',
                name: '充电全流程管理',
                title: '充电全流程管理',
                component: './OrderCenter/ProcessManage/List',
            },
            {
                path: '/orderCenter/charge-process/list/detail',
                name: '充电全流程管理详情',
                title: '充电全流程管理详情',
                hideInMenu: true,
                component: './OrderCenter/ProcessManage/Detail',
                parentKeys: ['/orderCenter/charge-process/list'],
            },
            {
                path: '/orderCenter/paying-agent',
                redirect: '/orderCenter/paying-agent/list',
            },
            {
                path: '/orderCenter/paying-agent/list',
                title: '代充管理',
                name: '代充管理',
                component: './OperationTools/PayingAgent/List',
            },
            {
                path: '/orderCenter/paying-agent/list/record',
                title: '代充管理',
                name: '代充管理',
                hideInMenu: true,
                component: './OperationTools/PayingAgent/Record',
                parentKeys: ['/orderCenter/paying-agent/list'],
            },
            {
                path: '/orderCenter/notice',
                title: '通知记录',
                name: '通知记录',
                component: './OperationTools/NoticeMgr/NoticeRecordList',
            },
        ],
    },
];
