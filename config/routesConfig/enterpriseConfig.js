module.exports = [
    {
        name: '企业客户',
        title: '企业客户',
        path: '/userCenter/enterprise',
        icon: 'contacts',
        routes: [
            {
                path: '/userCenter/enterprise',
                redirect: '/userCenter/enterprise/manage',
            },
            /*************** 企业信息 ***************/

            {
                path: '/userCenter/enterprise/manage',
                redirect: '/userCenter/enterprise/manage/list',
            },
            {
                path: '/userCenter/enterprise/manage/list',
                name: '企业信息',
                title: '企业信息',
                component: './EnterpriseManage/Manage/List',
            },
            {
                path: '/userCenter/enterprise/manage/list/add',
                title: '新增企业',
                name: '新增企业',
                hideInMenu: true,
                component: './EnterpriseManage/Manage/ManageUpdatePage',
                parentKeys: ['/userCenter/enterprise/manage/list'],
            },
            {
                path: '/userCenter/enterprise/manage/list/update/:companyId',
                name: '编辑企业',
                title: '编辑企业',
                hideInMenu: true,
                component: './EnterpriseManage/Manage/ManageUpdatePage',
                parentKeys: ['/userCenter/enterprise/manage/list'],
            },
            {
                path: '/userCenter/enterprise/manage/list/details/:companyId',
                name: '企业详情',
                title: '企业详情',
                hideInMenu: true,
                component: './EnterpriseManage/Manage/Detail',
                parentKeys: ['/userCenter/enterprise/manage/list'],
            },
            /*************** 企业员工 ***************/
            {
                path: '/userCenter/enterprise/manage/account',
                name: '企业账户',
                title: '企业账户',
                component: './EnterpriseManage/Account/Home',
            },
            /*************** 企业员工 ***************/
            {
                path: '/userCenter/enterprise/staff',
                redirect: '/userCenter/enterprise/staff/list',
            },
            {
                path: '/userCenter/enterprise/staff/list',
                title: '企业员工',
                name: '企业员工',
                component: './EnterpriseManage/Staff/StaffListPage',
            },
            {
                path: '/userCenter/enterprise/staff/list/order',
                name: '员工订单',
                title: '员工订单',
                hideInMenu: true,
                component: './EnterpriseManage/Staff/StaffOrderListPage',
                parentKeys: ['/userCenter/enterprise/staff/list'],
            },

            /*************** 企业订单 ***************/

            {
                path: '/userCenter/enterprise/order',
                redirect: '/userCenter/enterprise/order/list',
            },
            {
                path: '/userCenter/enterprise/order/list',
                name: '企业订单',
                title: '企业订单',
                component: './EnterpriseManage/Order/OrderListPage',
            },
            {
                path: '/userCenter/enterprise/order/list/invoice-add',
                name: '开发票',
                title: '开发票',
                component: './FinanceManage/InvoiceManage/InvoiceOpenPage',
                hideInMenu: true,
                parentKeys: ['/userCenter/enterprise/order/list'],
            },
            {
                path: '/userCenter/enterprise/order/list/invoice-detail',
                name: '开票详情',
                title: '开票详情',
                component: './FinanceManage/InvoiceManage/CompanyInvoiceDetailsPage',
                hideInMenu: true,
                parentKeys: ['/userCenter/enterprise/order/list'],
            },

            /*************** 企业申请 ***************/
            {
                path: '/userCenter/enterprise/apply',
                redirect: '/userCenter/enterprise/apply/list',
            },
            {
                path: '/userCenter/enterprise/apply/list',
                name: '企业申请',
                title: '企业申请',
                component: './EnterpriseManage/Apply/ApplyListPage',
            },

            /*************** 企业代扣 ***************/
            {
                path: '/userCenter/enterprise/withholding',
                redirect: '/userCenter/enterprise/withholding/list',
            },
            {
                path: '/userCenter/enterprise/withholding/list',
                name: '企业代扣',
                title: '企业代扣',
                component: './EnterpriseManage/Withholding/WithholdingListPage',
            },

            /*************** 企业车辆 ***************/

            {
                path: '/userCenter/enterprise/carmanage',
                name: '企业车辆',
                title: '企业车辆',
                component: './EnterpriseManage/CarManage/CarManageListPage',
            },
            {
                component: './404',
            },
        ],
    },
];
