// 兼职中心
module.exports = [
    {
        name: '兼职中心',
        path: '/partTimeCenter',
        title: '兼职中心',
        icon: 'user',
        routes: [
            {
                path: '/partTimeCenter/personnelManage',
                redirect: '/partTimeCenter/personnelManage/manage',
            },
            {
                path: '/partTimeCenter/personnelManage/manage',
                name: '人员管理',
                title: '人员管理',
                component: './PartTimeCenter/PersonnelManage/PersonnelManageListPage',
            },
            {
                path: '/partTimeCenter/feeRules/list',
                title: '费用规则',
                name: '费用规则',
                component: './PartTimeCenter/FeeRules/FeeRulesListPage',
            },
            {
                path: '/partTimeCenter/feeRules/list/add',
                title: '新建分销',
                name: '新建分销',
                component: './PartTimeCenter/FeeRules/DistributionUpdatePage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/feeRules/list/update/:id',
                title: '编辑分销',
                name: '编辑分销',
                component: './PartTimeCenter/FeeRules/DistributionUpdatePage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/feeRules/list/look/:id',
                title: '分销详情',
                name: '分销详情',
                component: './PartTimeCenter/FeeRules/DistributionDetailPage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/feeRules/list/parttime/add',
                title: '新建兼职',
                name: '新建兼职',
                component: './PartTimeCenter/FeeRules/PartTimeUpdatePage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/feeRules/list/parttime/update/:id',
                title: '编辑兼职',
                name: '编辑兼职',
                component: './PartTimeCenter/FeeRules/PartTimeUpdatePage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/feeRules/list/parttime/look/:id',
                title: '兼职详情',
                name: '兼职详情',
                component: './PartTimeCenter/FeeRules/PartTimeDetailPage',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/feeRules/list'],
            },
            {
                path: '/partTimeCenter/distribution/detail',
                title: '分销明细',
                name: '分销明细',
                component: './PartTimeCenter/Distribution/DistributionDetailPage',
            },
            {
                path: '/partTimeCenter/partTime/detail',
                title: '兼职明细',
                name: '兼职明细',
                component: './PartTimeCenter/Record/List',
            },
            {
                path: '/partTimeCenter/partTime/detail/look/:id',
                title: '兼职业务详情',
                name: '兼职业务详情',
                component: './PartTimeCenter/Record/Detail',
                hideInMenu: true,
                parentKeys: ['/partTimeCenter/partTime/detail'],
            },
        ],
    },
];
