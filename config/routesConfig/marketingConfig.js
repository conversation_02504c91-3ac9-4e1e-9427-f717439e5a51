import messageCenter from './marketing/messageCenter';

const businessActive2_0Factory = (name, path) => {
    return [
        {
            path: `/marketing/${path}`,
            name: name,
            title: name,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveListPage',
        },
        {
            path: `/marketing/${path}/plat/add`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/plat/update/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/plat/look/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveDetailPage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/plat/copy/:actId`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        /*************** 新场站营销 ***************/

        {
            path: `/marketing/${path}/user/add`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/user/update/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/user/look/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveDetailPage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/user/copy/:actId`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/add`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/update/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/look/:actId`,
            name: `${name}详情`,
            title: `${name}详情`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveDetailPage',
            parentKeys: [`/marketing/${path}`],
        },
        {
            path: `/marketing/${path}/copy/:actId`,
            name: `新建${name}`,
            title: `新建${name}`,
            hideInMenu: true,
            component: './MarketingManage/BusinessActive/ModuleActive/BusinessActiveUpdatePage',
            parentKeys: [`/marketing/${path}`],
        },
    ];
};

module.exports = [
    {
        name: '营销中心',
        path: '/marketing',
        title: '营销中心',
        icon: 'contacts',
        routes: [
            {
                path: '/marketing',
                redirect: '/marketing/businessActive',
            },

            {
                path: '/marketing/ossimg',
                name: '图片小工具',
                title: '图片小工具',
                redirect: '/marketing/ossimg/list',
                component: '../components/Layouts/FatherLayout',
            },
            {
                path: '/marketing/ossimg/list',
                name: '图片小工具',
                title: '图片小工具',
                component: './MarketingManage/OssImg/List',
            },

            /*************** 调价工具 ***************/
            {
                path: '/marketing/readjust',
                name: '调价工具',
                title: '调价工具',
                redirect: '/marketing/readjust/list',
                component: '../components/Layouts/FatherLayout',
            },
            {
                path: '/marketing/readjust/list',
                name: '调价列表',
                title: '调价列表',
                component: './MarketingManage/Readjust/List',
            },
            {
                path: '/marketing/readjust/add',
                name: '新增调价',
                title: '新增调价',
                component: './MarketingManage/Readjust/Edit',
                parentKeys: ['/marketing/readjust'],
                hideInMenu: true,
            },
            {
                path: '/marketing/readjust/update',
                name: '编辑调价',
                title: '编辑调价',
                component: './MarketingManage/Readjust/Edit',
                parentKeys: ['/marketing/readjust'],
                hideInMenu: true,
            },
            {
                path: '/marketing/readjust/detail',
                name: '调价详情',
                title: '调价详情',
                component: './MarketingManage/Readjust/Detail',
                parentKeys: ['/marketing/readjust'],
                hideInMenu: true,
            },
            {
                path: '/marketing/readjust/analysis',
                name: '调价分析',
                title: '调价分析',
                component: './MarketingManage/Readjust/Analysis',
                parentKeys: ['/marketing/readjust'],
                hideInMenu: true,
            },
            {
                path: '/marketing/readjust/price-analysis',
                name: '价格分析',
                title: '价格分析',
                component: './MarketingManage/Readjust/Analysis/Price',
                parentKeys: ['/marketing/readjust'],
                hideInMenu: true,
            },
            {
                path: '/marketing/modelManage/list',
                name: '模型管理',
                title: '模型管理',
                component: './MarketingManage/Readjust/Record/ModelManage/List',
            },
            {
                path: '/marketing/modelManage/detail',
                name: '查看模型详情',
                title: '查看模型详情',
                component: './MarketingManage/Readjust/Record/ModelManage/Detail',
                parentKeys: ['/marketing/modelManage/list'],
                hideInMenu: true,
            },

            /*************** 预设管理 ***************/
            {
                path: '/marketing/readjust/presets/list',
                name: '预设分组',
                title: '预设分组',
                component: './MarketingManage/Readjust/Presets/Group/List',
            },
            {
                path: '/marketing/readjust/presets/add',
                name: '添加预设分组',
                title: '添加预设分组',
                component: './MarketingManage/Readjust/Presets/Group/Edit',
                parentKeys: ['/marketing/readjust/presets/list'],
                hideInMenu: true,
            },
            {
                path: '/marketing/readjust/presets/update',
                name: '配置预设分组',
                title: '配置预设分组',
                component: './MarketingManage/Readjust/Presets/Group/Edit',
                parentKeys: ['/marketing/readjust/presets/list'],
                hideInMenu: true,
            },
            /*************** 工单管理 ***************/
            {
                path: '/marketing/workorder',
                redirect: '/marketing/workorder/manage',
            },
            {
                path: '/marketing/workorder/manage',
                name: '工单管理',
                title: '工单管理',
                component: './MarketingManage/WorkOrder/ManagePage',
            },
            {
                path: '/marketing/workorder/manage/pricing-tuning',
                name: '定价调优',
                title: '定价调优',
                component: './MarketingManage/WorkOrder/PricingTuning',
                parentKeys: ['/marketing/workorder/manage'],
                hideInMenu: true,
            },
            {
                path: '/marketing/workorder/manage/funding-department',
                name: '出资部门',
                title: '出资部门',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/FundingDepartment',
                parentKeys: ['/marketing/workorder/manage'],
            },
            {
                path: '/marketing/workorder/manage/add',
                name: '新建工单(基本信息)',
                title: '新建工单(基本信息)',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/WorkOrderUpdateBasePage',
                parentKeys: ['/marketing/workorder/manage'],
            },
            {
                path: '/marketing/workorder/manage/copy/basic/:id',
                name: '复制工单(基本信息)',
                title: '复制工单(基本信息)',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/WorkOrderUpdateBasePage',
                parentKeys: ['/marketing/workorder/manage'],
            },

            {
                path: '/marketing/workorder/manage/update/basic/:id',
                name: '编辑工单(基本信息)',
                title: '编辑工单(基本信息)',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/WorkOrderUpdateBasePage',
                parentKeys: ['/marketing/workorder/manage'],
            },
            {
                path: '/marketing/workorder/manage/update/plan/:id',
                name: '编辑工单(方案配置)',
                title: '编辑工单(方案配置)',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/WorkOrderUpdatePlanPage',
                parentKeys: ['/marketing/workorder/manage'],
            },
            {
                path: '/marketing/workorder/manage/detail/:id',
                name: '查看工单',
                title: '查看工单',
                hideInMenu: true,
                component: './MarketingManage/WorkOrder/WorkOrderDetailPage',
                parentKeys: ['/marketing/workorder/manage'],
            },
            {
                path: '/marketing/workorder/manage/prize-optimize',
                name: '定价调优',
                title: '定价调优',
                hideInMenu: true,
                component: './MarketingManage/PrizeOptimize/PriceOptimizePage.js',
                parentKeys: ['/marketing/workorder/manage'],
            },

            /*************** 工单权限设置 ***************/

            {
                path: '/marketing/workorderAuth',
                redirect: '/marketing/workorderAuth/authManage',
            },
            {
                path: '/marketing/workorderAuth/authManage',
                name: '工单权限设置',
                title: '工单权限设置',
                component: './MarketingManage/WorkOrder/AuthManagePage',
            },

            /*************** 充电卡管理 ***************/

            {
                path: '/marketing/chargecard/manage',
                redirect: '/marketing/chargecard/manage/list',
            },
            {
                path: '/marketing/chargecard/manage/list',
                name: '充电卡管理',
                title: '充电卡管理',
                component: './MarketingManage/WeekCard/WeekCardGroupBuyManagePage',
            },
            {
                path: '/marketing/chargecard/manage/list/detail/:actId/:custId/:buyNo',
                name: '充电卡详情',
                title: '充电卡详情',
                hideInMenu: true,
                component: './MarketingManage/WeekCard/WeekCardGroupBuyManageDetailPage',
                parentKeys: ['/marketing/chargecard/manage/list'],
            },

            /*************** 场站营销 ***************/

            {
                path: '/marketing/businessActive',
                name: '场站营销',
                title: '场站营销',
                component: '../components/Layouts/FatherLayout',

                routes: [
                    {
                        path: '/marketing/businessActive',
                        redirect: '/marketing/businessActive/list',
                    },

                    {
                        path: '/marketing/businessActive/list',
                        name: '场站营销',
                        title: '场站营销',
                        component: './MarketingManage/BusinessActive/BusinessActiveListPage',
                    },
                    {
                        path: '/marketing/businessActive/list/add',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/update/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/look/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/copy/:actId',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },

                    /*************** 旧场站营销 ***************/

                    {
                        path: '/marketing/businessActive/list/plat/add',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/plat/update/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/plat/look/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/plat/copy/:actId',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },

                    // 用户营销配置权限
                    {
                        path: '/marketing/businessActive/list/user/add',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/user/update/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/user/look/:actId',
                        name: '营销活动详情',
                        title: '营销活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },
                    {
                        path: '/marketing/businessActive/list/user/copy/:actId',
                        name: '新建营销活动',
                        title: '新建营销活动',
                        hideInMenu: true,
                        component: './MarketingManage/BusinessActive/BusinessActiveUpdatePage',
                        parentKeys: ['/marketing/businessActive/list'],
                    },

                    /*************** 新场站营销 ***************/

                    // 场站营销模板化重构
                    ...businessActive2_0Factory('场站营销', 'businessActive/moduleAct'),
                    ...businessActive2_0Factory('人群策略补贴', 'businessActive/module-user-group'),
                    ...businessActive2_0Factory(
                        '基础价格补贴',
                        'businessActive/module-basic-price',
                    ),

                    // 为防止场站营销mng迁移出问题，留一套旧的权限页面兜底访问旧的接口路径
                    ...businessActive2_0Factory('场站营销', 'businessActive/temp/moduleAct'),
                    ...businessActive2_0Factory(
                        '人群策略补贴',
                        'businessActive/temp/module-user-group',
                    ),
                    ...businessActive2_0Factory(
                        '基础价格补贴',
                        'businessActive/temp/module-basic-price',
                    ),

                    {
                        path: '/marketing/businessActive/module-basic-price/prize-optimize/summary',
                        name: '定价辅助',
                        title: '定价辅助',
                        hideInMenu: true,
                        component: './MarketingManage/PrizeOptimize/PriceOptimizeSummaryPage.js',
                        parentKeys: ['/marketing/businessActive/list'],
                    },

                    /*************** 毛利阈值管理 ***************/

                    {
                        path: '/marketing/businessActive/profit-threshold/list',
                        name: '毛利阈值管理',
                        title: '毛利阈值管理',
                        component: './MarketingManage/ProfitThreshold/ListPage',
                    },

                    {
                        path: '/marketing/businessActive/profit-threshold/list/record',
                        name: '操作记录',
                        title: '操作记录',
                        hideInMenu: true,
                        component: './MarketingManage/ProfitThreshold/RecordView',
                    },

                    /*************** 模板管理 ***************/

                    {
                        path: '/marketing/businessActive/module',
                        name: '模板管理',
                        title: '模板管理',
                        component:
                            './MarketingManage/BusinessActive/ModuleActive/BaseModuleManagePage',
                    },
                    /*************** 组队活动 ***************/

                    {
                        path: '/marketing/businessActive/team',
                        redirect: '/marketing/businessActive/team/manage',
                    },
                    {
                        path: '/marketing/businessActive/team/manage',
                        name: '组队活动',
                        title: '组队活动',
                        component: './MarketingManage/TeamActive/ListPage',
                    },
                    {
                        path: '/marketing/businessActive/team/add',
                        name: '新增组队活动',
                        title: '新增组队活动',
                        hideInMenu: true,
                        component: './MarketingManage/TeamActive/UpdatePage',
                        parentKeys: ['/marketing/businessActive/team/manage'],
                    },
                    {
                        path: '/marketing/businessActive/team/update/:actId',
                        name: '组队活动',
                        title: '组队活动',
                        hideInMenu: true,
                        component: './MarketingManage/TeamActive/UpdatePage',
                        parentKeys: ['/marketing/businessActive/team/manage'],
                    },
                    {
                        path: '/marketing/businessActive/team/detail/:actId',
                        name: '组队活动',
                        title: '组队活动',
                        hideInMenu: true,
                        component: './MarketingManage/TeamActive/UpdatePage',
                        parentKeys: ['/marketing/businessActive/team/manage'],
                    },
                    {
                        path: '/marketing/businessActive/team/copy/:actId',
                        name: '组队活动',
                        title: '组队活动',
                        hideInMenu: true,
                        component: './MarketingManage/TeamActive/UpdatePage',
                        parentKeys: ['/marketing/businessActive/team/manage'],
                    },

                    /*************** 组团活动 ***************/
                    {
                        path: '/marketing/businessActive/group',
                        redirect: '/marketing/businessActive/group/act',
                    },
                    {
                        path: '/marketing/businessActive/group/act',
                        name: '活动信息',
                        title: '活动信息',
                        component: './MarketingManage/GroupActive/ActInfoPage',
                    },
                    {
                        path: '/marketing/businessActive/group/actUpdate',
                        name: '活动修改',
                        title: '活动修改',
                        hideInMenu: true,
                        component: './MarketingManage/GroupActive/ActUpdatePage',
                        parentKeys: ['/marketing/businessActive/group/act'],
                    },
                    {
                        path: '/marketing/businessActive/group/deed',
                        name: '行为项管理',
                        title: '行为项管理',
                        hideInMenu: true,
                        component: './MarketingManage/GroupActive/DeedManagePage',
                        parentKeys: ['/marketing/businessActive/group/act'],
                    },
                    {
                        path: '/marketing/businessActive/group/manage',
                        name: '团队管理',
                        title: '团队管理',
                        component: './MarketingManage/GroupActive/GroupManagePage',
                    },
                    {
                        path: '/marketing/businessActive/group/detail/:groupId',
                        name: '团队信息',
                        title: '团队信息',
                        hideInMenu: true,
                        component: './MarketingManage/GroupActive/GroupDetailPage',
                        parentKeys: ['/marketing/businessActive/group/manage'],
                    },

                    {
                        component: './404',
                    },
                ],
            },
            /*************** 优惠券 ***************/

            {
                path: '/marketing/couponCenter',
                name: '优惠券',
                title: '优惠券',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/couponCenter',
                        redirect: '/marketing/couponCenter/coupon',
                    },
                    /*************** 优惠券管理1.0 ***************/
                    {
                        path: '/marketing/couponCenter/coupon',
                        redirect: '/marketing/couponCenter/coupon/list',
                    },
                    {
                        path: '/marketing/couponCenter/coupon/list',
                        name: '优惠券管理1.0',
                        title: '优惠券管理1.0',
                        component: './MarketingManage/CouponManage/CouponManageListPage',
                    },

                    {
                        path: '/marketing/couponCenter/coupon/list/add',
                        name: '新建券',
                        title: '新建券',
                        hideInMenu: true,
                        component: './MarketingManage/CouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/coupon/list'],
                    },
                    {
                        path: '/marketing/couponCenter/coupon/list/update/:cpnId',
                        name: '券详情',
                        title: '券详情',
                        hideInMenu: true,
                        component: './MarketingManage/CouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/coupon/list'],
                    },
                    {
                        path: '/marketing/couponCenter/coupon/list/look/:cpnId',
                        name: '券详情',
                        title: '券详情',
                        hideInMenu: true,
                        component: './MarketingManage/CouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/coupon/list'],
                    },
                    {
                        path: '/marketing/couponCenter/coupon/list/copy/:cpnId',
                        name: '复制券',
                        title: '复制券',
                        hideInMenu: true,
                        component: './MarketingManage/CouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/coupon/list'],
                    },
                    /*************** 优惠券管理2.0 ***************/
                    {
                        path: '/marketing/couponCenter/cpnManage',
                        redirect: '/marketing/couponCenter/cpnManage/list',
                    },
                    {
                        path: '/marketing/couponCenter/cpnManage/list',
                        name: '优惠券管理2.0',
                        title: '优惠券管理2.0',
                        component: './MarketingManage/NewCouponManage/CouponManageListPage',
                    },

                    {
                        path: '/marketing/couponCenter/cpnManage/list/add',
                        name: '新建券',
                        title: '新建券',
                        hideInMenu: true,
                        component: './MarketingManage/NewCouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/cpnManage/list'],
                    },
                    {
                        path: '/marketing/couponCenter/cpnManage/list/update/:prizeId',
                        name: '券详情',
                        title: '券详情',
                        hideInMenu: true,
                        component: './MarketingManage/NewCouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/cpnManage/list'],
                    },
                    {
                        path: '/marketing/couponCenter/cpnManage/list/look/:prizeId',
                        name: '券详情',
                        title: '券详情',
                        hideInMenu: true,
                        component: './MarketingManage/NewCouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/cpnManage/list'],
                    },
                    {
                        path: '/marketing/couponCenter/cpnManage/list/copy/:prizeId',
                        name: '复制券',
                        title: '复制券',
                        hideInMenu: true,
                        component: './MarketingManage/NewCouponManage/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/cpnManage/list'],
                    },

                    /*************** 优惠码管理 ***************/
                    {
                        path: '/marketing/couponCenter/codeManage',
                        redirect: '/marketing/couponCenter/codeManage/list',
                    },
                    {
                        path: '/marketing/couponCenter/codeManage/list',
                        name: '优惠码管理',
                        title: '优惠码管理',
                        component: './MarketingManage/NewCouponManage/CodeManageListPage',
                    },

                    {
                        path: '/marketing/couponCenter/codeManage/list/store/:codeId',
                        name: '码库',
                        title: '码库',
                        hideInMenu: true,
                        component: './MarketingManage/NewCouponManage/CodeStorePage',
                        parentKeys: ['/marketing/couponCenter/codeManage/list'],
                    },

                    /*************** 优惠券营销1.0 ***************/
                    {
                        path: '/marketing/couponCenter/appletgift',

                        redirect: '/marketing/couponCenter/appletgift/list',
                    },
                    {
                        path: '/marketing/couponCenter/appletgift/list',
                        name: '优惠券营销1.0',
                        title: '优惠券营销1.0',
                        component: './MarketingManage/AppletGift/AppletGiftListPage',
                    },
                    {
                        path: '/marketing/couponCenter/appletgift/list/add',
                        name: '新增活动',
                        title: '新增活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/appletgift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/appletgift/list/update/:actId',
                        name: '编辑活动',
                        title: '编辑活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/appletgift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/appletgift/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/appletgift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/appletgift/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponDetailPage',
                        parentKeys: ['/marketing/couponCenter/appletgift/list'],
                    },
                    /*************** 优惠券营销2.0 ***************/
                    {
                        path: '/marketing/couponCenter/applet-gift',
                        redirect: '/marketing/couponCenter/applet-gift/list',
                    },
                    {
                        path: '/marketing/couponCenter/applet-gift/list',
                        name: '优惠券营销2.0',
                        title: '优惠券营销2.0',
                        component: './MarketingManage/AppletGift/AppletGiftListPage',
                    },
                    {
                        path: '/marketing/couponCenter/applet-gift/list/add',
                        name: '新增活动',
                        title: '新增活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/applet-gift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/applet-gift/list/update/:actId/:actSubType',
                        name: '编辑活动',
                        title: '编辑活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/applet-gift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/applet-gift/list/copy/:actId/:actSubType',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponUpdatePage',
                        parentKeys: ['/marketing/couponCenter/applet-gift/list'],
                    },
                    {
                        path: '/marketing/couponCenter/applet-gift/list/look/:actId/:actSubType',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/AppletGift/CouponDetailPage',
                        parentKeys: ['/marketing/couponCenter/applet-gift/list'],
                    },

                    /*************** 优惠券活动 ***************/

                    {
                        path: '/marketing/couponCenter/couponActive',
                        redirect: '/marketing/couponCenter/couponActive/list',
                    },
                    {
                        path: '/marketing/couponCenter/couponActive/list',
                        name: '优惠券活动',
                        title: '优惠券活动',
                        component: './MarketingManage/CouponActive/CouponActiveListPage',
                    },
                    {
                        path: '/marketing/couponCenter/couponActive/list/add',
                        name: '新建优惠券活动',
                        title: '新建优惠券活动',
                        hideInMenu: true,
                        component: './MarketingManage/CouponActive/CouponActiveUpdatePage',
                        parentKeys: ['/marketing/couponCenter/couponActive/list'],
                    },
                    {
                        path: '/marketing/couponCenter/couponActive/list/update/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/CouponActive/CouponActiveUpdatePage',
                        parentKeys: ['/marketing/couponCenter/couponActive/list'],
                    },
                    {
                        path: '/marketing/couponCenter/couponActive/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/CouponActive/CouponActiveUpdatePage',
                        parentKeys: ['/marketing/couponCenter/couponActive/list'],
                    },
                    {
                        path: '/marketing/couponCenter/couponActive/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/CouponActive/CouponActiveUpdatePage',
                        parentKeys: ['/marketing/couponCenter/couponActive/list'],
                    },

                    /*************** 优惠券记录 ***************/
                    {
                        path: '/marketing/couponCenter/record',
                        redirect: '/marketing/couponCenter/record/list',
                    },
                    {
                        path: '/marketing/couponCenter/record/list',
                        name: '优惠券记录',
                        title: '优惠券记录',
                        component: './MarketingManage/CouponManage/CouponRecordListPage',
                    },
                    {
                        path: '/marketing/couponCenter/record/list/detail',
                        name: '优惠券记录详情',
                        title: '优惠券记录详情',
                        hideInMenu: true,
                        component: './MarketingManage/CouponManage/CouponRecordDetailPage',
                        parentKeys: ['/marketing/couponCenter/record/list'],
                    },

                    /*************** 发放记录 ***************/

                    {
                        path: '/marketing/couponCenter/couponPut',
                        name: '发放记录',
                        title: '发放记录',
                        component: './MarketingManage/CouponManage/CouponPutListPage',
                    },
                    {
                        path: '/marketing/couponCenter/adbCouponPut',
                        name: '发放记录',
                        title: '发放记录',
                        component: './MarketingManage/CouponManage/CouponPutAdbListPage',
                    },
                    {
                        path: '/marketing/couponCenter/couponUse',
                        name: '核销记录',
                        title: '核销记录',
                        component: './MarketingManage/CouponManage/CouponUseListPage',
                    },
                    {
                        path: '/marketing/couponCenter/adbCouponUse',
                        name: '核销记录',
                        title: '核销记录',
                        component: './MarketingManage/CouponManage/CouponUseAdbListPage',
                    },
                ],
            },

            /*************** 权益卡 ***************/

            {
                path: '/marketing/weekcard',
                name: '权益卡',
                title: '权益卡',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/weekcard',
                        redirect: '/marketing/weekcard/manage',
                    },
                    /*************** 权益卡管理 ***************/

                    {
                        path: '/marketing/weekcard/manage',
                        redirect: '/marketing/weekcard/manage/list',
                    },
                    {
                        path: '/marketing/weekcard/manage/list',
                        title: '权益卡管理',
                        name: '权益卡管理',
                        component: './MarketingManage/WeekCard/WeekCardManagePage',
                    },
                    {
                        path: '/marketing/weekcard/manage/list/details/:actId/:custId/:buyNo',
                        title: '权益卡详情',
                        name: '权益卡详情',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardDetailsPage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan',
                        name: '权益卡方案',
                        title: '权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanPage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan/add',
                        name: '新建权益卡方案',
                        title: '新建权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanUpdatePage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan/update/:actId',
                        name: '编辑权益卡方案',
                        title: '编辑权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanUpdatePage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan/copy/:actId',
                        name: '复制权益卡方案',
                        title: '复制权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanUpdatePage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan/look/:actId',
                        name: '查看权益卡方案',
                        title: '查看权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanDetailsPage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },
                    {
                        path: '/marketing/weekcard/manage/list/plan/details/:actId',
                        name: '查看权益卡方案',
                        title: '查看权益卡方案',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardPlanUpdatePage',
                        parentKeys: ['/marketing/weekcard/manage/list'],
                    },

                    /*************** 权益卡团购 ***************/

                    {
                        path: '/marketing/weekcard/groupbuy',
                        name: '权益卡团购',
                        title: '权益卡团购',
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyPage',
                    },
                    {
                        path: '/marketing/weekcard/groupbuy/add',
                        name: '新建权益卡团购',
                        title: '新建权益卡团购',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyUpdatePage',
                        parentKeys: ['/marketing/weekcard/groupbuy'],
                    },
                    {
                        path: '/marketing/weekcard/groupbuy/update/:actId',
                        name: '编辑权益卡团购',
                        title: '编辑权益卡团购',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyUpdatePage',
                        parentKeys: ['/marketing/weekcard/groupbuy'],
                    },
                    {
                        path: '/marketing/weekcard/groupbuy/copy/:actId',
                        name: '复制权益卡团购',
                        title: '复制权益卡团购',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyUpdatePage',
                        parentKeys: ['/marketing/weekcard/groupbuy'],
                    },
                    {
                        path: '/marketing/weekcard/groupbuy/look/:actId',
                        name: '查看权益卡团购',
                        title: '查看权益卡团购',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyDetailsPage',
                        parentKeys: ['/marketing/weekcard/groupbuy'],
                    },
                    {
                        path: '/marketing/weekcard/groupbuy/details/:actId',
                        name: '查看权益卡团购',
                        title: '查看权益卡团购',
                        hideInMenu: true,
                        component: './MarketingManage/WeekCard/WeekCardGroupBuyUpdatePage',
                        parentKeys: ['/marketing/weekcard/groupbuy'],
                    },

                    {
                        component: './404',
                    },
                ],
            },

            /*************** 平台活动 ***************/

            {
                path: '/marketing/card',
                name: '充电卡共享',
                title: '充电卡共享',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/card',
                        redirect: '/marketing/card/share',
                    },
                    {
                        path: '/marketing/card/share',
                        title: '充电卡共享',
                        name: '充电卡共享',
                        component: './MarketingManage/ChargeShare/ChargeShareManage',
                        hideInMenu: true,
                    },
                    {
                        path: '/marketing/card/share/detail',
                        title: '充电卡共享详情',
                        name: '充电卡共享详情',
                        component: './MarketingManage/ChargeShare/ChargeShareDetailPage',
                        hideInMenu: true,
                    },
                ],
            },

            {
                path: '/marketing/platformactive',
                name: '平台活动',
                title: '平台活动',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    /*************** 新人礼包 ***************/
                    {
                        path: '/marketing/platformactive/new-user',
                        redirect: '/marketing/platformactive/new-user/list',
                    },
                    {
                        path: '/marketing/platformactive/new-user/list',
                        name: '新人礼包',
                        title: '新人礼包',
                        component: './MarketingManage/NewUserGift/NewUserGiftListPage',
                    },
                    {
                        path: '/marketing/platformactive/new-user/list/add',
                        name: '新建活动',
                        title: '新建活动',
                        hideInMenu: true,
                        component: './MarketingManage/NewUserGift/NewUserGiftUpdatePage',
                        parentKeys: ['/marketing/platformactive/new-user/list'],
                    },
                    {
                        path: '/marketing/platformactive/new-user/list/update/:actId',
                        name: '编辑活动',
                        title: '编辑活动',
                        hideInMenu: true,
                        component: './MarketingManage/NewUserGift/NewUserGiftUpdatePage',
                        parentKeys: ['/marketing/platformactive/new-user/list'],
                    },
                    {
                        path: '/marketing/platformactive/new-user/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/NewUserGift/NewUserGiftUpdatePage',
                        parentKeys: ['/marketing/platformactive/new-user/list'],
                    },
                    {
                        path: '/marketing/platformactive/new-user/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/NewUserGift/NewUserGiftDetailPage',
                        parentKeys: ['/marketing/platformactive/new-user/list'],
                    },

                    /*************** 礼包管理 ***************/

                    {
                        path: '/marketing/platformactive/giftbag',
                        redirect: '/marketing/platformactive/giftbag/list',
                    },
                    {
                        path: '/marketing/platformactive/giftbag/list',
                        name: '礼包管理',
                        title: '礼包管理',
                        component: './MarketingManage/GiftBag/GiftBagListPage',
                    },
                    {
                        path: '/marketing/platformactive/giftbag/list/add',
                        name: '新建礼包',
                        title: '新建礼包',
                        hideInMenu: true,
                        component: './MarketingManage/GiftBag/GiftBagUpdatePage',
                        parentKeys: ['/marketing/platformactive/giftbag/list'],
                    },
                    {
                        path: '/marketing/platformactive/giftbag/list/update/:actId',
                        name: '礼包详情',
                        title: '礼包详情',
                        hideInMenu: true,
                        component: './MarketingManage/GiftBag/GiftBagUpdatePage',
                        parentKeys: ['/marketing/platformactive/giftbag/list'],
                    },
                    {
                        path: '/marketing/platformactive/giftbag/list/look/:actId',
                        name: '礼包详情',
                        title: '礼包详情',
                        hideInMenu: true,
                        component: './MarketingManage/GiftBag/GiftBagUpdatePage',
                        parentKeys: ['/marketing/platformactive/giftbag/list'],
                    },
                    {
                        path: '/marketing/platformactive/giftbag/list/copy/:actId',
                        name: '复制礼包',
                        title: '复制礼包',
                        hideInMenu: true,
                        component: './MarketingManage/GiftBag/GiftBagUpdatePage',
                        parentKeys: ['/marketing/platformactive/giftbag/list'],
                    },
                    /*************** 大促活动 ***************/
                    {
                        path: '/marketing/platformactive/large',

                        redirect: '/marketing/platformactive/large/list',
                    },
                    {
                        path: '/marketing/platformactive/large/list',
                        name: '会场活动',
                        title: '会场活动',
                        component: './MarketingManage/LargeActive/LargeActiveListPage',
                    },
                    {
                        path: '/marketing/platformactive/large/list/add',
                        name: '新建活动',
                        title: '新建活动',
                        hideInMenu: true,
                        component: './MarketingManage/LargeActive/LargeActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/large/list'],
                    },

                    {
                        path: '/marketing/platformactive/large/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/LargeActive/LargeActiveDetailPage',
                        parentKeys: ['/marketing/platformactive/large/list'],
                    },
                    {
                        path: '/marketing/platformactive/large/list/update/:actId',
                        name: '编辑活动',
                        title: '编辑活动',
                        hideInMenu: true,
                        component: './MarketingManage/LargeActive/LargeActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/large/list'],
                    },
                    {
                        path: '/marketing/platformactive/large/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/LargeActive/LargeActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/large/list'],
                    },
                    /*************** 限时秒杀 ***************/
                    {
                        path: '/marketing/platformactive/seckill',
                        redirect: '/marketing/platformactive/seckill/list',
                    },
                    {
                        path: '/marketing/platformactive/seckill/list',
                        name: '限时秒杀',
                        title: '限时秒杀',
                        component: './MarketingManage/Seckill/SeckillListPage',
                    },
                    {
                        path: '/marketing/platformactive/seckill/list/add',
                        name: '新增任务',
                        title: '新增任务',
                        hideInMenu: true,
                        component: './MarketingManage/Seckill/SeckillUpdatePage',
                        parentKeys: ['/marketing/platformactive/seckill/list'],
                    },
                    {
                        path: '/marketing/platformactive/seckill/list/update/:actId',
                        name: '编辑任务',
                        title: '编辑任务',
                        hideInMenu: true,
                        component: './MarketingManage/Seckill/SeckillUpdatePage',
                        parentKeys: ['/marketing/platformactive/seckill/list'],
                    },
                    {
                        path: '/marketing/platformactive/seckill/list/copy/:actId',
                        name: '复制任务',
                        title: '复制任务',
                        hideInMenu: true,
                        component: './MarketingManage/Seckill/SeckillUpdatePage',
                        parentKeys: ['/marketing/platformactive/seckill/list'],
                    },
                    {
                        path: '/marketing/platformactive/seckill/list/look/:actId',
                        name: '任务详情',
                        title: '任务详情',
                        hideInMenu: true,
                        component: './MarketingManage/Seckill/SeckillUpdatePage',
                        parentKeys: ['/marketing/platformactive/seckill/list'],
                    },
                    {
                        path: '/marketing/platformactive/seckill/list/data/:actId',
                        name: '限时秒杀活动数据',
                        title: '限时秒杀活动数据',
                        hideInMenu: true,
                        component: './MarketingManage/Seckill/SeckillDataPage',
                        parentKeys: ['/marketing/platformactive/seckill/list'],
                    },
                    /*************** 任务管理 ***************/
                    {
                        path: '/marketing/platformactive/taskManage',
                        redirect: '/marketing/platformactive/taskManage/list',
                    },
                    {
                        path: '/marketing/platformactive/taskManage/list',
                        name: '任务管理',
                        title: '任务管理',
                        component: './MarketingManage/TaskManage/TaskManageListPage',
                    },
                    {
                        path: '/marketing/platformactive/taskManage/list/add',
                        name: '新增任务',
                        title: '新增任务',
                        hideInMenu: true,
                        component: './MarketingManage/TaskManage/TaskFormPage',
                        parentKeys: ['/marketing/platformactive/taskManage/list'],
                    },
                    {
                        path: '/marketing/platformactive/taskManage/list/update/:taskId',
                        name: '编辑任务',
                        title: '编辑任务',
                        hideInMenu: true,
                        component: './MarketingManage/TaskManage/TaskFormPage',
                        parentKeys: ['/marketing/platformactive/taskManage/list'],
                    },
                    {
                        path: '/marketing/platformactive/taskManage/list/look/:taskId',
                        name: '任务详情',
                        title: '任务详情',
                        hideInMenu: true,
                        component: './MarketingManage/TaskManage/TaskDetailPage',
                        parentKeys: ['/marketing/platformactive/taskManage/list'],
                    },

                    /*************** 邀请有礼 ***************/
                    {
                        path: '/marketing/platformactive/invite',

                        redirect: '/marketing/platformactive/invite/list',
                    },
                    {
                        path: '/marketing/platformactive/invite/list',
                        name: '邀请有礼',
                        title: '邀请有礼',
                        component: './MarketingManage/Invite/Old/InviteListPage',
                    },
                    {
                        path: '/marketing/platformactive/invite/list/add',
                        name: '新建邀请有礼',
                        title: '新建邀请有礼',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Old/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite/list/update/:actId',
                        name: '编辑邀请有礼',
                        title: '编辑邀请有礼',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Old/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite/list/edit/:actId',
                        name: '邀请有礼详情',
                        title: '邀请有礼详情',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Old/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite/list/look/:actId',
                        name: '邀请有礼详情',
                        title: '邀请有礼详情',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Old/InviteDetailsPage',
                        parentKeys: ['/marketing/platformactive/invite/list'],
                    },
                    /*************** 新-邀请有礼 ***************/
                    {
                        path: '/marketing/platformactive/invite-mng',

                        redirect: '/marketing/platformactive/invite-mng/list',
                    },
                    {
                        path: '/marketing/platformactive/invite-mng/list',
                        name: '邀请有礼-新',
                        title: '邀请有礼-新',
                        component: './MarketingManage/Invite/Mng/InviteListPage',
                    },
                    {
                        path: '/marketing/platformactive/invite-mng/list/add',
                        name: '新建邀请有礼',
                        title: '新建邀请有礼',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Mng/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite-mng/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite-mng/list/update/:actId',
                        name: '编辑邀请有礼',
                        title: '编辑邀请有礼',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Mng/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite-mng/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite-mng/list/edit/:actId',
                        name: '邀请有礼详情',
                        title: '邀请有礼详情',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Mng/InviteUpdatePage',
                        parentKeys: ['/marketing/platformactive/invite-mng/list'],
                    },
                    {
                        path: '/marketing/platformactive/invite-mng/list/look/:actId',
                        name: '邀请有礼详情',
                        title: '邀请有礼详情',
                        hideInMenu: true,
                        component: './MarketingManage/Invite/Mng/InviteDetailsPage',
                        parentKeys: ['/marketing/platformactive/invite-mng/list'],
                    },

                    /*************** 福利中心 ***************/
                    {
                        path: '/marketing/platformactive/welfare',
                        redirect: '/marketing/platformactive/welfare/update',
                    },

                    {
                        path: '/marketing/platformactive/welfare/update',
                        name: '福利中心',
                        title: '福利中心',
                        component: './MarketingManage/Welfare/UpdatePage',
                    },

                    /*************** 充电有奖 ***************/
                    {
                        path: '/marketing/platformactive/prize',
                        redirect: '/marketing/platformactive/prize/list',
                    },
                    {
                        path: '/marketing/platformactive/prize/list',
                        name: '充电有奖',
                        title: '充电有奖',
                        component: './MarketingManage/Prize/PrizeList',
                    },
                    {
                        path: '/marketing/platformactive/prize/list/add',
                        name: '充电有奖',
                        title: '新建充电有奖',
                        hideInMenu: true,
                        component: './MarketingManage/Prize/PrizeEditPage',
                        parentKeys: ['/marketing/platformactive/prize/list'],
                    },
                    {
                        path: '/marketing/platformactive/prize/list/update/:taskId',
                        name: '充电有奖',
                        title: '编辑充电有奖',
                        hideInMenu: true,
                        component: './MarketingManage/Prize/PrizeEditPage',
                        parentKeys: ['/marketing/platformactive/prize/list'],
                    },
                    {
                        path: '/marketing/platformactive/prize/list/details/:taskId',
                        name: '充电有奖',
                        title: '查看充电有奖',
                        hideInMenu: true,
                        component: './MarketingManage/Prize/PrizeDetailPage',
                        parentKeys: ['/marketing/platformactive/prize/list'],
                    },

                    /*************** 抽奖活动 ***************/
                    {
                        path: '/marketing/platformactive/turnOver',

                        redirect: '/marketing/platformactive/turnOver/list',
                    },
                    {
                        path: '/marketing/platformactive/turnOver/list',
                        name: '抽奖活动',
                        title: '抽奖活动',
                        component: './MarketingManage/TurnOver/TurnOverListOldPage',
                    },
                    {
                        path: '/marketing/platformactive/turnOver/list/add',
                        name: '新建活动',
                        title: '新建活动',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdateOldPage',
                        parentKeys: ['/marketing/platformactive/turnOver/list'],
                    },
                    {
                        path: '/marketing/platformactive/turnOver/list/update/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdateOldPage',
                        parentKeys: ['/marketing/platformactive/turnOver/list'],
                    },
                    {
                        path: '/marketing/platformactive/turnOver/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdateOldPage',
                        parentKeys: ['/marketing/platformactive/turnOver/list'],
                    },
                    {
                        path: '/marketing/platformactive/turnOver/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdateOldPage',
                        parentKeys: ['/marketing/platformactive/turnOver/list'],
                    },

                    {
                        path: '/marketing/platformactive/turn-over/list',
                        name: '转盘活动',
                        title: '转盘活动',
                        component: './MarketingManage/TurnOver/TurnOverListPage',
                    },
                    {
                        path: '/marketing/platformactive/turn-over/list/add',
                        name: '新建活动',
                        title: '新建活动',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdatePage',
                        parentKeys: ['/marketing/platformactive/turn-over/list'],
                    },
                    {
                        path: '/marketing/platformactive/turn-over/list/update/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdatePage',
                        parentKeys: ['/marketing/platformactive/turn-over/list'],
                    },
                    {
                        path: '/marketing/platformactive/turn-over/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdatePage',
                        parentKeys: ['/marketing/platformactive/turn-over/list'],
                    },
                    {
                        path: '/marketing/platformactive/turn-over/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverUpdatePage',
                        parentKeys: ['/marketing/platformactive/turn-over/list'],
                    },
                    {
                        path: '/marketing/platformactive/turn-over/list/data/:actId',
                        name: '活动数据',
                        title: '活动数据',
                        hideInMenu: true,
                        component: './MarketingManage/TurnOver/TurnOverDataPage',
                        parentKeys: ['/marketing/platformactive/turn-over/list'],
                    },

                    /*************** 签到活动 ***************/

                    {
                        path: '/marketing/platformactive/signin',

                        redirect: '/marketing/platformactive/signin/list',
                    },
                    {
                        path: '/marketing/platformactive/signin/list',
                        name: '签到活动',
                        title: '签到活动',
                        component: './MarketingManage/Signin/SigninListPage',
                    },
                    {
                        path: '/marketing/platformactive/signin/list/record/:actId',
                        name: '签到活动',
                        title: '签到活动',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/SigninRecordListPage',
                        parentKeys: ['/marketing/platformactive/signin/list'],
                    },
                    {
                        path: '/marketing/platformactive/signin/list/add',
                        name: '新建活动',
                        title: '新建活动',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/SigninUpdatePage',
                        parentKeys: ['/marketing/platformactive/signin/list'],
                    },
                    {
                        path: '/marketing/platformactive/signin/list/update/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/SigninUpdatePage',
                        parentKeys: ['/marketing/platformactive/signin/list'],
                    },
                    {
                        path: '/marketing/platformactive/signin/list/look/:actId',
                        name: '活动详情',
                        title: '活动详情',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/SigninUpdatePage',
                        parentKeys: ['/marketing/platformactive/signin/list'],
                    },
                    {
                        path: '/marketing/platformactive/signin/list/copy/:actId',
                        name: '复制活动',
                        title: '复制活动',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/SigninUpdatePage',
                        parentKeys: ['/marketing/platformactive/signin/list'],
                    },

                    /*************** 活动推广 ***************/
                    {
                        path: '/marketing/platformactive/promotional',

                        redirect: '/marketing/platformactive/promotional/list',
                    },
                    {
                        path: '/marketing/platformactive/promotional/list',
                        name: '活动推广',
                        title: '活动推广',
                        component: './MarketingManage/PlatformActive/PlatformActiveListPage',
                    },
                    {
                        path: '/marketing/platformactive/promotional/list/add',
                        name: '新增活动',
                        title: '新增活动',
                        hideInMenu: true,
                        component: './MarketingManage/PlatformActive/PlatformActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/promotional/list'],
                    },
                    {
                        path: '/marketing/platformactive/promotional/list/look/:actId',
                        name: '查看活动',
                        title: '查看活动',
                        hideInMenu: true,
                        component: './MarketingManage/PlatformActive/PlatformActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/promotional/list'],
                    },
                    {
                        path: '/marketing/platformactive/promotional/list/update/:actId',
                        name: '编辑活动',
                        title: '编辑活动',
                        hideInMenu: true,
                        component: './MarketingManage/PlatformActive/PlatformActiveUpdatePage',
                        parentKeys: ['/marketing/platformactive/promotional/list'],
                    },
                    /*************** 商业活动(码上有钱) ***************/
                    {
                        name: '商业活动',
                        title: '商业活动',
                        path: '/marketing/platformactive/business/list',
                        routes: [
                            {
                                path: './',
                                exact: true,
                                component: './MarketingManage/BusinessTask/List',
                            },
                            {
                                path: '/marketing/platformactive/business/list/add',
                                name: '新增活动',
                                title: '新增活动',
                                hideInMenu: true,
                                component: './MarketingManage/BusinessTask/Add',
                                parentKeys: ['/marketing/platformactive/business/list'],
                            },
                            {
                                path: '/marketing/platformactive/business/list/edit/:taskId',
                                name: '编辑活动',
                                title: '编辑活动',
                                hideInMenu: true,
                                component: './MarketingManage/BusinessTask/Edit',
                                parentKeys: ['/marketing/platformactive/business/list'],
                            },
                            {
                                path: '/marketing/platformactive/business/list/detail/:taskId',
                                name: '活动详情',
                                title: '活动详情',
                                hideInMenu: true,
                                component: './MarketingManage/BusinessTask/Detail',
                                parentKeys: ['/marketing/platformactive/business/list'],
                            },
                        ],
                    },
                    {
                        component: './404',
                    },
                ],
            },

            {
                name: '任务中心',
                title: '任务中心',
                path: '/marketing/event',
                routes: [
                    {
                        path: '/marketing/event',
                        redirect: '/marketing/event/task/list',
                    },
                    {
                        path: '/marketing/event/task/list',
                        name: '任务管理',
                        title: '任务管理',
                        component: './EventManage/TaskManage/index',
                    },
                    {
                        path: '/marketing/event/task/list/add',
                        name: '新增任务',
                        title: '新增任务',
                        hideInMenu: true,
                        component: './EventManage/TaskManage/edit',
                        parentKeys: ['/marketing/event/task/list'],
                    },
                    {
                        path: '/marketing/event/task/list/edit',
                        name: '编辑任务',
                        title: '编辑任务',
                        hideInMenu: true,
                        component: './EventManage/TaskManage/edit',
                        parentKeys: ['/marketing/event/task/list'],
                    },
                    {
                        path: '/marketing/event/task/list/detail',
                        name: '任务详情',
                        title: '任务详情',
                        hideInMenu: true,
                        component: './EventManage/TaskManage/detail',
                        parentKeys: ['/marketing/event/task/list'],
                    },
                    {
                        path: '/marketing/event/task-package/list',
                        name: '任务包管理',
                        title: '任务包管理',
                        component: './EventManage/TaskPackageManage/index',
                    },
                    {
                        path: '/marketing/event/task-package/list/add',
                        name: '新增任务包',
                        title: '新增任务包',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/edit',
                        parentKeys: ['/marketing/event/task-package/list'],
                    },
                    {
                        path: '/marketing/event/task-package/list/edit',
                        name: '编辑任务包',
                        title: '编辑任务包',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/edit',
                        parentKeys: ['/marketing/event/task-package/list'],
                    },
                    {
                        path: '/marketing/event/task-package/list/detail',
                        name: '任务包详情',
                        title: '任务包详情',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/detail',
                        parentKeys: ['/marketing/event/task-package/list'],
                    },
                    {
                        path: '/marketing/event/charge-prize/list',
                        name: '充电有奖',
                        title: '充电有奖',
                        component: './EventManage/TaskPackageManage/index',
                    },
                    {
                        path: '/marketing/event/charge-prize/list/add',
                        name: '新增充电有奖',
                        title: '新增充电有奖',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/edit',
                        parentKeys: ['/marketing/event/charge-prize/list'],
                    },
                    {
                        path: '/marketing/event/charge-prize/list/edit',
                        name: '编辑充电有奖',
                        title: '编辑充电有奖',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/edit',
                        parentKeys: ['/marketing/event/charge-prize/list'],
                    },
                    {
                        path: '/marketing/event/charge-prize/list/detail',
                        name: '充电有奖详情',
                        title: '充电有奖详情',
                        hideInMenu: true,
                        component: './EventManage/TaskPackageManage/detail',
                        parentKeys: ['/marketing/event/charge-prize/list'],
                    },
                ],
            },

            /*************** 福利中心 ***************/

            {
                path: '/marketing/welfarecenter',
                redirect: '/marketing/welfarecenter/list',
            },
            {
                path: '/marketing/welfarecenter/list',
                name: '福利中心',
                title: '福利中心',
                component: './MarketingManage/WelfareCenter/WelfareListPage',
            },
            {
                path: '/marketing/welfarecenter/list/add',
                name: '新增福利',
                title: '新增福利',
                hideInMenu: true,
                component: './MarketingManage/WelfareCenter/WelfareUpdatePage',
                parentKeys: ['/marketing/welfarecenter/list'],
            },
            {
                path: '/marketing/welfarecenter/list/update/:actId',
                name: '福利修改',
                title: '福利修改',
                hideInMenu: true,
                component: './MarketingManage/WelfareCenter/WelfareUpdatePage',
                parentKeys: ['/marketing/welfarecenter/list'],
            },
            {
                path: '/marketing/welfarecenter/list/look/:actId',
                name: '福利详情',
                title: '福利详情',
                hideInMenu: true,
                component: './MarketingManage/WelfareCenter/WelfareUpdatePage',
                parentKeys: ['/marketing/welfarecenter/list'],
            },
            /*************** 消息中心 ***************/
            ...messageCenter,
            /*************** 活动位管理 ***************/
            {
                name: '活动位管理',
                title: '活动位管理',
                path: '/marketing/advertisement',
                routes: [
                    {
                        path: '/marketing/advertisement',
                        redirect: '/marketing/advertisement/list',
                    },
                    {
                        path: '/marketing/advertisement/list',
                        name: '图文广告',
                        title: '图文广告',
                        component: './MarketingManage/Advertisement/AdvertisementListPage',
                    },
                    {
                        path: '/marketing/advertisement/list/add',
                        name: '新建图文广告',
                        title: '新建图文广告',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/list'],
                    },
                    {
                        path: '/marketing/advertisement/list/update/:advertiseId',
                        name: '图文广告详情',
                        title: '图文广告详情',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/list'],
                    },
                    {
                        path: '/marketing/advertisement/list/copy/:advertiseId',
                        name: '图文广告详情',
                        title: '图文广告详情',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/list'],
                    },

                    {
                        path: '/marketing/advertisement/banner/list',
                        name: '文本广告',
                        title: '文本广告',
                        component: './MarketingManage/Advertisement/AdvertisementListPage',
                    },
                    {
                        path: '/marketing/advertisement/banner/list/add',
                        name: '新建文本广告',
                        title: '新建文本广告',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/banner/list'],
                    },
                    {
                        path: '/marketing/advertisement/banner/list/update/:advertiseId',
                        name: '文本广告详情',
                        title: '文本广告详情',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/banner/list'],
                    },
                    {
                        path: '/marketing/advertisement/banner/list/copy/:advertiseId',
                        name: '文本广告详情',
                        title: '文本广告详情',
                        hideInMenu: true,
                        component: './MarketingManage/Advertisement/AdvertisementUpdatePage',
                        parentKeys: ['/marketing/advertisement/banner/list'],
                    },
                ],
            },
            /*************** 商家充值 ***************/

            {
                path: '/marketing/rechargeManage',
                name: '商家充值',
                title: '商家充值',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/rechargeManage',
                        redirect: '/marketing/rechargeManage/rechargeActive',
                    },
                    /*************** 充值活动 ***************/
                    {
                        path: '/marketing/rechargeManage/rechargeActive',
                        redirect: '/marketing/rechargeManage/rechargeActive/list',
                    },
                    {
                        path: '/marketing/rechargeManage/rechargeActive/list',
                        name: '充值活动',
                        title: '充值活动',
                        component: './MarketingManage/RechargeManage/RechargeActiveListPage',
                    },
                    {
                        path: '/marketing/rechargeManage/rechargeActive/list/add',
                        name: '新增活动',
                        title: '新增活动',
                        component: './MarketingManage/RechargeManage/RechargeDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/marketing/rechargeManage/rechargeActive/list'],
                    },
                    {
                        path: '/marketing/rechargeManage/rechargeActive/list/update/:schemeId',
                        name: '活动编辑',
                        title: '活动编辑',
                        component: './MarketingManage/RechargeManage/RechargeDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/marketing/rechargeManage/rechargeActive/list'],
                    },
                    {
                        path: '/marketing/rechargeManage/rechargeActive/list/details/:schemeId',
                        name: '活动详情',
                        title: '活动详情',
                        component: './MarketingManage/RechargeManage/RechargeDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/marketing/rechargeManage/rechargeActive/list'],
                    },

                    /*************** 商家会员管理 ***************/
                    {
                        path: '/marketing/rechargeManage/memberManage',
                        redirect: '/marketing/rechargeManage/memberManage/list',
                    },
                    {
                        path: '/marketing/rechargeManage/memberManage/list',
                        name: '商家会员管理',
                        title: '商家会员管理',
                        component: './MarketingManage/RechargeManage/MemberManageListPage',
                    },
                    {
                        path: '/marketing/rechargeManage/memberManage/list/accountDetails/:payprivileCardId',
                        name: '商家会员管理',
                        title: '详情',
                        component: './MarketingManage/RechargeManage/MemberDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/marketing/rechargeManage/memberManage/list'],
                    },

                    /*************** 商家会员管理 ***************/

                    {
                        name: '记录查询',
                        title: '记录查询',
                        path: '/marketing/rechargeManage/records',
                        component: './MarketingManage/RechargeManage/RechargeRecordsPage',
                    },

                    {
                        component: './404',
                    },
                ],
            },
            {
                name: '小提示营销',
                title: '小提示营销',
                path: '/marketing/chargeTipsManage/manage',
                component: './MarketingManage/ChargeTipsManage/ManagePage',
            },
            /** 社群推广 */
            {
                path: '/marketing/community',
                name: '社群推广',
                title: '社群推广',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/community',
                        redirect: '/marketing/WxCommunity/Community',
                    },
                    {
                        name: '社群链接',
                        title: '社群链接',
                        path: '/marketing/community/links',
                        component: './MarketingManage/Community/Links',
                    },
                    {
                        name: '页面配置',
                        title: '页面配置',
                        hideInMenu: true,
                        path: '/marketing/community/landingpage',
                        component: './MarketingManage/Community/LandingPage',
                        parentKeys: ['/marketing/community/links'],
                    },
                ],
            },
            {
                name: '企微社群',
                title: '企微社群',
                path: '/marketing/WxCommunity/Community',
                component: './MarketingManage/WxCommunity/Community',
                parentKeys: ['/marketing/community'],
            },
            {
                name: '签到管理',
                title: '签到管理',
                path: '/marketing/signin/activity',
                routes: [
                    {
                        path: '/marketing/signin/activity',
                        redirect: '/marketing/signin/activity/list',
                    },
                    {
                        path: '/marketing/signin/activity/list',
                        name: '签到管理',
                        title: '签到管理',
                        exact: true,
                        component: './MarketingManage/Signin/Activity/List',
                    },
                    {
                        path: '/marketing/signin/activity/record',
                        name: '签到记录',
                        title: '签到记录',
                        exact: true,
                        component: './MarketingManage/Signin/Activity/Record',
                    },
                    {
                        name: '新建签到',
                        title: '新建签到',
                        path: '/marketing/signin/activity/list/add',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Add',
                        parentKeys: ['/marketing/signin/activity/list'],
                    },
                    {
                        name: '新建签到',
                        title: '新建签到',
                        path: '/marketing/signin/activity/list/add/:id',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Add',
                        parentKeys: ['/marketing/signin/activity/list'],
                    },
                    {
                        name: '编辑签到',
                        title: '编辑签到',
                        path: '/marketing/signin/activity/list/edit/:id',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Edit',
                        parentKeys: ['/marketing/signin/activity/list'],
                    },
                    {
                        name: '签到详情',
                        title: '签到详情',
                        path: '/marketing/signin/activity/list/detail/:id',
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Detail',
                        parentKeys: ['/marketing/signin/activity/list'],
                    },
                    {
                        path: '/marketing/signin/activity/supplement',
                        name: '补签卡管理',
                        title: '补签卡管理',
                        exact: true,
                        component: './MarketingManage/Signin/Activity/Supplement',
                    },
                    {
                        path: '/marketing/signin/activity/supplement/add',
                        name: '新增补签卡',
                        title: '新增补签卡',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Add',
                        parentKeys: ['/marketing/signin/activity/supplement'],
                    },
                    {
                        path: '/marketing/signin/activity/supplement/update/:actId',
                        name: '编辑补签卡',
                        title: '编辑补签卡',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Add',
                        parentKeys: ['/marketing/signin/activity/supplement'],
                    },
                    {
                        path: '/marketing/signin/activity/supplement/copy/:actId',
                        name: '复制补签卡',
                        title: '复制补签卡',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Add',
                        parentKeys: ['/marketing/signin/activity/supplement'],
                    },
                    {
                        path: '/marketing/signin/activity/supplement/look/:actId',
                        name: '查看补签卡',
                        title: '查看补签卡',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Add',
                        parentKeys: ['/marketing/signin/activity/supplement'],
                    },

                    {
                        path: '/marketing/signin/activity/supplement-record',
                        name: '补签卡记录',
                        title: '补签卡记录',
                        exact: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Record',
                    },
                    {
                        path: '/marketing/signin/activity/supplement-record/detail/:recordId',
                        name: '补签卡记录详情',
                        title: '补签卡记录详情',
                        exact: true,
                        hideInMenu: true,
                        component: './MarketingManage/Signin/Activity/Supplement/Record/detail',
                        parentKeys: ['/marketing/signin/activity/supplement-record'],
                    },
                ],
            },
            {
                name: '使用说明',
                title: '使用说明',
                path: '/marketing/Instructions/Instructions',
                component: './MarketingManage/Instructions/Instructions',
            },
            {
                name: '订阅消息',
                title: '订阅消息',
                path: '/marketing/subscribeMsg/manage',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgManagePage',
            },
            {
                name: '添加模板',
                title: '添加模板',
                path: '/marketing/subscribeMsg/manage/add',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgUpdate',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '编辑模板',
                title: '编辑模板',
                path: '/marketing/subscribeMsg/manage/update/:templateId',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgUpdate',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '模板详情',
                title: '模板详情',
                path: '/marketing/subscribeMsg/manage/details/:templateId',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgDetailPage',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '配置记录',
                title: '配置记录',
                path: '/marketing/subscribeMsg/manage/record/alipay',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgRecordPage',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '配置记录',
                title: '配置记录',
                path: '/marketing/subscribeMsg/manage/record/wechat',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgRecordPage',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '数据',
                title: '数据',
                path: '/marketing/subscribeMsg/manage/data/:templateId',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgDataPage',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '配置发送',
                title: '配置发送',
                path: '/marketing/subscribeMsg/manage/send/alipay',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgSend',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '配置发送',
                title: '配置发送',
                path: '/marketing/subscribeMsg/manage/send/wechat',
                component: './MarketingManage/SubscribeMsg/SubscribeMsgSend',
                parentKeys: ['/marketing/subscribeMsg/manage'],
                hideInMenu: true,
            },
            {
                name: '活动人群配置',
                title: '活动人群配置',
                path: '/marketing/activecrowd/list',
                component: './MarketingManage/ActiveCrowd/List',
            },

            /*************** 组件管理 ***************/

            {
                path: '/marketing/component',
                name: '组件管理',
                title: '组件管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/component',
                        redirect: '/marketing/component/module',
                    },
                    {
                        name: '组件模块管理',
                        title: '组件模块管理',
                        path: '/marketing/component/module',
                        component: './MarketingManage/ComponentManage/List',
                    },
                    {
                        name: '新增组件模块',
                        title: '新增组件模块',
                        path: '/marketing/component/module/add/:type',
                        component: './MarketingManage/ComponentManage/Add',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/module'],
                    },
                    {
                        name: '编辑组件模块',
                        title: '编辑组件模块',
                        path: '/marketing/component/module/edit/:id',
                        component: './MarketingManage/ComponentManage/Edit',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/module'],
                    },
                    {
                        name: '查看组件模块',
                        title: '查看组件模块',
                        path: '/marketing/component/module/detail/:id',
                        component: './MarketingManage/ComponentManage/Detail',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/module'],
                    },
                    {
                        name: '页面组件管理',
                        title: '页面组件管理',
                        path: '/marketing/component/pagecomponent',
                        component: './MarketingManage/PageComponentManage/List',
                    },
                    {
                        name: '添加组件',
                        title: '添加组件',
                        path: '/marketing/component/pagecomponent/add',
                        component: './MarketingManage/PageComponentManage/Add',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/pagecomponent'],
                    },

                    {
                        name: '编辑组件',
                        title: '编辑组件',
                        path: '/marketing/component/pagecomponent/edit/:id',
                        component: './MarketingManage/PageComponentManage/Edit',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/pagecomponent'],
                    },
                    {
                        name: '添加组件',
                        title: '添加组件',
                        path: '/marketing/component/pagecomponent/addVipGuide',
                        component: './MarketingManage/PageComponentManage/VipGuide',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/pagecomponent'],
                    },
                    {
                        name: '编辑组件',
                        title: '编辑组件',
                        path: '/marketing/component/pagecomponent/editVipGuide/:id',
                        component: './MarketingManage/PageComponentManage/VipGuide',
                        hideInMenu: true,
                        parentKeys: ['/marketing/component/pagecomponent'],
                    },
                ],
            },
            /*************** 商家活动管理 ***************/
            {
                path: '/marketing/merchantActivityManagement',
                name: '商家活动管理',
                title: '商家活动管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/merchantActivityManagement',
                        redirect: '/marketing/MerchantActivityManagement/targetedCouponIssuance',
                    },

                    {
                        name: '精准发券',
                        title: '精准发券',
                        path: '/marketing/merchantActivityManagement/targetedCouponIssuance',
                        component: './MarketingManage/TargetedCouponIssuance/List',
                    },
                    ...businessActive2_0Factory(
                        '打折立减',
                        'merchantActivityManagement/module-company-act',
                    ),
                    ...businessActive2_0Factory(
                        '打折立减',
                        'merchantActivityManagement/temp/module-company-act',
                    ),
                    {
                        name: '详情对比',
                        title: '详情对比',
                        path: '/marketing/merchantActivityManagement/targetedCouponIssuance/diff',
                        component: './MarketingManage/TargetedCouponIssuance/Diff',
                        hideInMenu: true,
                        parentKeys: [
                            '/marketing/merchantActivityManagement/targetedCouponIssuance',
                        ],
                    },
                    {
                        name: '查看精准发券',
                        title: '查看精准发券',
                        path: '/marketing/merchantActivityManagement/targetedCouponIssuance/detail',
                        component: './MarketingManage/TargetedCouponIssuance/Detail',
                        hideInMenu: true,
                        parentKeys: [
                            '/marketing/merchantActivityManagement/targetedCouponIssuance',
                        ],
                    },
                    {
                        name: '基础模板管理',
                        title: '基础模板管理',
                        path: '/marketing/merchantActivityManagement/businessModule',
                        component:
                            './MarketingManage/BusinessActive/ModuleActive/BaseModuleManagePage',
                    },
                    {
                        name: '营销分析',
                        title: '营销分析',
                        path: '/marketing/merchantActivityManagement/marketingAnalysis',
                        component: './MarketingManage/MarketingAnalysis/List',
                    },
                    {
                        name: '核销明细',
                        title: '核销明细',
                        path: '/marketing/merchantActivityManagement/marketingAnalysis/verification',
                        component: './MarketingManage/MarketingAnalysis/Verification/Details',
                        hideInMenu: true,
                        parentKeys: ['/marketing/merchantActivityManagement/marketingAnalysis'],
                    },
                    {
                        name: '打折立减',
                        title: '打折立减',
                        path: '/marketing/merchantActivityManagement/marketingAnalysis/discount',
                        component: './MarketingManage/MarketingAnalysis/Verification/Details',
                        hideInMenu: true,
                        parentKeys: ['/marketing/merchantActivityManagement/marketingAnalysis'],
                    },
                    {
                        name: '活动模板管理',
                        title: '活动模板管理',
                        path: '/marketing/merchantActivityManagement/activityTemplate',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/marketing/merchantActivityManagement/activityTemplate',
                                redirect:
                                    '/marketing/merchantActivityManagement/activityTemplate/targetedCouponIssuanceTemplate',
                            },
                            {
                                name: '精准发券模板',
                                title: '精准发券模板',
                                path: '/marketing/merchantActivityManagement/activityTemplate/targetedCouponIssuanceTemplate',
                                component: './MarketingManage/TargetedCouponIssuanceTemplate/List',
                            },
                            {
                                name: '编辑精准发券模板',
                                title: '编辑精准发券模板',
                                path: '/marketing/merchantActivityManagement/activityTemplate/targetedCouponIssuanceTemplate/edit',
                                component: './MarketingManage/TargetedCouponIssuanceTemplate/Edit',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/activityTemplate/targetedCouponIssuanceTemplate',
                                ],
                            },
                            {
                                name: '打折立减模板',
                                title: '打折立减模板',
                                path: '/marketing/merchantActivityManagement/activityTemplate/discountTemplate',
                                component: './MarketingManage/TargetedCouponIssuanceTemplate/List',
                            },
                            {
                                name: '编辑打折立减模板',
                                title: '编辑打折立减模板',
                                path: '/marketing/merchantActivityManagement/activityTemplate/discountTemplate/edit',
                                component: './MarketingManage/TargetedCouponIssuanceTemplate/Edit',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/activityTemplate/discountTemplate',
                                ],
                            },
                        ],
                    },
                    {
                        name: '用户配置管理',
                        title: '用户配置管理',
                        path: '/marketing/merchantActivityManagement/userConfigManage',
                        component: './MarketingManage/UserConfigManage/List',
                    },
                    {
                        name: '用户配置管理',
                        title: '用户配置管理',
                        path: '/marketing/merchantActivityManagement/userConfigManage/save',
                        component: './MarketingManage/UserConfigManage/Edit',
                        hideInMenu: true,
                    },

                    {
                        name: '营销中心配置',
                        title: '营销中心配置',
                        path: '/marketing/merchantActivityManagement/configManage',
                        component: './MarketingManage/MarketingConfig/Manage',
                    },
                    {
                        name: '基础配置',
                        title: '基础配置',
                        path: '/marketing/merchantActivityManagement/configManage/basic',
                        component: './MarketingManage/MarketingConfig/Basic',
                        hideInMenu: true,
                    },
                    {
                        name: '营销工具',
                        title: '营销工具',
                        path: '/marketing/merchantActivityManagement/configManage/tools',
                        component: './MarketingManage/MarketingConfig/Tools',
                        hideInMenu: true,
                    },
                    {
                        name: '营销工具',
                        title: '营销工具',
                        path: '/marketing/merchantActivityManagement/configManage/diagnosis',
                        component: './MarketingManage/MarketingConfig/Diagnosis',
                        hideInMenu: true,
                    },

                    {
                        name: '营销方案',
                        title: '营销方案',
                        path: '/marketing/merchantActivityManagement/configManage/plans',
                        component: './MarketingManage/MarketingConfig/Plans',
                        hideInMenu: true,
                    },
                    {
                        name: '案例分享',
                        title: '案例分享',
                        path: '/marketing/merchantActivityManagement/configManage/share',
                        component: './MarketingManage/MarketingConfig/Share',
                        hideInMenu: true,
                    },
                    {
                        name: '使用说明',
                        title: '使用说明',
                        path: '/marketing/merchantActivityManagement/configManage/explain',
                        component: './MarketingManage/MarketingConfig/Explain',
                        hideInMenu: true,
                    },
                    {
                        name: '线上招商方案',
                        title: '线上招商方案',
                        path: '/marketing/merchantActivityManagement/recruitMerchantsPlan',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan',
                                redirect:
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan',
                            },
                            {
                                name: '招商方案',
                                title: '招商方案',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan',
                                component: './MarketingManage/RecruitMerchantsPlan/Plan',
                            },
                            {
                                name: '新增招商方案',
                                title: '新增招商方案',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan/add',
                                component: './MarketingManage/RecruitMerchantsPlan/Plan/Add',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan',
                                ],
                            },
                            {
                                name: '编辑招商方案',
                                title: '编辑招商方案',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan/edit',
                                component: './MarketingManage/RecruitMerchantsPlan/Plan/Edit',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/plan',
                                ],
                            },
                            {
                                name: '报名列表',
                                title: '报名列表',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/enroll',
                                component: './MarketingManage/RecruitMerchantsPlan/Enroll',
                            },
                            {
                                name: '报名详情',
                                title: '报名详情',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/enroll/detail',
                                component: './MarketingManage/RecruitMerchantsPlan/Enroll/Detail',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/enroll',
                                ],
                            },
                            {
                                name: '审核',
                                title: '审核',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/enroll/examine',
                                component: './MarketingManage/RecruitMerchantsPlan/Enroll/Examine',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/enroll',
                                ],
                            },
                            {
                                name: '招商活动模板',
                                title: '招商活动模板',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/template',
                                component: './MarketingManage/RecruitMerchantsPlan/Template',
                            },
                            ...businessActive2_0Factory(
                                '基础价格补贴活动模板',
                                'merchantActivityManagement/recruitMerchantsPlan/template',
                            ),
                            {
                                name: '新建组队活动模板',
                                title: '新建组队活动模板',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/template/addTeam',
                                component:
                                    './MarketingManage/RecruitMerchantsPlan/Template/AddTeam',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/template',
                                ],
                            },
                            {
                                name: '其他福利',
                                title: '其他福利',
                                path: '/marketing/merchantActivityManagement/recruitMerchantsPlan/template/other',
                                component: './MarketingManage/RecruitMerchantsPlan/Template/Other',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/recruitMerchantsPlan/template',
                                ],
                            },
                        ],
                    },
                    {
                        name: '新站成长计划',
                        title: '新站成长计划',
                        path: '/marketing/merchantActivityManagement/newStationGrowthPlan',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan',
                                redirect:
                                    '/marketing/merchantActivityManagement/newStationGrowthPlan/plan',
                            },
                            {
                                name: '新站计划',
                                title: '新站计划',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/plan',
                                component: './MarketingManage/NewStationGrowthPlan/Plan',
                            },
                            {
                                name: '新建计划',
                                title: '新建计划',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/plan/add',
                                component: './MarketingManage/NewStationGrowthPlan/Plan/Add',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/newStationGrowthPlan/plan',
                                ],
                            },
                            {
                                name: '编辑计划',
                                title: '编辑计划',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/plan/edit',
                                component: './MarketingManage/NewStationGrowthPlan/Plan/Edit',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/newStationGrowthPlan/plan',
                                ],
                            },
                            {
                                name: '报名列表',
                                title: '报名列表',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/enroll',
                                component: './MarketingManage/NewStationGrowthPlan/Enroll',
                            },
                            {
                                name: '任务进度明细',
                                title: '任务进度明细',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/progress',
                                component: './MarketingManage/NewStationGrowthPlan/Progress',
                            },
                            {
                                name: '明细',
                                title: '明细',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/progress/detail',
                                component: './MarketingManage/NewStationGrowthPlan/Progress/Detail',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/newStationGrowthPlan/progress',
                                ],
                            },
                            {
                                name: '新站白名单',
                                title: '新站白名单',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/whitelist',
                                component: './MarketingManage/NewStationGrowthPlan/Whitelist',
                            },
                            {
                                name: '新站黑名单',
                                title: '新站黑名单',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/blacklist',
                                component: './MarketingManage/NewStationGrowthPlan/Blacklist',
                            },
                            {
                                name: '翻牌活动模板',
                                title: '翻牌活动模板',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/template',
                                component: './MarketingManage/NewStationGrowthPlan/Template',
                            },
                            {
                                name: '新建翻牌模板',
                                title: '新建翻牌模板',
                                path: '/marketing/merchantActivityManagement/newStationGrowthPlan/template/addTurn',
                                component:
                                    './MarketingManage/NewStationGrowthPlan/Template/AddTurn',
                                hideInMenu: true,
                                parentKeys: [
                                    '/marketing/merchantActivityManagement/newStationGrowthPlan/template',
                                ],
                            },
                        ],
                    },
                    {
                        name: '商家学院配置',
                        title: '商家学院配置',
                        path: '/marketing/merchantActivityManagement/businessCollege',
                        component: './MarketingManage/BusinessCollege',
                    },
                    {
                        name: '课程类别',
                        title: '新建类别',
                        path: '/marketing/merchantActivityManagement/businessCollege/category',
                        component: './MarketingManage/BusinessCollege/Category',
                        hideInMenu: true,
                        parentKeys: ['/marketing/merchantActivityManagement/businessCollege'],
                    },
                    {
                        name: '新建课程',
                        title: '新建课程',
                        path: '/marketing/merchantActivityManagement/businessCollege/add',
                        component: './MarketingManage/BusinessCollege/Add',
                        hideInMenu: true,
                        parentKeys: ['/marketing/merchantActivityManagement/businessCollege'],
                    },
                ],
            },
            /*************** 投放管理 ***************/
            {
                path: '/marketing/launch',
                name: '投放管理',
                title: '投放管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/marketing/launch',
                        redirect: '/marketing/Launch/activityExamine',
                    },
                    {
                        name: '投放活动审核',
                        title: '投放活动审核',
                        path: '/marketing/launch/activityExamine',
                        component: '../components/Layouts/FatherLayout',
                        routes: [
                            {
                                path: '/marketing/launch/activityExamine',
                                component: './MarketingManage/LaunchActivityExamine/List',
                            },
                            {
                                name: '投放活动审核日志',
                                title: '投放活动审核日志',
                                path: '/marketing/launch/activityExamine/activityExamineLog',
                                hideInMenu: true,
                                component: './MarketingManage/LaunchActivityExamineLog/List',
                                parentKeys: ['/marketing/launch/activityExamine'],
                            },
                            {
                                name: '投放活动审核日志详情',
                                title: '投放活动审核日志详情',
                                path: '/marketing/launch/activityExamine/activityExamineLogDetail',
                                hideInMenu: true,
                                component: './MarketingManage/LaunchActivityExamineLog/Detail',
                                parentKeys: ['/marketing/launch/activityExamine'],
                            },
                        ],
                    },

                    {
                        name: '投放计划管理',
                        title: '投放计划管理',
                        path: '/marketing/launch/plan',
                        component: './MarketingManage/LaunchPlan/List',
                    },
                ],
            },
            /*************** 订单优惠 ***************/
            {
                name: '订单优惠',
                title: '订单优惠',
                path: '/marketing/order-discounts',
                component: './MarketingManage/OrderDiscounts/List',
            },
            {
                name: '编辑订单优惠',
                title: '编辑订单优惠',
                path: '/marketing/order-discounts/edit',
                hideInMenu: true,
                component: './MarketingManage/OrderDiscounts/Edit',
                parentKeys: ['/marketing/order-discounts'],
            },
            {
                name: '查看订单优惠',
                title: '查看订单优惠',
                path: '/marketing/order-discounts/detail',
                hideInMenu: true,
                component: './MarketingManage/OrderDiscounts/Detail',
                parentKeys: ['/marketing/order-discounts'],
            },
            /*************** 页面模板管理 ***************/
            {
                name: '页面模板管理',
                title: '页面模板管理',
                path: '/marketing/page-template/manage',
                component: './MarketingManage/PageTemplateManage/List',
            },
            {
                name: '编辑页面模板',
                title: '编辑页面模板',
                path: '/marketing/page-template/edit',
                hideInMenu: true,
                component: './MarketingManage/PageTemplateManage/Edit',
                parentKeys: ['/marketing/page-template/manage'],
            },
            /*************** 发放权益活动 ***************/
            {
                name: '发放权益活动',
                title: '发放权益活动',
                path: '/marketing/distribute-benefits',
                component: './MarketingManage/DistributeBenefits/List',
            },
            {
                name: '编辑发放权益活动',
                title: '编辑发放权益活动',
                path: '/marketing/distribute-benefits/edit',
                hideInMenu: true,
                component: './MarketingManage/DistributeBenefits/Edit',
                parentKeys: ['/marketing/distribute-benefits'],
            },
            {
                name: '查看发放权益活动',
                title: '查看发放权益活动',
                path: '/marketing/distribute-benefits/detail',
                hideInMenu: true,
                component: './MarketingManage/DistributeBenefits/Detail',
                parentKeys: ['/marketing/distribute-benefits'],
            },
            // 下单引导策略
            {
                name: '下单引导策略',
                title: '下单引导策略',
                path: '/marketing/order-guidance-strategy',
                component: './MarketingManage/OrderGuidanceStrategy/List',
            },
            {
                name: '编辑下单引导策略',
                title: '编辑下单引导策略',
                path: '/marketing/order-guidance-strategy/edit',
                hideInMenu: true,
                component: './MarketingManage/OrderGuidanceStrategy/Edit',
                parentKeys: ['/marketing/order-guidance-strategy'],
            },
        ],
    },
];
