module.exports = [
    {
        name: '财务中心',
        title: '财务中心',
        icon: 'property-safety',
        path: '/financemanage',
        routes: [
            {
                path: '/financemanage',
                redirect: '/financemanage/ordercheck',
            },

            {
                path: '/financemanage/ordercheck',
                name: '订单对账',
                title: '订单对账',
                component: './FinanceManage/Ordercheck/OrderCheckListPage',
            },
            {
                path: '/financemanage/separateAccounts',
                name: '合作伙伴分账',
                title: '合作伙伴分账',
                component: './FinanceManage/SeparateAccounts/SeparateAccountsListPage',
            },

            /*************** 订单交易 ***************/
            {
                path: '/financemanage/orderTransaction',
                redirect: '/financemanage/orderTransaction/list',
            },
            {
                path: '/financemanage/orderTransaction/list',
                name: '订单交易',
                title: '订单交易',
                component: './FinanceManage/OrderTransaction/OrderTransactionListPage',
            },
            {
                path: '/financemanage/orderTransaction/list/details/:orderNo',
                name: '订单交易详情',
                title: '订单交易详情',
                component: './FinanceManage/OrderTransaction/OrderTransactionDetailsPage',
                hideInMenu: true,
                parentKeys: ['/financemanage/orderTransaction/list'],
            },

            /*************** 订单交易 ***************/
            {
                path: '/financemanage/adbOrderTransaction',
                redirect: '/financemanage/adbOrderTransaction/list',
            },
            {
                path: '/financemanage/adbOrderTransaction/list',
                name: '订单交易',
                title: '订单交易',
                component: './FinanceManage/OrderTransaction/OrderTransactionAdbListPage',
            },

            /*************** 运营商财务 ***************/

            {
                path: '/financemanage/operator',
                name: '运营商财务',
                title: '运营商财务',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    /*************** 账单管理 ***************/
                    {
                        path: '/financemanage/operator',
                        redirect: '/financemanage/operator/bill',
                    },

                    {
                        path: '/financemanage/operator/bill',
                        redirect: '/financemanage/operator/bill/billManage',
                    },
                    {
                        path: '/financemanage/operator/bill/billManage',
                        name: '账单管理',
                        title: '账单管理',
                        component: './FinanceManage/BillManage/BillManagePage',
                    },
                    {
                        path: '/financemanage/operator/bill/billSummary/day',
                        name: '日账单',
                        title: '日账单',
                        component: './FinanceManage/BillManage/BillSummaryPage',
                        hideInMenu: true,
                        parentKeys: ['/financemanage/operator/bill/billSummary/day'],
                    },
                    {
                        path: '/financemanage/operator/bill/billDetails/day',
                        name: '日账单明细',
                        title: '日账单明细',
                        component: './FinanceManage/BillManage/BillDetails/BillDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/financemanage/operator/bill/billSummary/day'],
                    },
                    {
                        path: '/financemanage/operator/bill/billSummary/day/month',
                        name: '月账单',
                        title: '月账单',
                        component: './FinanceManage/BillManage/BillSummaryPage',
                        hideInMenu: true,
                        parentKeys: ['/financemanage/operator/bill/billSummary/day'],
                    },
                    {
                        path: '/financemanage/operator/bill/billDetails/day/month',
                        name: '月账单明细',
                        title: '月账单明细',
                        component: './FinanceManage/BillManage/BillDetails/BillDetailsPage',
                        hideInMenu: true,
                        parentKeys: ['/financemanage/operator/bill/billSummary/day'],
                    },

                    {
                        path: '/financemanage/operator/tempBill',
                        name: '临时账单',
                        title: '临时账单',
                        component: './FinanceManage/BillManage/TempBill/TempBillListPage',
                    },
                    {
                        path: '/financemanage/operator/tradeDetails',
                        name: '收支明细',
                        title: '收支明细',
                        component: './FinanceManage/OperatorDetails/OperatorDetailsListPage',
                    },
                ],
            },
            /*************** 平台财务 ***************/
            {
                path: '/financemanage/platform',
                name: '平台财务',
                title: '平台财务',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/financemanage/platform/marketing',
                        name: '营销账单',
                        title: '营销账单',
                        component: './FinanceManage/Platform/Marketing/MarketingListPage',
                    },
                    {
                        path: '/financemanage/platform/personalBill',
                        name: '个人对账单',
                        title: '个人对账单',
                        component: './FinanceManage/Platform/PersonalBill/PersonalBillListPage',
                    },
                    {
                        path: '/financemanage/platform/settlementMsg',
                        name: '结算报文',
                        title: '结算报文',
                        component: './FinanceManage/Platform/SettlementMsg/SettlementListPage',
                    },
                ],
            },
            /*************** 坏账管理 ***************/

            {
                path: '/financemanage/refund',
                name: '坏账管理',
                title: '坏账管理',
                component: './FinanceManage/Refund/RefundListPage',
            },
            /*************** 发票管理 ***************/

            {
                path: '/financemanage/invoice',
                redirect: '/financemanage/invoice/manage',
            },
            {
                path: '/financemanage/invoice/manage',
                name: '发票管理',
                title: '发票管理',
                component: './FinanceManage/InvoiceManage/ManageListPage',
            },
            {
                path: '/financemanage/invoice/manage/details/:invoiceAppNo',
                name: '发票详情',
                title: '发票详情',
                component: './FinanceManage/InvoiceManage/DetailsPage',
                hideInMenu: true,
                parentKeys: ['/financemanage/invoice/manage'],
            },
            {
                path: '/financemanage/invoice/open',
                name: '开发票',
                title: '开发票',
                component: './FinanceManage/InvoiceManage/InvoiceOpenPage',
                hideInMenu: true,
                parentKeys: ['/financemanage/invoice/manage'],
            },
            {
                path: '/financemanage/invoice/pool',
                name: '发票池',
                title: '发票池',
                component: './FinanceManage/InvoiceManage/InvoicePoolPage',
            },

            {
                path: '/financemanage/contrastOrder',
                name: '对账管理',
                title: '对账管理',
                component: './FinanceManage/ContrastOrderManage/ContrastOrderManagePage',
            },
            /*************** 年度预算 ***************/

            {
                path: '/financemanage/budget',
                redirect: '/financemanage/budget/year',
            },
            {
                path: '/financemanage/budget/year',
                name: '年度预算',
                title: '年度预算',
                component: './FinanceManage/Budget/YearManagePage',
            },
            {
                path: '/financemanage/budget/year/add',
                name: '新增预算',
                title: '新增预算',
                component: './FinanceManage/Budget/UpdateYearPage',
                hideInMenu: true,
                parentKeys: ['/financemanage/budget/year'],
            },
            {
                path: '/financemanage/budget/year/update/:budgetId',
                name: '编辑预算',
                title: '编辑预算',
                component: './FinanceManage/Budget/UpdateYearPage',
                hideInMenu: true,
                parentKeys: ['/financemanage/budget/year'],
            },
            {
                path: '/financemanage/budget/year/month/:budgetId',
                name: '月度预算',
                title: '月度预算',
                component: './FinanceManage/Budget/MonthManagePage',
                hideInMenu: true,
                parentKeys: ['/financemanage/budget/year'],
            },

            {
                component: './404',
            },
        ],
    },
];
