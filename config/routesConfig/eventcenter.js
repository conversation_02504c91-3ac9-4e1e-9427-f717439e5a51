module.exports = [
    {
        path: '/eventcenter',
        name: '事件中心',
        title: '事件中心',
        icon: 'CarOutlined',
        component: '../components/Layouts/FatherLayout',
        routes: [
            {
                path: '/eventcenter/eventmanage',
                redirect: '/eventcenter/eventmanage/list',
            },
            {
                name: '事件管理',
                title: '事件管理',
                path: '/eventcenter/eventmanage/list',
                component: './MarketingManage/EventManage/List',
            },
            {
                name: '公共参数管理',
                title: '公共参数管理',
                path: '/eventcenter/eventmanage/common/param',
                component: './MarketingManage/EventManage/CommonParam',
                hideInMenu: true,
            },
            {
                name: '编辑事件',
                title: '编辑事件',
                path: '/eventcenter/eventmanage/list/record',
                component: './MarketingManage/EventManage/Record',
                hideInMenu: true,
            },
            {
                name: '目标事件管理',
                title: '目标事件管理',
                path: '/eventcenter/eventmanage/target/list',
                component: './MarketingManage/EventManage/TargetEventManage',
            },
            {
                name: '添加目标事件',
                title: '添加目标事件',
                path: '/eventcenter/eventmanage/target/list/add',
                component: './MarketingManage/EventManage/TargetEventManage/UpdatePage',
                hideInMenu: true,
            },
            {
                name: '编辑目标事件',
                title: '编辑目标事件',
                path: '/eventcenter/eventmanage/target/list/update/:targetId/:look?',
                component: './MarketingManage/EventManage/TargetEventManage/UpdatePage',
                hideInMenu: true,
            },
        ],
    },
];
