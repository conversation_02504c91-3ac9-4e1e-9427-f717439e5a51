import customerConfig from './customerConfig';
import fleetConfig from './fleetConfig';
module.exports = [
    {
        name: '商家中心',
        title: '商家中心',
        path: '/sellerCenter',
        icon: 'team',
        routes: [
            // 车队充电
            ...fleetConfig,
            {
                name: '三方渠道运营',
                icon: 'team',
                title: '三方渠道运营',
                path: '/sellerCenter/channel',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/sellerCenter/channel',
                        redirect: '/sellerCenter/channel/manage',
                    },

                    /*************** 渠道管理 ***************/

                    {
                        path: '/sellerCenter/channel/manage',
                        redirect: '/sellerCenter/channel/manage/list',
                    },
                    {
                        path: '/sellerCenter/channel/manage/list',
                        name: '渠道管理',
                        title: '渠道管理',
                        component: './SallerCenter/ChannelManage/ChannelManagePage',
                    },
                    {
                        path: '/sellerCenter/channel/manage/list/update',
                        name: '编辑渠道',
                        title: '编辑渠道',
                        component: './SallerCenter/ChannelManage/ChannelUpdatePage',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/channel/manage/list'],
                    },
                    {
                        path: '/sellerCenter/channel/manage/list/configure',
                        name: '渠道配置',
                        title: '渠道配置',
                        component: './SallerCenter/ChannelManage/ChannelConfigurePage',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/channel/manage/list'],
                    },
                    /*************** 渠道营销 ***************/
                    {
                        path: '/sellerCenter/channel/marketing',
                        name: '渠道营销',
                        title: '渠道营销',
                        component: './SallerCenter/Marketing/List',
                    },
                    {
                        path: '/sellerCenter/channel/marketing/add',
                        name: '新建渠道营销',
                        title: '新建渠道营销',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/channel/marketing'],
                        component: './SallerCenter/Marketing/Add',
                    },
                    {
                        path: '/sellerCenter/channel/marketing/add/:id',
                        name: '新建渠道营销',
                        title: '新建渠道营销',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/channel/marketing'],
                        component: './SallerCenter/Marketing/Add',
                    },
                    {
                        path: '/sellerCenter/channel/marketing/edit/:id',
                        name: '编辑渠道营销',
                        title: '编辑渠道营销',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/channel/marketing'],
                        component: './SallerCenter/Marketing/Edit',
                    },
                    /*************** 充电账户 ***************/

                    {
                        path: '/sellerCenter/channel/account',
                        name: '充电账户',
                        title: '充电账户',
                        component: './SallerCenter/AccountCenter/MarketingAccountPage',
                    },
                    /*************** 账单管理 ***************/

                    {
                        path: '/sellerCenter/channel/bill',
                        redirect: '/sellerCenter/channel/bill/list',
                    },
                    {
                        path: '/sellerCenter/channel/bill/list',
                        name: '账单管理',
                        title: '账单管理',
                        component: './SallerCenter/BillCenter/ChannelBillManagePage',
                    },
                    // {
                    //     path: '/sellerCenter/channel/bill/detail',
                    //     name: '账单明细',
                    //     title: '账单明细',
                    //     component: './SallerCenter/BillCenter/ChannelBillDetailPage',
                    // hideInMenu: true,
                    //     parentKeys: ['/sellerCenter/channel/bill/list'],
                    // },
                ],
            },
            {
                name: '运营商管理',
                icon: 'team',
                title: '运营商管理',
                path: '/sellerCenter/operatormanage',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/sellerCenter/operatormanage',
                        redirect: '/sellerCenter/operatormanage/operator',
                    },
                    /*************** 运营商管理 ***************/

                    {
                        path: '/sellerCenter/operatormanage/operator',

                        redirect: '/sellerCenter/operatormanage/operator/list',
                    },
                    {
                        path: '/sellerCenter/operatormanage/operator/list',
                        name: '运营商管理',
                        title: '运营商管理',
                        component: './OperatorManage/Operatormanage/OperatorList',
                    },
                    {
                        path: '/sellerCenter/operatormanage/operator/list/add',
                        name: '运营商新增',
                        title: '运营商新增',
                        component: './OperatorManage/Operatormanage/OperatorUpdateDetail',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/operatormanage/operator/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/operator/list/update/:buildId',
                        name: '运营商编辑',
                        title: '运营商编辑',
                        component: './OperatorManage/Operatormanage/OperatorUpdateDetail',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/operatormanage/operator/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/operator/list/detail/:buildId',
                        name: '运营商详情',
                        title: '运营商详情',
                        component: './OperatorManage/Operatormanage/OperatorDetail',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/operatormanage/operator/list'],
                    },

                    /*************** 进件审核 ***************/
                    {
                        path: '/sellerCenter/operatormanage/audit',
                        redirect: '/sellerCenter/operatormanage/audit/list',
                    },
                    {
                        path: '/sellerCenter/operatormanage/audit/list',
                        name: '进件审核',
                        title: '进件审核',
                        component: './OperatorManage/Auditmanage/AuditList',
                    },
                    {
                        path: '/sellerCenter/operatormanage/audit/list/detail/:operInputTempId',
                        name: '审核详情',
                        title: '审核详情',
                        component: './OperatorManage/Auditmanage/AuditDetail',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/operatormanage/audit/list'],
                    },

                    /*************** 分润规则 ***************/

                    {
                        path: '/sellerCenter/operatormanage/profitRule',
                        redirect: '/sellerCenter/operatormanage/profitRule/list',
                    },
                    {
                        path: '/sellerCenter/operatormanage/profitRule/list',
                        name: '分润规则',
                        title: '财务中心',
                        component: './FinanceManage/ProfitRule/ProfitRuleListPage',
                    },
                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/details',
                        name: '分润规则详情',
                        title: '分润规则详情',
                        hideInMenu: true,
                        component: './FinanceManage/ProfitRule/ProfitRuleDetails',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/templateManage',
                        name: '模板管理',
                        title: '模板管理',
                        hideInMenu: true,
                        component: './FinanceManage/ProfitRule/ProfitRuleTemplateListPage',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/editHistory',
                        name: '配置历史',
                        title: '配置历史',
                        hideInMenu: true,
                        component: './FinanceManage/ProfitRule/EditHistoryListPage',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/details/:ruleId',
                        name: '分润规则详情',
                        title: '分润规则详情',
                        hideInMenu: true,
                        component: './FinanceManage/ProfitRule/ProfitRuleDetails',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },

                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/stationlist/:operId',
                        name: '场站规则配置',
                        title: '场站规则配置',
                        hideInMenu: true,
                        component: './FinanceManage/ProfitRule/StationRuleListPage',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },

                    {
                        path: '/sellerCenter/operatormanage/profitRule/list/citylist/:operId',
                        name: '城市规则配置',
                        title: '城市规则配置',
                        hideInMenu: true,
                        component: './FinanceManage/CityRule/CityRuleListPage',
                        parentKeys: ['/sellerCenter/operatormanage/profitRule/list'],
                    },

                    /*************** 购电结算规则 ***************/

                    {
                        path: '/sellerCenter/operatormanage/purchase',
                        redirect: '/sellerCenter/operatormanage/purchase/list',
                    },
                    {
                        path: '/sellerCenter/operatormanage/purchase/list',
                        name: '购电结算规则',
                        title: '购电结算规则',
                        component: './FinanceManage/Purchase/PurchaseListPage',
                    },
                    {
                        path: '/sellerCenter/operatormanage/purchase/list/details',
                        name: '购电结算规则详情',
                        title: '购电结算规则详情',
                        hideInMenu: true,
                        component: './FinanceManage/Purchase/PurchaseDetails',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/purchase/list/editHistory',
                        name: '配置历史',
                        title: '配置历史',
                        hideInMenu: true,
                        component: './FinanceManage/Purchase/EditHistoryListPage',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/purchase/list/templateManage',
                        name: '模板管理',
                        title: '模板管理',
                        hideInMenu: true,
                        component: './FinanceManage/Purchase/PurchaseTemplateListPage',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/purchase/list/details/:ruleId',
                        name: '购电结算规则详情',
                        title: '购电结算规则详情',
                        hideInMenu: true,
                        component: './FinanceManage/Purchase/PurchaseDetails',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },

                    {
                        path: '/sellerCenter/operatormanage/purchase/list/stationlist/:operId',
                        name: '场站规则配置',
                        title: '场站规则配置',
                        hideInMenu: true,
                        component: './FinanceManage/Purchase/StationPurchaseListPage',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },

                    {
                        path: '/sellerCenter/operatormanage/purchase/list/citylist/:operId',
                        name: '城市规则配置',
                        title: '城市规则配置',
                        hideInMenu: true,
                        component: './FinanceManage/CityRule/CityRuleListPage',
                        parentKeys: ['/sellerCenter/operatormanage/purchase/list'],
                    },
                    {
                        path: '/sellerCenter/operatormanage/account-book-manage',
                        redirect: '/sellerCenter/operatormanage/account-book-manage/list',
                    },
                    {
                        path: '/sellerCenter/operatormanage/account-book-manage/list',
                        name: '台账管理',
                        title: '台账管理',
                        component: './OperatorManage/AccountBook/List',
                    },
                    {
                        path: '/sellerCenter/operatormanage/account-book-manage/detail',
                        name: '台账管理',
                        title: '台账管理',
                        component: './OperatorManage/AccountBook/Detail',
                        hideInMenu: true,
                        parentKeys: ['/sellerCenter/operatormanage/account-book-manage/list'],
                    },
                    /**************充电引导说明*************** */
                    {
                        path: '/sellerCenter/operatormanage/chargeGuide/list',
                        name: '充电引导说明',
                        title: '充电引导说明',
                        component: './FinanceManage/ChargeGuide/List',
                    },
                ],
            },
            ...customerConfig,
            /*************** 合作伙伴 ***************/
            {
                path: '/sellerCenter/partners',
                redirect: '/sellerCenter/partners/list',
            },
            {
                path: '/sellerCenter/partners/list',
                name: '合作伙伴',
                title: '合作伙伴',
                component: './OperatorManage/Partners/PartnersListPage',
            },
            {
                path: '/sellerCenter/partners/list/add',
                name: '新增伙伴',
                title: '新增伙伴',
                component: './OperatorManage/Partners/PartnersUpdatePage',
                hideInMenu: true,
                parentKeys: ['/sellerCenter/partners/list'],
            },
            {
                path: '/sellerCenter/partners/list/update/:partnerId',
                name: '伙伴详情',
                title: '伙伴详情',
                component: './OperatorManage/Partners/PartnersUpdatePage',
                hideInMenu: true,
                parentKeys: ['/sellerCenter/partners/list'],
            },
            /*************** 加入新电途 ***************/
            {
                path: '/sellerCenter/joinxdt',
                redirect: '/sellerCenter/joinxdt/list',
            },
            {
                path: '/sellerCenter/joinxdt/list',
                name: '运营商申请',
                title: '运营商申请',
                component: './SallerCenter/JoinXdt',
            },
            /*************** 线索管理 ***************/
            {
                path: '/sellerCenter/clue/manage',
                name: '线索管理',
                title: '线索管理',
                component: './SallerCenter/ClueCenter/ClueManagePage',
            },
            /************** 合同管理 **************/

            {
                path: '/sellerCenter/contract',
                redirect: '/sellerCenter/contract/todo/list',
            },
            {
                path: '/sellerCenter/contract/todo/list',
                name: '合同待办',
                title: '合同待办',
                component: './SallerCenter/ContractManage/ContractManageListPage',
            },
            {
                path: '/sellerCenter/contract/todo/list/detail/:merchantId',
                name: '合同待办',
                title: '合同待办',
                component: './SallerCenter/ContractManage/ContractManageDetailPage',
                hideInMenu: true,
                parentKeys: ['/sellerCenter/contract/todo/list'],
            },
        ],
    },
];
