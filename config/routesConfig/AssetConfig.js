module.exports = [
    {
        path: '/assetCenter',
        name: '资产中心',
        title: '资产中心',
        icon: 'property-safety',
        component: '../components/Layouts/FatherLayout',
        routes: [
            {
                path: '/assetCenter',
                redirect: '/assetCenter/informationManage',
            },
            /************** 场站管理 **************/
            {
                path: '/assetCenter/stationManage',
                redirect: '/assetCenter/stationManage/list',
            },

            {
                path: '/assetCenter/stationManage/list',
                name: '场站管理',
                title: '场站管理',
                component: './AssetCenter/StationManage/StationManageListPage',
            },
            {
                path: '/assetCenter/stationManage/list/detail/:stationId',
                name: '场站详情',
                title: '场站详情',
                component: './AssetCenter/StationManage/StationManageDetailPage',
                hideInMenu: true,
                parentKeys: ['/assetCenter/stationManage/list'],
            },
            {
                path: '/assetCenter/stationManage/list/history-price',
                name: '历史价格查询',
                title: '历史价格查询',
                component: './AssetCenter/StationManage/StationHistoryPricePage',
                hideInMenu: true,
                parentKeys: ['/assetCenter/stationManage/list'],
            },
            {
                path: '/assetCenter/stationManage/list/update/:stationId',
                name: '场站修改',
                title: '场站修改',
                component: './AssetCenter/StationManage/StationManageUpdatePage',
                hideInMenu: true,
                parentKeys: ['/assetCenter/stationManage/list'],
            },
            {
                path: '/assetCenter/stationManage/stopWhitelist',
                name: '场站停充白名单',
                title: '场站停充白名单',
                component: './AssetCenter/StationManage/StopWhitelist/List',
                parentKeys: ['/assetCenter/stationManage'],
            },

            /************** 审批记录 **************/
            {
                path: '/assetCenter/audit/list',
                name: '审批记录',
                title: '审批记录',
                component: './AssetCenter/Audit/List',
            },
            {
                path: '/assetCenter/audit/list/detail/:id',
                name: '审批详情',
                title: '审批详情',
                component: './AssetCenter/Audit/Detail',
                parentKeys: ['/assetCenter/audit/list'],
                hideInMenu: true,
            },
            /************** 电桩管理 **************/

            {
                path: '/assetCenter/pileManage',
                redirect: '/assetCenter/pileManage/list',
            },
            {
                path: '/assetCenter/pileManage/list',
                name: '电桩管理',
                title: '电桩管理',
                component: './AssetCenter/StationManage/PileManageListPage',
            },
            {
                path: '/assetCenter/pileManage/list/detail',
                name: '电桩详情',
                title: '电桩详情',
                component: './AssetCenter/StationManage/PileManageDetailPage',
                hideInMenu: true,
                parentKeys: ['/assetCenter/pileManage/list'],
            },
            /************** 操作记录 **************/
            {
                path: '/assetCenter/operationManage',
                redirect: '/assetCenter/operationManage/list',
            },

            {
                path: '/assetCenter/operationManage/list',
                name: '操作记录',
                title: '操作记录',
                component: './AssetCenter/OperatorManage/List',
            },
            /************** 信息变更管控 **************/
            {
                path: '/assetCenter/informationManage',
                redirect: '/assetCenter/informationManage/station/list',
            },
            {
                path: '/assetCenter/informationManage/station/list',
                name: '场站信息变更',
                title: '场站信息变更',
                component: './AssetCenter/InformationManage/StationInformationListPage',
            },

            {
                path: '/assetCenter/informationManage/station/list/detail/:serviceId',
                name: '场站信息变更详情',
                title: '场站信息变更详情',
                hideInMenu: true,
                component: './AssetCenter/InformationManage/StationInformationDetailPage',
                parentKeys: ['/assetCenter/informationManage/station/list'],
            },

            {
                path: '/assetCenter/informationManage/pile/list',
                name: '电桩信息变更',
                title: '电桩信息变更',
                component: './AssetCenter/InformationManage/PileInformationListPage',
            },

            {
                path: '/assetCenter/informationManage/pile/list/detail/:equipId',
                name: '电桩信息变更详情',
                title: '电桩信息变更详情',
                hideInMenu: true,
                component: './AssetCenter/InformationManage/PileInformationDetailPage',
                parentKeys: ['/assetCenter/informationManage/pile/list'],
            },

            {
                path: '/assetCenter/informationManage/sensitive',
                name: '敏感词库',
                title: '敏感词库',
                component: './AssetCenter/InformationManage/SensitiveManagePage',
            },

            /************** 标签管理 **************/

            {
                path: '/assetCenter/tagManage',
                redirect: '/assetCenter/tagManage/station',
            },

            {
                path: '/assetCenter/tagManage/station',
                name: '场站标签',
                title: '场站标签',
                component: './AssetCenter/TagManage/StationListPage',
            },

            /************** 贴码管理 **************/
            {
                path: './qrcode',
                name: '贴码管理',
                routes: [
                    {
                        path: './list',
                        name: '贴码管理',
                        title: '贴码管理',
                        component: './AssetCenter/QrcodeManage/List',
                        exact: true,
                    },
                    {
                        path: './list/batch/:id',
                        name: '二维码绑定情况',
                        title: '二维码绑定情况',
                        component: './AssetCenter/QrcodeManage/BatchList',
                        hideInMenu: true,
                    },
                    {
                        path: './template',
                        name: '模板管理',
                        title: '模板管理',
                        exact: true,
                        component: './AssetCenter/QrcodeManage/Template/List',
                    },
                    {
                        path: './template/add',
                        name: '新建模板',
                        title: '新建模板',
                        component: './AssetCenter/QrcodeManage/Template/Add',
                        hideInMenu: true,
                    },
                    {
                        path: './template/edit/:id',
                        name: '编辑模板',
                        title: '编辑模板',
                        component: './AssetCenter/QrcodeManage/Template/Edit',
                        hideInMenu: true,
                    },
                    {
                        path: './generate',
                        name: '生成二维码',
                        title: '生成二维码',
                        component: './AssetCenter/QrcodeManage/Template/Generate',
                    },
                ],
            },
            /************** 定时任务 **************/

            {
                path: '/assetCenter/TimerTaskManage',
                redirect: '/assetCenter/TimerTaskManage/list',
            },

            {
                path: '/assetCenter/TimerTaskManage/list',
                name: '定时管理',
                title: '定时管理',
                component: './AssetCenter/TimerTaskManage/TimerListPage',
            },
            {
                path: '/assetCenter/TimerTaskManage/list/add',
                name: '新增定时',
                title: '新建营销活动',
                hideInMenu: true,
                component: './AssetCenter/TimerTaskManage/TimerUpdatePage',
                parentKeys: ['/assetCenter/TimerTaskManage/list'],
            },
            {
                path: '/assetCenter/TimerTaskManage/list/update/:taskId',
                name: '定时修改',
                title: '定时修改',
                hideInMenu: true,
                component: './AssetCenter/TimerTaskManage/TimerUpdatePage',
                parentKeys: ['/assetCenter/TimerTaskManage/list'],
            },
            {
                path: '/assetCenter/TimerTaskManage/list/look/:taskId',
                name: '定时详情',
                title: '定时详情',
                hideInMenu: true,
                component: './AssetCenter/TimerTaskManage/TimerUpdatePage',
                parentKeys: ['/assetCenter/TimerTaskManage/list'],
            },
            {
                path: '/assetCenter/TimerTaskManage/task/list',
                name: '任务管理',
                title: '任务管理',
                component: './AssetCenter/TimerTaskManage/TaskListPage',
            },

            /************** 站点推荐 **************/
            {
                path: '/assetCenter/stationRecommend',
                name: '站点推荐',
                routes: [
                    {
                        path: '/assetCenter/stationRecommend',
                        redirect: '/assetCenter/stationRecommend/manage',
                    },
                    {
                        path: '/assetCenter/stationRecommend/manage',
                        name: '业务规则管理',
                        title: '业务规则管理',
                        component: './AssetCenter/StationRecommend/RuleManage/List',
                        exact: true,
                    },
                    {
                        path: '/assetCenter/stationRecommend/manage/edit',
                        name: '配置业务规则',
                        title: '配置业务规则',
                        component: './AssetCenter/StationRecommend/RuleManage/Edit',
                        hideInMenu: true,
                    },
                ],
            },
            /************** 站点卡片标签管理 **************/
            {
                path: '/assetCenter/stationCardLabel',
                name: '站点卡片标签',
                routes: [
                    {
                        path: '/assetCenter/stationCardLabel',
                        redirect: '/assetCenter/stationCardLabel/manage',
                    },
                    {
                        path: '/assetCenter/stationCardLabel/manage',
                        name: '站点卡片标签',
                        title: '站点卡片标签',
                        component: './AssetCenter/StationCardLabel/CardLabelManage/List',
                        exact: true,
                    },
                    {
                        path: '/assetCenter/stationCardLabel/manage/add',
                        name: '新增站点卡片标签',
                        title: '新增站点卡片标签',
                        component: './AssetCenter/StationCardLabel/CardLabelManage/Edit',
                        hideInMenu: true,
                    },
                    {
                        path: '/assetCenter/stationCardLabel/manage/edit',
                        name: '编辑站点卡片标签',
                        title: '编辑站点卡片标签',
                        component: './AssetCenter/StationCardLabel/CardLabelManage/Edit',
                        hideInMenu: true,
                    },
                ],
            },
            /************** 即插即充 **************/

            {
                path: '/assetCenter/plugAndCharge',
                redirect: '/assetCenter/plugAndCharge/asset',
            },
            {
                path: '/assetCenter/plugAndCharge/asset',
                name: '即插即充资产',
                title: '即插即充资产',
                component: './AssetCenter/PlugAndCharge/Asset',
                parentKeys: ['/assetCenter/plugAndCharge/asset'],
            },
            {
                path: '/assetCenter/plugAndCharge/config',
                name: '即插即充配置',
                title: '即插即充配置',
                component: './AssetCenter/PlugAndCharge/Config',
                parentKeys: ['/assetCenter/plugAndCharge/asset'],
            },

            {
                component: './404',
            },
        ],
    },
];
