const formatPriceRoute = (route, routeName) => {
    const preRoute = `/price/selling/${route}`;
    let preRouteName = routeName;
    if (routeName === '对外') {
        preRouteName = '平台';
    }

    return [
        {
            path: preRoute,
            title: `${routeName}售价`,
            name: `${routeName}售价`,
            component: './PriceManage/SellingPrice/List',
        },
        {
            path: `${preRoute}/add`,
            title: `${routeName}售价规则配置`,
            title: `${routeName}售价规则配置`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/RuleAdd',
            parentKeys: [preRoute],
        },
        {
            path: `${preRoute}/edit/:ruleId`,
            title: `${routeName}售价规则配置`,
            name: `${routeName}售价规则配置`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/RuleEdit',
            parentKeys: [preRoute],
        },
        {
            path: `${preRoute}/service/list/:operId/:operName`,

            title: `${routeName}售价规则详情`,
            name: `${routeName}售价规则详情`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/ServiceRuleList',
            parentKeys: [preRoute],
        },
        {
            path: `${preRoute}/service/detail/:operId/:operName`,
            title: `${preRouteName}定价配置明细`,
            name: `${preRouteName}定价配置明细`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/ServiceDetailList',
            parentKeys: [preRoute],
        },
        {
            path: `${preRoute}/station/detail/:stationId`,
            title: `场站${routeName}售价规则查看`,
            name: `场站${routeName}售价规则查看`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/StationRuleDetail',
            parentKeys: [preRoute],
        },
        {
            path: `${preRoute}/template`,
            title: `${preRouteName}定价规则模板`,
            name: `${preRouteName}定价规则模板`,
            hideInMenu: true,
            component: './PriceManage/SellingPrice/RuleTemplate',
            parentKeys: [preRoute],
        },
    ];
};
module.exports = [
    {
        path: '/price',
        name: '价格管理',
        title: '价格管理',
        icon: 'CarOutlined',
        component: '../components/Layouts/FatherLayout',
        routes: [...formatPriceRoute('rule', '对外'), ...formatPriceRoute('channelRule', '渠道')],
    },
];
