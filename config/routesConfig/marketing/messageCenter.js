module.exports = [
    {
        name: '消息中心',
        title: '消息中心',
        path: '/marketing/message',
        component: '../components/Layouts/FatherLayout',
        routes: [
            {
                path: '/marketing/message',
                redirect: '/marketing/message/template/manage',
            },
            {
                name: '消息模版管理',
                title: '消息模版管理',
                path: '/marketing/message/template/manage',
                component: './MessageCenter/Template/Manage',
            },
            {
                name: '新增消息模版',
                title: '新增消息模版',
                path: '/marketing/message/template/manage/add',
                component: './MessageCenter/Template/Manage/Add',
                hideInMenu: true,
                parentKeys: ['/marketing/message/template/manage'],
            },
            {
                name: '编辑消息模版',
                title: '编辑消息模版',
                path: '/marketing/message/template/manage/edit/:id',
                component: './MessageCenter/Template/Manage/Edit',
                hideInMenu: true,
                parentKeys: ['/marketing/message/template/manage'],
            },
            {
                name: '短信触达查询',
                title: '短信触达查询',
                path: '/marketing/message/sms/history',
                component: './MessageCenter/SMS/Manage',
            },
            {
                name: '触达任务管理',
                title: '触达任务管理',
                path: '/marketing/message/touch/manage',
                component: './MessageCenter/Touch/Manage/List',
            },
            {
                name: '创建触达任务',
                title: '创建触达任务',
                path: '/marketing/message/touch/manage/add',
                component: './MessageCenter/Touch/Manage/Add',
                hideInMenu: true,
            },
            {
                name: '自动推送场景管理',
                title: '自动推送场景管理',
                path: '/marketing/message/autopush/manage',
                component: './MessageCenter/AutoPush/Manage',
            },
            {
                name: '配置自动推送场景',
                title: '配置自动推送场景',
                path: '/marketing/message/autopush/manage/configure',
                component: './MessageCenter/AutoPush/Manage/Configure',
                hideInMenu: true,
            },
            {
                name: '订阅场景模版池管理',
                title: '订阅场景模版池管理',
                path: '/marketing/message/template/pool',
                component: './MessageCenter/Template/Pool',
            },
            {
                name: '编辑模版池',
                title: '编辑模版池',
                path: '/marketing/message/template/pool/edit/:id',
                component: './MessageCenter/Template/Pool/Edit',
                hideInMenu: true,
                parentKeys: ['/marketing/message/template/pool'],
            },
            {
                name: '推送节点管理',
                title: '推送节点管理',
                path: '/marketing/message/push-node/manage',
                component: './MessageCenter/Node/PushManage',
            },
            {
                name: '编辑推送节点',
                title: '编辑推送节点',
                path: '/marketing/message/push-node/manage/edit',
                component: './MessageCenter/Node/PushManage/add',
                hideInMenu: true,
            },
            {
                name: '新增推送节点',
                title: '新增推送节点',
                path: '/marketing/message/push-node/manage/add',
                component: './MessageCenter/Node/PushManage/add',
                hideInMenu: true,
            },
            {
                name: '查看推送节点',
                title: '查看推送节点',
                path: '/marketing/message/push-node/manage/detail',
                component: './MessageCenter/Node/PushManage/detail',
                hideInMenu: true,
            },
            {
                name: '订阅节点管理',
                title: '订阅节点管理',
                path: '/marketing/message/subscribe-node/manage',
                component: './MessageCenter/Node/SubscribeManage',
            },
            {
                name: '编辑订阅节点',
                title: '编辑订阅节点',
                path: '/marketing/message/subscribe-node/manage/edit',
                component: './MessageCenter/Node/SubscribeManage/add',
                hideInMenu: true,
            },
            {
                name: '新增订阅节点',
                title: '新增订阅节点',
                path: '/marketing/message/subscribe-node/manage/add',
                component: './MessageCenter/Node/SubscribeManage/add',
                hideInMenu: true,
            },
            {
                name: '查看订阅节点',
                title: '查看订阅节点',
                path: '/marketing/message/subscribe-node/manage/detail',
                component: './MessageCenter/Node/SubscribeManage/detail',
                hideInMenu: true,
            },
            {
                name: '消息场景管理',
                title: '消息场景管理',
                path: '/marketing/message/scene/manage',
                component: './MessageCenter/Scene/Manage',
            },
            {
                name: '新增消息场景',
                title: '新增消息场景',
                path: '/marketing/message/scene/manage/add',
                component: './MessageCenter/Scene/Manage/info',
                hideInMenu: true,
            },
            {
                name: '编辑消息场景',
                title: '编辑消息场景',
                path: '/marketing/message/scene/manage/edit',
                component: './MessageCenter/Scene/Manage/info',
                hideInMenu: true,
            },
        ],
    },
];
