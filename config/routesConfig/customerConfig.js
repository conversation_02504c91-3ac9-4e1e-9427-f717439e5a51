module.exports = [
    {
        name: '大客户运营',
        title: '大客户运营',
        path: '/sellerCenter/customer',
        icon: 'contacts',
        component: '../components/Layouts/FatherLayout',
        routes: [
            {
                path: '/sellerCenter/customer',
                redirect: '/sellerCenter/customer/manage',
            },

            /*************** 客户管理 ***************/

            {
                path: '/sellerCenter/customer/manage',
                redirect: '/sellerCenter/customer/manage/list',
            },
            {
                path: '/sellerCenter/customer/manage/list',
                name: '客户管理',
                title: '客户管理',
                component: './CustomerSystem/CustomerCenter/ManagePage',
            },
            {
                path: '/sellerCenter/customer/manage/list/add',
                title: '新增客户',
                name: '新增客户',
                hideInMenu: true,
                component: './CustomerSystem/CustomerCenter/UpdatePage',
                parentKeys: ['/sellerCenter/customer/manage/list'],
            },
            {
                path: '/sellerCenter/customer/manage/list/update',
                name: '编辑客户',
                title: '编辑客户',
                hideInMenu: true,
                component: './CustomerSystem/CustomerCenter/UpdatePage',
                parentKeys: ['/sellerCenter/customer/manage/list'],
            },

            /*************** 营销账户 ***************/
            {
                path: '/sellerCenter/customer/account',
                redirect: '/sellerCenter/customer/account/list',
            },
            {
                path: '/sellerCenter/customer/account/list',
                name: '营销账户',
                title: '营销账户',
                component: './CustomerSystem/AccountCenter/MarketingAccountPage',
            },

            /*************** 充电卡管理 ***************/

            {
                path: '/sellerCenter/customer/chargecard',
                redirect: '/sellerCenter/customer/chargecard/list',
            },
            {
                path: '/sellerCenter/customer/chargecard/list',
                name: '充电卡管理',
                title: '充电卡管理',
                component: './CustomerSystem/MarketingCenter/CardManagePage',
            },

            /*************** 优惠券管理 ***************/

            {
                path: '/sellerCenter/customer/coupon',
                redirect: '/sellerCenter/customer/coupon/list',
            },
            {
                path: '/sellerCenter/customer/coupon/list',
                name: '优惠券管理',
                title: '优惠券管理',
                component: './MarketingManage/CouponManage/CouponManageListPage',
            },

            {
                path: '/sellerCenter/customer/coupon/list/add',
                name: '新建券',
                title: '新建券',
                hideInMenu: true,
                component: './MarketingManage/CouponManage/CouponUpdatePage',
                parentKeys: ['/sellerCenter/customer/coupon/list'],
            },
            {
                path: '/sellerCenter/customer/coupon/list/update/:cpnId',
                name: '券详情',
                title: '券详情',
                hideInMenu: true,
                component: './MarketingManage/CouponManage/CouponUpdatePage',
                parentKeys: ['/sellerCenter/customer/coupon/list'],
            },
            {
                path: '/sellerCenter/customer/coupon/list/look/:cpnId',
                name: '券详情',
                title: '券详情',
                hideInMenu: true,
                component: './MarketingManage/CouponManage/CouponUpdatePage',
                parentKeys: ['/sellerCenter/customer/coupon/list'],
            },
            {
                path: '/sellerCenter/customer/coupon/list/copy/:cpnId',
                name: '复制券',
                title: '复制券',
                hideInMenu: true,
                component: './MarketingManage/CouponManage/CouponUpdatePage',
                parentKeys: ['/sellerCenter/customer/coupon/list'],
            },

            /*************** 账单管理 ***************/

            {
                path: '/sellerCenter/customer/finance',
                redirect: '/sellerCenter/customer/finance/bill',
            },
            {
                path: '/sellerCenter/customer/finance/bill',
                redirect: '/sellerCenter/customer/finance/bill/list',
            },
            {
                path: '/sellerCenter/customer/finance/bill/list',
                name: '账单管理',
                title: '账单管理',
                component: './CustomerSystem/FinanceManage/BillManagePage',
            },
            {
                path: '/sellerCenter/customer/finance/bill/list/detail/:id',
                name: '账单管理',
                title: '账单管理',
                hideInMenu: true,
                component: './CustomerSystem/FinanceManage/BillDetailPage',
                parentKeys: ['/sellerCenter/customer/finance/bill/list'],
            },
        ],
    },
];
