import enterpriseConfig from './enterpriseConfig';
module.exports = [
    {
        name: '用户中心',
        path: '/userCenter',
        title: '用户中心',
        icon: 'user',
        routes: [
            {
                path: '/userCenter',
                redirect: '/userCenter/userManage',
            },
            {
                path: '/userCenter/userManage',
                name: '用户管理',
                title: '用户管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    {
                        path: '/userCenter/userManage',
                        redirect: '/userCenter/userManage/manage',
                    },
                    {
                        path: '/userCenter/userManage/manage',
                        title: '用户管理',
                        name: '用户管理',
                        component: './UserManage/UserManage/UserManageListPage',
                    },
                    /*************** 用户视图 ***************/

                    {
                        path: '/userCenter/userManage/detail',
                        redirect: '/userCenter/userManage/detail/view',
                    },
                    {
                        // 为了重复点击菜单项可以实时刷新页面
                        path: '/userCenter/userManage/detail/view',
                        title: '用户视图',
                        name: '用户视图',
                        component: './UserManage/UserManage/UserManageDetailPage',
                    },
                    /*************** 用户解约 ***************/

                    {
                        path: '/userCenter/userManage/sign',
                        title: '用户解约',
                        name: '用户解约',
                        component: './OperationTools/Sign/SignListPage',
                    },
                    {
                        path: '/userCenter/userManage/doublecharge',
                        title: '多充管理',
                        name: '多充管理',
                        component: './UserManage/DoubleCharge/List',
                    },
                    {
                        path: '/userCenter/userManage/feedback',
                        title: '用户反馈',
                        name: '用户反馈',
                        component: './UserManage/Feedback/UserFeedbackListPage',
                    },
                    {
                        path: '/userCenter/userManage/evaluation',
                        title: '用户评价',
                        name: '用户评价',
                        component: './UserManage/Evaluation/List',
                    },
                    {
                        path: '/userCenter/userManage/entirement-card',
                        title: '企业卡审核',
                        name: '企业卡审核',
                        component: './UserManage/EntirementCard/EntirementCardListPage',
                    },
                    {
                        path: '/userCenter/userManage/complain',
                        title: '用户投诉',
                        name: '用户投诉',
                        component: './UserManage/Complain/List',
                    },
                    {
                        path: '/userCenter/userManage/complain/detail',
                        title: '投诉详情',
                        name: '投诉详情',
                        component: './UserManage/Complain/List/detail',
                        hideInMenu: true,
                    },
                    {
                        path: '/userCenter/userManage/complain/analysis',
                        title: '投诉分析',
                        name: '投诉分析',
                        component: './UserManage/Complain/List/analysis',
                        hideInMenu: true,
                    },
                ],
            },
            {
                path: '/userCenter/membership',
                name: '会员管理',
                title: '会员管理',
                component: '../components/Layouts/FatherLayout',
                routes: [
                    /*************** 会员查询 ***************/
                    {
                        path: '/userCenter/membership',
                        redirect: '/userCenter/membership/manager',
                    },

                    {
                        path: '/userCenter/membership/manager',
                        redirect: '/userCenter/membership/manager/list',
                    },
                    {
                        path: '/userCenter/membership/manager/list',
                        name: '会员查询',
                        title: '会员查询',
                        component: './MarketingManage/Membership/MembershipList',
                    },
                    {
                        path: '/userCenter/membership/manager/list/detail/:custId/:actId/:buyNo/:actSubType/:custVipId',
                        name: '会员详情',
                        title: '会员详情',
                        component: './MarketingManage/Membership/MembershipDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/manager/list'],
                    },

                    /*************** 等级会员 ***************/

                    {
                        path: '/userCenter/membership/vip-level',
                        redirect: '/userCenter/membership/vip-level/list',
                    },
                    {
                        path: '/userCenter/membership/vip-level/list',
                        name: '等级会员方案',
                        title: '等级会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipLevelList',
                    },
                    {
                        path: '/userCenter/membership/vip-level/list/edit/:id',
                        name: '编辑等级会员方案',
                        title: '编辑等级会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/vip-level/list'],
                    },
                    {
                        path: '/userCenter/membership/vip-level/list/detail/:id',
                        name: '等级会员方案详情',
                        title: '等级会员方案详情',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/vip-level/list'],
                    },

                    /*************** 会员方案 ***************/

                    {
                        path: '/userCenter/membership/plan',
                        redirect: '/userCenter/membership/plan/list',
                    },
                    {
                        path: '/userCenter/membership/plan/list',
                        name: '会员方案',
                        title: '会员方案',
                        component: './MarketingManage/Membership/MembershipPlan/MembershipPlanList',
                    },
                    // 1.0
                    {
                        path: '/userCenter/membership/plan/list/add',
                        name: '新建会员方案',
                        title: '新建会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage1_0',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/update/:id',
                        name: '会员方案详情',
                        title: '会员方案详情',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage1_0',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/detail/:id',
                        name: '会员方案详情',
                        title: '会员方案详情',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage1_0',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/copy/:id',
                        name: '复制会员方案',
                        title: '复制会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage1_0',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    // 2.0
                    {
                        path: '/userCenter/membership/plan/list/add-vip',
                        name: '新建会员方案',
                        title: '新建会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/update-vip/:id',
                        name: '会员方案详情',
                        title: '会员方案详情',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/detail-vip/:id',
                        name: '会员方案详情',
                        title: '会员方案详情',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },
                    {
                        path: '/userCenter/membership/plan/list/copy-vip/:id',
                        name: '复制会员方案',
                        title: '复制会员方案',
                        component:
                            './MarketingManage/Membership/MembershipPlan/MembershipPlanEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/plan/list'],
                    },

                    /*************** 会员权益 ***************/
                    {
                        path: '/userCenter/membership/rights',
                        redirect: '/userCenter/membership/rights/list',
                    },
                    {
                        path: '/userCenter/membership/rights/list',
                        name: '会员权益',
                        title: '会员权益',
                        component:
                            './MarketingManage/Membership/MembershipRights/MembershipRightsListPage',
                    },
                    {
                        path: '/userCenter/membership/rights/list/add',
                        name: '新建会员权益',
                        title: '新建会员权益',
                        component:
                            './MarketingManage/Membership/MembershipRights/MembershipRightsEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/update/:equityId/:equityNo',
                        name: '会员权益编辑',
                        title: '会员权益编辑',
                        component:
                            './MarketingManage/Membership/MembershipRights/MembershipRightsEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/detail/:equityId/:equityNo',
                        name: '会员权益详情',
                        title: '会员权益详情',
                        component:
                            './MarketingManage/Membership/MembershipRights/MembershipRightsEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/coupon-bags-list',
                        name: '券包列表',
                        title: '券包列表',
                        component: './MarketingManage/Membership/MembershipRights/CouponBagsList',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/coupon-bags-list/add',
                        name: '新建券包',
                        title: '新建券包',
                        component:
                            './MarketingManage/Membership/MembershipRights/CouponBagsUpdatePage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/coupon-bags-list/detail/:couponBagId',
                        name: '券包详情',
                        title: '券包详情',
                        component:
                            './MarketingManage/Membership/MembershipRights/CouponBagsUpdatePage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/coupon-bags-list/update/:couponBagId',
                        name: '编辑券包',
                        title: '编辑券包',
                        component:
                            './MarketingManage/Membership/MembershipRights/CouponBagsUpdatePage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },
                    {
                        path: '/userCenter/membership/rights/list/send-list',
                        name: '发放记录',
                        title: '发放记录',
                        component:
                            './MarketingManage/Membership/MembershipRights/RightSendRecordList',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/rights/list'],
                    },

                    /*************** 会员推广配置 ***************/

                    {
                        path: '/userCenter/membership/group',
                        name: '会员推广配置',
                        title: '会员推广配置',
                        component: './MarketingManage/Membership/MembershipGroupPage',
                    },
                    {
                        path: '/userCenter/membership/record',
                        name: '会员开卡记录',
                        title: '会员开卡记录',
                        component: './MarketingManage/Membership/MembershipRecordListPage',
                    },

                    /*************** 会员方案投放管理 ***************/

                    {
                        path: '/userCenter/membership/put',
                        name: '会员方案投放管理',
                        title: '会员方案投放管理',
                        component: './MarketingManage/Membership/MembershipPlanPutListPage',
                    },
                    {
                        path: '/userCenter/membership/put/add',
                        name: '新增方案投放管理',
                        title: '新增方案投放管理',
                        component: './MarketingManage/Membership/MembershipPlanPutEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/put'],
                    },
                    {
                        path: '/userCenter/membership/put/edit',
                        name: '编辑方案投放管理',
                        title: '编辑方案投放管理',
                        component: './MarketingManage/Membership/MembershipPlanPutEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/put'],
                    },
                    {
                        path: '/userCenter/membership/put/copy',
                        name: '复制方案投放管理',
                        title: '复制方案投放管理',
                        component: './MarketingManage/Membership/MembershipPlanPutEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/put'],
                    },
                    {
                        path: '/userCenter/membership/put/detail',
                        name: '查看方案投放管理',
                        title: '查看方案投放管理',
                        component: './MarketingManage/Membership/MembershipPlanPutEditPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/put'],
                    },
                    /*************** 会员策略 ***************/

                    {
                        path: '/userCenter/membership/strategy',
                        redirect: '/userCenter/membership/strategy/price-list',
                    },

                    {
                        path: '/userCenter/membership/strategy/price-list',
                        name: '会员策略',
                        title: '会员策略',
                        component: './MarketingManage/Membership/MembershipPriceStrategyList',
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/detail/add',
                        name: '新增会员策略',
                        title: '新增会员策略',
                        component: './MarketingManage/Membership/MembershipPriceStrategyDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/detail/look/:vipPricePolicyId',
                        name: '查看会员策略',
                        title: '查看会员策略',
                        component: './MarketingManage/Membership/MembershipPriceStrategyDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/detail/update/:vipPricePolicyId',
                        name: '编辑会员策略',
                        title: '编辑会员策略',
                        component: './MarketingManage/Membership/MembershipPriceStrategyDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/detail/copy/:vipPricePolicyId',
                        name: '复制会员策略',
                        title: '复制会员策略',
                        component: './MarketingManage/Membership/MembershipPriceStrategyDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/ad-list',
                        name: '线上策略投放',
                        title: '线上策略投放',
                        component: './MarketingManage/Membership/MembershipPriceStrategyAdListPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/ad-list/add',
                        name: '新增投放策略',
                        title: '新增投放策略',
                        component:
                            './MarketingManage/Membership/MembershipPriceStrategyAdDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/ad-list/detail/:id',
                        name: '编辑投放策略',
                        title: '编辑投放策略',
                        component:
                            './MarketingManage/Membership/MembershipPriceStrategyAdDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                    {
                        path: '/userCenter/membership/strategy/price-list/ad-list/copy/:id',
                        name: '复制投放策略',
                        title: '复制投放策略',
                        component:
                            './MarketingManage/Membership/MembershipPriceStrategyAdDetailPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },

                    {
                        path: '/userCenter/membership/strategy/price-list/popup',
                        name: '配置策略弹框',
                        title: '配置策略弹框',
                        component: './MarketingManage/Membership/MembershipPriceStrategyPopupPage',
                        hideInMenu: true,
                        parentKeys: ['/userCenter/membership/strategy/price-list'],
                    },
                ],
            },
            ...enterpriseConfig,

            {
                path: '/userCenter/crowd-manage/list',
                name: '人群画像管理',
                title: '人群画像管理',
                component: './UserManage/CrowdManage/CrowdManageListPage',
            },
            {
                path: '/userCenter/crowd-manage/detail/:crowdPortraitId/:crowdType',
                name: '编辑人群画像',
                title: '编辑人群画像',
                component: './UserManage/CrowdManage/CrowdDetailsPage',
                hideInMenu: true,
                parentKeys: ['/userCenter/crowd-manage/list'],
            },
            {
                path: '/userCenter/crowd-manage/look/:crowdPortraitId/:crowdType',
                name: '查看人群画像',
                title: '查看人群画像',
                component: './UserManage/CrowdManage/CrowdDetailsPage',
                hideInMenu: true,
                parentKeys: ['/userCenter/crowd-manage/list'],
            },
            {
                path: '/userCenter/crowd-manage/calculate/add',
                name: '添加人群计算',
                title: '添加人群计算',
                component: './UserManage/CrowdManage/CrowdCalculate/CrowdCalculatePage',
                hideInMenu: true,
                parentKeys: ['/userCenter/crowd-manage/list'],
            },
            {
                path: '/userCenter/crowd-manage/calculate/edit/:crowdId',
                name: '编辑人群计算规则',
                title: '编辑人群计算规则',
                component: './UserManage/CrowdManage/CrowdCalculate/CrowdCalculatePage',
                hideInMenu: true,
                parentKeys: ['/userCenter/crowd-manage/list'],
            },
            {
                path: '/userCenter/crowd-manage/calculate/detail/:crowdId',
                name: '查看人群计算规则',
                title: '查看人群计算规则',
                component: './UserManage/CrowdManage/CrowdCalculate/CrowdCalculatePage',
                hideInMenu: true,
                parentKeys: ['/userCenter/crowd-manage/list'],
            },
            {
                path: '/userCenter/blacklist',
                name: '商家黑名单管理',
                title: '商家黑名单管理',
                component: './UserManage/Blacklist/List',
            },
            {
                path: '/userCenter/city-keeper/list',
                name: '城市责任田',
                title: '城市责任田',
                component: './UserManage/CityKeeper/CityKeeperList',
            },
        ],
    },
];
