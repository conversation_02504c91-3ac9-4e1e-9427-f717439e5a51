import datacenterConfig from './routesConfig/datacenter';
import financeConfig from './routesConfig/financeConfig';
import orderConfig from './routesConfig/orderConfig';
import marketingConfig from './routesConfig/marketingConfig';
import userConfig from './routesConfig/userConfig';
import sellerCenter from './routesConfig/sellerCenter';
import AssetConfig from './routesConfig/AssetConfig';
import BasicConfig from './routesConfig/basicConfig';
import partTimeConfig from './routesConfig/partTimeConfig';
import PriceConfig from './routesConfig/priceConfig';
import eventcenter from './routesConfig/eventcenter';
import auditCenter from './routesConfig/auditCenter';
import merchatnConfig from './routesConfig/merchatnConfig';
import monitoring from './routesConfig/monitoring';
module.exports = [
    {
        path: '/bi-iframe/:pageCode',
        name: '报表统计',
        component: './BiIframe',
    },
    {
        path: '/screen/xdt',
        name: '大屏',
        title: '大屏',
        component: './Screen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/newscreen/xdt',
        name: '新大屏',
        title: '新大屏',
        component: './newScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/newscreen/newxdt',
        name: '新大屏',
        title: '新大屏',
        component: './newScreenXdt/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/xdt',
        name: '新电途大屏',
        title: '新电途大屏',
        component: './LargeScreen/XdtScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/newxdt',
        name: '新电途大屏',
        title: '新电途大屏',
        component: './LargeScreen/newXdtScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/wuxixdt',
        name: '新电途大屏',
        title: '新电途大屏',
        component: './LargeScreen/WuxiXdtScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/wuxixdtlong',
        name: '新电途大屏',
        title: '新电途大屏',
        component: './LargeScreen/WuxiXdtLongScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/wuxixdtlongtwo',
        name: '新电途大屏',
        title: '新电途大屏',
        component: './LargeScreen/WuxiXdtLongScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    {
        path: '/largescreen/store',
        name: '储能大屏',
        title: '储能大屏',
        component: './LargeScreen/StoreScreen/Screen.js',
        icon: 'FundProjectionScreenOutlined',
        layout: false,
        hideInMenu: true,
    },
    ...monitoring,
    ...sellerCenter,
    ...userConfig,
    ...marketingConfig,
    ...orderConfig,
    ...financeConfig,
    ...datacenterConfig,
    ...partTimeConfig,
    ...eventcenter,
    ...auditCenter,
    {
        name: '互联互通',
        title: '互联互通',
        icon: 'deployment-unit',
        path: '/interflowmanage',
        routes: [
            {
                path: '/interflowmanage',
                redirect: '/interflowmanage/audit/list',
            },
            {
                path: '/interflowmanage/audit/list',
                name: '新站审核',
                title: '新站审核',
                component: './InterflowManage/Audit/ListPage.js',
            },
            {
                path: '/interflowmanage/abnormaldata/list',
                name: '异常数据',
                title: '异常数据',
                component: './InterflowManage/Abnormaldata/AbnormaldataList',
            },
        ],
    },
    ...AssetConfig,
    ...BasicConfig,
    ...PriceConfig,
    ...merchatnConfig,
    {
        name: '客服中心',
        title: '客服中心',
        path: '/service-center',
        routes: [
            {
                path: '/service-center',
                redirect: '/service-center/qa',
            },
            {
                path: '/service-center/qa',
                name: '常见问题',
                title: '常见问题',
                component: './ServicerCenter/QA/QAListPage',
            },
            {
                path: '/service-center/qa/add',
                name: '常见问题新增',
                title: '常见问题新增',
                component: './ServicerCenter/QA/QAEditPage',
                parentKeys: ['/service-center/qa'],
                hideInMenu: true,
            },
            {
                path: '/service-center/qa/edit',
                name: '常见问题编辑',
                title: '常见问题编辑',
                component: './ServicerCenter/QA/QAEditPage',
                parentKeys: ['/service-center/qa'],
                hideInMenu: true,
            },
        ],
    },
    {
        path: '/',
        hideInMenu: true,
        component: './HomeLoading',
        exact: true,
    },
    {
        component: './404',
    },
];
