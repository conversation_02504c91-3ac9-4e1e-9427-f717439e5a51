import { useRequest } from 'ahooks';
import { Button, Form, Input, Row, Select, Space, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { history, connect } from 'umi';

import { ProtocolStatusEnum } from '@/constants/protocol';
import { addProtocol } from '@/services/BasicManagement/ProtocolApi';
import QuillRichInput from '@/components/QuillRichInput/index';

const ProtocolForm: React.FC<{
    type: 'ADD' | 'EDIT';
    initialVales?: any;
    dispatch?: any;
    global?: { codeInfo: Record<string, any> };
}> = ({ type, initialVales, dispatch, global }) => {
    const [form] = Form.useForm();
    const [disabled, setDisabled] = useState<boolean>(false);
    const richTextRef = useRef<{ getRichTextLength: () => number }>();

    useEffect(() => {
        dispatch({
            type: 'global/initCode',
            code: 'agreementInfoType',
        });
    }, []);

    useEffect(() => {
        if (type === 'EDIT') {
            if (
                initialVales?.infoStatus === ProtocolStatusEnum.ENABLED ||
                initialVales?.infoStatus === ProtocolStatusEnum.DISABLED
            ) {
                setDisabled(true);
            } else {
                setDisabled(false);
            }
            form.setFieldsValue({ ...initialVales });
            dispatch({
                type: 'global/initSecondTypeCode',
                firstType: 'agreementInfoType',
                secondType: initialVales?.infoType,
            });
        } else {
            setDisabled(false);
        }
    }, [type, initialVales]);

    const { run, loading } = useRequest(
        (params: API.AgreementInfoQueryVo) => {
            return addProtocol(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('保存协议成功');
                    history.replace('/basic/protocol');
                } else {
                    message.error(res?.msg || '保存协议失败');
                }
            },
            onError: () => {
                message.error('保存协议失败');
            },
        },
    );

    const submit = (formData: any) => {
        const params = { ...formData, title: formData?.title?.trim() };
        if (type === 'EDIT') {
            params.id = initialVales?.id;
        }
        run(params);
    };

    const clearSubType = () => {
        form.setFieldsValue({ infoSubType: '' });
    };

    const infoType = Form.useWatch('infoType', form);

    useEffect(() => {
        if (infoType) {
            dispatch({
                type: 'global/initSecondTypeCode',
                firstType: 'agreementInfoType',
                secondType: infoType,
            });
        }
    }, [infoType]);

    return (
        <Form form={form} onFinish={submit} wrapperCol={{ span: 10 }}>
            <Form.Item
                label="协议类目"
                name="infoType"
                required
                rules={[{ required: true, message: '请选择协议类目' }]}
            >
                <Select
                    options={global?.codeInfo?.agreementInfoType}
                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                    disabled={disabled}
                    onChange={clearSubType}
                />
            </Form.Item>
            <Form.Item
                label="协议类型"
                name="infoSubType"
                required
                rules={[{ required: true, message: '请选择协议类型' }]}
            >
                <Select
                    options={global?.codeInfo[`agreementInfoType/${infoType}`]}
                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                    showSearch
                    disabled={disabled}
                />
            </Form.Item>
            <Form.Item
                label="协议名称"
                name="title"
                required
                rules={[{ required: true, message: '请输入协议名称', transform: (v) => v?.trim() }]}
            >
                <Input
                    placeholder="请输入协议名称，限15个字以内"
                    showCount
                    allowClear
                    disabled={disabled}
                    maxLength={15}
                />
            </Form.Item>
            <QuillRichInput
                label="正文内容"
                required
                ref={richTextRef}
                rules={[
                    { required: true, message: '请输入正文内容' },
                    () => ({
                        validator(rule: any, value: string) {
                            if (value == '<p><br></p>') {
                                return Promise.reject('请输入正文内容');
                            }
                            const markLength = richTextRef?.current?.getRichTextLength() || 0;

                            if (value && markLength - 1 > 50000) {
                                return Promise.reject('限50000个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
                name="content"
                initialValue={''}
                wrapperCol={{ span: 20 }}
                placeholder="请填写"
            />
            <Form.Item wrapperCol={{ span: 20 }}>
                <Row justify="center" style={{ marginTop: '32px' }}>
                    <Space size="large">
                        <Button type="primary" htmlType="submit" loading={loading}>
                            提交
                        </Button>
                        <Button
                            htmlType="reset"
                            onClick={() => {
                                history.replace('/basic/protocol');
                            }}
                        >
                            取消
                        </Button>
                    </Space>
                </Row>
            </Form.Item>
        </Form>
    );
};

export default connect(({ global }: any) => ({ global }))(ProtocolForm);
