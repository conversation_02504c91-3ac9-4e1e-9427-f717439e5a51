import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { Button, Card, Descriptions, Space, Tag, Typography, message } from 'antd';
import React, { useEffect } from 'react';
import { useParams, history, Link } from 'umi';

import { getProtocolDetail } from '@/services/BasicManagement/ProtocolApi';

const ProtocolDetailPage = () => {
    const params: { id: string } = useParams();

    const id = params?.id;

    const { loading, data, run } = useRequest(
        (pid) => {
            return getProtocolDetail(pid);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret !== 200) {
                    message.error(res?.msg || '查询协议失败');
                }
            },
            onError: () => {
                message.error('查询协议失败');
            },
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>查看协议</Typography.Title>
                </Space>
            }
            loading={loading}
        >
            <Card>
                <Descriptions layout="horizontal" column={3}>
                    <Descriptions.Item label="协议名称" span={3}>
                        {data?.data?.title}
                    </Descriptions.Item>
                    <Descriptions.Item label="协议类目">
                        {data?.data?.infoTypeName}
                    </Descriptions.Item>
                    <Descriptions.Item label="协议类型">
                        {data?.data?.infoSubTypeName}
                    </Descriptions.Item>
                    <Descriptions.Item label="协议状态">
                        {data?.data?.infoStatusName}
                    </Descriptions.Item>
                    <Descriptions.Item label="协议版本">
                        <Space>
                            {data?.data?.version}
                            {data?.data?.isFirst === '1' ? <Tag color="success">当前版本</Tag> : ''}
                        </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="生效日期">
                        {data?.data?.effTime || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="失效日期">
                        {data?.data?.expTime || '-'}
                    </Descriptions.Item>
                </Descriptions>
                <Card
                    title="正文内容"
                    extra={
                        data?.data?.isFirst === '1' ? (
                            <Link to={`/basic/protocol/edit/${id}`}>
                                <Button type="primary">编辑</Button>
                            </Link>
                        ) : (
                            ''
                        )
                    }
                >
                    <div
                        dangerouslySetInnerHTML={{ __html: data?.data?.content as string }}
                        style={{
                            wordBreak: 'break-all',
                            whiteSpace: 'pre-wrap',
                            wordWrap: 'break-word',
                        }}
                    ></div>
                </Card>
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(ProtocolDetailPage);
