import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card } from 'antd';
import React from 'react';

import ProtocolForm from '../components/ProtocolForm';

const ProtocolAddPage = () => {
    return (
        <PageHeaderWrapper>
            <Card>
                <ProtocolForm type="ADD" />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(ProtocolAddPage);
