import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { Card, message } from 'antd';
import React, { useEffect } from 'react';
import { useParams } from 'umi';

import ProtocolForm from '../components/ProtocolForm';
import { getProtocolDetail } from '@/services/BasicManagement/ProtocolApi';

const ProtocolEditPage = () => {
    const params: { id: string } = useParams();

    const id = params?.id;

    const { loading, data, run } = useRequest(
        (pid) => {
            return getProtocolDetail(pid);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret !== 200) {
                    message.error(res?.msg || '查询协议失败');
                }
            },
            onError: () => {
                message.error('查询协议失败');
            },
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    return (
        <PageHeaderWrapper loading={loading}>
            <Card>
                <ProtocolForm type="EDIT" initialVales={data?.data} />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(ProtocolEditPage);
