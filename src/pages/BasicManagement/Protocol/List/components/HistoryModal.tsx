import { usePagination, useRequest } from 'ahooks';
import { Modal, Space, Spin, Tag, Typography, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { Link } from 'umi';

import TablePro from '@/components/TablePro';
import { getProtocolDetail, queryProtocolList } from '@/services/BasicManagement/ProtocolApi';

const HistoryModal: React.FC<{
    visible: boolean;
    onCancel: () => void;
    protocolVo?: API.AgreementInfoQueryVo;
}> = ({ visible, onCancel, protocolVo }) => {
    const [showPreview, setShowPreview] = useState<boolean>(false);
    const [previewVersion, setPreviewVersion] = useState<API.AgreementInfoQueryVo>();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryProtocolList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        if (visible) {
            searchList({ current: 1, pageSize: pagination.pageSize }, { id: protocolVo?.id });
        }
    }, [visible]);
    const previewProtocol = (protocol: API.AgreementInfoQueryVo) => {
        setPreviewVersion(protocol);
        setShowPreview(true);
    };
    const { loading, data, run } = useRequest(
        (pid) => {
            return getProtocolDetail(pid);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret !== 200) {
                    message.error(res?.msg || '查询协议失败');
                }
            },
            onError: () => {
                message.error('查询协议失败');
            },
        },
    );

    useEffect(() => {
        if (showPreview) {
            run(previewVersion?.id);
        }
    }, [showPreview, previewVersion]);

    const columns: ColumnsType<any> = [
        {
            title: '版本',
            width: 120,
            dataIndex: 'version',
            render: (value: string, record: API.AgreementInfoQueryVo) => {
                return (
                    <Space>
                        <Typography.Text>{value}</Typography.Text>
                        {record?.isFirst === '1' ? <Tag color="success">当前版本</Tag> : ''}
                    </Space>
                );
            },
        },
        {
            title: '生效日期',
            width: 120,
            dataIndex: 'effTime',
        },
        {
            title: '失效日期',
            width: 120,
            dataIndex: 'expTime',
        },
        {
            title: '操作',
            width: 80,
            dataIndex: 'operation',
            fixed: 'right',
            sorter: false,
            render: (_, record) => {
                return (
                    <Space>
                        <Link to={`/basic/protocol/detail/${record?.id}`}>查看</Link>
                        {record?.isFirst === '1' ? (
                            <Typography.Link onClick={() => previewProtocol(record)}>
                                预览
                            </Typography.Link>
                        ) : (
                            ''
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <>
            <Modal
                visible={visible}
                onCancel={onCancel}
                destroyOnClose
                title="历史版本"
                width={940}
                closeIcon={false}
                footer={false}
            >
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
            </Modal>
            <Modal
                visible={showPreview}
                title="预览协议"
                width={375}
                onCancel={() => {
                    setShowPreview(false);
                }}
                destroyOnClose
                footer={null}
            >
                <Spin spinning={loading}>
                    <div
                        style={{ height: '500px', overflowY: 'auto' }}
                        dangerouslySetInnerHTML={{ __html: data?.data?.content as string }}
                    ></div>
                </Spin>
            </Modal>
        </>
    );
};

export default React.memo(HistoryModal);
