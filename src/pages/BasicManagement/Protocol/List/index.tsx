import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Popconfirm,
    Select,
    Space,
    Tabs,
    Typography,
    message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, connect } from 'umi';

import styles from '@/assets/styles/common.less';
import { ProtocolStatusEnum, ProtocolStatusTabOptions } from '@/constants/protocol';
import {
    changeProtocolStatus,
    deleteProtocol,
    queryProtocolList,
} from '@/services/BasicManagement/ProtocolApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import HistoryModal from './components/HistoryModal';

const ProtocolPage: React.FC<{
    dispatch?: any;
    global?: { codeInfo: Record<string, any> };
}> = ({ dispatch, global }) => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string>(ProtocolStatusTabOptions[0].key);
    const [showHistory, setShowHistory] = useState<boolean>(false);
    const [showHistoryProtocol, setShowHistoryProtocol] = useState<API.AgreementInfoQueryVo>();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryProtocolList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: deleteRequest, loading: deleteLoading } = useRequest(
        (id) => {
            return deleteProtocol(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('删除成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
            onError: () => {
                message.error('删除失败');
            },
        },
    );

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (id, status) => {
            return changeProtocolStatus(id, status);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData, isFirst: '1' };
        if (currentTab && currentTab !== ProtocolStatusTabOptions[0].key) {
            params.infoStatus = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    useEffect(() => {
        dispatch({
            type: 'global/initAllSecondTypeCode',
            firstType: 'agreementInfoType',
        });
        searchList({ current: 1, pageSize: pagination.pageSize });
    }, []);

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const refreshList = () => {
        searchList(pagination, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const confirmDelete = (id: number) => {
        deleteRequest(id);
    };

    const confirmChangeStatus = (id: number, status: string) => {
        changeStatusRequest(id, status);
    };

    const onModalCancel = () => {
        setShowHistory(false);
        setShowHistoryProtocol(undefined);
    };

    const columns: ColumnsType<API.AgreementInfoQueryVo> = [
        {
            title: '协议类目',
            width: 120,
            dataIndex: 'infoTypeName',
        },
        {
            title: '协议类型',
            width: 120,
            dataIndex: 'infoSubTypeName',
        },
        {
            title: '协议编号',
            width: 120,
            dataIndex: 'infoSubType',
        },
        {
            title: '协议名称',
            width: 180,
            dataIndex: 'title',
        },
        {
            title: '当前版本',
            width: 120,
            dataIndex: 'version',
        },
        {
            title: '启用日期',
            width: 120,
            dataIndex: 'effTime',
        },
        {
            title: '禁用日期',
            width: 120,
            dataIndex: 'stopTime',
        },
        {
            title: '最近更新',
            width: 120,
            dataIndex: 'updateTime',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'infoStatusName',
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'id',
            sorter: false,
            fixed: 'right',
            render: (id, record) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                setShowHistory(true);
                                setShowHistoryProtocol(record);
                            }}
                        >
                            历史版本
                        </Typography.Link>
                        {record?.infoStatus === ProtocolStatusEnum.ENABLED && (
                            <Popconfirm
                                title="确认要禁用该协议吗？"
                                onConfirm={() =>
                                    confirmChangeStatus(id, ProtocolStatusEnum.DISABLED)
                                }
                                okButtonProps={{ loading: changeStatusLoading }}
                            >
                                <Typography.Link type="warning">禁用</Typography.Link>
                            </Popconfirm>
                        )}
                        <Link to={`/basic/protocol/edit/${id}`}>编辑</Link>
                        {record?.infoStatus === ProtocolStatusEnum.INIT && (
                            <Popconfirm
                                title="确认要启用该协议吗？"
                                onConfirm={() =>
                                    confirmChangeStatus(id, ProtocolStatusEnum.ENABLED)
                                }
                                okButtonProps={{ loading: changeStatusLoading }}
                            >
                                <Typography.Link type="success">启用</Typography.Link>
                            </Popconfirm>
                        )}
                        {record?.infoStatus === ProtocolStatusEnum.INIT && (
                            <Popconfirm
                                title="确认要删除该协议吗？"
                                onConfirm={() => confirmDelete(id)}
                                okButtonProps={{ loading: deleteLoading }}
                            >
                                <Typography.Link type="danger">删除</Typography.Link>
                            </Popconfirm>
                        )}
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                        <Col span={8}>
                            <Form.Item label="协议名称" name="title">
                                <Input maxLength={15} allowClear placeholder="请填写" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="协议类型" name="infoSubType">
                                <Select
                                    options={global?.codeInfo?.['agreementInfoType/ALL_CHILDREN']}
                                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    allowClear
                                    placeholder="请选择"
                                />
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Link to="/basic/protocol/add">
                            <Button type="primary">新增</Button>
                        </Link>
                    </Space>
                </div>
                <Tabs defaultActiveKey={ProtocolStatusTabOptions[0].key} onChange={changeTabType}>
                    {ProtocolStatusTabOptions.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
            <HistoryModal
                visible={showHistory}
                onCancel={onModalCancel}
                protocolVo={showHistoryProtocol}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global }) => ({ global }))(ProtocolPage);
