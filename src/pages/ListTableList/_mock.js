import { parse } from 'url';

// mock tableListDataSource
const genList = (current, pageSize) => {
    const tableListDataSource = [];

    for (let i = 0; i < pageSize; i += 1) {
        const index = (current - 1) * 10 + i;
        tableListDataSource.push({
            key: index,
            disabled: i % 6 === 0,
            href: 'https://ant.design',
            avatar: [
                'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
                'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
            ][i % 2],
            name: `TradeCode ${index}`,
            owner: '曲丽丽',
            desc: '这是一段描述',
            callNo: Math.floor(Math.random() * 1000),
            status: Math.floor(Math.random() * 10) % 4,
            updatedAt: new Date(),
            createdAt: new Date(),
            progress: Math.ceil(Math.random() * 100),
        });
    }

    tableListDataSource.reverse();
    return tableListDataSource;
};

let tableListDataSource = genList(1, 100);

function getRule(req, res, u) {
    let url = u;

    if (!url || Object.prototype.toString.call(url) !== '[object String]') {
        // eslint-disable-next-line prefer-destructuring
        url = req.url;
    }

    const { current = 1, pageSize = 10 } = req.query;
    const params = parse(url, true).query;
    let dataSource = [...tableListDataSource].slice((current - 1) * pageSize, current * pageSize);

    if (params.sorter) {
        const s = params.sorter.split('_');
        dataSource = dataSource.sort((prev, next) => {
            if (s[1] === 'descend') {
                return next[s[0]] - prev[s[0]];
            }

            return prev[s[0]] - next[s[0]];
        });
    }

    if (params.status) {
        const status = params.status.split(',');
        let filterDataSource = [];
        status.forEach((s) => {
            filterDataSource = filterDataSource.concat(
                dataSource.filter((item) => {
                    if (parseInt(`${item.status}`, 10) === parseInt(s.split('')[0], 10)) {
                        return true;
                    }

                    return false;
                }),
            );
        });
        dataSource = filterDataSource;
    }

    if (params.name) {
        dataSource = dataSource.filter((data) => data.name.includes(params.name || ''));
    }

    const result = {
        data: dataSource,
        total: tableListDataSource.length,
        success: true,
        pageSize,
        current: parseInt(`${params.currentPage}`, 10) || 1,
    };
    return res.json(result);
}

function postRule(req, res, u, b) {
    let url = u;

    if (!url || Object.prototype.toString.call(url) !== '[object String]') {
        // eslint-disable-next-line prefer-destructuring
        url = req.url;
    }

    const body = (b && b.body) || req.body;
    const { method, name, desc, key } = body;

    switch (method) {
        /* eslint no-case-declarations:0 */
        case 'delete':
            tableListDataSource = tableListDataSource.filter(
                (item) => key.indexOf(item.key) === -1,
            );
            break;

        case 'post':
            (() => {
                const i = Math.ceil(Math.random() * 10000);
                const newRule = {
                    key: tableListDataSource.length,
                    href: 'https://ant.design',
                    avatar: [
                        'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
                        'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
                    ][i % 2],
                    name,
                    owner: '曲丽丽',
                    desc,
                    callNo: Math.floor(Math.random() * 1000),
                    status: Math.floor(Math.random() * 10) % 2,
                    updatedAt: new Date(),
                    createdAt: new Date(),
                    progress: Math.ceil(Math.random() * 100),
                };
                tableListDataSource.unshift(newRule);
                return res.json(newRule);
            })();

            return;

        case 'update':
            (() => {
                let newRule = {};
                tableListDataSource = tableListDataSource.map((item) => {
                    if (item.key === key) {
                        newRule = { ...item, desc, name };
                        return { ...item, desc, name };
                    }

                    return item;
                });
                return res.json(newRule);
            })();

            return;

        default:
            break;
    }

    const result = {
        list: tableListDataSource,
        pagination: {
            total: tableListDataSource.length,
        },
    };
    res.json(result);
}

export default {
    'GET /api/rule': getRule,
    'POST /api/rule': postRule,
};
