import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Dropdown, Menu, message } from 'antd';
import React, { Fragment, useState, useRef } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { queryRule, updateRule, addRule, removeRule } from './service';
import { formatTableColumns } from '@/utils/utils';
/**
 * 添加节点
 * @param fields
 */

const handleAdd = async (fields) => {
    const hide = message.loading('正在添加');

    try {
        await addRule({ ...fields });
        hide();
        message.success('添加成功');
        return true;
    } catch (error) {
        hide();
        message.error('添加失败请重试！');
        return false;
    }
};
/**
 * 更新节点
 * @param fields
 */

const handleUpdate = async (fields) => {
    const hide = message.loading('正在配置');

    try {
        await updateRule({
            name: fields.name,
            desc: fields.desc,
            key: fields.key,
        });
        hide();
        message.success('配置成功');
        return true;
    } catch (error) {
        hide();
        message.error('配置失败请重试！');
        return false;
    }
};
/**
 *  删除节点
 * @param selectedRows
 */

const handleRemove = async (selectedRows) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;

    try {
        await removeRule({
            key: selectedRows.map((row) => row.key),
        });
        hide();
        message.success('删除成功，即将刷新');
        return true;
    } catch (error) {
        hide();
        message.error('删除失败，请重试');
        return false;
    }
};

const TableList = () => {
    const [sorter, setSorter] = useState('');
    const [createModalVisible, handleModalVisible] = useState(false);
    const [updateModalVisible, handleUpdateModalVisible] = useState(false);
    const [stepFormValues, setStepFormValues] = useState({});
    const actionRef = useRef();
    const columns = formatTableColumns([
        {
            title: '规则名称',
            dataIndex: 'name',
            rules: [
                {
                    required: true,
                    message: '规则名称为必填项',
                },
            ],
        },
        {
            title: '描述',
            dataIndex: 'desc',
            valueType: 'textarea',
        },
        {
            title: '服务调用次数',
            dataIndex: 'callNo',
            sorter: true,
            hideInForm: true,
            renderText: (val) => `${val} 万`,
        },
        {
            title: '状态',
            dataIndex: 'status',
            hideInForm: true,
            valueEnum: {
                0: {
                    text: '关闭',
                    status: 'Default',
                },
                1: {
                    text: '运行中',
                    status: 'Processing',
                },
                2: {
                    text: '已上线',
                    status: 'Success',
                },
                3: {
                    text: '异常',
                    status: 'Error',
                },
            },
        },
        {
            title: '上次调度时间',
            dataIndex: 'updatedAt',
            sorter: true,
            valueType: 'dateTime',
            hideInForm: true,
        },
        {
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
            render: (_, record) => (
                <>
                    <a
                        onClick={() => {
                            handleUpdateModalVisible(true);
                            setStepFormValues(record);
                        }}
                    >
                        配置
                    </a>
                    <Divider type="vertical" />
                    <a href="">订阅警报</a>
                </>
            ),
        },
    ]);
    return (
        <PageHeaderWrapper>
            <ProTable
                // headerTitle=""
                actionRef={actionRef}
                rowKey="key"
                onChange={(_, _filter, _sorter) => {
                    const sorterResult = _sorter;

                    if (sorterResult.field) {
                        setSorter(`${sorterResult.field}_${sorterResult.order}`);
                    }
                }}
                params={{
                    sorter,
                }}
                search={{}}
                toolBarRender={(action, { selectedRows }) => {
                    return (
                        <Fragment>
                            <Button type="primary" onClick={() => handleModalVisible(true)}>
                                <PlusOutlined /> 新建
                            </Button>
                            {selectedRows && selectedRows.length > 0 && (
                                <Dropdown
                                    overlay={
                                        <Menu
                                            onClick={async (e) => {
                                                if (e.key === 'remove') {
                                                    await handleRemove(selectedRows);
                                                    action.reload();
                                                }
                                            }}
                                            selectedKeys={[]}
                                        >
                                            <Menu.Item key="remove">批量删除</Menu.Item>
                                            <Menu.Item key="approval">批量审批</Menu.Item>
                                        </Menu>
                                    }
                                >
                                    <Button>
                                        批量操作 <DownOutlined />
                                    </Button>
                                </Dropdown>
                            )}
                        </Fragment>
                    );
                }}
                tableAlertRender={(selectedRowKeys, selectedRows) => (
                    <div>
                        已选择{' '}
                        <a
                            style={{
                                fontWeight: 600,
                            }}
                        >
                            {selectedRowKeys.length}
                        </a>{' '}
                        项&nbsp;&nbsp;
                        <span>
                            服务调用次数总计{' '}
                            {selectedRows.reduce((pre, item) => pre + item.callNo, 0)} 万
                        </span>
                    </div>
                )}
                request={(params) => queryRule(params)}
                columns={columns}
                rowSelection={{}}
            />
            <CreateForm
                onCancel={() => handleModalVisible(false)}
                modalVisible={createModalVisible}
            >
                <ProTable
                    onSubmit={async (value) => {
                        const success = await handleAdd(value);

                        if (success) {
                            handleModalVisible(false);

                            if (actionRef.current) {
                                actionRef.current.reload();
                            }
                        }
                    }}
                    rowKey="key"
                    type="form"
                    columns={columns}
                    rowSelection={{}}
                />
            </CreateForm>
            {stepFormValues && Object.keys(stepFormValues).length ? (
                <UpdateForm
                    onSubmit={async (value) => {
                        const success = await handleUpdate(value);

                        if (success) {
                            handleModalVisible(false);
                            setStepFormValues({});

                            if (actionRef.current) {
                                actionRef.current.reload();
                            }
                        }
                    }}
                    onCancel={() => {
                        handleUpdateModalVisible(false);
                        setStepFormValues({});
                    }}
                    updateModalVisible={updateModalVisible}
                    values={stepFormValues}
                />
            ) : null}
        </PageHeaderWrapper>
    );
};

export default TableList;
