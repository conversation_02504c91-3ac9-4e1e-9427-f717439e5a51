import React from 'react';
import { Modal } from 'antd';

const CreateForm = (props) => {
    const { modalVisible, onCancel } = props;
    return (
        <Modal
            destroyOnClose
            title="新建规则"
            visible={modalVisible}
            onCancel={() => onCancel()}
            footer={null}
            maskClosable={false}
        >
            {props.children}
        </Modal>
    );
};

export default CreateForm;
