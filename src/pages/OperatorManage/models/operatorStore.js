import { message } from 'antd';
import {
    qryXdtOperatorInfosApi,
    xdtOperatorInitApi,
    operStopAndStartApi,
    queryInputInfosApi,
    indirectInitApi,
    saveInitApi,
    saveOperatorsApi,
    qryXdtOperatorInfoDetailApi,
    qryInputDetailApi,
    qryDownAreaInfosApi,
    qryBuildParentInfoApi,
    mockApi,
} from '@/services/OperationMng/OperationMngApi.js';

export default {
    namespace: 'operator',
    state: {
        // 运营商管理
        operationList: [], // 运营商管理列表
        operationListNum: 0, // 运营商管理列表记录数
        linkModeList: [], // 接入模式
        orderChannelList: [], // 订单渠道
        operCooperationTypeList: [], // 资金模式

        // 父级运营商下的运营商信息
        childInfos: [], //

        operatorInfo: null, // 运营商详情信息
        operatorEditInfo: null, // 运营商编辑展示的详情，和上面的详情信息做数据隔离，避免详情的弹窗数据相互覆盖
        merchantTypeList: [], // 运营商类型
        operCertTypeList: [], // 运营商证件类型
        legalCertTypeList: [], // 法人证件类型
        settleTypeList: [], // 进件结算方式
        taxPayerTypeList: [], // 纳税人资格种类
        provinceList: [], // 级联框省的标准代码
        childrenAreaList: [], // 级联框市或县的标准代码
        invoiceAccessModelList: [], // 开票接入方式标准代码
        invoiceTypeCodelList: [], // 发票类型标准代码

        // 进件审核
        auditList: [], // 进件审核列表数据
        auditListNum: 0, // 进件审核列表数据记录数
        orgBoList: [], // 管理单位下拉框数据
        buildList: [], // 运营商下拉框数据
        applyTypeList: [], // 申请类型下拉框数据
        auditDetailInfo: {}, // 进件审核详情
        threeChannelList: [], // 第三方渠道
    },

    effects: {
        *qryXdtOperatorInfos({ payload }, { call, put }) {
            try {
                const response = yield call(qryXdtOperatorInfosApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        operationList: response.data.list,
                        operationListNum: response.data.total,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *xdtOperatorInit({ payload }, { call, put }) {
            try {
                const response = yield call(xdtOperatorInitApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        linkModeList: response.data.linkModeList,
                        operCooperationTypeList: response.data.operCooperationTypeList,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *operStopAndStart({ payload }, { call, put }) {
            try {
                const response = yield call(operStopAndStartApi, payload);
                message.success('操作成功！');
            } catch (error) {
                console.log(error);
            }
        },

        *queryInputInfos({ payload }, { call, put }) {
            try {
                const response = yield call(queryInputInfosApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        auditList: response.data.list,
                        auditListNum: response.data.total,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *indirectInit({ payload }, { call, put }) {
            try {
                const response = yield call(indirectInitApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        orgBoList: response.data.orgBoList,
                        buildList: response.data.buildList,
                        applyTypeList: response.data.applyTypeList,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *saveInit({ payload }, { call, put }) {
            try {
                const response = yield call(saveInitApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        linkModeList: response.data.linkModeList || [],
                        orderChannelList: response.data.orderChannelList || [],
                        merchantTypeList: response.data.merchantTypeList || [],
                        operCertTypeList: response.data.operCertTypeList || [],
                        legalCertTypeList: response.data.legalCertTypeList || [],
                        settleTypeList: response.data.settleTypeList || [],
                        taxPayerTypeList: response.data.taxPayerTypeList || [],
                        provinceList: response.data.provinceList || [],
                        invoiceAccessModelList: response.data.invoiceAccessModelList || [],
                        invoiceTypeCodelList: response.data.invoiceTypeCodelList || [],
                        operCooperationTypeList: response.data.operCooperationTypeList || [],
                        threeChannelList: response.data.threeChannelList || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },

        *qryDownAreaInfos({ payload }, { call, put }) {
            try {
                const response = yield call(qryDownAreaInfosApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        childrenAreaList: response.data.areaList || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },

        *saveOperators({ payload, callback }, { call, put }) {
            try {
                const response = yield call(saveOperatorsApi, payload);
                message.success('保存成功！');
                callback();
                yield put({
                    type: 'setGeneralData',
                    payload: {},
                });
            } catch (error) {
                console.log(error);
            }
        },
        *qryXdtOperatorInfoDetail({ payload }, { call, put }) {
            try {
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        operatorInfo: undefined,
                    },
                });
                const response = yield call(qryXdtOperatorInfoDetailApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        operatorInfo: response.data,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *qryXdtOperatorEditInfoDetail({ payload }, { call, put }) {
            try {
                const response = yield call(qryXdtOperatorInfoDetailApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        operatorEditInfo: response.data,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *qryInputDetail({ payload }, { call, put }) {
            try {
                const response = yield call(qryInputDetailApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        auditDetailInfo: response.data || {},
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *changeCommonData({ payload }, { call, put }) {
            try {
                yield put({
                    type: 'setGeneralData',
                    payload: { ...payload },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *testMock({ payload }, { call, put }) {
            try {
                const response = yield call(mockApi, payload);
            } catch (error) {
                console.log(error);
            }
        },
        *qryBuildParentInfo({ payload }, { call, put }) {
            try {
                const {
                    data: { buildList },
                } = yield call(qryBuildParentInfoApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        childInfos: buildList || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
    },

    reducers: {
        setGeneralData(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
    },
};
