import { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Row,
    Select,
    Alert,
    DatePicker,
    Tabs,
    Modal,
    Space,
} from 'antd';
import {
    SearchOutlined,
    PlusOutlined,
    ExclamationCircleOutlined,
    CheckCircleTwoTone,
    CloseCircleOutlined,
} from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import usePageState from '@/hooks/usePageState.js';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { exportTableByParams } from '@/utils/utils';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Select';
import TablePro from '@/components/TablePro';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import styles from '@/assets/styles/common.less';
import { qryXdtOperatorInfosPath } from '@/services/OperationMng/OperationMngApi.js';

const { TabPane } = Tabs;
const { MonthPicker, RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const inputOptions = {
    autoComplete: 'off',
};

const SearchLayout = (props) => {
    const {
        form,
        dispatch,
        onSubmit,
        onReset,
        tableLoading,
        operator: { operCooperationTypeList, linkModeList, orderChannelList, threeChannelList },
        exportFormEvent,
    } = props;

    useEffect(() => {
        if (orderChannelList.length == 0 || !threeChannelList.length) {
            dispatch({
                type: 'operator/saveInit',
                options: {},
            });
        }
    }, []);

    const modeOptions = useMemo(
        () =>
            (operCooperationTypeList &&
                operCooperationTypeList.map((ele) => (
                    <Option value={ele.codeValue} key={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                ))) ||
            [],
        [operCooperationTypeList],
    );
    const linkOptions = useMemo(
        () =>
            (linkModeList &&
                linkModeList.map((ele) => (
                    <Option value={ele.codeValue} key={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                ))) ||
            [],
        [linkModeList],
    );
    const resetEvent = () => {
        onReset();
    };

    const channelOptions = useMemo(
        () =>
            orderChannelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [orderChannelList],
    );

    const thirdChannelOptions = useMemo(
        () =>
            threeChannelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [threeChannelList],
    );

    return (
        <Form form={form} onFinish={onSubmit} initialValues={{}} scrollToFirstError>
            <SearchOptionsBar
                loading={tableLoading}
                onReset={resetEvent}
                onExportForm={exportFormEvent}
            >
                <Col span={8}>
                    <OperSelectTypeItem
                        formParams={{ filterAbandon: false }}
                        {...formItemLayout}
                        form={form}
                    />
                </Col>
                {/* <Col span={8}>
                    <FormItem name="buildName" label="运营商名称:" {...formItemLayout}>
                        <Input placeholder="请填写运营商名称" {...inputOptions} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="buildShortname" label="运营商简称:" {...formItemLayout}>
                        <Input placeholder="请填写运营商简称" {...inputOptions} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="buildNo" label="运营商编号:" {...formItemLayout}>
                        <Input placeholder="请填写运营商编号" {...inputOptions} />
                    </FormItem>
                </Col> */}
                <Col span={8}>
                    <FormItem name="linkMode" label="运营商类型:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {linkOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="cooperationType" label="合作类型:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {modeOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="合作平台:" name="cooperationPlatform">
                        <SelectCooperationPlatform multiple></SelectCooperationPlatform>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="profitState" label="分润配置:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="settleType" label="接入管存" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            <Option value="02">是</Option>
                            <Option value="01">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="alipayInputState" label="支付宝进件:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="wxApplymentState" label="微信进件:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="actFlag" label="参与平台活动:" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="dateRange" label="选择日期:" {...formItemLayout}>
                        <RangePicker />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单渠道:" name="orderChannel" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {channelOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="开放第三方充电" name="openThreeFlag" {...formItemLayout}>
                        <Select placeholder="全部" allowClear>
                            <Option value="1">开</Option>
                            <Option value="0">关</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="第三方渠道" name="openThreeOperChannel" {...formItemLayout}>
                        <Select placeholder="全部" allowClear>
                            {thirdChannelOptions}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ProgressModal = (props) => {
    const { dispatch, showModal, onClose } = props;

    const onFinish = (values) => {};

    return (
        <Modal
            title="开启运营"
            visible={showModal}
            footer={null}
            onCancel={onClose}
            maskClosable={false}
        >
            <Row>
                <Col>
                    <CheckCircleTwoTone />
                </Col>
                <Col flex="1">创建运营商</Col>
                <Col>已完成</Col>
            </Row>
            <Row>
                <Col>
                    <CheckCircleTwoTone />
                </Col>
                <Col flex="1">支付宝进件</Col>
                <Col>已完成</Col>
            </Row>
            <Row>
                <Col>
                    <CheckCircleTwoTone />
                </Col>
                <Col flex="1">微信进件</Col>
                <Col>已完成</Col>
            </Row>
            <Row>
                <Col>
                    <CloseCircleOutlined />
                </Col>
                <Col flex="1">配置分润</Col>
                <Col>
                    <a>去配置</a>
                </Col>
            </Row>
            <Button type="primary">开启运营</Button>
        </Modal>
    );
};

const OperatorList = (props) => {
    const {
        dispatch,
        tableLoading,
        operator: { operationList, operationListNum },
        global: { pageInit },
        history,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [selectedRowKeys, changeSelectedRowKeys] = useState([]); // 选中的记录
    const [showProgressModal, changeShowProgressModal] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            checkStatus: '',
        },
        props,
    );

    useEffect(() => {
        dispatch({
            type: 'operator/xdtOperatorInit',
            payload: {},
        });

        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // const changeTabTypeEvent = type => {
    //     changePageInfo({ tabType: type, pageIndex: 1 });
    // };

    const searchData = (isDownload = false) => {
        const data = form.getFieldsValue();

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        let params = {
            // buildName: data.buildName,
            // buildShortname: data.buildShortname,
            // buildNo: data.buildNo,
            linkMode: data.linkMode,
            cooperationType: data.cooperationType,
            alipayInputState: data.alipayInputState,
            wxApplymentState: data.wxApplymentState,
            profitState: data.profitState,
            cooperationPlatform:
                data.cooperationPlatform?.join?.(',') || data.cooperationPlatform || undefined,
            joinBeginTime: data.dateRange ? data.dateRange[0].format('YYYY-MM-DD') : '',
            joinEndTime: data.dateRange ? data.dateRange[1].format('YYYY-MM-DD') : '',
            actFlag: data.actFlag,
            operId: data.operId,
            orderChannel: data.orderChannel,
            openThreeFlag: data.openThreeFlag,
            openThreeOperChannel: data.openThreeOperChannel,
            settleType: data.settleType,
        };

        if (isDownload) {
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }

            exportTableByParams({
                methodUrl: qryXdtOperatorInfosPath,
                options: params,
                columnsStr: columnsStrs,
            });
        } else {
            params.pageNum = pageInfo.pageIndex;
            params.totalNum = pageInfo.pageSize;
            dispatch({
                type: 'operator/qryXdtOperatorInfos',
                payload: params,
            });
        }
    };

    const handleSearch = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const addRecord = () => {
        props.history.push('/sellerCenter/operatormanage/operator/list/add');
    };

    // 恢复
    const startOper = () => {
        confirm({
            title: '是否确定恢复?',
            content: <span>确定恢复吗!</span>,
            okText: '确定',
            cancelText: '取消',
            icon: <ExclamationCircleOutlined />,
            onOk: async () => {
                await dispatch({
                    type: 'operator/operStopAndStart',
                    payload: {
                        buildId: selectedRowKeys[0],
                        handleFlag: '01',
                    },
                });
                changeSelectedRowKeys([]);
                changePageInfo({ pageIndex: 1 });
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const closeProgressModal = () => {
        changeShowProgressModal(false);
    };

    const gotoProfitRule = (item) => {
        const params = {
            operName: item.orgCodeName,
            operId: item.operNo,
        };
        if (item.cooperationType === '02') {
            dispatch({
                type: 'purchaseModel/setSelectRuleInfo',
                info: params,
            });
        } else {
            dispatch({
                type: 'profitRuleModel/setSelectRuleInfo',
                info: params,
            });
        }

        history.push(
            `/sellerCenter/operatormanage/${
                item.cooperationType === '02' ? 'purchase' : 'profitRule'
            }/list/details${item.ruleId ? `/${item.ruleId}` : ''}`,
        );
    };

    const columns = [
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'joinTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商编号',
            width: 200,
            dataIndex: 'operNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商名称',
            width: 200,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商简称',
            width: 200,
            dataIndex: 'operNickName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商类型',
            width: 140,
            dataIndex: 'linkModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '合作类型',
            width: 150,
            dataIndex: 'cooperationTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '合作平台',
            width: 180,
            dataIndex: 'cooperationPlatformName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '接入管存',
            width: 120,
            dataIndex: 'settleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付宝进件',
            width: 150,
            dataIndex: 'alipayInputStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '微信进件',
            width: 150,
            dataIndex: 'wxApplymentStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '参与平台活动',
            width: 150,
            dataIndex: 'actFlagName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方充电',
            width: 120,
            dataIndex: 'openThreeFlagName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方渠道',
            width: 200,
            dataIndex: 'openThreeOperChannelName',
            render(text, record) {
                return (
                    <div title={text} style={{ width: '250px' }} className={styles['text-clamp-2']}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '分润配置',
            width: 140,
            dataIndex: 'profitStateName',
            render(text, record) {
                return (
                    <span className={styles['table-btn']} onClick={() => gotoProfitRule(record)}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '订单渠道',
            width: 250,
            dataIndex: 'orderChannelName',
            render(text, record) {
                return (
                    <div title={text} style={{ width: '250px' }} className={styles['text-clamp-2']}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '合同编号',
            width: 150,
            dataIndex: 'operProjectCode',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 90,
            fixed: 'right',
            render: (text, record) => (
                <Fragment>
                    <Space>
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                props.history.push(
                                    `/sellerCenter/operatormanage/operator/list/detail/${record.parentBuildId}`,
                                );
                            }}
                        >
                            详情
                        </span>
                        {/* {record.checkStatus != '01' ? (
                            <span
                                className={styles['table-btn']}
                                onClick={() =>
                                    props.history.push(
                                        `/sellerCenter/operatormanage/operator/list/update/${record.buildId}`,
                                    )
                                }
                            >
                                编辑
                            </span>
                        ) : null} */}
                    </Space>
                </Fragment>
            ),
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={handleSearch}
                    onReset={resetData}
                    exportFormEvent={() => searchData(true)}
                />
                <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginBottom: '20px' }}>
                    <Col>
                        <Button type="primary" onClick={() => addRecord()}>
                            <PlusOutlined />
                            新建
                        </Button>
                    </Col>
                </Row>
                {/* <Tabs defaultActiveKey="" onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key="" />
                    <TabPane tab="待启动" key="01" />
                    <TabPane tab="运营中" key="02" />
                    <TabPane tab="已停运" key="03" />
                </Tabs> */}
                <TablePro
                    name="operatorlist"
                    loading={tableLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={operationList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: operationListNum,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
            <ProgressModal showModal={showProgressModal} onClose={closeProgressModal} {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, operator, loading }) => ({
    global,
    operator,
    tableLoading: loading.effects['operator/qryXdtOperatorInfos'],
}))(OperatorList);
