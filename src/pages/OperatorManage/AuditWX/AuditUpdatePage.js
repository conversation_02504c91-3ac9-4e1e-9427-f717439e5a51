import React, { Fragment, useState, useEffect } from 'react';
import request from '@/utils/request';
import { connect } from 'dva';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Row,
    Select,
    Alert,
    DatePicker,
    message,
    Radio,
    Checkbox,
    Tooltip,
    Upload,
    Switch,
    Anchor,
    Modal,
    Cascader,
    Spin,
} from 'antd';
import { InfoCircleOutlined, PlusOutlined, LoadingOutlined, FormOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import moment from 'moment';
import styles from '../style.less';
import { qryDownAreaInfosApi } from '@/services/OperationMng/OperationMngApi.js';

const FormItem = Form.Item;
const { Option } = Select;
const { Link } = Anchor;
const { RangePicker } = DatePicker;

const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 10,
    },
};

/**
 * 基本信息层
 */
const BasicInfoView = (props) => {
    const {
        form,
        formDisabled,
        changeFormDisabled,
        match,
        auditWXModel: {
            provinceList,
            childrenAreaList,
            merchantTypeList,
            operCertTypeList,
            legalCertTypeList,
            settleTypeList,
            taxPayerTypeList,
            editActInfo,
        },
    } = props;

    const { buildId } = match.params;

    const [editButtonDisabled, changeEditButtonDisabled] = useState(false); // 控制编辑按钮

    // 图片相关
    const [updateLoading, changeLoading] = useState(false);
    const [previewVisible, changePreviewVisible] = useState(false);
    const [previewImage, changePreviewImage] = useState('');
    const [fileList, changeFileList] = useState([]);

    const [addressOption, changeAddressOption] = useState([]); // 地址级联框数据

    useEffect(() => {
        initData();
    }, [editActInfo]);

    const initData = async () => {
        if (editActInfo) {
            let initialValues = {};
            if (editActInfo) {
                initialValues = {
                    operatorNo: editActInfo.operNo,
                    operatorID: editActInfo.alipayInputId,
                    buildName: editActInfo.buildName,
                    buildShortName: editActInfo.buildShortname,
                    merchantType: editActInfo.merchantType,
                    operMcc: editActInfo.operMcc,
                    operCertType: editActInfo.operCertType,
                    socialCreditCode: editActInfo.socialCreditCode,
                    legalPerson: editActInfo.legalPerson,
                    legalCertType: editActInfo.legalCertType,
                    legalCertNo: editActInfo.legalCertNo,
                    signTime: editActInfo.signTime
                        ? moment(editActInfo.signTime, 'YYYY-MM-DD HH:mm:ss')
                        : null,
                    address: [editActInfo.province, editActInfo.city, editActInfo.county],
                    servicePhone: editActInfo.servicePhone,
                    buildContact: editActInfo.contactName,
                    buildTel: editActInfo.contactMobile,
                    buildEmail: editActInfo.buildEmail,
                    operSettleType: 1,
                    examineFlag: editActInfo.examineFlag == '1',
                    invoiceFlag: editActInfo.invoiceFlag == 'true',
                    alipayAccountName: editActInfo.alipayAccountName,
                    actFlag: editActInfo.actFlag == '1',
                };

                if (editActInfo.invoiceFlag == 'true') {
                    initialValues = {
                        ...initialValues,
                        taxPayerType: editActInfo.taxPayerType,
                        qualificationTime: editActInfo.qualificationTime
                            ? moment(editActInfo.qualificationTime, 'YYYY-MM-DD')
                            : null,
                        invoiceTitle: editActInfo.invoiceTitle,
                        taxNum: editActInfo.taxNum,
                        invoiceBankName: editActInfo.invoiceBankName,
                        invoiceBankAccount: editActInfo.invoiceBankAccount,
                        invoiceAddr: editActInfo.invoiceAddr,
                        invoiceMobile: editActInfo.invoiceMobile,
                        recipients: editActInfo.recipients,
                        receiverAddress: [
                            editActInfo.invoiceProvince,
                            editActInfo.invoiceCity,
                            editActInfo.invoiceCounty,
                        ],
                        invoiceUserAddr: editActInfo.invoiceUserAddr,
                        phoneNo: editActInfo.phoneNo,
                    };
                }
            }
            form.setFieldsValue(initialValues);

            // 初始化级联框的省数据
            // const provinceData = provinceList.map((item, index) => ({
            //     label: item.areaName,
            //     value: item.areaCode,
            //     isLeaf: false,
            // }));
            const provinceData = [];
            // 经营地址级联框的数据
            if (editActInfo.province && editActInfo.city && editActInfo.county) {
                // 市

                const {
                    data: { areaList: cityList },
                } = await qryDownAreaInfosApi({ upAreaCode: editActInfo.province });
                const cityData = cityList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                const index = provinceData.findIndex((val) => editActInfo.province == val.value);
                if (index > -1) {
                    provinceData[index].children = cityData;
                }
                // 县
                const {
                    data: { areaList: countyList },
                } = await qryDownAreaInfosApi({ upAreaCode: editActInfo.city });
                const countyData = countyList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: true,
                }));
                const index2 = cityData.findIndex((val) => val.value == editActInfo.city);
                if (index2 > -1) {
                    provinceData[index].children[index2].children = countyData;
                }
            }
            // 收件人地址级联框的数据
            if (
                editActInfo.invoiceProvince &&
                editActInfo.invoiceCity &&
                editActInfo.invoiceCounty
            ) {
                // 市

                const {
                    data: { areaList: cityList },
                } = await qryDownAreaInfosApi({ upAreaCode: editActInfo.invoiceProvince });
                const cityData = cityList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                const index = provinceData.findIndex(
                    (val) => editActInfo.invoiceProvince == val.value,
                );
                if (index > -1) {
                    provinceData[index].children = cityData;
                }
                // 县

                const {
                    data: { areaList: countyList },
                } = await qryDownAreaInfosApi({ upAreaCode: editActInfo.invoiceCity });
                const countyData = countyList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: true,
                }));
                const index2 = cityData.findIndex((val) => val.value == editActInfo.invoiceCity);
                if (index2 > -1) {
                    provinceData[index].children[index2].children = countyData;
                }
            }
            changeAddressOption(provinceData);
        }
    };
    // 级联框懒加载数据
    const loadAddressData = async (selectedOptions) => {
        if (selectedOptions.length < 3) {
            const targetOption = selectedOptions[selectedOptions.length - 1];
            targetOption.loading = true;

            const {
                data: { areaList },
            } = await qryDownAreaInfosApi({
                upAreaCode: targetOption.value,
            });
            targetOption.loading = false;
            const children = areaList;
            const isLeaf = selectedOptions.length == 2;
            const dealData = children.map((item, index) => ({
                label: item.areaName,
                value: item.areaCode,
                isLeaf,
            }));
            targetOption.children = dealData;

            changeAddressOption([...addressOption]);
        }
    };

    const appAmountValidator = (rule, value, callback) => {
        const reg = /^(-?\d+)(\.\d{1,2})?$/;
        if (value) {
            if (!reg.test(value)) {
                callback('最多保留两位小数');
            }
        }
        callback();
    };

    /* ----图片处理相关函数---- begin */
    const beforeUpload = (file) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            message.error('You can only upload JPG/PNG file!');
            file.status = 'error';
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
            message.error('Image must smaller than 2MB!');
        }
        return isJpgOrPng && isLt2M;
    };
    const handleChange = (info) => {
        if (info.file.status === 'uploading') {
            changeLoading(true);
            return;
        }
        if (info.file.status === 'done' || info.file.status === 'error') {
            changeLoading(false);
        }
        changeFileList(info.fileList);
    };
    const getBase64 = (file) =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = (error) => reject(error);
        });
    const handleCancel = () => changePreviewVisible(false);
    const handlePreview = async (file) => {
        if (!file.url && !file.preview) {
            file.preview = await getBase64(file.originFileObj);
        }
        changePreviewImage(file.url || file.preview);
        changePreviewVisible(true);
    };
    /* ----图片处理相关函数----end */

    // 查看审核详情
    const goAuditDetail = () => {
        props.history.push(`/sellerCenter/operatormanage/audit/list/detail/${editActInfo.buildId}`);
    };

    const exelUrl =
        'https://gw.alipayobjects.com/os/basement_prod/82cb70f7-abbd-417a-91ba-73c1849f07ea.xlsx';

    // 控制全局表单是否可编辑
    // if (formDisabled) {
    //   getFieldDecorator = (...rest) => {
    //     return element => {
    //       let NewElement = React.cloneElement(element, {
    //         disabled: true,
    //       });
    //       return form.getFieldDecorator(...rest)(NewElement);
    //     };
    //   };
    // }

    const uploadProps = {
        action: '/oper/uploadCertImage',
        customRequest: ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            const formData = new FormData();
            let options = {
                ...data,
            };
            if (options) {
                Object.keys(options).forEach((key) => {
                    formData.append(key, options[key]);
                });
            }
            formData.append(filename, file);

            request
                .post(action, formData, {
                    withCredentials,
                    headers,
                    onUploadProgress: ({ total, loaded }) => {
                        onProgress(
                            { percent: Number(Math.round((loaded / total) * 100).toFixed(2)) },
                            file,
                        );
                    },
                })
                .then(({ data: response }) => {
                    this.setState({ legalImgOssKey: response.data.imageId });
                    onSuccess(response, file);
                })
                .catch(onError);

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType: 'picture-card',
        onChange: handleChange,
        beforeUpload,
        onPreview: handlePreview,
        multiple: true,
    };
    const uploadButton = (
        <div>
            {updateLoading ? <LoadingOutlined /> : <PlusOutlined />}
            <div className="ant-upload-text">Upload</div>
        </div>
    );

    return (
        <Card
            title={
                <span>
                    <span className={styles.formTitle}>基础信息</span>
                    {editActInfo && editActInfo.checkStatus == '01' && (
                        <Fragment>
                            <span
                                style={{
                                    marginLeft: '20px',
                                    fontSize: '15px',
                                    color: 'red',
                                }}
                            >
                                进件信息审核中,
                            </span>
                            <a style={{ fontSize: '15px' }} onClick={goAuditDetail}>
                                {' '}
                                前往查看详情
                            </a>
                        </Fragment>
                    )}
                    {buildId && (
                        <span style={{ cssFloat: 'right' }}>
                            <Button
                                type="primary"
                                icon={<FormOutlined />}
                                disabled={
                                    editButtonDisabled ||
                                    (editActInfo && editActInfo.checkStatus == '01')
                                }
                                onClick={() => changeFormDisabled(false)}
                            >
                                编辑
                            </Button>
                        </span>
                    )}
                </span>
            }
            style={{ paddingTop: '20px' }}
            id="baseInfo"
            bordered={false}
        >
            <Form.Item name="operatorNo" label="运营商编号：" {...formItemLayout} required>
                <Input placeholder="创建成功后自动生成" maxLength={10} disabled />
            </Form.Item>
            <Form.Item
                name="buildName"
                label="运营商名称："
                rules={[
                    {
                        required: true,
                        message: '请输入运营商名称',
                    },
                ]}
                {...formItemLayout}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="buildShortName"
                rules={[
                    {
                        required: true,
                        message: '请输入运营商简称',
                    },
                ]}
                label="运营商简称："
                {...formItemLayout}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="merchantType"
                label="主体类型："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请输入',
                    },
                ]}
            >
                <Select placeholder="请选择" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="operCertType"
                rules={[
                    {
                        required: true,
                        message: '请输入',
                    },
                ]}
                label={
                    <span>
                        证件类型
                        <Tooltip title="当前不支持选择个体商户身份证">
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    </span>
                }
                {...formItemLayout}
            >
                <Select placeholder="请选择" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="socialCreditCode"
                label={
                    <span>
                        证件编号
                        <Tooltip title="企业或者个体工商户提供营业执照，事业单位提供事业证号">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </span>
                }
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请输入运营商证件编号',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label={
                    <span>
                        证件扫描件
                        <Tooltip title="运营商证件类型为个人商户身份证是不可填写">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </span>
                }
                name="iconUrl"
                rules={[{ required: true, message: '请上传证件扫描件' }]}
            >
                <Upload
                    {...uploadProps}
                    data={{ addWatermarkFlag: '1', businessType: 'operCertBk' }}
                    disabled={formDisabled}
                >
                    {fileList.length > 0 ? null : uploadButton}
                </Upload>
                <Modal
                    visible={previewVisible}
                    footer={null}
                    onCancel={handleCancel}
                    maskClosable={false}
                >
                    <img alt="example" style={{ width: '100%' }} src={previewImage} />
                </Modal>
            </Form.Item>
            <Form.Item
                name="address"
                label="经营地址："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请选择',
                    },
                ]}
            >
                <Cascader
                    options={addressOption}
                    loadData={loadAddressData}
                    disabled={formDisabled}
                    placeholder="请选择"
                />
            </Form.Item>
            <Form.Item
                name="buildAddr"
                wrapperCol={{ span: 10, offset: 6 }}
                rules={[
                    {
                        required: true,
                        message: '请输入',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} autoComplete="off" />
            </Form.Item>

            <Form.Item
                name="legalCertType"
                label="法人证件类型："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请输选择',
                    },
                ]}
            >
                <Select placeholder="请选择" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="legalPerson"
                label="法人名称："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请输入法人名称',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="legalCertNo"
                label="法人身份证号："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请输入法人身份证号',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                name="signTime"
                label="身份证有效期："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请选择',
                    },
                ]}
            >
                <RangePicker disabled={formDisabled} format="YYYY-MM-DD" />
            </Form.Item>
            <Form.Item
                label={
                    <span>
                        上传身份证正面 或护照照片
                        <Tooltip title="港澳台身份证需提供身份证正面或护照照片">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </span>
                }
                name="iconUrl"
                rules={[{ required: true, message: '请上传身份证正面 或护照照片' }]}
            >
                <Upload {...uploadProps} disabled={formDisabled}>
                    {fileList.length > 0 ? null : uploadButton}
                </Upload>
                <Modal
                    visible={previewVisible}
                    footer={null}
                    onCancel={handleCancel}
                    maskClosable={false}
                >
                    <img alt="example" style={{ width: '100%' }} src={previewImage} />
                </Modal>
            </Form.Item>
            <Form.Item
                label="上传身份证反面"
                name="iconUrl"
                rules={[{ required: true, message: '请上传身份证正面 或护照照片' }]}
            >
                <Upload {...uploadProps} disabled={formDisabled}>
                    {fileList.length > 0 ? null : uploadButton}
                </Upload>
                <Modal
                    visible={previewVisible}
                    footer={null}
                    onCancel={handleCancel}
                    maskClosable={false}
                >
                    <img alt="example" style={{ width: '100%' }} src={previewImage} />
                </Modal>
            </Form.Item>
        </Card>
    );
};
/**
 * 运营商配置
 */
const OperateInfoView = (props) => {
    const { form, formDisabled } = props;

    return (
        <Card
            title={<span className={styles.formTitle}>进件信息</span>}
            style={{ paddingTop: '20px' }}
            id="operateInfo"
            bordered={false}
        >
            <Form.Item
                label="微信进件ID"
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <Input placeholder="进件审核通过后自动生成" disabled />
            </Form.Item>
            <Form.Item
                label="身份证有效期限"
                name="time"
                rules={[
                    {
                        required: true,
                    },
                ]}
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 18,
                    },
                }}
            >
                <Radio.Group>
                    <Radio value="02">长期</Radio>
                    <Radio value="01" style={{ display: 'block' }}>
                        限期
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.time !== curValues.time
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const time = getFieldValue('time');
                                let disabled = false;
                                if (time != '01') {
                                    disabled = true;
                                }
                                return (
                                    <Form.Item
                                        noStyle
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="请选择"
                                            disabled={disabled || formDisabled}
                                        />
                                    </Form.Item>
                                );
                            }}
                        </FormItem>
                    </Radio>
                </Radio.Group>
            </Form.Item>
            <Form.Item
                label="运营商收款"
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <Radio.Group>
                    <Radio value="01">是</Radio>
                    <Radio value="02">否</Radio>
                </Radio.Group>
            </Form.Item>
            <Form.Item
                label="账户类型"
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <Radio.Group>
                    <Radio value="01">对公账户</Radio>
                    <Radio value="02">对私账户</Radio>
                </Radio.Group>
            </Form.Item>
            <Form.Item
                label="开户银行名称"
                rules={[
                    {
                        required: true,
                        message: '开户银行名称不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="开户银行省市编码"
                rules={[
                    {
                        required: true,
                        message: '开户银行省市编码不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="开户银行联行号"
                rules={[
                    {
                        required: true,
                        message: '开户银行联行号不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label={
                    <span>
                        开户银行全称
                        <Tooltip title="需包含支行信息">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </span>
                }
            >
                <Input type="email" placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="开户名称"
                rules={[
                    {
                        required: true,
                        message: '开户名称不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="银行账号"
                rules={[
                    {
                        required: true,
                        message: '银行账号不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item label="超级管理员类型">
                <Radio.Group>
                    <Radio value="01">法人</Radio>
                    <Radio value="02">经营者</Radio>
                </Radio.Group>
            </Form.Item>
            <Form.Item
                label="超级管理员姓名"
                rules={[
                    {
                        required: true,
                        message: '超级管理员姓名不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="超级管理员身份证号"
                rules={[
                    {
                        required: true,
                        message: '超级管理员身份证号不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="超级管理员手机号"
                rules={[
                    {
                        required: true,
                        message: '超级管理员手机号不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="超级管理员邮件"
                rules={[
                    {
                        required: true,
                        message: '超级管理员邮件不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
            <Form.Item
                label="店铺链接"
                rules={[
                    {
                        required: true,
                        message: '店铺链接不能为空',
                    },
                ]}
            >
                <Input placeholder="请输入" disabled={formDisabled} />
            </Form.Item>
        </Card>
    );
};

const AuditUpdatePage = (props) => {
    const {
        dispatch,
        location,
        match,
        auditWXModel: {
            provinceList,
            childrenAreaList,
            merchantTypeList,
            operCertTypeList,
            legalCertTypeList,
            settleTypeList,
            taxPayerTypeList,
            editActInfo,
        },
        history,
        infoLoading,
    } = props;

    const [form] = Form.useForm();
    const [formDisabled, changeFormDisabled] = useState(false); // 控制表单编辑
    const [legalImgOssKey, changeLegalImgOssKey] = useState(''); // 上传身份证后返回的oss key

    const { buildId } = match.params;

    useEffect(() => {
        // 获取各个下拉框数据
        dispatch({
            type: 'auditWXModel/saveInit',
            payload: {},
        });

        // 初始化表单数据
        if (buildId) {
            dispatch({
                type: 'auditWXModel/qryXdtOperatorInfoDetail',
                payload: { buildId },
            });
            changeFormDisabled(true);
        }
    }, []);

    // 返回
    const goBackPage = () => {
        history.goBack();
    };

    const handleSubmit = (values) => {
        if (
            values.legalCertType == '105' ||
            values.legalCertType == '106' ||
            values.legalCertType == '108'
        ) {
            message.warn('港澳台身份证需提供身份证正面或护照照片!');
        }
        const params = {
            buildName: values.buildName,
            buildShortName: values.buildShortName,
            merchantType: values.merchantType,
            operMcc: values.operMcc,
            certificateType: values.certificateType,
            socialCreditCode: values.socialCreditCode,
            legalPerson: values.legalPerson,
            legalCertType: values.legalCertType,
            legalCertNo: values.legalCertNo,
            // 护照照片
            legalImgOssKey: legalImgOssKey || '',
            signTime: values.signTime.format('YYYY-MM-DD'),
            province: values.address[0],
            city: values.address[1],
            county: values.address[2],
            buildAddr: values.buildAddr,
            servicePhone: values.servicePhone,
            buildContact: values.buildContact,
            buildTel: values.buildTel,
            operSettleType: values.operSettleType,
            alipayAccountName: values.alipayAccountName,
            isChecked: values.isChecked,
            invoiceFlag: values.invoiceFlag == true ? '1' : '0',
            taxPayerType: values.taxPayerType,
            qualificationTime: values.qualificationTime
                ? values.qualificationTime.format('YYYY-MM-DD')
                : '',
            invoiceTitle: values.invoiceTitle,
            taxNum: values.taxNum,
            invoiceBankName: values.invoiceBankName,
            invoiceBankAccount: values.invoiceBankAccount,
            invoiceAddr: values.invoiceAddr,
            invoiceMobile: values.invoiceMobile,
            recipients: values.recipients,
            invoiceProvince: values.receiverAddress ? values.receiverAddress[0] : '',
            invoiceCity: values.receiverAddress ? values.receiverAddress[1] : '',
            invoiceCounty: values.receiverAddress ? values.receiverAddress[2] : '',
            invoiceUserAddr: values.invoiceUserAddr,
            phoneNo: values.phoneNo,
            actFlag: values.actFlag == true ? '1' : '0',
        };

        if (editActInfo && buildId) {
            params.buildId = buildId;
            params.inputInvoiceId = editActInfo.inputInvoiceId;
            params.operInputTempId = editActInfo.operInputTempId;
        }
        dispatch({
            type: 'auditWXModel/saveOperators',
            payload: params,
            callback: () => {
                props.history.goBack();
            },
        });
    };

    return (
        <PageHeaderWrapper title="微信进件">
            <Spin spinning={infoLoading || false} tip="Loading...">
                <Row>
                    <Col flex="1">
                        <Form
                            form={form}
                            onFinish={handleSubmit}
                            {...formItemLayout}
                            initialValues={{}}
                            scrollToFirstError
                        >
                            <BasicInfoView {...props} form={form} formDisabled={formDisabled} />

                            {/* 运营商配置 */}
                            <OperateInfoView form={form} formDisabled={formDisabled} />
                            <Card>
                                <Row>
                                    {!formDisabled ? (
                                        <Fragment>
                                            <Col span={12} offset={8}>
                                                <Button type="primary" htmlType="submit">
                                                    提交
                                                </Button>
                                                <Button
                                                    onClick={goBackPage}
                                                    style={{ marginLeft: '20px' }}
                                                >
                                                    取消
                                                </Button>
                                            </Col>
                                        </Fragment>
                                    ) : (
                                        <Col span={12} offset={8}>
                                            <Button type="primary" onClick={goBackPage}>
                                                返回
                                            </Button>
                                        </Col>
                                    )}
                                </Row>
                            </Card>
                        </Form>
                    </Col>
                    <Col flex="0 0 auto" style={{ backgroundColor: '#fff' }}>
                        <Anchor
                            style={{
                                padding: '30px 20px',
                                backgroundColor: 'transparent',
                                fontSize: '16px',
                                fontWeight: 'bold',
                            }}
                            bounds={300}
                            offsetTop={60}
                        >
                            <Link href="#baseInfo" title={<span>基础信息</span>} />
                            <div style={{ height: '50px' }} />
                            <Link href="#operateInfo" title={<span>进件信息</span>} />
                        </Anchor>
                    </Col>
                </Row>
            </Spin>
        </PageHeaderWrapper>
    );
};

export default connect(({ auditWXModel, loading }) => ({
    auditWXModel,
    infoLoading: loading.effects['auditWXModel/initEditActInfo'],
}))(AuditUpdatePage);
