import type { ProFormInstance } from '@ant-design/pro-form';
import { PlusOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import { Button, message, Popconfirm, Space, Spin, Typography, Radio, Switch } from 'antd';
import { useRef, useState, useEffect, useMemo } from 'react';
import { history, Link } from 'umi';
import { isEmpty, renderTableDataIndexText } from '@/utils/utils';

import XdtProTable from '@/components/XdtProTable';
import {
    operCoursePageApi,
    operCourseDeleteApi,
    operCourseSortApi,
} from '@/services/Marketing/BusinessCollegeApi';
import { useRequest } from 'ahooks';

const ListPage = () => {
    const { query = {}, pathname } = history.location;

    const { courseId } = query as any;
    const actionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();
    const [tabValue, changeTabValue] = useState<string>('ALL');
    const [tableList, updateTableList] = useState<API.BusinessCoursePaginationResponse[]>([]);

    const { run: changePositionRequest, loading: changePositionLoading } = useRequest(
        (currentId, newId) => {
            return operCourseSortApi({
                courseId: currentId,
                sortedCourseId: newId,
            });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('操作成功');
                    actionRef.current?.reload();
                } else {
                    message.error(res?.msg || '操作失败');
                }
            },
            onError: () => {
                message.error('操作失败');
            },
        },
    );

    const onMove = (record: any, index: number) => {
        changePositionRequest(record?.courseId, tableList?.[index]?.courseId);
    };

    // 删除
    const { run: handleDelete, loading } = useRequest(
        (params) => {
            return operCourseDeleteApi(params?.courseId);
        },
        {
            manual: true,
            onSuccess(res) {
                if (res.ret === 200) {
                    message.success('操作成功');
                    actionRef.current?.reload();
                }
            },
        },
    );

    const columns: ProColumnType<any>[] = [
        {
            title: '课程名称',
            dataIndex: 'courseName',
            fieldProps: {
                maxLength: 15,
            },
            order: 10,
            width: 160,
        },
        {
            title: '课程ID',
            dataIndex: 'courseId',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '使用平台',
            dataIndex: 'usePlatformName',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTimeStr',
            hideInSearch: true,
        },
        {
            title: '更新时间',
            width: 200,
            dataIndex: 'updatedTimeStr',
            hideInSearch: true,
        },
        {
            title: '操作',
            hideInSearch: true,
            fixed: 'right',
            renderText(_, record: any, index) {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                const path = `/marketing/merchantActivityManagement/businessCollege/add?id=${record.courseId}&method=LOOK`;
                                history.push(path);
                            }}
                        >
                            查看
                        </Typography.Link>
                        <Typography.Link
                            onClick={() => {
                                const path = `/marketing/merchantActivityManagement/businessCollege/add?id=${record.courseId}&method=EDIT`;
                                history.push(path);
                            }}
                        >
                            编辑
                        </Typography.Link>
                        {/* 只有tab切到后台，才可改顺序 */}
                        {index !== 0 && (tabValue === '01' || tabValue === '02') && (
                            <Button
                                type="link"
                                loading={changePositionLoading}
                                onClick={() => onMove(record, index - 1)}
                                style={{ padding: 0, height: 20 }}
                            >
                                上移
                            </Button>
                        )}
                        {index !== tableList?.length - 1 &&
                            (tabValue === '01' || tabValue === '02') && (
                                <Button
                                    type="link"
                                    loading={changePositionLoading}
                                    onClick={() => onMove(record, index + 1)}
                                    style={{ padding: 0, height: 20 }}
                                >
                                    下移
                                </Button>
                            )}
                        <Spin spinning={loading}>
                            <Popconfirm
                                title="确定要删除吗？"
                                onConfirm={() => {
                                    handleDelete(record);
                                }}
                            >
                                <Typography.Link type="danger">删除</Typography.Link>
                            </Popconfirm>
                        </Spin>
                    </Space>
                );
            },
            width: 200,
        },
    ];

    const otherParams = useMemo(() => {
        return {
            usePlatform: tabValue === 'ALL' ? undefined : tabValue,
        };
    }, [tabValue]);

    return (
        <PageHeaderWrapper title="列表">
            <XdtProTable
                actionRef={actionRef}
                columns={columns}
                formRef={formRef}
                prefixKey="investmentActTemplateList"
                requestApi={operCoursePageApi}
                dataSourceLoadCallback={(list: any[]) => {
                    updateTableList(list);
                }}
                rowKey="courseId"
                tabsConfig={{
                    dataIndex: 'usePlatform',
                    options: [
                        {
                            label: '全部',
                            key: 'ALL',
                        },
                        {
                            label: '后台',
                            key: '01',
                        },
                        {
                            label: '小程序',
                            key: '02',
                        },
                    ],
                    onChange: (newVal: string | undefined) => {
                        if (typeof newVal === 'string') {
                            changeTabValue(newVal);
                        }
                    },
                }}
                otherParams={otherParams}
                toolButtons={[
                    <Link
                        key="add"
                        to={`/marketing/merchantActivityManagement/businessCollege/add`}
                    >
                        <Button type="primary" icon={<PlusOutlined />}>
                            新建
                        </Button>
                    </Link>,
                    <Link
                        style={{
                            marginLeft: '40px',
                            position: 'absolute',
                            right: '50px',
                            top: '24px',
                        }}
                        key="other"
                        to={`/marketing/merchantActivityManagement/businessCollege/category`}
                    >
                        课程类别
                    </Link>,
                ]}
            />
        </PageHeaderWrapper>
    );
};

export default ListPage;
