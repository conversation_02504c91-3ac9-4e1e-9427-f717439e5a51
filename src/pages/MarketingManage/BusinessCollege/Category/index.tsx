import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    Space,
    Card,
    Form,
    Col,
    Input,
    Row,
    Button,
    Tree,
    message,
    Popconfirm,
    Modal,
    Spin,
    Switch,
    Tooltip,
    Typography,
} from 'antd';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { LeftOutlined, PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { usePagination, useRequest } from 'ahooks';
import { useParams, history } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import {
    operCourseTypePageApi,
    operCourseTypeDeleteApi,
    operCourseTypeSaveApi,
} from '@/services/Marketing/BusinessCollegeApi';

const CatagoryAllKey = '0-0';

const CatagoryAddModal: React.FC<{
    onFinish: () => void;
    initRef: any;
    data?: any;
    id?: string;
}> = ({ onFinish, initRef, data }) => {
    const [form] = Form.useForm();
    const [isWaiting, updateWaiting] = useState(false);
    const [visible, updateVisible] = useState(false);
    const [item, updateItem] = useState<API.MktCourseTypeListVo>();
    useImperativeHandle(initRef, () => ({
        show: (value?: API.MktCourseTypeListVo) => {
            updateItem(value);
            updateVisible(true);
            if (value?.courseTypeName) {
                form.setFieldValue('courseTypeName', value?.courseTypeName);
            } else {
                form.resetFields();
            }
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    return (
        <Modal
            title={item?.courseTypeId ? '编辑分类' : '新建分类'}
            okButtonProps={{ loading: isWaiting }}
            visible={visible}
            onCancel={onClose}
            onOk={() => {
                form.validateFields().then(async (values) => {
                    try {
                        updateWaiting(true);
                        let params = {};
                        if (item?.courseTypeId) {
                            const arr = data?.map((val: any) => {
                                if (val.courseTypeId == item?.courseTypeId) {
                                    val.courseTypeName = values.courseTypeName;
                                }
                                return val;
                            });
                            params = { courseTypeList: [...arr] };
                        } else {
                            params = { courseTypeList: [...data, values] };
                        }

                        await operCourseTypeSaveApi(params);
                        onClose();
                        onFinish?.();
                    } catch (error) {
                    } finally {
                        updateWaiting(false);
                    }
                });
            }}
            width={420}
        >
            <Form form={form} wrapperCol={{ span: 22 }}>
                <Form.Item
                    name="courseTypeName"
                    label={'分类'}
                    rules={[{ required: true, message: '请输入' }]}
                >
                    <Input
                        maxLength={10}
                        placeholder="请填写，最多10个字"
                        allowClear
                        autoComplete="off"
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};
const EditPage = () => {
    const { query = {}, pathname } = history.location;

    const { id, method } = query as any;
    const addCatagoryRef: any = useRef();
    const catagoryRef: any = useRef();
    const [categoryData, setCategoryData] = useState<API.MktCourseTypeListVo[]>([]);
    useEffect(() => {
        catagoryRef.current?.refresh();
    }, []);

    // 左侧的分类样式
    const CatogaryLayout: React.FC<{
        onSelectKeys?: (a: any) => void;
        onAddKeys?: () => void;
        initRef: any;
    }> = (props) => {
        const { onSelectKeys, onAddKeys, initRef } = props;
        const [selectedKeys, updateSelectedKeys] = useState<string[]>([CatagoryAllKey]);
        const [form] = Form.useForm();
        const [gData, setGData] = useState<any>([]);

        const refresh = async (params?: any) => {
            try {
                const info = {
                    ...params,
                };
                const { data: response } = await operCourseTypePageApi(info);
                setCategoryData(Array.isArray(response) && response?.length ? response : []);
                return response;
            } catch (error) {}
        };

        useImperativeHandle(initRef, () => ({
            refresh,
            // data,
        }));

        const deleteEvent = async (id: string) => {
            try {
                await operCourseTypeDeleteApi(id);
                message.success('操作成功');
                refresh();
            } catch (error) {}
        };

        useEffect(() => {
            const datas = [
                {
                    title: (
                        <Row>
                            <Col span={21}>
                                <text style={{ fontWeight: 'bold' }}>课程类别</text>
                            </Col>
                            <Col span={3}>
                                <PlusCircleOutlined
                                    style={{ color: 'blue' }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onAddKeys?.();
                                    }}
                                />
                            </Col>
                        </Row>
                    ),
                    key: CatagoryAllKey,
                    children: categoryData?.map((ele: API.MktCourseTypeListVo) => ({
                        title: (
                            <Row>
                                <Col
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onSelectKeys?.(ele);
                                    }}
                                    span={21}
                                >
                                    <span>{ele.courseTypeName}</span>
                                </Col>
                                <Col span={3}>
                                    <Popconfirm
                                        title="确定要删除吗？"
                                        onConfirm={(e) => {
                                            e?.stopPropagation();
                                            deleteEvent(`${ele.courseTypeId}`);
                                        }}
                                    >
                                        <MinusCircleOutlined
                                            style={{ color: 'red' }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                        />
                                    </Popconfirm>
                                </Col>
                            </Row>
                        ),
                        key: `${ele.courseTypeId}`,
                    })),
                },
            ];
            setGData(datas);
        }, [categoryData]);

        return (
            <Spin spinning={false}>
                <Tree
                    expandedKeys={[CatagoryAllKey]}
                    blockNode
                    treeData={gData}
                    onSelect={(keys: any, { selected }) => {
                        if (selected) {
                        }
                    }}
                    selectedKeys={selectedKeys}
                />
            </Spin>
        );
    };

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.push(`/marketing/merchantActivityManagement/businessCollege`);
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>课程类别</Typography.Title>
                </Space>
            }
        >
            <Card bodyStyle={{ paddingLeft: '40px', minHeight: '500px', width: '350px' }}>
                <CatogaryLayout
                    onSelectKeys={(item: API.MktCourseTypeListVo) => {
                        addCatagoryRef.current?.show(item);
                    }}
                    onAddKeys={() => {
                        addCatagoryRef.current?.show();
                    }}
                    initRef={catagoryRef}
                />
            </Card>
            <CatagoryAddModal
                initRef={addCatagoryRef}
                onFinish={() => {
                    catagoryRef.current?.refresh();
                }}
                data={categoryData}
            />
        </PageHeaderWrapper>
    );
};

export default EditPage;
