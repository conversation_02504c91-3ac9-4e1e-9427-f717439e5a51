import QuillRichInput from '@/components/QuillRichInput';
import {
    operCourseTypeDownApi,
    operCourseSaveApi,
    operCourseDetailApi,
} from '@/services/Marketing/BusinessCollegeApi';
import UpLoadVideoItem from '@/components/UpLoadImg/UpLoadVideoItem';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, Row, Select, Space, message, Radio, Tabs } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { useLocation } from 'umi';

const formItemLayout: any = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout: any = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

export default React.memo((props: any) => {
    const { route } = props;
    const params: any = useLocation();
    const { query } = params;
    const [form] = Form.useForm();

    const { method } = query;

    const isLook = useMemo(() => {
        return method === 'LOOK';
    }, [method]); // 是否可查看

    const { run, loading, data } = useRequest(
        async (params) => {
            const options = {
                ...params,
            };
            const { data } = await operCourseDetailApi(options?.id);
            return data;
        },
        {
            manual: true,
        },
    );

    const { data: courseTypeData } = useRequest(
        (params) => {
            const options = {
                pageIndex: 1,
                pageSize: 999,
                ...params,
            };
            return operCourseTypeDownApi(options);
        },
        // {
        //     manual: true,
        // },
    );

    useEffect(() => {
        if (query?.id) {
            run(query);
        }
    }, [query]);

    useEffect(() => {
        if (data) {
            form.setFieldsValue(data);
        }
    }, [data]);

    const submitEvent = (isGoon: boolean) => {
        form.validateFields().then(async (values) => {
            try {
                if (values?.courseContentText === '<p><br></p>') {
                    values.courseContentText = '';
                }
                const options = {
                    courseType: '01',
                    coverImage: '01',
                    courseStatus: '01', // 课程状态 01上线 09下线
                    ...values,
                };
                await operCourseSaveApi(options);
                message.success('保存成功');
                if (isGoon) {
                    form.resetFields();
                } else {
                    goBack();
                }
            } catch (error) {}
        });
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={loading}>
                <Form
                    form={form}
                    initialValues={{ type: '02', content: undefined }}
                    {...formItemLayout}
                >
                    <Form.Item noStyle name={'courseId'} />
                    <Form.Item
                        name="courseName"
                        label={'课程名称'}
                        rules={[{ required: true, message: '请填写' }]}
                        {...formItemFixedWidthLayout}
                    >
                        <Input
                            disabled={isLook}
                            maxLength={15}
                            placeholder="请填写，最多15个字"
                            allowClear
                            autoComplete="off"
                            showCount
                        />
                    </Form.Item>
                    <Form.Item
                        name="usePlatform"
                        label={'使用平台'}
                        rules={[{ required: true, message: '请选择' }]}
                        {...formItemFixedWidthLayout}
                    >
                        <Select disabled={isLook} placeholder="请选择">
                            <Select.Option value={'01'} key={'01'}>
                                后台
                            </Select.Option>
                            <Select.Option value={'02'} key={'02'}>
                                小程序
                            </Select.Option>
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="courseType"
                        label={'课程类别'}
                        rules={[{ required: true, message: '请选择' }]}
                        {...formItemFixedWidthLayout}
                    >
                        <Select disabled={isLook} placeholder="请选择">
                            {courseTypeData?.data?.map((ele: any) => (
                                <Select.Option value={ele.courseTypeValue} key={ele.courseTypeId}>
                                    {ele.courseTypeName}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label="封面图片"
                        name="coverImage"
                        rules={[{ required: true, message: '请选择封面图片' }]}
                    >
                        <UpLoadImgItem
                            disabled={isLook}
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'imgUrl',
                                relaTable: 'e_mkt_act',
                            }}
                            sizeInfo={{
                                size: 500,
                                // width: 800,
                                // height: 600,
                            }}
                        ></UpLoadImgItem>
                    </Form.Item>
                    <Form.Item
                        label="课程内容"
                        name="course"
                        required
                        // rules={[
                        //     {
                        //         required: true,
                        //         message: '至少配置一种形式，支持文本和视频的全部配置',
                        //     },
                        // ]}
                    >
                        <Tabs>
                            <Tabs.TabPane tab="图文" key="item-1" forceRender>
                                <QuillRichInput
                                    name="courseContentText"
                                    label=""
                                    disabled={isLook}
                                    // required
                                    // rules={[
                                    //     () => ({
                                    //         validator(rule: any, value: any) {
                                    //             if (!value || value == '<p><br></p>') {
                                    //                 return Promise.reject('请输入内容');
                                    //             }

                                    //             return Promise.resolve();
                                    //         },
                                    //     }),
                                    // ]}
                                    initialValue={''}
                                    height={'500px'}
                                    modules={{
                                        toolbar: {
                                            container: [
                                                ['bold', 'italic', 'underline', 'strike'],
                                                ['blockquote', 'code-block'],
                                                [{ header: 1 }, { header: 2 }],
                                                [{ list: 'ordered' }, { list: 'bullet' }],
                                                [{ script: 'sub' }, { script: 'super' }],
                                                [{ indent: '-1' }, { indent: '+1' }],
                                                [{ direction: 'rtl' }],
                                                [{ size: ['small', false, 'large', 'huge'] }], //字体设置
                                                [
                                                    {
                                                        color: [],
                                                    },
                                                ],
                                                [
                                                    {
                                                        background: [],
                                                    },
                                                ],
                                                [{ font: [] }],
                                                [{ align: [] }],
                                                ['link', 'image'], // a链接和图片的显示
                                            ],
                                        },
                                    }}
                                />
                            </Tabs.TabPane>
                            <Tabs.TabPane tab="视频" key="item-2" forceRender>
                                <Form.Item noStyle name="courseContentVideo">
                                    <UpLoadVideoItem
                                        sizeInfo={{ size: 1024 * 100 }}
                                        disabled={isLook}
                                    ></UpLoadVideoItem>
                                </Form.Item>
                            </Tabs.TabPane>
                        </Tabs>
                    </Form.Item>

                    <Form.Item wrapperCol={{ span: 20 }}>
                        <Row justify="center" style={{ marginTop: '32px' }}>
                            <Space size="large">
                                <Button
                                    type="primary"
                                    loading={loading}
                                    onClick={() => {
                                        const values = form.getFieldsValue();
                                        if (
                                            (!values?.courseContentText ||
                                                values?.courseContentText === '<p><br></p>') &&
                                            !values?.courseContentVideo
                                        ) {
                                            form.validateFields().then(() => {
                                                message.error(
                                                    '课程内容至少配置一种形式，支持文本和视频的全部配置',
                                                );
                                            });
                                            return;
                                        }
                                        submitEvent(false);
                                    }}
                                >
                                    提交
                                </Button>
                                {/* {(!query?.id && (
                                    <Button
                                        type="primary"
                                        loading={loading}
                                        onClick={() => {
                                            submitEvent(true);
                                        }}
                                    >
                                        提交并继续添加
                                    </Button>
                                )) ||
                                    null} */}
                                <Button
                                    onClick={() => {
                                        history.go(-1);
                                    }}
                                >
                                    取消
                                </Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
});
