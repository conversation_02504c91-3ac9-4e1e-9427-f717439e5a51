import { Form, TimePicker } from 'antd';

const { RangePicker: TimeRangePicker } = TimePicker;
const FormItem = Form.Item;
const ActTimeFormItem = (props) => {
    const { name = 'actTime', label = '选择时段', disabled } = props;
    const disabledMinutes = (selectedHour) => {
        const disabledList = [];
        for (let index = 0; index <= 59; index++) {
            if (index % 30 != 0 && index != 59) {
                disabledList.push(index);
            }
            if (index == 59 && selectedHour != 23) {
                disabledList.push(index);
            }
        }
        return disabledList;
    };
    return (
        <FormItem
            rules={[
                {
                    required: true,
                    message: '请选择活动时段',
                },
            ]}
            {...props}
            name={name}
            label={label}

            // validateStatus={timeStatus}
            // help={timeStatus === 'error' && '请填写生效时间'}
        >
            <TimeRangePicker
                disabled={disabled}
                hideDisabledOptions
                disabledTime={() => {
                    return { disabledMinutes: disabledMinutes };
                }}
                format="HH:mm"
            ></TimeRangePicker>
        </FormItem>
    );
};
export default ActTimeFormItem;
