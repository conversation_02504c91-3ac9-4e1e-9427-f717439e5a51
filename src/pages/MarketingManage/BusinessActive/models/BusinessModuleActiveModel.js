import {
    getModuleActTemplateList<PERSON>pi,
    getModuleActTemplateDetailApi,
    getMktActListApi,
    getMktActDetailApi,
    getActTemplateListApi,
    getMngMktActListApi,
    getMngMktActDetailApi,
    getActTemplateListMngApi,
    getActTemplateDetailMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { ACT_CONFIG_TYPE, ACT_MENU_TYPE } from '../ModuleActive/components/ActModuleConfig';

import {
    getMngWorkorderActDetailApi,
    getWorkorderActDetailApi,
} from '@/services/Marketing/MarketingWorkorderApi';
import { investmentActTemplateDetailApi } from '@/services/Marketing/RecruitMerchantsPlanApi';

// 模板化配置场站活动重构
const businessModuleActive = {
    namespace: 'businessModuleActiveModel',
    state: {
        moduleTemplateList: [], // 模板列表
        moduleTemplateTotal: 0, // 模板数量
        moduleTemplateInfo: undefined, // 模板详情

        actList: [], // 活动列表
        actTotal: 0, // 活动列表数
        actInfo: undefined, // 活动详情

        crwodTemplateList: [],
        cycleTemplateList: [],
        timeTemplateList: [],
    },
    effects: {
        // 模板下拉列表
        *getModuleActTemplateOptions({ templateType }, { call, put, select }) {
            try {
                const state = yield select();
                const {
                    user: { currentUser },
                } = state;
                const operType = currentUser.operId ? '01' : '02';
                const {
                    data: { records: list },
                } = yield call(getActTemplateListMngApi, { templateType: templateType, operType });

                let listKey = '';
                switch (templateType) {
                    case ACT_CONFIG_TYPE.CROWD:
                        listKey = 'crwodTemplateList';
                        break;
                    case ACT_CONFIG_TYPE.CYCLE:
                        listKey = 'cycleTemplateList';
                        break;
                    case ACT_CONFIG_TYPE.TIME:
                        listKey = 'timeTemplateList';
                        break;
                    default:
                        break;
                }
                yield put({
                    type: 'updateProperty',
                    params: {
                        [listKey]: list,
                    },
                });
            } catch (error) {}
        },
        // 模板列表
        *getModuleActTemplateList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: moduleTemplateList, total: moduleTemplateTotal },
                } = yield call(getActTemplateListMngApi, options);

                yield put({
                    type: 'updateProperty',
                    params: {
                        moduleTemplateList,
                        moduleTemplateTotal,
                    },
                });
            } catch (error) {}
        },
        // 模板详情
        *getModuleActTemplateInfo({ options }, { call, put, select }) {
            try {
                const { data: moduleTemplateInfo } = yield call(
                    getActTemplateDetailMngApi,
                    options,
                );

                yield put({
                    type: 'updateProperty',
                    params: {
                        moduleTemplateInfo,
                    },
                });
            } catch (error) {}
        },
        // 活动列表
        *getMktActList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list: actList, total: actTotal },
                } = yield call(getMktActListApi, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actList,
                        actTotal,
                    },
                });
            } catch (error) {}
        },
        *getMngMktActList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: actList, total: actTotal },
                } = yield call(getMngMktActListApi, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actList,
                        actTotal,
                    },
                });
            } catch (error) {}
        },

        /**
         * 查询详情
         */
        *initEditActInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getMktActDetailApi, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actInfo: data || undefined,
                    },
                });
            } catch (error) {}
        },
        *initMngEditActInfo({ options: opt }, { call, put, select }) {
            try {
                // 转一层，处理下兼容问题
                let options = opt || {};
                const { menuType } = options;
                if (menuType) {
                    delete options.menuType;
                }
                let api = getMngMktActDetailApi;
                if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                    options = { templateActId: opt?.actId };
                    api = investmentActTemplateDetailApi;
                }
                const { data } = yield call(api, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actInfo: data || undefined,
                    },
                });
            } catch (error) {}
        },
        // 工单看到的活动详情
        *initWorkorderEditActInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getWorkorderActDetailApi, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actInfo: data,
                    },
                });
            } catch (error) {}
        },
        *initMngWorkorderEditActInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getMngWorkorderActDetailApi, options);
                yield put({
                    type: 'updateProperty',
                    params: {
                        actInfo: data,
                    },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateProperty(state, { params: _params }) {
            let params = _params;
            if (_params.actInfo?.originalData) {
                const originalData = JSON.parse(_params.actInfo?.originalData);
                if (originalData) {
                    if (originalData.gridList) {
                        if (typeof originalData.gridList == 'string') {
                            // 解决有的时候换行符\\n被解析为\n的情况，要还原回去，否则会被转成\n
                            const key = '-临时换行占位用于替换换行符-';
                            let temp = originalData.gridList?.replaceAll('\\n', key);
                            temp = temp?.replaceAll('\n', key);
                            temp = temp?.replaceAll(key, '\\n');
                            originalData.gridList = temp;
                        }
                        originalData.gridList = JSON.parse(originalData.gridList);
                    }
                    if (originalData.mapGridList) {
                        if (typeof originalData.mapGridList == 'string') {
                            // 解决有的时候换行符\\n被解析为\n的情况，要还原回去，否则会被转成\n
                            const key = '-临时换行占位用于替换换行符-';
                            let temp = originalData.mapGridList?.replaceAll('\\n', key);
                            temp = temp?.replaceAll('\n', key);
                            temp = temp?.replaceAll(key, '\\n');
                            originalData.mapGridList = temp;
                        }
                        originalData.mapGridList = JSON.parse(originalData.mapGridList);
                    }
                }
                params.actInfo = { ..._params.actInfo, ...originalData };
                delete params.actInfo.originalData;
            }
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default businessModuleActive;
