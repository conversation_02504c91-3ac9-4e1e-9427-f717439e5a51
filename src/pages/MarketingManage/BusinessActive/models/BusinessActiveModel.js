import {
    getBusinessActiveListApi,
    getBusinessActiveDetailApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { getCityAndStationByOperIdApi } from '@/services/CommonApi';
import { getWorkorderActDetailApi } from '@/services/Marketing/MarketingWorkorderApi';

const businessActive = {
    namespace: 'businessActiveModel',
    state: {
        businessActiveList: [], // 商家营销列表
        businessActiveListTotal: 0,
        stationOptions: [], // 筛选站点列表
        editActInfo: {}, // 当前详情信息
    },
    effects: {
        /**
         * 商家营销列表
         */
        *getBusinessActiveList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBusinessActiveListApi, options);

                yield put({
                    type: 'updateBusinessActiveList',
                    businessActiveList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         *
         */
        *initStationOptions(_, { call, put, select }) {
            try {
                const { operId } = yield select((state) => state.user.currentUser);
                if (operId) {
                    const { data: cityAndStationList } = yield call(getCityAndStationByOperIdApi, {
                        operId,
                    });

                    yield put({
                        type: 'updateStationOptions',
                        list: cityAndStationList,
                    });
                }
            } catch (error) {
                console.log(7777, error);
            }
        },

        /**
         * 查询详情
         */
        *initEditActInfo({ actId }, { call, put, select }) {
            try {
                const { data } = yield call(getBusinessActiveDetailApi, actId);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
        // 工单看到的活动详情
        *initWorkorderEditActInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getWorkorderActDetailApi, options);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateBusinessActiveList(state, { businessActiveList, total }) {
            return {
                ...state,
                businessActiveList,
                businessActiveListTotal: total,
            };
        },
        updateStationOptions(state, { list }) {
            return {
                ...state,
                stationOptions: list,
            };
        },

        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default businessActive;
