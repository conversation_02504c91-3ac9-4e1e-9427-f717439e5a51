import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';
import { connect } from 'umi';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import { saveBusinessActiveApi } from '@/services/Marketing/MarketingBusinessActiveApi';
import {
    Button,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    Table,
    Switch,
} from 'antd';

import { LeftOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';

import { MODELTYPS, ADJUST_TYPS } from './BusinessConfig';
import DiscountFormItem from './DiscountFormItem';
import ActTimeAndDiscountFormItem from './ActTimeAndDiscountFormItem';
import { SELECT_TYPES, STATION_CONFIG_PAGE_TYPES } from '@/config/declare';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import ConflictModal from './ModuleActive/components/ConflictModal';

import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import BudgetFormItem from '@/components/BudgetFormItem/index';
import { BEARING_TYPES } from '@/components/BudgetFormItem/budgetConfig';
import { isEmpty } from '@/utils/utils';
import { saveStationScopeInfoApi, getStationScopeListApi } from '@/services/CommonApi';
import { stopMktActApi } from '@/services/Marketing/MarketingBusinessActiveApi';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const { confirm } = Modal;

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    OPERATOR: '2', // 运营商
    CITY: '3', // 城市
};

// 商家营销子编辑页面
export const BusinessActiveUpdatePageRander = (props) => {
    const {
        dispatch,
        currentUser,
        detalistLoading,
        global,
        businessActiveModel: { editActInfo },
        initRef,
        limitOperIds, // 活动范围限制运营商

        // 方法
        callbackJsonEvent, // 工单使用，原始数据回调
        workorderEvent, // 详情仅停止调用
        isWorkorderOrigin, // 是加载原始活动数据，还是加载工单活动数据
        goBack,

        // 数据源
        actId: actIdSign,

        // 权限项控制
        isPlat,
        isUser,
        isLock,
        isCopy,
        openSomeThing,
        login = {},
    } = props;

    const isWorkorder = !(isWorkorderOrigin || callbackJsonEvent);

    const { channelOptions, codeInfo } = global || {};
    const { adjustType: adjustTypeList } = codeInfo;
    const [actId, changeActId] = useState(actIdSign);
    useImperativeHandle(initRef, () => ({
        goBack,
    }));

    const [submitLoading, changeSubmitLoading] = useState(false);

    const [form] = Form.useForm();

    const budgetRef = useRef();
    const conflictRef = useRef();
    const [conflictParams, updateConflictParams] = useState();

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    useEffect(() => {
        return () => {
            dispatch({
                type: 'businessActiveModel/updateEditActInfo',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        initEvent(actId);
    }, [actId, isWorkorderOrigin]);

    const initEvent = (id) => {
        if (id) {
            if (isWorkorderOrigin) {
                dispatch({
                    type: 'businessActiveModel/initWorkorderEditActInfo',
                    options: { orderActId: id },
                });
            } else {
                dispatch({
                    type: 'businessActiveModel/initEditActInfo',
                    actId: id,
                });
            }
        }

        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
                actId: id,
            });
        }
        if (!adjustTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'adjustType',
            });
        }
    };

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
        }
    }, [editActInfo]);

    const channelOptionList = useMemo(
        () =>
            channelOptions.map((ele) => (
                <Checkbox key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Checkbox>
            )),
        [channelOptions],
    );

    const columns = [
        {
            title: '类型',
            dataIndex: 'discountType',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '非会员',
            dataIndex: 'discountValue',
            render(text, record) {
                let result = record.discountValue >= 0 ? `${text}${record.unit}` : '-';
                let preText = '';
                if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                    let preUnit = record.adjustMethodType === '0' ? '-' : '+';
                    const beforeStr =
                        record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                            ? '配置结算'
                            : '（结算价/原价）';
                    preText = `${beforeStr}${preUnit}`;
                }
                return (
                    <Space>
                        <span title={preText}>{preText}</span>
                        <span style={record.discountFlag ? { color: 'red' } : {}} title={result}>
                            {result}
                        </span>
                    </Space>
                );
            },
        },
        {
            title: '会员',
            dataIndex: 'vipDiscountValue',
            render(text, record) {
                let result = record.vipDiscountValue >= 0 ? `${text}${record.unit}` : '-';
                let preText = '';
                if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                    let preUnit = record.vipAdjustMethodType === '0' ? '-' : '+';
                    const beforeStr =
                        record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                            ? '配置结算'
                            : '（结算价/原价）';
                    preText = `${beforeStr}${preUnit}`;
                }
                return (
                    <Space>
                        <span title={preText}>{preText}</span>
                        <span style={record.vipDiscountFlag ? { color: 'red' } : {}} title={result}>
                            {result}
                        </span>
                    </Space>
                );
            },
        },
    ];

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const params = { ...info };
            if (info.actChannel) {
                params.actChannel =
                    info.actChannel && info.actChannel != '' ? info.actChannel.split(',') : [];
            }
            if (info.activeCrowd) {
                params.activeCrowd = !isEmpty(info.activeCrowd) ? info.activeCrowd.split(',') : [];
            } else {
                params.activeCrowd = [];
            }

            if (!params.adjustDetailList?.length > 0) {
                params.adjustDetailList = [{}];
            }

            if (isPlat && info.belongType == 'user') {
                params.belongType = undefined;
            }
            if (isUser && (info.belongType == 'plat' || info.belongType == 'oper')) {
                params.belongType = undefined;
            }

            if (info.effTime && info.expTime) {
                params.dateTime = [moment(info.effTime), moment(info.expTime)];
            }
            if (info.beginTime && info.endTime) {
                params.actTime = [moment(info.beginTime), moment(info.endTime)];
            }

            if (info.budgetMap) {
                //预算配置初始化

                for (const key in info.budgetMap) {
                    if (Object.hasOwnProperty.call(info.budgetMap, key)) {
                        const element = info.budgetMap[key];
                        params[key] = element;
                    }
                }
                if (info.budgetMap?.expenseBearing) {
                    params.expenseBearing =
                        (info.budgetMap?.expenseBearing &&
                            info.budgetMap?.expenseBearing.split(',')) ||
                        [];
                }

                if (params.budgetId && params.dateTime[0] && params.dateTime[1]) {
                    if (budgetRef?.current?.update) {
                        let options = {
                            budgetId: params.budgetId,
                            effTime: params.dateTime[0],
                            expTime: params.dateTime[1],
                            actId: actId || '',
                        };
                        budgetRef?.current?.update(options);
                    }
                }
            } else {
                params.expenseBearing = [];
            }
            if (
                info.allDayFlag == '0' &&
                info.actTimeIntervalList &&
                info.actTimeIntervalList instanceof Array
            ) {
                params.actTimeIntervalList = info.actTimeIntervalList.map((ele) => ({
                    dctValue: ele.dctValue,
                    dctCondValue: ele.dctCondValue,
                    vipDiscountValue: ele.vipDiscountValue,
                    actTime: [moment(ele.beginTime, 'HH:mm'), moment(ele.endTime, 'HH:mm')],
                    vipDiscountValueAdjust: ele.vipDiscountValueAdjust,
                    adjustDetailList: ele.adjustDetailList || [{}],
                }));
            } else {
                params.actTimeIntervalList = [];
            }
            params.discountLimitFlag = params.discountLimitFlag == '1';
            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    const changeType = () => {
        const actTimeIntervalList = form.getFieldValue('actTimeIntervalList');
        if (actTimeIntervalList.length) {
            const validateNames = [];
            actTimeIntervalList.forEach((ele, index) => {
                validateNames.push(['actTimeIntervalList', index, 'vipDiscountValue']);
                validateNames.push(['actTimeIntervalList', index, 'dctValue']);
            });
            form?.validateFields([...validateNames]);
        }
        form?.validateFields(['vipDiscountValue', 'dctValue']);
    };

    /**
     * 保存优惠券
     * type  save/send
     */

    const submitEvent = async (type, values = {}, blank) => {
        try {
            const params = {
                overlapFlag: values.overlapFlag,
                saveType: type,
                actName: values.actName,
                belongType: values.belongType,
                actMarks: values.actMarks,
                actState: values.actState,
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                allDayFlag: values.allDayFlag,

                discountLimitAmt: values.discountLimitAmt,
                discountLimitAmtDay: values.discountLimitAmtDay,
                discountLimitAmtTime: values.discountLimitAmtTime,
                actType: values.actType,
                actChannel: values.actChannel.join(','),

                dctCondValue: values.dctCondValue,
                dctValue: values.dctValue,
                vipDiscountValue: values.vipDiscountValue,
                vthreeDiscountValue: values.vthreeDiscountValue,
                subChargeFeeFlag: values.subChargeFeeFlag || '0',

                expenseBearing:
                    (values.expenseBearing &&
                        values.expenseBearing instanceof Array &&
                        values.expenseBearing.join(',')) ||
                    values.expenseBearing ||
                    '',
                budgetId: values.budgetId || '',
                budgetAmount: values.budgetAmount || 0,
                adjustPriceType: values.adjustPriceType,
                discountLimitFlag: '0',
                stationInfo: values.stationInfo || undefined,
                delScopeInfo: values.delScopeInfo || undefined,
            };
            if (values.adjustDetailList) {
                const adjustDetailList = values.adjustDetailList.map((ele, index) => {
                    return { ...ele, adjustSn: index };
                });
                params.adjustDetailList = JSON.stringify(adjustDetailList);
            }
            params.activeCrowdFlag = values.activeCrowdFlag;
            if (values.activeCrowdFlag === '0') {
                params.activeCrowd = values.activeCrowd.join(',');
            } else {
                params.activeCrowd = '';
            }

            if (values.belongType != 'oper') {
                // 活动类型为运营商时不显示该字段
                params.discountLimitFlag = (values.discountLimitFlag && '1') || '0';
            }

            if (values.allDayFlag === '0') {
                const actTimeIntervalList = values.actTimeIntervalList.map((ele) => {
                    const adjustDetailList =
                        ele?.adjustDetailList?.map((element, index) => {
                            return { ...element, adjustSn: index };
                        }) || null;
                    return {
                        dctValue: ele.dctValue,
                        dctCondValue: ele.dctCondValue,
                        vipDiscountValue: ele.vipDiscountValue,
                        vthreeDiscountValue: ele.vthreeDiscountValue,
                        beginTime: (ele.actTime && ele.actTime[0].format('HH:mm')) || '',
                        endTime: (ele.actTime && ele.actTime[1].format('HH:mm')) || '',
                        adjustDetailList: adjustDetailList,
                    };
                });

                params.actTimeIntervalList = JSON.stringify(actTimeIntervalList);
            }

            const stationOptions = {};
            const {
                addStations = [],
                allStations = [],
                defaultStations = [],
                delStations = [],
            } = values.stationConfig || {};

            stationOptions.addStationIds = addStations.map((ele) => ele.stationId);
            stationOptions.submitStationIds = allStations.map((ele) => ele.stationId);
            stationOptions.delStationIds = delStations.map((ele) => ele.stationId);
            const {
                data: { stationScopeKey },
            } = await saveStationScopeInfoApi(stationOptions);

            params.stationScopeKey = stationScopeKey;

            delete params.stationConfig;

            if (actId && !isCopy) {
                params.actId = actId;
            }

            if (!callbackJsonEvent) {
                if (values.actState != 2) {
                    if (type == 'save') {
                        params.actState = '0';
                    } else if (type == 'send') {
                        params.actState = '1';
                    }
                }
            } else {
                //工单新建如果没有活动类型的话  使用已有活动的类型提交
                if (isEmpty(values.actState)) {
                    if (isEmpty(editActInfo?.actState)) {
                        if (type == 'save') {
                            params.actState = '0';
                        } else if (type == 'send') {
                            params.actState = '1';
                        }
                    } else {
                        params.actState = editActInfo?.actState;
                    }
                }
            }

            if (callbackJsonEvent) {
                if (params.stationInfo) {
                    delete params.stationInfo;
                }
                if (params.delScopeInfo) {
                    delete params.delScopeInfo;
                }
                callbackJsonEvent(type, params, blank);
                return;
            }

            changeSubmitLoading(true);

            const {
                data: { actId: newActId, conflictActList, profitCheckStr },
            } = await saveBusinessActiveApi(params);

            if (profitCheckStr?.length) {
                Modal.confirm({ title: profitCheckStr, zIndex: 199 });
            }
            if (conflictActList?.length) {
                updateConflictParams({ type, values: { ...values, overlapFlag: 1 }, blank });
                conflictRef.current?.show(conflictActList);
                return;
            } else {
                if (conflictParams) {
                    updateConflictParams(undefined);
                }
                conflictRef.current?.onClose();
            }

            if (newActId) {
                changeActId?.(newActId);
            }

            if (type == 'save') {
                message.success('保存成功');
                // if (newActId) {
                //     dispatch({
                //         type: 'businessActiveModel/initEditActInfo',
                //         actId: newActId,
                //     });
                // }
                if (values?.budgetId) {
                    let options = {
                        budgetId: values?.budgetId || '',
                        effTime: values.dateTime[0],
                        expTime: values.dateTime[1],
                        actId: newActId,
                    };
                    budgetRef?.current?.update(options);
                }
            } else if (type == 'send') {
                message.success('提交成功');
                goBack();
            }
            if (blank) {
                window.open(
                    `${window.location.origin}${PUBLIC_PATH}marketing/businessActive/list/copy/${newActId}`,
                    'top',
                );
            }
            changeSubmitLoading(false);
        } catch (error) {
            if (budgetRef?.current?.update && values?.budgetId) {
                let options = {
                    budgetId: values.budgetId,
                    effTime: values.dateTime[0],
                    expTime: values.dateTime[1],
                    actId: actId || '',
                };
                budgetRef?.current?.update(options);
            }
        } finally {
            changeSubmitLoading(false);
        }
    };

    const saveCouponEvent = async (type, blank) => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();

            const hasVip = values.belongType == 'plat' || values.belongType == 'user';

            let confirmList = []; //提交确认提醒框内容数组

            //校验分时段或者未分时时 会员和非会员配置超过阈值的情况
            if (values.allDayFlag == 0) {
                // 分时段
                values.actTimeIntervalList?.forEach((ele) => {
                    let confimOptions = {
                        title: '',
                        children: [],
                    };
                    let discountOptions = {
                        discountValue: ele.dctValue,
                        vipDiscountValue: ele.vipDiscountValue,
                        vthreeDiscountValue: ele.vthreeDiscountValue,
                    };

                    if (values.actType == MODELTYPS.DISCOUNT) {
                        // 打折低于3折
                        if (ele.dctValue < 3 || ele.vipDiscountValue < 3) {
                            if (ele.dctValue < 3) {
                                discountOptions.discountFlag = true;
                            }
                            if (hasVip && ele.vipDiscountValue < 3) {
                                discountOptions.vipDiscountFlag = true;
                            }

                            discountOptions.discountType = '打折';
                            discountOptions.unit = '折';
                            confimOptions.children.push(discountOptions);
                        }
                    }
                    if (values.actType == MODELTYPS.REDUCTION) {
                        // 立减大于2毛
                        if (ele.dctValue > 0.2 || ele.vipDiscountValue > 0.2) {
                            if (ele.dctValue > 0.2) {
                                discountOptions.discountFlag = true;
                            }
                            if (hasVip && ele.vipDiscountValue > 0.2) {
                                discountOptions.vipDiscountFlag = true;
                            }
                            discountOptions.discountType = '立减';
                            discountOptions.unit = '元/度';
                            confimOptions.children.push(discountOptions);
                        }
                    }
                    if (values.actType == MODELTYPS.DYNAMIC) {
                        let discountList = [];

                        ele?.adjustDetailList.forEach((element) => {
                            let itemOptions = {
                                adjustType: element.adjustType,
                                discountValue: element.adjustValue,
                                vipDiscountValue: element.adjustVipValue,
                                vthreeDiscountValue: element.adjustThreeDiscountValue,
                            };
                            if (element.adjustType === ADJUST_TYPS.DISCOUNT) {
                                itemOptions.discountType = '打折';
                                itemOptions.unit = '折';
                                if (element.adjustValue < 3 || element.adjustVipValue < 3) {
                                    if (element.adjustValue < 3) {
                                        itemOptions.discountFlag = true;
                                    }
                                    if (element.adjustVipValue < 3) {
                                        itemOptions.vipDiscountFlag = true;
                                    }
                                    discountList.push(itemOptions);
                                }
                            } else if (
                                element.adjustType === ADJUST_TYPS.REDUCE ||
                                element.adjustType === ADJUST_TYPS.ADD
                            ) {
                                itemOptions.discountType =
                                    element.adjustType === ADJUST_TYPS.REDUCE ? '立减' : '立加';
                                itemOptions.unit = '元/度';
                                if (element.adjustValue > 0.2 || element.adjustVipValue > 0.2) {
                                    if (element.adjustValue > 0.2) {
                                        itemOptions.discountFlag = true;
                                    }
                                    if (element.adjustVipValue > 0.2) {
                                        itemOptions.vipDiscountFlag = true;
                                    }
                                    discountList.push(itemOptions);
                                }
                            } else if (element.adjustType === ADJUST_TYPS.DYNAMIC) {
                                itemOptions.discountType = '动态打折';
                                itemOptions.unit = '折';
                                itemOptions.adjustMethodType = element.adjustMethodType;
                                itemOptions.vipAdjustMethodType = element.vipAdjustMethodType;
                                if (element.adjustValue > 3 || element.adjustVipValue > 3) {
                                    if (element.adjustValue > 3) {
                                        itemOptions.discountFlag = true;
                                    }
                                    if (element.adjustVipValue > 3) {
                                        itemOptions.vipDiscountFlag = true;
                                    }
                                    discountList.push(itemOptions);
                                }
                            }
                            if (discountList?.length > 0) {
                                confimOptions.children = discountList;
                            }
                        });
                    }

                    if (confimOptions?.children?.length > 0) {
                        const [startTime, endTime] = ele.actTime;
                        confimOptions.title = `时段（${startTime.format('HH:mm')}-${endTime.format(
                            'HH:mm',
                        )})`;
                        confirmList.push(confimOptions);
                    }
                });
            } else {
                let confimOptions = {
                    title: '',
                    children: [],
                };

                let discountOptions = {
                    discountValue: values.dctValue,
                    vipDiscountValue: values.vipDiscountValue,
                    vthreeDiscountValue: values.vthreeDiscountValue,
                };
                if (values.actType == MODELTYPS.DISCOUNT) {
                    // 打折低于3折
                    if (values.dctValue < 3) {
                        discountOptions.discountFlag = true;
                    }
                    if (hasVip && values.vipDiscountValue < 3) {
                        discountOptions.vipDiscountFlag = true;
                    }
                    discountOptions.discountType = '打折';
                    discountOptions.unit = '折';
                    if (discountOptions.discountFlag || discountOptions.vipDiscountFlag) {
                        confimOptions.children.push(discountOptions);
                    }
                } else if (values.actType == MODELTYPS.REDUCTION) {
                    // 立减大于2毛
                    if (values.dctValue > 0.2) {
                        discountOptions.discountFlag = true;
                    }
                    if (hasVip && values.vipDiscountValue > 0.2) {
                        discountOptions.vipDiscountFlag = true;
                    }
                    discountOptions.discountType = '立减';
                    discountOptions.unit = '元/度';
                    if (discountOptions.discountFlag || discountOptions.vipDiscountFlag) {
                        confimOptions.children.push(discountOptions);
                    }
                } else if (values.actType == MODELTYPS.DYNAMIC) {
                    let discountList = [];
                    values?.adjustDetailList.forEach((element) => {
                        let itemOptions = {
                            adjustType: element.adjustType,
                            discountValue: element.adjustValue,
                            vipDiscountValue: element.adjustVipValue,
                        };
                        if (element.adjustType === ADJUST_TYPS.DISCOUNT) {
                            itemOptions.discountType = '打折';
                            itemOptions.unit = '折';
                            if (element.adjustValue < 3 || element.adjustVipValue < 3) {
                                if (element.adjustValue < 3) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.adjustVipValue < 3) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                discountList.push(itemOptions);
                            }
                        } else if (
                            element.adjustType === ADJUST_TYPS.REDUCE ||
                            element.adjustType === ADJUST_TYPS.ADD
                        ) {
                            itemOptions.discountType =
                                element.adjustType === ADJUST_TYPS.REDUCE ? '立减' : '立加';
                            itemOptions.unit = '元/度';
                            if (element.adjustValue > 0.2 || element.adjustVipValue > 0.2) {
                                if (element.adjustValue > 0.2) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.adjustVipValue > 0.2) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                discountList.push(itemOptions);
                            }
                        } else if (element.adjustType === ADJUST_TYPS.DYNAMIC) {
                            itemOptions.discountType = '动态打折';
                            itemOptions.unit = '折';
                            itemOptions.adjustMethodType = element.adjustMethodType;
                            itemOptions.vipAdjustMethodType = element.vipAdjustMethodType;
                            if (element.adjustValue > 3 || element.adjustVipValue > 3) {
                                if (element.adjustValue > 3) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.adjustVipValue > 3) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                discountList.push(itemOptions);
                            }
                        }
                        if (discountList?.length > 0) {
                            confimOptions.children = discountList;
                        }
                    });
                }

                if (confimOptions?.children?.length > 0) {
                    confirmList.push(confimOptions);
                }
            }

            if (confirmList?.length > 0) {
                confirm({
                    title: '请确认以下配置',
                    width: 800,

                    content: (
                        <div style={{ height: '500px', overflowY: 'auto' }}>
                            {confirmList.map((ele, index) => {
                                return (
                                    <Card bordered={false} key={index} title={ele.title}>
                                        <Table
                                            columns={columns}
                                            dataSource={ele.children}
                                            pagination={false}
                                        ></Table>
                                    </Card>
                                );
                            })}
                        </div>
                    ),
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        submitEvent(type, values, blank);
                    },
                });
            } else {
                submitEvent(type, values, blank);
            }
        } catch (error) {
            console.log(9999, error);
            changeSubmitLoading(false);
        } finally {
            changeSubmitLoading(false);
        }
    };

    return (
        <Card loading={detalistLoading}>
            <Form.Provider onFormFinish={(name, { values, forms }) => {}}>
                <Form
                    form={form}
                    initialValues={{
                        areaRangeType: CAPITALTYPS.OPERATOR,
                        actType: MODELTYPS.DISCOUNT,
                        activeCrowdFlag: '1', // 全部
                        belongType: isUser ? 'user' : (currentUser?.operId && 'oper') || 'plat',
                    }}
                    scrollToFirstError
                    name="basicForm"
                >
                    <div className={commonStyles['form-title']}>活动信息</div>

                    <FormItem
                        label={<span>活动名称</span>}
                        name="actName"
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
                    >
                        <Input
                            disabled={isLock}
                            maxLength={15}
                            placeholder="请输入主标题，最多15个字"
                            autoComplete="off"
                        />
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.stationConfig !== curValues.stationConfig
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const stationConfig = getFieldValue('stationConfig');
                            const { allStations } = stationConfig || {};
                            // 筛选出购电场站
                            const buyStations =
                                allStations?.filter &&
                                allStations?.filter((item) => item.cooperationType == '02');
                            const containBuy = buyStations?.length > 0;

                            return (
                                <FormItem
                                    label={<span>活动类型</span>}
                                    name="belongType"
                                    {...formItemFixedWidthLayout}
                                    rules={[
                                        {
                                            required: true,
                                            whitespace: true,
                                            message: '请选择活动类型',
                                        },
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value === 'oper' && containBuy) {
                                                    return Promise.reject(
                                                        '活动范围包含购电模式运营商时，不允许选择运营商类型',
                                                    );
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Radio.Group
                                        disabled={currentUser?.operId || isLock}
                                        onChange={(event) => {
                                            const {
                                                target: { value },
                                            } = event;
                                            if (value === 'oper') {
                                                form.setFieldsValue({
                                                    expenseBearing: [BEARING_TYPES.BUSINESS],
                                                });
                                            } else {
                                                form.setFieldsValue({
                                                    expenseBearing: [],
                                                });
                                            }
                                        }}
                                    >
                                        {(isPlat && (
                                            <>
                                                <Radio value={'plat'}>平台</Radio>
                                                <Radio value={'oper'} disabled={containBuy}>
                                                    {(containBuy && (
                                                        <Tooltip title="活动范围包含购电模式运营商时，不允许选择此类型">
                                                            <span>运营商</span>
                                                        </Tooltip>
                                                    )) || <span>运营商</span>}
                                                </Radio>
                                            </>
                                        )) ||
                                            (isUser && <Radio value={'user'}>用户</Radio>) || (
                                                <>
                                                    <Radio value={'plat'}>平台</Radio>
                                                    <Radio value={'oper'} disabled={containBuy}>
                                                        {(containBuy && (
                                                            <Tooltip title="活动范围包含购电模式运营商时，不允许选择此类型">
                                                                <span>运营商</span>
                                                            </Tooltip>
                                                        )) || <span>运营商</span>}
                                                    </Radio>
                                                    <Radio value={'user'}>用户</Radio>
                                                </>
                                            )}
                                    </Radio.Group>
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <CheckBoxGroup
                        label="投放渠道"
                        name={'actChannel'}
                        form={form}
                        selectList={channelOptions}
                        disabled={isLock}
                        required
                        wrapperCol={{ span: 8 }}
                        rules={[{ required: true, message: '请选择投放渠道' }]}
                        {...formItemLayout}
                        valueType="select"
                    ></CheckBoxGroup>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.dateTime !== curValues.dateTime
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const dateTime = getFieldValue('dateTime');
                            return (
                                <FormItem
                                    name="dateTime"
                                    label="活动有效期:"
                                    {...formItemFixedWidthLayout}
                                    rules={[
                                        { required: true, message: '请选择活动有效期' },
                                        (_) => ({
                                            validator(rule, value) {
                                                if (!value) {
                                                    return Promise.reject('');
                                                }
                                                if (!value[0]) {
                                                    return Promise.reject('请选择活动开始日期');
                                                }
                                                if (!value[1]) {
                                                    return Promise.reject('请选择活动失效日期');
                                                }
                                                if (value[1]) {
                                                    const nowTime = +new Date();
                                                    const sendEndTime = +new Date(value[1]);

                                                    if (sendEndTime < nowTime) {
                                                        return Promise.reject(
                                                            '活动失效日期不能早于当前时间',
                                                        );
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <RangePicker
                                        ranges={
                                            dateTime && dateTime[0] && dateTime[1]
                                                ? {
                                                      起始日期顺延7天: [
                                                          moment(dateTime[0]).add('7', 'day'),
                                                          moment(dateTime[1]).add('7', 'day'),
                                                      ],
                                                  }
                                                : {}
                                        }
                                        disabled={!openSomeThing}
                                        disabledTime={disabledRangeTime}
                                        showTime={{
                                            format: 'HH:mm:ss',
                                            defaultValue: [
                                                moment('00:00:00', 'HH:mm:ss'),
                                                moment('23:59:59', 'HH:mm:ss'),
                                            ],
                                        }}
                                        format="YYYY-MM-DD HH:mm:ss"
                                    />
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const belongType = getFieldValue('belongType');
                            return (
                                <FormItem
                                    label={<span>营销方式</span>}
                                    name="actType"
                                    {...formItemFixedWidthLayout}
                                    rules={[
                                        { required: true, message: '请选择营销方式' },
                                        (_) => ({
                                            validator(rule, value) {
                                                if (
                                                    belongType !== 'plat' &&
                                                    value === MODELTYPS.DYNAMIC
                                                ) {
                                                    return Promise.reject(
                                                        '非平台类型不能配置动态调价',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Radio.Group
                                        disabled={isLock}
                                        onChange={() => {
                                            changeType();
                                        }}
                                    >
                                        <Radio value={MODELTYPS.DISCOUNT}>打折</Radio>
                                        <Radio value={MODELTYPS.REDUCTION}>立减</Radio>
                                        <Radio
                                            disabled={belongType !== 'plat'}
                                            value={MODELTYPS.DYNAMIC}
                                        >
                                            动态调价
                                        </Radio>
                                        <Radio disabled value={MODELTYPS.FULL}>
                                            满减
                                        </Radio>
                                    </Radio.Group>
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        name="allDayFlag"
                        label="活动时段:"
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'1'}
                    >
                        <Radio.Group
                            disabled={isLock}
                            onChange={() => {
                                setTimeout(() => {
                                    changeType();
                                }, 500);
                            }}
                        >
                            <Radio value={'1'}>全天</Radio>
                            <Radio value={'0'}>部分时段</Radio>
                        </Radio.Group>
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.actType !== curValues.actType ||
                            prevValues.belongType !== curValues.belongType ||
                            prevValues.actTimeIntervalList !== curValues.actTimeIntervalList
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const actType = getFieldValue('actType');
                            const belongType = getFieldValue('belongType');
                            const actTimeIntervalList = getFieldValue('actTimeIntervalList');
                            return actType === MODELTYPS.DYNAMIC && belongType === 'plat' ? (
                                <FormItem
                                    key="adjustPriceType"
                                    name="adjustPriceType"
                                    initialValue={ADJUST_PRICE_TYPES.DEFAULT}
                                    rules={[{ required: true, message: '请选择' }]}
                                    {...formItemFixedWidthLayout}
                                    label={
                                        <span>
                                            价格基准
                                            <Tooltip title="参与计算折扣的基准价。选择结算价后，若场站没有结算价将自动以原价计算">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </span>
                                    }
                                >
                                    <Radio.Group
                                        disabled={isLock}
                                        onChange={() => {
                                            let list = actTimeIntervalList || [];
                                            form.validateFields([
                                                ...list.map((ele, index) => [
                                                    'actTimeIntervalList',
                                                    index,
                                                    'adjustDetailList',
                                                ]),

                                                'adjustDetailList',
                                            ]);
                                            // form.setFieldsValue({
                                            //     adjustDetailList: [{}],
                                            // });
                                        }}
                                    >
                                        <Radio value={ADJUST_PRICE_TYPES.DEFAULT}>默认</Radio>
                                        <Radio value={ADJUST_PRICE_TYPES.OPER_ORIGIN}>原价</Radio>
                                        <Radio value={ADJUST_PRICE_TYPES.PUSH}>结算价</Radio>
                                    </Radio.Group>
                                </FormItem>
                            ) : null;
                        }}
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.allDayFlag !== curValues.allDayFlag ||
                            prevValues.actType !== curValues.actType ||
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const allDayFlag = getFieldValue('allDayFlag');
                            const actType = getFieldValue('actType');
                            const belongType = getFieldValue('belongType');
                            return [
                                allDayFlag === '0' ? (
                                    <ActTimeAndDiscountFormItem
                                        form={form}
                                        name="actTimeIntervalList"
                                        label="选择时段"
                                        actType={actType}
                                        disabled={isLock}
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择活动时段',
                                            },
                                        ]}
                                        formItemLayout={{
                                            ...formItemFixedWidthLayout,
                                            ...{
                                                wrapperCol: {
                                                    span: 24,
                                                },
                                            },
                                        }}
                                        typeList={adjustTypeList}
                                        hasVip
                                        belongType={belongType}
                                    />
                                ) : (
                                    <DiscountFormItem
                                        form={form}
                                        actType={actType}
                                        disabled={isLock}
                                        formItemLayout={formItemFixedWidthLayout}
                                        hasVip
                                        belongType={belongType}
                                        typeList={adjustTypeList}
                                    />
                                ),
                                actType === MODELTYPS.REDUCTION ? (
                                    <FormItem
                                        label={
                                            <span>
                                                是否减电费
                                                <Tooltip title="配置减电费后，立减额度超出服务费部分再减电费">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        required
                                        {...formItemLayout}
                                    >
                                        <Space>
                                            <FormItem
                                                noStyle
                                                initialValue="0"
                                                name="subChargeFeeFlag"
                                                rules={[
                                                    {
                                                        required: true,

                                                        message: '请填写',
                                                    },
                                                ]}
                                            >
                                                <Radio.Group disabled={isLock}>
                                                    <Radio value="1">是</Radio>
                                                    <Radio value="0">否</Radio>
                                                </Radio.Group>
                                            </FormItem>
                                        </Space>
                                    </FormItem>
                                ) : null,
                            ];
                        }}
                    </FormItem>
                    {/* <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.discountLimitFlag !== curValues.discountLimitFlag ||
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const belongType = getFieldValue('belongType');
                            const discountLimitFlag =
                                getFieldValue('discountLimitFlag') && belongType != 'oper';

                            return (
                                (belongType != 'oper' && (
                                    <>
                                        <FormItem
                                            name="discountLimitFlag"
                                            {...formItemFixedWidthLayout}
                                            labelCol={{
                                                flex: '0 0 130px',
                                            }}
                                            label={
                                                <span>
                                                    会员优惠限制
                                                    <Tooltip title="关闭时会员优惠无上限；开启时会员优惠达上限后使用普通活动价计费">
                                                        <InfoCircleOutlined
                                                            style={{ marginLeft: '6px' }}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            }
                                            valuePropName="checked"
                                            required
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (value) {
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Switch
                                                checkedChildren="开"
                                                unCheckedChildren="关"
                                                disabled={isLock}
                                            />
                                        </FormItem>
                                        {discountLimitFlag && (
                                            <>
                                                <FormItem label="每日优惠上限" {...formItemLayout}>
                                                    <Space>
                                                        <FormItem
                                                            noStyle
                                                            name="discountLimitAmtDay"
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            rules={[
                                                                ({ getFieldValue }) => ({
                                                                    validator(rule, value) {
                                                                        const discountLimitAmtDay =
                                                                            getFieldValue(
                                                                                'discountLimitAmtDay',
                                                                            );
                                                                        const discountLimitAmtTime =
                                                                            getFieldValue(
                                                                                'discountLimitAmtTime',
                                                                            );
                                                                        const discountLimitAmt =
                                                                            getFieldValue(
                                                                                'discountLimitAmt',
                                                                            );

                                                                        if (
                                                                            discountLimitAmtDay ==
                                                                                null &&
                                                                            discountLimitAmtTime ==
                                                                                null &&
                                                                            discountLimitAmt == null
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '至少填写一项优惠上限',
                                                                            );
                                                                        }

                                                                        if (
                                                                            discountLimitAmtDay !==
                                                                                null &&
                                                                            discountLimitAmtTime !==
                                                                                null &&
                                                                            Number(
                                                                                discountLimitAmtTime,
                                                                            ) >
                                                                                Number(
                                                                                    discountLimitAmtDay,
                                                                                )
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '每日优惠要大于每次优惠上限',
                                                                            );
                                                                        }

                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                        >
                                                            <InputNumber
                                                                disabled={isLock}
                                                                precision={2}
                                                                step={0.01}
                                                                min={0.01}
                                                                max={99999}
                                                                placeholder="不填表示无上限"
                                                                style={{ width: '140px' }}
                                                            />
                                                        </FormItem>
                                                        元
                                                    </Space>
                                                </FormItem>
                                                <FormItem label="总优惠上限" {...formItemLayout}>
                                                    <Space>
                                                        <FormItem
                                                            noStyle
                                                            name="discountLimitAmt"
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            rules={[
                                                                ({ getFieldValue }) => ({
                                                                    validator(rule, value) {
                                                                        const discountLimitAmtDay =
                                                                            getFieldValue(
                                                                                'discountLimitAmtDay',
                                                                            );
                                                                        const discountLimitAmtTime =
                                                                            getFieldValue(
                                                                                'discountLimitAmtTime',
                                                                            );
                                                                        const discountLimitAmt =
                                                                            getFieldValue(
                                                                                'discountLimitAmt',
                                                                            );

                                                                        if (
                                                                            discountLimitAmtDay ==
                                                                                null &&
                                                                            discountLimitAmtTime ==
                                                                                null &&
                                                                            discountLimitAmt == null
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '至少填写一项优惠上限',
                                                                            );
                                                                        }
                                                                        if (
                                                                            discountLimitAmtDay !==
                                                                                null &&
                                                                            discountLimitAmt !==
                                                                                null &&
                                                                            Number(
                                                                                discountLimitAmtDay,
                                                                            ) >
                                                                                Number(
                                                                                    discountLimitAmt,
                                                                                )
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '总优惠要大于每日优惠上限',
                                                                            );
                                                                        }

                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                        >
                                                            <InputNumber
                                                                disabled={isLock}
                                                                precision={2}
                                                                step={0.01}
                                                                min={0.01}
                                                                max={99999}
                                                                placeholder="不填表示无上限"
                                                                style={{ width: '140px' }}
                                                            />
                                                        </FormItem>
                                                        元
                                                    </Space>
                                                </FormItem>
                                            </>
                                        )}
                                    </>
                                )) ||
                                null
                            );
                        }}
                    </FormItem> */}

                    {/* <FormItem
                        label={<span>优惠时效</span>}
                        wrapperCol={{ span: 16 }}
                        rules={[{ required: true, message: '请填写副标题名称' }]}
                    >
                        <Radio.Group>
                            <Radio> 不限</Radio>
                            <Radio>
                                <Space>
                                    首次参与后
                                    <FormItem noStyle name="">
                                        <InputNumber min={1} precision={0} step={1}></InputNumber>
                                    </FormItem>
                                    天
                                </Space>
                            </Radio>
                        </Radio.Group>
                    </FormItem> */}
                    {/* <FormItem
                        label={<span>是否叠加</span>}
                        rules={[{ required: true, message: '请填写副标题名称' }]}
                    >
                        <Radio.Group>
                            <Radio> 叠加</Radio>
                            <Radio> 不叠加</Radio>
                        </Radio.Group>
                    </FormItem> */}

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.dateTime !== curValues.dateTime ||
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const dateTime = getFieldValue('dateTime');
                            const belongType = getFieldValue('belongType');
                            return (
                                <BudgetFormItem
                                    ref={budgetRef}
                                    form={form}
                                    effTime={dateTime && dateTime[0]}
                                    expTime={dateTime && dateTime[1]}
                                    belongType={belongType}
                                    disabled={isLock}
                                    currentUser={currentUser}
                                    rules={
                                        (editActInfo?.actState === '2' && {
                                            expenseBearing: [],
                                        }) ||
                                        false
                                    }
                                ></BudgetFormItem>
                            );
                        }}
                    </FormItem>

                    <div className={commonStyles['form-title']}>活动范围</div>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const belongType = getFieldValue('belongType');
                            return (
                                <Fragment>
                                    {/* <ActGroupCheckbox
                                            disabled={isLock}
                                            valueType="select"
                                            defaultCheckedAll={editActInfo?.activeCrowdFlag == '1'}
                                            label="活动人群"
                                            form={form}
                                            required
                                            customerOnly={belongType == 'user'}
                                            {...formItemFixedWidthLayout}
                                        /> */}
                                    <ActiveCrowdCheckTree
                                        disabled={isLock}
                                        label="活动人群"
                                        form={form}
                                        required
                                        onlyIds={
                                            belongType === 'user'
                                                ? ['01', '02', '03', '04', '05', '06', '07']
                                                : undefined
                                        }
                                        {...formItemFixedWidthLayout}
                                    />
                                </Fragment>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const belongType = getFieldValue('belongType');

                            let recordParamsConfig = {};
                            if (isWorkorder) {
                                recordParamsConfig = {
                                    recordParams: {
                                        relateId: actId,
                                        scene: 'stc_station_market',
                                    },
                                };
                            }
                            return (
                                <FormItem
                                    label="活动范围"
                                    name={'stationConfig'}
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                const { addStations, allStations } = value;
                                                if (isEmpty(allStations) && isEmpty(addStations)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                if (
                                                    addStations.length > 20000 ||
                                                    allStations.length > 20000
                                                ) {
                                                    return Promise.reject(
                                                        '一次性最多配置2万个站点',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <SearchStationItem
                                        title="活动范围"
                                        limitOperIds={limitOperIds}
                                        disabled={!openSomeThing}
                                        required
                                        purchase={
                                            belongType == 'oper'
                                                ? SELECT_TYPES.EXCEPTBUY
                                                : SELECT_TYPES.ALL
                                        }
                                        currentUser={currentUser}
                                        hasStastics
                                        requestInfo={
                                            actId &&
                                            !isEmpty(editActInfo) && {
                                                listApi: getStationScopeListApi,
                                                params: {
                                                    scopeRelateId: actId,
                                                    scopeRelateType:
                                                        callbackJsonEvent && isWorkorderOrigin
                                                            ? STATION_CONFIG_PAGE_TYPES.WORKER
                                                            : STATION_CONFIG_PAGE_TYPES.BUSINESS_1,
                                                },
                                                ...recordParamsConfig,
                                            }
                                        }
                                        isCopy={isCopy}
                                    ></SearchStationItem>
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <div className={commonStyles['form-submit']}>
                        {isWorkorder && !isLock && editActInfo?.actState < 1 && (
                            <Button
                                className={commonStyles['form-btn-left']}
                                type="primary"
                                loading={submitLoading}
                                onClick={() => {
                                    saveCouponEvent('save');
                                }}
                            >
                                保存
                            </Button>
                        )}
                        {(!isLock || openSomeThing) && (
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('send');
                                    }}
                                >
                                    提交
                                </Button>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('send', true);
                                    }}
                                >
                                    提交并继续添加
                                </Button>
                                {((editActInfo?.actState == 1 || editActInfo?.actState == 2) &&
                                    editActInfo?.operType != 1 && // 工单的新增类型不可改
                                    !isCopy && (
                                        <Button
                                            className={commonStyles['form-btn']}
                                            loading={submitLoading}
                                            onClick={() => {
                                                confirm({
                                                    title: `确定停止${editActInfo.actName}活动?`,
                                                    icon: <ExclamationCircleOutlined />,
                                                    content: '',
                                                    okText: '是',
                                                    okType: 'danger',
                                                    cancelText: '否',
                                                    onOk: async () => {
                                                        try {
                                                            if (workorderEvent) {
                                                                workorderEvent('stop', {
                                                                    ...editActInfo,
                                                                    actId,
                                                                });
                                                            } else {
                                                                await stopMktActApi({
                                                                    actId,
                                                                    parentActFlag:
                                                                        editActInfo.parentActFlag,
                                                                    categoryType: '01',
                                                                });
                                                                goBack();
                                                            }
                                                        } catch (error) {}
                                                    },
                                                    onCancel() {
                                                        console.log('Cancel');
                                                    },
                                                });
                                            }}
                                            danger
                                        >
                                            停止
                                        </Button>
                                    )) ||
                                    null}
                            </Fragment>
                        )}

                        <Button className={commonStyles['form-btn']} onClick={goBack}>
                            返回
                        </Button>
                    </div>
                </Form>
            </Form.Provider>

            <ConflictModal
                initRef={conflictRef}
                onFinish={() => {
                    submitEvent(
                        conflictParams?.type,
                        conflictParams?.values,
                        conflictParams?.blank,
                    );
                }}
            />
        </Card>
    );
};

const BusinessActiveUpdatePage = (props) => {
    const {
        match,
        history,
        route,
        businessActiveModel: { editActInfo },
    } = props;

    const ref = useRef();

    const actId = match.params.actId || null;

    const isPlat = route.path.indexOf('/plat') > -1;
    const isUser = route.path.indexOf('/user') > -1;
    const isLock = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return true;
        }
        if (route.path.indexOf('/update') >= 0 && editActInfo?.actState >= 2) {
            return true;
        }
        return false;
    }, [editActInfo]); // 是否可编辑
    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy') >= 0) {
            return true;
        }
        return false;
    }, []);
    const openSomeThing = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return false;
        }
        return !editActInfo || editActInfo?.actState == 2 || !isLock;
    }, [editActInfo, isLock]); // 是否可编辑

    const goBack = () => {
        // history.go(-1);
        history.replace('/marketing/businessActive/moduleAct');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={ref?.current?.goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <BusinessActiveUpdatePageRander
                {...props}
                actId={actId}
                initRef={ref}
                isPlat={isPlat}
                isUser={isUser}
                isLock={isLock}
                isCopy={isCopy}
                openSomeThing={openSomeThing}
                goBack={goBack}
            />
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, businessActiveModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    businessActiveModel,
    detalistLoading:
        loading.effects['businessActiveModel/initEditActInfo'] ||
        loading.effects['businessActiveModel/initWorkorderEditActInfo'],
}))(BusinessActiveUpdatePage);
