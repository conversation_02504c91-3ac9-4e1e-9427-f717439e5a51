.dynamic-delete-button {
  position: relative;
  margin-bottom: 24px;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.mark-text {
  color: #999;
}

.actModule-table{
    width: 100%;
    overflow-x: auto;
    vertical-align: top;
    td{
        vertical-align: top;
    }
    :global{
        .ant-dropdown-menu-title-content{
            overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                word-break: keep-all;
        }
    }
    // .table-pre-title{
    //     max-width: 50%;
    // }
}
.actModule-table-view{
    min-width: 1000px;
}







// @media screen and (max-width: 1600px){

// .table-pre-title{
//     max-width: 100px;
// }
// }
