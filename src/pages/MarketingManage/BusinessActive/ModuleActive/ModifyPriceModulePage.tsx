import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Col,
    Form,
    Input,
    Popconfirm,
    Row,
    Select,
    Space,
    Switch,
    Tabs,
    message,
    Alert,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { PlusOutlined } from '@ant-design/icons';
import {
    getCityTemplateListApi,
    getStationTemplateListApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { MODULE_TYPES } from './components/declare';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import ModifyPriceModuleModal from './components/ModifyPriceModuleModal';

const { Link } = Typography;

const formItemLayout: any = {
    labelCol: { flex: '0 0 100px' },
    labelAlign: 'right',
};
const TabsOptions = [
    {
        key: '01',
        label: '站点模板',
    },
    {
        key: '02',
        label: '城市模板',
    },
];
const ListPage: React.FC = () => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string | number>(TabsOptions[0].key);
    const [visible, setVisible] = useState(false);
    const [detailParams, setDetailParams] = useState({});
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const request =
                currentTab === '01' ? getStationTemplateListApi : getCityTemplateListApi;

            const response = await request({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        return params;
    };

    const onFinish = () => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const changeTabType = (value: string) => {
        form.resetFields();
        setCurrentTab(value);
    };

    const stationColumns: ColumnsType = [
        {
            title: '省份',
            width: 120,
            dataIndex: 'provinceName',
        },
        {
            title: '城市',
            width: 120,
            dataIndex: 'cityName',
        },
        {
            title: '运营商',
            width: 200,
            dataIndex: 'operatorName',
        },
        {
            title: '站点ID',
            width: 120,
            dataIndex: 'stationId',
        },
        {
            title: '站点名称',
            width: 200,
            dataIndex: 'stationName',
        },
        {
            title: '模板详情',
            width: 400,
            render: (text, record: any, index) => {
                const timeList = record?.detailVos?.map((item: any) => {
                    return `${item?.timePeriodFlagName}时段：${item?.timePeriod}`;
                });
                const timeListStr = timeList?.join('，');

                return <span style={{ wordBreak: 'break-all' }}>{timeListStr}</span>;
            },
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creator',
        },

        {
            title: '操作',
            width: 200,
            dataIndex: 'templateId',
            sorter: false,
            fixed: 'right',
            render: (text, record: any, index) => {
                const btnList = [];
                const lookBtn = (
                    <Button
                        type="link"
                        onClick={() => {
                            setVisible(true);
                            setDetailParams({ stationId: record?.stationId });
                        }}
                    >
                        配置
                    </Button>
                );

                btnList.push(lookBtn);

                return <Space>{btnList}</Space>;
            },
        },
    ];
    const cityColumns: ColumnsType = [
        {
            title: '模板ID',
            width: 120,
            dataIndex: 'priceAdjustTimePeriodTemplateId',
        },
        {
            title: '城市',
            width: 160,
            dataIndex: 'cityName',
        },
        {
            title: '模板详情',
            width: 400,
            render: (text, record: any, index) => {
                const timeList = record?.detailVos?.map((item: any) => {
                    return `${item?.timePeriodFlagName}时段：${item?.timePeriod}`;
                });
                const timeListStr = timeList?.join('，');

                return <span style={{ wordBreak: 'break-all' }}>{timeListStr}</span>;
            },
        },
        {
            title: '创建时间',
            width: 160,
            dataIndex: 'created',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creator',
        },
        {
            title: '操作',
            width: 200,
            dataIndex: '',
            sorter: false,
            fixed: 'right',
            render: (text, record: any, index) => {
                const btnList = [];
                const lookBtn = (
                    <Button
                        type="link"
                        onClick={() => {
                            setVisible(true);
                            setDetailParams({ city: record?.city });
                        }}
                    >
                        配置
                    </Button>
                );

                btnList.push(lookBtn);

                return <Space>{btnList}</Space>;
            },
        },
    ];
    const onAdd = () => {
        setVisible(true);
    };
    return (
        <>
            <Alert
                type="info"
                showIcon
                message={
                    <Space direction="vertical" size={4}>
                        <Typography.Text>
                            时段模板用于【调价工具】应用时段时使用{' '}
                            <Link
                                onClick={() => {
                                    window.open(
                                        `https://alidocs.dingtalk.com/i/nodes/r1R7q3QmWe7LLBY3udZE66jMJxkXOEP2?doc_type=wiki_doc`,
                                    );
                                }}
                            >
                                详细说明
                            </Link>
                        </Typography.Text>
                        <Typography.Text>
                            每个城市仅允许最多一个模板，每个站点仅允许最多一个模板
                        </Typography.Text>
                    </Space>
                }
            />
            <Tabs defaultActiveKey={TabsOptions[0].key as string} onChange={changeTabType}>
                {TabsOptions.map((v) => (
                    <Tabs.TabPane tab={v.label} key={v.key} />
                ))}
            </Tabs>
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    {currentTab === '01' && (
                        <Col span={8}>
                            <Form.Item label="省份" name="provinceName">
                                <Input
                                    maxLength={20}
                                    allowClear
                                    placeholder="请输入省份"
                                    autoComplete="off"
                                />
                            </Form.Item>
                        </Col>
                    )}
                    {currentTab === '02' && (
                        <Col span={8}>
                            <Form.Item label="模板ID" name="priceAdjustTimePeriodTemplateId">
                                <Input allowClear placeholder="请输入模板ID" autoComplete="off" />
                            </Form.Item>
                        </Col>
                    )}
                    <Col span={8}>
                        <Form.Item label="城市" name="cityName">
                            <Input
                                maxLength={20}
                                allowClear
                                placeholder="请输入城市"
                                autoComplete="off"
                            />
                        </Form.Item>
                    </Col>
                    {currentTab === '01' && (
                        <>
                            <Col span={8}>
                                <OperSelectItem
                                    form={form}
                                    name="operatorId"
                                    label="运营商"
                                    formItemLayout={formItemLayout}
                                />
                            </Col>
                            <Col span={8}>
                                <Form.Item label="站点ID" name="stationId">
                                    <Input
                                        maxLength={16}
                                        allowClear
                                        placeholder="请输入站点ID"
                                        autoComplete="off"
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="站点名称" name="stationName">
                                    <Input
                                        maxLength={16}
                                        allowClear
                                        placeholder="请输入站点名称"
                                        autoComplete="off"
                                    />
                                </Form.Item>
                            </Col>
                        </>
                    )}
                </SearchOptionsBar>
            </Form>
            {currentTab === '02' && (
                <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginBottom: '20px' }}>
                    <Col>
                        <Button type="primary" icon={<PlusOutlined />} onClick={() => onAdd()}>
                            添加模板
                        </Button>
                    </Col>
                </Row>
            )}

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="templateId"
                dataSource={listData?.list}
                columns={currentTab === '01' ? stationColumns : cityColumns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
                tabType={currentTab}
            />
            {visible && (
                <ModifyPriceModuleModal
                    visible={visible}
                    detailParams={detailParams}
                    currentTab={currentTab}
                    onSuccess={() => {
                        setVisible(false);
                        setDetailParams('');
                        onFinish();
                    }}
                    onClose={() => {
                        setVisible(false);
                        setDetailParams('');
                    }}
                />
            )}
        </>
    );
};

export default ListPage;
