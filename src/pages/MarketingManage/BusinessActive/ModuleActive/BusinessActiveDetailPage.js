import { PageHeaderWrapper } from '@ant-design/pro-layout';
import React, { useEffect, useState, useRef, useImperativeHandle, Fragment, useMemo } from 'react';
import { connect, Link } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import { Card, Divider, Descriptions, Button, Typography } from 'antd';

import { LeftOutlined } from '@ant-design/icons';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import { getStationScopeListApi } from '@/services/CommonApi';
import { STATION_CONFIG_PAGE_TYPES } from '@/config/declare';

import ActiveDetailChannelTable from './components/ActiveDetailChannelTable';
import { ACT_MENU_TYPE, BusinessActiveType } from './components/ActModuleConfig';
import moment from 'moment';
import { isEmpty } from '@/utils/utils';

// 商家营销子编辑页面
export const BusinessActiveDetailPageRander = (props) => {
    const {
        dispatch,
        currentUser,
        detalisLoading,
        global,
        businessModuleActiveModel: { actInfo = {} },
        initRef,
        history,

        // 方法
        isWorkorderOrigin, // 是加载原始活动数据，还是加载工单活动数据
        goBack,
        defaultMeueType,

        // 数据源
        actId: actIdSign,
    } = props;

    const {
        location: { pathname },
    } = history;
    const isOldPage = window.location.href?.indexOf('businessActive/temp') >= 0;

    const { channelOptions, codeInfo } = global || {};
    const { adjustType: adjustTypeList } = codeInfo;
    const [actId, changeActId] = useState(actIdSign);
    useImperativeHandle(initRef, () => ({
        goBack,
    }));

    useEffect(() => {
        return () => {
            dispatch({
                type: 'businessModuleActiveModel/updateProperty',
                params: {
                    actInfo: undefined,
                },
            });
        };
    }, []);

    const menuType = useMemo(() => {
        if (defaultMeueType) {
            return defaultMeueType;
        }
        if (Object.keys(actInfo)?.length) {
            return BusinessActiveType(actInfo, pathname);
        }
        return BusinessActiveType(undefined, pathname);
    }, [actInfo, defaultMeueType]);

    useEffect(() => {
        initEvent(actId);
    }, [actId, isWorkorderOrigin, menuType]);

    const initEvent = (id) => {
        if (id) {
            if (isWorkorderOrigin) {
                dispatch({
                    type: isOldPage
                        ? 'businessModuleActiveModel/initWorkorderEditActInfo'
                        : 'businessModuleActiveModel/initMngWorkorderEditActInfo',
                    options: { orderActId: id },
                });
            } else {
                dispatch({
                    type: isOldPage
                        ? 'businessModuleActiveModel/initEditActInfo'
                        : 'businessModuleActiveModel/initMngEditActInfo',
                    options: {
                        actId: id,
                        categoryType: menuType == ACT_MENU_TYPE.USER_GROUP ? '02' : '01',
                        menuType,
                    },
                });
            }
        }

        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
                actId: id,
            });
        }
        if (!adjustTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'adjustType',
            });
        }
    };

    const contributName = useMemo(() => {
        let name = '未知';
        switch (actInfo?.contributParty) {
            case '01':
                name = '运营商';
                break;
            case '02':
                name = '平台';
                break;
            case '04':
                name = `混合出资（运营商${actInfo?.operatorContributRatio}%，平台${
                    (isEmpty(actInfo?.platformContributRatio) &&
                    actInfo?.operatorContributRatio !== 0
                        ? (100.0 - parseFloat(actInfo?.operatorContributRatio)).toFixed(2)
                        : actInfo?.platformContributRatio) || '0.00'
                }%）`;
                break;

            default:
                break;
        }
        return name;
    }, [actInfo]);
    const financeDepListName = useMemo(() => {
        const { financeDepList = '[]' } = actInfo;
        const depName = JSON.parse(financeDepList) || [];
        const nameList = depName.map(
            (item) => `${item?.departmentName || item.label}: ${item?.contributRatio || 0}%`,
        );
        return nameList.join(',');
    }, [actInfo]);

    useEffect(() => {
        if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && actInfo) {
            // 招商是直接json存储的，所以要转个name出来反显
            let isChanged = false;
            if (actInfo?.actChannel && !actInfo?.actChannelName) {
                const names = [];
                const actChannel =
                    typeof actInfo?.actChannel == 'string'
                        ? actInfo?.actChannel.split(',')
                        : actInfo.actChannel;
                if (Array.isArray(actChannel)) {
                    channelOptions?.forEach((ele) => {
                        if (actChannel.includes(ele.codeValue)) {
                            names.push(ele.codeName);
                        }
                    });
                }
                actInfo.actChannelName = names.join(',');
                isChanged = true;
            }
            if (actInfo?.gridList?.[0] && !actInfo?.gridList?.[0].actChannelName) {
                actInfo.gridList[0].actChannelName = '平台渠道';
                isChanged = true;
            }
            if (actInfo?.mapActChannel && !actInfo?.mapActChannelName) {
                const names = [];
                const mapActChannel =
                    typeof actInfo?.mapActChannel == 'string'
                        ? actInfo?.mapActChannel.split(',')
                        : actInfo.mapActChannel;
                if (Array.isArray(mapActChannel)) {
                    channelOptions?.forEach((ele) => {
                        if (mapActChannel.includes(ele.codeValue)) {
                            names.push(ele.codeName);
                        }
                    });
                }
                actInfo.mapActChannelName = names.join(',');
                isChanged = true;
            }
            if (actInfo?.mapGridList?.[0]?.actChannel) {
                for (const mapItem of actInfo?.mapGridList) {
                    if (!mapItem) {
                        continue;
                    }
                    if (!mapItem.actChannelName) {
                        const names = [];
                        const mapActChannel =
                            typeof mapItem.actChannel == 'string'
                                ? mapItem.actChannel.split(',')
                                : mapItem.actChannel;
                        if (Array.isArray(mapActChannel)) {
                            channelOptions?.forEach((ele) => {
                                if (mapActChannel.includes(ele.codeValue)) {
                                    names.push(ele.codeName);
                                }
                            });
                        }

                        mapItem.actChannelName = names.join(',');
                        isChanged = true;
                    }
                }
            }
            if (isChanged) {
                dispatch({
                    type: 'businessModuleActiveModel/updateProperty',
                    params: {
                        actInfo,
                    },
                });
            }
        }
    }, [actInfo, channelOptions, menuType]);

    const createFormRender = useMemo(() => {
        if (actInfo?.createFromType == 'adjust_price') {
            return (
                <Link
                    to={`/marketing/readjust/detail?priceAdjustTaskId=${actInfo?.createFromId}`}
                    target="__blank"
                >
                    调价工具｜{actInfo?.createFromId}
                </Link>
            );
        } else if (actInfo?.createFromType == 'work_order') {
            return '工单';
        } else {
            return '无来源';
        }
    }, [actInfo]);

    return (
        <Card loading={detalisLoading}>
            <div className={commonStyles['form-title']}>基础信息</div>

            <Descriptions>
                {(menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && (
                    <Fragment>
                        <Descriptions.Item label="模板名称">
                            {actInfo?.templateActName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="模板概述">
                            {actInfo?.templateActRemarks || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="状态">
                            {(actInfo?.templateActStatus === '01' && '启用') ||
                                (actInfo?.templateActStatus == '09' && '停用') ||
                                '-'}
                        </Descriptions.Item>
                    </Fragment>
                )) || (
                    <Fragment>
                        <Descriptions.Item label="活动名称">
                            {actInfo?.actName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="活动有效期">
                            {((actInfo?.effTime || actInfo?.expTime) && (
                                <div>
                                    <span>{`${actInfo?.effTime || '-'}`} 至</span>
                                    <br />
                                    <span>{`${actInfo?.expTime || '-'}`}</span>
                                </div>
                            )) ||
                                '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="状态">
                            {actInfo?.actStateName || '-'}
                        </Descriptions.Item>
                    </Fragment>
                )}
                {(menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                    menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) && (
                    <Descriptions.Item label="活动出资">{contributName}</Descriptions.Item>
                )}
                {(menuType == ACT_MENU_TYPE.USER_GROUP ||
                    menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                    menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) &&
                    (actInfo?.operatorContributRatio < 100 || actInfo?.contributParty === '02') && (
                        <Descriptions.Item label="出资部门">{financeDepListName}</Descriptions.Item>
                    )}
                {(menuType == ACT_MENU_TYPE.ACT_2 || menuType == ACT_MENU_TYPE.COMPANY_ACT) && (
                    <Descriptions.Item label="活动类型">
                        {actInfo?.belongTypeName || '-'}
                    </Descriptions.Item>
                )}
                {(menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && (
                    <Fragment>
                        <Descriptions.Item label="创建时间">
                            {moment(actInfo?.createdTime).format('YYYY-MM-DD HH:mm:ss') || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="创建人">
                            {actInfo?.createdBy || '-'}
                        </Descriptions.Item>
                    </Fragment>
                )) || (
                    <Fragment>
                        <Descriptions.Item label="创建时间">
                            {actInfo?.creTime || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="创建人">
                            {actInfo?.creEmp || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="停止时间">
                            {actInfo?.closeTime || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="停止人">
                            {actInfo?.closeEmp || '-'}
                        </Descriptions.Item>
                        {menuType == ACT_MENU_TYPE.COMPANY_ACT && (
                            <Descriptions.Item label="创建方">
                                {actInfo?.createSourceName || '-'}
                            </Descriptions.Item>
                        )}
                        <Descriptions.Item label="生效时间">
                            {actInfo?.linkActTimeStr || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="活动平台">
                            {actInfo?.cooperationPlatformName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="创建来源">{createFormRender}</Descriptions.Item>
                    </Fragment>
                )}
            </Descriptions>

            <Divider />
            <div className={commonStyles['form-title']}>活动配置</div>

            <Descriptions>
                <Descriptions.Item label="平台渠道" span={3}>
                    {actInfo?.actChannelName || '-'}
                </Descriptions.Item>
                {menuType == ACT_MENU_TYPE.USER_GROUP ? undefined : (
                    <Descriptions.Item label="图商渠道" span={1}>
                        {actInfo?.mapActChannelName || '-'}
                    </Descriptions.Item>
                )}

                {menuType == ACT_MENU_TYPE.USER_GROUP ? undefined : (
                    <Descriptions.Item label="是否独立配置" span={2}>
                        {actInfo?.independentConfig === '1' ? '是' : '否'}
                    </Descriptions.Item>
                )}
                <Descriptions.Item span={3}>
                    <ActiveDetailChannelTable {...props} actInfo={actInfo} menuType={menuType} />
                </Descriptions.Item>
                {/* {menuType == ACT_MENU_TYPE.USER_GROUP || menuType == ACT_MENU_TYPE.COMPANY_ACT
                    ? undefined
                    : (actInfo?.discountLimitFlagName && (
                          <Descriptions.Item label="会员优惠限制">
                              {actInfo?.discountLimitFlagName}
                          </Descriptions.Item>
                      )) ||
                      null}
                {menuType == ACT_MENU_TYPE.USER_GROUP || menuType == ACT_MENU_TYPE.COMPANY_ACT
                    ? undefined
                    : (actInfo?.discountLimitFlag == '1' && (
                          <Fragment>
                              <Descriptions.Item label="每日优惠上限">
                                  {actInfo?.discountLimitAmtDay || '-'}
                              </Descriptions.Item>
                              <Descriptions.Item label="总优惠上限">
                                  {actInfo?.discountLimitAmt || '-'}
                              </Descriptions.Item>
                          </Fragment>
                      )) ||
                      null} */}
            </Descriptions>

            {menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ? undefined : (
                <Fragment>
                    <Divider />
                    <div className={commonStyles['form-title']}>活动范围</div>

                    <SearchStationItem
                        title="活动范围"
                        disabled
                        currentUser={currentUser}
                        hasStastics
                        requestInfo={
                            actId &&
                            actInfo && {
                                listApi: getStationScopeListApi,
                                params: {
                                    scopeRelateId: actId,
                                    scopeRelateType: isWorkorderOrigin
                                        ? STATION_CONFIG_PAGE_TYPES.WORKER
                                        : STATION_CONFIG_PAGE_TYPES.BUSINESS_2,
                                },
                                recordParams: {
                                    relateId: actId,
                                    scene: 'stc_station_market',
                                },
                            }
                        }
                        actInfo={actInfo}
                        menuType={menuType}
                        // 价格估算显示
                        isPriceEstimation={
                            menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                            menuType == ACT_MENU_TYPE.COMPANY_ACT
                        }
                        isPrizeOptimize={menuType == ACT_MENU_TYPE.BASIC_PRICE}
                    />
                </Fragment>
            )}

            <div style={{ textAlign: 'center' }}>
                <Button className={commonStyles['form-btn']} onClick={goBack}>
                    返回
                </Button>
            </div>
        </Card>
    );
};

const BusinessActiveDetailPage = (props) => {
    const {
        match,
        history,
        route,
        businessModuleActiveModel: { actInfo },
    } = props;

    const {
        location: { pathname },
    } = history;

    const ref = useRef();

    const actId = match.params.actId || null;

    const goBack = () => {
        const lastMenuType = props.location?.state?.menuType || props.location?.query?.redirect;
        if (lastMenuType) {
            let path = 'businessActive/moduleAct';
            // 如果是由新页面发起的复制
            if (lastMenuType == ACT_MENU_TYPE.USER_GROUP) {
                path = 'businessActive/module-user-group';
            } else if (lastMenuType == ACT_MENU_TYPE.BASIC_PRICE) {
                path = 'businessActive/module-basic-price';
            } else if (lastMenuType == ACT_MENU_TYPE.COMPANY_ACT) {
                path = 'merchantActivityManagement/module-company-act';
            } else if (lastMenuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                path = 'merchantActivityManagement/recruitMerchantsPlan/template';
            }
            history.replace(`/marketing/${path}`);
        } else if (route.path.indexOf('/merchantActivityManagement/recruitMerchantsPlan') >= 0) {
            // 招商的需求
            const backPath = '/marketing/merchantActivityManagement/recruitMerchantsPlan/template';
            history.replace(backPath);
        } else {
            // 返回，前提是path固定三层
            const paths = pathname.split('/');
            const backPath = paths.slice(0, 4)?.join('/');
            history.replace(backPath);
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={ref?.current?.goBack}>
                    <LeftOutlined />
                    {actInfo?.actName ? `${actInfo?.actName}详情` : route.name}
                </div>
            }
        >
            <BusinessActiveDetailPageRander
                {...props}
                actId={actId}
                initRef={ref}
                goBack={goBack}
            />
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, businessModuleActiveModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    businessModuleActiveModel,
    detalisLoading:
        loading.effects['businessModuleActiveModel/initEditActInfo'] ||
        loading.effects['businessModuleActiveModel/initMngEditActInfo'],
}))(BusinessActiveDetailPage);
