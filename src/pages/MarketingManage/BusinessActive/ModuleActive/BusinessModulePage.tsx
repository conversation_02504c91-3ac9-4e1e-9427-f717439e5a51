import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Col,
    Form,
    Input,
    Popconfirm,
    Row,
    Select,
    Space,
    Switch,
    Tabs,
    message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { PlusOutlined } from '@ant-design/icons';
import BusinessModuleModal from './components/businessModuleModal';
import {
    deleteMktActTemplateApi,
    getMktActTemplatePageApi,
    updateSortMktActTemplateApi,
    updateStatusMktActTemplateApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { MODULE_TYPES } from './components/declare';

const TabsOptions = [
    {
        key: '',
        label: '全部',
    },
    {
        key: '01',
        label: '已启用',
    },
    {
        key: '09',
        label: '已禁用',
    },
];
const ListPage: React.FC = () => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string | number>(TabsOptions[0].key);
    const [visible, setVisible] = useState(false);
    const [changeStatusLoading, setChangeStatusLoading] = useState(false);
    const [id, setId] = useState('');
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getMktActTemplatePageApi({
                ...params,
                pageSize,
                pageIndex: current,
                createSource: '02',
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        if (currentTab && currentTab !== TabsOptions[0].key) {
            params.availableStatus = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };
    const confirmChangeStatus = (templateId: number, status: string) => {
        setChangeStatusLoading(true);
        updateStatusMktActTemplateApi({
            templateId,
            availableStatus: status,
        })
            .then(() => {
                message.success('操作成功');
                onFinish();
            })
            .finally(() => setChangeStatusLoading(false));
    };
    const onDelete = (templateId: number) => {
        deleteMktActTemplateApi(templateId).then(() => {
            message.success('操作成功');
            onFinish();
        });
    };
    const { run: changePositionRequest, loading: changePositionLoading } = useRequest(
        (currentId, newId) => {
            return updateSortMktActTemplateApi({
                templateId: currentId,
                sortedTemplateId: newId,
            });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('操作成功');
                    onFinish();
                } else {
                    message.error(res?.msg || '操作失败');
                }
            },
            onError: () => {
                message.error('操作失败');
            },
        },
    );
    const onMove = (record: any, index: number) => {
        const { list = [] } = listData;
        changePositionRequest(record?.templateId, list[index]?.templateId);
    };
    const columns: ColumnsType = [
        {
            title: '模板分类',
            width: 120,
            dataIndex: 'templateTypeName',
            fixed: 'left',
        },
        {
            title: '模板ID',
            width: 120,
            dataIndex: 'templateId',
            fixed: 'left',
        },
        {
            title: '模板名称',
            width: 200,
            dataIndex: 'templateName',
            fixed: 'left',
        },

        {
            title: '模板内容',
            width: 400,
            ellipsis: true,
            dataIndex: 'templateDetail',
        },
        {
            title: '推荐文案',
            width: 400,
            ellipsis: true,
            dataIndex: 'recommendDesc',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createdTime',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'availableStatus',
            render(text, record) {
                return (
                    <Popconfirm
                        title={`${text === '01' ? '是否禁用？' : '是否启用？'}?`}
                        onConfirm={() => {
                            confirmChangeStatus(record?.templateId, text === '01' ? '09' : '01');
                        }}
                    >
                        <Switch
                            checked={text === '01'}
                            checkedChildren="启用"
                            unCheckedChildren="禁用"
                            loading={changeStatusLoading}
                        />
                    </Popconfirm>
                );
            },
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'templateId',
            sorter: false,
            fixed: 'right',
            render: (id, record, index) => {
                return (
                    <Space>
                        <a
                            onClick={() => {
                                setVisible(true);
                                setId(record?.templateId);
                            }}
                        >
                            编辑
                        </a>
                        {record?.availableStatus === '09' && (
                            <Popconfirm
                                title={`确定要删除此模板？`}
                                onConfirm={() => {
                                    onDelete(record?.templateId);
                                }}
                            >
                                <a>删除</a>
                            </Popconfirm>
                        )}
                        {/* 只有选中已启用，并且当前模板是已启用状态，才可改顺序 */}
                        {index !== 0 && record?.availableStatus == '01' && currentTab == '01' && (
                            <Button
                                type="link"
                                loading={changePositionLoading}
                                onClick={() => onMove(record, index - 1)}
                            >
                                上移
                            </Button>
                        )}
                        {index !== listData?.list?.length - 1 &&
                            record?.availableStatus == '01' &&
                            currentTab == '01' && (
                                <Button
                                    type="link"
                                    loading={changePositionLoading}
                                    onClick={() => onMove(record, index + 1)}
                                >
                                    下移
                                </Button>
                            )}
                    </Space>
                );
            },
        },
    ];
    const onAdd = () => {
        setVisible(true);
    };
    return (
        <>
            {visible && (
                <BusinessModuleModal
                    visible={visible}
                    id={id}
                    onSuccess={() => {
                        setVisible(false);
                        setId('');
                        onFinish();
                    }}
                    onClose={() => {
                        setVisible(false);
                        setId('');
                    }}
                />
            )}
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <Form.Item label="模板名称" name="templateName">
                            <Input
                                maxLength={16}
                                allowClear
                                placeholder="请输入模板名称"
                                autoComplete="off"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="模板分类" name="templateType">
                            <Select
                                allowClear
                                placeholder="请选择模板分类"
                                options={MODULE_TYPES}
                            />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginBottom: '20px' }}>
                <Col>
                    <Button type="primary" icon={<PlusOutlined />} onClick={() => onAdd()}>
                        新建模板
                    </Button>
                </Col>
            </Row>
            <Tabs defaultActiveKey={TabsOptions[0].key as string} onChange={changeTabType}>
                {TabsOptions.map((v) => (
                    <Tabs.TabPane tab={v.label} key={v.key} />
                ))}
            </Tabs>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="templateId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
                tabType={currentTab}
            />
        </>
    );
};

export default ListPage;
