import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Tabs } from 'antd';
import ModuleManagePage from './ModuleManagePage';
import BusinessModulePage from './BusinessModulePage';
import ModifyPriceModulePage from './ModifyPriceModulePage';
const { TabPane } = Tabs;
const Index = () => {
    // 判断是否单独显示商家端模板
    const isBusinessModule =
        window.location.href?.indexOf('/merchantActivityManagement/businessModule') >= 0;

    return (
        <PageHeaderWrapper>
            <Card>
                {isBusinessModule ? (
                    <BusinessModulePage />
                ) : (
                    <Tabs>
                        <TabPane tab="平台模板" key={1}>
                            <ModuleManagePage />
                        </TabPane>
                        <TabPane tab="商家端模板" key={2}>
                            <BusinessModulePage />
                        </TabPane>
                        <TabPane tab="调价模板" key={3}>
                            <ModifyPriceModulePage />
                        </TabPane>
                    </Tabs>
                )}
            </Card>
        </PageHeaderWrapper>
    );
};
export default Index;
