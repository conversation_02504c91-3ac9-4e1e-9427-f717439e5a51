import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';
import { connect, Link } from 'umi';
import debounce from 'lodash/debounce';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import styles from './BusinessActiveUpdatePage.less';
import SelectChannelItem, { filterKeyList } from '@/components/SelectChannelItem/index';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Radio';

import {
    checkOperatorsProfit,
    saveMktActApi,
    saveMngMktActApi,
    stopMngMktActApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { getStationScopeListApi } from '@/services/CommonApi';
import {
    Button,
    Row,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    Switch,
    Divider,
    Typography,
} from 'antd';

import {
    LeftOutlined,
    InfoCircleOutlined,
    SwapOutlined,
    ExclamationCircleOutlined,
} from '@ant-design/icons';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import {
    MODELTYPS,
    ADJUST_TYPS,
    ACT_MENU_TYPE,
    BusinessActiveType,
} from './components/ActModuleConfig';
import { SELECT_TYPES, STATION_CONFIG_PAGE_TYPES, ADJUST_PRICE_TYPES } from '@/config/declare';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';

import { BEARING_TYPES } from '@/components/BudgetFormItem/budgetConfig';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import ActModuleTableFormItem from './components/ActModuleTableFormItem';
import { CONFIG_MODEL } from './components/ActModuleConfig';
import ConflictModal from './components/ConflictModal';
import ActiveDetailChannelTable from './components/ActiveDetailChannelTable';

import { stopMktActApi } from '@/services/Marketing/MarketingBusinessActiveApi';
import { saveStationScopeInfoApi } from '@/services/CommonApi';
import { ACTIVE_CONFIG_TYPE } from './components/declare';
import { hasVthreeDiscountValue } from './verify';
import { queryCityDiscountLimitInfo } from '@/services/Marketing/BusinessTaskApi';
import { formatActivePage } from '@/utils/utils';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';
import FundingDepartmentForm from './components/FundingDepartmentForm';
import { investmentActTemplateSaveApi } from '@/services/Marketing/RecruitMerchantsPlanApi';
const { confirm } = Modal;

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    OPERATOR: '2', // 运营商
    CITY: '3', // 城市
};

// 商家营销子编辑页面
export const BusinessActiveUpdatePageRander = (props) => {
    const {
        dispatch,
        currentUser,
        detalistLoading,
        global,
        businessModuleActiveModel: { actInfo },
        initRef,
        limitOperIds, // 活动范围限制运营商
        // 方法
        callbackJsonEvent, // 工单使用，原始数据回调
        workorderEvent, // 详情仅停止调用
        isWorkorderOrigin, // 是加载原始活动数据，还是加载工单活动数据
        opers = [],
        goBack,
        defaultValues, // 工单预填入信息
        defaultMeueType, // 工单传入的策略类型

        // 定价调优
        isPrizeOptimize, // 是否定焦调优流程
        prizeOptimizeStaticTimes,
        prizeOptimizeCallback, // 定价调优提交按钮的回调，回调最原始的form数据，但是要先过本地校验

        // 数据源
        actId: actIdSign,
        // 权限项控制
        isPlat,
        isUser,
        isLock,
        isCopy: isCopySign, // 传入的拷贝字段，如果保存完成后，要覆盖掉
        openSomeThing,
        history,
    } = props;

    const {
        location: { pathname },
    } = history;

    const isWorkorder = !(isWorkorderOrigin || callbackJsonEvent);
    const isOldPage = window.location.href?.indexOf('businessActive/temp') >= 0;

    const { channelOptions, codeInfo } = global || {};
    const [commonChannelOptions, updateCommonChannelOptions] = useState([]);
    const [mapChannelOptions, updateMapChannelOptions] = useState([]);
    const { adjustType: adjustTypeList } = codeInfo;
    const [actId, changeActId] = useState(actIdSign);
    const [isCopy, changeIsCopy] = useState(isCopySign);
    const conflictRef = useRef();
    const [conflictParams, updateConflictParams] = useState();

    useImperativeHandle(initRef, () => ({
        goBack: goBack,
    }));

    const [submitLoading, changeSubmitLoading] = useState(false);

    const [form] = Form.useForm();
    const cooperationPlatform = Form.useWatch('cooperationPlatform', form);
    const budgetRef = useRef();
    const prizeOptimizeStationRef = useRef();

    const configType = Form.useWatch('configType', form);

    const configTypeName = useMemo(() => {
        let name = '';
        if (configType === CONFIG_MODEL.CYCLE) {
            name = '按人群配置';
        } else if (configType === CONFIG_MODEL.CROWD) {
            name = '按周期配置';
        }
        return name;
    }, [configType]);

    const payEleOper = useMemo(() => {
        return opers.find((ele) => ele.cooperationType == '02');
    }, [opers]);

    const payEleOperListNames = useMemo(() => {
        return opers
            .filter((ele) => ele.cooperationType == '02' && ele.operNickname)
            .map((ele) => ele.operNickname)
            .join('、');
    }, [opers]);

    const toggleConfigType = () => {
        let newValue = CONFIG_MODEL.CROWD;
        if (configType === CONFIG_MODEL.CROWD) {
            newValue = CONFIG_MODEL.CYCLE;
        } else if (configType === CONFIG_MODEL.CYCLE) {
            newValue = CONFIG_MODEL.CROWD;
        }
        form.setFieldsValue({
            configType: newValue,
            gridList: '',
            mapGridList: '',
        });
    };

    const reloadGridList = (params) => {
        form.setFieldsValue({
            ...params,
            gridList: '',
            mapGridList: '',
        });
    };
    const range = (start, end) => {
        const result = [];
        for (let i = start; i < end; i++) {
            result.push(i);
        }
        return result;
    };
    const disabledRangeTime = (date, type) => {
        // const momentDate = moment(date).format('YYYY-MM-DD');
        // const currentDate = moment().format('YYYY-MM-DD');
        // const currentHour = moment().hour();
        // const currentMinute = moment().minute();
        if (type === 'start') {
            return {
                // disabledHours: () => {
                //     if (momentDate === currentDate) {
                //         return range(0, 24).splice(0, currentHour);
                //     }
                // },
                // disabledMinutes: () => {
                //     if (momentDate === currentDate && moment() >= moment(date)) {
                //         return range(0, 60).splice(0, currentMinute + 1);
                //     }
                // },
                // disabledSeconds: () => {
                //     return range(0, 60).splice(1, 60);
                // },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    return range(0, 60).splice(0, 59);
                },
            };
        }
        return null;
    };
    const disabledDate = (current) => {
        return current && current < moment().subtract(1, 'day').endOf('day');
    };
    useEffect(() => {
        return () => {
            dispatch({
                type: 'businessModuleActiveModel/updateProperty',
                params: {
                    actInfo: undefined,
                },
            });
        };
    }, []);

    useEffect(() => {
        initEvent(actId);
    }, [actId, isWorkorderOrigin]);

    const [isInit, setInitFlag] = useState(true);
    useEffect(() => {
        if (form && defaultValues && isInit) {
            const params = { ...defaultValues };
            params.menuType = menuType;
            form.setFieldsValue(params);
            if (isPrizeOptimize && prizeOptimizeStationRef.current) {
                prizeOptimizeStationRef.current?.setAllStations(
                    defaultValues?.stationConfig?.defaultStations,
                );
            }
            setInitFlag(false);
        }
    }, [defaultValues, form, menuType, isPrizeOptimize, prizeOptimizeStationRef]);

    useEffect(() => {
        if (
            commonChannelOptions instanceof Array &&
            commonChannelOptions.length > 0 &&
            (!actId || menuType != ACT_MENU_TYPE.ACT_2)
        ) {
            form.setFieldsValue({
                actChannel: filterKeyList(commonChannelOptions, '新电途').map(
                    (ele) => ele.codeValue,
                ),
            });
        }
    }, [commonChannelOptions, menuType]);

    const initEvent = (id) => {
        if (id) {
            if (isWorkorderOrigin) {
                dispatch({
                    type: isOldPage
                        ? 'businessModuleActiveModel/initWorkorderEditActInfo'
                        : 'businessModuleActiveModel/initMngWorkorderEditActInfo',
                    options: { orderActId: id },
                });
            } else {
                dispatch({
                    type: isOldPage
                        ? 'businessModuleActiveModel/initEditActInfo'
                        : 'businessModuleActiveModel/initMngEditActInfo',
                    options: {
                        actId: id,
                        categoryType: menuType == ACT_MENU_TYPE.USER_GROUP ? '02' : '01',
                        menuType,
                    },
                });
            }
        } else {
            const params = { menuType };
            form.setFieldsValue(params);
        }

        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
                actId: id,
            });
        }
        if (!adjustTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'adjustType',
            });
        }
    };

    useEffect(() => {
        const commonList = [];
        const mapList = [];

        channelOptions?.forEach((ele) => {
            if (ele.codeName?.indexOf('高德') >= 0) {
                mapList.push({ label: '高德', value: ele.codeValue });
            } else if (ele.codeName?.indexOf('百度') >= 0) {
                mapList.push({ label: '百度', value: ele.codeValue });
            } else {
                commonList.push(ele);
            }
        });
        updateCommonChannelOptions(commonList);
        updateMapChannelOptions(mapList);
        if (mapList?.length) {
            form.setFieldsValue({ mapActChannel: mapList?.map((ele) => ele.value) });
        }
    }, [channelOptions]);

    useEffect(() => {
        if (actInfo) {
            initEditInfo(actInfo);
        }
    }, [actInfo]);

    const menuType = useMemo(() => {
        let m;
        if (defaultMeueType) {
            m = defaultMeueType;
        }
        // 如果是复制，以当前路径为准，不以详情返回的menuType为准
        else if (actInfo && !isCopy) {
            m = BusinessActiveType(actInfo, pathname);
        } else {
            m = BusinessActiveType(undefined, pathname);
        }
        form.setFieldsValue({ menuType: m });
        return m;
    }, [actInfo, defaultMeueType, isCopy]);

    const isSimpleControl = useMemo(() => {
        // 是否是简化版的底部操作，整合了定价调优和招商模板的展示
        return isPrizeOptimize || menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE;
    }, [isPrizeOptimize, menuType]);

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const params = { ...info };
            if (!params.menuType) {
                params.menuType = BusinessActiveType(info);
            }
            if (!defaultMeueType && isCopy) {
                // 复制场景，以当前menu为准
                params.menuType = menuType;
            } else if (defaultMeueType) {
                // 工单场景，以传入的为准
                params.menuType = menuType;
            }

            params.actChannel =
                info.actChannel && info.actChannel != '' ? info.actChannel.split(',') : [];
            if (info.mapActChannel) {
                params.mapActChannel =
                    info.mapActChannel && info.mapActChannel != ''
                        ? info.mapActChannel.split(',')
                        : [];
            }

            if (info.activeCrowd) {
                params.activeCrowd =
                    info.activeCrowd && info.activeCrowd != '' ? info.activeCrowd.split(',') : [];
            } else {
                params.activeCrowd = [];
            }

            if (params.menuType == ACT_MENU_TYPE.ACT_1 || params.menuType == ACT_MENU_TYPE.ACT_2) {
                if (isPlat && info.belongType == 'user') {
                    params.belongType = undefined;
                }
                if (isUser && (info.belongType == 'plat' || info.belongType == 'oper')) {
                    params.belongType = undefined;
                }
            } else if (!params.belongType) {
                if (
                    params.menuType == ACT_MENU_TYPE.USER_GROUP ||
                    params.menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                    params.menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE
                ) {
                    params.belongType = 'plat';
                } else if (params.menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                    params.belongType = 'oper';
                }
            }

            if (info.effTime && info.expTime) {
                // 复制的页面详情去除有效日期数据，工单的提交并添加，需要延用上一条保存成功的活动有效期，「提交并继续添加」要把上条活动的日期自动带进来
                if (!isCopy || callbackJsonEvent || props.location?.query?.f == 1) {
                    params.dateTime = [moment(info.effTime), moment(info.expTime)];
                }
            }
            if (info.beginTime && info.endTime) {
                params.actTime = [moment(info.beginTime), moment(info.endTime)];
            }
            if (info.synPlatChannel) {
                params.synPlatChannel = info.synPlatChannel === '1' ? true : false;
            }

            let temp_ = ['01', '02'];

            if (info.mapGridList instanceof Array && info.mapGridList.length > 0) {
                let channelList = info.mapGridList.reduce((a, b) => {
                    const itemChannelList = b?.actChannel?.split(',').filter((ele) => ele);
                    return a.concat(itemChannelList);
                }, []);
                params.mapActChannel = [...new Set(channelList)];
                if (info.mapGridList.length > 1 && info.independentConfig === '1') {
                    if (info.synPlatChannel !== '1') {
                        params.aloneConfig = true;
                    }
                }
            }

            if (!info.mapGridList || info.mapGridList.length == 0) {
                if (info.synPlatChannel === '0') {
                    temp_ = temp_.filter((ele) => ele != '02');
                }
            }

            if (!info.gridList || info.gridList.length == 0) {
                temp_ = temp_.filter((ele) => ele != '01');
            } else if (params.menuType == ACT_MENU_TYPE.BASIC_PRICE) {
                // 如果是基础价格补贴或者商家活动类型，且配置了活动人群，进入判断，固化人群
                if (
                    params.configType == ACTIVE_CONFIG_TYPE.CYCLE ||
                    params.gridList?.[0]?.activeCrowdFlag == '0'
                ) {
                    // 如果是按周期配置，或者按人群+部分人群的情况
                    params.configType == ACTIVE_CONFIG_TYPE.CROWD;
                    params.gridList = '';
                } else if (params.gridList?.length > 1) {
                    params.gridList = [params.gridList?.[0]];
                }
            } else if (params.menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                params.gridList?.map((ele) => {
                    if (info.crowdType) {
                        // 新活动，必定有crowdType，要反显出来
                        ele.activeCrowdFlag = info.crowdType;
                    } else if (isCopy && ele.activeCrowdFlag == '1') {
                        // 复制了旧的活动，则flag清空
                        // ele.activeCrowdFlag = undefined;
                    }
                    // 其他情况为查看，不予处理，旧的活动显示人群为全部，新的活动显示人群为平台人群/商家人群
                });
            }

            if (params.menuType == ACT_MENU_TYPE.USER_GROUP) {
                // 人群没有图商，平台渠道默认全选
                params.actChannel = commonChannelOptions?.map?.((ele) => ele.codeValue);
                delete params.synPlatChannel;
                delete params.aloneConfig;
                temp_ = ['01'];
            }
            params.temp_ = temp_;

            if (isCopy && params.menuType == ACT_MENU_TYPE.BASIC_PRICE && !params.contributParty) {
                params.contributParty = '02';
            }
            params.discountLimitFlag = params.discountLimitFlag == '1';
            params.financeDepList = JSON.parse(params?.financeDepList || '[]');
            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    const formatTemplateItemEvent = (list) => {
        if (list instanceof Array) {
            const childReg = /^[a-zA-Z]*List$/;

            for (let index = 0; index < list.length; index++) {
                const element = list[index];
                const childKeys = Object.keys(element);

                if (isEmpty(element['activeCrowdFlag'])) {
                    if (isEmpty(element['activeCrowd'])) {
                        element['activeCrowdFlag'] = '1';
                    }
                }
                if (element['cycleValueFlag']) {
                    if (isEmpty(element['cycleValue'])) {
                        element['cycleValueFlag'] = '1';
                    }
                    if (element['cycleValueFlag'] === '1') {
                        // 由于招商需求上，后端直接json存储，如果要转name的情况，后端代码改动太大，沟通后前端帮忙处理
                        element['cycleValueName'] = '每天';
                    }
                }
                if (element['allDayFlag']) {
                    if (isEmpty(element['timePeriods'])) {
                        element['allDayFlag'] = '1';
                    }
                    if (element['allDayFlag'] == '1') {
                        // 由于招商需求上，后端直接json存储，如果要转name的情况，后端代码改动太大，沟通后前端帮忙处理
                        element['allDayFlagName'] = '全天';
                    }
                }

                const keyChildIndex = childKeys.findIndex((ele) => {
                    return childReg.test(ele) && !isEmpty(element[ele]);
                });

                const childKeyValue = childKeys[keyChildIndex];

                if (childKeyValue === 'discountList') {
                } else {
                    if (
                        childKeyValue &&
                        element[childKeyValue] &&
                        element[childKeyValue] instanceof Array
                    ) {
                        formatTemplateItemEvent(element[childKeyValue]);
                    }
                }
            }
        }
    };

    const saveStations = async (values) => {
        try {
            const stationOptions = {};
            const {
                addStations = [],
                allStations = [],
                delStations = [],
            } = values.stationConfig || {};

            stationOptions.addStationIds = isPrizeOptimize
                ? defaultValues?.stationConfig?.defaultStations?.map((ele) => ele.stationId) // 定价调整会先带入城市，且不可修改
                : addStations.map((ele) => ele.stationId);
            stationOptions.submitStationIds = isPrizeOptimize
                ? defaultValues?.stationConfig?.defaultStations?.map((ele) => ele.stationId) // 定价调整会先带入城市，且不可修改
                : allStations.map((ele) => ele.stationId);
            stationOptions.delStationIds = delStations.map((ele) => ele.stationId);

            if (
                stationOptions.addStationIds?.length ||
                stationOptions.submitStationIds?.length ||
                stationOptions.delStationIds?.length
            ) {
                const {
                    data: { stationScopeKey },
                } = await saveStationScopeInfoApi(stationOptions);

                return stationScopeKey;
            }
            return undefined;
        } catch (error) {
            return undefined;
        }
    };

    // 检验活动场站的是否存在分润运营商，以及是否配了价格基准为商家结算价，若都有则红字提示，并且不能提交活动。
    const checkStationInfo = async () => {
        if (
            (menuType != ACT_MENU_TYPE.BASIC_PRICE &&
                menuType != ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) ||
            (isLock && !openSomeThing)
        ) {
            // 【基础价格补贴】限制分润场站配活动时价格基准不能选结算价，详情也跳过
            return;
        }
        const checkingFlag = form.getFieldValue('checkingFlag');
        try {
            const values = form.getFieldsValue();

            if (values.gridList?.length) {
                formatTemplateItemEvent(values.gridList);
            }
            // 只有priceType == 03，商家结算价的时候，才需要校验所选场站是否包含分润运营商，触发接口调用
            if (
                values.gridList?.some(({ cycleList }) =>
                    cycleList?.some(({ periodsList }) =>
                        periodsList?.some(({ discountList }) =>
                            discountList?.some(
                                (ele) => ele.adjustPriceType == ADJUST_PRICE_TYPES.PUSH,
                            ),
                        ),
                    ),
                )
            ) {
                if (checkingFlag != '1') {
                    form.setFieldsValue({ checkingFlag: '1', checkingMsg: undefined });
                    form.validateFields(['gridList']);

                    const stationScopeKey = await saveStations(values);
                    if (stationScopeKey) {
                        const params = { stationScopeKey };
                        const { data } = await checkOperatorsProfit(params);
                        form.setFieldsValue({
                            checkingFlag: '0',
                            checkingMsg: data,
                        });
                        return;
                    }
                }
            }
            // 提示报错
            form.setFieldsValue({ checkingFlag: '0', checkingMsg: undefined });
        } catch (error) {
            form.setFieldsValue({ checkingFlag: '0', checkingMsg: undefined });
        } finally {
            form.validateFields(['gridList']);
        }
    };

    /**
     * 保存优惠券
     * type  save/send
     */

    const submitEvent = async (
        type,
        values = {},
        blank,
        replaceFlag,
        jumpCheckProfit, // 是否跳过校验
    ) => {
        try {
            let crowdType = values.crowdType;
            const gridList =
                (Array.isArray(values.gridList) && copyObjectCommon(values.gridList)) || [];
            if (
                menuType == ACT_MENU_TYPE.COMPANY_ACT &&
                values.gridList?.length &&
                (!crowdType || crowdType !== '0') // 选的不是全部
            ) {
                crowdType = values.gridList[0]?.['activeCrowdFlag'];
                if (crowdType == '1') {
                    crowdType = undefined;
                } else {
                    gridList.map((ele) => {
                        if (ele.activeCrowdFlag == '01' || ele.activeCrowdFlag == '02') {
                            ele.activeCrowdFlag = '0';
                        }
                    });
                }
            }
            const params = {
                cooperationPlatform: values.cooperationPlatform,
                overlapFlag: values.overlapFlag,
                replaceFlag,
                saveType: type,
                categoryType: menuType == ACT_MENU_TYPE.USER_GROUP ? '02' : '01',
                parentActFlag: '1',
                configType: values.configType,
                actName: values.actName,
                belongType: values.belongType,
                actMarks: values.actMarks,
                actState: values.actState,
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',

                discountLimitAmt: values.discountLimitAmt,
                discountLimitAmtDay: values.discountLimitAmtDay,
                discountLimitAmtTime: values.discountLimitAmtTime,
                actType: values.actType,
                gridList: JSON.stringify(gridList),
                mapGridList: JSON.stringify(values.mapGridList),
                synPlatChannel: values.synPlatChannel ? '1' : '0',
                stationInfo: values.stationInfo || undefined,
                delScopeInfo: values.delScopeInfo || undefined,
                independentConfig: values.aloneConfig ? '1' : '0',
                operatorContributRatio: values.operatorContributRatio,
                platformContributRatio: values.platformContributRatio,
                contributParty: values.contributParty,
                menuType: values.menuType,
                financeDepList: values?.financeDepList
                    ? JSON.stringify(values?.financeDepList)
                    : undefined,
                temp_: values.temp_,
                crowdType,
            };

            if (values.actChannel) {
                params.actChannel = values.actChannel.join(',');
            }
            if (values?.temp_?.includes('02')) {
                if (values.mapActChannel) {
                    params.mapActChannel = values.mapActChannel.join(',');
                }
            } else {
                delete params.mapActChannel;
            }

            if (values.belongType != 'oper') {
                // 活动类型为运营商时不显示该字段
                params.discountLimitFlag = (values.discountLimitFlag && '1') || '0';
            }
            if (menuType == ACT_MENU_TYPE.BASIC_PRICE) {
                params.onlyCheckProfit = !jumpCheckProfit;
            }

            if (type === 'params') {
                return params;
            }
            if (menuType != ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                // 招商不包含场站
                const stationScopeKey = await saveStations(values);
                params.stationScopeKey = stationScopeKey;

                if (actId && !isCopy) {
                    params.actId = actId;
                }
            } else {
                if (values.templateActId && !isCopy) {
                    params.templateActId = values.templateActId;
                }
                params.templateActType = '02'; // 基础就是02  组队就是11
                params.templateActStatus = '01'; // 01启用 09停用
                params.originalData = JSON.stringify(params);
                params.templateActName = values.templateActName;
                params.templateActRemarks = values.templateActRemarks;

                delete params.gridList;
                delete params.mapGridList;
                delete params.financeDepList;
            }
            delete params.stationConfig;

            if (!callbackJsonEvent) {
                if (values.actState != 2) {
                    if (type == 'save') {
                        params.actState = '0';
                    } else if (type == 'send') {
                        params.actState = '1';
                    }
                }
            } else {
                //工单新建如果没有活动类型的话  使用已有活动的类型提交
                if (isEmpty(values.actState)) {
                    if (isEmpty(actInfo?.actState)) {
                        if (type == 'save') {
                            params.actState = '0';
                        } else if (type == 'send') {
                            params.actState = '1';
                        }
                    } else {
                        params.actState = actInfo?.actState;
                    }
                }
            }

            if (callbackJsonEvent) {
                if (params.stationInfo) {
                    delete params.stationInfo;
                }
                if (params.delScopeInfo) {
                    delete params.delScopeInfo;
                }
                callbackJsonEvent(type, params, blank);
                return;
            }

            if (prizeOptimizeCallback) {
                if (params.stationInfo) {
                    delete params.stationInfo;
                }
                if (params.delScopeInfo) {
                    delete params.delScopeInfo;
                }
                prizeOptimizeCallback({
                    stationId: defaultValues?.stationId,
                    pageValues: values,
                    submitValues: params,
                });
                return;
            }
            // console.log(params, values);
            // return;
            changeSubmitLoading(true);

            let api = isOldPage ? saveMktActApi : saveMngMktActApi;
            if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                api = investmentActTemplateSaveApi;
            }
            let {
                data: { actId: newActId, conflictActList, profitCheckStr, templateActId },
            } = await api(params);
            if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                newActId = templateActId;
            }

            if (profitCheckStr?.length && params?.onlyCheckProfit) {
                Modal.confirm({
                    title: profitCheckStr,
                    zIndex: 199,
                    onOk: () => {
                        submitEvent(type, values, blank, replaceFlag, true);
                    },
                });
                return;
            }
            if (conflictActList?.length) {
                updateConflictParams({ type, values: { ...values, overlapFlag: 1 }, blank });
                conflictRef.current?.show(conflictActList);
                return;
            } else {
                if (conflictParams) {
                    updateConflictParams(undefined);
                }
                conflictRef.current?.onClose();
            }
            if (type == 'save') {
                if (newActId) {
                    changeActId?.(newActId);
                }
                changeIsCopy(false);
                message.success('保存成功');
                if (values?.budgetId) {
                    let options = {
                        budgetId: values?.budgetId || '',
                        effTime: values.dateTime[0],
                        expTime: values.dateTime[1],
                        actId: newActId,
                    };
                    budgetRef?.current?.update(options);
                }
            } else if (type == 'send') {
                message.success('提交成功');
                goBack();
            }
            if (blank) {
                const lastMenuType =
                    props.location?.state?.menuType || props.location?.query?.redirect || menuType;
                let path = 'businessActive/moduleAct';
                // 如果是由新页面发起的复制
                if (lastMenuType == ACT_MENU_TYPE.USER_GROUP) {
                    path = 'businessActive/module-user-group';
                } else if (lastMenuType == ACT_MENU_TYPE.BASIC_PRICE) {
                    path = 'businessActive/module-basic-price';
                } else if (lastMenuType == ACT_MENU_TYPE.COMPANY_ACT) {
                    path = 'merchantActivityManagement/module-company-act';
                }
                // 带个f，标识下是从「提交并继续添加」进来的，做特殊处理
                window.open(
                    `${window.location.origin}${PUBLIC_PATH}marketing/${path}/copy/${newActId}?redirect=${lastMenuType}&f=1`,
                    'top',
                );
            }
            changeSubmitLoading(false);
        } catch (error) {
            if (budgetRef?.current?.update && values?.budgetId) {
                let options = {
                    budgetId: values.budgetId,
                    effTime: values.dateTime[0],
                    expTime: values.dateTime[1],
                    actId: actId || '',
                };
                budgetRef?.current?.update(options);
            }
        } finally {
            changeSubmitLoading(false);
        }
    };

    // 渠道选择的外显文案编辑事件外露方法
    const channelEditRef = useRef();
    const mapChannelEditRef = useRef();
    const [saveFlag, setSaveFlag] = useState(false);
    const saveCouponEvent = async (type, blank) => {
        if (submitLoading) {
            return;
        }
        try {
            channelEditRef.current?.finishEdit();
            mapChannelEditRef.current?.finishEdit();

            if (type === 'save') {
                setSaveFlag(true);
                const list = ['actName', 'belongType', 'dateTime'];
                if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                    list.push('gridList');
                }
                await form.validateFields(list);
            } else {
                setSaveFlag(false);
                await form.validateFields();
            }
            const values = form.getFieldsValue();
            console.debug('saveCouponEvent ==> values', values);
            const { financeDepList = [] } = values;
            if (financeDepList.length > 0) {
                const newData = financeDepList.map((item) => item.contributRatio);
                const sum = newData.reduce((a, b) => a * 1 + b * 1, 0);
                if (sum !== 100) {
                    message.error('出资部门比例总和必须等于100%');
                    return false;
                }
            }

            let hasVip = values.belongType == 'plat' || values.belongType == 'user';
            if (menuType == ACT_MENU_TYPE.USER_GROUP) {
                hasVip = false;
            }

            const checkDiscountItemEvent = (list, preTableText = '') => {
                console.debug('checkDiscountItemEvent ==> list', list);
                if (list instanceof Array) {
                    const childReg = /^[a-zA-Z]*List$/;

                    for (let index = 0; index < list.length; index++) {
                        const element = list[index];
                        const childKeys = Object.keys(element);

                        let tableIndexText = preTableText;
                        const nameKeys = ['activeCrowdName', 'cycleValueName', 'timePeriods'];
                        for (const keyItem of nameKeys) {
                            if (element[keyItem]) {
                                tableIndexText += element[keyItem];
                            }
                        }

                        const keyChildIndex = childKeys.findIndex((ele) => {
                            return childReg.test(ele) && !isEmpty(element[ele]);
                        });

                        const childKeyValue = childKeys[keyChildIndex];

                        if (childKeyValue === 'discountList') {
                            if (element && element['discountList'][0]) {
                                let messageerror = formatDistcountConfirmList(
                                    element['discountList'][0],
                                );

                                if (messageerror?.length > 0) {
                                    element['discountList'][0].desc = messageerror;
                                }
                            }
                        } else {
                            if (
                                childKeyValue &&
                                element[childKeyValue] &&
                                element[childKeyValue] instanceof Array
                            ) {
                                checkDiscountItemEvent(element[childKeyValue], tableIndexText);
                            }
                        }
                    }
                }
            };

            const formatDistcountConfirmList = (discountInfo) => {
                let confirmList = []; //提交确认提醒框内容数组
                //校验分时段或者未分时时 会员和非会员配置超过阈值的情况
                console.debug('formatDistcountConfirmList ==> discountInfo', discountInfo);

                let discountOptions = {
                    discountValue: discountInfo.dctValue || 0,
                    vthreeDiscountValue: (hasVip && discountInfo.vthreeDiscountValue) || undefined,
                    vipDiscountValue: (hasVip && discountInfo.vipDiscountValue) || undefined,
                    proVipDiscountValue: (hasVip && discountInfo.proVipDiscountValue) || undefined,
                };
                if (
                    discountInfo.actType == MODELTYPS.DISCOUNT ||
                    (menuType == ACT_MENU_TYPE.USER_GROUP &&
                        discountInfo.actType == MODELTYPS.CROWDPLLICY_DISCOUNT)
                ) {
                    // 打折低于3折
                    if (discountInfo.dctValue < 3) {
                        discountOptions.discountFlag = true;
                    }
                    if (hasVip && discountInfo.vthreeDiscountValue < 3) {
                        discountOptions.v3DiscountFlag = true;
                    }
                    if (hasVip && discountInfo.vipDiscountValue < 3) {
                        discountOptions.vipDiscountFlag = true;
                    }
                    if (hasVip && discountInfo.proVipDiscountValue < 3) {
                        discountOptions.proVipDiscountFlag = true;
                    }
                    discountOptions.discountType = '打折';
                    discountOptions.unit = '折';
                    if (
                        discountOptions.discountFlag ||
                        discountOptions.v3DiscountFlag ||
                        discountOptions.vipDiscountFlag ||
                        discountOptions.proVipDiscountFlag
                    ) {
                        confirmList.push(discountOptions);
                    }
                } else if (
                    discountInfo.actType == MODELTYPS.REDUCTION ||
                    (menuType == ACT_MENU_TYPE.USER_GROUP &&
                        discountInfo.actType == MODELTYPS.CROWDPLLICY_REDUCTION)
                ) {
                    // 立减大于2毛
                    if (discountInfo.dctValue > 0.2) {
                        discountOptions.discountFlag = true;
                    }
                    if (hasVip && discountInfo.vthreeDiscountValue > 0.2) {
                        discountOptions.v3DiscountFlag = true;
                    }
                    if (hasVip && discountInfo.vipDiscountValue > 0.2) {
                        discountOptions.vipDiscountFlag = true;
                    }
                    if (hasVip && discountInfo.proVipDiscountValue > 0.2) {
                        discountOptions.proVipDiscountFlag = true;
                    }
                    discountOptions.discountType = '立减';
                    discountOptions.unit = '元/度';
                    if (
                        discountOptions.discountFlag ||
                        discountOptions.v3DiscountFlag ||
                        discountOptions.vipDiscountFlag ||
                        discountOptions.proVipDiscountFlag
                    ) {
                        confirmList.push(discountOptions);
                    }
                } else if (discountInfo.actType == MODELTYPS.DYNAMIC) {
                    let discountList = [];
                    discountInfo?.adjustDetailList?.forEach((element) => {
                        console.debug('✈️formatDistcountConfirmList ==> element', element);
                        let itemOptions = {
                            adjustType: element.adjustType,
                            discountValue: element.adjustValue,
                            vipDiscountValue: element.adjustVipValue,
                            proVipDiscountValue: element.proVipDiscountValue,
                            vthreeDiscountValue: element.adjustThreeDiscountValue,
                            adjustPriceType: discountInfo?.adjustPriceType,
                        };
                        if (element.adjustType === ADJUST_TYPS.DISCOUNT) {
                            itemOptions.discountType = '打折';
                            itemOptions.unit = '折';
                            if (
                                element.adjustValue < 3 ||
                                element.vthreeDiscountValue < 3 ||
                                element.proVipDiscountValue < 3 ||
                                element.adjustVipValue < 3
                            ) {
                                if (element.adjustValue < 3) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.vthreeDiscountValue < 3) {
                                    itemOptions.v3DiscountFlag = true;
                                }
                                if (element.adjustVipValue < 3) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                if (element.proVipDiscountValue < 3) {
                                    itemOptions.proVipDiscountFlag = true;
                                }
                                discountList.push(itemOptions);
                            }
                        } else if (
                            element.adjustType === ADJUST_TYPS.REDUCE ||
                            element.adjustType === ADJUST_TYPS.ADD
                        ) {
                            itemOptions.discountType =
                                element.adjustType === ADJUST_TYPS.REDUCE ? '立减' : '立加';
                            itemOptions.unit = '元/度';
                            if (
                                element.adjustValue > 0.2 ||
                                element.adjustThreeDiscountValue > 0.2 ||
                                element.adjustVipValue > 0.2 ||
                                element.proVipDiscountValue > 0.2
                            ) {
                                if (element.adjustValue > 0.2) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.adjustThreeDiscountValue > 0.2) {
                                    itemOptions.v3DiscountFlag = true;
                                }
                                if (element.adjustVipValue > 0.2) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                if (element.proVipDiscountValue > 0.2) {
                                    itemOptions.proVipDiscountFlag = true;
                                }
                                console.debug(
                                    '✈️formatDistcountConfirmList ==> itemOptions',
                                    itemOptions,
                                );
                                discountList.push(itemOptions);
                            }
                        } else if (element.adjustType === ADJUST_TYPS.DYNAMIC) {
                            itemOptions.discountType = '动态打折';
                            itemOptions.unit = '折';
                            itemOptions.adjustMethodType = element.adjustMethodType;
                            itemOptions.vipAdjustMethodType = element.vipAdjustMethodType;
                            itemOptions.vthreeMethodType = element.vthreeMethodType;
                            itemOptions.proVipMethodType = element.proVipMethodType;
                            if (
                                element.adjustValue > 3 ||
                                element.vthreeDiscountValue > 3 ||
                                element.proVipDiscountValue > 3 ||
                                element.adjustVipValue > 3
                            ) {
                                if (element.adjustValue > 3) {
                                    itemOptions.discountFlag = true;
                                }
                                if (element.vthreeDiscountValue > 3) {
                                    itemOptions.v3DiscountFlag = true;
                                }
                                if (element.adjustVipValue > 3) {
                                    itemOptions.vipDiscountFlag = true;
                                }
                                if (element.proVipDiscountValue > 3) {
                                    itemOptions.proVipDiscountFlag = true;
                                }
                                discountList.push(itemOptions);
                            }
                        }
                        if (discountList?.length > 0) {
                            confirmList = discountList;
                        }
                    });
                }

                return confirmList;
            };

            // 格式化去除没选了选择模板 但是没真的选模板的数据全部还原成全部
            formatTemplateItemEvent(values.gridList);
            formatTemplateItemEvent(values.mapGridList);

            //格式化文案判断是否有优惠设置太大并拼成对应优惠文案
            checkDiscountItemEvent(values.gridList);
            checkDiscountItemEvent(values.mapGridList);

            const includeDiscountItemEvent = (list, preTableText = '') => {
                let includeList = [];
                if (list instanceof Array) {
                    const childReg = /^[a-zA-Z]*List$/;

                    for (let index = 0; index < list.length; index++) {
                        const element = list[index];
                        const childKeys = Object.keys(element);

                        let tableIndexText = preTableText;
                        const nameKeys = ['activeCrowdName', 'cycleValueName', 'timePeriods'];
                        for (const keyItem of nameKeys) {
                            if (element[keyItem]) {
                                tableIndexText += element[keyItem];
                            }
                        }

                        const keyChildIndex = childKeys.findIndex((ele) => {
                            return childReg.test(ele) && !isEmpty(element[ele]);
                        });

                        const childKeyValue = childKeys[keyChildIndex];

                        if (childKeyValue === 'discountList') {
                            if (element && element['discountList'][0]) {
                                if (element['discountList'][0].desc) {
                                    includeList.push(element);
                                }
                            }
                        } else {
                            if (
                                childKeyValue &&
                                element[childKeyValue] &&
                                element[childKeyValue] instanceof Array
                            ) {
                                const childList = includeDiscountItemEvent(
                                    element[childKeyValue],
                                    tableIndexText,
                                );
                                element[childKeyValue] = childList;
                                if (childList.length > 0) {
                                    includeList.push(element);
                                }
                            }
                        }
                    }
                }
                return includeList;
            };

            let formatGridlist = copyObjectCommon(values.gridList);

            if (formatGridlist instanceof Array) {
                formatGridlist.forEach((element) => {
                    if (values.actChannel instanceof Array) {
                        element.actChannel = values.actChannel.join(',');
                        const channelNameList = values.actChannel.map((ele) => {
                            const channelItem = commonChannelOptions.find(
                                (channelOption) => channelOption.codeValue === ele,
                            );
                            return channelItem?.codeName;
                        });
                        const list = Array.from(new Set(channelNameList));
                        element.actChannelName = list.join(',');
                    }
                });
            }

            let formatMapGridList = copyObjectCommon(values.mapGridList);

            if (formatMapGridList instanceof Array) {
                formatMapGridList.forEach((element) => {
                    const itemActChannelList = element?.actChannel?.split(',') || [];
                    const formatItemChannelList = itemActChannelList.filter((ele) => ele);
                    let names = [];
                    if (formatItemChannelList.includes('11')) {
                        names.push('高德');
                    }
                    if (formatItemChannelList.includes('15')) {
                        names.push('百度');
                    }
                    if (names?.length > 0) {
                        element.actChannelName = names.join(',');
                    }
                });
            }

            const confirmGridList = includeDiscountItemEvent(formatGridlist);
            const confirmMapGridList = includeDiscountItemEvent(formatMapGridList);
            if (confirmGridList.length > 0 || confirmMapGridList.length > 0) {
                const confirmInfo = {
                    configType: values.configType,
                    gridList: confirmGridList,
                    mapGridList: confirmMapGridList,
                };

                confirm({
                    title: '请确认以下配置',
                    width: 1200,
                    content: (
                        <ActiveDetailChannelTable
                            key={1}
                            {...props}
                            actInfo={confirmInfo}
                            menuType={menuType}
                        />
                    ),
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        submitEvent(type, values, blank);
                    },
                });
            } else {
                submitEvent(type, values, blank);
            }
        } catch (error) {
            changeSubmitLoading(false);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const fetchSave = debounce(saveCouponEvent, 300);
    const columns = [
        {
            title: '冲突活动',
            dataIndex: 'actName',
            editable: true,
            render(text, record) {
                let path = formatActivePage({
                    type: String(record.actType),
                    actId: record.parentActId || record.actId,
                    parentActFlag: record.parentActFlag,
                });
                if (path?.length) {
                    return (
                        <Link to={path} target="_blank">
                            {text}
                        </Link>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本次活动',
            dataIndex: 'originActName',
            editable: true,
            render(text, record) {
                return (
                    <span title={text} style={{ whiteSpace: 'pre' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '冲突场站',
            dataIndex: 'stationName',
            editable: true,
            render(text, record) {
                const stationNames = record?.stationName?.split(',')?.join?.('\n');
                return (
                    <Tooltip title={record?.stationName}>
                        <div
                            style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: '5',
                                whiteSpace: 'pre',
                            }}
                        >
                            {stationNames}
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render(text, record) {
                let path;
                if (record?.parentActFlag === '1') {
                    path = `/marketing/businessActive/moduleAct/update/`;
                } else {
                    path = `/marketing/businessActive/list/update/`;
                }
                return (
                    <Link to={`${path}${record.actId}`} target="_blank">
                        手动解决
                    </Link>
                );
            },
        },
    ];

    return (
        <Card loading={detalistLoading}>
            <Form
                form={form}
                initialValues={{
                    areaRangeType: CAPITALTYPS.OPERATOR,
                    actType:
                        (menuType == ACT_MENU_TYPE.USER_GROUP && MODELTYPS.CROWDPLLICY_DISCOUNT) ||
                        MODELTYPS.DISCOUNT,
                    activeCrowdFlag: '1', // 全部
                    belongType:
                        menuType == ACT_MENU_TYPE.ACT_1 || menuType == ACT_MENU_TYPE.ACT_2
                            ? isUser
                                ? 'user'
                                : (currentUser?.operId && 'oper') || 'plat'
                            : menuType == ACT_MENU_TYPE.USER_GROUP ||
                              menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ||
                              menuType == ACT_MENU_TYPE.BASIC_PRICE
                            ? 'plat'
                            : (menuType == ACT_MENU_TYPE.COMPANY_ACT && 'oper') || undefined,
                    configType: CONFIG_MODEL.CROWD,
                    contributParty: '02',
                    temp_:
                        menuType == ACT_MENU_TYPE.ACT_2 || menuType == ACT_MENU_TYPE.USER_GROUP
                            ? ['01']
                            : ['01', '02'],
                }}
                scrollToFirstError
                name="basicForm"
            >
                <FormItem name="menuType" noStyle />

                <div className={commonStyles['form-title']}>
                    {menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE
                        ? '模板信息'
                        : '基础信息'}
                </div>

                {(menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && (
                    <Fragment>
                        <Form.Item name="templateActId" noStyle />
                        <Form.Item name="belongType" noStyle />
                        <FormItem
                            label={<span>模板名称</span>}
                            name="templateActName"
                            {...formItemFixedWidthLayout}
                            rules={[{ required: true, whitespace: true, message: '请填写' }]}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={15}
                                placeholder="请填写"
                                autoComplete="off"
                                showCount
                            />
                        </FormItem>
                        <FormItem
                            label={<span>模板概述</span>}
                            name="templateActRemarks"
                            {...formItemFixedWidthLayout}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={15}
                                placeholder="请输入引入本活动模板对商家的价值，展示在小程序端"
                                autoComplete="off"
                                showCount
                            />
                        </FormItem>
                    </Fragment>
                )) || (
                    <FormItem
                        label={<span>活动名称</span>}
                        name="actName"
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
                    >
                        <Input
                            disabled={isLock}
                            maxLength={30}
                            placeholder="请输入主标题，最多30个字"
                            autoComplete="off"
                        />
                    </FormItem>
                )}

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.stationConfig !== curValues.stationConfig ||
                        prevValues.contributParty !== curValues.contributParty ||
                        prevValues.operatorContributRatio !== curValues.operatorContributRatio ||
                        prevValues.menuType !== curValues.menuType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const menuType = getFieldValue('menuType');

                        if (
                            menuType == ACT_MENU_TYPE.USER_GROUP ||
                            menuType == ACT_MENU_TYPE.COMPANY_ACT
                        ) {
                            // 新活动不需要显示活动类型，传固定值
                            return <FormItem noStyle name={'belongType'} />;
                        }
                        if (
                            menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                            menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE
                        ) {
                            const contributParty = getFieldValue('contributParty');
                            const operatorContributRatio = getFieldValue('operatorContributRatio');

                            return (
                                <Fragment>
                                    <FormItem
                                        label="活动出资"
                                        name="contributParty"
                                        {...formItemFixedWidthLayout}
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择活动出资方',
                                            },
                                        ]}
                                        initialValue={'02'}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Radio.Group disabled={isLock}>
                                            <Radio value="02">平台</Radio>
                                            <Radio value="04">
                                                <Space>
                                                    混合出资
                                                    {contributParty === '04' &&
                                                        operatorContributRatio > 0 &&
                                                        isWorkorder && (
                                                            <Typography.Text type="danger">
                                                                混合出资下不可配置购电运营商
                                                            </Typography.Text>
                                                        )}
                                                </Space>
                                            </Radio>
                                        </Radio.Group>
                                    </FormItem>
                                    {(contributParty == '04' && (
                                        <FormItem
                                            label="运营商出资比例"
                                            {...formItemLayout}
                                            required
                                        >
                                            <FormItem
                                                name="operatorContributRatio"
                                                rules={[{ required: true, message: '请填写' }]}
                                                noStyle
                                                initialValue={undefined}
                                            >
                                                <InputNumber
                                                    disabled={isLock}
                                                    min={0}
                                                    max={100}
                                                    placeholder="请填写"
                                                    precision={2}
                                                    step={0.01}
                                                />
                                            </FormItem>
                                            <Space>
                                                %
                                                {contributParty === '04' &&
                                                    operatorContributRatio > 0 &&
                                                    !isWorkorder &&
                                                    payEleOperListNames && (
                                                        <Typography.Text type="danger">
                                                            {payEleOperListNames}
                                                            运营商为购电运营商，不支持出资配置
                                                        </Typography.Text>
                                                    )}
                                            </Space>
                                        </FormItem>
                                    )) ||
                                        null}
                                    {(contributParty == '04' && (
                                        <FormItem
                                            label="平台出资比例"
                                            name="platformContributRatio"
                                            {...formItemLayout}
                                            required
                                        >
                                            {operatorContributRatio !== 0 &&
                                            (!operatorContributRatio ||
                                                isNaN(operatorContributRatio))
                                                ? '0.00'
                                                : (
                                                      100.0 - parseFloat(operatorContributRatio)
                                                  ).toFixed(2)}
                                            %
                                        </FormItem>
                                    )) ||
                                        null}

                                    <FormItem noStyle name={'belongType'} />
                                </Fragment>
                            );
                        }
                        const stationConfig = getFieldValue('stationConfig');
                        const { allStations } = stationConfig || {};
                        // 筛选出购电场站
                        const buyStations =
                            allStations?.filter &&
                            allStations?.filter((item) => item.cooperationType == '02');
                        const containBuy = buyStations?.length > 0;

                        return (
                            <FormItem
                                label={<span>活动类型</span>}
                                name="belongType"
                                {...formItemFixedWidthLayout}
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: '请选择活动类型',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (value === 'oper' && containBuy) {
                                                return Promise.reject(
                                                    '活动范围包含购电模式运营商时，不允许选择运营商类型',
                                                );
                                            }

                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <Radio.Group
                                    disabled={currentUser?.operId || isLock}
                                    onChange={(event) => {
                                        const {
                                            target: { value },
                                        } = event;
                                        if (value === 'oper') {
                                            reloadGridList({
                                                expenseBearing: [BEARING_TYPES.BUSINESS],
                                            });
                                        } else {
                                            reloadGridList({
                                                expenseBearing: [],
                                            });
                                        }
                                    }}
                                >
                                    {(isPlat && (
                                        <>
                                            <Radio value={'plat'}>平台</Radio>
                                            <Radio value={'oper'} disabled={containBuy}>
                                                {(containBuy && (
                                                    <Tooltip title="活动范围包含购电模式运营商时，不允许选择此类型">
                                                        <span>运营商</span>
                                                    </Tooltip>
                                                )) || <span>运营商</span>}
                                            </Radio>
                                        </>
                                    )) ||
                                        (isUser && <Radio value={'user'}>用户</Radio>) || (
                                            <>
                                                <Radio value={'plat'}>平台</Radio>
                                                <Radio value={'oper'} disabled={containBuy}>
                                                    {(containBuy && (
                                                        <Tooltip title="活动范围包含购电模式运营商时，不允许选择此类型">
                                                            <span>运营商</span>
                                                        </Tooltip>
                                                    )) || <span>运营商</span>}
                                                </Radio>
                                                <Radio value={'user'}>用户</Radio>
                                            </>
                                        )}
                                </Radio.Group>
                            </FormItem>
                        );
                    }}
                </FormItem>
                {/* 基础价格补贴和人群策略和招商需要出资部门，商家暂时不需要 */}
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.operatorContributRatio !== curValues.operatorContributRatio ||
                        prevValues.contributParty !== curValues.contributParty
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const operatorContributRatio = getFieldValue('operatorContributRatio');
                        const contributParty = getFieldValue('contributParty');
                        return (
                            <>
                                {(menuType == ACT_MENU_TYPE.USER_GROUP ||
                                    menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                                    menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) &&
                                    (operatorContributRatio < 100 || contributParty === '02') && (
                                        <FormItem
                                            label="出资部门"
                                            {...formItemFixedWidthLayout}
                                            name="financeDepList"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择出资部门',
                                                },
                                            ]}
                                        >
                                            <FundingDepartmentForm disabled={isLock} />
                                        </FormItem>
                                    )}
                            </>
                        );
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.dateTime !== curValues.dateTime
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                            return null;
                        }
                        const dateTime = getFieldValue('dateTime');
                        return (
                            <FormItem
                                name="dateTime"
                                label="活动有效期:"
                                {...formItemFixedWidthLayout}
                                rules={[
                                    { required: true, message: '请选择活动有效期' },
                                    (_) => ({
                                        validator(rule, value) {
                                            if (!value) {
                                                return Promise.reject('');
                                            }
                                            if (!value[0]) {
                                                return Promise.reject('请选择活动开始日期');
                                            }
                                            if (!value[1]) {
                                                return Promise.reject('请选择活动失效日期');
                                            }
                                            if (value[1]) {
                                                const nowTime = moment();
                                                const sendStartTime = moment(value[0]);
                                                const sendEndTime = moment(value[1]);
                                                if (
                                                    (!actId ||
                                                        actInfo?.actState === '0' ||
                                                        actInfo?.actState === '1' ||
                                                        isCopy) &&
                                                    sendStartTime.diff(nowTime, 'days') < 0
                                                ) {
                                                    return Promise.reject(
                                                        '活动开始日期不能早于当日',
                                                    );
                                                }
                                                if (sendEndTime.diff(nowTime, 'days') < 0) {
                                                    return Promise.reject(
                                                        '活动失效日期不能早于当日',
                                                    );
                                                }
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <RangePicker
                                    ranges={
                                        dateTime && dateTime[0] && dateTime[1]
                                            ? {
                                                  起始日期顺延7天: [
                                                      moment(dateTime[0]).add('7', 'day'),
                                                      moment(dateTime[1]).add('7', 'day'),
                                                  ],
                                              }
                                            : {}
                                    }
                                    disabled={[isLock, !openSomeThing]}
                                    disabledTime={disabledRangeTime}
                                    disabledDate={disabledDate}
                                    showTime={{
                                        format: 'HH:mm:ss',
                                        defaultValue: [
                                            moment('00:00:00', 'HH:mm:ss'),
                                            moment('23:59:59', 'HH:mm:ss'),
                                        ],
                                    }}
                                    format="YYYY-MM-DD HH:mm:ss"
                                />
                            </FormItem>
                        );
                    }}
                </FormItem>

                {(menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && (
                    <Fragment>
                        <FormItem
                            name="configType"
                            initialValue={ACTIVE_CONFIG_TYPE.CYCLE}
                            noStyle
                        />
                    </Fragment>
                )) || (
                    <Fragment>
                        {menuType === ACT_MENU_TYPE.COMPANY_ACT && (
                            <FormItem
                                label="活动平台"
                                name="cooperationPlatform"
                                {...formItemFixedWidthLayout}
                                rules={[{ required: true, message: '请选择所属平台' }]}
                                initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                            >
                                <SelectCooperationPlatform
                                    disabled={isLock}
                                    allowClear={false}
                                    onChange={() => {
                                        prizeOptimizeStationRef?.current?.reset();
                                    }}
                                />
                            </FormItem>
                        )}

                        <Divider />
                        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                            <div className={commonStyles['form-title']}>
                                <Space>
                                    活动配置
                                    {(menuType == ACT_MENU_TYPE.ACT_2 && (
                                        <FormItem
                                            name="configType"
                                            initialValue={ACTIVE_CONFIG_TYPE.CYCLE}
                                            noStyle
                                        >
                                            {!isLock && (
                                                <span
                                                    className={commonStyles['table-btn']}
                                                    style={{ fontSize: '16px' }}
                                                    onClick={toggleConfigType}
                                                >
                                                    {configTypeName}
                                                    <SwapOutlined />
                                                </span>
                                            )}
                                        </FormItem>
                                    )) || (
                                        <FormItem
                                            name="configType"
                                            initialValue={ACTIVE_CONFIG_TYPE.CROWD}
                                            noStyle
                                        />
                                    )}
                                </Space>
                            </div>
                        </div>
                    </Fragment>
                )}

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.menuType !== curValues.menuType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const menuType = getFieldValue('menuType');
                        return (
                            menuType !== ACT_MENU_TYPE.USER_GROUP &&
                            menuType !== ACT_MENU_TYPE.COMPANY_ACT && (
                                <FormItem
                                    label="投放渠道"
                                    validateTrigger={['onChange']}
                                    name={'temp_'}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value?.length) {
                                                    return Promise.reject('请选择');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    {...formItemLayout}
                                    required
                                >
                                    <Checkbox.Group
                                        disabled={
                                            isLock ||
                                            menuType == ACT_MENU_TYPE.USER_GROUP ||
                                            isPrizeOptimize
                                        }
                                        options={[
                                            {
                                                label: '平台渠道',
                                                value: '01',
                                            },
                                            {
                                                label: '图商渠道',
                                                value: '02',
                                            },
                                        ]}
                                    />
                                </FormItem>
                            )
                        );
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.belongType !== curValues.belongType ||
                        prevValues.menuType !== curValues.menuType ||
                        prevValues.temp_ !== curValues.temp_ ||
                        prevValues.mapActChannel !== curValues.mapActChannel ||
                        prevValues.synPlatChannel !== curValues.synPlatChannel ||
                        prevValues.gridList !== curValues.gridList ||
                        prevValues.mapGridList !== curValues.mapGridList ||
                        prevValues.actChannel !== curValues.actChannel ||
                        prevValues.aloneConfig !== curValues.aloneConfig
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const temp_ = getFieldValue('temp_');
                        const menuType = getFieldValue('menuType');
                        const mapActChannel = getFieldValue('mapActChannel');
                        const belongType = getFieldValue('belongType');
                        const synPlatChannel = getFieldValue('synPlatChannel');
                        const aloneConfig = getFieldValue('aloneConfig');
                        let aloneConfigChannels = mapActChannel;
                        if (mapActChannel?.length > 0) {
                            aloneConfigChannels = [mapActChannel.join(',')];
                        }

                        const hasPlat =
                            menuType != ACT_MENU_TYPE.COMPANY_ACT && temp_?.includes('01');
                        const hasOper =
                            menuType != ACT_MENU_TYPE.COMPANY_ACT && temp_?.includes('02');
                        const gridList = getFieldValue('gridList');

                        const checkCrowdEvent = (list) => {
                            //校验是否人群全配的全部
                            let all = true;

                            if (list instanceof Array) {
                                const flagReg = /^[a-zA-Z]*Flag$/;
                                const childReg = /^[a-zA-Z]*List$/;

                                for (let index = 0; index < list.length; index++) {
                                    const element = list[index];
                                    const childKeys = Object.keys(element);
                                    const keyIndex = childKeys.findIndex((ele) => {
                                        return flagReg.test(ele) && !isEmpty(element[ele]);
                                    });
                                    const keyChildIndex = childKeys.findIndex((ele) => {
                                        return childReg.test(ele) && !isEmpty(element[ele]);
                                    });
                                    const flagKeyValue = childKeys[keyIndex];

                                    const childKeyValue = childKeys[keyChildIndex];

                                    if (flagKeyValue === 'activeCrowdFlag') {
                                        if (element[flagKeyValue] != '1') {
                                            all = false;

                                            break;
                                        }
                                    }
                                    if (
                                        childKeyValue &&
                                        element[childKeyValue] &&
                                        element[childKeyValue] instanceof Array
                                    ) {
                                        all = checkCrowdEvent(element[childKeyValue]);
                                        if (!all) {
                                            break;
                                        }
                                    }
                                }
                            }

                            return all;
                        };

                        const checkDiscountItemEvent = (list, preTableText = '') => {
                            //校验是否配置优惠
                            let messageerror = '';

                            if (list instanceof Array) {
                                const childReg = /^[a-zA-Z]*List$/;

                                const flagReg = /^[a-zA-Z]*Flag$/;

                                for (let index = 0; index < list.length; index++) {
                                    const element = list[index];
                                    const childKeys = Object.keys(element);

                                    const flagKeyIndex = childKeys.findIndex((ele) => {
                                        return flagReg.test(ele) && !isEmpty(element[ele]);
                                    });

                                    const flagKeyValue = childKeys[flagKeyIndex];

                                    let tableIndexText = preTableText;
                                    const nameKeys = [
                                        'activeCrowdName',
                                        'cycleValueName',
                                        'timePeriods',
                                    ];

                                    if (element[flagKeyValue] != '1') {
                                        for (const keyItem of nameKeys) {
                                            if (element[keyItem]) {
                                                if (element[keyItem].length > 15) {
                                                    tableIndexText += `${element[keyItem].substring(
                                                        0,
                                                        15,
                                                    )}...`;
                                                } else {
                                                    tableIndexText += element[keyItem];
                                                }
                                            }
                                        }
                                    }

                                    const keyChildIndex = childKeys.findIndex((ele) => {
                                        return (
                                            childReg.test(ele) &&
                                            ele != 'crowdDetailList' &&
                                            !isEmpty(element[ele])
                                        );
                                    });

                                    const childKeyValue = childKeys[keyChildIndex];

                                    let tableAllText = '';
                                    if (element[flagKeyValue] != '1' && element.actChannelName) {
                                        tableAllText = `${element.actChannelName}`;
                                    }
                                    if (tableIndexText) {
                                        tableAllText += tableIndexText;
                                    }
                                    if (
                                        !isEmpty(element[flagKeyValue]) &&
                                        element[flagKeyValue] != '1'
                                    ) {
                                        //校验是否selectType选了模板 但是没选值的情况
                                        let errorTemplate = '';
                                        for (const keyItem of nameKeys) {
                                            if (element[keyItem]) {
                                                errorTemplate += element[keyItem];
                                            }
                                        }
                                        let templateName = '';
                                        switch (flagKeyValue) {
                                            case 'activeCrowdFlag':
                                                templateName = '人群';
                                                break;
                                            case 'cycleValueFlag':
                                                templateName = '周期';
                                                break;
                                            case 'allDayFlag':
                                                templateName = '时段';
                                                break;

                                            default:
                                                break;
                                        }
                                        if (!errorTemplate) {
                                            messageerror += `【请选择${
                                                tableAllText ? `${tableAllText}的` : ''
                                            }${templateName}模板】`;
                                        }
                                    }

                                    if (childKeyValue === 'discountList') {
                                        if (element && element['discountList']?.[0]) {
                                            if (!element['discountList'][0].discountDesc) {
                                                messageerror += `【请配置${
                                                    tableAllText ? `${tableAllText}的` : ''
                                                }活动优惠方式】 `;
                                            }
                                            if (
                                                menuType ==
                                                    ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE &&
                                                !element['discountList'][0].dctExternalCopywriting
                                                    ?.length
                                            ) {
                                                messageerror += `【请配置${
                                                    tableAllText ? `${tableAllText}的` : ''
                                                }优惠外显文案】 `;
                                            }
                                        } else {
                                            messageerror += `【请配置${
                                                tableAllText ? `${tableAllText}的` : ''
                                            }活动优惠方式】 `;
                                        }
                                    } else {
                                        if (
                                            childKeyValue &&
                                            element[childKeyValue] &&
                                            element[childKeyValue] instanceof Array
                                        ) {
                                            messageerror += checkDiscountItemEvent(
                                                element[childKeyValue],
                                                tableIndexText,
                                            );
                                        }
                                    }
                                }
                            }

                            return messageerror;
                        };

                        const allCrowdFlag = checkCrowdEvent(gridList);

                        return (
                            <Fragment>
                                {hasPlat && (
                                    <FormItem
                                        label={
                                            menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE
                                                ? '平台渠道'
                                                : '下单渠道'
                                        }
                                        name="actChannel"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择投放渠道',
                                            },
                                        ]}
                                        {...{
                                            ...formItemLayout,
                                            wrapperCol: {
                                                span: 12,
                                            },
                                        }}
                                    >
                                        <SelectChannelItem
                                            optionList={commonChannelOptions}
                                            // transform={(list) => {
                                            //     return list.join(',');
                                            // }}
                                            disabled={
                                                isLock ||
                                                menuType == ACT_MENU_TYPE.USER_GROUP ||
                                                isPrizeOptimize
                                            }
                                            filterKeys={['新电途']}
                                        ></SelectChannelItem>
                                    </FormItem>
                                )}
                                {hasOper && (
                                    <Fragment>
                                        <Row>
                                            <Col flex={'0 0 300px'}>
                                                <FormItem
                                                    label="图商渠道"
                                                    validateTrigger={['onChange']}
                                                    name={'mapActChannel'}
                                                    {...formItemLayout}
                                                    required
                                                    initialValue={mapChannelOptions?.map(
                                                        (ele) => ele.value,
                                                    )}
                                                    rules={[
                                                        ({ getFieldValue }) => ({
                                                            validator(rule, value) {
                                                                if (!value?.length) {
                                                                    return Promise.reject(
                                                                        '请选择渠道',
                                                                    );
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <Checkbox.Group
                                                        disabled={
                                                            isLock ||
                                                            menuType == ACT_MENU_TYPE.USER_GROUP ||
                                                            isPrizeOptimize
                                                        }
                                                        options={mapChannelOptions}
                                                    />
                                                </FormItem>
                                            </Col>

                                            <Col>
                                                {hasPlat && (
                                                    <Space>
                                                        {!synPlatChannel &&
                                                            mapActChannel &&
                                                            mapActChannel?.length > 1 && (
                                                                <FormItem
                                                                    name="aloneConfig"
                                                                    label="独立配置"
                                                                    valuePropName="checked"
                                                                >
                                                                    <Switch
                                                                        checkedChildren="开"
                                                                        unCheckedChildren="关"
                                                                        disabled={
                                                                            isLock ||
                                                                            menuType ==
                                                                                ACT_MENU_TYPE.USER_GROUP
                                                                        }
                                                                    />
                                                                </FormItem>
                                                            )}
                                                        <FormItem
                                                            name="synPlatChannel"
                                                            label="同步平台渠道"
                                                            valuePropName="checked"
                                                            initialValue
                                                        >
                                                            <Switch
                                                                checkedChildren="开"
                                                                unCheckedChildren="关"
                                                                disabled={
                                                                    isLock ||
                                                                    isPrizeOptimize ||
                                                                    !allCrowdFlag ||
                                                                    menuType ==
                                                                        ACT_MENU_TYPE.USER_GROUP
                                                                }
                                                            />
                                                        </FormItem>
                                                    </Space>
                                                )}
                                            </Col>
                                        </Row>
                                    </Fragment>
                                )}
                                {(hasPlat || menuType == ACT_MENU_TYPE.COMPANY_ACT) && (
                                    <Fragment>
                                        {/* 仅当有图商类型时显示标题 */}
                                        {(!synPlatChannel || (!hasPlat && hasOper)) &&
                                            hasOper &&
                                            mapActChannel?.length > 0 && (
                                                <Divider orientationMargin="0" orientation="left">
                                                    <h3>平台渠道</h3>
                                                </Divider>
                                            )}
                                        <FormItem
                                            name="gridList"
                                            wrapperCol={{ span: 24 }}
                                            rules={[
                                                (_) => ({
                                                    validator(rule, value) {
                                                        if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                                                            // 保存时的校验，商家活动仅需判断人群是否选中，其他校验可放过
                                                            let errorMessage = null;
                                                            for (let i = 0; i < value.length; i++) {
                                                                const element = value[i];
                                                                if (
                                                                    element.activeCrowdFlag !=
                                                                        '1' &&
                                                                    !element.activeCrowd?.length
                                                                ) {
                                                                    errorMessage = '请配置人群';
                                                                    break;
                                                                }
                                                            }
                                                            if (errorMessage) {
                                                                return Promise.reject(errorMessage);
                                                            }
                                                        }
                                                        if (saveFlag) {
                                                            return Promise.resolve();
                                                        }

                                                        if (!value?.length) {
                                                            return Promise.reject('请选择');
                                                        }

                                                        if (!allCrowdFlag) {
                                                            form.setFieldsValue({
                                                                synPlatChannel: false,
                                                            });
                                                        }

                                                        const errorMessage =
                                                            checkDiscountItemEvent(value);
                                                        if (errorMessage) {
                                                            return Promise.reject(errorMessage);
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                                (_) => ({
                                                    validator(rule, value) {
                                                        const checkingFlag =
                                                            getFieldValue('checkingFlag');
                                                        if (checkingFlag == '1') {
                                                            return Promise.reject('请稍候');
                                                        }
                                                        const checkingMsg =
                                                            getFieldValue('checkingMsg');
                                                        if (checkingMsg?.length) {
                                                            return Promise.reject(checkingMsg);
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <ActModuleTableFormItem
                                                {...props}
                                                belongType={belongType}
                                                sortType={configType}
                                                disabled={isLock}
                                                menuType={menuType}
                                                staticTimes={prizeOptimizeStaticTimes}
                                                staticCycle={isPrizeOptimize}
                                                priceSelectCallback={() => {
                                                    checkStationInfo();
                                                }}
                                                isNewDiscountActive
                                                cooperationPlatform={cooperationPlatform}
                                                editRef={channelEditRef}
                                            ></ActModuleTableFormItem>
                                        </FormItem>

                                        {/* 异步校验状态，1为进行中，其他则无 */}
                                        <FormItem name="checkingFlag" noStyle />
                                        {/* 异步校验结果反馈，非undefined则提示 */}
                                        <FormItem name="checkingMsg" noStyle />
                                    </Fragment>
                                )}

                                {(!synPlatChannel || (!hasPlat && hasOper)) &&
                                    hasOper &&
                                    mapActChannel?.length > 0 && (
                                        <Fragment>
                                            <Divider orientationMargin="0" orientation="left">
                                                <h3>图商渠道</h3>
                                            </Divider>
                                            <FormItem
                                                name="mapGridList"
                                                wrapperCol={{ span: 24 }}
                                                rules={[
                                                    (_) => ({
                                                        validator(rule, value) {
                                                            if (!value?.length) {
                                                                return Promise.reject('请选择');
                                                            }

                                                            const errorMessage =
                                                                checkDiscountItemEvent(value);
                                                            if (errorMessage) {
                                                                return Promise.reject(errorMessage);
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <ActModuleTableFormItem
                                                    {...props}
                                                    channelList={
                                                        (aloneConfig && mapActChannel) ||
                                                        aloneConfigChannels
                                                    }
                                                    belongType={belongType}
                                                    sortType={configType}
                                                    disabled={isLock}
                                                    menuType={menuType}
                                                    editRef={mapChannelEditRef}
                                                ></ActModuleTableFormItem>
                                            </FormItem>
                                        </Fragment>
                                    )}
                                <Divider />
                            </Fragment>
                        );
                    }}
                </FormItem>

                {/* <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.menuType !== curValues.menuType ||
                        prevValues.discountLimitFlag !== curValues.discountLimitFlag ||
                        prevValues.discountLimitFlag !== curValues.discountLimitFlag
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const menuType = getFieldValue('menuType');
                        if (
                            menuType == ACT_MENU_TYPE.USER_GROUP ||
                            menuType == ACT_MENU_TYPE.COMPANY_ACT
                        ) {
                            return null;
                        }
                        const belongType = getFieldValue('belongType');
                        const discountLimitFlag =
                            getFieldValue('discountLimitFlag') && belongType != 'oper';

                        return (
                            (belongType != 'oper' && (
                                <>
                                    <FormItem
                                        name="discountLimitFlag"
                                        {...formItemFixedWidthLayout}
                                        dependencies={['gridList', 'mapGridList']}
                                        labelCol={{
                                            flex: '0 0 130px',
                                        }}
                                        label={
                                            <span>
                                                会员优惠限制
                                                <Tooltip title="关闭时会员优惠无上限；开启时会员优惠达上限后使用普通活动价计费">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        valuePropName="checked"
                                        required
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    //开启会员限高的校验是否有V3专享
                                                    if (value) {
                                                        const gridList = getFieldValue('gridList');
                                                        const mapGridList =
                                                            getFieldValue('mapGridList');
                                                        if (
                                                            hasVthreeDiscountValue(
                                                                gridList,
                                                                mapGridList,
                                                            )
                                                        ) {
                                                            return Promise.reject(
                                                                '【开启会员优惠限制不支持配置V3专享优惠】',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <Switch
                                            checkedChildren="开"
                                            unCheckedChildren="关"
                                            disabled={isLock || isPrizeOptimize}
                                        />
                                    </FormItem>
                                    {discountLimitFlag && (
                                        <>
                                            <FormItem label="每日优惠上限" {...formItemLayout}>
                                                <Space>
                                                    <FormItem
                                                        noStyle
                                                        name="discountLimitAmtDay"
                                                        validateTrigger={['onChange', 'onBlur']}
                                                        rules={[
                                                            ({ getFieldValue }) => ({
                                                                validator(rule, value) {
                                                                    const discountLimitAmtDay =
                                                                        getFieldValue(
                                                                            'discountLimitAmtDay',
                                                                        );
                                                                    const discountLimitAmtTime =
                                                                        getFieldValue(
                                                                            'discountLimitAmtTime',
                                                                        );
                                                                    const discountLimitAmt =
                                                                        getFieldValue(
                                                                            'discountLimitAmt',
                                                                        );

                                                                    if (
                                                                        discountLimitAmtDay ==
                                                                            null &&
                                                                        discountLimitAmtTime ==
                                                                            null &&
                                                                        discountLimitAmt == null
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '至少填写一项优惠上限',
                                                                        );
                                                                    }

                                                                    if (
                                                                        discountLimitAmtDay !==
                                                                            null &&
                                                                        discountLimitAmtTime !==
                                                                            null &&
                                                                        Number(
                                                                            discountLimitAmtTime,
                                                                        ) >
                                                                            Number(
                                                                                discountLimitAmtDay,
                                                                            )
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '每日优惠要大于每次优惠上限',
                                                                        );
                                                                    }

                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            disabled={isLock}
                                                            precision={2}
                                                            step={0.01}
                                                            min={0.01}
                                                            max={99999}
                                                            placeholder="不填表示无上限"
                                                            style={{ width: '140px' }}
                                                        />
                                                    </FormItem>
                                                    元
                                                </Space>
                                            </FormItem>
                                            <FormItem label="总优惠上限" {...formItemLayout}>
                                                <Space>
                                                    <FormItem
                                                        noStyle
                                                        name="discountLimitAmt"
                                                        validateTrigger={['onChange', 'onBlur']}
                                                        rules={[
                                                            ({ getFieldValue }) => ({
                                                                validator(rule, value) {
                                                                    const discountLimitAmtDay =
                                                                        getFieldValue(
                                                                            'discountLimitAmtDay',
                                                                        );
                                                                    const discountLimitAmtTime =
                                                                        getFieldValue(
                                                                            'discountLimitAmtTime',
                                                                        );
                                                                    const discountLimitAmt =
                                                                        getFieldValue(
                                                                            'discountLimitAmt',
                                                                        );

                                                                    if (
                                                                        discountLimitAmtDay ==
                                                                            null &&
                                                                        discountLimitAmtTime ==
                                                                            null &&
                                                                        discountLimitAmt == null
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '至少填写一项优惠上限',
                                                                        );
                                                                    }
                                                                    if (
                                                                        discountLimitAmtDay !==
                                                                            null &&
                                                                        discountLimitAmt !== null &&
                                                                        Number(
                                                                            discountLimitAmtDay,
                                                                        ) > Number(discountLimitAmt)
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '总优惠要大于每日优惠上限',
                                                                        );
                                                                    }

                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            disabled={isLock}
                                                            precision={2}
                                                            step={0.01}
                                                            min={0.01}
                                                            max={99999}
                                                            placeholder="不填表示无上限"
                                                            style={{ width: '140px' }}
                                                        />
                                                    </FormItem>
                                                    元
                                                </Space>
                                            </FormItem>
                                        </>
                                    )}
                                    <Divider />
                                </>
                            )) ||
                            null
                        );
                    }}
                </FormItem> */}

                {(menuType != ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && (
                    <div className={commonStyles['form-title']}>活动范围</div>
                )) ||
                    null}

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.belongType !== curValues.belongType ||
                        prevValues.operatorContributRatio !== curValues.operatorContributRatio ||
                        prevValues.contributParty !== curValues.contributParty
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                            return;
                        }
                        const belongType = getFieldValue('belongType');
                        const operatorContributRatio = getFieldValue('operatorContributRatio');
                        const contributParty = getFieldValue('contributParty');

                        let recordParamsConfig = {};
                        if (isWorkorder) {
                            recordParamsConfig = {
                                recordParams: {
                                    relateId: actId,
                                    scene: 'stc_station_market',
                                },
                            };
                        }
                        return (
                            <FormItem
                                label="活动范围"
                                name={'stationConfig'}
                                required
                                dependencies={['gridList', 'mapGridList']}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        async validator(rule, value) {
                                            if (isEmpty(value)) {
                                                return Promise.reject('请配置指定场站');
                                            }
                                            const { addStations, allStations } = value;
                                            if (isEmpty(allStations) && isEmpty(addStations)) {
                                                return Promise.reject('请配置指定场站');
                                            }
                                            if (
                                                addStations.length > 20000 ||
                                                allStations.length > 20000
                                            ) {
                                                return Promise.reject('一次性最多配置2万个站点');
                                            }
                                            const gridList = getFieldValue('gridList');
                                            const mapGridList = getFieldValue('mapGridList');
                                            const hasV3 = hasVthreeDiscountValue(
                                                gridList,
                                                mapGridList,
                                            );

                                            if (hasV3) {
                                                try {
                                                    const verifyResult =
                                                        await queryCityDiscountLimitInfo(
                                                            allStations?.map((v) => v.stationId),
                                                        );
                                                    if (verifyResult?.data?.length > 0) {
                                                        return Promise.reject(
                                                            `【${verifyResult?.data
                                                                ?.map((v) => v.cityName)
                                                                ?.join(
                                                                    '、',
                                                                )} 已开启会员城市限高，不支持配置V3专享优惠】`,
                                                        );
                                                    }
                                                } catch (error) {}
                                            }

                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <SearchStationItem
                                    title="活动范围"
                                    limitOperIds={limitOperIds}
                                    exceptOperIds={
                                        (menuType === ACT_MENU_TYPE.COMPANY_ACT && [
                                            { operId: '*********' },
                                            { operId: 'MA1MY0GF9' },
                                        ]) ||
                                        undefined
                                    }
                                    disabled={!openSomeThing || isPrizeOptimize}
                                    cooperationPlatform={
                                        menuType === ACT_MENU_TYPE.BASIC_PRICE
                                            ? 'xdt'
                                            : menuType === ACT_MENU_TYPE.COMPANY_ACT
                                            ? cooperationPlatform
                                            : undefined
                                    }
                                    required
                                    purchase={
                                        menuType !== ACT_MENU_TYPE.COMPANY_ACT &&
                                        (belongType == 'oper' ||
                                            (contributParty === '04' && operatorContributRatio > 0))
                                            ? SELECT_TYPES.EXCEPTBUY
                                            : SELECT_TYPES.ALL
                                    }
                                    currentUser={currentUser}
                                    hasStastics
                                    requestInfo={
                                        actId &&
                                        actInfo && {
                                            listApi: getStationScopeListApi,
                                            params: {
                                                scopeRelateId: actId,
                                                scopeRelateType:
                                                    callbackJsonEvent && isWorkorderOrigin
                                                        ? STATION_CONFIG_PAGE_TYPES.WORKER
                                                        : STATION_CONFIG_PAGE_TYPES.BUSINESS_2,
                                            },
                                            ...recordParamsConfig,
                                        }
                                    }
                                    isCopy={isCopy}
                                    ref={prizeOptimizeStationRef}
                                    // 是否是工单
                                    isWorkorderOrigin={isWorkorderOrigin}
                                    isPrizeOptimize={
                                        // 除了定价调整，其他页面的基础价格补贴，场站范围都显示定价辅助入口
                                        !isPrizeOptimize && menuType == ACT_MENU_TYPE.BASIC_PRICE
                                    }
                                    menuType={menuType}
                                    submitEvent={() => {
                                        return form.validateFields().then(() => {
                                            const values = form.getFieldValue();
                                            const unEnabled = values.gridList?.find((crowdEle) =>
                                                crowdEle?.cycleList?.find((cycleEle) =>
                                                    cycleEle?.periodsList?.find((periodsEle) =>
                                                        periodsEle?.discountList?.find(
                                                            (discountEle) =>
                                                                discountEle?.adjustPriceType ==
                                                                ADJUST_PRICE_TYPES.SETTLE,
                                                        ),
                                                    ),
                                                ),
                                            );
                                            if (unEnabled) {
                                                message.error(
                                                    '营销方式为配置结算价时，不支持价格估算',
                                                );
                                                return Promise.reject(
                                                    '营销方式为配置结算价时，不支持价格估算',
                                                );
                                            }
                                            return submitEvent('params', values);
                                        });
                                    }}
                                    // 价格估算显示
                                    isPriceEstimation={
                                        menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                                        menuType == ACT_MENU_TYPE.COMPANY_ACT
                                    }
                                    onChange={() => {
                                        checkStationInfo();
                                    }}
                                ></SearchStationItem>
                            </FormItem>
                        );
                    }}
                </FormItem>

                <div className={commonStyles['form-submit']}>
                    {(!isLock || openSomeThing) && (
                        <Fragment>
                            <Button
                                className={commonStyles['form-btn']}
                                type="primary"
                                loading={submitLoading}
                                disabled={submitLoading}
                                onClick={() => {
                                    fetchSave('send');
                                }}
                            >
                                提交
                            </Button>
                            {!isSimpleControl && (
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    disabled={submitLoading}
                                    onClick={() => {
                                        fetchSave('send', true);
                                    }}
                                >
                                    提交并继续添加
                                </Button>
                            )}
                        </Fragment>
                    )}
                    {(!(actInfo?.actState > 0) || isCopy) &&
                        isWorkorder &&
                        !isLock &&
                        !isSimpleControl && (
                            <Button
                                className={commonStyles['form-btn']}
                                loading={submitLoading}
                                disabled={submitLoading}
                                onClick={() => {
                                    fetchSave('save');
                                }}
                            >
                                保存草稿
                            </Button>
                        )}
                    {((actInfo?.actState == 1 || actInfo?.actState == 2) &&
                        actInfo?.operType != 1 && // 工单的新增类型不可改
                        !isSimpleControl &&
                        !isCopy && (
                            <Button
                                className={styles['form-btn']}
                                loading={submitLoading}
                                disabled={submitLoading}
                                onClick={() => {
                                    confirm({
                                        title: `确定停止${actInfo.actName}活动?`,
                                        icon: <ExclamationCircleOutlined />,
                                        content: '',
                                        okText: '是',
                                        okType: 'danger',
                                        cancelText: '否',
                                        onOk: async () => {
                                            try {
                                                if (workorderEvent) {
                                                    workorderEvent('stop', { ...actInfo, actId });
                                                } else {
                                                    const api = isOldPage
                                                        ? stopMktActApi
                                                        : stopMngMktActApi;
                                                    await api({
                                                        actId,
                                                        parentActFlag: actInfo.parentActFlag,
                                                        categoryType: '01',
                                                    });
                                                    goBack();
                                                }
                                            } catch (error) {}
                                        },
                                        onCancel() {
                                            console.log('Cancel');
                                        },
                                    });
                                }}
                                danger
                            >
                                停止
                            </Button>
                        )) ||
                        null}
                    <Button className={commonStyles['form-btn']} onClick={goBack}>
                        返回
                    </Button>
                </div>
            </Form>

            <ConflictModal
                initRef={conflictRef}
                columns={columns}
                onFinish={(replaceFlag) => {
                    submitEvent(
                        conflictParams?.type,
                        conflictParams?.values,
                        conflictParams?.blank,
                        replaceFlag,
                    );
                }}
            />
        </Card>
    );
};

const BusinessActiveUpdatePage = (props) => {
    const {
        match,
        history,
        route,
        businessModuleActiveModel: { actInfo = {} },
    } = props;

    const {
        location: { pathname },
    } = history;

    const ref = useRef();

    const actId = match.params.actId || null;

    const isPlat = route.path.indexOf('/plat') > -1;
    const isUser = route.path.indexOf('/user') > -1;
    const isLock = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return true;
        }
        if (route.path.indexOf('/update') >= 0 && actInfo?.actState >= 2) {
            return true;
        }
        return false;
    }, [actInfo]); // 是否可编辑
    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy') >= 0) {
            return true;
        }
        return false;
    }, [actInfo]);
    const openSomeThing = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return false;
        }
        return !actInfo || actInfo?.actState == 2 || !isLock;
    }, [actInfo, isLock]); // 是否可编辑

    const goBack = () => {
        const lastMenuType = props.location?.state?.menuType || props.location?.query?.redirect;
        if (lastMenuType) {
            let path = 'businessActive/moduleAct';
            // 如果是由新页面发起的复制
            if (lastMenuType == ACT_MENU_TYPE.USER_GROUP) {
                path = 'businessActive/module-user-group';
            } else if (lastMenuType == ACT_MENU_TYPE.BASIC_PRICE) {
                path = 'businessActive/module-basic-price';
            } else if (lastMenuType == ACT_MENU_TYPE.COMPANY_ACT) {
                path = 'merchantActivityManagement/module-company-act';
            } else if (lastMenuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                path = 'merchantActivityManagement/recruitMerchantsPlan/template';
            }
            history.replace(`/marketing/${path}`);
        } else if (route.path.indexOf('/merchantActivityManagement/recruitMerchantsPlan') >= 0) {
            // 招商的需求
            const backPath = '/marketing/merchantActivityManagement/recruitMerchantsPlan/template';
            history.replace(backPath);
        } else {
            // 返回，前提是path固定三层
            const paths = pathname.split('/');
            const backPath = paths.slice(0, 4)?.join('/');
            history.replace(backPath);
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div
                    className="page-title"
                    onClick={() => {
                        ref?.current?.goBack();
                    }}
                >
                    <LeftOutlined />
                    {actInfo?.actName
                        ? `${actInfo?.actName}${isCopy ? '复制' : '编辑'} `
                        : route.name}
                </div>
            }
        >
            <BusinessActiveUpdatePageRander
                {...props}
                actId={actId}
                initRef={ref}
                isPlat={isPlat}
                isUser={isUser}
                isLock={isLock}
                isCopy={isCopy}
                openSomeThing={openSomeThing}
                goBack={goBack}
            />
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, businessModuleActiveModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    businessModuleActiveModel,
    detalistLoading:
        loading.effects['businessModuleActiveModel/initEditActInfo'] ||
        loading.effects['businessModuleActiveModel/initMngEditActInfo'] ||
        loading.effects['businessModuleActiveModel/initMngWorkorderEditActInfo'] ||
        loading.effects['businessModuleActiveModel/initWorkorderEditActInfo'],
}))(BusinessActiveUpdatePage);
