import { Form, Input, InputNumber, Modal, Radio, Select, Space, Switch, message } from 'antd';
import { MODULE_TYPES, MODULE_TYPE_CODES } from './declare';
import ActiveModuleWeekLayout from './ActiveModuleWeekLayout';
import ActiveModuleTimeLayout from './ActiveModuleTimeLayout';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';
import {
    getMktActTemplateDetailApi,
    saveMktActTemplateApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { useEffect, useState } from 'react';
import { convertFieldValuesToParams, convertModuleInfoToFieldValues } from './utils';
import { getCrowdPopTreeListApi } from '@/services/Marketing/TargetedCouponIssuanceApi';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Radio';
const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
};
const CrowdSelect = (props) => {
    const { options, value = [], onChange, isPlatform } = props;
    const onPropsChange = (value) => {
        if (isPlatform) {
            onChange(value);
        } else {
            onChange([value]);
        }
    };
    return (
        <Select
            mode={isPlatform ? 'multiple' : undefined}
            filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            showSearch
            optionFilterProp="children"
            onChange={onPropsChange}
            value={isPlatform ? value : value}
            options={options}
        />
    );
};
const BusinessModuleModal = (props) => {
    const { visible, onClose, id, onSuccess } = props;
    const [form] = Form.useForm();
    const templateType = Form.useWatch('templateType', form);
    const templateActType = Form.useWatch('templateActType', form);
    const templateSubType = Form.useWatch('templateSubType', form);
    const cooperationPlatform = Form.useWatch('cooperationPlatform', form);
    const [platformCrowdList, setPlatformCrowdList] = useState([]);
    const [businessCrowdList, setBusinessCrowdList] = useState([]);

    const [loading, setLoading] = useState(false);
    useEffect(() => {
        if (id) {
            getMktActTemplateDetailApi(id).then((res) => {
                const { data } = res;
                const { availableStatus } = data;
                data.availableStatus = availableStatus === '01' ? true : false;
                form.setFieldsValue({ ...data, ...convertModuleInfoToFieldValues(data) });
            });
        }
    }, [id]);
    useEffect(() => {
        if (cooperationPlatform) {
            // 后端说先写死大一些的数据
            // 平台人群
            getCrowdPopTreeListApi({
                labelType: '05,04',
                useModel: '03',
                actType: '02',
                cooperationPlatform,
            }).then((res) => {
                const { data: records = [] } = res;
                records.map((item, index) => {
                    item.label = item.crowdName;
                    item.value = item.crowdId;
                    return item;
                });
                setPlatformCrowdList(records);
            });
            // 商家人群
            getCrowdPopTreeListApi({
                labelType: '07',
                useModel: '03',
                actType: '02',
                cooperationPlatform,
            }).then((res) => {
                const { data: records = [] } = res;
                records.map((item, index) => {
                    item.label = item.crowdName;
                    item.value = item.crowdId;
                    return item;
                });
                setBusinessCrowdList(records);
            });
        }
    }, [cooperationPlatform]);
    const onFinish = (values) => {
        const { availableStatus, templateSubType } = values;
        values.availableStatus = availableStatus ? '01' : '09';
        if (templateType == '01') {
            // 人群模板，需要把crowdDetailId塞到数组里面一起传
            const list = [];
            const crowdInfo = values[`detail_${templateType}`]?.[0];
            const { activeCrowd } = crowdInfo;
            activeCrowd?.map((crowdId: string) => {
                const crowdObj: any =
                    templateSubType == '01'
                        ? platformCrowdList.find?.((crowd: any) => crowd.crowdId == crowdId)
                        : businessCrowdList.find?.((crowd: any) => crowd.crowdId == crowdId);
                if (crowdObj) {
                    list.push(crowdObj);
                }
            });
            if (list.length) {
                crowdInfo.crowdDetailList = list;
            }
        }
        const params = convertFieldValuesToParams(values);
        if (typeof params?.details === 'string' && params?.details) {
            params.details = JSON.parse(params.details);
            delete params.dctValue;
        }
        params.createSource = '02';
        setLoading(true);
        saveMktActTemplateApi(params)
            .then(() => {
                message.success('操作成功');
                onSuccess();
            })
            .finally(() => setLoading(false));
    };
    const onChangeRadio = (value) => {
        form.setFieldValue(`detail_${templateType}`, [{}]);
    };
    return (
        <Modal
            width={800}
            title={id ? '编辑模板' : '新增模板'}
            visible={visible}
            onCancel={onClose}
            destroyOnClose
            confirmLoading={loading}
            onOk={() => form.submit()}
        >
            <Form form={form} onFinish={onFinish} {...formItemLayout}>
                <Form.Item hidden name="templateId" />
                <Form.Item
                    label="模板分类"
                    name="templateType"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Select placeholder="请选择模板分类" options={MODULE_TYPES} />
                </Form.Item>
                <Form.Item
                    label="模板名称"
                    name="templateName"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input
                        placeholder="请输入模板名称，建议每个模板不一致"
                        showCount
                        maxLength={15}
                        autoComplete="off"
                    />
                </Form.Item>
                {templateType == MODULE_TYPE_CODES.GROUP && (
                    <>
                        <Form.Item
                            label="平台"
                            name="cooperationPlatform"
                            rules={[
                                {
                                    required: true,
                                    message: '请选择',
                                },
                            ]}
                            initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                        >
                            <SelectCooperationPlatform></SelectCooperationPlatform>
                        </Form.Item>
                        {cooperationPlatform && (
                            <Form.Item label="模板内容" required style={{ marginBottom: 0 }}>
                                <Form.Item
                                    name="templateSubType"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择',
                                        },
                                    ]}
                                >
                                    <Radio.Group onChange={(e) => onChangeRadio(e.target.value)}>
                                        <Radio value={'01'}>平台人群</Radio>
                                        <Radio value={'02'}>商家人群</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                {templateSubType && (
                                    <Form.List name={`detail_${templateType}`} initialValue={[{}]}>
                                        {(fields, { add, remove }) => (
                                            <>
                                                {fields.map((field, index) => {
                                                    return (
                                                        <Form.Item
                                                            key={field.key}
                                                            name={[field.name, 'activeCrowd']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择',
                                                                },
                                                            ]}
                                                        >
                                                            <CrowdSelect
                                                                isPlatform={
                                                                    templateSubType === '01'
                                                                }
                                                                options={
                                                                    templateSubType === '01'
                                                                        ? platformCrowdList
                                                                        : templateSubType === '02'
                                                                        ? businessCrowdList
                                                                        : []
                                                                }
                                                            />
                                                        </Form.Item>
                                                    );
                                                })}
                                            </>
                                        )}
                                    </Form.List>
                                )}
                            </Form.Item>
                        )}
                    </>
                )}

                {templateType == MODULE_TYPE_CODES.WEEK && (
                    <ActiveModuleWeekLayout
                        form={form}
                        formItemLayout={formItemLayout}
                        editId={id}
                        maxCount={5}
                        // name={name}  // 因为内部还要区分是按周、按日，不由此传参，约定好detail_0201和detail_0202
                    />
                )}
                {templateType == MODULE_TYPE_CODES.TIME && (
                    <ActiveModuleTimeLayout
                        form={form}
                        formItemLayout={formItemLayout}
                        maxCount={5}
                        name={`detail_${templateType}`}
                    />
                )}
                {templateType == MODULE_TYPE_CODES.PRICE && (
                    <>
                        <Form.Item label="模板内容" required style={{ marginBottom: 0 }}>
                            <Form.Item
                                name="templateActType"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择活动',
                                    },
                                ]}
                            >
                                <Radio.Group>
                                    <Radio value={'02'}>打折</Radio>
                                    <Radio value={'10'}>立减</Radio>
                                </Radio.Group>
                            </Form.Item>
                            {templateActType && (
                                <Form.Item>
                                    <Space>
                                        {templateActType === '02'
                                            ? '服务费折扣'
                                            : templateActType === '10'
                                            ? '服务费立减'
                                            : ''}
                                        <Form.Item
                                            noStyle
                                            name="dctValue"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请填写',
                                                },
                                            ]}
                                        >
                                            <InputNumber
                                                precision={templateActType === '10' ? 4 : 2}
                                                step={templateActType === '10' ? 0.0001 : 0.01}
                                                min={0}
                                                max={10}
                                                placeholder="请填写"
                                            />
                                        </Form.Item>
                                        {templateActType === '02'
                                            ? '折'
                                            : templateActType === '10'
                                            ? '元/度'
                                            : ''}
                                    </Space>
                                </Form.Item>
                            )}
                        </Form.Item>
                    </>
                )}

                <Form.Item
                    label="推荐文案"
                    name="recommendDesc"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Input
                        placeholder="请输入内容会展示在商家后台"
                        showCount
                        maxLength={15}
                        autoComplete="off"
                    />
                </Form.Item>
                <Form.Item
                    label="状态"
                    name="availableStatus"
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                    initialValue
                    valuePropName="checked"
                >
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default BusinessModuleModal;
