import { Col, Form, Input, InputNumber, Row, Select, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import { getFinanceDepListApi } from '@/services/Marketing/MarketingWorkorderApi';
import { useRequest } from 'ahooks';
import { isEmpty } from 'lodash';

const FundingDepartmentForm = (props) => {
    const { value = [], onChange, disabled } = props;
    const {
        run,
        loading,
        data: optionsData,
    } = useRequest(() => {
        return getFinanceDepListApi().then((res) => res?.data);
    });

    const onChangeSelect = (values = [], info = []) => {
        const newValue = JSON.parse(JSON.stringify(value));
        const newData = values.map((item) => {
            const valueObj = newValue.filter((items) => items.departmentId === item.value);
            let newItem = item;
            if (!isEmpty(valueObj)) {
                newItem = Object.assign(item, valueObj[0]);
            }
            newItem.departmentId = newItem.value || newItem.departmentId;
            newItem.departmentName = newItem.label || newItem.departmentName;
            return newItem;
        });
        onChange(newData);
    };
    const valueToDep = (array = []) => {
        return array.map((item: any) => {
            // 没有修改过，才自动填写，否则以手动修改的数值作为展示
            if (!item?.edited && array?.length == 1) {
                item.contributRatio = 100;
            } else if (!item?.edited && !item?.contributRatio) {
                item.contributRatio = 0;
            }
            item.label = item.label || item.departmentName;
            item.value = item.value || item.departmentId;
            return item;
        });
    };

    const onChangeInputNumber = (val: number | string | null, id: number) => {
        const newData = value.map((item: any) => {
            item.edited = true; // 编辑过的做个记录
            if (item.departmentId === id || item.value === id) {
                item.contributRatio = val;
            }
            return item;
        });
        onChange(newData);
    };
    return (
        <>
            <Select
                options={optionsData}
                fieldNames={{ label: 'departmentName', value: 'departmentId' }}
                placeholder="请选择出资部门"
                mode="multiple"
                labelInValue
                disabled={disabled}
                value={valueToDep(value)}
                onChange={onChangeSelect}
            />
            {
                <Row gutter={[20, 20]} style={{ marginTop: 20 }}>
                    {value.map((item, i) => {
                        return (
                            <>
                                <Col span={8}>
                                    <span
                                        style={{
                                            alignContent: 'center',
                                            display: 'flex',
                                            flexWrap: 'wrap',
                                            height: '100%',
                                        }}
                                    >
                                        {item.label}
                                    </span>
                                </Col>
                                <Col span={16}>
                                    <InputNumber
                                        disabled={disabled}
                                        min={0}
                                        max={100}
                                        addonAfter={'%'}
                                        value={item.contributRatio}
                                        onChange={(value) => onChangeInputNumber(value, item.value)}
                                    />
                                </Col>
                            </>
                        );
                    })}
                </Row>
            }
        </>
    );
};

export default FundingDepartmentForm;
