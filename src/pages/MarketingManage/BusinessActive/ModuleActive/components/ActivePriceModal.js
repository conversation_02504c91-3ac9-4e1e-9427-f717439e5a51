import { Fragment, useState, useEffect } from 'react';
import { Form, Input, Modal, Radio, Checkbox, Col } from 'antd';
import { useImperativeHandle } from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { MODULE_TYPES, MODULE_TYPE_CODES, MODULE_ACT_TYPE_CODES } from './declare';
import ActiveModulePriceLayout from './ActiveModulePriceLayout';
import { convertModuleInfoToFieldValues, convertFieldValuesToParams } from './utils';
import {
    saveModuleActTemplateApi,
    getModuleActTemplateDetailApi,
    getModuleActTemplateContentApi,
    getModuleActTemplateListApi,
    getActTemplateListMngApi,
    getActTemplateDetailMngApi,
    saveModuleActTemplateMngApi,
    getModuleActTemplateContentMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import commonStyles from '@/assets/styles/common.less';
import { ACT_MENU_TYPE } from './ActModuleConfig';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const formItemLayout = {
    labelCol: {
        flex: '0 0 118px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, tableLoading, exportFormEvent } = props;

    const resetEvent = () => {
        onReset();
    };

    return (
        <Form
            form={form}
            onFinish={onSubmit}
            initialValues={{}}
            scrollToFirstError
            component={'div'}
        >
            <SearchOptionsBar form={form} loading={tableLoading} onReset={resetEvent}>
                <Col span={16}>
                    <Form.Item name="templateName" label="模板名称" {...formItemLayout}>
                        <Input placeholder="请填写" allowClear autoComplete="off" />
                    </Form.Item>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ModalList = (props) => {
    const { belongType, templateType, onFinish, currentUser } = props;
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    // useEffect(() => {
    //     resetData();
    // }, [belongType, templateType]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const [list, updateList] = useState([]);
    const [total, updateListTotal] = useState(0);
    const searchData = async () => {
        const data = form.getFieldsValue();

        let params = {
            templateType: templateType,
            actBelongType: belongType,
            templateName: data.templateName || undefined,
            operType: (currentUser?.operId?.length && '01') || '02',
        };

        params.pageNum = pageInfo.pageIndex;
        params.totalNum = pageInfo.pageSize;

        try {
            const {
                data: { records: moduleTemplateList, total: moduleTemplateTotal },
            } = await getActTemplateListMngApi(params);
            updateList(moduleTemplateList);
            updateListTotal(moduleTemplateTotal);
        } catch (error) {}
    };

    const handleSearch = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '模板名称',
            width: 100,
            dataIndex: 'templateName',
            render(text, record) {
                return (
                    <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '活动类型',
            width: 120,
            dataIndex: 'templateBelongTypeName',
            render(text, record) {
                return (
                    <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '营销方式',
            width: 120,
            dataIndex: 'templateActTypeName',
            render(text, record) {
                return (
                    <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '模板内容',
            width: 300,
            dataIndex: 'templateDetail',
            render(text, record) {
                return (
                    <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '操作',
            width: 60,
            fixed: 'right',
            render: (text, record) => (
                <Fragment>
                    <span
                        className={commonStyles['table-btn']}
                        onClick={() => {
                            onFinish?.(record);
                        }}
                    >
                        选择
                    </span>
                </Fragment>
            ),
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                {...props}
                form={form}
                onSubmit={handleSearch}
                onReset={resetData}
                exportFormEvent={() => searchData(true)}
            />
            <TablePro
                name="operatorlist"
                loading={false}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={list}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: total,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                filterHeader={false}
                offsetHeader={0}
            />
        </Fragment>
    );
};

const ActivePriceModal = (props) => {
    const { initRef, onFinish } = props;

    const [activeForm] = Form.useForm();
    const [visible, changeVisible] = useState(false);
    const [modalShowFooter, updateModalShowFooter] = useState(undefined);
    const [modalWidth, updateModalWidth] = useState(0);

    const [openParams, updateOpenParams] = useState(null);
    const [menuType, updateMenuType] = useState(ACT_MENU_TYPE.ACT_2);
    const templateActType = Form.useWatch('templateActType', activeForm);
    useImperativeHandle(initRef, () => ({
        show: ({
            belongType: _belongType,
            actParams = {},
            openParams: _editParams,
            menuType: _menuType,
        } = {}) => {
            changeVisible(true);
            updateOpenParams(_editParams);

            if (_menuType) {
                updateMenuType(_menuType);
            }

            activeForm.resetFields();
            activeForm.setFieldsValue({
                ...actParams,
                templateType: MODULE_TYPE_CODES.PRICE,
                belongType: _belongType,
                adjustDetailList: actParams.adjustDetailList || undefined, // 过滤掉null
                adjustPriceType: actParams.adjustPriceType || undefined, // 过滤掉null
            });
        },
    }));

    const onClose = () => {
        changeVisible(false);
    };

    const changeType = (type, templateActType) => {
        updateModalShowFooter(type == '01' ? undefined : false);

        let width = 620;
        if (type == '02') {
            width = 880;
        } else if (templateActType == MODULE_ACT_TYPE_CODES.DYNAMIC) {
            width = 1660;
        }
        updateModalWidth(width);
    };

    return (
        <Modal
            title={'活动优惠设置'}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            width={modalWidth}
            footer={modalShowFooter}
            onOk={() => {
                activeForm
                    .validateFields()
                    .then(async (data) => {
                        let fixNum = 4;
                        if (
                            [
                                MODULE_ACT_TYPE_CODES.DISCOUNT,
                                MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT,
                            ].includes(data.templateActType)
                        ) {
                            fixNum = 2;
                        }
                        data.dctValue =
                            data?.dctValue || data?.dctValue == 0
                                ? Number(data?.dctValue)?.toFixed(fixNum)
                                : undefined;
                        data.vthreeDiscountValue =
                            data?.vthreeDiscountValue || data?.vthreeDiscountValue == 0
                                ? Number(data?.vthreeDiscountValue)?.toFixed(fixNum)
                                : undefined;
                        data.vipDiscountValue =
                            data?.vipDiscountValue || data?.vipDiscountValue == 0
                                ? Number(data?.vipDiscountValue)?.toFixed(fixNum)
                                : undefined;
                        data.proVipDiscountValue =
                            data?.proVipDiscountValue || data?.proVipDiscountValue == 0
                                ? Number(data?.proVipDiscountValue)?.toFixed(fixNum)
                                : undefined;

                        const saveFlag = data.saveFlag?.indexOf('1') >= 0;
                        delete data.setType;
                        delete data.saveFlag;
                        const params = convertFieldValuesToParams({ ...data });
                        let res;
                        try {
                            // 需要后端接口返回模板内容后回传出去
                            if (saveFlag) {
                                params.templateStatus = '0'; // 模板状态;0可用 1删除，另外一个场景传1
                                params.contentFlag = true; // 保存后，接口会返回模板内容
                                const {
                                    data: { templateContent },
                                } = await saveModuleActTemplateMngApi(params);
                                res = templateContent;
                            } else {
                                // 通过获取模板内容接口取得数据后回调
                                const {
                                    data: { templateContent },
                                } = await getModuleActTemplateContentMngApi(params);
                                res = templateContent;
                            }
                            data.adjustDetailList?.map((ele, index) => {
                                let eleFixNum = 4;
                                if (ele.adjustType == '01' || ele.adjustType == '04') {
                                    eleFixNum = 2;
                                }
                                ele.adjustValue =
                                    ele?.adjustValue || ele?.adjustValue == 0
                                        ? Number(ele?.adjustValue)?.toFixed(eleFixNum)
                                        : undefined;
                                ele.adjustVipValue =
                                    ele?.adjustVipValue || ele?.adjustVipValue == 0
                                        ? Number(ele?.adjustVipValue)?.toFixed(eleFixNum)
                                        : undefined;
                                ele.proVipDiscountValue =
                                    ele?.proVipDiscountValue || ele?.proVipDiscountValue == 0
                                        ? Number(ele?.proVipDiscountValue)?.toFixed(eleFixNum)
                                        : undefined;
                                ele.adjustThreeDiscountValue =
                                    ele?.adjustThreeDiscountValue ||
                                    ele?.adjustThreeDiscountValue == 0
                                        ? Number(ele?.adjustThreeDiscountValue)?.toFixed(eleFixNum)
                                        : undefined;
                            });
                            const callbackObj = {
                                ...data,
                                templateName: undefined,
                                templateDetail: res,
                            };
                            onFinish?.(callbackObj, openParams);
                            onClose();
                        } catch (error) {}
                    })
                    .catch();
            }}
        >
            <Form
                form={activeForm}
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
            >
                <Form.Item name="templateType" noStyle />
                <Form.Item name="belongType" noStyle />

                {(menuType != ACT_MENU_TYPE.USER_GROUP && (
                    <Form.Item
                        label="配置方式"
                        name="setType"
                        initialValue={MODULE_TYPES?.[0]?.value}
                        {...formItemLayout}
                        rules={[
                            {
                                required: true,
                                whitespace: true,
                                message: '请输入',
                            },
                        ]}
                    >
                        <Radio.Group>
                            {[
                                { label: '手动添加', value: '01' },
                                { label: '使用模板', value: '02' },
                            ].map((ele, index) => (
                                <Radio key={index} value={ele.value}>
                                    {ele.label}
                                </Radio>
                            ))}
                        </Radio.Group>
                    </Form.Item>
                )) || <Form.Item name="setType" noStyle initialValue={'01'} />}

                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.setType !== curValues.setType ||
                        prevValues.templateActType !== curValues.templateActType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const setType = getFieldValue('setType');
                        const templateActType = getFieldValue('templateActType');
                        changeType(setType, templateActType);
                    }}
                </Form.Item>

                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.setType !== curValues.setType ||
                        prevValues.saveFlag !== curValues.saveFlag ||
                        prevValues.templateType !== curValues.templateType ||
                        prevValues.adjustPriceType !== curValues.adjustPriceType ||
                        prevValues.belongType !== curValues.belongType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const setType = getFieldValue('setType');
                        const belongType = getFieldValue('belongType');
                        const saveFlag = getFieldValue('saveFlag');
                        const templateType = getFieldValue('templateType');
                        const adjustPriceType = getFieldValue('adjustPriceType');
                        return (
                            (setType == '01' && (
                                <Fragment>
                                    <ActiveModulePriceLayout
                                        {...props}
                                        menuType={menuType}
                                        form={activeForm}
                                        formItemLayout={formItemLayout}
                                        formItemFixedWidthLayout={formItemFixedWidthLayout}
                                        belongType={belongType}
                                    />

                                    {menuType == ACT_MENU_TYPE.USER_GROUP ||
                                    templateActType == MODULE_ACT_TYPE_CODES.PRICE ||
                                    adjustPriceType == ADJUST_PRICE_TYPES.SETTLE ? (
                                        <Form.Item name="saveFlag" noStyle />
                                    ) : (
                                        <Form.Item
                                            label=" "
                                            colon={false}
                                            name="saveFlag"
                                            {...formItemLayout}
                                        >
                                            <Checkbox.Group>
                                                <Checkbox value="1">保存为优惠模板</Checkbox>
                                            </Checkbox.Group>
                                        </Form.Item>
                                    )}

                                    {(saveFlag?.indexOf('1') >= 0 && (
                                        <Form.Item
                                            label="模板名称"
                                            name="templateName"
                                            {...formItemFixedWidthLayout}
                                            rules={[
                                                {
                                                    required: true,
                                                    whitespace: true,
                                                    message: '请输入',
                                                },
                                            ]}
                                        >
                                            <Input
                                                placeholder="请填写"
                                                maxLength={30}
                                                showCount
                                                autoComplete="off"
                                            />
                                        </Form.Item>
                                    )) ||
                                        null}
                                </Fragment>
                            )) ||
                            (setType == '02' && (
                                <ModalList
                                    belongType={belongType}
                                    templateType={templateType}
                                    onFinish={async (record) => {
                                        try {
                                            const { data } = await getActTemplateDetailMngApi({
                                                templateId: record.templateId,
                                            });
                                            const params = convertModuleInfoToFieldValues(data);
                                            onFinish?.(
                                                {
                                                    ...params,
                                                    subChargeFeeFlag:
                                                        params.subChargeFeeFlag == '1',
                                                    templateName: undefined,
                                                    templateDetail: record.templateDetail,
                                                },
                                                openParams,
                                            );
                                            onClose();
                                        } catch (error) {}
                                    }}
                                />
                            )) ||
                            null
                        );
                    }}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ActivePriceModal;
