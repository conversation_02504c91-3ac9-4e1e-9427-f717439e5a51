import { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { Form, Input, Modal, Radio, Checkbox } from 'antd';
import { useImperativeHandle } from 'react';

import {
    MODULE_TYPES,
    MODULE_TYPE_CODES,
    MODULE_WEEK_TYPE_CODES,
    MODULE_ACT_TYPE_CODES,
} from './declare';
import { convertModuleInfoToFieldValues, convertFieldValuesToParams } from './utils';
import { MODELTYPS } from '../../BusinessConfig';
import ActiveModuleGroupLayout from './ActiveModuleGroupLayout';
import ActiveModuleWeekLayout from './ActiveModuleWeekLayout';
import ActiveModuleTimeLayout from './ActiveModuleTimeLayout';
import ActiveModulePriceLayout from './ActiveModulePriceLayout';

import {
    saveModuleActTemplateApi,
    saveModuleActTemplateMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import moment from 'moment';

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const ModuleEditModal = (props) => {
    const {
        dispatch,
        initRef,
        businessModuleActiveModel: { moduleTemplateInfo },
        onFinish,
        zIndex = 100,
    } = props;

    const [moduleForm] = Form.useForm();
    const [visible, changeVisible] = useState(false);
    const [editId, updateEditId] = useState();
    const [defaultType, updateDefaultType] = useState();
    const [belongType, updateDelongType] = useState();
    const [modalWidth, updateModalWidth] = useState(0);

    const [openParams, updateOpenParams] = useState(null);
    useImperativeHandle(initRef, () => ({
        show: ({
            id,
            defaultType: _defaultType,
            belongType: _belongType,
            openParams: _editParams,
        }) => {
            moduleForm.resetFields();
            updateEditId(id);
            updateDefaultType(_defaultType);
            updateDelongType(_belongType);
            updateOpenParams(_editParams);
            changeVisible(true);

            if (id) {
                dispatch({
                    type: 'businessModuleActiveModel/getModuleActTemplateInfo',
                    options: { templateId: id },
                });
            }
            moduleForm.setFieldsValue({ templateType: _defaultType, belongType: _belongType });
        },
    }));

    useEffect(() => {
        if (moduleTemplateInfo) {
            const params = convertModuleInfoToFieldValues(moduleTemplateInfo);
            moduleForm.setFieldsValue({ ...params });
        }
    }, [moduleTemplateInfo]);

    const onClose = () => {
        changeVisible(false);
        dispatch({
            type: 'businessModuleActiveModel/updateProperty',
            params: {
                moduleTemplateInfo: undefined,
            },
        });
    };

    const changeType = (type, templateSubType) => {
        let width = 620;
        if (type == MODULE_TYPE_CODES.WEEK) {
            if (templateSubType == MODULE_WEEK_TYPE_CODES.WEEK) {
                width = 880;
            }
        } else if (type == MODULE_TYPE_CODES.PRICE) {
            const templateActType = moduleForm.getFieldValue('templateActType');
            if (templateActType == '30') {
                width = 1580;
            }
        }
        updateModalWidth(width);
    };

    return (
        <Modal
            title={editId ? '编辑模板' : '新增模板'}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            width={modalWidth}
            zIndex={zIndex}
            bodyStyle={{
                paddingLeft: 12,
                paddingRight: 12,
            }}
            onOk={() => {
                moduleForm.validateFields().then(async (data) => {
                    const params = convertFieldValuesToParams(data);
                    // 模板状态;0可用 1删除 templateStatus;
                    params.templateStatus = data?.saveFlag?.includes('1') ? '0' : '1';
                    params.templateName = data?.templateName || '';
                    params.createSource = '01';
                    delete params.saveFlag;
                    try {
                        const {
                            data: { templateId },
                        } = await saveModuleActTemplateMngApi(params);
                        onFinish?.(
                            {
                                templateType: params.templateType,
                                id: templateId,
                            },
                            openParams,
                        );
                        updateOpenParams(null);
                        onClose();
                    } catch (error) {}
                });
            }}
            destroyOnClose
        >
            <Form
                form={moduleForm}
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
            >
                <Form.Item name="templateId" noStyle />

                <Form.Item shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const templateType = getFieldValue('templateType');
                        const templateSubType = getFieldValue('templateSubType');
                        changeType(templateType, templateSubType);

                        return (
                            <Form.Item
                                label="模板类型"
                                name="templateType"
                                initialValue={MODULE_TYPES?.[0]?.value}
                                {...formItemLayout}
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: '请输入',
                                    },
                                ]}
                            >
                                {editId || defaultType?.length ? (
                                    <span>{`${
                                        MODULE_TYPES.find((ele) => ele.value == templateType)?.label
                                    }`}</span>
                                ) : (
                                    <Radio.Group>
                                        {MODULE_TYPES.map((ele, index) => (
                                            <Radio key={index} value={ele.value}>
                                                {ele.label}
                                            </Radio>
                                        ))}
                                    </Radio.Group>
                                )}
                            </Form.Item>
                        );
                    }}
                </Form.Item>

                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.templateType !== curValues.templateType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const templateType = getFieldValue('templateType');
                        const name = `detail_${templateType}`;
                        return (
                            (templateType == MODULE_TYPE_CODES.GROUP && (
                                <ActiveModuleGroupLayout
                                    form={moduleForm}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    name={name}
                                    multiple
                                />
                            )) ||
                            (templateType == MODULE_TYPE_CODES.WEEK && (
                                <ActiveModuleWeekLayout
                                    multiple
                                    form={moduleForm}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    editId={editId}
                                    // name={name}  // 因为内部还要区分是按周、按日，不由此传参，约定好detail_0201和detail_0202
                                />
                            )) ||
                            (templateType == MODULE_TYPE_CODES.TIME && (
                                <ActiveModuleTimeLayout
                                    multiple
                                    form={moduleForm}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    name={name}
                                />
                            )) ||
                            (templateType == MODULE_TYPE_CODES.PRICE && (
                                <ActiveModulePriceLayout
                                    {...props}
                                    menuType={'06'} //模板编码
                                    form={moduleForm}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    name={name}
                                    belongType={belongType}
                                />
                            )) ||
                            null
                        );
                    }}
                </Form.Item>

                {(!editId && (
                    <Form.Item
                        label=" "
                        colon={false}
                        name="saveFlag"
                        initialValue={['1']}
                        {...formItemLayout}
                    >
                        <Checkbox.Group>
                            <Checkbox value="1">保存为模板</Checkbox>
                        </Checkbox.Group>
                    </Form.Item>
                )) ||
                    null}

                <Form.Item shouldUpdate={(pre, cur) => pre.saveFlag != cur.saveFlag} noStyle>
                    {({ getFieldValue }) => {
                        const saveFlag = getFieldValue('saveFlag');
                        return saveFlag?.includes('1') || editId ? (
                            <Form.Item
                                label="模板名称"
                                name="templateName"
                                {...formItemFixedWidthLayout}
                                rules={[
                                    {
                                        required: true,
                                        whitespace: true,
                                        message: '请输入',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="请填写"
                                    maxLength={30}
                                    showCount
                                    autoComplete="off"
                                />
                            </Form.Item>
                        ) : null;
                    }}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ModuleEditModal;
