import { MODULE_TYPE_CODES, MODULE_WEEK_TYPE_CODES, MODULE_ACT_TYPE_CODES } from './declare';
import { MODELTYPS } from '../../BusinessConfig';
import moment from 'moment';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

// 判断时段是否重叠，可前后一致
export const checkTimeOverlap = (timeA, timeB) => {
    if (!Array.isArray(timeA) || !Array.isArray(timeB)) return false;
    const maxA = timeA[0].valueOf();
    const maxB = timeB[0].valueOf();
    const minA = timeA[1].valueOf();
    const minB = timeB[1].valueOf();
    const max = [Math.floor(maxA / 1000), Math.floor(maxB / 1000)];
    const min = [Math.floor(minA / 1000), Math.floor(minB / 1000)];
    return Math.max.apply(null, max) < Math.min.apply(null, min);
};

// 判断日期是否重叠
export const checkDayOverlap = (dayA, dayB) => {
    if (!dayA?.['start'] || !dayA?.['end'] || !dayB?.['start'] || !dayB?.['end']) return false;
    const maxA = dayA['start'].valueOf();
    const maxB = dayB['start'].valueOf();
    const minA = dayA['end'].valueOf();
    const minB = dayB['end'].valueOf();
    const max = [maxA, maxB];
    const min = [minA, minB];
    return Math.max.apply(null, max) <= Math.min.apply(null, min);
};

// 模板详情数据拆分为页面展示用的结构
export const convertModuleInfoToFieldValues = (info) => {
    if (!info) {
        return {};
    }
    const detail = [];
    const otherParams = {};
    let contentKey = `detail_${info.templateType}`;
    info.detailBoList?.map((ele) => {
        if (info.templateType == MODULE_TYPE_CODES.GROUP) {
            detail.push({ activeCrowd: ele.ruleValue.split?.(',') || [ele.ruleValue] });
        } else if (info.templateType == MODULE_TYPE_CODES.WEEK) {
            const rules = ele.ruleValue?.split?.(',');
            contentKey = `detail_${info.templateSubType}`;
            if (info.templateSubType == MODULE_WEEK_TYPE_CODES.WEEK) {
                detail.push(rules);
            } else if (info.templateSubType == MODULE_WEEK_TYPE_CODES.DAY) {
                const ruleValue = [];
                rules.map((ruleEle) => {
                    if (ruleEle.indexOf('~') >= 0) {
                        const values = ruleEle.split?.('~');
                        ruleValue.push({ start: values?.[0], end: values?.[1] });
                    }
                });
                detail.push({ rules: ruleValue });
            }
        } else if (info.templateType == MODULE_TYPE_CODES.TIME) {
            const rules = ele.ruleValue?.split?.(',');
            const ruleValue = [];
            rules.map((ruleEle) => {
                if (ruleEle.indexOf('~') >= 0) {
                    const values = ruleEle.split?.('~');
                    ruleValue.push([
                        values?.[0] && moment(values?.[0], 'HH:mm'),
                        values?.[0] && moment(values?.[1], 'HH:mm'),
                    ]);
                }
            });
            detail.push(ruleValue);
        } else if (info.templateType == MODULE_TYPE_CODES.PRICE) {
            if (
                info.templateActType == MODULE_ACT_TYPE_CODES.DISCOUNT ||
                info.templateActType == MODULE_ACT_TYPE_CODES.REDUCTION ||
                info.templateActType == MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT ||
                info.templateActType == MODULE_ACT_TYPE_CODES.CROWDPLLICY_REDUCTION
            ) {
                otherParams.subChargeFeeFlag = info.subChargeFeeFlag == '1';
                const detailObj = info.detailBoList?.[0];
                if (detailObj) {
                    otherParams.dctValue = detailObj.ruleValue; // 服务费
                    otherParams.vthreeDiscountValue = detailObj.vthreeDiscountValue; // V3专享立减
                    otherParams.vipDiscountValue = detailObj.ruleVipValue; // 会员立减
                    otherParams.proVipDiscountValue = detailObj.proVipDiscountValue; // 会员立减
                }
            } else {
                // 动态调价
                otherParams.adjustDetailList = info.detailBoList?.map((ele) => ({
                    adjustValue: ele.ruleValue,
                    vthreeDiscountValue: ele.vthreeDiscountValue,
                    adjustThreeDiscountValue: ele.vthreeDiscountValue,
                    adjustVipValue: ele.ruleVipValue,
                    proVipDiscountValue: ele.proVipDiscountValue,
                    adjustType: ele.ruleAdjustType,
                    adjustMethodType: ele.ruleMethodType,
                    vthreeMethodType: ele.vthreeMethodType,
                    vipAdjustMethodType: ele.ruleVipMethodType,
                    proVipMethodType: ele.proVipMethodType,
                    adjustSn: ele.ruleSn,
                }));

                otherParams.adjustPriceType =
                    info.templateSubType?.substr(2, 2) || ADJUST_PRICE_TYPES.DEFAULT;
            }
            otherParams.belongType = info.templateBelongType;
        } else {
            detail.push({ ruleValue: ele.ruleValue });
        }
    });
    const params = {
        ...info,
        ...otherParams,
        detailBoList: undefined,
        [contentKey]: detail,
    };
    console.debug('params', params);
    return params;
};

// 把页面数据转换为提交参数
export const convertFieldValuesToParams = (data) => {
    if (!data) {
        return {};
    }
    const templateType = data.templateType;
    const details = [];
    const otherParams = {};
    let key = `detail_${templateType}`;
    if (templateType == MODULE_TYPE_CODES.GROUP) {
        data[key].map((ele) => {
            details.push({
                ruleValue: ele.activeCrowd?.join(',') || ele.activeCrowd,
                crowdDetailList: ele.crowdDetailList,
            });
        });
    } else if (templateType == MODULE_TYPE_CODES.WEEK) {
        key = `detail_${data.templateSubType}`;
        data[key].map((ele) => {
            if (ele.rules) {
                // 按日，分级取
                const rules = [];
                ele.rules.map((ruleEle) => {
                    const ruleValue = `${ruleEle.start}~${ruleEle.end}`;
                    rules.push(ruleValue);
                });
                details.push({
                    ruleValue: rules.join?.(',') || rules,
                });
            } else {
                details.push({
                    ruleValue: ele?.join?.(',') || ele,
                });
            }
        });
    } else if (templateType == MODULE_TYPE_CODES.TIME) {
        data[key].map((ele) => {
            const rules = [];
            ele?.map((ruleEle) => {
                const [start, end] = ruleEle;
                rules.push(`${start.format('HH:mm')}~${end.format('HH:mm')}`);
            });
            details.push({
                ruleValue: rules?.join(',') || rules,
            });
        });
    } else if (templateType == MODULE_TYPE_CODES.PRICE) {
        const templateActType = data.templateActType;
        if (
            templateActType == MODULE_ACT_TYPE_CODES.DISCOUNT ||
            templateActType == MODULE_ACT_TYPE_CODES.REDUCTION ||
            templateActType == MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT ||
            templateActType == MODULE_ACT_TYPE_CODES.CROWDPLLICY_REDUCTION ||
            templateActType == MODULE_ACT_TYPE_CODES.PRICE ||
            templateActType == MODULE_ACT_TYPE_CODES.CROWDPLLICY_PRICE
        ) {
            let eleFixNum = 4;
            if (
                [
                    MODULE_ACT_TYPE_CODES.DISCOUNT,
                    MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT,
                ].includes(templateActType)
            ) {
                eleFixNum = 2;
            }
            const dctValue =
                data?.dctValue || data?.dctValue == 0
                    ? Number(data?.dctValue)?.toFixed(eleFixNum)
                    : undefined;
            const vipDiscountValue =
                data?.vipDiscountValue || data?.vipDiscountValue == 0
                    ? Number(data?.vipDiscountValue)?.toFixed(eleFixNum)
                    : undefined;
            const proVipDiscountValue =
                data?.proVipDiscountValue || data?.proVipDiscountValue == 0
                    ? Number(data?.proVipDiscountValue)?.toFixed(eleFixNum)
                    : undefined;
            const vthreeDiscountValue =
                data?.vthreeDiscountValue || data?.vthreeDiscountValue == 0
                    ? Number(data?.vthreeDiscountValue)?.toFixed(eleFixNum)
                    : undefined;
            details.push({
                ruleValue: dctValue,
                vthreeDiscountValue: vthreeDiscountValue,
                ruleVipValue: vipDiscountValue,
                proVipDiscountValue: proVipDiscountValue,
            });

            otherParams.subChargeFeeFlag = data.subChargeFeeFlag ? '1' : '0';
        } else {
            // 动态调价
            data.adjustDetailList?.map((ele, index) => {
                let eleFixNum = 4;
                if (ele.adjustType == '01' || ele.adjustType == '04') {
                    eleFixNum = 2;
                }
                const adjustValue =
                    ele?.adjustValue || ele?.adjustValue == 0
                        ? Number(ele?.adjustValue)?.toFixed(eleFixNum)
                        : undefined;
                const adjustVipValue =
                    ele?.adjustVipValue || ele?.adjustVipValue == 0
                        ? Number(ele?.adjustVipValue)?.toFixed(eleFixNum)
                        : undefined;
                const proVipDiscountValue =
                    ele?.proVipDiscountValue || ele?.proVipDiscountValue == 0
                        ? Number(ele?.proVipDiscountValue)?.toFixed(eleFixNum)
                        : undefined;
                const adjustThreeDiscountValue =
                    ele?.adjustThreeDiscountValue || ele?.adjustThreeDiscountValue == 0
                        ? Number(ele?.adjustThreeDiscountValue)?.toFixed(eleFixNum)
                        : undefined;
                const obj = {
                    ruleValue: adjustValue,
                    ruleVipValue: adjustVipValue,
                    proVipDiscountValue: proVipDiscountValue,
                    vthreeDiscountValue: adjustThreeDiscountValue,
                    ruleAdjustType: ele.adjustType,
                    adjustSn: index,
                };
                if (ele.adjustType == MODELTYPS.DYNAMIC) {
                    obj.ruleMethodType = ele.adjustMethodType;
                    obj.ruleVipMethodType = ele.vipAdjustMethodType;
                    obj.proVipMethodType = ele.proVipMethodType;
                    obj.vthreeMethodType = ele.vthreeMethodType;
                }
                details.push(obj);
            });
            delete data.adjustDetailList;
            otherParams.templateSubType = `04${data.adjustPriceType}`;
        }
        otherParams.templateBelongType = data.belongType;
    }
    const params = {
        ...data,
        [key]: undefined,
        details,
        ...otherParams,
    };
    return params;
};

export const padDecimalTo = (num, length) => {
    if (num === undefined) {
        return num;
    }
    // 将数字转换为字符串
    const numStr = typeof num == 'string' ? num : num.toString();
    // 找到小数点的位置
    const dotIndex = numStr.indexOf('.');
    // 如果小数点不存在，就在末尾添加一个小数点再补零
    if (dotIndex === -1) {
        return numStr.concat('.', '0'.repeat(length));
    }
    // 计算需要补零的长度
    const padLength = length + 1; // 加 1 因为包含小数点
    // 使用 padEnd 补足零
    return numStr.padEnd(dotIndex + padLength, '0');
};
