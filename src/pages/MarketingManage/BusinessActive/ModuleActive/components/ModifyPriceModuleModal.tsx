// 添加城市/场站时段模板
import { usePagination, useRequest } from 'ahooks';
import {
    Form,
    Input,
    InputNumber,
    Modal,
    Radio,
    Select,
    Space,
    Switch,
    message,
    Descriptions,
    Button,
    Typography,
    Row,
    Col,
    Popconfirm,
    ConfigProvider,
    Empty,
    Badge,
} from 'antd';
import { useRef } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { MODULE_TYPES, MODULE_TYPE_CODES } from './declare';
import {
    getCityTemplateDetailApi,
    getStationTemplateDetailApi,
    saveCityTemplateApi,
    saveStationTemplateApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { PlusOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import commonStyles from '@/assets/styles/common.less';
import CityTransferModal from '@/components/CityTransferModal';
import XdtProTable from '@/components/XdtProTable';
import ChangeTimeRangeModal from './ChangeTimeRangeModal';

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
};

const defaultTypeOptions: any = [
    {
        value: '0',
        label: '尖时段',
        color: '#f0a661',
    },
    {
        value: '1',
        label: '峰时段',
        color: '#f3dc72',
    },
    {
        value: '2',
        label: '平时段',
        color: '#69a5f1',
    },
    {
        value: '3',
        label: '谷时段',
        color: '#8fe16f',
    },
    {
        value: '4',
        label: '深时段',
        color: '#a8d8e2',
    },
];

const ModifyPriceModuleModal = (props: any) => {
    const { visible, onClose, detailParams, onSuccess, currentTab } = props;
    const [form] = Form.useForm();
    const timePeriodConfigType = Form.useWatch('timePeriodConfigType', form);
    const cityRef = useRef<any>();
    const [dataSource, setDataSource] = useState<any>([]);
    const [timeVisible, setTimeVisible] = useState(false);
    const [dataItem, setDataItem] = useState<any>();
    const [dataIndex, setDataIndex] = useState<any>();
    const [typeOptions, setTypeOptions] = useState<any>(copyObjectCommon(defaultTypeOptions));
    const [optionsKey, setOptionsKey] = useState<string>('');

    const [loading, setLoading] = useState(false);
    useEffect(() => {
        if (detailParams?.city || detailParams?.stationId) {
            getData(detailParams);
        }
    }, [detailParams]);

    useEffect(() => {
        if (dataSource?.length > 0) {
            dataSource?.forEach((o: any) => {
                if (typeOptions[o?.timePeriodFlag]) {
                    typeOptions[o?.timePeriodFlag].disabled = true;
                }
            });
            setTypeOptions(typeOptions);
        }
    }, [optionsKey]);

    const {
        run: getData,
        loading: detailLoading,
        data: detailInfo,
    } = useRequest(
        (params: any) => {
            const request =
                currentTab === '01' ? getStationTemplateDetailApi : getCityTemplateDetailApi;
            return request({
                ...params,
            });
        },
        {
            manual: true,
            onSuccess(res) {
                if (res.ret === 200 && res.data) {
                    setDataSource(res?.data?.detailVos || []);
                    if (
                        (currentTab === '01' && res?.data?.timePeriodConfigType === '2') ||
                        currentTab === '02'
                    ) {
                        res?.data?.detailVos?.forEach((o: any) => {
                            if (typeOptions[o?.timePeriodFlag]) {
                                typeOptions[o?.timePeriodFlag].disabled = true;
                            }
                        });
                        setTypeOptions(typeOptions);
                    }

                    form.setFieldsValue({ ...res?.data });
                }
            },
        },
    );

    const onChangeRadio = (e: any) => {
        getData({
            stationId: detailParams?.stationId,
            timePeriodConfigType: e.target.value,
        });
    };

    const onFinish = (values: any) => {
        console.log(values);
        let detailParamList = undefined;
        if ((currentTab === '01' && timePeriodConfigType === '2') || currentTab === '02') {
            detailParamList = dataSource;
            if (dataSource?.length === 0) {
                message.error('时段配置信息不能为空');
                return;
            } else if (dataSource?.length > 0) {
                let isTimePeriod = false;
                dataSource?.forEach((el: any) => {
                    if (!el?.timePeriod) {
                        isTimePeriod = true;
                    }
                });
                if (isTimePeriod) {
                    message.error('存在未填写时段范围');
                    return;
                }
            }
        }
        const params = {
            detailParamList,
            ...values,
        };
        setLoading(true);
        const request = currentTab === '01' ? saveStationTemplateApi : saveCityTemplateApi;
        request(params)
            .then(() => {
                message.success('操作成功');
                onSuccess?.();
            })
            .finally(() => setLoading(false));
    };

    const onAdd = () => {
        const dataSourceCopy: any = copyObjectCommon(dataSource);
        if (
            dataSourceCopy?.length > 0 &&
            !dataSourceCopy[dataSourceCopy?.length - 1]?.timePeriodFlag
        ) {
            message.warning('请先选择时段类型再新增');
            return;
        }
        setDataSource([
            ...dataSourceCopy,
            {
                id: dataSourceCopy?.length,
                timePeriodFlag: null,
                timePeriod: null,
            },
        ]);
    };
    // 清空时段配置
    const onClear = () => {
        setTypeOptions(copyObjectCommon(defaultTypeOptions));
        setDataSource([]);
    };

    const columns: any = [
        {
            title: '时段类型',
            dataIndex: 'timePeriodFlag',
            hideInSearch: true,
            width: 200,
            sorter: false,
            render: (text, record: any, index: number) => {
                if (record?.timePeriodFlag) {
                    if (record?.timePeriodFlag === '10') {
                        return '全天';
                    } else {
                        return (
                            <Badge
                                color={defaultTypeOptions?.[record?.timePeriodFlag]?.color}
                                text={defaultTypeOptions?.[record?.timePeriodFlag]?.label}
                            />
                        );
                    }
                } else {
                    return (
                        <Select
                            defaultValue={record?.timePeriodFlag}
                            placeholder="请选择时段类型"
                            onChange={(value) => {
                                dataSource[index].timePeriodFlag = value;
                                setDataSource(dataSource);
                                setOptionsKey(value);
                            }}
                            options={typeOptions}
                        />
                    );
                }
            },
        },
        {
            title: '时段范围',
            dataIndex: 'timePeriod',
            readonly: true,
            width: '60%',
        },
        ...((currentTab === '01' && timePeriodConfigType === '2') || currentTab === '02'
            ? [
                  {
                      title: '操作',
                      valueType: 'option',
                      width: 200,
                      render: (text, record: any, index: number) => {
                          const btnList = [];
                          const editBtn = (
                              <Button
                                  type="link"
                                  disabled={
                                      !(
                                          record?.timePeriodFlag === '0' ||
                                          (record?.timePeriodFlag !== '0' &&
                                              Number(record?.timePeriodFlag) > 0)
                                      )
                                  }
                                  onClick={() => {
                                      // action?.startEditable?.(record.id);
                                      setTimeVisible(true);
                                      setDataItem(record);
                                      setDataIndex(index);
                                  }}
                              >
                                  修改时间范围
                              </Button>
                          );
                          btnList.push(editBtn);
                          return <Space>{btnList}</Space>;
                      },
                  },
              ]
            : []),
    ];

    return (
        <Modal
            width={1000}
            title={`添加${currentTab === '01' ? '站点' : '城市'}时段模板`}
            visible={visible}
            onCancel={onClose}
            destroyOnClose
            confirmLoading={loading}
            onOk={() => form.submit()}
            okText="应用"
        >
            <Form form={form} onFinish={onFinish} {...formItemLayout}>
                <Form.Item hidden name="stationId" />
                <Form.Item hidden name="priceAdjustTimePeriodTemplateId" />
                <div className={commonStyles['form-title']}>基本信息</div>
                {currentTab === '01' && (
                    <Descriptions>
                        <Descriptions.Item label="站点ID">
                            {detailInfo?.data?.stationId || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="站点名称">
                            {detailInfo?.data?.stationName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="省份">
                            {detailInfo?.data?.provinceName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="城市">
                            {detailInfo?.data?.cityName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="运营商">
                            {detailInfo?.data?.operatorName || '-'}
                        </Descriptions.Item>
                    </Descriptions>
                )}
                {currentTab === '02' && (
                    <>
                        <Form.Item noStyle shouldUpdate={(pre, after) => true}>
                            {({ getFieldValue, setFieldsValue }) => {
                                const city = getFieldValue('city');
                                const cityName = getFieldValue('cityName');

                                const noCity = isEmpty(city);

                                // const cityName = cityRef?.current?.getSelectInfo();

                                return (
                                    <>
                                        <Form.Item
                                            required
                                            name="city"
                                            label={'选择城市'}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择城市',
                                                },
                                            ]}
                                        >
                                            {noCity ? (
                                                <Button
                                                    type="primary"
                                                    onClick={() => {
                                                        cityRef?.current?.show();
                                                    }}
                                                >
                                                    +添加城市
                                                </Button>
                                            ) : (
                                                <Space>
                                                    {cityName}
                                                    {/* <Typography.Link
                                                        onClick={() => {
                                                            cityRef?.current?.show({
                                                                defaultKeys: [city],
                                                            });
                                                        }}
                                                    >
                                                        编辑
                                                    </Typography.Link> */}
                                                </Space>
                                            )}
                                        </Form.Item>
                                        <Form.Item noStyle name="cityName"></Form.Item>
                                        <Form.Item noStyle name="province"></Form.Item>
                                        <Form.Item noStyle name="provinceName"></Form.Item>
                                        <CityTransferModal
                                            ref={cityRef}
                                            multiple={false}
                                            onFinish={(
                                                citys: {
                                                    areaCode: string;
                                                    areaName: string;
                                                    upAreaCode: string;
                                                    upAreaName: string;
                                                }[],
                                            ) => {
                                                setFieldsValue({
                                                    city: citys && citys?.[0].areaCode,
                                                    province: citys && citys?.[0].upAreaCode,
                                                    cityName: citys && citys?.[0].areaName,
                                                    provinceName: citys && citys?.[0].upAreaName,
                                                });
                                            }}
                                        ></CityTransferModal>
                                    </>
                                );
                            }}
                        </Form.Item>
                    </>
                )}
                <div className={commonStyles['form-title']}>时段配置</div>
                {currentTab === '01' && (
                    <>
                        <Form.Item
                            label="配置方式"
                            required
                            style={{ marginBottom: 0 }}
                            name="timePeriodConfigType"
                            rules={[
                                {
                                    required: true,
                                    message: '请选择活动',
                                },
                            ]}
                        >
                            <Radio.Group onChange={onChangeRadio}>
                                <Radio value={'0'}>自动获取</Radio>
                                <Radio value={'1'}>应用城市时段配置</Radio>
                                <Radio value={'2'}>自定义</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {timePeriodConfigType === '0' && (
                            <div style={{ color: '#999', marginLeft: '110px', fontSize: '13px' }}>
                                系统将根据近期推送价格（原价）自动划分时段，按天自动更新
                            </div>
                        )}
                        {timePeriodConfigType === '1' && (
                            <div style={{ color: '#999', marginLeft: '110px', fontSize: '13px' }}>
                                站点自动应用城市时段的配置，按小时自动更新
                            </div>
                        )}
                    </>
                )}
                <Row
                    justify="space-between"
                    gutter={{ md: 8, lg: 24, xl: 48 }}
                    style={{ margin: '20px 0' }}
                >
                    <Col span={12}>
                        {currentTab === '01' &&
                            (timePeriodConfigType === '0' || timePeriodConfigType === '1') && (
                                <span>更新时间：{detailInfo?.data?.modified || '-'}</span>
                            )}
                        {((currentTab === '01' && timePeriodConfigType === '2') ||
                            currentTab === '02') && (
                            <Button
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={() => onAdd()}
                                disabled={dataSource && dataSource?.length >= 5}
                            >
                                添加时段
                            </Button>
                        )}
                    </Col>
                    <Col>
                        {/* {currentTab === '01' &&
                            (timePeriodConfigType === '0' || timePeriodConfigType === '1') && (
                                <Button
                                    type="link"
                                    icon={<SyncOutlined />}
                                    onClick={() => getData()}
                                >
                                    获取数据
                                </Button>
                            )} */}
                        {((currentTab === '01' && timePeriodConfigType === '2') ||
                            currentTab === '02') && (
                            <Popconfirm
                                title="是否清空所有时段配置？"
                                okText="确定"
                                cancelText="取消"
                                onConfirm={() => {
                                    onClear();
                                }}
                            >
                                <Button type="link" icon={<DeleteOutlined />}>
                                    清空时段配置
                                </Button>
                            </Popconfirm>
                        )}
                    </Col>
                </Row>
            </Form>
            <ConfigProvider renderEmpty={() => <Empty description="未获取到数据" />}>
                <XdtProTable
                    columns={columns}
                    dataSource={dataSource}
                    remeberState={false}
                    rowKey="timePeriodFlag"
                    tableLayout="fixed"
                    tableAlertOptionRender={false}
                    search={false}
                    toolbar={{ settings: undefined }}
                    pagination={false}
                />
            </ConfigProvider>

            <ChangeTimeRangeModal
                visible={timeVisible}
                dataSource={dataSource}
                dataItem={dataItem}
                dataIndex={dataIndex}
                onSuccess={(val: any) => {
                    setTimeVisible(false);
                    setDataItem({});
                    // 提交配置时间范围
                    let copyList: any = [];
                    copyList = copyObjectCommon(dataSource);
                    const arr = copyList?.map((item: any) => {
                        if (item?.timePeriodFlag === val?.timePeriodFlag) {
                            item.timePeriod = val?.timePeriod;
                        }
                        delete item?.splitTimeRange;
                        return item;
                    });

                    setDataSource(arr);
                }}
                onClose={() => {
                    setTimeVisible(false);
                    setDataItem({});
                }}
            />
        </Modal>
    );
};

export default ModifyPriceModuleModal;
