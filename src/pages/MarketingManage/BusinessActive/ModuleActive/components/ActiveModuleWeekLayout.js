import { Button, Row, Col, Form, Input, message, Radio, Checkbox, InputNumber } from 'antd';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { ActiveModuleListLayout } from './ActCommonLayout';
import {
    MODULE_WEEK_TYPE_CODES,
    MODULE_WEEK_TYPE,
    MODULE_WEEK_OPTIONS,
    MODULE_TYPE_CODES,
} from './declare';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Fragment } from 'react';

import styles from '@/assets/styles/common.less';
import baseStyles from '@/assets/styles/base.less';
import { checkDayOverlap } from './utils';
const FormItem = Form.Item;

const ActiveModuleWeekLayout = (props) => {
    const { editId, formItemLayout, formItemFixedWidthLayout, multiple, maxCount = 10 } = props;

    const weekCard = (index, list = []) => {
        // const disableIds = [];
        // for (let listIndex = 0; listIndex < list.length; listIndex++) {
        //     const element = list[listIndex];
        //     if (index != listIndex) {
        //         disableIds.push(...(element || []));
        //     }
        // }

        // const weekOptions = MODULE_WEEK_OPTIONS.map((ele) => {
        //     return {
        //         ...ele,
        //         disabled: disableIds.indexOf(`${ele.value}`) >= 0,
        //     };
        // });

        // 产品要支持都可以选，其他规则勾选的同时，已勾选的规则取消勾选
        return (
            <FormItem
                validateTrigger={['onChange']}
                name={index}
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value?.length) {
                                return Promise.reject('请选择');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
                required
            >
                <Checkbox.Group
                    options={MODULE_WEEK_OPTIONS}
                    onChange={(value) => {
                        list[index] = value;
                        for (let listIndex = 0; listIndex < list.length; listIndex++) {
                            const element = list[listIndex];
                            if (index !== listIndex && element?.length) {
                                // 判断其他规则是否和当前规则有重叠
                                (value || []).map((ele) => {
                                    const otherEle = element.findIndex((others) => others == ele);
                                    if (otherEle >= 0) {
                                        element.splice(otherEle, 1);
                                    }
                                });
                            }
                        }
                    }}
                />
            </FormItem>
        );
    };

    const dayCard = (ruleIndex, list) => {
        const alignStyle = {
            marginTop: '6px',
            position: 'absolute',
        };
        const name = [ruleIndex, 'rules'];

        const dayRule = (index) => {
            let hasTime = false;
            list?.map((rule, ruleEleIndex) => {
                if (hasTime) {
                    return;
                }
                rule?.rules?.map((day, eleIndex) => {
                    if (hasTime) {
                        return;
                    }
                    if (ruleEleIndex == ruleIndex && eleIndex == index) {
                    } else if (checkDayOverlap(day, list[ruleIndex]['rules'][index])) {
                        hasTime = true;
                    }
                });
            });
            return hasTime;
        };

        return (
            <Form.List name={name} initialValue={[{}]}>
                {(fields, { add, remove }) => (
                    <Fragment>
                        {fields.map((field, index) => {
                            const curField = list[ruleIndex]?.['rules']?.[index];
                            return (
                                <Row
                                    gutter={[12, 6]}
                                    className={baseStyles['formItemDetail']}
                                    key={field.key}
                                >
                                    <Col span={6}>
                                        <FormItem
                                            name={[index, 'start']}
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('请输入');
                                                        }

                                                        list[ruleIndex]['rules'][index]['start'] =
                                                            value;

                                                        if (dayRule(index, value)) {
                                                            return Promise.reject(`时间重叠`);
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <InputNumber
                                                precision={0}
                                                min={1}
                                                max={curField?.['end'] || 31}
                                                placeholder="开始日期"
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span={1}>
                                        <span style={alignStyle}>~</span>
                                    </Col>
                                    <Col span={6}>
                                        <FormItem
                                            name={[index, 'end']}
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('请输入');
                                                        }

                                                        list[ruleIndex]['rules'][index]['end'] =
                                                            value;

                                                        if (dayRule(index, value)) {
                                                            return Promise.reject(`时间重叠`);
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <InputNumber
                                                precision={0}
                                                min={curField?.['start'] || 1}
                                                max={31}
                                                placeholder="结束日期"
                                                onChange={() => {}}
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col span={1}>
                                        <span style={alignStyle}>号</span>
                                    </Col>
                                    <Col>
                                        {(index == 0 && (
                                            <PlusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{
                                                    margin: '0 8px',
                                                    color: 'black',
                                                }}
                                                onClick={() => {
                                                    if (fields?.length < maxCount) {
                                                        add();
                                                    } else {
                                                        message.error(
                                                            `单条规则最多支持${maxCount}条规则`,
                                                        );
                                                    }
                                                }}
                                            />
                                        )) || (
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{
                                                    margin: '0 8px',
                                                    color: 'black',
                                                }}
                                                onClick={() => {
                                                    remove(field.name);
                                                }}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            );
                        })}
                    </Fragment>
                )}
            </Form.List>
        );
    };

    return (
        <Fragment>
            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.templateSubType !== curValues.templateSubType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const templateSubType = getFieldValue('templateSubType');
                    return (
                        <Form.Item
                            label="周期类型"
                            name="templateSubType"
                            initialValue={MODULE_WEEK_TYPE?.[0]?.value}
                            {...formItemLayout}
                            rules={[
                                {
                                    required: true,
                                    message: '请选择',
                                },
                            ]}
                        >
                            {editId ? (
                                <span>{`${
                                    MODULE_WEEK_TYPE.find((ele) => ele.value == templateSubType)
                                        ?.label
                                }`}</span>
                            ) : (
                                <Radio.Group>
                                    {MODULE_WEEK_TYPE.map((ele, index) => (
                                        <Radio key={index} value={ele.value}>
                                            {ele.label}
                                        </Radio>
                                    ))}
                                </Radio.Group>
                            )}
                        </Form.Item>
                    );
                }}
            </FormItem>

            <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                {({ getFieldValue }) => {
                    const templateSubType = getFieldValue('templateSubType');
                    const name = `detail_${templateSubType}`;
                    return (
                        <Form.Item
                            label="模板内容"
                            name={name}
                            {...formItemLayout}
                            rules={[
                                {
                                    required: true,
                                    message: '请选择',
                                },
                            ]}
                        >
                            <ActiveModuleListLayout
                                type={MODULE_TYPE_CODES.WEEK}
                                multiple={multiple}
                                name={name}
                                ruleContent={(index) => (
                                    <Form.Item shouldUpdate noStyle>
                                        {({ getFieldValue }) => {
                                            const dateTime = getFieldValue('templateSubType');
                                            const list = getFieldValue(name) || [];
                                            return (
                                                (dateTime == MODULE_WEEK_TYPE_CODES.WEEK &&
                                                    weekCard(index, list)) ||
                                                (dateTime == MODULE_WEEK_TYPE_CODES.DAY &&
                                                    dayCard(index, list)) ||
                                                null
                                            );
                                        }}
                                    </Form.Item>
                                )}
                                maxCount={
                                    templateSubType == MODULE_WEEK_TYPE_CODES.WEEK ? 7 : maxCount
                                }
                            />
                        </Form.Item>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

export default ActiveModuleWeekLayout;
