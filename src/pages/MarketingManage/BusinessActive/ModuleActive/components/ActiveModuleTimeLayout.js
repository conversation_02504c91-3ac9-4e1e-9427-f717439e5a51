import { Button, Row, Col, Form, DatePicker, message } from 'antd';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { ActiveModuleListLayout } from './ActCommonLayout';
import {
    MODULE_WEEK_TYPE_CODES,
    MODULE_WEEK_TYPE,
    MODULE_WEEK_OPTIONS,
    MODULE_TYPE_CODES,
} from './declare';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Fragment } from 'react';
import ActTimeFormItem from '../../ActTimeFormItem';
import { checkTimeOverlap } from './utils';

import styles from '@/assets/styles/common.less';
import baseStyles from '@/assets/styles/base.less';
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ActiveModuleTimeLayout = (props) => {
    const { name, formItemLayout, formItemFixedWidthLayout, multiple, maxCount = 10 } = props;
    const timeCard = (ruleIndex, list) => {
        return (
            <Form.List name={ruleIndex} initialValue={[{}]}>
                {(fields, { add, remove }) => (
                    <div style={{ marginLeft: 6 }}>
                        {fields.map((field, index) => (
                            <Row
                                key={field.key}
                                gutter={[12, 6]}
                                className={baseStyles['formItemDetail']}
                            >
                                <ActTimeFormItem
                                    label=""
                                    fieldKey={field.fieldKey}
                                    name={index}
                                    validateTrigger={['onChange', 'onBlur']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const formData = getFieldValue(
                                                    ...[name, ruleIndex],
                                                );
                                                let hasTime = false;
                                                if (!value?.[0] || !value?.[1]) {
                                                    return Promise.reject('请选择时段');
                                                }
                                                if (value?.[0] && value?.[1]) {
                                                    if (
                                                        value?.[0].format?.('HH:mm') ==
                                                        value?.[1].format?.('HH:mm')
                                                    ) {
                                                        return Promise.reject(`起止时间不可相同`);
                                                    }
                                                    if (
                                                        value?.[0] &&
                                                        value?.[0].format?.('HH:mm') == '23:59'
                                                    ) {
                                                        return Promise.reject(
                                                            `开始时段不能选择23:59`,
                                                        );
                                                    }
                                                    formData?.map((rule, ruleEleIndex) => {
                                                        rule?.map((actTime, eleIndex) => {
                                                            if (
                                                                ruleEleIndex == ruleIndex &&
                                                                eleIndex == index
                                                            ) {
                                                            } else if (
                                                                actTime?.[0] &&
                                                                actTime?.[1]
                                                            ) {
                                                                if (
                                                                    checkTimeOverlap(actTime, value)
                                                                )
                                                                    hasTime = true;
                                                            }
                                                        });
                                                    });
                                                }

                                                if (hasTime) {
                                                    return Promise.reject(`配置时段不能重叠`);
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                />
                                <Col>
                                    {(index == 0 && (
                                        <PlusCircleOutlined
                                            className={styles['dynamic-delete-button']}
                                            style={{
                                                margin: '0 8px',
                                                color: 'black',
                                            }}
                                            onClick={() => {
                                                if (fields?.length < maxCount) {
                                                    add();
                                                } else {
                                                    message.error(
                                                        `单条规则最多支持${maxCount}条规则`,
                                                    );
                                                }
                                            }}
                                        />
                                    )) || (
                                        <MinusCircleOutlined
                                            className={styles['dynamic-delete-button']}
                                            style={{
                                                margin: '0 8px',
                                                color: 'black',
                                            }}
                                            onClick={() => {
                                                remove(field.name);
                                            }}
                                        />
                                    )}
                                </Col>
                            </Row>
                        ))}
                    </div>
                )}
            </Form.List>
        );
    };

    return (
        <Form.Item label="模板内容" {...formItemLayout} name={name} required>
            <ActiveModuleListLayout
                type={MODULE_TYPE_CODES.TIME}
                name={name}
                multiple={multiple}
                ruleContent={(index) => (
                    <Form.Item shouldUpdate noStyle>
                        {({ getFieldValue }) => {
                            const list = getFieldValue(name) || [];
                            return timeCard(index, list);
                        }}
                    </Form.Item>
                )}
            />
        </Form.Item>
    );
};

export default ActiveModuleTimeLayout;
