import {
    useMemo,
    useState,
    useCallback,
    useEffect,
    useRef,
    Fragment,
    useImperativeHandle,
} from 'react';
import { Button, Form, Input, Popover, Radio, Space, Table } from 'antd';

import { copyObjectCommon, isEmpty } from '@/utils/utils';
import commonStyles from '@/assets/styles/common.less';
import updateStyles from '../BusinessActiveUpdatePage.less';

import ActivePriceModal from './ActivePriceModal';
import ModuleEditModal from './ModuleEditModal';
import ActTemplateSelectModal from './ActTemplateSelectModal';
import { ADJUST_PRICE_TYPES } from '@/config/declare';
import classnames from 'classnames';

import {
    getActTemplateDetailApi,
    getActTemplateDetailMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';

import { ACT_CONFIG_TYPE, ACT_MENU_TYPE, CONFIG_MODEL, MODELTYPS } from './ActModuleConfig';
import { ActCrowdSelectModal } from './ActCrowdSelectModal';
import DisdountCrowdSelectModal from './DisdountCrowdSelectModal';
import { padDecimalTo } from './utils';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import baseStyles from '@/assets/styles/base.less';

const formatAllChannelList = (arr) => {
    let list = [];

    for (const item of arr) {
        const otherItems = [...arr].filter((ele) => ele != item);
        list.push(item);
        if (otherItems instanceof Array && otherItems.length > 0) {
            while (otherItems.length > 0) {
                list.push([item, ...otherItems].join(','));
                otherItems.pop();
            }
        }
    }
    return list;
};

const mapChannelList = ['11', '15'];
const AllChannelList = formatAllChannelList(mapChannelList); //排列组合所有渠道

const SELECT_TYPES = {
    DISDOUNT_ALL_CROWD: '1', // 打折立减多加一个自己的全部人群，01挪为平台人群，02挪为商家人群
    ALL: '01',
    TEMP: '02',
};

const titleStyle = {
    position: 'absolute',
    transform: 'translateX(-100%)',
    top: '20px',
    left: '-20px',
    padding: '5px 20px',
    borderRadius: '20px',
    backgroundColor: '#f8f8f8',
};

const getColKeyByType = (type) => {
    let key = '';
    switch (type) {
        case ACT_CONFIG_TYPE.CROWD:
            key = 'crowd';
            break;
        case ACT_CONFIG_TYPE.CYCLE:
            key = 'cycle';
            break;
        case ACT_CONFIG_TYPE.TIME:
            key = 'time';
            break;
        case ACT_CONFIG_TYPE.DISCOUNT:
            key = 'discount';
            break;
        case ACT_CONFIG_TYPE.DISCOUNT_DISPLAY:
            key = 'dctExternalCopywriting';
            break;
        default:
            break;
    }
    return key;
};

const ActModuleTableFormItem = (props) => {
    const {
        value,
        onChange,
        disabled, //禁用
        readonly, //只读
        sortType = CONFIG_MODEL.CROWD,
        belongType,
        menuType,
        channelList, //对应特殊渠道
        dispatch,
        businessModuleActiveModel,
        currentUser,
        staticTimes, // = ['00:00~02:00', '02:00~03:00'], //固定时段
        staticCycle = false, // 是否固定周期
        priceSelectCallback, // 价格设置弹窗点确认后的回调事件，基础价格补贴有用
        isNewDiscountActive, // 当前打折立减是否是新活动，用于活动人群区分显示逻辑
        cooperationPlatform, //平台类型
        editRef, // 用于外显文案的编辑结束，因当前编辑中无法得知什么时候该退出编辑，所以要调用方再提交前触发退出编辑
        ...otherProps
    } = props;
    const {
        crwodTemplateList = [],
        cycleTemplateList = [],
        timeTemplateList = [],
    } = businessModuleActiveModel;

    const priceRef = useRef();
    const templateRef = useRef();
    const selectTemplateRefs = useRef({}); //模板选择弹窗

    const [resultTable, updateResultTable] = useState([]); //表格平铺数据 用于组件数据控制

    // 应后端要求，打折立减需求需要在人群中新增此字段，为了避免改动到旧逻辑，单领出来维护
    const [crowdDetailList, updateCrowdDetailList] = useState([]);

    const [templateMaxWidth, updateTemplateMaxWidth] = useState(200);

    const [tempSaveInfo, updateTempSaveInfo] = useState(null);

    const [curEditTableItemId, updateCurEditTableItemId] = useState(null); //当前配置的item对应的tableId

    //首个列的类型
    const firstType = useMemo(() => {
        let type = '';
        if (sortType === CONFIG_MODEL.CROWD) {
            type = ACT_CONFIG_TYPE.CROWD;
        } else if (sortType === CONFIG_MODEL.CYCLE) {
            type = ACT_CONFIG_TYPE.CYCLE;
        }
        return type;
    }, [sortType]);

    //所有列类型
    const colTypeList = useMemo(() => {
        const tableHeaderKeys = [];
        if (sortType === CONFIG_MODEL.CROWD) {
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CROWD);
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CYCLE);
        } else if (sortType === CONFIG_MODEL.CYCLE) {
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CYCLE);
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CROWD);
        }

        tableHeaderKeys.push(ACT_CONFIG_TYPE.TIME);
        tableHeaderKeys.push(ACT_CONFIG_TYPE.DISCOUNT);

        if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
            tableHeaderKeys.push(ACT_CONFIG_TYPE.DISCOUNT_DISPLAY);
        }
        return tableHeaderKeys;
    }, [sortType]);

    useEffect(() => {
        initAllOptions();

        //控制屏幕大小时绝对定位模块的大小
        const tableClassDom = document.getElementsByClassName(updateStyles['actModule-table']);
        if (tableClassDom[0].offsetWidth < 1600) {
            updateTemplateMaxWidth(100);
        }
    }, []);

    useEffect(() => {
        const copyAllChannelList = copyObjectCommon(AllChannelList);

        let exCludeChannelList = copyAllChannelList;

        if (channelList instanceof Array) {
            channelList.forEach((element) => {
                const excludeIndex = exCludeChannelList.findIndex((ele) => {
                    return ele == element;
                });
                if (excludeIndex >= 0) {
                    exCludeChannelList.splice(excludeIndex, 1);
                }
            });
        }

        //过滤未选择的渠道的数据
        let excludeValue = value;
        if (value instanceof Array) {
            excludeValue = value.filter((ele) => {
                return !exCludeChannelList.includes(ele.actChannel);
            });
        }

        const list = formTableList(excludeValue);
        updateResultTable(list);

        //有变化才更新表单
        if (value && value.length != excludeValue.length) {
            changeFormValueEvent(list);
        } else if (!value) {
            changeFormValueEvent(list);
        }
    }, [value, sortType, channelList]);

    /**
     * 初始化模板选项
     */
    const initAllOptions = () => {
        if (crwodTemplateList?.length === 0) {
            initCrwodTemplateList();
        }
        if (cycleTemplateList?.length === 0) {
            initCycleTemplateList();
        }
        if (timeTemplateList?.length === 0) {
            initTimeTemplateList();
        }
    };

    const updateTemplateOptionsByType = (type) => {
        switch (type) {
            case ACT_CONFIG_TYPE.CROWD:
                initCrwodTemplateList();
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                initCycleTemplateList();
                break;
            case ACT_CONFIG_TYPE.TIME:
                initTimeTemplateList();
                break;
            default:
                break;
        }
    };

    //初始化人群模板选项
    const initCrwodTemplateList = async () => {
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.CROWD,
        });
    };

    //初始化周期模板选项
    const initCycleTemplateList = async () => {
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.CYCLE,
        });
    };

    //初始化时段模板选项
    const initTimeTemplateList = async () => {
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.TIME,
        });
    };

    //初始化所有自己数据
    const initChildList = (item) => {
        const dataType = item.dataType;
        const itemTableIdList = item.tableId.split('-');
        const level = colTypeList.findIndex((ele) => {
            return ele === dataType;
        });
        let list = [];
        let discountList = [];
        const basePid = copyObjectCommon(itemTableIdList);
        for (let index = level + 1; index < colTypeList.length; index++) {
            const element = colTypeList[index];
            basePid.push(0);
            const preItem = list[list.length - 1];
            let newItem = {
                tableId: basePid.join('-'),
                dataType: element,
                selectType: SELECT_TYPES.ALL,
            };
            if (preItem) {
                newItem.pid = preItem.tableId;
                if (preItem.actChannel) {
                    newItem.actChannel = preItem.actChannel;
                }
            } else {
                newItem.pid = item.tableId;
                if (item.actChannel) {
                    newItem.actChannel = item.actChannel;
                }
            }
            // list.push(newItem);

            if (element === ACT_CONFIG_TYPE.TIME) {
                if (staticTimes instanceof Array && staticTimes?.length > 0) {
                    //如果有固定时段 时段默认选择模版
                    newItem.selectType = SELECT_TYPES.TEMP;
                }
            }

            if (element === ACT_CONFIG_TYPE.DISCOUNT) {
                //如果有默认时段
                if (staticTimes instanceof Array && staticTimes?.length > 0) {
                    //手动创建时段的子集
                    staticTimes.forEach((timeItem, timeIndex) => {
                        const newTimePids = copyObjectCommon(itemTableIdList);
                        newTimePids.push(0);
                        newTimePids.push(timeIndex);

                        let newDiscountItem = {
                            tableId: newTimePids.join('-'),
                            dataType: element,
                            selectType: SELECT_TYPES.ALL,
                        };
                        if (preItem) {
                            newDiscountItem.pid = preItem.tableId;
                            if (preItem.actChannel) {
                                newDiscountItem.actChannel = preItem.actChannel;
                            }
                        } else {
                            newDiscountItem.pid = item.tableId;
                            if (item.actChannel) {
                                newDiscountItem.actChannel = item.actChannel;
                            }
                        }
                        newDiscountItem.templateValue = {
                            ruleValue: timeItem,
                            ruleValueName: timeItem,
                        };
                        discountList.push(newDiscountItem);
                    });
                } else {
                    list.push(newItem);
                }
            } else {
                list.push(newItem);
            }
        }

        //合并和父级子集
        const newList = [...list, ...discountList];

        return newList;
    };

    const clearTableChildrenById = (tableId) => {
        let table = copyObjectCommon(resultTable);

        const itemIndex = table.findIndex((ele) => {
            return ele.tableId == tableId;
        });
        let findItem = table[itemIndex];
        const newItem = {
            ...findItem,
            selectType: SELECT_TYPES.ALL,
        };
        if (itemIndex == 0 && menuType == ACT_MENU_TYPE.COMPANY_ACT) {
            // 打折立减的人群默认选择全部
            newItem.selectType = SELECT_TYPES.DISDOUNT_ALL_CROWD;
        }

        table[itemIndex] = newItem;

        const newList = table.filter((ele) => {
            return !(ele.pid && ele.pid.indexOf(newItem.tableId) === 0);
        });

        const childList = initChildList(newItem);

        const newTable = copyObjectCommon([...newList, ...childList]);
        updateResultTable(newTable);
        //更新表单的值
        changeFormValueEvent(newTable);
    };

    //通过tableId 更新表格里的某一项 并更新整个表格重新渲染
    const updateTableItemById = (tableId, info) => {
        //用于最后一项活动的设置 和 其他配置模板类型开关的选择
        let table = copyObjectCommon(resultTable);

        let itemIndex = table.findIndex((ele) => {
            return ele.tableId == tableId;
        });
        if (itemIndex >= 0) {
            const findItem = table[itemIndex];
            table[itemIndex] = { ...findItem, ...info };
        }

        const newTable = copyObjectCommon(table);
        updateResultTable(newTable);

        //更新表单的值
        changeFormValueEvent(newTable);
        return newTable;
    };
    const updateTableItemChildren = (item, childList, table) => {
        const newItem = {
            ...item,
        };

        //获取不是当前item子集的其他数据
        const newList = table.filter((ele) => {
            return !(ele.pid && ele.pid.indexOf(newItem.tableId) === 0);
        });

        let itemChildList = []; //新初始化子集数据

        const itemTableIdList = item.tableId.split('-');

        const level = colTypeList.findIndex((ele) => {
            return ele === newItem.dataType;
        });
        childList.forEach((element, index) => {
            const basePid = [...itemTableIdList];
            const timePids = copyObjectCommon(itemTableIdList);
            basePid.push(index);

            const elementDataType = colTypeList[level + 1];
            let item = {
                tableId: basePid.join('-'),
                dataType: colTypeList[level + 1],
                selectType: SELECT_TYPES.ALL,
                pid: newItem.tableId,
                templateValue: element,
            };
            if (newItem.actChannel) {
                item.actChannel = newItem.actChannel;
            }

            if (elementDataType === ACT_CONFIG_TYPE.TIME) {
                if (staticTimes instanceof Array && staticTimes?.length > 0) {
                    item.selectType = SELECT_TYPES.TEMP;
                }
            }

            if (elementDataType === ACT_CONFIG_TYPE.DISCOUNT) {
                if (staticTimes instanceof Array && staticTimes?.length > 0) {
                    item.selectType = SELECT_TYPES.TEMP;
                }
            }

            itemChildList.push(item);

            // itemChildList.push(item);

            const childAndChild = initChildList(item);

            itemChildList = [...itemChildList, ...childAndChild];
        });

        const newTable = copyObjectCommon([...newList, ...itemChildList]);

        updateResultTable(newTable);

        //更新表单的值
        changeFormValueEvent(newTable);
    };

    /**
     * 获取模板详情信息
     */
    const getTemplateDetailEvent = async (templateId) => {
        try {
            const { data } = await getActTemplateDetailMngApi({ templateId });
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    //选择模板
    const selectTemplateEvent = async (templateId, item) => {
        try {
            const data = await getTemplateDetailEvent(templateId);

            const { detailBoList, templateSubType } = data;

            //选中的模板的值是传递给当前item 清空更新对应所有子集的数据
            if (item.dataType === ACT_CONFIG_TYPE.CYCLE) {
                //周期字段需要截取周期类型并插入数组中
                detailBoList.forEach((element) => {
                    element.templateSubType = templateSubType;
                });
            }

            updateTableItemChildren(item, detailBoList, copyObjectCommon(resultTable));

            return detailBoList;
        } catch (error) {
            console.log(6666, error);
            return Promise.reject(error);
        }
    };

    const itemDropItemSelectEvent = (key, type, itemInfo) => {
        //每列模板选择事件    判断选择模板还是选择创建
        if (key === 'create') {
            //创建模板
            updateCurEditTableItemId(itemInfo.tableId);
            let modalParams = {
                belongType: belongType,
                defaultType: type,
                openParams: {
                    ...itemInfo,
                },
            };

            templateRef?.current?.show(modalParams);
        } else {
            key && selectTemplateEvent(key, itemInfo);
        }
    };

    const formatTemplateTypeText = (type) => {
        let name = '';
        switch (type) {
            case MODELTYPS.DISCOUNT:
                name = '打折';
                break;
            case MODELTYPS.CROWDPLLICY_DISCOUNT:
                name = '打折';
                break;
            case MODELTYPS.REDUCTION:
                name = '立减';
                break;
            case MODELTYPS.CROWDPLLICY_REDUCTION:
                name = '立减';
                break;
            case MODELTYPS.PRICE:
                name = '一口价';
                break;
            case MODELTYPS.CROWDPLLICY_PRICE:
                name = '一口价';
                break;
            case MODELTYPS.DYNAMIC:
                name = '动态调价';
                break;

            default:
                break;
        }
        return name;
    };

    const getChannelName = (actChannel) => {
        let channelName = '';
        switch (actChannel) {
            case '11':
                channelName = '高德';
                break;
            case '15':
                channelName = '百度';
                break;

            default:
                break;
        }
        return channelName;
    };

    const channelItemChildListKey = (dataType) => {
        let key = '';
        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'cycleList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'periodsList';
                }
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'periodsList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'activeCrowdList';
                }

                break;
            case ACT_CONFIG_TYPE.TIME:
                key = 'discountList';

                break;
            default:
                break;
        }
        return key;
    };

    //格式化图商渠道数据
    const formatChannelItemToTableEvent = (channelItem, preLevelIdList) => {
        const dataType = channelItem.dataType;
        const itemTableIdList = channelItem?.tableId?.split('-') || preLevelIdList;

        const level = colTypeList.findIndex((ele) => {
            return ele === dataType;
        });

        let list = [];

        const tableId = itemTableIdList.join('-');

        let parentIds = itemTableIdList.slice(0, -1);
        let newItem = {
            tableId: tableId,
            pid: parentIds.join('-'),
            dataType: colTypeList[level + 1] || dataType,
            actChannel: channelItem.actChannel,
        };
        let templateValue = {};

        const childListKey = channelItemChildListKey(channelItem.dataType);
        const nextChildList = channelItem[childListKey];

        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                templateValue.ruleValue = channelItem.activeCrowd;
                templateValue.ruleValueName = channelItem.activeCrowdName;

                if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                    newItem.selectType = channelItem.activeCrowdFlag;
                } else {
                    newItem.selectType =
                        channelItem.activeCrowdFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                }

                break;
            case ACT_CONFIG_TYPE.CYCLE:
                templateValue.templateSubType = channelItem.cycleType;
                templateValue.ruleValue = channelItem.cycleValue;
                templateValue.ruleValueName = channelItem.cycleValueName;
                newItem.selectType =
                    channelItem.cycleValueFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;

                break;
            case ACT_CONFIG_TYPE.TIME:
                templateValue.ruleValue = channelItem.timePeriods;
                templateValue.ruleValueName = channelItem.timePeriods;

                if (channelItem.discountList && channelItem.discountList[0]) {
                    let discountItemInfo = channelItem.discountList[0];

                    let discountValue = {
                        actId: discountItemInfo.actId || '',
                        templateActType: discountItemInfo.actType,
                        dctValue: discountItemInfo.dctValue,
                        vthreeDiscountValue: discountItemInfo.vthreeDiscountValue,
                        proVipDiscountValue: discountItemInfo.proVipDiscountValue,
                        vipDiscountValue: discountItemInfo.vipDiscountValue,
                        adjustPriceType: discountItemInfo.adjustPriceType,
                        adjustDetailList: discountItemInfo.adjustDetailList,
                        templateDetail: discountItemInfo.discountDesc,
                        subChargeFeeFlag: discountItemInfo.subChargeFeeFlag,
                        dctExternalCopywriting: discountItemInfo.dctExternalCopywriting,
                    };
                    discountValue.adjustDetailList?.map((ele) => {
                        ele.adjustValue = padDecimalTo(
                            ele.adjustValue,
                            ele.adjustType == '01' || ele.adjustType == '04' ? 2 : 4,
                        );
                        ele.adjustVipValue = padDecimalTo(
                            ele.adjustVipValue,
                            ele.adjustType == '01' || ele.adjustType == '04' ? 2 : 4,
                        );
                        ele.adjustThreeDiscountValue = padDecimalTo(
                            ele.adjustThreeDiscountValue,
                            ele.adjustType == '01' || ele.adjustType == '04' ? 2 : 4,
                        );
                        ele.proVipDiscountValue = padDecimalTo(
                            ele.proVipDiscountValue,
                            ele.adjustType == '01' || ele.adjustType == '04' ? 2 : 4,
                        );
                    });
                    newItem.discountValue = discountValue;
                }

                newItem.selectType =
                    channelItem.allDayFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                break;

            default:
                break;
        }

        newItem.templateValue = templateValue;

        if (nextChildList instanceof Array && nextChildList.length > 0) {
            switch (nextChildList[0].dataType) {
                case ACT_CONFIG_TYPE.CROWD:
                    if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                        newItem.selectType = nextChildList[0].activeCrowdFlag;
                    } else {
                        newItem.selectType =
                            nextChildList[0].activeCrowdFlag === '1'
                                ? SELECT_TYPES.ALL
                                : SELECT_TYPES.TEMP;
                    }

                    break;
                case ACT_CONFIG_TYPE.CYCLE:
                    newItem.selectType =
                        nextChildList[0].cycleValueFlag === '1'
                            ? SELECT_TYPES.ALL
                            : SELECT_TYPES.TEMP;

                    break;
                case ACT_CONFIG_TYPE.TIME:
                    newItem.selectType =
                        nextChildList[0].allDayFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                    break;

                default:
                    break;
            }
        }

        list.push(newItem);

        if (
            menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE &&
            dataType == ACT_CONFIG_TYPE.TIME
        ) {
            // 由于后端定义的招商优惠外显文案层级和优惠在同层，所以直接再拼一层子集
            list.push({
                tableId: `${tableId}-0`,
                pid: newItem?.tableId,
                dataType: ACT_CONFIG_TYPE.DISCOUNT_DISPLAY,
                actChannel: channelItem.actChannel,
                dctExternalCopywriting: newItem?.discountValue?.dctExternalCopywriting,
            });
        }

        if (
            dataType != ACT_CONFIG_TYPE.TIME &&
            nextChildList instanceof Array &&
            nextChildList.length > 0
        ) {
            list = [...list, ...formatChannelListToTableEvent(nextChildList, tableId)];
        }

        return list;
    };

    //格式化层不同渠道的一位数组
    const formatChannelListToTableEvent = (channelList, pid) => {
        let list = [];

        channelList.forEach((element, elementIndex) => {
            //初始化首条数据

            let preLevelIdList = [...String(pid).split('-'), elementIndex];

            // 将子集生成出来塞进数组
            list = [...list, ...formatChannelItemToTableEvent(element, preLevelIdList)];
        });

        return list;
    };

    /**
     * 表单树结构数据格式化层一维表格数据
     */
    const formatFormToTableEvent = (originList) => {
        let list = [];
        let originChannelList = [];
        const crowdList = [];

        originList.map((originItem) => {
            if (originChannelList.length == 0) {
                originChannelList.push({ actChannel: originItem.actChannel, list: [originItem] });
            } else {
                let res = originChannelList.some((item) => {
                    if (item.actChannel === originItem.actChannel) {
                        item.list.push(originItem);
                        return true;
                    }
                });
                if (!res) {
                    originChannelList.push({
                        actChannel: originItem.actChannel,
                        list: [originItem],
                    });
                }
            }

            originItem.crowdDetailList?.map((ele) => {
                if (!crowdList?.some((cacheEle) => cacheEle.crowdDetailId == ele.crowdDetailId)) {
                    crowdList.push(ele);
                }
            });
        });

        if (!crowdDetailList?.length && crowdList.length) {
            updateCrowdDetailList(crowdList);
        }
        const formatOriginList = originChannelList.map((ele) => {
            return ele.list;
        });

        formatOriginList.forEach((channelList, index) => {
            let selectType = '';

            const element = channelList[0];
            if (element) {
                switch (element.dataType) {
                    case ACT_CONFIG_TYPE.CROWD:
                        if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                            selectType = element.activeCrowdFlag;
                        } else {
                            selectType =
                                element.activeCrowdFlag === '1'
                                    ? SELECT_TYPES.ALL
                                    : SELECT_TYPES.TEMP;
                        }

                        break;
                    case ACT_CONFIG_TYPE.CYCLE:
                        selectType =
                            element.cycleValueFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                        break;

                    default:
                        break;
                }

                list.push({
                    dataType: firstType,
                    tableId: `${index}`,
                    selectType: selectType,
                    actChannel: element.actChannel,
                });
            }

            list = [...list, ...formatChannelListToTableEvent(channelList, index)];
        });

        return list;
    };
    /**
     * 获取子集的数据
     */
    const findNexLevelChildrenList = (parentItem, list) => {
        const newList = list.filter((ele) => {
            return ele.pid && ele.pid === parentItem.tableId;
        });
        return newList;
    };
    /**
     * 寻找过滤相同渠道的数据
     */
    const findAllChildrenList = (parentItem, list) => {
        const newList = list.filter((ele) => {
            return (
                (ele.pid && ele.pid.indexOf(parentItem.tableId) >= 0) ||
                ele.tableId === parentItem.tableId
            );
        });
        return newList;
    };

    const findAllChannelList = (list) => {
        //格式化数据为不同渠道对应一个数组 [[平台渠道],[高德],[百度]]
        let newList = [];

        for (const item of list) {
            let childList = [];
            if (!item.pid) {
                childList = findAllChildrenList(item, list);
                newList.push(childList);
            }
        }
        return newList;
    };

    const fomatItemFormValue = (item, nextList, allList) => {
        const childListKey = itemChildListKey(item.dataType);

        if (item.dataType === ACT_CONFIG_TYPE.DISCOUNT) {
            let resultItem = {
                actChannel: item.actChannel,
                dataType: item.dataType,
            };
            //格式化sn 使用数组下标
            let formatDdjustDetailList = item.discountValue?.adjustDetailList?.map(
                (ele, eleIndex) => {
                    return {
                        ...ele,
                        adjustSn: eleIndex,
                    };
                },
            );

            let discountItemValue = {
                actId: item.discountValue?.actId || '',
                actType: item.discountValue?.templateActType,
                dctValue: item.discountValue?.dctValue,
                vthreeDiscountValue: item.discountValue?.vthreeDiscountValue,
                proVipDiscountValue: item.discountValue?.proVipDiscountValue,
                vipDiscountValue: item.discountValue?.vipDiscountValue,
                adjustPriceType: item.discountValue?.adjustPriceType,
                adjustDetailList: formatDdjustDetailList,
                subChargeFeeFlag: item.discountValue?.subChargeFeeFlag,
                discountDesc: item.discountValue?.templateDetail,
                timePeriods: item.templateValue?.ruleValue || '',
            };
            if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
                discountItemValue.dctExternalCopywriting = nextList?.[0]?.dctExternalCopywriting;
            }
            resultItem = { ...resultItem, ...discountItemValue };
            return [resultItem];
        }

        const formatNextList = nextList.map((ele) => {
            let resultItem = {
                actChannel: item.actChannel,
                dataType: item.dataType,
            };

            switch (item.dataType) {
                case ACT_CONFIG_TYPE.CROWD:
                    resultItem.activeCrowd = ele?.templateValue?.ruleValue;
                    resultItem.activeCrowdName = ele?.templateValue?.ruleValueName;
                    if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                        // 判断人群是否单独选的全部，如是，则延用全部，如否，走新逻辑
                        if (item.selectType == SELECT_TYPES.DISDOUNT_ALL_CROWD) {
                            resultItem.activeCrowdFlag = '1';
                            resultItem.crowdDetailList = [];
                        } else {
                            resultItem.activeCrowdFlag = item.selectType;
                            resultItem.crowdDetailList = crowdDetailList;
                        }
                    } else {
                        resultItem.activeCrowdFlag =
                            item.selectType === SELECT_TYPES.ALL ? '1' : '0';
                    }
                    break;
                case ACT_CONFIG_TYPE.CYCLE:
                    resultItem.cycleType = ele?.templateValue?.templateSubType;
                    resultItem.cycleValue = ele?.templateValue?.ruleValue;
                    resultItem.cycleValueName = ele?.templateValue?.ruleValueName;
                    resultItem.cycleValueFlag = item.selectType === SELECT_TYPES.ALL ? '1' : '0';
                    break;
                case ACT_CONFIG_TYPE.TIME:
                    resultItem.timePeriods = ele?.templateValue?.ruleValue;
                    resultItem.timePeriods = ele?.templateValue?.ruleValueName;
                    resultItem.allDayFlag = item.selectType === SELECT_TYPES.ALL ? '1' : '0';
                    break;
                default:
                    break;
            }
            if (childListKey) {
                const childNextList = findNexLevelChildrenList(ele, allList);
                resultItem[childListKey] = fomatItemFormValue(ele, childNextList, allList);
            }
            return resultItem;
        });

        return formatNextList;
    };

    const itemChildListKey = (dataType) => {
        let key = '';
        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'cycleList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'periodsList';
                }
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'periodsList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'activeCrowdList';
                }
                break;
            case ACT_CONFIG_TYPE.TIME:
                key = 'discountList';
                break;
            default:
                break;
        }
        return key;
    };

    const formatParentFormValue = (parentItem, allChildrenList) => {
        const templateValueList = findNexLevelChildrenList(parentItem, allChildrenList);
        const formatList = fomatItemFormValue(parentItem, templateValueList, allChildrenList);

        // console.log(44455, formatList);
        return formatList;
    };

    const formatChannelListEvent = (list) => {
        let parentList = list.filter((ele) => !ele.pid);
        let resultList = [];
        parentList.forEach((parentItem) => {
            const parentAllChildrenList = findAllChildrenList(parentItem, list);
            resultList = [
                ...resultList,
                ...formatParentFormValue(parentItem, parentAllChildrenList),
            ];
        });
        return resultList;
    };

    const changeFormValueEvent = (tableValue) => {
        const formValue = formatTableToFormEvent(tableValue);
        onChange && onChange(formValue);
    };

    /**
     * 将表格数据格式化层表单树结构
     */
    const formatTableToFormEvent = (originList) => {
        let list = [];
        const channelTableInfo = findAllChannelList(originList);
        if (channelTableInfo instanceof Array) {
            channelTableInfo.forEach((channelTableList) => {
                list = [...list, ...formatChannelListEvent(channelTableList)];
            });
        }
        return list;
    };

    const initEmtpyList = useCallback(
        (tableId, actChannel) => {
            let list = [];
            const basePid = [];
            for (let index = 0; index < colTypeList.length; index++) {
                const element = colTypeList[index];
                const timePids = copyObjectCommon(basePid);
                if (index == 0) {
                    basePid.push(tableId);
                } else {
                    basePid.push(0);
                }
                const preItem = list[index - 1];
                let item = {
                    tableId: basePid.join('-'),
                    dataType: element,
                    selectType: SELECT_TYPES.ALL,
                };
                if (preItem) {
                    item.pid = preItem.tableId;
                }
                if (actChannel) {
                    item.actChannel = actChannel;
                }

                if (element === ACT_CONFIG_TYPE.CROWD && menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                    // 打折立减的人群默认选择全部
                    item.selectType = SELECT_TYPES.DISDOUNT_ALL_CROWD;
                }

                if (element === ACT_CONFIG_TYPE.TIME) {
                    if (staticTimes instanceof Array && staticTimes?.length > 0) {
                        item.selectType = SELECT_TYPES.TEMP;
                    }
                }

                if (element === ACT_CONFIG_TYPE.DISCOUNT) {
                    //如果有默认时段
                    if (staticTimes instanceof Array && staticTimes?.length > 0) {
                        staticTimes.forEach((timeItem, timeIndex) => {
                            const newTimePids = copyObjectCommon(timePids);
                            newTimePids.push(timeIndex);

                            const newPreItem = list.find(
                                (ele) => ele.tableId === timePids.join('-'),
                            );
                            let item = {
                                tableId: newTimePids.join('-'),
                                dataType: element,
                                selectType: SELECT_TYPES.ALL,
                            };
                            if (newPreItem) {
                                item.pid = newPreItem.tableId;
                            }
                            if (actChannel) {
                                item.actChannel = actChannel;
                            }
                            item.templateValue = {
                                ruleValue: timeItem,
                                ruleValueName: timeItem,
                            };
                            list.push(item);
                        });
                    } else {
                        list.push(item);
                    }
                } else {
                    list.push(item);
                }
            }

            return list;
        },
        [sortType, colTypeList, staticTimes],
    );

    //格式化表格
    const formTableList = (originList) => {
        let list = [];

        // ******************************远端数据初始格式化为可用格式的数据************************************

        if (originList) {
            //父级值更新后 重新渲染表格
            let formatOriginList = null;
            if (typeof originList === 'string') {
                formatOriginList = JSON.parse(originList);
            } else if (originList instanceof Array) {
                formatOriginList = originList;
            }

            const newList = formatFormToTableEvent(formatOriginList);

            list = newList;

            let maxId = 0;
            newList.forEach((element) => {
                if (!element.pid) {
                    if (Number(element.tableId) > maxId) {
                        maxId = element.tableId;
                    }
                }
            });

            //如果渠道变更  如果有新选的 初始化新的数组插入
            if (channelList instanceof Array && channelList.length > 0) {
                channelList.forEach((item, index) => {
                    const hasChannel = list.some((ele) => ele.actChannel === item);

                    if (!hasChannel) {
                        list = [...list, ...initEmtpyList(String(maxId + index + 1), item)];
                    }
                });
                //
            }
        }

        // ******************************如果远端没有数据，格式化为默认空数据格式************************************

        if (list?.length === 0) {
            if (channelList instanceof Array && channelList.length > 0) {
                channelList.forEach((item, index) => {
                    list = [...list, ...initEmtpyList(index, item)];
                });
                //
            } else {
                list = initEmtpyList(0);
            }
        }

        return list;
    };

    const checkAllChild = (tableId) => {
        let list = [];
        const childItems = resultTable.filter((ele) => ele.pid == tableId);

        if (childItems.length > 0) {
            list = childItems;
            childItems.forEach((ele, index) => {
                list = [...list, ...checkAllChild(ele.tableId)];
            });
        }

        return list;
    };

    const renderTemplateDom = (info) => {
        let ruleText = '';

        if (!isEmpty(info.templateValue) && info.templateValue.ruleValue) {
            const { ruleValueName, ruleValue } = info.templateValue;

            ruleText = ruleValueName; // || JSON.stringify(itemInfo.templateValue);
        }

        if (ruleText) {
            return (
                <div style={titleStyle}>
                    <span
                        className={classnames(
                            commonStyles['text-clamp-2'],
                            updateStyles['table-pre-title'],
                        )}
                        style={{ maxWidth: `${templateMaxWidth}px` }}
                        title={ruleText}
                    >
                        {ruleText}
                    </span>
                </div>
            );
        }
        return undefined;
    };

    const selectModalRender = (tempInfo, type, disabled) => (
        <ActTemplateSelectModal
            ref={(newRef) => {
                if (selectTemplateRefs?.current) {
                    selectTemplateRefs.current[tempInfo?.tableId] = newRef;
                }
            }}
            {...props}
            currentUser={currentUser}
            templateType={type}
            otherParams={{
                templateType: type,
                operType: (currentUser?.operId?.length && '01') || '02',
            }}
            onSelectEvent={(templateId) => {
                updateTableItemById(tempInfo?.tableId, {
                    selectType: SELECT_TYPES.TEMP,
                });

                tempInfo.selectType = SELECT_TYPES.TEMP;
                itemDropItemSelectEvent(templateId, type, tempInfo);
            }}
            onCreateEvent={() => {
                updateTableItemById(tempInfo?.tableId, {
                    selectType: SELECT_TYPES.TEMP,
                });
                itemDropItemSelectEvent('create', type, tempInfo);
            }}
            disabled={disabled}
        ></ActTemplateSelectModal>
    );

    const colChannelDom = (colType, record) => {
        let itemChannelDom = null;

        if (record.actChannel && colType === firstType && channelList) {
            let channelName = '';
            if (record.actChannel) {
                const itemActChannelList = record?.actChannel?.split(',') || [];
                const formatItemChannelList = itemActChannelList.filter((ele) => ele);
                if (formatItemChannelList?.length > 1) {
                    channelName = formatItemChannelList
                        .map((ele) => getChannelName(ele))
                        .filter((ele) => ele)
                        ?.join(',');
                } else {
                    channelName = getChannelName(record.actChannel);
                }
            }

            itemChannelDom = channelName;
        }
        return itemChannelDom;
    };

    const crowdRef = useRef();

    const crowdColumnItem = {
        title: '活动人群',
        width: menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ? 60 : 120,
        onCell: (record, index) => {
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.CROWD);
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            return {
                rowSpan: record[colDataTypeKey].rowSpan,
            };
        },
        render(text, record) {
            const selectDefaultText = '全部';
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.CROWD);
            const recordColType = colTypeList[colTypeIndex];
            const colDataTypeKey = `${getColKeyByType(recordColType)}Item`;
            let ruleText = renderTemplateDom(record[colDataTypeKey]);

            let disableRadio = disabled;

            if (recordColType === ACT_CONFIG_TYPE.CROWD && channelList) {
                //百度高德不让改人群
                const itemActChannelList = record[colDataTypeKey]?.actChannel?.split(',') || [];
                const formatItemChannelList = itemActChannelList.filter((ele) => ele);
                const hasDisabled = formatItemChannelList.some((ele) =>
                    AllChannelList.includes(ele),
                );
                disableRadio = hasDisabled || disabled;
            }

            if (
                menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE
            ) {
                // 基础价格补贴、招商模板不让改人群
                disableRadio = true;
            }
            const tempInfo = resultTable.find(
                (ele) => record[colDataTypeKey].tableId === ele?.tableId,
            );

            if (readonly) {
                return tempInfo.selectType === SELECT_TYPES.ALL ? selectDefaultText : '已选配置';
            }

            const selectModal = selectModalRender(tempInfo, recordColType, disableRadio);
            const itemChannelItem = colChannelDom(ACT_CONFIG_TYPE.CROWD, record);

            let allRadioDisabled = disableRadio;
            if (menuType == ACT_MENU_TYPE.USER_GROUP) {
                if (record[colDataTypeKey].selectType != '02') {
                    // 人群策略补贴类型不让选全部，绕开图商
                    updateTableItemById(record[colDataTypeKey].tableId, {
                        ...record[colDataTypeKey],
                        selectType: '02',
                    });
                    return;
                }
                allRadioDisabled = true;
            }
            return (
                <Space>
                    {itemChannelItem && <span>{itemChannelItem}</span>}

                    {ruleText}
                    {menuType == ACT_MENU_TYPE.COMPANY_ACT ? (
                        <Fragment>
                            <Radio.Group
                                onChange={(event) => {
                                    if (event.target.value != SELECT_TYPES.DISDOUNT_ALL_CROWD) {
                                        const isPlatform = event.target.value == '01';
                                        crowdRef.current.show({
                                            isPlatform,
                                            otherParams: {
                                                scopeType: !isPlatform ? '4' : undefined,
                                                cooperationPlatform,
                                            },
                                        });
                                    } else {
                                        // 选中all 就清空当前对象和子对象
                                        clearTableChildrenById(record[colDataTypeKey].tableId);
                                    }
                                }}
                                value={record?.[colDataTypeKey]?.selectType}
                                disabled={disabled}
                            >
                                {!isNewDiscountActive ? (
                                    // 旧的活动，固定显示全部
                                    <Radio value={'1'} disabled>
                                        全部
                                    </Radio>
                                ) : (
                                    <Space direction="vertical">
                                        <Radio value={'1'}>全部人群</Radio>
                                        <Radio
                                            value={'01'}
                                            onClick={() => {
                                                crowdRef.current.show({
                                                    isPlatform: true,
                                                    otherParams: {
                                                        cooperationPlatform,
                                                    },
                                                });
                                            }}
                                        >
                                            平台人群
                                        </Radio>
                                        <Radio
                                            value={'02'}
                                            onClick={(e) => {
                                                crowdRef.current.show({
                                                    isPlatform: false,
                                                    otherParams: {
                                                        scopeType: '4',
                                                        cooperationPlatform,
                                                    },
                                                });
                                            }}
                                        >
                                            商家人群
                                        </Radio>
                                    </Space>
                                )}
                            </Radio.Group>

                            <DisdountCrowdSelectModal
                                useModel="01"
                                initRef={crowdRef}
                                onFinish={({ isPlatform, values }) => {
                                    const tempInfo = record[colDataTypeKey];
                                    tempInfo.selectType = isPlatform ? '01' : '02';
                                    updateCrowdDetailList(values);

                                    const newTable = updateTableItemById(tempInfo?.tableId, {
                                        ...tempInfo,
                                    });

                                    const ids = [];
                                    const names = [];
                                    values.map((ele) => {
                                        ids.push(ele.crowdId);
                                        names.push(ele.crowdName);
                                    });
                                    const detailBoList = [];
                                    if (ids.length && names.length) {
                                        detailBoList.push({
                                            id: ids.join(','),
                                            ruleValue: ids.join(','),
                                            ruleValueName: names.join(','),
                                        });
                                    }
                                    updateTableItemChildren(
                                        tempInfo,
                                        detailBoList,
                                        copyObjectCommon(newTable),
                                    );
                                }}
                            />
                        </Fragment>
                    ) : (
                        <Radio.Group
                            onChange={(event) => {
                                const {
                                    target: { value: newSelectType },
                                } = event;
                                if (newSelectType === SELECT_TYPES.ALL) {
                                    //选中all 就清空当前对象和子对象
                                    clearTableChildrenById(record[colDataTypeKey].tableId);
                                } else {
                                    //修改对应值
                                    updateTableItemById(record[colDataTypeKey].tableId, {
                                        ...record[colDataTypeKey],
                                        selectType: newSelectType,
                                    });
                                }
                            }}
                            value={record[colDataTypeKey].selectType}
                            disabled={disableRadio}
                        >
                            <Space direction="vertical">
                                {/* 人群策略补贴类型不让选全部 */}
                                <Radio disabled={allRadioDisabled} value={SELECT_TYPES.ALL}>
                                    {selectDefaultText}
                                </Radio>
                                <Radio value={SELECT_TYPES.TEMP}>{selectModal}</Radio>
                            </Space>
                        </Radio.Group>
                    )}
                </Space>
            );
        },
    };

    const cycleColumnItem = {
        title: '活动周期',
        width: menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ? 140 : 120,
        onCell: (record, index) => {
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.CYCLE);
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            return {
                rowSpan: record[colDataTypeKey].rowSpan,
            };
        },
        render(text, record) {
            let selectDefaultText = '每天';
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.CYCLE);
            const recordColType = colTypeList[colTypeIndex];
            const colDataTypeKey = `${getColKeyByType(recordColType)}Item`;

            let ruleText = renderTemplateDom(record[colDataTypeKey]);

            let disableRadio = staticCycle || disabled;

            const tempInfo = resultTable.find(
                (ele) => record[colDataTypeKey].tableId === ele?.tableId,
            );

            if (readonly) {
                return tempInfo.selectType === SELECT_TYPES.ALL ? selectDefaultText : '已选配置';
            }

            const selectModal = selectModalRender(tempInfo, recordColType, disableRadio);

            const itemChannelItem = colChannelDom(ACT_CONFIG_TYPE.CYCLE, record);

            return (
                <Space>
                    {itemChannelItem && <span>{itemChannelItem}</span>}
                    {ruleText}
                    <Radio.Group
                        onChange={(event) => {
                            const {
                                target: { value: newSelectType },
                            } = event;

                            if (newSelectType === SELECT_TYPES.ALL) {
                                //选中all 就清空当前对象和子对象
                                clearTableChildrenById(record[colDataTypeKey].tableId);
                            } else {
                                //修改对应值
                                updateTableItemById(record[colDataTypeKey].tableId, {
                                    ...record[colDataTypeKey],
                                    selectType: newSelectType,
                                });
                            }
                        }}
                        value={record[colDataTypeKey].selectType}
                        disabled={disableRadio}
                    >
                        <Space direction="vertical">
                            <Radio value={SELECT_TYPES.ALL}>{selectDefaultText}</Radio>
                            <Radio value={SELECT_TYPES.TEMP}>{selectModal}</Radio>
                        </Space>
                    </Radio.Group>
                </Space>
            );
        },
    };

    const timeColumnItem = {
        title: '活动时段',
        width: menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ? 140 : 120,
        onCell: (record, index) => {
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.TIME);
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            return {
                rowSpan: record[colDataTypeKey].rowSpan,
            };
        },
        render(text, record) {
            const selectDefaultText = '全天';

            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.TIME);
            const recordColType = colTypeList[colTypeIndex];
            const colDataTypeKey = `${getColKeyByType(recordColType)}Item`;

            let ruleText = renderTemplateDom(record[colDataTypeKey]);

            let disableRadio = disabled;

            const tempInfo = resultTable.find(
                (ele) => record[colDataTypeKey].tableId === ele?.tableId,
            );

            if (readonly) {
                return tempInfo.selectType === SELECT_TYPES.ALL ? selectDefaultText : '已选配置';
            }

            const selectModal = selectModalRender(tempInfo, recordColType, disableRadio);

            return (
                <Space>
                    {ruleText}

                    {!staticTimes || staticTimes?.length == 0 ? (
                        <Radio.Group
                            onChange={(event) => {
                                const {
                                    target: { value: newSelectType },
                                } = event;

                                if (newSelectType === SELECT_TYPES.ALL) {
                                    //选中all 就清空当前对象和子对象
                                    clearTableChildrenById(record[colDataTypeKey].tableId);
                                } else {
                                    //修改对应值
                                    updateTableItemById(record[colDataTypeKey].tableId, {
                                        ...record[colDataTypeKey],
                                        selectType: newSelectType,
                                    });
                                }
                            }}
                            value={record[colDataTypeKey].selectType}
                            disabled={disableRadio}
                        >
                            <Space direction="vertical">
                                <Radio value={SELECT_TYPES.ALL}>{selectDefaultText}</Radio>
                                <Radio value={SELECT_TYPES.TEMP}>{selectModal}</Radio>
                            </Space>
                        </Radio.Group>
                    ) : null}
                </Space>
            );
        },
    };

    const discountColumnItem = {
        title: '活动优惠',
        width: menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE ? 80 : 120,
        onCell: (record, index) => {
            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.DISCOUNT);
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            return {
                rowSpan: record[colDataTypeKey].rowSpan,
            };
        },
        render(text, record) {
            const discountStyles = {
                'word-break': 'break-all',
                'white-space': 'pre-wrap',
            };

            const actTypeName = formatTemplateTypeText(record?.discountValue?.templateActType);

            let priceTypeName = '';
            switch (record?.discountValue?.adjustPriceType) {
                case ADJUST_PRICE_TYPES.DEFAULT:
                    priceTypeName = '默认';
                    break;
                case ADJUST_PRICE_TYPES.OPER_ORIGIN:
                    priceTypeName = '原价';
                    break;
                case ADJUST_PRICE_TYPES.PUSH:
                    priceTypeName = '推送结算价';
                    break;
                case ADJUST_PRICE_TYPES.SETTLE:
                    priceTypeName = '配置结算价';
                    break;
                default:
                    break;
            }

            if (
                record?.discountValue?.templateActType === MODELTYPS.REDUCTION &&
                record?.discountValue?.subChargeFeeFlag === '1'
            ) {
                priceTypeName = '减电费';
            }

            const priceTypeNameDom = (priceTypeName?.length && `(${priceTypeName})`) || '';

            const actTypeDom =
                (actTypeName && (
                    <span>
                        营销方式：{actTypeName} {priceTypeNameDom}
                        {'\n'}
                    </span>
                )) ||
                '';

            const colTypeIndex = colTypeList.findIndex((ele) => ele === ACT_CONFIG_TYPE.DISCOUNT);
            const recordColType = colTypeList[colTypeIndex];
            const colDataTypeKey = `${getColKeyByType(recordColType)}Item`;
            let ruleText = renderTemplateDom(record[colDataTypeKey]);

            const tempInfo = resultTable.find(
                (ele) => (record[colDataTypeKey]?.tableId || record.tableId) === ele?.tableId,
            );

            return (
                <Space>
                    {ruleText}
                    <span style={discountStyles}>
                        {actTypeDom}
                        {record?.discountValue?.templateDetail ||
                            tempInfo?.discountValue?.templateDetail}
                    </span>

                    {!readonly && (
                        <span
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                updateCurEditTableItemId(tempInfo.tableId);
                                let actParams = {};

                                if (
                                    tempInfo.discountValue &&
                                    tempInfo.discountValue.templateActType
                                ) {
                                    actParams = {
                                        ...tempInfo.discountValue,
                                        adjustDetailList: tempInfo.discountValue?.adjustDetailList,
                                    };

                                    if (actParams.templateActType === MODELTYPS.REDUCTION) {
                                        actParams.subChargeFeeFlag =
                                            tempInfo.discountValue.subChargeFeeFlag === '1'
                                                ? true
                                                : false;
                                    }
                                } else if (tempSaveInfo) {
                                    //有暂存配置的信息直接带入
                                    actParams = tempSaveInfo;
                                }
                                priceRef.current.show({
                                    belongType: belongType,
                                    actParams: actParams,
                                    menuType: menuType,
                                    openParams: {
                                        ...tempInfo,
                                    },
                                });
                            }}
                            style={{ whiteSpace: 'nowrap' }}
                        >
                            设置
                        </span>
                    )}
                </Space>
            );
        },
    };

    const [discountDisplayItemId, updateDiscountDisplayItemId] = useState(false);
    const [discountDisplayForm] = Form.useForm();
    useImperativeHandle(editRef, () => ({
        finishEdit: () => {
            if (discountDisplayItemId) {
                const data = discountDisplayForm.getFieldsValue();
                updateTableItemById(discountDisplayItemId, data);
                updateDiscountDisplayItemId(undefined);
            }
        },
    }));

    const discountDisplayColumnItem = {
        title: '优惠外显文案',
        width: 80,
        onCell: (record, index) => {
            // 和人群用同一个合并项
            const colTypeIndex = colTypeList.findIndex(
                (ele) => ele === ACT_CONFIG_TYPE.DISCOUNT_DISPLAY,
            );
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            return {
                rowSpan: record[colDataTypeKey].rowSpan,
            };
        },
        render(text, record) {
            const colTypeIndex = colTypeList.findIndex(
                (ele) => ele === ACT_CONFIG_TYPE.DISCOUNT_DISPLAY,
            );
            const colDataTypeKey = `${getColKeyByType(colTypeList[colTypeIndex])}Item`;
            const tempInfo = resultTable.find(
                (ele) => (record[colDataTypeKey]?.tableId || record.tableId) === ele?.tableId,
            );
            return (
                <Space direction="vertical" style={{ width: '100%' }}>
                    {discountDisplayItemId && record.tableId == discountDisplayItemId ? (
                        <div className={baseStyles.formItemDetail}>
                            <Form form={discountDisplayForm}>
                                <Form.Item noStyle>
                                    <Form.Item name="dctExternalCopywriting">
                                        <Input
                                            placeholder="请输入"
                                            autoComplete="off"
                                            allowClear
                                            maxLength={20}
                                            showCount
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Form.Item>
                            </Form>
                            <div style={{ textAlign: 'right' }}>
                                <Space>
                                    <CloseOutlined
                                        style={{
                                            color: '#f50',
                                        }}
                                        onClick={() => {
                                            updateDiscountDisplayItemId(undefined);
                                        }}
                                    />
                                    <CheckOutlined
                                        style={{ color: '#52c41a' }}
                                        onClick={() => {
                                            const data = discountDisplayForm.getFieldsValue();
                                            updateDiscountDisplayItemId(undefined);
                                            updateTableItemById(record.tableId, data);
                                        }}
                                    />
                                </Space>
                            </div>
                        </div>
                    ) : (
                        <Space>
                            <span style={{ overflowWrap: 'anywhere' }}>
                                {record?.dctExternalCopywriting}
                            </span>
                            {!readonly && (
                                <span
                                    className={commonStyles['table-btn']}
                                    onClick={() => {
                                        if (
                                            discountDisplayItemId &&
                                            discountDisplayItemId != tempInfo.tableId
                                        ) {
                                            // 直接选了其他外显文案的编辑，则之前编辑途中的数据要实时保存
                                            const data = discountDisplayForm.getFieldsValue();
                                            updateTableItemById(discountDisplayItemId, data);
                                        }
                                        updateDiscountDisplayItemId(tempInfo.tableId);
                                        discountDisplayForm.setFieldsValue(tempInfo);
                                    }}
                                    style={{ whiteSpace: 'nowrap' }}
                                >
                                    {record?.dctExternalCopywriting ? '修改' : '请输入'}
                                </span>
                            )}
                        </Space>
                    )}
                </Space>
            );
        },
    };

    let list = [];

    colTypeList.forEach((item, index) => {
        if (item === ACT_CONFIG_TYPE.CROWD) {
            list.push(crowdColumnItem);
        } else if (item === ACT_CONFIG_TYPE.CYCLE) {
            list.push(cycleColumnItem);
        } else if (item === ACT_CONFIG_TYPE.TIME) {
            list.push(timeColumnItem);
        } else if (item === ACT_CONFIG_TYPE.DISCOUNT) {
            list.push(discountColumnItem);
        } else if (item === ACT_CONFIG_TYPE.DISCOUNT_DISPLAY) {
            list.push(discountDisplayColumnItem);
        }
    });

    //表格列配置
    const moduleColumns = list;

    //格式化resultTable数据用于table渲染
    const formatResultTable = useMemo(() => {
        let list = [];

        const tempColTypeList = copyObjectCommon(colTypeList);

        //最后一层的类型
        const lastDataType = tempColTypeList[tempColTypeList?.length - 1];

        //查找数组中所有最后一层的数据
        const levelItems = resultTable.filter((ele) => ele.dataType === lastDataType);

        //删除最后一类 开始遍历
        tempColTypeList.pop();

        //遍历所有自己数组 开始将自己上层。每集的数据根据对应列类型记录到指定字段中
        levelItems.forEach((item, index) => {
            //取得当前列的类型。并设置存储字段名。 类型名+Item
            const levelKey = `${getColKeyByType(item.dataType)}Item`;
            //将当前列的数据记录到对应类型Item字段里 并设置占行
            item[levelKey] = {
                ...item,
                rowSpan: 1,
            };

            //记录上次的字段名（即子集字段名）
            let beforeLevelKey = levelKey;

            for (let index = tempColTypeList.length - 1; index >= 0; index--) {
                //开始遍历每一层上级 塞数据
                const levelDataType = tempColTypeList[index];
                const levelItems = resultTable.filter((ele) => ele.dataType === levelDataType);

                const plevelKey = `${getColKeyByType(levelDataType)}Item`;
                const newLevelItems = copyObjectCommon(levelItems);
                for (const pItem of newLevelItems) {
                    if (pItem.tableId === item[beforeLevelKey].pid) {
                        const childItems = checkAllChild(pItem.tableId);
                        const lastLevelChild = childItems.filter((child) => {
                            const eleLevel = child.tableId?.split('-').length;

                            return eleLevel === colTypeList.length;
                        });
                        //先把每层各有多少子集算好放进rowSpan
                        item[plevelKey] = {
                            ...pItem,
                            rowSpan: lastLevelChild.length,
                        };
                    }
                }
                beforeLevelKey = plevelKey;
            }
            list.push(item);
        });

        for (let i = list.length - 1; i >= 0; i--) {
            //开始遍历每一层
            const item = list[i];

            const otherItems = list.filter((ele) => ele.tableId != item.tableId);

            for (let index = tempColTypeList.length - 1; index >= 0; index--) {
                //遍历获取所有项
                const levelDataType = tempColTypeList[index];
                const plevelKey = `${getColKeyByType(levelDataType)}Item`;

                //如果同层有相同的id并且已有占位 就要把当前项的占位清0
                const hasSeatRow = otherItems.find((ele) => {
                    return (
                        ele[plevelKey].tableId === item[plevelKey].tableId &&
                        ele[plevelKey].rowSpan > 0
                    );
                });
                if (hasSeatRow) {
                    item[plevelKey].rowSpan = 0;
                }
            }
        }
        return list;
    });

    //表格渲染
    const tableRender = useMemo(() => {
        return (
            <div className={updateStyles['actModule-table']}>
                <Table
                    bordered
                    columns={moduleColumns}
                    dataSource={formatResultTable}
                    pagination={false}
                ></Table>
                <ActivePriceModal
                    {...props}
                    initRef={priceRef}
                    onFinish={(params, openParams) => {
                        const discountValue = {
                            ...params,
                        };
                        if (openParams?.discountValue?.actId) {
                            discountValue.actId = openParams.discountValue.actId;
                        }
                        if (params.templateActType === MODELTYPS.REDUCTION) {
                            discountValue.subChargeFeeFlag = params.subChargeFeeFlag ? '1' : '0';
                        }
                        updateTableItemById(openParams.tableId, {
                            discountValue: discountValue,
                        });

                        const copyParams = copyObjectCommon(params);
                        delete copyParams.dctValue;
                        delete copyParams.vthreeDiscountValue;
                        delete copyParams.proVipDiscountValue;
                        delete copyParams.vipDiscountValue;

                        if (copyParams.adjustDetailList instanceof Array) {
                            copyParams.adjustDetailList.forEach((element) => {
                                delete element.adjustValue;
                                delete element.adjustThreeDiscountValue;
                                delete element.proVipDiscountValue;
                                delete element.adjustVipValue;
                            });
                        }
                        //保存模板信息
                        updateTempSaveInfo(copyParams);
                        priceSelectCallback?.();
                    }}
                />
                <ModuleEditModal
                    {...props}
                    zIndex={124}
                    initRef={templateRef}
                    onFinish={(params, openParams) => {
                        const { id } = params;
                        selectTemplateEvent(id, openParams);

                        //更新对应模板选项
                        updateTemplateOptionsByType(openParams.dataType);

                        if (!isEmpty(selectTemplateRefs?.current)) {
                            //关闭模板选择弹窗
                            for (const key in selectTemplateRefs?.current) {
                                if (Object.hasOwnProperty.call(selectTemplateRefs?.current, key)) {
                                    const element = selectTemplateRefs?.current[key];
                                    element?.closeModal();
                                }
                            }
                        }
                    }}
                ></ModuleEditModal>
            </div>
        );
    });

    return tableRender;
};
export default ActModuleTableFormItem;
