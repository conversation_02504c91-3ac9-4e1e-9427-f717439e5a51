import TablePro from '@/components/TablePro';
import { getCrowdPopTreeListApi } from '@/services/Marketing/TargetedCouponIssuanceApi';
import { useRequest } from 'ahooks';
import { Modal } from 'antd';
import { useEffect, useImperativeHandle, useMemo, useState } from 'react';

const DisdountCrowdSelectModal = (props: any) => {
    const {
        initRef,
        onFinish,
        useModel, // 01 商家自建；02 打折立减活动模板；03 场站营销基础模板
    } = props;

    const [visible, setVisible] = useState(false);
    const [isPlatform, setIsPlatform] = useState(false);
    const [selectedRows, updateSelectRows] = useState<any>([]);
    const [otherParams, updateOtherParams] = useState<any>({});
    useImperativeHandle(initRef, () => ({
        show: ({
            isPlatform: _isPlatform,
            selectedRows: _selectedRows = [],
            otherParams: _otherParams = {},
        }: {
            isPlatform: boolean;
            selectedRows?: any;
            otherParams?: any;
        }) => {
            setIsPlatform(_isPlatform);
            setVisible(true);
            updateSelectRows(_selectedRows);
            updateOtherParams(_otherParams);
        },
    }));

    const onClose = () => {
        setVisible(false);
    };
    const columns = useMemo(() => {
        const list = [
            {
                title: '人群',
                dataIndex: 'crowdName',
                width: 140,
            },
            {
                title: '含义',
                dataIndex: 'labelRemark',
                width: 120,
            },
        ];
        return list;
    }, [isPlatform]);

    const {
        run,
        loading,
        data = [],
    } = useRequest(
        (params) => {
            return getCrowdPopTreeListApi(params).then((res) => res?.data);
        },
        {
            manual: true,
        },
    );

    const searchData = () => {
        run({
            labelType: isPlatform ? '04,05' : '07',
            useModel,
            actType: '02',
            ...otherParams,
        });
    };

    useEffect(() => {
        if (visible) {
            searchData();
        }
    }, [visible]);

    const rowSelection = {
        type: 'checkbox',
        selectedRowKeys: selectedRows.map((item: any) => item.crowdDetailId),
        onChange: (selectedRowKeys: any, _selectedRows: any) => {
            // for (let index = selectedRows.length - 1; index >= 0; index--) {
            //     const element = selectedRows[index];
            //     if (element.children) {
            //         selectedRows.splice(index, 1);
            //     }
            // }
            updateSelectRows([..._selectedRows]);
        },
    };

    const onConfirm = (values: any) => {
        onFinish?.({ isPlatform, values });
        onClose();
    };

    return (
        <Modal
            title="用户人群"
            visible={visible}
            onCancel={onClose}
            // okText={'添加'}
            // cancelText={step == 1 ? '上一步' : '取消'}
            okButtonProps={{
                disabled: !selectedRows?.length,
                loading: loading,
            }}
            width={880}
            onOk={() => {
                onConfirm(selectedRows);
            }}
        >
            <TablePro
                name={`DisdountCrowdSelectModal_list`}
                rowSelection={(data?.length && rowSelection) || undefined}
                loading={loading}
                scroll={{ x: 'max-content' }}
                rowKey={'crowdDetailId'}
                dataSource={data}
                columns={columns}
            />
        </Modal>
    );
};

export default DisdountCrowdSelectModal;
