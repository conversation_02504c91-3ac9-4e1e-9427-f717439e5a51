import { Fragment, useEffect } from 'react';
import { Button, Col, Form, Input, message, Row, Table, Card, Space } from 'antd';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { ActiveModuleListLayout } from './ActCommonLayout';
import { ACT_MENU_TYPE, ADJUST_TYPS } from './ActModuleConfig';
import { ACTIVE_CONFIG_TYPE, MODULE_ACT_NAME } from './declare';
import { useMemo } from 'react';
import { isEmpty } from '@/utils/utils';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const ActiveDetailChannelTable = (props) => {
    const {
        dispatch,
        global: { custLabelTypeList },
        actInfo = {},
        menuType,
    } = props;

    useEffect(() => {
        if (!(custLabelTypeList instanceof Array) || custLabelTypeList?.length === 0) {
            dispatch({
                type: 'global/initCustLabelTypeList',
            });
        }
    }, []);

    const getCycleName = ({ cycleValueFlag, cycleValueName }) => {
        if (cycleValueFlag == '1') {
            return '每天';
        }

        if (cycleValueName) {
            return cycleValueName;
        }
        return undefined;
    };

    const getActiveCrowdName = ({ activeCrowdFlag, activeCrowd }) => {
        let res = '';
        if (activeCrowdFlag == '1') {
            res = '全部';
        } else if (activeCrowd?.length) {
            const types = activeCrowd.split(',');
            const matchedNames = [];
            types.map((ele) => {
                let name = '-';
                const findName = (list) => {
                    for (const labelEle of list) {
                        if (labelEle?.children) {
                            for (const element of labelEle?.children) {
                                if (element?.children) {
                                    findName(element?.children);
                                } else if (element.codeValue == ele) {
                                    name = element.codeName;
                                }

                                if (name !== '-') {
                                    break;
                                }
                            }
                        } else if (labelEle?.codeValue == ele) {
                            name = labelEle.codeName;
                            break;
                        }

                        if (name !== '-') {
                            break;
                        }
                    }
                };
                findName(custLabelTypeList);
                matchedNames.push(name);
            });
            res = matchedNames?.join(',');
        }
        return res;
    };

    const getPeriodsName = ({ allDayFlag, allDayFlagName, timePeriods }) => {
        if (allDayFlag == '1') {
            return '全天';
        }

        if (timePeriods) {
            return timePeriods;
        }
        if (allDayFlagName) {
            return allDayFlagName;
        }
    };

    const getActTypeName = ({ actType, subChargeFeeFlag, adjustPriceType }) => {
        let name;
        if (actType) {
            let priceTypeName = '';
            switch (adjustPriceType) {
                case ADJUST_PRICE_TYPES.DEFAULT:
                    priceTypeName = '默认';
                    break;
                case ADJUST_PRICE_TYPES.OPER_ORIGIN:
                    priceTypeName = '原价';
                    break;
                case ADJUST_PRICE_TYPES.PUSH:
                    priceTypeName = '结算价';
                    break;
                default:
                    break;
            }
            if (subChargeFeeFlag == '1') {
                priceTypeName = '减电费';
            }
            name = MODULE_ACT_NAME[actType];
            if (priceTypeName?.length) {
                name += `(${priceTypeName})`;
            }
        }
        return name;
    };

    const columnSortItem = useMemo(() => {
        const list = [];
        if (actInfo?.configType == ACTIVE_CONFIG_TYPE.CYCLE) {
            // 按周期配置
            list.push(
                ...[
                    { nameKey: 'actChannelName' },
                    {
                        nameFunction: getCycleName,
                        nameKey: 'cycleValueName',
                    },
                    {
                        listKey: 'activeCrowdList',
                        nameFunction: getActiveCrowdName, // 备用，通过方法取值，最好是通过接口字段直接取
                        nameKey: 'activeCrowdName',
                    },
                    {
                        listKey: 'periodsList',
                        nameFunction: getPeriodsName,
                        nameKey: 'periodsdName',
                    },
                    {
                        listKey: 'discountList',
                        nameFunction: getActTypeName,
                        nameKey: 'actTypeName',
                    },
                    {
                        nameKey: 'discountDesc',
                    },
                ],
            );
        } else if (actInfo?.configType == ACTIVE_CONFIG_TYPE.CROWD) {
            // 按人群配置
            list.push(
                ...[
                    { nameKey: 'actChannelName' },
                    {
                        nameFunction: getActiveCrowdName,
                        nameKey: 'activeCrowdName',
                    },
                    {
                        listKey: 'cycleList',
                        nameFunction: getCycleName,
                        nameKey: 'cycleValueName',
                    },
                    {
                        listKey: 'periodsList',
                        nameFunction: getPeriodsName,
                        nameKey: 'periodsdName',
                    },
                    {
                        listKey: 'discountList',
                        nameFunction: getActTypeName,
                        nameKey: 'actTypeName',
                    },
                    {
                        nameKey: 'discountDesc',
                    },
                ],
            );
        }
        if (menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
            list.push({
                nameKey: 'dctExternalCopywriting',
            });
        }
        return list;
    }, [actInfo, custLabelTypeList, menuType]);

    // 处理合并单元格，需要重新构造一个展示对象
    const showList = useMemo(() => {
        const list = [];
        // 按周期配置
        const loopCycle = (_list) => {
            if (!_list) {
                return [];
            }
            const tempList = [];
            let columnRowSpan1 = 0; // 渠道合并行
            let columnRowSpan2 = 0; // 活动周期合并行
            let columnRowSpan3 = 0; // 活动人群合并行

            for (let index1 = _list.length - 1; index1 >= 0; index1--) {
                const item1 = _list[index1];
                const commonItem1 = {
                    ...item1,
                    [columnSortItem[0]?.nameKey]:
                        item1[columnSortItem[0]?.nameKey] ||
                        columnSortItem[0]?.nameFunction?.(item1),
                    [columnSortItem[1]?.nameKey]:
                        item1[columnSortItem[1]?.nameKey] ||
                        columnSortItem[1]?.nameFunction?.(item1),
                };

                const subList2 = item1[columnSortItem[2]?.listKey];
                if (subList2?.length) {
                    columnRowSpan1 = 0;
                    for (let index2 = subList2?.length - 1; index2 >= 0; index2--) {
                        // 人群列
                        const item2 = subList2[index2];
                        const commonItem2 = {
                            ...commonItem1,
                            ...item2,
                            [columnSortItem[2]?.nameKey]:
                                item2[columnSortItem[2]?.nameKey] ||
                                columnSortItem[2]?.nameFunction?.(item2),
                        };

                        const subList3 = item2[columnSortItem[3]?.listKey];
                        if (subList3?.length) {
                            columnRowSpan2 = 0;
                            for (let index3 = subList3?.length - 1; index3 >= 0; index3--) {
                                // 时段列
                                const item3 = subList3[index3];
                                const commonItem3 = {
                                    ...commonItem2,
                                    ...item3,
                                    [columnSortItem[3]?.nameKey]:
                                        item3[columnSortItem[3]?.nameKey] ||
                                        columnSortItem[3]?.nameFunction?.(item3),
                                };

                                const subList4 = item3[columnSortItem[4]?.listKey];
                                if (subList4?.length) {
                                    columnRowSpan3 = 0;
                                    for (let index4 = subList4?.length - 1; index4 >= 0; index4--) {
                                        // 循环到优惠的第一行的时候，进行合并项统计
                                        ++columnRowSpan3;
                                        ++columnRowSpan2;
                                        ++columnRowSpan1;

                                        // 营销方式优惠列
                                        const item4 = subList4[index4];
                                        const resultItem = {
                                            ...commonItem3,
                                            ...item4,
                                            columnRowSpan3: 0,
                                            columnRowSpan2: 0,
                                            columnRowSpan1: 0,
                                            [columnSortItem[4]?.nameKey]:
                                                item4[columnSortItem[4]?.nameKey] ||
                                                columnSortItem[4]?.nameFunction?.(item4),
                                        };

                                        if (index4 == 0) {
                                            resultItem.columnRowSpan3 = columnRowSpan3;
                                            if (index3 == 0) {
                                                resultItem.columnRowSpan2 = columnRowSpan2;
                                                if (index2 == 0) {
                                                    resultItem.columnRowSpan1 = columnRowSpan1;
                                                }
                                            }
                                        }
                                        tempList.splice(0, 0, resultItem);
                                    }
                                } else {
                                    // 循环到优惠的第一行的时候，进行合并项统计
                                    ++columnRowSpan2;
                                    ++columnRowSpan1;

                                    // 活动
                                    const resultItem = {
                                        ...commonItem3,
                                        columnRowSpan3: 1,
                                        columnRowSpan2: 0,
                                        columnRowSpan1: 0,
                                    };
                                    if (index3 == 0) {
                                        resultItem.columnRowSpan2 = columnRowSpan2;
                                        if (index2 == 0) {
                                            resultItem.columnRowSpan1 = columnRowSpan1;
                                        }
                                    }
                                    tempList.splice(0, 0, resultItem);
                                }
                            }
                        } else {
                            // 循环到优惠的第一行的时候，进行合并项统计
                            ++columnRowSpan1;

                            // 活动
                            const resultItem = {
                                ...commonItem2,
                                columnRowSpan3: 1,
                                columnRowSpan2: 1,
                                columnRowSpan1: 0,
                            };
                            if (index2 == 0) {
                                resultItem.columnRowSpan1 = columnRowSpan1;
                            }
                            tempList.splice(0, 0, resultItem);
                        }
                    }
                } else {
                    // 循环到优惠的第一行的时候，进行合并项统计

                    // 活动
                    const resultItem = {
                        columnRowSpan3: 1,
                        columnRowSpan2: 1,
                        columnRowSpan1: 1,
                    };
                    tempList.splice(0, 0, resultItem);
                }
            }
            return tempList;
        };
        const platList = loopCycle(actInfo?.gridList);
        list.push(...platList);
        const cycleList = loopCycle(actInfo?.mapGridList);
        list.push(...cycleList);
        return list;
    }, [columnSortItem]);

    const columns = useMemo(
        () => [
            ...(menuType == ACT_MENU_TYPE.USER_GROUP || menuType == ACT_MENU_TYPE.COMPANY_ACT
                ? []
                : [
                      {
                          title: '投放渠道',
                          width: 180,
                          dataIndex: columnSortItem[0]?.nameKey,
                          render(text, record) {
                              return (
                                  <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                      {text || '-'}
                                  </span>
                              );
                          },
                          onCell: (record, index) => {
                              return {
                                  rowSpan: record.columnRowSpan1,
                              };
                          },
                      },
                  ]),
            {
                title: actInfo?.configType == ACTIVE_CONFIG_TYPE.CYCLE ? '活动周期' : '活动人群',
                width: 160,
                dataIndex: columnSortItem[1]?.nameKey,
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
                onCell: (record, index) => {
                    return {
                        rowSpan: record.columnRowSpan2,
                    };
                },
            },
            {
                title: actInfo?.configType == ACTIVE_CONFIG_TYPE.CYCLE ? '活动人群' : '活动周期',
                width: 180,
                dataIndex: columnSortItem[2]?.nameKey,
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
                onCell: (record, index) => {
                    return {
                        rowSpan: record.columnRowSpan3,
                    };
                },
            },
            {
                title: '活动时段',
                width: 120,
                dataIndex: columnSortItem[3]?.nameKey,
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            {
                title: '营销方式',
                width: 120,
                dataIndex: columnSortItem[4]?.nameKey,
                render(text, record) {
                    if (!text || text === 'null') {
                        text = '-';
                    }
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            {
                title: '活动优惠',
                width: 360,
                dataIndex: columnSortItem[5]?.nameKey,
                render(text, record) {
                    if (record.desc) {
                        return renderDiscountTable(record.desc);
                    }
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            ...((menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE && [
                {
                    title: '优惠外显文案',
                    width: 120,
                    dataIndex: columnSortItem[6]?.nameKey,
                    render(text, record) {
                        return (
                            <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                {text || '-'}
                            </span>
                        );
                    },
                },
            ]) ||
                []),
        ],
        [actInfo, columnSortItem, menuType],
    );

    const renderDiscountTable = (list) => {
        return list.map((record, index) => {
            const titleDom = <span>{record.discountType}</span>;
            let result = record.discountValue >= 0 ? `${record.discountValue}${record.unit}` : '-';
            let preText = '';
            if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                let preUnit = record.adjustMethodType === '0' ? '-' : '+';
                const beforeStr =
                    record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                        ? '配置结算'
                        : '（结算价/原价）';
                preText = `${beforeStr}${preUnit}`;
            }
            let v3Result =
                !isEmpty(record.vthreeDiscountValue) && record.vthreeDiscountValue >= 0
                    ? `${record.vthreeDiscountValue}${record.unit}`
                    : '';
            let v3PreText = '';
            if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                let preUnit = record.vthreeAdjustMethodType === '0' ? '-' : '+';
                const beforeStr =
                    record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                        ? '配置结算'
                        : '（结算价/原价）';
                v3PreText = `${beforeStr}${preUnit}`;
            }
            let vipResult =
                !isEmpty(record.vipDiscountValue) && record.vipDiscountValue >= 0
                    ? `${record.vipDiscountValue}${record.unit}`
                    : '';
            let vipPreText = '';
            if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                let preUnit = record.vipAdjustMethodType === '0' ? '-' : '+';
                const beforeStr =
                    record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                        ? '配置结算'
                        : '（结算价/原价）';
                vipPreText = `${beforeStr}${preUnit}`;
            }
            let proVipResult =
                !isEmpty(record.proVipDiscountValue) && record.proVipDiscountValue >= 0
                    ? `${record.proVipDiscountValue}${record.unit}`
                    : '';
            let proVipPreText = '';
            if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                let preUnit = record.proVipMethodType === '0' ? '-' : '+';
                const beforeStr =
                    record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                        ? '配置结算'
                        : '（结算价/原价）';
                proVipPreText = `${beforeStr}${preUnit}`;
            }
            const commonDom = (
                <span>
                    普通用户{titleDom}：<span title={preText}>{preText}</span>
                    <span style={record.discountFlag ? { color: 'red' } : {}} title={result}>
                        {result}
                    </span>
                </span>
            );
            const v3Dom = (
                <span>
                    V3专享{titleDom}：<span title={v3PreText}>{v3PreText}</span>
                    <span style={record.v3DiscountFlag ? { color: 'red' } : {}} title={v3Result}>
                        {v3Result}
                    </span>
                </span>
            );
            const vipDom = (
                <span>
                    会员用户{titleDom}：<span title={vipPreText}>{vipPreText}</span>
                    <span style={record.vipDiscountFlag ? { color: 'red' } : {}} title={vipResult}>
                        {vipResult}
                    </span>
                </span>
            );
            const proVipDom = (
                <span>
                    联合会员Pro价{titleDom}：<span title={proVipPreText}>{proVipPreText}</span>
                    <span
                        style={record.proVipDiscountFlag ? { color: 'red' } : {}}
                        title={proVipResult}
                    >
                        {proVipResult}
                    </span>
                </span>
            );
            return (
                <Space key={index}>
                    {commonDom} {v3Result && v3Dom} {vipResult && vipDom}
                    {proVipResult && proVipDom}
                </Space>
            );
        });
    };
    return (
        <Form.Item>
            <Table columns={columns} dataSource={showList} bordered pagination={false} />
        </Form.Item>
    );
};

export default ActiveDetailChannelTable;
