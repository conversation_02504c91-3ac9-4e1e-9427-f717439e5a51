import { Fragment } from 'react';
import { Button, Col, Form, Input, message, Row } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { MODULE_TYPE_CODES } from './declare';
import styles from '@/assets/styles/common.less';
import baseStyles from '@/assets/styles/base.less';

const FormItem = Form.Item;

export const ActiveModuleListLayout = (props) => {
    const { form, name = 'list', ruleContent, maxCount = 10, multiple, type } = props;

    return (
        <Form.List
            name={name}
            initialValue={multiple ? [] : [type === MODULE_TYPE_CODES.TIME ? [{}] : '']}
        >
            {(fields, { add, remove }) => (
                <Fragment>
                    {fields.map((field, index) => {
                        return (
                            <Row
                                key={field.key}
                                gutter={[12, 6]}
                                className={baseStyles['formItemDetail']}
                            >
                                {multiple && (
                                    <>
                                        <Col>
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{
                                                    margin: '0 8px',
                                                }}
                                                onClick={() => {
                                                    remove(field.name);
                                                }}
                                            />
                                        </Col>
                                        <Col>
                                            <FormItem>
                                                <span>规则{`${index + 1}`}</span>
                                            </FormItem>
                                        </Col>
                                    </>
                                )}

                                <Col flex="1">{ruleContent(index)}</Col>
                            </Row>
                        );
                    })}
                    {multiple && (
                        <FormItem label="">
                            <Button
                                type="dashed"
                                onClick={() => {
                                    if (fields?.length < maxCount) {
                                        add();
                                    } else {
                                        message.error(`最多支持${maxCount}条规则`);
                                    }
                                }}
                                style={{ width: '200px' }}
                            >
                                <PlusOutlined />
                                新增规则
                            </Button>
                        </FormItem>
                    )}
                </Fragment>
            )}
        </Form.List>
    );
};
