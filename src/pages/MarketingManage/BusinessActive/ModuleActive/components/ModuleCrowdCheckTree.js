import { useEffect, useMemo } from 'react';
import { Select, TreeSelect } from 'antd';
import { useRequest } from 'ahooks';
import { queryCrowdTagExistList } from '@/services/Marketing/ActiveCrowdApi';
import { CROWD_STATUS } from '@/constants/crowd';
const { SHOW_CHILD } = TreeSelect;

const initTreeList = (treeList, disableIds, noDisabledOption = false) => {
    //先排序按照启用的在前面，禁用的在后面排序
    const newList = [
        ...treeList?.filter((v) => v?.status === CROWD_STATUS.ENABLE),
        ...treeList?.filter((v) => v?.status === CROWD_STATUS.DISABLE),
    ];
    //先过滤再禁用
    return newList.map((ele) => {
        let treeItem = {
            title: ele.crowdName,
            label: ele.crowdName,
            value: ele.custCrowd,
            disabled:
                !noDisabledOption &&
                (disableIds?.includes(ele.custCrowd) || ele?.status === CROWD_STATUS.DISABLE),
        };
        return treeItem;
    });
};
const ActiveCrowdCheckTree = (props) => {
    const {
        value,
        onChange,
        disabled,
        disableIds, // 不可选择的人群id
        //人群来源，新电途人群/CDP人群
        sourceType,
        //人群状态，启用/禁用
        crowdStatus,
        //选择模式
        mode = 'multiple',
        noDisabledOption, //是否不出现不可选择的人群
    } = props;
    const { run, data, loading } = useRequest(
        (type, status) => {
            return queryCrowdTagExistList({
                sourceType: type,
                status,
            });
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        run(sourceType, crowdStatus);
    }, [sourceType, crowdStatus]);

    const treeOptions = useMemo(() => {
        if (data?.data) {
            return [...initTreeList(data?.data, disableIds, noDisabledOption)];
        } else {
            return [];
        }
    }, [data, disableIds, noDisabledOption]);

    const changeSelectEvent = (newValue) => {
        updateFormData(newValue);
    };
    const updateFormData = (newValue) => {
        onChange && onChange(newValue);
    };

    // select属性
    return (
        <Select
            value={value}
            options={treeOptions}
            showCheckedStrategy={SHOW_CHILD}
            loading={loading}
            showArrow
            showSearch
            placeholder="请选择"
            onChange={changeSelectEvent}
            disabled={disabled}
            mode={mode}
            allowClear
            filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase()) ||
                (option?.value ?? '').toLowerCase().includes(input.toLowerCase())
            }
        />
    );
};

export default ActiveCrowdCheckTree;
