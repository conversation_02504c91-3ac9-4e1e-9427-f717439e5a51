export const ACT_CONFIG_TYPE = {
    CROWD: '01', //人群
    CYCLE: '02', //周期
    TIME: '03', //时段
    DISCOUNT: '04', //优惠
    DISCOUNT_DISPLAY: '05', // 招商优惠外显
};
export const CONFIG_MODEL = {
    CROWD: '02', //人群
    CYCLE: '01', //周期
};

export const MODELTYPS = {
    DISCOUNT: '02', // 打折
    REDUCTION: '10', // 立减
    DYNAMIC: '30', //动态
    PRICE: '50', //一口价
    CROWDPLLICY_DISCOUNT: '46', // 策略打折
    CROWDPLLICY_REDUCTION: '47', // 策略立减
    CROWDPLLICY_PRICE: '51', // 策略一口价
};

export const ADJUST_TYPS = {
    DISCOUNT: '01', // 打折
    REDUCE: '02', //立减
    ADD: '03', //立加
    DYNAMIC: '04', //动态
};

export const ACT_MENU_TYPE = {
    ACT_1: '01', // 旧的活动
    ACT_2: '02', // 2.0
    USER_GROUP: '03', // 人群策略补贴
    BASIC_PRICE: '04', // 基础价格补贴
    COMPANY_ACT: '05', // 商家活动
    TEMPLATE: '06', //前端模板组件定义
    MERCHANTS_BASIC_PRICE_TEMPLATE: '07', // 招商，新建基础价格补贴活动模板
};

export const BusinessActiveType = (item, pathname) => {
    let menuType;
    if (item && item.menuType != ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) {
        /**
         * 人群策略：categoryType给02，belongType传plat-平台
         * 基础价格：categoryType给01，belongType给plat-平台
         * 商家活动：categoryType给01，belongType给oper-运营商
         * 如果是招商类型，强制走根据path判断的方法
         */
        const { categoryType, belongType, parentActFlag } = item;
        if (parentActFlag !== '1') {
            menuType = ACT_MENU_TYPE.ACT_1;
        } else {
            if (categoryType == '02') {
                menuType = ACT_MENU_TYPE.USER_GROUP;
            } else if (categoryType == '01') {
                if (belongType == 'plat') {
                    menuType = ACT_MENU_TYPE.BASIC_PRICE;
                } else if (belongType == 'oper') {
                    menuType = ACT_MENU_TYPE.COMPANY_ACT;
                }
            }
        }
    }
    if (!menuType && pathname) {
        // 仅做兜底用，可以用在新建页，工单不应走进来
        if (pathname.indexOf('businessActive/module-user-group') > -1) {
            menuType = ACT_MENU_TYPE.USER_GROUP;
        } else if (pathname.indexOf('businessActive/module-basic-price') > -1) {
            menuType = ACT_MENU_TYPE.BASIC_PRICE;
        } else if (pathname.indexOf('merchantActivityManagement/module-company-act') > -1) {
            menuType = ACT_MENU_TYPE.COMPANY_ACT;
        } else if (pathname.indexOf('businessActive/moduleAct') > -1) {
            menuType = ACT_MENU_TYPE.ACT_2;
        } else if (
            pathname.indexOf('merchantActivityManagement/recruitMerchantsPlan/template') > -1
        ) {
            menuType = ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE;
        } else {
            menuType = ACT_MENU_TYPE.ACT_1;
        }
    }
    return menuType;
};
