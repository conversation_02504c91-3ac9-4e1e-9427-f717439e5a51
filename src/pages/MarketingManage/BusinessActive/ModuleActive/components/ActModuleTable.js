import { useMemo, useState, useCallback, useEffect, useRef, Fragment } from 'react';
import { Row, Col, Radio, Space, Divider, Card, Dropdown, Menu } from 'antd';

import { copyObjectCommon, isEmpty } from '@/utils/utils';
import commonStyles from '@/assets/styles/common.less';
import updateStyles from '../BusinessActiveUpdatePage.less';

import ActivePriceModal from './ActivePriceModal';
import ModuleEditModal from './ModuleEditModal';
import ActTemplateSelectModal from './ActTemplateSelectModal';

import { DownOutlined } from '@ant-design/icons';

import classnames from 'classnames';

import {
    getActTemplateListApi,
    getActTemplateDetailApi,
    getActTemplateDetailMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';

import { ACT_CONFIG_TYPE, CONFIG_MODEL, MODELTYPS } from './ActModuleConfig';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const formatAllChannelList = (arr) => {
    let list = [];

    for (const item of arr) {
        const otherItems = [...arr].filter((ele) => ele != item);
        list.push(item);
        if (otherItems instanceof Array && otherItems.length > 0) {
            while (otherItems.length > 0) {
                list.push([item, ...otherItems].join(','));
                otherItems.pop();
            }
        }
    }
    return list;
};

const MAX_LEVEL = 4;

const MAX_SPAN = 24;
const mapChannelList = ['11', '15'];
const AllChannelList = formatAllChannelList(mapChannelList); //排列组合所有渠道

const SELECT_TYPES = {
    ALL: '01',
    TEMP: '02',
};

const getColNameByType = (type) => {
    let key = '';
    switch (type) {
        case ACT_CONFIG_TYPE.CROWD:
            key = '活动人群';

            break;
        case ACT_CONFIG_TYPE.CYCLE:
            key = '活动周期';
            break;
        case ACT_CONFIG_TYPE.TIME:
            key = '活动时段';
            break;
        case ACT_CONFIG_TYPE.DISCOUNT:
            key = '活动优惠';
            break;

        default:
            break;
    }
    return key;
};
const getTemplateOptionsByType = async (type) => {
    try {
        const {
            data: { list },
        } = await getActTemplateListApi({
            templateType: type,
        });
        return list || [];
    } catch (error) {
        return Promise.reject(error);
    }
};
const ActModuleTable = (props) => {
    const {
        value,
        onChange,
        disabled, //禁用
        readonly, //只读
        sortType = CONFIG_MODEL.CROWD,
        belongType,
        channelList, //对应特殊渠道
        dispatch,
        businessModuleActiveModel,
        currentUser,
        menuType,
        ...otherProps
    } = props;

    const {
        crwodTemplateList = [],
        cycleTemplateList = [],
        timeTemplateList = [],
    } = businessModuleActiveModel;

    const priceRef = useRef();
    const templateRef = useRef();
    const selectTemplateRefs = useRef({}); //模板选择弹窗

    const [resultTable, updateResultTable] = useState([]);

    const [templateMaxWidth, updateTemplateMaxWidth] = useState(200);

    // const [crwodTemplateList, updateCrwodTemplateList] = useState([]);
    // const [cycleTemplateList, updateCycleTemplateList] = useState([]);
    // const [timeTemplateList, updateTimeTemplateList] = useState([]);
    // const [discountTemplateList, updateDiscountTemplateList] = useState([]);

    const [tempSaveInfo, updateTempSaveInfo] = useState(null);

    const [curEditTableItemId, updateCurEditTableItemId] = useState(null); //当前配置的item对应的tableId

    const firstType = useMemo(() => {
        let type = '';
        if (sortType === CONFIG_MODEL.CROWD) {
            type = ACT_CONFIG_TYPE.CROWD;
        } else if (sortType === CONFIG_MODEL.CYCLE) {
            type = ACT_CONFIG_TYPE.CYCLE;
        }
        return type;
    }, [sortType]);
    const colTypeList = useMemo(() => {
        const tableHeaderKeys = [];
        if (sortType === CONFIG_MODEL.CROWD) {
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CROWD);
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CYCLE);
        } else if (sortType === CONFIG_MODEL.CYCLE) {
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CYCLE);
            tableHeaderKeys.push(ACT_CONFIG_TYPE.CROWD);
        }

        tableHeaderKeys.push(ACT_CONFIG_TYPE.TIME);
        tableHeaderKeys.push(ACT_CONFIG_TYPE.DISCOUNT);

        return tableHeaderKeys;
    }, [sortType]);

    useEffect(() => {
        initAllOptions();

        // const formValue = [
        //     {
        //         dataType: '02',
        //         cycleType: '0201',
        //         cycleValue: '2,6',
        //         cycleValueFlag: '0',
        //         activeCrowdList: [
        //             {
        //                 dataType: '01',
        //                 activeCrowdFlag: '1',
        //                 periodsList: [
        //                     {
        //                         dataType: '03',
        //                         allDayFlag: '1',
        //                         discountList: [{ dataType: '04', timePeriods: '' }],
        //                     },
        //                 ],
        //             },
        //         ],
        //     },
        //     {
        //         dataType: '02',
        //         cycleType: '0201',
        //         cycleValue: '3,4,5',
        //         cycleValueFlag: '0',
        //         activeCrowdList: [
        //             {
        //                 dataType: '01',
        //                 activeCrowdFlag: '1',
        //                 periodsList: [
        //                     {
        //                         dataType: '03',
        //                         allDayFlag: '1',
        //                         discountList: [{ dataType: '04', timePeriods: '' }],
        //                     },
        //                 ],
        //             },
        //         ],
        //     },
        // ];

        // const newList = formatFormToTableEvent(formValue);
        // updateResultTable(newList);

        //按周期数据
        // const tableValue = [
        //     { dataType: '02', tableId: '0', selectType: '02' },
        //     {
        //         tableId: '0-0',
        //         dataType: '01',
        //         selectType: '01',
        //         pid: '0',
        //         templateValue: {
        //             id: '250',
        //             templateId: '117',
        //             ruleSn: '1',
        //             ruleMethodType: null,
        //             ruleValue: '2,6',
        //             ruleValueName: '周二、周六',
        //             ruleVipMethodType: null,
        //             ruleVipValue: null,
        //             createTime: '2022-11-25 20:02:27.0',
        //             updatedTime: '2022-11-25 20:02:27.0',
        //             ruleAdjustType: null,
        //             templateSubType: '0201',
        //         },
        //     },
        //     { tableId: '0-0-0', dataType: '03', selectType: '01', pid: '0-0' },
        //     { tableId: '0-0-0-0', dataType: '04', selectType: '01', pid: '0-0-0' },
        //     {
        //         tableId: '0-1',
        //         dataType: '01',
        //         selectType: '01',
        //         pid: '0',
        //         templateValue: {
        //             id: '251',
        //             templateId: '117',
        //             ruleSn: '2',
        //             ruleMethodType: null,
        //             ruleValue: '3,4,5',
        //             ruleValueName: '周三、周四、周五',
        //             ruleVipMethodType: null,
        //             ruleVipValue: null,
        //             createTime: '2022-11-25 20:02:27.0',
        //             updatedTime: '2022-11-25 20:02:27.0',
        //             ruleAdjustType: null,
        //             templateSubType: '0201',
        //         },
        //     },
        //     { tableId: '0-1-0', dataType: '03', selectType: '01', pid: '0-1' },
        //     { tableId: '0-1-0-0', dataType: '04', selectType: '01', pid: '0-1-0' },
        // ];
        // formatTableToFormEvent(tableValue);

        const tableClassDom = document.getElementsByClassName(updateStyles['actModule-table']);

        // console.log(1111222233, tableClassDom[0].offsetWidth);
        if (tableClassDom[0].offsetWidth < 1600) {
            updateTemplateMaxWidth(100);
        }
    }, []);

    // useEffect(() => {
    //     const list = formTableList(value);
    //     updateResultTable(list);
    // }, [channelList]);

    useEffect(() => {
        const copyAllChannelList = copyObjectCommon(AllChannelList);

        let exCludeChannelList = copyAllChannelList;

        if (channelList instanceof Array) {
            channelList.forEach((element) => {
                const excludeIndex = exCludeChannelList.findIndex((ele) => {
                    return ele == element;
                });
                if (excludeIndex >= 0) {
                    exCludeChannelList.splice(excludeIndex, 1);
                }
            });
        }

        //过滤未选择的渠道的数据
        let excludeValue = value;
        if (value instanceof Array) {
            excludeValue = value.filter((ele) => {
                return !exCludeChannelList.includes(ele.actChannel);
            });
        }

        const list = formTableList(excludeValue);
        updateResultTable(list);

        if (value && value.length != excludeValue.length) {
            changeFormValueEvent(list);
        } else if (!value) {
            changeFormValueEvent(list);
        }
    }, [value, sortType, channelList]);

    /**
     * 初始化模板选项
     */
    const initAllOptions = () => {
        if (crwodTemplateList?.length === 0) {
            initCrwodTemplateList();
        }
        if (cycleTemplateList?.length === 0) {
            initCycleTemplateList();
        }
        if (timeTemplateList?.length === 0) {
            initTimeTemplateList();
        }

        // initDiscountTemplateList();
    };

    const updateTemplateOptionsByType = (type) => {
        switch (type) {
            case ACT_CONFIG_TYPE.CROWD:
                initCrwodTemplateList();

                break;
            case ACT_CONFIG_TYPE.CYCLE:
                initCycleTemplateList();

                break;

            case ACT_CONFIG_TYPE.TIME:
                initTimeTemplateList();

                break;

            default:
                break;
        }
    };

    //初始化人群模板选项
    const initCrwodTemplateList = async () => {
        // try {
        //     const list = await getTemplateOptionsByType(ACT_CONFIG_TYPE.CROWD);
        //     updateCrwodTemplateList(list || []);
        //     return list || [];
        // } catch (error) {
        //     return Promise.reject(error);
        // }
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.CROWD,
        });
    };

    const crwodTemplateOptions = useMemo(() => {
        if (crwodTemplateList instanceof Array) {
            return crwodTemplateList.map((ele) => {
                return {
                    key: ele.templateId,
                    label: (
                        <div className="text-line" title={ele.templateName}>
                            {ele.templateName}
                        </div>
                    ),
                };
            });
        } else {
            return [];
        }
    }, [crwodTemplateList]);

    //初始化周期模板选项
    const initCycleTemplateList = async () => {
        // try {
        //     const list = await getTemplateOptionsByType(ACT_CONFIG_TYPE.CYCLE);
        //     updateCycleTemplateList(list || []);
        //     return list || [];
        // } catch (error) {
        //     return Promise.reject(error);
        // }
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.CYCLE,
        });
    };

    const cycleTemplateOptions = useMemo(() => {
        if (cycleTemplateList instanceof Array) {
            return cycleTemplateList.map((ele) => {
                return {
                    key: ele.templateId,
                    label: (
                        <div className="text-line" title={ele.templateName}>
                            {ele.templateName}
                        </div>
                    ),
                };
            });
        } else {
            return [];
        }
    }, [cycleTemplateList]);

    //初始化时段模板选项
    const initTimeTemplateList = async () => {
        // try {
        //     const list = await getTemplateOptionsByType(ACT_CONFIG_TYPE.TIME);
        //     updateTimeTemplateList(list || []);
        //     return list || [];
        // } catch (error) {
        //     return Promise.reject(error);
        // }
        dispatch({
            type: 'businessModuleActiveModel/getModuleActTemplateOptions',
            templateType: ACT_CONFIG_TYPE.TIME,
        });
    };

    const timeTemplateOptions = useMemo(() => {
        if (timeTemplateList instanceof Array) {
            return timeTemplateList.map((ele) => {
                return {
                    key: ele.templateId,
                    label: (
                        <div className="text-line" title={ele.templateName}>
                            {ele.templateName}
                        </div>
                    ),
                };
            });
        } else {
            return [];
        }
    }, [timeTemplateList]);

    const initChildList = (item) => {
        const dataType = item.dataType;
        const itemTableIdList = item.tableId.split('-');
        const level = colTypeList.findIndex((ele) => {
            return ele === dataType;
        });
        let list = [];
        const basePid = itemTableIdList;
        for (let index = level + 1; index < colTypeList.length; index++) {
            const element = colTypeList[index];
            basePid.push(0);
            const preItem = list[list.length - 1];
            let newItem = {
                tableId: basePid.join('-'),
                dataType: element,
                selectType: SELECT_TYPES.ALL,
            };
            if (preItem) {
                newItem.pid = preItem.tableId;
                if (preItem.actChannel) {
                    newItem.actChannel = preItem.actChannel;
                }
            } else {
                newItem.pid = item.tableId;
                if (item.actChannel) {
                    newItem.actChannel = item.actChannel;
                }
            }
            list.push(newItem);
        }

        return list;
    };

    const clearTableChildrenById = (tableId) => {
        let table = copyObjectCommon(resultTable);

        const itemIndex = table.findIndex((ele) => {
            return ele.tableId == tableId;
        });
        let findItem = table[itemIndex];
        const newItem = {
            ...findItem,
            selectType: SELECT_TYPES.ALL,
        };
        table[itemIndex] = newItem;

        const newList = table.filter((ele) => {
            // return !(ele.pid && ele.pid.indexOf(newItem.tableId) >= 0) || !ele.pid;
            return !(ele.pid && ele.pid.indexOf(newItem.tableId) === 0);
        });

        const childList = initChildList(newItem);

        const newTable = copyObjectCommon([...newList, ...childList]);
        updateResultTable(newTable);
        //更新表单的值
        changeFormValueEvent(newTable);
    };

    //通过tableId 更新表格里的某一项 并更新整个表格重新渲染
    const updateTableItemById = (tableId, info) => {
        //用于最后一项活动的设置 和 其他配置模板类型开关的选择
        let table = copyObjectCommon(resultTable);

        let itemIndex = table.findIndex((ele) => {
            return ele.tableId == tableId;
        });
        if (itemIndex >= 0) {
            const findItem = table[itemIndex];
            table[itemIndex] = { ...findItem, ...info };
        }

        const newTable = copyObjectCommon(table);
        updateResultTable(newTable);

        //更新表单的值
        changeFormValueEvent(newTable);
    };
    const updateTableItemChildren = (item, childList) => {
        const tableId = item.tableId;

        let table = copyObjectCommon(resultTable);

        const newItem = {
            ...item,
        };

        //获取不是当前item子集的其他数据
        const newList = table.filter((ele) => {
            return !(ele.pid && ele.pid.indexOf(newItem.tableId) === 0);
        });

        let itemChildList = []; //新初始化子集数据

        const itemTableIdList = item.tableId.split('-');

        const level = colTypeList.findIndex((ele) => {
            return ele === newItem.dataType;
        });
        childList.forEach((element, index) => {
            const basePid = [...itemTableIdList];
            basePid.push(index);
            let item = {
                tableId: basePid.join('-'),
                dataType: colTypeList[level + 1],
                selectType: SELECT_TYPES.ALL,
                pid: newItem.tableId,
                templateValue: element,
            };
            if (newItem.actChannel) {
                item.actChannel = newItem.actChannel;
            }
            itemChildList.push(item);

            const childAndChild = initChildList(item);

            itemChildList = [...itemChildList, ...childAndChild];
        });

        const newTable = copyObjectCommon([...newList, ...itemChildList]);

        updateResultTable(newTable);

        //更新表单的值
        changeFormValueEvent(newTable);
    };

    /**
     * 获取模板详情信息
     */
    const getTemplateDetailEvent = async (templateId) => {
        try {
            const { data } = await getActTemplateDetailMngApi({ templateId });

            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    //选择模板
    const selectTemplateEvent = async (templateId, item) => {
        try {
            const data = await getTemplateDetailEvent(templateId);

            const { detailBoList, templateSubType } = data;

            //选中的模板的值是传递给当前item 清空更新对应所有子集的数据

            if (item.dataType === ACT_CONFIG_TYPE.CYCLE) {
                //周期字段需要截取周期类型并插入数组中
                detailBoList.forEach((element) => {
                    element.templateSubType = templateSubType;
                });
            }

            updateTableItemChildren(item, detailBoList);

            return detailBoList;
        } catch (error) {
            console.log(6666, error);
            return Promise.reject(error);
        }
    };

    const itemDropItemSelectEvent = (key, type, itemInfo) => {
        //每列模板选择事件    判断选择模板还是选择创建
        if (key === 'create') {
            //创建模板
            updateCurEditTableItemId(itemInfo.tableId);
            let modalParams = {
                belongType: belongType,
                defaultType: type,
                openParams: {
                    ...itemInfo,
                },
            };

            templateRef?.current?.show(modalParams);
        } else {
            key && selectTemplateEvent(key, itemInfo);
        }
    };

    const renderItemFormItemByType = (type, itemInfo) => {
        let radioOptions = [];
        let selectDefaultText = '';

        let disableRadio = disabled;

        if (type === ACT_CONFIG_TYPE.CROWD && channelList) {
            //百度高德不让改人群
            const itemActChannelList = itemInfo?.actChannel?.split(',') || [];
            const formatItemChannelList = itemActChannelList.filter((ele) => ele);
            const hasDisabled = formatItemChannelList.some((ele) => AllChannelList.includes(ele));
            disableRadio = hasDisabled || disabled;
        }

        const dropItemDom = (menuDom) => {
            return (
                <Fragment>
                    <Dropdown
                        zIndex={5}
                        overlay={menuDom}
                        getPopupContainer={() => {
                            return document.getElementsByClassName(
                                updateStyles['actModule-table'],
                            )[0];
                        }}
                        disabled={disableRadio}
                        destroyPopupOnHide
                    >
                        <Space>
                            选择模板
                            <DownOutlined />
                        </Space>
                    </Dropdown>
                </Fragment>
            );
        };

        let createItem = {
            key: 'create',
            label: <span className={commonStyles['table-btn']}>创建</span>,
        };
        if (disabled || readonly) {
            //禁用创建按钮
            createItem.disabled = true;
        }

        const menuStyle = {
            width: '200px',
            maxHeight: '300px',
            overflowY: 'auto',
        };

        const selectModal = (
            <ActTemplateSelectModal
                ref={(newRef) => {
                    if (selectTemplateRefs?.current) {
                        selectTemplateRefs.current[itemInfo?.tableId] = newRef;
                    }
                }}
                {...props}
                currentUser={currentUser}
                templateType={type}
                otherParams={{
                    templateType: type,
                    operType: (currentUser?.operId?.length && '01') || '02',
                }}
                onSelectEvent={(templateId) => {
                    updateTableItemById(itemInfo?.tableId, { selectType: SELECT_TYPES.TEMP });

                    itemInfo.selectType = SELECT_TYPES.TEMP;
                    itemDropItemSelectEvent(templateId, type, itemInfo);
                }}
                onCreateEvent={() => {
                    updateTableItemById(itemInfo?.tableId, { selectType: SELECT_TYPES.TEMP });
                    itemDropItemSelectEvent('create', type, itemInfo);
                }}
                disabled={disableRadio}
            ></ActTemplateSelectModal>
        );

        if (type === ACT_CONFIG_TYPE.CROWD) {
            // selectOptions = crwodTemplateOptions;

            const dropMenuDom = (
                <Menu
                    items={[createItem, ...crwodTemplateOptions]}
                    onClick={(event) => {
                        const { key } = event;
                        itemDropItemSelectEvent(key, type, itemInfo);
                    }}
                    style={{ ...menuStyle }}
                ></Menu>
            );

            radioOptions = [
                <Radio key={SELECT_TYPES.ALL} value={SELECT_TYPES.ALL}>
                    全部
                </Radio>,
                <Radio key={SELECT_TYPES.TEMP} value={SELECT_TYPES.TEMP}>
                    <Space>
                        {selectModal}
                        {/* {itemInfo.selectType == SELECT_TYPES.TEMP
                            ? dropItemDom(dropMenuDom)
                            : '选择模板'} */}
                    </Space>
                </Radio>,
            ];
            selectDefaultText = '全部';
        } else if (type === ACT_CONFIG_TYPE.CYCLE) {
            // selectOptions = cycleTemplateOptions;
            const dropMenuDom = (
                <Menu
                    items={[createItem, ...cycleTemplateOptions]}
                    onClick={(event) => {
                        const { key } = event;
                        itemDropItemSelectEvent(key, type, itemInfo);
                    }}
                    style={{ ...menuStyle }}
                ></Menu>
            );

            radioOptions = [
                <Radio key={SELECT_TYPES.ALL} value={SELECT_TYPES.ALL}>
                    每天
                </Radio>,
                <Radio key={SELECT_TYPES.TEMP} value={SELECT_TYPES.TEMP}>
                    <Space>
                        {selectModal}
                        {/* {itemInfo.selectType == SELECT_TYPES.TEMP
                            ? dropItemDom(dropMenuDom)
                            : '选择模板'} */}
                    </Space>
                </Radio>,
            ];
            selectDefaultText = '每天';
        } else if (type === ACT_CONFIG_TYPE.TIME) {
            // selectOptions = timeTemplateOptions;
            const dropMenuDom = (
                <Menu
                    items={[createItem, ...timeTemplateOptions]}
                    onClick={(event) => {
                        const { key } = event;
                        itemDropItemSelectEvent(key, type, itemInfo);
                    }}
                    style={{ ...menuStyle }}
                ></Menu>
            );

            radioOptions = [
                <Radio key={SELECT_TYPES.ALL} value={SELECT_TYPES.ALL}>
                    全天
                </Radio>,
                <Radio key={SELECT_TYPES.TEMP} value={SELECT_TYPES.TEMP}>
                    <Space>
                        {selectModal}
                        {/* {itemInfo.selectType == SELECT_TYPES.TEMP
                            ? dropItemDom(dropMenuDom)
                            : '选择模板'} */}
                    </Space>
                </Radio>,
            ];
            selectDefaultText = '全天';
        } else if (type === ACT_CONFIG_TYPE.DISCOUNT) {
            const discountStyles = {
                'word-break': 'break-all',
                'white-space': 'pre-wrap',
            };
            const actTypeName = formatTemplateTypeText(itemInfo?.discountValue?.templateActType);

            let priceTypeName = '';
            switch (itemInfo?.discountValue?.adjustPriceType) {
                case ADJUST_PRICE_TYPES.DEFAULT:
                    priceTypeName = '默认';
                    break;
                case ADJUST_PRICE_TYPES.OPER_ORIGIN:
                    priceTypeName = '原价';
                    break;
                case ADJUST_PRICE_TYPES.PUSH:
                    priceTypeName = '推送结算价';
                    break;
                case ADJUST_PRICE_TYPES.SETTLE:
                    priceTypeName = '配置结算价';
                    break;

                default:
                    break;
            }

            if (
                itemInfo?.discountValue?.templateActType === MODELTYPS.REDUCTION &&
                itemInfo?.discountValue?.subChargeFeeFlag === '1'
            ) {
                priceTypeName = '减电费';
            }

            const priceTypeNameDom = priceTypeName && `(${priceTypeName})`;

            const actTypeDom =
                (actTypeName && (
                    <span>
                        营销方式：{actTypeName} {priceTypeNameDom}
                        {'\n'}
                    </span>
                )) ||
                '';
            return (
                <Space>
                    <span style={{ ...discountStyles }}>
                        {actTypeDom}
                        {itemInfo?.discountValue?.templateDetail}
                    </span>
                    {!readonly && (
                        <span
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                updateCurEditTableItemId(itemInfo.tableId);
                                let actParams = {};

                                if (
                                    itemInfo.discountValue &&
                                    itemInfo.discountValue.templateActType
                                ) {
                                    actParams = {
                                        ...itemInfo.discountValue,
                                    };

                                    if (actParams.templateActType === MODELTYPS.REDUCTION) {
                                        actParams.subChargeFeeFlag =
                                            itemInfo.discountValue.subChargeFeeFlag === '1'
                                                ? true
                                                : false;
                                    }
                                } else if (tempSaveInfo) {
                                    //有暂存配置的信息直接带入
                                    actParams = tempSaveInfo;
                                }
                                priceRef.current.show({
                                    belongType: belongType,
                                    actParams: actParams,
                                    menuType,
                                    openParams: {
                                        ...itemInfo,
                                    },
                                });
                            }}
                        >
                            设置
                        </span>
                    )}
                </Space>
            );
        }

        if (readonly) {
            if (type !== ACT_CONFIG_TYPE.DISCOUNT) {
                return itemInfo.selectType === SELECT_TYPES.ALL ? selectDefaultText : '已选配置';
            } else {
                return '';
                // return JSON.stringify(itemInfo);
            }
        }

        return (
            <Fragment>
                <Radio.Group
                    value={itemInfo.selectType}
                    disabled={disableRadio}
                    onChange={(event) => {
                        const {
                            target: { value },
                        } = event;
                        let info = {
                            selectType: value,
                        };
                        if (value === SELECT_TYPES.ALL) {
                            //选中all 就清空当前对象和子对象
                            clearTableChildrenById(itemInfo.tableId);
                        } else {
                            //修改对应值
                            updateTableItemById(itemInfo.tableId, info);
                        }
                    }}
                >
                    <Space direction="vertical">{radioOptions}</Space>
                </Radio.Group>
            </Fragment>
        );
    };

    const formatRuleValueText = (type, ruleValue) => {
        let key = '';
        switch (type) {
            case ACT_CONFIG_TYPE.CROWD:
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                key = '活动周期';
                break;
            case ACT_CONFIG_TYPE.TIME:
                key = '活动时段';
                break;
            case ACT_CONFIG_TYPE.DISCOUNT:
                key = '活动优惠';
                break;

            default:
                break;
        }
        return key;
    };

    const formatTemplateTypeText = (type) => {
        let name = '';
        switch (type) {
            case MODELTYPS.DISCOUNT:
                name = '打折';
                break;
            case MODELTYPS.CROWDPLLICY_DISCOUNT:
                name = '打折';
                break;
            case MODELTYPS.REDUCTION:
                name = '立减';
                break;
            case MODELTYPS.CROWDPLLICY_REDUCTION:
                name = '立减';
                break;
            case MODELTYPS.DYNAMIC:
                name = '动态调价';
                break;

            default:
                break;
        }
        return name;
    };

    //渲染每一列
    const renderItemByType = (type, itemInfo) => {
        let titleStyle = {
            position: 'absolute',
            transform: 'translateX(-100%)',
            left: '-20px',
            padding: '5px 20px',
            borderRadius: '20px',
            backgroundColor: '#f8f8f8',
        };

        let spanStyle = {
            maxWidth: '200px',
        };

        let channelStyle = {
            marginRight: '20px',
        };

        let itemPreDom = null;

        if (
            itemInfo.templateValue &&
            !isEmpty(itemInfo.templateValue) &&
            itemInfo.templateValue.ruleValue
        ) {
            //父级配置数据值
            const { ruleValueName, ruleValue } = itemInfo.templateValue;

            const ruleText = ruleValueName; // || JSON.stringify(itemInfo.templateValue);

            itemPreDom = (
                <Col flex={'0 0 auto'} style={{ ...titleStyle }}>
                    <span
                        className={classnames(
                            commonStyles['text-clamp-2'],
                            updateStyles['table-pre-title'],
                        )}
                        style={{ maxWidth: `${templateMaxWidth}px` }}
                        title={ruleText}
                    >
                        {ruleText}
                    </span>
                </Col>
            );
        }

        let itemChannelDom = null;

        if (itemInfo.actChannel && !itemInfo.pid && channelList) {
            let channelName = '';
            if (itemInfo.actChannel) {
                const itemActChannelList = itemInfo?.actChannel?.split(',') || [];
                const formatItemChannelList = itemActChannelList.filter((ele) => ele);
                if (formatItemChannelList?.length > 1) {
                    channelName = formatItemChannelList
                        .map((ele) => getChannelName(ele))
                        .filter((ele) => ele)
                        ?.join(',');
                } else {
                    channelName = getChannelName(itemInfo.actChannel);
                }
            }

            itemChannelDom = (
                <Col style={{ ...channelStyle }} flex={'0 0 auto'}>
                    {channelName}
                </Col>
            );
        }

        return (
            <Row>
                {itemPreDom}
                {itemChannelDom}
                <Col flex={'1'}>{renderItemFormItemByType(type, itemInfo)}</Col>
            </Row>
        );
    };
    const getChannelName = (actChannel) => {
        let channelName = '';
        switch (actChannel) {
            case '11':
                channelName = '高德';
                break;
            case '15':
                channelName = '百度';
                break;

            default:
                break;
        }
        return channelName;
    };

    const channelItemChildListKey = (dataType) => {
        let key = '';
        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'cycleList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'periodsList';
                }
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'periodsList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'activeCrowdList';
                }

                break;
            case ACT_CONFIG_TYPE.TIME:
                key = 'discountList';

                break;
            default:
                break;
        }
        return key;
    };

    const formatChannelItemToTableEvent = (channelItem, preLevelIdList) => {
        const dataType = channelItem.dataType;
        const itemTableIdList = channelItem?.tableId?.split('-') || preLevelIdList;

        const level = colTypeList.findIndex((ele) => {
            return ele === dataType;
        });

        let list = [];

        const tableId = itemTableIdList.join('-');

        let parentIds = itemTableIdList.slice(0, -1);
        let newItem = {
            tableId: tableId,
            pid: parentIds.join('-'),
            dataType: colTypeList[level + 1] || dataType,
            actChannel: channelItem.actChannel,
        };
        let templateValue = {};

        const childListKey = channelItemChildListKey(channelItem.dataType);
        const nextChildList = channelItem[childListKey];

        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                templateValue.ruleValue = channelItem.activeCrowd;
                templateValue.ruleValueName = channelItem.activeCrowdName;

                newItem.selectType =
                    channelItem.activeCrowdFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;

                break;
            case ACT_CONFIG_TYPE.CYCLE:
                templateValue.templateSubType = channelItem.cycleType;
                templateValue.ruleValue = channelItem.cycleValue;
                templateValue.ruleValueName = channelItem.cycleValueName;
                newItem.selectType =
                    channelItem.cycleValueFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;

                break;
            case ACT_CONFIG_TYPE.TIME:
                templateValue.ruleValue = channelItem.timePeriods;
                templateValue.ruleValueName = channelItem.timePeriods;

                if (channelItem.discountList && channelItem.discountList[0]) {
                    let discountItemInfo = channelItem.discountList[0];

                    let discountValue = {
                        actId: discountItemInfo.actId || '',
                        templateActType: discountItemInfo.actType,
                        dctValue: discountItemInfo.dctValue,

                        vipDiscountValue: discountItemInfo.vipDiscountValue,

                        adjustPriceType: discountItemInfo.adjustPriceType,
                        adjustDetailList: discountItemInfo.adjustDetailList,
                        templateDetail: discountItemInfo.discountDesc,
                        subChargeFeeFlag: discountItemInfo.subChargeFeeFlag,
                    };
                    newItem.discountValue = discountValue;
                }

                newItem.selectType =
                    channelItem.allDayFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                break;

            default:
                break;
        }

        newItem.templateValue = templateValue;

        if (nextChildList instanceof Array && nextChildList.length > 0) {
            switch (nextChildList[0].dataType) {
                case ACT_CONFIG_TYPE.CROWD:
                    newItem.selectType =
                        nextChildList[0].activeCrowdFlag === '1'
                            ? SELECT_TYPES.ALL
                            : SELECT_TYPES.TEMP;

                    break;
                case ACT_CONFIG_TYPE.CYCLE:
                    newItem.selectType =
                        nextChildList[0].cycleValueFlag === '1'
                            ? SELECT_TYPES.ALL
                            : SELECT_TYPES.TEMP;

                    break;
                case ACT_CONFIG_TYPE.TIME:
                    newItem.selectType =
                        nextChildList[0].allDayFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                    break;

                default:
                    break;
            }
        }

        list.push(newItem);

        if (
            dataType != ACT_CONFIG_TYPE.TIME &&
            nextChildList instanceof Array &&
            nextChildList.length > 0
        ) {
            list = [...list, ...formatChannelListToTableEvent(nextChildList, tableId)];
        }

        return list;
    };

    //格式化层不同渠道的一位数组
    const formatChannelListToTableEvent = (channelList, pid) => {
        let list = [];

        channelList.forEach((element, elementIndex) => {
            //初始化首条数据

            let preLevelIdList = [...String(pid).split('-'), elementIndex];

            // 将子集生成出来塞进数组
            list = [...list, ...formatChannelItemToTableEvent(element, preLevelIdList)];
        });

        return list;
    };

    /**
     * 表单树结构数据格式化层一维表格数据
     */
    const formatFormToTableEvent = (originList) => {
        let list = [];

        let originChannelList = [];

        originList.map((originItem) => {
            if (originChannelList.length == 0) {
                originChannelList.push({ actChannel: originItem.actChannel, list: [originItem] });
            } else {
                let res = originChannelList.some((item) => {
                    if (item.actChannel === originItem.actChannel) {
                        item.list.push(originItem);
                        return true;
                    }
                });
                if (!res) {
                    originChannelList.push({
                        actChannel: originItem.actChannel,
                        list: [originItem],
                    });
                }
            }
        });

        const formatOriginList = originChannelList.map((ele) => {
            return ele.list;
        });

        formatOriginList.forEach((channelList, index) => {
            let selectType = '';

            const element = channelList[0];
            if (element) {
                switch (element.dataType) {
                    case ACT_CONFIG_TYPE.CROWD:
                        selectType =
                            element.activeCrowdFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;

                        break;
                    case ACT_CONFIG_TYPE.CYCLE:
                        selectType =
                            element.cycleValueFlag === '1' ? SELECT_TYPES.ALL : SELECT_TYPES.TEMP;
                        break;

                    default:
                        break;
                }

                list.push({
                    dataType: firstType,
                    tableId: `${index}`,
                    selectType: selectType,
                    actChannel: element.actChannel,
                });
            }

            list = [...list, ...formatChannelListToTableEvent(channelList, index)];
        });

        // console.log(7777, formatOriginList, list);

        return list;
    };
    /**
     * 获取子集的数据
     */
    const findNexLevelChildrenList = (parentItem, list) => {
        const newList = list.filter((ele) => {
            return ele.pid && ele.pid === parentItem.tableId;
        });
        return newList;
    };
    /**
     * 寻找过滤相同渠道的数据
     */
    const findAllChildrenList = (parentItem, list) => {
        const newList = list.filter((ele) => {
            return (
                (ele.pid && ele.pid.indexOf(parentItem.tableId) >= 0) ||
                ele.tableId === parentItem.tableId
            );
        });
        return newList;
    };

    const findAllChannelList = (list) => {
        //格式化数据为不同渠道对应一个数组 [[平台渠道],[高德],[百度]]
        let newList = [];

        for (const item of list) {
            let childList = [];
            if (!item.pid) {
                childList = findAllChildrenList(item, list);
                newList.push(childList);
            }
        }
        return newList;
    };

    const fomatItemFormVlue = (item, nextList, allList) => {
        const childListKey = itemChildListKey(item.dataType);

        //格式化sn 使用数组下标

        let formatDdjustDetailList = item.discountValue?.adjustDetailList?.map((ele, eleIndex) => {
            return {
                ...ele,
                adjustSn: eleIndex,
            };
        });

        if (item.dataType === ACT_CONFIG_TYPE.DISCOUNT) {
            let resultItem = {
                actChannel: item.actChannel,
                dataType: item.dataType,
            };

            //格式化sn 使用数组下标

            let formatDdjustDetailList = item.discountValue?.adjustDetailList?.map(
                (ele, eleIndex) => {
                    return {
                        ...ele,
                        adjustSn: eleIndex,
                    };
                },
            );

            let discountItemValue = {
                actId: item.discountValue?.actId || '',
                actType: item.discountValue?.templateActType,
                dctValue: item.discountValue?.dctValue,
                vipDiscountValue: item.discountValue?.vipDiscountValue,
                adjustPriceType: item.discountValue?.adjustPriceType,
                adjustDetailList: formatDdjustDetailList,
                subChargeFeeFlag: item.discountValue?.subChargeFeeFlag,

                discountDesc: item.discountValue?.templateDetail,
                timePeriods: item.templateValue?.ruleValue || '',
            };

            resultItem = { ...resultItem, ...discountItemValue };
            return [resultItem];
        }

        const formatNextList = nextList.map((ele) => {
            let resultItem = {
                actChannel: item.actChannel,
                dataType: item.dataType,
            };

            switch (item.dataType) {
                case ACT_CONFIG_TYPE.CROWD:
                    resultItem.activeCrowd = ele?.templateValue?.ruleValue;
                    resultItem.activeCrowdName = ele?.templateValue?.ruleValueName;
                    resultItem.activeCrowdFlag = item.selectType === SELECT_TYPES.ALL ? '1' : '0';
                    break;
                case ACT_CONFIG_TYPE.CYCLE:
                    resultItem.cycleType = ele?.templateValue?.templateSubType;
                    resultItem.cycleValue = ele?.templateValue?.ruleValue;
                    resultItem.cycleValueName = ele?.templateValue?.ruleValueName;
                    resultItem.cycleValueFlag = item.selectType === SELECT_TYPES.ALL ? '1' : '0';
                    break;
                case ACT_CONFIG_TYPE.TIME:
                    resultItem.timePeriods = ele?.templateValue?.ruleValue;
                    resultItem.timePeriods = ele?.templateValue?.ruleValueName;
                    resultItem.allDayFlag = item.selectType === SELECT_TYPES.ALL ? '1' : '0';

                    break;

                default:
                    break;
            }
            if (childListKey) {
                const childNextList = findNexLevelChildrenList(ele, allList);
                resultItem[childListKey] = fomatItemFormVlue(ele, childNextList, allList);
            }
            return resultItem;
        });

        return formatNextList;
    };

    const itemChildListKey = (dataType) => {
        let key = '';
        switch (dataType) {
            case ACT_CONFIG_TYPE.CROWD:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'cycleList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'periodsList';
                }
                break;
            case ACT_CONFIG_TYPE.CYCLE:
                if (sortType === CONFIG_MODEL.CROWD) {
                    key = 'periodsList';
                } else if (sortType === CONFIG_MODEL.CYCLE) {
                    key = 'activeCrowdList';
                }

                break;
            case ACT_CONFIG_TYPE.TIME:
                key = 'discountList';
                break;

            default:
                break;
        }
        return key;
    };

    const formatParentFormValue = (parentItem, allChildrenList) => {
        const templateValueList = findNexLevelChildrenList(parentItem, allChildrenList);
        const formatList = fomatItemFormVlue(parentItem, templateValueList, allChildrenList);

        // console.log(44455, formatList);
        return formatList;
    };

    const formatChannelListEvent = (list) => {
        let parentList = list.filter((ele) => !ele.pid);

        let resultList = [];

        parentList.forEach((parentItem) => {
            // const dataType = parentItem.dataType;
            const parentAllChildrenList = findAllChildrenList(parentItem, list);
            resultList = [
                ...resultList,
                ...formatParentFormValue(parentItem, parentAllChildrenList),
            ];
        });
        return resultList;
    };

    const changeFormValueEvent = (tableValue) => {
        const formValue = formatTableToFormEvent(tableValue);

        onChange && onChange(formValue);
    };

    /**
     * 将表格数据格式化层表单树结构
     */
    const formatTableToFormEvent = (originList) => {
        let list = [];

        // console.log(7777888, originList, value);

        const channelTableInfo = findAllChannelList(originList);

        if (channelTableInfo instanceof Array) {
            channelTableInfo.forEach((channelTableList) => {
                list = [...list, ...formatChannelListEvent(channelTableList)];
            });
        }

        // console.log(7777999, list);

        return list;
    };

    const initEmtpyList = useCallback(
        (tableId, actChannel) => {
            let list = [];
            const basePid = [];
            for (let index = 0; index < colTypeList.length; index++) {
                const element = colTypeList[index];
                if (index == 0) {
                    basePid.push(tableId);
                } else {
                    basePid.push(0);
                }
                const preItem = list[index - 1];
                let item = {
                    tableId: basePid.join('-'),
                    dataType: element,
                    selectType: SELECT_TYPES.ALL,
                };
                if (preItem) {
                    item.pid = preItem.tableId;
                }
                if (actChannel) {
                    item.actChannel = actChannel;
                }
                list.push(item);
            }

            return list;
        },
        [sortType, colTypeList],
    );

    //格式化表格
    const formTableList = (originList) => {
        let list = [];

        // console.log(464646464, originList);

        // ******************************远端数据初始格式化为可用格式的数据************************************

        if (originList) {
            //父级值更新后 重新渲染表格
            let formatOriginList = null;
            if (typeof originList === 'string') {
                formatOriginList = JSON.parse(originList);
            } else if (originList instanceof Array) {
                formatOriginList = originList;
            }

            const newList = formatFormToTableEvent(formatOriginList);

            // console.log(545454, formatOriginList, JSON.stringify(formatOriginList));
            // console.log(646464, newList, JSON.stringify(newList));

            list = newList;

            let maxId = 0;
            newList.forEach((element) => {
                if (!element.pid) {
                    if (Number(element.tableId) > maxId) {
                        maxId = element.tableId;
                    }
                }
            });

            //如果渠道变更  如果有新选的 初始化新的数组插入
            if (channelList instanceof Array && channelList.length > 0) {
                channelList.forEach((item, index) => {
                    const hasChannel = list.some((ele) => ele.actChannel === item);

                    if (!hasChannel) {
                        list = [...list, ...initEmtpyList(String(maxId + index + 1), item)];
                    }
                });
                //
            }
        }

        // ******************************如果远端没有数据，格式化为默认空数据格式************************************

        if (list?.length === 0) {
            if (channelList instanceof Array && channelList.length > 0) {
                channelList.forEach((item, index) => {
                    list = [...list, ...initEmtpyList(index, item)];
                });
                //
            } else {
                list = initEmtpyList(0);
            }
        }

        return list;
    };

    const tableItemChildRender = (level, list, pid) => {
        //查看当前列的数据类型
        const levelDataType = colTypeList[level];

        //过滤出当前列的数组
        const levelList = resultTable.filter((ele) => {
            if (pid && ele.pid != pid) {
                return false;
            }
            return ele.dataType === levelDataType;
        });

        if (levelList instanceof Array) {
            return levelList.map((ele, index) => {
                return tableItemRender(level, ele, index === levelList.length - 1);
            });
        } else {
            return [];
        }
    };

    const tableItemRender = (level, item, noBorder) => {
        const itemWidth = MAX_SPAN / (MAX_LEVEL - level);
        const otherWidth = MAX_SPAN - itemWidth;

        const list = resultTable.filter((ele) => {
            return ele.dataType === item.dataType;
        });

        const childList = resultTable.filter((ele) => {
            return ele.pid === item.tableId;
        });

        let styles = {
            // padding: '10px',
            borderTop: '1px solid #efefef',
            borderLeft: '1px solid #efefef',
            borderBottom: '1px solid #efefef',
            borderRight: '1px solid #efefef',
        };
        if (level != 0) {
            styles.borderTop = 'none';
            styles.borderRight = 'none';
        }
        if (level == MAX_LEVEL - 1) {
            // styles.border = 'none';
            // styles.borderLeft = 'none';
            // styles.borderRight = 'none';
        }
        if (level != 0 && noBorder) {
            styles.borderBottom = 'none';
        }

        let colStyles = { padding: '15px' };
        return (
            <Fragment>
                <Row style={{ ...styles }} key={item.tableId}>
                    <Col style={{ ...colStyles }} span={itemWidth}>
                        {renderItemByType(item.dataType, item)}
                    </Col>
                    {otherWidth > 0 && (
                        <Col span={otherWidth}>
                            {tableItemChildRender(level + 1, childList, item.tableId)}
                        </Col>
                    )}
                </Row>
                {/* <br></br> */}
            </Fragment>
        );
    };

    //表格渲染
    const tableRender = useMemo(() => {
        let tableDom = [];
        tableDom = tableItemChildRender(0, resultTable);

        const headWidth = MAX_SPAN / colTypeList.length;

        let styles = {
            padding: '10px',
        };
        let tableHeadDom = (
            <Row style={{ backgroundColor: '#efefef' }}>
                {colTypeList.map((ele) => {
                    return (
                        <Col style={styles} key="ele" span={headWidth}>
                            <h3>{getColNameByType(ele)}</h3>
                        </Col>
                    );
                })}
            </Row>
        );

        return (
            <div className={updateStyles['actModule-table']}>
                <div className={updateStyles['actModule-table-view']}>
                    {(channelList && (
                        <Divider orientationMargin="0" orientation="left">
                            <h3>图商渠道</h3>
                        </Divider>
                    )) || (
                        <Divider orientationMargin="0" orientation="left">
                            <h3>平台渠道</h3>
                        </Divider>
                    )}

                    {tableHeadDom}
                    {tableDom}
                </div>
                <ActivePriceModal
                    {...props}
                    initRef={priceRef}
                    onFinish={(params, openParams) => {
                        const discountValue = {
                            ...params,
                        };
                        if (openParams?.discountValue?.actId) {
                            discountValue.actId = openParams.discountValue.actId;
                        }
                        if (params.templateActType === MODELTYPS.REDUCTION) {
                            discountValue.subChargeFeeFlag = params.subChargeFeeFlag ? '1' : '0';
                        }
                        updateTableItemById(openParams.tableId, {
                            discountValue: discountValue,
                        });

                        const copyParams = copyObjectCommon(params);
                        delete copyParams.dctValue;
                        delete copyParams.vipDiscountValue;

                        if (copyParams.adjustDetailList instanceof Array) {
                            copyParams.adjustDetailList.forEach((element) => {
                                delete element.adjustValue;
                                delete element.adjustVipValue;
                            });
                        }

                        //保存模板信息
                        updateTempSaveInfo(copyParams);
                    }}
                />
                <ModuleEditModal
                    {...props}
                    zIndex={124}
                    initRef={templateRef}
                    onFinish={(params, openParams) => {
                        const { id } = params;
                        selectTemplateEvent(id, openParams);

                        //更新对应模板选项
                        updateTemplateOptionsByType(openParams.dataType);

                        if (!isEmpty(selectTemplateRefs?.current)) {
                            //关闭模板选择弹窗
                            for (const key in selectTemplateRefs?.current) {
                                if (Object.hasOwnProperty.call(selectTemplateRefs?.current, key)) {
                                    const element = selectTemplateRefs?.current[key];
                                    element?.closeModal();
                                }
                            }
                        }
                    }}
                ></ModuleEditModal>
            </div>
        );
    });

    return tableRender;
};
export default ActModuleTable;
