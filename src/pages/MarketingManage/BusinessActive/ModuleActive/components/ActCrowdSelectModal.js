import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState';
import { Modal } from 'antd';
import { useImperativeHandle, useState } from 'react';

export const ActCrowdSelectModal = (props) => {
    const { initRef, callback } = props;

    const [visible, updateVisible] = useState(false);
    const [selectedRows, updateSelectRows] = useState([]);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [listLoading, updateListLoading] = useState(false);
    const [crowdCalculateList, updateCrowdCalculateList] = useState([]);
    const [crowdCalculateTotal, updateCrowdCalculateTotal] = useState(0);
    useImperativeHandle(initRef, () => ({
        show: (type, selectedItems = []) => {
            updateVisible(true);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const onConfirm = () => {};

    const columns = [
        {
            title: '人群',
            dataIndex: 'cdpCrowdName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '说明',
            dataIndex: 'cdpCrowdName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const rowSelection = {
        type: 'checkbox',
        checkStrictly: false,
        selectedRowKeys: selectedRows.map((item) => item['']),
        onChange: (selectedRowKeys, selectedRows) => {
            for (let index = selectedRows.length - 1; index >= 0; index--) {
                const element = selectedRows[index];
                if (element.children) {
                    selectedRows.splice(index, 1);
                }
            }
            updateSelectRows(selectedRows);
        },
        getCheckboxProps: (record) => ({
            name: record[''],
        }),
    };

    return (
        <Modal title="用户人群" visible={visible} onCancel={onClose} width={880} onOk={onConfirm}>
            <TablePro
                name={`_list`}
                rowSelection={rowSelection}
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={''}
                dataSource={crowdCalculateList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: crowdCalculateTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Modal>
    );
};
