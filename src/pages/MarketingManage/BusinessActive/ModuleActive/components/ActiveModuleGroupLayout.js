import { Form } from 'antd';

import ActiveCrowdCheckTree from './ModuleCrowdCheckTree';
import { ActiveModuleListLayout } from './ActCommonLayout';
import { queryCrowdTagExistList } from '@/services/Marketing/ActiveCrowdApi';
import { CROWD_STATUS } from '@/constants/crowd';

const FormItem = Form.Item;

const ActiveModuleGroupLayout = (props) => {
    const { form, name = 'list', formItemLayout, multiple } = props;
    return (
        <Form.Item
            label="模板内容"
            {...formItemLayout}
            name={name}
            rules={[
                {
                    required: true,
                    message: '请选择',
                },
            ]}
        >
            <ActiveModuleListLayout
                name={name}
                multiple={multiple}
                ruleContent={(index) => {
                    const list = form.getFieldValue(name) || [];
                    const disableIds = [];
                    for (let listIndex = 0; listIndex < list.length; listIndex++) {
                        const element = list[listIndex];
                        if (index != listIndex) {
                            disableIds.push(...(element?.activeCrowd || []));
                        }
                    }

                    return (
                        <FormItem
                            name={[index, 'activeCrowd']}
                            rules={[
                                { required: true, message: '请配置人群' },
                                (_) => ({
                                    async validator(rule, value) {
                                        if (value) {
                                            const verifyResult = await queryCrowdTagExistList({
                                                crowdId: value?.join(','),
                                            });
                                            const disableCrowds =
                                                verifyResult?.data?.filter(
                                                    (v) => v?.status === CROWD_STATUS.DISABLE,
                                                ) ?? [];
                                            if (
                                                Array.isArray(value) &&
                                                value.length !== verifyResult?.data?.length
                                            ) {
                                                return Promise.reject(
                                                    '存在未配置人群，请在活动人群配置中配置该人群',
                                                );
                                            }
                                            if (verifyResult && disableCrowds?.length > 0) {
                                                return Promise.reject(
                                                    `${disableCrowds
                                                        ?.map((v) => v.crowdName)
                                                        ?.join('，')}人群已被禁用，请重新选择`,
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            noStyle
                        >
                            <ActiveCrowdCheckTree disableIds={disableIds} />
                        </FormItem>
                    );
                }}
            />
        </Form.Item>
    );
};

export default ActiveModuleGroupLayout;
