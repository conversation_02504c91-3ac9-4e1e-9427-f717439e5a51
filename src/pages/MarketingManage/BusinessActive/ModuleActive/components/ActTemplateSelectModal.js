import {
    useImperativeHandle,
    forwardRef,
    useEffect,
    useState,
    Fragment,
    useMemo,
    useRef,
} from 'react';
import { Modal, Card, Form, Input, Col, Space, Popconfirm, message, Button } from 'antd';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import {
    getModuleActTemplateListApi,
    deleteModuleActTemplateApi,
    getActTemplateListMngApi,
    deleteModuleActTemplateMngApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';

import { ACT_CONFIG_TYPE } from './ActModuleConfig';
import styles from '@/assets/styles/common.less';
import ModuleEditModal from './ModuleEditModal';
import { queryCrowdTagExistList } from '@/services/Marketing/ActiveCrowdApi';
import { CROWD_STATUS } from '@/constants/crowd';

const FormItem = Form.Item;

const ActTemplateSelectCard = (props) => {
    const {
        currentUser,
        templateType,
        otherParams,
        onCreateEvent, //创建回调
        onEditEvent, //编辑回调
        onSelectEvent, //选择回调
        onSelectHandler,
        zIndex = 10,
    } = props;

    const [form] = Form.useForm();

    const templateRef = useRef();

    const [tableList, updateTableList] = useState([]);
    const [tableListTotal, updateTableListTotal] = useState(0);
    const [listLoading, updateListLoading] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = form.getFieldsValue();
            const params = {
                ...data,
                ...otherParams,
                templateType: templateType,
                pageIndex: pageInfo.pageIndex,
                totalNum: pageInfo.pageSize,
            };
            updateListLoading(true);
            const {
                data: { records: moduleTemplateList, total: moduleTemplateTotal },
            } = await getActTemplateListMngApi(params);
            updateTableList(moduleTemplateList);
            updateTableListTotal(moduleTemplateTotal);
            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const selectRecord = async (record) => {
        try {
            if (templateType === '01') {
                const verifyResult = await queryCrowdTagExistList({
                    crowdId: record?.crowdIdList?.join(','),
                });
                if (
                    Array.isArray(record?.crowdIdList) &&
                    record?.crowdIdList.length !== verifyResult?.data?.length
                ) {
                    message.error('存在未配置人群，请在活动人群配置中配置该人群');
                } else if (
                    verifyResult &&
                    verifyResult?.data?.filter((v) => v?.status === CROWD_STATUS.DISABLE)?.length >
                        0
                ) {
                    const canModify =
                        currentUser?.name == 'SYSADMIN' || currentUser?.name == record.createdBy;
                    Modal.confirm({
                        title: '无法选择',
                        content: '该模板存在已禁用人群，无法使用，请重新编辑',
                        type: 'warning',
                        onOk: () => {
                            if (canModify) {
                                templateRef?.current?.show({ id: record.templateId });
                            }
                        },
                        okText: canModify ? '去编辑' : '确定',
                    });
                } else {
                    onSelectEvent && onSelectEvent(record?.templateId);
                    onSelectHandler && onSelectHandler(record?.templateId);
                }
            } else {
                onSelectEvent && onSelectEvent(record?.templateId);
                onSelectHandler && onSelectHandler(record?.templateId);
            }
        } catch (error) {
            message.error('处理人群信息失败');
        }
    };

    const editRecord = (id) => {
        onEditEvent && onEditEvent(id);
    };

    const columns = useMemo(() => {
        let otherCol = [];
        if (templateType === ACT_CONFIG_TYPE.CYCLE) {
            otherCol.push({
                title: '周期类型',
                width: 120,
                dataIndex: 'templateSubTypeName',
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            });
        }
        let list = [
            {
                title: '模板名称',
                width: 200,
                dataIndex: 'templateName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            ...otherCol,
            {
                title: '模板内容',
                width: 240,
                dataIndex: 'templateDetail',
                render(text, record) {
                    return (
                        <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                            {text || '-'}
                        </span>
                    );
                },
            },
            {
                title: '操作',
                width: 160,
                fixed: 'right',
                render: (text, record) => (
                    <Fragment>
                        <Space>
                            <span
                                className={styles['table-btn']}
                                onClick={() => {
                                    selectRecord(record);
                                }}
                            >
                                选择
                            </span>
                            {((currentUser?.name == 'SYSADMIN' ||
                                currentUser?.name == record.createdBy) && (
                                <span
                                    className={styles['table-btn']}
                                    onClick={() => {
                                        templateRef?.current?.show({ id: record.templateId });
                                    }}
                                >
                                    编辑
                                </span>
                            )) ||
                                null}
                            {((currentUser?.name == 'SYSADMIN' ||
                                currentUser?.name == record.createdBy) && (
                                <Popconfirm
                                    title="确定删除吗？"
                                    onConfirm={async () => {
                                        try {
                                            await deleteModuleActTemplateMngApi({
                                                templateId: record.templateId,
                                            });
                                            message.success('操作成功');
                                            searchData();
                                        } catch (error) {}
                                    }}
                                >
                                    <span className={styles['table-btn']}>删除</span>
                                </Popconfirm>
                            )) ||
                                null}
                        </Space>
                    </Fragment>
                ),
            },
        ];

        return list;
    }, [templateType]);

    const resetForm = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    return (
        <Fragment>
            <Card style={{ width: '800px' }} bordered={false}>
                <Form
                    form={form}
                    onFinish={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                        <Col span={8}>
                            <FormItem label="模板名称:" name="templateName">
                                <Input autoComplete="off" allowClear placeholder="请填写" />
                            </FormItem>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <Button
                    className="mg-b-20"
                    type="primary"
                    onClick={() => {
                        onCreateEvent();
                    }}
                >
                    手动添加
                </Button>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content', y: 350 }}
                    rowKey={(record) => record.templateId}
                    dataSource={tableList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: tableListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: false,
                        showQuickJumper: false,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
                {/* 专门用于编辑列表的模板 */}
                <ModuleEditModal
                    {...props}
                    zIndex={zIndex + 1}
                    initRef={templateRef}
                    onFinish={(params, openParams) => {
                        searchData();
                    }}
                ></ModuleEditModal>
            </Card>
        </Fragment>
    );
};

const ActTemplateSelectModal = (props, ref) => {
    const { disabled, zIndex = 122 } = props;
    const [visible, toggleVisible] = useState(false);
    const openModal = () => {
        toggleVisible(true);
    };
    const closeModal = () => {
        toggleVisible(false);
    };
    useImperativeHandle(ref, () => {
        return {
            closeModal,
        };
    });
    return (
        <Fragment>
            <span
                onClick={() => {
                    !disabled && openModal();
                }}
            >
                选择模板
            </span>
            <Modal
                width={850}
                visible={visible}
                footer={false}
                onCancel={closeModal}
                destroyOnClose
                zIndex={zIndex}
            >
                <ActTemplateSelectCard
                    {...props}
                    zIndex={zIndex + 1}
                    onCancel={closeModal}
                    onSelectHandler={() => {
                        closeModal();
                    }}
                ></ActTemplateSelectCard>
            </Modal>
        </Fragment>
    );
};

export default forwardRef(ActTemplateSelectModal);
