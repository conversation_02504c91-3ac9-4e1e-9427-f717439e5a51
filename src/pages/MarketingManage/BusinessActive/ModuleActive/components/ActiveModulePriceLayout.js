import { InfoCircleOutlined } from '@ant-design/icons';
import { Form, Radio, Tooltip, Switch, Popconfirm } from 'antd';
import { Fragment, useEffect, useMemo } from 'react';
import { connect } from 'umi';

import DiscountFormItem from '../../DiscountFormItem';
import { MODELTYPS } from '../../BusinessConfig';
import { MODULE_ACT_TYPE, MODULE_ACT_TYPE_CODES } from './declare';
import { ACT_MENU_TYPE } from './ActModuleConfig';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const ActiveModulePriceLayout = (props) => {
    const { form, dispatch, global, formItemLayout, currentUser, belongType, menuType } = props;

    const { codeInfo } = global || {};
    const { adjustType: adjustTypeList } = codeInfo;
    useEffect(() => {
        if (!adjustTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'adjustType',
            });
        }
    }, []);

    const belongName = useMemo(() => {
        let name = '';
        if (belongType == 'plat') {
            name = '平台';
        } else if (belongType == 'oper') {
            name = '运营商';
        }
        return name;
    }, [belongType]);
    return (
        <Fragment>
            {menuType == ACT_MENU_TYPE.USER_GROUP ? null : (
                <FormItem
                    label={<span>活动类型</span>}
                    name="belongType"
                    {...formItemLayout}
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请选择活动类型',
                        },
                    ]}
                    initialValue={currentUser?.operId?.length ? 'oper' : 'plat'}
                >
                    {(belongType && belongName) || (
                        <Radio.Group>
                            {(!currentUser?.operId?.length && <Radio value={'plat'}>平台</Radio>) ||
                                null}
                            <Radio value={'oper'}>运营商</Radio>
                        </Radio.Group>
                    )}
                </FormItem>
            )}

            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.belongType !== curValues.belongType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const belongType = getFieldValue('belongType');
                    const initValue =
                        menuType == ACT_MENU_TYPE.USER_GROUP
                            ? MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT
                            : MODULE_ACT_TYPE_CODES.DISCOUNT;
                    return (
                        <FormItem
                            label={<span>营销方式</span>}
                            name="templateActType"
                            {...formItemLayout}
                            rules={[
                                { required: true, message: '请选择营销方式' },
                                (_) => ({
                                    validator(rule, value) {
                                        if (
                                            belongType !== 'plat' &&
                                            value === MODULE_ACT_TYPE_CODES.DYNAMIC
                                        ) {
                                            return Promise.reject('非平台类型不能配置动态调价');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            initialValue={initValue}
                        >
                            <Radio.Group>
                                {(
                                    (menuType == ACT_MENU_TYPE.USER_GROUP && [
                                        {
                                            label: '打折',
                                            value: MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT,
                                        },
                                        {
                                            label: '立减',
                                            value: MODULE_ACT_TYPE_CODES.CROWDPLLICY_REDUCTION,
                                        },
                                        {
                                            label: '一口价',
                                            value: MODULE_ACT_TYPE_CODES.CROWDPLLICY_PRICE,
                                        },
                                    ]) ||
                                    (menuType === ACT_MENU_TYPE.TEMPLATE &&
                                        MODULE_ACT_TYPE.filter(
                                            (item) => item.value !== MODULE_ACT_TYPE_CODES.PRICE,
                                        )) ||
                                    MODULE_ACT_TYPE
                                ).map((ele, index) => {
                                    return (
                                        <Radio
                                            value={ele.value}
                                            key={index}
                                            disabled={
                                                belongType !== 'plat' &&
                                                ele.value === MODULE_ACT_TYPE_CODES.DYNAMIC
                                            }
                                        >
                                            {ele.label}
                                        </Radio>
                                    );
                                })}
                            </Radio.Group>
                        </FormItem>
                    );
                }}
            </FormItem>

            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.templateActType !== curValues.templateActType ||
                    prevValues.belongType !== curValues.belongType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const templateActType = getFieldValue('templateActType');
                    const belongType = getFieldValue('belongType');
                    return templateActType === MODULE_ACT_TYPE_CODES.DYNAMIC &&
                        belongType === 'plat' ? (
                        <FormItem
                            key="adjustPriceType"
                            name="adjustPriceType"
                            initialValue={ADJUST_PRICE_TYPES.DEFAULT}
                            rules={[{ required: true, message: '请选择' }]}
                            label={
                                <span>
                                    价格基准
                                    <Tooltip title="参与计算折扣的基准价。选择结算价后，若场站没有结算价将自动以原价计算">
                                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                    </Tooltip>
                                </span>
                            }
                            {...formItemLayout}
                        >
                            <Radio.Group
                                onChange={() => {
                                    // form.setFieldsValue({
                                    //     adjustDetailList: [{}],
                                    // });
                                    form.validateFields(['adjustDetailList']);
                                }}
                            >
                                <Radio value={ADJUST_PRICE_TYPES.DEFAULT}>默认</Radio>
                                <Radio value={ADJUST_PRICE_TYPES.OPER_ORIGIN}>商家原价</Radio>
                                <Radio value={ADJUST_PRICE_TYPES.PUSH}>
                                    {((menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                                        menuType ==
                                            ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) && (
                                        <Fragment>
                                            推送结算价
                                            <Tooltip title="分润场站做活动，请勿选择推送结算价">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </Fragment>
                                    )) ||
                                        '商家结算价'}
                                </Radio>
                                {((menuType == ACT_MENU_TYPE.BASIC_PRICE ||
                                    menuType == ACT_MENU_TYPE.MERCHANTS_BASIC_PRICE_TEMPLATE) && (
                                    <Radio value={ADJUST_PRICE_TYPES.SETTLE}>配置结算价</Radio>
                                )) ||
                                    null}
                            </Radio.Group>
                        </FormItem>
                    ) : null;
                }}
            </FormItem>

            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.templateActType !== curValues.templateActType ||
                    prevValues.belongType !== curValues.belongType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const templateActType = getFieldValue('templateActType');
                    const belongType = getFieldValue('belongType');
                    // 新旧类型的转换
                    const convertObj = {
                        [MODULE_ACT_TYPE_CODES.DISCOUNT]: MODELTYPS.DISCOUNT,
                        [MODULE_ACT_TYPE_CODES.REDUCTION]: MODELTYPS.REDUCTION,
                        [MODULE_ACT_TYPE_CODES.DYNAMIC]: MODELTYPS.DYNAMIC,
                        [MODULE_ACT_TYPE_CODES.PRICE]: MODELTYPS.PRICE,
                        [MODULE_ACT_TYPE_CODES.CROWDPLLICY_DISCOUNT]: MODELTYPS.DISCOUNT,
                        [MODULE_ACT_TYPE_CODES.CROWDPLLICY_REDUCTION]: MODELTYPS.REDUCTION,
                        [MODULE_ACT_TYPE_CODES.CROWDPLLICY_PRICE]: MODELTYPS.PRICE,
                    };

                    return [
                        <DiscountFormItem
                            form={form}
                            actType={convertObj[templateActType]}
                            formItemLayout={formItemLayout}
                            hasVip={menuType !== ACT_MENU_TYPE.USER_GROUP} // 运营商账号在模板规则中可以加没有会员价的模板
                            belongType={belongType}
                            typeList={adjustTypeList}
                            key="dis"
                        />,
                        menuType !== ACT_MENU_TYPE.USER_GROUP &&
                        templateActType === MODULE_ACT_TYPE_CODES.REDUCTION ? (
                            <FormItem
                                label={
                                    <span>
                                        减电费
                                        <Tooltip title="配置减电费后，立减额度超出服务费部分再减电费">
                                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                        </Tooltip>
                                    </span>
                                }
                                name={'subChargeFeeFlag'}
                                valuePropName={'checked'}
                                required
                                {...formItemLayout}
                            >
                                <Switch checkedChildren="开" unCheckedChildren="关" />
                            </FormItem>
                        ) : null,
                    ];
                }}
            </FormItem>
        </Fragment>
    );
};
export default connect(({ global, user, businessActiveModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    businessActiveModel,
    detalisLoading: loading.effects['businessActiveModel/initEditActInfo'],
}))(ActiveModulePriceLayout);
