// 修改时间范围弹窗
import { usePagination, useRequest } from 'ahooks';
import {
    Form,
    Modal,
    Radio,
    Select,
    Space,
    message,
    Descriptions,
    Button,
    Typography,
    Row,
    Col,
    Badge,
} from 'antd';
import { useRef } from 'react';
import React, { useEffect, useState } from 'react';
import { CheckCard } from '@ant-design/pro-components';
import { copyObjectCommon, isEmpty } from '@/utils/utils';

const ChangeTimeRangeModal = (props: any) => {
    const { visible, onClose, dataItem, dataIndex, dataSource, onSuccess } = props;
    const [form] = Form.useForm();
    const [timePeriodList, setTimePeriodList] = useState<any>([]);
    const [formatDataSource, setFormatDataSource] = useState<any>([]);

    useEffect(() => {
        if (dataSource?.length > 0) {
            changeDataSource();
        } else {
            initTimePeriodList();
        }
    }, [visible]);
    useEffect(() => {
        if (formatDataSource?.length > 0) {
            initTimePeriodList();
        }
    }, [formatDataSource]);

    const typeColor = ['#f0a661', '#f3dc72', '#69a5f1', '#8fe16f', '#a8d8e2'];
    const typeEnum = [
        { text: '尖时段', color: '#f0a661', value: '0' },
        {
            text: '峰时段',
            color: '#f3dc72',
            value: '1',
        },
        {
            text: '平时段',
            color: '#69a5f1',
            value: '2',
        },
        {
            text: '谷时段',
            color: '#8fe16f',
            value: '3',
        },
        {
            text: '深时段',
            color: '#a8d8e2',
            value: '4',
        },
    ];
    const changeDataSource = () => {
        const arr = dataSource?.map((item: any) => {
            item.splitTimeRange = splitRange(item.timePeriod || '');
            return item;
        });
        setFormatDataSource(arr);
    };
    // 切割时间段为时间数组
    const splitRange = (dataRange: any) => {
        const dataRangeArr = dataRange?.split(',');
        const arr: number[] = [];
        dataRangeArr.forEach((element: string) => {
            const start = Number(element.substring(0, 2));
            const end = Number(element.substring(6, 8));

            for (let i = start; i < end; i++) {
                arr.push(i);
            }
        });
        return arr;
    };
    // 初始化24小时选择列表
    const initTimePeriodList = () => {
        const arr: any = [];
        for (let i = 0; i <= 23; i++) {
            let timePeriod = '';
            if (i < 9) {
                timePeriod = `0${i}:00~0${i + 1}:00`;
            } else if (i === 9) {
                timePeriod = `0${i}:00~${i + 1}:00`;
            } else {
                timePeriod = `${i}:00~${i + 1}:00`;
            }
            arr.push({
                value: i,
                timePeriod,
                timePeriodFlag: '',
                disabled: false,
                checked: false,
                bgColor: '#fff',
            });
        }
        if (formatDataSource && formatDataSource?.length > 0) {
            // 回填已有数据
            formatDataSource.forEach((ele: any) => {
                ele?.splitTimeRange?.forEach((val: number) => {
                    arr[val].timePeriodFlag = ele?.timePeriodFlag;
                    arr[val].disabled = dataItem?.timePeriodFlag !== ele?.timePeriodFlag;
                    arr[val].checked = dataItem?.timePeriodFlag === ele?.timePeriodFlag;
                    arr[val].bgColor = typeColor[ele?.timePeriodFlag];
                    // arr[val] = {
                    //     value: val,
                    //     timePeriod,
                    //     timePeriodFlag: '',
                    //     disabled: false,
                    //     checked: false,
                    //     bgColor: '#fff',
                    // };
                });
            });
        }

        setTimePeriodList(arr);
    };
    // 数字转小时
    const formatPeriodNumber = (i: number | string) => {
        let timePeriod = '';
        i = Number(i);
        if (i < 10) {
            timePeriod = `0${i}:00`;
        } else {
            timePeriod = `${i}:00`;
        }
        return timePeriod;
    };
    // 多个连续时段合并成一个起始段
    const groupConsecutiveNumbers = (list: number[]) => {
        let start = list[0]; //记录连续数字的开始位置

        const resultList: string[] = []; //输出的结果

        const continuity = (index: number) => {
            if (index >= list.length) {
                return [];
            }
            if (list[index + 1] - list[index] === 1) {
                continuity(index + 1); //为连续数字，继续执行此方法
            } else {
                if (start !== list[index]) {
                    resultList.push(`${start}~${list[index]}`);
                } else {
                    resultList.push(`${start}`);
                }
                start = list[index + 1];
                continuity(index + 1);
            }
            return resultList;
        };
        return continuity(0);
    };

    const handleSubmit = async (values: any) => {
        if (!values['checkbox-group']) {
            form.resetFields();
            onClose?.();
            return;
        }
        const consecutiveNumbers = values['checkbox-group']?.sort((a: number, b: number) => a - b);
        const formatArr = groupConsecutiveNumbers(consecutiveNumbers);
        // 转00：00~00：00格式
        const timePeriodArr = formatArr?.map((val: string) => {
            if (val?.length > 2) {
                const rangeArr = val?.split('~');
                const start = rangeArr[0];
                const end = rangeArr[1];
                val = `${formatPeriodNumber(start)}~${formatPeriodNumber(Number(end) + 1)}`;
            } else {
                val = `${formatPeriodNumber(val)}~${formatPeriodNumber(Number(val) + 1)}`;
            }
            return val;
        });
        const timePeriodString = timePeriodArr?.join(',');
        form.resetFields();
        onSuccess?.({
            timePeriod: timePeriodString,
            timePeriodFlag: dataItem?.timePeriodFlag,
        });
    };

    return (
        <Modal
            width={1000}
            title={`配置时间范围`}
            visible={visible}
            onCancel={() => {
                form.resetFields();
                onClose?.();
            }}
            destroyOnClose
            onOk={() => form.submit()}
            okText="应用"
        >
            <Space style={{ marginBottom: '20px' }}>
                {typeEnum?.map((val: any, index) => (
                    <Badge
                        key={index}
                        // status={val?.status} //
                        color={val?.color}
                        text={
                            <span style={{ color: val?.color }}>{`${val?.text}${
                                dataItem?.timePeriodFlag === val?.value ? '（当前）' : ''
                            }`}</span>
                        }
                    />
                ))}
            </Space>
            <Form form={form} onFinish={handleSubmit} layout="vertical">
                <Form.Item name="checkbox-group">
                    <CheckCard.Group
                        multiple
                        style={{ width: '100%' }}
                        defaultValue={formatDataSource?.[dataIndex]?.splitTimeRange}
                        size="small"
                    >
                        <Row gutter={[16, 16]}>
                            {timePeriodList?.map((item: any) => (
                                <Col key={item.value} span={4}>
                                    <CheckCard
                                        style={{
                                            width: '150px',
                                            backgroundColor: `${item.bgColor || undefined}`,
                                        }}
                                        bordered={false}
                                        disabled={item?.disabled}
                                        // defaultChecked={item?.checked}
                                        title={`${item.value}点`}
                                        description={item.timePeriod}
                                        value={item.value}
                                        onChange={(e) => {
                                            let copyList = [];
                                            copyList = copyObjectCommon(timePeriodList);
                                            if (!item?.checked) {
                                                copyList[item.value].bgColor =
                                                    typeColor[dataItem?.timePeriodFlag];
                                                copyList[item.value].timePeriodFlag =
                                                    dataItem?.timePeriodFlag;
                                                copyList[item.value].checked = true;
                                            } else {
                                                copyList[item.value].bgColor = '#fff';
                                                copyList[item.value].timePeriodFlag =
                                                    dataItem?.timePeriodFlag;
                                                copyList[item.value].checked = false;
                                            }
                                            setTimePeriodList(copyList);
                                        }}
                                    />
                                </Col>
                            ))}
                        </Row>
                    </CheckCard.Group>
                </Form.Item>
                {/* <Form.Item>
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item> */}
            </Form>
        </Modal>
    );
};

export default ChangeTimeRangeModal;
