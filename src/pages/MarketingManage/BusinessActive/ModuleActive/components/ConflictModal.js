import { Link } from 'umi';
import { Button, Modal, Space, Table, Tooltip, message } from 'antd';
import { useImperativeHandle, useState } from 'react';

const { confirm } = Modal;

// 冲突提示弹窗
const ConflictModal = (props) => {
    const { initRef, onFinish, columns = [], zIndex } = props;
    const [resultList, updateResultList] = useState([]);
    const [visible, updateVisible] = useState(false);
    useImperativeHandle(initRef, () => ({
        show: (list = [], callback) => {
            updateResultList([...list]);
            updateVisible(true);
        },
        onClose,
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const onSwitch = () => {
        confirm({
            title: '直接切换',
            content: (
                <Space direction="vertical">
                    <span>执行后将自动解决活动冲突</span>
                    <span style={{ color: 'red' }}>
                        若冲突活动中所有场站都为冲突站，将自动停止冲突活动；
                    </span>
                    <span style={{ color: 'red' }}>
                        若冲突活动中仅部分场站为冲突站，自动将冲突站从冲突活动中移除；
                    </span>
                </Space>
            ),
            onOk: () => {
                try {
                    onFinish?.();
                } catch (error) {
                } finally {
                }
            },
        });
    };
    const onCover = () => {
        confirm({
            title: '新活动生效后切换',
            content: (
                <Space direction="vertical">
                    <span>执行后将自动解决活动冲突</span>
                    <span style={{ color: 'red' }}>
                        部分冲突：如果冲突场站所在原活动的起始日期早于新配置活动的起始日期，则冲突场站自动将冲突站从冲突活动中移除，自动创建独立活动，运行至新活动起始时间终止
                    </span>
                    <span style={{ color: 'red' }}>
                        全部冲突：如果原活动的起始日期早于新配置活动的起始日期，原冲突活动自动停止，冲突场站自动创建独立活动，运行至新活动起始时间终止
                    </span>
                </Space>
            ),
            onOk: () => {
                try {
                    onFinish?.(1);
                } catch (error) {
                } finally {
                }
            },
        });
    };
    return (
        <Modal
            title="活动冲突提醒"
            destroyOnClose
            width={1000}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            okText="直接覆盖"
            onOk={() => {
                if (resultList.some((ele) => ele.conflictDealFlag == '0')) {
                    message.error('存在正在审批的活动，暂无法操作');
                    return;
                }
                confirm({
                    title: '直接覆盖',
                    okText: '是的',
                    cancelText: '不,直接切换',
                    content:
                        '冲突场站是否在新活动生效前沿用冲突活动规则继续运行，新活动生效后切换至新活动？',
                    onOk: () => onCover(),
                    onCancel: () => onSwitch(),
                });
            }}
            zIndex={zIndex}
        >
            <Space direction="vertical" size={5} style={{ color: 'gray', marginBottom: '6px' }}>
                <span>
                    温馨提示：场站营销冲突一般是由于当前活动场站与其他活动场站，存在活动类型、渠道、时间、人群上的冲突
                </span>
            </Space>
            <br></br>

            <Table
                scroll={{ x: 'max-content', y: 450 }}
                dataSource={resultList}
                pagination={false}
                columns={columns}
                rowClassName={() => 'editable-row'}
            />
        </Modal>
    );
};

export default ConflictModal;
