import { isEmpty } from '@/utils/utils';

interface ActiveRule {
    cycleList?: ActiveRule[];
    periodsList?: ActiveRule[];
    discountList?: {
        actChannel?: string;
        actId?: string;
        actType?: string;
        adjustDetailList?: {
            adjustMethodType: string;
            adjustSn: string;
            adjustThreeDiscountValue: string;
            adjustType: string;
            adjustValue: string;
            adjustVipValue: string;
            vipAdjustMethodType: string;
            vthreeMethodType?: string;
        }[];
        adjustPriceType?: string;
        dataType?: string;
        dctValue?: string;
        discountDesc?: string;
        subChargeFeeFlag?: string;
        timePeriods?: string;
        vipDiscountValue?: string;
        vthreeDiscountValue?: string;
    }[];
}

export function hasVthreeDiscountValue(gridList: ActiveRule[], mapGridList: ActiveRule[]): boolean {
    let hasV3: boolean = false;
    try {
        gridList?.every((v) => {
            return v?.cycleList?.every((c) => {
                return c?.periodsList?.every((p) => {
                    return p?.discountList?.every((d) => {
                        if (!isEmpty(d?.adjustDetailList)) {
                            const adjustResult = d.adjustDetailList?.every((adjust) => {
                                if (!isEmpty(adjust?.adjustThreeDiscountValue)) {
                                    hasV3 = true;
                                    return false;
                                } else {
                                    return true;
                                }
                            });
                            return adjustResult;
                        } else if (!isEmpty(d?.vthreeDiscountValue)) {
                            hasV3 = true;
                            return false;
                        } else {
                            return true;
                        }
                    });
                });
            });
        });
        mapGridList?.every((v) => {
            return v?.cycleList?.every((c) => {
                return c?.periodsList?.every((p) => {
                    return p?.discountList?.every((d) => {
                        if (!isEmpty(d?.adjustDetailList)) {
                            return d.adjustDetailList?.every((adjust) => {
                                if (!isEmpty(adjust?.adjustThreeDiscountValue)) {
                                    hasV3 = true;
                                    return false;
                                } else {
                                    return true;
                                }
                            });
                        } else if (!isEmpty(d?.vthreeDiscountValue)) {
                            hasV3 = true;
                            return false;
                        } else {
                            return true;
                        }
                    });
                });
            });
        });
    } catch (error) {}
    return hasV3;
}
