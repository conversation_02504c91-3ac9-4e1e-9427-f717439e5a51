import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    Modal,
    Select,
    Space,
    Input,
    DatePicker,
    TreeSelect,
    Tabs,
    Radio,
    Spin,
    Typography,
} from 'antd';
import { connect, Link, useAccess } from 'umi';
import React, { useEffect, useState, useRef, useMemo } from 'react';

import {
    getMktActDetailChildApi,
    stopMktActApi,
    deleteMktActApi,
    getMngMktActDetailChildApi,
    deleteMngMktActApi,
    stopMngMktActApi,
    exportBusinessStationActAsynApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { STATUS_TYPES } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './BusinessActive.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';
import ActiveCrowdCheckTree from './components/ModuleCrowdCheckTree';
import { ACT_MENU_TYPE, BusinessActiveType } from './components/ActModuleConfig';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CacheAreaView from '@/components/CacheAreaView';

const { SHOW_CHILD } = TreeSelect;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        history,
        onSubmit,
        onReset,
        listLoading,
        global: { pageInit, systemAccountList, codeInfo = {} },
        currentUser,
        menuType,
        onExportForm,
        exportName,
    } = props;

    const { createSource: createSourceList } = codeInfo;

    const {
        location: { pathname, query },
    } = history;

    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]) {
            if (pageInit[pathname].form.stationName) {
                stationRef.current.fetchStation(pageInit[pathname].form.stationName);
            }
        }
        if (!systemAccountList?.length) {
            dispatch({
                type: 'global/getSystemAccountList',
                options: {},
            });
        }
        if (!createSourceList) {
            dispatch({
                type: 'global/initCode',
                code: 'createSource',
            });
        }
    }, []);
    useEffect(() => {
        if (query.actIdStr) {
            form.setFieldsValue({ actIdStr: query.actIdStr });
        }
    }, [query.actIdStr]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const systemAccountOptions = useMemo(() => {
        if (systemAccountList instanceof Array) {
            return systemAccountList.map((ele) => {
                return (
                    <Option value={ele.accountName} key={ele.accountId}>
                        {ele.accountName}
                    </Option>
                );
            });
        } else {
            return [];
        }
    }, [systemAccountList]);

    const span = useMemo(() => {
        if (pathname.indexOf('workorder/') >= 0) {
            return 12;
        }
        return 8;
    }, [pathname]);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                exportName={exportName}
                onExportForm={onExportForm}
            >
                <Form.Item noStyle name="actIdStr"></Form.Item>
                <Col span={span}>
                    <FormItem label="活动名称:" name="actName">
                        <Input autoComplete="off" allowClear placeholder="请填写" />
                    </FormItem>
                </Col>

                {menuType == ACT_MENU_TYPE.COMPANY_ACT && (
                    <Col span={span}>
                        <OperSelectTypeItem label="活动运营商" name="orgCode" form={form} />
                    </Col>
                )}

                <Col span={span}>
                    <AllStationSelect
                        form={form}
                        ref={stationRef}
                        label="活动场站"
                        name="stationIds"
                    />
                </Col>
                <Col span={span}>
                    <FormItem label="活动编号:" name="actNo">
                        <Input autoComplete="off" allowClear placeholder="请填写" />
                    </FormItem>
                </Col>

                {menuType !== ACT_MENU_TYPE.ACT_1 && menuType !== ACT_MENU_TYPE.ACT_2 && (
                    <Col span={span}>
                        <FormItem name="dateTime" label="活动有效期" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} allowClear />
                        </FormItem>
                    </Col>
                )}

                {currentUser?.operId ||
                (menuType != ACT_MENU_TYPE.ACT_1 && menuType != ACT_MENU_TYPE.ACT_2) ? null : (
                    <Col span={span}>
                        <FormItem label="活动类型:" name="belongType">
                            <Select allowClear placeholder="请选择">
                                <Option value={'plat'}>平台</Option>
                                <Option value={'oper'}>运营商</Option>
                                <Option value={'user'}>用户</Option>
                            </Select>
                        </FormItem>
                    </Col>
                )}
                {menuType !== ACT_MENU_TYPE.BASIC_PRICE && menuType !== ACT_MENU_TYPE.COMPANY_ACT && (
                    <Col span={span}>
                        <FormItem label="活动人群" name="activeCrowd">
                            <ActiveCrowdCheckTree noDisabledOption />
                        </FormItem>
                    </Col>
                )}
                {(menuType == ACT_MENU_TYPE.ACT_1 || menuType == ACT_MENU_TYPE.ACT_2) && (
                    <Col span={span}>
                        <FormItem name="dateTime" label="活动有效期" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} allowClear />
                        </FormItem>
                    </Col>
                )}
                {currentUser?.operId ? null : (
                    <Col span={span}>
                        <FormItem label="创建人" name="creEmp">
                            <Select
                                placeholder="请选择"
                                showSearch
                                filterOption={(input, option) =>
                                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                }
                                allowClear
                            >
                                {systemAccountOptions}
                            </Select>
                        </FormItem>
                    </Col>
                )}
                {menuType == ACT_MENU_TYPE.BASIC_PRICE && (
                    <Col span={8}>
                        <FormItem label="小蜜蜂场站" name="directOperFlag">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>是</Option>
                                <Option value={'0'}>否</Option>
                            </Select>
                        </FormItem>
                    </Col>
                )}

                {menuType != ACT_MENU_TYPE.ACT_1 && (
                    <Col span={span}>
                        <FormItem name="dateTime2" label="创建时间" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} allowClear />
                        </FormItem>
                    </Col>
                )}
                {currentUser?.operId ? null : (
                    <Col span={span}>
                        <FormItem label="停止人" name="closeEmp">
                            <Select
                                placeholder="请选择"
                                showSearch
                                filterOption={(input, option) =>
                                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                }
                                allowClear
                            >
                                {systemAccountOptions}
                            </Select>
                        </FormItem>
                    </Col>
                )}
                {currentUser?.operId ? null : (
                    <Col span={span}>
                        <FormItem name="closeDateTime" label="停止时间" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} allowClear />
                        </FormItem>
                    </Col>
                )}
                {currentUser?.operId ? null : (
                    <Col span={span}>
                        <Form.Item label="创建方" name="createSource">
                            <Select
                                allowClear
                                placeholder="请选择创建方"
                                options={createSourceList}
                                fieldNames={{ label: 'codeName', value: 'codeValue' }}
                            />
                        </Form.Item>
                    </Col>
                )}
            </SearchOptionsBar>
        </Form>
    );
};

export const expandedRowRender = (record, menuType, isOldPage) => {
    // 处理合并单元格，需要重新构造一个展示对象
    const showList = () => {
        const list = [];
        for (const actItem of record.childList || []) {
            if (actItem?.actPeriodsList?.length) {
                actItem?.actPeriodsList?.map((ele, index) => {
                    const item = {
                        ...actItem,
                        ...ele,
                        rowSpan: index == 0 ? actItem?.actPeriodsList?.length : 0,
                    };
                    delete item.actPeriodsList;
                    list.push(item);
                });
            } else {
                actItem.rowSpan = 1;
                list.push(actItem);
            }
        }
        return list;
    };

    return (
        <Spin spinning={record?.loading} id={record?.actId}>
            <TablePro
                rowKey="actId"
                columns={[
                    ...(menuType == ACT_MENU_TYPE.USER_GROUP
                        ? []
                        : [
                              {
                                  title: '投放渠道',
                                  width: 180,
                                  dataIndex: 'actChannelName',
                                  render(text) {
                                      return (
                                          <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                              {text || '-'}
                                          </span>
                                      );
                                  },
                                  onCell: (record) => {
                                      return {
                                          rowSpan: record.rowSpan,
                                      };
                                  },
                              },
                          ]),
                    {
                        title: '活动人群',
                        width: 120,
                        dataIndex: 'activeCrowdName',
                        render(text) {
                            return (
                                <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                    {text || '-'}
                                </span>
                            );
                        },
                        onCell: (record) => {
                            return {
                                rowSpan: record.rowSpan,
                            };
                        },
                    },
                    {
                        title: '活动周期',
                        width: 160,
                        dataIndex: isOldPage ? 'cycleValueName' : 'cycleValueFlagName',
                        render(text) {
                            return (
                                <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                    {text || '-'}
                                </span>
                            );
                        },
                        onCell: (record) => {
                            return {
                                rowSpan: record.rowSpan,
                            };
                        },
                    },
                    {
                        title: '活动时段',
                        width: 120,
                        dataIndex: 'actPeriodsName',
                        render(text) {
                            return (
                                <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                    {text || '-'}
                                </span>
                            );
                        },
                    },
                    {
                        title: '营销方式',
                        width: 120,
                        dataIndex: 'actTypeName',
                        render(text) {
                            if (!text || text === 'null') {
                                text = '-';
                            }
                            return (
                                <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                    {text}
                                </span>
                            );
                        },
                    },
                    {
                        title: '活动优惠',
                        width: 220,
                        dataIndex: 'discountDesc',
                        render(text) {
                            return (
                                <span title={text} style={{ whiteSpace: 'break-spaces' }}>
                                    {text || '-'}
                                </span>
                            );
                        },
                    },
                ]}
                filterHeader={false}
                dataSource={showList() || []}
                pagination={false}
                sticky={false}
            />
        </Spin>
    );
};

export const BusinessActiveListLayout = (props) => {
    const {
        dispatch,
        history,
        businessModuleActiveModel: { actList, actTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },
        workorderEvent,
        filterParams,
        defaultMeueType,
    } = props;
    const access = useAccess();
    const cacheRef = useRef();
    const {
        location: { pathname, query },
    } = history;
    const isOldPage = window.location.href?.indexOf('businessActive/temp') >= 0;

    const menuType = useMemo(
        () => defaultMeueType || BusinessActiveType(undefined, pathname),
        [defaultMeueType],
    );
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );
    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'businessModuleActiveModel/updateProperty',
                params: { actList: [], actTotal: 0 },
            });
        };
    }, []);

    const [randomRowKey, setRandomRowKey] = useState('');

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [workOrderParams, updateWorkOrderParams] = useState();
    useEffect(() => {
        if (filterParams && JSON.stringify(workOrderParams) != JSON.stringify(filterParams)) {
            // 避免被多次调用，本地做个数据暂存
            changePageInfo({ pageIndex: 1 });
            updateWorkOrderParams(filterParams);
        }
    }, [filterParams]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        setRandomRowKey(new Date().getTime());
        const data = form.getFieldsValue();
        const params = {
            ...(filterParams || {}),
            ...data,
            pageIndex: isDownload ? undefined : pageInfo.pageIndex,
            pageSize: isDownload ? undefined : pageInfo.pageSize,
            activeCrowd: data.activeCrowd && data.activeCrowd.join(','),
            beginDate: data.dateTime?.[0]?.format('YYYY-MM-DD'),
            endDate: data.dateTime?.[1]?.format('YYYY-MM-DD'),
            dateTime: undefined,
            creTimeBgn: data.dateTime2?.[0].format('YYYY-MM-DD'),
            creTimeEnd: data.dateTime2?.[1].format('YYYY-MM-DD'),
            dateTime2: undefined,
            closeTimeBgn: data.closeDateTime?.[0].format('YYYY-MM-DD'),
            closeTimeEnd: data.closeDateTime?.[1].format('YYYY-MM-DD'),
            closeDateTime: undefined,
            // buildId: operId,
        };
        if (menuType == ACT_MENU_TYPE.USER_GROUP) {
            params.categoryType = '02';
        } else if (menuType == ACT_MENU_TYPE.BASIC_PRICE) {
            params.categoryType = '01';
            params.belongType = 'plat';
        } else if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
            params.categoryType = '01';
            params.belongType = 'oper';
        } else {
            params.categoryType = '01';
        }
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }
        if (workorderEvent) {
            // 工单要过滤掉创建方是商家端的
            params.createSource = '01';
        }

        if (isDownload) {
            if (menuType == ACT_MENU_TYPE.COMPANY_ACT) {
                // 下载
                await exportBusinessStationActAsynApi({
                    uploadParam: JSON.stringify(params),
                    bizType: 'businessStationActExport',
                });
                cacheRef?.current?.count();
            } else {
                const columnsStrs = [];
                for (const item of columns) {
                    if (item.dataIndex) {
                        columnsStrs.push({
                            key: item.dataIndex,
                            value: item.title,
                        });
                    }
                }
                exportTableByParams({
                    methodUrl: '/bil/act/common/oper-mktact-list',
                    options: params,
                    columnsStr: columnsStrs,
                });
            }
        } else {
            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: isOldPage
                    ? 'businessModuleActiveModel/getMktActList'
                    : 'businessModuleActiveModel/getMngMktActList',
                options: params,
            });
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const stopBusinessActiveEvent = async (item) => {
        confirm({
            title: `确定停止${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (workorderEvent) {
                        workorderEvent('stop', item);
                    } else {
                        const api = isOldPage ? stopMktActApi : stopMngMktActApi;
                        await api({
                            actId: item.actId,
                            parentActFlag: item.parentActFlag,
                            categoryType: '01',
                        });
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteBusinessActiveEvent = async (item) => {
        confirm({
            title: `确定删除${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (workorderEvent) {
                        workorderEvent('delete', item);
                        return;
                    } else {
                        const api = isOldPage ? deleteMktActApi : deleteMngMktActApi;
                        await api({
                            actId: item.actId,
                            parentActFlag: item.parentActFlag,
                            categoryType: '01',
                        });
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // 动态获取下级页面
    const getNextPagePath = (type = 'add', item) => {
        let path;
        // 如果是场站营销2.0的页面，需要判断当前项是1.0/2.0，其他页面是单独接口，不需要判断
        let targetPaths;
        if (item.menuType == ACT_MENU_TYPE.USER_GROUP) {
            targetPaths = isOldPage
                ? '/marketing/businessActive/temp/module-user-group'
                : '/marketing/businessActive/module-user-group';
        } else if (item.menuType == ACT_MENU_TYPE.BASIC_PRICE) {
            targetPaths = isOldPage
                ? '/marketing/businessActive/temp/module-basic-price'
                : '/marketing/businessActive/module-basic-price';
        } else if (item.menuType == ACT_MENU_TYPE.COMPANY_ACT) {
            targetPaths = isOldPage
                ? '/marketing/merchantActivityManagement/temp/module-company-act'
                : '/marketing/merchantActivityManagement/module-company-act';
        } else {
            targetPaths =
                item?.parentActFlag === '1'
                    ? isOldPage
                        ? '/marketing/businessActive/temp/moduleAct'
                        : '/marketing/businessActive/moduleAct'
                    : '/marketing/businessActive/list';
        }
        if (dynamicMenu) {
            for (let index = 0; index < dynamicMenu?.length; index++) {
                const element = dynamicMenu[index];
                if (element?.path == '/marketing') {
                    for (let x = 0; x < element.routes?.length; x++) {
                        const ele = element.routes[x];

                        if (
                            ele?.path.indexOf(targetPaths) > -1 &&
                            (ele?.path == `${targetPaths}/plat` ||
                                ele?.path == `${targetPaths}/user`)
                        ) {
                            path = `${ele.path}/${type}`;
                            break;
                        }
                    }
                }
                if (path?.length) {
                    break;
                }
            }
        }

        if (!path?.length) {
            path = `${targetPaths}/${type}`;
        }
        return path;
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        if (workorderEvent) {
            workorderEvent('add');
            return;
        }
        const path = getNextPagePath('add', { parentActFlag: '1', menuType });
        history.push(`${path}?redirect=${menuType}`, { menuType });
    };

    const editBusinessActiveEvent = (item) => {
        // 营销3.0，编辑跳到对应类型的编辑页
        if (workorderEvent) {
            workorderEvent('update', item);
            return;
        }
        const path = getNextPagePath('update', item);
        history.push(`${path}/${item.actId}?redirect=${menuType}`);
    };

    const lookBusinessActiveEvent = (item) => {
        if (workorderEvent) {
            workorderEvent('look', item);
            return;
        }
        const path = getNextPagePath('look', item);
        history.push(`${path}/${item.actId}?redirect=${menuType}`);
    };

    const copyBusinessActivePath = (item) => {
        // 营销3.0，复制跳到当前菜单对应的新增页，1.0的不处理，实在兼容不了
        const path = getNextPagePath('copy', {
            ...item,
            menuType:
                item.menuType == ACT_MENU_TYPE.ACT_2 && item?.parentActFlag != '1'
                    ? ACT_MENU_TYPE.ACT_1
                    : menuType,
        });
        return `${path}/${item.actId}?redirect=${menuType}`;
    };

    const columns = [
        {
            title: '活动有效期',
            width: 200,
            dataIndex: 'actTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '状态',
            width: 100,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '活动名称',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动编号',
            width: 200,
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...((menuType == ACT_MENU_TYPE.COMPANY_ACT && [
            {
                title: '活动运营商',
                width: 200,
                dataIndex: 'orgName',
                render(text, record) {
                    return (
                        <Typography.Text ellipsis={{ tooltip: true }} style={{ width: 200 }}>
                            {text}
                        </Typography.Text>
                    );
                },
            },
            {
                title: '活动场站',
                width: 180,
                dataIndex: 'cityName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]) || [
            {
                title: '活动范围',
                width: 180,
                dataIndex: 'cityName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]),
        ...((menuType == ACT_MENU_TYPE.ACT_2 && [
            {
                title: '活动类型',
                width: 120,
                dataIndex: 'belongTypeName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]) ||
            []),
        ...((menuType == ACT_MENU_TYPE.COMPANY_ACT && [
            {
                title: '活动平台',
                width: 120,
                dataIndex: 'cooperationPlatformName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]) ||
            []),
        ...((menuType == ACT_MENU_TYPE.COMPANY_ACT && [
            {
                title: '创建方',
                width: 120,
                dataIndex: 'createSourceName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]) ||
            []),
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'creTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creEmp',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (text, record, index) => {
                let controlItems = [];
                const copyPath = copyBusinessActivePath(record);
                let isEditAccess = true;
                let isDeleteAccess = true;
                let isStopAccess = true;
                let isCopyAccess = true;
                // 商家活动（打折立减）
                if (menuType === ACT_MENU_TYPE.COMPANY_ACT) {
                    if (record.createSource != '01') {
                        isEditAccess = false;
                        isDeleteAccess = false;
                        isCopyAccess = false;
                    }
                }
                // 基础价格，人群策略
                if (
                    menuType === ACT_MENU_TYPE.USER_GROUP ||
                    menuType === ACT_MENU_TYPE.BASIC_PRICE
                ) {
                    // 编辑权限禁用
                    if (!access['btn:activity_edit'] && !workorderEvent) {
                        isEditAccess = false;
                    }
                    // 删除权限禁用
                    if (!access['btn:activity_delete'] && !workorderEvent) {
                        isDeleteAccess = false;
                    }
                    // 复制权限禁用
                    if (!access['btn:activity_copy'] && !workorderEvent) {
                        isCopyAccess = false;
                    }
                    // 停止权限禁用
                    if (!access['btn:activity_stop'] && !workorderEvent) {
                        isStopAccess = false;
                    }
                }
                const editBtn = isEditAccess && (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            editBusinessActiveEvent(record);
                        }}
                        key="edit"
                    >
                        编辑
                    </span>
                );
                const lookBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            lookBusinessActiveEvent(record);
                        }}
                        key="detail"
                    >
                        详情
                    </span>
                );

                const delBtn = isDeleteAccess && (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            deleteBusinessActiveEvent(record);
                        }}
                        key="delete"
                    >
                        删除
                    </span>
                );
                const stopBtn = isStopAccess && (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            stopBusinessActiveEvent(record);
                        }}
                        key="stop"
                    >
                        停止
                    </span>
                );

                switch (record.actState) {
                    case STATUS_TYPES.DRAFT:
                        controlItems.push(lookBtn);
                        controlItems.push(editBtn);
                        controlItems.push(delBtn);
                        break;
                    case STATUS_TYPES.NOSTART:
                        controlItems.push(lookBtn);
                        controlItems.push(editBtn);
                        controlItems.push(stopBtn);
                        break;
                    case STATUS_TYPES.DOING:
                        controlItems.push(lookBtn);
                        controlItems.push(editBtn);
                        controlItems.push(stopBtn);
                        break;
                    case STATUS_TYPES.END:
                    case STATUS_TYPES.STOP:
                        controlItems.push(lookBtn);
                        break;
                    default:
                        break;
                }
                if (isCopyAccess) {
                    controlItems.push(
                        workorderEvent ? (
                            <span
                                className={styles['table-btn']}
                                onClick={() => {
                                    workorderEvent('copy', record);
                                }}
                                key="copy"
                            >
                                复制
                            </span>
                        ) : (
                            <Link
                                className={styles['table-btn']}
                                key="copy"
                                to={copyPath}
                                target="_blank"
                            >
                                复制
                            </Link>
                        ),
                    );
                }
                return <Space style={{ whiteSpace: 'nowrap' }}>{controlItems}</Space>;
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                exportName={menuType == ACT_MENU_TYPE.COMPANY_ACT && '导出至暂存区'}
                onExportForm={menuType == ACT_MENU_TYPE.COMPANY_ACT ? () => searchData(true) : null}
                menuType={menuType}
            />
            {workorderEvent ? null : (
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        新建
                    </Button>
                </div>
            )}

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${randomRowKey}${record?.actId}`}
                dataSource={actList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: actTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabsRender={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Tabs
                            defaultActiveKey={pageInfo?.tabType || '00'}
                            onChange={changeTabTypeEvent}
                        >
                            <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                            <TabPane tab="未开始" key={STATUS_TYPES.NOSTART} />
                            <TabPane tab="进行中" key={STATUS_TYPES.DOING} />
                            <TabPane tab="已停止" key={STATUS_TYPES.STOP} />
                            <TabPane tab="已结束" key={STATUS_TYPES.END} />
                            <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
                        </Tabs>
                        {menuType == ACT_MENU_TYPE.COMPANY_ACT && (
                            <CacheAreaView bizType="businessStationActExport" initRef={cacheRef} />
                        )}
                    </div>
                }
                tabType={pageInfo.tabType}
                offsetHeader={workorderEvent ? 0 : undefined}
                expandable={
                    (actList?.length && {
                        expandedRowRender: (record) =>
                            expandedRowRender(record, menuType, isOldPage),
                        onExpand: async (expanded, record) => {
                            if (record.childList) {
                                return;
                            }
                            try {
                                record.loading = true;
                                const api = isOldPage
                                    ? getMktActDetailChildApi
                                    : getMngMktActDetailChildApi;
                                let childList;
                                const { data } = await api({
                                    actId: record.actId,
                                    parentActFlag: record.parentActFlag,
                                });
                                if (isOldPage) {
                                    childList = data?.childList;
                                } else {
                                    childList = data;
                                }
                                const targetObj = actList.find((ele) => ele.actId == record.actId);
                                targetObj.childList = childList;
                                record.loading = targetObj.loading = false;
                                dispatch({
                                    type: 'businessModuleActiveModel/updateProperty',
                                    params: {
                                        actList: actList,
                                    },
                                });
                            } catch (error) {
                            } finally {
                                record.loading = false;
                            }
                        },
                    }) ||
                    null
                }
                className={
                    workorderEvent
                        ? styles['alert-scroll-fixed']
                        : styles['table-expanded-row-unfixed']
                }
                sticky={workorderEvent ? { offsetHeader: -24 } : 64}
                showToggleExpanded
            />
        </Card>
    );
};

const BusinessActiveListPage = (props) => {
    return (
        <PageHeaderWrapper>
            <BusinessActiveListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, login, user, businessModuleActiveModel, loading }) => ({
    global,
    login,
    currentUser: user.currentUser,
    businessModuleActiveModel,
    listLoading:
        loading.effects['businessModuleActiveModel/getMktActList'] ||
        loading.effects['businessModuleActiveModel/getMngMktActList'],
    optionsLoading: loading.effects['businessModuleActiveModel/initStationOptions'],
}))(BusinessActiveListPage);
