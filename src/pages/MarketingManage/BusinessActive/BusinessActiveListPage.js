import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    TreeSelect,
    Tabs,
    Spin,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    stopMiniBusinessActiveApi,
    deleteMiniBusinessActiveApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { STATUS_TYPES } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './BusinessActive.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';
import ActGroupCheckbox from '@/components/ActGroupCheckbox';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';

const { SHOW_CHILD } = TreeSelect;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        history,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        businessActiveModel: { stationOptions },
        global: { pageInit },
        currentUser,
        optionsLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]) {
            if (pageInit[pathname].form.stationName) {
                stationRef.current.fetchStation(pageInit[pathname].form.stationName);
            }
        }
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const treeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        placeholder: '请选择',
        maxTagCount: 3,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 3}个</span>,
        style: {
            width: '100%',
        },
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onExportForm={onExportForm} onReset={resetForm}>
                <Col span={8}>
                    <FormItem label="活动名称:" name="actName">
                        <Input autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <AllStationSelect
                        form={form}
                        ref={stationRef}
                        label="活动场站"
                        name="stationIds"
                    />
                </Col>

                {currentUser?.operId ? null : (
                    <Col span={8}>
                        <FormItem label="活动类型:" name="belongType">
                            <Select>
                                <Option value={'plat'}>平台</Option>
                                <Option value={'oper'}>运营商</Option>
                                <Option value={'user'}>用户</Option>
                            </Select>
                        </FormItem>
                    </Col>
                )}

                <Col span={8}>
                    {/* <ActGroupCheckbox
                        label="活动人群"
                        form={form}
                        maxTagCount="responsive"
                        valueType="select"
                        allowClear
                    /> */}
                    <ActiveCrowdCheckTree
                        label="活动人群"
                        form={form}
                        maxTagCount="responsive"
                        allowClear
                        hideAll
                        rules={[]}
                    />
                </Col>
                <Col span={8}>
                    <FormItem name="dateTime" label="活动时间范围:" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export const BusinessActiveListLayout = (props) => {
    const {
        dispatch,
        history,
        businessActiveModel: { businessActiveList, businessActiveListTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },

        workorderEvent,
        operId,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );
    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = (isDownload) => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: isDownload ? undefined : pageInfo.pageIndex,
            pageSize: isDownload ? undefined : pageInfo.pageSize,
            stationIds: data.stationIds,
            actName: data.actName,
            activeCrowd: (data.activeCrowd && data.activeCrowd.join(',')) || '',
            belongType: data.belongType || '',
            beginDate: (data.dateTime && data.dateTime[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dateTime && data.dateTime[1].format('YYYY-MM-DD')) || '',
            dateTime: undefined,
            buildId: operId,
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }

        if (isDownload) {
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: '/bil/act/common/oper-mktact-list',
                options: params,
                columnsStr: columnsStrs,
            });
        } else {
            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'businessActiveModel/getBusinessActiveList',
                options: params,
            });
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const stopBusinessActiveEvent = async (item) => {
        confirm({
            title: `确定停止${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (workorderEvent) {
                        workorderEvent('stop', item);
                    } else {
                        await stopMiniBusinessActiveApi(item.actId);
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteBusinessActiveEvent = async (item) => {
        confirm({
            title: `确定删除${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (workorderEvent) {
                        workorderEvent('delete', item);
                        return;
                    } else {
                        await deleteMiniBusinessActiveApi(item.actId);
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // //导出
    // const exportFormEvent = () => {
    //     const data = form.getFieldsValue();
    //     const params = {
    //         pageIndex: pageInfo.pageIndex,
    //         pageSize,
    //         actName: data.actName,
    //         actId: data.actId,
    //         actSubType: data.actSubType,
    //         beginDate:
    //             (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
    //         endDate:
    //             (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
    //     };
    //     if (tabType !== STATUS_TYPES.ALL) {
    //         params.actState = tabType;
    //     }
    //     let columnsStrs = [];
    //     for (const item of columns) {
    //         if (item.dataIndex) {
    //             columnsStrs.push({
    //                 key: item.dataIndex,
    //                 value: item.title,
    //             });
    //         }
    //     }
    //     exportTableByParams({
    //         methodUrl: '/bil/coupon/couponPutList',
    //         options: params,
    //         columnsStr: columnsStrs,
    //     });
    // };

    // 动态获取下级页面
    const getNextPagePath = (type = 'add') => {
        let path;
        if (dynamicMenu) {
            for (let index = 0; index < dynamicMenu?.length; index++) {
                const element = dynamicMenu[index];
                if (element?.path == '/marketing') {
                    for (let x = 0; x < element.routes?.length; x++) {
                        const ele = element.routes[x];
                        if (
                            ele?.path == '/marketing/businessActive/list/plat' ||
                            ele?.path == '/marketing/businessActive/list/user'
                        ) {
                            path = `${ele.path}/${type}`;
                            break;
                        }
                    }
                }
                if (path?.length) {
                    break;
                }
            }
        }

        if (!path?.length) {
            path = `/marketing/businessActive/list/${type}`;
        }
        return path;
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        if (workorderEvent) {
            workorderEvent('add');
            return;
        }
        const path = getNextPagePath('add');
        history.push(path);
    };

    const editBusinessActiveEvent = (item) => {
        if (workorderEvent) {
            workorderEvent('update', item);
            return;
        }
        const path = getNextPagePath('update');
        history.push(`${path}/${item.actId}`);
    };

    const lookBusinessActiveEvent = (item) => {
        if (workorderEvent) {
            workorderEvent('look', item);
            return;
        }
        const path = getNextPagePath('look');
        history.push(`${path}/${item.actId}`);
    };

    const copyBusinessActivePath = (item) => {
        const path = getNextPagePath('copy');
        return `${path}/${item.actId}`;
    };

    const columns = [
        {
            title: '活动时间',
            width: 340,
            dataIndex: 'actTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '活动名称',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '活动类型',
            width: 200,
            dataIndex: 'belongTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动人群',
            width: 200,
            dataIndex: 'activeCrowdName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '营销方式',
            width: 140,
            dataIndex: 'actTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '非会员优惠',
            width: 300,
            dataIndex: 'discountRemark',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '300px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '会员优惠',
            width: 300,
            dataIndex: 'vipDiscountRemark',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '300px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '活动范围',
            width: 200,
            dataIndex: 'cityName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '参与人次',
            width: 140,
            dataIndex: 'joinNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '总优惠金额',
            width: 140,
            dataIndex: 'discountAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 160,
            dataIndex: 'creTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '停止时间',
            width: 160,
            dataIndex: 'closeTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '状态',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (text, record, index) => {
                let controlItems = [];
                const copyPath = copyBusinessActivePath(record);
                switch (record.actState) {
                    case STATUS_TYPES.DRAFT:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => editBusinessActiveEvent(record)}
                            >
                                编辑
                            </span>,
                        );
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookBusinessActiveEvent(record)}
                            >
                                详情
                            </span>,
                        );

                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => deleteBusinessActiveEvent(record)}
                            >
                                删除
                            </span>,
                        );
                        break;
                    case STATUS_TYPES.NOSTART:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => editBusinessActiveEvent(record)}
                            >
                                编辑
                            </span>,
                        );
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookBusinessActiveEvent(record)}
                            >
                                详情
                            </span>,
                        );
                        break;
                    case STATUS_TYPES.DOING:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => editBusinessActiveEvent(record)}
                            >
                                编辑
                            </span>,
                        );
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookBusinessActiveEvent(record)}
                            >
                                详情
                            </span>,
                        );
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => stopBusinessActiveEvent(record)}
                            >
                                停止
                            </span>,
                        );
                        break;
                    case STATUS_TYPES.END:
                    case STATUS_TYPES.STOP:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookBusinessActiveEvent(record)}
                            >
                                详情
                            </span>,
                        );
                        break;
                    default:
                        break;
                }
                controlItems.push(
                    workorderEvent ? (
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                workorderEvent('copy', record);
                            }}
                        >
                            复制
                        </span>
                    ) : (
                        <Link to={copyPath} target="_blank">
                            复制
                        </Link>
                    ),
                );
                return <Space style={{ whiteSpace: 'nowrap' }}>{controlItems}</Space>;
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />
            {workorderEvent ? null : (
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        新建
                    </Button>
                </div>
            )}
            <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                <TabPane tab="未开始" key={STATUS_TYPES.NOSTART} />
                <TabPane tab="进行中" key={STATUS_TYPES.DOING} />
                <TabPane tab="已停止" key={STATUS_TYPES.STOP} />
                <TabPane tab="已结束" key={STATUS_TYPES.END} />
                <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
            </Tabs>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.actId}
                dataSource={businessActiveList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: businessActiveListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                offsetHeader={workorderEvent ? 0 : undefined}
            />
        </Card>
    );
};

const BusinessActiveListPage = (props) => {
    return (
        <PageHeaderWrapper>
            <BusinessActiveListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, login, user, businessActiveModel, loading }) => ({
    global,
    login,
    currentUser: user.currentUser,
    businessActiveModel,
    listLoading: loading.effects['businessActiveModel/getBusinessActiveList'],
    optionsLoading: loading.effects['businessActiveModel/initStationOptions'],
}))(BusinessActiveListPage);
