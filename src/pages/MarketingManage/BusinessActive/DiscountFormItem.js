import { Form, InputNumber, Space } from 'antd';
import { Fragment } from 'react';

import AdjustFormItem from './AdjustFormItem';
import { ADJUST_TYPS, MODELTYPS } from './BusinessConfig';
import { ACT_TYPES as CHANNEL_ACT_TYPES } from '@/constants/channel';
import { isEmpty } from '@/utils/utils';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const FormItem = Form.Item;
const DiscountFormItem = (props) => {
    const {
        actType,
        name,
        disabled,
        formItemLayout,
        hasVip, // 是否有会员配置
        superName,
        belongType,
        typeList,
        form,
        ...otherProps
    } = props;
    let formatName = [];
    if (name && name instanceof Array) [(formatName = formatName.concat(name))];
    const validateName = superName
        ? [superName, ...formatName, 'vipDiscountValue']
        : [...formatName, 'vipDiscountValue'];
    const validateName2 = superName
        ? [superName, ...formatName, 'vthreeDiscountValue']
        : [...formatName, 'vthreeDiscountValue'];
    if (actType == MODELTYPS.DISCOUNT || actType == CHANNEL_ACT_TYPES.DISCOUNT) {
        return (
            <Fragment>
                {/** 费用模板中，如果活动类型为平台，则显示新的文案和增加V3专享价字段，需求：34668 */}
                <FormItem
                    label={belongType === 'plat' ? '普通用户折扣' : '服务费折扣'}
                    required
                    wrapperCol={{ span: 14 }}
                    {...formItemLayout}
                >
                    <Space>
                        <FormItem
                            noStyle
                            name={[...formatName, 'dctValue']}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value && value !== 0.0) {
                                            return Promise.reject('请输入折扣');
                                        }
                                        if (value > 10) {
                                            return Promise.reject('服务费折扣不支持大于10折');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                disabled={disabled}
                                precision={2}
                                step={0.01}
                                min={0}
                                max={10}
                                placeholder="请填写"
                                onChange={() => {
                                    form?.validateFields([validateName, validateName2]);
                                }}
                            />
                        </FormItem>
                        <span className="text-line">折（如填写7.5则表示打7.5折）</span>
                    </Space>
                </FormItem>

                {(hasVip && belongType == 'plat' && (
                    <Fragment>
                        <FormItem
                            label="V3专享折扣"
                            {...formItemLayout}
                            tooltip="会员限高不支持配置V3专享"
                        >
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vthreeDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                //V3非空的时候才校验
                                                if (!isEmpty(value)) {
                                                    const list = getFieldValue(superName);
                                                    const dctValue =
                                                        (superName &&
                                                            list &&
                                                            list[name] &&
                                                            list[name].dctValue) ||
                                                        getFieldValue('dctValue');
                                                    const vipDiscountValue =
                                                        (superName &&
                                                            list &&
                                                            list[name] &&
                                                            list[name].vipDiscountValue) ||
                                                        getFieldValue('vipDiscountValue');
                                                    if (!value && value !== 0.0) {
                                                        return Promise.reject('请输入折扣');
                                                    }
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject('需小于普通用户折扣');
                                                    }
                                                    if (Number(value) <= Number(vipDiscountValue)) {
                                                        return Promise.reject('需大于会员折扣');
                                                    }
                                                    if (Number(value) > 10) {
                                                        return Promise.reject(
                                                            'V3专享折扣不支持大于10折',
                                                        );
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        max={10}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName]);
                                        }}
                                    />
                                </FormItem>
                                折（需小于普通用户折扣，大于会员折扣）
                            </Space>
                        </FormItem>
                        <FormItem label="会员折扣" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                const dctValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].dctValue) ||
                                                    getFieldValue('dctValue');
                                                const vthreeDiscountValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].vthreeDiscountValue) ||
                                                    getFieldValue('vthreeDiscountValue');
                                                // if (!value && value !== 0.0) {
                                                //     return Promise.reject('请输入折扣');
                                                // }
                                                if (!isEmpty(value)) {
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject('需小于普通用户折扣');
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vthreeDiscountValue) &&
                                                        Number(value) >= Number(vthreeDiscountValue)
                                                    ) {
                                                        return Promise.reject('需小于V3专享折扣');
                                                    }
                                                    if (Number(value) > 10) {
                                                        return Promise.reject(
                                                            '会员折扣不支持大于10折',
                                                        );
                                                    }
                                                } else {
                                                    if (!isEmpty(vthreeDiscountValue)) {
                                                        return Promise.reject(
                                                            '已配V3优惠，会员优惠必填',
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        max={10}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                折（需小于V3专享折扣）
                            </Space>
                        </FormItem>
                        <FormItem label="联合会员Pro价" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'proVipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                const dctValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].dctValue) ||
                                                    getFieldValue('dctValue');
                                                const vipDiscountValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].vipDiscountValue) ||
                                                    getFieldValue('vipDiscountValue');
                                                if (!isEmpty(value)) {
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject('需小于普通用户折扣');
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vipDiscountValue) &&
                                                        Number(value) >= Number(vipDiscountValue)
                                                    ) {
                                                        return Promise.reject('需小于会员折扣');
                                                    }
                                                    if (Number(value) > 10) {
                                                        return Promise.reject(
                                                            '联合会员Pro折扣不支持大于10折',
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        max={10}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                折（需小于会员折扣，仅在支付宝小程序生效）
                            </Space>
                        </FormItem>
                    </Fragment>
                )) ||
                    null}
            </Fragment>
        );
    }
    if (actType == MODELTYPS.REDUCTION || actType == CHANNEL_ACT_TYPES.REDUCTION) {
        return (
            <Fragment>
                <FormItem
                    label={belongType === 'plat' ? '普通用户立减' : '服务费立减'}
                    required
                    {...formItemLayout}
                >
                    <Space>
                        <FormItem
                            noStyle
                            name={[...formatName, 'dctValue']}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value && value !== 0.0) {
                                            return Promise.reject('请输入金额');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                disabled={disabled}
                                precision={4}
                                step={0.0001}
                                min={0}
                                max={99999}
                                placeholder="请填写"
                                onChange={() => {
                                    form?.validateFields([validateName, validateName2]);
                                }}
                            />
                        </FormItem>
                        元/度
                    </Space>
                </FormItem>
                {(hasVip && belongType == 'plat' && (
                    <Fragment>
                        <FormItem
                            label="V3专享立减"
                            {...formItemLayout}
                            tooltip="会员限高不支持配置V3专享"
                        >
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vthreeDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                //V3非空的时候才校验
                                                if (!isEmpty(value)) {
                                                    const list = getFieldValue(superName);
                                                    let dctValue = 0;
                                                    if (
                                                        list &&
                                                        list[name] &&
                                                        list[name].dctValue >= 0
                                                    ) {
                                                        dctValue = list[name].dctValue;
                                                    } else {
                                                        dctValue = getFieldValue('dctValue');
                                                    }
                                                    let vipDiscountValue = 0;
                                                    if (
                                                        list &&
                                                        list[name] &&
                                                        list[name].vipDiscountValue >= 0
                                                    ) {
                                                        vipDiscountValue =
                                                            list[name].vipDiscountValue;
                                                    } else {
                                                        vipDiscountValue =
                                                            getFieldValue('vipDiscountValue');
                                                    }
                                                    if (!value && value !== 0.0) {
                                                        return Promise.reject('请输入金额');
                                                    }

                                                    if (Number(value) <= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需大于普通用户立减金额',
                                                        );
                                                    }
                                                    if (Number(value) >= Number(vipDiscountValue)) {
                                                        return Promise.reject('需小于会员立减金额');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        max={99999}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需大于普通用户立减金额，小于会员立减金额）
                            </Space>
                        </FormItem>
                        <FormItem label="会员立减" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                let dctValue = 0;
                                                if (
                                                    list &&
                                                    list[name] &&
                                                    list[name].dctValue >= 0
                                                ) {
                                                    dctValue = list[name].dctValue;
                                                } else {
                                                    dctValue = getFieldValue('dctValue');
                                                }
                                                let vthreeDiscountValue = 0;
                                                if (
                                                    list &&
                                                    list[name] &&
                                                    list[name].vthreeDiscountValue >= 0
                                                ) {
                                                    vthreeDiscountValue =
                                                        list[name].vthreeDiscountValue;
                                                } else {
                                                    vthreeDiscountValue =
                                                        getFieldValue('vthreeDiscountValue');
                                                }
                                                // if (!value && value !== 0.0) {
                                                //     return Promise.reject('请输入金额');
                                                // }
                                                if (!isEmpty(value)) {
                                                    if (Number(value) <= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需大于普通用户立减金额',
                                                        );
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vthreeDiscountValue) &&
                                                        Number(value) <= Number(vthreeDiscountValue)
                                                    ) {
                                                        return Promise.reject(
                                                            '需大于V3专享立减金额',
                                                        );
                                                    }
                                                } else {
                                                    if (!isEmpty(vthreeDiscountValue)) {
                                                        return Promise.reject(
                                                            '已配V3优惠，会员优惠必填',
                                                        );
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        max={99999}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需大于V3专享立减金额）
                            </Space>
                        </FormItem>
                        <FormItem label="联合会员Pro价" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'proVipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                let dctValue = 0;
                                                if (
                                                    list &&
                                                    list[name] &&
                                                    list[name].dctValue >= 0
                                                ) {
                                                    dctValue = list[name].dctValue;
                                                } else {
                                                    dctValue = getFieldValue('dctValue');
                                                }
                                                let vipDiscountValue = 0;
                                                if (
                                                    list &&
                                                    list[name] &&
                                                    list[name].vthreeDiscountValue >= 0
                                                ) {
                                                    vipDiscountValue = list[name].vipDiscountValue;
                                                } else {
                                                    vipDiscountValue =
                                                        getFieldValue('vipDiscountValue');
                                                }
                                                if (!isEmpty(value)) {
                                                    if (Number(value) <= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需大于普通用户立减金额',
                                                        );
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vipDiscountValue) &&
                                                        Number(value) <= Number(vipDiscountValue)
                                                    ) {
                                                        return Promise.reject('需大于会员立减金额');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        max={99999}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需大于等于会员立减，仅在支付宝小程序生效）
                            </Space>
                        </FormItem>
                    </Fragment>
                )) ||
                    null}
            </Fragment>
        );
    }
    if (actType == MODELTYPS.FULL) {
        return (
            <Fragment>
                <FormItem label="满" required wrapperCol={{ span: 14 }}>
                    <Space>
                        <FormItem
                            noStyle
                            name={[...formatName, 'dctCondValue']}
                            rules={[
                                {
                                    required: true,

                                    message: '请填写',
                                },
                            ]}
                        >
                            <InputNumber
                                disabled={disabled}
                                precision={4}
                                step={0.0001}
                                min={0}
                                max={99999}
                                placeholder="请填写"
                            />
                        </FormItem>
                        元立减
                        <FormItem
                            noStyle
                            name={[...formatName, 'dctValue']}
                            rules={[
                                {
                                    required: true,

                                    message: '请填写',
                                },
                            ]}
                        >
                            <InputNumber
                                disabled={disabled}
                                precision={4}
                                step={0.0001}
                                min={0}
                                max={99999}
                                placeholder="请填写"
                            />
                        </FormItem>
                        元
                    </Space>
                </FormItem>
            </Fragment>
        );
    }
    if (actType === MODELTYPS.DYNAMIC && belongType === 'plat') {
        return (
            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.adjustPriceType !== curValues.adjustPriceType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const adjustPriceType = getFieldValue('adjustPriceType');
                    // 屏蔽：改为禁用的方式，否则调整顺序或者切换价格基准时label会丢失，显示出code
                    // let adjustTypeList = typeList;
                    // if (adjustPriceType !== '02') {
                    //     //如果不是按原价时 过滤动态打折类型的选项
                    //     adjustTypeList =
                    //         typeList?.filter((ele) => ele.codeValue !== ADJUST_TYPS.DYNAMIC) || [];
                    // }
                    return (
                        <Fragment>
                            <AdjustFormItem
                                label="服务费优惠"
                                adjustPriceType={adjustPriceType}
                                typeList={typeList}
                                name={[...formatName, 'adjustDetailList']}
                                parentName={superName || []}
                                disabled={disabled}
                                rules={[
                                    { required: true, message: '请配置服务费优惠方案' },
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (value && value?.length > 0) {
                                                const adjustPriceType =
                                                    getFieldValue('adjustPriceType');

                                                if (adjustPriceType == ADJUST_PRICE_TYPES.SETTLE) {
                                                    if (
                                                        value?.some(
                                                            (ele) =>
                                                                ele.adjustType ==
                                                                ADJUST_TYPS.DISCOUNT,
                                                        )
                                                    ) {
                                                        return Promise.reject(
                                                            '配置结算价不可配置打折优惠',
                                                        );
                                                    }
                                                    if (value?.length > 1) {
                                                        return Promise.reject(
                                                            '配置结算价仅可配置一种优惠方式',
                                                        );
                                                    }
                                                } else if (
                                                    adjustPriceType !==
                                                    ADJUST_PRICE_TYPES.OPER_ORIGIN
                                                ) {
                                                    //原价才有动态折扣可以配置
                                                    let dynamicItem = value?.find((ele) => {
                                                        return (
                                                            ele?.adjustType === ADJUST_TYPS.DYNAMIC
                                                        );
                                                    });
                                                    if (dynamicItem) {
                                                        return Promise.reject(
                                                            '价格基准为原价时才可配置动态打折优惠',
                                                        );
                                                    }
                                                }

                                                const hasV3Item = value?.find(
                                                    (ele) => !isEmpty(ele.adjustThreeDiscountValue),
                                                );
                                                if (hasV3Item) {
                                                    const hasV3ItemList = value?.filter(
                                                        (ele) =>
                                                            !isEmpty(ele.adjustThreeDiscountValue),
                                                    );
                                                    if (hasV3ItemList.length !== value?.length) {
                                                        return Promise.reject(
                                                            `配置了V3专享价，则其余类型的优惠也需要配置V3专享价`,
                                                        );
                                                    }
                                                }
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                                required
                                form={form}
                                {...formItemLayout}
                                wrapperCol={{ span: 24 }}
                                {...otherProps}
                            ></AdjustFormItem>
                            <br></br>
                        </Fragment>
                    );
                }}
            </FormItem>
        );
    }
    if (actType === MODELTYPS.PRICE) {
        return (
            <Fragment>
                <FormItem
                    label={belongType === 'plat' ? '普通用户一口价' : '服务费一口价'}
                    required
                    wrapperCol={{ span: 14 }}
                    {...formItemLayout}
                >
                    <Space>
                        <FormItem
                            noStyle
                            name={[...formatName, 'dctValue']}
                            rules={[
                                {
                                    required: true,
                                    message: `请输入金额`,
                                },
                            ]}
                        >
                            <InputNumber
                                precision={4}
                                step={0.0001}
                                min={0}
                                placeholder="请填写"
                                onChange={() => {
                                    form?.validateFields([validateName, validateName2]);
                                }}
                            />
                        </FormItem>
                        <span className="text-line">
                            元/度 {belongType === 'plat' ? '（配置服务费一口价）' : ''}
                        </span>
                    </Space>
                </FormItem>

                {(hasVip && belongType == 'plat' && (
                    <Fragment>
                        <FormItem label="V3专享一口价" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vthreeDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                //V3非空的时候才校验
                                                if (!isEmpty(value)) {
                                                    const list = getFieldValue(superName);
                                                    const dctValue =
                                                        (superName &&
                                                            list &&
                                                            list[name] &&
                                                            list[name].dctValue) ||
                                                        getFieldValue('dctValue');
                                                    const vipDiscountValue =
                                                        (superName &&
                                                            list &&
                                                            list[name] &&
                                                            list[name].vipDiscountValue) ||
                                                        getFieldValue('vipDiscountValue');
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需小于普通用户一口价金额',
                                                        );
                                                    }
                                                    if (Number(value) <= Number(vipDiscountValue)) {
                                                        return Promise.reject('需大于会员一口价');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需小于普通用户一口价，大于会员一口价）
                            </Space>
                        </FormItem>
                        <FormItem label="会员一口价" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'vipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                const dctValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].dctValue) ||
                                                    getFieldValue('dctValue');
                                                const vthreeDiscountValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].vthreeDiscountValue) ||
                                                    getFieldValue('vthreeDiscountValue');

                                                if (!isEmpty(value)) {
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需小于普通用户一口价',
                                                        );
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vthreeDiscountValue) &&
                                                        Number(value) >= Number(vthreeDiscountValue)
                                                    ) {
                                                        return Promise.reject('需小于V3专享一口价');
                                                    }
                                                } else {
                                                    if (!isEmpty(vthreeDiscountValue)) {
                                                        return Promise.reject(
                                                            '已配V3专享一口价，会员一口价必填',
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需小于V3专享一口价）
                            </Space>
                        </FormItem>
                        <FormItem label="联合会员Pro价" {...formItemLayout}>
                            <Space>
                                <FormItem
                                    noStyle
                                    name={[...formatName, 'proVipDiscountValue']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const list = getFieldValue(superName);
                                                const dctValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].dctValue) ||
                                                    getFieldValue('dctValue');
                                                const vipDiscountValue =
                                                    (superName &&
                                                        list &&
                                                        list[name] &&
                                                        list[name].vipDiscountValue) ||
                                                    getFieldValue('vipDiscountValue');

                                                if (!isEmpty(value)) {
                                                    if (Number(value) >= Number(dctValue)) {
                                                        return Promise.reject(
                                                            '需小于普通用户一口价',
                                                        );
                                                    }
                                                    //V3非空的时候才校验
                                                    if (
                                                        !isEmpty(vipDiscountValue) &&
                                                        Number(value) >= Number(vipDiscountValue)
                                                    ) {
                                                        return Promise.reject('需小于会员一口价');
                                                    }
                                                } else {
                                                    if (!isEmpty(vipDiscountValue)) {
                                                        return Promise.reject(
                                                            '已配会员一口价，联合会员Pro一口价必填',
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        disabled={disabled}
                                        precision={4}
                                        step={0.0001}
                                        min={0}
                                        placeholder="请填写"
                                        onChange={() => {
                                            form?.validateFields([validateName2]);
                                        }}
                                    />
                                </FormItem>
                                元/度（需小于会员一口价，仅在支付宝小程序生效）
                            </Space>
                        </FormItem>
                    </Fragment>
                )) ||
                    null}
            </Fragment>
        );
    }
    return null;
};
export default DiscountFormItem;
