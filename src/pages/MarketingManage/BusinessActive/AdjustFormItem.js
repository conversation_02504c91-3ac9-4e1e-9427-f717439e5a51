import {
    UpCircleOutlined,
    DownCircleOutlined,
    PlusCircleOutlined,
    CloseCircleOutlined,
    InfoCircleOutlined,
} from '@ant-design/icons';
import {
    Form,
    Radio,
    InputNumber,
    Space,
    Select,
    Row,
    Col,
    Divider,
    Tooltip,
    message,
    Typography,
} from 'antd';
import { Fragment, useMemo } from 'react';

import { isEmpty } from '@/utils/utils';
import { ADJUST_TYPS } from './BusinessConfig';
import { ADJUST_PRICE_TYPES } from '@/config/declare';

const FormItem = Form.Item;
const { Option } = Select;
const iconSize = 24;

const formatAdjustTypeName = (adjustType) => {
    let name = '';
    switch (adjustType) {
        case ADJUST_TYPS.ADD:
            name = '立加优惠的';
            break;
        case ADJUST_TYPS.REDUCE:
            name = '立减优惠的';
            break;
        case ADJUST_TYPS.DISCOUNT:
            name = '折扣优惠的';
            break;
        case ADJUST_TYPS.DYNAMIC:
            name = '动态优惠的';
            break;

        default:
            break;
    }
    return name;
};

const SelectBefore = (props) => {
    const { typeName, name, validateName, disabled, onChange, adjustPriceType } = props;

    return (
        <Space size={4}>
            {(adjustPriceType != ADJUST_PRICE_TYPES.SETTLE && (
                <span style={{ fontSize: '12px' }}>(结算价/原价)</span>
            )) ||
                null}

            <FormItem
                name={[...name, typeName]}
                noStyle
                initialValue={'0'}
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (typeName === 'proVipMethodType' && value === '1') {
                                const adjustMethodType = getFieldValue([
                                    ...validateName,
                                    'adjustMethodType',
                                ]);
                                const vthreeMethodType = getFieldValue([
                                    ...validateName,
                                    'vthreeMethodType',
                                ]);
                                const vipMethodType = getFieldValue([
                                    ...validateName,
                                    'vipAdjustMethodType',
                                ]);
                                let preError = '';
                                if (validateName?.length > 2) {
                                    preError = `时段${validateName[1] + 1}`;
                                }
                                if (adjustMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}普通用户下调价格时，联合会员Pro不能上调价格`,
                                    );
                                }
                                if (vthreeMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}V3专享下调价格时，联合会员Pro不能上调价格`,
                                    );
                                }
                                if (vipMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}会员下调价格时，联合会员Pro不能上调价格`,
                                    );
                                }
                            }
                            if (typeName === 'vipAdjustMethodType' && value === '1') {
                                const adjustMethodType = getFieldValue([
                                    ...validateName,
                                    'adjustMethodType',
                                ]);
                                const vthreeMethodType = getFieldValue([
                                    ...validateName,
                                    'vthreeMethodType',
                                ]);
                                let preError = '';
                                if (validateName?.length > 2) {
                                    preError = `时段${validateName[1] + 1}`;
                                }
                                if (adjustMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}普通用户下调价格时，会员不能上调价格`,
                                    );
                                }
                                if (vthreeMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}V3专享下调价格时，会员不能上调价格`,
                                    );
                                }
                            }
                            if (typeName === 'vthreeMethodType' && value === '1') {
                                const adjustMethodType = getFieldValue([
                                    ...validateName,
                                    'adjustMethodType',
                                ]);
                                let preError = '';
                                if (validateName?.length > 2) {
                                    preError = `时段${validateName[1] + 1}`;
                                }
                                if (adjustMethodType == 0) {
                                    return Promise.reject(
                                        `${preError}普通用户下调价格时，V3专享不能上调价格`,
                                    );
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Radio.Group
                    disabled={disabled}
                    onChange={() => {
                        onChange && onChange();
                    }}
                    size="small"
                >
                    <Radio value="1">+</Radio>
                    <Radio value="0">-</Radio>
                </Radio.Group>
            </FormItem>
        </Space>
    );
};

const AdjustFormItem = (props) => {
    const {
        name = '', // name是表单name属性
        label = '服务费调价',
        adjustPriceType, //价格基准类型
        typeList = [
            {
                codeValue: ADJUST_TYPS.DISCOUNT,
                codeName: '打折',
            },
            {
                codeValue: ADJUST_TYPS.REDUCE,
                codeName: '立减',
            },
            {
                codeValue: ADJUST_TYPS.ADD,
                codeName: '立加',
            },
            {
                codeValue: ADJUST_TYPS.DYNAMIC,
                codeName: '动态打折',
            },
        ],
        parentName = [], //父级完整名称
        disabled,
        rules = [],
        form,
        ...otherOptions
    } = props;
    let formItemName = [];
    if (typeof name === 'string') {
        formItemName = [name];
    } else if (name instanceof Array) {
        formItemName = name;
    }

    const validateName = parentName ? [...parentName, ...formItemName] : [...formItemName]; //表单校验的name 由父级和本身name组合 如果是list组件父级只需要传list的名字不需要带下标

    const setFormValues = (values) => {
        if (parentName?.length) {
            let index = 0;
            const superName = validateName[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < validateName.length) {
                if (!obj) {
                    obj = superObj[validateName[index]];
                } else {
                    obj = obj[validateName[index]];
                }
            }

            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });

            form.setFieldsValue({ [superName]: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    const maxLimit = useMemo(() => {
        if (adjustPriceType === ADJUST_PRICE_TYPES.OPER_ORIGIN) {
            return 4;
        }
        if (adjustPriceType === ADJUST_PRICE_TYPES.SETTLE) {
            return 1;
        }
        return 3;
    }, [adjustPriceType]);

    const canIadd = () => {
        let hasAdd = true;
        const formItemData = form.getFieldValue(formItemName);
        if (!isEmpty(formItemData)) {
            if (formItemData.length >= maxLimit) {
                hasAdd = false;
            }
        }
        return !disabled && hasAdd;
    };

    return (
        <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
            {({ getFieldValue }) => {
                const listData = form.getFieldValue(validateName);
                const swapItems = (arr, index1, index2) => {
                    arr[index1] = arr.splice(index2, 1, arr[index1])[0];
                    return arr;
                };

                const upperEvent = (indexPos) => {
                    if (indexPos == 0) {
                        return;
                    }
                    const newListData = swapItems(listData, indexPos, indexPos - 1);
                    setFormValues({ [formItemName[formItemName.length - 1]]: newListData });
                };
                const downEvent = (indexPos) => {
                    if (indexPos == listData.length - 1) {
                        return;
                    }
                    const newListData = swapItems(listData, indexPos, indexPos + 1);

                    setFormValues({ [formItemName[formItemName.length - 1]]: newListData });
                };

                const hasDynamic = listData?.find((ele) => ele?.adjustType === ADJUST_TYPS.DYNAMIC);
                return (
                    <FormItem {...otherOptions} label={label}>
                        <Row gutter={12}>
                            <Col span={2}>
                                类型
                                {adjustPriceType == ADJUST_PRICE_TYPES.SETTLE ? null : (
                                    <Tooltip title="动态打折：若场站没有结算价只有原价，则动态打折不生效">
                                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                    </Tooltip>
                                )}
                            </Col>
                            <Col span={5}>普通用户</Col>
                            <Col span={5}>
                                <Space size="small">
                                    <Typography.Text>V3专享</Typography.Text>
                                    <Tooltip title="会员限高不支持配置V3专享"></Tooltip>
                                    <InfoCircleOutlined />
                                </Space>
                            </Col>
                            <Col span={5}>会员</Col>
                            <Col span={5}>联合会员Pro价</Col>
                            <Col span={2}>
                                操作
                                <Tooltip title="上移、下移将影响计算顺序">
                                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                </Tooltip>
                            </Col>
                        </Row>
                        <FormItem
                            name={[...formItemName]}
                            initialValue={[
                                {
                                    adjustType: ADJUST_TYPS.DISCOUNT,
                                },
                            ]}
                            rules={[
                                ...rules,
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (value) {
                                            let errList = [];
                                            for (const item of value) {
                                                let errTitle = '';
                                                let errorPreMessage = '';

                                                if (formItemName?.length > 1) {
                                                    errTitle = `时段${
                                                        formItemName[formItemName.length - 2] + 1
                                                    }`;
                                                }
                                                if (!item.adjustType) {
                                                    errorPreMessage += '优惠类型不能为空 ';
                                                    errList.push(errTitle + errorPreMessage);
                                                    break;
                                                }
                                                if (isEmpty(item.adjustValue)) {
                                                    errorPreMessage += '普通用户优惠值不能为空 ';
                                                }
                                                if (isEmpty(item.adjustVipValue)) {
                                                    errorPreMessage += '会员优惠值不能为空 ';
                                                }

                                                const adjustType = item.adjustType;
                                                const adjustValue = item.adjustValue;
                                                const adjustThreeDiscountValue =
                                                    item.adjustThreeDiscountValue;
                                                const adjustVipValue = item.adjustVipValue;
                                                const proVipDiscountValue =
                                                    item.proVipDiscountValue;
                                                const adjustMethodType = item.adjustMethodType;
                                                const vthreeMethodType = item.vthreeMethodType;
                                                const vipAdjustMethodType =
                                                    item.vipAdjustMethodType;
                                                const proVipMethodType = item.proVipMethodType;

                                                if (adjustType === ADJUST_TYPS.DISCOUNT) {
                                                    if (isEmpty(adjustThreeDiscountValue)) {
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustValue) <
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需小于等于普通用户折扣 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) <=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需小于会员折扣 ';
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        if (
                                                            Number(adjustValue) <
                                                            Number(adjustThreeDiscountValue)
                                                        ) {
                                                            errorPreMessage +=
                                                                'V3专享需小于等于普通用户折扣 ';
                                                        }
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustThreeDiscountValue) <
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需小于等于V3专享折扣 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) <=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需小于会员折扣 ';
                                                                }
                                                            }
                                                        } else {
                                                            return Promise.reject(
                                                                '已配V3优惠，会员优惠必填',
                                                            );
                                                        }
                                                    }
                                                }
                                                if (adjustType === ADJUST_TYPS.REDUCE) {
                                                    if (isEmpty(adjustThreeDiscountValue)) {
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustValue) >
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需大于等于普通用户立减金额 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) >=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需大于会员立减金额 ';
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        if (
                                                            Number(adjustValue) >
                                                            Number(adjustThreeDiscountValue)
                                                        ) {
                                                            errorPreMessage +=
                                                                'V3专享需大于等于普通用户立减金额 ';
                                                        }
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustThreeDiscountValue) >
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需大于等于V3专享立减金额 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) >=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需大于会员立减金额 ';
                                                                }
                                                            }
                                                        } else {
                                                            return Promise.reject(
                                                                '已配V3优惠，会员优惠必填',
                                                            );
                                                        }
                                                    }
                                                }
                                                if (adjustType === ADJUST_TYPS.ADD) {
                                                    if (isEmpty(adjustThreeDiscountValue)) {
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustValue) <
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需小于等于普通用户立加金额 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) <=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需小于会员立加金额 ';
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        if (
                                                            Number(adjustValue) <
                                                            Number(adjustThreeDiscountValue)
                                                        ) {
                                                            errorPreMessage +=
                                                                'V3专享需小于等于普通用户立加金额 ';
                                                        }
                                                        if (!isEmpty(adjustVipValue)) {
                                                            if (
                                                                Number(adjustThreeDiscountValue) <
                                                                Number(adjustVipValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '会员需小于等于V3专享立加金额 ';
                                                            }
                                                            if (!isEmpty(proVipDiscountValue)) {
                                                                if (
                                                                    Number(adjustVipValue) <=
                                                                    Number(proVipDiscountValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '联合会员Pro价需小于会员立加金额 ';
                                                                }
                                                            }
                                                        } else {
                                                            return Promise.reject(
                                                                '已配V3立加，会员立加必填',
                                                            );
                                                        }
                                                    }
                                                }

                                                if (adjustType === ADJUST_TYPS.DYNAMIC) {
                                                    if (
                                                        adjustMethodType === '0' &&
                                                        (vthreeMethodType === '0' ||
                                                            isEmpty(vthreeMethodType)) &&
                                                        vipAdjustMethodType === '0' &&
                                                        proVipMethodType === '0'
                                                    ) {
                                                        if (isEmpty(adjustThreeDiscountValue)) {
                                                            if (!isEmpty(adjustVipValue)) {
                                                                if (
                                                                    Number(adjustValue) >
                                                                    Number(adjustVipValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '加大打折力度时，会员需大于等于普通用户填写值 ';
                                                                }
                                                                if (!isEmpty(proVipDiscountValue)) {
                                                                    if (
                                                                        Number(adjustVipValue) >=
                                                                        Number(proVipDiscountValue)
                                                                    ) {
                                                                        errorPreMessage +=
                                                                            '加大打折力度时，联合会员Pro价需大于会员填写值 ';
                                                                    }
                                                                }
                                                            }
                                                        } else {
                                                            if (
                                                                Number(adjustValue) >
                                                                Number(adjustThreeDiscountValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '加大打折力度时，V3专享需大于等于普通用户填写值 ';
                                                            }
                                                            if (!isEmpty(adjustVipValue)) {
                                                                if (
                                                                    Number(
                                                                        adjustThreeDiscountValue,
                                                                    ) > Number(adjustVipValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '加大打折力度时，会员需大于等于V3专享填写值 ';
                                                                }
                                                                if (!isEmpty(proVipDiscountValue)) {
                                                                    if (
                                                                        Number(adjustVipValue) >=
                                                                        Number(proVipDiscountValue)
                                                                    ) {
                                                                        errorPreMessage +=
                                                                            '加大打折力度时，联合会员Pro价需大于会员填写值 ';
                                                                    }
                                                                }
                                                            } else {
                                                                return Promise.reject(
                                                                    '已配V3优惠，会员优惠必填',
                                                                );
                                                            }
                                                        }
                                                    } else if (
                                                        adjustMethodType === '1' &&
                                                        (vthreeMethodType === '1' ||
                                                            isEmpty(vthreeMethodType)) &&
                                                        vipAdjustMethodType === '1' &&
                                                        proVipMethodType === '1'
                                                    ) {
                                                        if (isEmpty(adjustThreeDiscountValue)) {
                                                            if (!isEmpty(adjustVipValue)) {
                                                                if (
                                                                    Number(adjustValue) <
                                                                    Number(adjustVipValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '降低打折力度时，会员需小于等于普通用户填写值 ';
                                                                }
                                                                if (!isEmpty(proVipDiscountValue)) {
                                                                    if (
                                                                        Number(adjustVipValue) <=
                                                                        Number(proVipDiscountValue)
                                                                    ) {
                                                                        errorPreMessage +=
                                                                            '降低打折力度时，联合会员Pro价需小于会员填写值 ';
                                                                    }
                                                                }
                                                            }
                                                        } else {
                                                            if (
                                                                Number(adjustValue) <
                                                                Number(adjustThreeDiscountValue)
                                                            ) {
                                                                errorPreMessage +=
                                                                    '降低打折力度时，V3专享需小于等于普通用户填写值 ';
                                                            }
                                                            if (!isEmpty(adjustVipValue)) {
                                                                if (
                                                                    Number(
                                                                        adjustThreeDiscountValue,
                                                                    ) < Number(adjustVipValue)
                                                                ) {
                                                                    errorPreMessage +=
                                                                        '降低打折力度时，会员需小于等于V3专享填写值 ';
                                                                }
                                                                if (!isEmpty(proVipDiscountValue)) {
                                                                    if (
                                                                        Number(adjustVipValue) <=
                                                                        Number(proVipDiscountValue)
                                                                    ) {
                                                                        errorPreMessage +=
                                                                            '降低打折力度时，联合会员Pro价需小于会员填写值 ';
                                                                    }
                                                                }
                                                            } else {
                                                                return Promise.reject(
                                                                    '已配V3优惠，会员优惠必填',
                                                                );
                                                            }
                                                        }
                                                    }
                                                }

                                                errTitle += formatAdjustTypeName(item.adjustType);
                                                if (errorPreMessage?.length > 0) {
                                                    errList.push(errTitle + errorPreMessage);
                                                }
                                            }
                                            if (errList?.length > 0) {
                                                return Promise.reject(errList);
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            noStyle
                        >
                            <Form.List name={[...formItemName]}>
                                {(fields, { add, remove }) => {
                                    return fields.map((field, index) => {
                                        const adjustType =
                                            (listData &&
                                                listData[index] &&
                                                listData[index]?.adjustType) ||
                                            ADJUST_TYPS.DISCOUNT;
                                        const isDiscount =
                                            adjustType === ADJUST_TYPS.ADD ||
                                            adjustType === ADJUST_TYPS.REDUCE
                                                ? false
                                                : true;

                                        const typeOptions =
                                            typeList?.map((ele) => {
                                                let typeDisabled = listData?.find(
                                                    (element) =>
                                                        element?.adjustType === ele?.codeValue,
                                                );
                                                if (adjustPriceType == ADJUST_PRICE_TYPES.SETTLE) {
                                                    // 配置结算价，支持动态打折、立加、立减类型
                                                    typeDisabled = ![
                                                        ADJUST_TYPS.REDUCE,
                                                        ADJUST_TYPS.ADD,
                                                        ADJUST_TYPS.DYNAMIC,
                                                    ].includes(ele.codeValue);
                                                } else if (
                                                    adjustPriceType !==
                                                        ADJUST_PRICE_TYPES.OPER_ORIGIN &&
                                                    ele.codeValue == ADJUST_TYPS.DYNAMIC
                                                ) {
                                                    // 动态打折，在价格基准是原价时可选，其他情况不可选中
                                                    typeDisabled = true;
                                                }
                                                return (
                                                    <Option
                                                        disabled={typeDisabled}
                                                        value={ele.codeValue}
                                                        key={ele.codeValue}
                                                    >
                                                        {ele.codeName}
                                                    </Option>
                                                );
                                            }) || [];

                                        const numberConifg = isDiscount
                                            ? {
                                                  max: 10,
                                                  min: 0,
                                              }
                                            : {
                                                  min: 0,
                                              };

                                        let errorPreMessage = '';

                                        if (formItemName?.length > 1) {
                                            errorPreMessage = `时段${
                                                formItemName[formItemName.length - 2] + 1
                                            }`;
                                        }

                                        errorPreMessage += formatAdjustTypeName(adjustType);

                                        return (
                                            <Fragment key={index}>
                                                <Divider style={{ marginTop: '8px' }}></Divider>
                                                <Row gutter={12}>
                                                    <FormItem
                                                        name={[field.name, 'adjustSn']}
                                                        noStyle
                                                        initialValue={index}
                                                    ></FormItem>
                                                    <Col span={2}>
                                                        <FormItem
                                                            name={[field.name, 'adjustType']}
                                                            noStyle
                                                        >
                                                            <Select
                                                                disabled={disabled}
                                                                onChange={() => {
                                                                    form.validateFields([
                                                                        validateName,
                                                                    ]);
                                                                }}
                                                            >
                                                                {typeOptions}
                                                            </Select>
                                                        </FormItem>
                                                    </Col>
                                                    <Col span={5}>
                                                        <Space size="small">
                                                            <FormItem
                                                                name={[field.name, 'adjustValue']}
                                                                noStyle
                                                            >
                                                                <InputNumber
                                                                    addonBefore={
                                                                        (adjustType ===
                                                                            ADJUST_TYPS.DYNAMIC && (
                                                                            <SelectBefore
                                                                                validateName={
                                                                                    validateName
                                                                                }
                                                                                typeName={
                                                                                    'adjustMethodType'
                                                                                }
                                                                                name={[field.name]}
                                                                                disabled={disabled}
                                                                                onChange={() => {
                                                                                    form.validateFields(
                                                                                        [
                                                                                            validateName,
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'adjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vipAdjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vthreeMethodType',
                                                                                            ],
                                                                                        ],
                                                                                    );
                                                                                }}
                                                                                adjustPriceType={
                                                                                    adjustPriceType
                                                                                }
                                                                            />
                                                                        )) ||
                                                                        null
                                                                    }
                                                                    precision={isDiscount ? 2 : 4}
                                                                    step={
                                                                        isDiscount ? 0.01 : 0.0001
                                                                    }
                                                                    {...numberConifg}
                                                                    disabled={disabled}
                                                                    placeholder="请填写"
                                                                    onChange={() => {
                                                                        form.validateFields([
                                                                            validateName,
                                                                        ]);
                                                                    }}
                                                                ></InputNumber>
                                                            </FormItem>
                                                            {(isDiscount && '折') || '元/度'}
                                                            {adjustType ===
                                                                ADJUST_TYPS.DISCOUNT && (
                                                                <Tooltip title="如填写7.5则表示打7.5折">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.DYNAMIC && (
                                                                <Tooltip title="通过算出运营商的折扣，在折扣的基础上加大/降低打折力度">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                        </Space>
                                                    </Col>
                                                    <Col span={5}>
                                                        <Space size="small">
                                                            <FormItem
                                                                name={[
                                                                    field.name,
                                                                    'adjustThreeDiscountValue',
                                                                ]}
                                                                noStyle
                                                            >
                                                                <InputNumber
                                                                    addonBefore={
                                                                        (adjustType ===
                                                                            ADJUST_TYPS.DYNAMIC && (
                                                                            <SelectBefore
                                                                                validateName={[
                                                                                    ...validateName,
                                                                                    field.name,
                                                                                ]}
                                                                                typeName={
                                                                                    'vthreeMethodType'
                                                                                }
                                                                                name={[field.name]}
                                                                                disabled={disabled}
                                                                                onChange={() => {
                                                                                    form.validateFields(
                                                                                        [
                                                                                            validateName,
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'proVipMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vipAdjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'adjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vthreeMethodType',
                                                                                            ],
                                                                                        ],
                                                                                    );
                                                                                }}
                                                                                adjustPriceType={
                                                                                    adjustPriceType
                                                                                }
                                                                            />
                                                                        )) ||
                                                                        null
                                                                    }
                                                                    precision={isDiscount ? 2 : 4}
                                                                    step={
                                                                        isDiscount ? 0.01 : 0.0001
                                                                    }
                                                                    {...numberConifg}
                                                                    disabled={disabled}
                                                                    placeholder={`请填写`}
                                                                    onChange={() => {
                                                                        form.validateFields([
                                                                            validateName,
                                                                        ]);
                                                                    }}
                                                                ></InputNumber>
                                                            </FormItem>
                                                            {(isDiscount && '折') || '元/度'}
                                                            {adjustType ===
                                                                ADJUST_TYPS.DISCOUNT && (
                                                                <Tooltip title="需小于普通用户折扣，大于等于会员折扣">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.REDUCE && (
                                                                <Tooltip title="需大于普通用户立减金额，小于等于会员立减金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.ADD && (
                                                                <Tooltip title="需小于普通用户立加金额，大于等于会员立加金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                        </Space>
                                                    </Col>
                                                    <Col span={5}>
                                                        <Space size="small">
                                                            <FormItem
                                                                name={[
                                                                    field.name,
                                                                    'adjustVipValue',
                                                                ]}
                                                                noStyle
                                                            >
                                                                <InputNumber
                                                                    addonBefore={
                                                                        (adjustType ===
                                                                            ADJUST_TYPS.DYNAMIC && (
                                                                            <SelectBefore
                                                                                validateName={[
                                                                                    ...validateName,
                                                                                    field.name,
                                                                                ]}
                                                                                typeName={
                                                                                    'vipAdjustMethodType'
                                                                                }
                                                                                name={[field.name]}
                                                                                disabled={disabled}
                                                                                onChange={() => {
                                                                                    form.validateFields(
                                                                                        [
                                                                                            validateName,
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vipAdjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'adjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vthreeMethodType',
                                                                                            ],
                                                                                        ],
                                                                                    );
                                                                                }}
                                                                                adjustPriceType={
                                                                                    adjustPriceType
                                                                                }
                                                                            />
                                                                        )) ||
                                                                        null
                                                                    }
                                                                    precision={isDiscount ? 2 : 4}
                                                                    step={
                                                                        isDiscount ? 0.01 : 0.0001
                                                                    }
                                                                    {...numberConifg}
                                                                    disabled={disabled}
                                                                    placeholder={`请填写`}
                                                                    onChange={() => {
                                                                        form.validateFields([
                                                                            validateName,
                                                                        ]);
                                                                    }}
                                                                ></InputNumber>
                                                            </FormItem>
                                                            {(isDiscount && '折') || '元/度'}
                                                            {adjustType ===
                                                                ADJUST_TYPS.DISCOUNT && (
                                                                <Tooltip title="需小于等于V3专享折扣">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.REDUCE && (
                                                                <Tooltip title="需大于等于V3专享立减金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.ADD && (
                                                                <Tooltip title="需小于等于V3专享立加金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                        </Space>
                                                    </Col>
                                                    <Col span={5}>
                                                        <Space size="small">
                                                            <FormItem
                                                                name={[
                                                                    field.name,
                                                                    'proVipDiscountValue',
                                                                ]}
                                                                noStyle
                                                            >
                                                                <InputNumber
                                                                    addonBefore={
                                                                        (adjustType ===
                                                                            ADJUST_TYPS.DYNAMIC && (
                                                                            <SelectBefore
                                                                                validateName={[
                                                                                    ...validateName,
                                                                                    field.name,
                                                                                ]}
                                                                                typeName={
                                                                                    'proVipMethodType'
                                                                                }
                                                                                name={[field.name]}
                                                                                disabled={disabled}
                                                                                onChange={() => {
                                                                                    form.validateFields(
                                                                                        [
                                                                                            validateName,
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vipAdjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'proVipMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'adjustMethodType',
                                                                                            ],
                                                                                            [
                                                                                                ...validateName,
                                                                                                field.name,
                                                                                                'vthreeMethodType',
                                                                                            ],
                                                                                        ],
                                                                                    );
                                                                                }}
                                                                                adjustPriceType={
                                                                                    adjustPriceType
                                                                                }
                                                                            />
                                                                        )) ||
                                                                        null
                                                                    }
                                                                    precision={isDiscount ? 2 : 4}
                                                                    step={
                                                                        isDiscount ? 0.01 : 0.0001
                                                                    }
                                                                    {...numberConifg}
                                                                    disabled={disabled}
                                                                    placeholder={`请填写`}
                                                                    onChange={() => {
                                                                        form.validateFields([
                                                                            validateName,
                                                                        ]);
                                                                    }}
                                                                ></InputNumber>
                                                            </FormItem>
                                                            {(isDiscount && '折') || '元/度'}
                                                            {adjustType ===
                                                                ADJUST_TYPS.DISCOUNT && (
                                                                <Tooltip title="需小于会员专享折扣">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.REDUCE && (
                                                                <Tooltip title="需大于会员立减金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                            {adjustType === ADJUST_TYPS.ADD && (
                                                                <Tooltip title="需小于会员立加金额">
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            )}
                                                        </Space>
                                                    </Col>
                                                    <Col span={2}>
                                                        {!disabled && (
                                                            <Space size="small">
                                                                {index > 0 && (
                                                                    <UpCircleOutlined
                                                                        style={{
                                                                            fontSize: iconSize,
                                                                        }}
                                                                        onClick={() => {
                                                                            upperEvent(index);
                                                                        }}
                                                                    />
                                                                )}
                                                                {listData?.length > 1 &&
                                                                    index <
                                                                        listData?.length - 1 && (
                                                                        <DownCircleOutlined
                                                                            style={{
                                                                                fontSize: iconSize,
                                                                            }}
                                                                            onClick={() => {
                                                                                downEvent(index);
                                                                            }}
                                                                        />
                                                                    )}
                                                                {adjustPriceType ===
                                                                ADJUST_PRICE_TYPES.SETTLE ? null : (
                                                                    <PlusCircleOutlined
                                                                        style={{
                                                                            fontSize: iconSize,
                                                                        }}
                                                                        onClick={() => {
                                                                            if (!canIadd()) {
                                                                                message.error(
                                                                                    '无法再添加',
                                                                                );
                                                                                return;
                                                                            }
                                                                            add();
                                                                        }}
                                                                    />
                                                                )}

                                                                {index > 0 && (
                                                                    <CloseCircleOutlined
                                                                        style={{
                                                                            fontSize: iconSize,
                                                                        }}
                                                                        onClick={() => {
                                                                            remove(field.name);
                                                                            form.validateFields([
                                                                                validateName,
                                                                            ]);
                                                                        }}
                                                                    />
                                                                )}
                                                            </Space>
                                                        )}
                                                    </Col>
                                                </Row>
                                            </Fragment>
                                        );
                                    });
                                }}
                            </Form.List>
                        </FormItem>
                    </FormItem>
                );
            }}
        </FormItem>
    );
};
export default AdjustFormItem;
