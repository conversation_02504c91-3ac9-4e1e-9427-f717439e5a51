/**
 * 根据运营商查询对应城市下的站点
 */

import { Fragment, useEffect, useState, useMemo, useImperativeHandle } from 'react';
import { Form, Button, Row, Col, Card, Divider, Space } from 'antd';
import classnames from 'classnames';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from './BusinessActiveUpdatePage.less';

import DiscountFormItem from './DiscountFormItem';
import ActTimeFormItem from './ActTimeFormItem';
import { checkTimeOverlap } from './ModuleActive/components/utils';

const FormItem = Form.Item;

const ActTimeAndDiscountFormItem = (props) => {
    const {
        form,
        actType,
        formItemLayout,
        initRef,
        label = '选择时段',
        name = 'actTimeIntervalList',
        disabled,
        rules,
        hasVip,
        belongType,
        typeList,
    } = props;

    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        init: async (id) => {
            try {
            } catch (error) {}
        },
    }));

    const initEvent = () => {};

    return (
        <Form.Item name={name} {...formItemLayout} label={label} rules={rules}>
            <Form.List name={name}>
                {(fields, { add, remove }) => (
                    <Space direction="vertical" style={{ width: '100%' }}>
                        {fields.map((field, index) => (
                            <Card key={index} style={{ marginBottom: '20px' }}>
                                <Row gutter={20}>
                                    <Col flex="1">
                                        <Space>
                                            <ActTimeFormItem
                                                label=""
                                                fieldKey={field.fieldKey}
                                                name={[field.name, 'actTime']}
                                                validateTrigger={['onChange', 'onBlur']}
                                                disabled={disabled}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择时段',
                                                    },
                                                    () => ({
                                                        validator(rule, value) {
                                                            const formData =
                                                                form.getFieldValue(name);
                                                            let hasTime = false;
                                                            if (value && value[0] && value[1]) {
                                                                if (
                                                                    value &&
                                                                    value[0] &&
                                                                    value[0].format('HH:mm') ==
                                                                        '23:59'
                                                                ) {
                                                                    return Promise.reject(
                                                                        `开始时段不能选择23:59`,
                                                                    );
                                                                }
                                                                formData.forEach(
                                                                    (element, eleIndex) => {
                                                                        const { actTime } = element;

                                                                        if (
                                                                            eleIndex != index &&
                                                                            actTime &&
                                                                            actTime[0] &&
                                                                            actTime[1]
                                                                        ) {
                                                                            if (
                                                                                checkTimeOverlap(
                                                                                    actTime,
                                                                                    value,
                                                                                )
                                                                                // value[0].isBetween(
                                                                                //     actTime[0],
                                                                                //     actTime[1],
                                                                                // ) ||
                                                                                // value[1].isBetween(
                                                                                //     actTime[0],
                                                                                //     actTime[1],
                                                                                // ) ||
                                                                                // actTime[0].isBetween(
                                                                                //     value[0],
                                                                                //     value[1],
                                                                                // ) ||
                                                                                // actTime[1].isBetween(
                                                                                //     value[0],
                                                                                //     value[1],
                                                                                // )
                                                                            )
                                                                                hasTime = true;
                                                                        }
                                                                    },
                                                                );
                                                            }

                                                            if (hasTime) {
                                                                return Promise.reject(
                                                                    `配置时段不能重叠`,
                                                                );
                                                            }

                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            />
                                            {disabled ? null : (
                                                <Col>
                                                    <MinusCircleOutlined
                                                        className={classnames(
                                                            styles['dynamic-delete-button'],
                                                            'mg-lr',
                                                        )}
                                                        style={{ fontSize: 24 }}
                                                        onClick={() => {
                                                            remove(field.name);
                                                        }}
                                                    />
                                                </Col>
                                            )}
                                        </Space>
                                        <FormItem
                                            noStyle
                                            shouldUpdate={(prevValues, curValues) => true}
                                        >
                                            {({ getFieldValue }) => {
                                                return (
                                                    <Fragment>
                                                        <div style={{ marginBottom: '20px' }}>
                                                            服务费优惠
                                                        </div>

                                                        <DiscountFormItem
                                                            form={form}
                                                            fieldKey={field.fieldKey}
                                                            name={[field.name]}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            disabled={disabled}
                                                            actType={actType}
                                                            hasVip={hasVip}
                                                            superName={[name]}
                                                            belongType={belongType}
                                                            typeList={typeList}
                                                            noStyle
                                                        />
                                                    </Fragment>
                                                );
                                            }}
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Card>
                        ))}
                        <FormItem label="">
                            <Button
                                disabled={disabled}
                                type="dashed"
                                onClick={() => {
                                    add();
                                }}
                                style={{ width: '200px' }}
                            >
                                <PlusOutlined />
                                添加时段
                            </Button>
                        </FormItem>
                    </Space>
                )}
            </Form.List>
        </Form.Item>
    );
};
export default ActTimeAndDiscountFormItem;
