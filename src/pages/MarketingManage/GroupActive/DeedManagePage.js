import {
    Card,
    Modal,
    Button,
    Space,
    Form,
    Select,
    Input,
    Popconfirm,
    Radio,
    Col,
    message,
} from 'antd';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { LeftOutlined } from '@ant-design/icons';

import { useEffect, useState, useRef, useMemo } from 'react';
import commonStyles from '@/assets/styles/common.less';

import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import UpLoadImg from '@/components/UpLoadImg/index';
import {
    saveGroupBehaviorApi,
    changeGroupBehaviorStatusApi,
} from '@/services/Marketing/MarketingGroupApi';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <FormItem label="行为项名称" name="deedName">
                        <Input placeholder="请输入" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="行为项编号" name="deedNo">
                        <Input placeholder="请输入" autoComplete="off" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};
const DeedManagePage = (props) => {
    const {
        listLoading,
        route,
        dispatch,
        history,
        global: { codeInfo },
        groupModel: { deedManageList, deedManageListTotal },
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();

    const { teamDeedCalcDimension: teamDeedCalcDimensionList, deepItem: deepItemList } = codeInfo;
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const iconRef = useRef();
    const [showEditModal, toggleEditModal] = useState(false);
    const [editDeedInfo, updateEditDeedInfo] = useState(null);

    useEffect(() => {
        if (!teamDeedCalcDimensionList) {
            dispatch({
                type: 'global/initCode',
                code: 'teamDeedCalcDimension',
            });
        }
        if (!deepItemList) {
            dispatch({
                type: 'global/initCode',
                code: 'deepItem',
            });
        }
        return () => {};
    }, []);

    const deepItemOptions = useMemo(() => {
        if (deepItemList) {
            return deepItemList.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [deepItemList]);

    const teamDeedCalcDimensionOptions = useMemo(() => {
        if (teamDeedCalcDimensionList instanceof Array) {
            return teamDeedCalcDimensionList.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [teamDeedCalcDimensionList]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    // 调用搜索接口
    const searchData = () => {
        searchForm.validateFields().then((data) => {
            try {
                const data = searchForm.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'groupModel/getDeedManageList',
                    options: params,
                });
            } catch (error) {
                console.log(543543, error);
                return Promise.reject(error);
            }
        });
    };

    const onFinish = async (values) => {
        try {
            let params = {
                ...values,
            };
            if (editDeedInfo) {
                params.deedId = editDeedInfo.deedId;
            }
            await saveGroupBehaviorApi(params);
            await searchData();
            message.success('保存成功');
            closeEditModal();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const addDeedEvent = () => {
        toggleEditModal(true);
    };
    const closeEditModal = () => {
        updateEditDeedInfo(null);
        form.resetFields();
        toggleEditModal(false);
    };

    const editDeedEvent = (item) => {
        updateEditDeedInfo(item);
        form.setFieldsValue(item);
        toggleEditModal(true);
        setTimeout(() => {
            iconRef.current?.init?.(item.iconUrl);
        }, 200);
    };

    const changeDeedStatusEvent = async (item, status) => {
        try {
            let params = {
                deedNo: item.deedNo,
                deedActive: status,
            };
            await changeGroupBehaviorStatusApi(params);
            await searchData();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const columns = [
        {
            title: '行为项编号',
            width: 160,
            dataIndex: 'deedNo',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '行为项名称',
            width: 160,
            dataIndex: 'deedName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '行为类别',
            width: 160,
            dataIndex: 'deedTypeName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '行为项描述',
            width: 160,
            dataIndex: 'deedDescribe',
            ellipsis: true,
            render(text, record) {
                return (
                    <div className="text-wrap" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '状态',
            width: 160,
            dataIndex: 'deedActiveName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{record.deedActive === '1' ? '启用' : '停用'}</span>;
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                const editBtn = (
                    <span
                        className={commonStyles['table-btn']}
                        onClick={() => {
                            editDeedEvent(record);
                        }}
                    >
                        编辑
                    </span>
                );
                const startBtn = (
                    <Popconfirm
                        title="是否启用？"
                        okText="确认"
                        cancelText="取消"
                        onConfirm={() => {
                            changeDeedStatusEvent(record, '1');
                        }}
                    >
                        <span className={commonStyles['table-btn']}>启用</span>
                    </Popconfirm>
                );
                const stopBtn = (
                    <Popconfirm
                        title="是否停用？"
                        okText="确认"
                        cancelText="取消"
                        onConfirm={() => {
                            changeDeedStatusEvent(record, '2');
                        }}
                    >
                        <span className={commonStyles['table-btn']}>停用</span>
                    </Popconfirm>
                );

                return (
                    <Space>
                        {editBtn}
                        {record.deedActive === '1' ? stopBtn : startBtn}
                    </Space>
                );
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card bordered={false}>
                <SearchLayout
                    form={searchForm}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div>
                    <Button type="primary" onClick={addDeedEvent}>
                        + 新增
                    </Button>
                </div>
                <br></br>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.deedId}
                    dataSource={deedManageList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: deedManageListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    noSort
                />
                <Modal
                    title={`${!editDeedInfo ? '新增' : '编辑'}行为项`}
                    destroyOnClose
                    width={600}
                    visible={showEditModal}
                    onCancel={() => {
                        closeEditModal();
                    }}
                    footer={null}
                >
                    <Form {...formItemLayout} form={form} onFinish={onFinish}>
                        <FormItem
                            name="deedNo"
                            label="行为"
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <Select pagination="请选择">{deepItemOptions}</Select>
                        </FormItem>
                        <FormItem
                            name="deedType"
                            label="行为类别"
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <Radio.Group>
                                <Radio value={'01'}>奖励</Radio>
                                <Radio value={'02'}>惩罚</Radio>
                            </Radio.Group>
                        </FormItem>
                        <FormItem
                            name="deedName"
                            label="行为项名称"
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <Input
                                pagination="请填写"
                                autoComplete="off"
                                maxLength={25}
                                showCount
                            ></Input>
                        </FormItem>
                        <FormItem
                            name="deedCalcDimension"
                            label="计算维度"
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <Select pagination="请选择">{teamDeedCalcDimensionOptions}</Select>
                        </FormItem>
                        <UpLoadImg
                            form={form}
                            ref={iconRef}
                            label="图标"
                            key="iconUrl"
                            name="iconUrl"
                            rules={[{ required: true, message: '请选择图标图片' }]}
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'iconUrl',
                                relaTable: 'e_mkt_act',
                            }}
                            sizeInfo={{
                                size: 60,
                            }}
                            required
                        />
                        <FormItem
                            name="deedDescribe"
                            label="行为项描述"
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <TextArea
                                rows={6}
                                pagination="请填写"
                                maxLength={255}
                                showCount
                            ></TextArea>
                        </FormItem>

                        <FormItem style={{ textAlign: 'center' }}>
                            <Space>
                                <Button type="primary" htmlType="submit">
                                    提交
                                </Button>
                                <Button onClick={closeEditModal}>取消</Button>
                            </Space>
                        </FormItem>
                    </Form>
                </Modal>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, groupModel, loading }) => ({
    global,
    groupModel,
    listLoading: loading.effects['groupModel/getDeedManageList'],
}))(DeedManagePage);
