import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    forwardRef,
    useImperativeHandle,
} from 'react';
import {
    Descriptions,
    Card,
    Modal,
    Button,
    Space,
    Form,
    message,
    Popconfirm,
    Col,
    Input,
} from 'antd';
import { connect } from 'dva';
import commonStyles from '@/assets/styles/common.less';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import LevelView from './LevelView';
import TaskView from './TaskView';

import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import RecordList from '@/components/OperStationSearchList/SearchList/RecordList';
import { getStationScopeListApi } from '@/services/CommonApi';
import { STATION_CONFIG_PAGE_TYPES } from '@/config/declare';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import { isEmpty } from '@/utils/utils';
import {
    saveGroupScopeApi,
    delGroupScopeApi,
    exportGroupListApi,
    exportGroupDetailStationsApi,
} from '@/services/Marketing/MarketingGroupApi';
const FormItem = Form.Item;

const { TextArea } = Input;
const TAB_TYPES = {
    ACT: 'tab1',
    LEVEL: 'tab2',
    TASK: 'tab3',
};
const tabList = [
    {
        key: TAB_TYPES.ACT,
        tab: '活动信息',
    },
    {
        key: TAB_TYPES.LEVEL,
        tab: '等级配置',
    },
    {
        key: TAB_TYPES.TASK,
        tab: '任务配置',
    },
];

const ActView = (props) => {
    const {
        dispatch,
        global: { codeInfo = {} },
        history,
        groupModel,
    } = props;

    const { actChannel: actChannelList } = codeInfo;

    const [toggle, changeToggle] = useState(false);

    const { editActInfo } = groupModel;
    const goEditPage = () => {
        history.push(`/marketing/businessActive/group/actUpdate`);
    };

    useEffect(() => {
        if (!actChannelList) {
            dispatch({
                type: 'global/initCode',
                code: 'actChannel',
            });
        }
    }, []);

    const actDetail = useMemo(() => {
        return editActInfo?.detail || null;
    }, [editActInfo]);

    const budgetmap = useMemo(() => {
        return editActInfo?.budgetmap || null;
    }, [editActInfo]);

    const actChannelName = useMemo(() => {
        if (!actDetail) return '-';
        const typeName = [];
        const actChannels = actDetail?.actChannel?.split?.(',') || actDetail?.actChannel || [];
        for (const actChannel of actChannels) {
            const name =
                actChannelList?.find?.((ele) => ele.codeValue == actChannel)?.codeName ||
                actChannel;
            typeName.push(name);
        }
        if (typeName.length == 0 && actDetail?.actChannel?.length) {
            typeName.push('其他');
        } else if (!actDetail?.actChannel?.length) {
            typeName.push('-');
        }
        return typeName.join(',');
    }, [actChannelList, actDetail]);
    return (
        <Fragment>
            <div>
                <Button type="primary" onClick={goEditPage}>
                    修改
                </Button>
            </div>
            <br></br>
            <Descriptions>
                <Descriptions.Item label="默认等级">{actDetail?.levelName || ''}</Descriptions.Item>
                <Descriptions.Item label="每日充电能量上限">
                    {actDetail?.dayLimitEnergy || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="每日优惠上限">
                    {actDetail?.discountLimitAmt || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="费用承担">
                    {budgetmap?.expenseBearingDesc || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="预算名称">
                    {budgetmap?.budgetName || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="活动计划配置预算">
                    {budgetmap?.budgetAmount || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="投放渠道" span={3}>
                    {actChannelName}
                </Descriptions.Item>
                <Descriptions.Item label="活动描述" span={3}>
                    <div
                        className={!toggle ? commonStyles['text-clamp-6'] : 'text-wrap'}
                        onClick={() => {
                            changeToggle(!toggle);
                        }}
                    >
                        {actDetail?.actMarks || '-'}
                    </div>
                </Descriptions.Item>
            </Descriptions>
        </Fragment>
    );
};

const TabView = (props) => {
    const { tabType } = props;

    if (tabType === TAB_TYPES.ACT) {
        return <ActView {...props}></ActView>;
    } else if (tabType === TAB_TYPES.LEVEL) {
        return <LevelView {...props}></LevelView>;
    } else if (tabType === TAB_TYPES.TASK) {
        return <TaskView {...props}></TaskView>;
    }

    return null;
};

const StationView = forwardRef((props, ref) => {
    const { actId, actType } = props;
    const [listLoading, updateListLoading] = useState(false);
    const [tableList, updateTableList] = useState([]);
    const [tableListTotal, updateTableListTotal] = useState(0);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, { global: {} });

    const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑
    const operGroupRef = useRef();

    useImperativeHandle(ref, () => {
        return {
            reload: () => {
                resetData();
            },
        };
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [form] = Form.useForm();

    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const searchData = async () => {
        try {
            const data = form.getFieldsValue(true);
            const params = {
                ...data,
                city: data.cityList?.join?.(','),
                stationName: data.searchKey?.split('\n')?.join?.(','),
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                scopeRelateId: actId,
                scopeRelateType: actType,
            };

            updateListLoading(true);
            const {
                data: { records, total },
            } = await getStationScopeListApi(params);
            updateTableList(records || []);
            updateTableListTotal(total);
            return records;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        updateSelectedRowKeys([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                let result = !isEmpty(record.index) ? record.index : index + 1;
                return <span title={result}>{result}</span>;
            },
        },
        {
            title: '运营商 ',
            dataIndex: 'buildName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市 ',
            dataIndex: 'cityName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站ID',
            dataIndex: 'stationId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站 ',
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作 ',
            render(text, record, index) {
                return (
                    <Popconfirm
                        title={`确定要删除此场站？`}
                        onConfirm={() => {
                            delStationEvent([record.stationId]);
                        }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                );
            },
        },
    ];

    const delStationEvent = async (rows) => {
        try {
            const delScopeInfo = rows.map((ele) => ele);
            let params = {
                delScopeInfo: JSON.stringify(delScopeInfo),
                actId: actId,
            };
            await delGroupScopeApi(params);
            message.success('删除成功');
            updateSelectedRowKeys([]);
            await searchData();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const onExportForm = async () => {
        const data = form.getFieldsValue(true);
        exportEvent(data, columns);
    };

    const exportEvent = (data, tableColumns) => {
        try {
            let params = {
                ...data,

                city: data.cityList?.join?.(','),
                stationName: data.searchKey?.split('\n')?.join?.(','),
                actId: actId,
                scopeRelateId: actId,
                scopeRelateType: actType,
            };
            exportGroupDetailStationsApi(params);
            return;
        } catch (error) {
            throw new Error(error);
        }
    };

    // 有选中的场站
    const hasSelectedStations = useMemo(() => {
        return selectedRowKeys.length > 0;
    }, [selectedRowKeys]);

    const onSelectChange = (_selectedRowKeys) => {
        const filterSelects = selectedRowKeys.filter((ele) => {
            const curPageItem = tableList.find((tableItem) => tableItem.stationId === ele);
            return (curPageItem && _selectedRowKeys.includes(ele)) || !curPageItem;
        });
        const newSelects = [...new Set([...filterSelects, ..._selectedRowKeys])];

        updateSelectedRowKeys(newSelects);
    };

    // 配置记录
    const recordRef = useRef();
    const showRecordEvent = () => {
        recordRef.current?.show();
    };

    return (
        <Fragment>
            <Form
                form={form}
                onFinish={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetData}
                    onExportForm={onExportForm}
                    onSearchGroup={onSearchGroup}
                >
                    <Col span={8}>
                        <FormItem noStyle name="searchKey">
                            <TextArea
                                placeholder="请输入场站进行查询,多个场站使用回车间隔"
                                allowClear
                                rows={1}
                            />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <OperSelectTypeItem
                            form={form}
                            initialValue={undefined}
                            disabled={false}
                            disabledFilter={false}
                        />
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城市"
                            name="cityList"
                            placeholder="请选择"
                            formItemLayout={{ labelAlign: 'right' }}
                            showArrow
                            provinceSelectable
                            rules={null}
                        />
                    </Col>
                    <FormItem noStyle name="stationIds" />
                </SearchOptionsBar>
            </Form>

            <div className="mg-b-20">
                <Space>
                    <Popconfirm
                        title="即将删除已勾选场站"
                        onConfirm={() => delStationEvent(selectedRowKeys)}
                        disabled={!hasSelectedStations}
                    >
                        <Button type="primary" disabled={!hasSelectedStations}>
                            删除选中项
                        </Button>
                    </Popconfirm>

                    <Button onClick={showRecordEvent}>配置记录</Button>
                </Space>
            </div>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${record.stationId}`}
                dataSource={tableList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: tableListTotal,
                    pageSize: pageInfo.pageSize,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                sticky={{ offsetHeader: 0 }}
                rowSelection={{
                    selectedRowKeys,
                    onChange: onSelectChange,
                    getCheckboxProps: (record) => ({
                        disabled: !record.stationId,
                    }),
                }}
            />
            <RecordList
                initRef={recordRef}
                recordParams={{
                    relateId: actId,
                    scene: 'stc_team_group',
                }}
            />
            <OperGroupImportModal
                title="批量查询"
                initRef={operGroupRef}
                onConfirm={(addStationList) => {
                    const list =
                        (addStationList?.length && addStationList.map((item) => item.stationId)) ||
                        [];
                    form.setFieldsValue({ stationIds: list });
                    const values = form.getFieldsValue();
                    // // onSubmit(values);
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
            />
        </Fragment>
    );
});
const ActInfoPage = (props) => {
    const { user, dispatch, route, groupModel } = props;

    const { editActInfo } = groupModel;

    const { currentUser } = user;

    const [stationForm] = Form.useForm();

    const [activeTab, changeActiveTab] = useState(TAB_TYPES.ACT);
    const [stationModalType, updateStationModalType] = useState(0); // 场站弹窗类型，0隐藏，1新增，2删除
    const [submitLoading, updateSubmitLoading] = useState(false);

    const actDetail = useMemo(() => {
        return editActInfo?.detail || null;
    }, [editActInfo]);

    useEffect(() => {
        initData();
        return () => {};
    }, []);

    const initData = () => {
        dispatch({
            type: 'groupModel/getActDetail',
        });
    };

    const openEditStationModal = (type) => {
        updateStationModalType(type);
    };

    const closeEditStationModal = () => {
        updateStationModalType(0);
        stationForm.setFieldsValue({ stationInfo: undefined, delScopeInfo: undefined });
    };

    const onStationAddFinish = async (values, type) => {
        // type: 1新增，2删除
        try {
            updateSubmitLoading(true);
            let params = {
                saveType: 'send',
                actId: actDetail?.actId,
                // stationInfo: (type == 1 && JSON.stringify(values.stationInfos)) || undefined,
                // delScopeInfo: (type == 2 && values.delScopeInfo) || undefined,
            };

            const {
                addStations = [],
                allStations = [],
                defaultStations = [],
                delStations = [],
            } = values.stationConfig || {};

            if (addStations?.length > 0) {
                params.addStationIds = JSON.stringify(addStations.map((ele) => ele.stationId));
            }

            const { data, msg } = await saveGroupScopeApi(params);

            stationRef.current?.reload();

            closeEditStationModal();
            if (msg) {
                message.success(msg);
            }

            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateSubmitLoading(false);
        }
    };

    const stationRef = useRef();

    return (
        <PageHeaderWrapper
            title={<div className="page-title">{actDetail?.actName || route.name}</div>}
            content={
                <Descriptions>
                    <Descriptions.Item label="活动状态">
                        {actDetail?.actStatusName || ''}
                    </Descriptions.Item>
                    <Descriptions.Item span={2} label="活动时间">
                        {`${actDetail?.effTime || ''}~${actDetail?.expTime || ''}`}
                    </Descriptions.Item>
                </Descriptions>
            }
        >
            <Card
                tabList={tabList}
                activeTabKey={activeTab}
                onTabChange={(key) => {
                    changeActiveTab(key);
                }}
            >
                <TabView {...props} tabType={activeTab}></TabView>
            </Card>
            <br></br>
            <Card
                title={'活动场站'}
                extra={
                    <Space>
                        <Button
                            type="primary"
                            onClick={() => {
                                openEditStationModal(1);
                            }}
                        >
                            新增
                        </Button>
                    </Space>
                }
            >
                {actDetail?.actId && (
                    <StationView
                        ref={stationRef}
                        actId={actDetail?.actId}
                        actType={STATION_CONFIG_PAGE_TYPES.GROUP}
                    ></StationView>
                )}
            </Card>
            <br></br>

            <Modal
                title="添加场站"
                destroyOnClose
                width={1200}
                visible={stationModalType != 0}
                onCancel={() => {
                    closeEditStationModal();
                }}
                footer={null}
                maskClosable={false}
            >
                <Form form={stationForm} onFinish={(values) => onStationAddFinish(values, 1)}>
                    <FormItem
                        label="活动范围"
                        name={'stationConfig'}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请选择要添加场站');
                                    }
                                    const { addStations, allStations } = value;
                                    if (isEmpty(allStations) && isEmpty(addStations)) {
                                        return Promise.reject('请选择要添加场站');
                                    }
                                    if (addStations.length > 20000) {
                                        return Promise.reject('一次性最多添加2万个站点');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <SearchStationItem
                            currentUser={currentUser}
                            hasStastics
                            operationRecord={false}
                            emptyText="请选择要添加场站"
                        ></SearchStationItem>
                    </FormItem>

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.stationConfig != curValues.stationConfig
                        }
                    >
                        {({ getFieldValue }) => {
                            const stationConfig = getFieldValue('stationConfig') || {};
                            const { addStations, allStations } = stationConfig;
                            return (
                                (allStations?.length && (
                                    <FormItem style={{ textAlign: 'center' }}>
                                        <Space>
                                            <Button
                                                type="primary"
                                                htmlType="submit"
                                                loading={submitLoading}
                                            >
                                                提交
                                            </Button>
                                            <Button onClick={closeEditStationModal}>取消</Button>
                                        </Space>
                                    </FormItem>
                                )) ||
                                null
                            );
                        }}
                    </FormItem>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, groupModel, loading }) => ({
    global,
    user,
    groupModel,
    detalisLoading: loading.effects['groupModel/getActDetail'],
}))(ActInfoPage);
