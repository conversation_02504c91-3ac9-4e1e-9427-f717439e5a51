import { <PERSON><PERSON><PERSON>erWrapper } from '@ant-design/pro-layout';
import { LeftOutlined } from '@ant-design/icons';
import { Card, Button, Space, Form, Input, InputNumber, Radio, DatePicker, message } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useMemo } from 'react';
import { connect } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import { saveGroupActApi } from '@/services/Marketing/MarketingGroupApi';
import BudgetFormItem from '@/components/BudgetFormItem/index';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import SelelctLevelFormItem from './components/SelelctLevelFormItem';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const formItemLayout = {
    labelCol: {
        flex: '0 0 200px',
    },
    wrapperCol: {
        span: 10,
    },
};

const ActUpdatePage = (props) => {
    const {
        route,
        dispatch,
        history,
        user,
        global: { codeInfo },
        groupModel: { editActInfo },
    } = props;
    const { currentUser } = user;

    const [form] = Form.useForm();

    const budgetRef = useRef();

    const { actChannel } = codeInfo;

    useEffect(() => {
        if (!actChannel) {
            dispatch({
                type: 'global/initCode',
                code: 'actChannel',
            });
        }
    }, []);

    useEffect(() => {
        initData();
    }, []);

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
        }
    }, [editActInfo]);

    const initData = () => {
        dispatch({
            type: 'groupModel/getActDetail',
        });
    };

    const actDetail = useMemo(() => {
        return editActInfo?.detail || null;
    }, [editActInfo]);

    const isLock = useMemo(() => {
        if (actDetail?.actStatus >= 2) {
            return true;
        }
        return false;
    }, [actDetail]); // 是否可编辑

    const initEditInfo = (info) => {
        const params = {};

        const { detail } = info;
        if (detail) {
            if (detail.effTime && detail.expTime) {
                params.dateTime = [moment(detail.effTime), moment(detail.expTime)];
            }
            if (detail.beginTime && detail.endTime) {
                params.actTime = [moment(detail.beginTime), moment(detail.endTime)];
            }

            if (detail.actStatus) {
                params.actStatus = detail.actStatus;
            }

            if (detail.levelId) {
                params.defaultLevelId = detail.levelId;
            }

            if (detail.dayLimitEnergy) {
                params.dayLimitEnergy = detail.dayLimitEnergy;
            }

            if (detail.discountLimitAmt) {
                params.discountLimitAmt = detail.discountLimitAmt;
            }

            if (detail.actMarks) {
                params.actMarks = detail.actMarks;
            }
            if (detail.actChannel) {
                params.actChannel = detail?.actChannel?.split?.(',') || detail?.actChannel;
            }
        }

        if (info.budgetmap) {
            //预算配置初始化

            for (const key in info.budgetmap) {
                if (Object.hasOwnProperty.call(info.budgetmap, key)) {
                    const element = info.budgetmap[key];
                    params[key] = element;
                }
            }
            if (info.budgetmap?.expenseBearing) {
                params.expenseBearing =
                    (info.budgetmap?.expenseBearing && info.budgetmap?.expenseBearing.split(',')) ||
                    [];
            }

            if (params.budgetId && params.dateTime[0] && params.dateTime[1]) {
                if (budgetRef?.current?.update) {
                    let options = {
                        budgetId: params.budgetId,
                        effTime: params.dateTime[0],
                        expTime: params.dateTime[1],
                        actId: actDetail?.actId || '',
                    };
                    budgetRef?.current?.update(options);
                }
            }
        } else {
            params.expenseBearing = [];
        }

        form.setFieldsValue(params);
    };

    const onFinish = async (values) => {
        try {
            let params = {
                actMarks: values.actMarks || '',
                actStatus: values.actStatus || '',
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                defaultLevelId: values.defaultLevelId || '',
                dayLimitEnergy: values.dayLimitEnergy || '',

                discountLimitAmt: values.discountLimitAmt || '',
                actChannel: values.actChannel?.join(',') || '',

                actType: '12',

                expenseBearing: values.expenseBearing.join(','),
                budgetId: values.budgetId || '',
                budgetAmount: values.budgetAmount || 0,
            };
            if (actDetail?.actId) {
                params.actId = actDetail?.actId;
            }

            await saveGroupActApi(params);
            message.success('保存成功');
            goBack();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const goBack = () => {
        history.go(-1);
    };

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {actDetail?.actName || route.name}
                </div>
            }
        >
            <Card>
                <Form {...formItemLayout} form={form} onFinish={onFinish}>
                    <div className={commonStyles['form-title']}>活动信息</div>
                    <FormItem
                        label="活动状态"
                        name="actStatus"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Radio.Group>
                            <Radio value={'1'}>开启</Radio>
                            <Radio value={'2'}>关闭</Radio>
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        label="活动时间"
                        name="dateTime"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <RangePicker
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: [
                                    moment('00:00:00', 'HH:mm:ss'),
                                    moment('23:59:59', 'HH:mm:ss'),
                                ],
                            }}
                            disabledTime={disabledRangeTime}
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </FormItem>
                    <CheckBoxGroup
                        label="投放渠道"
                        name={'actChannel'}
                        form={form}
                        selectList={actChannel}
                        required
                        rules={[{ required: true, message: '请选择投放渠道' }]}
                        valueType="select"
                    ></CheckBoxGroup>

                    <SelelctLevelFormItem
                        actId={actDetail?.actId}
                        label="默认等级"
                        name="defaultLevelId"
                        rules={[{ required: true, message: '请填写' }]}
                    ></SelelctLevelFormItem>
                    <FormItem
                        name="dayLimitEnergy"
                        label="每日充电能量上限"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={9999999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                        ></InputNumber>
                    </FormItem>
                    <FormItem
                        label="每日优惠上限"
                        name="discountLimitAmt"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={99999}
                            min={0}
                            step={1}
                            precision={2}
                            pagination="请填写"
                            addonAfter="元"
                        ></InputNumber>
                    </FormItem>
                    <FormItem
                        label="活动描述"
                        name="actMarks"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <TextArea
                            placeholder="请填写"
                            rows={6}
                            maxLength={1024}
                            showCount
                        ></TextArea>
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.dateTime !== curValues.dateTime ||
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const dateTime = getFieldValue('dateTime');
                            return (
                                <BudgetFormItem
                                    ref={budgetRef}
                                    form={form}
                                    effTime={dateTime && dateTime[0]}
                                    expTime={dateTime && dateTime[1]}
                                    disabled={isLock}
                                    currentUser={currentUser}
                                    rules={
                                        (actDetail?.actStatus == '2' && {
                                            expenseBearing: [],
                                        }) ||
                                        false
                                    }
                                    formItemLayout={formItemLayout}
                                ></BudgetFormItem>
                            );
                        }}
                    </FormItem>
                    <br></br>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={goBack}>取消</Button>
                    </Space>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, groupModel, loading }) => ({
    global,
    user,
    groupModel,
    detalisLoading: loading.effects['groupModel/getGroupDetail'],
}))(ActUpdatePage);
