import { useEffect } from 'react';
import { Form, Select } from 'antd';
import { useMemo, useState } from 'react';
import { getGroupLevelListApi } from '@/services/Marketing/MarketingGroupApi';

const FormItem = Form.Item;

const Option = Select;

const SelectLevelItem = (props) => {
    const { value, onChange, actId } = props;
    const [levelList, changeLevelList] = useState([]);
    useEffect(() => {
        initLevelList();
    }, [actId]);
    const levelOptions = useMemo(() => {
        return levelList.map((ele) => {
            return (
                <Option key={ele.levelId} value={ele.levelId}>
                    {ele.levelName}
                </Option>
            );
        });
    }, [levelList]);
    const initLevelList = async () => {
        try {
            const { data: levelList } = await getGroupLevelListApi(actId);
            changeLevelList(levelList);
            return levelList;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const onChangeLevelEvent = (level) => {
        onChange && onChange(level);
    };
    return (
        <Select value={value} onChange={onChangeLevelEvent} allowClear>
            {levelOptions}
        </Select>
    );
};
const SelelctLevelFormItem = (props) => {
    const { name = 'defaultLevelId', label = '默认等级', rules, ...otherProps } = props;
    return (
        <FormItem label={label} name={name} rules={rules}>
            <SelectLevelItem {...otherProps}></SelectLevelItem>
        </FormItem>
    );
};
export default SelelctLevelFormItem;
