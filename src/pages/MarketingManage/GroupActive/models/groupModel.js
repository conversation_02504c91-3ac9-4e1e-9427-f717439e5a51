import {
    getGroupList<PERSON><PERSON>,
    getGroupDetailApi,
    getGroupInfoApi,
    getGroupBehaviorListApi,
} from '@/services/Marketing/MarketingGroupApi';

const groupModel = {
    namespace: 'groupModel',
    state: {
        groupList: [], // 列表
        groupListTotal: 0, // 条数
        groupInfo: null, // 团队详情

        editActInfo: null, // 详情

        deedManageList: [], //行为项管理列表
        deedManageListTotal: 0, // 行为项管理列表条数
    },
    effects: {
        *getActDetail({ actId }, { call, put, select }) {
            try {
                const { data } = yield call(getGroupDetailApi, actId);

                yield put({
                    type: 'updateActInfo',
                    actInfo: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         *
         */
        *getGroupList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: groupList, total: groupListTotal },
                } = yield call(getGroupListApi, options);

                yield put({
                    type: 'updateManageList',
                    list: groupList,
                    total: groupListTotal,
                });
                return groupList;
            } catch (error) {
                return Promise.reject(error);
            }
        },

        *getGroupDetail({ teamId }, { call, put, select }) {
            try {
                const { data } = yield call(getGroupInfoApi, teamId);

                yield put({
                    type: 'updateGroupInfo',
                    groupInfo: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },

        /**
         *
         */
        *getDeedManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: deedManageList, total: deedManageListTotal },
                } = yield call(getGroupBehaviorListApi, options);

                yield put({
                    type: 'updateDeedManageList',
                    list: deedManageList,
                    total: deedManageListTotal,
                });
                return deedManageList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateActInfo(state, { actInfo }) {
            return {
                ...state,
                editActInfo: actInfo,
            };
        },
        updateManageList(state, { list, total }) {
            return {
                ...state,
                groupList: list,
                groupListTotal: total,
            };
        },
        updateGroupInfo(state, { groupInfo }) {
            return {
                ...state,
                groupInfo: groupInfo,
            };
        },
        updateDeedManageList(state, { list, total }) {
            return {
                ...state,
                deedManageList: list,
                deedManageListTotal: total,
            };
        },
    },
};
export default groupModel;
