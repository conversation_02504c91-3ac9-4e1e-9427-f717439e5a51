import { Modal, Button, Space, Form, Select, Input, InputNumber, Popconfirm, message } from 'antd';
import { Fragment, useState, useMemo } from 'react';

import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import SelectColor from '@/components/SelectColor/index';
import { saveGroupLevelApi, delGroupLevelApi } from '@/services/Marketing/MarketingGroupApi';

import commonStyles from '@/assets/styles/common.less';

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
};
const LevelView = (props) => {
    const {
        dispatch,
        detalisLoading,
        groupModel,
        global: { codeInfo },
    } = props;
    const { editActInfo } = groupModel;

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    const [form] = Form.useForm();

    const [showEditModal, toggleEditModal] = useState(false);
    const [editLevelInfo, updateEditLevelInfo] = useState(null);

    const actDetail = useMemo(() => {
        return editActInfo?.detail || null;
    }, [editActInfo]);

    const levelList = useMemo(() => {
        return editActInfo?.levelList || [];
    }, [editActInfo]);

    const refreshEvent = () => {
        dispatch({
            type: 'groupModel/getActDetail',
        });
    };
    const delLevelEvent = async (item) => {
        try {
            let params = {
                levelId: item.levelId,
            };
            await delGroupLevelApi(params);
            refreshEvent();
            message.success('删除成功');
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onFinish = async (values) => {
        try {
            let params = {
                ...values,
                actId: actDetail?.actId || '',
            };
            if (editLevelInfo) {
                params.levelId = editLevelInfo.levelId;
            }
            await saveGroupLevelApi(params);
            refreshEvent();
            message.success('保存成功');
            closeEditModal();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const addTaskEvent = () => {
        toggleEditModal(true);
    };
    const closeEditModal = () => {
        updateEditLevelInfo(null);
        form.resetFields();
        toggleEditModal(false);
    };

    const editLevelEvent = (item) => {
        updateEditLevelInfo(item);
        form.setFieldsValue(item);
        toggleEditModal(true);
    };

    const columns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '等级名称',
            width: 160,
            dataIndex: 'levelName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '升级能量',
            width: 160,
            dataIndex: 'levelEnergy',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '背景浅色',
            width: 160,
            dataIndex: 'lightColour',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '背景深色',
            width: 160,
            dataIndex: 'deepColour',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠类型',
            width: 160,
            dataIndex: 'discountTypeName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠折扣',
            width: 160,
            dataIndex: 'discountValue',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                const editBtn = (
                    <span
                        className={commonStyles['table-btn']}
                        onClick={() => {
                            editLevelEvent(record);
                        }}
                    >
                        修改
                    </span>
                );
                const delBtn = (
                    <Popconfirm
                        title="是否删除？"
                        okText="确认"
                        cancelText="取消"
                        onConfirm={() => {
                            delLevelEvent(record);
                        }}
                    >
                        <span className={commonStyles['table-btn']}>删除</span>
                    </Popconfirm>
                );

                return (
                    <Space>
                        {editBtn}
                        {delBtn}
                    </Space>
                );
            },
        },
    ];
    return (
        <Fragment>
            <div>
                <Button type="primary" onClick={addTaskEvent}>
                    + 添加等级
                </Button>
            </div>
            <br></br>
            <TablePro
                name="level"
                loading={detalisLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.levelId}
                dataSource={levelList}
                columns={columns}
                noSort
            />
            <Modal
                title={`${!editLevelInfo ? '新增' : '编辑'}等级`}
                destroyOnClose
                width={600}
                visible={showEditModal}
                onCancel={() => {
                    closeEditModal();
                }}
                footer={null}
            >
                <Form {...formItemLayout} form={form} onFinish={onFinish}>
                    <FormItem
                        name="levelName"
                        label="等级名称"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            pagination="请填写"
                            autoComplete="off"
                            maxLength={4}
                            showCount
                        ></Input>
                    </FormItem>
                    <FormItem
                        name="levelEnergy"
                        label="升级能量"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={999999999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                        ></InputNumber>
                    </FormItem>
                    <SelectColor
                        form={form}
                        name="lightColour"
                        label="背景浅色"
                        rules={[{ required: true, message: '请选择' }]}
                        required
                    ></SelectColor>
                    <SelectColor
                        form={form}
                        name="deepColour"
                        label="背景深色"
                        rules={[{ required: true, message: '请选择' }]}
                        required
                    ></SelectColor>
                    <FormItem
                        name="discountType"
                        label="优惠类型"
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'01'}
                    >
                        <Select pagination="请选择">
                            <Option value="01">服务费</Option>
                        </Select>
                    </FormItem>

                    <FormItem
                        label="优惠折扣"
                        name="discountValue"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={10}
                            min={0}
                            step={0.01}
                            precision={2}
                            pagination="请填写"
                            addonAfter="折（如填写7.5则表示打7.5折）"
                        ></InputNumber>
                    </FormItem>
                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeEditModal}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Fragment>
    );
};

export default LevelView;
