import { useEffect, useState } from 'react';
import { Card, Modal, Button, Space, Form, Col, Input, message } from 'antd';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import NumericInput from '@/components/NumericInput/index';

import { PageHeaderWrapper } from '@ant-design/pro-layout';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { connect } from 'dva';
import { Link } from 'umi';
import SelelctLevelFormItem from './components/SelelctLevelFormItem';
import styles from '@/assets/styles/common.less';
import { exportGroupListApi, saveGroupApi } from '@/services/Marketing/MarketingGroupApi';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                minSpan={40}
                onExportForm={onExportForm}
            >
                <Col span={8}>
                    <FormItem label="团队名称" name="teamName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="成员号码" name="commanderMobile">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <SelelctLevelFormItem
                        placeholder="请选择"
                        label="团队等级"
                        name="levelId"
                    ></SelelctLevelFormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const GroupManagePage = (props) => {
    const {
        route,
        dispatch,
        history,
        groupModel: {
            groupList = [], // 列表
            groupListTotal = 0,
        },
    } = props;
    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const [showEditModal, toggleEditModal] = useState(false);
    const [editGroupInfo, updateEditGroupInfo] = useState(null);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    // 调用搜索接口
    const searchData = () => {
        searchForm.validateFields().then((data) => {
            try {
                const data = searchForm.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'groupModel/getGroupList',
                    options: params,
                });

                return;
            } catch (error) {
                console.log(5555, error);
                return Promise.reject(error);
            }
        });
    };

    // 导出
    const exportFormEvent = () => {
        const data = searchForm.getFieldsValue();
        const params = {
            ...data,
        };
        exportGroupListApi(params);
    };

    const onFinish = async (values) => {
        try {
            let params = {
                teamId: editGroupInfo?.teamId || '',

                teamNo: values.teamNo || '',

                teamName: values.teamName || '',

                commanderMobile: values.commanderMobile || '',

                levelId: values.levelId || '',
            };
            await saveGroupApi(params);
            message.success('编辑成功');
            await searchData();
            closeEditModal();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const closeEditModal = () => {
        updateEditGroupInfo(null);
        form.resetFields();
        toggleEditModal(false);
    };

    const editGroupEvent = (item) => {
        updateEditGroupInfo(item);
        form.setFieldsValue(item);
        toggleEditModal(true);
    };

    const columns = [
        {
            title: '团队名称',
            width: 160,
            dataIndex: 'teamName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '团队编号',
            width: 160,
            dataIndex: 'teamNo',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '团长号码',
            width: 160,
            dataIndex: 'commanderMobile',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '团队人数',
            width: 160,
            dataIndex: 'teamNum',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '团队等级',
            width: 160,
            dataIndex: 'levelName',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本周能量',
            width: 160,
            dataIndex: 'curEnergy',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '上周能量',
            width: 160,
            dataIndex: 'preEnergy',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'gmtCreate',
            ellipsis: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                const editBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            editGroupEvent(record);
                        }}
                    >
                        编辑
                    </span>
                );
                const lookBtn = (
                    <Link to={`/marketing/businessActive/group/detail/${record.teamId}`}>查看</Link>
                );
                return (
                    <Space>
                        {lookBtn}

                        {editBtn}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card bordered={false}>
                <SearchLayout
                    form={searchForm}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <TablePro
                    loading={false}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.teamId}
                    dataSource={groupList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: groupListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    noSort
                />
            </Card>
            <Modal
                title={`编辑团队`}
                destroyOnClose
                width={600}
                visible={showEditModal}
                onCancel={() => {
                    closeEditModal();
                }}
                footer={null}
            >
                <Form {...formItemLayout} form={form} onFinish={onFinish}>
                    <FormItem
                        noStyle
                        shouldUpdate={(pre, after) => {
                            return pre.teamNo !== after.teamNo;
                        }}
                    >
                        {({ getFieldValue }) => {
                            const teamNo = getFieldValue('teamNo');
                            return (
                                <FormItem name="teamNo" label="团队编号">
                                    {teamNo}
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        name="teamName"
                        label="团长名称"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            pagination="请填写"
                            autoComplete="off"
                            maxLength={10}
                            showCount
                        ></Input>
                    </FormItem>

                    <FormItem
                        name="commanderMobile"
                        label="团长号码"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <NumericInput
                            placeholder="请填写"
                            maxLength={11}
                            autoComplete="off"
                            isHide
                        ></NumericInput>
                    </FormItem>

                    <SelelctLevelFormItem name="levelId" label="团队等级"></SelelctLevelFormItem>

                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                保存
                            </Button>
                            <Button onClick={closeEditModal}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, groupModel, loading }) => ({
    global,
    groupModel,
    listLoading: loading.effects['groupModel/getGroupList'],
}))(GroupManagePage);
