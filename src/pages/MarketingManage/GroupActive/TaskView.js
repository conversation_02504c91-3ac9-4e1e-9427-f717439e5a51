import {
    Modal,
    Button,
    Space,
    Form,
    Select,
    InputNumber,
    Tooltip,
    Popconfirm,
    message,
} from 'antd';
import { Fragment, useEffect, useState, useMemo } from 'react';
import commonStyles from '@/assets/styles/common.less';

import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
    delGroupRankApi,
    saveGroupRankApi,
    getGroupBehaviorListApi,
} from '@/services/Marketing/MarketingGroupApi';
import { history } from 'umi';

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
};
const TaskView = (props) => {
    const {
        dispatch,
        detalisLoading,
        groupModel,
        global: { codeInfo },
    } = props;

    const { editActInfo } = groupModel;

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    const [form] = Form.useForm();

    const [showEditModal, toggleEditModal] = useState(false);
    const [editTaskInfo, updateEditTaskInfo] = useState(null);
    const [deepItemList, updateDeepItemList] = useState([]);

    useEffect(() => {
        if (deepItemList?.length === 0) {
            initDeepList();
        }
    }, []);

    const initDeepList = async () => {
        try {
            let parmas = {
                pageIndex: 1,
                pageSize: 99,
                deedActive: '1',
            };
            const {
                data: { records: list },
            } = await getGroupBehaviorListApi(parmas);
            updateDeepItemList(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const deepItemOptions = useMemo(() => {
        if (deepItemList) {
            return deepItemList.map((ele) => {
                return (
                    <Option key={ele.deedNo} value={ele.deedNo}>
                        {ele.deedName}
                    </Option>
                );
            });
        }
        return [];
    }, [deepItemList]);

    const taskList = useMemo(() => {
        return editActInfo?.taskList || [];
    }, [editActInfo]);

    const actDetail = useMemo(() => {
        return editActInfo?.detail || null;
    }, [editActInfo]);

    const refreshEvent = () => {
        dispatch({
            type: 'groupModel/getActDetail',
        });
    };

    const addTaskEvent = () => {
        toggleEditModal(true);
    };
    const editTaskEvent = (item) => {
        updateEditTaskInfo(item);
        form.setFieldsValue(item);
        toggleEditModal(true);
    };

    const delTaskEvent = async (item) => {
        try {
            let params = {
                deedId: item.deedId,
            };
            await delGroupRankApi(params);
            refreshEvent();
            message.success('删除成功');
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onFinish = async (values) => {
        try {
            let params = {
                ...values,
                actId: actDetail?.actId || '',
            };
            if (editTaskInfo) {
                params.deedId = editTaskInfo.deedId;
            }
            await saveGroupRankApi(params);
            refreshEvent();
            message.success('保存成功');
            closeEditModal();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const closeEditModal = () => {
        updateEditTaskInfo(null);
        form.resetFields();
        toggleEditModal(false);
    };

    const columns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '行为项',
            width: 160,
            dataIndex: 'deedName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '行为类型',
            width: 160,
            dataIndex: 'deedTypeName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '协作类型',
            width: 160,
            dataIndex: 'cooperationModelName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '触发次数',
            width: 160,
            dataIndex: 'triggerTimes',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: (
                <Tooltip title={'获得能量需要完成行为项的次数'}>
                    触发条件
                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                </Tooltip>
            ),
            width: 160,
            dataIndex: 'triggerCondition',

            render(text, record) {
                const result = record.triggerCondition >= 0 ? `${record.triggerCondition}次` : '-';
                return <span title={result}>{result}</span>;
            },
        },
        {
            title: '计算方式',
            width: 160,
            dataIndex: 'calculateModelName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '计算基数',
            width: 160,
            dataIndex: 'calculateBaseTypeName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '赠送能量',
            width: 160,
            dataIndex: 'giveValue',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '能量上限',
            width: 160,
            dataIndex: 'giveValueUpperlimit',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '适用时间',
            width: 160,
            dataIndex: 'applyTimeName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '重置频率',
            width: 160,
            dataIndex: 'resetFrequencyName',

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                const editBtn = (
                    <span
                        className={commonStyles['table-btn']}
                        onClick={() => {
                            editTaskEvent(record);
                        }}
                    >
                        修改
                    </span>
                );
                const delBtn = (
                    <Popconfirm
                        title="是否删除？"
                        okText="确认"
                        cancelText="取消"
                        onConfirm={() => {
                            delTaskEvent(record);
                        }}
                    >
                        <span className={commonStyles['table-btn']}>删除</span>
                    </Popconfirm>
                );

                return (
                    <Space>
                        {editBtn}
                        {delBtn}
                    </Space>
                );
            },
        },
    ];

    const goDeedPage = () => {
        history.push('/marketing/businessActive/group/deed');
    };
    return (
        <Fragment>
            <div>
                <Space>
                    <Button type="primary" onClick={addTaskEvent}>
                        + 添加任务
                    </Button>
                    <Button onClick={goDeedPage}>行为项管理</Button>
                </Space>
            </div>

            <br></br>
            <TablePro
                name="task"
                loading={detalisLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.deedId}
                dataSource={taskList}
                columns={columns}
            />
            <Modal
                title={`${!editTaskInfo ? '新增' : '编辑'}任务`}
                destroyOnClose
                width={600}
                visible={showEditModal}
                onCancel={() => {
                    closeEditModal();
                }}
                bodyStyle={{ maxHeight: '500px', overflowY: 'auto' }}
                footer={null}
            >
                <Form {...formItemLayout} form={form} onFinish={onFinish}>
                    <FormItem
                        name="deedNo"
                        label="行为项"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">{deepItemOptions}</Select>
                    </FormItem>
                    <FormItem
                        name="deedType"
                        label="行为类别"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">
                            <Option value="01">奖励</Option>
                            <Option value="02">惩罚</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        name="cooperationModel"
                        label="协作类型"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">
                            <Option value="02">单人</Option>
                            <Option value="01">协作</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        label={
                            <Tooltip title={'不填表示无限次'}>
                                触发次数
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                        name="triggerTimes"
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={99999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                            addonAfter="次"
                        ></InputNumber>
                    </FormItem>
                    <FormItem
                        label={
                            <Tooltip title={'获得能量需要完成行为项的次数'}>
                                触发条件
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                        name="triggerCondition"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={99999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                            addonAfter="次"
                        ></InputNumber>
                    </FormItem>
                    <FormItem
                        name="calculateModel"
                        label="计算方式"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">
                            <Option value="01">固定值</Option>
                            <Option value="02">倍率</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        name="calculateBaseType"
                        label="计算基数"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">
                            <Option value="01">单次值</Option>
                            <Option value="02">累计值</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        name="giveValue"
                        label="赠送能量"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={99999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                        ></InputNumber>
                    </FormItem>
                    <FormItem
                        name="giveValueUpperlimit"
                        label={
                            <Tooltip title={'不填表示没有上限'}>
                                能量上限
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            max={9999999}
                            min={0}
                            step={1}
                            precision={0}
                            pagination="请填写"
                        ></InputNumber>
                    </FormItem>
                    <FormItem name="applyTime" label="适用时间">
                        <Select pagination="请选择">
                            <Option value="01">节假日</Option>
                            <Option value="02">工作日</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        name="resetFrequency"
                        label="重置频率"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select pagination="请选择">
                            <Option value="01">一次性</Option>
                            <Option value="02">每日</Option>
                            <Option value="03">每周</Option>
                        </Select>
                    </FormItem>

                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeEditModal}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Fragment>
    );
};

export default TaskView;
