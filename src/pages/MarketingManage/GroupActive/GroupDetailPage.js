import { Descriptions, Card } from 'antd';
import { connect, useParams } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { LeftOutlined } from '@ant-design/icons';

import { useEffect, useState } from 'react';

import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import {
    getGroupEnergyListApi,
    getGroupApplyListApi,
    getGroupMemberListApi,
} from '@/services/Marketing/MarketingGroupApi';

import commonStyles from '@/assets/styles/common.less';
import classnames from 'classnames';
const TAB_TYPES = {
    MEMBER: 'tab1',
    APPLY: 'tab2',
};
const tabList = [
    {
        key: TAB_TYPES.MEMBER,
        tab: '团队成员',
    },
    {
        key: TAB_TYPES.APPLY,
        tab: '申请日志',
    },
];

const energyColumns = [
    {
        title: '时间',
        dataIndex: 'creatTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '手机号',
        dataIndex: 'mobile',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '能量',
        dataIndex: 'energy',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '行为',
        dataIndex: 'recordTypeName',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
];

const applyColumns = [
    {
        title: '申请时间',
        dataIndex: 'applyTime',
        width: 240,

        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '手机号',
        width: 180,
        dataIndex: 'mobile',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '申请内容',
        dataIndex: 'applyContent',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '申请状态',
        dataIndex: 'applyStatusName',
        width: 140,

        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '审核时间',
        width: 240,
        dataIndex: 'approvalTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
];

const TabView = (props) => {
    const { tabType } = props;

    if (tabType === TAB_TYPES.MEMBER) {
        return <MemberView {...props} />;
    } else if (tabType === TAB_TYPES.APPLY) {
        return <ApplyView {...props} />;
    }

    return null;
};

const ApplyView = (props) => {
    const { groupId } = props;
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'apply');

    const [applyList, updateApplyList] = useState([]);
    const [applyListTotal, updateApplyListTotal] = useState(0);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            let options = {
                teamId: groupId,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };
            const { data } = await getGroupApplyListApi(options);
            const { records, total } = data;
            updateApplyListTotal(total);
            updateApplyList(records);
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <TablePro
            name="apply"
            loading={false}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            dataSource={applyList}
            columns={applyColumns}
            onChange={onTableChange}
            pagination={{
                current: pageInfo.pageIndex,
                total: applyListTotal,
                pageSize: pageInfo.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
            }}
        ></TablePro>
    );
};

const MemberView = (props) => {
    const { groupId } = props;
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'member');

    const [memberList, updateMemberList] = useState([]);
    const [memberListTotal, updateMemberListTotal] = useState(0);

    const memberColumns = [
        {
            title: '手机号',
            dataIndex: 'mobile',
            width: 160,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户类型',
            dataIndex: 'custNatureName',
            width: 160,

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '用户性质',
            dataIndex: 'custTypeName',
            width: 160,

            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '加入时间',
            dataIndex: 'joinTime',
            width: 200,

            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '本周贡献',
            dataIndex: 'curEnergy',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            let options = {
                teamId: groupId,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };
            const { data } = await getGroupMemberListApi(options);
            const { records, total } = data;
            updateMemberListTotal(total);
            updateMemberList(records);
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <TablePro
            name="member"
            loading={false}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            dataSource={memberList}
            columns={memberColumns}
            onChange={onTableChange}
            pagination={{
                current: pageInfo.pageIndex,
                total: memberListTotal,
                pageSize: pageInfo.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
            }}
        ></TablePro>
    );
};

const EnergyView = (props) => {
    const { groupId } = props;
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'energy');

    const [energyList, updateEnergyList] = useState([]);
    const [energyListTotal, updateEnergyListTotal] = useState(0);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            let options = {
                teamId: groupId,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };
            const { data } = await getGroupEnergyListApi(options);
            const { records, total } = data;
            updateEnergyListTotal(total);
            updateEnergyList(records);
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Card
            title={
                <div
                    className={classnames(commonStyles['form-title'])}
                    style={{ marginBottom: '0' }}
                >
                    能量日志
                </div>
            }
        >
            <TablePro
                name="entery"
                loading={false}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={energyList}
                columns={energyColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: energyListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            ></TablePro>
        </Card>
    );
};

const GroupDetailPage = (props) => {
    const { route, dispatch, history, groupModel } = props;

    const { groupInfo } = groupModel;
    const [activeTab, changeActiveTab] = useState(TAB_TYPES.MEMBER);
    const { groupId } = useParams();

    useEffect(() => {
        dispatch({
            type: 'groupModel/getGroupDetail',
            teamId: groupId,
        });

        return () => {
            dispatch({
                type: 'groupModel/updateGroupInfo',
                groupInfo: null,
            });
        };
    }, []);

    const goBack = () => {
        history.replace('/marketing/businessActive/group/manage');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card
                title={
                    <div
                        className={classnames(commonStyles['form-title'])}
                        style={{ marginBottom: '0' }}
                    >
                        团队信息
                    </div>
                }
            >
                <Descriptions>
                    <Descriptions.Item label="团队名称">
                        {(groupInfo && groupInfo.teamName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="团队编号">
                        {(groupInfo && groupInfo.teamNo) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="创建时间">
                        {(groupInfo && groupInfo.gmtCreate) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="团长号码">
                        {(groupInfo && groupInfo.commanderMobile) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="团队人数">
                        {(groupInfo && groupInfo.teamNum) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="团队等级">
                        {(groupInfo && groupInfo.levelName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="本周能量">
                        {(groupInfo && groupInfo.curEnergy) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="上周能量">
                        {(groupInfo && groupInfo.preEnergy) || ''}
                    </Descriptions.Item>
                </Descriptions>
            </Card>
            <br></br>
            <Card
                tabList={tabList}
                activeTabKey={activeTab}
                onTabChange={(key) => {
                    changeActiveTab(key);
                }}
            >
                <TabView tabType={activeTab} {...props} groupId={groupId}></TabView>
            </Card>
            <br></br>
            <EnergyView {...props} groupId={groupId}></EnergyView>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, groupModel, loading }) => ({
    global,
    user,
    groupModel,
    detalisLoading: loading.effects['groupModel/getGroupDetail'],
}))(GroupDetailPage);
