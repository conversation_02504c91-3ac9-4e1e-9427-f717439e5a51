import { Modal, Form, Space, Button, Row, Radio, Input, Divider, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

import {
    CommunityChannelEnum,
    CommunityChannelOptions,
    CommunityLinkStatusEnum,
    CommunityLinkStatusOptions,
} from '@/constants/community';
import CityTransferModal from '@/components/CityTransferModal';
import { useRequest } from 'ahooks';
import { addCommunityLink, updateCommunityLink } from '@/services/Marketing/CommunityApi';
import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';

const LinksForm: React.FC<{
    visible: boolean;
    onCancel: (refresh: boolean) => void;
    mode: 'ADD' | 'EDIT' | 'COPY';
    initValues?: any;
}> = ({ visible, onCancel, mode, initValues }) => {
    const [form] = Form.useForm();
    const cityModalRef = useRef<{ show: (props?: any) => void }>();
    const citys = Form.useWatch('citys', form);

    useEffect(() => {
        if (visible) {
            if (mode === 'EDIT' || mode === 'COPY') {
                form.setFieldsValue({ ...initValues, citys: initValues?.cityCode?.split(',') });
            } else {
                form.setFieldsValue({ chatGroupStatus: CommunityLinkStatusEnum.ENABLED });
            }
        }
    }, [visible, mode, initValues, form]);

    const cancel = (refresh: boolean = false) => {
        form.resetFields();
        onCancel(refresh);
    };

    const { run: addReq, loading: addLoading } = useRequest(
        (params) => {
            return addCommunityLink(params);
        },
        {
            manual: true,
            onSuccess: (result) => {
                if (result?.ret === 200) {
                    message.success('新增成功');
                    cancel(true);
                } else {
                    message.error(result?.msg || '新增失败');
                }
            },
        },
    );

    const { run: updateReq, loading: updateLoading } = useRequest(
        (params) => {
            return updateCommunityLink(params);
        },
        {
            manual: true,
            onSuccess: (result) => {
                if (result?.ret === 200) {
                    message.success('编辑成功');
                    cancel(true);
                } else {
                    message.error(result?.msg || '编辑失败');
                }
            },
        },
    );
    const chatGroupChannel = Form.useWatch('chatGroupChannel', form);
    const submit = (formData: any) => {
        const params: API.CommunityLinkVo = {
            ...formData,
            chatGroupName: formData.chatGroupName?.trim(),
            chatGroupLink: formData.chatGroupLink?.trim(),
        };
        if (mode === 'EDIT') {
            params.chatGroupId = initValues.chatGroupId;
            updateReq(params);
        } else {
            addReq(params);
        }
    };
    return (
        <>
            <Modal
                visible={visible}
                onCancel={() => {
                    cancel(false);
                }}
                title={mode === 'EDIT' ? '编辑链接' : '添加链接'}
                footer={null}
                destroyOnClose
                zIndex={99}
            >
                <Form form={form} onFinish={submit} wrapperCol={{ span: 16 }}>
                    <Form.Item
                        label="社群渠道"
                        name="chatGroupChannel"
                        required
                        rules={[{ required: true, message: '请选择社群渠道' }]}
                    >
                        <Radio.Group options={CommunityChannelOptions} />
                    </Form.Item>
                    <Form.Item
                        label="社群名称"
                        name="chatGroupName"
                        required
                        rules={[
                            {
                                required: true,
                                message: '请填写社群名称',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                    >
                        <Input
                            placeholder="请填写社群名称，限10字"
                            maxLength={10}
                            showCount
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item
                        label="社群城市"
                        name="citys"
                        required
                        rules={[
                            () => ({
                                validator() {
                                    if (!citys?.length) {
                                        return Promise.reject('请选择开通城市');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        {(citys?.length && (
                            <Space size="small">
                                <Button
                                    type="link"
                                    onClick={() => {
                                        cityModalRef?.current?.show({
                                            defaultKeys: citys,
                                        });
                                    }}
                                >
                                    编辑
                                </Button>

                                <Divider type="vertical" style={{ margin: 0 }} />
                                <Button
                                    type="link"
                                    onClick={() =>
                                        cityModalRef?.current?.show({
                                            defaultKeys: citys,
                                            disabled: true,
                                        })
                                    }
                                >
                                    查看
                                </Button>
                            </Space>
                        )) || (
                            <Button
                                type="primary"
                                onClick={() => {
                                    cityModalRef?.current?.show();
                                }}
                            >
                                城市
                            </Button>
                        )}
                    </Form.Item>
                    {chatGroupChannel === CommunityChannelEnum.ALIPAY && (
                        <Form.Item
                            label="社群链接"
                            name="chatGroupLink"
                            required
                            rules={[
                                {
                                    required: true,
                                    message: '请填写社群链接',
                                    transform: (v) => v?.trim(),
                                },
                            ]}
                        >
                            <Input placeholder="请填写社群链接" allowClear maxLength={500} />
                        </Form.Item>
                    )}
                    {chatGroupChannel === CommunityChannelEnum.WEIXIN && (
                        <Form.Item
                            label="社群码"
                            name="groupCodeUrl"
                            required
                            rules={[
                                {
                                    required: true,
                                    message: '请上传二维码图片',
                                },
                            ]}
                        >
                            <UpLoadImgCustom
                                initialValue={
                                    initValues?.groupCodeUrl
                                        ? [
                                              {
                                                  uid: initValues?.groupCodeUrl,
                                                  url: initValues?.groupCodeUrl,
                                                  status: 'done',
                                              },
                                          ]
                                        : []
                                }
                                sizeInfo={{ size: 100 }}
                                maxCount={1}
                                multiple={false}
                                placeholder="格式支持png、jpg、jpeg,大小不得超过100KB"
                                uploadData={{
                                    contentType: '02',
                                    contRemrk: 'groupCodeUrl',
                                    relaTable: 'a_station_label',
                                }}
                            />
                        </Form.Item>
                    )}
                    <Form.Item
                        label="社群状态"
                        name="chatGroupStatus"
                        required
                        rules={[{ required: true, message: '请选择社群状态' }]}
                    >
                        <Radio.Group options={CommunityLinkStatusOptions} />
                    </Form.Item>
                    <Form.Item wrapperCol={{ span: 24 }}>
                        <Row justify="center">
                            <Space>
                                <Button
                                    type="primary"
                                    htmlType="submit"
                                    loading={addLoading || updateLoading}
                                >
                                    提交
                                </Button>
                                <Button htmlType="reset" onClick={() => cancel(false)}>
                                    取消
                                </Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Modal>
            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys: any) => {
                    form.setFieldsValue({ citys: citys.map((ele: any) => ele.areaCode) });
                }}
                zIndex={100}
                deleteEnabled={false}
            />
        </>
    );
};

export default React.memo(LinksForm);
