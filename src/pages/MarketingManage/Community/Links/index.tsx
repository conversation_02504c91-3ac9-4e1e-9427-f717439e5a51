import { PlusCircleOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest, useUpdateEffect } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Popconfirm,
    Select,
    Space,
    Tabs,
    Typography,
    message,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { Link } from 'umi';

import styles from '@/assets/styles/common.less';
import {
    CommunityChannelOptions,
    CommunityLinkStatusEnum,
    CommunityLinkStatusTabs,
} from '@/constants/community';
import {
    deleteCommunityLink,
    queryCommunityLinkList,
    exportCommunityLinkList,
} from '@/services/Marketing/CommunityApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import CitysSelect from '@/components/CitysSelect';
import TablePro from '@/components/TablePro';
import LinksForm from './components/LinksForm';

const LinksPage = () => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string>(CommunityLinkStatusTabs[0].key);
    const [showForm, setShowForm] = useState<boolean>(false);
    const [initValues, setInitValues] = useState();
    const [formMode, setFormMode] = useState<'ADD' | 'EDIT' | 'COPY'>('ADD');
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryCommunityLinkList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: deleteRequest, loading: deleteLoading } = useRequest(
        (id) => {
            return deleteCommunityLink(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('删除成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
            onError: () => {
                message.error('删除失败');
            },
        },
    );

    const { run: exportRequest, loading: exportLoading } = useRequest(
        (params) => {
            return exportCommunityLinkList(params);
        },
        {
            manual: true,
        },
    );

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params: API.CommunityPaginationRequest = {
            ...formData,
            chatGroupName: formData?.chatGroupName?.trim(),
        };
        if (currentTab && currentTab !== CommunityLinkStatusTabs[0].key) {
            params.chatGroupStatus = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const exportForm = () => {
        exportRequest(getParams());
    };

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize });
    }, []);

    useUpdateEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const refreshList = () => {
        searchList(pagination, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const confirmDelete = (id: number) => {
        deleteRequest(id);
    };

    const onModalCancel = (refresh: boolean) => {
        setShowForm(false);
        setFormMode('ADD');
        setInitValues(undefined);
        if (refresh) {
            refreshList();
        }
    };

    const addLink = () => {
        setShowForm(true);
        setFormMode('ADD');
        setInitValues(undefined);
    };

    const columns: ColumnsType<any> = [
        {
            title: '社群渠道',
            width: 120,
            dataIndex: 'groupChannelName',
        },
        {
            title: '社群名称',
            width: 120,
            dataIndex: 'chatGroupName',
        },
        {
            title: '社群城市',
            width: 120,
            dataIndex: 'cityName',
        },
        {
            title: '社群状态',
            width: 180,
            dataIndex: 'statusName',
            render: (value: string, record: API.CommunityLinkVo) => {
                return (
                    <Typography.Text
                        title={value}
                        type={
                            record?.chatGroupStatus === CommunityLinkStatusEnum.ENABLED
                                ? 'success'
                                : 'danger'
                        }
                    >
                        {value}
                    </Typography.Text>
                );
            },
        },
        {
            title: '更新时间',
            width: 120,
            dataIndex: 'updatedTime',
        },
        {
            title: '操作',
            width: 140,
            dataIndex: 'chatGroupId',
            fixed: 'right',
            sorter: false,
            render: (id, record) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                setShowForm(true);
                                setFormMode('EDIT');
                                setInitValues(record);
                            }}
                        >
                            编辑
                        </Typography.Link>
                        <Popconfirm
                            title="确认要删除该链接吗？"
                            onConfirm={() => confirmDelete(id)}
                            okButtonProps={{ loading: deleteLoading }}
                        >
                            <Typography.Link>删除</Typography.Link>
                        </Popconfirm>
                        <Typography.Link
                            onClick={() => {
                                setShowForm(true);
                                setFormMode('COPY');
                                setInitValues(record);
                            }}
                        >
                            复制
                        </Typography.Link>
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish} labelCol={{ span: 6 }}>
                    <SearchOptionsBar
                        loading={listLoading}
                        onReset={resetForm}
                        minSpan={24 * 2}
                        onExportForm={exportForm}
                        exportPrivateLoading={exportLoading}
                    >
                        <Col span={8}>
                            <Form.Item label="社群渠道" name="chatGroupChannel">
                                <Select
                                    options={CommunityChannelOptions}
                                    allowClear
                                    placeholder="请选择"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="社群名称" name="chatGroupName">
                                <Input maxLength={50} placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <CitysSelect
                                label="城市"
                                name="citys"
                                placeholder="请选择"
                                formItemLayout={{ labelAlign: 'right' }}
                                showArrow
                                allowClear
                                provinceSelectable
                                rules={null}
                                multiple
                            />
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Button type="primary" icon={<PlusCircleOutlined />} onClick={addLink}>
                            新增
                        </Button>
                        <Link to="/marketing/community/landingpage">
                            <Button>页面配置</Button>
                        </Link>
                    </Space>
                </div>
                <Tabs defaultActiveKey={CommunityLinkStatusTabs[0].key} onChange={changeTabType}>
                    {CommunityLinkStatusTabs.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="chatGroupId"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
            <LinksForm
                visible={showForm}
                onCancel={onModalCancel}
                mode={formMode}
                initValues={initValues}
            />
        </PageHeaderWrapper>
    );
};

export default React.memo(LinksPage);
