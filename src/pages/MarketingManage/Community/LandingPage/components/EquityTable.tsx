import React, { useContext, useEffect, useRef, useState } from 'react';
import { Image, Select, Space, Tag, Typography } from 'antd';
import { Button, Form, Input, Popconfirm, Table } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { CommunityChannelOptions } from '@/constants/community';
import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';
import SelectColor from '@/components/SelectColor';
import { PlusOutlined } from '@ant-design/icons';

const EditableContext = React.createContext<FormInstance<any> | null>(null);

interface EditableRowProps {
    index: number;
}

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    children: React.ReactNode;
    dataIndex: keyof API.PageEquityVo;
    record: API.PageEquityVo;
    handleSave: (record: API.PageEquityVo) => void;
}

type EditableTableProps = Parameters<typeof Table>[0];

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

const EquityTable: React.FC<{
    form: FormInstance;
    fieldName: string;
    disabled: boolean;
    datas?: any[];
}> = ({ form, fieldName, disabled, datas }) => {
    const [dataSource, setDataSource] = useState<API.PageEquityVo[]>(datas || []);
    const handleDelete = (key: React.Key) => {
        const newDataSource = dataSource?.filter((v) => v?.key !== key);
        setDataSource(newDataSource);
        form?.setFieldsValue({
            [fieldName]: newDataSource?.length > 0 ? newDataSource : undefined,
        });
    };

    const handleAdd = () => {
        const newData: API.PageEquityVo = {
            key: Math.random(),
        };
        setDataSource([...dataSource, newData]);
    };

    const handleSave = (row: API.PageEquityVo) => {
        const newData = [...dataSource];
        const index = newData.findIndex((item) => row.key === item.key);
        if (index > -1) {
            const item = newData[index];
            newData[index] = {
                ...item,
                ...row,
            };
            setDataSource(newData);
            form?.setFieldsValue({ [fieldName]: newData });
        }
    };

    const moveUp = (index: number) => {
        if (index > 0) {
            const newDataSource = [...dataSource];
            const current = dataSource[index];
            const upItem = dataSource[index - 1];
            newDataSource[index - 1] = current;
            newDataSource[index] = upItem;
            setDataSource(newDataSource);
            form.setFieldsValue({ [fieldName]: newDataSource });
        }
    };

    const moveDown = (index: number) => {
        if (index < dataSource?.length - 1) {
            const newDataSource = [...dataSource];
            const current = dataSource[index];
            const downItem = dataSource[index + 1];
            newDataSource[index + 1] = current;
            newDataSource[index] = downItem;
            setDataSource(newDataSource);
            form.setFieldsValue({ [fieldName]: newDataSource });
        }
    };

    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex?: string })[] = [
        {
            title: '序号',
            width: 80,
            render: (_: any, record: any, index: number) => {
                return ` ${index + 1}`;
            },
        },
        {
            title: '投放渠道',
            width: 160,
            dataIndex: 'channel',
            editable: !disabled,
            render: (value: string) => {
                const text =
                    CommunityChannelOptions?.filter((v) => v.value === value)?.[0]?.label || '全部';
                return <Typography.Text title={text}>{text}</Typography.Text>;
            },
        },
        {
            title: '权益icon',
            width: 160,
            dataIndex: 'equityIconUrl',
            editable: !disabled,
            render: (value: string) => {
                return <Image src={value} width={48} preview={false} />;
            },
        },
        {
            title: '权益名称',
            width: 160,
            dataIndex: 'equityName',
            editable: !disabled,
        },
        {
            title: '权益描述',
            width: 160,
            dataIndex: 'equityDesc',
            editable: !disabled,
        },
        {
            title: '权益描述颜色',
            width: 160,
            dataIndex: 'equityDescColor',
            editable: !disabled,
            render: (value: string) => {
                return <Tag color={value}>{value}</Tag>;
            },
        },
    ];
    if (!disabled) {
        defaultColumns.push({
            title: '操作',
            width: 120,
            dataIndex: 'operation',
            render: (_, record: any, index: number) => {
                return (
                    <Space>
                        <Popconfirm title="确定要删除" onConfirm={() => handleDelete(record.key)}>
                            <Typography.Link>删除</Typography.Link>
                        </Popconfirm>
                        {index !== 0 && (
                            <Typography.Link
                                onClick={() => {
                                    moveUp(index);
                                }}
                            >
                                上移
                            </Typography.Link>
                        )}
                        {index !== dataSource?.length - 1 && (
                            <Typography.Link
                                onClick={() => {
                                    moveDown(index);
                                }}
                            >
                                下移
                            </Typography.Link>
                        )}
                    </Space>
                );
            },
        });
    }

    const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
        const [form] = Form.useForm();
        return (
            <Form form={form} component={false}>
                <EditableContext.Provider value={form}>
                    <tr {...props} style={{ textAlign: 'center', verticalAlign: 'top' }} />
                </EditableContext.Provider>
            </Form>
        );
    };
    const EditableCell: React.FC<EditableCellProps> = ({
        title,
        editable,
        children,
        dataIndex,
        record,
        handleSave,
        ...restProps
    }) => {
        const form = useContext(EditableContext);
        const save = async () => {
            try {
                const values = await form?.validateFields();
                handleSave({ ...record, ...values });
            } catch (errInfo) {}
        };

        let childNode = children;
        switch (dataIndex) {
            case 'channel':
                childNode = (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            {
                                required: true,
                                message: '请选择投放渠道',
                            },
                        ]}
                        initialValue={record?.channel}
                    >
                        <Select
                            options={[{ label: '全部', value: '0' }, ...CommunityChannelOptions]}
                            onChange={save}
                        />
                    </Form.Item>
                );
                break;
            case 'equityIconUrl':
                childNode = (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            {
                                required: true,
                                message: '请上传权益icon',
                            },
                        ]}
                        initialValue={record?.equityIconUrl}
                    >
                        <UpLoadImgCustom
                            sizeInfo={{ size: 100 }}
                            maxCount={1}
                            showUploderText={false}
                            showPlaceholder={false}
                            uploadData={{
                                contentType: '02',
                                contRemrk: dataIndex,
                                relaTable: 'a_station_label',
                            }}
                            initialValue={
                                record?.equityIconUrl
                                    ? [
                                          {
                                              uid: record?.equityIconUrl,
                                              url: record?.equityIconUrl,
                                              status: 'done',
                                          },
                                      ]
                                    : undefined
                            }
                            uploadButton={
                                <div
                                    style={{
                                        width: '60px',
                                        height: '60px',
                                        border: '1px solid rgba(187, 187, 187, 100)',
                                        textAlign: 'center',
                                        fontSize: '36px',
                                        color: 'rgba(16, 16, 16, 100)',
                                        lineHeight: '60px',
                                        borderRadius: '2px',
                                    }}
                                >
                                    <PlusOutlined />
                                </div>
                            }
                            listType="picture"
                            onChange={save}
                        />
                    </Form.Item>
                );
                break;
            case 'equityName':
                childNode = (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            {
                                required: true,
                                message: '请填写权益名称',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                        initialValue={record?.equityName}
                    >
                        <Input maxLength={10} placeholder="请输入，限10字" onBlur={save} />
                    </Form.Item>
                );
                break;
            case 'equityDesc':
                childNode = (
                    <Form.Item
                        style={{ margin: 0 }}
                        name={dataIndex}
                        rules={[
                            {
                                required: true,
                                message: '请填写权益描述',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                        initialValue={record?.equityDesc}
                    >
                        <Input maxLength={10} placeholder="请输入，限10字" onBlur={save} />
                    </Form.Item>
                );
                break;
            case 'equityDescColor':
                childNode = (
                    <SelectColor
                        style={{ margin: 0 }}
                        name={dataIndex}
                        form={form}
                        rules={[
                            {
                                required: true,
                                message: '请填写或者选择',
                                transform: (v: any) => v?.trim(),
                            },
                        ]}
                        onChange={save}
                        initialValue={record?.equityDescColor}
                    />
                );
                break;
            default:
                break;
        }
        return <td {...restProps}>{childNode}</td>;
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: API.PageEquityVo) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave,
            }),
        };
    });

    return (
        <div>
            {!disabled && (
                <Button onClick={handleAdd} type="primary" style={{ marginBottom: 16 }}>
                    新增
                </Button>
            )}
            <Table
                components={components}
                bordered
                rowKey={(record: API.PageEquityVo) => {
                    return record?.equityId || (record?.key as React.Key);
                }}
                dataSource={dataSource}
                columns={columns as ColumnTypes}
                pagination={false}
            />
        </div>
    );
};

export default React.memo(EquityTable);
