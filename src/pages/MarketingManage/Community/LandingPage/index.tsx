import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Row, Space, Tag, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { history } from 'umi';

import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';
import SelectColor from '@/components/SelectColor';
import EquityTable from './components/EquityTable';
import ActLinkItem from '@/components/ActLinkItem';
import { ACT_LINK_TYPES } from '@/config/declare';
import { useRequest } from 'ahooks';
import { savePageConfig, getPageConfig } from '@/services/Marketing/CommunityApi';

const LandingPage = () => {
    const [mode, setMode] = useState<'detail' | 'edit'>('detail');
    const disabled = mode === 'detail';
    const bgRef = useRef<any>();
    const iconRef = useRef<any>();

    const {
        run: queryConfig,
        data: configData,
        loading: queryLoading,
    } = useRequest(
        () => {
            return getPageConfig();
        },
        {
            manual: true,
        },
    );

    const { run: saveConfig, loading: saveLoading } = useRequest(
        (params) => {
            return savePageConfig(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('保存配置成功');
                    queryConfig();
                    setMode('detail');
                } else {
                    message.error(res?.msg || '保存配置失败');
                }
            },
        },
    );

    useEffect(() => {
        queryConfig();
    }, []);

    const [form] = Form.useForm();

    const saveFunc = (formData: API.PageConfigVo) => {
        console.error('save==>', formData);
        const params = {
            ...formData,
            groupEquityList: formData?.groupEquityList?.map(
                (v: API.PageEquityVo, index: number) => {
                    return { ...v, key: undefined, equitySort: index + 1 };
                },
            ),
        };
        saveConfig(params);
    };

    useEffect(() => {
        if (configData?.data?.headerImgUrl) {
            bgRef?.current?.init({
                uid: configData?.data?.headerImgUrl,
                url: configData?.data?.headerImgUrl,
                status: 'done',
            });
        }
        if (configData?.data?.iconUrl) {
            iconRef?.current?.init({
                uid: configData?.data?.iconUrl,
                url: configData?.data?.iconUrl,
                status: 'done',
            });
        }
    }, [configData?.data]);
    return (
        <PageHeaderWrapper loading={queryLoading}>
            <Card>
                <Form form={form} onFinish={saveFunc} scrollToFirstError>
                    <Form.Item
                        label="页面头图"
                        name="headerImgUrl"
                        required
                        rules={[{ required: true, message: '请上传页面头图' }]}
                        initialValue={configData?.data?.headerImgUrl}
                    >
                        <UpLoadImgCustom
                            sizeInfo={{ size: 100 }}
                            maxCount={1}
                            ref={bgRef}
                            placeholder="格式支持png、jpg、jpeg,大小不得超过100KB"
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'headerImgUrl',
                                relaTable: 'a_station_label',
                            }}
                            disabled={disabled}
                        />
                    </Form.Item>
                    {mode === 'detail' && (
                        <Form.Item label="页面背景" required>
                            {!!configData?.data?.bgColor && (
                                <Tag color={configData?.data?.bgColor}>
                                    {configData?.data?.bgColor}
                                </Tag>
                            )}
                        </Form.Item>
                    )}
                    {mode === 'edit' && (
                        <SelectColor
                            form={form}
                            wrapperCol={{ span: 8 }}
                            required
                            label="页面背景"
                            name="bgColor"
                            rules={[{ required: true, message: '请填写或者选择页面背景色' }]}
                            placeholder="请填写或者选择页面背景色"
                            disabled={disabled}
                            initialValue={configData?.data?.bgColor}
                        />
                    )}
                    <Form.Item
                        label="按钮图片"
                        name="iconUrl"
                        required
                        rules={[{ required: true, message: '请上传按钮图片' }]}
                        initialValue={configData?.data?.iconUrl}
                    >
                        <UpLoadImgCustom
                            ref={iconRef}
                            sizeInfo={{ size: 100 }}
                            maxCount={1}
                            placeholder="格式支持png、jpg、jpeg,大小不得超过100KB"
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'iconUrl',
                                relaTable: 'a_station_label',
                            }}
                            disabled={disabled}
                        />
                    </Form.Item>
                    <Form.Item
                        label="社群权益"
                        name="groupEquityList"
                        required
                        rules={[{ required: true, message: '请添加社群权益' }]}
                        initialValue={configData?.data?.groupEquityList}
                    >
                        <EquityTable
                            form={form}
                            fieldName="groupEquityList"
                            disabled={disabled}
                            datas={configData?.data?.groupEquityList?.map((v: API.PageEquityVo) => {
                                return { ...v, key: Math.random() };
                            })}
                        />
                    </Form.Item>
                    <ActLinkItem
                        label="页面链接"
                        disableds={[
                            ACT_LINK_TYPES.AMAP,
                            ACT_LINK_TYPES.BAIDU,
                            ACT_LINK_TYPES.BAIDU_MAP,
                            ACT_LINK_TYPES.YSF,
                            ACT_LINK_TYPES.WECHAT_LINK,
                            ACT_LINK_TYPES.WECHAT_QRCODE,
                        ]}
                        path="/pagesAssociation/join"
                        readOnly
                        wrapperCol={{ span: 12 }}
                    />
                    <Form.Item>
                        <Row justify="center">
                            <Space>
                                {mode === 'detail' && (
                                    <>
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                setMode('edit');
                                            }}
                                        >
                                            编辑
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                history.replace('/marketing/community/links');
                                            }}
                                        >
                                            返回
                                        </Button>
                                    </>
                                )}
                                {mode === 'edit' && (
                                    <>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            loading={saveLoading}
                                        >
                                            保存
                                        </Button>
                                        <Button
                                            htmlType="reset"
                                            onClick={() => {
                                                form?.resetFields();
                                                setMode('detail');
                                            }}
                                        >
                                            取消
                                        </Button>
                                    </>
                                )}
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(LandingPage);
