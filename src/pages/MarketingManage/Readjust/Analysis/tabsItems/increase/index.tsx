import ReactEcharts from 'echarts-for-react';
import { Card, Typography, Descriptions, Form, Space } from 'antd';
import { useMemo } from 'react';
import { useLocation } from 'umi';
import { useRequest } from 'ahooks';
import { getChargePqEffectApi, getStationPeriodsDetailApi } from '@/services/Marketing/ReadjustApi';
import SelectRadioBtnItem from '../../components/SelectRadioBtnItem';
import BiIframe from '../../components/BiIframe';
import { renderTableDataIndexText } from '@/utils/utils';
const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'left',
};
const AnalsisIncrease = (props: { stationId?: string; stationName?: string }) => {
    const { stationId = '', stationName } = props;

    const [form] = Form.useForm();
    const adjustModelType = Form.useWatch('adjustModelType', form);
    const { data, run, loading } = useRequest(
        async (params = {}) => {
            try {
                const values = form.getFieldsValue();
                const options = {
                    ...params,
                    ...values,
                    stationId,
                };
                const timePeriodItem = periodList.find(
                    (ele) => ele.timePeriodFlag == options.timePeriodFlag,
                );
                if (timePeriodItem) {
                    options.timePeriod = timePeriodItem.timePeriod;
                }
                const { data } = await getChargePqEffectApi(options);
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    const {
        run: initStationPeriods,
        data: periodList,
        loading: periodLoading,
    } = useRequest(async () => {
        try {
            const {
                data: { detailVos = [] },
            } = await getStationPeriodsDetailApi(stationId);
            setTimeout(() => {
                run();
            }, 200);
            return detailVos;
        } catch (error) {
            return Promise.reject(error);
        }
    });
    const echartsOptions = useMemo(() => {
        const { funcValueList = [] } = data ?? {};
        return {
            color: ['#3BA1FF', '#FBD438', '#F04864', '#F04864', '#9860E5', '#37CBCB'],
            xAxis: {
                name: 'x(价格：元)',
                type: 'category',
                axisTick: {
                    alignWithLabel: true,
                },
                data: funcValueList?.map((ele: any) => ele.x),
            },
            yAxis: {
                name: `y(${adjustModelType == 'market_share' ? '市占率' : '电量'}：${
                    adjustModelType == 'market_share' ? '%' : '度'
                })`,
                type: 'value',
            },
            series: [
                {
                    data: funcValueList?.map((ele: any) => ele.y),
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        opacity: 0.2,
                    },
                },
            ],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
                formatter: `x(价格：元)：{b}<br /> y(${
                    adjustModelType == 'market_share' ? '市占率' : '电量'
                }：${adjustModelType == 'market_share' ? '%' : '度'})：{c}`,
            },
        };
    }, [data, adjustModelType]);
    return (
        <Card
            loading={periodLoading}
            title={<Typography.Title level={4}>影响电量</Typography.Title>}
            bordered={false}
        >
            <p>
                <Typography.Text type="secondary">数据更新时间: {data?.billDate}</Typography.Text>
            </p>
            <p>
                <Space>
                    <Typography.Title level={5}>
                        当前主要影响场站电量因子为：{data?.maxElecFactor}
                    </Typography.Title>

                    <Typography.Title level={5}>
                        置信度：{renderTableDataIndexText({ text: data?.confidenceLevel })}
                    </Typography.Title>
                </Space>
            </p>

            <p>
                <Typography.Title level={5}>定价影响预测</Typography.Title>
            </p>

            <Form
                form={form}
                onValuesChange={() => {
                    run();
                }}
                scrollToFirstError
                {...formItemLayout}
            >
                <Form.Item label="预测指标" name="adjustModelType" initialValue={'charge_pq'}>
                    <SelectRadioBtnItem
                        options={[
                            {
                                label: '电量',
                                value: 'charge_pq',
                            },
                            {
                                label: '市占率',
                                value: 'market_share',
                            },
                        ]}
                    ></SelectRadioBtnItem>
                </Form.Item>
                <Form.Item
                    label="时段"
                    name="timePeriodFlag"
                    initialValue={periodList?.[0].timePeriodFlag}
                >
                    <SelectRadioBtnItem
                        options={periodList?.map((ele: any) => {
                            return {
                                label: ele.timePeriodFlagName,
                                value: ele.timePeriodFlag,
                            };
                        })}
                    ></SelectRadioBtnItem>
                </Form.Item>

                <Form.Item label="节假日" name="holidayFlag" initialValue={'0'}>
                    <SelectRadioBtnItem
                        options={[
                            {
                                label: '工作日',
                                value: '0',
                            },
                            // {
                            //     label: '节假日',
                            //     value: '1',
                            // },
                        ]}
                    ></SelectRadioBtnItem>
                </Form.Item>

                <Form.Item label="价格类型" name="priceType" initialValue={'0'}>
                    <SelectRadioBtnItem
                        options={[
                            {
                                label: '普通',
                                value: '0',
                            },
                            {
                                label: '会员',
                                value: '1',
                            },
                        ]}
                    ></SelectRadioBtnItem>
                </Form.Item>
            </Form>

            <ReactEcharts style={{ width: '100%' }} option={echartsOptions} showLoading={loading} />

            <BiIframe
                stationId={stationId}
                stationName={stationName}
                pageId="de6dbcbb-60fc-4a9d-b40a-8071f83c36b7"
            ></BiIframe>
        </Card>
    );
};

export default AnalsisIncrease;
