import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card } from 'antd';
import React, { useEffect } from 'react';
import { connect, useParams } from 'umi';

import TaskForm from '../components/Form';

const EditTaskPage: React.FC = (props: any) => {
    const { dispatch, global, detalisLoading, businessTaskManageModel, route } = props;

    const { editActInfo }: { editActInfo: API.BusinessActDetailVo } = businessTaskManageModel;

    const { taskId } = useParams() as any;
    useEffect(() => {
        if (taskId) {
            dispatch({
                type: 'businessTaskManageModel/getTaskDetail',
                taskId,
            });
        }
    }, [taskId]);

    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route?.name}
                </div>
            }
        >
            <Card loading={detalisLoading}>
                <TaskForm
                    global={global}
                    dispatch={dispatch}
                    initialValues={editActInfo}
                    mode="EDIT"
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, businessTaskManageModel, loading }: any) => ({
    global,
    businessTaskManageModel,
    detalisLoading: loading.effects['businessTaskManageModel/getTaskDetail'],
}))(EditTaskPage);
