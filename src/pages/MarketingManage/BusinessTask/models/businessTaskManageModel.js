import { getTaskList<PERSON><PERSON>, getTaskDetailApi } from '@/services/Marketing/BusinessTaskApi';

const businessTaskManageModel = {
    namespace: 'businessTaskManageModel',
    state: {
        taskList: [], // 列表
        taskListTotal: 0, // 条数

        editActInfo: null, // 详情
    },
    effects: {
        /**
         *
         */
        *getTaskList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: taskList, total: taskListTotal },
                } = yield call(getTaskListApi, options);

                yield put({
                    type: 'updateManageList',
                    list: taskList,
                    total: taskListTotal,
                });
                return taskList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *getTaskDetail({ taskId }, { call, put, select }) {
            try {
                const { data } = yield call(getTaskDetailApi, taskId);

                yield put({
                    type: 'updateTaskInfo',
                    taskInfo: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateManageList(state, { list, total }) {
            return {
                ...state,
                taskList: list,
                taskListTotal: total,
            };
        },
        updateTaskInfo(state, { taskInfo }) {
            return {
                ...state,
                editActInfo: taskInfo,
            };
        },
    },
};
export default businessTaskManageModel;
