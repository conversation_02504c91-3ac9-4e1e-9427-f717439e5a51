import { Col, Form, FormInstance, Input, Select } from 'antd';
import React from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import { TASK_TYPE_OPTIONS } from '../../constants';

const SearchLayout: React.FC<{
    form: FormInstance;
    listLoading: boolean;
    onSubmit: (formData: any) => void;
    onReset: () => void;
}> = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;
    const onFinish = (values: any) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24}>
                <Col span={8}>
                    <Form.Item label="活动名称" name="actName">
                        <Input placeholder="请填写活动名称" autoComplete="off" allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="活动类型" name="actSubType">
                        <Select
                            placeholder="请选择"
                            options={TASK_TYPE_OPTIONS}
                            allowClear
                        ></Select>
                    </Form.Item>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
