import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { <PERSON>ton, Card, Form, Space, Popconfirm, message, Spin, Typography, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { connect, history } from 'umi';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { deleteTaskApi } from '@/services/Marketing/MarketingTaskApi';
import SearchLayout from './components/SearchLayout';
import { TASK_STATUS_ENUM, TASK_STATUS_OPTIONS } from '../constants';
import { updateTaskStatusApi } from '@/services/Marketing/BusinessTaskApi';

const TaskManageListPage = (props: any) => {
    const {
        dispatch,
        listLoading,
        businessTaskManageModel: { taskList, taskListTotal },
    } = props;
    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const [deleteTaskId, setDeleteTaskId] = useState<string>('');
    const [abortTaskId, setAbortTaskId] = useState<string>('');
    const [activeStatus, setActiveStatus] = useState<string>(TASK_STATUS_OPTIONS[0].value);
    useEffect(() => {
        searchData();
    }, [pageInfo, activeStatus]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then(() => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    actName: data?.actName?.trim(),
                    actState:
                        activeStatus !== TASK_STATUS_OPTIONS[0].value ? activeStatus : undefined,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });
                dispatch({
                    type: 'businessTaskManageModel/getTaskList',
                    options: params,
                });
            } catch (error) {
                console.error(error);
            }
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const goAddEvent = () => {
        history.push('/marketing/platformactive/business/list/add');
    };

    const goEditEvent = (item: any) => {
        if (item?.actId) history.push(`/marketing/platformactive/business/list/edit/${item.actId}`);
    };
    const goLookEvent = (item: any) => {
        if (item?.actId)
            history.push(`/marketing/platformactive/business/list/detail/${item.actId}`);
    };
    const deleteTask = (item: any) => {
        const id = item?.actId;
        setDeleteTaskId(id);
        deleteTaskApi(id)
            .then((res: any) => {
                if (res?.ret === 200) {
                    message.success(res?.msg || '删除成功');
                    searchData();
                } else {
                    message.error(res?.msg || '删除活动失败，请稍后再试');
                }
                setDeleteTaskId('');
            })
            .catch(() => {
                message.error('删除活动失败，请稍后再试');
                setDeleteTaskId('');
            });
    };

    const abortTask = (item: any) => {
        const id = item?.actId;
        setAbortTaskId(id);
        updateTaskStatusApi(id, TASK_STATUS_ENUM.STOP)
            .then((res: any) => {
                if (res?.ret === 200) {
                    message.success(res?.msg || '终止成功');
                    searchData();
                } else {
                    message.error(res?.msg || '终止活动失败，请稍后再试');
                }
                setAbortTaskId('');
            })
            .catch(() => {
                message.error('终止活动失败，请稍后再试');
                setAbortTaskId('');
            });
    };

    const columns = [
        {
            title: '活动ID',
            width: 80,
            dataIndex: 'actId',
            fixed: 'left',
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称',
            width: 160,
            dataIndex: 'actName',
            fixed: 'left',
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动类型',
            width: 120,
            dataIndex: 'actSubTypeName',
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动时间',
            width: 200,
            dataIndex: 'actTime',
        },
        {
            title: '更新时间',
            width: 140,
            dataIndex: 'dataOperTime',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'actStateName',
        },
        {
            title: '操作',
            width: 180,
            dataIndex: 'actId',
            sorter: false,
            fixed: 'right',
            render: (value: string, record: any) => {
                return (
                    <Space>
                        <Typography.Link onClick={() => goLookEvent(record)}>详情</Typography.Link>
                        {[
                            TASK_STATUS_ENUM.CRAFT,
                            TASK_STATUS_ENUM.NOSTART,
                            TASK_STATUS_ENUM.RUNNING,
                        ].includes(record?.actStatus) && (
                            <Typography.Link onClick={() => goEditEvent(record)}>
                                编辑
                            </Typography.Link>
                        )}
                        {TASK_STATUS_ENUM.RUNNING === record?.actStatus && (
                            <Popconfirm
                                title="确认要终止该活动吗？"
                                onConfirm={() => {
                                    abortTask(record);
                                }}
                            >
                                <Spin spinning={value === abortTaskId} size="small">
                                    <Typography.Link type="warning">终止</Typography.Link>
                                </Spin>
                            </Popconfirm>
                        )}
                        {/* {TASK_STATUS_ENUM.STOP === record?.actStatus && (
                            <Popconfirm
                                title="确认要启用该活动吗？"
                                onConfirm={() => {
                                    abortTask(record);
                                }}
                            >
                                <Spin spinning={value === abortTaskId} size="small">
                                    <Typography.Link type="warning">启用</Typography.Link>
                                </Spin>
                            </Popconfirm>
                        )} */}
                        {[TASK_STATUS_ENUM.CRAFT, TASK_STATUS_ENUM.NOSTART].includes(
                            record?.actStatus,
                        ) && (
                            <Popconfirm
                                title="确认删除该活动吗？"
                                onConfirm={() => {
                                    deleteTask(record);
                                }}
                            >
                                <Spin spinning={value === deleteTaskId} size="small">
                                    <Typography.Link type="danger">删除</Typography.Link>
                                </Spin>
                            </Popconfirm>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    listLoading={listLoading}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state: any) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                ></SearchLayout>
                <Button type="primary" onClick={goAddEvent}>
                    + 新建
                </Button>
                <div className="mg-t-20"></div>
                <Tabs
                    onChange={(v) => {
                        setActiveStatus(v);
                    }}
                    activeKey={activeStatus}
                >
                    {TASK_STATUS_OPTIONS?.map((v) => (
                        <Tabs.TabPane key={v?.value} tabKey={v?.value} tab={v?.label} />
                    ))}
                </Tabs>
                <TablePro
                    name="tasklist"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    offsetHeader={false}
                    rowKey="actId"
                    dataSource={taskList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: taskListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total} 条`,
                    }}
                ></TablePro>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, businessTaskManageModel, loading }: any) => ({
    global,
    businessTaskManageModel,
    listLoading: loading.effects['businessTaskManageModel/getTaskList'],
}))(TaskManageListPage);
