import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Descriptions, Table, Typography } from 'antd';
import { connect, history, useParams, Link } from 'umi';
import React, { useEffect } from 'react';

import { TASK_TYPE_OPTIONS } from '../constants';

const labelStyle = {
    color: '#000',
    fontSize: '14px',
    fontWeight: '500',
};

const TaskDetailPage = (props: any) => {
    const { route, dispatch, detalisLoading, businessTaskManageModel } = props;

    const { editActInfo }: { editActInfo: API.BusinessActDetailVo } = businessTaskManageModel;
    const { taskId } = useParams() as any;

    useEffect(() => {
        if (taskId) {
            dispatch({
                type: 'businessTaskManageModel/getTaskDetail',
                taskId,
            });
        }
    }, [taskId]);

    const goBack = () => {
        history.replace('/marketing/platformactive/business/list');
    };

    const couponColumns = [
        {
            title: '序号 ',
            width: 80,
            render(text: string, record: any, index: number) {
                return <span>{index + 1}</span>;
            },
        },
        {
            title: '券名称 ',
            width: 140,
            dataIndex: 'prizeName',
            render(text: string) {
                return (
                    <span title={text}>{text?.length > 8 ? text.substr(0, 8) + '...' : text}</span>
                );
            },
        },
        {
            title: '券组编号 ',
            width: 140,
            dataIndex: 'prizeNo',
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠额度',
            width: 160,
            dataIndex: 'dctValueName',
            render(text: string) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '券类型',
            width: 120,
            dataIndex: 'prizeTypeName',
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '可用库存',
            width: 120,
            dataIndex: 'stockUpLimit',
            render(text: string, record: API.PrizeVo) {
                let count = 0;
                const { stockUpLimit, stockLimitNum, totalUseStockNum = 0 } = record;
                if (stockUpLimit) {
                    const diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    count = diff > 0 ? diff : 0;
                    return <span>{count}</span>;
                } else {
                    return <span>{'-'}</span>;
                }
            },
        },
        {
            title: '已发数量',
            width: 120,
            dataIndex: 'prizePutNum',
        },
        {
            title: '单人发放量',
            width: 120,
            dataIndex: 'singlePutNum',
        },
        {
            title: '有效期',
            width: 220,
            dataIndex: 'expirationDesc',
        },
        {
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text: string, record: any) {
                return (
                    <Link
                        to={`/marketing/couponCenter/cpnManage/list/look/${record.prizeId}`}
                        target="_blank"
                    >
                        查看
                    </Link>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route?.name}
                </div>
            }
        >
            <Card loading={detalisLoading}>
                <Descriptions title="基础信息" column={3} layout="vertical" labelStyle={labelStyle}>
                    <Descriptions.Item label="活动名称">{editActInfo?.actName}</Descriptions.Item>
                    <Descriptions.Item label="活动类型">
                        {TASK_TYPE_OPTIONS.filter((v) => v?.value === editActInfo?.actSubType)?.[0]
                            ?.label || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="活动时间">
                        {editActInfo?.effTime
                            ? `${editActInfo?.effTime || '-'} ~ ${editActInfo?.expTime}` || '-'
                            : ''}
                    </Descriptions.Item>
                </Descriptions>
                <div style={{ marginTop: '16px', marginBottom: '16px' }}>
                    <Typography.Title level={5}>奖励明细</Typography.Title>
                    <Table
                        dataSource={editActInfo?.giftBagPrizeInfoVo?.detPrizeList}
                        rowKey="giftId"
                        columns={couponColumns as any}
                        pagination={false}
                    />
                    <Descriptions style={{ marginTop: '16px' }}>
                        <Descriptions.Item span={3} label="奖励份数">
                            {editActInfo?.giftBagPrizeInfoVo?.stockNum}
                        </Descriptions.Item>
                        <Descriptions.Item span={3} label="奖励已发">
                            {editActInfo?.giftBagPrizeInfoVo?.giftBagPutNum}
                        </Descriptions.Item>
                    </Descriptions>
                </div>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, businessTaskManageModel, loading }: any) => ({
    global,
    businessTaskManageModel,
    detalisLoading: loading.effects['businessTaskManageModel/getTaskDetail'],
}))(TaskDetailPage);
