import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card } from 'antd';
import { connect } from 'umi';

import TaskForm from '../components/Form';

const AddTaskPage = (props: any) => {
    const { dispatch, global, route } = props;

    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route?.name}
                </div>
            }
        >
            <Card>
                <TaskForm
                    initialValues={undefined}
                    global={global}
                    dispatch={dispatch}
                    mode="ADD"
                />
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global }: any) => ({
    global,
}))(AddTaskPage);
