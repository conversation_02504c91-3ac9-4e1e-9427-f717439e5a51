import {
    Button,
    Form,
    message,
    Radio,
    Input,
    notification,
    Typography,
    DatePicker,
    Select,
} from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import moment from 'moment';
import React, { useState, useEffect, useMemo } from 'react';
import { history } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import { addTaskApi } from '@/services/Marketing/BusinessTaskApi';
import { REWARD_TYPE_OPTIONS, TASK_STATUS_ENUM, TASK_TYPE_OPTIONS } from '../constants';
import { CouponComponents } from '@/newComponents/SelectPrizeItem/SelectCouponItem';
import { NEW_COUPON_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
        result.push(i);
    }
    return result;
};

const TaskFormPage: React.FC<{
    global: any;
    dispatch: any;
    mode: 'ADD' | 'EDIT';
    initialValues: any;
}> = ({ global, dispatch, mode, initialValues }) => {
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [form] = Form.useForm();

    const refreshData = () => {
        if (initialValues?.actId) {
            dispatch({
                type: 'businessTaskManageModel/getTaskDetail',
                taskId: initialValues?.actId,
            });
        }
    };

    const submitSaveEvent = async (values: any, saveType: 'save' | 'send') => {
        changeSubmitLoading(true);
        try {
            const params = {
                ...values,
                actName: values.actName?.trim(),
                actId: mode === 'EDIT' ? initialValues?.actId : undefined,
                rewardType: undefined,
                effTime:
                    values?.actTimeRange &&
                    values?.actTimeRange[0] &&
                    values?.actTimeRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
                expTime:
                    values?.actTimeRange &&
                    values?.actTimeRange[1] &&
                    values?.actTimeRange[1]?.format('YYYY-MM-DD HH:mm:ss'),
                actTimeRange: undefined,
                giftBagPrizeInfoParams: values?.prizeList?.map((v: API.PrizeVo) => {
                    return {
                        prizeId: v?.prizeId,
                        singlePutNum: v?.singlePutNum,
                    };
                }),
                alipayCouponFlag:
                    values?.prizeList?.filter((v: API.PrizeVo) => {
                        return v?.prizeType == NEW_COUPON_TYPES.ALI;
                    })?.length > 0
                        ? '1'
                        : '0',
                prizeList: undefined,
                saveType,
            };
            const result: any = await addTaskApi(params);

            changeSubmitLoading(false);
            const data = result?.data;
            if (data && result?.ret === 200) {
                if (data?.conflictId) {
                    notification.error({
                        message: (
                            <>
                                与
                                <Typography.Link
                                    href={`${window.routerBase}marketing/platformactive/business/list/detail/${data?.conflictId}`}
                                    target="_blank"
                                >
                                    {data?.conflictName || '点击查看'}
                                </Typography.Link>
                                任务重复
                            </>
                        ),
                    });
                } else {
                    message.success('保存成功');
                    history.push('/marketing/platformactive/business/list');
                }
            } else {
                notification.error({
                    message: mode === 'ADD' ? '添加任务失败' : '编辑任务失败',
                });
            }
            return;
        } catch (error) {
            changeSubmitLoading(false);
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (mode === 'EDIT') {
            form.setFieldsValue({
                actName: initialValues?.actName,
                actTimeRange: [
                    initialValues?.effTime ? moment(initialValues?.effTime) : undefined,
                    initialValues?.expTime ? moment(initialValues?.expTime) : undefined,
                ],
                stockNum: initialValues?.giftBagPrizeInfoVo?.stockNum,
                prizeList: initialValues?.giftBagPrizeInfoVo?.detPrizeList,
            });
        }
    }, [initialValues, mode]);

    const rangePickerDisabled: boolean = useMemo(() => {
        return initialValues?.actState !== TASK_STATUS_ENUM.CRAFT && mode === 'EDIT';
    }, [mode, initialValues]);
    const actName = Form.useWatch('actName', form);

    const disabledRangeTime: RangePickerProps['disabledTime'] = (_, type) => {
        if (rangePickerDisabled && type === 'end') {
            const now = moment();
            return {
                disabledHours: () => range(0, parseInt(now.format('HH'))),
                disabledMinutes: () => range(0, parseInt(now.format('mm'))),
                disabledSeconds: () => [0, parseInt(now.format('ss'))],
            };
        }
        return {
            disabledHours: () => [],
            disabledMinutes: () => [],
            disabledSeconds: () => [],
        };
    };

    const disabledDate: RangePickerProps['disabledDate'] = (current) => {
        return rangePickerDisabled && current && current < moment().startOf('day');
    };

    return (
        <Form form={form} scrollToFirstError wrapperCol={{ span: 8 }}>
            <div className={commonStyles['form-title']}>基础信息</div>
            <FormItem
                name="actName"
                label="活动名称"
                rules={[
                    {
                        required: true,
                        transform: (v) => v?.trim(),
                        message: '请输入活动名称',
                    },
                ]}
            >
                <Input placeholder="请输入，限15字" autoComplete="off" maxLength={15}></Input>
            </FormItem>
            <FormItem
                name="actTimeRange"
                label="活动时间"
                rules={[
                    {
                        required: true,
                        message: '请输入活动时间',
                    },
                ]}
            >
                <DatePicker.RangePicker
                    showTime={{
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    }}
                    disabled={rangePickerDisabled ? [true, false] : [false, false]}
                    disabledDate={disabledDate}
                    disabledTime={disabledRangeTime}
                    format="YYYY-MM-DD HH:mm:ss"
                />
            </FormItem>
            <FormItem
                name="actSubType"
                label="活动类型"
                rules={[
                    {
                        required: true,
                        message: '请选择活动类型',
                    },
                ]}
                initialValue={TASK_TYPE_OPTIONS[0].value}
            >
                <Select disabled={mode === 'EDIT'} options={TASK_TYPE_OPTIONS} />
            </FormItem>
            <FormItem
                name="rewardType"
                label="活动奖励"
                rules={[
                    {
                        required: true,
                        message: '请配置活动奖励',
                    },
                ]}
                initialValue={REWARD_TYPE_OPTIONS[0].value}
            >
                <Radio.Group options={REWARD_TYPE_OPTIONS} disabled={mode === 'EDIT'} />
            </FormItem>
            <CouponComponents
                listName="prizeList"
                global={global}
                dispatch={dispatch}
                actId={initialValues?.actId}
                actSubId={initialValues?.giftBagPrizeInfoVo?.actSubId}
                appendExtParams={{
                    actType: initialValues?.actType,
                    stockParamList: { actSubId: initialValues?.giftBagPrizeInfoVo?.actSubId },
                }}
                form={form}
                disabled={initialValues?.actState === TASK_STATUS_ENUM.RUNNING}
                canAppend={initialValues?.actState === TASK_STATUS_ENUM.RUNNING}
                onRefresh={refreshData}
                // couponRef={couponRef}
                formItemLayout={{
                    wrapperCol: {
                        span: 22,
                    },
                }}
            />
            <div className={commonStyles['form-submit']}>
                {(mode === 'ADD' || initialValues?.actState === TASK_STATUS_ENUM.CRAFT) && (
                    <Button
                        className={commonStyles['form-btn']}
                        type="primary"
                        loading={submitLoading}
                        disabled={!actName}
                        onClick={() => {
                            try {
                                const formData = form.getFieldsValue(true);
                                submitSaveEvent(formData, 'save');
                            } catch (error) {}
                        }}
                    >
                        保存
                    </Button>
                )}
                <Button
                    className={commonStyles['form-btn']}
                    onClick={() => {
                        history.goBack();
                    }}
                >
                    取消
                </Button>
                <Button
                    className={commonStyles['form-btn']}
                    type="primary"
                    loading={submitLoading}
                    onClick={async () => {
                        try {
                            const formData = await form.validateFields();
                            submitSaveEvent(formData, 'send');
                        } catch (error) {}
                    }}
                >
                    提交
                </Button>
            </div>
        </Form>
    );
};

export default React.memo(TaskFormPage);
