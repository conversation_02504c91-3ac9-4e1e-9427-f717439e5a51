import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Form,
    Tooltip,
    Radio,
    Space,
    Button,
    Modal,
    Row,
    Col,
    Input,
    message,
    Popconfirm,
} from 'antd';

import { InfoCircleOutlined, PlusOutlined, EditOutlined } from '@ant-design/icons';
import { ACTSUBTYPES, ACT_VERSION_TYPES } from '@/config/declare';
import SelectColor from '@/components/SelectColor';
import UpLoadImg from '@/components/UpLoadImg/index';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import LayoutPreview from '@/components/LayoutPreview';
import { saveTemplateApi, delTemplateApi } from '@/services/Marketing/MarketingGiftApi';
import templateStyles from './TemplateLayout.less';
import classnames from 'classnames';

import couponBgImg from '@/assets/images/novice_coupon_bg.png';

const FormItem = Form.Item;

export const TemplatePopupView = (props) => {
    const { version } = props;
    if (version !== ACT_VERSION_TYPES.V_2_0) {
        return null;
    }
    return (
        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
            {({ getFieldValue }) => {
                const actSubType = getFieldValue('actSubType');
                const popIcon = getFieldValue('popIcon');
                const actPushBoList = getFieldValue('actPushBoList');
                const buttonIcon = getFieldValue('buttonIcon');
                const buttonTitle = getFieldValue('buttonTitle');
                const hourTimes = getFieldValue('hourTimes');
                const minuteTimes = getFieldValue('minuteTimes');
                const popType = getFieldValue('popType');

                const { cpnList = [] } = actPushBoList?.[0] || {};

                let layouts = [];
                layouts.push(
                    <div className={templateStyles['template-pop-view']}>
                        <div className={templateStyles['pop-window']}>
                            {popType === '1' && (
                                <div className={templateStyles['star-animation']}></div>
                            )}

                            <div className={templateStyles['pop-header']}>
                                <img src={popIcon}></img>
                            </div>
                            <div className={templateStyles['pop-coupon']}>
                                {cpnList.map((ele, index) => {
                                    return (
                                        <div
                                            className={templateStyles['pop-coupon-item']}
                                            key={index}
                                        >
                                            <img
                                                className={templateStyles['pop-coupon-item-bg']}
                                                src={couponBgImg}
                                                alt=""
                                            />
                                            <div className={templateStyles['pop-coupon-item-info']}>
                                                <div className={templateStyles['info-left']}>
                                                    <span className={templateStyles['left-price']}>
                                                        {`${ele.dctValue}`}
                                                    </span>
                                                    <span className={templateStyles['left-unit']}>
                                                        {ele.dctType === '02' ? '折' : '元'}
                                                    </span>
                                                </div>
                                                <div className={templateStyles['info-right']}>
                                                    <div className={templateStyles['right-title']}>
                                                        {`${ele.prizeName}`}
                                                    </div>
                                                    <div className={templateStyles['right-remark']}>
                                                        {`满${ele.dctCondValue}元可用`}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                            <div className={templateStyles['pop-btn-bar']}>
                                <div className={templateStyles['pop-refuse']}>放弃领取</div>
                                <div className={templateStyles['pop-confirm']}>
                                    <img
                                        className={templateStyles['confirm-img']}
                                        src={buttonIcon}
                                        alt=""
                                    />
                                    <div className={templateStyles['confirm-info']}>
                                        <div className={templateStyles['confirm-title']}>
                                            {buttonTitle}
                                        </div>
                                        {(hourTimes || minuteTimes) && (
                                            <div className={templateStyles['confirm-countdown']}>
                                                {`${hourTimes || '00'}:${minuteTimes || '00'}:00`}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>,
                );

                return actSubType === ACTSUBTYPES.POPUP && <LayoutPreview layouts={layouts} />;
            }}
        </FormItem>
    );
};

export const TemplatePreview = (props) => {
    const {
        appletGiftModel: { templateList },
        appletGiftModel2: { templateList2 },
        version,
    } = props;

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return templateList2;
        }
        return templateList;
    }, [version, templateList, templateList2]);

    const rate = 2.5;
    const bgSize = { width: 780 / rate, height: 1688 / rate };
    const buttonSize = { width: 650 / rate, height: 120 / rate };
    const headSize = { width: 780 / rate, height: 535 / rate };
    return (
        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
            {({ getFieldValue }) => {
                const activityForm = getFieldValue('activityForm');
                const actSubType = getFieldValue('actSubType');
                const isInfoTemplate = getFieldValue('isInfoTemplate');
                const infoTemplateId = getFieldValue('infoTemplateId');
                const directDisplay = getFieldValue('directDisplay'); // 如果=='1'，表示直接展示优惠券信息，预览就不需要显示第一页
                const templateObj = list?.find((ele) => ele.infoTemplateId == infoTemplateId);
                const bgIcon =
                    isInfoTemplate == '1' ? templateObj?.bgIcon : getFieldValue('bgIcon');
                const buttonIcon =
                    isInfoTemplate == '1' ? templateObj?.buttonIcon : getFieldValue('buttonIcon');
                const headIcon =
                    isInfoTemplate == '1' ? templateObj?.headIcon : getFieldValue('headIcon');
                const bgColour =
                    isInfoTemplate == '1' ? templateObj?.bgColour : getFieldValue('bgColour');
                const buttonColour =
                    isInfoTemplate == '1'
                        ? templateObj?.buttonColour
                        : getFieldValue('buttonColour');

                const buttonTitle =
                    isInfoTemplate == '1' ? templateObj?.buttonTitle : getFieldValue('buttonTitle');
                const subButtonTitle =
                    isInfoTemplate == '1'
                        ? templateObj?.subButtonTitle
                        : getFieldValue('subButtonTitle');

                const layouts = [];
                if (directDisplay != '1') {
                    layouts.push(
                        <div
                            key="1"
                            style={{
                                width: `${bgSize.width}px`,
                                height: `${bgSize.height}px`,
                                backgroundColor: bgColour,
                                position: 'relative',
                            }}
                        >
                            <img
                                key="bg"
                                style={{
                                    width: `${bgSize.width}px`,
                                    height: `${bgSize.height}px`,
                                }}
                                src={bgIcon}
                            />
                            <div
                                style={{
                                    position: 'absolute',
                                    bottom: '60px',
                                    width: `${buttonSize.width}px`,
                                    height: `${buttonSize.height}px`,
                                    left: `${(bgSize.width - buttonSize.width) / 2}px`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                {(buttonIcon && (
                                    <img
                                        src={buttonIcon}
                                        style={{
                                            position: 'absolute',
                                            width: `${buttonSize.width}px`,
                                            height: `${buttonSize.height}px`,
                                        }}
                                    />
                                )) ||
                                    null}
                                <span
                                    style={{
                                        zIndex: '1',
                                        color: buttonColour,
                                    }}
                                >
                                    立即领取
                                </span>
                            </div>
                        </div>,
                    );
                }
                layouts.push(
                    <div
                        key="2"
                        style={{
                            position: 'relative',
                            width: `${bgSize.width}px`,
                            height: `${bgSize.height}px`,
                            backgroundColor: bgColour,
                            overflowY: 'auto',
                        }}
                    >
                        {(headIcon && (
                            <img
                                key="top"
                                style={{
                                    width: `${headSize.width}px`,
                                    // height: `${headSize.height}px`,
                                }}
                                src={headIcon}
                            />
                        )) ||
                            null}
                        <div
                            style={{
                                position: 'relative',
                                textAlign: 'center',
                                paddingTop: '12px',
                            }}
                        >
                            <Space direction="vertical">
                                {[{}, {}, {}].map((_, index) => {
                                    return (
                                        <div
                                            key={index}
                                            style={{
                                                width: `${bgSize.width - 40}px`,
                                                height: '62px',
                                                backgroundColor: 'white',
                                                borderRadius: '12px',
                                                overflow: 'auto',
                                            }}
                                        >
                                            <Row style={{ height: '100%' }}>
                                                <Col
                                                    span={8}
                                                    style={{
                                                        backgroundColor: '#faf1b3',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                    }}
                                                >
                                                    <div>
                                                        <span
                                                            style={{
                                                                fontWeight: 'bold',
                                                                color: 'red',
                                                                fontSize: '14px',
                                                            }}
                                                        >
                                                            ¥ XX
                                                        </span>
                                                        <br />
                                                        <span
                                                            style={{
                                                                fontWeight: 'normal',
                                                                color: 'red',
                                                                fontSize: '12px',
                                                            }}
                                                        >
                                                            满x元立减
                                                        </span>
                                                    </div>
                                                </Col>
                                                <Col
                                                    flex="auto"
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                    }}
                                                >
                                                    <div>
                                                        <span
                                                            style={{
                                                                fontWeight: 'bold',
                                                                color: '#a33402',
                                                                fontSize: '14px',
                                                            }}
                                                        >
                                                            xx专享价
                                                        </span>
                                                        <br />
                                                        <span
                                                            style={{
                                                                color: 'gray',
                                                                fontSize: '10px',
                                                            }}
                                                        >
                                                            有效期：2022-xx-xx
                                                        </span>
                                                    </div>
                                                </Col>
                                            </Row>
                                        </div>
                                    );
                                })}
                            </Space>
                        </div>
                        <div
                            style={{
                                width: `${buttonSize.width}px`,
                                height: `${buttonSize.height}px`,
                                display: 'flex',
                                margin: '10px auto',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}
                        >
                            {(buttonIcon && (
                                <img
                                    src={buttonIcon}
                                    style={{
                                        position: 'absolute',
                                        width: `${buttonSize.width}px`,
                                        height: `${buttonSize.height}px`,
                                    }}
                                />
                            )) ||
                                null}
                            <div
                                style={{
                                    zIndex: '1',
                                    color: buttonColour,
                                    fontWeight: 'bold',
                                }}
                            >
                                {buttonTitle || '去使用'}
                            </div>
                            {subButtonTitle && (
                                <div
                                    style={{
                                        zIndex: '1',
                                        color: buttonColour,
                                        fontSize: '12px',
                                    }}
                                >
                                    {subButtonTitle || ''}
                                </div>
                            )}
                        </div>
                    </div>,
                );
                return (
                    activityForm == '01' &&
                    actSubType === ACTSUBTYPES.DIRECT && <LayoutPreview layouts={layouts} />
                );
            }}
        </FormItem>
    );
};

const TemplateLayout = (props) => {
    const { form, disabled, formItemFixedWidthLayout } = props;
    return (
        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
            {({ getFieldValue }) => {
                const activityForm = getFieldValue('activityForm');
                const actSubType = getFieldValue('actSubType');
                const isInfoTemplate = getFieldValue('isInfoTemplate');
                return (
                    <>
                        {/* 仅免费领取使用模板功能 */}
                        {(activityForm == '01' && actSubType === ACTSUBTYPES.DIRECT && (
                            <FormItem label={`免费页面模板`} {...formItemFixedWidthLayout} required>
                                <Space direction="vertical" style={{ padding: '8px 0 0' }}>
                                    <FormItem name="isInfoTemplate" noStyle initialValue={'1'}>
                                        <Radio.Group disabled={disabled}>
                                            <Radio value="1">默认模板</Radio>
                                            <Radio value="0">自定义</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                    {(isInfoTemplate == '1' && (
                                        <TemplateDefaultLayout {...props} />
                                    )) ||
                                        null}
                                </Space>
                            </FormItem>
                        )) ||
                            null}
                        {actSubType === ACTSUBTYPES.DIRECT &&
                        activityForm == '01' &&
                        isInfoTemplate == '1' ? null : (
                            <TemplateCustomLayout
                                {...props}
                                form={form}
                                activityForm={activityForm}
                                disabled={disabled}
                            />
                        )}
                    </>
                );
            }}
        </FormItem>
    );
};

// 默认模板
const TemplateDefaultLayout = (props) => {
    const {
        form,
        dispatch,
        disabled,
        formItemLayout: pageFormItemLayout,
        appletGiftModel: { templateList, templateInfo },
        appletGiftModel2: { templateList2, templateInfo2 },
        version,
    } = props;

    useEffect(() => {
        refreshTemplateList();
    }, []);

    const modelName = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return 'appletGiftModel2';
        }
        return 'appletGiftModel';
    }, [version]);

    const refreshTemplateList = () => {
        dispatch({
            type: `${modelName}/getTemplateList`,
            options: { infoTemplateType: '01' },
        });
    };

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return templateList2;
        }
        return templateList;
    }, [version, templateList, templateList2]);

    const info = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return templateInfo2;
        }
        return templateInfo;
    }, [version, templateInfo, templateInfo2]);

    const formItemLayout = useMemo(() => {
        return { ...pageFormItemLayout, wrapperCol: undefined };
    }, pageFormItemLayout);

    const SHOW_TYPE = {
        ADD: '1',
        EDIT: '2',
        HIDDEN: '0',
    };
    const [editType, updateEditType] = useState(SHOW_TYPE.HIDDEN);
    const [editForm] = Form.useForm();
    const showEditEvent = (type, infoTemplateId) => {
        if (type == SHOW_TYPE.EDIT) {
            dispatch({
                type: `${modelName}/getTemplateDetail`,
                options: { infoTemplateId },
            });
        }
        updateEditType(type);
    };

    const closeEditEvent = () => {
        editForm.resetFields();
        dispatch({
            type: `${modelName}/updateState`,
            data: { templateInfo: undefined, templateInfo2: undefined },
        });
        updateEditType(SHOW_TYPE.HIDDEN);
    };

    const editSuccess = () => {
        message.success('操作成功');
        closeEditEvent();
        refreshTemplateList();
    };

    useEffect(() => {
        if (info) {
            editForm.setFieldsValue({ ...(info || {}) });
        }
    }, [info]);

    useEffect(() => {
        const infoTemplateId = form.getFieldValue('infoTemplateId');
        if (list?.length && !infoTemplateId) {
            form.setFieldsValue({ infoTemplateId: list[0].infoTemplateId });
        }
    }, [list]);

    return (
        <div
            style={{
                maxWidth: `${document.body.clientWidth - 500}px`,
                overflowX: 'auto',
                marginTop: '12px',
            }}
        >
            <FormItem name="infoTemplateId" noStyle />
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const infoTemplateId = getFieldValue('infoTemplateId');
                    const borderSize = { width: '110px', height: '127px' };
                    return (
                        <Space align="start">
                            {list.map((ele, index) => {
                                return (
                                    ((disabled && ele.infoTemplateId == infoTemplateId) ||
                                        !disabled) && (
                                        <div
                                            style={{ display: 'grid', textAlign: 'center' }}
                                            key={index}
                                        >
                                            <a
                                                style={{
                                                    borderStyle: 'solid',
                                                    borderColor:
                                                        (infoTemplateId == ele.infoTemplateId &&
                                                            '#1890ff') ||
                                                        'white',
                                                }}
                                                onClick={() => {
                                                    form.setFieldsValue({
                                                        infoTemplateId: ele.infoTemplateId,
                                                    });
                                                }}
                                            >
                                                {(ele.bgIcon && (
                                                    <img style={borderSize} src={ele.bgIcon} />
                                                )) ||
                                                    null}
                                            </a>
                                            <Space
                                                style={{
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    marginTop: '6px',
                                                }}
                                            >
                                                <div
                                                    className="text-line"
                                                    style={{ width: '86px' }}
                                                >
                                                    {ele.infoTemplateName || '-'}
                                                </div>
                                                {disabled ? null : (
                                                    <Popconfirm
                                                        key="confirm"
                                                        title={`编辑后，已使用此模板的活动页面配置将变更为自定义，请谨慎操作！`}
                                                        onConfirm={() => {
                                                            showEditEvent(
                                                                SHOW_TYPE.EDIT,
                                                                ele.infoTemplateId,
                                                            );
                                                        }}
                                                    >
                                                        <a>
                                                            <EditOutlined />
                                                        </a>
                                                    </Popconfirm>
                                                )}
                                            </Space>
                                        </div>
                                    )
                                );
                            })}
                            {((!list || list?.length < 10) && !disabled && (
                                <Button
                                    style={{
                                        marginTop: '2px',
                                        ...borderSize,
                                    }}
                                    onClick={() => {
                                        showEditEvent(SHOW_TYPE.ADD);
                                    }}
                                >
                                    <Space direction="vertical">
                                        <PlusOutlined />
                                        新建模板
                                    </Space>
                                </Button>
                            )) ||
                                null}
                        </Space>
                    );
                }}
            </FormItem>

            <Modal
                title={(editType == SHOW_TYPE.ADD && '新建模板') || '编辑模板'}
                visible={editType !== SHOW_TYPE.HIDDEN}
                width={520}
                onCancel={closeEditEvent}
                footer={[
                    <Button key="cancel" onClick={closeEditEvent}>
                        取消
                    </Button>,
                    (editType == SHOW_TYPE.EDIT && (
                        <Popconfirm
                            key="del"
                            title={`确认删除后，已使用此模板的活动页面配置将变更为自定义，是否确认删除？`}
                            onConfirm={async () => {
                                const infoTemplateId = editForm.getFieldValue('infoTemplateId');
                                try {
                                    await delTemplateApi({ infoTemplateId });
                                    editSuccess();
                                } catch (error) {}
                            }}
                        >
                            <Button danger>删除模板</Button>
                        </Popconfirm>
                    )) ||
                        false,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={() => {
                            editForm.validateFields().then(async (values) => {
                                try {
                                    const params = { ...values };
                                    await saveTemplateApi(params);
                                    editSuccess();
                                } catch (error) {}
                            });
                        }}
                    >
                        确定
                    </Button>,
                ]}
            >
                <div
                    style={{
                        maxHeight: `${document.body.clientHeight - 300}px`,
                        overflowY: 'auto',
                    }}
                >
                    <Form form={editForm} name="templateForm">
                        <Form.Item name="actSubType" noStyle initialValue={ACTSUBTYPES.DIRECT} />
                        <Form.Item name="activityForm" noStyle initialValue={'01'} />
                        <Form.Item name="infoTemplateType" noStyle initialValue={'01'} />
                        <Form.Item name="infoTemplateId" noStyle />
                        <Form.Item
                            name="infoTemplateName"
                            label="模板名称"
                            {...formItemLayout}
                            rules={[
                                {
                                    required: true,
                                    message: '请填写',
                                },
                            ]}
                        >
                            <Input placeholder="请输入" maxLength={10} showCount />
                        </Form.Item>
                        <TemplateCustomLayout
                            bgIcon={templateInfo?.bgIcon}
                            headIcon={templateInfo?.headIcon}
                            buttonIcon={templateInfo?.buttonIcon}
                            disabled={props?.disabled}
                            form={editForm}
                            formItemLayout={formItemLayout}
                            formItemFixedWidthLayout={formItemLayout}
                        />
                    </Form>
                </div>
            </Modal>
        </div>
    );
};

// 自定义
export const TemplateCustomLayout = (props) => {
    const {
        form,
        disabled,
        bgIcon,
        headIcon,
        buttonIcon,
        formItemFixedWidthLayout,
        formItemLayout,
    } = props;

    const bgIconRef = useRef();

    useEffect(() => {
        if (bgIcon) {
            bgIconRef.current?.init(bgIcon);
        }
    }, [bgIcon]);

    return (
        <Fragment>
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    const activityForm = getFieldValue('activityForm');
                    const directDisplay = getFieldValue('directDisplay');

                    return (
                        <Fragment>
                            {activityForm === '01' ? (
                                <UpLoadImg
                                    form={form}
                                    ref={bgIconRef}
                                    formItemLayout={formItemLayout}
                                    label={
                                        <span>
                                            背景图片
                                            <Tooltip title="未领取活动页面的底图">
                                                <InfoCircleOutlined
                                                    style={{
                                                        marginLeft: '6px',
                                                    }}
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    labelName="背景图片"
                                    key="bgIcon"
                                    name="bgIcon"
                                    disabled={disabled}
                                    rules={[
                                        {
                                            required: directDisplay != '1',
                                            message: '请选择背景图片',
                                        },
                                    ]}
                                    uploadData={{
                                        contentType: '02',
                                        contRemrk: 'bgIcon',
                                        relaTable: 'e_mkt_act',
                                    }}
                                    sizeInfo={{
                                        size: 300,
                                        width: 780,
                                        height: 1688,
                                    }}
                                    required
                                    style={{ display: directDisplay == '1' ? 'none' : undefined }}
                                />
                            ) : null}

                            {actSubType === ACTSUBTYPES.DIRECT ? (
                                <FormItem
                                    name="headIcon"
                                    label={
                                        <span>
                                            头部图片
                                            <Tooltip title="领取后活动页面的顶部图片">
                                                <InfoCircleOutlined
                                                    style={{
                                                        marginLeft: '6px',
                                                    }}
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    rules={[{ required: true, message: '请选择头部图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'headIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 100,
                                            width: 750,
                                        }}
                                        placeholder="尺寸为750*X,格式支持png,jpg,jpeg,大小不得超过100kb"
                                    ></UpLoadImgItem>
                                </FormItem>
                            ) : null}

                            <SelectColor
                                form={form}
                                name="bgColour"
                                label={
                                    <span>
                                        背景颜色
                                        <Tooltip title="领取后活动页面的渲染色">
                                            <InfoCircleOutlined
                                                style={{
                                                    marginLeft: '6px',
                                                }}
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                maxLength={20}
                                placeholder="请填写背景颜色"
                                rules={[{ required: true, message: '请填写背景颜色' }]}
                                required
                                {...formItemFixedWidthLayout}
                                disabled={disabled}
                            />

                            <SelectColor
                                form={form}
                                name="buttonColour"
                                label={
                                    <span>
                                        按钮文字颜色
                                        <Tooltip title="领取前后活动页面的按钮文字颜色">
                                            <InfoCircleOutlined
                                                style={{
                                                    marginLeft: '6px',
                                                }}
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                maxLength={20}
                                placeholder="请填写按钮文字颜色"
                                rules={[{ required: true, message: '请填写按钮文字颜色' }]}
                                required
                                {...formItemFixedWidthLayout}
                                disabled={disabled}
                            />

                            <FormItem
                                name="buttonIcon"
                                label={
                                    <span>
                                        按钮图片
                                        <Tooltip title="领取前后活动页面的按钮图片">
                                            <InfoCircleOutlined
                                                style={{
                                                    marginLeft: '6px',
                                                }}
                                            />
                                        </Tooltip>
                                    </span>
                                }
                                rules={[{ required: true, message: '请选择按钮图片' }]}
                            >
                                <UpLoadImgItem
                                    disabled={disabled}
                                    uploadData={{
                                        contentType: '02',
                                        contRemrk: 'buttonIcon',
                                        relaTable: 'e_mkt_act',
                                    }}
                                    sizeInfo={{
                                        size: 100,
                                        width: 650,
                                        height: 120,
                                    }}
                                ></UpLoadImgItem>
                            </FormItem>
                            <FormItem
                                name="buttonTitle"
                                label="使用按钮主文案"
                                rules={[{ required: true, message: '请填写主文案' }]}
                                {...formItemFixedWidthLayout}
                            >
                                <Input
                                    autoComplete="off"
                                    placeholder="请填写"
                                    showCount
                                    maxLength={10}
                                ></Input>
                            </FormItem>

                            <FormItem
                                name="subButtonTitle"
                                label="使用按钮副文案"
                                {...formItemFixedWidthLayout}
                            >
                                <Input
                                    autoComplete="off"
                                    placeholder="请填写"
                                    showCount
                                    maxLength={15}
                                ></Input>
                            </FormItem>
                            <FormItem
                                name="buttonEvent"
                                label="使用按钮落地页"
                                rules={[{ required: true, message: '请选择' }]}
                                initialValue={'01'}
                            >
                                <Radio.Group>
                                    <Radio value={'01'}>红包列表</Radio>
                                    <Radio value={'02'}>扫码列表</Radio>
                                </Radio.Group>
                            </FormItem>
                        </Fragment>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

export default connect(({ global, appletGiftModel, appletGiftModel2, couponModel }) => ({
    global,
    appletGiftModel,
    appletGiftModel2,
    couponModel,
}))(TemplateLayout);
