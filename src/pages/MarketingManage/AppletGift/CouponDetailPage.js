import React, { Fragment, useState, useEffect, useMemo, useRef, useImperativeHandle } from 'react';
import { connect, Link } from 'umi';
import {
    Button,
    Card,
    Col,
    Input,
    InputNumber,
    Select,
    Form,
    Row,
    DatePicker,
    Space,
    Upload,
    Descriptions,
    Empty,
    Tabs,
    Popconfirm,
    message,
    Modal,
} from 'antd';

const { TabPane } = Tabs;

import { LeftOutlined } from '@ant-design/icons';

import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { exportTableByParams } from '@/utils/utils';
import {
    getAddGiftInfoApi,
    saveGiftApi,
    refundCpnBagApi,
} from '@/services/Marketing/MarketingGiftApi';
import { ACTSUBTYPES, ACT_VERSION_TYPES, EXCHANGESTATUS } from '@/config/declare';
import TablePro from '@/components/TablePro';
import { STATUS_TYPES } from '@/config/declare';
import CouponTable from '@/components/CouponComponents/CouponTable';
import usePageState from '@/hooks/usePageState.js';
import commonStyles from '@/assets/styles/common.less';
import { CouponUpdatePage } from './CouponUpdatePage';
import { MNG_BIL_URL } from '@/config/global';
import {
    exportExchangeListApi,
    exportReveiveListApi,
    getSendGiftRecordDetailApi,
} from '@/services/Marketing/MarketingGiftApi2';
import { SendGiftDetailFormItem } from './AppletGiftListPage';
import { previewPrizeTaskApi, savePrizeTaskApi } from '@/services/UserManage/UserApi';

const { Option } = Select;

const TAB_TYPES = {
    TAB_COUPON: '01',
    TAB_LINK: '02',
    TAB_RECORD: '03',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
};

const modelName = (version) => {
    if (version == ACT_VERSION_TYPES.V_2_0) {
        return 'appletGiftModel2';
    }
    return 'appletGiftModel';
};

// 购买记录
const CouponsView = (props) => {
    const {
        global: { codeInfo },
        couponModel,
        dispatch,
        appletGiftModel: { editActInfo = {} },
        version,
    } = props;
    const { custLoyalType: custLoyalTypeList, sourceChannel: sourceChannelList } = codeInfo;

    const couponList = useMemo(() => {
        let actPushBoList = [];

        if (editActInfo) {
            const { actSubType, putActPushBoList } = editActInfo;

            // if (editActInfo.actTimeList?.length) {
            //     let actTimeList = editActInfo.actTimeList.map((ele) => ({
            //         beginTime: moment(ele.beginTime, 'HH:mm'),
            //         endTime: moment(ele.endTime, 'HH:mm'),
            //     }));
            //     params.actTimeList = actTimeList;
            // }

            if (putActPushBoList) {
                let actPushBoListInfo = putActPushBoList.map((ele) => {
                    let item = {
                        cpnList: ele.cpnList || [],
                        activeCrowdFlag: ele.activeCrowdFlag || '0',
                        activeCrowd: (ele.activeCrowd && ele.activeCrowd.split(',')) || [],
                        actGetLimitNum: ele.actGetLimitNum,
                        everyDayGetLimitNum: ele.everyDayGetLimitNum,
                        actEveryDayGetLimitNum: ele.actEveryDayGetLimitNum,
                        actGetLimitType: ele.actGetLimitType,
                        saleAmt: ele.saleAmt,
                        actChannel: ele.actChannel,
                        actPushId: ele.actPushId,
                        actId: ele.actId,
                    };
                    if (ele.sourceChannel?.indexOf('00') > -1) {
                        // 如果是全选，接口要求只要传'00'，详情同样处理
                        item.sourceChannel = '00';
                    } else if (Array.isArray(ele?.sourceChannel)) {
                        item.sourceChannel =
                            (ele.sourceChannel && ele.sourceChannel.split(',')) || [];
                    }

                    if (ele.cityCodes) {
                        item.cityCodes = (ele.sourceChannel && ele.cityCodes.split(',')) || [];
                    }
                    return item;
                });
                if (actSubType === ACTSUBTYPES.DIRECT) {
                    actPushBoList = actPushBoListInfo;
                } else {
                    if (actPushBoListInfo[0]) {
                        actPushBoList = [actPushBoListInfo[0]];
                    }
                }
            } else {
                let options = {
                    cpnList: editActInfo.putCpnList || [],
                    activeCrowdFlag: editActInfo.activeCrowdFlag || '0',
                    activeCrowd:
                        (editActInfo.activeCrowd && editActInfo.activeCrowd.split(',')) || [],
                    actGetLimitNum: editActInfo.actGetLimitNum,
                    everyDayGetLimitNum: editActInfo.everyDayGetLimitNum,
                    actEveryDayGetLimitNum: editActInfo.actEveryDayGetLimitNum,
                    actGetLimitType: editActInfo.actGetLimitType,
                    saleAmt: editActInfo.saleAmt,
                };
                if (editActInfo.actChannel) {
                    options.actChannel = editActInfo.actChannel.split(',') || [];
                }
                // if (editActInfo.sourceChannel?.indexOf('00') > -1) {
                //     // 如果是全选，接口要求只要传'00'，详情同样处理
                //     options.sourceChannel = '00';
                // } else if (Array.isArray(editActInfo?.sourceChannel)) {
                //     options.sourceChannel =
                //         (editActInfo.sourceChannel && editActInfo.sourceChannel.split(',')) || [];
                // }

                if (editActInfo.popScope) {
                    options.popScope = editActInfo.popScope;
                }

                options.sourceChannel =
                    (editActInfo.sourceChannel?.split && editActInfo.sourceChannel.split(',')) ||
                    editActInfo.sourceChannel;
                options.cityCodes = editActInfo.popScopeInfo || undefined;

                // if (editActInfo.cityCodes) {
                //     options.cityCodes =
                //         (editActInfo.sourceChannel && editActInfo.cityCodes.split(',')) || [];
                // }
                actPushBoList = [options];
            }
        }
        return actPushBoList;
    }, [editActInfo]);

    const [form] = Form.useForm();

    const renderCouponView = useMemo(() => {
        return couponList.map((ele, index) => {
            const { actSubType } = editActInfo;
            const activeCrowdName = () => {
                const names = [];

                if (ele && custLoyalTypeList) {
                    let activeCrowdList = ele.activeCrowd || [];
                    custLoyalTypeList?.forEach((element) => {
                        if (
                            activeCrowdList.includes(element.codeValue) ||
                            ele.activeCrowdFlag == '1'
                        ) {
                            names.push(element.codeName);
                        }
                    });
                }
                return names.length ? names.join(',') : '-';
            };

            const sourceChannelName = () => {
                const names = [];

                if (ele && sourceChannelList) {
                    let tempList = ele.sourceChannel || [];
                    sourceChannelList?.forEach((element) => {
                        if (tempList.includes(element.codeValue) || ele.sourceChannel == '00') {
                            names.push(element.codeName);
                        }
                    });
                }
                return names.length ? names.join(',') : '-';
            };
            const title = actSubType === ACTSUBTYPES.DIRECT ? `用户${index + 1}` : null;
            return (
                <Fragment key={index}>
                    <Card title={title}>
                        <Descriptions column={2}>
                            <Descriptions.Item label="活动人群">
                                {activeCrowdName()}
                            </Descriptions.Item>
                            {ele.sourceChannel ? (
                                <Descriptions.Item label="渠道来源">
                                    {sourceChannelName()}
                                </Descriptions.Item>
                            ) : null}

                            {actSubType === ACTSUBTYPES.DIRECT ||
                                (actSubType === ACTSUBTYPES.RECEIVE && (
                                    <Descriptions.Item label="销售金额">
                                        {ele?.saleAmt || '-'}元
                                    </Descriptions.Item>
                                )) ||
                                null}
                        </Descriptions>
                    </Card>
                    <CouponTable
                        form={form}
                        dispatch={dispatch}
                        global={global}
                        couponModel={couponModel}
                        actId={editActInfo?.actId}
                        needCheck={false}
                        editabled={false}
                        totalItem={null}
                        putCpnList={ele?.cpnList}
                        onRefresh={() => {
                            dispatch({
                                type: `${modelName(version)}/initEditActInfo`,
                                actId: editActInfo?.actId,
                            });
                        }}
                        pageType="appletGift"
                    />
                </Fragment>
            );
        });
    }, [couponList, editActInfo]);

    return <Form form={form}> {renderCouponView}</Form>;
};

// 购买记录
const ActLinkView = (props) => {
    const {
        appletGiftModel: { editActInfo = {} },
    } = props;

    const [form] = Form.useForm();

    const xdtLink = useMemo(() => {
        if (editActInfo) {
            const actNo = editActInfo.actNo;
            let basePath = `/pagesActive/gift/index?`;
            if (editActInfo.actSubType === ACTSUBTYPES.DIRECT) {
                basePath = `/pagesActive/gift/coupon?`;
            }
            const params = [`actGiftNo=${actNo}`];
            const paramsFormat = params.join('&');
            const payPath = basePath + paramsFormat;

            const xdtLink = payPath;
            return xdtLink;
        }
        return undefined;
    }, [editActInfo]);

    const giftLink = useMemo(() => {
        if (xdtLink) {
            const giftLink = `alipays://platformapi/startapp?appId=2019092067648076&page=${encodeURIComponent(
                xdtLink,
            )}`;
            return giftLink;
        }
        return '';
    }, [xdtLink]);

    const cityNames = useMemo(() => {
        const names = [];
        editActInfo?.cityInfos?.forEach((ele) => {
            names.push(ele.areaName);
        });
        return names.length ? names.join(',') : '部分区域';
    }, [editActInfo]);

    return (
        <Form form={form} {...formItemLayout}>
            {/* {editActInfo?.saleAmt == 0 ? null : (
                <>
                    <Form.Item label="是否弹窗" name="popFlag">
                        {(editActInfo.popFlag == 1 && '是') || '否'}
                    </Form.Item>
                    {(editActInfo.popFlag == 1 && (
                        <>
                            <Form.Item label="未登录显示" name="loginFlag">
                                {(editActInfo.loginFlag == 1 && '是') || '否'}
                            </Form.Item>

                            <Form.Item name="popIcon" label="弹窗图片">
                                <Upload
                                    fileList={[
                                        {
                                            uid: '1',
                                            name: '弹窗图片',
                                            status: 'done',
                                            url: editActInfo?.popIcon,
                                        },
                                    ]}
                                ></Upload>
                            </Form.Item>

                            <Form.Item label="弹窗范围">
                                {editActInfo?.popScope == 'all'
                                    ? '全平台'
                                    : editActInfo?.popScope == 'city'
                                    ? cityNames
                                    : null}
                            </Form.Item>
                        </>
                    )) ||
                        null}
                </>
            )} */}
            <Form.Item label="外部领奖链接">
                <Form.Item noStyle>
                    <div style={{ wordWrap: 'break-word' }}>{giftLink}</div>
                </Form.Item>
            </Form.Item>
            <Form.Item label="内部领奖链接">
                <Form.Item noStyle>
                    <div style={{ wordWrap: 'break-word' }}>{xdtLink}</div>
                </Form.Item>
            </Form.Item>
        </Form>
    );
};

// 领取记录
const ReceiveRecordSearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, receiveListLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            // {...formItemLayout}
            form={form}
            onFinish={onFinish}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <Form.Item label="手机号:" name="mobile">
                        <InputNumber
                            controls={false}
                            style={{ width: '100%' }}
                            placeholder="请填写"
                            maxLength={11}
                        />
                    </Form.Item>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit" loading={receiveListLoading}>
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const ReceiveRecordView = (props) => {
    const {
        dispatch,
        location,
        appletGiftModel: { reveiveList, reveiveTotal },
        appletGiftModel2: { reveiveList2, reveiveTotal2 },
        receiveListLoading,
        actId,
        actSubType,
        version,
    } = props;

    const [form] = Form.useForm();

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return reveiveList2;
        }
        return reveiveList;
    }, [version, reveiveList, reveiveList2]);

    const total = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return reveiveTotal2;
        }
        return reveiveTotal;
    }, [version, reveiveTotal, reveiveTotal2]);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        if (location?.query?.mobile) {
            form.setFieldsValue({ mobile: location?.query?.mobile });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            actId,
            actSubType,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
        };

        dispatch({
            type: `${modelName(version)}/getReveiveList`,
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            actId,
            actSubType,
        };

        const columnsStrs = [];
        if (version == ACT_VERSION_TYPES.V_2_0) {
            exportReveiveListApi(params);
        } else {
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: '/bil/act/gift-receive-list',
                options: params,
                columnsStr: columnsStrs,
            });
        }
    };

    const [tipViewVisible, updateTipViewVisible] = useState(false);
    const [tipItem, updateTipItem] = useState();

    const showTipViewEvent = (item) => {
        updateTipViewVisible(true);
        updateTipItem({ ...item });
    };

    const closeTipViewEvent = () => {
        updateTipViewVisible(false);
        updateTipItem(undefined);
    };

    const confirmBackEvent = async () => {
        try {
            // 不区分优惠券1.0/2.0，后端开发说就用旧的
            await refundCpnBagApi({ orderNo: tipItem.orderNo });
            message.success('操作成功');
            searchData();
            closeTipViewEvent();
        } catch (error) {}
    };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '领取时间',
            width: 240,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...(actSubType != ACTSUBTYPES.SEND
            ? [
                  {
                      title: '领取渠道',
                      width: 240,
                      dataIndex: 'cpnGetChannelName',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
              ]
            : []),
        {
            title: '领取人',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包总额',
            width: 140,
            dataIndex: 'packageAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付金额',
            width: 140,
            dataIndex: 'buyAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已优惠金额',
            width: 140,
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作 ',
            width: 80,
            fixed: 'right',
            render(text, record, index) {
                return (
                    (record.refundAmt == 0 && record.orderNo?.length > 0 && (
                        <a onClick={() => showTipViewEvent(record)}>退款</a>
                    )) ||
                    null
                );
            },
        },
    ];

    return (
        <Fragment>
            <ReceiveRecordSearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <TablePro
                name="reciver"
                loading={receiveListLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={list}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    total: total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <Modal
                title="退款提醒"
                width={480}
                visible={tipViewVisible}
                onCancel={closeTipViewEvent}
                onOk={confirmBackEvent}
                maskClosable={false}
            >
                <Space style={{ margin: '0 24px' }} direction="vertical">
                    <p>{`是否为${tipItem?.mobile || '-'}的用户办理退卡，退卡金额${
                        Math.round(
                            (parseFloat(tipItem?.buyAmt || '0') -
                                parseFloat(tipItem?.usedAmt || '0')) *
                                100,
                        ) / 100
                    }元=购卡金额${tipItem?.buyAmt || '-'}元-已优惠金额${
                        tipItem?.usedAmt || '-'
                    }元，点击确认会将退款费用原路退回购卡账户。`}</p>
                    <p style={{ color: '#f50000' }}>注：支付宝券无法作废，退款后仍可被核销。</p>
                </Space>
            </Modal>
        </Fragment>
    );
};

// 发放记录详情弹窗
const AppletGiftSendDetailModal = (props) => {
    const { initRef } = props;

    const [visible, updateVisible] = useState(false);
    const [loading, updateLoading] = useState(false);
    const [form] = Form.useForm();
    const [refluxFlag, updateRefluxFlag] = useState('');
    useImperativeHandle(initRef, () => ({
        show: (item) => {
            updateVisible(true);
            initData(item?.taskId);
            form.setFieldsValue(item);
        },
    }));

    const onClose = () => {
        form.resetFields();
        updateVisible(false);
    };

    const initData = async (taskId) => {
        if (!taskId) {
            return;
        }
        try {
            updateLoading(true);
            const { data } = await getSendGiftRecordDetailApi({ taskId });
            const params = data;
            if (data.giftBagPrizeInfoVo?.detPrizeList?.length) {
                params.detPrizeList = data.giftBagPrizeInfoVo?.detPrizeList;
                delete params.giftBagPrizeInfoVo;
            }
            form.setFieldsValue(params);
            if (data?.sendStatus === '05' || form.getFieldValue('sendStatus') === '05') {
                getPreviewPrizeTask(params, taskId);
            }
        } catch (error) {
        } finally {
            updateLoading(false);
        }
    };
    const getPreviewPrizeTask = async (params, taskId) => {
        try {
            const { data } = await previewPrizeTaskApi({ taskId });
            updateRefluxFlag(data?.refluxFlag);
            form.setFieldsValue({
                ...params,
                ...data,
            });
        } catch (error) {
        } finally {
        }
    };
    return (
        <Modal
            visible={visible}
            onCancel={onClose}
            title={'查看发券任务详情'}
            width={880}
            okButtonProps={{ style: { display: refluxFlag !== '1' ? 'none' : '' } }}
            cancelText="关闭"
            okText="发放"
            onOk={() => {
                form.validateFields()
                    .then(async (params) => {
                        const formData = form.getFieldsValue();

                        await savePrizeTaskApi({
                            actId: formData?.actId,
                            taskId: formData?.taskId,
                        });
                        if (refluxFlag === '1') {
                            message.success('发放成功');
                        } else {
                            message.success('操作成功');
                        }
                        onClose();
                    })
                    .catch((e) => {});
            }}
        >
            <Form form={form} loading={loading}>
                <Form.Item name={'actId'} noStyle />
                <Form.Item name={'taskId'} noStyle />
                <Form.Item name={'taskName'} noStyle />
                <Form.Item name={'sendTime'} noStyle />
                <h1 className={commonStyles['form-title']}>基本信息</h1>
                <Form.Item noStyle shouldUpdate>
                    {() => {
                        const values = form.getFieldsValue();
                        const { taskId, taskName, sendTime } = values;
                        return (
                            <Descriptions column={2}>
                                <Descriptions.Item label="任务ID">
                                    {taskId || '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="任务名称">
                                    {taskName || '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="发放时间">
                                    {sendTime || '-'}
                                </Descriptions.Item>
                            </Descriptions>
                        );
                    }}
                </Form.Item>
                <SendGiftDetailFormItem {...props} form={form} needTitle />
            </Form>
        </Modal>
    );
};

const SendRecordView = (props) => {
    const {
        dispatch,
        location,
        appletGiftModel2: { sendRecordList, sendRecordTotal },
        actId,
        sendGiftRecordListLoading,
    } = props;

    const [form] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const detailRef = useRef();

    useEffect(() => {
        if (location?.query?.mobile) {
            form.setFieldsValue({ mobile: location?.query?.mobile });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            actId,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
        };

        dispatch({
            type: `appletGiftModel2/getSendGiftRecordList`,
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '任务ID',
            width: 100,
            dataIndex: 'taskId',
            render(text, record, index) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '任务名称',
            width: 140,
            dataIndex: 'taskName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发放奖品',
            width: 160,
            dataIndex: 'prizeNames',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已发放份数',
            width: 140,
            dataIndex: 'sendNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发放时间',
            width: 200,
            dataIndex: 'sendTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发放状态',
            width: 140,
            dataIndex: 'sendStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 140,
            dataIndex: 'createdBy',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作 ',
            width: 80,
            fixed: 'right',
            render(text, record, index) {
                return (
                    <Button
                        type="link"
                        onClick={() => {
                            detailRef.current.show(record);
                        }}
                    >
                        详情
                    </Button>
                );
            },
        },
    ];

    return (
        <Fragment>
            <Form
                // {...formItemLayout}
                form={form}
                onFinish={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                scrollToFirstError
            >
                <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                    <Col span={8}>
                        <Form.Item label="任务名称" name="taskName">
                            <Input placeholder="请输入" autoComplete="off" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="任务ID" name="taskId">
                            <Input placeholder="请输入" autoComplete="off" />
                        </Form.Item>
                    </Col>

                    <Col>
                        <Space>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={sendGiftRecordListLoading}
                            >
                                查询
                            </Button>
                            <Button onClick={resetData}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>

            <TablePro
                name="reciver"
                loading={sendGiftRecordListLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={sendRecordList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    total: sendRecordTotal,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <AppletGiftSendDetailModal {...props} initRef={detailRef} />
        </Fragment>
    );
};

const ExchangeRecordView = (props) => {
    const {
        dispatch,
        appletGiftModel: { exchangeList, exchangeTotal },
        appletGiftModel2: { exchangeList2, exchangeTotal2 },
        infoLoading,
        actId,
        actSubType,
        version,
    } = props;

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return exchangeList2;
        }
        return exchangeList;
    }, [version, exchangeList, exchangeList2]);

    const total = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return exchangeTotal2;
        }
        return exchangeTotal;
    }, [version, exchangeTotal, exchangeTotal2]);

    const [exchangeForm] = Form.useForm();
    const searchEvent = (isDownload) => {
        const data = exchangeForm.getFieldsValue();
        const params = {
            ...data,
            actId,
            actSubType,
            pageIndex: isDownload ? undefined : pageInfo.pageIndex,
            pageSize: isDownload ? undefined : pageInfo.pageSize,
        };

        if (isDownload) {
            if (version == ACT_VERSION_TYPES.V_2_0) {
                exportExchangeListApi(params);
            } else {
                const columnsStrs = [];
                for (const item of exchangeColumns) {
                    if (item.dataIndex) {
                        columnsStrs.push({
                            key: item.dataIndex,
                            value: item.title,
                        });
                    }
                }
                exportTableByParams({
                    methodUrl: '/bil/act/put/exchange-list',
                    options: params,
                    columnsStr: columnsStrs,
                    baseUrl: version == ACT_VERSION_TYPES.V_2_0 ? MNG_BIL_URL : undefined,
                });
            }
        } else {
            dispatch({
                type: `${modelName(version)}/getExchangeList`,
                options: params,
            });
        }
    };
    const resetSearchForm = () => {
        exchangeForm.resetFields();
        queryData();
    };

    useEffect(() => {
        searchEvent();
    }, [pageInfo]);

    const queryData = () => {
        if (pageInfo.pageNum == 1) {
            searchEvent();
            return;
        }
        changePageInfo({ pageIndex: 1 });
    };

    const exchangeColumns = [
        {
            title: '序号',
            width: 80,
            render(text, record, index) {
                return (
                    <span title={pageInfo.pageIndex + index}>
                        {(pageInfo.pageIndex - 1) * pageInfo.pageSize + index + 1}
                    </span>
                );
            },
        },
        {
            title: '兑换码',
            width: 140,
            dataIndex: version == ACT_VERSION_TYPES.V_2_0 ? 'cpnExchangeCode' : 'exchangeCode',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '兑换渠道',
            width: 120,
            dataIndex: 'exchangeChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '领取人',
            width: 120,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '领取时间',
            width: 160,
            dataIndex: 'exchangeTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'exchangeStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <Form
                form={exchangeForm}
                {...{
                    labelAlign: 'left',
                    wrapperCol: {
                        span: 16,
                    },
                }}
            >
                <Row>
                    <Col span={8}>
                        <Form.Item label="手机号" name="mobile">
                            <Input />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="兑换状态" name="exchangeStatus">
                            <Select placeholder="请选择" allowClear>
                                <Option value={EXCHANGESTATUS.UNTAKE} key={EXCHANGESTATUS.UNTAKE}>
                                    未领取
                                </Option>
                                <Option value={EXCHANGESTATUS.TAKED} key={EXCHANGESTATUS.TAKED}>
                                    已领取
                                </Option>
                                <Option value={EXCHANGESTATUS.OUTDATE} key={EXCHANGESTATUS.OUTDATE}>
                                    已失效
                                </Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col>
                        <Space>
                            <Button type="primary" onClick={queryData}>
                                查询
                            </Button>
                            <Button type="primary" onClick={() => searchEvent(true)}>
                                导出
                            </Button>
                            <Button onClick={resetSearchForm}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>

            <TablePro
                name="list"
                loading={infoLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) =>
                    version == ACT_VERSION_TYPES.V_2_0
                        ? record.cpnExchangeCode
                        : record.exchangeCode
                }
                dataSource={list}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    total: total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                columns={exchangeColumns}
                noSort
            />
        </Fragment>
    );
};

const AppletGiftDetailPage = (props) => {
    const {
        dispatch,
        history,
        location,
        route,
        match,
        receiveListLoading,
        global: { codeInfo = {} },
        appletGiftModel: { editActInfo = {}, reveiveTotal, exchangeTotal },
        appletGiftModel2: { editActInfo2 = {}, reveiveTotal2, exchangeTotal2 },
    } = props;
    const {
        location: { pathname },
    } = history;

    const version = useMemo(() => {
        if (pathname?.indexOf('/applet-gift/') >= 0) {
            return ACT_VERSION_TYPES.V_2_0;
        }
        return ACT_VERSION_TYPES.V_1_0;
    }, [pathname]);

    const info = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return editActInfo2;
        }
        return editActInfo;
    }, [version, editActInfo, editActInfo2]);

    const { custLoyalType: custLoyalTypeList, sourceChannel: sourceChannelList } = codeInfo;
    const { actId, actSubType } = match.params || {};

    // 初始化
    useEffect(() => {
        if (!custLoyalTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'custLoyalType',
            });
        }
        if (!sourceChannelList) {
            dispatch({
                type: 'global/initCode',
                code: 'sourceChannel',
            });
        }

        dispatch({
            type: `${modelName(version)}/initEditActInfo`,
            params: {
                actId,
                actSubType,
            },
        });

        return () => {
            dispatch({
                type: `${modelName(version)}/updateEditActInfo`,
                data: { editActInfo: {}, editActInfo2: {} },
            });
        };
    }, []);
    const [curTab, changeTabEvent] = useState(location?.query?.tabType || TAB_TYPES.TAB_COUPON);

    const contentView = useMemo(() => {
        if (info.actSubType === ACTSUBTYPES.COUPON || info.actSubType == ACTSUBTYPES.ALI) {
            // 兑换记录
            return (
                <ExchangeRecordView
                    {...props}
                    actId={actId}
                    actSubType={actSubType || info?.actSubType}
                    version={version}
                ></ExchangeRecordView>
            );
        }
        return (
            <ReceiveRecordView
                {...props}
                actId={actId}
                actSubType={actSubType || info?.actSubType}
                version={version}
            ></ReceiveRecordView>
        );
        return <Empty></Empty>;
    }, [
        info,
        curTab,
        reveiveTotal,
        reveiveTotal2,
        receiveListLoading,
        exchangeTotal,
        exchangeTotal2,
    ]);

    const goBack = () => {
        let path = `/marketing/couponCenter/appletgift/list`;
        if (version == ACT_VERSION_TYPES.V_2_0) {
            path = `/marketing/couponCenter/applet-gift/list`;
        }
        history.replace(path);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card bordered={false}>
                <Tabs activeKey={curTab} onChange={changeTabEvent}>
                    <TabPane tab={'活动信息'} key={TAB_TYPES.TAB_COUPON}>
                        <CouponUpdatePage {...props} />
                    </TabPane>
                    {info?.actSubType &&
                    info.actSubType != ACTSUBTYPES.POPUP &&
                    info.actSubType != ACTSUBTYPES.SEND ? (
                        <TabPane
                            tab={
                                ((info.actSubType == ACTSUBTYPES.COUPON ||
                                    info.actSubType == ACTSUBTYPES.ALI) &&
                                    '兑换记录') ||
                                '领取记录'
                            }
                            key={TAB_TYPES.TAB_RECORD}
                        >
                            {contentView}
                        </TabPane>
                    ) : (
                        (info.actSubType == ACTSUBTYPES.SEND && (
                            <TabPane tab={'发放详情'} key={TAB_TYPES.TAB_RECORD}>
                                <Card bordered={false} bodyStyle={{ padding: '0 12px' }}>
                                    <Tabs defaultActiveKey={'01'} size="small">
                                        <TabPane tab={'领取记录'} key={'01'}>
                                            {contentView}
                                        </TabPane>
                                        <TabPane tab={'发放记录'} key={'02'}>
                                            <SendRecordView {...props} actId={actId} />
                                        </TabPane>
                                    </Tabs>
                                </Card>
                            </TabPane>
                        )) ||
                        null
                    )}
                </Tabs>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, appletGiftModel, appletGiftModel2, couponModel, loading }) => ({
    global,
    appletGiftModel,
    appletGiftModel2,
    couponModel,
    infoLoading:
        loading.effects['appletGiftModel/initEditActInfo'] ||
        loading.effects['appletGiftModel2/initEditActInfo'],
    receiveListLoading:
        loading.effects['appletGiftModel/getReveiveList'] ||
        loading.effects['appletGiftModel2/getReveiveList'],
    sendGiftRecordListLoading: loading.effects['appletGiftModel2/getSendGiftRecordList'],
}))(AppletGiftDetailPage);
