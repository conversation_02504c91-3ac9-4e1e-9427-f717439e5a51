import { Form } from 'antd';

import commonStyles from '@/assets/styles/common.less';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import { ACT_VERSION_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const FaceIconLayout = (props) => {
    const { form, editActInfo, disabled, formItemLayout, formItemFixedWidthLayout, version } =
        props;

    return (
        <>
            <div className={commonStyles['form-title']}>页面配置</div>
            <FormItem
                name={version == ACT_VERSION_TYPES.V_2_0 ? 'bgIcon' : 'faceIcon'}
                label="券封面"
                rules={[{ required: true, message: '请选择券封面图片' }]}
            >
                <UpLoadImgItem
                    disabled={disabled}
                    uploadData={{
                        contentType: '02',
                        contRemrk: 'faceIcon',
                        relaTable: 'e_mkt_act',
                    }}
                    sizeInfo={{
                        size: 1024 * 2,
                        width: 670,
                        height: 335,
                    }}
                ></UpLoadImgItem>
            </FormItem>
        </>
    );
};

export default FaceIconLayout;
