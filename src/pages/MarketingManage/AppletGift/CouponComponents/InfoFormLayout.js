import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import { Button, Input, Select, Form, DatePicker, Radio, Checkbox, InputNumber, Space } from 'antd';

import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

import moment from 'moment';
import { ACTSUBTYPES, COOPERATION_PLATFORM_TYPES } from '@/config/declare';

import QuillRichInput from '@/components/QuillRichInput/index';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Select';
import { isEmpty } from '@/utils/utils';

const { RangePicker } = DatePicker;

const { Option } = Select;

const FormItem = Form.Item;

const InfoFormLayout = (props) => {
    const {
        dispatch,
        form,
        isLock,
        isDraft,
        isCopy,
        openSomeThing,
        editActInfo,
        global: { codeInfo },
        actTypeList,
        refreshWXUrl,
        formItemLayout,
        formItemFixedWidthLayout,
    } = props;

    const markRef = useRef();
    const { actChannel } = codeInfo;

    const cooperationPlatform = Form.useWatch('cooperationPlatform', form);

    // 新电途以下渠道不让勾选：合创（0609），福清，E福州（0605），响车车（0604），大湾区（0611），就到出行（0603）
    const channelDisableds = ['0609', '0605', '0604', '0611', '0603'];
    const channelOptionList = useMemo(() => {
        let arr = actChannel;
        switch (cooperationPlatform) {
            case COOPERATION_PLATFORM_TYPES.XDT:
                arr = arr?.filter((item) => {
                    return item.codeName.indexOf('新电途-') > -1;
                });
                break;
            case COOPERATION_PLATFORM_TYPES.XDT_X:
                arr = arr?.filter((item) => {
                    return item.codeName.indexOf('新电途X-') > -1;
                });
                break;
            default:
                break;
        }
        return (
            arr?.map((ele) => (
                <Checkbox
                    key={ele.codeValue}
                    value={ele.codeValue}
                    disabled={channelDisableds.some((code) => ele.codeValue == code)}
                >
                    {ele.codeName}
                </Checkbox>
            )) || []
        );
    }, [actChannel, cooperationPlatform]);

    const actTypeOptions = useMemo(() => {
        return actTypeList.map((ele) => {
            return (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            );
        });
    }, [actTypeList]);

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    const changeActSubTypeEvent = (value) => {
        if (value == '01') {
            form.setFieldsValue({
                actGetLimitNum: 1,
            });
        }
        if (value == ACTSUBTYPES.POPUP) {
            form.setFieldsValue({
                cooperationPlatform: COOPERATION_PLATFORM_TYPES.XDT,
            });
        }
        if (value !== ACTSUBTYPES.DIRECT && value !== ACTSUBTYPES.RECEIVE) {
            form.setFieldsValue({
                activityForm: '01',
            });
        }
        if (value == ACTSUBTYPES.ALI) {
            form.setFieldsValue({ actPushBoList: [{ actGetLimitType: '01' }] });
        }
        form.setFieldsValue({
            actPushBoList: [{ actGetLimitType: '01' }],
            buttonIcon: null,
        });
        refreshWXUrl?.();
    };

    const limitTimeRule = (actTimeList, currentIndex, isStart) => {
        let time = actTimeList && actTimeList[currentIndex];
        let beginTime = time?.beginTime;
        let endTime = time?.endTime;

        if (!beginTime && isStart) return Promise.reject('请输入开始时间');
        if (!endTime && !isStart) return Promise.reject('请输入结束时间');

        if (beginTime && endTime && beginTime > endTime) {
            return Promise.reject('开始时间不能大于结束时间');
        }

        // 和其他时段进行比较
        for (let otherIndex = 0; otherIndex < actTimeList?.length; otherIndex++) {
            const otherLimit = actTimeList && actTimeList[otherIndex];
            if (!otherLimit || otherIndex == currentIndex) continue;
            if (isStart && beginTime > otherLimit.beginTime && beginTime < otherLimit.endTime) {
                // 开始时间段和其他时间重叠
                return Promise.reject('开始时间和其他时段有重叠');
            }
            if (!isStart && endTime > otherLimit.beginTime && endTime < otherLimit.endTime) {
                // 结束时间点和其他时间重叠
                return Promise.reject('结束时间和其他时段有重叠');
            }
        }
        return Promise.resolve();
    };

    const formActSubType = Form.useWatch('actSubType', form);
    return (
        <Fragment>
            <FormItem label="活动编号" name="actNo" {...formItemFixedWidthLayout} required>
                <Input disabled="disabled" placeholder="自动生成" onBlur={refreshWXUrl} />
            </FormItem>
            <FormItem
                label="活动类型"
                name="actSubType"
                {...formItemFixedWidthLayout}
                rules={[{ required: true, message: '请选择活动类型' }]}
                // initialValue={ACTSUBTYPES.DIRECT}
            >
                <Select
                    disabled={isLock || (!isCopy && editActInfo?.actId)}
                    placeholder="请选择"
                    onChange={changeActSubTypeEvent}
                >
                    {actTypeOptions}
                </Select>
            </FormItem>
            <FormItem
                label="活动名称"
                name="actName"
                {...formItemFixedWidthLayout}
                rules={[{ required: true, message: '请填写活动名称' }]}
            >
                <Input
                    disabled={!openSomeThing}
                    placeholder="请输入活动名称"
                    maxLength={25}
                    autoComplete="off"
                    showCount
                />
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actSubType !== curValues.actSubType
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    return actSubType !== ACTSUBTYPES.ALI ? (
                        <FormItem
                            label="活动平台"
                            name="cooperationPlatform"
                            {...formItemFixedWidthLayout}
                            rules={[{ required: true, message: '请选择所属平台' }]}
                        >
                            <SelectCooperationPlatform
                                allowClear={false}
                                disabled={isLock || (!isCopy && editActInfo?.actId)}
                                disabledIds={
                                    actSubType == ACTSUBTYPES.POPUP
                                        ? [COOPERATION_PLATFORM_TYPES.XDT_X]
                                        : undefined
                                }
                                // disabled={isLock || editActInfo?.actId}
                            />
                        </FormItem>
                    ) : (
                        <FormItem
                            name="cooperationPlatform"
                            initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                            noStyle
                        ></FormItem>
                    );
                }}
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actSubType !== curValues.actSubType
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    return (
                        (actSubType == ACTSUBTYPES.POPUP || actSubType === ACTSUBTYPES.DIRECT) && (
                            <FormItem
                                label="活动用途"
                                name="configType"
                                rules={[{ required: true }]}
                                initialValue="01"
                            >
                                <Radio.Group
                                    disabled={
                                        !isCopy &&
                                        ((actSubType == ACTSUBTYPES.POPUP && isLock) ||
                                            (actSubType === ACTSUBTYPES.DIRECT && !isDraft))
                                    }
                                    onChange={() => {
                                        // 经测试同意，切换的时候清掉人群信息，避免cdp人群和基础人群混乱
                                        const actPushBoList =
                                            form.getFieldValue('actPushBoList') || [];
                                        for (const item of actPushBoList) {
                                            delete item.activeCrowd;
                                            delete item.crowdInfo;
                                        }
                                        form.setFieldsValue({ actPushBoList });
                                    }}
                                >
                                    <Radio value="01">日常活动</Radio>
                                    <Radio value="02">限时秒杀</Radio>
                                </Radio.Group>
                            </FormItem>
                        )
                    );
                }}
            </FormItem>

            <FormItem
                label="活动有效期"
                name="dateTime"
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请选择活动时间' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value) {
                                return Promise.reject('');
                            }
                            if (!value[0]) {
                                return Promise.reject('请选择活动开始日期');
                            }
                            if (!value[1]) {
                                return Promise.reject('请选择活动失效日期');
                            }
                            if (value[1]) {
                                const nowTime = +new Date();
                                const sendEndTime = +new Date(value[1]);

                                if (sendEndTime < nowTime) {
                                    return Promise.reject('活动失效日期不能早于当前时间');
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <RangePicker
                    disabled={[isLock, !openSomeThing]}
                    showTime={{
                        format: 'HH:mm:ss',
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    }}
                    disabledTime={disabledRangeTime}
                    format="YYYY-MM-DD HH:mm:ss"
                />
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actSubType !== curValues.actSubType ||
                    prevValues.actTimeLimitFlag !== curValues.actTimeLimitFlag ||
                    prevValues.actTimeList !== curValues.actTimeList ||
                    prevValues.configType !== curValues.configType
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    const actTimeLimitFlag = getFieldValue('actTimeLimitFlag');
                    const actTimeList = getFieldValue('actTimeList');
                    const configType = getFieldValue('configType');

                    return (
                        <>
                            {actSubType == ACTSUBTYPES.DIRECT && (
                                <Fragment>
                                    <FormItem
                                        label={<span>适用渠道</span>}
                                        name={'actChannel'}
                                        {...formItemLayout}
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject('请选择渠道');
                                                    }
                                                    const shareFlag = getFieldValue('shareFlag');
                                                    if (shareFlag == '1') {
                                                        if (
                                                            value.includes('01') &&
                                                            !value.includes('22')
                                                        ) {
                                                            return Promise.reject(
                                                                '选择了新电途-APP渠道时为保证成功邀请好友，请同时选择新电途-新电途微信小程序渠道。',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        required
                                    >
                                        <Checkbox.Group disabled={isLock}>
                                            {channelOptionList}
                                        </Checkbox.Group>
                                    </FormItem>
                                </Fragment>
                            )}
                            {/* 日常活动 */}
                            {actSubType == ACTSUBTYPES.POPUP && configType === '01' && (
                                <FormItem label="领券限时" wrapperCol={{ span: 24 }}>
                                    <Space>
                                        <FormItem noStyle name={'hourTimes'}>
                                            <InputNumber
                                                precision={0}
                                                step={1}
                                                min={0}
                                                placeholder="请填写"
                                                addonAfter="小时"
                                                disabled={isLock}
                                            ></InputNumber>
                                        </FormItem>
                                        <FormItem noStyle name={'minuteTimes'}>
                                            <InputNumber
                                                precision={0}
                                                step={1}
                                                min={0}
                                                max={59}
                                                placeholder="请填写"
                                                addonAfter="分钟"
                                                disabled={isLock}
                                            ></InputNumber>
                                        </FormItem>
                                        <span style={{ color: 'gray' }}>
                                            不设置代表不限制领取时间
                                        </span>
                                    </Space>
                                </FormItem>
                            )}

                            {(actSubType == ACTSUBTYPES.RECEIVE ||
                                actSubType == ACTSUBTYPES.DIRECT ||
                                actSubType == ACTSUBTYPES.POPUP) && (
                                <FormItem
                                    label="限制时段"
                                    name="actTimeLimitFlag"
                                    required
                                    rules={[
                                        () => ({
                                            validator(rule, value) {
                                                if (actTimeLimitFlag == 1 && !actTimeList?.length) {
                                                    return Promise.reject(
                                                        '请添加限制时段，或选择“不限制”',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <FormItem
                                        name="actTimeLimitFlag"
                                        noStyle={actTimeLimitFlag == 1 ? false : true}
                                        initialValue={'0'}
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择',
                                            },
                                        ]}
                                    >
                                        <Radio.Group disabled={isLock}>
                                            <Radio value="0">不限制</Radio>
                                            <Radio value="1">限制</Radio>
                                        </Radio.Group>
                                    </FormItem>

                                    {(actTimeLimitFlag == 1 && (
                                        <Form.List name="actTimeList">
                                            {(fields, { add, remove }) => {
                                                return (
                                                    <Fragment>
                                                        {fields.map((field, index) => {
                                                            return (
                                                                <>
                                                                    <Form.Item
                                                                        style={{
                                                                            whiteSpace: 'nowrap',
                                                                        }}
                                                                    >
                                                                        <span>从&nbsp;</span>
                                                                        <Form.Item
                                                                            noStyle
                                                                            name={[
                                                                                index,
                                                                                'beginTime',
                                                                            ]}
                                                                            rules={[
                                                                                () => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        return limitTimeRule(
                                                                                            actTimeList,
                                                                                            index,
                                                                                            true,
                                                                                        );
                                                                                    },
                                                                                }),
                                                                            ]}
                                                                        >
                                                                            <DatePicker
                                                                                format="HH:mm"
                                                                                picker="time"
                                                                                showNow={false}
                                                                                placeholder="开始时间"
                                                                                disabled={isLock}
                                                                            />
                                                                        </Form.Item>
                                                                        &nbsp;至&nbsp;
                                                                        <Form.Item
                                                                            noStyle
                                                                            name={[
                                                                                index,
                                                                                'endTime',
                                                                            ]}
                                                                            rules={[
                                                                                () => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        return limitTimeRule(
                                                                                            actTimeList,
                                                                                            index,
                                                                                            false,
                                                                                        );
                                                                                    },
                                                                                }),
                                                                            ]}
                                                                        >
                                                                            <DatePicker
                                                                                format="HH:mm"
                                                                                picker="time"
                                                                                showNow={false}
                                                                                placeholder="结束时间"
                                                                                disabled={isLock}
                                                                            />
                                                                        </Form.Item>
                                                                        {!isLock ? (
                                                                            <MinusCircleOutlined
                                                                                onClick={() => {
                                                                                    remove(
                                                                                        field.name,
                                                                                    );
                                                                                }}
                                                                                style={{
                                                                                    width: '8%',
                                                                                }}
                                                                            />
                                                                        ) : null}
                                                                    </Form.Item>
                                                                </>
                                                            );
                                                        })}

                                                        <FormItem label="">
                                                            <Button
                                                                type="primary"
                                                                disabled={isLock}
                                                                onClick={() => {
                                                                    add();
                                                                }}
                                                            >
                                                                <PlusOutlined />
                                                                增加时段
                                                            </Button>
                                                        </FormItem>
                                                    </Fragment>
                                                );
                                            }}
                                        </Form.List>
                                    )) ||
                                        null}
                                </FormItem>
                            )}
                            {((actSubType == ACTSUBTYPES.RECEIVE ||
                                actSubType == ACTSUBTYPES.DIRECT) && (
                                <FormItem
                                    label="活动形式"
                                    name="activityForm"
                                    {...formItemFixedWidthLayout}
                                    rules={[{ required: true, message: '请选择活动形式' }]}
                                    initialValue={'01'}
                                >
                                    <Radio.Group
                                        disabled={isLock}
                                        onChange={(e) => {
                                            if (e.target.value == '02') {
                                                // 如果选了付费领取，是否展示只能选是，并且不支持切换
                                                form.setFieldsValue({ directDisplay: '1' });
                                            }
                                        }}
                                    >
                                        <Radio value="01">免费领取</Radio>
                                        <Radio
                                            value="02"
                                            disabled={
                                                cooperationPlatform ===
                                                COOPERATION_PLATFORM_TYPES.XDT_X
                                            }
                                        >
                                            付费领取
                                        </Radio>
                                    </Radio.Group>
                                </FormItem>
                            )) ||
                                null}
                        </>
                    );
                }}
            </FormItem>

            {/* <FormItem label="活动说明" name="actMarks" {...formItemFixedWidthLayout}>
                <TextArea
                    disabled={isLock}
                    rows={6}
                    maxLength={200}
                    placeholder="请填写"
                    showCount
                />
            </FormItem> */}

            <QuillRichInput
                ref={markRef}
                label="活动说明"
                name="actMarks"
                {...formItemFixedWidthLayout}
                wrapperCol={{
                    span: 12,
                }}
                disabled={!openSomeThing}
                placeholder="请填写"
                rules={[
                    { required: true },
                    () => ({
                        validator(rule, value) {
                            if (value == '<p><br></p>') {
                                return Promise.reject('请填写活动说明');
                            }
                            const markLength = markRef?.current?.getRichTextLength();

                            if (value && markLength - 1 > 3000) {
                                return Promise.reject('限3000个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            ></QuillRichInput>
        </Fragment>
    );
};

export default InfoFormLayout;
