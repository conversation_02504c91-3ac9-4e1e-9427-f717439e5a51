import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import {
    Button,
    InputNumber,
    Form,
    Tooltip,
    Space,
    Radio,
    Checkbox,
    Popconfirm,
    Divider,
    Select,
    Typography,
} from 'antd';

import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';

import {
    ACTSUBTYPES,
    EXCHANGESTATUS,
    APPLET_ACT_TYPES,
    ACT_VERSION_TYPES,
    COOPERATION_PLATFORM_TYPES,
} from '@/config/declare';
import CityImportModal from '@/components/CitysSelect/CityImportModal';
import commonStyles from '@/assets/styles/common.less';
import styles from '../AppletGift.less';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import CouponTable from '@/components/CouponComponents/CouponTable';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import { copyObjectCommon, filterTreeList, isEmpty } from '@/utils/utils';
import CityTransferModal from '@/components/CityTransferModal';
import {
    COUPON_CUSTOM_PAGE_TYPE,
    CouponComponents,
} from '@/newComponents/SelectPrizeItem/SelectCouponItem';
import PriceCrowdItem from '../../Membership/components/PriceCrowdItem';
import { validatorCrowdItem } from '../../Membership//components/ChildStrategyItem';

const FormItem = Form.Item;

const NEW_REGISTER_USER = '28'; //新注册用户类型

const SUBMIT_TYPES = {
    SAVE: 'save',
    SUBMIT: 'submit',
};

export const USER_JOIN_TYPES = {
    NEW: '1', //新用户参加
    OTHER: '0', //其他用户
};

const RuleLayout = (props) => {
    const {
        applyType = '',
        dispatch,
        match,
        name = 'actPushBoList',
        form,
        isLock,
        isEnd,
        isLook,
        openSomeThing,
        appletGiftModel: { editActInfo = {} },
        appletGiftModel2: { editActInfo2 = {} },
        couponModel,
        newCouponModel,
        global,
        isCopy,
        submitType,
        route,
        formItemLayout,
        formItemFixedWidthLayout,
        version,
        appendFinished,
    } = props;

    const { codeInfo, custLabelTypeList } = global;

    const formItemName = useMemo(() => {
        if (typeof name === 'string') {
            return [name];
        } else if (name instanceof Array) {
            return name;
        }
    }, [name]);

    const cityModalRef = useRef();
    const cityImportModalRef = useRef();
    const [cooperationPlatform, setCooperationPlatform] = useState(COOPERATION_PLATFORM_TYPES.XDT);
    const { sourceChannel: sourceChannelList, actChannel } = codeInfo;

    useEffect(() => {
        if (!(custLabelTypeList instanceof Array) || custLabelTypeList?.length === 0) {
            dispatch({
                type: 'global/initCustLabelTypeList',
            });
        }
        if (!actChannel) {
            dispatch({
                type: 'global/initCode',
                code: 'actChannel',
            });
        }
    }, []);

    const channelOptionList = useMemo(() => {
        let arr = actChannel;
        switch (cooperationPlatform) {
            case COOPERATION_PLATFORM_TYPES.XDT:
                arr = arr?.filter((item) => {
                    return item.codeName.indexOf('新电途-') > -1;
                });
                break;
            case COOPERATION_PLATFORM_TYPES.XDT_X:
                arr = arr?.filter((item) => {
                    return item.codeName.indexOf('新电途X-') > -1;
                });
                break;
            default:
                break;
        }
        return (
            arr?.map((ele) => (
                <Checkbox key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Checkbox>
            )) || []
        );
    }, [actChannel, cooperationPlatform]);

    const info = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return editActInfo2;
        }
        return editActInfo;
    }, [version, editActInfo, editActInfo2]);

    const selectCityFinish = (citys = [], indexKey) => {
        let obj;
        let values;
        for (let index = 0; index < formItemName.length; index++) {
            const key = formItemName[index];
            if (!values) {
                values = form.getFieldValue(key);
            } else {
                obj = (obj || values).key;
            }
            if (index == formItemName.length - 1) {
                obj = (obj || values)?.[indexKey];
                obj.cityCodes = citys.map((ele) => ele.areaCode || ele);
            }
        }
        form.setFieldsValue({
            [formItemName[0]]: values,
        });
    };

    return (
        <Fragment>
            <Form.List name={formItemName} initialValue={[{}]}>
                {(fields, { add, remove }) => {
                    return (
                        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                            {({ getFieldValue }) => {
                                const actSubType = getFieldValue('actSubType');
                                const activityForm = getFieldValue('activityForm');
                                const configType = getFieldValue('configType');
                                const cooperationPlatform = getFieldValue('cooperationPlatform');

                                setCooperationPlatform(cooperationPlatform);
                                const listData = getFieldValue(formItemName);

                                const canIadd =
                                    !isLock &&
                                    actSubType === ACTSUBTYPES.DIRECT &&
                                    listData?.length < 8;
                                return (
                                    <Fragment>
                                        {fields.map((field, index) => {
                                            const popScope = listData[field.name]?.popScope;
                                            const putCpnList = listData[field.name]?.cpnList;
                                            const actSubId = listData[field.name]?.actSubId;
                                            const onlyNewJoinFlag =
                                                listData[field.name]?.onlyNewJoinFlag;
                                            const showTimesType =
                                                listData[field.name]?.showTimesType;
                                            const advertiseType =
                                                listData[field.name]?.advertiseType;
                                            let otherCpnList = [];
                                            for (let i = listData?.length - 1; i >= 0; i--) {
                                                const task = listData[i];
                                                const cpnList = task?.cpnList;
                                                if (index != i && cpnList?.length) {
                                                    otherCpnList = [...cpnList, ...otherCpnList];
                                                }
                                            }

                                            const { custLabelList = [] } =
                                                listData[field.name].crowdInfo || {};

                                            //当前用户选中人群
                                            let curItemActiveCrowd = [];

                                            if (configType === '01') {
                                                curItemActiveCrowd = custLabelList || [];
                                            } else if (configType === '02') {
                                                curItemActiveCrowd =
                                                    listData[field.name]?.activeCrowd || [];
                                            }

                                            //所有用户选中人群
                                            let allActiveCrowds = listData.reduce((pre, last) => {
                                                const { custLabelList: childCustLiabelList } =
                                                    last.crowdInfo || {};
                                                return [
                                                    ...pre,
                                                    ...(childCustLiabelList ||
                                                        last.activeCrowd ||
                                                        []),
                                                ];
                                            }, []);

                                            //当前人群禁选项
                                            let curDisableCrowds = [];

                                            //页面领取禁用这几个人群选择
                                            const defaultDisableCrowds = [];

                                            if (actSubType === ACTSUBTYPES.DIRECT) {
                                                defaultDisableCrowds.push(
                                                    '01',
                                                    '02',
                                                    '03',
                                                    '04',
                                                    '05',
                                                    '06',
                                                    '28',
                                                    'novip1',
                                                    'novip2',
                                                    'novip3',
                                                    'novip4',
                                                    'expvip1',
                                                    'expvip2',
                                                    'expvip3',
                                                    'effvip1',
                                                    'effvip2',
                                                    'effvip3',
                                                    'effvip4',
                                                );
                                            }
                                            if (defaultDisableCrowds?.length) {
                                                const treeList =
                                                    copyObjectCommon(custLabelTypeList);
                                                curDisableCrowds = filterTreeList(
                                                    treeList,
                                                    defaultDisableCrowds,
                                                );
                                            }

                                            // if (actSubType === ACTSUBTYPES.POPUP) {
                                            if (curItemActiveCrowd.includes(NEW_REGISTER_USER)) {
                                                //包含新人就不能选其他选项

                                                if (custLabelTypeList instanceof Array) {
                                                    const treeList =
                                                        copyObjectCommon(custLabelTypeList);

                                                    curDisableCrowds = filterTreeList(treeList, [
                                                        NEW_REGISTER_USER,
                                                    ]);
                                                }
                                            }
                                            // }

                                            //计算当前用户不能选的最终选项
                                            curDisableCrowds = [
                                                ...curDisableCrowds,
                                                ...allActiveCrowds,
                                            ].filter((ele) => !curItemActiveCrowd.includes(ele));

                                            console.log(
                                                424324,
                                                curDisableCrowds,
                                                curItemActiveCrowd,
                                            );

                                            return (
                                                <Fragment key={index}>
                                                    {actSubType === ACTSUBTYPES.POPUP &&
                                                    configType == '01' ? (
                                                        <FormItem
                                                            label={'目标用户'}
                                                            name={[field.name, 'onlyNewJoinFlag']}
                                                            initialValue={USER_JOIN_TYPES.OTHER}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择',
                                                                },
                                                            ]}
                                                        >
                                                            <Radio.Group disabled={isLock}>
                                                                <Radio
                                                                    value={USER_JOIN_TYPES.OTHER}
                                                                >
                                                                    选择人群
                                                                </Radio>
                                                                <Radio value={USER_JOIN_TYPES.NEW}>
                                                                    <Space direction="vertical">
                                                                        <span>仅限新用户参与</span>
                                                                        {onlyNewJoinFlag ==
                                                                            USER_JOIN_TYPES.NEW && (
                                                                            <Space
                                                                                direction="vertical"
                                                                                onClick={(
                                                                                    event,
                                                                                ) => {
                                                                                    event.stopPropagation();
                                                                                    event.preventDefault();
                                                                                }}
                                                                            >
                                                                                <Typography.Text type="secondary">
                                                                                    主要应用于【新人礼包】场景
                                                                                </Typography.Text>
                                                                                <FormItem
                                                                                    name={[
                                                                                        field.name,
                                                                                        'sourceChannel',
                                                                                    ]}
                                                                                    noStyle
                                                                                    rules={[
                                                                                        {
                                                                                            required: true,
                                                                                            message:
                                                                                                '请选择注册场景码',
                                                                                        },
                                                                                    ]}
                                                                                >
                                                                                    <Select
                                                                                        placeholder="请选择"
                                                                                        addonBefore="且用户注册场景码属于"
                                                                                        allowClear
                                                                                        mode="multiple"
                                                                                        disabled={
                                                                                            isLock
                                                                                        }
                                                                                        options={sourceChannelList?.map(
                                                                                            (
                                                                                                ele,
                                                                                            ) => {
                                                                                                return {
                                                                                                    label: ele.codeName,
                                                                                                    value: ele.codeValue,
                                                                                                };
                                                                                            },
                                                                                        )}
                                                                                    ></Select>
                                                                                </FormItem>
                                                                            </Space>
                                                                        )}
                                                                    </Space>
                                                                </Radio>
                                                            </Radio.Group>
                                                        </FormItem>
                                                    ) : null}
                                                    {actSubType === ACTSUBTYPES.DIRECT ? (
                                                        <FormItem
                                                            label={
                                                                <h3
                                                                    className={
                                                                        commonStyles['card-title']
                                                                    }
                                                                    style={{ margin: 0 }}
                                                                >
                                                                    {`活动用户${field.name + 1}`}
                                                                </h3>
                                                            }
                                                        >
                                                            {isLock || index < 1 ? null : (
                                                                <Popconfirm
                                                                    title={`确认删除活动用户${
                                                                        field.name + 1
                                                                    }？`}
                                                                    okText="是"
                                                                    cancelText="否"
                                                                    onConfirm={() => {
                                                                        remove(field.name);
                                                                    }}
                                                                >
                                                                    <Button plain danger>
                                                                        删除
                                                                    </Button>
                                                                </Popconfirm>
                                                            )}
                                                        </FormItem>
                                                    ) : null}
                                                    {(actSubType === ACTSUBTYPES.DIRECT ||
                                                        actSubType === ACTSUBTYPES.RECEIVE ||
                                                        actSubType === ACTSUBTYPES.POPUP) && (
                                                        <Fragment>
                                                            {/* 限时秒杀使用旧人群 */}
                                                            {version === ACT_VERSION_TYPES.V_2_0 &&
                                                            configType != '02' ? (
                                                                <FormItem
                                                                    name={[field.name, 'crowdInfo']}
                                                                    label="人群选择"
                                                                    wrapperCol={{ span: 24 }}
                                                                    required
                                                                    hidden={
                                                                        onlyNewJoinFlag ===
                                                                        USER_JOIN_TYPES.NEW
                                                                    }
                                                                    rules={[
                                                                        (_) => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    onlyNewJoinFlag ==
                                                                                    USER_JOIN_TYPES.NEW
                                                                                ) {
                                                                                    return Promise.resolve();
                                                                                }
                                                                                if (
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        '请选择人群',
                                                                                    );
                                                                                }

                                                                                const crowdHelp =
                                                                                    validatorCrowdItem(
                                                                                        value,
                                                                                    );
                                                                                if (crowdHelp) {
                                                                                    return Promise.reject(
                                                                                        crowdHelp,
                                                                                    );
                                                                                }

                                                                                const {
                                                                                    custLabelList,
                                                                                } = value;
                                                                                if (
                                                                                    // actSubType ===
                                                                                    //     ACTSUBTYPES.POPUP &&
                                                                                    custLabelList.includes(
                                                                                        NEW_REGISTER_USER,
                                                                                    ) &&
                                                                                    custLabelList.length >
                                                                                        1
                                                                                ) {
                                                                                    //新手
                                                                                    listData[
                                                                                        index
                                                                                    ].crowdInfo = {
                                                                                        ...listData[
                                                                                            index
                                                                                        ].crowdInfo,
                                                                                        custLabelList:
                                                                                            [
                                                                                                NEW_REGISTER_USER,
                                                                                            ],
                                                                                    };
                                                                                    listData[
                                                                                        index
                                                                                    ].activeCrowd =
                                                                                        [
                                                                                            NEW_REGISTER_USER,
                                                                                        ];

                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            [formItemName]:
                                                                                                listData,
                                                                                        },
                                                                                    );
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <PriceCrowdItem
                                                                        applyType={applyType}
                                                                        disabled={isLock}
                                                                        disabledIds={
                                                                            curDisableCrowds
                                                                        }
                                                                        allXdtLabel
                                                                    ></PriceCrowdItem>
                                                                </FormItem>
                                                            ) : (
                                                                <ActiveCrowdCheckTree
                                                                    label="参与用户"
                                                                    form={form}
                                                                    parentName={[
                                                                        ...formItemName,
                                                                        field.name,
                                                                    ]}
                                                                    required
                                                                    rules={[
                                                                        (_) => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    !value ||
                                                                                    value?.length ===
                                                                                        0
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        '请选择参与用户',
                                                                                    );
                                                                                }

                                                                                if (
                                                                                    // actSubType ===
                                                                                    //     ACTSUBTYPES.POPUP &&
                                                                                    value.includes(
                                                                                        NEW_REGISTER_USER,
                                                                                    ) &&
                                                                                    value.length > 1
                                                                                ) {
                                                                                    //新手
                                                                                    listData[
                                                                                        index
                                                                                    ].activeCrowd =
                                                                                        [
                                                                                            NEW_REGISTER_USER,
                                                                                        ];
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            [formItemName]:
                                                                                                listData,
                                                                                        },
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                    disabled={isLock}
                                                                    maxTagCount={3}
                                                                    initialValue={[]}
                                                                    disableIds={curDisableCrowds}
                                                                    hideAll
                                                                    {...formItemFixedWidthLayout}
                                                                />
                                                            )}
                                                        </Fragment>
                                                    )}
                                                    {actSubType == ACTSUBTYPES.RECEIVE && (
                                                        <CheckBoxGroup
                                                            label="注册来源"
                                                            name={[field.name, 'sourceChannel']}
                                                            initialValue={
                                                                (sourceChannelList &&
                                                                    sourceChannelList?.map(
                                                                        (ele) => ele.codeValue,
                                                                    )) ||
                                                                []
                                                            }
                                                            parentName={formItemName}
                                                            fieldKey={field.fieldKey}
                                                            form={form}
                                                            selectList={sourceChannelList}
                                                            disabled={isLock}
                                                            required
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择渠道来源',
                                                                },
                                                            ]}
                                                        ></CheckBoxGroup>
                                                    )}
                                                    {actSubType == ACTSUBTYPES.POPUP && (
                                                        <Fragment>
                                                            <FormItem
                                                                label={<span>适用渠道</span>}
                                                                name={[field.name, 'actChannel']}
                                                                wrapperCol={{ span: 16 }}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '请选择投放渠道',
                                                                    },
                                                                ]}
                                                            >
                                                                <Checkbox.Group disabled={isLock}>
                                                                    {channelOptionList}
                                                                </Checkbox.Group>
                                                            </FormItem>
                                                            <FormItem
                                                                label="适用区域"
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '请设置适用区域',
                                                                    },
                                                                ]}
                                                                initialValue="all"
                                                                name={[field.name, 'popScope']}
                                                            >
                                                                <Radio.Group
                                                                    disabled={!openSomeThing}
                                                                >
                                                                    <Radio value={'all'}>
                                                                        全平台
                                                                    </Radio>
                                                                    <Radio value={'city'}>
                                                                        选择区域
                                                                    </Radio>
                                                                </Radio.Group>
                                                            </FormItem>

                                                            {popScope === 'city' && (
                                                                <FormItem
                                                                    noStyle
                                                                    shouldUpdate={(
                                                                        prevValues,
                                                                        curValues,
                                                                    ) =>
                                                                        prevValues?.[field.name]
                                                                            ?.cityCodes !==
                                                                        curValues?.[field.name]
                                                                            ?.cityCodes
                                                                    }
                                                                >
                                                                    {({ getFieldValue }) => {
                                                                        const cityCodes =
                                                                            getFieldValue([
                                                                                ...formItemName,
                                                                                field.name,
                                                                                'cityCodes',
                                                                            ]);
                                                                        return (
                                                                            <FormItem
                                                                                label="开通城市"
                                                                                name={[
                                                                                    field.name,
                                                                                    'cityCodes',
                                                                                ]}
                                                                                required
                                                                                rules={[
                                                                                    ({
                                                                                        getFieldValue,
                                                                                    }) => ({
                                                                                        validator(
                                                                                            rule,
                                                                                            value,
                                                                                        ) {
                                                                                            if (
                                                                                                !cityCodes?.length
                                                                                            ) {
                                                                                                return Promise.reject(
                                                                                                    '请选择开通城市',
                                                                                                );
                                                                                            }
                                                                                            return Promise.resolve();
                                                                                        },
                                                                                    }),
                                                                                ]}
                                                                            >
                                                                                {(cityCodes?.length && (
                                                                                    <Space size="small">
                                                                                        {(openSomeThing && (
                                                                                            <Button
                                                                                                type="link"
                                                                                                onClick={() => {
                                                                                                    cityModalRef?.current?.show(
                                                                                                        {
                                                                                                            defaultKeys:
                                                                                                                cityCodes,
                                                                                                            disabledKeys:
                                                                                                                info?.cityInfos?.map(
                                                                                                                    (
                                                                                                                        ele,
                                                                                                                    ) =>
                                                                                                                        ele.areaCode,
                                                                                                                ),
                                                                                                        },
                                                                                                    );
                                                                                                }}
                                                                                            >
                                                                                                编辑
                                                                                            </Button>
                                                                                        )) ||
                                                                                            null}
                                                                                        {(openSomeThing && (
                                                                                            <Divider
                                                                                                type="vertical"
                                                                                                style={{
                                                                                                    margin: 0,
                                                                                                }}
                                                                                            />
                                                                                        )) ||
                                                                                            null}
                                                                                        <Button
                                                                                            type="link"
                                                                                            onClick={() =>
                                                                                                cityModalRef?.current?.show(
                                                                                                    {
                                                                                                        defaultKeys:
                                                                                                            cityCodes,
                                                                                                        disabled: true,
                                                                                                    },
                                                                                                )
                                                                                            }
                                                                                        >
                                                                                            查看
                                                                                        </Button>
                                                                                        {(openSomeThing && (
                                                                                            <Divider
                                                                                                type="vertical"
                                                                                                style={{
                                                                                                    margin: 0,
                                                                                                }}
                                                                                            />
                                                                                        )) ||
                                                                                            null}
                                                                                        {(openSomeThing && (
                                                                                            <Button
                                                                                                type="link"
                                                                                                onClick={() => {
                                                                                                    cityImportModalRef?.current?.show(
                                                                                                        {
                                                                                                            ids: cityCodes,
                                                                                                        },
                                                                                                    );
                                                                                                }}
                                                                                            >
                                                                                                导入
                                                                                            </Button>
                                                                                        )) ||
                                                                                            null}
                                                                                    </Space>
                                                                                )) ||
                                                                                    (openSomeThing && (
                                                                                        <Space size="small">
                                                                                            <Button
                                                                                                type="primary"
                                                                                                onClick={() => {
                                                                                                    cityModalRef?.current?.show();
                                                                                                }}
                                                                                                disabled={
                                                                                                    !openSomeThing &&
                                                                                                    info?.actSubType !==
                                                                                                        ACTSUBTYPES.POPUP
                                                                                                }
                                                                                            >
                                                                                                选择区域
                                                                                            </Button>

                                                                                            <Button
                                                                                                type="primary"
                                                                                                onClick={() => {
                                                                                                    cityImportModalRef?.current?.show(
                                                                                                        {
                                                                                                            ids: cityCodes,
                                                                                                            disabledIds:
                                                                                                                info?.cityInfos?.map(
                                                                                                                    (
                                                                                                                        ele,
                                                                                                                    ) =>
                                                                                                                        ele.areaCode,
                                                                                                                ),
                                                                                                        },
                                                                                                    );
                                                                                                }}
                                                                                                disabled={
                                                                                                    !openSomeThing &&
                                                                                                    info?.actSubType !==
                                                                                                        ACTSUBTYPES.POPUP
                                                                                                }
                                                                                            >
                                                                                                文本导入
                                                                                            </Button>
                                                                                        </Space>
                                                                                    )) ||
                                                                                    null}

                                                                                <CityTransferModal
                                                                                    ref={
                                                                                        cityModalRef
                                                                                    }
                                                                                    onFinish={(
                                                                                        citys,
                                                                                    ) =>
                                                                                        selectCityFinish(
                                                                                            citys,
                                                                                            field.name,
                                                                                        )
                                                                                    }
                                                                                    deleteEnabled={
                                                                                        false
                                                                                    }
                                                                                />

                                                                                <CityImportModal
                                                                                    initRef={
                                                                                        cityImportModalRef
                                                                                    }
                                                                                    onFinish={(
                                                                                        citys,
                                                                                    ) => {
                                                                                        selectCityFinish(
                                                                                            [
                                                                                                ...(cityCodes ||
                                                                                                    []),
                                                                                                ...(citys ||
                                                                                                    []),
                                                                                            ],
                                                                                            field.name,
                                                                                        );
                                                                                    }}
                                                                                />
                                                                            </FormItem>
                                                                        );
                                                                    }}
                                                                </FormItem>
                                                                // <CitysSelect
                                                                //     label="区域"
                                                                //     name={[field.name, 'cityCodes']}
                                                                //     placeholder="请选择"
                                                                //     formItemLayout={
                                                                //         formItemFixedWidthLayout
                                                                //     }
                                                                //     disabled={
                                                                //         isLock &&
                                                                //         info?.actSubType !==
                                                                //             ACTSUBTYPES.POPUP
                                                                //     }
                                                                //     showArrow
                                                                //     allowClear
                                                                //     defaultCityIds={getFieldValue([
                                                                //         ...formItemName,
                                                                //         field.name,
                                                                //         'cityCodes',
                                                                //     ])}
                                                                //     provinceSelectable
                                                                // />
                                                            )}

                                                            {configType == '02' && (
                                                                <FormItem
                                                                    label="投放位置:"
                                                                    name={[
                                                                        field.name,
                                                                        'advertiseType',
                                                                    ]}
                                                                    {...formItemLayout}
                                                                    valuePropName="checked"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message: '请选择',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <Checkbox.Group
                                                                        placeholder={
                                                                            '请选择投放位置'
                                                                        }
                                                                        allowClear
                                                                        form={form}
                                                                        value={advertiseType}
                                                                        disabled={!openSomeThing}
                                                                    >
                                                                        {[
                                                                            {
                                                                                codeValue: '00',
                                                                                codeName: '全局',
                                                                            },
                                                                            {
                                                                                codeValue: '01',
                                                                                codeName: '首页',
                                                                            },
                                                                            {
                                                                                codeValue: '02',
                                                                                codeName: '下单页',
                                                                            },
                                                                            {
                                                                                codeValue: '03',
                                                                                codeName: '活动页',
                                                                            },
                                                                        ]?.map((ele, index) => (
                                                                            <Checkbox
                                                                                value={
                                                                                    ele.codeValue
                                                                                }
                                                                                key={ele.codeValue}
                                                                                disabled={
                                                                                    (advertiseType?.includes(
                                                                                        '00',
                                                                                    ) &&
                                                                                        ele.codeValue !=
                                                                                            '00') ||
                                                                                    (advertiseType?.length &&
                                                                                        !advertiseType?.includes(
                                                                                            '00',
                                                                                        ) &&
                                                                                        ele.codeValue ==
                                                                                            '00')
                                                                                }
                                                                            >
                                                                                {ele.codeName}
                                                                            </Checkbox>
                                                                        ))}
                                                                    </Checkbox.Group>
                                                                </FormItem>
                                                            )}
                                                        </Fragment>
                                                    )}

                                                    {version == ACT_VERSION_TYPES.V_2_0 ? (
                                                        <Fragment>
                                                            <FormItem
                                                                name={[
                                                                    field.fieldKey,
                                                                    'giftBagType',
                                                                ]}
                                                                noStyle
                                                                initialValue={'01'}
                                                            />
                                                            <FormItem
                                                                name={[
                                                                    field.fieldKey,
                                                                    'recommendCycle',
                                                                ]}
                                                                noStyle
                                                            />
                                                            <FormItem
                                                                name={[
                                                                    field.fieldKey,
                                                                    'recommendPrizeId',
                                                                ]}
                                                                noStyle
                                                            />
                                                            <CouponComponents
                                                                actId={info?.actId}
                                                                actSubId={actSubId}
                                                                dispatch={dispatch}
                                                                global={global}
                                                                couponModel={couponModel}
                                                                newCouponModel={newCouponModel}
                                                                form={form}
                                                                needCheck
                                                                disabled={info?.actState >= 1}
                                                                formItemLayout={{
                                                                    ...formItemLayout,
                                                                    wrapperCol: {
                                                                        span: 24,
                                                                    },
                                                                }}
                                                                title={'配置礼包'}
                                                                parentName={[field.fieldKey]}
                                                                listName="cpnList"
                                                                fullParentName={[
                                                                    ...formItemName,
                                                                    index,
                                                                ]}
                                                                onRefresh={() => {
                                                                    appendFinished?.();
                                                                }}
                                                                // 直接发放的商家端创建的活动不允许追加
                                                                canAppend={
                                                                    !(
                                                                        info?.actSubType ==
                                                                            ACTSUBTYPES.SEND &&
                                                                        info?.createSource == '02'
                                                                    ) &&
                                                                    (info?.actState == 2 ||
                                                                        info?.actState == 1)
                                                                }
                                                                singleMode={
                                                                    actSubType == ACTSUBTYPES.ALI
                                                                }
                                                                pageType={
                                                                    COUPON_CUSTOM_PAGE_TYPE.CPN_ACT_2_0
                                                                }
                                                                isCopy={isCopy}
                                                                extInParams={
                                                                    actSubType ==
                                                                        ACTSUBTYPES.ALI && {
                                                                        prizeType: '21',
                                                                    }
                                                                }
                                                                listParams={{ cooperationPlatform }}
                                                                prizeClass={
                                                                    actSubType == ACTSUBTYPES.ALI
                                                                        ? null
                                                                        : '1'
                                                                }
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!value) {
                                                                                return Promise.reject(
                                                                                    '请选择优惠券',
                                                                                );
                                                                            }
                                                                            if (
                                                                                value instanceof
                                                                                    Array &&
                                                                                value.length === 0
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    '请选择优惠券',
                                                                                );
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                                // 直接领取、兑换码兑换、支付宝商家券、第三方，不支持推荐功能
                                                                canRecommend={
                                                                    actSubType !==
                                                                        ACTSUBTYPES.SEND &&
                                                                    actSubType !==
                                                                        ACTSUBTYPES.PRIZE_SEND &&
                                                                    actSubType !==
                                                                        ACTSUBTYPES.COUPON
                                                                }
                                                                appendExtParams={{
                                                                    actType: editActInfo2?.actType,
                                                                    stockParamList: { actSubId },
                                                                }}
                                                            />
                                                            <FormItem noStyle name="actSubId" />
                                                        </Fragment>
                                                    ) : (
                                                        <CouponTable
                                                            title={
                                                                (actSubType == ACTSUBTYPES.ALI &&
                                                                    '配置礼包') ||
                                                                '优惠券'
                                                            }
                                                            form={form}
                                                            dispatch={dispatch}
                                                            global={global}
                                                            couponModel={couponModel}
                                                            actId={info?.actId}
                                                            needCheck={
                                                                submitType == SUBMIT_TYPES.SUBMIT
                                                            }
                                                            editabled={
                                                                route.path.indexOf('/look') < 0 &&
                                                                (isCopy ||
                                                                    (info?.actSubType !=
                                                                        ACTSUBTYPES.SEND &&
                                                                        info?.actSubType !=
                                                                            ACTSUBTYPES.PRIZE_SEND &&
                                                                        info?.actSubType !=
                                                                            ACTSUBTYPES.COUPON))
                                                            }
                                                            actState={info?.actState}
                                                            isCopy={isCopy}
                                                            fieldKey={field.fieldKey}
                                                            filePath={[...formItemName, index]}
                                                            rules={[
                                                                (_) => ({
                                                                    validator(rule, value) {
                                                                        if (!value) {
                                                                            return Promise.reject(
                                                                                '请选择优惠券',
                                                                            );
                                                                        }
                                                                        if (
                                                                            value instanceof
                                                                                Array &&
                                                                            value.length === 0
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '请选择优惠券',
                                                                            );
                                                                        }

                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                            putCpnList={putCpnList || []}
                                                            disabledIds={otherCpnList.map(
                                                                (ele) => ele.cpnId,
                                                            )}
                                                            onRefresh={() => {
                                                                appendFinished?.();
                                                            }}
                                                            formItemLayout={{
                                                                ...formItemLayout,
                                                                wrapperCol: {
                                                                    span: 24,
                                                                },
                                                            }}
                                                            singleMode={
                                                                actSubType == ACTSUBTYPES.ALI
                                                            }
                                                            extInParams={
                                                                actSubType == ACTSUBTYPES.ALI && {
                                                                    cpnOwner: '03',
                                                                }
                                                            }
                                                            // pageType="appletGift"
                                                            pageType={
                                                                actSubType == ACTSUBTYPES.ALI &&
                                                                'aliCpn'
                                                            }
                                                        />
                                                    )}

                                                    <div style={{ marginTop: '20px' }}>
                                                        <FormItem
                                                            noStyle
                                                            shouldUpdate={(prevValues, curValues) =>
                                                                true
                                                            }
                                                        >
                                                            {({ getFieldValue }) => {
                                                                const actSubType =
                                                                    getFieldValue('actSubType');

                                                                return (
                                                                    <Fragment>
                                                                        {(actSubType !==
                                                                            ACTSUBTYPES.THIRD &&
                                                                            actSubType !==
                                                                                ACTSUBTYPES.SEND && (
                                                                                <Fragment>
                                                                                    <FormItem
                                                                                        name={[
                                                                                            field.name,
                                                                                            'actGetLimitType',
                                                                                        ]}
                                                                                        fieldKey={
                                                                                            field.fieldKey
                                                                                        }
                                                                                        initialValue={
                                                                                            '01'
                                                                                        }
                                                                                        label={
                                                                                            <span>
                                                                                                单次领取数量
                                                                                                <Tooltip title="配置券发放的数量是固定的还是根据前端参数来发">
                                                                                                    <InfoCircleOutlined
                                                                                                        style={{
                                                                                                            marginLeft:
                                                                                                                '6px',
                                                                                                        }}
                                                                                                    />
                                                                                                </Tooltip>
                                                                                            </span>
                                                                                        }
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                message:
                                                                                                    '请选择',
                                                                                            },
                                                                                            (
                                                                                                _,
                                                                                            ) => ({
                                                                                                validator(
                                                                                                    rule,
                                                                                                    value,
                                                                                                ) {
                                                                                                    if (
                                                                                                        !value
                                                                                                    ) {
                                                                                                        return Promise.reject(
                                                                                                            '',
                                                                                                        );
                                                                                                    }
                                                                                                    if (
                                                                                                        (actSubType ==
                                                                                                            ACTSUBTYPES.SEND ||
                                                                                                            actSubType ==
                                                                                                                ACTSUBTYPES.PRIZE_SEND ||
                                                                                                            actSubType ==
                                                                                                                ACTSUBTYPES.COUPON) &&
                                                                                                        value ==
                                                                                                            '02'
                                                                                                    ) {
                                                                                                        return Promise.reject(
                                                                                                            `活动类型为${
                                                                                                                ((actSubType ==
                                                                                                                    ACTSUBTYPES.SEND ||
                                                                                                                    actSubType ==
                                                                                                                        ACTSUBTYPES.PRIZE_SEND) &&
                                                                                                                    '直接发放') ||
                                                                                                                '兑换码兑换'
                                                                                                            }时，请选择固定数量`,
                                                                                                        );
                                                                                                    }
                                                                                                    return Promise.resolve();
                                                                                                },
                                                                                            }),
                                                                                        ]}
                                                                                    >
                                                                                        <Radio.Group
                                                                                            disabled
                                                                                        >
                                                                                            <Radio value="01">
                                                                                                固定数量
                                                                                            </Radio>
                                                                                            <Radio value="02">
                                                                                                参数领取
                                                                                                {(actSubType !=
                                                                                                    ACTSUBTYPES.ALI && (
                                                                                                    <Tooltip title="用于第三方领取（如:花芝租）">
                                                                                                        <InfoCircleOutlined
                                                                                                            style={{
                                                                                                                marginLeft:
                                                                                                                    '6px',
                                                                                                            }}
                                                                                                        />
                                                                                                    </Tooltip>
                                                                                                )) ||
                                                                                                    null}
                                                                                            </Radio>
                                                                                        </Radio.Group>
                                                                                    </FormItem>
                                                                                    <FormItem
                                                                                        label={
                                                                                            <span>
                                                                                                每人限领数量
                                                                                                {/* <Tooltip title="收藏有礼活动系默认座每个用户限领1组">
                                                                                        <InfoCircleOutlined
                                                                                            style={{
                                                                                                marginLeft:
                                                                                                    '6px',
                                                                                            }}
                                                                                        />
                                                                                    </Tooltip> */}
                                                                                            </span>
                                                                                        }
                                                                                        required
                                                                                        extra={
                                                                                            actSubType ===
                                                                                                ACTSUBTYPES.PRIZE_SEND &&
                                                                                            '本字段配置暂不生效'
                                                                                        }
                                                                                    >
                                                                                        <FormItem
                                                                                            name={[
                                                                                                field.name,
                                                                                                'actGetLimitNum',
                                                                                            ]}
                                                                                            fieldKey={
                                                                                                field.fieldKey
                                                                                            }
                                                                                            initialValue={
                                                                                                1
                                                                                            }
                                                                                            noStyle
                                                                                            rules={[
                                                                                                {
                                                                                                    required: true,
                                                                                                    message:
                                                                                                        '请填写限制数量',
                                                                                                },
                                                                                                (
                                                                                                    _,
                                                                                                ) => ({
                                                                                                    validator(
                                                                                                        rule,
                                                                                                        value,
                                                                                                    ) {
                                                                                                        const {
                                                                                                            custLabelList = [],
                                                                                                        } =
                                                                                                            listData[
                                                                                                                index
                                                                                                            ]
                                                                                                                ?.crowdInfo ||
                                                                                                            {};

                                                                                                        const activeCrowd =
                                                                                                            listData[
                                                                                                                index
                                                                                                            ]
                                                                                                                .activeCrowd;

                                                                                                        let crowdList =
                                                                                                            custLabelList;

                                                                                                        if (
                                                                                                            version ==
                                                                                                            ACT_VERSION_TYPES.V_1_0
                                                                                                        ) {
                                                                                                            crowdList =
                                                                                                                activeCrowd;
                                                                                                        }

                                                                                                        if (
                                                                                                            crowdList?.find(
                                                                                                                (
                                                                                                                    element,
                                                                                                                ) =>
                                                                                                                    element?.custLabel ===
                                                                                                                        NEW_REGISTER_USER ||
                                                                                                                    element ===
                                                                                                                        NEW_REGISTER_USER,
                                                                                                            ) &&
                                                                                                            value >=
                                                                                                                0 &&
                                                                                                            value !=
                                                                                                                1
                                                                                                        ) {
                                                                                                            return Promise.reject(
                                                                                                                `新注册用户只能配1组`,
                                                                                                            );
                                                                                                        }
                                                                                                        return Promise.resolve();
                                                                                                    },
                                                                                                }),
                                                                                            ]}
                                                                                        >
                                                                                            <InputNumber
                                                                                                precision={
                                                                                                    0
                                                                                                }
                                                                                                min={
                                                                                                    1
                                                                                                }
                                                                                                disabled={
                                                                                                    isLock
                                                                                                }
                                                                                            />
                                                                                        </FormItem>
                                                                                        <span
                                                                                            className={
                                                                                                styles.btnMargin
                                                                                            }
                                                                                        >
                                                                                            组
                                                                                        </span>
                                                                                    </FormItem>
                                                                                </Fragment>
                                                                            )) ||
                                                                            null}
                                                                        {(actSubType ===
                                                                            ACTSUBTYPES.DIRECT ||
                                                                            actSubType ===
                                                                                ACTSUBTYPES.RECEIVE ||
                                                                            actSubType ===
                                                                                ACTSUBTYPES.POPUP) && (
                                                                            <FormItem
                                                                                label="每人日限领数量"
                                                                                required
                                                                            >
                                                                                <FormItem
                                                                                    name={[
                                                                                        field.name,
                                                                                        'everyDayGetLimitNum',
                                                                                    ]}
                                                                                    fieldKey={
                                                                                        field.fieldKey
                                                                                    }
                                                                                    noStyle
                                                                                    initialValue={1}
                                                                                    rules={[
                                                                                        {
                                                                                            required: true,
                                                                                            message:
                                                                                                '请填写每人日限领数量',
                                                                                        },
                                                                                        (_) => ({
                                                                                            validator(
                                                                                                rule,
                                                                                                value,
                                                                                            ) {
                                                                                                const {
                                                                                                    custLabelList = [],
                                                                                                } =
                                                                                                    listData[
                                                                                                        index
                                                                                                    ]
                                                                                                        .crowdInfo ||
                                                                                                    {};

                                                                                                const activeCrowd =
                                                                                                    listData[
                                                                                                        index
                                                                                                    ]
                                                                                                        .activeCrowd;
                                                                                                const actGetLimitNum =
                                                                                                    listData[
                                                                                                        index
                                                                                                    ]
                                                                                                        .actGetLimitNum;

                                                                                                let crowdList =
                                                                                                    custLabelList;

                                                                                                if (
                                                                                                    version ==
                                                                                                    ACT_VERSION_TYPES.V_1_0
                                                                                                ) {
                                                                                                    crowdList =
                                                                                                        activeCrowd;
                                                                                                }
                                                                                                if (
                                                                                                    crowdList?.find(
                                                                                                        (
                                                                                                            element,
                                                                                                        ) =>
                                                                                                            element?.custLabel ===
                                                                                                                NEW_REGISTER_USER ||
                                                                                                            element ===
                                                                                                                NEW_REGISTER_USER,
                                                                                                    ) &&
                                                                                                    value >=
                                                                                                        0 &&
                                                                                                    value !=
                                                                                                        1
                                                                                                ) {
                                                                                                    return Promise.reject(
                                                                                                        `新注册用户只能配1组`,
                                                                                                    );
                                                                                                }

                                                                                                if (
                                                                                                    value &&
                                                                                                    actGetLimitNum &&
                                                                                                    Number(
                                                                                                        value,
                                                                                                    ) >
                                                                                                        Number(
                                                                                                            actGetLimitNum,
                                                                                                        )
                                                                                                ) {
                                                                                                    return Promise.reject(
                                                                                                        `每人日限领数量不能大于限领数量`,
                                                                                                    );
                                                                                                }
                                                                                                const cpnList =
                                                                                                    listData[
                                                                                                        index
                                                                                                    ]
                                                                                                        ?.cpnList;
                                                                                                if (
                                                                                                    cpnList &&
                                                                                                    cpnList.length >
                                                                                                        0
                                                                                                ) {
                                                                                                    let minPutNum =
                                                                                                        undefined;
                                                                                                    for (const cpn of cpnList) {
                                                                                                        const putTimes =
                                                                                                            Number(
                                                                                                                cpn.unGetNum ||
                                                                                                                    cpn.cpnNum,
                                                                                                            ) /
                                                                                                            Number(
                                                                                                                cpn.putNum,
                                                                                                            );
                                                                                                        if (
                                                                                                            minPutNum ===
                                                                                                                undefined ||
                                                                                                            minPutNum >
                                                                                                                putTimes
                                                                                                        ) {
                                                                                                            minPutNum =
                                                                                                                putTimes;
                                                                                                        }
                                                                                                    }
                                                                                                    if (
                                                                                                        value &&
                                                                                                        Number(
                                                                                                            value,
                                                                                                        ) >
                                                                                                            minPutNum
                                                                                                    ) {
                                                                                                        return Promise.reject(
                                                                                                            `每人日限领数量不能大于优惠券库存`,
                                                                                                        );
                                                                                                    }
                                                                                                }
                                                                                                return Promise.resolve();
                                                                                            },
                                                                                        }),
                                                                                    ]}
                                                                                >
                                                                                    <InputNumber
                                                                                        precision={
                                                                                            0
                                                                                        }
                                                                                        min={1}
                                                                                        disabled={
                                                                                            isLock
                                                                                        }
                                                                                    />
                                                                                </FormItem>
                                                                                <span
                                                                                    className={
                                                                                        styles.btnMargin
                                                                                    }
                                                                                >
                                                                                    组
                                                                                </span>
                                                                            </FormItem>
                                                                        )}
                                                                        {actSubType ===
                                                                            ACTSUBTYPES.POPUP &&
                                                                            configType == '01' && (
                                                                                <Fragment>
                                                                                    <FormItem
                                                                                        label="触发节点"
                                                                                        name={[
                                                                                            field.name,
                                                                                            'openType',
                                                                                        ]}
                                                                                        rules={[
                                                                                            {
                                                                                                required: true,
                                                                                                message:
                                                                                                    '请选择',
                                                                                            },
                                                                                        ]}
                                                                                        initialValue={[
                                                                                            '01',
                                                                                            '02',
                                                                                        ]}
                                                                                    >
                                                                                        <Checkbox.Group
                                                                                            disabled={
                                                                                                isLook
                                                                                            }
                                                                                        >
                                                                                            <Checkbox value="01">
                                                                                                每次打开客户端或小程序时
                                                                                            </Checkbox>
                                                                                            <Checkbox value="02">
                                                                                                注册或登录成功时
                                                                                            </Checkbox>
                                                                                        </Checkbox.Group>
                                                                                    </FormItem>
                                                                                    <FormItem
                                                                                        label="展示频率"
                                                                                        name={[
                                                                                            field.name,
                                                                                            'showTimesType',
                                                                                        ]}
                                                                                        initialValue={
                                                                                            '01'
                                                                                        }
                                                                                        rules={[
                                                                                            {
                                                                                                required:
                                                                                                    showTimesType ===
                                                                                                        '01' &&
                                                                                                    true,
                                                                                                message:
                                                                                                    '请选择',
                                                                                            },
                                                                                        ]}
                                                                                    >
                                                                                        <Radio.Group
                                                                                            disabled={
                                                                                                isLock
                                                                                            }
                                                                                        >
                                                                                            <Radio value="01">
                                                                                                <FormItem
                                                                                                    noStyle
                                                                                                    name={[
                                                                                                        field.name,
                                                                                                        'showTimes',
                                                                                                    ]}
                                                                                                    rules={[
                                                                                                        {
                                                                                                            required: true,
                                                                                                            message:
                                                                                                                '请选择',
                                                                                                        },
                                                                                                    ]}
                                                                                                >
                                                                                                    <InputNumber
                                                                                                        addonBefore="最多展示"
                                                                                                        addonAfter="次"
                                                                                                        precision={
                                                                                                            0
                                                                                                        }
                                                                                                        min={
                                                                                                            1
                                                                                                        }
                                                                                                        disabled={
                                                                                                            isLock ||
                                                                                                            showTimesType !==
                                                                                                                '01'
                                                                                                        }
                                                                                                    />
                                                                                                </FormItem>
                                                                                            </Radio>
                                                                                            <Radio value="02">
                                                                                                <Space className="mg-t-20">
                                                                                                    <FormItem
                                                                                                        noStyle
                                                                                                        name={[
                                                                                                            field.name,
                                                                                                            'perDayNum',
                                                                                                        ]}
                                                                                                        rules={[
                                                                                                            {
                                                                                                                required:
                                                                                                                    showTimesType ===
                                                                                                                        '02' &&
                                                                                                                    true,
                                                                                                                message:
                                                                                                                    '请选择',
                                                                                                            },
                                                                                                        ]}
                                                                                                    >
                                                                                                        <InputNumber
                                                                                                            addonBefore="每"
                                                                                                            addonAfter="天"
                                                                                                            precision={
                                                                                                                0
                                                                                                            }
                                                                                                            min={
                                                                                                                1
                                                                                                            }
                                                                                                            disabled={
                                                                                                                isLock ||
                                                                                                                showTimesType !==
                                                                                                                    '02'
                                                                                                            }
                                                                                                        />
                                                                                                    </FormItem>
                                                                                                    <FormItem
                                                                                                        noStyle
                                                                                                        name={[
                                                                                                            field.name,
                                                                                                            'perDayTimes',
                                                                                                        ]}
                                                                                                        rules={[
                                                                                                            {
                                                                                                                required:
                                                                                                                    showTimesType ===
                                                                                                                        '02' &&
                                                                                                                    true,
                                                                                                                message:
                                                                                                                    '请选择',
                                                                                                            },
                                                                                                        ]}
                                                                                                    >
                                                                                                        <InputNumber
                                                                                                            addonBefore="展示"
                                                                                                            addonAfter="次"
                                                                                                            precision={
                                                                                                                0
                                                                                                            }
                                                                                                            min={
                                                                                                                1
                                                                                                            }
                                                                                                            disabled={
                                                                                                                isLock ||
                                                                                                                showTimesType !==
                                                                                                                    '02'
                                                                                                            }
                                                                                                        />
                                                                                                    </FormItem>
                                                                                                </Space>
                                                                                            </Radio>
                                                                                        </Radio.Group>
                                                                                    </FormItem>
                                                                                </Fragment>
                                                                            )}
                                                                        {actSubType ===
                                                                            ACTSUBTYPES.DIRECT &&
                                                                            !isCopy && (
                                                                                <FormItem
                                                                                    name={[
                                                                                        field.name,
                                                                                        'mktActPushId',
                                                                                    ]}
                                                                                    noStyle
                                                                                />
                                                                            )}
                                                                        {actSubType ===
                                                                            ACTSUBTYPES.DIRECT && (
                                                                            <FormItem
                                                                                label="每日限领库存"
                                                                                required
                                                                            >
                                                                                <FormItem
                                                                                    name={[
                                                                                        field.name,
                                                                                        'actEveryDayGetLimitNum',
                                                                                    ]}
                                                                                    fieldKey={
                                                                                        field.fieldKey
                                                                                    }
                                                                                    noStyle
                                                                                    rules={[
                                                                                        {
                                                                                            required: true,
                                                                                            message:
                                                                                                '请填写每日限领库存',
                                                                                        },
                                                                                        (_) => ({
                                                                                            validator(
                                                                                                rule,
                                                                                                value,
                                                                                            ) {
                                                                                                const cpnList =
                                                                                                    listData[
                                                                                                        index
                                                                                                    ]
                                                                                                        ?.cpnList;
                                                                                                if (
                                                                                                    cpnList &&
                                                                                                    cpnList.length >
                                                                                                        0
                                                                                                ) {
                                                                                                    let minPutNum =
                                                                                                        undefined;
                                                                                                    for (const cpn of cpnList) {
                                                                                                        const putTimes =
                                                                                                            Number(
                                                                                                                cpn.unGetNum ||
                                                                                                                    cpn.cpnNum,
                                                                                                            ) /
                                                                                                            Number(
                                                                                                                cpn.putNum,
                                                                                                            );
                                                                                                        if (
                                                                                                            minPutNum ===
                                                                                                                undefined ||
                                                                                                            minPutNum >
                                                                                                                putTimes
                                                                                                        ) {
                                                                                                            minPutNum =
                                                                                                                putTimes;
                                                                                                        }
                                                                                                    }
                                                                                                    if (
                                                                                                        value &&
                                                                                                        Number(
                                                                                                            value,
                                                                                                        ) >
                                                                                                            minPutNum
                                                                                                    ) {
                                                                                                        return Promise.reject(
                                                                                                            `每日限领库存不能大于优惠券库存`,
                                                                                                        );
                                                                                                    }
                                                                                                }
                                                                                                return Promise.resolve();
                                                                                            },
                                                                                        }),
                                                                                    ]}
                                                                                >
                                                                                    <InputNumber
                                                                                        precision={
                                                                                            0
                                                                                        }
                                                                                        min={0}
                                                                                        max={
                                                                                            Number.MAX_SAFE_INTEGER
                                                                                        }
                                                                                        disabled={
                                                                                            !isCopy &&
                                                                                            (isEnd ||
                                                                                                isLook)
                                                                                        }
                                                                                    />
                                                                                </FormItem>
                                                                                <span
                                                                                    className={
                                                                                        styles.btnMargin
                                                                                    }
                                                                                >
                                                                                    组（0代表无限制）
                                                                                </span>
                                                                            </FormItem>
                                                                        )}
                                                                    </Fragment>
                                                                );
                                                            }}
                                                        </FormItem>
                                                    </div>

                                                    {(activityForm === '02' && (
                                                        <FormItem label="销售金额" required>
                                                            <div
                                                                style={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                }}
                                                            >
                                                                <FormItem
                                                                    name={[field.name, 'saleAmt']}
                                                                    fieldKey={field.fieldKey}
                                                                    noStyle
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请填写销售金额',
                                                                        },
                                                                        // (_) => ({
                                                                        //     validator(rule, value) {
                                                                        //         if (
                                                                        //             !isEmpty(
                                                                        //                 value,
                                                                        //             ) &&
                                                                        //             Number(value) <=
                                                                        //                 0
                                                                        //         ) {
                                                                        //             return Promise.reject(
                                                                        //                 '销售金额最低0.01元',
                                                                        //             );
                                                                        //         }
                                                                        //         return Promise.resolve();
                                                                        //     },
                                                                        // }),
                                                                    ]}
                                                                >
                                                                    <InputNumber
                                                                        precision={2}
                                                                        min={0.01}
                                                                        disabled={isLock}
                                                                    />
                                                                </FormItem>
                                                                <div
                                                                    style={{
                                                                        marginLeft: '10px',
                                                                    }}
                                                                >
                                                                    礼包购买金额
                                                                </div>
                                                            </div>
                                                        </FormItem>
                                                    )) ||
                                                        null}
                                                </Fragment>
                                            );
                                        })}
                                        <FormItem>
                                            {canIadd ? (
                                                <Button
                                                    disabled={!canIadd}
                                                    type="primary"
                                                    onClick={() => {
                                                        add({});
                                                    }}
                                                    style={{ width: '200px' }}
                                                >
                                                    <PlusOutlined />
                                                    新增用户
                                                </Button>
                                            ) : null}
                                        </FormItem>
                                    </Fragment>
                                );
                            }}
                        </FormItem>
                    );
                }}
            </Form.List>
        </Fragment>
    );
};

export default RuleLayout;
