import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { Input, Form, Tooltip, Radio } from 'antd';

import { InfoCircleOutlined } from '@ant-design/icons';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

const FormItem = Form.Item;

const OtherLayout = (props) => {
    const {
        dispatch,
        form,
        disabled,
        editActInfo,
        global: { codeInfo },
        formItemLayout,
        formItemFixedWidthLayout,
    } = props;

    const { marketChannelType: marketChannelTypeList } = codeInfo;

    const shareFlag = Form.useWatch('shareFlag', form);

    useEffect(() => {
        if (!marketChannelTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'marketChannelType',
            });
        }
    }, []);

    return (
        <Fragment>
            <FormItem
                name="shareFlag"
                initialValue={'0'}
                label={
                    <span>
                        是否分享
                        <Tooltip title="选择“是”则前端展示分享该活动的按钮">
                            <InfoCircleOutlined
                                style={{
                                    marginLeft: '6px',
                                }}
                            />
                        </Tooltip>
                    </span>
                }
                {...formItemLayout}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={'0'}>否</Radio>
                    <Radio value={'1'}>是</Radio>
                </Radio.Group>
            </FormItem>

            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    return (
                        (shareFlag === '1' && (
                            <Fragment>
                                {/* <FormItem
                                    name="buttonTitle"
                                    label={'分享按钮文案'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '填写分享按钮文案',
                                        },
                                    ]}
                                    {...formItemLayout}
                                >
                                    <Input
                                        maxLength={10}
                                        showCount
                                        placeholder="填写分享按钮文案"
                                        disabled={disabled}
                                    ></Input>
                                </FormItem> */}
                                <FormItem
                                    name="shareTitle"
                                    label={'分享标题'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '填写分享标题',
                                        },
                                    ]}
                                    {...formItemLayout}
                                >
                                    <Input
                                        maxLength={20}
                                        showCount
                                        placeholder="填写分享标题"
                                        disabled={disabled}
                                    ></Input>
                                </FormItem>

                                <FormItem
                                    name="wxShareIcon"
                                    label={
                                        <span>
                                            微信分享图片
                                            <Tooltip title="微信分享图片">
                                                <InfoCircleOutlined
                                                    style={{
                                                        marginLeft: '6px',
                                                    }}
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择微信分享图片',
                                        },
                                    ]}
                                    {...formItemLayout}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'wxShareIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 100,
                                            width: 750,
                                            height: 600,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>

                                <FormItem
                                    name="aliSqueak"
                                    label={'支付宝吱口令文案'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '填写支付宝吱口令文案',
                                        },
                                    ]}
                                    {...formItemLayout}
                                >
                                    <Input
                                        placeholder="填写支付宝吱口令文案"
                                        maxLength={14}
                                        showCount
                                        disabled={disabled}
                                    ></Input>
                                </FormItem>

                                <FormItem
                                    name="aliShareIcon"
                                    label={
                                        <span>
                                            支付宝分享图片
                                            <Tooltip title="支付宝分享图片">
                                                <InfoCircleOutlined
                                                    style={{
                                                        marginLeft: '6px',
                                                    }}
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择支付宝分享图片',
                                        },
                                    ]}
                                    {...formItemLayout}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'aliShareIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 100,
                                            width: 750,
                                            height: 825,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>
                            </Fragment>
                        )) ||
                        null
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

export default OtherLayout;
