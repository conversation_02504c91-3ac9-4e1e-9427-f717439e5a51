import React, { Fragment, useState, useEffect, useMemo, useRef, useImperativeHandle } from 'react';
import { Button, Col, Input, Select, Form, Row, Space } from 'antd';

import { exportTableByParams } from '@/utils/utils';
import { ACTSUBTYPES, EXCHANGESTATUS, APPLET_ACT_TYPES, ACT_VERSION_TYPES } from '@/config/declare';
import TablePro from '@/components/TablePro';
import commonStyles from '@/assets/styles/common.less';
import { MNG_BIL_URL } from '@/config/global';
import { exportExchangeListApi } from '@/services/Marketing/MarketingGiftApi2';

const { Option } = Select;

const FormItem = Form.Item;

const ExchangeLayout = (props) => {
    const {
        dispatch,
        listLoading,
        actId,
        actSubType,
        isLock,
        appletGiftModel: { exchangeList, exchangeTotal },
        appletGiftModel2: { exchangeList2, exchangeTotal2 },
        formItemLayout,
        formItemFixedWidthLayout,
        version,
        initRef,
    } = props;

    useImperativeHandle(initRef, () => ({
        searchEvent,
    }));

    const modelName = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return 'appletGiftModel2';
        }
        return 'appletGiftModel';
    }, [version]);

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return exchangeList2;
        }
        return exchangeList;
    }, [version, exchangeList, exchangeList2]);

    const total = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return exchangeTotal2;
        }
        return exchangeTotal;
    }, [version, exchangeTotal, exchangeTotal2]);

    const [exchangeForm] = Form.useForm();
    const searchEvent = (isDownload) => {
        const data = exchangeForm.getFieldsValue();
        const params = {
            ...data,
            actId,
            actSubType,
            pageIndex: isDownload ? undefined : pageNum,
            pageSize: isDownload ? undefined : pageSize,
        };

        if (isDownload) {
            const columnsStrs = [];
            for (const item of exchangeColumns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            if (version == ACT_VERSION_TYPES.V_2_0) {
                exportExchangeListApi(params);
            } else {
                exportTableByParams({
                    methodUrl: '/bil/act/put/exchange-list',
                    options: params,
                    columnsStr: columnsStrs,
                });
            }
        } else {
            dispatch({
                type: `${modelName}/getExchangeList`,
                options: params,
            });
        }
    };
    const resetSearchForm = () => {
        exchangeForm.resetFields();
        queryData();
    };

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    useEffect(() => {
        searchEvent();
    }, [pageNum, pageSize]);

    const queryData = () => {
        if (pageNum == 1) {
            searchEvent();
            return;
        }
        changePageNum(1);
    };

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
    };

    const exchangeColumns = [
        {
            title: '序号',
            width: 80,
            render(text, record, index) {
                return <span title={pageNum + index}>{(pageNum - 1) * pageSize + index + 1}</span>;
            },
        },
        {
            title: '兑换码',
            width: 140,
            dataIndex: version == ACT_VERSION_TYPES.V_2_0 ? 'cpnExchangeCode' : 'exchangeCode',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '领取人',
            width: 120,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '领取时间',
            width: 160,
            dataIndex: 'exchangeTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'exchangeStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <div className={commonStyles['form-title']}>兑换情况</div>
            <div style={{ width: '880px' }}>
                <Form
                    form={exchangeForm}
                    {...{
                        labelAlign: 'left',
                        wrapperCol: {
                            span: 16,
                        },
                    }}
                >
                    <Row>
                        <Col span={8}>
                            <FormItem label="手机号" name="mobile">
                                <Input />
                            </FormItem>
                        </Col>
                        <Col span={8}>
                            <FormItem label="兑换状态" name="exchangeStatus">
                                <Select placeholder="请选择" allowClear>
                                    <Option
                                        value={EXCHANGESTATUS.UNTAKE}
                                        key={EXCHANGESTATUS.UNTAKE}
                                    >
                                        未领取
                                    </Option>
                                    <Option value={EXCHANGESTATUS.TAKED} key={EXCHANGESTATUS.TAKED}>
                                        已领取
                                    </Option>
                                    <Option
                                        value={EXCHANGESTATUS.OUTDATE}
                                        key={EXCHANGESTATUS.OUTDATE}
                                    >
                                        已失效
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                        <Col>
                            <Space>
                                <Button type="primary" onClick={queryData}>
                                    查询
                                </Button>
                                <Button type="primary" onClick={() => searchEvent(true)}>
                                    导出
                                </Button>
                                <Button onClick={resetSearchForm}>重置</Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.cpnId}
                    dataSource={list}
                    onChange={changePageInfo}
                    pagination={{
                        current: pageNum,
                        total: total,
                        pageSize,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    columns={exchangeColumns}
                    noSort
                />
            </div>
        </Fragment>
    );
};

export default ExchangeLayout;
