import { Form, Select, Input, InputNumber, Space, Typography, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { isEmpty } from '@/utils/utils';

export const OTHER_ACT_CHANNEL = {
    // NONE: '', //不配置
    KWAI: 'kwai', //快手
};
const OtherChannelLayout = (props: { isLock?: boolean; isCopy?: boolean }) => {
    const { isLock, isCopy } = props;
    return (
        <>
            <Form.Item label="三方平台信息" required>
                <Space>
                    <Form.Item
                        noStyle
                        name="thirdPlatType"
                        // rules={[
                        //     {
                        //         required: true,
                        //         message: '请配置',
                        //     },
                        // ]}
                    >
                        <Select
                            placeholder="请选择"
                            disabled={isLock && !isCopy}
                            style={{ width: '200px' }}
                        >
                            {/* <Select.Option value={OTHER_ACT_CHANNEL.NONE}>不配置</Select.Option> */}
                            <Select.Option value={OTHER_ACT_CHANNEL.KWAI}>快手</Select.Option>
                        </Select>
                    </Form.Item>
                    <Tooltip title="对接三方平台涉及商品SKU时使用">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </Space>
            </Form.Item>

            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.thirdPlatType !== curValues.thirdPlatType
                }
            >
                {({ getFieldValue }) => {
                    const thirdPlatType = getFieldValue('thirdPlatType');
                    if (thirdPlatType == OTHER_ACT_CHANNEL.KWAI) {
                        return (
                            <>
                                <Form.Item label="快手商品SKU" name="thirdSkuId">
                                    <Input placeholder="请填写" autoComplete="off"></Input>
                                </Form.Item>
                                {/* <Form.Item
                                    label="每日库存"
                                    name="dayStore"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置');
                                                }
                                                if (value == 0) {
                                                    return Promise.reject(
                                                        '库存必须大于0或者-1不限制',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="请填写"
                                        min={-1}
                                        step={1}
                                        precision={0}
                                        addonAfter={
                                            <Space>
                                                份
                                                <Typography.Text type="secondary">
                                                    （-1代表不限购）
                                                </Typography.Text>
                                            </Space>
                                        }
                                    ></InputNumber>
                                </Form.Item>
                                <Form.Item
                                    label="单账号限购"
                                    name="accountLimitAmt"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置');
                                                }
                                                if (value == 0) {
                                                    return Promise.reject(
                                                        '限购必须大于0或者-1不限制',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="请填写"
                                        min={-1}
                                        step={1}
                                        precision={0}
                                        addonAfter={
                                            <Space>
                                                份
                                                <Typography.Text type="secondary">
                                                    （-1代表不限购）
                                                </Typography.Text>
                                            </Space>
                                        }
                                    ></InputNumber>
                                </Form.Item>
                                <Form.Item
                                    label="单日账号每日最多购买"
                                    name="accountDayLimitAmt"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置');
                                                }
                                                if (value == 0) {
                                                    return Promise.reject('必须大于0或者-1不限制');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="请填写"
                                        min={-1}
                                        step={1}
                                        precision={0}
                                        addonAfter={
                                            <Space>
                                                份
                                                <Typography.Text type="secondary">
                                                    （-1代表不限购）
                                                </Typography.Text>
                                            </Space>
                                        }
                                    ></InputNumber>
                                </Form.Item> */}
                            </>
                        );
                    }
                    return null;
                }}
            </Form.Item>
        </>
    );
};
export default OtherChannelLayout;
