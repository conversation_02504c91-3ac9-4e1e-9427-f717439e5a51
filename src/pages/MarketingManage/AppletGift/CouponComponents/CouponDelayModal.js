import { DatePicker, Form, Modal } from 'antd';
import moment from 'moment';
import { useImperativeHandle, useState } from 'react';

const { RangePicker } = DatePicker;

export const CouponDelayModal = (props) => {
    const { initRef, label = '活动', onSubmit } = props;
    const [form] = Form.useForm();

    const [visible, updateVisible] = useState(false);
    const [loading, updateLoading] = useState(false);
    const [time, updateTime] = useState([]);
    const onClose = () => {
        updateVisible(false);
    };

    useImperativeHandle(initRef, () => ({
        show: ({ startTime, endTime, ...rest }) => {
            updateVisible(true);
            updateTime([startTime, endTime]);
            form.setFieldsValue({ ...rest, dates: [startTime, endTime] });
        },
        onClose,
    }));

    const disabledDate = (current) => {
        return current && (current < time[1] || current < moment());
    };

    const onFinish = () => {
        form.validateFields().then((value) => {
            updateLoading(true);
            try {
                const params = { ...value };
                params.expTime = value?.dates?.[1]?.format('YYYY-MM-DD HH:mm:ss');
                delete params.dates;
                onSubmit?.(params).then(() => {
                    onClose();
                });
            } catch (error) {
            } finally {
                updateLoading(false);
            }
        });
    };

    return (
        <Modal
            title={`${label}延期`}
            destroyOnClose
            width={560}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            onOk={onFinish}
            okButtonProps={{ loading: loading }}
        >
            <Form form={form}>
                <Form.Item name="prizeId" noStyle />
                <Form.Item name="actId" noStyle />
                <Form.Item name="buyNo" noStyle />
                <Form.Item
                    label={`${label}有效期`}
                    name="dates"
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (!value) {
                                    return Promise.reject('请选择');
                                }
                                if (!value[0]) {
                                    return Promise.reject('请选择开始时间');
                                }
                                if (!value[1]) {
                                    return Promise.reject('请选择结束时间');
                                }
                                if (value[1] < moment()) {
                                    return Promise.reject('结束时间不可早于当前时间');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <RangePicker
                        format="YYYY-MM-DD HH:mm:ss"
                        disabled={[true, false]}
                        allowClear={false}
                        disabledDate={disabledDate}
                        showTime
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};
