import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { Button, Select, Form, Modal, Tooltip, message, Space, Radio, Input } from 'antd';

import { InfoCircleOutlined } from '@ant-design/icons';

import { ACTSUBTYPES, EXCHANGESTATUS, APPLET_ACT_TYPES } from '@/config/declare';
import { YSF_APPID } from '@/config/global';
import LinkTypeBar from '@/components/LinkTypeBar';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

import ActLinkItem from '@/components/ActLinkItem/index';
import TemplateLayout, { TemplatePreview } from '../TemplateLayout';
import SelectColor from '@/components/SelectColor/index';
import { isEmpty, createQrcodeCommon } from '@/utils/utils';

const FormItem = Form.Item;

const ActiveLayout = (props) => {
    const {
        dispatch,
        form,
        disabled,
        editActInfo,
        global: { codeInfo },
        refreshWXUrl,
        actId,
        formItemLayout,
        formItemFixedWidthLayout,
        version,
    } = props;

    const { marketChannelType: marketChannelTypeList } = codeInfo;

    const [marketChannel, changeMarketChannel] = useState();

    useEffect(() => {
        if (!marketChannelTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'marketChannelType',
            });
        }
    }, []);

    const marketChannelTypeOptions = useMemo(() => {
        if (marketChannelTypeList) {
            return marketChannelTypeList.map((ele) => {
                return (
                    <Select.Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Select.Option>
                );
            });
        }
        return [];
    }, [marketChannelTypeList]);

    const showScanEvent = async (qrcode, title) => {
        try {
            if (isEmpty(qrcode)) {
                message.error('生产二维码失败');
                return Promise.error('生产二维码失败');
            }
            const url = await createQrcodeCommon(qrcode);
            Modal.info({
                title: `${title}二维码`,
                icon: null,
                maskClosable: true,
                width: 370,
                content: <img width="300" height="300" src={url} />,
                footer: null,
                onOk() {},
                onCancel() {
                    console.log('Cancel');
                },
            });
        } catch (error) {
            console.log(3333, error);
            return Promise.reject('生产二维码失败');
        }
    };

    return (
        <Fragment>
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');

                    const actNo = getFieldValue('actNo');

                    const configType = getFieldValue('configType');

                    const adIconLinkUrl = getFieldValue('adIconLinkUrl');

                    const activityForm = getFieldValue('activityForm');

                    const directDisplay = getFieldValue('directDisplay');

                    const spsFlag = getFieldValue('spsFlag');

                    let basePath = `/pagesActive/gift/index?`;

                    if (actSubType === ACTSUBTYPES.DIRECT) {
                        basePath = `/pagesActive/gift/coupon?`;
                    }

                    const params = [`actGiftNo=${actNo}`];

                    if (marketChannel) {
                        params.push(`marketChannel=${marketChannel}`);
                    }

                    const paramsFormat = params.join('&');

                    const payPath = basePath + paramsFormat;

                    // const payPath = lastPath.replace(/\s+/g, '');

                    const xdtLink = payPath;

                    const ysfQrcodePath = `https://applet.open.95516.com/${YSF_APPID}?ysfPage=${encodeURIComponent(
                        payPath,
                    )}`;
                    const mergePath = `https://act.xdtev.com/gift-coupon?${paramsFormat}`;

                    if (actSubType == ACTSUBTYPES.POPUP) {
                        return configType === '02' ? (
                            <Fragment>
                                <FormItem
                                    label="弹窗图片"
                                    name="popIcon"
                                    rules={[{ required: true, message: '请选择弹窗图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'popIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 60,
                                            width: 600,
                                            height: 800,
                                            suggest: true,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>
                                <FormItem
                                    name="buttonIcon"
                                    label="按钮图片"
                                    rules={[{ required: true, message: '请选择按钮图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'buttonIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 60,
                                            width: 500,
                                            height: 100,
                                            suggest: true,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>
                            </Fragment>
                        ) : (
                            <Fragment>
                                <FormItem
                                    label="弹窗头图"
                                    name="popIcon"
                                    rules={[{ required: true, message: '请选择弹窗图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'popIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 60,
                                            width: 580,
                                            height: 245,
                                            suggest: true,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>

                                <FormItem
                                    name="buttonIcon"
                                    label="按钮图片"
                                    rules={[{ required: true, message: '请选择按钮图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'buttonIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 60,
                                            width: 580,
                                            height: 88,
                                            suggest: true,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem>
                                {/* <FormItem
                                    name="cancelIcon"
                                    label="取消按钮图片"
                                    rules={[{ required: true, message: '请选择按钮图片' }]}
                                >
                                    <UpLoadImgItem
                                        disabled={disabled}
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'cancelIcon',
                                            relaTable: 'e_mkt_act',
                                        }}
                                        sizeInfo={{
                                            size: 60,
                                            width: 500,
                                            height: 100,
                                            suggest: true,
                                        }}
                                    ></UpLoadImgItem>
                                </FormItem> */}
                                <FormItem
                                    label="按钮文案"
                                    name="buttonTitle"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请填写',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="请填写"
                                        autoComplete="off"
                                        showCount
                                        maxLength={10}
                                        disabled={disabled}
                                    ></Input>
                                </FormItem>
                                {/* <FormItem
                                    label="取消按钮文案"
                                    name="cancelTitle"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请填写',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="请填写"
                                        autoComplete="off"
                                        showCount
                                        maxLength={10}
                                        disabled={disabled}
                                    ></Input>
                                </FormItem> */}
                                <FormItem
                                    label="弹窗动效"
                                    name="popType"
                                    rules={[
                                        {
                                            required: true,
                                            message: '选择',
                                        },
                                    ]}
                                    initialValue={'0'}
                                >
                                    <Radio.Group disabled={disabled}>
                                        <Radio value="0">无动效</Radio>
                                        <Radio value={'1'}>撒花</Radio>
                                    </Radio.Group>
                                </FormItem>
                                <FormItem
                                    label="是否展示悬浮按钮"
                                    name="spsFlag"
                                    rules={[
                                        {
                                            required: true,
                                            message: '选择',
                                        },
                                    ]}
                                    initialValue={'1'}
                                >
                                    <Radio.Group disabled={disabled}>
                                        <Radio value="1">是</Radio>
                                        <Radio value="0">否</Radio>
                                    </Radio.Group>
                                </FormItem>
                                {spsFlag === '1' && (
                                    <FormItem
                                        name="spsImg"
                                        label="悬浮按钮图片"
                                        rules={[{ required: true, message: '请配置按钮图片' }]}
                                    >
                                        <UpLoadImgItem
                                            disabled={disabled}
                                            uploadData={{
                                                contentType: '02',
                                                contRemrk: 'spsImg',
                                                relaTable: 'e_mkt_act',
                                            }}
                                            sizeInfo={{
                                                size: 60,
                                                width: 130,
                                                height: 120,
                                                suggest: true,
                                            }}
                                        ></UpLoadImgItem>
                                    </FormItem>
                                )}
                            </Fragment>
                        );
                    } else {
                        return (
                            <Fragment>
                                <FormItem
                                    name="directDisplay"
                                    initialValue={'0'}
                                    label={
                                        <span>
                                            是否直接展示
                                            <Tooltip title="选择“是”则前端不显示背景图直接展示优惠券信息">
                                                <InfoCircleOutlined
                                                    style={{
                                                        marginLeft: '6px',
                                                    }}
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    required
                                >
                                    <Radio.Group disabled={disabled || activityForm == '02'}>
                                        <Radio value={'0'}>否</Radio>
                                        <Radio value={'1'}>是</Radio>
                                    </Radio.Group>
                                </FormItem>

                                <TemplateLayout
                                    {...(editActInfo || {})}
                                    disabled={disabled}
                                    form={form}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    formItemLayout={formItemLayout}
                                    version={version}
                                />

                                {actSubType == ACTSUBTYPES.DIRECT ? (
                                    <Fragment>
                                        <FormItem
                                            name="adIconLinkUrl"
                                            label={
                                                <span>
                                                    广告图片
                                                    <Tooltip title="领取后活动页面的广告位">
                                                        <InfoCircleOutlined
                                                            style={{
                                                                marginLeft: '6px',
                                                            }}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            }
                                        >
                                            <UpLoadImgItem
                                                disabled={disabled}
                                                uploadData={{
                                                    contentType: '02',
                                                    contRemrk: 'adIconLinkUrl',
                                                    relaTable: 'e_mkt_act',
                                                }}
                                                sizeInfo={{
                                                    size: 100,
                                                    width: 690,
                                                    height: 164,
                                                }}
                                            ></UpLoadImgItem>
                                        </FormItem>

                                        {adIconLinkUrl ? (
                                            <LinkTypeBar
                                                label={
                                                    <span>
                                                        跳转类型
                                                        <Tooltip title="配置了图片，就必须配置跳转类型，否则不予提交">
                                                            <InfoCircleOutlined
                                                                style={{
                                                                    marginLeft: '6px',
                                                                }}
                                                            />
                                                        </Tooltip>
                                                    </span>
                                                }
                                                disabled={disabled}
                                                required
                                            />
                                        ) : null}
                                    </Fragment>
                                ) : null}
                                <Fragment>
                                    <ActLinkItem
                                        actType={APPLET_ACT_TYPES.GIFT}
                                        actId={actId}
                                        path={xdtLink}
                                        mergeLabel={'支付宝&微信外部投放'}
                                        mergePath={mergePath}
                                        wrapperCol={{ span: 20 }}
                                    ></ActLinkItem>
                                    {/* <FormItem label="支付宝&微信聚合码">
                                        <Space className="pd-r-20">
                                            {mergePath}
                                            <Button
                                                onClick={() => {
                                                    showScanEvent(
                                                        mergePath,
                                                        '支付宝&微信聚合码链接',
                                                    );
                                                }}
                                            >
                                                二维码
                                            </Button>
                                        </Space>
                                    </FormItem> */}

                                    <FormItem label="云闪付领取链接">
                                        <Space className="pd-r-20">
                                            {ysfQrcodePath}
                                            <Button
                                                onClick={() => {
                                                    showScanEvent(ysfQrcodePath, '云闪付领取链接');
                                                }}
                                            >
                                                二维码
                                            </Button>
                                        </Space>
                                    </FormItem>
                                </Fragment>
                            </Fragment>
                        );
                    }
                }}
            </FormItem>
        </Fragment>
    );
};

export default ActiveLayout;
