import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Radio,
    InputNumber,
    Result,
    Upload,
    message,
    Tooltip,
    Typography,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useImperativeHandle, useMemo } from 'react';
import {
    stopMiniGiftApi,
    deleteMiniGiftApi,
    qryPutCustInfoApi,
    importUserListApi,
    putGroupCouponApi,
    putMobilesCouponApi,
    deleteActUserApi,
    getAddGiftInfoApi,
    downloadUserListFailExportApi,
} from '@/services/Marketing/MarketingGiftApi';
import { ACTSUBTYPES, ACT_VERSION_TYPES, APPLY_TYPE } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { ExclamationCircleOutlined, PlusOutlined, RedoOutlined } from '@ant-design/icons';
import styles from './AppletGift.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import CitysSelect from '@/components/CitysSelect/index.js';
import commonStyles from '@/assets/styles/common.less';

import ImportModal from '@/components/ImportModal';
import {
    delayGiftApi,
    deleteActUserApi2,
    deleteMiniGiftApi2,
    downloadUserListFailExportApi2,
    exportGiftListApi,
    importUserIdListApi,
    importUserListApi2,
    putCouponApi,
    qryPutCustInfoApi2,
    stopMiniGiftApi2,
} from '@/services/Marketing/MarketingGiftApi2';
import TargetUserSelect, { CROWD_TYPES } from '@/components/TargetUserSelect';
import SelectCouponItem, {
    COUPON_CUSTOM_PAGE_TYPE,
} from '@/newComponents/SelectPrizeItem/SelectCouponItem';
import { previewPrizeTaskApi, savePrizeTaskApi } from '@/services/UserManage/UserApi';
import { CouponDelayModal } from './CouponComponents/CouponDelayModal';
import moment from 'moment';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    ALL: '00',
    NOSTART: '1', // 未开始
    DOING: '2', // 进行中
    END: '3', // 已结束
    STOP: '4', // 已停止
    DRAFT: '0', // 草稿
};

const PUT_TARGET = {
    GROUP: '01', // 按人群发放
    USER_MOBILE: '02', // 指定用户手机号发放
    USER_ID: '03', // 指定用户id发放
};
// 回流状态
const REFLUX_STATUS = {
    NOSTART: '00', // 未回流
    DOING: '01', // 回流中
    END: '02', // 已回流
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'right',
};

export const SendGiftDetailFormItem = (props) => {
    const { dispatch, global, form, needTitle = false } = props;
    return (
        <Fragment>
            <FormItem noStyle name="temp_type" />
            <FormItem noStyle name="taskName" />
            <FormItem noStyle name="temp_cdp_list" />
            <FormItem noStyle name="sendCustType" />
            <FormItem noStyle name="singlePutNum" />
            <FormItem noStyle name="detPrizeList" />
            {/* 详情才有的列表值 */}
            {/* 指定用户列表 */}
            <FormItem noStyle name="sendCustList" />
            {/* 人群计算 */}
            <FormItem noStyle name="calcLabelList" />
            {/* cdp人群信息 */}
            <FormItem noStyle name="cdpList" />
            {/* 新电途人群信息 */}
            <FormItem noStyle name="custLabelList" />
            {/* cdp实体人群信息 */}
            <FormItem noStyle name="entityLabelList" />
            {/* cdp画像信息 */}
            <FormItem noStyle name="labelList" />
            <FormItem noStyle name="custNum" />
            <FormItem noStyle name="prizeNum" />
            <FormItem noStyle name="refluxFlag" />
            <FormItem noStyle name="sendStatus" />

            <FormItem noStyle shouldUpdate>
                {() => {
                    const values = form.getFieldsValue();
                    const {
                        temp_type,
                        sendCustType,
                        temp_cdp_list,
                        singlePutNum,
                        detPrizeList,
                        sendCustList,
                        calcLabelList,
                        cdpList,
                        custLabelList,
                        entityLabelList,
                        labelList,
                    } = values;
                    const putTragetMap = {
                        [PUT_TARGET.GROUP]: '按人群发放',
                        [PUT_TARGET.USER_ID]: '指定用户id发放',
                        [PUT_TARGET.USER_MOBILE]: '指定用户手机号发放',
                    };

                    let idKey = 'cdpCrowdId';
                    let nameKey = 'cdpCrowdName';

                    let sendGroupType = temp_type;
                    let sendTargetType = sendCustType || PUT_TARGET.GROUP;
                    let cdpItems = temp_cdp_list;
                    if (calcLabelList?.length) {
                        sendGroupType = CROWD_TYPES.XDT_CALCULATE;
                        cdpItems = calcLabelList;
                    } else if (cdpList?.length) {
                        sendGroupType = CROWD_TYPES.CDP_USER;
                        cdpItems = cdpList;
                    } else if (custLabelList?.length) {
                        sendGroupType = CROWD_TYPES.XDT_USER;
                        cdpItems = custLabelList;

                        idKey = undefined;
                        nameKey = 'custLabelName';
                    } else if (entityLabelList?.length) {
                        sendGroupType = CROWD_TYPES.CDP_REAL;
                        cdpItems = entityLabelList;
                    } else if (!sendCustType && sendCustList?.length) {
                        sendTargetType = PUT_TARGET.USER_MOBILE;
                    }

                    return (
                        <Fragment>
                            {(needTitle && (
                                <h1 className={commonStyles['form-title']}>目标用户</h1>
                            )) ||
                                undefined}
                            <FormItem label="发放目标用户">
                                <span style={{ marginRight: '6px' }}>
                                    {putTragetMap[sendTargetType]}
                                </span>
                                {sendTargetType == PUT_TARGET.GROUP && cdpItems?.length
                                    ? (idKey &&
                                          cdpItems.map((ele, index) => {
                                              const eleId = ele?.[idKey];
                                              let detailPath = `/userCenter/crowd-manage/look/${eleId}/${
                                                  ele.labelType || sendGroupType
                                              }`;
                                              if (
                                                  ele.labelType == '08' ||
                                                  sendGroupType == CROWD_TYPES.XDT_CALCULATE
                                              ) {
                                                  // 人群计算先跳到自己的详情页
                                                  detailPath = `/userCenter/crowd-manage/calculate/detail/${eleId}`;
                                              }
                                              return (
                                                  <span key={index}>
                                                      <Link to={detailPath} target="_blank">
                                                          {`${ele?.[idKey]} | ${ele?.[nameKey]}`}
                                                      </Link>
                                                      {index == cdpItems.length - 1 ? '' : '、'}
                                                  </span>
                                              );
                                          })) || (
                                          <div>
                                              {cdpItems.map((ele, index) => (
                                                  <span key={index}>
                                                      {ele?.[nameKey]}
                                                      {index == cdpItems.length - 1 ? '' : '、'}
                                                  </span>
                                              ))}
                                          </div>
                                      )
                                    : undefined}
                            </FormItem>
                            {((sendTargetType == PUT_TARGET.USER_MOBILE ||
                                sendCustType === PUT_TARGET.USER_ID) &&
                                sendCustList?.length && (
                                    <FormItem label=" " colon={false}>
                                        <TablePro
                                            name="user-list"
                                            scroll={{ x: 'max-content' }}
                                            rowKey={(record) => record.custId}
                                            dataSource={sendCustList}
                                            columns={[
                                                {
                                                    title: '用户ID',
                                                    width: 120,
                                                    dataIndex: 'custId',
                                                    render(text, record) {
                                                        return <span title={text}>{text}</span>;
                                                    },
                                                },
                                                {
                                                    title: '用户手机号',
                                                    width: 140,
                                                    dataIndex: 'mobile',
                                                    render(text, record) {
                                                        return <span title={text}>{text}</span>;
                                                    },
                                                },
                                            ]}
                                            noSort
                                            style={{ marginTop: '10px' }}
                                        />
                                    </FormItem>
                                )) ||
                                undefined}

                            <FormItem label="发放奖品">
                                <SelectCouponItem
                                    global={global}
                                    dispatch={dispatch}
                                    pageType={COUPON_CUSTOM_PAGE_TYPE.SEND_GIFT}
                                    form={form}
                                    value={detPrizeList}
                                    disabled
                                />
                            </FormItem>

                            {(needTitle && (
                                <h1 className={commonStyles['form-title']}>发放配置</h1>
                            )) ||
                                undefined}
                            <FormItem label="奖品发放数量">
                                <Space>
                                    <span>每人发放</span>
                                    <span style={{ color: 'red' }}>{singlePutNum}</span>
                                    <span>份</span>
                                </Space>
                            </FormItem>
                            <FormItem noStyle shouldUpdate>
                                {() => {
                                    const values = form.getFieldsValue();
                                    const { custNum, prizeNum, refluxFlag, sendStatus } = values;
                                    return (
                                        sendStatus === '05' && (
                                            <Fragment>
                                                <FormItem
                                                    label={
                                                        <div style={{ color: 'red' }}>预估人数</div>
                                                    }
                                                    colon={false}
                                                >
                                                    {refluxFlag === '1' ||
                                                    sendCustType === PUT_TARGET.USER_MOBILE ||
                                                    sendCustType === PUT_TARGET.USER_ID ? (
                                                        <span style={{ color: 'red' }}>
                                                            {custNum}
                                                        </span>
                                                    ) : (
                                                        <span style={{ color: 'red' }}>
                                                            正在回流数据......
                                                        </span>
                                                    )}
                                                </FormItem>

                                                <FormItem
                                                    label={
                                                        <div style={{ color: 'red' }}>
                                                            预估奖品发放数
                                                        </div>
                                                    }
                                                    colon={false}
                                                >
                                                    {refluxFlag === '1' ||
                                                    sendCustType === PUT_TARGET.USER_MOBILE ||
                                                    sendCustType === PUT_TARGET.USER_ID ? (
                                                        <span style={{ color: 'red' }}>
                                                            {prizeNum}
                                                        </span>
                                                    ) : (
                                                        <span style={{ color: 'red' }}>
                                                            正在回流数据......
                                                        </span>
                                                    )}
                                                </FormItem>
                                            </Fragment>
                                        )
                                    );
                                }}
                            </FormItem>
                        </Fragment>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};

const SendGiftModal = (props) => {
    const {
        dispatch,
        sendRef,
        listLoading,
        appletGiftModel: { actUserTotal, actUserList },
        appletGiftModel2: { actUserTotal2, actUserList2 },
        version,
        onFinish,
    } = props;

    const [isShow, updateShow] = useState(false);
    const [step, updateStep] = useState(0);
    const [isWaiting, updateWaiting] = useState(false);
    const [editRecord, updateEditRecord] = useState(undefined);

    const modelName = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return 'appletGiftModel2';
        }
        return 'appletGiftModel';
    }, [version]);

    const userList = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return actUserList2;
        }
        return actUserList;
    }, [version, actUserList, actUserList2]);

    const userTotal = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return actUserTotal2;
        }
        return actUserTotal;
    }, [version, actUserTotal, actUserTotal2]);

    useImperativeHandle(sendRef, () => ({
        show: (record) => {
            updateStep(0);
            updateEditRecord(record);
            updateActPutId(undefined);

            dispatch({
                type: `${modelName}/updateState`,
                data: { actUserTotal: 0, actUserList: [], actUserTotal2: 0, actUserList2: [] },
            });
            form.resetFields();
            form.setFieldsValue({ sendCustType: PUT_TARGET.GROUP });
            if (version == ACT_VERSION_TYPES.V_1_0) {
                dispatch({
                    type: `${modelName}/getCustLoyalTypeList`,
                    options: {},
                });
            }

            updateShow(true);
        },
    }));

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const [actPutId, updateActPutId] = useState(undefined);
    useEffect(() => {
        if (actPutId) {
            loadUserList();
        }
    }, [pageNum, pageSize, actPutId]);

    const [form] = Form.useForm();
    const sendCustType = Form.useWatch('sendCustType', form);

    const userColumns = [
        {
            title: '序号',
            width: 80,
            render(text, record, index) {
                return <span title={pageNum + index}>{(pageNum - 1) * pageSize + index + 1}</span>;
            },
        },
        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户名称',
            width: 120,
            dataIndex: 'custName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户类型',
            width: 160,
            dataIndex: 'custTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 160,
            render(text, record) {
                return (
                    <span
                        className={styles['table-btn']}
                        onClick={async () => {
                            confirm({
                                title: `确定删除用户${record.custName || ''}?`,
                                icon: <ExclamationCircleOutlined />,
                                content: '',
                                okText: '是',
                                okType: 'danger',
                                cancelText: '否',
                                onOk: async () => {
                                    try {
                                        if (version == ACT_VERSION_TYPES.V_2_0) {
                                            await deleteActUserApi2({
                                                ...record,
                                            });
                                        } else {
                                            await deleteActUserApi({
                                                ...record,
                                            });
                                        }
                                        if (actPutId) {
                                            loadUserList();
                                        }
                                    } catch (error) {}
                                },
                                onCancel() {
                                    console.log('Cancel');
                                },
                            });
                        }}
                    >
                        删除
                    </span>
                );
            },
        },
    ];

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
    };

    const loadUserList = () => {
        let params = {
            pageSize,
            pageIndex: pageNum,
            actPutId,
        };
        // if (version == ACT_VERSION_TYPES.V_2_0) {
        //     const formData = new FormData();
        //     formData.append('actId', editRecord?.actId);
        //     for (const key in params) {
        //         formData.append(key, params[key]);
        //     }
        //     params = formData;
        // }

        dispatch({
            type: `${modelName}/getActUserList`,
            options: params,
        }).then(() => {
            setTimeout(() => {
                form.validateFields(['crowd_list']);
            }, 200);
        });
    };

    const addSingleUserEvent = async () => {
        const data = form.getFieldsValue() || {};
        const { mobile, custId } = data;
        try {
            const {
                data: { actPutId: id },
            } = await (version == ACT_VERSION_TYPES.V_2_0
                ? qryPutCustInfoApi2({ mobile, custId, actPutId, actId: editRecord?.actId })
                : qryPutCustInfoApi({ mobile, custId, actPutId, actId: editRecord?.actId }));
            if (actPutId) {
                loadUserList();
            } else {
                updateActPutId(id);
            }
            form.setFieldsValue({ mobile: undefined, custId: undefined });
            form.validateFields(['crowd_list']);
        } catch (e) {
            console.log(e);
        }
    };

    // 是否发放，否的话调预览
    const uploadData = (isSend = true) => {
        form.validateFields()
            .then(async (values) => {
                updateWaiting(true);
                try {
                    const params = {
                        ...values,
                        actId: editRecord.actId,
                    };
                    delete params.mobile;
                    delete params.calcLabelList;
                    delete params.cdpList;
                    delete params.custLabelList;
                    delete params.entityLabelList;

                    if (values.sendCustType == PUT_TARGET.GROUP) {
                        if (params.temp_type == CROWD_TYPES.XDT_CALCULATE) {
                            params.calcLabelList = params.temp_cdp_list.map((ele) => ({
                                cdpCrowdId: ele.crowdId,
                                cdpCrowdName: ele.crowdName,
                                labelRemark: ele.labelRemark,
                                labelType: ele.crowdType,
                            }));
                        } else if (params.temp_type == CROWD_TYPES.CDP_REAL) {
                            params.entityLabelList = params.temp_cdp_list.map((ele) => ({
                                cdpCrowdId: ele.crowdId,
                                cdpCrowdName: ele.crowdName,
                                labelRemark: ele.labelRemark,
                                ruleId: ele.ruleId,
                                linkType: 'station_id',
                                linkTypeValue: ele.linkTypeValue,
                                labelType: ele.crowdType,
                            }));
                        } else if (params.temp_type == CROWD_TYPES.CDP_USER) {
                            params.cdpList = params.temp_cdp_list.map((ele) => ({
                                cdpCrowdId: ele.cdpCrowdId,
                                cdpCrowdName: ele.cdpCrowdName,
                                labelRemark: ele.labelRemark,
                                labelType: '01',
                            }));
                        } else if (params.temp_type == CROWD_TYPES.XDT_USER) {
                            params.custLabelList = params.temp_cdp_list.map((ele) => ({
                                custLabel: ele.crowdPortraitId,
                                custLabelName: ele.crowdPortraitName,
                            }));
                        }
                    } else {
                        params.actPutId = actPutId;
                    }

                    delete params.temp_type;
                    delete params.temp_cdp_list;

                    if (isSend) {
                        await savePrizeTaskApi(params);
                        if (values?.refluxFlag === '1') {
                            message.success('发放成功');
                        } else {
                            message.success('操作成功');
                        }

                        updateShow(false);
                        onFinish?.();
                    } else {
                        const { data } = await previewPrizeTaskApi(params);
                        form.setFieldsValue({
                            calcLabelList: undefined,
                            cdpList: undefined,
                            custLabelList: undefined,
                            entityLabelList: undefined,
                            ...params,
                            ...data,
                        });
                        updateStep(1);
                    }
                } catch (error) {
                } finally {
                    updateWaiting(false);
                }
            })
            .catch((e) => {});
    };

    const importModalRef = useRef();

    const activeCrowdFlag = Form.useWatch('activeCrowdFlag', form);
    useEffect(() => {
        if (activeCrowdFlag !== undefined) {
            dispatch({
                type: `${modelName}/getCustLoyalTypeList`,
                options: { activeCrowdFlag },
            });
        }
    }, [activeCrowdFlag]);

    if (step == 1) {
        const refluxFlag = form.getFieldValue('refluxFlag');
        const custNum = form.getFieldValue('custNum');
        const sendCustType = form.getFieldValue('sendCustType');
        return (
            <Modal
                title={
                    <div>
                        预览
                        <Button
                            type="text"
                            onClick={() => {
                                uploadData(false);
                            }}
                            // style={{ marginLeft: '12px' }}
                        >
                            <RedoOutlined />
                        </Button>
                    </div>
                }
                destroyOnClose
                width={800}
                visible={isShow}
                okText={
                    refluxFlag === '1' ||
                    sendCustType === PUT_TARGET.USER_MOBILE ||
                    sendCustType === PUT_TARGET.USER_ID
                        ? `发放${custNum === 0 ? '(无目标推送用户)' : ''}`
                        : '暂存'
                }
                cancelText="上一步"
                onOk={() => {
                    uploadData(true);
                }}
                okButtonProps={{
                    loading: isWaiting,
                    disabled: refluxFlag === '1' && custNum === 0,
                }}
                cancelButtonProps={{ onClick: () => updateStep(0) }}
                onCancel={() => updateShow(false)}
                maskClosable={false}
            >
                <Form {...formItemLayout} form={form} wrapperCol={{ span: 24 }} labelAlign="left">
                    <FormItem noStyle name="custNum" />
                    <FormItem noStyle name="prizeNum" />
                    <FormItem noStyle name="refluxFlag" />
                    <SendGiftDetailFormItem {...props} form={form} />

                    <FormItem noStyle shouldUpdate>
                        {() => {
                            const values = form.getFieldsValue();
                            const { custNum, prizeNum, sendCustType } = values;
                            return (
                                <Fragment>
                                    <FormItem
                                        label={<div style={{ color: 'red' }}>预估人数</div>}
                                        colon={false}
                                    >
                                        {refluxFlag === '1' ||
                                        sendCustType === PUT_TARGET.USER_MOBILE ||
                                        sendCustType === PUT_TARGET.USER_ID ? (
                                            <span style={{ color: 'red' }}>{custNum}</span>
                                        ) : (
                                            <span style={{ color: 'red' }}>
                                                正在回流数据......
                                                <Typography.Link
                                                    style={{ marginLeft: '20px' }}
                                                    onClick={() => {
                                                        uploadData(false);
                                                    }}
                                                >
                                                    刷新
                                                </Typography.Link>
                                            </span>
                                        )}
                                    </FormItem>

                                    <FormItem
                                        label={<div style={{ color: 'red' }}>预估奖品发放数</div>}
                                        colon={false}
                                    >
                                        {refluxFlag === '1' ||
                                        sendCustType === PUT_TARGET.USER_MOBILE ||
                                        sendCustType === PUT_TARGET.USER_ID ? (
                                            <span style={{ color: 'red' }}>{prizeNum}</span>
                                        ) : (
                                            <span style={{ color: 'red' }}>正在回流数据......</span>
                                        )}
                                    </FormItem>
                                </Fragment>
                            );
                        }}
                    </FormItem>
                </Form>
            </Modal>
        );
    }

    return (
        <Modal
            title="直接发放"
            destroyOnClose
            width={800}
            visible={isShow}
            okText="下一步"
            onOk={() => {
                uploadData(false);
            }}
            okButtonProps={{ loading: isWaiting }}
            onCancel={() => updateShow(false)}
            maskClosable={false}
        >
            <Form form={form} {...formItemLayout} wrapperCol={{ span: 12 }}>
                <h1 className={commonStyles['form-title']}>基本信息</h1>
                <FormItem name="taskName" label="任务名称" rules={[{ required: true }]}>
                    <Input placeholder="请输入" maxLength={20} autoComplete="off" />
                </FormItem>

                <h1 className={commonStyles['form-title']}>目标用户</h1>
                <FormItem
                    label="发放目标用户"
                    name="sendCustType"
                    required
                    wrapperCol={{ span: 16 }}
                >
                    <Radio.Group
                        onChange={() => {
                            updateActPutId(undefined);
                            dispatch({
                                type: `${modelName}/updateState`,
                                data: {
                                    actUserTotal: 0,
                                    actUserList: [],
                                    actUserTotal2: 0,
                                    actUserList2: [],
                                },
                            });
                            form.setFieldsValue({
                                mobile: undefined,
                                custId: undefined,
                            });
                        }}
                    >
                        <Radio value={PUT_TARGET.GROUP}>按人群发放</Radio>
                        <Radio value={PUT_TARGET.USER_ID}>指定用户id发放</Radio>
                        <Radio value={PUT_TARGET.USER_MOBILE}>指定用户手机号发放</Radio>
                    </Radio.Group>
                </FormItem>

                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.sendCustType !== curValues.sendCustType ||
                        prevValues.activeCrowd !== curValues.activeCrowd ||
                        prevValues.activeCrowdFlag !== curValues.activeCrowdFlag ||
                        prevValues.custId !== curValues.custId ||
                        prevValues.mobile !== curValues.mobile
                    }
                >
                    {({ getFieldValue }) => {
                        const sendCustType = getFieldValue('sendCustType');
                        if (sendCustType == PUT_TARGET.GROUP) {
                            return <TargetUserSelect applyType={APPLY_TYPE.APPLETGIFT} />;
                        }
                        const inputValue = getFieldValue(
                            sendCustType == PUT_TARGET.USER_MOBILE ? 'mobile' : 'custId',
                        );

                        return (
                            <Fragment>
                                <Form.Item
                                    name="crowd_list"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (userList?.length == 0) {
                                                    return Promise.reject('请添加用户');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Space>
                                        <FormItem
                                            noStyle
                                            name={
                                                sendCustType == PUT_TARGET.USER_MOBILE
                                                    ? 'mobile'
                                                    : 'custId'
                                            }
                                        >
                                            <Input
                                                placeholder={`请输入用户${
                                                    sendCustType == PUT_TARGET.USER_MOBILE
                                                        ? '手机号'
                                                        : 'id'
                                                }`}
                                                style={{ width: '100%' }}
                                                autoComplete="off"
                                            />
                                        </FormItem>
                                        <Button
                                            type="primary"
                                            onClick={addSingleUserEvent}
                                            disabled={!inputValue || inputValue.length == 0}
                                        >
                                            添加
                                        </Button>
                                        <Button
                                            type="link"
                                            onClick={() => importModalRef.current.show()}
                                        >
                                            <PlusOutlined />
                                            导入用户
                                        </Button>
                                    </Space>
                                </Form.Item>

                                {(userList?.length && (
                                    <TablePro
                                        name="user-list"
                                        loading={listLoading}
                                        scroll={{ x: 'max-content' }}
                                        rowKey={(record) => record.cpnId}
                                        dataSource={userList}
                                        onChange={changePageInfo}
                                        pagination={{
                                            current: pageNum,
                                            total: userTotal,
                                            pageSize,
                                            showSizeChanger: true,
                                            showQuickJumper: true,
                                            showTotal: (total) => `共 ${total} 条`,
                                        }}
                                        columns={userColumns}
                                        noSort
                                        style={{ marginTop: '10px' }}
                                    />
                                )) ||
                                    null}
                            </Fragment>
                        );
                    }}
                </FormItem>

                <h1 className={commonStyles['form-title']}>发放配置</h1>
                <FormItem
                    label="奖品发放数量"
                    name="singlePutNum"
                    rules={[
                        { required: true },
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                // if (!value) {
                                //     return Promise.reject('请输入1-99的整数');
                                // }
                                if (value < 1 || value > 99) {
                                    return Promise.reject('请输入1-99的整数');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    每人发放
                    <FormItem noStyle name="singlePutNum">
                        <InputNumber
                            style={{ width: '100px', margin: '0 12px' }}
                            placeholder="1-99的整数"
                            // value={singlePutNum}
                            precision={0}
                        />
                    </FormItem>
                    份
                </FormItem>
            </Form>

            <ImportModal
                title={`批量导入用户`}
                downLoadPath={`/aliMini/xdt/static/excel/用户导入${
                    sendCustType == PUT_TARGET.USER_MOBILE ? '' : 'id'
                }模板.xlsx`}
                surfix={['.xlsx']}
                onUpload={async (formData, callback) => {
                    actPutId && formData.append('actPutId', actPutId);
                    formData.append('actId', editRecord.actId);
                    const {
                        ret,
                        data: { putFailExportKey, msg, actPutId: id },
                    } = await (version == ACT_VERSION_TYPES.V_2_0
                        ? (sendCustType == PUT_TARGET.USER_MOBILE &&
                              importUserListApi2(formData)) ||
                          importUserIdListApi(formData)
                        : importUserListApi(formData));

                    if (actPutId) {
                        loadUserList();
                    } else updateActPutId(id);
                    callback &&
                        callback({
                            ret: putFailExportKey ? 'false' : 'success',
                            importTime: putFailExportKey,
                            msg,
                        });
                }}
                onDoanLoadError={(putFailExportKey) => {
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        downloadUserListFailExportApi2(putFailExportKey);
                    } else {
                        downloadUserListFailExportApi(putFailExportKey);
                    }
                }}
                ref={importModalRef}
            />
        </Modal>
    );
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, dispatch } = props;

    const [actTypeList, changeActTypeList] = useState([]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    useEffect(() => {
        initAddInfo();
    }, []);

    // 获取actNo
    const initAddInfo = async () => {
        try {
            // 不区分优惠券1.0/2.0，后端开发说就用旧的
            const {
                data: { actNo, actTypeList },
            } = await getAddGiftInfoApi();
            changeActTypeList(actTypeList || []);

            return actNo;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const actTypeOptions = useMemo(
        () =>
            actTypeList?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || [],
        [actTypeList],
    );

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [null, null],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="活动名称:" name="actName">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动编号:" name="actNo">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动类型:" name="actSubType">
                        <Select
                            placeholder="请选择"
                            allowClear
                            onChange={() => {
                                form.setFieldsValue({ cityList: undefined });
                            }}
                        >
                            {actTypeOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动日期:" name="dates">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        return (
                            (actSubType == ACTSUBTYPES.POPUP && (
                                <Col span={8}>
                                    <CitysSelect
                                        label="适用区域"
                                        name="cityList"
                                        placeholder="请选择"
                                        formItemLayout={{ labelAlign: 'right' }}
                                        showArrow
                                        provinceSelectable
                                        provinceFilterEmpty
                                        rules={null}
                                    />
                                </Col>
                            )) || <FormItem name="cityList" noStyle />
                        );
                    }}
                </FormItem>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const AppletGiftListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        appletGiftModel: { appletGiftList, appletGiftListTotal },
        appletGiftModel2: { appletGiftList2, appletGiftListTotal2 },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const version = useMemo(() => {
        if (pathname?.indexOf('/applet-gift/') >= 0) {
            return ACT_VERSION_TYPES.V_2_0;
        }
        return ACT_VERSION_TYPES.V_1_0;
    }, [pathname]);

    const list = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return appletGiftList2;
        }
        return appletGiftList;
    }, [version, appletGiftList, appletGiftList2]);

    const total = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return appletGiftListTotal2;
        }
        return appletGiftListTotal;
    }, [version, appletGiftListTotal, appletGiftListTotal2]);

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = (isDownload = false) => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            dates: undefined,
            pageIndex: (!isDownload && pageInfo.pageIndex) || undefined,
            pageSize: (!isDownload && pageInfo.pageSize) || undefined,
            beginDate:
                (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD HH:mm:ss')) || '',
            endDate:
                (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD HH:mm:ss')) || '',
        };
        if (version == ACT_VERSION_TYPES.V_1_0 && data.cityList?.length) {
            params.cityCodes = data.cityList?.join?.(',') || data.cityList;
            params.cityList = undefined;
        }
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }

        if (isDownload) {
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            if (version == ACT_VERSION_TYPES.V_2_0) {
                exportGiftListApi(params);
            } else {
                exportTableByParams({
                    methodUrl: '/bil/coupon/couponPutList',
                    options: params,
                    columnsStr: columnsStrs,
                });
            }
        } else {
            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            if (version == ACT_VERSION_TYPES.V_2_0) {
                dispatch({
                    type: 'appletGiftModel2/getAppletGiftList',
                    options: { ...params, actVersion: '2.0' },
                });
            } else {
                dispatch({
                    type: 'appletGiftModel/getAppletGiftList',
                    options: params,
                });
            }
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const stopGiftEvent = async (item) => {
        confirm({
            title: `确定停止${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        await stopMiniGiftApi2({
                            actId: item.actId,
                            actSubType: item.actSubType,
                        });
                    } else {
                        await stopMiniGiftApi({ actId: item.actId });
                    }

                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteGiftEvent = async (item) => {
        confirm({
            title: `确定删除${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        await deleteMiniGiftApi2({
                            actId: item.actId,
                            actSubType: item.actSubType,
                        });
                    } else {
                        await deleteMiniGiftApi({ actId: item.actId });
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // 直接发券
    const sendRef = useRef();
    const sendGiftEvent = (record) => {
        sendRef.current.show(record);
    };

    // 券延期
    const delayRef = useRef();

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push(`${pathname}/add`);
    };

    const editGiftEvent = (item) => {
        let path = `${pathname}/update/${item.actId}`;
        if (version == ACT_VERSION_TYPES.V_2_0) {
            path = path + `/${item.actSubType}`;
        }
        history.push(path);
    };

    const lookGiftEvent = (item) => {
        let path = `${pathname}/look/${item.actId}`;
        if (version == ACT_VERSION_TYPES.V_2_0) {
            path = path + `/${item.actSubType}`;
        }
        history.push(path);
    };

    const columns = [
        {
            title: '活动编号',
            width: 180,
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动类型',
            width: 160,
            dataIndex: 'actSubTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动ID',
            width: 200,
            dataIndex: 'actId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动时间',
            width: 320,
            dataIndex: 'creTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '发放对象',
        //     width: 140,
        //     dataIndex: 'actStateName',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '活动状态',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '累计参与人数',
            width: 140,
            dataIndex: 'putPeopleNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '累计发放数量',
            width: 140,
            dataIndex: 'alreadyPutNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '剩余奖品数量',
            width: 140,
            dataIndex: 'surpluPutNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '归属平台',
            dataIndex: 'cooperationPlatformName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '使用方',
            dataIndex: 'userTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '创建来源',
            dataIndex: 'createSourceName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '创建人',
            dataIndex: 'creEmp',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            // width: 220,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                // 直接发券商家端的券不可被编辑
                let editItem =
                    record.actSubType != ACTSUBTYPES.WEIXIN &&
                    record.actSubType == ACTSUBTYPES.SEND &&
                    record.createSource == '02' ? undefined : (
                        <span className={styles['table-btn']} onClick={() => editGiftEvent(record)}>
                            编辑
                        </span>
                    );
                let copyPath = `${pathname}/copy/${record.actId}`;
                if (version == ACT_VERSION_TYPES.V_2_0) {
                    copyPath = copyPath + `/${record.actSubType}`;
                }
                let copyItem =
                    record.actSubType != ACTSUBTYPES.WEIXIN ? (
                        <Link className={styles['table-btn']} to={copyPath} target="_blank">
                            复制
                        </Link>
                    ) : undefined;
                let lookitem = (
                    <span className={styles['table-btn']} onClick={() => lookGiftEvent(record)}>
                        详情
                    </span>
                );
                let deleteItem =
                    record.actSubType == ACTSUBTYPES.SEND &&
                    record.createSource == '02' ? undefined : (
                        <span
                            className={styles['table-btn']}
                            onClick={() => deleteGiftEvent(record)}
                        >
                            删除
                        </span>
                    );
                let stopItem =
                    record.actSubType == ACTSUBTYPES.SEND &&
                    record.createSource == '02' ? undefined : (
                        <span className={styles['table-btn']} onClick={() => stopGiftEvent(record)}>
                            停止
                        </span>
                    );
                let sendItem = (
                    <span className={styles['table-btn']} onClick={() => sendGiftEvent(record)}>
                        发券
                    </span>
                );
                let delayItem = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            delayRef.current?.show({
                                actId: record.actId,
                                startTime: moment(record.effTime),
                                endTime: moment(record.expTime),
                            });
                        }}
                    >
                        延期
                    </span>
                );

                let linkItem = (
                    <Tooltip
                        placement="topLeft"
                        title={`${window.location.origin}/operweb/coupon?actId=${record.actId}`}
                    >
                        <CopyToClipboard
                            text={`${window.location.origin}/operweb/coupon?actId=${record.actId}`}
                            onCopy={() => {
                                message.success('复制成功');
                            }}
                            className={styles['table-btn']}
                        >
                            <span className={styles['table-btn']}>链接</span>
                        </CopyToClipboard>
                    </Tooltip>
                );
                if (record.actState == STATUS_TYPES.DRAFT) {
                    btnList.push(editItem);
                    btnList.push(lookitem);

                    btnList.push(deleteItem);
                }
                if (record.actState == STATUS_TYPES.NOSTART) {
                    btnList.push(editItem);
                    btnList.push(lookitem);
                }
                if (record.actState == STATUS_TYPES.DOING) {
                    btnList.push(editItem);
                    btnList.push(lookitem);

                    btnList.push(stopItem);
                    if (record.actSubType == ACTSUBTYPES.SEND) {
                        btnList.push(sendItem);
                    }
                    if (record.actSubType == ACTSUBTYPES.COUPON) {
                        btnList.push(linkItem);
                    }
                }
                if (record.actState == STATUS_TYPES.END || record.actState == STATUS_TYPES.STOP) {
                    btnList.push(lookitem);
                }
                if (version == ACT_VERSION_TYPES.V_2_0) {
                    btnList.push(copyItem);
                    if (
                        record.actSubType == ACTSUBTYPES.COUPON &&
                        record.actState == STATUS_TYPES.END &&
                        record.effTime &&
                        record.expTime
                    ) {
                        btnList.push(delayItem);
                    }
                }

                return (
                    <Row>
                        {btnList.map((ele, index) => (
                            <Col key={index}>{ele}</Col>
                        ))}
                    </Row>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper title="优惠券营销">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />
                {version == ACT_VERSION_TYPES.V_2_0 && (
                    <div className={styles['btn-bar']}>
                        <Button type="primary" onClick={gotoAddEvent}>
                            新建
                        </Button>
                    </div>
                )}
                <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="未开始" key={STATUS_TYPES.NOSTART} />
                    <TabPane tab="进行中" key={STATUS_TYPES.DOING} />
                    <TabPane tab="已结束" key={STATUS_TYPES.END} />
                    {version == ACT_VERSION_TYPES.V_2_0 && (
                        <Fragment>
                            <TabPane tab="已停止" key={STATUS_TYPES.STOP} />
                            <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
                        </Fragment>
                    )}
                </Tabs>

                <TablePro
                    name={version + 'list'}
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => `${record.actId}`}
                    dataSource={list}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: total,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <SendGiftModal
                sendRef={sendRef}
                {...props}
                version={version}
                onFinish={() => {
                    searchData();
                }}
            />
            <CouponDelayModal
                initRef={delayRef}
                label="活动"
                onSubmit={async (values) => {
                    try {
                        await delayGiftApi(values);
                        searchData();
                        return Promise.resolve();
                    } catch (error) {
                        return Promise.reject();
                    }
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, appletGiftModel, appletGiftModel2, loading }) => ({
    global,
    appletGiftModel,
    appletGiftModel2,
    listLoading: loading.effects['appletGiftModel/getAppletGiftList'],
}))(AppletGiftListPage);
