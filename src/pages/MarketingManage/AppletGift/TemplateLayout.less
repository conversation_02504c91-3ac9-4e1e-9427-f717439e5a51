@import url('//at.alicdn.com/t/font_1271235_oedhwvhfptd.css');
.template-pop-view{
    position: relative;
    width:375px;
    height:600px;
    background-color: rgba(0,0,0,0.8);
}
.pop-window{
    position: absolute;
    left:50%;
    top:50%;
    width:290px;
    transform: translate(-50%,-50%);
    border-radius: 30px;
    .pop-close{
        position: absolute;
        right:0;
        top:-18px;
        color:#fff;
        font-size: 27px;
        z-index: 5;
    }
    .pop-header{
        position: relative;
        width:100%;
        height:123px;
        z-index: 1;
        border-radius: 30px 30px 0 0;
        overflow: hidden;
        img{
            display: block;
            width:100%;
            height:100%;
        }
    }
    .pop-coupon{
        position: relative;
        margin-top:-30px;
        width:100%;
        padding:20px 14px;
        background-color: #fff;
        border-radius: 30px;
        z-index: 2;
        .pop-coupon-item{
            position: relative;
            display: flex;
            align-items: center;
            width: 263px;
            height:65px;
            &:not(:last-child){
                margin-bottom:10px;
            }
            .pop-coupon-item-bg{
                position: absolute;
                width:100%;
                height:100%;
                left:0;
                right:0;
                z-index: 0;
            }
            .pop-coupon-item-info{
                position: relative;
                z-index: 2;
                display: flex;
                align-items: center;
                justify-content: center;
                color:#fff;
                letter-spacing:0;
                line-height: 1;
                .info-left{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 110px;
                    flex-shrink: 0;
                    .left-price{
                        font-size: 44px;
                        font-weight: bold;
                    }
                    .left-unit{
                        font-size: 15px;
                    }
                }
                .info-right{
                    padding-left:20px;
                    flex:1;
                    .right-title{
                        font-size: 15px;
                        font-weight: bold;
                    }
                    .right-remark{
                        margin-top:8px;
                        font-size: 12px;
                    }
                }
            }

        }
    }
    .pop-btn-bar{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }
    .pop-refuse{
        margin-right:5px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex:1;
        height:44px;
        background-color: #666;
        border-radius:22px;
        color:#fff;
        font-size: 16px;
                font-weight: bold;
    }
    .pop-confirm{
        position: relative;
        flex:1.5;
        height:44px;
        line-height: 1;
        .confirm-img{
            position: absolute;
            left:0;
            top:0;
           width:100%;
           height:100%;
           z-index:0;
           border-radius: 22px;
        }
        .confirm-info{
            width:100%;
            height:100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;
            line-height: 1;
            color:#fff;
            .confirm-title{
                font-size: 16px;
                font-weight: bold;
            }
            .confirm-countdown{
                font-size: 12px;

            }
        }

    }
}


.star-animation {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.5);
    width: 750px;
    height: 449px;
    z-index: -1;
    background-size: 24000px 449px;
    background-repeat: no-repeat;
    animation-name: start;
    animation-duration: 1.28s;
    animation-delay: 0s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
    animation-timing-function: steps(1);
    background-image: url('../../../assets/images/start.png');
}

@keyframes start {
    0% {
        width: 750px;
        height: 449px;
        background-size: 24000px 449px;
    }

    3.13% {
        background-position: -750px 0;
    }

    6.25% {
        background-position: -1500px 0;
    }

    9.38% {
        background-position: -2250px 0;
    }

    12.50% {
        background-position: -3000px 0;
    }

    15.63% {
        background-position: -3750px 0;
    }

    18.75% {
        background-position: -4500px 0;
    }

    21.88% {
        background-position: -5250px 0;
    }

    25.00% {
        background-position: -6000px 0;
    }

    28.13% {
        background-position: -6750px 0;
    }

    31.25% {
        background-position: -7500px 0;
    }

    34.38% {
        background-position: -8250px 0;
    }

    37.50% {
        background-position: -9000px 0;
    }

    40.63% {
        background-position: -9750px 0;
    }

    43.75% {
        background-position: -10500px 0;
    }

    46.88% {
        background-position: -11250px 0;
    }

    50.00% {
        background-position: -12000px 0;
    }

    53.13% {
        background-position: -12750px 0;
    }

    56.25% {
        background-position: -13500px 0;
    }

    59.38% {
        background-position: -14250px 0;
    }

    62.50% {
        background-position: -15000px 0;
    }

    65.63% {
        background-position: -15750px 0;
    }

    68.75% {
        background-position: -16500px 0;
    }

    71.88% {
        background-position: -17250px 0;
    }

    75.00% {
        background-position: -18000px 0;
    }

    78.13% {
        background-position: -18750px 0;
    }

    81.25% {
        background-position: -19500px 0;
    }

    84.38% {
        background-position: -20250px 0;
    }

    87.50% {
        background-position: -21000px 0;
    }

    90.63% {
        background-position: -21750px 0;
    }

    93.75% {
        background-position: -22500px 0;
    }

    96.88%,
    100% {
        background-position: -23250px 0;
    }
}
