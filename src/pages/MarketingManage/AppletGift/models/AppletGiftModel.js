import {
    getGiftList<PERSON><PERSON>,
    getGiftDetailApi,
    getActGroupNumApi,
    getActUserListApi,
    getExchangeListApi,
    getReveiveListApi,
    getTemplateListApi,
    getTemplateDetailApi,
} from '@/services/Marketing/MarketingGiftApi';

const appletGiftModel = {
    namespace: 'appletGiftModel',
    state: {
        appletGiftList: [], // 有礼列表
        appletGiftListTotal: 0,
        editActInfo: {}, // 当前有礼详情信息

        custLoyalTypeList: undefined, // 活动人群
        actUserList: undefined, // 活动发放用户列表
        actUserTotal: 0,

        exchangeList: undefined, // 活动兑换记录列表
        exchangeTotal: 0,

        reveiveList: undefined, // 优惠券营销领取记录
        reveiveTotal: 0,

        templateList: [], // 查询模板列表
        templateInfo: undefined, // 查询模板详情
    },
    effects: {
        /**
         * 有礼列表
         */
        *getAppletGiftList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getGiftListApi, options);

                yield put({
                    type: 'updateAppletGiftList',
                    appletGiftList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ params }, { call, put, select }) {
            try {
                const { data } = yield call(getGiftDetailApi, params);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },

        // 获取各群体人数
        *getCustLoyalTypeList({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getActGroupNumApi, options);
                yield put({
                    type: 'updateState',
                    data: { custLoyalTypeList: data?.getCustLoyalTypeList || [] },
                });
            } catch (error) {}
        },

        *getActUserList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, list },
                } = yield call(getActUserListApi, options);
                yield put({
                    type: 'updateState',
                    data: { actUserTotal: total, actUserList: list },
                });
            } catch (error) {}
        },

        *getExchangeList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, list },
                } = yield call(getExchangeListApi, options);
                yield put({
                    type: 'updateState',
                    data: { exchangeTotal: total, exchangeList: list },
                });
            } catch (error) {}
        },

        *getReveiveList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, list },
                } = yield call(getReveiveListApi, options);
                yield put({
                    type: 'updateState',
                    data: { reveiveList: list, reveiveTotal: total },
                });
            } catch (error) {}
        },
        // 查询模板列表
        *getTemplateList({ options }, { call, put, select }) {
            try {
                const {
                    data: { infoTemplateVoList },
                } = yield call(getTemplateListApi, options);
                yield put({
                    type: 'updateState',
                    data: { templateList: infoTemplateVoList },
                });
            } catch (error) {}
        },
        // 查询模板详情
        *getTemplateDetail({ options }, { call, put, select }) {
            try {
                const {
                    data: { infoTemplateVo },
                } = yield call(getTemplateDetailApi, options);
                yield put({
                    type: 'updateState',
                    data: { templateInfo: infoTemplateVo },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateState(state, { data }) {
            return {
                ...state,
                ...data,
            };
        },

        updateAppletGiftList(state, { appletGiftList, total }) {
            return {
                ...state,
                appletGiftList,
                appletGiftListTotal: total,
            };
        },

        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default appletGiftModel;
