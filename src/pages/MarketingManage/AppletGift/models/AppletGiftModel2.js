import {
    getGiftList<PERSON><PERSON>,
    getGiftDetailApi,
    getReveiveListApi,
    getExchangeListApi,
    getTemplateListApi,
    getTemplateDetailApi,
    getActGroupNumApi,
    getActUserList<PERSON>pi,
    getSendGiftRecordListApi,
} from '@/services/Marketing/MarketingGiftApi2';

const appletGiftModel2 = {
    namespace: 'appletGiftModel2',
    state: {
        appletGiftList2: [], // 有礼列表
        appletGiftListTotal2: 0,
        editActInfo2: {}, // 当前有礼详情信息

        custLoyalTypeList2: undefined, // 活动人群
        actUserList2: undefined, // 活动发放用户列表
        actUserTotal2: 0,

        reveiveList2: [], // 领取记录
        reveiveTotal2: 0,

        exchangeList2: [], //兑换情况
        exchangeTotal2: 0,

        templateList2: [], // 查询模板列表
        templateInfo2: undefined, // 查询模板详情

        sendRecordList: [], // 发券记录
        sendRecordTotal: 0,
    },
    effects: {
        /**
         * 有礼列表
         */
        *getAppletGiftList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getGiftListApi, options);

                yield put({
                    type: 'updateState',
                    data: {
                        appletGiftList2: list,
                        appletGiftListTotal2: total,
                    },
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ params }, { call, put, select }) {
            try {
                const { data } = yield call(getGiftDetailApi, params);
                // 兼容1.0字段
                // if (data?.giftBagPrizeInfoVo && !data?.giftBagRules) {
                //     const giftBagRules = [{ giftBagPrizeInfoVo: data?.giftBagPrizeInfoVo }];
                //     data.giftBagRules = giftBagRules;
                // }
                if (data?.giftBagRules?.length) {
                    const putActPushBoList = data.giftBagRules.map((ele) => {
                        // if (!ele.actGetLimitType && data.actGetLimitType) {
                        //     ele.actGetLimitType = data.actGetLimitType;
                        // }
                        // if (!ele.actGetLimitNum && data.actGetLimitNum) {
                        //     ele.actGetLimitNum = data.actGetLimitNum;
                        // }
                        // if (!ele.everyDayGetLimitNum && data.everyDayGetLimitNum) {
                        //     ele.everyDayGetLimitNum = data.everyDayGetLimitNum;
                        // }
                        // ele.sourceChannel = data.sourceChannel;
                        // ele.actChannel = data.actChannel;
                        // ele.popScope = data.popScope;
                        // ele.cityCodes = data.cityCodes;
                        // ele.advertiseType = data.advertiseType;
                        ele.cpnList = ele.giftBagPrizeInfoVo?.detPrizeList;
                        return {
                            ...ele,
                            ...ele.giftBagPrizeInfoVo,
                            giftBagType: data.giftBagType,
                        };
                    });
                    data.putActPushBoList = putActPushBoList;
                } else if (data?.giftBagPrizeInfoVo) {
                    data.putCpnList = data.giftBagPrizeInfoVo?.detPrizeList;
                    data.stockNum = data.giftBagPrizeInfoVo?.stockNum;
                    data.giftBagPutNum = data.giftBagPrizeInfoVo?.giftBagPutNum;
                }
                yield put({
                    type: 'updateState',
                    data: {
                        editActInfo2: data,
                    },
                });
            } catch (error) {}
        },
        *getReveiveList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getReveiveListApi, options);

                yield put({
                    type: 'updateState',
                    data: { reveiveList2: list, reveiveTotal2: total },
                });
            } catch (error) {}
        },
        *getExchangeList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, records: list },
                } = yield call(getExchangeListApi, options);
                yield put({
                    type: 'updateState',
                    data: { exchangeTotal2: total, exchangeList2: list },
                });
            } catch (error) {}
        },
        // 查询模板列表
        *getTemplateList({ options }, { call, put, select }) {
            try {
                const { data: infoTemplateVoList } = yield call(getTemplateListApi, options);
                yield put({
                    type: 'updateState',
                    data: { templateList2: infoTemplateVoList },
                });
            } catch (error) {}
        },
        // 查询模板详情
        *getTemplateDetail({ options }, { call, put, select }) {
            try {
                const { data: infoTemplateVo } = yield call(getTemplateDetailApi, options);
                yield put({
                    type: 'updateState',
                    data: { templateInfo2: infoTemplateVo },
                });
            } catch (error) {}
        },
        // 获取各群体人数
        *getCustLoyalTypeList({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getActGroupNumApi, options);
                yield put({
                    type: 'updateState',
                    data: { custLoyalTypeList2: data || [] },
                });
            } catch (error) {}
        },

        *getActUserList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, records: list },
                } = yield call(getActUserListApi, options);
                yield put({
                    type: 'updateState',
                    data: { actUserTotal2: total, actUserList2: list },
                });
            } catch (error) {}
        },

        *getSendGiftRecordList({ options }, { call, put, select }) {
            try {
                const {
                    data: { total, records: list },
                } = yield call(getSendGiftRecordListApi, options);
                yield put({
                    type: 'updateState',
                    data: { sendRecordList: list, sendRecordTotal: total },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateState(state, { data }) {
            return {
                ...state,
                ...data,
            };
        },
    },
};
export default appletGiftModel2;
