import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import { Button, Card, Form, Modal, message } from 'antd';

import { LeftOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { getAddGiftInfoApi, saveGift<PERSON><PERSON>, getWXUrlApi } from '@/services/Marketing/MarketingGiftApi';

import { addCpnApi } from '@/services/Marketing/MarketingTurnoverApi';
import { YSF_APPID } from '@/config/global';
import TablePro from '@/components/TablePro';
import CityImportModal from '@/components/CitysSelect/CityImportModal';
import { saveGiftApi as saveMngGiftApi } from '@/services/Marketing/MarketingGiftApi2';
import {
    ACTSUBTYPES,
    EXCHANGESTATUS,
    APPLET_ACT_TYPES,
    ACT_VERSION_TYPES,
    ACT_SUB_STATUS,
    STATUS_TYPES,
    COOPERATION_PLATFORM_TYPES,
} from '@/config/declare';
import commonStyles from '@/assets/styles/common.less';
import styles from './AppletGift.less';
import { TemplatePreview, TemplatePopupView } from './TemplateLayout';
import InfoFormLayout from './CouponComponents/InfoFormLayout';
import RuleLayout, { USER_JOIN_TYPES } from './CouponComponents/RuleLayout';
import ExchangeLayout from './CouponComponents/ExchangeLayout';
import ActiveLayout from './CouponComponents/ActiveLayout';
import FaceIconLayout from './CouponComponents/FaceIconLayout';
import OtherLayout from './CouponComponents/OtherLayout';
import OtherChannelLayout, { OTHER_ACT_CHANNEL } from './CouponComponents/OtherChannelLayout';

import { isEmpty } from '@/utils/utils';

const { confirm } = Modal;

const FormItem = Form.Item;

const NEW_REGISTER_USER = '28'; //新注册用户类型
const UNACTIVATION_USER = '01'; //新注册用户类型 activation

// 跳转方式
const LINK_TYPES = {
    HOME: '01', // 首页
    CARD: '02', // 我的卡包
    OTHER: '03', // 自定义
    GIFT: '04', // 默认礼包领取页面
};

const SUBMIT_TYPES = {
    SAVE: 'save',
    SUBMIT: 'submit',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 180px',
    },
    labelAlign: 'left',
    wrapperCol: {
        span: 12,
    },
    labelWrap: true,
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

export const CouponUpdatePage = (props) => {
    const {
        dispatch,
        history,
        location,
        route,
        match,
        global: { codeInfo },
        appletGiftModel: { editActInfo = {} },
        appletGiftModel2: { editActInfo2 },
    } = props;
    const {
        location: { pathname, query },
    } = history;

    const version = useMemo(() => {
        if (pathname?.indexOf('/applet-gift/') >= 0) {
            return ACT_VERSION_TYPES.V_2_0;
        }
        return ACT_VERSION_TYPES.V_1_0;
    }, [pathname]);

    const modelName = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return 'appletGiftModel2';
        }
        return 'appletGiftModel';
    }, [version]);

    const info = useMemo(() => {
        if (version == ACT_VERSION_TYPES.V_2_0) {
            return editActInfo2;
        }
        return editActInfo;
    }, [version, editActInfo, editActInfo2]);

    const { sourceChannel: sourceChannelList } = codeInfo;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [actId, changeActId] = useState(() => {
        return match.params.actId || null;
    });
    const [actSubType, changeActSubType] = useState(() => {
        return match.params.actSubType || null;
    });
    const [isCopy, changeCopy] = useState(() => {
        if (route.path.indexOf('/copy') >= 0) {
            return true;
        }
        return false;
    });
    const isLock = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return true;
        }
        if (route.path.indexOf('/update') >= 0 && info?.actState >= 2) {
            return true;
        }
        return false;
    }, [info]); // 是否可编辑

    const openSomeThing = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return false;
        }
        return !info || info?.actState == 2 || !isLock;
    }, [info, isLock]); // 是否可编辑

    //是否为查看模式
    const isLook = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return true;
        }
        return false;
    }, [route.path]);
    //是否已结束，用于进行中可编辑数据
    const isEnd = useMemo(() => {
        if (info?.actState > 2) {
            return true;
        } else {
            return false;
        }
    }, [info]);

    //是否为草稿，用于仅草稿可编辑
    const isDraft = useMemo(() => {
        if (isEmpty(info) || info?.actState === '0' || info?.actState === 0) {
            return true;
        } else {
            return false;
        }
    }, [info]);

    const [actTypeList, changeActTypeList] = useState([]);
    const exchangeRef = useRef();

    // 初始化
    useEffect(() => {
        if (!sourceChannelList) {
            dispatch({
                type: 'global/initCode',
                code: 'sourceChannel',
            });
        }
        loadData(actId, actSubType);
        return () => {
            dispatch({
                type: `${modelName}/updateState`,
                data: { editActInfo: {}, editActInfo2: {} },
            });
        };
    }, []);

    const loadData = async (id, actSubType) => {
        try {
            if (id) {
                dispatch({
                    type: `${modelName}/initEditActInfo`,
                    params: {
                        actId: id,
                        actSubType,
                    },
                });
                const actNo = await initAddInfo();
                if (isCopy) {
                    form.setFieldsValue({ actNo });
                }
            } else {
                const actNo = await initAddInfo();
                form.setFieldsValue({
                    actNo,
                    actTimeList: [{}],
                    actSubType: query.actSubType,
                    cooperationPlatform: COOPERATION_PLATFORM_TYPES.XDT,
                });
            }
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const refreshWXUrl = async () => {
        try {
            const params = pathLink();
            if (!params || !actId) {
                return;
            }
            // 不区分优惠券1.0/2.0，后端开发说就用旧的
            const {
                data: { urlscheme },
            } = await getWXUrlApi(params);
            form.setFieldsValue({ wxurl: urlscheme });
        } catch (error) {}
    };

    const pathLink = () => {
        try {
            const { actSubType, actNo } = form.getFieldsValue() || {};
            if (!actSubType?.length || !actNo?.length) {
                return undefined;
            }
            let basePath = `/pagesActive/gift/index`;

            if (actSubType === ACTSUBTYPES.DIRECT) {
                basePath = `/pagesActive/gift/coupon`;
            }

            const params = [`actGiftNo=${actNo}`];
            return { path: basePath, query: params.join('&') };
        } catch (error) {
            return undefined;
        }
    };

    // 初始化详情
    useEffect(() => {
        if (info) {
            let params = {
                actId: info.actId,
                actSubType: info.actSubType || '',
                actName: info.actName || '',
                cooperationPlatform: info.cooperationPlatform || '',
                actMarks: info.actMarks || '',
                actTimeLimitFlag: info.actTimeLimitFlag,
                activityForm: info.activityForm,
                shareFlag: info.shareFlag,
                isInfoTemplate: info.isInfoTemplate || '1',
                infoTemplateId: info.infoTemplateId,
                directDisplay: info.directDisplay,
                configType: info.configType,
                activeCrowdFlag: '0',

                spsFlag: info.spsFlag,
                popType: info.popType,
                // cancelTitle: info.cancelTitle,
                minuteTimes: info.minuteTimes,
                hourTimes: info.hourTimes,
                buttonTitle: info.buttonTitle || '',
                thirdPlatType: info.thirdPlatType,
                thirdSkuId: info.thirdSkuId,
            };
            if (info.actChannel) {
                params.actChannel = info.actChannel?.split?.(',') || [];
            }

            if (!isCopy) {
                params.actNo = info.actNo;
            }

            if (info.effTime && info.expTime) {
                params.dateTime = [moment(info.effTime), moment(info.expTime)];
            }

            if (info.bgIcon) {
                params.bgIcon = info.bgIcon;
            }
            if (info.headIcon) {
                params.headIcon = info.headIcon;
            }
            if (info.buttonIcon) {
                params.buttonIcon = info.buttonIcon;
            }
            // if (info.cancelIcon) {
            //     params.cancelIcon = info.cancelIcon;
            // }
            if (info.spsImg) {
                params.spsImg = info.spsImg;
            }
            if (info.buttonTitle) {
                params.buttonTitle = info.buttonTitle;
            }
            if (info.subButtonTitle) {
                params.subButtonTitle = info.subButtonTitle;
            }
            if (info.buttonEvent) {
                params.buttonEvent = info.buttonEvent;
            }

            if (info.adIconLinkUrl) {
                params.adIconLinkUrl = info.adIconLinkUrl;
            }
            if (info.popIcon) {
                params.popIcon = info.popIcon;
            }
            if (info.faceIcon) {
                params.faceIcon = info.faceIcon;
            }

            if (info.buttonColour) {
                params.buttonColour = info.buttonColour;
            }
            if (info.bgColour) {
                params.bgColour = info.bgColour;
            }
            if (info.coberPic) {
                params.coberPic = info.coberPic;
            }

            if (info.linkType) {
                params.linkType = info.linkType;
            }
            if (info.linkUrl) {
                params.linkUrl = info.linkUrl;
            }
            if (info.appId) {
                params.appId = info.appId;
            }

            const extendData = [];
            if (info.extend) {
                const extendInfo = JSON.parse(info.extend);
                for (const key in extendInfo) {
                    if (extendInfo.hasOwnProperty(key)) {
                        const element = extendInfo[key];
                        extendData.push({
                            name: key,
                            value: element,
                        });
                    }
                }
            }
            params.extend = extendData;

            if (!info.actTimeList?.length) {
                // 用于切换为限制的时候默认展示一栏
                params.actTimeList = [{}];
            }

            if (info.actTimeList?.length) {
                let actTimeList = info.actTimeList.map((ele) => ({
                    beginTime: moment(ele.beginTime, 'HH:mm'),
                    endTime: moment(ele.endTime, 'HH:mm'),
                }));
                params.actTimeList = actTimeList;
            }

            if (info?.shareFlag === '1') {
                params.buttonTitle = info?.buttonTitle || '';
                params.shareTitle = info?.shareTitle || '';

                params.wxShareIcon = info?.wxShareIcon || '';

                params.aliShareIcon = info?.aliShareIcon || '';

                params.aliSqueak = info?.aliSqueak || '';
            }

            if (info.putActPushBoList) {
                let list = info.putActPushBoList;
                let actPushBoListInfo = list.map((ele) => {
                    const crowdInfo = {
                        labelList: ele?.labelList || [],
                        custLabelList: ele?.custLabelList || [],
                        cdpList: ele?.cdpList || [],
                    };
                    let item = {
                        ...ele,
                        cpnList: !isCopy && ele.cpnList ? ele.cpnList : [],
                        crowdInfo,
                        activeCrowdFlag: '0',
                        activeCrowd: (ele.activeCrowd && ele.activeCrowd.split(',')) || [],
                    };
                    if (params.actSubType === ACTSUBTYPES.DIRECT) {
                        item.actEveryDayGetLimitNum = ele.actEveryDayGetLimitNum;
                    }
                    if (isCopy) {
                        delete item.actPushId;
                    }
                    for (const cpnItem of item.cpnList) {
                        if (cpnItem.cpnOwner === '03' || cpnItem.cpnOwner === '04') {
                            cpnItem.cpnAddType = '02';
                        }
                    }
                    if (ele.sourceChannel?.indexOf('00') > -1) {
                        // 如果是全选，接口要求只要传'00'，详情同样处理
                        item.sourceChannel =
                            (sourceChannelList && sourceChannelList?.map((ele) => ele.codeValue)) ||
                            [];
                    } else if (typeof ele?.sourceChannel === 'string') {
                        item.sourceChannel =
                            (ele.sourceChannel && ele.sourceChannel.split(',')) || [];
                    }

                    if (ele.cityCodes) {
                        item.cityCodes = (ele.sourceChannel && ele.cityCodes.split(',')) || [];
                    }
                    if (ele.cityInfos instanceof Array) {
                        item.cityCodes = ele.cityInfos?.map((cityItem) => cityItem.city);
                    }
                    return item;
                });
                if (params.actSubType === ACTSUBTYPES.DIRECT) {
                    params.actPushBoList = actPushBoListInfo;
                } else {
                    if (actPushBoListInfo[0]) {
                        params = { ...params, ...actPushBoListInfo[0] };
                    }
                }
            } else {
                const crowdInfo = {
                    labelList: info?.labelList || [],
                    custLabelList: info?.custLabelList || [],
                    cdpList: info?.cdpList || [],
                };
                let options = {
                    ...info,
                    cpnList: !isCopy && info.putCpnList ? info.putCpnList : [],
                    crowdInfo,
                    activeCrowdFlag: '0',
                    activeCrowd: (info.activeCrowd && info.activeCrowd.split(',')) || [],
                    actGetLimitNum: info.actGetLimitNum,
                    everyDayGetLimitNum: info.everyDayGetLimitNum,
                    actGetLimitType: info.actGetLimitType,
                    saleAmt: info.saleAmt,
                    advertiseType: info.advertiseType?.split?.(','),
                    stockNum: info.stockNum,
                    giftBagPutNum: info.giftBagPutNum,
                    giftBagType: info.giftBagType,
                    actSubId: info.actSubId,
                    recommendCycle: isCopy ? undefined : info.recommendCycle,
                    recommendPrizeId: isCopy ? undefined : info.recommendPrizeId,
                };
                for (const cpnItem of options.cpnList) {
                    if (cpnItem.cpnOwner === '03' || cpnItem.cpnOwner === '04') {
                        cpnItem.cpnAddType = '02';
                    }
                }
                if (info.actChannel) {
                    options.actChannel = info.actChannel.split(',') || [];
                }
                if (info.sourceChannel?.indexOf('00') > -1) {
                    // 如果是全选，接口要求只要传'00'，详情同样处理
                    options.sourceChannel =
                        (sourceChannelList && sourceChannelList?.map((ele) => ele.codeValue)) || [];
                } else if (typeof info?.sourceChannel === 'string') {
                    options.sourceChannel =
                        (info.sourceChannel && info.sourceChannel.split(',')) || [];
                }

                if (info.popScope) {
                    options.popScope = info.popScope;
                }

                options.cityCodes = info.popScopeInfo || undefined;

                if (info.cityInfos instanceof Array) {
                    options.cityCodes = info.cityInfos?.map((ele) => ele.city);
                }

                params.actPushBoList = [options];
            }
            console.log(info, params);
            form.setFieldsValue(params);
            refreshWXUrl();
        }
    }, [info]);

    // 获取actNo
    const initAddInfo = async () => {
        try {
            // 不区分优惠券1.0/2.0，后端开发说就用旧的
            const {
                data: { actNo, actTypeList },
            } = await getAddGiftInfoApi();
            changeActTypeList(actTypeList || []);

            return actNo;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const showSubmitConfirm = async ({ title, content }) => {
        return new Promise((resolve, reject) => {
            confirm({
                title: title || '',
                icon: <ExclamationCircleOutlined />,
                content: content || '',
                onOk() {
                    resolve();
                },
                onCancel() {
                    reject();
                },
            });
        });
    };

    /**
     * 保存
     * type  save/send
     */
    const [submitType, updateSubmitType] = useState(SUBMIT_TYPES.SAVE);
    const saveGiftEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        updateSubmitType(type);
        try {
            const values = await form.validateFields();
            let params = {
                actNo: values.actNo,
                actSubType: values.actSubType || '',
                actName: values.actName || '',
                cooperationPlatform: values.cooperationPlatform || '',
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                actMarks: values.actMarks || '',
                actTimeLimitFlag: values.actTimeLimitFlag,
                popScope: values.popScope,
                activityForm: values.activityForm,
                shareFlag: values.shareFlag,
                isInfoTemplate: values.isInfoTemplate,
                infoTemplateId: values.infoTemplateId,
                directDisplay: values.directDisplay || '',
                configType: values.configType,
                actChannel: values.actChannel?.join?.(',') || undefined,
                recommendCycle: values.recommendCycle,
                recommendPrizeId: values.recommendPrizeId,

                spsFlag: values.spsFlag,
                popType: values.popType,
                // cancelTitle: values.cancelTitle,
                minuteTimes: values.minuteTimes,
                hourTimes: values.hourTimes,
                buttonTitle: values.buttonTitle || '',
            };

            if (
                (values.actSubType == ACTSUBTYPES.RECEIVE ||
                    values.actSubType == ACTSUBTYPES.DIRECT) &&
                values.activityForm == '02'
            ) {
                // 如果是直接领取类型，活动形式选成了付费，直接领取展示要传是
                params.directDisplay = '1';
            }

            if (values.bgIcon) {
                params.bgIcon = values.bgIcon;
            }
            if (values.headIcon) {
                params.headIcon = values.headIcon;
            }
            if (values.popIcon) {
                params.popIcon = values.popIcon;
            }
            if (values.buttonIcon) {
                params.buttonIcon = values.buttonIcon;
            }
            if (values.buttonTitle) {
                params.buttonTitle = values.buttonTitle;
            }
            if (values.subButtonTitle) {
                params.subButtonTitle = values.subButtonTitle;
            }
            if (values.buttonEvent) {
                params.buttonEvent = values.buttonEvent;
            }
            if (values.faceIcon) {
                params.faceIcon = values.faceIcon;
            }

            if (values.adIconLinkUrl) {
                params.adIconLinkUrl = values.adIconLinkUrl;
            }
            if (values.buttonColour) {
                params.buttonColour = values.buttonColour;
            }
            // if (values.cancelIcon) {
            //     params.cancelIcon = values.cancelIcon;
            // }
            if (values.spsImg) {
                params.spsImg = values.spsImg;
            }
            if (values.bgColour) {
                params.bgColour = values.bgColour;
            }
            if (values.coberPic) {
                params.coberPic = values.coberPic;
            }

            if (values.linkType) {
                params.linkType = values.linkType;
            }
            if (values.linkUrl) {
                params.linkUrl = values.linkUrl.replace(/[\r\n\s]/g, '');
            }
            if (values.appId) {
                params.appId = values.appId;
            }
            if (values.extend) {
                const extend = {};
                for (const item of values.extend) {
                    extend[item.name] = item.value;
                }
                params.extend = JSON.stringify(extend);
            }
            if (values.actTimeLimitFlag == '1' && values.actTimeList?.length) {
                let actTimeList = values.actTimeList.map((ele) => ({
                    beginTime: ele.beginTime.format('HH:mm'),
                    endTime: ele.endTime.format('HH:mm'),
                }));
                params.actTimeListStr = JSON.stringify(actTimeList || []);
            } else {
                params.actTimeListStr = '';
            }

            if (values.actSubType == ACTSUBTYPES.THIRD) {
                params.thirdPlatType = values.thirdPlatType;
                if (values.thirdPlatType == OTHER_ACT_CHANNEL.KWAI) {
                    params.thirdSkuId = values.thirdSkuId;
                    // params.dayStore = values.dayStore;
                    // params.accountLimitAmt = values.accountLimitAmt;
                    // params.accountDayLimitAmt = values.accountDayLimitAmt;
                }
            }

            let activeCrowdError = '';
            if (values.actPushBoList) {
                let actPushBoListInfo = values.actPushBoList.map((ele) => {
                    const { crowdInfo } = ele;

                    let item = {
                        cpnList: ele.cpnList || [],
                        activeCrowdFlag: ele.activeCrowdFlag || '0',
                        activeCrowd:
                            (ele.activeCrowd?.length &&
                                ele.activeCrowd.join &&
                                ele.activeCrowd.join(',')) ||
                            ele.activeCrowd,
                        ...crowdInfo,
                        actGetLimitNum: ele.actGetLimitNum,
                        everyDayGetLimitNum: ele.everyDayGetLimitNum,
                        actEveryDayGetLimitNum: ele.actEveryDayGetLimitNum,
                        actGetLimitType: ele.actGetLimitType,
                        saleAmt: ele.saleAmt || '0.00',
                        actPushId: ele.actPushId,
                        actId: ele.actId,
                        advertiseType: ele.advertiseType,
                        stockNum: ele.stockNum,
                        giftBagPutNum: ele.giftBagPutNum,
                        giftBagType: ele.giftBagType,
                        recommendCycle: ele.recommendCycle,
                        recommendPrizeId: ele.recommendPrizeId,
                        mktActPushId: isCopy ? undefined : ele.mktActPushId,

                        openType: ele.openType,
                        showTimesType: ele.showTimesType,
                        showTimes: ele.showTimes,
                        perDayNum: ele.perDayNum,
                        perDayTimes: ele.perDayTimes,
                        dayNum: ele.dayNum,
                    };
                    if (values.configType == '01') {
                        //日常的话根据配置传
                        item.onlyNewJoinFlag = ele.onlyNewJoinFlag;
                    } else if (values.configType == '02') {
                        //限时秒杀一定传0
                        item.onlyNewJoinFlag = '0';
                    }
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        item.giftBagPrizeInfoParams = ele.cpnList;
                        delete item.cpnList;
                    }
                    if (isCopy) {
                        delete item.actPushId;
                    }
                    if (Array.isArray(ele?.sourceChannel)) {
                        if (
                            item.onlyNewJoinFlag == USER_JOIN_TYPES.OTHER &&
                            ele.sourceChannel.length === sourceChannelList.length
                        ) {
                            item.sourceChannel = '00';
                        } else {
                            item.sourceChannel = ele.sourceChannel.join(',');
                        }
                    }

                    if (ele.actChannel) {
                        item.actChannel = ele.actChannel.join(',');
                    }

                    if (ele.cityCodes) {
                        item.cityCodes = ele.cityCodes.join(',');
                    }
                    if (ele.popScope) {
                        item.popScope = ele.popScope;
                    }
                    return item;
                });
                if (params.actSubType === ACTSUBTYPES.DIRECT) {
                    //activeCrowdError

                    if (actPushBoListInfo.length > 1) {
                        let registerIndex = -1;
                        let activationIndex = -1;
                        actPushBoListInfo.forEach((element, index) => {
                            const { crowdInfo = {} } = element;
                            const { custLabelList = [] } = crowdInfo;
                            if (
                                custLabelList.indexOf(NEW_REGISTER_USER) >= 0 ||
                                element?.activeCrowd?.indexOf(NEW_REGISTER_USER) >= 0
                            ) {
                                registerIndex = index;
                            }
                            if (
                                custLabelList.indexOf(UNACTIVATION_USER) >= 0 ||
                                element?.activeCrowd?.indexOf(UNACTIVATION_USER) >= 0
                            ) {
                                activationIndex = index;
                            }
                        });
                        if (
                            registerIndex >= 0 &&
                            activationIndex >= 0 &&
                            registerIndex != activationIndex
                        ) {
                            activeCrowdError = `活动用户${
                                registerIndex + 1
                            }的配置包含了新注册用户，活动用户${
                                activationIndex + 1
                            }的配置包含了未激活用户，会导致用户在不同场景下重复领奖，请确认！`;
                        }
                    }
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        params.giftBagRules = actPushBoListInfo;
                    } else {
                        params.actPushBoList = JSON.stringify(actPushBoListInfo);
                    }
                } else {
                    if (actPushBoListInfo[0]) {
                        params = { ...params, ...actPushBoListInfo[0] };
                        if (version == ACT_VERSION_TYPES.V_1_0) {
                            params.cpnList = JSON.stringify(params.cpnList);
                        }
                        if (params.crowdInfo) {
                            params = { ...params, ...params.crowdInfo };
                            delete params.crowdInfo;
                        }
                        params.activeCrowd =
                            (params.activeCrowd?.length &&
                                params.activeCrowd.join &&
                                params.activeCrowd.join(',')) ||
                            params.activeCrowd;
                    }
                    if (params.actSubType === ACTSUBTYPES.POPUP) {
                        params.popFlag = '1';
                        if (params.advertiseType?.length) {
                            params.advertiseType = params.advertiseType?.join(',');
                        }
                    }
                }
            }
            // }

            if (values?.shareFlag === '1') {
                params.buttonTitle = values?.buttonTitle || '';
                params.shareTitle = values?.shareTitle || '';

                params.wxShareIcon = values?.wxShareIcon || '';

                params.aliShareIcon = values?.aliShareIcon || '';

                params.aliSqueak = values?.aliSqueak || '';
            }

            if (actId) {
                params.actId = actId;
            }
            if (isCopy) {
                delete params.actId;
            }

            if (
                params.actSubType === ACTSUBTYPES.POPUP &&
                (params?.custLabelList?.indexOf(NEW_REGISTER_USER) >= 0 ||
                    params?.activeCrowd?.indexOf(NEW_REGISTER_USER) >= 0)
            ) {
                //弹窗模式选了新人活动
                params.loginFlag = '1';
            } else {
                params.loginFlag = '0';
            }

            if (type == 'save') {
                params.release = '0';
            } else if (type == 'submit') {
                params.release = '1';
            }

            // if (activeCrowdError) {
            //     await showSubmitConfirm({ title: '提示', content: activeCrowdError });
            // }

            let newId;
            changeSubmitLoading(true);

            const subType = params.actSubType;

            if (version == ACT_VERSION_TYPES.V_2_0) {
                const { data } = await saveMngGiftApi(params);
                newId = data;
            } else {
                const {
                    data: { actId: id },
                } = await saveGiftApi(params);
                newId = id;
            }

            changeActId(newId);
            changeActSubType(subType);

            if (type == 'save') {
                message.success('保存成功');
                if (isCopy) {
                    let path = `/marketing/couponCenter/appletgift/list/update/${newId}`;
                    if (version == ACT_VERSION_TYPES.V_2_0) {
                        path = `/marketing/couponCenter/applet-gift/list/update/${newId}/${subType}`;
                    }
                    history.replace(path);
                } else {
                    loadData(newId, subType);
                }
            } else if (type == 'submit') {
                message.success('提交成功');
                goBack();
            }
            changeCopy(false);
            return;
        } catch (error) {
            console.log(error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const goBack = () => {
        let path = `/marketing/couponCenter/appletgift/list`;
        if (version == ACT_VERSION_TYPES.V_2_0) {
            path = `/marketing/couponCenter/applet-gift/list`;
        }
        history.replace(path);
    };

    return (
        <Card bordered={false}>
            <Form
                form={form}
                initialValues={{
                    contentLink: LINK_TYPES.GIFT,
                }}
                {...formItemLayout}
                scrollToFirstError
            >
                <div className={commonStyles['form-title']}>基础信息</div>
                <InfoFormLayout
                    form={form}
                    editActInfo={info}
                    isLock={isLock}
                    isDraft={isDraft}
                    isCopy={isCopy}
                    openSomeThing={openSomeThing}
                    actTypeList={actTypeList}
                    submitType={submitType}
                    refreshWXUrl={refreshWXUrl}
                    formItemLayout={formItemLayout}
                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                    {...props}
                />
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        // 活动类型为直接发放时无发布信息
                        return (
                            ((actSubType == ACTSUBTYPES.RECEIVE ||
                                actSubType == ACTSUBTYPES.DIRECT ||
                                actSubType == ACTSUBTYPES.POPUP) && (
                                <Fragment>
                                    <div className={commonStyles['form-title']}>页面配置</div>
                                    <ActiveLayout
                                        {...props}
                                        disabled={!openSomeThing}
                                        form={form}
                                        editActInfo={info}
                                        actId={actId}
                                        formItemLayout={formItemLayout}
                                        formItemFixedWidthLayout={formItemFixedWidthLayout}
                                        version={version}
                                    />
                                </Fragment>
                            )) ||
                            null
                        );
                    }}
                </FormItem>
                <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        return actSubType ? (
                            <Fragment>
                                <div className={commonStyles['form-title']}>
                                    {(actSubType == ACTSUBTYPES.ALI && '优惠券') || '规则配置'}
                                </div>
                                <RuleLayout
                                    applyType={'05'}
                                    name="actPushBoList"
                                    form={form}
                                    {...props}
                                    isLock={isLock}
                                    openSomeThing={openSomeThing}
                                    isCopy={isCopy}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    version={version}
                                    isEnd={isEnd}
                                    isLook={isLook}
                                    appendFinished={() => {
                                        loadData(actId, actSubType);
                                        // 外部监听reload事件的调用，目前用在详情页
                                        setTimeout(() => {
                                            // 隔3秒后自动刷新兑换情况
                                            exchangeRef.current?.searchEvent();
                                        }, 3000);
                                    }}
                                />
                            </Fragment>
                        ) : null;
                    }}
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        if (actSubType == ACTSUBTYPES.THIRD) {
                            return (
                                <OtherChannelLayout
                                    isLock={isLock}
                                    isCopy={isCopy}
                                ></OtherChannelLayout>
                            );
                        }
                        return null;
                    }}
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        if (actSubType == ACTSUBTYPES.ALI) {
                            return (
                                <FaceIconLayout
                                    disabled={isLock}
                                    form={form}
                                    editActInfo={info}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    version={version}
                                />
                            );
                        }
                        return null;
                    }}
                </FormItem>

                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        // 活动类型为直接发放时无发布信息
                        return (
                            <>
                                {/* {actSubType == ACTSUBTYPES.RECEIVE && (
                                        <Fragment>
                                            <div className={styles.formTitle}>发布信息</div>
                                            <ReleaseLayout
                                                form={form}
                                                editActInfo={info}
                                                isLock={isLock}
                                            />
                                        </Fragment>
                                    )} */}
                                {(actSubType == ACTSUBTYPES.RECEIVE ||
                                    actSubType == ACTSUBTYPES.DIRECT) && (
                                    <Fragment>
                                        <div className={commonStyles['form-title']}>其他配置</div>
                                        <OtherLayout
                                            {...props}
                                            disabled={!openSomeThing}
                                            form={form}
                                            editActInfo={info}
                                            formItemLayout={formItemLayout}
                                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                                        />
                                    </Fragment>
                                )}
                            </>
                        );
                    }}
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.actSubType !== curValues.actSubType
                    }
                >
                    {({ getFieldValue }) => {
                        const actSubType = getFieldValue('actSubType');
                        // 活动类型为兑换码兑换时，可以查看生成的兑换码及兑换码的兑换情况。
                        return (
                            <Fragment>
                                {actSubType == ACTSUBTYPES.COUPON && isLock && (
                                    <ExchangeLayout
                                        actId={actId}
                                        actSubType={actSubType}
                                        formItemLayout={formItemLayout}
                                        formItemFixedWidthLayout={formItemFixedWidthLayout}
                                        version={version}
                                        initRef={exchangeRef}
                                        {...props}
                                    />
                                )}
                            </Fragment>
                        );
                    }}
                </FormItem>
                <div className={styles['form-submit']}>
                    {isLock ? (
                        <Fragment>
                            {openSomeThing &&
                                (info?.actSubType === ACTSUBTYPES.DIRECT ||
                                    info?.actSubType === ACTSUBTYPES.RECEIVE ||
                                    info?.actSubType === ACTSUBTYPES.POPUP ||
                                    version == ACT_VERSION_TYPES.V_2_0) && (
                                    <Button
                                        className={styles['form-btn']}
                                        type="primary"
                                        loading={submitLoading}
                                        onClick={() => {
                                            saveGiftEvent(SUBMIT_TYPES.SUBMIT);
                                        }}
                                    >
                                        提交
                                    </Button>
                                )}
                            <Button className={styles['form-btn']} onClick={goBack}>
                                返回
                            </Button>
                        </Fragment>
                    ) : (
                        <Fragment>
                            {info?.actState < 1 && (
                                <Button
                                    className={styles['form-btn-left']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveGiftEvent(SUBMIT_TYPES.SAVE);
                                    }}
                                >
                                    保存
                                </Button>
                            )}
                            <Button
                                className={styles['form-btn']}
                                type="primary"
                                loading={submitLoading}
                                onClick={() => {
                                    saveGiftEvent(SUBMIT_TYPES.SUBMIT);
                                }}
                            >
                                提交
                            </Button>

                            <Button className={styles['form-btn']} onClick={goBack}>
                                取消
                            </Button>
                        </Fragment>
                    )}
                </div>
                <TemplatePreview
                    {...props}
                    form={form}
                    disabled={!openSomeThing}
                    version={version}
                />
                <TemplatePopupView version={version} {...props} form={form}></TemplatePopupView>
            </Form>
        </Card>
    );
};

const AppletGiftUpdate = (props) => {
    const { history, route } = props;
    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
            content="小程序有礼是商户权益与支付宝流量玩法相结合的一种自运营工具，需结合支付宝后台配置"
        >
            <CouponUpdatePage {...props} />
        </PageHeaderWrapper>
    );
};
export default connect(
    ({ global, appletGiftModel, appletGiftModel2, couponModel, newCouponModel }) => ({
        global,
        appletGiftModel,
        appletGiftModel2,
        couponModel,
        newCouponModel,
    }),
)(AppletGiftUpdate);
