import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Modal,
    Popconfirm,
    Row,
    Select,
    Space,
    Switch,
    Tabs,
    Typography,
    message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, connect, history } from 'umi';

import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import {
    ComponentModuleStatusEnum,
    ComponentModuleStatusTabOptions,
    ComponentModuleTypeOptions,
} from '@/constants/componentManage';
import {
    deleteModule,
    getModulePageList,
    updateStatus,
} from '@/services/Marketing/ComponentManageApi';

const ListPage: React.FC<{
    dispatch?: any;
    global?: { codeInfo: Record<string, any> };
}> = ({ dispatch, global }) => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string | number>(
        ComponentModuleStatusTabOptions[0].key,
    );
    const [showModal, setShowModal] = useState<boolean>(false);
    const [relaActTypeParamList, setRelaActTypeParamList] = useState<any>();
    const [relaActTypeParam, setRelaActTypeParam] = useState<any>();
    const [addForm] = Form.useForm();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getModulePageList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: deleteRequest, loading: deleteLoading } = useRequest(
        (id) => {
            return deleteModule(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('删除成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
            onError: () => {
                message.error('删除失败');
            },
        },
    );

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (id) => {
            return updateStatus(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize });
    }, []);

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const getParams = (type = '') => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        if (currentTab && currentTab !== ComponentModuleStatusTabOptions[0].key) {
            params.moduleStatus = currentTab;
        }
        if (type !== 'resetForm') {
            params.relaActTypeParamList = relaActTypeParamList?.map((item: any) => {
                return {
                    relaActType: item.value,
                    relaActSubType: item.subType,
                };
            });
            delete params.relaActTypeList;
        }

        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams('resetForm'));
    };

    const refreshList = () => {
        searchList(pagination, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const confirmDelete = (id: number) => {
        deleteRequest(id);
    };

    const confirmChangeStatus = (id: number, status: number) => {
        changeStatusRequest(id);
    };

    const showAddModal = () => {
        setShowModal(true);
    };

    const columns: ColumnsType<API.ModuleQueryVo> = [
        {
            title: '组件模块ID',
            width: 120,
            dataIndex: 'moduleId',
            fixed: 'left',
        },
        {
            title: '组件模块名称',
            width: 140,
            dataIndex: 'moduleName',
            fixed: 'left',
        },
        {
            title: '组件模块类型',
            width: 140,
            dataIndex: 'relaActTypeName',
        },
        {
            title: '关联组件数量',
            width: 140,
            dataIndex: 'relaAssemblyNum',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createdTime',
        },
        {
            title: '所属平台',
            width: 120,
            dataIndex: 'cooperationPlatformName',
        },
        {
            title: '使用方',
            width: 120,
            dataIndex: 'userTypeName',
        },
        {
            title: '创建来源',
            width: 120,
            dataIndex: 'createSourceName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'moduleStatus',
            render(value: number, record: API.ModuleQueryVo) {
                return (
                    <Switch
                        checked={value === ComponentModuleStatusEnum.ENABLED}
                        checkedChildren="启动"
                        unCheckedChildren="关闭"
                        loading={changeStatusLoading}
                        onChange={() => {
                            confirmChangeStatus(
                                record?.moduleId as number,
                                value === ComponentModuleStatusEnum.ENABLED
                                    ? ComponentModuleStatusEnum.DISABLED
                                    : ComponentModuleStatusEnum.ENABLED,
                            );
                        }}
                    />
                );
            },
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'moduleId',
            sorter: false,
            fixed: 'right',
            render: (id, record: API.ModuleQueryVo) => {
                return (
                    <Space>
                        <Link to={`/marketing/component/module/detail/${id}`}>查看</Link>
                        <Link to={`/marketing/component/module/edit/${id}`}>编辑</Link>
                        {record?.moduleStatus === ComponentModuleStatusEnum.DISABLED && (
                            <Popconfirm
                                title="是否确认删除组件?"
                                onConfirm={() => confirmDelete(id)}
                                okButtonProps={{ loading: deleteLoading }}
                            >
                                <Typography.Link type="danger">删除</Typography.Link>
                            </Popconfirm>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                        <Col span={6}>
                            <Form.Item label="组件模块名称" name="moduleName">
                                <Input maxLength={16} allowClear placeholder="请输入组件模块名称" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label="组件模块ID" name="moduleId">
                                <Input maxLength={32} allowClear placeholder="请输入组件模块ID" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label="组件模块类型" name="relaActTypeList">
                                <Select
                                    options={ComponentModuleTypeOptions}
                                    // fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    allowClear
                                    mode="multiple"
                                    placeholder="请选择组件模块类型"
                                    onChange={(value: string, options) => {
                                        setRelaActTypeParamList(options);
                                    }}
                                />
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Button type="primary" icon="+" onClick={showAddModal}>
                            添加组件模块
                        </Button>
                    </Space>
                </div>
                <Tabs
                    defaultActiveKey={ComponentModuleStatusTabOptions[0].key as string}
                    onChange={changeTabType}
                >
                    {ComponentModuleStatusTabOptions.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="moduleId"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
            <Modal
                title="新增组件模块"
                visible={showModal}
                onCancel={() => {
                    addForm.resetFields();
                    setShowModal(false);
                }}
                destroyOnClose
                footer={false}
                width={420}
            >
                <Form
                    form={addForm}
                    onFinish={(data) => {
                        history.push(
                            `/marketing/component/module/add/${relaActTypeParam.value}?subType=${relaActTypeParam.subType}`,
                        );
                    }}
                    wrapperCol={{ span: 14, offset: 5 }}
                >
                    <Form.Item
                        name="type"
                        required
                        rules={[{ required: true, message: '请选择组件模块类型' }]}
                    >
                        <Select
                            options={ComponentModuleTypeOptions}
                            allowClear
                            showArrow
                            placeholder="请选择组件模块类型"
                            onChange={(value: string, options) => {
                                setRelaActTypeParam(options);
                            }}
                        />
                    </Form.Item>
                    <Form.Item>
                        <Row style={{ width: '100%', marginTop: '24px' }} justify="center">
                            <Button htmlType="submit" type="primary">
                                添加
                            </Button>
                        </Row>
                    </Form.Item>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global }: any) => ({ global }))(ListPage);
