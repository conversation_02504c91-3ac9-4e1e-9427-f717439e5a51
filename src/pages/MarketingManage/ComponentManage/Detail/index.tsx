import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Space, Typography } from 'antd';
import { useParams, history } from 'umi';

import ModuleDetail from '../components/ModuleDetail';

const DetailPage = () => {
    const params = useParams();
    const { id } = params as any;

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>查看组件模块</Typography.Title>
                </Space>
            }
        >
            <ModuleDetail id={id} />
        </PageHeaderWrapper>
    );
};

export default DetailPage;
