import { LeftOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { Space, Typography } from 'antd';
import { useEffect } from 'react';
import { useParams, history } from 'umi';

import { getModule } from '@/services/Marketing/ComponentManageApi';
import ModuleForm from '../components/ModuleForm';
import { getTaskPackageDetail } from '@/services/Marketing/MngEventApi';
import { ComponentModuleTypeEnum } from '@/constants/componentManage';

const EditPage = () => {
    const params = useParams();
    const { id } = params as any;

    const { run, loading, data } = useRequest(
        (id) => {
            return getModule(id);
        },
        {
            manual: true,
        },
    );

    const {
        run: queryTask,
        loading: taskLoading,
        data: taskData,
    } = useRequest(
        (id) => {
            return getTaskPackageDetail(id);
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    useEffect(() => {
        if (
            [ComponentModuleTypeEnum.TASK, ComponentModuleTypeEnum.RECHARGE].includes(
                data?.data?.relaActType,
            )
        ) {
            queryTask(data?.data?.relaActId);
        }
    }, [data?.data?.relaActType, data?.data?.relaActId]);

    return (
        <PageHeaderWrapper
            loading={loading || taskLoading}
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>编辑组件模块</Typography.Title>
                </Space>
            }
        >
            <ProCard>
                {data?.data && (
                    <ModuleForm
                        method="EDIT"
                        type={data?.data?.relaActType as string}
                        subType={data?.data?.relaActSubType as string}
                        initialValue={data?.data}
                        relTaskPackage={taskData?.data}
                    />
                )}
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default EditPage;
