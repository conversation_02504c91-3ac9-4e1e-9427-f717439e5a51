import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import ProCard from '@ant-design/pro-card';
import { Space, Typography } from 'antd';
import { useParams, history, useLocation } from 'umi';

import ModuleForm from '../components/ModuleForm';

const AddPage = () => {
    const params = useParams();
    const location: any = useLocation();
    const {
        query: { subType },
    }: { query: { subType: string } } = location;
    const { type } = params as any;

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>添加组件模块</Typography.Title>
                </Space>
            }
        >
            <ProCard>
                <ModuleForm method="ADD" type={type} subType={subType} />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AddPage;
