import { usePagination } from 'ahooks';
import { Col, Form, Input, Modal, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useImperativeHandle, useState } from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { getVipPlanSignList } from '@/services/Marketing/MngBilApi';

interface Props {
    onSelect: (record: any) => void;
}

const SignSelectModal = (props: Props, ref: any) => {
    const { onSelect } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [cooperationPlatform, setCooperationPlatform] = useState<string>('');

    useImperativeHandle(ref, () => {
        return {
            open: (cooperationPlatform: string) => {
                setVisible(true);
                setCooperationPlatform(cooperationPlatform);
                onFinish({ cooperationPlatform });
            },
        };
    });

    const onCancel = () => {
        form.resetFields();
        mutate(undefined);
        setSelectedRowKeys([]);
        setSelectedRows([]);
        setVisible(false);
    };

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        mutate,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getVipPlanSignList({
                cooperationPlatform: cooperationPlatform,
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const onFinish = (formData: any) => {
        searchList(pagination, { ...formData });
    };
    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, {});
    };

    const onConfirmSelect = () => {
        if (selectedRows && selectedRows.length > 0) {
            if (onSelect) {
                onSelect(selectedRows[0]);
            }
            onCancel();
        } else {
            message.error('请选择要关联的会员方案');
        }
    };

    const columns: ColumnsType<API.TaskPackageQueryVo> = [
        {
            title: '方案ID',
            width: 120,
            dataIndex: 'actId',
        },
        {
            title: '方案名称',
            width: 120,
            dataIndex: 'actName',
        },
        {
            title: '权益名称',
            width: 120,
            dataIndex: 'gainName',
        },
        {
            title: '关联活动ID',
            width: 120,
            dataIndex: 'signId',
        },
        {
            title: '关联活动名称',
            width: 120,
            dataIndex: 'signName',
        },
        {
            title: '更新时间',
            width: 120,
            dataIndex: 'dataOperTime',
        },
        {
            title: '创建人',
            width: 80,
            dataIndex: 'creEmp',
        },
        {
            title: '状态',
            width: 80,
            dataIndex: 'actStatus',
        },
    ];

    return (
        <Modal
            title="选择关联会员方案"
            destroyOnClose
            onCancel={onCancel}
            onOk={onConfirmSelect}
            visible={visible}
            width={1080}
        >
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                    <Col span={8}>
                        <Form.Item label="方案名称" name="actName">
                            <Input maxLength={16} allowClear placeholder="请输入方案名称" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="方案ID" name="actId">
                            <Input maxLength={32} allowClear placeholder="请输入方案ID" />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="actId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                rowSelection={{
                    fixed: true,
                    onChange: (newSelectedRowKeys: any[], newSelectedRows: any[]) => {
                        console.log(
                            'selectedRowKeys changed: ',
                            newSelectedRowKeys,
                            newSelectedRows,
                        );
                        setSelectedRowKeys(newSelectedRowKeys);
                        setSelectedRows(newSelectedRows);
                    },
                    selectedRowKeys,
                    type: 'radio',
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Modal>
    );
};

export default React.forwardRef(SignSelectModal);
