import {
    Form,
    Input,
    Radio,
    Typography,
    Collapse,
    Space,
    Tag,
    Divider,
    Card,
    Checkbox,
} from 'antd';
import type { FormInstance } from 'antd/es/form';
import React from 'react';
import { Link } from 'umi';

import ProgressFormItem, { checkProgressItem } from './ProgressFormItem';

import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import {
    ComponentModuleTypeEnum,
    MODULE_BTN_CLICK_ENUM,
    MODULE_BTN_SHOW_ENUM,
    MODULE_BTN_STATUS_ENUM,
    TASK_REWARD_TYPE_ENUM,
    moduleBtnTitleEnumWhenMerge,
    moduleBtnTitleEnumWhenOneself,
    moduleTaskTitleEnumWhenMerge,
    moduleTaskTitleEnumWhenOneself,
} from '@/constants/componentManage';

const { Panel } = Collapse;

enum RECHARGE_MODULE_TYPES {
    MAIN = 'main',
    PRIZE = 'prize',
    STYLE = 'style',
}

const RechargeForm: React.FC<{
    //新增任务包类型
    type: string;
    //FORM表单对象
    form: FormInstance;
    //初始化数据，编辑或者有复制功能使用
    initialValue?: API.ModuleQueryVo;
    //关联任务包
    relativeAct: API.TaskPackageQueryVo;
    //主模块按钮配置
    mainBtnConfList: API.ModuleBtnConfParam[];
    //任务配置
    moduleTaskConfParamList: API.ModuleTaskConfParam[];
    changeModuleTaskConfParamList?: (taskId: string, info: API.ModuleBtnConfParam) => void;
}> = ({
    type,
    relativeAct,
    form,
    initialValue,
    mainBtnConfList,
    moduleTaskConfParamList,
    changeModuleTaskConfParamList,
}) => {
    const updateTaskInfo = (taskId: string, info: API.ModuleBtnConfParam) => {
        changeModuleTaskConfParamList && changeModuleTaskConfParamList(taskId, info);
    };
    return (
        <>
            {/* 任务包 展示性字段 */}

            {/* 任务包 配置性字段 */}
            <Typography.Title level={4}>展示和交互配置</Typography.Title>
            <Form.Item
                name="configModule"
                initialValue={RECHARGE_MODULE_TYPES.MAIN}
                validateTrigger="submit"
                rules={[
                    {
                        validator(rule, value, callback) {
                            try {
                                moduleTaskConfParamList?.forEach((task, index) => {
                                    if (!task.title) {
                                        throw new Error(`请配置任务${index}标题`);
                                    }
                                    if (!task.prizeName) {
                                        throw new Error(`请配置任务${task.title}奖品名称`);
                                    }
                                    if (!task.prizeImage) {
                                        throw new Error(`请配置任务${task.title}奖品图片`);
                                    }
                                });
                            } catch (error) {
                                return Promise.reject('请完成任务条件配置');
                            }
                            // if (value === RECHARGE_MODULE_TYPES.MAIN) {
                            //     try {
                            //         moduleTaskConfParamList?.forEach((task) => {
                            //             if (!task.title) {
                            //                 throw new Error('请完成任务条件配置');
                            //             } else {
                            //                 task.moduleBtnConfParamList?.forEach((btn) => {
                            //                     if (
                            //                         btn.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW &&
                            //                         btn.btnClickFlag ===
                            //                             MODULE_BTN_CLICK_ENUM.ENABLE &&
                            //                         !btn.btnPath
                            //                     ) {
                            //                         throw new Error('请完成任务条件配置');
                            //                     }
                            //                 });
                            //             }
                            //         });
                            //     } catch (error) {
                            //         return Promise.reject('请完成任务条件配置');
                            //     }
                            //     return Promise.resolve();
                            // } else {
                            //     if (type === ComponentModuleTypeEnum.TASK) {
                            //         const title = form.getFieldValue('title');
                            //         const prizeImage = form.getFieldValue('prizeImage');
                            //         if (!title || !prizeImage) {
                            //             return Promise.reject('请完成主模块配置');
                            //         }
                            //         if (relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
                            //             const prizeName = form.getFieldValue('prizeName');
                            //             if (!prizeName) {
                            //                 return Promise.reject('请完成主模块配置');
                            //             }
                            //         }
                            //     }
                            // }
                            return Promise.resolve();
                        },
                    },
                ]}
            >
                <Radio.Group
                    buttonStyle="solid"
                    defaultValue={RECHARGE_MODULE_TYPES.MAIN}
                    size="large"
                    style={{ marginTop: 16 }}
                >
                    <Radio.Button value={RECHARGE_MODULE_TYPES.MAIN}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            主模块配置
                        </Space>
                    </Radio.Button>

                    <Radio.Button value={RECHARGE_MODULE_TYPES.PRIZE}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            奖品配置
                        </Space>
                    </Radio.Button>

                    <Radio.Button value={RECHARGE_MODULE_TYPES.STYLE}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            样式和风格配置
                        </Space>
                    </Radio.Button>
                </Radio.Group>
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(prev, cur) => prev.configModule !== cur.configModule}>
                {({ getFieldValue }) => {
                    const configModule = getFieldValue('configModule');
                    return (
                        <>
                            <Form.Item hidden={configModule !== RECHARGE_MODULE_TYPES.MAIN} noStyle>
                                <Divider orientation="left" dashed orientationMargin={20}>
                                    标题
                                </Divider>
                                <Form.Item
                                    name="title"
                                    label="标题名称"
                                    required
                                    rules={[
                                        {
                                            required: true,
                                            whitespace: true,
                                            message: '请输入标题',
                                        },
                                    ]}
                                    wrapperCol={{ span: 8 }}
                                >
                                    <Input
                                        maxLength={16}
                                        showCount
                                        allowClear
                                        placeholder="请输入标题"
                                    />
                                </Form.Item>

                                <Divider orientation="left" dashed orientationMargin={20}>
                                    卡片
                                </Divider>

                                <Form.Item label="动作">
                                    <Tag color="blue">点击时跳转</Tag>
                                </Form.Item>

                                <Form.Item
                                    name={['cardBtn', 'btnPath']}
                                    label="跳转链接"
                                    // rules={[
                                    //     {
                                    //         required: true,
                                    //         whitespace: true,
                                    //         message: '请输入跳转链接',
                                    //     },
                                    // ]}
                                >
                                    <Input
                                        maxLength={500}
                                        placeholder="请输入内部跳转链接，示例：/pagesPaymember/paymember/pay"
                                        showCount
                                        allowClear
                                    />
                                </Form.Item>

                                <Divider orientation="left" dashed orientationMargin={20}>
                                    按钮
                                </Divider>

                                <Collapse>
                                    {mainBtnConfList?.map((conf: API.ModuleBtnConfParam) => {
                                        return (
                                            <Panel
                                                header={
                                                    relativeAct?.rewardType ===
                                                    TASK_REWARD_TYPE_ENUM.MERGE
                                                        ? moduleBtnTitleEnumWhenMerge[
                                                              conf.status as string
                                                          ]
                                                        : moduleBtnTitleEnumWhenOneself[
                                                              conf.status as string
                                                          ] || conf.status
                                                }
                                                key={`${conf.status}`}
                                                forceRender
                                            >
                                                <Form.Item label="展示按钮" required>
                                                    {conf.btnShowFlag ===
                                                    MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                        <Tag color="success">展示按钮</Tag>
                                                    ) : (
                                                        <Tag color="error">不展示按钮</Tag>
                                                    )}
                                                </Form.Item>
                                                {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                    <Form.Item label="按钮文案" required>
                                                        <Tag>{conf.btnDesc}</Tag>
                                                    </Form.Item>
                                                )}
                                                {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                    <Form.Item label="按钮操作" required>
                                                        <Space>
                                                            {conf.btnClickFlag ===
                                                            MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                <Tag color="success">
                                                                    按钮可点击
                                                                </Tag>
                                                            ) : (
                                                                <Tag color="error">
                                                                    按钮不可点击
                                                                </Tag>
                                                            )}
                                                            {conf.btnClickFlag ===
                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                conf.status ===
                                                                    MODULE_BTN_STATUS_ENUM.UNSTART && (
                                                                    <Typography.Text type="secondary">
                                                                        点击按钮时触发领取任务
                                                                    </Typography.Text>
                                                                )}
                                                            {conf.btnClickFlag ===
                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                conf.status ===
                                                                    MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                    <Typography.Text type="secondary">
                                                                        点击按钮时触发发奖
                                                                    </Typography.Text>
                                                                )}
                                                        </Space>
                                                    </Form.Item>
                                                )}
                                            </Panel>
                                        );
                                    })}
                                </Collapse>

                                {relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE && (
                                    <>
                                        <Divider orientation="left" dashed orientationMargin={20}>
                                            奖品
                                        </Divider>
                                        <Form.Item
                                            label="奖品名称"
                                            name={'prizeName'}
                                            required
                                            wrapperCol={{ span: 8 }}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请输入奖品名称',
                                                },
                                            ]}
                                        >
                                            <Input
                                                maxLength={8}
                                                showCount
                                                allowClear
                                                placeholder="请输入奖品名称"
                                            />
                                        </Form.Item>
                                        <Form.Item
                                            name={'prizeImage'}
                                            label="奖品图片"
                                            required
                                            wrapperCol={{ span: 8 }}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请配置奖品图片',
                                                },
                                            ]}
                                        >
                                            <UpLoadImgItem
                                                sizeInfo={{
                                                    width: 100,
                                                    height: 100,
                                                    size: 100,
                                                }}
                                                multiple={false}
                                                placeholder="格式支持png、jpg、jpeg,大小100px * 100px, 不得超过100KB"
                                                uploadData={{
                                                    contentType: '02',
                                                    contRemrk: 'iconUrl',
                                                    relaTable: 'e_mkt_act',
                                                }}
                                            />
                                        </Form.Item>
                                    </>
                                )}

                                <Divider orientation="left" dashed orientationMargin={20}>
                                    任务进度
                                </Divider>
                                <Form.Item
                                    name="moduleTaskProgress"
                                    noStyle
                                    rules={[
                                        {
                                            validator(rule, value, callback) {
                                                const errMsg = checkProgressItem(value);
                                                if (errMsg) {
                                                    return Promise.reject(errMsg);
                                                }
                                                return Promise.resolve();
                                            },
                                        },
                                    ]}
                                    initialValue={{
                                        taskId: '',
                                        showProgressFlag: 0,
                                        showContent: '',
                                    }}
                                >
                                    <ProgressFormItem
                                        taskList={relativeAct?.taskList}
                                    ></ProgressFormItem>
                                </Form.Item>
                            </Form.Item>
                            <Form.Item
                                hidden={configModule !== RECHARGE_MODULE_TYPES.PRIZE}
                                noStyle
                            >
                                <Collapse
                                    defaultActiveKey={moduleTaskConfParamList?.map(
                                        (v) => `${v?.taskId}`,
                                    )}
                                >
                                    {moduleTaskConfParamList?.map((task, index) => {
                                        return (
                                            <Panel
                                                header={`条件：${task?.taskName}（${task?.taskId}）`}
                                                key={`${task?.taskId}`}
                                                extra={
                                                    <Link
                                                        target="_blank"
                                                        to={`/marketing/event/task/list/detail?id=${task?.taskId}`}
                                                    >
                                                        详情
                                                    </Link>
                                                }
                                                forceRender
                                            >
                                                <Divider orientation="left">
                                                    <Typography.Title level={5}>
                                                        标题
                                                    </Typography.Title>
                                                </Divider>

                                                <Form.Item
                                                    label="标题名称"
                                                    required
                                                    wrapperCol={{ span: 8 }}
                                                    help={
                                                        !task.title && (
                                                            <Typography.Text type="danger">
                                                                请输入奖品名称
                                                            </Typography.Text>
                                                        )
                                                    }
                                                    validateStatus={!task.title ? 'error' : ''}
                                                >
                                                    <Input
                                                        value={task.title}
                                                        maxLength={5}
                                                        showCount
                                                        allowClear
                                                        placeholder="请输入标题"
                                                        onChange={(event) => {
                                                            const {
                                                                target: { value: newVal },
                                                            } = event;

                                                            updateTaskInfo(task?.taskId, {
                                                                ...task,
                                                                title: newVal,
                                                            });
                                                        }}
                                                    />
                                                </Form.Item>
                                                <Divider orientation="left">
                                                    <Typography.Title level={5}>
                                                        奖品
                                                    </Typography.Title>
                                                </Divider>

                                                <Form.Item
                                                    label="奖品名称"
                                                    required
                                                    wrapperCol={{ span: 8 }}
                                                    help={
                                                        !task.prizeName && (
                                                            <Typography.Text type="danger">
                                                                请输入奖品名称
                                                            </Typography.Text>
                                                        )
                                                    }
                                                    validateStatus={!task.prizeName ? 'error' : ''}
                                                >
                                                    <Input
                                                        value={task.prizeName}
                                                        maxLength={5}
                                                        showCount
                                                        allowClear
                                                        placeholder="请输入奖品名称"
                                                        onChange={(event) => {
                                                            const {
                                                                target: { value: newVal },
                                                            } = event;

                                                            updateTaskInfo(task?.taskId, {
                                                                ...task,
                                                                prizeName: newVal,
                                                            });
                                                        }}
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="奖品图片"
                                                    required
                                                    wrapperCol={{ span: 8 }}
                                                    help={
                                                        !task.prizeImage && (
                                                            <Typography.Text type="danger">
                                                                请上传奖品图片
                                                            </Typography.Text>
                                                        )
                                                    }
                                                    validateStatus={!task.prizeImage ? 'error' : ''}
                                                >
                                                    <UpLoadImgItem
                                                        value={task.prizeImage}
                                                        sizeInfo={{
                                                            width: 100,
                                                            height: 100,
                                                            size: 100,
                                                        }}
                                                        multiple={false}
                                                        placeholder="格式支持png、jpg、jpeg,大小100px * 100px, 不得超过100KB"
                                                        uploadData={{
                                                            contentType: '02',
                                                            contRemrk: 'iconUrl',
                                                            relaTable: 'e_mkt_act',
                                                        }}
                                                        onChange={(newVal) => {
                                                            updateTaskInfo(task?.taskId, {
                                                                ...task,
                                                                prizeImage: newVal,
                                                            });
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Panel>
                                        );
                                    })}
                                </Collapse>
                            </Form.Item>
                            <Form.Item
                                hidden={configModule !== RECHARGE_MODULE_TYPES.STYLE}
                                noStyle
                            >
                                <Form.Item
                                    label="风格"
                                    name="styleType"
                                    rules={[
                                        {
                                            required: true,

                                            message: '请选择风格',
                                        },
                                    ]}
                                    initialValue={'01'}
                                >
                                    <Radio.Group>
                                        <Radio value={'02'}>新人冲单</Radio>
                                        <Radio value={'01'}>日常冲单活动</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Form.Item>
                        </>
                    );
                }}
            </Form.Item>
        </>
    );
};

export default RechargeForm;
