import { MODULE_BTN_STATUS_ENUM, ComponentModuleTypeEnum } from '@/constants/componentManage';
import ProCard from '@ant-design/pro-card';
import moment from 'moment';
import React, { useMemo } from 'react';
import LayoutPreview from '@/components/LayoutPreview';

const PreviewModuleConf: React.FC<{
    moduleConf: any;
    relativeAct: any;
    type: any;
    subType: any;
}> = ({ moduleConf, relativeAct, type, subType }) => {
    console.debug('PreviewModuleConf params ==>', {
        moduleConf,
        relativeAct,
        type,
        subType,
    });
    const conf = useMemo(() => {
        console.error('previewModuleObj==>', moduleConf);
        let relaActType = relativeAct?.relaActType || type;
        if (relaActType == ComponentModuleTypeEnum.RECHARGE) {
            relaActType = ComponentModuleTypeEnum.TASK;
        }
        console.log(321321, moduleConf);
        return moduleConf
            ? {
                  assemblyStyle:
                      relaActType == ComponentModuleTypeEnum.STATION_RECOMMEND ? 'act' : 'default',
                  moduleQueryVoList: [
                      {
                          mainModuleConfQueryVo: {
                              currModuleBtnConfQueryVo:
                                  moduleConf?.mainModuleConfParam?.moduleBtnConfParamList?.find(
                                      (v: any) => v.status === MODULE_BTN_STATUS_ENUM.UNSTART,
                                  ),
                              prizeImage: moduleConf?.mainModuleConfParam?.prizeImage,
                              prizeImagePath: moduleConf?.mainModuleConfParam?.prizeImage,
                              prizeName: moduleConf?.mainModuleConfParam?.prizeName,
                              title: moduleConf?.mainModuleConfParam?.title,
                              subTitle: moduleConf?.mainModuleConfParam?.subTitle,
                              styleType: moduleConf?.mainModuleConfParam?.styleType,
                          },
                          moduleActInfo: {
                              actProgressStatus:
                                  relativeAct?.relaActType === ComponentModuleTypeEnum.REWARD ||
                                  type === ComponentModuleTypeEnum.REWARD ||
                                  relativeAct?.relaActType === ComponentModuleTypeEnum.EQUITY ||
                                  type === ComponentModuleTypeEnum.EQUITY
                                      ? MODULE_BTN_STATUS_ENUM.UNRECEIVE
                                      : MODULE_BTN_STATUS_ENUM.UNSTART,
                              effTime: relativeAct?.effTime
                                  ? moment(relativeAct?.effTime).format('YYYY-MM-DD HH:mm:ss')
                                  : undefined,
                              expTime: relativeAct?.expTime
                                  ? moment(relativeAct?.expTime).format('YYYY-MM-DD HH:mm:ss')
                                  : undefined,
                              moduleActTaskInfoList: moduleConf?.moduleTaskConfParamList?.map(
                                  (v: any) => {
                                      let moduleTaskProgressInfo = {};
                                      if (
                                          moduleConf?.mainModuleConfParam?.moduleTaskProgress &&
                                          v?.taskId ==
                                              moduleConf?.mainModuleConfParam?.moduleTaskProgress
                                                  ?.taskId
                                      ) {
                                          moduleTaskProgressInfo =
                                              moduleConf?.mainModuleConfParam?.moduleTaskProgress;
                                      }
                                      return {
                                          currTaskConfQueryVo: {
                                              ...moduleTaskProgressInfo,
                                              currTaskBtnConf: v?.moduleBtnConfParamList?.find(
                                                  (btn: any) =>
                                                      btn.status === MODULE_BTN_STATUS_ENUM.UNSTART,
                                              ),
                                              taskId: v?.taskId,
                                              prizeName: v?.prizeName,

                                              title: v?.title,
                                          },
                                          taskId: v?.taskId,
                                          taskName: v?.taskName,
                                          taskProgressStatus: MODULE_BTN_STATUS_ENUM.UNSTART,
                                      };
                                  },
                              ),
                              rewardType: relativeAct?.rewardType,
                          },
                          relaActId: relativeAct?.relaActId,
                          relaActType: relaActType,
                          relaActSubType: relativeAct?.relaActSubType || subType,
                          stationQueryVos: moduleConf?.stationQueryVos?.slice(0, 1),
                      },
                  ],
              }
            : {};
    }, [moduleConf]);
    console.log('preview conf ==>', conf);
    window.sessionStorage.setItem('xdt_assemblyInfo', JSON.stringify(conf));
    return (
        <LayoutPreview
            layouts={[
                <iframe
                    key="index"
                    src={`${
                        window.location.origin
                    }${PUBLIC_PATH}preview/index.html#/?timestamp=${new Date().getTime()}`}
                    style={{
                        border: 0,
                        width: '445px',
                        minHeight: '466px',
                        backgroundColor: '#efefef',
                    }}
                ></iframe>,
            ]}
        ></LayoutPreview>
        // <div
        //     style={{
        //         width: '493px',
        //         minHeight: '466px',
        //         height: 'auto',
        //         position: 'absolute',
        //         top: '0px',
        //         right: '0px',
        //     }}
        // >
        //     <ProCard title="预览" bordered>
        //         <iframe
        //             src={`${
        //                 window.location.origin
        //             }${PUBLIC_PATH}preview/index.html#/?timestamp=${new Date().getTime()}`}
        //             style={{
        //                 border: 0,
        //                 width: '445px',
        //                 minHeight: '466px',
        //                 backgroundColor: '#efefef',
        //             }}
        //         ></iframe>
        //     </ProCard>
        // </div>
    );
};

export default React.memo(PreviewModuleConf);
