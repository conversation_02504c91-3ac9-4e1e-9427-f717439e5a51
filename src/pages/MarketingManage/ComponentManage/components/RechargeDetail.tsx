import { Collapse, Descriptions, Image, Radio, Space, Tag, Typography, Form } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'umi';
import ShowPicture from '@/components/ShowPicture';

import {
    ComponentModuleTypeEnum,
    MODULE_BTN_CLICK_ENUM,
    MODULE_BTN_SHOW_ENUM,
    MODULE_BTN_STATUS_ENUM,
    TASK_REWARD_TYPE_ENUM,
    moduleBtnTitleEnumWhenMerge,
    moduleBtnTitleEnumWhenOneself,
    moduleTaskTitleEnumWhenMerge,
    moduleTaskTitleEnumWhenOneself,
} from '@/constants/componentManage';
const { Panel } = Collapse;
enum RECHARGE_MODULE_TYPES {
    MAIN = 'main',
    PRIZE = 'prize',
    STYLE = 'style',
}
const RechargeDetail = (props: any) => {
    const { info, taskInfo } = props;
    const [selectTab, setSelectTab] = useState<RECHARGE_MODULE_TYPES>(RECHARGE_MODULE_TYPES.MAIN);

    const styleTypeName = useMemo(() => {
        let resultName = '';

        if (info?.mainModuleConfQueryVo) {
            switch (info?.mainModuleConfQueryVo?.styleType) {
                case '01':
                    resultName = '日常冲单活动';
                    break;
                case '02':
                    resultName = '新人冲单';
                    break;

                default:
                    break;
            }
        }
        return resultName;
    }, [info]);
    const taskResult = useMemo(() => {
        let result = '';
        const moduleTaskProgress = info?.mainModuleConfQueryVo?.moduleTaskProgress;
        if (moduleTaskProgress && taskInfo?.taskList instanceof Array) {
            for (const element of taskInfo?.taskList) {
                if (element.taskId == moduleTaskProgress.taskId) {
                    result = `${element.taskName}(${element.taskId})`;
                    break;
                }
            }
        }

        return result;
    }, [info, taskInfo]);

    const getTaskName = (taskId: string) => {
        let result = '';

        if (taskInfo?.taskList instanceof Array) {
            for (const element of taskInfo?.taskList) {
                if (element.taskId == taskId) {
                    result = `${element.taskName}(${element.taskId})`;
                    break;
                }
            }
        }
        return result;
    };
    return (
        <>
            <p>
                <Radio.Group
                    buttonStyle="solid"
                    defaultValue={RECHARGE_MODULE_TYPES.MAIN}
                    size="large"
                    style={{ marginTop: 16 }}
                    onChange={(event) => {
                        const {
                            target: { value: newVal },
                        } = event;
                        setSelectTab(newVal);
                    }}
                >
                    <Radio.Button value={RECHARGE_MODULE_TYPES.MAIN}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            主模块配置
                        </Space>
                    </Radio.Button>

                    <Radio.Button value={RECHARGE_MODULE_TYPES.PRIZE}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            奖品配置
                        </Space>
                    </Radio.Button>

                    <Radio.Button value={RECHARGE_MODULE_TYPES.STYLE}>
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            样式和风格配置
                        </Space>
                    </Radio.Button>
                </Radio.Group>
            </p>
            <Form.Item hidden={selectTab !== RECHARGE_MODULE_TYPES.MAIN} noStyle>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <Typography.Title level={5}>主模块配置</Typography.Title>
                    <Descriptions column={1}>
                        <Descriptions.Item label="标题名称">
                            {info?.mainModuleConfQueryVo?.title}
                        </Descriptions.Item>
                    </Descriptions>
                    <Typography.Title level={5}>卡片</Typography.Title>
                    <Descriptions column={1}>
                        <Descriptions.Item label="动作">点击时跳转</Descriptions.Item>
                        <Descriptions.Item label="跳转链接">
                            {info?.cardBtn?.btnPath || '暂无配置'}
                        </Descriptions.Item>
                    </Descriptions>
                    {taskInfo?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE && (
                        <>
                            <Typography.Title level={5}>主模块奖品展示配置</Typography.Title>
                            <Descriptions column={1}>
                                <Descriptions.Item label="奖品图片">
                                    <Image src={info?.mainModuleConfQueryVo?.prizeImagePath} />
                                </Descriptions.Item>

                                <Descriptions.Item label="奖品名称">
                                    {info?.mainModuleConfQueryVo?.prizeName}
                                </Descriptions.Item>

                                <Descriptions.Item label="奖品说明">
                                    {info?.mainModuleConfQueryVo?.prizeRemark}
                                </Descriptions.Item>
                            </Descriptions>
                        </>
                    )}
                    <Typography.Title level={5}>主模块按钮配置</Typography.Title>
                    <Collapse>
                        {info?.mainModuleConfQueryVo?.moduleBtnConfQueryVos?.map(
                            (conf: API.ModuleBtnConfParam) => {
                                return (
                                    <Panel
                                        header={
                                            taskInfo?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE
                                                ? moduleBtnTitleEnumWhenMerge[conf.status as string]
                                                : moduleBtnTitleEnumWhenOneself[
                                                      conf.status as string
                                                  ] || conf.status
                                        }
                                        key={`${conf.status}`}
                                        forceRender
                                    >
                                        <Descriptions column={1}>
                                            <Descriptions.Item label="展示按钮">
                                                {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                    <Tag color="success">展示按钮</Tag>
                                                ) : (
                                                    <Tag color="error">不展示按钮</Tag>
                                                )}
                                            </Descriptions.Item>
                                            {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                <Descriptions.Item label="按钮文案">
                                                    <Tag>{conf.btnDesc}</Tag>
                                                </Descriptions.Item>
                                            )}
                                            {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                <Descriptions.Item label="按钮操作">
                                                    <Space>
                                                        {conf.btnClickFlag ===
                                                        MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                            <Tag color="success">按钮可点击</Tag>
                                                        ) : (
                                                            <Tag color="error">按钮不可点击</Tag>
                                                        )}
                                                        {conf.btnClickFlag ===
                                                            MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                            conf.status ===
                                                                MODULE_BTN_STATUS_ENUM.UNSTART && (
                                                                <Typography.Text type="secondary">
                                                                    点击按钮时触发领取任务
                                                                </Typography.Text>
                                                            )}
                                                        {conf.btnClickFlag ===
                                                            MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                            conf.status ===
                                                                MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                <Typography.Text type="secondary">
                                                                    点击按钮时触发发奖
                                                                </Typography.Text>
                                                            )}
                                                    </Space>
                                                </Descriptions.Item>
                                            )}
                                        </Descriptions>
                                    </Panel>
                                );
                            },
                        )}
                    </Collapse>
                    <Typography.Title level={5}>任务进度</Typography.Title>
                    <Descriptions column={1}>
                        <Descriptions.Item label="是否展示">
                            {info?.mainModuleConfQueryVo?.moduleTaskProgress?.showProgressFlag == 1
                                ? '展示'
                                : '不展示'}
                        </Descriptions.Item>
                        <Descriptions.Item label="关联任务">
                            {info?.mainModuleConfQueryVo?.moduleTaskProgress?.taskId}

                            <Link
                                target="_blank"
                                to={`/marketing/event/task/list/detail?id=${info?.mainModuleConfQueryVo?.moduleTaskProgress?.taskId}`}
                            >
                                {taskResult}
                            </Link>
                        </Descriptions.Item>
                        <Descriptions.Item label="展示文案">
                            {info?.mainModuleConfQueryVo?.moduleTaskProgress?.showContent}
                        </Descriptions.Item>
                    </Descriptions>
                </Space>
            </Form.Item>
            <Form.Item hidden={selectTab !== RECHARGE_MODULE_TYPES.PRIZE} noStyle>
                <Collapse
                    defaultActiveKey={info?.moduleTaskConfQueryVoList?.map((v) => `${v?.taskId}`)}
                >
                    {info?.moduleTaskConfQueryVoList?.map((task) => {
                        return (
                            <Panel
                                header={`条件：${getTaskName(task.taskId)}`}
                                key={`${task?.taskId}`}
                                extra={
                                    <Link
                                        target="_blank"
                                        to={`/marketing/event/task/list/detail?id=${task?.taskId}`}
                                    >
                                        详情
                                    </Link>
                                }
                                forceRender
                            >
                                <Descriptions column={1}>
                                    <Descriptions.Item label="标题">
                                        {task?.title}
                                    </Descriptions.Item>

                                    <Descriptions.Item label="奖品名称">
                                        {task?.prizeName}
                                    </Descriptions.Item>
                                    <Descriptions.Item label="奖品图片">
                                        <ShowPicture url={task?.prizeImage}></ShowPicture>
                                    </Descriptions.Item>
                                </Descriptions>
                                {task.moduleBtnConfQueryVoList?.length > 0 && (
                                    <Collapse>
                                        {task.moduleBtnConfQueryVoList?.map((btn) => {
                                            return (
                                                <Panel
                                                    header={
                                                        taskInfo?.rewardType ===
                                                        TASK_REWARD_TYPE_ENUM.MERGE
                                                            ? moduleTaskTitleEnumWhenMerge[
                                                                  btn.status as string
                                                              ]
                                                            : moduleTaskTitleEnumWhenOneself[
                                                                  btn.status as string
                                                              ] || btn.status
                                                    }
                                                    key={`${btn.status}`}
                                                    forceRender
                                                >
                                                    <Descriptions column={1}>
                                                        <Descriptions.Item label="展示按钮">
                                                            {btn.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                                <Tag color="success">展示按钮</Tag>
                                                            ) : (
                                                                <Tag color="error">不展示按钮</Tag>
                                                            )}
                                                        </Descriptions.Item>
                                                        {btn.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW && (
                                                            <Descriptions.Item label="按钮文案">
                                                                <Tag>{btn.btnDesc}</Tag>
                                                            </Descriptions.Item>
                                                        )}
                                                        {btn.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW && (
                                                            <Descriptions.Item label="按钮操作">
                                                                <Space>
                                                                    {btn.btnClickFlag ===
                                                                    MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                        <Tag color="success">
                                                                            按钮可点击
                                                                        </Tag>
                                                                    ) : (
                                                                        <Tag color="error">
                                                                            按钮不可点击
                                                                        </Tag>
                                                                    )}
                                                                    {btn.btnClickFlag ===
                                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                        btn.status ===
                                                                            MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                            <Typography.Text type="secondary">
                                                                                点击按钮时跳转页面
                                                                            </Typography.Text>
                                                                        )}
                                                                    {btn.btnClickFlag ===
                                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                        btn.status ===
                                                                            MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                            <Typography.Text type="secondary">
                                                                                点击按钮发放奖励
                                                                            </Typography.Text>
                                                                        )}
                                                                </Space>
                                                            </Descriptions.Item>
                                                        )}
                                                        {btn.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW &&
                                                            btn.btnClickFlag ===
                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                            btn.status ===
                                                                MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                <Descriptions.Item label="跳转链接">
                                                                    {btn?.btnPath}
                                                                </Descriptions.Item>
                                                            )}
                                                    </Descriptions>
                                                </Panel>
                                            );
                                        })}
                                    </Collapse>
                                )}
                            </Panel>
                        );
                    })}
                </Collapse>
            </Form.Item>
            <Form.Item hidden={selectTab !== RECHARGE_MODULE_TYPES.STYLE} noStyle>
                <Descriptions column={1}>
                    <Descriptions.Item label="风格">{styleTypeName}</Descriptions.Item>
                </Descriptions>
            </Form.Item>
        </>
    );
};
export default RechargeDetail;
