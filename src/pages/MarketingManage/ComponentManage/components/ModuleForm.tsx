import { useRequest } from 'ahooks';
import { Button, Form, Input, Radio, Row, Space, Typography, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { Link, history } from 'umi';

import TaskForm from './TaskForm';
import RechargeForm from './RechargeForm';
import TaskSelectModal from './TaskSelectModal';
import ChargeSelectModal from './ChargeSelectModal';

import RewardSelectModal from './RewardSelectModal';
import EquitySelectModal from './EquitySelectModal';
import {
    ComponentModuleTypeEnum,
    MODULE_BTN_CLICK_ENUM,
    MODULE_BTN_SHOW_ENUM,
    MODULE_BTN_STATUS_ENUM,
    TASK_OBTAIN_TYPE_ENUM,
    TASK_OPEN_TYPE_ENUM,
    TASK_REWARD_TYPE_ENUM,
} from '@/constants/componentManage';
import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Radio';
import { addModule } from '@/services/Marketing/ComponentManageApi';
import SignSelectModal from './SignSelectModal';
import PreviewModuleConf from './PreviewConf';
import StationRecommendForm from './StationRecommendForm';
import { isEmpty } from '@/utils/utils';
import { ACTSUBTYPES, COOPERATION_PLATFORM_TYPES } from '@/config/declare';

interface Props {
    //表单模式，新增或编辑
    method: 'ADD' | 'EDIT';
    //任务类型，任务包（新的）、充电有奖、组团活动、组队活动、会员方案、发奖活动
    type: string;
    subType: string;
    //初始化数据，编辑或者有复制功能使用
    initialValue?: API.ModuleQueryVo;
    //编辑数据时，初始关联的任务包
    relTaskPackage?: API.TaskPackageQueryVo;
}
const ModuleForm: React.FC<Props> = ({ method, type, subType, initialValue, relTaskPackage }) => {
    const [form] = Form.useForm();
    const taskRef = useRef<any>();
    const chargeRef = useRef();
    const signRef = useRef<any>();
    const rewardRef = useRef<any>();
    const EquityRef = useRef<any>();

    const [relativeAct, setRelativeAct] =
        useState<Partial<API.TaskPackageQueryVo & API.VipPlanSignQueryVo & API.PrizeSendQueryVo>>();
    const [mainBtnConfParamList, setMainBtnConfParamList] = useState<API.ModuleBtnConfParam[]>([]);
    const [moduleTaskConfParamList, setModuleTaskConfParamList] = useState<
        API.ModuleTaskConfParam[]
    >([]);
    const [moduleConf, setModuleConf] = useState<API.AddModuleParams>();
    useEffect(() => {
        window.sessionStorage.removeItem('xdt_assemblyInfo');
    }, []);
    useEffect(() => {
        if (initialValue) {
            const params: API.AddModuleParams = {
                moduleId: initialValue?.moduleId,
                moduleName: initialValue?.moduleName,
                cooperationPlatform: initialValue?.cooperationPlatform,
                title: initialValue?.mainModuleConfQueryVo?.title,
                subTitle: initialValue?.mainModuleConfQueryVo?.subTitle,
                prizeRemark: initialValue?.mainModuleConfQueryVo?.prizeRemark,
                prizeImage: initialValue?.mainModuleConfQueryVo?.prizeImagePath,
            };
            if (type === ComponentModuleTypeEnum.TASK) {
                params.relativeAct = relTaskPackage;
                if (relTaskPackage?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
                    params.prizeName = initialValue?.mainModuleConfQueryVo?.prizeName;
                }

                setRelativeAct(relTaskPackage);
            } else if (type === ComponentModuleTypeEnum.RECHARGE) {
                params.relativeAct = relTaskPackage;
                params.moduleTaskProgress = initialValue?.mainModuleConfQueryVo?.moduleTaskProgress;
                params.cardBtn = initialValue?.mainModuleConfQueryVo?.cardBtn;
                params.styleType = initialValue?.mainModuleConfQueryVo?.styleType;
                if (relTaskPackage?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
                    params.prizeName = initialValue?.mainModuleConfQueryVo?.prizeName;
                }
                setRelativeAct(relTaskPackage);
            } else if (type === ComponentModuleTypeEnum.SIGN) {
                setRelativeAct({
                    actId: `${initialValue?.relaActId}`,
                    actName: initialValue?.relaActName,
                    actSubTypeName: initialValue?.relaActTypeName,
                });
            } else if (type === ComponentModuleTypeEnum.STATION_RECOMMEND) {
                const { stationRecommendQueryVo } = initialValue;

                params.configRuleType = stationRecommendQueryVo?.configRuleType;
                params.distance = stationRecommendQueryVo?.distance;

                params.stationInfo = { allStation: stationRecommendQueryVo?.detailList };
                params.stationQueryVos = stationRecommendQueryVo?.detailList;
            } else if (
                type === ComponentModuleTypeEnum.REWARD ||
                type === ComponentModuleTypeEnum.EQUITY
            ) {
                const act = {
                    actId: `${initialValue?.relaActId}`,
                    actName: initialValue?.relaActName,
                    actSubTypeName: initialValue?.relaActTypeName,
                    relaActType: initialValue?.relaActType,
                    relaActSubType: initialValue?.relaActSubType,
                };
                params.relativeAct = act;
                setRelativeAct(act);
            }
            setMainBtnConfParamList(
                initialValue?.mainModuleConfQueryVo?.moduleBtnConfQueryVos || [],
            );
            setModuleTaskConfParamList(
                initialValue?.moduleTaskConfQueryVoList?.map((v) => {
                    const task: API.ModuleTaskConfParam = {
                        moduleBtnConfParamList: v.moduleBtnConfQueryVoList,
                        taskId: v?.taskId,
                        taskName: v?.taskName,
                        title: v?.title,
                        prizeName: v?.prizeName,
                        prizeImage: v?.prizeImage,
                    };

                    const taskInfo = relTaskPackage?.taskList?.find(
                        (ele) => ele.taskId == v?.taskId,
                    );
                    if (taskInfo) {
                        task.taskName = taskInfo.taskName;
                    }

                    return task;
                }) || [],
            );
            form.setFieldsValue(params);
        } else {
            form.resetFields();
        }
    }, [initialValue, relTaskPackage]);

    useEffect(() => {
        if (initialValue && type === ComponentModuleTypeEnum.SIGN) {
            previewFormChange(relativeAct);
        } else if (initialValue && relativeAct) {
            previewFormChange(relativeAct);
        }
    }, [initialValue, type, relativeAct]);

    const openActModal = () => {
        if (type === ComponentModuleTypeEnum.TASK) {
            taskRef.current?.open(cooperationPlatform);
        } else if (type === ComponentModuleTypeEnum.RECHARGE) {
            chargeRef.current?.open(cooperationPlatform);
        } else if (type === ComponentModuleTypeEnum.SIGN) {
            signRef.current?.open(cooperationPlatform);
        } else if (type === ComponentModuleTypeEnum.REWARD) {
            rewardRef.current?.open(cooperationPlatform);
        } else if (type === ComponentModuleTypeEnum.EQUITY) {
            EquityRef.current?.open(cooperationPlatform);
        }
    };

    const getMainBtnList = (record: API.TaskPackageQueryVo) => {
        const list: API.ModuleBtnConfParam[] = [];
        //合并发奖，主模块按钮配置生成逻辑
        if (record?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };
            const complete: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取奖励',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            const claim: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.DISABLE,
                btnDesc: '已领取奖励',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.CLAIM,
            };
            switch (record?.openType) {
                case TASK_OPEN_TYPE_ENUM.HAND:
                    if (record?.obtainType === TASK_OBTAIN_TYPE_ENUM.HAND) {
                        list.push(unstart, unreceive, recive, complete, claim);
                    } else {
                        list.push(unstart, unreceive, recive, claim);
                    }
                    break;
                case TASK_OPEN_TYPE_ENUM.AUTO:
                    if (record?.obtainType === TASK_OBTAIN_TYPE_ENUM.HAND) {
                        list.push(unstart, recive, complete, claim);
                    } else {
                        list.push(unstart, recive, claim);
                    }
                    break;
                default:
                    message.error('任务开启触发类型错误: ' + record?.openType);
                    break;
            }
        }
        //独立发奖，主模块按钮配置生成逻辑
        else if (record?.rewardType === TASK_REWARD_TYPE_ENUM.ONE_SELF) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };
            const completeBag: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            switch (record?.openType) {
                case TASK_OPEN_TYPE_ENUM.HAND:
                    list.push(unstart, unreceive, recive, completeBag);
                    break;
                case TASK_OPEN_TYPE_ENUM.AUTO:
                    list.push(unstart, recive, completeBag);
                    break;
                default:
                    message.error('任务开启触发类型错误: ' + record?.openType);
                    break;
            }
        } else {
            message.error('任务奖励规则类型错误: ' + record?.rewardType);
        }
        return list;
    };

    const getChargeMainBtnList = (record: API.TaskPackageQueryVo) => {
        const list: API.ModuleBtnConfParam[] = [];
        //合并发奖，主模块按钮配置生成逻辑
        if (record?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };

            const complete: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取奖励',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            const claim: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.CLAIM,
            };
            switch (record?.openType) {
                case TASK_OPEN_TYPE_ENUM.HAND:
                    if (record?.obtainType === TASK_OBTAIN_TYPE_ENUM.HAND) {
                        list.push(unstart, unreceive, recive, complete, claim);
                    } else {
                        list.push(unstart, unreceive, recive, claim);
                    }
                    break;
                case TASK_OPEN_TYPE_ENUM.AUTO:
                    if (record?.obtainType === TASK_OBTAIN_TYPE_ENUM.HAND) {
                        list.push(unstart, recive, complete, claim);
                    } else {
                        list.push(unstart, recive, claim);
                    }
                    break;
                default:
                    message.error('任务开启触发类型错误: ' + record?.openType);
                    break;
            }
        }
        //独立发奖，主模块按钮配置生成逻辑
        else if (record?.rewardType === TASK_REWARD_TYPE_ENUM.ONE_SELF) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };

            const completeBag: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            switch (record?.openType) {
                case TASK_OPEN_TYPE_ENUM.HAND:
                    list.push(unstart, unreceive, recive, completeBag);
                    break;
                case TASK_OPEN_TYPE_ENUM.AUTO:
                    list.push(unstart, recive, completeBag);
                    break;
                default:
                    message.error('任务开启触发类型错误: ' + record?.openType);
                    break;
            }
        } else {
            message.error('任务奖励规则类型错误: ' + record?.rewardType);
        }
        return list;
    };

    const getTaskBtnList = (record: API.TaskPackageQueryVo, taskId?: string) => {
        const list: API.ModuleBtnConfParam[] = [];
        //合并发奖，主模块按钮配置生成逻辑
        if (record?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '去完成',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };
            const complete: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.DISABLE,
                btnDesc: '已完成',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            const claim: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.DISABLE,
                btnDesc: '已完成',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.CLAIM,
            };
            switch (record?.openType) {
                case TASK_OPEN_TYPE_ENUM.HAND:
                    list.push(unstart, unreceive, recive, complete, claim);
                    break;
                case TASK_OPEN_TYPE_ENUM.AUTO:
                    list.push(unstart, recive, complete, claim);
                    break;
                default:
                    message.error('任务领奖触发类型错误: ' + record?.obtainType);
                    break;
            }
        }
        //独立发奖，主模块按钮配置生成逻辑
        else if (record?.rewardType === TASK_REWARD_TYPE_ENUM.ONE_SELF) {
            const unstart: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNSTART,
            };
            const unreceive: API.ModuleBtnConfParam = {
                btnShowFlag: MODULE_BTN_SHOW_ENUM.HIDE,
                status: MODULE_BTN_STATUS_ENUM.UNRECEIVE,
            };
            const recive: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '去完成',

                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.RECIVE,
            };
            const complete: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.ENABLE,
                btnDesc: '领取奖励',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.COMPLETE,
            };
            const claim: API.ModuleBtnConfParam = {
                btnClickFlag: MODULE_BTN_CLICK_ENUM.DISABLE,
                btnDesc: '已领取奖励',
                btnShowFlag: MODULE_BTN_SHOW_ENUM.SHOW,
                status: MODULE_BTN_STATUS_ENUM.CLAIM,
            };
            switch (record?.obtainType) {
                case TASK_OBTAIN_TYPE_ENUM.HAND:
                    if (record?.openType === TASK_OPEN_TYPE_ENUM.HAND) {
                        list.push(unstart, unreceive, recive, complete, claim);
                    } else {
                        list.push(unstart, recive, complete, claim);
                    }
                    break;
                case TASK_OBTAIN_TYPE_ENUM.AUTO:
                    if (record?.openType === TASK_OPEN_TYPE_ENUM.HAND) {
                        list.push(unstart, unreceive, recive, claim);
                    } else {
                        list.push(unstart, recive, complete);
                    }
                    break;
                default:
                    message.error('任务领奖触发类型错误: ' + record?.openType);
                    break;
            }
        } else {
            message.error('任务奖励规则类型错误: ' + record?.rewardType);
        }
        return list;
    };

    const getModuleTaskConfList = (record: API.TaskPackageQueryVo) => {
        const list: API.ModuleTaskConfParam[] = [];

        record?.taskList?.forEach((task: API.TaskQueryVo) => {
            const conf: API.ModuleTaskConfParam = {
                moduleBtnConfParamList: getTaskBtnList(record, task?.taskId),
                taskId: task?.taskId,
                taskName: task?.taskName,
                title: task?.title,
                prizeName: task?.prizeName,
            };
            if (type === ComponentModuleTypeEnum.RECHARGE) {
                conf.moduleBtnConfParamList = [];
            }

            list.push(conf);
        });
        return list;
    };

    const onSelectTask = (record: API.TaskPackageQueryVo) => {
        form.setFieldValue('relativeAct', record);
        setMainBtnConfParamList(getMainBtnList(record));
        setModuleTaskConfParamList(getModuleTaskConfList(record));
        setRelativeAct(record);
        previewFormChange(record);
    };
    const onSelectCharge = (record: API.TaskPackageQueryVo) => {
        form.setFieldValue('relativeAct', record);
        setMainBtnConfParamList(getChargeMainBtnList(record));
        setModuleTaskConfParamList(getModuleTaskConfList(record));
        setRelativeAct(record);
        previewFormChange(record);
    };
    const onSelectSign = (record: any) => {
        form.setFieldValue('relativeAct', record);
        setRelativeAct(record);
        previewFormChange(record);
    };
    const onSelectReward = (record: API.PrizeSendQueryVo) => {
        console.debug('onSelectReward==>', record);
        form.setFieldValue('relativeAct', record);
        setRelativeAct(record);
        previewFormChange(record);
    };
    const onSelectEquity = (record: API.PrizeSendQueryVo) => {
        onSelectReward(record);
    };

    const { run, loading } = useRequest(
        (params) => {
            return addModule(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res.ret === 200) {
                    message.success('保存成功');
                    history.replace('/marketing/component/module');
                }
            },
        },
    );

    const getSubmitObject = (formData: any) => {
        const params: API.AddModuleParams = {
            moduleId: method === 'EDIT' ? initialValue?.moduleId : undefined,
            moduleName: formData?.moduleName?.trim(),
            cooperationPlatform: formData?.cooperationPlatform,
            relaActType: type,
            relaActSubType: subType,
        };
        let relaActId = undefined;
        switch (type) {
            case ComponentModuleTypeEnum.TASK:
                relaActId = relativeAct?.packageId;
                //主模块配置
                params.mainModuleConfParam = {
                    moduleBtnConfParamList: mainBtnConfParamList,
                    prizeImage: formData?.prizeImage,
                    prizeName:
                        relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE
                            ? formData?.prizeName?.trim()
                            : undefined,
                    title: formData?.title?.trim(),
                    prizeRemark: formData?.prizeRemark?.trim(),
                };
                //任务模块配置
                params.moduleTaskConfParamList = moduleTaskConfParamList?.map((task) => {
                    const taskParam = {
                        ...task,

                        moduleBtnConfParamList: task?.moduleBtnConfParamList?.map((btn) => {
                            return {
                                ...btn,
                            };
                        }),
                    };

                    return taskParam;
                });
                break;
            case ComponentModuleTypeEnum.RECHARGE:
                relaActId = relativeAct?.packageId;
                //主模块配置
                const mainModuleConfParam = {
                    moduleBtnConfParamList: mainBtnConfParamList,
                    title: formData?.title?.trim(),
                    styleType: formData?.styleType,
                    moduleTaskProgress: formData?.moduleTaskProgress,
                    cardBtn: formData?.cardBtn,
                };
                if (relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
                    mainModuleConfParam.prizeImage = formData?.prizeImage;
                    mainModuleConfParam.prizeName = formData?.prizeName?.trim();
                }
                params.mainModuleConfParam = mainModuleConfParam;
                //任务模块配置
                params.moduleTaskConfParamList = moduleTaskConfParamList?.map((task) => {
                    const taskParam = {
                        ...task,

                        moduleBtnConfParamList: task?.moduleBtnConfParamList?.map((btn) => {
                            return {
                                ...btn,
                            };
                        }),
                    };

                    return taskParam;
                });
                break;
            case ComponentModuleTypeEnum.SIGN:
                relaActId = relativeAct?.actId;
                break;
            case ComponentModuleTypeEnum.REWARD:
                relaActId = relativeAct?.actId;
                params.actName = relativeAct?.actName;
                params.actSubTypeName = relativeAct?.actSubTypeName;
                //主模块配置
                params.mainModuleConfParam = {
                    prizeImage: formData?.prizeImage,
                    title: formData?.title?.trim(),
                    subTitle: formData?.subTitle?.trim(),
                    prizeRemark: formData?.prizeRemark?.trim(),
                };
                break;
            case ComponentModuleTypeEnum.EQUITY:
                relaActId = relativeAct?.actId;
                params.actName = relativeAct?.actName;
                params.actSubTypeName = relativeAct?.actSubTypeName;
                //主模块配置
                params.mainModuleConfParam = {
                    prizeImage: formData?.prizeImage,
                    title: formData?.title?.trim(),
                    subTitle: formData?.subTitle?.trim(),
                    prizeRemark: formData?.prizeRemark?.trim(),
                };
                break;
            case ComponentModuleTypeEnum.STATION_RECOMMEND:
                const { allStations } = formData?.stationInfo;

                params.stationRecommendParam = {
                    distance: formData?.distance,
                    configRuleType: formData?.configRuleType,
                    stationList: allStations
                        ?.filter((ele) => !ele.equityFlag)
                        ?.map((ele: any) => ele.stationId),
                };
                params.stationQueryVos = allStations;

                break;
            default:
                break;
        }
        params.relaActId = relaActId;
        return params;
    };

    const deleteRelAct = () => {
        setRelativeAct(undefined);
        setMainBtnConfParamList([]);
        setModuleTaskConfParamList([]);
        const formData = form.getFieldsValue(true);
        const param: any = {
            moduleName: formData?.moduleName,
        };
        if (formData?.moduleId) {
            param.moduleId = formData.moduleId;
        }
        form.resetFields();
        form.setFieldsValue(param);
        previewFormChange(undefined);
    };

    const submitForm = (formData: any) => {
        const params = getSubmitObject(formData);

        delete params.stationQueryVos;

        run(params);
    };

    const previewFormChange = (actInfo: any) => {
        const formData = form.getFieldsValue(true);
        if (!isEmpty(formData) && actInfo) {
            const params = getSubmitObject(formData);
            setModuleConf(params);
        } else {
            setModuleConf(undefined);
        }
    };

    const cooperationPlatform = Form.useWatch('cooperationPlatform', form);
    return (
        <>
            {moduleConf && (
                <PreviewModuleConf
                    moduleConf={moduleConf}
                    relativeAct={relativeAct}
                    type={type}
                    subType={subType}
                />
            )}

            <Form
                form={form}
                onFinish={submitForm}
                wrapperCol={{ span: 16 }}
                labelCol={{ flex: '0 0 120px' }}
                labelAlign="right"
                style={{ minHeight: 'calc(100vh - 380px)' }}
            >
                <Typography.Title level={4}>基础信息</Typography.Title>
                {method === 'EDIT' && (
                    <Form.Item
                        name="moduleId"
                        label="组件模块ID"
                        required={method === 'EDIT'}
                        wrapperCol={{ span: 8 }}
                    >
                        <Input disabled />
                    </Form.Item>
                )}
                <Form.Item
                    name="moduleName"
                    label="组件模块名称"
                    required
                    rules={[{ required: true, whitespace: true, message: '请输入组件模块名称' }]}
                    wrapperCol={{ span: 8 }}
                >
                    <Input maxLength={16} showCount allowClear placeholder="请输入组件模块名称" />
                </Form.Item>
                {type === ComponentModuleTypeEnum.RECHARGE ? (
                    <Form.Item
                        name="cooperationPlatform"
                        hidden
                        initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                    ></Form.Item>
                ) : (
                    <Form.Item
                        name="cooperationPlatform"
                        label="归属平台"
                        required
                        initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                        rules={[{ required: true, whitespace: true, message: '请输入归属平台' }]}
                        wrapperCol={{ span: 8 }}
                    >
                        <SelectCooperationPlatform disabled={method === 'EDIT'} />
                    </Form.Item>
                )}

                {type === ComponentModuleTypeEnum.TASK && (
                    <Form.Item
                        name="relativeAct"
                        label="关联活动"
                        required
                        rules={[{ required: true, message: '请添加关联活动' }]}
                    >
                        {relativeAct ? (
                            <Space>
                                <Link
                                    target="_blank"
                                    to={`/marketing/event/task-package/list/detail?id=${relativeAct?.packageId}`}
                                >
                                    {`${relativeAct?.rewardTypeName} | ${relativeAct?.packageId} | ${relativeAct?.packageName}`}
                                </Link>
                                <Typography.Link type="warning" onClick={deleteRelAct}>
                                    删除
                                </Typography.Link>
                            </Space>
                        ) : (
                            <Button type="primary" icon="+" onClick={openActModal}>
                                关联活动
                            </Button>
                        )}
                    </Form.Item>
                )}
                {type === ComponentModuleTypeEnum.RECHARGE && (
                    <>
                        <Form.Item label="组件模块类型" required>
                            卡片
                        </Form.Item>
                        <Form.Item label="关联类型" required>
                            充电有奖
                        </Form.Item>
                        <Form.Item label="领奖方式" required>
                            {relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE
                                ? '合并领奖'
                                : '独立领奖'}
                        </Form.Item>

                        <Form.Item
                            name="relativeAct"
                            label="关联活动"
                            required
                            rules={[{ required: true, message: '请添加关联活动' }]}
                        >
                            {relativeAct ? (
                                <Space>
                                    <Link
                                        target="_blank"
                                        to={`/marketing/event/task-package/list/detail?id=${relativeAct?.packageId}`}
                                    >
                                        {`${relativeAct?.rewardTypeName} | ${relativeAct?.packageId} | ${relativeAct?.packageName}`}
                                    </Link>
                                    <Typography.Link type="danger" onClick={deleteRelAct}>
                                        删除
                                    </Typography.Link>
                                </Space>
                            ) : (
                                <Button type="primary" icon="+" onClick={openActModal}>
                                    关联活动
                                </Button>
                            )}
                        </Form.Item>
                    </>
                )}
                {type === ComponentModuleTypeEnum.SIGN && (
                    <Form.Item
                        name="relativeAct"
                        label="关联会员方案"
                        required
                        rules={[{ required: true, message: '请添加关联会员方案' }]}
                    >
                        {relativeAct ? (
                            <Space>
                                <Link
                                    target="_blank"
                                    to={`/userCenter/membership/plan/list/detail-vip/${relativeAct?.actId}`}
                                >
                                    {`${relativeAct?.actName} | ${relativeAct?.actId}`}
                                </Link>
                                <Typography.Link type="danger" onClick={deleteRelAct}>
                                    删除
                                </Typography.Link>
                            </Space>
                        ) : (
                            <Button type="primary" icon="+" onClick={openActModal}>
                                关联会员方案
                            </Button>
                        )}
                    </Form.Item>
                )}
                {type === ComponentModuleTypeEnum.REWARD && (
                    <Form.Item
                        name="relativeAct"
                        label="关联发奖活动"
                        required
                        rules={[{ required: true, message: '请添加关联发奖活动' }]}
                    >
                        {relativeAct ? (
                            <Space>
                                <Link
                                    target="_blank"
                                    to={`/marketing/couponCenter/applet-gift/list/look/${relativeAct?.actId}/${ACTSUBTYPES.PRIZE_SEND}`}
                                >
                                    {`${relativeAct?.actSubTypeName} | ${relativeAct?.actId} | ${relativeAct?.actName}`}
                                </Link>
                                <Typography.Link type="warning" onClick={deleteRelAct}>
                                    删除
                                </Typography.Link>
                            </Space>
                        ) : (
                            <Button type="primary" icon="+" onClick={openActModal}>
                                关联发奖活动
                            </Button>
                        )}
                    </Form.Item>
                )}
                {type === ComponentModuleTypeEnum.EQUITY && (
                    <Form.Item
                        name="relativeAct"
                        label="关联活动"
                        required
                        rules={[{ required: true, message: '请添加关联发放权益活动' }]}
                    >
                        {relativeAct ? (
                            <Space>
                                <Link
                                    target="_blank"
                                    to={`/marketing/distribute-benefits/detail?id=${relativeAct?.actId}`}
                                >
                                    {`${relativeAct?.actTypeName || '发放权益'} | ${
                                        relativeAct?.actId
                                    } | ${relativeAct?.actName}`}
                                </Link>
                                <Typography.Link type="warning" onClick={deleteRelAct}>
                                    删除
                                </Typography.Link>
                            </Space>
                        ) : (
                            <Button type="primary" icon="+" onClick={openActModal}>
                                关联发放权益活动
                            </Button>
                        )}
                    </Form.Item>
                )}
                {/* 站点推荐 */}
                {type === ComponentModuleTypeEnum.STATION_RECOMMEND && (
                    <StationRecommendForm
                        type={type}
                        defaultStations={initialValue?.stationRecommendQueryVo?.detailList}
                    ></StationRecommendForm>
                )}

                {/* 任务包相关配置 */}
                {relativeAct && type === ComponentModuleTypeEnum.TASK && (
                    <TaskForm
                        form={form}
                        type={type}
                        initialValue={initialValue}
                        relativeAct={relativeAct}
                        mainBtnConfList={mainBtnConfParamList}
                        moduleTaskConfParamList={moduleTaskConfParamList}
                        changeModuleTaskBtnConfParamList={(taskId, list) => {
                            const newList = [...moduleTaskConfParamList];
                            const findNum = newList.findIndex((ele) => ele.taskId == taskId);
                            if (findNum >= 0) {
                                newList[findNum].moduleBtnConfParamList = list;
                            }

                            setModuleTaskConfParamList(newList);
                        }}
                        changeModuleTaskConfParamList={(taskId, info) => {
                            const newList = [...moduleTaskConfParamList];
                            const findNum = newList.findIndex((ele) => ele.taskId == taskId);
                            if (findNum >= 0) {
                                newList[findNum] = info;
                            }

                            setModuleTaskConfParamList(newList);
                        }}
                    />
                )}

                {relativeAct && type === ComponentModuleTypeEnum.RECHARGE && (
                    <RechargeForm
                        form={form}
                        type={type}
                        initialValue={initialValue}
                        relativeAct={relativeAct}
                        mainBtnConfList={mainBtnConfParamList}
                        moduleTaskConfParamList={moduleTaskConfParamList}
                        changeModuleTaskConfParamList={(taskId, info) => {
                            const newList = [...moduleTaskConfParamList];
                            const findNum = newList.findIndex((ele) => ele.taskId == taskId);
                            if (findNum >= 0) {
                                newList[findNum] = info;
                            }

                            setModuleTaskConfParamList(newList);
                        }}
                    />
                )}
                {/* 发奖类型配置REWARD、发放权益类型配置EQUITY */}
                {relativeAct &&
                    (type === ComponentModuleTypeEnum.REWARD ||
                        type === ComponentModuleTypeEnum.EQUITY) && (
                        <>
                            <Typography.Title level={4}>展示配置</Typography.Title>
                            <Typography.Title level={5}>主模块配置</Typography.Title>
                            <Form.Item
                                label="标题"
                                name="title"
                                required
                                rules={[
                                    { required: true, whitespace: true, message: '请输入标题' },
                                ]}
                                wrapperCol={{ span: 8 }}
                            >
                                <Input
                                    maxLength={16}
                                    showCount
                                    allowClear
                                    placeholder="请输入标题"
                                />
                            </Form.Item>
                            <Form.Item
                                label="副文案"
                                name="subTitle"
                                required
                                rules={[
                                    { required: true, whitespace: true, message: '请输入副文案' },
                                ]}
                                wrapperCol={{ span: 8 }}
                            >
                                <Input
                                    maxLength={16}
                                    showCount
                                    allowClear
                                    placeholder="请输入副文案"
                                />
                            </Form.Item>
                            <Typography.Title level={5}>主模块奖品展示配置</Typography.Title>
                            <Form.Item
                                name="prizeImage"
                                label="奖品图片"
                                required
                                rules={[{ required: true, message: '请上传奖品图片' }]}
                                wrapperCol={{ span: 8 }}
                            >
                                <UpLoadImgCustom
                                    maxCount={1}
                                    sizeInfo={{
                                        width: 100,
                                        height: 100,
                                        size: 100,
                                    }}
                                    multiple={false}
                                    initialValue={
                                        form.getFieldValue('prizeImage') &&
                                        initialValue?.mainModuleConfQueryVo?.prizeImagePath
                                            ? [
                                                  {
                                                      url: initialValue?.mainModuleConfQueryVo
                                                          ?.prizeImagePath,
                                                      uid: initialValue?.mainModuleConfQueryVo
                                                          ?.prizeImagePath,
                                                      status: 'done',
                                                  },
                                              ]
                                            : undefined
                                    }
                                    placeholder="格式支持png、jpg、jpeg,大小100px * 100px, 不得超过100KB"
                                    uploadData={{
                                        contentType: '02',
                                        contRemrk: 'groupCodeUrl',
                                        relaTable: 'a_station_label',
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="奖品说明" name="prizeRemark" wrapperCol={{ span: 8 }}>
                                <Input
                                    maxLength={100}
                                    showCount
                                    allowClear
                                    placeholder="请输入奖品说明"
                                />
                            </Form.Item>
                        </>
                    )}
                {/* 其他类型配置 */}
                {relativeAct &&
                    [
                        ComponentModuleTypeEnum.SIGN,
                        ComponentModuleTypeEnum.GROUP,
                        ComponentModuleTypeEnum.TEAM,
                    ].includes(type) && (
                        <>
                            <Typography.Title level={4}>展示配置</Typography.Title>
                            <Typography.Title level={5}>主模块配置</Typography.Title>
                            <Form.Item label=" " colon={false}>
                                <Radio checked>取活动固定配置</Radio>
                            </Form.Item>
                        </>
                    )}
                <Form.Item wrapperCol={{ span: 24 }}>
                    <Row style={{ width: '100%', marginTop: 24 }} justify="center">
                        <Space size="large">
                            <Button
                                htmlType="reset"
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                取消
                            </Button>
                            <Button onClick={previewFormChange}>预览</Button>
                            <Button type="primary" htmlType="submit" loading={loading}>
                                确定
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
            <TaskSelectModal ref={taskRef} onSelect={onSelectTask} />
            <ChargeSelectModal ref={chargeRef} onSelect={onSelectCharge} />
            <SignSelectModal ref={signRef} onSelect={onSelectSign} />
            <RewardSelectModal ref={rewardRef} onSelect={onSelectReward} />
            <EquitySelectModal ref={EquityRef} onSelect={onSelectEquity} />
        </>
    );
};

export default ModuleForm;
