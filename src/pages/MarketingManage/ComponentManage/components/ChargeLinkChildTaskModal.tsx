// 目标事件配置（单独配置，弹窗）

import { Form, Modal } from 'antd';
import React, { useImperativeHandle, useState, forwardRef } from 'react';
import XdtProTable from '@/components/XdtProTable/index';
import moment from 'moment';

const ChargeLinkChildTaskModal: React.FC<{
    onFinish: any;
    taskList?: any[];
}> = (props, ref: any) => {
    const { taskList, onFinish } = props;

    const [visible, updateVisible] = useState(false);
    const [disabledIds, updateDisabledIds] = useState<string[] | undefined>();

    const [selectList, updateSelectList] = useState<any>([]); //
    useImperativeHandle(ref, () => ({
        open: ({ disabledIds: _disabledIds }: { disabledIds?: string[] } = {}) => {
            updateVisible(true);
            updateDisabledIds(_disabledIds);

            updateSelectList([]);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const rowSelection = {
        type: 'radio',
        checkStrictly: false,
        selectedRowKeys: selectList.map((item: any) => item.taskId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            updateSelectList(selectedRows);
        },
        getCheckboxProps: (record: any) => ({
            disabled: disabledIds && disabledIds.indexOf(`${record?.taskId}`) >= 0,
            name: record.taskId,
        }),
    };

    const columns = [
        {
            title: '任务ID',
            width: 120,
            dataIndex: 'taskId',
        },
        {
            title: '任务名称',
            width: 140,
            dataIndex: 'taskName',
        },
        {
            title: '任务编号',
            width: 140,
            dataIndex: 'taskNo',
        },
    ];

    return (
        <Modal
            visible={visible}
            title="任务配置"
            onCancel={onClose}
            onOk={() => {
                onFinish?.(selectList);
                onClose();
            }}
            width={920}
            okButtonProps={{ disabled: selectList?.length == 0 }}
        >
            <XdtProTable
                search={false}
                rowKey={'taskId'}
                columns={columns}
                dataSource={taskList}
                rowSelection={rowSelection}
            ></XdtProTable>
        </Modal>
    );
};
export default forwardRef(ChargeLinkChildTaskModal);
