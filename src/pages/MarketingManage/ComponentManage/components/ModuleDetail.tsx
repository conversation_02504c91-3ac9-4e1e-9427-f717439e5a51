import ProCard from '@ant-design/pro-card';
import { useRequest } from 'ahooks';
import { Collapse, Descriptions, Image, Radio, Space, Tag, Typography } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'umi';

import {
    ComponentModuleTypeEnum,
    MODULE_BTN_CLICK_ENUM,
    MODULE_BTN_SHOW_ENUM,
    MODULE_BTN_STATUS_ENUM,
    TASK_REWARD_TYPE_ENUM,
    moduleBtnTitleEnumWhenMerge,
    moduleBtnTitleEnumWhenOneself,
    moduleTaskTitleEnumWhenMerge,
    moduleTaskTitleEnumWhenOneself,
} from '@/constants/componentManage';
import { getModule } from '@/services/Marketing/ComponentManageApi';
import { getTaskPackageDetail } from '@/services/Marketing/MngEventApi';
import PreviewConf from './PreviewConf';
import { ACTSUBTYPES } from '@/config/declare';
import RechargeDetail from './RechargeDetail';
import StationRecommendDetail from './StationRecommendDetail';

const { Panel } = Collapse;

const ModuleDetail: React.FC<{ id: string | number }> = ({ id }) => {
    const [selectTab, setSelectTab] = useState<string>('main');

    const { run, loading, data } = useRequest(
        (id) => {
            return getModule(id);
        },
        {
            manual: true,
        },
    );

    const {
        run: queryTask,
        loading: taskLoading,
        data: taskData,
    } = useRequest(
        (id) => {
            return getTaskPackageDetail(id);
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    useEffect(() => {
        if (
            data?.data?.relaActType === ComponentModuleTypeEnum.TASK ||
            data?.data?.relaActType === ComponentModuleTypeEnum.RECHARGE
        ) {
            queryTask(data?.data?.relaActId);
        }
    }, [data?.data?.relaActType, data?.data?.relaActId]);

    const moduleConf = useMemo(() => {
        const params: API.AddModuleParams = {
            moduleId: data?.data?.moduleId,
            moduleName: data?.data?.moduleName,
            relaActType: data?.data?.relaActType,
            relaActSubType: data?.data?.relaActSubType,
            stationQueryVos: data?.data?.stationRecommendQueryVo?.detailList,
        };
        let relaActId = undefined;
        switch (data?.data?.relaActType) {
            case ComponentModuleTypeEnum.TASK:
                relaActId = data?.data?.relaActId;
                //主模块配置
                params.mainModuleConfParam = {
                    ...data?.data?.mainModuleConfQueryVo,
                    prizeImage: data?.data?.mainModuleConfQueryVo?.prizeImagePath,
                    prizeRemark: data?.data?.mainModuleConfQueryVo?.prizeRemark,
                };
                //任务模块配置
                params.moduleTaskConfParamList = data?.data?.moduleTaskConfQueryVoList;
                break;
            case ComponentModuleTypeEnum.RECHARGE:
                relaActId = data?.data?.relaActId;
                //主模块配置
                params.mainModuleConfParam = {
                    ...data?.data?.mainModuleConfQueryVo,
                    prizeImage: data?.data?.mainModuleConfQueryVo?.prizeImagePath,
                    prizeRemark: data?.data?.mainModuleConfQueryVo?.prizeRemark,
                    moduleTaskProgress: data?.data?.mainModuleConfQueryVo?.moduleTaskProgress,
                    cardBtn: data?.data?.mainModuleConfQueryVo?.cardBtn,
                    styleType: data?.data?.mainModuleConfQueryVo?.styleType,
                    prizeName: data?.data?.mainModuleConfQueryVo?.prizeName,
                };

                //任务模块配置
                params.moduleTaskConfParamList = data?.data?.moduleTaskConfQueryVoList;
                break;
            case ComponentModuleTypeEnum.SIGN:
                relaActId = data?.data?.relaActId;
                break;
            case ComponentModuleTypeEnum.REWARD:
                relaActId = data?.data?.relaActId;
                //主模块配置
                params.mainModuleConfParam = {
                    ...data?.data?.mainModuleConfQueryVo,
                    prizeImage: data?.data?.mainModuleConfQueryVo?.prizeImagePath,
                    prizeRemark: data?.data?.mainModuleConfQueryVo?.prizeRemark,
                };
                break;
            case ComponentModuleTypeEnum.EQUITY:
                relaActId = data?.data?.relaActId;
                //主模块配置
                params.mainModuleConfParam = {
                    ...data?.data?.mainModuleConfQueryVo,
                    prizeImage: data?.data?.mainModuleConfQueryVo?.prizeImagePath,
                    prizeRemark: data?.data?.mainModuleConfQueryVo?.prizeRemark,
                };
                break;

            default:
                break;
        }
        params.relaActId = relaActId;
        return params;
    }, [data?.data, taskData?.data]);

    return (
        <ProCard loading={loading || taskLoading} style={{ minHeight: 'calc(100vh - 300px)' }}>
            <PreviewConf
                moduleConf={moduleConf}
                relativeAct={taskData?.data}
                type={data?.data?.relaActType}
                subType={data?.data?.relaActSubType}
            />
            <Space direction="vertical" style={{ width: '100%' }}>
                <Typography.Title level={4}>基础信息</Typography.Title>
                <Descriptions column={1}>
                    <Descriptions.Item label="组件模块ID">{data?.data?.moduleId}</Descriptions.Item>
                    <Descriptions.Item label="组件模块名称">
                        {data?.data?.moduleName}
                    </Descriptions.Item>
                    {data?.data?.relaActType === ComponentModuleTypeEnum.RECHARGE && (
                        <>
                            <Descriptions.Item label="组件模块类型">卡片</Descriptions.Item>
                            <Descriptions.Item label="关联类型">充电有奖</Descriptions.Item>
                            <Descriptions.Item label="领奖方式">
                                {data?.data?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE
                                    ? '合并领奖'
                                    : '独立领奖'}
                            </Descriptions.Item>
                        </>
                    )}
                    <Descriptions.Item label="关联活动">
                        {data?.data?.relaActType === ComponentModuleTypeEnum.TASK && (
                            <Link
                                target="_blank"
                                to={`/marketing/event/task-package/list/detail?id=${taskData?.data?.packageId}`}
                            >
                                {`${taskData?.data?.rewardTypeName} | ${taskData?.data?.packageId} | ${taskData?.data?.packageName}`}
                            </Link>
                        )}
                        {data?.data?.relaActType === ComponentModuleTypeEnum.RECHARGE && (
                            <Link
                                target="_blank"
                                to={`/marketing/event/task-package/list/detail?id=${taskData?.data?.packageId}`}
                            >
                                {`${taskData?.data?.rewardTypeName} | ${taskData?.data?.packageId} | ${taskData?.data?.packageName}`}
                            </Link>
                        )}
                        {data?.data?.relaActType === ComponentModuleTypeEnum.SIGN && (
                            <Link
                                target="_blank"
                                to={`/userCenter/membership/plan/list/detail-vip/${data?.data?.relaActId}`}
                            >
                                {`${data?.data?.relaActName} | ${data?.data?.relaActId}`}
                            </Link>
                        )}
                        {data?.data?.relaActType === ComponentModuleTypeEnum.REWARD && (
                            <Link
                                target="_blank"
                                to={`/marketing/couponCenter/applet-gift/list/look/${data?.data?.relaActId}/${ACTSUBTYPES.PRIZE_SEND}`}
                            >
                                {`${data?.data?.relaActTypeName} | ${data?.data?.relaActId} | ${data?.data?.relaActName}`}
                            </Link>
                        )}
                        {data?.data?.relaActType === ComponentModuleTypeEnum.EQUITY && (
                            <Link
                                target="_blank"
                                to={`/marketing/distribute-benefits/detail?id=${data?.data?.relaActId}`}
                            >
                                {`${data?.data?.relaActTypeName || '发放权益'} | ${
                                    data?.data?.relaActId
                                } | ${data?.data?.relaActName}`}
                            </Link>
                        )}
                    </Descriptions.Item>
                </Descriptions>
                {data?.data?.relaActType === ComponentModuleTypeEnum.TASK && (
                    <>
                        <Typography.Title level={4}>触发和完成规则</Typography.Title>
                        <Descriptions column={1}>
                            <Descriptions.Item label="任务触发">
                                {taskData?.data?.openTypeName}
                            </Descriptions.Item>
                            <Descriptions.Item label="任务完成">
                                {taskData?.data?.obtainTypeName}
                            </Descriptions.Item>
                            <Descriptions.Item label="领奖规则">
                                {taskData?.data?.rewardTypeName}
                            </Descriptions.Item>
                        </Descriptions>
                    </>
                )}
                <Typography.Title level={4}>展示配置</Typography.Title>
                {data?.data?.relaActType === ComponentModuleTypeEnum.TASK && (
                    <>
                        <Radio.Group
                            buttonStyle="solid"
                            defaultValue="main"
                            size="large"
                            style={{ marginTop: 16 }}
                            onChange={(e) => {
                                setSelectTab(e.target.value);
                            }}
                        >
                            <Radio.Button value="main">
                                <Space>
                                    <Typography.Text type="danger">*</Typography.Text>
                                    主模块配置
                                </Space>
                            </Radio.Button>
                            <Radio.Button value="condition">
                                <Space>
                                    <Typography.Text type="danger">*</Typography.Text>
                                    任务条件配置
                                </Space>
                            </Radio.Button>
                        </Radio.Group>
                        <div
                            style={{
                                opacity: selectTab === 'main' ? 1 : 0,
                                height: selectTab === 'main' ? 'auto' : 0,
                                overflow: 'hidden',
                            }}
                        >
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Typography.Title level={5}>主模块配置</Typography.Title>
                                <Descriptions column={1}>
                                    <Descriptions.Item label="标题">
                                        {data?.data?.mainModuleConfQueryVo?.title}
                                    </Descriptions.Item>
                                </Descriptions>
                                <Typography.Title level={5}>主模块奖品展示配置</Typography.Title>
                                <Descriptions column={1}>
                                    <Descriptions.Item label="奖品图片">
                                        <Image
                                            src={data?.data?.mainModuleConfQueryVo?.prizeImagePath}
                                        />
                                    </Descriptions.Item>
                                    {taskData?.data?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE && (
                                        <Descriptions.Item label="奖品名称">
                                            {data?.data?.mainModuleConfQueryVo?.prizeName}
                                        </Descriptions.Item>
                                    )}
                                    <Descriptions.Item label="奖品说明">
                                        {data?.data?.mainModuleConfQueryVo?.prizeRemark}
                                    </Descriptions.Item>
                                </Descriptions>
                                <Typography.Title level={5}>主模块按钮配置</Typography.Title>
                                <Collapse>
                                    {data?.data?.mainModuleConfQueryVo?.moduleBtnConfQueryVos?.map(
                                        (conf: API.ModuleBtnConfParam) => {
                                            return (
                                                <Panel
                                                    header={
                                                        taskData?.data?.rewardType ===
                                                        TASK_REWARD_TYPE_ENUM.MERGE
                                                            ? moduleBtnTitleEnumWhenMerge[
                                                                  conf.status as string
                                                              ]
                                                            : moduleBtnTitleEnumWhenOneself[
                                                                  conf.status as string
                                                              ] || conf.status
                                                    }
                                                    key={`${conf.status}`}
                                                    forceRender
                                                >
                                                    <Descriptions column={1}>
                                                        <Descriptions.Item label="展示按钮">
                                                            {conf.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                                <Tag color="success">展示按钮</Tag>
                                                            ) : (
                                                                <Tag color="error">不展示按钮</Tag>
                                                            )}
                                                        </Descriptions.Item>
                                                        {conf.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW && (
                                                            <Descriptions.Item label="按钮文案">
                                                                <Tag>{conf.btnDesc}</Tag>
                                                            </Descriptions.Item>
                                                        )}
                                                        {conf.btnShowFlag ===
                                                            MODULE_BTN_SHOW_ENUM.SHOW && (
                                                            <Descriptions.Item label="按钮操作">
                                                                <Space>
                                                                    {conf.btnClickFlag ===
                                                                    MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                        <Tag color="success">
                                                                            按钮可点击
                                                                        </Tag>
                                                                    ) : (
                                                                        <Tag color="error">
                                                                            按钮不可点击
                                                                        </Tag>
                                                                    )}
                                                                    {conf.btnClickFlag ===
                                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                        conf.status ===
                                                                            MODULE_BTN_STATUS_ENUM.UNSTART && (
                                                                            <Typography.Text type="secondary">
                                                                                点击按钮时触发领取任务
                                                                            </Typography.Text>
                                                                        )}
                                                                    {conf.btnClickFlag ===
                                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                        conf.status ===
                                                                            MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                            <Typography.Text type="secondary">
                                                                                点击按钮时触发发奖
                                                                            </Typography.Text>
                                                                        )}
                                                                </Space>
                                                            </Descriptions.Item>
                                                        )}
                                                    </Descriptions>
                                                </Panel>
                                            );
                                        },
                                    )}
                                </Collapse>
                            </Space>
                        </div>
                        <div
                            style={{
                                opacity: selectTab === 'condition' ? 1 : 0,
                                height: selectTab === 'condition' ? 'auto' : 0,
                                overflow: 'hidden',
                            }}
                        >
                            <Collapse
                                defaultActiveKey={data?.data?.moduleTaskConfQueryVoList?.map(
                                    (v) => `${v?.taskId}`,
                                )}
                            >
                                {data?.data?.moduleTaskConfQueryVoList?.map((task) => {
                                    return (
                                        <Panel
                                            header={`条件：${task?.taskName}（${task?.taskId}）`}
                                            key={`${task?.taskId}`}
                                            extra={
                                                <Link
                                                    target="_blank"
                                                    to={`/marketing/event/task/list/detail?id=${task?.taskId}`}
                                                >
                                                    详情
                                                </Link>
                                            }
                                            forceRender
                                        >
                                            <Descriptions column={1}>
                                                <Descriptions.Item label="标题">
                                                    {task?.title}
                                                </Descriptions.Item>
                                                {taskData?.data?.rewardType ===
                                                    TASK_REWARD_TYPE_ENUM.ONE_SELF && (
                                                    <Descriptions.Item label="奖品名称">
                                                        {task?.prizeName}
                                                    </Descriptions.Item>
                                                )}
                                            </Descriptions>
                                            <Collapse>
                                                {task.moduleBtnConfQueryVoList?.map((btn) => {
                                                    return (
                                                        <Panel
                                                            header={
                                                                taskData?.data?.rewardType ===
                                                                TASK_REWARD_TYPE_ENUM.MERGE
                                                                    ? moduleTaskTitleEnumWhenMerge[
                                                                          btn.status as string
                                                                      ]
                                                                    : moduleTaskTitleEnumWhenOneself[
                                                                          btn.status as string
                                                                      ] || btn.status
                                                            }
                                                            key={`${btn.status}`}
                                                            forceRender
                                                        >
                                                            <Descriptions column={1}>
                                                                <Descriptions.Item label="展示按钮">
                                                                    {btn.btnShowFlag ===
                                                                    MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                                        <Tag color="success">
                                                                            展示按钮
                                                                        </Tag>
                                                                    ) : (
                                                                        <Tag color="error">
                                                                            不展示按钮
                                                                        </Tag>
                                                                    )}
                                                                </Descriptions.Item>
                                                                {btn.btnShowFlag ===
                                                                    MODULE_BTN_SHOW_ENUM.SHOW && (
                                                                    <Descriptions.Item label="按钮文案">
                                                                        <Tag>{btn.btnDesc}</Tag>
                                                                    </Descriptions.Item>
                                                                )}
                                                                {btn.btnShowFlag ===
                                                                    MODULE_BTN_SHOW_ENUM.SHOW && (
                                                                    <Descriptions.Item label="按钮操作">
                                                                        <Space>
                                                                            {btn.btnClickFlag ===
                                                                            MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                                <Tag color="success">
                                                                                    按钮可点击
                                                                                </Tag>
                                                                            ) : (
                                                                                <Tag color="error">
                                                                                    按钮不可点击
                                                                                </Tag>
                                                                            )}
                                                                            {btn.btnClickFlag ===
                                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                                btn.status ===
                                                                                    MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                                    <Typography.Text type="secondary">
                                                                                        点击按钮时跳转页面
                                                                                    </Typography.Text>
                                                                                )}
                                                                            {btn.btnClickFlag ===
                                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                                btn.status ===
                                                                                    MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                                    <Typography.Text type="secondary">
                                                                                        点击按钮发放奖励
                                                                                    </Typography.Text>
                                                                                )}
                                                                        </Space>
                                                                    </Descriptions.Item>
                                                                )}
                                                                {btn.btnShowFlag ===
                                                                    MODULE_BTN_SHOW_ENUM.SHOW &&
                                                                    btn.btnClickFlag ===
                                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                    btn.status ===
                                                                        MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                        <Descriptions.Item label="跳转链接">
                                                                            {btn?.btnPath}
                                                                        </Descriptions.Item>
                                                                    )}
                                                            </Descriptions>
                                                        </Panel>
                                                    );
                                                })}
                                            </Collapse>
                                        </Panel>
                                    );
                                })}
                            </Collapse>
                        </div>
                    </>
                )}
                {data?.data?.relaActType === ComponentModuleTypeEnum.RECHARGE && (
                    <RechargeDetail info={data.data} taskInfo={taskData?.data}></RechargeDetail>
                )}
                {data?.data?.relaActType === ComponentModuleTypeEnum.STATION_RECOMMEND && (
                    <StationRecommendDetail info={data.data}></StationRecommendDetail>
                )}
                {data?.data?.relaActType === ComponentModuleTypeEnum.SIGN && (
                    <>
                        <Typography.Title level={5}>主模块配置</Typography.Title>
                        <Descriptions column={1} colon={false}>
                            <Descriptions.Item>
                                <Radio checked>取活动固定配置</Radio>
                            </Descriptions.Item>
                        </Descriptions>
                    </>
                )}
                {(data?.data?.relaActType === ComponentModuleTypeEnum.REWARD ||
                    data?.data?.relaActType === ComponentModuleTypeEnum.EQUITY) && (
                    <>
                        <Space direction="vertical" style={{ width: '100%' }}>
                            <Typography.Title level={5}>主模块配置</Typography.Title>
                            <Descriptions column={1}>
                                <Descriptions.Item label="标题">
                                    {data?.data?.mainModuleConfQueryVo?.title}
                                </Descriptions.Item>
                                <Descriptions.Item label="副文案">
                                    {data?.data?.mainModuleConfQueryVo?.subTitle}
                                </Descriptions.Item>
                            </Descriptions>
                            <Typography.Title level={5}>主模块奖品展示配置</Typography.Title>
                            <Descriptions column={1}>
                                <Descriptions.Item label="奖品图片">
                                    <Image
                                        src={data?.data?.mainModuleConfQueryVo?.prizeImagePath}
                                    />
                                </Descriptions.Item>
                                <Descriptions.Item label="奖品说明">
                                    {data?.data?.mainModuleConfQueryVo?.prizeRemark}
                                </Descriptions.Item>
                            </Descriptions>
                        </Space>
                    </>
                )}
            </Space>
        </ProCard>
    );
};

export default React.memo(ModuleDetail);
