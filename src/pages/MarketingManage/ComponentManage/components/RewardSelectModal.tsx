// 选择发奖活动
import { usePagination } from 'ahooks';
import { Button, Col, Form, Input, Modal, Select, Space, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import moment from 'moment';
import React, { useImperativeHandle, useState } from 'react';
import { Link } from 'umi';

import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { getGiftListApi } from '@/services/Marketing/MarketingGiftApi2';
import { ACTSUBTYPES } from '@/config/declare';

interface Props {
    onSelect: (record: any) => void;
}
const RewardSelectModal = (props: Props, ref: any) => {
    const { onSelect } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [cooperationPlatform, setCooperationPlatform] = useState<string>('');

    useImperativeHandle(ref, () => {
        return {
            open: (cooperationPlatform: string) => {
                setVisible(true);
                setCooperationPlatform(cooperationPlatform);
                onFinish({ cooperationPlatform });
            },
        };
    });

    const onCancel = () => {
        form.resetFields();
        mutate(undefined);
        setSelectedRowKeys([]);
        setSelectedRows([]);
        setVisible(false);
    };

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        refresh,
        mutate,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getGiftListApi({
                cooperationPlatform: cooperationPlatform,
                ...params,
                actSubType: ACTSUBTYPES.PRIZE_SEND,
                actState: '1,2',
                pageSize,
                pageIndex: current,
                actVersion: '2.0',
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const onFinish = (formData: any) => {
        searchList(pagination, {
            ...formData,
            rewardType: formData?.rewardType?.join(','),
        });
    };
    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, {});
    };

    const onConfirmSelect = () => {
        if (selectedRows && selectedRows.length > 0) {
            if (onSelect) {
                onSelect(selectedRows[0]);
            }
            onCancel();
        } else {
            message.error('请选择要关联的任务包');
        }
    };

    const columns: ColumnsType<API.TaskPackageQueryVo> = [
        {
            title: '发奖活动ID',
            width: 140,
            dataIndex: 'actId',
        },
        {
            title: '活动名称',
            width: 120,
            dataIndex: 'actName',
        },
        {
            title: '活动类型',
            width: 120,
            dataIndex: 'actSubTypeName',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createTime',
            render(value) {
                return value ? moment(value)?.format('YYYY-MM-DD HH:mm:ss') : '-';
            },
        },
        {
            title: '更新时间',
            width: 120,
            dataIndex: 'dataOperTime',
            render(value) {
                return value ? moment(value)?.format('YYYY-MM-DD HH:mm:ss') : '-';
            },
        },
        {
            title: '创建来源',
            width: 120,
            dataIndex: 'createSourceName',
        },
        {
            title: '创建人',
            width: 100,
            dataIndex: 'creEmp',
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'actStateName',
        },
    ];

    return (
        <Modal
            title="选择关联活动"
            destroyOnClose
            onCancel={onCancel}
            onOk={onConfirmSelect}
            visible={visible}
            width={1080}
        >
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                    <Col span={6}>
                        <Form.Item label="活动名称" name="actName">
                            <Input maxLength={16} allowClear placeholder="请输入活动名称" />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item label="活动ID" name="actId">
                            <Input maxLength={32} allowClear placeholder="请输入活动ID" />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <div className={styles['btn-bar']}>
                <Space>
                    <Link
                        to={`/marketing/couponCenter/applet-gift/list/add?actSubType=${ACTSUBTYPES.PRIZE_SEND}`}
                        target="_blank"
                    >
                        <Button type="primary" icon="+">
                            创建活动
                        </Button>
                    </Link>
                    <Button onClick={refresh}>刷新</Button>
                </Space>
            </div>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="actId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                rowSelection={{
                    fixed: true,
                    onChange: (newSelectedRowKeys: any[], newSelectedRows: any[]) => {
                        console.log(
                            'selectedRowKeys changed: ',
                            newSelectedRowKeys,
                            newSelectedRows,
                        );
                        setSelectedRowKeys(newSelectedRowKeys);
                        setSelectedRows(newSelectedRows);
                    },
                    selectedRowKeys,
                    type: 'radio',
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Modal>
    );
};

export default React.forwardRef(RewardSelectModal);
