import { usePagination } from 'ahooks';
import { Button, Col, Form, Input, Modal, Select, Space, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import moment from 'moment';
import React, { useImperativeHandle, useState } from 'react';
import { Link } from 'umi';

import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { getTaskPackagePageList } from '@/services/Marketing/MngEventApi';
interface Props {
    onSelect: (record: any) => void;
}
const TaskSelectModal = (props: Props, ref: any) => {
    const { onSelect } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const [cooperationPlatform, setCooperationPlatform] = useState<string>('');

    useImperativeHandle(ref, () => {
        return {
            open: (cooperationPlatform: string) => {
                setVisible(true);
                setCooperationPlatform(cooperationPlatform);
                onFinish({ cooperationPlatform });
            },
        };
    });

    const onCancel = () => {
        form.resetFields();
        mutate(undefined);
        setSelectedRowKeys([]);
        setSelectedRows([]);
        setVisible(false);
    };

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        refresh,
        mutate,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getTaskPackagePageList({
                cooperationPlatform: cooperationPlatform,
                bizType: '01', //默认任务包类型
                ...params,
                processesFlag: '1',
                activeCrowdFlag: '1',
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const onFinish = (formData: any) => {
        searchList(pagination, {
            ...formData,
            rewardType: formData?.rewardType?.join(','),
        });
    };
    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, { cooperationPlatform });
    };

    const onConfirmSelect = () => {
        if (selectedRows && selectedRows.length > 0) {
            if (onSelect) {
                onSelect(selectedRows[0]);
            }
            onCancel();
        } else {
            message.error('请选择要关联的任务包');
        }
    };

    const columns: ColumnsType<API.TaskPackageQueryVo> = [
        {
            title: '任务包ID',
            width: 120,
            dataIndex: 'packageId',
        },
        {
            title: '任务名称',
            width: 120,
            dataIndex: 'packageName',
        },
        {
            title: '奖励规则',
            width: 120,
            dataIndex: 'rewardTypeName',
        },
        {
            title: '任务奖励触发规则',
            width: 80,
            dataIndex: 'obtainTypeName',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createdTime',
            render(value) {
                return value ? moment(value)?.format('YYYY-MM-DD HH:mm:ss') : '-';
            },
        },
        {
            title: '创建人',
            width: 80,
            dataIndex: 'createdBy',
        },
        {
            title: '状态',
            width: 80,
            dataIndex: 'packageStateName',
        },
    ];

    return (
        <Modal
            title="选择关联任务包"
            destroyOnClose
            onCancel={onCancel}
            onOk={onConfirmSelect}
            visible={visible}
            width={1080}
        >
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                    <Col span={6}>
                        <Form.Item label="任务包名称" name="packageName">
                            <Input maxLength={16} allowClear placeholder="请输入任务包名称" />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item label="任务包ID" name="packageId">
                            <Input maxLength={32} allowClear placeholder="请输入任务包ID" />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item label="奖励规则" name="rewardType">
                            <Select
                                options={[
                                    {
                                        label: '单独领奖',
                                        value: '01',
                                    },
                                    {
                                        label: '合并领奖 ',
                                        value: '02',
                                    },
                                ]}
                                mode="multiple"
                                allowClear
                                placeholder="请选择奖励规则"
                            />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <div className={styles['btn-bar']}>
                <Space>
                    <Link to="/marketing/event/task-package/list/add" target="_blank">
                        <Button type="primary" icon="+">
                            创建活动
                        </Button>
                    </Link>
                    <Button onClick={refresh}>刷新</Button>
                </Space>
            </div>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="packageId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                rowSelection={{
                    fixed: true,
                    onChange: (newSelectedRowKeys: any[], newSelectedRows: any[]) => {
                        console.log(
                            'selectedRowKeys changed: ',
                            newSelectedRowKeys,
                            newSelectedRows,
                        );
                        setSelectedRowKeys(newSelectedRowKeys);
                        setSelectedRows(newSelectedRows);
                    },
                    selectedRowKeys,
                    type: 'radio',
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Modal>
    );
};

export default React.forwardRef(TaskSelectModal);
