import { useMemo, useRef } from 'react';
import { Descriptions, Typography } from 'antd';
import RuleView, {
    RuleTypes,
} from '@/pages/AssetCenter/StationCardLabel/CardLabelManage/Edit/RuleView';
import ShowStationModal from '@/components/OperStationSearchList/SearchList/ShowStationModal';
import { isEmpty, renderTableDataIndexText } from '@/utils/utils';

const StationRecommendDetail = (props: { info: any }) => {
    const { info } = props;

    const showStationRef = useRef();

    const stationRecommendQueryVo = useMemo(() => {
        return info?.stationRecommendQueryVo;
    }, [info]);
    const configRuleTypeName = useMemo(() => {
        const { configRuleType } = stationRecommendQueryVo;
        switch (configRuleType) {
            case RuleTypes.Station:
                return '固定站点';
            case RuleTypes.Rule:
                return '按规则获取站点';
            default:
                return '';
        }
    }, [stationRecommendQueryVo]);
    // case ComponentModuleTypeEnum.STATION_RECOMMEND:
    //             const { stationRecommendQueryVo } = data?.data;

    //             params.configRuleType = stationRecommendQueryVo?.configRuleType;
    //             params.distance = stationRecommendQueryVo?.distance;

    //             params.stationList = stationRecommendQueryVo?.detailList;
    return (
        <>
            <Descriptions column={1} colon={false}>
                <Descriptions.Item label={`距离范围`}>
                    {stationRecommendQueryVo?.distance}km内的站点才展示
                </Descriptions.Item>
                <Descriptions.Item label={`标签规则`}>{configRuleTypeName}</Descriptions.Item>
                {stationRecommendQueryVo?.configRuleType == RuleTypes.Station && (
                    <Descriptions.Item label={`站点信息`}>
                        <Typography.Link
                            onClick={() => {
                                showStationRef?.current?.open(stationRecommendQueryVo?.detailList);
                            }}
                        >
                            查看
                        </Typography.Link>
                    </Descriptions.Item>
                )}
            </Descriptions>
            <ShowStationModal
                ref={showStationRef}
                showFirstTime={false}
                extendColumns={[
                    {
                        title: '生效时间',
                        dataIndex: 'effTime',
                        width: '200px',
                        render(text: string) {
                            return renderTableDataIndexText({ text });
                        },
                    },
                    {
                        title: '过期时间',
                        dataIndex: 'expTime',
                        width: '200px',
                        render(text: string) {
                            return renderTableDataIndexText({ text });
                        },
                    },
                ]}
            ></ShowStationModal>
        </>
    );
};
export default StationRecommendDetail;
