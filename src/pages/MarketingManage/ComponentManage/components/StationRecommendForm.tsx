import { Typography, Form, InputNumber, Radio } from 'antd';
import { useEffect, useRef } from 'react';
import { ComponentModuleTypeEnum } from '@/constants/componentManage';
import { isEmpty, renderTableDataIndexText } from '@/utils/utils';
import RuleView, {
    RuleTypes,
} from '@/pages/AssetCenter/StationCardLabel/CardLabelManage/Edit/RuleView';
import { ConfigSubTypes } from '@/pages/AssetCenter/StationCardLabel/CardLabelManage/Edit/index';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';

const StationRecommendForm = (props: { type: string; defaultStations?: any[] }) => {
    const { type, defaultStations } = props;
    const stationRef = useRef();
    useEffect(() => {
        if (!isEmpty(defaultStations)) {
            stationRef?.current?.setAllStations(defaultStations);
        }
    }, [defaultStations]);
    return (
        (type === ComponentModuleTypeEnum.STATION_RECOMMEND && (
            <>
                <Typography.Title level={4}>规则配置</Typography.Title>
                <Form.Item
                    label="距离范围"
                    name="distance"
                    required
                    rules={[
                        // { required: true, message: '请输入规则配置' },
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请输入规则配置`);
                                }
                                if (value > 10 || value < 1) {
                                    return Promise.reject(`范围最小为1，最大为10`);
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <InputNumber
                        addonAfter="km内的站点才展示"
                        // max={10}
                        step={1}
                        precision={0}
                        addonBefore="距离为"
                    ></InputNumber>
                </Form.Item>

                <Form.Item
                    label="标签规则"
                    name="configRuleType"
                    initialValue={RuleTypes.Station}
                    wrapperCol={{ span: 24 }}
                    rules={[{ required: true, message: '请选择' }]}
                >
                    <Radio.Group>
                        <Radio value={RuleTypes.Station}>固定站点</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item noStyle shouldUpdate={(prevValues, curValues) => true}>
                    {({ getFieldValue, setFieldsValue }) => {
                        const configRuleType = getFieldValue('configRuleType');
                        const stationInfo = getFieldValue('stationInfo');
                        const { allStations = [] } = stationInfo ?? {};
                        const disabledDelIds = allStations
                            .filter((ele) => ele.equityFlag)
                            ?.map((ele) => ele.stationId);

                        console.log(5566, allStations, disabledDelIds);
                        return (
                            configRuleType == RuleTypes.Station && (
                                <Form.Item
                                    label=""
                                    name={'stationInfo'}
                                    wrapperCol={{ span: 24 }}
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                const { addStations, allStations } = value;
                                                if (isEmpty(allStations) && isEmpty(addStations)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                if (
                                                    addStations.length > 20000 ||
                                                    allStations.length > 20000
                                                ) {
                                                    return Promise.reject(
                                                        '一次性最多配置2万个站点',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <SearchStationItem
                                        ref={stationRef}
                                        title="活动范围"
                                        deleteAllEnabled={false}
                                        disabledStationIds={disabledDelIds}
                                        showFirstTime={false}
                                        extendColumns={[
                                            {
                                                title: '生效时间',
                                                dataIndex: 'effTime',
                                                width: '200px',
                                                render(text: string) {
                                                    return renderTableDataIndexText({ text });
                                                },
                                            },
                                            {
                                                title: '过期时间',
                                                dataIndex: 'expTime',
                                                width: '200px',
                                                render(text: string) {
                                                    return renderTableDataIndexText({ text });
                                                },
                                            },
                                        ]}
                                    ></SearchStationItem>
                                </Form.Item>
                            )
                        );
                    }}
                </Form.Item>

                {/* <Form.Item
                    name="configSubType"
                    initialValue={ConfigSubTypes.Offline}
                    hidden
                ></Form.Item>
                <RuleView stationRef={stationRef} ConfigSubTypes={ConfigSubTypes}></RuleView> */}
            </>
        )) ||
        null
    );
};

export default StationRecommendForm;
