import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Switch, Form, Input, message, Typography } from 'antd';
import { isEmpty } from '@/utils/utils';
import ChargeLinkChildTaskItem from './ChargeLinkChildTaskItem';

type ProgressVo = {
    taskId?: string;
    showProgressFlag?: number;
    showContent?: string;
};
export const checkProgressItem = (info: ProgressVo) => {
    let errMsg = '';
    if (info?.showProgressFlag == 1) {
        if (isEmpty(info.taskId) || isEmpty(info.showContent)) {
            errMsg = '请完善任务进度信息';
        }
    }
    return errMsg;
};
const ProgressFormItem = (
    props: API.CommonFormItem<ProgressVo> & { taskList?: any[] },
    ref: any,
) => {
    const { value, onChange, disabled = false, taskList = [] } = props;

    const [info, updateInfo] = useState<ProgressVo>();

    useEffect(() => {
        if (value) {
            updateInfo(value);
        }
    }, [value]);

    const changeInfoEvent = (params: ProgressVo) => {
        updateFormItem({
            ...info,
            ...params,
        });
    };

    const updateFormItem = (params: ProgressVo) => {
        updateInfo(params);
        onChange && onChange(params);
    };

    useImperativeHandle(ref, () => {
        return {};
    });
    return (
        <>
            <Form.Item label="展示任务进度">
                <Switch
                    checkedChildren="展示"
                    unCheckedChildren="不展示"
                    checked={info?.showProgressFlag === 1}
                    disabled={disabled}
                    onChange={(checked) => {
                        if (checked) {
                            changeInfoEvent({
                                showProgressFlag: 1,
                            });
                        } else {
                            changeInfoEvent({
                                showProgressFlag: 0,
                            });
                        }
                    }}
                />
            </Form.Item>
            {info?.showProgressFlag === 1 && (
                <>
                    <Form.Item
                        label="关联任务"
                        required
                        help={
                            !info.taskId && (
                                <Typography.Text type="danger">
                                    请关联展示进度的任务
                                </Typography.Text>
                            )
                        }
                        validateStatus={!info.taskId ? 'error' : ''}
                    >
                        <ChargeLinkChildTaskItem
                            value={info?.taskId}
                            taskList={taskList}
                            onChange={(newValue) => {
                                changeInfoEvent({
                                    taskId: newValue,
                                });
                            }}
                        ></ChargeLinkChildTaskItem>
                    </Form.Item>
                    <Form.Item
                        label="用户端展示文案"
                        required
                        help={
                            !info.showContent && (
                                <Typography.Text type="danger">
                                    请配置用户端展示文案
                                </Typography.Text>
                            )
                        }
                        validateStatus={!info.showContent ? 'error' : ''}
                    >
                        <Input
                            value={info?.showContent}
                            maxLength={6}
                            showCount
                            allowClear
                            onChange={(e) => {
                                changeInfoEvent({
                                    showContent: e.target.value,
                                });
                            }}
                        />
                    </Form.Item>
                </>
            )}
        </>
    );
};
export default forwardRef(ProgressFormItem);
