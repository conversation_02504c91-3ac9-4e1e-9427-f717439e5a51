import { Form, Input, Radio, Typography, Collapse, Space, Tag, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React from 'react';
import { Link } from 'umi';

import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';
import {
    ComponentModuleTypeEnum,
    MODULE_BTN_CLICK_ENUM,
    MODULE_BTN_SHOW_ENUM,
    MODULE_BTN_STATUS_ENUM,
    TASK_REWARD_TYPE_ENUM,
    moduleBtnTitleEnumWhenMerge,
    moduleBtnTitleEnumWhenOneself,
    moduleTaskTitleEnumWhenMerge,
    moduleTaskTitleEnumWhenOneself,
} from '@/constants/componentManage';

const { Panel } = Collapse;

const TaskForm: React.FC<{
    //新增任务包类型
    type: string;
    //FORM表单对象
    form: FormInstance;
    //初始化数据，编辑或者有复制功能使用
    initialValue?: API.ModuleQueryVo;
    //关联任务包
    relativeAct: API.TaskPackageQueryVo;
    //主模块按钮配置
    mainBtnConfList: API.ModuleBtnConfParam[];
    //任务配置
    moduleTaskConfParamList: API.ModuleTaskConfParam[];
    changeModuleTaskConfParamList?: (taskId: string, info: API.ModuleBtnConfParam) => void;
    changeModuleTaskBtnConfParamList: (taskId: string, list: API.ModuleBtnConfParam[]) => void;
}> = ({
    type,
    relativeAct,
    form,
    initialValue,
    mainBtnConfList,
    moduleTaskConfParamList,
    changeModuleTaskConfParamList,
    changeModuleTaskBtnConfParamList,
}) => {
    return (
        <>
            {/* 任务包 展示性字段 */}
            <Typography.Title level={4}>触发和完成规则</Typography.Title>
            <Form.Item label="任务触发">
                <Typography.Text>{relativeAct?.openTypeName}</Typography.Text>
            </Form.Item>
            <Form.Item label="任务完成">
                <Typography.Text>{relativeAct?.obtainTypeName}</Typography.Text>
            </Form.Item>
            <Form.Item label="领奖规则">
                <Typography.Text>{relativeAct?.rewardTypeName}</Typography.Text>
            </Form.Item>
            {/* 任务包 配置性字段 */}
            <Typography.Title level={4}>展示配置</Typography.Title>
            <Form.Item
                name="configModule"
                initialValue="main"
                validateTrigger="submit"
                rules={[
                    {
                        validator(rule, value, callback) {
                            if (value === 'main') {
                                try {
                                    moduleTaskConfParamList?.forEach((task) => {
                                        if (!task.title) {
                                            throw new Error('请完成任务条件配置');
                                        } else {
                                            task.moduleBtnConfParamList?.forEach((btn) => {
                                                if (
                                                    btn.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW &&
                                                    btn.btnClickFlag ===
                                                        MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                    !btn.btnPath
                                                ) {
                                                    throw new Error('请完成任务条件配置');
                                                }
                                            });
                                        }
                                    });
                                } catch (error) {
                                    return Promise.reject('请完成任务条件配置');
                                }
                                return Promise.resolve();
                            } else {
                                if (type === ComponentModuleTypeEnum.TASK) {
                                    const title = form.getFieldValue('title');
                                    const prizeImage = form.getFieldValue('prizeImage');
                                    if (!title || !prizeImage) {
                                        return Promise.reject('请完成主模块配置');
                                    }
                                    if (relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE) {
                                        const prizeName = form.getFieldValue('prizeName');
                                        if (!prizeName) {
                                            return Promise.reject('请完成主模块配置');
                                        }
                                    }
                                }
                            }
                            return Promise.resolve();
                        },
                    },
                ]}
            >
                <Radio.Group
                    buttonStyle="solid"
                    defaultValue="main"
                    size="large"
                    style={{ marginTop: 16 }}
                >
                    <Radio.Button value="main">
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            主模块配置
                        </Space>
                    </Radio.Button>
                    <Radio.Button value="condition">
                        <Space>
                            <Typography.Text type="danger">*</Typography.Text>
                            任务条件配置
                        </Space>
                    </Radio.Button>
                </Radio.Group>
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(prev, cur) => prev.configModule !== cur.configModule}>
                {({ getFieldValue }) => {
                    const configModule = getFieldValue('configModule');
                    return (
                        <>
                            <div
                                style={{
                                    opacity: configModule === 'main' ? 1 : 0,
                                    height: configModule === 'main' ? 'auto' : 0,
                                    overflow: 'hidden',
                                }}
                            >
                                <Typography.Title level={5}>主模块配置</Typography.Title>
                                <Form.Item
                                    name="title"
                                    label="标题"
                                    required
                                    rules={[
                                        { required: true, whitespace: true, message: '请输入标题' },
                                    ]}
                                    wrapperCol={{ span: 8 }}
                                >
                                    <Input
                                        maxLength={16}
                                        showCount
                                        allowClear
                                        placeholder="请输入标题"
                                    />
                                </Form.Item>
                                <Typography.Title level={5}>主模块奖品展示配置</Typography.Title>
                                <Form.Item
                                    name="prizeImage"
                                    label="奖品图片"
                                    required
                                    rules={[{ required: true, message: '请上传奖品图片' }]}
                                    wrapperCol={{ span: 8 }}
                                >
                                    <UpLoadImgCustom
                                        maxCount={1}
                                        sizeInfo={{
                                            width: 100,
                                            height: 100,
                                            size: 100,
                                        }}
                                        multiple={false}
                                        initialValue={
                                            form.getFieldValue('prizeImage') &&
                                            initialValue?.mainModuleConfQueryVo?.prizeImagePath
                                                ? [
                                                      {
                                                          url: initialValue?.mainModuleConfQueryVo
                                                              ?.prizeImagePath,
                                                          uid: initialValue?.mainModuleConfQueryVo
                                                              ?.prizeImagePath,
                                                          status: 'done',
                                                      },
                                                  ]
                                                : undefined
                                        }
                                        placeholder="格式支持png、jpg、jpeg,大小100px * 100px, 不得超过100KB"
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'iconUrl',
                                            relaTable: 'e_mkt_act',
                                        }}
                                    />
                                </Form.Item>
                                {relativeAct?.rewardType === TASK_REWARD_TYPE_ENUM.MERGE && (
                                    <Form.Item
                                        name="prizeName"
                                        label="奖品名称"
                                        required
                                        rules={[
                                            {
                                                required: true,
                                                whitespace: true,
                                                message: '请输入奖品名称',
                                            },
                                        ]}
                                        wrapperCol={{ span: 8 }}
                                    >
                                        <Input
                                            maxLength={8}
                                            allowClear
                                            placeholder="请输入奖品名称"
                                            showCount
                                        />
                                    </Form.Item>
                                )}
                                <Form.Item
                                    label="奖品说明"
                                    name="prizeRemark"
                                    wrapperCol={{ span: 8 }}
                                >
                                    <Input
                                        maxLength={100}
                                        showCount
                                        allowClear
                                        placeholder="请输入奖品说明"
                                    />
                                </Form.Item>
                                <Typography.Title level={5}>主模块按钮配置</Typography.Title>
                                <Collapse>
                                    {mainBtnConfList?.map((conf: API.ModuleBtnConfParam) => {
                                        return (
                                            <Panel
                                                header={
                                                    relativeAct?.rewardType ===
                                                    TASK_REWARD_TYPE_ENUM.MERGE
                                                        ? moduleBtnTitleEnumWhenMerge[
                                                              conf.status as string
                                                          ]
                                                        : moduleBtnTitleEnumWhenOneself[
                                                              conf.status as string
                                                          ] || conf.status
                                                }
                                                key={`${conf.status}`}
                                                forceRender
                                            >
                                                <Form.Item label="展示按钮" required>
                                                    {conf.btnShowFlag ===
                                                    MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                        <Tag color="success">展示按钮</Tag>
                                                    ) : (
                                                        <Tag color="error">不展示按钮</Tag>
                                                    )}
                                                </Form.Item>
                                                {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                    <Form.Item label="按钮文案" required>
                                                        <Tag>{conf.btnDesc}</Tag>
                                                    </Form.Item>
                                                )}
                                                {conf.btnShowFlag === MODULE_BTN_SHOW_ENUM.SHOW && (
                                                    <Form.Item label="按钮操作" required>
                                                        <Space>
                                                            {conf.btnClickFlag ===
                                                            MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                <Tag color="success">
                                                                    按钮可点击
                                                                </Tag>
                                                            ) : (
                                                                <Tag color="error">
                                                                    按钮不可点击
                                                                </Tag>
                                                            )}
                                                            {conf.btnClickFlag ===
                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                conf.status ===
                                                                    MODULE_BTN_STATUS_ENUM.UNSTART && (
                                                                    <Typography.Text type="secondary">
                                                                        点击按钮时触发领取任务
                                                                    </Typography.Text>
                                                                )}
                                                            {conf.btnClickFlag ===
                                                                MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                conf.status ===
                                                                    MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                    <Typography.Text type="secondary">
                                                                        点击按钮时触发发奖
                                                                    </Typography.Text>
                                                                )}
                                                        </Space>
                                                    </Form.Item>
                                                )}
                                            </Panel>
                                        );
                                    })}
                                </Collapse>
                            </div>
                            <div
                                style={{
                                    opacity: configModule === 'condition' ? 1 : 0,
                                    height: configModule === 'condition' ? 'auto' : 0,
                                    overflow: 'hidden',
                                }}
                            >
                                <Collapse
                                    defaultActiveKey={moduleTaskConfParamList?.map(
                                        (v) => `${v?.taskId}`,
                                    )}
                                >
                                    {moduleTaskConfParamList?.map((task, index) => {
                                        console.log(32323, task);
                                        return (
                                            <Panel
                                                header={`条件：${task?.taskName}（${task?.taskId}）`}
                                                key={`${task?.taskId}`}
                                                extra={
                                                    <Link
                                                        target="_blank"
                                                        to={`/marketing/event/task/list/detail?id=${task?.taskId}`}
                                                    >
                                                        详情
                                                    </Link>
                                                }
                                                forceRender
                                            >
                                                <Form.Item
                                                    label="标题"
                                                    required
                                                    help={
                                                        !task.title && (
                                                            <Typography.Text type="danger">
                                                                请输入奖品名称
                                                            </Typography.Text>
                                                        )
                                                    }
                                                    validateStatus={!task.title ? 'error' : ''}
                                                >
                                                    <Input
                                                        value={task.title}
                                                        maxLength={16}
                                                        showCount
                                                        allowClear
                                                        placeholder="请输入标题"
                                                        onChange={(event) => {
                                                            const {
                                                                target: { value: newVal },
                                                            } = event;

                                                            changeModuleTaskConfParamList &&
                                                                changeModuleTaskConfParamList(
                                                                    task?.taskId,
                                                                    {
                                                                        ...task,
                                                                        title: newVal,
                                                                    },
                                                                );
                                                        }}
                                                    />
                                                </Form.Item>
                                                {relativeAct?.rewardType ===
                                                    TASK_REWARD_TYPE_ENUM.ONE_SELF && (
                                                    <Form.Item
                                                        label="奖品名称"
                                                        required
                                                        wrapperCol={{ span: 8 }}
                                                        help={
                                                            !task.prizeName && (
                                                                <Typography.Text type="danger">
                                                                    请输入奖品名称
                                                                </Typography.Text>
                                                            )
                                                        }
                                                        validateStatus={
                                                            !task.prizeName ? 'error' : ''
                                                        }
                                                    >
                                                        <Input
                                                            value={task.prizeName}
                                                            maxLength={6}
                                                            showCount
                                                            allowClear
                                                            placeholder="请输入奖品名称"
                                                            onChange={(event) => {
                                                                const {
                                                                    target: { value: newVal },
                                                                } = event;

                                                                changeModuleTaskConfParamList &&
                                                                    changeModuleTaskConfParamList(
                                                                        task?.taskId,
                                                                        {
                                                                            ...task,
                                                                            prizeName: newVal,
                                                                        },
                                                                    );
                                                            }}
                                                        />
                                                    </Form.Item>
                                                )}
                                                <Collapse
                                                    defaultActiveKey={task.moduleBtnConfParamList
                                                        ?.filter(
                                                            (v) =>
                                                                v?.btnShowFlag ==
                                                                    MODULE_BTN_SHOW_ENUM.SHOW &&
                                                                v?.btnClickFlag ==
                                                                    MODULE_BTN_CLICK_ENUM.ENABLE,
                                                        )
                                                        ?.map((v) => v.status as string)}
                                                >
                                                    {task.moduleBtnConfParamList?.map(
                                                        (btn, index) => {
                                                            return (
                                                                <Panel
                                                                    header={
                                                                        relativeAct?.rewardType ===
                                                                        TASK_REWARD_TYPE_ENUM.MERGE
                                                                            ? moduleTaskTitleEnumWhenMerge[
                                                                                  btn.status as string
                                                                              ]
                                                                            : moduleTaskTitleEnumWhenOneself[
                                                                                  btn.status as string
                                                                              ] || btn.status
                                                                    }
                                                                    key={`${btn.status}`}
                                                                    forceRender
                                                                >
                                                                    {btn.status ==
                                                                    MODULE_BTN_STATUS_ENUM.RECIVE ? (
                                                                        <Form.Item label="展示按钮">
                                                                            <Select
                                                                                placeholder="请选择"
                                                                                value={
                                                                                    btn.btnShowFlag
                                                                                }
                                                                                onChange={(
                                                                                    newVal,
                                                                                ) => {
                                                                                    const newList =
                                                                                        [
                                                                                            ...task.moduleBtnConfParamList,
                                                                                        ];
                                                                                    newList[
                                                                                        index
                                                                                    ].btnShowFlag =
                                                                                        newVal;
                                                                                    changeModuleTaskBtnConfParamList &&
                                                                                        changeModuleTaskBtnConfParamList(
                                                                                            task?.taskId,
                                                                                            newList,
                                                                                        );
                                                                                }}
                                                                            >
                                                                                <Select.Option
                                                                                    value={
                                                                                        MODULE_BTN_SHOW_ENUM.SHOW
                                                                                    }
                                                                                >
                                                                                    展示按钮
                                                                                </Select.Option>
                                                                                <Select.Option
                                                                                    value={
                                                                                        MODULE_BTN_SHOW_ENUM.HIDE
                                                                                    }
                                                                                >
                                                                                    不展示按钮
                                                                                </Select.Option>
                                                                            </Select>
                                                                        </Form.Item>
                                                                    ) : (
                                                                        <Form.Item
                                                                            label="展示按钮"
                                                                            required
                                                                        >
                                                                            {btn.btnShowFlag ===
                                                                            MODULE_BTN_SHOW_ENUM.SHOW ? (
                                                                                <Tag color="success">
                                                                                    展示按钮
                                                                                </Tag>
                                                                            ) : (
                                                                                <Tag color="error">
                                                                                    不展示按钮
                                                                                </Tag>
                                                                            )}
                                                                        </Form.Item>
                                                                    )}

                                                                    {btn.btnShowFlag ===
                                                                        MODULE_BTN_SHOW_ENUM.SHOW && (
                                                                        <Form.Item
                                                                            label="按钮文案"
                                                                            required
                                                                        >
                                                                            <Tag>{btn.btnDesc}</Tag>
                                                                        </Form.Item>
                                                                    )}
                                                                    {btn.btnShowFlag ===
                                                                        MODULE_BTN_SHOW_ENUM.SHOW && (
                                                                        <Form.Item
                                                                            label="按钮操作"
                                                                            required
                                                                        >
                                                                            <Space>
                                                                                {btn.btnClickFlag ===
                                                                                MODULE_BTN_CLICK_ENUM.ENABLE ? (
                                                                                    <Tag color="success">
                                                                                        按钮可点击
                                                                                    </Tag>
                                                                                ) : (
                                                                                    <Tag color="error">
                                                                                        按钮不可点击
                                                                                    </Tag>
                                                                                )}
                                                                                {btn.btnClickFlag ===
                                                                                    MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                                    btn.status ===
                                                                                        MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                                        <Typography.Text type="secondary">
                                                                                            点击按钮时跳转页面
                                                                                        </Typography.Text>
                                                                                    )}
                                                                                {btn.btnClickFlag ===
                                                                                    MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                                    btn.status ===
                                                                                        MODULE_BTN_STATUS_ENUM.COMPLETE && (
                                                                                        <Typography.Text type="secondary">
                                                                                            点击按钮发放奖励
                                                                                        </Typography.Text>
                                                                                    )}
                                                                            </Space>
                                                                        </Form.Item>
                                                                    )}
                                                                    {btn.btnShowFlag ===
                                                                        MODULE_BTN_SHOW_ENUM.SHOW &&
                                                                        btn.btnClickFlag ===
                                                                            MODULE_BTN_CLICK_ENUM.ENABLE &&
                                                                        btn.status ===
                                                                            MODULE_BTN_STATUS_ENUM.RECIVE && (
                                                                            <Form.Item
                                                                                label="跳转链接"
                                                                                required
                                                                                help={
                                                                                    !btn.btnPath && (
                                                                                        <Typography.Text type="secondary">
                                                                                            请输入内部跳转链接，示例：/pagesPaymember/paymember/pay
                                                                                        </Typography.Text>
                                                                                    )
                                                                                }
                                                                                validateStatus={
                                                                                    !btn.btnPath
                                                                                        ? 'error'
                                                                                        : ''
                                                                                }
                                                                            >
                                                                                <Input
                                                                                    value={
                                                                                        btn?.btnPath ||
                                                                                        ''
                                                                                    }
                                                                                    onChange={(
                                                                                        e,
                                                                                    ) => {
                                                                                        const {
                                                                                            target: {
                                                                                                value: newVal,
                                                                                            },
                                                                                        } = e;

                                                                                        const newList =
                                                                                            [
                                                                                                ...task.moduleBtnConfParamList,
                                                                                            ];
                                                                                        newList[
                                                                                            index
                                                                                        ].btnPath =
                                                                                            newVal;
                                                                                        changeModuleTaskBtnConfParamList &&
                                                                                            changeModuleTaskBtnConfParamList(
                                                                                                task?.taskId,
                                                                                                newList,
                                                                                            );
                                                                                    }}
                                                                                    maxLength={500}
                                                                                    placeholder="请输入内部跳转链接，示例：/pagesPaymember/paymember/pay"
                                                                                    showCount
                                                                                    allowClear
                                                                                />
                                                                            </Form.Item>
                                                                        )}
                                                                </Panel>
                                                            );
                                                        },
                                                    )}
                                                </Collapse>
                                            </Panel>
                                        );
                                    })}
                                </Collapse>
                            </div>
                        </>
                    );
                }}
            </Form.Item>
        </>
    );
};

export default TaskForm;
