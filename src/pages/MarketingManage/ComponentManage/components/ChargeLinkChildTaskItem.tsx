import { forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { Button, Typography } from 'antd';
import ChargeLinkChildTaskModal from './ChargeLinkChildTaskModal';
const ChargeLinkChildTaskItem = (
    props: API.CommonFormItem<string> & { taskList: any[] },
    ref: any,
) => {
    const { value, onChange, disabled = false, taskList = [] } = props;
    const modalRef = useRef();
    useImperativeHandle(ref, () => {
        return {};
    });

    const taskResult = useMemo(() => {
        let result = '';
        if (value) {
            for (const element of taskList) {
                if (element.taskId == value) {
                    result = `${element.taskName}(${element.taskId})`;
                    break;
                }
            }
        }

        return result;
    }, [value, taskList]);

    return (
        <>
            {!value ? (
                !disabled && (
                    <Button
                        onClick={() => {
                            modalRef?.current?.open();
                        }}
                        type="primary"
                    >
                        关联任务
                    </Button>
                )
            ) : (
                <Typography.Link
                    onClick={() => {
                        modalRef?.current?.open([value]);
                    }}
                >
                    {taskResult}
                </Typography.Link>
            )}
            <ChargeLinkChildTaskModal
                taskList={taskList}
                ref={modalRef}
                onFinish={(list) => {
                    if (list instanceof Array) {
                        onChange && onChange(list?.[0]?.taskId);
                    }
                }}
            ></ChargeLinkChildTaskModal>
        </>
    );
};

export default forwardRef(ChargeLinkChildTaskItem);
