import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import {
    Button,
    Card,
    Input,
    InputNumber,
    Select,
    Form,
    Checkbox,
    DatePicker,
    Tooltip,
    message,
    Space,
    Radio,
    Switch,
    Divider,
    Popconfirm,
    Col,
    Typography,
} from 'antd';

import { InfoCircleOutlined, DeleteOutlined } from '@ant-design/icons';

import { ACT_TYPES, LARGE_ACT_TYPES } from '@/config/declare';

import SelectActiveItem from '../../LargeActive/components/SelectActiveItem';
import { turnColumns } from '../../LargeActive/components/ActiveColumns';

import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import styles from '../Invite.less';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree/index';
import CityTransferModal from '@/components/CityTransferModal';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import QuillRichInput from '@/components/QuillRichInput';
import SelectCouponItem, {
    COUPON_CUSTOM_PAGE_TYPE,
} from '@/newComponents/SelectPrizeItem/SelectCouponItem';
import { AppendItem } from '../../TurnOver/TurnOverUpdatePage';
import baseStyles from '@/assets/styles/base.less';
import { getTurntableApi } from '@/services/Marketing/MarketingLargeActiveApi';

import PriceCrowdItem, { validatorCrowdItem } from '../../Membership/components/PriceCrowdItem';

const { RangePicker } = DatePicker;

const { Option } = Select;

const FormItem = Form.Item;

// 任务类型
export const TASK_TYPES = {
    TASK_TAKE: '01', // 领奖
    TASK_REGIST: '02', // 注册
    TASK_CHARGE: '03', // 充电
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const isSuperset = (setA, setB) => {
    let bigSet = setA;
    let smallSet = setB;
    if (setA.size < setB.size) {
        bigSet = setB;
        smallSet = setA;
    }
    for (let elem of smallSet) {
        if (!bigSet.has(elem)) {
            return false;
        }
    }
    return true;
};

const hasIntersectingActIds = (originData) => {
    if (!Array.isArray(originData) || originData.length === 0) {
        return false;
    }

    for (let index = 0; index < originData.length; index++) {
        const item = originData[index];

        // 获取当前元素内部的 invitedBos 数组中的 invitedTurnPrize 包含所有 actId

        const currentActIds = new Set();

        if (Array.isArray(item.invitedBos)) {
            for (const bos of item.invitedBos) {
                if (bos?.prizeType?.includes('02') && Array.isArray(bos.invitedTurnPrize)) {
                    for (const prize of bos.invitedTurnPrize) {
                        if (prize.actId) {
                            currentActIds.add(prize.actId);
                        }
                    }
                }
            }
        }

        for (let i = 0; i < originData.length; i++) {
            if (index != i) {
                // 和非当前元素判断，判断是否是包含结果
                const element = originData[i];
                const otherActIds = new Set();

                if (Array.isArray(element.invitedBos)) {
                    for (const bos of element.invitedBos) {
                        if (bos?.prizeType?.includes('02') && Array.isArray(bos.invitedTurnPrize)) {
                            for (const prize of bos.invitedTurnPrize) {
                                if (prize.actId) {
                                    otherActIds.add(prize.actId);
                                }
                            }
                        }
                    }
                }

                if (
                    currentActIds.size > 0 &&
                    otherActIds.size > 0 &&
                    !isSuperset(otherActIds, currentActIds)
                ) {
                    return false;
                }
            }
        }
    }

    return true;
};

export const initTaskInfo = [
    {
        inviteTaskType: TASK_TYPES.TASK_TAKE,
        invitedBos: [
            {
                inviteTaskSn: '1',
                inviteTargetType: '01',
                limitType: '1',
            },
        ],
        inviterBos: [{ inviteTaskSn: '1', inviteTargetType: '02', temp_0: '0' }],
    },
    {
        inviteTaskType: TASK_TYPES.TASK_REGIST,
        invitedBos: [
            {
                inviteTaskSn: '1',
                inviteTargetType: '01',
                activeCrowd: '28',
                limitType: '2',
                limitNum: '1',
            },
        ],
        inviterBos: [{ inviteTaskSn: '1', inviteTargetType: '02', temp_0: '0' }],
    },
    {
        inviteTaskType: TASK_TYPES.TASK_CHARGE,
        invitedBos: [
            {
                inviteTaskSn: '1',
                inviteTargetType: '01',
                limitType: '1',
            },
        ],
        inviterBos: [{ inviteTaskSn: '1', inviteTargetType: '02', temp_0: '0' }],
    },
];

export const InfoFormLayout = (props) => {
    const { editActInfo, disabled, form } = props;

    const cityModalRef = useRef();
    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    const disabledDate = (current) => {
        if (
            isEmpty(editActInfo) ||
            JSON.stringify(editActInfo) == '{}' ||
            editActInfo?.actState < 2
        ) {
            return false;
        }
        if (editActInfo?.actState == 2) {
            let endTime = moment(editActInfo.expTime, 'YYYY-MM-DD HH:mm:ss');
            // 进行中的活动结束时间，不可在修改时间之前
            if (current && endTime) {
                return current <= endTime;
            }
        }

        return true;
    };

    return (
        <Fragment>
            <FormItem
                label="活动名称:"
                name="actName"
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请填写活动名称' },
                    () => ({
                        validator(rule, value) {
                            if (value && value.length > 24) {
                                return Promise.reject('限24个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Input placeholder="限24个字" autoComplete="off" maxLength={24} showCount />
            </FormItem>

            <FormItem
                label="活动有效期:"
                name="dateTime"
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请选择活动时间' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value) {
                                return Promise.reject('');
                            }
                            if (!value[0]) {
                                return Promise.reject('请选择活动开始日期');
                            }
                            if (!value[1]) {
                                return Promise.reject('请选择活动失效日期');
                            }
                            if (value[1]) {
                                const nowTime = +new Date();
                                const sendEndTime = +new Date(value[1]);

                                if (sendEndTime < nowTime) {
                                    return Promise.reject('活动失效日期不能早于当前时间');
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <RangePicker
                    disabled={[disabled, false]}
                    showTime={{
                        format: 'HH:mm:ss',
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    }}
                    disabledTime={disabledRangeTime}
                    disabledDate={disabledDate}
                    format="YYYY-MM-DD HH:mm:ss"
                />
            </FormItem>

            <FormItem
                label="活动用途"
                name="actLbl"
                rules={[
                    {
                        required: editActInfo?.actState >= 1 ? false : true,
                        message: '请填写活动名称',
                    },
                ]}
                initialValue={'02'}
                {...formItemFixedWidthLayout}
                required
            >
                {editActInfo?.actState >= 1 ? (
                    editActInfo.actLblName
                ) : (
                    <Select placeholder="请选择" allowClear>
                        {[
                            { codeValue: '01', codeName: '线上营销' },
                            { codeValue: '02', codeName: '线下推广' },
                        ].map((ele) => {
                            return (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            );
                        })}
                    </Select>
                )}
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actLbl != curValues.actLbl || prevValues.actCity != curValues.actCity
                }
            >
                {({ getFieldValue }) => {
                    const actLbl = getFieldValue('actLbl');
                    const actCity = getFieldValue('actCity');
                    return (
                        actLbl == '02' && (
                            <FormItem
                                label="推广区域"
                                name="actCity"
                                required
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (!actCity?.length) {
                                                return Promise.reject('请选择推广区域');
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                {(actCity?.length && (
                                    <Space size="small">
                                        <a
                                            onClick={() => {
                                                cityModalRef?.current?.show({
                                                    defaultKeys: actCity,
                                                });
                                            }}
                                        >
                                            编辑
                                        </a>
                                        <Divider type="vertical" style={{ margin: '0 12px' }} />
                                        <a
                                            onClick={() =>
                                                cityModalRef?.current?.show({
                                                    defaultKeys: actCity,
                                                    disabled: true,
                                                })
                                            }
                                        >
                                            查看
                                        </a>
                                    </Space>
                                )) || (
                                        <a
                                            onClick={() => {
                                                cityModalRef?.current?.show();
                                            }}
                                            disabled={editActInfo?.actState > 2}
                                        >
                                            新增城市
                                        </a>
                                    ) ||
                                    null}
                            </FormItem>
                        )
                    );
                }}
            </FormItem>

            <FormItem
                label={
                    <span>
                        参与时效
                        <Tooltip title="绑定关系建立后至活动助力任务失效的有效时长">
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    </span>
                }
                {...formItemFixedWidthLayout}
                required
            >
                <Space>
                    <FormItem
                        name="inviteTerm"
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={1}
                            precision={0}
                            disabled={disabled}
                            style={{ width: '100%' }}
                        />
                    </FormItem>
                    <span>天</span>
                </Space>
            </FormItem>

            <QuillRichInput
                label="活动规则:"
                name="actMarks"
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请填写活动规则' },
                    () => ({
                        validator(rule, value) {
                            if (value == '<p><br></p>') {
                                return Promise.reject('请填写活动规则');
                            }
                            if (value && value.length > 3000) {
                                return Promise.reject('限2000个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
                wrapperCol={{
                    span: 20,
                }}
                placeholder="请输入活动的详细说明，其中包括面额说明，规则说明等"
                required
                initialValue={''}
            />

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(city) => {
                    form.setFieldsValue({ actCity: city?.map((ele) => ele.areaCode) });
                }}
            />
        </Fragment>
    );
};

// 被邀请奖励，参与者展示
const InvitedUserLayout = (props) => {
    const {
        dispatch,
        editActInfo,
        refresh,
        form,
        superFileKey,
        superFileName,
        fileKey,
        fileName,
        couponModel,
        global,
        actId,
        disabled,
        originInvitedBos,
        linkTypes,
    } = props;

    console.log(111222, superFileKey, fileKey);

    const taskType = linkTypes[superFileKey].key;
    const appendRef = useRef();

    const lookTurnEvent = (item) => {
        let lookPath = `${basePath}marketing/platformactive/turn-over/list/look/${item.actId}?type=${item.actType}`;
        window.open(lookPath, '_blank');
    };

    const dateTime = Form.useWatch('dateTime', form);
    return (
        <Fragment>
            <FormItem name={[fileKey, 'inviteTaskId']} noStyle />
            <FormItem name={[fileKey, 'actId']} noStyle />
            <FormItem name={[fileKey, 'inviteTargetType']} noStyle initialValue={'01'} />

            {(taskType == TASK_TYPES.TASK_REGIST && (
                <Fragment>
                    <FormItem
                        name={[fileKey, 'activeCrowd']}
                        initialValue={['28']}
                        label="用户类型"
                        valuePropName="checked"
                        {...formItemFixedWidthLayout}
                    >
                        <Checkbox disabled style={{ marginTop: '4px' }} key={'28'} value="28">
                            新注册用户
                        </Checkbox>
                    </FormItem>
                </Fragment>

                // <FormItem
                //     name={[fileKey, 'activeCrowdConfig']}
                //     label="用户类型"
                //     required
                //     wrapperCol={{ span: 24 }}
                //     initialValue={{
                //         labelList: [],
                //         cdpList: [],
                //         custLabelList: ['28'],
                //     }}
                // >
                //     <PriceCrowdItem
                //         disabled
                //         applyType={'04'}
                //         onlyIds={['28']}
                //         singleMode
                //     ></PriceCrowdItem>
                // </FormItem>
            )) || (
                <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                    {({ getFieldValue }) => {
                        const invitedBos = getFieldValue([superFileName, superFileKey, fileName]);
                        let disableIds = [];
                        if (
                            editActInfo &&
                            editActInfo?.actState != '0' &&
                            originInvitedBos &&
                            originInvitedBos[fileKey]?.activeCrowdConfig
                        ) {
                            if (
                                originInvitedBos[fileKey]?.activeCrowdConfig
                                    ?.custLabelList instanceof Array
                            ) {
                                disableIds = [
                                    ...disableIds,
                                    ...originInvitedBos[
                                        fileKey
                                    ].activeCrowdConfig.custLabelList.map((ele) => ele.custLabel),
                                ];
                            }
                            if (
                                originInvitedBos[fileKey]?.activeCrowdConfig?.labelList instanceof
                                Array
                            ) {
                                disableIds = [
                                    ...disableIds,
                                    ...originInvitedBos[fileKey].activeCrowdConfig.labelList.map(
                                        (ele) => ele.uniqueId,
                                    ),
                                ];
                            }
                            if (
                                originInvitedBos[fileKey]?.activeCrowdConfig?.cdpList instanceof
                                Array
                            ) {
                                disableIds = [
                                    ...disableIds,
                                    ...originInvitedBos[fileKey].activeCrowdConfig.cdpList.map(
                                        (ele) => ele.cdpCrowdId,
                                    ),
                                ];
                            }
                        }
                        invitedBos?.forEach((ele, index) => {
                            if (index != fileKey) {
                                disableIds = [
                                    ...((ele &&
                                        ele.activeCrowdConfig?.custLabelList?.map(
                                            (ele) => ele.custLabel,
                                        )) ||
                                        []),
                                    ...((ele &&
                                        ele.activeCrowdConfig?.labelList?.map(
                                            (ele) => ele.uniqueId,
                                        )) ||
                                        []),
                                    ...((ele &&
                                        ele.activeCrowdConfig?.cdpList?.map(
                                            (ele) => ele.cdpCrowdId,
                                        )) ||
                                        []),
                                    ...disableIds,
                                ];
                            }
                        });
                        console.log('人群过滤项+++++++', fileKey, invitedBos, disableIds);
                        return (
                            <FormItem
                                name={[fileKey, 'activeCrowdConfig']}
                                label="用户类型"
                                required
                                wrapperCol={{ span: 24 }}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            const { custLabelList, labelList, cdpList } =
                                                value || {};
                                            const res = validatorCrowdItem({
                                                custLabelList,
                                                labelList,
                                                cdpList,
                                            });
                                            if (res?.length) {
                                                return Promise.reject(res);
                                            }

                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <PriceCrowdItem
                                    applyType={'08'}
                                    disabledIds={disableIds}
                                    onlyIds={
                                        (taskType == TASK_TYPES.TASK_REGIST && ['06']) || undefined
                                    }
                                    excludeIds={
                                        (taskType !== TASK_TYPES.TASK_CHARGE && [
                                            '20',
                                            '21',
                                            '22',
                                            '27',
                                        ]) || ['20', '21', '22']
                                    }
                                    allXdtLabel
                                ></PriceCrowdItem>
                            </FormItem>
                            // <ActiveCrowdCheckTree
                            //     label="用户类型"
                            //     form={form}
                            //     parentName={[superFileName, superFileKey, fileName, fileKey]}
                            //     required
                            //     rules={[
                            //         ({ getFieldValue }) => ({
                            //             validator(rule, value) {
                            //                 // console.log(value);
                            //                 if (value instanceof Array && value?.length > 0) {
                            //                     return Promise.resolve();
                            //                 }
                            //                 return Promise.reject(`请选择用户类型`);
                            //             },
                            //         }),
                            //     ]}
                            //     initialValue={[]}
                            //     disableIds={disableIds}
                            //     onlyIds={
                            //         (taskType == TASK_TYPES.TASK_REGIST && ['06']) || undefined
                            //     }
                            //     excludeIds={
                            //         (taskType !== TASK_TYPES.TASK_CHARGE && [
                            //             '20',
                            //             '21',
                            //             '22',
                            //             '27',
                            //         ]) || ['20', '21', '22']
                            //     }
                            //     hideAll
                            //     showTableSearch
                            //     allowClear={false}
                            //     {...formItemFixedWidthLayout}
                            // />
                        );
                    }}
                </FormItem>
            )}

            {(taskType == TASK_TYPES.TASK_CHARGE && (
                <FormItem label={<span>完成充电量</span>} {...formItemFixedWidthLayout} required>
                    <Space>
                        <FormItem
                            name={[fileKey, 'completePq']}
                            noStyle
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <InputNumber
                                min={0}
                                precision={2}
                                disabled={disabled}
                                style={{ width: '100%' }}
                            />
                        </FormItem>
                        <span>度</span>
                    </Space>
                </FormItem>
            )) ||
                null}

            <FormItem
                label="成功助力次数限制"
                required
                {...formItemLayout}
                labelCol={{ flex: undefined }}
            >
                <Space>
                    <FormItem
                        name={[fileKey, 'limitType']}
                        noStyle
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'1'}
                    >
                        <Select
                            placeholder="无限制"
                            disabled={disabled || taskType == TASK_TYPES.TASK_REGIST}
                            style={{ width: '100px' }}
                        >
                            <Option key="1">每日</Option>
                            <Option key="2">活动期间</Option>
                        </Select>
                    </FormItem>
                    <FormItem
                        name={[fileKey, 'limitNum']}
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={1}
                            precision={0}
                            disabled={disabled || taskType == TASK_TYPES.TASK_REGIST}
                            style={{ width: '100%' }}
                        />
                    </FormItem>
                    <span>次</span>
                    <Tooltip title="被邀请者在限制时间范围内可以多少人成功助力">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </Space>
            </FormItem>

            <FormItem
                label="奖品设置"
                name={[fileKey, 'prizeType']}
                initialValue="01"
                // rules={
                //     taskType !== TASK_TYPES.TASK_REGIST
                //         ? [{ required: true, message: '请选择' }]
                //         : false
                // }
            >
                <Radio.Group disabled={disabled}>
                    <Radio value="01">优惠券</Radio>
                    <Radio value="02">转盘</Radio>
                </Radio.Group>
            </FormItem>

            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const prizeInfo = getFieldValue([
                        superFileName,
                        superFileKey,
                        fileName,
                        fileKey,
                    ]);
                    const prizeType = prizeInfo?.prizeType;
                    const cpnList = prizeInfo?.invitedCpnPrize;
                    const turnList = prizeInfo?.invitedTurnPrize;
                    const remainStockNum = prizeInfo?.remainStockNum;

                    const countCpnNumber = cpnList?.reduce((a, b) => {
                        return Math.round((a + Number(b?.singlePutNum)) * 100) / 100;
                    }, 0);

                    //被邀请人不同类型的活动只能配置相同的转盘
                    const superFileInfo = getFieldValue(superFileName);

                    let allowTurnIdList = [];

                    let allSelectTurnList = [];

                    superFileInfo?.forEach((ele, index) => {
                        if (index == superFileKey) {
                            //不同类型类型只要选了1个转盘了 其他类型任务的转盘都要跟他选一样的
                            ele[fileName].forEach((child) => {
                                if (
                                    child?.prizeType?.includes('02') &&
                                    !isEmpty(child?.invitedTurnPrize)
                                ) {
                                    allSelectTurnList = [
                                        ...allSelectTurnList,
                                        ...child.invitedTurnPrize?.map((ele) => ele.actId),
                                    ];
                                }
                            });
                        }
                    });

                    const hasTurnItemInfo = superFileInfo?.find((ele, index) => {
                        //不同类型任务只要选了1个转盘了 其他类型的任务的转盘都要跟他选一样的
                        if (index != superFileKey) {
                            //不同类型类型只要选了1个转盘了 其他类型任务的转盘都要跟他选一样的
                            return ele[fileName].find(
                                (child) =>
                                    child?.prizeType?.includes('02') &&
                                    !isEmpty(child?.invitedTurnPrize),
                            );
                        }
                        return null;
                    });
                    if (hasTurnItemInfo && !isEmpty(hasTurnItemInfo[fileName])) {
                        let otherTurnIdList = hasTurnItemInfo[fileName].reduce((a, b) => {
                            if (!isEmpty(b?.invitedTurnPrize)) {
                                return [...a, ...b.invitedTurnPrize?.map((ele) => ele.actId)];
                            }
                            return a;
                        }, []);
                        if (otherTurnIdList.length < allSelectTurnList.length) {
                            allowTurnIdList = allSelectTurnList;
                        } else {
                            allowTurnIdList = otherTurnIdList;
                        }
                    }

                    return (
                        <Fragment>
                            {(prizeType?.includes('01') && (
                                <Fragment>
                                    <Form.Item
                                        {...{
                                            ...formItemLayout,
                                            wrapperCol: {
                                                offset: '120px',
                                            },
                                        }}
                                    >
                                        <div className={baseStyles['formItemDetail']}>
                                            <Form.Item
                                                name={[fileKey, 'invitedCpnPrize']}
                                                rules={[
                                                    // { required: true, message: '请选择优惠券' },
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            if (isEmpty(value)) {
                                                                return Promise.reject(
                                                                    `请选择优惠券`,
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                noStyle
                                            >
                                                <SelectCouponItem
                                                    form={form}
                                                    actId={actId}
                                                    actSubId={editActInfo?.actSubId}
                                                    global={global}
                                                    dispatch={dispatch}
                                                    disabled={editActInfo?.actState >= 2}
                                                    extInParams={{
                                                        dctType: '01',
                                                    }}
                                                    // disabledIds={
                                                    //     disabledIds
                                                    // }
                                                    onRefresh={() => {
                                                        refresh && refresh(actId);
                                                    }}
                                                    prizeClass={null}
                                                    pageType={
                                                        COUPON_CUSTOM_PAGE_TYPE.TURN_OVER_FLIP
                                                    }
                                                    columns={[
                                                        {
                                                            title: '序号 ',
                                                            width: 60,
                                                            render(text, record, index) {
                                                                return (
                                                                    <span title={index}>
                                                                        {index + 1}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券名称 ',
                                                            width: 180,
                                                            dataIndex: 'prizeName',
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券组编号 ',
                                                            width: 140,
                                                            dataIndex: 'prizeNo',
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>{text}</span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '优惠额度',
                                                            dataIndex: 'dctValueName',
                                                            width: 160,
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券类型',
                                                            dataIndex: 'prizeTypeName',
                                                            width: 120,
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },

                                                        {
                                                            title: '单人发券数',
                                                            width: 140,
                                                            dataIndex: 'singlePutNum',
                                                            render(text, record, index) {
                                                                const minNum = 1;
                                                                // if (!isCopy && actId) {
                                                                //     // 新增/复制可随意填库存，编辑不可少于上次提交的库存数量
                                                                //     editActInfo?.cellList?.map((ele) => {
                                                                //         const lastObj =
                                                                //             ele?.prizeList?.find(
                                                                //                 (subEle) =>
                                                                //                     subEle.prizeId ==
                                                                //                     record.prizeId,
                                                                //             );
                                                                //         if (lastObj) {
                                                                //             minNum = lastObj.stockNum;
                                                                //         }
                                                                //     });
                                                                // }
                                                                return (
                                                                    <FormItem
                                                                        name={[
                                                                            fileKey,
                                                                            'invitedCpnPrize',
                                                                            index,
                                                                            'singlePutNum',
                                                                        ]}
                                                                        initialValue={1}
                                                                        rules={
                                                                            !record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <
                                                                                    2) && [
                                                                                ({
                                                                                    getFieldValue,
                                                                                }) => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        if (
                                                                                            isEmpty(
                                                                                                value,
                                                                                            )
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `单人发券数不能为空`,
                                                                                            );
                                                                                        }

                                                                                        return Promise.resolve();
                                                                                    },
                                                                                }),
                                                                            ]
                                                                        }
                                                                    >
                                                                        {(!record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <
                                                                                    2) && (
                                                                                <InputNumber
                                                                                    min={minNum}
                                                                                    step={1}
                                                                                    precision={0}
                                                                                    value={
                                                                                        text || ''
                                                                                    }
                                                                                />
                                                                            )) || (
                                                                            <span title={text}>
                                                                                {text}
                                                                            </span>
                                                                        )}
                                                                    </FormItem>
                                                                );
                                                            },
                                                        },

                                                        {
                                                            title: '中奖概率(%)',
                                                            width: 140,
                                                            dataIndex: 'winningRate',
                                                            render(text, record, index) {
                                                                return (
                                                                    <FormItem
                                                                        name={[
                                                                            fileKey,
                                                                            'invitedCpnPrize',
                                                                            index,
                                                                            'winningRate',
                                                                        ]}
                                                                        initialValue={100}
                                                                        rules={
                                                                            !record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <=
                                                                                    2) && [
                                                                                ({
                                                                                    getFieldValue,
                                                                                }) => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        if (
                                                                                            isEmpty(
                                                                                                value,
                                                                                            )
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `中奖概率未填写`,
                                                                                            );
                                                                                        }

                                                                                        return Promise.resolve();
                                                                                    },
                                                                                }),
                                                                            ]
                                                                        }
                                                                    >
                                                                        {(!record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <=
                                                                                    2) && (
                                                                                <InputNumber
                                                                                    min={1}
                                                                                    max={100}
                                                                                    step={1}
                                                                                    precision={2}
                                                                                    value={
                                                                                        text || ''
                                                                                    }
                                                                                />
                                                                            )) || (
                                                                            <span title={text}>
                                                                                {text}
                                                                            </span>
                                                                        )}
                                                                    </FormItem>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '有效期 ',
                                                            width: 220,
                                                            dataIndex: 'expirationDesc',
                                                            render(text, record) {
                                                                if (!text?.length) {
                                                                    text =
                                                                        record.putTime ||
                                                                        record.getEndDate ||
                                                                        record.getTime;
                                                                }
                                                                return (
                                                                    <span title={text}>{text}</span>
                                                                );
                                                            },
                                                        },
                                                    ]}
                                                    // appendBtn={
                                                    //     editActInfo?.actState == 2 &&
                                                    //     ((item, index) => {
                                                    //         return (
                                                    //             <span
                                                    //                 className={
                                                    //                     commonStyles['table-btn']
                                                    //                 }
                                                    //                 onClick={() => {
                                                    //                     // 构造一个请求对象，前提是index不可变动
                                                    //                     const newItem = {
                                                    //                         ...item,
                                                    //                         actId: editActInfo?.actId,
                                                    //                     };
                                                    //                     appendRef.current.show(
                                                    //                         newItem,
                                                    //                     );
                                                    //                 }}
                                                    //             >
                                                    //                 追加
                                                    //             </span>
                                                    //         );
                                                    //     })
                                                    // }
                                                />
                                            </Form.Item>

                                            <AppendItem
                                                initRef={appendRef}
                                                onFinish={() => {
                                                    refresh && refresh(actId);
                                                }}
                                            />
                                        </div>
                                        <Typography.Link>
                                            单人发券总数: {countCpnNumber || 0}
                                        </Typography.Link>
                                    </Form.Item>
                                    <FormItem label="库存数量" required>
                                        <Space>
                                            <FormItem
                                                noStyle
                                                name={[fileKey, 'stockNum']}
                                                rules={[
                                                    { required: true, message: '请配置库存数量' },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={editActInfo?.actState >= 2}
                                                    min={1}
                                                    step={1}
                                                    precision={0}
                                                    addonAfter={'份'}
                                                />
                                            </FormItem>
                                            {editActInfo?.actState == 2 && (
                                                <span
                                                    className={commonStyles['table-btn']}
                                                    onClick={() => {
                                                        // 构造一个请求对象，前提是index不可变动
                                                        const newItem = {
                                                            ...prizeInfo,
                                                            stockNum: 0,
                                                        };
                                                        appendRef.current.show(newItem);
                                                    }}
                                                >
                                                    追加库存
                                                </span>
                                            )}
                                        </Space>
                                    </FormItem>
                                    {!isEmpty(remainStockNum) && (
                                        <FormItem
                                            label="剩余库存"
                                            name={[fileKey, 'remainStockNum']}
                                        >
                                            {`${remainStockNum}份`}
                                        </FormItem>
                                    )}
                                </Fragment>
                            )) ||
                                null}
                            {(prizeType?.includes('02') && (
                                <FormItem
                                    name={[fileKey, 'invitedTurnPrize']}
                                    wrapperCol={{ span: 24 }}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject(`请配置转盘`);
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <SelectActiveItem
                                        allowIdList={allowTurnIdList}
                                        multiple={false}
                                        disabled={disabled}
                                        title="转盘"
                                        addBtnText="+ 选择转盘"
                                        columns={turnColumns()}
                                        onLook={lookTurnEvent}
                                        searchApi={getTurntableApi}
                                        searchParams={{
                                            activeFlag: '1',
                                            actType: ACT_TYPES.TURN_PLATE,
                                            configType: '03', //邀请转盘
                                            effTimeStr:
                                                (dateTime &&
                                                    dateTime[0]?.format('YYYY-MM-DD HH:mm:ss')) ||
                                                '',
                                            expTimeStr:
                                                (dateTime &&
                                                    dateTime[1]?.format('YYYY-MM-DD HH:mm:ss')) ||
                                                '',
                                        }}
                                        searchRender={searchNode}
                                    ></SelectActiveItem>
                                </FormItem>
                            )) ||
                                null}
                        </Fragment>
                    );
                }}
            </FormItem>
        </Fragment>
    );
};
const basePath = `${window.location.origin}${PUBLIC_PATH}`;
const searchNode = (
    <Fragment>
        <Col span={8}>
            <FormItem name="actNo" label="活动编码">
                <Input placeholder="请填写" autoComplete="off"></Input>
            </FormItem>
        </Col>
        <Col span={8}>
            <FormItem name="actName" label="活动名称">
                <Input placeholder="请填写" autoComplete="off"></Input>
            </FormItem>
        </Col>
    </Fragment>
);
// 邀请者奖励，邀请者配置
const InviterLayout = (props) => {
    const {
        dispatch,
        editActInfo,
        refresh,
        global: { codeInfo },
        form,
        listLoading,
        superFileKey,
        superFileName,
        fileKey,
        fileName,
        couponModel,
        turnoverModel,
        global,
        actId,
        disabled,
        originInviterBos,
    } = props;

    const appendRef = useRef();

    const cityModalRef = useRef();

    const lookTurnEvent = (item) => {
        let lookPath = `${basePath}marketing/platformactive/turn-over/list/look/${item.actId}?type=${item.actType}`;
        window.open(lookPath, '_blank');
    };

    const dateTime = Form.useWatch('dateTime', form);

    console.log(321321, superFileKey, superFileName);

    return (
        <Fragment>
            <FormItem name={[fileKey, 'inviteTargetType']} noStyle initialValue={'02'} />
            <FormItem name={[fileKey, 'inviteTaskSn']} noStyle initialValue={fileKey} />
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    let disableIds = [];

                    if (
                        editActInfo &&
                        editActInfo?.actState != '0' &&
                        originInviterBos &&
                        originInviterBos[fileKey]?.activeCrowdConfig
                    ) {
                        if (
                            originInviterBos[fileKey]?.activeCrowdConfig?.custLabelList instanceof
                            Array
                        ) {
                            disableIds = [
                                ...disableIds,
                                ...originInviterBos[fileKey].activeCrowdConfig.custLabelList.map(
                                    (ele) => ele.custLabel,
                                ),
                            ];
                        }
                        if (
                            originInviterBos[fileKey]?.activeCrowdConfig?.labelList instanceof Array
                        ) {
                            disableIds = [
                                ...disableIds,
                                ...originInviterBos[fileKey].activeCrowdConfig.labelList.map(
                                    (ele) => ele.uniqueId,
                                ),
                            ];
                        }
                        if (
                            originInviterBos[fileKey]?.activeCrowdConfig?.cdpList instanceof Array
                        ) {
                            disableIds = [
                                ...disableIds,
                                ...originInviterBos[fileKey].activeCrowdConfig.cdpList.map(
                                    (ele) => ele.cdpCrowdId,
                                ),
                            ];
                        }
                    }

                    return (
                        // <ActiveCrowdCheckTree
                        //     label="用户类型"
                        //     form={form}
                        //     parentName={[superFileName, superFileKey, fileName, fileKey]}
                        //     required
                        //     initialValue={[]}
                        //     excludeIds={['20', '21', '22', '27']}
                        //     hideAll
                        //     showTableSearch
                        //     allowClear={false}
                        //     disableIds={disableIds}
                        //     {...formItemFixedWidthLayout}
                        // />

                        <FormItem
                            name={[fileKey, 'activeCrowdConfig']}
                            label="用户类型"
                            required
                            wrapperCol={{ span: 24 }}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        const { custLabelList, labelList, cdpList } = value || {};
                                        const res = validatorCrowdItem({
                                            custLabelList,
                                            labelList,
                                            cdpList,
                                        });
                                        if (res?.length) {
                                            return Promise.reject(res);
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <PriceCrowdItem
                                applyType={'08'}
                                disabledIds={disableIds}
                                excludeIds={['20', '21', '22', '27']}
                                allXdtLabel
                            ></PriceCrowdItem>
                        </FormItem>
                    );
                }}
            </FormItem>

            <FormItem label={<span>任务达标数</span>} {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name={[fileKey, 'completeNum']}
                        noStyle
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber
                            min={1}
                            precision={0}
                            disabled={disabled}
                            style={{ width: '100%' }}
                        />
                    </FormItem>
                    <Tooltip title="在本邀请任务中想要获得1次奖励需要成功邀请的人数">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </Space>
            </FormItem>

            <FormItem
                label="获得奖励次数限制"
                required
                // {...formItemLayout}
                labelCol={{ flex: undefined }}
            >
                <FormItem>
                    <FormItem
                        name={[fileKey, 'temp_0']}
                        noStyle
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group disabled={disabled}>
                            <Radio value="1">限制</Radio>
                            <Radio value="0">不限制</Radio>
                        </Radio.Group>
                    </FormItem>
                    <Tooltip title="邀请者在限制时间范围内可以获得的奖励次数">
                        <InfoCircleOutlined />
                    </Tooltip>
                </FormItem>

                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues?.[superFileName]?.[superFileKey]?.[fileName]?.[fileKey]
                            ?.temp_0 !==
                        curValues?.[superFileName]?.[superFileKey]?.[fileName]?.[fileKey]?.temp_0
                    }
                >
                    {({ getFieldValue }) => {
                        const temp_0 = getFieldValue([
                            superFileName,
                            superFileKey,
                            fileName,
                            fileKey,
                            'temp_0',
                        ]);
                        if (temp_0 !== '1') {
                            return null;
                        }
                        return (
                            <Space>
                                <FormItem
                                    name={[fileKey, 'limitType']}
                                    noStyle
                                    rules={[{ required: true, message: '请选择' }]}
                                >
                                    <Select disabled={disabled} style={{ minWidth: '100px' }}>
                                        <Option key="1">每日</Option>
                                        <Option key="2">活动期间</Option>
                                    </Select>
                                </FormItem>
                                <FormItem
                                    name={[fileKey, 'limitNum']}
                                    noStyle
                                    rules={[{ required: true, message: '请填写' }]}
                                >
                                    <InputNumber
                                        min={1}
                                        precision={0}
                                        disabled={disabled}
                                        style={{ width: '100%' }}
                                    />
                                </FormItem>
                                <span>次</span>
                            </Space>
                        );
                    }}
                </FormItem>
            </FormItem>

            <FormItem
                label="奖品设置"
                name={[fileKey, 'prizeType']}
                rules={[{ required: true, message: '请选择' }]}
            >
                <Checkbox.Group disabled={disabled}>
                    <Checkbox key="01" value="01">
                        优惠券
                    </Checkbox>
                    <Checkbox key="02" value="02">
                        转盘
                    </Checkbox>
                </Checkbox.Group>
            </FormItem>

            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const prizeInfo = getFieldValue([
                        superFileName,
                        superFileKey,
                        fileName,
                        fileKey,
                    ]);
                    const prizeType = prizeInfo?.prizeType;
                    const cpnList = prizeInfo?.invitedCpnPrize;
                    const turnList = prizeInfo?.invitedTurnPrize;
                    const remainStockNum = prizeInfo?.remainStockNum;

                    const countCpnNumber = cpnList?.reduce((a, b) => {
                        return Math.round((a + Number(b?.singlePutNum)) * 100) / 100;
                    }, 0);

                    //邀请人不同类型的活动只能配置相同的转盘
                    const superFileInfo = getFieldValue(superFileName);

                    let allowTurnIdList = [];
                    const hasTurnItemInfo = superFileInfo?.find((ele, index) => {
                        if (index != superFileKey) {
                            //不同类型类型只要选了1个转盘了 其他类型任务的转盘都要跟他选一样的
                            return ele[fileName].find((child) => child.prizeType?.includes('02'));
                        }
                        return null;
                    });
                    if (hasTurnItemInfo && !isEmpty(hasTurnItemInfo[fileName])) {
                        allowTurnIdList = hasTurnItemInfo[fileName].reduce((a, b) => {
                            if (a) {
                                return a;
                            }
                            if (!isEmpty(b.invitedTurnPrize)) {
                                return b.invitedTurnPrize?.map((ele) => ele.actId);
                            }
                        }, undefined);
                    }

                    console.log(7777777, cpnList, countCpnNumber, allowTurnIdList, hasTurnItemInfo);

                    return (
                        <Fragment>
                            {(prizeType?.includes('01') && (
                                <Fragment>
                                    <Form.Item
                                        {...{
                                            ...formItemLayout,
                                            wrapperCol: {
                                                offset: '120px',
                                            },
                                        }}
                                    >
                                        <div className={baseStyles['formItemDetail']}>
                                            <Form.Item
                                                name={[fileKey, 'invitedCpnPrize']}
                                                rules={[
                                                    // { required: true, message: '请选择优惠券' },
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            if (isEmpty(value)) {
                                                                return Promise.reject(
                                                                    `请选择优惠券`,
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                noStyle
                                            >
                                                <SelectCouponItem
                                                    form={form}
                                                    actId={actId}
                                                    actSubId={editActInfo?.actSubId}
                                                    global={global}
                                                    dispatch={dispatch}
                                                    disabled={editActInfo?.actState >= 2}
                                                    extInParams={{
                                                        dctType: '01',
                                                    }}
                                                    // disabledIds={
                                                    //     disabledIds
                                                    // }
                                                    onRefresh={() => {
                                                        refresh && refresh(actId);
                                                    }}
                                                    prizeClass={null}
                                                    pageType={
                                                        COUPON_CUSTOM_PAGE_TYPE.TURN_OVER_FLIP
                                                    }
                                                    columns={[
                                                        {
                                                            title: '序号 ',
                                                            width: 60,
                                                            render(text, record, index) {
                                                                return (
                                                                    <span title={index}>
                                                                        {index + 1}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券名称 ',
                                                            width: 180,
                                                            dataIndex: 'prizeName',
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券组编号 ',
                                                            width: 140,
                                                            dataIndex: 'prizeNo',
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>{text}</span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '优惠额度',
                                                            dataIndex: 'dctValueName',
                                                            width: 160,
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '券类型',
                                                            dataIndex: 'prizeTypeName',
                                                            width: 120,
                                                            render(text, record) {
                                                                return (
                                                                    <span title={text}>
                                                                        {text || '-'}
                                                                    </span>
                                                                );
                                                            },
                                                        },

                                                        {
                                                            title: '单人发券数',
                                                            width: 140,
                                                            dataIndex: 'singlePutNum',
                                                            render(text, record, index) {
                                                                const minNum = 1;

                                                                return (
                                                                    <FormItem
                                                                        name={[
                                                                            fileKey,
                                                                            'invitedCpnPrize',
                                                                            index,
                                                                            'singlePutNum',
                                                                        ]}
                                                                        initialValue={1}
                                                                        rules={
                                                                            !record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <
                                                                                    2) && [
                                                                                ({
                                                                                    getFieldValue,
                                                                                }) => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        if (
                                                                                            isEmpty(
                                                                                                value,
                                                                                            )
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `单人发券数不能为空`,
                                                                                            );
                                                                                        }

                                                                                        return Promise.resolve();
                                                                                    },
                                                                                }),
                                                                            ]
                                                                        }
                                                                    >
                                                                        {(!record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <
                                                                                    2) && (
                                                                                <InputNumber
                                                                                    min={minNum}
                                                                                    step={1}
                                                                                    precision={0}
                                                                                    value={
                                                                                        text || ''
                                                                                    }
                                                                                />
                                                                            )) || (
                                                                            <span title={text}>
                                                                                {text}
                                                                            </span>
                                                                        )}
                                                                    </FormItem>
                                                                );
                                                            },
                                                        },

                                                        {
                                                            title: '中奖概率(%)',
                                                            width: 140,
                                                            dataIndex: 'winningRate',
                                                            render(text, record, index) {
                                                                return (
                                                                    <FormItem
                                                                        name={[
                                                                            fileKey,
                                                                            'invitedCpnPrize',
                                                                            index,
                                                                            'winningRate',
                                                                        ]}
                                                                        initialValue={100}
                                                                        rules={
                                                                            !record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <=
                                                                                    2) && [
                                                                                ({
                                                                                    getFieldValue,
                                                                                }) => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        if (
                                                                                            isEmpty(
                                                                                                value,
                                                                                            )
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `中奖概率未填写`,
                                                                                            );
                                                                                        }

                                                                                        return Promise.resolve();
                                                                                    },
                                                                                }),
                                                                            ]
                                                                        }
                                                                    >
                                                                        {(!record.pushPrizeId &&
                                                                            (isEmpty(editActInfo) ||
                                                                                editActInfo?.actState <=
                                                                                    2) && (
                                                                                <InputNumber
                                                                                    min={1}
                                                                                    max={100}
                                                                                    step={1}
                                                                                    precision={2}
                                                                                    value={
                                                                                        text || ''
                                                                                    }
                                                                                />
                                                                            )) || (
                                                                            <span title={text}>
                                                                                {text}
                                                                            </span>
                                                                        )}
                                                                    </FormItem>
                                                                );
                                                            },
                                                        },
                                                        {
                                                            title: '有效期 ',
                                                            width: 220,
                                                            dataIndex: 'expirationDesc',
                                                            render(text, record) {
                                                                if (!text?.length) {
                                                                    text =
                                                                        record.putTime ||
                                                                        record.getEndDate ||
                                                                        record.getTime;
                                                                }
                                                                return (
                                                                    <span title={text}>{text}</span>
                                                                );
                                                            },
                                                        },
                                                    ]}
                                                    // appendBtn={
                                                    //     editActInfo?.actState == 2 &&
                                                    //     ((item, index) => {
                                                    //         return (
                                                    //             <span
                                                    //                 className={
                                                    //                     commonStyles['table-btn']
                                                    //                 }
                                                    //                 onClick={() => {
                                                    //                     // 构造一个请求对象，前提是index不可变动
                                                    //                     const newItem = {
                                                    //                         ...item,
                                                    //                         actId: editActInfo?.actId,
                                                    //                     };
                                                    //                     appendRef.current.show(
                                                    //                         newItem,
                                                    //                     );
                                                    //                 }}
                                                    //             >
                                                    //                 追加
                                                    //             </span>
                                                    //         );
                                                    //     })
                                                    // }
                                                ></SelectCouponItem>
                                            </Form.Item>
                                            <AppendItem
                                                initRef={appendRef}
                                                onFinish={() => {
                                                    refresh && refresh(actId);
                                                }}
                                            />

                                            <Typography.Link>
                                                单人发券总数: {countCpnNumber || 0}
                                            </Typography.Link>
                                        </div>
                                    </Form.Item>

                                    <FormItem label="库存数量" required>
                                        <Space>
                                            <FormItem
                                                noStyle
                                                name={[fileKey, 'stockNum']}
                                                rules={[
                                                    { required: true, message: '请配置库存数量' },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={editActInfo?.actState >= 2}
                                                    min={1}
                                                    step={1}
                                                    precision={0}
                                                    addonAfter={'份'}
                                                />
                                            </FormItem>
                                            {editActInfo?.actState == 2 && (
                                                <span
                                                    className={commonStyles['table-btn']}
                                                    onClick={() => {
                                                        // 构造一个请求对象，前提是index不可变动
                                                        const newItem = {
                                                            ...prizeInfo,
                                                            stockNum: 0,
                                                        };
                                                        appendRef.current.show(newItem);
                                                    }}
                                                >
                                                    追加库存
                                                </span>
                                            )}
                                        </Space>
                                    </FormItem>
                                    {!isEmpty(remainStockNum) && (
                                        <FormItem
                                            label="剩余库存"
                                            name={[fileKey, 'remainStockNum']}
                                        >
                                            {`${remainStockNum}份`}
                                        </FormItem>
                                    )}
                                </Fragment>
                            )) ||
                                null}
                            {(prizeType?.includes('02') && (
                                <FormItem
                                    name={[fileKey, 'invitedTurnPrize']}
                                    wrapperCol={{ span: 24 }}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject(`请配置转盘`);
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <SelectActiveItem
                                        allowIdList={allowTurnIdList}
                                        multiple={false}
                                        disabled={disabled}
                                        title="转盘"
                                        addBtnText="+ 选择转盘"
                                        columns={turnColumns()}
                                        onLook={lookTurnEvent}
                                        searchApi={getTurntableApi}
                                        searchParams={{
                                            activeFlag: '1',
                                            actType: ACT_TYPES.TURN_PLATE,
                                            configType: '03', //邀请转盘
                                            effTimeStr:
                                                (dateTime &&
                                                    dateTime[0]?.format('YYYY-MM-DD HH:mm:ss')) ||
                                                '',
                                            expTimeStr:
                                                (dateTime &&
                                                    dateTime[1]?.format('YYYY-MM-DD HH:mm:ss')) ||
                                                '',
                                        }}
                                        searchRender={searchNode}
                                    ></SelectActiveItem>
                                </FormItem>
                            )) ||
                                null}
                        </Fragment>
                    );
                }}
            </FormItem>
            <CityTransferModal ref={cityModalRef} deleteEnabled={false} />
        </Fragment>
    );
};

const PrizeLayout = (props) => {
    const {
        form,
        fileKey,
        fileName,
        editActInfo,
        refresh,
        inviteTaskType,
        disabled,
        originData,
        delOriginInvitedData,
    } = props;

    /**
     * 原始数据被邀请者
     */
    const originInvitedBos = useMemo(() => {
        if (originData && originData[fileKey]) {
            return originData[fileKey]?.invitedBos || [];
        } else {
            return [];
        }
    }, [originData]);

    /**
     * 原始数据被邀请者
     */
    const originInviterBos = useMemo(() => {
        if (originData && originData[fileKey]) {
            return originData[fileKey]?.inviterBos || [];
        } else {
            return [];
        }
    }, [originData]);

    return (
        <Fragment>
            {/* <FormItem
                label="任务名称"
                name={[fileKey, 'inviteTaskName']}
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请输入' },
                    () => ({
                        validator(rule, value) {
                            if (value && value.length > 15) {
                                return Promise.reject('限15个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Input placeholder="不超过15个字" maxLength={16} autoComplete="off" />
            </FormItem> */}
            <FormItem
                label="任务描述"
                name={[fileKey, 'inviteTaskMarks']}
                {...formItemFixedWidthLayout}
                rules={[
                    { required: true, message: '请输入' },
                    () => ({
                        validator(rule, value) {
                            if (value && value.length > 24) {
                                return Promise.reject('限24个字');
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Input placeholder="不超过24个字" maxLength={24} autoComplete="off" showCount />
            </FormItem>
            <Card
                title={<span className={commonStyles['card-title']}>被邀请者配置</span>}
                type="inner"
            >
                <Form.List name={[fileKey, 'invitedBos']} initialValue={[]}>
                    {(fields, { add, remove }) => (
                        <Fragment>
                            {fields.map((field, index) => {
                                return (
                                    <Fragment key={index}>
                                        <br></br>
                                        <FormItem>
                                            <Space>
                                                <div style={{ margin: '0', fontWeight: 'bold' }}>
                                                    <Space>
                                                        参与用户{index + 1}
                                                        {!disabled && index > 0 && (
                                                            <Popconfirm
                                                                title="确认删除？"
                                                                okText="是"
                                                                cancelText="否"
                                                                onConfirm={() => {
                                                                    remove(field.name);
                                                                    delOriginInvitedData(
                                                                        field.name,
                                                                    );
                                                                }}
                                                            >
                                                                <DeleteOutlined
                                                                    style={{ fontSize: '26px' }}
                                                                />
                                                            </Popconfirm>
                                                        )}
                                                    </Space>
                                                </div>
                                                {/* {(index == 0 && (
                                                    <FormItem
                                                        name={[field.name, 'useInvite']}
                                                        noStyle
                                                    ></FormItem>
                                                )) || (
                                                    <FormItem
                                                        name={[field.name, 'useInvite']}
                                                        valuePropName={'checked'}
                                                        noStyle
                                                    >
                                                        <Switch
                                                            onChange={(value) => {
                                                                const taskInfo =
                                                                    form.getFieldValue('taskInfo');
                                                                const list =
                                                                    taskInfo[fileKey].invitedBos;
                                                                if (
                                                                    !list[field.name]?.inviteTaskSn
                                                                ) {
                                                                    list[field.name] = {
                                                                        inviteTaskSn: `${
                                                                            index + 1
                                                                        }`,
                                                                    };
                                                                    form.setFieldsValue({
                                                                        taskInfo,
                                                                    });
                                                                }
                                                            }}
                                                            disabled={editActInfo?.actState >= 2}
                                                        ></Switch>
                                                    </FormItem>
                                                )} */}
                                            </Space>
                                        </FormItem>
                                        <FormItem
                                            name={[field.name, 'inviteTaskSn']}
                                            noStyle
                                            initialValue={index}
                                        />
                                        <InvitedUserLayout
                                            {...props}
                                            key={index}
                                            superFileKey={fileKey}
                                            superFileName={fileName}
                                            fileKey={field.name}
                                            fileName={'invitedBos'}
                                            form={form}
                                            disabled={disabled}
                                            originInvitedBos={originInvitedBos}
                                        />
                                        {!disabled &&
                                        inviteTaskType !== TASK_TYPES.TASK_REGIST &&
                                        index === fields?.length - 1 &&
                                        fields?.length < 10 ? (
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    add();
                                                }}
                                            >
                                                新增参与用户
                                            </Button>
                                        ) : null}

                                        <Divider></Divider>
                                    </Fragment>
                                );
                            })}
                        </Fragment>
                    )}
                </Form.List>
            </Card>
            <br />
            <Card
                title={<span className={commonStyles['card-title']}>邀请者配置</span>}
                type="inner"
            >
                <Form.List name={[fileKey, 'inviterBos']}>
                    {(fields, { add, remove }) => (
                        <Fragment>
                            {fields.map((field, index) => {
                                return (
                                    <InviterLayout
                                        {...props}
                                        key={index}
                                        superFileKey={fileKey}
                                        superFileName={fileName}
                                        fileKey={field.name}
                                        fileName={'inviterBos'}
                                        form={form}
                                        disabled={disabled}
                                        originInviterBos={originInviterBos}
                                    />
                                );
                            })}
                        </Fragment>
                    )}
                </Form.List>
            </Card>
        </Fragment>
    );
};

const InviteUpdateActInfo = (props) => {
    const {
        actId,
        form,
        actSourceForm,
        dispatch,
        initEditActInfo,
        editActInfo,
        refresh,
        goBack,
        saveGiftEvent,
    } = props;

    const [submitLoading, changeSubmitLoading] = useState(false);

    const iconRef = useRef();

    const [originData, updateOriginData] = useState(null);

    const disabledUpdate = useMemo(() => {
        if (editActInfo?.actState >= 2) {
            return true;
        }
        return false;
    }, [editActInfo]);

    useEffect(() => {
        if (initEditActInfo) {
            updateOriginData(initEditActInfo?.taskInfo || null);
        }
    }, [initEditActInfo]);

    // 获取actNo
    // const initAddInfo = async () => {
    //     try {
    //         const {
    //             data: { actNo },
    //         } = await getAddGiftInfoApi();
    //         form.setFieldsValue({ actNo });
    //     } catch (error) {}
    // };

    return (
        <Form
            form={form}
            {...formItemLayout}
            scrollToFirstError
            initialValues={{
                taskInfo: initTaskInfo,
            }}
        >
            <div className={styles.formTitle}>活动信息</div>
            <InfoFormLayout
                disabled={disabledUpdate}
                iconRef={iconRef}
                form={form}
                editActInfo={editActInfo}
            />

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) => prevValues.actLbl != curValues.actLbl}
            >
                {({ getFieldValue }) => {
                    const actLbl = getFieldValue('actLbl');
                    const taskInfo = getFieldValue('taskInfo');
                    const linkTypes = [
                        { title: '任务-邀请领奖', key: TASK_TYPES.TASK_TAKE },
                        { title: '任务-邀请注册', key: TASK_TYPES.TASK_REGIST },
                        { title: '任务-邀请充电', key: TASK_TYPES.TASK_CHARGE },
                    ];
                    if (actLbl == '02') {
                        // 线下推广没有邀请注册
                        linkTypes.splice(1, 1);
                    }

                    if (taskInfo?.length !== linkTypes.length) {
                        if (taskInfo?.length > linkTypes.length) {
                            taskInfo.splice(1, 1);
                        } else {
                            taskInfo.splice(1, 0, { ...initTaskInfo[1], useTask: false });
                        }
                        form.setFieldsValue({ taskInfo });
                    }
                    return (
                        <Form.Item
                            name={'taskInfo'}
                            rules={[
                                () => ({
                                    validator(rule, value) {
                                        if (!isEmpty(value)) {
                                            if (!hasIntersectingActIds(value)) {
                                                return Promise.reject(
                                                    '不同任务类型下的转盘活动要配置一致',
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Form.List name={'taskInfo'}>
                                {(fields, { add, remove }) => (
                                    <Fragment>
                                        {fields.map((field, index) => (
                                            <Fragment key={index}>
                                                <br></br>
                                                <FormItem>
                                                    <Space>
                                                        <div
                                                            className={styles.formTitle}
                                                            style={{ margin: '0' }}
                                                        >
                                                            {linkTypes?.[index]?.title}
                                                        </div>
                                                        <FormItem
                                                            name={[field.name, 'useTask']}
                                                            valuePropName={'checked'}
                                                            noStyle
                                                        >
                                                            <Switch
                                                                // onChange={(value) => {
                                                                //     if (!value) {
                                                                //         const taskInfo =
                                                                //             form.getFieldValue('taskInfo');
                                                                //         const list = [...taskInfo];
                                                                //         list[field.name] = {
                                                                //             inviteTaskType:
                                                                //                 linkTypes[index].key,
                                                                //             invitedBos: [
                                                                //                 { inviteTaskSn: '1' },
                                                                //                 { inviteTaskSn: '2' },
                                                                //                 { inviteTaskSn: '3' },
                                                                //             ],
                                                                //             inviterBos: [
                                                                //                 { inviteTaskSn: '1' },
                                                                //             ],
                                                                //         };

                                                                //         form.setFieldsValue({
                                                                //             taskInfo: list,
                                                                //         });
                                                                //     }
                                                                // }}
                                                                disabled={disabledUpdate}
                                                            ></Switch>
                                                        </FormItem>
                                                    </Space>
                                                </FormItem>
                                                <FormItem
                                                    name={[field.name, 'inviteTaskType']}
                                                    noStyle
                                                />
                                                <FormItem
                                                    noStyle
                                                    shouldUpdate={(prevValues, curValues) =>
                                                        prevValues.taskInfo?.[field?.name]
                                                            ?.useTask !==
                                                        curValues.taskInfo?.[field?.name]?.useTask
                                                    }
                                                >
                                                    {({ getFieldValue }) => {
                                                        const taskInfo = getFieldValue('taskInfo');
                                                        let hasUse =
                                                            taskInfo[index].useTask == true;
                                                        const inviteTaskType =
                                                            taskInfo[index].inviteTaskType;

                                                        return hasUse ? (
                                                            <PrizeLayout
                                                                {...props}
                                                                form={form}
                                                                fileName={'taskInfo'}
                                                                fileKey={field.name}
                                                                actId={actId}
                                                                inviteTaskType={inviteTaskType}
                                                                disabled={disabledUpdate}
                                                                originData={originData}
                                                                delOriginInvitedData={(
                                                                    delIndex,
                                                                ) => {
                                                                    const originList =
                                                                        copyObjectCommon(
                                                                            originData,
                                                                        );
                                                                    if (
                                                                        originList &&
                                                                        originList[field.name]
                                                                    ) {
                                                                        originList[
                                                                            field.name
                                                                        ]?.invitedBos.splice(
                                                                            delIndex,
                                                                            1,
                                                                        );
                                                                        updateOriginData(
                                                                            originList,
                                                                        );
                                                                    }
                                                                }}
                                                                linkTypes={linkTypes}
                                                            />
                                                        ) : null;
                                                    }}
                                                </FormItem>
                                            </Fragment>
                                        ))}
                                    </Fragment>
                                )}
                            </Form.List>
                        </Form.Item>
                    );
                }}
            </FormItem>

            <br />
            <div className={styles.formTitle}>其他限制</div>

            <FormItem
                label="被邀请者成功助力次数限制"
                {...formItemFixedWidthLayout}
                {...{
                    labelCol: {
                        flex: '0 0 180px',
                    },
                }}
            >
                <Space>
                    <span>本场活动</span>
                    <FormItem name="actGetLimitNum" noStyle>
                        <InputNumber min={1} precision={0} disabled={editActInfo?.actState >= 2} />
                    </FormItem>
                    <span>次</span>
                </Space>
            </FormItem>

            <div className={styles['form-submit']}>
                <Fragment>
                    {((isEmpty(editActInfo) ||
                        JSON.stringify(editActInfo) == '{}' ||
                        editActInfo?.actState <= 2) && (
                        <Button
                            className={styles['form-btn-left']}
                            type="primary"
                            loading={submitLoading}
                            onClick={() => {
                                // 和后端约定，下一步全部都传草稿状态
                                // 后端判断逻辑是：如果状态不是0，并且没有素材  那就不保存
                                if (submitLoading) {
                                    return;
                                }
                                saveGiftEvent(form, 'save');
                            }}
                        >
                            下一步
                        </Button>
                    )) ||
                        null}

                    <Button className={styles['form-btn']} onClick={goBack}>
                        返回
                    </Button>
                </Fragment>
            </div>
        </Form>
    );
};

export default InviteUpdateActInfo;
