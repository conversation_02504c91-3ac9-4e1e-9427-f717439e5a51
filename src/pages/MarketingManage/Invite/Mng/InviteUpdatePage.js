import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import { Card, Form, message, Modal } from 'antd';

import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';

import moment from 'moment';
import { copyObjectCommon } from '@/utils/utils';
import { saveInviteApi, getInviteDetailApi } from '@/services/Marketing/MarketingMngInviteApi';
import InviteUpdateActInfo, { TASK_TYPES, initTaskInfo } from './InviteUpdateNewActInfo';
import InviteUpdateSourceInfo, { getInviteImgSize } from '../InviteUpdateSourceInfo';

import { useRequest } from 'ahooks';

const InviteUpdate = (props) => {
    const { dispatch, history, route, match } = props;

    const { run: initData, data: editActInfo } = useRequest(
        async (actId) => {
            const { data } = await getInviteDetailApi(actId);
            return data;
        },
        {
            manual: true,
        },
    );

    console.log(54545, editActInfo);

    // 用于跨页面校验
    const [actInfoForm] = Form.useForm();
    const [actSourceForm] = Form.useForm();
    const [actId, changeActId] = useState(match.params.actId || null);
    const [activeTabKey, setActiveTabKey] = useState('tab1');
    const tabList = useMemo(
        () => [
            {
                tab: '活动配置',
                key: 'tab1',
                disabled: actId && editActInfo?.actState > 2,
            },
            {
                tab: '素材管理',
                key: 'tab2',
                disabled: actId ? false : true,
            },
        ],
        [actId, editActInfo],
    );

    // 初始化
    useEffect(() => {
        if (actId) {
            initData(actId);
        }
    }, []);

    // 初始化详情
    useEffect(() => {
        if (editActInfo?.actState > 2 && activeTabKey == 'tab1') {
            // 初始化的时候活动状态已经是已结束，主动调用切换到素材页
            setActiveTabKey('tab2');
        }
    }, [editActInfo]);

    // 初始化详情
    useEffect(() => {
        if (initEditActInfo && activeTabKey == 'tab1') {
            actInfoForm.setFieldsValue(initEditActInfo);
        }
    }, [initEditActInfo, editActInfo]);

    const initEditActInfo = useMemo(() => {
        if (editActInfo && JSON.stringify(editActInfo) != '{}') {
            const params = {
                inviteTerm: editActInfo.inviteTerm,
                completePq: editActInfo.completePq,
                completeNum: editActInfo.completeNum,
                squeak: editActInfo.squeak,
                actMarks: editActInfo.actMarks,
                actName: editActInfo.actName,
                actGetLimitNum: editActInfo.actGetLimitNum,
                actLbl: editActInfo.actLbl,
                actCity: editActInfo.actCity?.split(',') || editActInfo.actCity,
            };

            if (editActInfo.effTime && editActInfo.expTime) {
                params.dateTime = [moment(editActInfo.effTime), moment(editActInfo.expTime)];
            }
            //初始化默认数据
            const list = copyObjectCommon(initTaskInfo);
            if (editActInfo.actLbl == '02') {
                // 线下推广没有邀请注册任务
                list.splice(1, 1);
            }
            for (const ele of editActInfo?.inviteTaskList || []) {
                // 把同一个任务类型（inviteTaskType）的邀请者和被邀请者归类到一起

                const bos = {
                    inviteTaskSn: ele.inviteTaskSn, // 参与用户x
                    inviteTargetType: ele.inviteTargetType,
                    activeCrowd:
                        (ele.activeCrowd && ele.activeCrowd?.split(',')) || ele.activeCrowd,
                    completePq: ele.completePq,
                    completeNum: ele.completeNum,
                    limitType: ele.limitType,
                    limitNum: ele.limitNum,
                    inviteTaskId: ele.inviteTaskId,
                    actId: ele.actId,
                    actSubId: ele.actSubId,
                    actType: ele.actType,

                    stockNum: ele.stockNum,
                    remainStockNum: ele.remainStockNum,
                };
                bos.activeCrowdConfig = {
                    cdpList: ele.cdpList || [],
                    custLabelList: ele.custLabelList || [],
                    labelList: ele.labelList || [],
                };

                if (
                    ele.inviteTargetType === '01' &&
                    ele.inviteTaskType === TASK_TYPES.TASK_REGIST
                ) {
                    bos.activeCrowd = '28';
                }

                const invitedCpnPrize = [];
                const invitedTurnPrize = [];
                for (const prizeEle of ele?.invitePrizeBos || []) {
                    if (prizeEle.invitePrizeType == '01') {
                        // 优惠券
                        invitedCpnPrize.push({
                            ...prizeEle,
                            // invitePrizeSystemId: prizeEle.invitePrizeSystemId, // 邀请送奖励ID 修改时必传
                            // cpnName: prizeEle.invitePrizeName, // 奖励名称
                            // cpnId: prizeEle.invitePrizeId, // 奖励ID 券id或者活动id
                            // cpnNo: prizeEle.invitePrizeNo, // 奖励编号
                            // putNum: prizeEle.invitePrizeNum, // 奖励数量
                            // invitePrizeRate: prizeEle.invitePrizeRate, // 奖励发放概率
                            // getEndDate: prizeEle.putTime,
                            cpnAddType:
                                prizeEle.cpnOwner === '03' || prizeEle.cpnOwner === '04'
                                    ? '03'
                                    : '',
                        });
                    } else if (prizeEle.invitePrizeType == '02') {
                        // 翻牌活动

                        invitedTurnPrize.push({
                            ...prizeEle,
                            // invitePrizeSystemId: prizeEle.invitePrizeSystemId, // 邀请送奖励ID 修改时必传
                            actName: prizeEle.prizeName, // 奖励名称
                            // actId: prizeEle.prizeId, // 奖励ID 券id或者活动id
                            actNo: prizeEle.prizeNo, // 奖励编号
                            // cpnAddType:
                            //     prizeEle.cpnOwner === '03' || prizeEle.cpnOwner === '04'
                            //         ? '03'
                            //         : '',
                            // effTime: prizeEle.effTime,
                            // expTime: prizeEle.expTime,
                            // activeCrowd: prizeEle.activeCrowd,
                        });
                    }
                }
                if (invitedCpnPrize?.length) {
                    bos.invitedCpnPrize = invitedCpnPrize;
                }
                if (invitedTurnPrize?.length) {
                    bos.invitedTurnPrize = invitedTurnPrize;
                }

                let findInfo = list.filter((info) => info.inviteTaskType == ele.inviteTaskType);
                const taskInfo = {
                    inviteTaskName: ele.inviteTaskName,
                    inviteTaskMarks: ele.inviteTaskMarks,
                    inviteTaskType: ele.inviteTaskType,
                    inviteTaskId: ele.inviteTaskId,
                    inviteTargetType: ele.inviteTargetType,
                    actId: ele.actId,
                };
                const info = findInfo?.[0] || taskInfo;

                info.inviteTaskName = ele.inviteTaskName;
                info.inviteTaskMarks = ele.inviteTaskMarks;

                if (ele.inviteTargetType == '01') {
                    // 被邀请者
                    bos.prizeType = ele.prizeType;
                    if (info.invitedBos) {
                        //匹配对应数据回填
                        const bosIndex = info.invitedBos.findIndex((item) => {
                            return item.inviteTaskSn === bos.inviteTaskSn;
                        });
                        if (bosIndex >= 0) {
                            bos.temp_0 = bos.limitType == '0' ? '0' : '1';
                            bos.limitType = bos.limitType > 0 ? bos.limitType : '';
                            info.invitedBos[bosIndex] = bos;
                        } else {
                            info.invitedBos.push(bos);
                        }
                    } else {
                        info.invitedBos = [bos];
                    }
                } else if (ele.inviteTargetType == '02') {
                    // 邀请者
                    bos.prizeType =
                        (ele.prizeType.split && ele.prizeType.split(',')) ||
                        (ele.prizeType && [ele.prizeType]) ||
                        [];
                    if (info.inviterBos) {
                        //匹配对应数据回填
                        const bosIndex = info.inviterBos.findIndex((item) => {
                            return item.inviteTaskSn === bos.inviteTaskSn;
                        });
                        if (bosIndex >= 0) {
                            bos.temp_0 = bos.limitType == '0' ? '0' : '1';
                            bos.limitType = bos.limitType > 0 ? bos.limitType : '';
                            info.inviterBos[bosIndex] = bos;
                        } else {
                            info.inviterBos.push(bos);
                        }
                    } else {
                        info.inviterBos = [bos];
                    }
                }
                info.useTask = true;

                if (!findInfo?.[0]) {
                    list.push(info);
                }
            }

            params.taskInfo = list;
            return params;
        } else {
            return null;
        }
    }, [editActInfo]);

    const [hasTurnOver, updateHasTurnOver] = useState(false);
    const [cacheInfoParams, updateCacheInfoParams] = useState();
    const nestStepEvent = async () => {
        // 下一步按钮
        // 需要校验活动配置的信息内容，内容有误不允许跳转
        try {
            await actInfoForm.validateFields();
            // 需要把素材管理的邀请头图尺寸传过去，兼容切换tab的事件
            const infoValues = actInfoForm.getFieldsValue();
            let hasTurnOverFlag = false; // 是否包含转盘，结果用于判断素材页的图片尺寸
            if (infoValues.taskInfo) {
                for (const taskItem of infoValues.taskInfo) {
                    if (hasTurnOverFlag) {
                        break;
                    }
                    const roopBlock = (bos = []) => {
                        for (const subItem of bos) {
                            if (hasTurnOverFlag) {
                                break;
                            }
                            const prizeType =
                                (subItem?.prizeType?.join && subItem?.prizeType?.join(',')) ||
                                subItem?.prizeType; // 奖励类型（多选） 01优惠券 02转盘;
                            if (prizeType && prizeType.indexOf('02') != -1) {
                                hasTurnOverFlag = true; // 包含了转盘
                                break;
                            }
                        }
                    };
                    // 只有被邀请人有选择转盘的权限
                    // roopBlock(taskItem?.invitedBos);
                    roopBlock(taskItem?.inviterBos, true);
                }
            }
            updateCacheInfoParams({ ...infoValues });

            updateHasTurnOver(hasTurnOverFlag);
            setActiveTabKey('tab2'); // 只要是活动配置，都要自动进素材管理，以素材管理的提交为准
        } catch (error) {
            message.error('请检查活动配置信息内容');
        }
    };

    /**
     * 保存
     * type  save/send
     */
    const saveGiftEvent = async (form, type) => {
        try {
            await form.validateFields();
            // 构造信息数据
            const infoValues =
                form === actSourceForm ? cacheInfoParams : actInfoForm.getFieldsValue();

            const infoParams = {
                inviteTerm: infoValues.inviteTerm,
                actName: infoValues.actName,
                squeak: infoValues.squeak,
                actMarks: infoValues.actMarks,
                effTime:
                    (infoValues.dateTime && infoValues.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) ||
                    '',
                expTime:
                    (infoValues.dateTime && infoValues.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) ||
                    '',
                actType: 28,
                actGetLimitNum: infoValues.actGetLimitNum,
                actLbl: infoValues.actLbl,
                actCity: infoValues.actCity?.join?.(',') || infoValues.actCity,
            };

            let hasTurnOver = false; // 是否包含转盘，结果用于判断素材页的图片尺寸
            if (infoValues.taskInfo) {
                const list = [];
                for (const taskItem of infoValues.taskInfo) {
                    const roopBlock = (bos = [], insertFlag) => {
                        for (const subItem of bos) {
                            let isEmpty = true;
                            const summaryInfo = {
                                ...taskItem,
                                ...subItem,
                                limitType: subItem.limitType || '0',
                                activeCrowd:
                                    (subItem?.activeCrowd?.join &&
                                        subItem?.activeCrowd?.join(',')) ||
                                    subItem?.activeCrowd, //活动人群
                                prizeType:
                                    (subItem?.prizeType?.join && subItem?.prizeType?.join(',')) ||
                                    subItem?.prizeType, // 奖励类型（多选） 01优惠券 02转盘
                                ...subItem?.activeCrowdConfig,
                            };
                            delete summaryInfo.activeCrowdConfig;
                            if (summaryInfo.temp_0 === '0') {
                                summaryInfo.limitType = '0';
                                summaryInfo.limitNum = '';
                            }
                            if (
                                subItem.inviteTargetType === '01' &&
                                taskItem.inviteTaskType === TASK_TYPES.TASK_REGIST
                            ) {
                                summaryInfo.activeCrowd = '28';
                            }

                            const prizeList = [];
                            if (summaryInfo.prizeType?.indexOf('01') != -1) {
                                for (const cpnItem of subItem?.invitedCpnPrize || []) {
                                    prizeList.push({
                                        // invitePrizeSystemId: cpnItem.invitePrizeSystemId, // 邀请送奖励ID 修改时必传
                                        // invitePrizeName: cpnItem.cpnName, // 奖励名称
                                        // invitePrizeId: cpnItem.cpnId, // 奖励ID 券id或者活动id
                                        // invitePrizeNo: cpnItem.cpnNo, // 奖励编号
                                        // invitePrizeNum: cpnItem.putNum, // 奖励数量
                                        // invitePrizeRate: cpnItem.invitePrizeRate, // 奖励发放概率
                                        ...cpnItem,
                                        invitePrizeType: '01', // 01优惠券 02翻牌活动
                                    });
                                }
                            }
                            if (summaryInfo.prizeType?.indexOf('02') != -1) {
                                for (const actItem of subItem?.invitedTurnPrize || []) {
                                    prizeList.push({
                                        ...actItem,
                                        invitePrizeSystemId: actItem.invitePrizeSystemId, // 邀请送奖励ID 修改时必传
                                        prizeName: actItem.actName, // 奖励名称
                                        prizeId: actItem.actId, // 奖励ID 券id或者活动id
                                        prizeNo: actItem.actNo, // 奖励编号
                                        invitePrizeType: '02', // 01优惠券 02翻牌活动
                                    });
                                }
                                hasTurnOver = true; // 包含了转盘
                            }

                            // 记录在当前任务中，有没有参与用户，必须配置一个

                            if (subItem.inviteTargetType === '01' || prizeList.length !== 0) {
                                isEmpty = false;
                            }

                            if (!isEmpty) {
                                summaryInfo.invitePrizeBos = prizeList; //JSON.stringify(prizeList);

                                if (summaryInfo.useTask || insertFlag) {
                                    list.push(summaryInfo);
                                }
                            }
                        }
                    };
                    roopBlock(taskItem?.invitedBos);
                    roopBlock(taskItem?.inviterBos);

                    // console.log(44444444444, taskItem);

                    // if (taskItem.useTask && isEmpty) {
                    //     // 开启任务后，无参与用户，进入判断
                    //     const taskIndex = linkTypes.findIndex(
                    //         (ele) => ele.key == taskItem.inviteTaskType,
                    //     );
                    //     const taskName = linkTypes[taskIndex].title;
                    //     message.error(`${taskName || ''}请配置参与用户`);
                    //     return;
                    // }
                }

                list.forEach((ele) => {
                    delete ele.invitedBos;
                    delete ele.inviterBos;

                    delete ele.invitedTurnPrize;
                    delete ele.invitedCpnPrize;
                });
                if (!list?.length) {
                    message.error('您暂未配置任务');
                    return;
                }
                // console.log(5555555, list);
                infoParams.taskInfo = list;
            }

            const targetValues = getInviteImgSize(hasTurnOver);
            const sourceParams = (actInfoForm === form ? {} : actSourceForm.getFieldsValue()) || {};
            if (actSourceForm === form) {
                // 素材的提交
                // 再次校验提交时候的尺寸和有无转盘所需的尺寸相一致，用于不重新提交图片的场景
                if (
                    sourceParams?.inviteWidth != targetValues.inviteWidth &&
                    sourceParams?.inviteHeight != targetValues.inviteHeight &&
                    ((!editActInfo?.inviteImg && sourceParams.inviteImg) ||
                        editActInfo?.inviteImg == sourceParams.inviteImg)
                ) {
                    // 只需要判断邀请头图地址有没有变化就行，至于提交的尺寸对不对，交给上传组件判断
                    message.error(
                        `${hasTurnOver ? '有' : '无'}转盘时邀请页头图尺寸限制：${
                            targetValues.inviteWidth
                        }*${targetValues.inviteHeight}`,
                    );
                    return;
                }

                const shareInfo = [];
                const platNames = [
                    { name: 'wx', code: '22' },
                    { name: 'ali', code: '10' },
                ];
                for (let index = 0; index < platNames.length; index++) {
                    const name = platNames[index].name;
                    const platInfo = { shareType: platNames[index].code };

                    for (const item of sourceParams.shareInfo) {
                        platInfo[item.value] = item[name];
                    }
                    shareInfo.push(platInfo);
                }

                sourceParams.shareInfo = shareInfo;
            }

            if (window.location.hostname.indexOf('/add') >= 0) {
                // 如果是首次提交素材，后端做任务的替换处理，要给后端一个标识
                infoParams.firstFlag = true;
            }
            if (type == 'save') {
                infoParams.actState = editActInfo?.actState || '0';
            } else if (type == 'submit') {
                infoParams.actState = '2';
            }
            if (actId) {
                infoParams.actId = actId;
            }

            const params = { ...infoParams, ...sourceParams };

            // if (actSourceForm == form) {
            // console.log(params);
            // return;
            // }
            const { data: id } = await saveInviteApi(params);

            changeActId(id);
            if (type == 'save' && actInfoForm === form) {
                initData(id);
            } else if (type == 'submit') {
                // 素材点提交，提交完成直接返回
                message.success('提交成功');
                goBack();
            }

            if (type == 'save') {
                message.success('保存成功');
            }
            if (actInfoForm === form) {
                nestStepEvent();
            }
        } catch (error) {
            console.log(22222, error);
        }
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card
                tabList={tabList}
                activeTabKey={activeTabKey}
                onTabChange={(key) => {
                    if (key == 'tab2') {
                        nestStepEvent();
                    } else {
                        // Modal.confirm({
                        //     title: '手动切换后，未保存的编辑项将被重置',
                        //     content: '是否继续？',
                        //     onOk: () => {
                        //     },
                        // });
                        setActiveTabKey(key);
                    }
                }}
            >
                {(activeTabKey == 'tab1' && (
                    <InviteUpdateActInfo
                        {...props}
                        editActInfo={editActInfo}
                        refresh={initData}
                        form={actInfoForm}
                        actSourceForm={actSourceForm}
                        actId={actId}
                        goBack={goBack}
                        saveGiftEvent={saveGiftEvent}
                        initEditActInfo={initEditActInfo}
                    />
                )) ||
                    null}
                {(activeTabKey == 'tab2' && (
                    <InviteUpdateSourceInfo
                        {...props}
                        inviteModel={{ editActInfo }}
                        actInfoForm={actInfoForm}
                        form={actSourceForm}
                        actId={actId}
                        goBack={goBack}
                        saveGiftEvent={saveGiftEvent}
                        hasTurnOver={hasTurnOver}
                    />
                )) ||
                    null}
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, couponModel, turnoverModel }) => ({
    global,
    couponModel,
    turnoverModel,
}))(InviteUpdate);
