import {
    But<PERSON>,
    Row,
    Col,
    Card,
    Form,
    message,
    InputNumber,
    Select,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    Modal,
    Table,
    Popconfirm,
    Alert,
} from 'antd';
import React, {
    Fragment,
    useState,
    useRef,
    useContext,
    useEffect,
    useMemo,
    useImperativeHandle,
} from 'react';
import { Link } from 'umi';
import commonStyle from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { copyObjectCommon, isEmpty, isObjectEqual } from '@/utils/utils';
import { getGiftBagDisplayListApi } from '@/services/Marketing/MarketingGiftBagApi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { GIFT_TYPES } from '@/config/declare';

import { NEW_COUPON_TYPES } from '@/config/declare';

import useWindowState from '@/hooks/useWindowState';

const { confirm } = Modal;

const FormItem = Form.Item;

const Option = Select.Option;

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const inputRef = useRef(null);
    const form = useContext(EditableContext);

    useEffect(() => {
        if (record) {
            if (!isEmpty(record[dataIndex])) {
                form.setFieldsValue({
                    [dataIndex]: record[dataIndex],
                });
            }
        }
    }, []);

    const save = () => {
        const values = form.getFieldsValue();

        handleSave &&
            handleSave({
                ...record,
                ...values,
            });
    };
    let childNode = children;
    const disabledTry = useMemo(() => {
        let isTry = false;

        if (record && record.giftType === '03') {
            isTry = true;
        }

        return isTry;
    }, [record]);
    if (editable) {
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                required
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请填写${title}`);
                            }
                            if (!(Number(value) > 0)) {
                                return Promise.reject(`${title}值必须为大于0正整数`);
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <InputNumber
                    ref={inputRef}
                    onPressEnter={save}
                    onBlur={save}
                    precision={0}
                    step={1}
                    min={0}
                    disabled={disabledTry}
                />
            </Form.Item>
        );
    }
    return <td {...restProps}>{childNode}</td>;
};

const CouponSelectView = (props) => {
    const {
        value,
        onChange,
        dispatch,
        global,
        disabledIds, // 限制不允许勾选的id列表
    } = props;
    const { codeInfo } = global;
    const { prizeType: prizeTypeList } = codeInfo;

    const { modalHeight } = useWindowState();

    const [cpnList, updateCpnList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [listLoading, updateListLoading] = useState(false);

    const [couponMangeList, updateCouponMangeList] = useState([]);
    const [couponMangeListTotal, updateCouponMangeListTotal] = useState(0);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        () => ({
            tabType: '03',
        }),
        props,
    );

    useEffect(() => {
        if (isEmpty(prizeTypeList)) {
            dispatch({
                type: 'global/initCode',
                code: 'prizeType',
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [form] = Form.useForm();

    const searchData = async () => {
        try {
            const data = form.getFieldsValue(true);
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,

                prizeName: data.prizeName || '',
                prizeType: data.prizeType || '',
                prizeNo: data.prizeNo || '',
                giftDisplayType: '01',
                actState: '2',
            };

            updateListLoading(true);
            const {
                data: {
                    giftCouponList: { records, total },
                },
            } = await getGiftBagDisplayListApi(params);
            updateCouponMangeList(records);
            updateCouponMangeListTotal(total);
            updateListLoading(false);
            return records;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const resetData = () => {
        form.resetFields();
        updateCpnList([]);
        updateCouponMangeList([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const prizeTypeOptions = useMemo(() => {
        if (prizeTypeList instanceof Array) {
            return prizeTypeList.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [prizeTypeList]);

    const columns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                const resultIndex = (pageInfo.pageIndex - 1) * pageInfo.pageSize + (index + 1);
                return <span title={resultIndex}>{resultIndex}</span>;
            },
        },
        {
            title: '券名称',
            width: 200,
            dataIndex: 'prizeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '券备注',
            width: 100,
            dataIndex: 'remark',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券类型',
            width: 100,
            dataIndex: 'prizeTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠类型',
            dataIndex: 'dctTypeName',
            width: 120,
            render(text) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠额度',
            width: 140,
            dataIndex: 'dctValueName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用券',
            width: 200,
            dataIndex: 'stockLimitNum',
            render(text, record) {
                let count = 0;
                const { stockUpLimit, stockLimitNum, totalUseStockNum = 0 } = record;
                if (stockUpLimit) {
                    const diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    count = diff > 0 ? diff : 0;
                    return <span title={count}>{count}</span>;
                } else {
                    return <span title={text}>{'-'}</span>;
                }
            },
        },

        {
            title: '券组编号',
            width: 140,
            dataIndex: 'prizeNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '创建时间',
            dataIndex: 'createdTime',
            width: 160,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const rowSelection = {
        type: 'checkbox',
        selectedRowKeys: cpnList.map((item) => `${item.prizeId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            // 筛选出非当前页的勾选项，不予处理
            let otherCpns = cpnList.filter(
                (x) => couponMangeList.filter((now) => now.prizeId == x.prizeId).length == 0,
            );
            let nowPageCpns = couponMangeList.filter(
                (x) => selectedRows.filter((now) => now.prizeId == x.prizeId).length > 0,
            );
            const newList = [...otherCpns, ...nowPageCpns];

            updateCpnList(newList);
            onChange && onChange(newList);
        },
        getCheckboxProps: (record) => ({
            disabled: disabledIds?.indexOf(record.prizeId) >= 0,
            name: record.prizeId,
        }),
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    return (
        <Fragment>
            <Form
                form={form}
                name="cpn-search"
                onFinish={searchData}
                labelCol={{ flex: '0 0 80px' }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    form={form}
                    loading={listLoading}
                    onReset={resetData}
                    minSpan={40}
                >
                    <FormItem name="vipCouponFlag" noStyle />
                    <Col span={8}>
                        <FormItem label="券名称" name="prizeName">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="券编号" name="prizeNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="券类型" name="prizeType">
                            <Select placeholder="请选择" allowClear>
                                {prizeTypeOptions}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="优惠类型" name="dctType">
                            <Select placeholder="请选择" allowClear>
                                <Select.Option value={'01'}>满减券</Select.Option>
                                <Select.Option value={'02'}>折扣券</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                components={components}
                loading={listLoading}
                scroll={{ x: 'max-content', y: modalHeight }}
                rowKey={(record) => record.prizeId}
                dataSource={couponMangeList}
                columns={columns}
                onChange={onTableChange}
                rowSelection={
                    couponMangeList?.length > 0
                        ? {
                              type: 'checkbox',
                              ...rowSelection,
                          }
                        : false
                }
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponMangeListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                sticky={{ offsetHeader: 0 }}
            />
        </Fragment>
    );
};

const EditModalView = (props) => {
    const { visible, onClose, onFinish, onChange, disabledIds = [], ...otherProps } = props;
    const [form] = Form.useForm();

    const giftType = Form.useWatch('giftType', form);
    const cpnListValue = Form.useWatch('cpnList', form);

    const [rightList, updateRightList] = useState([]);
    const [memberList, updateMemberList] = useState([]);

    useEffect(() => {
        searchRightEvent();
        searchMemberEvent();
    }, []);

    const searchRightEvent = async (searchText) => {
        try {
            const params = {
                pageIndex: 1,
                pageSize: 99,
                actName: searchText,
                giftDisplayType: '02',
                actState: '2',
            };
            const {
                data: {
                    giftCardList: { records, total },
                },
            } = await getGiftBagDisplayListApi(params);
            updateRightList(records);
            return records;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const searchMemberEvent = async (searchText) => {
        try {
            const params = {
                pageIndex: 1,
                pageSize: 99,
                giftDisplayType: '03',
                actName: searchText,
                actState: '2',
            };
            const {
                data: {
                    giftVipExperienceList: { records, total },
                },
            } = await getGiftBagDisplayListApi(params);
            updateMemberList(records);
            return records;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const formRender = useMemo(() => {
        let formDom = null;
        switch (giftType) {
            case GIFT_TYPES.COUPON:
                formDom = (
                    <Fragment>
                        <Form.Item name="cpnList" required>
                            <CouponSelectView
                                disabledIds={disabledIds}
                                {...otherProps}
                            ></CouponSelectView>
                        </Form.Item>
                    </Fragment>
                );
                break;
            case GIFT_TYPES.RIGHT:
                formDom = (
                    <Fragment>
                        <Form.Item name="prizeName" noStyle></Form.Item>
                        <Form.Item name="giftTypeName" noStyle></Form.Item>

                        <Form.Item
                            name="actId"
                            label="权益方案"
                            rules={[{ required: true, message: '请选择方案' }]}
                        >
                            <Select
                                showSearch
                                filterOption={false}
                                onSearch={searchRightEvent}
                                notFoundContent={null}
                                options={(rightList || []).map((d) => ({
                                    value: d.actId,
                                    label: d.actName,
                                    disabled: disabledIds.includes(String(d.actId)),
                                }))}
                                onChange={(newId) => {
                                    const findItem = rightList?.find((ele) => ele.actId === newId);
                                    if (findItem) {
                                        form.setFieldsValue({
                                            prizeName: findItem.actName,
                                            giftTypeName: findItem.giftTypeName,
                                            actCycle: findItem.actCycle,
                                        });
                                    }
                                }}
                                placeholder="请选择"
                            ></Select>
                        </Form.Item>
                        <Form.Item
                            name="actCycle"
                            label="权益周期"
                            initialValue={1}
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (isEmpty(value)) {
                                            return Promise.reject(`请填写`);
                                        }
                                        if (!(Number(value) > 0)) {
                                            return Promise.reject(`值必须为大于0正整数`);
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                disabled
                                addonAfter={'天'}
                                precision={0}
                                step={1}
                                min={0}
                            ></InputNumber>
                        </Form.Item>
                        <Form.Item
                            name="singlePutNum"
                            label="单人发放量"
                            initialValue={1}
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (isEmpty(value)) {
                                            return Promise.reject(`请填写`);
                                        }
                                        if (!(Number(value) > 0)) {
                                            return Promise.reject(`值必须为大于0正整数`);
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                addonAfter={'份'}
                                precision={0}
                                step={1}
                                min={0}
                            ></InputNumber>
                        </Form.Item>
                    </Fragment>
                );

                break;
            case GIFT_TYPES.EXP_MEMBER:
                formDom = (
                    <Fragment>
                        <Form.Item name="prizeName" noStyle></Form.Item>
                        <Form.Item name="giftTypeName" noStyle></Form.Item>

                        <Form.Item
                            name="actId"
                            label="会员方案"
                            rules={[{ required: true, message: '请选择方案' }]}
                        >
                            <Select
                                showSearch
                                filterOption={false}
                                onSearch={searchMemberEvent}
                                notFoundContent={null}
                                options={(memberList || []).map((d) => ({
                                    value: d.actId,
                                    label: d.actName,
                                    disabled: disabledIds.includes(String(d.actId)),
                                }))}
                                onChange={(newId) => {
                                    const findItem = memberList?.find((ele) => ele.actId == newId);

                                    if (findItem) {
                                        form.setFieldsValue({
                                            prizeName: findItem.actName,
                                            giftTypeName: findItem.giftTypeName,
                                            experienceCycle: findItem.experienceCycle,
                                        });
                                    }
                                }}
                                placeholder="请选择"
                            ></Select>
                        </Form.Item>
                        <Form.Item
                            name="experienceCycle"
                            label="体验周期"
                            initialValue={1}
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (isEmpty(value)) {
                                            return Promise.reject(`请填写`);
                                        }
                                        if (!(Number(value) > 0)) {
                                            return Promise.reject(`值必须为大于0正整数`);
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                disabled
                                addonAfter={'天'}
                                precision={0}
                                step={1}
                                min={0}
                            ></InputNumber>
                        </Form.Item>

                        <Form.Item
                            name="singlePutNum"
                            label="单人发放量"
                            initialValue={1}
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (isEmpty(value)) {
                                            return Promise.reject(`请填写`);
                                        }
                                        if (!(Number(value) > 0)) {
                                            return Promise.reject(`值必须为大于0正整数`);
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <InputNumber
                                addonAfter={'份'}
                                precision={0}
                                disabled
                                step={1}
                                min={0}
                            ></InputNumber>
                        </Form.Item>
                    </Fragment>
                );

                break;

            default:
                break;
        }
        return formDom;
    });

    const finishEvent = (values) => {
        onFinish && onFinish(values);
    };
    const modalFormLayout = useMemo(() => {
        if (giftType !== GIFT_TYPES.COUPON) {
            return {
                wrapperCol: {
                    span: 8,
                },
            };
        }
        return {};
    }, [giftType]);
    return (
        <Form
            form={form}
            name="gift-edit"
            scrollToFirstError
            onFinish={finishEvent}
            {...modalFormLayout}
        >
            <Form.Item
                label="礼包类型"
                name="giftType"
                rules={[{ required: true, message: '请选择礼品类型' }]}
                initialValue={GIFT_TYPES.COUPON}
                wrapperCol={{ span: 24 }}
            >
                <Radio.Group
                    onChange={(event) => {
                        const {
                            target: { value: selectValue },
                        } = event;
                        form.setFieldsValue({
                            experienceCycle: '',
                            singlePutNum: 1,
                            actId: '',
                            actCycle: '',
                        });
                    }}
                >
                    <Radio value={GIFT_TYPES.COUPON}>优惠券</Radio>
                    <Radio value={GIFT_TYPES.RIGHT}>权益卡</Radio>

                    <Radio value={GIFT_TYPES.EXP_MEMBER}>体验会员</Radio>
                    <Radio value={GIFT_TYPES.DRAW} disabled>
                        抽奖次数
                    </Radio>

                    <Radio value={GIFT_TYPES.ENERGY} disabled>
                        能量
                    </Radio>

                    <Radio value={GIFT_TYPES.INTEGRAL} disabled>
                        积分
                    </Radio>
                </Radio.Group>
            </Form.Item>
            {formRender}

            <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                <Button type="primary" htmlType="submit">
                    提交
                </Button>
                <Button onClick={onClose}>取消</Button>
            </Space>
        </Form>
    );
};

const GiftBagItem = (props) => {
    const { value, onChange, disabled, ...otherProps } = props;

    useEffect(() => {
        if (value instanceof Array && JSON.stringify(value) !== JSON.stringify(resultList)) {
            updateResultList(value);
        }
    }, [value]);

    const [resultList, setResultList] = useState([]);
    const updateResultList = (list) => {
        list.forEach((ele, index) => (ele.index = index));

        setResultList(list);
    };

    const [showEditView, toggleEditView] = useState(false);

    const updateFormItemValue = (newList) => {
        onChange && onChange(newList);
    };

    const giftColumns = [
        {
            title: '序号',
            width: 80,
            render(text, record, index) {
                return <span title={record.index}>{record.index + 1}</span>;
            },
        },
        {
            title: '奖品名称',
            width: 180,
            dataIndex: 'prizeName',
            render(text, record) {
                const resultText = record.prizeName || record.actName;
                return <span title={resultText}>{resultText}</span>;
            },
        },
        {
            title: '奖品类型 ',
            width: 140,
            dataIndex: 'giftTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '单人发放量',
            width: 120,
            dataIndex: 'singlePutNum',
            onCell: (record, index) => ({
                record,
                index: index,
                editable: !disabled,
                dataIndex: 'singlePutNum',
                title: '单人发放量',
                handleSave,
            }),
        },
        {
            title: '操作',
            width: 120,
            render(text, record) {
                const delBtn = (
                    <Popconfirm
                        title={`确定要删除${record.prizeName}？`}
                        onConfirm={() => {
                            delRowEvent(record);
                        }}
                    >
                        <span className={commonStyle['table-btn']}>删除</span>
                    </Popconfirm>
                );

                // switch
                // if(record.giftType === ){

                // }
                let lookPath = '';
                switch (record.giftType) {
                    case GIFT_TYPES.COUPON:
                        lookPath = `/marketing/couponCenter/cpnManage/list/look/${record.prizeId}`;
                        break;
                    case GIFT_TYPES.RIGHT:
                        lookPath = `/marketing/weekcard/manage/list/plan/look/${record.prizeId}`;
                        break;
                    case GIFT_TYPES.EXP_MEMBER:
                        lookPath = `/userCenter/membership/plan/list/detail-vip/${record.prizeId}`;
                        break;

                    default:
                        break;
                }
                const lookBtn = (
                    <Link to={lookPath} target="_blank">
                        查看
                    </Link>
                );

                const btnList = [];
                btnList.push(lookBtn);
                if (!disabled) {
                    btnList.push(delBtn);
                }

                return <Space>{btnList}</Space>;
            },
        },
    ];

    const handleSave = (row) => {
        const newData = [...resultList];
        const index = newData.findIndex((item) => row.prizeId === item.prizeId);
        const item = newData[index];
        newData.splice(index, 1, {
            ...item,
            ...row,
        });
        updateResultList(newData);
        updateFormItemValue(newData);
    };

    const delRowEvent = (row) => {
        const newData = [...resultList];
        const index = newData.findIndex((item) => row.prizeId === item.prizeId);
        newData.splice(index, 1);
        updateResultList(newData);
        updateFormItemValue(newData);
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const closeEditView = () => {
        toggleEditView(false);
    };
    const openAddView = () => {
        toggleEditView(true);
    };

    const saveGiftEvent = (values) => {
        const { giftType } = values;
        const oldList = copyObjectCommon(resultList);
        const newList = [...oldList];

        if (giftType === GIFT_TYPES.COUPON) {
            const { cpnList } = values;
            if (cpnList instanceof Array) {
                for (const item of cpnList) {
                    let findItemIndex = newList.findIndex((ele) => ele.prizeId === item.prizeId);
                    if (findItemIndex < 0) {
                        item.singlePutNum = 1;
                        newList.push(item);
                    } else {
                        newList[findItemIndex] = item;
                    }
                }
            }
        } else if (giftType === GIFT_TYPES.RIGHT) {
            newList.push({ ...values, prizeId: values.actId });
        } else if (giftType === GIFT_TYPES.EXP_MEMBER) {
            newList.push({ ...values, prizeId: values.actId });
        }
        updateResultList(newList);
        updateFormItemValue(newList);
        closeEditView();
    };

    return (
        <Fragment>
            <Button type="primary" disabled={disabled} onClick={openAddView}>
                + 添加礼品
            </Button>

            <p className="mg-tb-20">
                <Alert message="礼包内所有优惠券都库存充足时，礼包才可继续发放" type="info"></Alert>
            </p>
            <Table
                components={components}
                columns={giftColumns}
                dataSource={resultList}
                pagination={false}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.prizeId}
            ></Table>
            <Modal
                visible={showEditView}
                title={'添加礼品'}
                onCancel={closeEditView}
                width={1200}
                footer={false}
                destroyOnClose
            >
                <EditModalView
                    {...props}
                    onClose={closeEditView}
                    onFinish={saveGiftEvent}
                ></EditModalView>
            </Modal>
        </Fragment>
    );
};
export default GiftBagItem;
