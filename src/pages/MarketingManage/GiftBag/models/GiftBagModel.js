import {
    getGiftBagListApi,
    getGiftBagActListApi,
    getGiftBagDetailApi,
} from '@/services/Marketing/MarketingGiftBagApi';
import {
    getMngWorkorderActDetailApi,
    getWorkorderActDetailApi,
} from '@/services/Marketing/MarketingWorkorderApi';

const giftBagModel = {
    namespace: 'giftBagModel',
    state: {
        giftBagList: [], // 礼包列表
        giftBagListTotal: 0,
        editActInfo: {}, // 当前详情信息

        linkActList: [], //关联活动列表
        linkActListTotal: 0, //关联活动列表
    },
    effects: {
        /**
         * 礼包列表
         */
        *getGiftBagList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getGiftBagListApi, options);

                yield put({
                    type: 'updateGiftBagList',
                    giftBagList: records,
                    total,
                });
            } catch (error) {}
        },

        /**
         * 查询详情
         */
        *initEditActInfo({ actId }, { call, put, select }) {
            try {
                const { data } = yield call(getGiftBagDetailApi, actId);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
        // 工单看到的活动详情
        *initWorkorderEditActInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getMngWorkorderActDetailApi, options);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },

        /**
         * 关联活动列表
         */
        *getLinkActList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getGiftBagActListApi, options);

                yield put({
                    type: 'updateLinkActList',
                    list: records,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateGiftBagList(state, { giftBagList, total }) {
            return {
                ...state,
                giftBagList,
                giftBagListTotal: total,
            };
        },
        updateLinkActList(state, { list, total }) {
            return {
                ...state,
                linkActList: list,
                linkActListTotal: total,
            };
        },
        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default giftBagModel;
