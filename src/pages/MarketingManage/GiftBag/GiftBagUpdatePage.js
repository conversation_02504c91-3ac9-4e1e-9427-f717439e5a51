import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    forwardRef,
    useImperativeHandle,
} from 'react';
import { connect, Link } from 'umi';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import { STATUS_TYPES } from '@/config/declare';
import {
    saveGiftBagApi,
    exportGiftBagActListApi,
    getGiftBagActListApi,
} from '@/services/Marketing/MarketingGiftBagApi';
import {
    Button,
    Card,
    Form,
    message,
    InputNumber,
    Select,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    Tabs,
    Col,
} from 'antd';

import { LeftOutlined, InfoCircleOutlined } from '@ant-design/icons';
import usePageState from '@/hooks/usePageState.js';

import GiftBagItem from './GiftBagItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';

import { isEmpty, exportTableByParams, formatActivePage, getPageQuery } from '@/utils/utils';

const { Option } = Select;

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    OPERATOR: '2', // 运营商
    CITY: '3', // 城市
};

const TAB_TYPES = {
    INFO: 'tab1',
    LINK: 'tab2',
};
const tabList = [
    {
        key: TAB_TYPES.INFO,
        tab: '礼包信息',
    },
    {
        key: TAB_TYPES.LINK,
        tab: '关联活动',
    },
];

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, tableLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={
                {
                    // dates: [moment().subtract(15, 'days'), moment().add(15, 'days')],
                }
            }
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={tableLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
            >
                <Col span={8}>
                    <FormItem label="活动名称" name="actName">
                        <Input autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="活动时间:" name="dates">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const LinkActView = (props) => {
    const {
        dispatch,
        history,
        giftBagModel: { linkActList, linkActListTotal },
        actId,
        listLoading,
    } = props;
    const [searchForm] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        return () => {
            dispatch({
                type: 'giftBagModel/updateLinkActList',
                list: [],
                total: 0,
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = searchForm.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            actName: data.actName,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            giftbagId: actId,
        };

        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }

        dispatch({
            type: 'giftBagModel/getLinkActList',
            options: params,
        });
    };

    //导出
    const exportFormEvent = () => {
        const data = searchForm.getFieldsValue();
        const params = {
            actName: data.actName,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            giftbagId: actId,
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }
        // let columnsStrs = [];
        // for (const item of columns) {
        //     if (item.dataIndex) {
        //         columnsStrs.push({
        //             key: item.dataIndex,
        //             value: item.title,
        //         });
        //     }
        // }
        // exportTableByParams({
        //     methodUrl: '/bil/coupon/couponPutList',
        //     options: params,
        //     columnsStr: columnsStrs,
        // });
        exportGiftBagActListApi(params);
    };

    const resetData = () => {
        searchForm.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = [
        {
            title: '活动名称',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                let path = formatActivePage({ type: String(record.actType), actId: record.actId });
                if (path) {
                    return (
                        <Link to={path} target="_blank">
                            {text}
                        </Link>
                    );
                }
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '活动库存',
            width: 200,
            dataIndex: 'giftbagStockNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发放数量',
            width: 200,
            dataIndex: 'giftbagPutNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动状态',
            width: 200,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                const timeResult = `${record.effTime}-${record.expTime}`;
                return <span title={timeResult}>{timeResult}</span>;
            },
        },
    ];
    return (
        <Fragment>
            <SearchLayout
                form={searchForm}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                tableLoading={listLoading}
                onExportForm={exportFormEvent}
            />
            <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
                <TabPane tab="未开始" key={STATUS_TYPES.NOSTART} />
                <TabPane tab="进行中" key={STATUS_TYPES.DOING} />
                <TabPane tab="已结束" key={STATUS_TYPES.END} />
                <TabPane tab="已取消" key={STATUS_TYPES.STOP} />
            </Tabs>

            <TablePro
                name="linklist"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.actId}
                dataSource={linkActList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: linkActListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};

// 礼包子编辑页面
export const LotteryConfigureView = forwardRef((props, ref) => {
    const {
        dispatch,
        history,
        currentUser,
        giftBagModel: { editActInfo },
        detalisLoading,
        global,
        couponModel,
        isLock: lockSign,
        isCopy: copySign,
        isWorkorder,
        workorderOrigin,
        limitOperIds,
        actId: actIdSign, //是否是工单临时表数据
        closeEvent,
        onFinish, //工单提交事件
    } = props;
    const [isLock, changeLock] = useState(lockSign); // 是否可编辑
    const [actId, changeActId] = useState(actIdSign);
    const [isCopy, changeCopy] = useState(copySign);

    const [submitLoading, changeSubmitLoading] = useState(false);

    const [linkActNum, changeLinkActNum] = useState(0);

    const [activeTab, changeActiveTab] = useState(TAB_TYPES.INFO);

    const [form] = Form.useForm();

    const prizeGiftQueryVoList = Form.useWatch('prizeGiftJson', form);

    useImperativeHandle(ref, () => ({
        initData: initData,
    }));

    useEffect(() => {
        const { tab } = getPageQuery();
        if (tab) {
            changeActiveTab(tab);
        }
        return () => {
            dispatch({
                type: 'giftBagModel/updateEditActInfo',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        changeActId(actIdSign);
    }, [actIdSign]);

    useEffect(() => {
        initData();
    }, [actId]);

    const initData = () => {
        if (actId) {
            if (isWorkorder && !workorderOrigin) {
                dispatch({
                    type: 'giftBagModel/initWorkorderEditActInfo',
                    options: { orderActId: actId },
                });
            } else {
                dispatch({
                    type: 'giftBagModel/initEditActInfo',
                    actId: actId,
                });
            }
        }
    };

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
            if (actId) {
                checkActNum();
            }
        }
    }, [editActInfo, currentUser]);

    const checkActNum = async () => {
        try {
            const {
                data: { total },
            } = await getGiftBagActListApi({ giftbagId: actId, pageIndex: 1, pageSize: 10 });
            changeLinkActNum(total);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const params = {
                giftbagName: info.giftbagName,
                remark: info.remark,
            };
            if (info.prizeGiftQueryVoList instanceof Array) {
                params.prizeGiftJson = info.prizeGiftQueryVoList;
            }

            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    /**
     * 保存礼包
     * type  save/send
     */
    const saveGiftEvent = async (values) => {
        if (submitLoading) {
            return;
        }
        try {
            const type = values.saveType;

            const params = {
                saveType: values.saveType,
                remark: values.remark,
                giftbagName: values.giftbagName,
                prizeGiftJson: values.prizeGiftJson,
            };

            if (!isCopy && actId) {
                params.giftbagId = actId;
            }

            if (!isWorkorder || !actId) {
                if (type == 'save') {
                    params.actState = '0';
                } else if (type == 'send') {
                    params.actState = '1';
                }
            } else {
                if (editActInfo?.actState) {
                    params.actState = editActInfo?.actState;
                }
            }

            if (onFinish) {
                onFinish(type, params);
                return;
            }
            changeSubmitLoading(true);

            const {
                data: { prizeId: newActId },
            } = await saveGiftBagApi(params);

            changeCopy(false);

            if (!actId) {
                changeActId(newActId);
            } else {
                initData();
            }

            if (type == 'save') {
                message.success('保存成功');
                history.replace(`/marketing/platformactive/giftbag/list/update/${newActId}`);
            } else if (type == 'send') {
                message.success('提交成功');
                closeEvent && closeEvent();
            }
        } catch (error) {
            console.log(9999, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const infoView = useMemo(() => {
        return (
            <Form
                form={form}
                name="gift-form"
                initialValues={{
                    areaRangeType: CAPITALTYPS.ALL,
                    temp_0: '1',
                }}
                scrollToFirstError
                onFinish={saveGiftEvent}
                {...formItemFixedWidthLayout}
            >
                <div className={commonStyles['form-title']}>基本信息</div>
                <FormItem
                    label={<span>礼包名称</span>}
                    name="giftbagName"
                    rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
                >
                    <Input
                        disabled={isLock || (!isCopy && editActInfo?.actState >= 2)}
                        maxLength={15}
                        placeholder="如：新人礼包，最多支持15个字"
                        autoComplete="off"
                        showCount
                    />
                </FormItem>

                <FormItem label={<span>礼包描述</span>} name="remark">
                    <Input
                        disabled={isLock}
                        maxLength={30}
                        placeholder="仅内部可见区分同类礼包，最多支持30字"
                        autoComplete="off"
                        showCount
                    />
                </FormItem>

                <div className={commonStyles['form-title']}>礼品配置</div>

                <FormItem
                    label="包含礼品"
                    name="prizeGiftJson"
                    wrapperCol={{ span: 24 }}
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请配置礼包`);
                                }
                                let errList = [];
                                for (const item of value) {
                                    if (
                                        isEmpty(item.singlePutNum) ||
                                        !(Number(item.singlePutNum) > 0)
                                    ) {
                                        errList.push(item.prizeName);
                                    }
                                }
                                if (errList.length > 0) {
                                    const errResult = `请配置${errList.join('、')}的单人发放数量`;
                                    return Promise.reject(errResult);
                                }

                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <GiftBagItem
                        {...props}
                        disabled={!isCopy && (isLock || linkActNum > 0)}
                        disabledIds={prizeGiftQueryVoList?.map((ele) => ele.prizeId || ele.actId)}
                    ></GiftBagItem>
                </FormItem>

                <FormItem name="saveType" noStyle></FormItem>

                <div className={commonStyles['form-submit']}>
                    <Fragment>
                        {!isLock && (
                            <Button
                                className={commonStyles['form-btn']}
                                type="primary"
                                loading={submitLoading}
                                onClick={() => {
                                    form.setFieldsValue({
                                        saveType: 'send',
                                    });
                                    form.submit();
                                }}
                            >
                                提交
                            </Button>
                        )}
                        <Button
                            className={commonStyles['form-btn']}
                            onClick={() => {
                                closeEvent && closeEvent();
                            }}
                        >
                            返回
                        </Button>
                    </Fragment>
                </div>
            </Form>
        );
    });

    const pageView = useMemo(() => {
        if (isLock) {
            let contentView = null;
            if (activeTab === TAB_TYPES.INFO) {
                contentView = infoView;
            } else if (activeTab === TAB_TYPES.LINK) {
                contentView = <LinkActView actId={actId} {...props}></LinkActView>;
            }
            return (
                <Card
                    loading={detalisLoading}
                    tabList={tabList}
                    activeTabKey={activeTab}
                    onTabChange={(key) => {
                        changeActiveTab(key);
                    }}
                >
                    {contentView}
                </Card>
            );
        } else {
            return <Card loading={detalisLoading}>{infoView}</Card>;
        }
    });

    return pageView;
});
const GiftBagUpdatePage = (props) => {
    const {
        dispatch,
        match,
        history,
        route,
        currentUser,
        giftBagModel: { editActInfo },
        global,
        couponModel,
        closeEvent,
    } = props;
    const [isLock, changeLock] = useState(
        route.path == '/marketing/platformactive/giftbag/list/look/:actId',
    ); // 是否可编辑
    const [actId, changeActId] = useState(match.params.actId || null);
    const [isCopy, changeCopy] = useState(() => {
        if (route.path.indexOf('/marketing/platformactive/giftbag/list/copy') >= 0) {
            return true;
        }
        return false;
    });
    const goBack = () => {
        history.replace('/marketing/platformactive/giftbag/list');
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <LotteryConfigureView
                {...props}
                actId={actId}
                isCopy={isCopy}
                isLock={isLock}
                closeEvent={goBack}
            ></LotteryConfigureView>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, giftBagModel, loading, couponModel }) => ({
    global,
    currentUser: user.currentUser,
    giftBagModel,
    detalisLoading: loading.effects['giftBagModel/initEditActInfo'],
    operatorLoading: loading.effects['giftBagModel/getOperationList'],
    listLoading: loading.effects['giftBagModel/getLinkActList'],
    couponModel,
    // submitLoading: loading.effects['giftBagModel/getCouponMangeList'],
}))(GiftBagUpdatePage);
