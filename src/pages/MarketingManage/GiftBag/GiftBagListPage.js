import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import {
    stopMiniGiftBagApi,
    deleteMiniGiftBagApi,
    exportGiftBagListApi,
} from '@/services/Marketing/MarketingGiftBagApi';
import { getCompanyOperListApi } from '@/services/Enterprise/EnterpriseManageApi';
import { STATUS_TYPES } from '@/config/declare';
import { exportTableByParams, formatActivePage } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './GiftBag.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import LinkActFormItem from '../CouponManage/components/LinkActFormItem';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, tableLoading, operId } = props;

    const stationRef = useRef();

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={tableLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
            >
                <Col span={8}>
                    <FormItem label="礼包名称" name="giftbagName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <LinkActFormItem form={form} name="actId" />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export const GiftBagListLayout = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        giftBagModel: { giftBagList, giftBagListTotal },
        listLoading,
        isWorkorder,
        workorderEvent,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'giftBagModel/getGiftBagList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const deleteGiftBagEvent = async (item) => {
        confirm({
            title: `确定删除${item.giftbagName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (isWorkorder) {
                        workorderEvent && workorderEvent('del', 'actlist', item);
                    } else {
                        await deleteMiniGiftBagApi(item.giftbagId);
                        searchData();
                    }
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    //导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            type: '02',
        };

        exportGiftBagListApi(params);

        // let columnsStrs = [];
        // for (const item of columns) {
        //     if (item.dataIndex) {
        //         columnsStrs.push({
        //             key: item.dataIndex,
        //             value: item.title,
        //         });
        //     }
        // }
        // exportTableByParams({
        //     methodUrl: '/bil/coupon/couponPutList',
        //     options: params,
        //     columnsStr: columnsStrs,
        // });
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push('/marketing/platformactive/giftbag/list/add');
    };

    const editGiftBagEvent = (item) => {
        if (isWorkorder) {
            workorderEvent && workorderEvent('update', 'actlist', item);
        } else {
            history.push(`/marketing/platformactive/giftbag/list/update/${item.giftbagId}`);
        }
    };

    const lookGiftBagEvent = (item) => {
        if (isWorkorder) {
            workorderEvent && workorderEvent('look', 'actlist', item);
        } else {
            history.push(`/marketing/platformactive/giftbag/list/look/${item.giftbagId}`);
        }
    };

    const copyGiftBagPath = (item) => {
        return `/marketing/platformactive/giftbag/list/copy/${item.giftbagId}`;
    };

    const columns = [
        {
            title: '礼包名称',
            width: 140,
            dataIndex: 'giftbagName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '礼包描述',
            width: 200,
            dataIndex: 'remark',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '关联活动',
            width: 120,
            dataIndex: 'actNum',
            render(text, record) {
                let path = `/marketing/platformactive/giftbag/list/look/${record.giftbagId}?tab=tab2`;
                if (path) {
                    return (
                        <Link to={path} target="_blank">
                            {text}
                        </Link>
                    );
                }
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '已发放数量',
            width: 140,
            dataIndex: 'putNum',
            render(text = '', record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '总库存',
            width: 140,
            dataIndex: 'totalStockNum',
            render(text, record) {
                return <span title={text}>{text || 0}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },

        {
            title: '操作',
            width: 260,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                const copyPath = copyGiftBagPath(record);
                const copyBtn = (
                    <Link to={copyPath} target="_blank">
                        复制
                    </Link>
                );

                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editGiftBagEvent(record)}>
                        编辑
                    </span>
                );
                const lookBtn = (
                    <span className={styles['table-btn']} onClick={() => lookGiftBagEvent(record)}>
                        详情
                    </span>
                );
                const delBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => deleteGiftBagEvent(record)}
                    >
                        删除
                    </span>
                );

                if (Number(record.actNum) > 0) {
                    btnList.push(editBtn);
                    btnList.push(lookBtn);

                    btnList.push(copyBtn);
                } else {
                    btnList.push(editBtn);
                    btnList.push(lookBtn);

                    btnList.push(delBtn);

                    btnList.push(copyBtn);
                }

                return <Space>{btnList}</Space>;
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                tableLoading={listLoading}
                onExportForm={exportFormEvent}
            />
            {!isWorkorder && (
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        新建
                    </Button>
                </div>
            )}

            <TablePro
                name="turnlist"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.giftbagId}
                dataSource={giftBagList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: giftBagListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                offsetHeader={isWorkorder ? 0 : undefined}
            />
        </Card>
    );
};

const GiftBagListPage = (props) => {
    return (
        <PageHeaderWrapper>
            <GiftBagListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, giftBagModel, loading }) => ({
    global,
    giftBagModel,
    currentUser: user.currentUser,
    listLoading: loading.effects['giftBagModel/getGiftBagList'],
}))(GiftBagListPage);
