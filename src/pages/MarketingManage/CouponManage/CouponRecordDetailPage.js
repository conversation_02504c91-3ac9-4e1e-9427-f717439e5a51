import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Descriptions } from 'antd';
import { connect, Link, useParams } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import { formatActivePage } from '@/utils/utils';
import { useMemo } from 'react';

const CouponRecordDetailPage = (props) => {
    const {
        history,
        location,
        route,
        dispatch,
        infoLoading,
        couponManageModel: { couponPutUseInfo },
    } = props;
    const { id, cpnType } = location?.query || {};

    useEffect(() => {
        dispatch({
            type: 'couponManageModel/getCouponPutUseInfo',
            options: { putDetId: id, cpnType },
        });
        return () => {
            dispatch({ type: 'couponManageModel/updateCouponPutUseInfo', info: null });
        };
    }, []);
    const goBack = () => {
        history.replace('/marketing/couponCenter/record/list');
    };
    const linkActPath = useMemo(() => {
        if (couponPutUseInfo) {
            let path = formatActivePage({
                type: String(couponPutUseInfo.actType),
                actId: couponPutUseInfo.actId,
            });
            let actName = couponPutUseInfo.actName || '-';
            if (path) {
                return (
                    <Link to={path} target="_blank">
                        {actName}
                    </Link>
                );
            }
            return <span title={actName}>{actName}</span>;
        }
        return '-';
    }, [couponPutUseInfo]);

    const linkOrderPath = useMemo(() => {
        if (couponPutUseInfo) {
            let orderNo = couponPutUseInfo.orderNo;

            let path = `/financemanage/orderTransaction/list/details/${orderNo}`;
            if (path) {
                return (
                    <Link to={path} target="_blank">
                        {orderNo}
                    </Link>
                );
            }
            return <span title={orderNo}>{orderNo}</span>;
        }
        return '-';
    }, [couponPutUseInfo]);
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card title="发放信息" loading={infoLoading}>
                <Descriptions>
                    <Descriptions.Item label="发放时间">
                        {(couponPutUseInfo && couponPutUseInfo.putTime) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="券名称">
                        {(couponPutUseInfo?.cpnId && couponPutUseInfo?.cpnName && (
                            <Link
                                key={'01'}
                                to={
                                    couponPutUseInfo.cpnType == '01'
                                        ? `/marketing/couponCenter/coupon/list/look/${couponPutUseInfo.cpnId}`
                                        : `/marketing/couponCenter/cpnManage/list/look/${couponPutUseInfo.cpnId}`
                                }
                                target="_blank"
                            >
                                {couponPutUseInfo?.cpnName}
                            </Link>
                        )) ||
                            couponPutUseInfo?.cpnName ||
                            '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="领取渠道">
                        {couponPutUseInfo?.putChannel || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="有效期">
                        {couponPutUseInfo?.cpnDate || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="优惠额度">
                        {couponPutUseInfo?.dctDesc || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="关联活动">{linkActPath}</Descriptions.Item>
                    <Descriptions.Item label="用户手机号">
                        {(couponPutUseInfo && couponPutUseInfo.mobile) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="券id">
                        {(couponPutUseInfo && couponPutUseInfo.cpnId) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="券组编号">
                        {(couponPutUseInfo && couponPutUseInfo.cpnNo) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="出资方">
                        {(couponPutUseInfo && couponPutUseInfo.contributPartyName) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建方">
                        {(couponPutUseInfo && couponPutUseInfo.createOperator) || '-'}
                    </Descriptions.Item>

                    <Descriptions.Item label="资金类型">
                        {(couponPutUseInfo && couponPutUseInfo.capitalTypeName) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="邦道模板ID">
                        {(couponPutUseInfo && couponPutUseInfo.templateId) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="发放人">
                        {(couponPutUseInfo && couponPutUseInfo.putEmp) || '-'}
                    </Descriptions.Item>
                </Descriptions>
            </Card>
            <br></br>
            <Card title="核销信息">
                <Descriptions>
                    <Descriptions.Item label="核销状态">
                        {(couponPutUseInfo && couponPutUseInfo.useStatusName) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="核销时间">
                        {(couponPutUseInfo && couponPutUseInfo.usedTime) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="核销金额">
                        {(couponPutUseInfo && couponPutUseInfo.usedAmt) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="支付宝交易号">
                        {(couponPutUseInfo && couponPutUseInfo.reconciliationNo) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="充电订单号">{linkOrderPath}</Descriptions.Item>
                    <Descriptions.Item label="城市">
                        {(couponPutUseInfo && couponPutUseInfo.cityName) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="运营商">
                        {(couponPutUseInfo && couponPutUseInfo.operatorName) || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="场站名称">
                        {(couponPutUseInfo && couponPutUseInfo.stationName) || '-'}
                    </Descriptions.Item>
                </Descriptions>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, couponManageModel, loading }) => ({
    global,
    couponManageModel,
    infoLoading: loading.effects['couponManageModel/getCouponPutUseInfo'],
}))(CouponRecordDetailPage);
