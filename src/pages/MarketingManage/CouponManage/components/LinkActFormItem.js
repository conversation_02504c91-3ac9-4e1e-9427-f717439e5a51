import {
    Button,
    Card,
    Form,
    // message,
    // Icon,
    Input,
    Modal,
    Row,
    Col,
    Select,
    Tabs,
    Space,
    message,
} from 'antd';
import {
    Fragment,
    useState,
    useRef,
    useMemo,
    useEffect,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { CloseCircleOutlined } from '@ant-design/icons';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { connect } from 'umi';

import { getCouponPutUseActListApi } from '@/services/Marketing/MarketingCouponApi';
import { STATUS_TYPES } from '@/config/declare';

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, global, dispatch } = props;

    const { codeInfo } = global;
    const { actType } = codeInfo;

    useEffect(() => {
        if (!actType) {
            dispatch({
                type: 'global/initCode',
                code: 'actType',
            });
        }
    }, []);

    const actTypeOptions = useMemo(() => {
        if (actType instanceof Array) {
            return actType.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [actType]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            name="linkAct"
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <FormItem label="活动名称:" name="actName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动编号:" name="actNo">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="活动类型:" name="actType">
                        <Select placeholder="请选择">{actTypeOptions}</Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ActListModal = forwardRef((props, ref) => {
    const { visible, onClose, onFinish, global } = props;

    const [form] = Form.useForm();

    const [listLoading, updateListLoading] = useState(false);

    const [list, updateList] = useState([]);
    const [listTotal, updateListTotal] = useState(0);

    const [selectList, updateSelectList] = useState([]);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        undefined,
        'actLinkModal',
    );

    useImperativeHandle(ref, () => ({
        clearSelect: () => {
            updateSelectList([]);
        },
    }));
    useEffect(() => {
        searchData();
    }, [pageInfo]);
    useEffect(() => {
        if (!visible) {
            updateList([]);
        }
    }, [visible]);
    const searchData = async () => {
        try {
            updateListLoading(true);
            const data = form.getFieldsValue();
            let params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                ...data,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.actState = pageInfo.tabType;
            }
            const {
                data: { records, total },
            } = await getCouponPutUseActListApi(params);
            updateList(records);
            updateListTotal(total);
            return records;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };
    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };
    const changeTabType = (type) => {
        changePageInfo((state) => ({
            ...state,
            tabType: type,
            pageIndex: 1,
        }));
    };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '活动编号',
            dataIndex: 'actNo',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动类型',
            dataIndex: 'actTypeName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称',
            dataIndex: 'actName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动时间',
            dataIndex: 'actTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动状态',
            dataIndex: 'actStateName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '累计参与人数',
        //     dataIndex: 'putTime',
        //     width: 200,
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: '累计发放数量',
        //     dataIndex: 'putTime',
        //     width: 200,
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: '剩余奖品数量',
        //     dataIndex: 'putTime',
        //     width: 200,
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
    ];

    const rowSelection = {
        type: 'checkbox',
        selectedRowKeys: selectList.map((item) => item.actId),
        onChange: (selectedRowKeys, selectedRows) => {
            // 筛选出非当前页的勾选项，不予处理
            let otherCpns = selectList.filter(
                (x) => list.filter((now) => now.actId == x.actId).length == 0,
            );
            updateSelectList([...otherCpns, ...selectedRows]);
        },
        getCheckboxProps: (record) => ({
            name: record.actId,
        }),
    };
    return (
        <Fragment>
            <Modal
                title={'选择关联活动'}
                destroyOnClose
                width={1200}
                visible={visible}
                onCancel={() => onClose && onClose()}
                footer={null}
                maskClosable={false}
            >
                <SearchLayout
                    {...props}
                    form={form}
                    listLoading={listLoading}
                    onSubmit={searchData}
                    onReset={resetData}
                ></SearchLayout>
                <Tabs activeKey={pageInfo.tabType} onChange={changeTabType}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="未开始" key={STATUS_TYPES.NOSTART} />
                    <TabPane tab="进行中" key={STATUS_TYPES.DOING} />
                    <TabPane tab="已停止" key={STATUS_TYPES.STOP} />
                    <TabPane tab="已结束" key={STATUS_TYPES.END} />
                    <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
                </Tabs>
                <TablePro
                    name="link"
                    loading={listLoading}
                    scroll={{ x: 'max-content', y: 250 }}
                    rowKey={(record, index) => record.actId}
                    dataSource={list}
                    columns={columns}
                    onChange={onTableChange}
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: listTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                        type="primary"
                        onClick={() => {
                            onFinish && onFinish(selectList);
                        }}
                    >
                        提交
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </Space>
            </Modal>
        </Fragment>
    );
});

const LinkActFormItem = (props) => {
    const { label = '关联活动', name, form, ...otherProps } = props;

    const actIdStr = Form.useWatch('actId', form);

    const selectRef = useRef();

    useEffect(() => {
        let actIdList = [];
        if (typeof actIdStr === 'string') {
            actIdList = actIdStr.split(',');
        }

        let list = selectActList.filter((ele) => actIdList.includes(ele.actId));
        updateSelectActList(list);
    }, [actIdStr]);

    const [selectActList, updateSelectActList] = useState([]);
    const [visible, toggleVisible] = useState(false);
    const selectActName = useMemo(() => {
        return selectActList.reduce((a, b) => {
            return a + b.actName + ',';
        }, '');
    }, [selectActList]);
    const openModalEvent = () => {
        toggleVisible(true);
    };
    const closeModalEvent = () => {
        toggleVisible(false);
    };
    const changeActEvent = (list) => {
        updateSelectActList(list);
        if (list?.length) {
            const result = list.map((ele) => ele.actId);
            form?.setFieldsValue({ [name]: result.join(',') });
        }
        closeModalEvent();
    };
    const clearActEvent = (list) => {
        updateSelectActList([]);
        form?.setFieldsValue({ [name]: [] });
        selectRef.current && selectRef.current.clearSelect();
    };
    return (
        <Fragment>
            <FormItem label={label}>
                <Input
                    value={selectActName}
                    readOnly
                    placeholder="请选择"
                    suffix={
                        selectActList?.length > 0 ? (
                            <CloseCircleOutlined onClick={clearActEvent} />
                        ) : undefined
                    }
                    onClick={() => {
                        openModalEvent();
                    }}
                ></Input>
            </FormItem>
            <FormItem noStyle name={name} {...otherProps}></FormItem>
            {visible && (
                <ActListModal
                    {...props}
                    ref={selectRef}
                    selectActList={selectActList}
                    visible={visible}
                    onClose={closeModalEvent}
                    onFinish={changeActEvent}
                ></ActListModal>
            )}
        </Fragment>
    );
};
export default connect(({ global, loading }) => ({
    global,
}))(LinkActFormItem);
