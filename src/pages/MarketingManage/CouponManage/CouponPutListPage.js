import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    message,
    // Icon,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
    Space,
} from 'antd';
import { connect, Link } from 'umi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import usePageState from '@/hooks/usePageState.js';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';

import { delCouponApi } from '@/services/Marketing/MarketingCouponApi';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const STATUS_TYPES = {
    ALL: '00', // 全部
    NOUSE: '01', // 未核销
    USE: '03', // 已核销
    OVER: '04', // 已过期
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const { location, listLoading, form, onSubmit, onReset, onExportForm } = props;

    const {
        query: { cpnNo, creTime },
    } = location;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [creTime ? moment(creTime) : moment().subtract(1, 'weeks'), moment()],
                cpnNo,
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onExportForm={onExportForm}
                onReset={resetForm}
                open={!!creTime}
            >
                <Col span={8}>
                    <FormItem
                        label="发放日期:"
                        name="dates"
                        {...formItemLayout}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择结束日期');
                                    }
                                    if (value[0] && value[1]) {
                                        const startTime = +new Date(value[0]);
                                        const endTime = +new Date(value[1]);
                                        const dest = 60 * 1000 * 60 * 24 * 60;

                                        if (Math.abs(startTime - endTime) > dest) {
                                            return Promise.reject('选取范围最大不超过60天');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="用户手机号:" name="mobile" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="关联活动:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券ID:" name="thirdRelateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券组编号:" name="cpnNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="支付宝模板ID:" name="alipayTempLateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="邦道模板ID:" name="tempLateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="出资方:" name="contributParty" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="01">运营商</Option>
                            <Option value="02">平台</Option>
                            <Option value="04">混合出资</Option>
                            <Option value="05">大客户</Option>
                        </Select>
                    </FormItem>
                </Col>

                {/* <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col> */}
            </SearchOptionsBar>
        </Form>
    );
};

const CouponManageListPage = (props) => {
    const {
        dispatch,
        history,
        couponManageModel: { couponPutList, couponPutListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(
        () => () => {
            dispatch({
                type: 'couponManageModel/updateCouponPutList',
                couponPutList: [],
                total: 0,
            });
        },
        [],
    );

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
            actName: data.actName || '',
            contributParty: data.contributParty || '',
            tempLateId: data.tempLateId || '',
            alipayTempLateId: data.alipayTempLateId || '',
            cpnNo: data.cpnNo || '',
            thirdRelateId: data.thirdRelateId || '',
            mobile: data.mobile || '',
            useStatus: pageInfo.tabType,
        };

        dispatch({
            type: 'couponManageModel/getCouponPutList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
            actName: data.actName || '',
            contributParty: data.contributParty || '',
            tempLateId: data.tempLateId || '',
            alipayTempLateId: data.alipayTempLateId || '',
            cpnNo: data.cpnNo || '',
            thirdRelateId: data.thirdRelateId || '',
            mobile: data.mobile || '',
            useStatus: pageInfo.tabType,
        };
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: '/bil/coupon/couponPutList',
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const columns = [
        {
            title: '发放时间',
            dataIndex: 'putTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券状态',
            width: 200,
            dataIndex: 'writeOffName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券ID',
            width: 260,
            dataIndex: 'thirdRelateId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '关联活动',
            width: 260,
            dataIndex: 'actName',
            render(text, record) {
                let UrlLink = null;
                switch (String(record.actType)) {
                    case '14':
                        UrlLink = (
                            <Link
                                to={{
                                    pathname: `/marketing/couponCenter/appletgift/look/${record.actId}`,
                                }}
                                target="_blank"
                            >
                                {text}
                            </Link>
                        );
                        break;
                    case '15':
                        UrlLink = (
                            <Link
                                to={{
                                    pathname: `/marketing/platformactive/turnOver/list/look/${record.actId}`,
                                }}
                                target="_blank"
                            >
                                {text}
                            </Link>
                        );
                        break;

                    default:
                        UrlLink = <span title={text}>{text}</span>;
                        break;
                }
                return UrlLink;
            },
        },
        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券面额',
            width: 120,
            align: 'right',
            dataIndex: 'cpnAmt',
            render(text, record) {
                return <span title={text}>{String(record.cpnAmt)}</span>;
            },
        },

        {
            title: '出资方',
            width: 120,
            dataIndex: 'contributPartyName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券组编号',
            width: 120,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建方',
            width: 140,
            dataIndex: 'createOperatorTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '资金类型',
            width: 140,
            dataIndex: 'capitalTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '邦道模板ID',
            width: 260,
            dataIndex: 'templateId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <Tabs defaultActiveKey={STATUS_TYPES.ALL} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="未核销" key={STATUS_TYPES.NOUSE} />
                    <TabPane tab="已核销" key={STATUS_TYPES.USE} />
                    <TabPane tab="已过期" key={STATUS_TYPES.OVER} />
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.getId}
                    dataSource={couponPutList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: couponPutListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ couponManageModel, loading }) => ({
    couponManageModel,
    listLoading: loading.effects['couponManageModel/getCouponPutList'],
}))(CouponManageListPage);
