import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
    Space,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import usePageState from '@/hooks/usePageState.js';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import { delCouponApi } from '@/services/Marketing/MarketingCouponApi';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const STATUS_TYPES = {
    ALL: '00', // 全部
    COST: '01', // 消费
    BACK: '02', // 退回
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const { location, listLoading, form, onSubmit, onReset, onExportForm } = props;

    const {
        query: { cpnNo, creTime },
    } = location;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [creTime ? moment(creTime) : moment().subtract(1, 'weeks'), moment()],
                cpnNo,
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onExportForm={onExportForm}
                onReset={resetForm}
                open={!!creTime}
            >
                <Col span={8}>
                    <FormItem label="核销日期:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="用户手机号:" name="mobile" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="关联活动:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券ID:" name="thirdRelateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单号:" name="appNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="支付宝交易号:" name="reconciliationNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="券组编号:" name="cpnNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="支付宝模板ID:" name="alipayTemplateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="邦道模板ID:" name="templateId" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem form={form} {...formItemLayout} />
                </Col>
                <Col span={8}>
                    <FormItem label="场站名称:" name="stationName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="出资方:" name="contributParty" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="01">运营商</Option>
                            <Option value="02">平台</Option>
                            <Option value="04">混合出资</Option>
                            <Option value="05">大客户</Option>
                        </Select>
                    </FormItem>
                </Col>

                {/* <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col> */}
            </SearchOptionsBar>
        </Form>
    );
};

const CouponManageListPage = (props) => {
    const {
        dispatch,
        history,
        couponManageModel: { couponUseList, couponUseListTotal },

        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(
        () => () => {
            dispatch({
                type: 'couponManageModel/updateCouponUseList',
                couponUseList: [],
                total: 0,
            });
        },
        [],
    );

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
            actName: data.actName || '',
            contributParty: data.contributParty || '',
            templateId: data.templateId || '',
            alipayTemplateId: data.alipayTemplateId || '',
            cpnNo: data.cpnNo || '',
            thirdRelateId: data.thirdRelateId || '',
            mobile: data.mobile || '',
            appNo: data.appNo,
            reconciliationNo: data.reconciliationNo,
            operId: data.operId,
            stationName: data.stationName,
        };

        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.appType = pageInfo.tabType;
        }

        dispatch({
            type: 'couponManageModel/getCouponUseList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
            actName: data.actName || '',
            contributParty: data.contributParty || '',
            templateId: data.templateId || '',
            alipayTemplateId: data.alipayTemplateId || '',
            cpnNo: data.cpnNo || '',
            thirdRelateId: data.thirdRelateId || '',
            mobile: data.mobile || '',
            useStatus: pageInfo.tabType,
            appNo: data.appNo,
            reconciliationNo: data.reconciliationNo,
            operId: data.operId,
            stationName: data.stationName,
        };

        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: '/bil/coupon/couponUseList',
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const columns = [
        {
            title: '核销时间',
            dataIndex: 'usedTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销类型',
            dataIndex: 'appTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '关联活动',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                let UrlLink = null;
                switch (String(record.actType)) {
                    case '14':
                        UrlLink = (
                            <Link
                                to={{
                                    pathname: `/marketing/couponCenter/appletgift/list/look/${record.actId}`,
                                }}
                                target="_blank"
                            >
                                {text}
                            </Link>
                        );
                        break;
                    case '15':
                        UrlLink = (
                            <Link
                                to={{
                                    pathname: `/marketing/platformactive/turnOver/list/look/${record.actId}`,
                                }}
                                target="_blank"
                            >
                                {text}
                            </Link>
                        );
                        break;

                    default:
                        UrlLink = <span title={text}>{text}</span>;
                        break;
                }
                return UrlLink;
            },
        },
        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销金额',
            width: 120,
            align: 'right',
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{String(record.usedAmt)}</span>;
            },
        },
        {
            title: '充电订单号',
            width: 200,
            dataIndex: 'appNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付宝交易号',
            width: 200,
            dataIndex: 'reconciliationNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '出资方',
            width: 120,
            dataIndex: 'contributPartyName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券组编号',
            width: 120,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券ID',
            width: 260,
            dataIndex: 'thirdRelateId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建方',
            width: 140,
            dataIndex: 'createOperatorTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '资金类型',
            width: 140,
            dataIndex: 'capitalTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站名称',
            width: 140,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <Tabs defaultActiveKey={STATUS_TYPES.ALL} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="消费" key={STATUS_TYPES.COST} />
                    <TabPane tab="退回" key={STATUS_TYPES.BACK} />
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={couponUseList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: couponUseListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, couponManageModel, loading }) => ({
    global,
    couponManageModel,
    listLoading: loading.effects['couponManageModel/getCouponUseList'],
}))(CouponManageListPage);
