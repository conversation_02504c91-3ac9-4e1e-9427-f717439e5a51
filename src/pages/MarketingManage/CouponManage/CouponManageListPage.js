import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    InputNumber,
    Row,
    Select,
    Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
    Space,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import React, { Fragment, useEffect, useState, useMemo, useRef } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';

import {
    delCouponApi,
    addCouponNumApi,
    stopCouponApi,
    synCouponStateApi,
} from '@/services/Marketing/MarketingCouponApi';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const STATUS_TYPES = {
    ALL: '00',
    DRAFT: '01', // 草稿
    NOBEGIN: '02', // 未开始
    SEND: '03', // 可发放
    END: '04', // 已结束
    UNPAY: '05', // 待付款
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
        currentUser,
        user: {
            currentUser: { operId },
        },
        global: { cpnOwnerOptions, enterpriseChildList },
    } = props;

    const isSaller = props.location?.pathname?.indexOf?.('sellerCenter') > 0;

    useEffect(() => {
        if (!cpnOwnerOptions || cpnOwnerOptions.length == 0) {
            dispatch({
                type: 'global/initCpnOwnerOptions',
            });
        }
    }, []);

    const cpnOwnerOptionsView = useMemo(
        () =>
            cpnOwnerOptions.map((ele) => {
                if (operId && ele.codeValue == '02') {
                    // 运营商账号不能设第三方外部券
                    return null;
                }
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            }),
        [cpnOwnerOptions],
    );

    const onFinish = () => {
        onSubmit();
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            initialValues={{
                dates: [moment().subtract(60, 'days').endOf('day'), moment().endOf('day')],
            }}
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onExportForm={onExportForm} onReset={resetForm}>
                <Col span={8} key={1}>
                    <FormItem
                        label="创建日期:"
                        name="dates"
                        {...formItemLayout}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    const actName = getFieldValue('actName');
                                    const cpnName = getFieldValue('cpnName');
                                    const cpnNo = getFieldValue('cpnNo');

                                    if (!cpnNo && !actName && !cpnName && !value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (value instanceof Array) {
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0].endOf('day'));

                                            let beforeDate = value[1]
                                                .clone()
                                                .subtract(60, 'days')
                                                .endOf('day');
                                            let errResult = '60天';

                                            if (actName || cpnName) {
                                                beforeDate = value[1]
                                                    .clone()
                                                    .subtract(1, 'years')
                                                    .endOf('day');
                                                errResult = '1年';
                                            }

                                            const beforeTime = +new Date(beforeDate);

                                            if (startTime < beforeTime) {
                                                return Promise.reject(
                                                    `选取范围最大不超过${errResult}`,
                                                );
                                            }
                                        }
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8} key={2}>
                    <FormItem label="活动名称:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8} key={3}>
                    <FormItem label="券名称:" name="cpnName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8} key={4}>
                    <FormItem label="券类型:" name="cpnOwner" {...formItemLayout}>
                        <Select placeholder="请选择">{cpnOwnerOptionsView}</Select>
                    </FormItem>
                </Col>
                <Col span={8} key={5}>
                    <FormItem label="资金类型:" name="capitalType" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="01">资金券</Option>
                            <Option value="02">优惠券</Option>
                            <Option value="03">定向券</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8} key={6}>
                    <FormItem label="券组编号:" name="cpnNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8} key={7}>
                    <FormItem label="出资方:" name="contributParty" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="01">运营商</Option>
                            <Option value="02">平台</Option>
                            <Option value="04">混合出资</Option>
                            <Option value="05">大客户</Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const CouponManageListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        couponModel: { couponMangeList, couponMangeListTotal, almNum, cpnAlmValue },
        listLoading,
        currentUser,
        global: { cpnOwnerOptions, enterpriseChildList },
        customerSystemModel,
    } = props;

    const isSaller = props.location?.pathname?.indexOf?.('sellerCenter') > 0;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [appendForm] = Form.useForm();

    const [showAppendView, toggleAppendView] = useState(false);

    const [showPayView, togglePayView] = useState(false);
    const [curCpnInfo, changeCurCpnInfo] = useState(null);
    const [appendCount, changeAppendCount] = useState(0);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        () => ({
            tabType: isSaller ? undefined : STATUS_TYPES.SEND,
            almFlag: false,
        }),
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'couponModel/updateCouponMangeProperty',
                params: {
                    couponMangeList: [],
                    couponMangeListTotal: 0,
                    almNum: 0,
                    cpnAlmValue: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
                cpnName: data.cpnName || '',
                cpnOwner: data.cpnOwner || '',
                cpnNo: data.cpnNo || '',
                actName: data.actName || '',
                capitalType: data.capitalType || '',
                contributParty: data.contributParty || '',
                almFlag: pageInfo.almFlag ? '1' : '0',
                enterpriseId: data.enterpriseId || '',
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.cpnStatus = pageInfo.tabType;
            }
            if (data.childEnterpriseId) {
                params.enterpriseId = data.childEnterpriseId;
                params.childEnterpriseId = undefined;
            }
            if ((params.actName || params.cpnName) && (!params.beginDate || !params.endDate)) {
                params.beginDate = moment().subtract(1, 'years').format('YYYY-MM-DD');
                params.endDate = moment().format('YYYY-MM-DD');
            }

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'couponModel/getCouponMangeList',
                options: params,
            });
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    // 导出
    const exportFormEvent = () => {
        form.validateFields().then((data) => {
            const params = {
                beginDate: (data.dates && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1].format('YYYY-MM-DD')) || '',
                cpnName: data.cpnName || '',
                cpnOwner: data.cpnOwner || '',
                cpnNo: data.cpnNo || '',
                actName: data.actName || '',
                capitalType: data.capitalType || '',
                contributParty: data.contributParty || '',
                enterpriseId: data.enterpriseId,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.cpnStatus = pageInfo.tabType;
            }
            if (data.childEnterpriseId) {
                params.enterpriseId = data.childEnterpriseId;
                params.childEnterpriseId = undefined;
            }

            if ((params.actName || params.cpnName) && (!params.beginDate || !params.endDate)) {
                params.beginDate = moment().subtract(1, 'years').format('YYYY-MM-DD');
                params.endDate = moment().format('YYYY-MM-DD');
            }

            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: '/bil/coupon/couponList',
                options: params,
                columnsStr: columnsStrs,
            });
        });
    };

    const changeTabType = (type) => {
        changePageInfo((state) => ({
            ...state,
            tabType: type,
            pageIndex: 1,
        }));
    };

    const changeAppendCountEvent = (value) => {
        changeAppendCount(value);
    };

    const openAppendViewEvent = (item) => {
        changeAppendCount(0);
        changeCurCpnInfo(item);
        toggleAppendView(true);
    };
    const closeAppendViewEvent = () => {
        changeCurCpnInfo(null);
        toggleAppendView(false);
    };

    const openPayViewEvent = (item) => {
        changeCurCpnInfo(item);
        togglePayView(true);
    };
    const closePayViewEvent = () => {
        changeCurCpnInfo(null);
        togglePayView(false);
    };
    const syncStatusEvent = async () => {
        try {
            await synCouponStateApi(curCpnInfo.cpnId);
            searchData();
            togglePayView(false);
        } catch (error) {}
    };

    const onAppendFinish = async (values) => {
        try {
            const { append } = values;
            await addCouponNumApi({
                cpnId: curCpnInfo.cpnId,
                cpnAddNum: append,
            });
            searchData();
            message.success('追加成功');
            changeCurCpnInfo(null);
            toggleAppendView(false);
        } catch (error) {
            console.log(444, error);
        } finally {
        }
    };

    const jumpPath = useMemo(() => {
        if (isSaller) {
            return '/sellerCenter/customer';
        }
        return '/marketing/couponCenter';
    }, [isSaller]);

    const enterpriseId = Form.useWatch('enterpriseId', form);
    const jumpMarketingEvent = (item) => {
        dispatch({
            type: 'customerSystemModel/updateSearchParams',
            params: {
                tabType: '04',
                enterpriseId: enterpriseId,
                actNo: item.actNo,
                childEnterpriseId:
                    (enterpriseId != item.enterpriseId && item.enterpriseId) || undefined,
            },
        });
        history.push('/sellerCenter/customer/account/list');
    };

    const gotoUpdateCouponEvent = (item) => {
        history.push(`${jumpPath}/coupon/list/update/${item.cpnId}`);
    };
    const gotoDetailsCouponEvent = (item) => {
        history.push(`${jumpPath}/coupon/list/look/${item.cpnId}`);
    };
    const gotoCopyCouponPath = (item) => {
        return `${jumpPath}/coupon/list/copy/${item.cpnId}`;
    };
    const delCouponEvent = (item) => {
        confirm({
            title: `确定删除${item.cpnName}?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await delCouponApi({ cpnId: item.cpnId });
                    message.success('删除成功');
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const stopCouponEvent = async (item) => {
        confirm({
            title: `确定停止${item.cpnName}?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await stopCouponApi({ cpnId: item.cpnId });
                    message.success('删除成功');
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const gotoAddEvent = () => {
        history.push(`${jumpPath}/coupon/list/add`);
    };
    const gotoPutPage = (item) => {
        if (item.putNum > 0) {
            history.push(
                `/marketing/couponCenter/couponPut?cpnNo=${item.cpnNo}&creTime=${item.creTime}`,
            );
        }
    };
    const gotoUsePage = (item) => {
        if (item.useNum > 0) {
            history.push(
                `/marketing/couponCenter/couponUse?cpnNo=${item.cpnNo}&creTime=${item.creTime}`,
            );
        }
    };

    const columns = useMemo(() => {
        if (isSaller) {
            return [
                {
                    title: '发放有效期',
                    dataIndex: 'getDate',
                    width: 200,
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '券组编号',
                    width: 120,
                    dataIndex: 'cpnNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '优惠券名称',
                    width: 160,
                    dataIndex: 'cpnName',
                    render(text, record) {
                        if (record.cpnId) {
                            return (
                                <Link
                                    key={'01'}
                                    to={`/marketing/couponCenter/coupon/list/look/${record.cpnId}`}
                                    target="_blank"
                                >
                                    {text}
                                </Link>
                            );
                        }
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '券类型',
                    width: 100,
                    dataIndex: 'cpnOwnerName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '券面额',
                    width: 100,
                    dataIndex: 'cpnAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '券门槛',
                    width: 140,
                    dataIndex: 'dctCondValue',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '有效期',
                    width: 200,
                    dataIndex: 'validTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '数量',
                    width: 120,
                    dataIndex: 'cpnNum',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '发放数量',
                    width: 120,
                    dataIndex: 'putNum',
                    render(text, record) {
                        if (record.putNum > 0) {
                            return <a onClick={() => jumpMarketingEvent(record)}>{text || '0'}</a>;
                        }
                        return text || '0';
                    },
                },
                {
                    title: '核销数量',
                    width: 120,
                    dataIndex: 'useNum',
                    render(text, record) {
                        if (record.useNum > 0) {
                            return <a onClick={() => jumpMarketingEvent(record)}>{text || '0'}</a>;
                        }
                        return text || '0';
                    },
                },
                {
                    title: '券状态',
                    width: 120,
                    dataIndex: 'cpnStatusName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
            ];
        }
        return [
            {
                title: '创建时间',
                dataIndex: 'creTime',
                width: 160,
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '券名称',
                width: 200,
                dataIndex: 'cpnName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '关联活动',
                width: 200,
                dataIndex: 'actName',
                render(text, record) {
                    let UrlLink = null;
                    switch (String(record.actType)) {
                        case '14':
                            UrlLink = (
                                <Link
                                    to={{
                                        pathname: `/marketing/couponCenter/appletgift/list/look/${record.actId}`,
                                    }}
                                    target="_blank"
                                >
                                    {text}
                                </Link>
                            );
                            break;
                        case '15':
                            UrlLink = (
                                <Link
                                    to={{
                                        pathname: `/marketing/platformactive/turnOver/list/look/${record.actId}`,
                                    }}
                                    target="_blank"
                                >
                                    {text}
                                </Link>
                            );
                            break;

                        default:
                            UrlLink = <span title={text}>{text}</span>;
                            break;
                    }
                    return UrlLink;
                },
            },
            {
                title: '出资方',
                width: 100,
                dataIndex: 'contributPartyName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '券类型',
                width: 100,
                dataIndex: 'cpnOwnerName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '面额',
                width: 100,
                dataIndex: 'cpnAmt',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '创建数量',
                width: 120,
                dataIndex: 'cpnNum',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '发放数量',
                width: 120,
                dataIndex: 'putNum',
                render(text, record) {
                    if (record.putNum > 0) {
                        return (
                            <Link
                                to={`/marketing/couponCenter/couponPut?cpnNo=${record.cpnNo}&creTime=${record.creTime}`}
                                target="_blank"
                            >
                                {text || '0'}
                            </Link>
                        );
                    }
                    return text || '0';
                },
            },
            {
                title: '核销数量',
                width: 120,
                dataIndex: 'useNum',
                render(text, record) {
                    if (record.useNum > 0) {
                        return (
                            <Link
                                to={`/marketing/couponCenter/couponUse?cpnNo=${record.cpnNo}&creTime=${record.creTime}`}
                                target="_blank"
                            >
                                {text || '0'}
                            </Link>
                        );
                    }
                    return text || '0';
                },
            },
            {
                title: '券组编号',
                width: 120,
                dataIndex: 'cpnNo',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '创建方',
                width: 100,
                dataIndex: 'createOperator',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '资金类型',
                width: 120,
                dataIndex: 'capitalTypeName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: '可发放状态',
                width: 140,
                dataIndex: 'cpnStatusName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '操作',
                fixed: 'right',
                width: 240,
                align: 'center',
                render(text, record) {
                    let btnList = [];

                    if (record.cpnStatus == STATUS_TYPES.SEND) {
                        btnList = btnList.concat([
                            <span
                                className={styles['table-btn']}
                                onClick={() => {
                                    gotoDetailsCouponEvent(record);
                                }}
                                key={`detils_${record.cpnId}`}
                            >
                                详情
                            </span>,
                            <span
                                className={styles['table-btn']}
                                key={`stop_${record.cpnId}`}
                                onClick={() => {
                                    stopCouponEvent(record);
                                }}
                            >
                                终止
                            </span>,
                        ]);
                    }
                    if (record.cpnStatus == STATUS_TYPES.UNPAY) {
                        btnList = btnList.concat([
                            <span
                                className={styles['table-btn']}
                                key={`detils_${record.cpnId}`}
                                onClick={() => {
                                    gotoDetailsCouponEvent(record);
                                }}
                            >
                                详情
                            </span>,
                            <span
                                className={styles['table-btn']}
                                key={`del_${record.cpnId}`}
                                onClick={() => {
                                    delCouponEvent(record);
                                }}
                            >
                                删除
                            </span>,
                            <span
                                className={styles['table-btn']}
                                key={`pay_${record.cpnId}`}
                                onClick={() => {
                                    openPayViewEvent(record);
                                }}
                            >
                                付款链接
                            </span>,
                        ]);
                    }
                    if (
                        record.cpnStatus == STATUS_TYPES.NOBEGIN ||
                        record.cpnStatus == STATUS_TYPES.DRAFT
                    ) {
                        btnList = btnList.concat([
                            <span
                                className={styles['table-btn']}
                                key={`edit_${record.cpnId}`}
                                onClick={() => {
                                    gotoUpdateCouponEvent(record);
                                }}
                            >
                                编辑
                            </span>,
                            <span
                                className={styles['table-btn']}
                                key={`detils_${record.cpnId}`}
                                onClick={() => {
                                    gotoDetailsCouponEvent(record);
                                }}
                            >
                                详情
                            </span>,
                            <span
                                className={styles['table-btn']}
                                key={`del_${record.cpnId}`}
                                onClick={() => {
                                    delCouponEvent(record);
                                }}
                            >
                                删除
                            </span>,
                        ]);
                    }
                    if (record.cpnStatus == STATUS_TYPES.END) {
                        btnList = btnList.concat([
                            <span
                                className={styles['table-btn']}
                                key={`detils_${record.cpnId}`}
                                onClick={() => {
                                    gotoDetailsCouponEvent(record);
                                }}
                            >
                                详情
                            </span>,
                        ]);
                    }

                    const copyPath = gotoCopyCouponPath(record);

                    btnList.push(
                        <Link to={copyPath} target="_blank">
                            复制
                        </Link>,
                    );
                    return <Space>{btnList}</Space>;
                },
            },
        ];
    }, [isSaller, enterpriseId]);

    return (
        <PageHeaderWrapper
            content={
                isSaller ? (
                    <Form
                        {...formItemLayout}
                        form={form}
                        initialValues={{ childEnterpriseId: currentUser?.childEnterpriseId }}
                    >
                        <Row gutter={24}>
                            <Col span={8}>
                                <EnterpriseSelectItem
                                    rules={[{ required: true, message: '请选择客户' }]}
                                    onChange={() => {
                                        form.setFieldsValue({ childEnterpriseId: undefined });
                                        changePageInfo((state) => ({
                                            ...state,
                                            pageIndex: 1,
                                        }));
                                    }}
                                />
                            </Col>
                            <Col span={8}>
                                <FormItem label="子公司" name="childEnterpriseId">
                                    <Select
                                        placeholder="请选择"
                                        autoComplete="off"
                                        disabled={currentUser?.childEnterpriseId?.length > 0}
                                        onChange={() => {
                                            changePageInfo((state) => ({
                                                ...state,
                                                pageIndex: 1,
                                            }));
                                        }}
                                        allowClear
                                    >
                                        {enterpriseChildList?.map((item) => {
                                            return (
                                                <Option
                                                    value={item.enterpriseId}
                                                    key={item.enterpriseId}
                                                >
                                                    {item.enterpriseName}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                ) : null
            }
        >
            <Card>
                {isSaller ? null : (
                    <SearchLayout
                        form={form}
                        {...props}
                        onSubmit={() => {
                            changePageInfo((state) => ({
                                ...state,
                                pageIndex: 1,
                            }));
                        }}
                        onReset={resetData}
                        onExportForm={!isSaller && exportFormEvent}
                    />
                )}
                {isSaller ? null : (
                    <div className={styles['btn-bar']}>
                        <Button type="primary" onClick={gotoAddEvent}>
                            新建
                        </Button>
                    </div>
                )}
                {isSaller ? null : (
                    <Tabs activeKey={pageInfo.tabType} onChange={changeTabType}>
                        <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                        <TabPane tab="草稿" key={STATUS_TYPES.DRAFT} />
                        <TabPane tab="待付款" key={STATUS_TYPES.UNPAY} />
                        <TabPane tab="未开始" key={STATUS_TYPES.NOBEGIN} />
                        <TabPane tab="可发放" key={STATUS_TYPES.SEND} />
                        <TabPane tab="已结束" key={STATUS_TYPES.END} />
                    </Tabs>
                )}
                {almNum > 0 ? (
                    <Alert
                        style={{ marginBottom: '15px' }}
                        message={
                            <div>
                                预警：存在 {almNum} 张券剩余可发放数量小于{cpnAlmValue}%
                                {!pageInfo.almFlag ? (
                                    <span
                                        className={styles['table-btn']}
                                        onClick={() => {
                                            changePageInfo({
                                                almFlag: true,
                                            });
                                        }}
                                    >
                                        查看
                                    </span>
                                ) : (
                                    <span
                                        className={styles['table-btn']}
                                        onClick={() => {
                                            changePageInfo({
                                                almFlag: false,
                                            });
                                        }}
                                    >
                                        清空筛选
                                    </span>
                                )}
                            </div>
                        }
                        type="warning"
                        showIcon
                    />
                ) : null}

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.cpnId}
                    dataSource={couponMangeList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: couponMangeListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={(curCpnInfo && curCpnInfo.cpnName) || ''}
                width={300}
                visible={showAppendView}
                onCancel={closeAppendViewEvent}
                footer={null}
                maskClosable={false}
            >
                <Form
                    {...formItemLayout}
                    form={appendForm}
                    onFinish={onAppendFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <FormItem label="追加数量">
                        <FormItem
                            name="append"
                            noStyle
                            rules={[{ required: true, message: '请填写' }]}
                        >
                            <InputNumber
                                value={appendCount}
                                onChange={changeAppendCountEvent}
                                min={1}
                                step={1}
                                placeholder="请输入"
                            />
                        </FormItem>
                        <span> 张</span>
                    </FormItem>
                    <div className={styles['btn-bar']}>
                        <Button className={styles['btn-item']} type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button className={styles['btn-item']} onClick={closeAppendViewEvent}>
                            取消
                        </Button>
                    </div>
                </Form>
            </Modal>
            <Modal
                title="付款链接"
                width={300}
                visible={showPayView}
                onCancel={closePayViewEvent}
                footer={null}
                maskClosable={false}
            >
                <div>{(curCpnInfo && curCpnInfo.confirmUri) || ''}</div>

                <div>
                    <Button type="primary" onClick={syncStatusEvent}>
                        我已完成支付
                    </Button>
                </div>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, couponModel, loading, customerSystemModel }) => ({
    global,
    user,
    couponModel,
    listLoading: loading.effects['couponModel/getCouponMangeList'],
    currentUser: user.currentUser,
    customerSystemModel,
}))(CouponManageListPage);
