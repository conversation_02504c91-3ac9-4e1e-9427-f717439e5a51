import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Popconfirm,
    Space,
    Tabs,
    DatePicker,
    message,
    Alert,
    Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, history } from 'umi';

import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
// import {
//     getOrderDiscountList,
//     updateStateOrderDiscount,
// } from '@/services/Marketing/OrderDiscountsApi';
import moment from 'moment';
import {
    getActEquityPage,
    updateActEquityStatus,
} from '@/services/Marketing/DistributeBenefitsApi';
import TargetEventsModal from '../components/targetEventsModal';
const TabsOptions = [
    {
        key: '',
        label: '全部',
    },
    {
        key: '0',
        label: '草稿',
    },
    {
        key: '1',
        label: '未开始',
    },
    {
        key: '2',
        label: '进行中',
    },
    {
        key: '3',
        label: '已结束',
    },
];
const { RangePicker } = DatePicker;
const ListPage: React.FC = () => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string | number>(TabsOptions[0].key);
    const [visible, setVisible] = useState(false);
    const [id, setId] = useState('');

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getActEquityPage({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (id, status) => {
            return updateActEquityStatus({ actId: id, actState: status });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    const refreshList = () => {
        searchList(pagination, getParams());
    };
    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        const { date = [] } = params;
        params.beginDate = date[0] ? moment(date[0]).format('YYYY-MM-DD HH:mm:ss') : undefined;
        params.endDate = date[1] ? moment(date[1]).format('YYYY-MM-DD HH:mm:ss') : undefined;
        delete params.date;
        if (currentTab && currentTab !== TabsOptions[0].key) {
            params.actState = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const confirmChangeStatus = (id: number, status: number) => {
        changeStatusRequest(id, status);
    };

    const columns: ColumnsType = [
        {
            title: '活动ID',
            width: 120,
            dataIndex: 'actId',
            fixed: 'left',
        },
        {
            title: '活动名称',
            width: 200,
            dataIndex: 'actName',
            fixed: 'left',
        },

        {
            title: '有效期',
            width: 120,
            dataIndex: '',
            render: (_, record) => {
                return `${record?.effTime}- ${record?.expTime}`;
            },
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'creTime',
        },
        {
            title: '更新时间',
            width: 120,
            dataIndex: 'dataOperTime',
        },
        {
            title: '创建来源',
            width: 120,
            dataIndex: 'createSourceName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creEmp',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'actStateName',
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'actId',
            sorter: false,
            fixed: 'right',
            render: (id, record, index) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                history.push({
                                    pathname: `/marketing/distribute-benefits/detail`,
                                    query: { id },
                                });
                            }}
                        >
                            详情
                        </Typography.Link>
                        <Link
                            to={`/marketing/distribute-benefits/edit?id=${id}&&actState=${record?.actState}`}
                        >
                            编辑
                        </Link>
                        {(record?.actState === '1' || record?.actState === '2') && (
                            <Typography.Link onClick={() => confirmChangeStatus(id, '3')}>
                                停止
                            </Typography.Link>
                        )}
                        {record?.actState === '0' && (
                            <Typography.Link onClick={() => confirmChangeStatus(id, '1')}>
                                上线
                            </Typography.Link>
                        )}
                        {(record?.actState === '1' || record?.actState === '2') && (
                            <Typography.Link
                                onClick={() => {
                                    setVisible(true);
                                    setId(id);
                                }}
                            >
                                生成目标事件
                            </Typography.Link>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            {visible && (
                <TargetEventsModal
                    visible={visible}
                    id={id}
                    onClose={() => {
                        setVisible(false);
                        setId('');
                    }}
                />
            )}
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                        <Col span={8}>
                            <Form.Item label="活动名称" name="actName">
                                <Input maxLength={16} allowClear placeholder="请输入活动名称" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="活动ID" name="actId">
                                <Input maxLength={16} allowClear placeholder="请输入活动ID" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="有效期" name="date">
                                <RangePicker format="YYYY-MM-DD" />
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Button
                            type="primary"
                            onClick={() => {
                                history.push({
                                    pathname: `/marketing/distribute-benefits/edit`,
                                });
                            }}
                        >
                            创建活动
                        </Button>
                    </Space>
                </div>
                <Tabs defaultActiveKey={TabsOptions[0].key as string} onChange={changeTabType}>
                    {TabsOptions.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="actId"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default ListPage;
