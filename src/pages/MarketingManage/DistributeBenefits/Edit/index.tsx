import { LeftOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import {
    Space,
    Typography,
    Form,
    Input,
    Select,
    DatePicker,
    Button,
    message,
    InputNumber,
    Radio,
} from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useLocation, history, connect } from 'umi';
import AddEquityFormItem from '../components/addEquityFormItem';
import { getActEquityDetail, saveActEquity } from '@/services/Marketing/DistributeBenefitsApi';

const EditPage = (props) => {
    const { query }: any = useLocation();
    const { id, actState } = query;
    const [form] = Form.useForm();
    const { run, loading } = useRequest(
        (id) => {
            return getActEquityDetail({ actId: id });
        },
        {
            manual: true,
            onSuccess: (res) => {
                const { data = {} } = res;
                const { effTime, expTime } = data;
                data.effTime = effTime ? moment(effTime) : undefined;
                data.expTime = effTime ? moment(expTime) : undefined;
                form.setFieldsValue(data);
            },
        },
    );
    const { run: save, loading: saveLoading } = useRequest(
        (values) => {
            return saveActEquity(values);
        },
        {
            manual: true,
            onSuccess: (res) => {
                message.success('保存成功');
                history.goBack();
            },
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    const onFinish = (values: any): void => {
        const { effTime, expTime } = values;
        values.effTime = effTime ? moment(effTime).format('YYYY-MM-DD HH:mm:ss') : undefined;
        values.expTime = expTime ? moment(expTime).format('YYYY-MM-DD HH:mm:ss') : undefined;
        save(values);
    };
    return (
        <PageHeaderWrapper
            loading={loading}
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>{id ? '编辑' : '新建'}发放权益</Typography.Title>
                </Space>
            }
        >
            <ProCard>
                <Form
                    form={form}
                    onFinish={onFinish}
                    wrapperCol={{ span: 8 }}
                    labelCol={{ flex: '0 0 160px' }}
                    labelAlign="right"
                >
                    <Typography.Title level={5}>基础信息</Typography.Title>
                    <Form.Item name="actId" hidden />
                    <Form.Item name="actType" hidden initialValue={'61'} />
                    <Form.Item name="actState" hidden initialValue={'0'} />
                    <Form.Item
                        label="活动名称"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="actName"
                    >
                        <Input allowClear placeholder="请输入活动名称" />
                    </Form.Item>
                    <Form.Item label="有效期" required>
                        <Space>
                            <Form.Item
                                name="effTime"
                                noStyle
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择开始时间',
                                    },
                                ]}
                            >
                                <DatePicker placeholder="请选择开始时间" showTime />
                            </Form.Item>
                            <span>-</span>
                            <Form.Item
                                name="expTime"
                                noStyle
                                dependencies={['effTime']}
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择结束时间',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            const effTime = getFieldValue('effTime');
                                            if (value) {
                                                if (effTime) {
                                                    if (moment(effTime) < moment(value)) {
                                                        if (moment(value) > moment()) {
                                                            return Promise.resolve();
                                                        } else {
                                                            return Promise.reject(
                                                                new Error('结束时间要大于当前时间'),
                                                            );
                                                        }
                                                    } else {
                                                        return Promise.reject(
                                                            '结束时间要大于开始时间',
                                                        );
                                                    }
                                                } else {
                                                    return Promise.reject('请选择开始时间');
                                                }
                                            }
                                        },
                                    }),
                                ]}
                            >
                                <DatePicker showTime placeholder="请选择结束时间" />
                            </Form.Item>
                        </Space>
                    </Form.Item>

                    <Form.Item
                        label="活动说明"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="actMarks"
                    >
                        <Input.TextArea allowClear placeholder="请输入活动说明" />
                    </Form.Item>
                    <Typography.Title level={5}>奖品配置</Typography.Title>
                    <Form.Item
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="prizeList"
                    >
                        <AddEquityFormItem
                            disabled={actState && (actState === '2' || actState === '3')}
                        />
                    </Form.Item>
                    <Typography.Title level={5}>规则配置</Typography.Title>
                    <Form.Item
                        label="活动库存"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="actStocks"
                    >
                        <InputNumber
                            disabled={actState && (actState === '2' || actState === '3')}
                            placeholder="请输入活动库存"
                            style={{ width: 200 }}
                            min={1}
                            max={9999999}
                        />
                    </Form.Item>

                    <Space style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <Button onClick={() => history.goBack()}>关闭</Button>
                        <Button type="primary" htmlType="submit" loading={saveLoading}>
                            提交
                        </Button>
                    </Space>
                </Form>
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default EditPage;
