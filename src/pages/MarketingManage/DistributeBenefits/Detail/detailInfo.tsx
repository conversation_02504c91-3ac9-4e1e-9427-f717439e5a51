import { Descriptions, Spin, Table } from 'antd';
import { getActEquityDetail } from '@/services/Marketing/DistributeBenefitsApi';
import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Link } from 'umi';

const DetailInfo = (props) => {
    const { id } = props;
    const [detailData, setDetailData] = useState<any>({});
    const { run, loading } = useRequest(
        (id) => {
            return getActEquityDetail({ actId: id });
        },
        {
            manual: true,
            onSuccess: (res) => {
                const { data = {} } = res;
                setDetailData(data);
            },
        },
    );
    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);
    const columns = [
        {
            title: '权益名称',
            width: 200,
            dataIndex: 'equityName',
            fixed: 'left',
            render: (text, record) => {
                return (
                    <Link to={`/marketing/order-discounts/detail?id=${record?.equityId}`}>
                        {`${record?.equityTypeName} | ${record?.equityId} | ${record?.equityName}`}^
                    </Link>
                );
            },
        },
        {
            title: '优惠方式',
            width: 120,
            dataIndex: 'discountTypeName',
        },
        {
            title: '优惠力度',
            width: 120,
            dataIndex: 'discountValue',
        },

        {
            title: '创建时间',
            width: 120,
            dataIndex: 'created',
        },
        {
            title: '出资方',
            width: 120,
            dataIndex: 'contributePartyName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creator',
        },
    ];
    return (
        <Spin spinning={loading}>
            <Descriptions title="基础信息" column={1}>
                <Descriptions.Item label="活动名称">{detailData?.actName}</Descriptions.Item>
                <Descriptions.Item label="有效期">{`${detailData?.effTime} - ${detailData?.expTime}`}</Descriptions.Item>
                <Descriptions.Item label="活动说明">{detailData?.actMarks}</Descriptions.Item>
            </Descriptions>
            <Descriptions title="奖品配置" column={1}></Descriptions>
            <Table
                style={{ marginBottom: 20, minWidth: 1000 }}
                columns={columns}
                dataSource={detailData?.prizeList || []}
                pagination={false}
            />
            <Descriptions title="规则配置" column={1}>
                <Descriptions.Item label="活动库存">
                    {detailData?.actStocks}
                    {`(剩余库存：${detailData?.remainStock})`}
                </Descriptions.Item>
            </Descriptions>
        </Spin>
    );
};

export default DetailInfo;
