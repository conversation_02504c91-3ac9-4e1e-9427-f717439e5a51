import { usePagination } from 'ahooks';
import { Col, Form, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, history } from 'umi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { getTargetEventPage } from '@/services/Marketing/DistributeBenefitsApi';

const AssociateTargetEventsTable: React.FC = (props) => {
    const { id } = props;
    const [form] = Form.useForm();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getTargetEventPage({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, []);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };

        params.actId = id;
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const columns: ColumnsType = [
        {
            title: '目标事件ID',
            width: 120,
            dataIndex: 'targetId',
            fixed: 'left',
            render: (text, record) => {
                return (
                    <Link to={`/eventcenter/eventmanage/target/list/update/${text}/look`}>
                        {text}
                    </Link>
                );
            },
        },

        {
            title: '目标事件名称',
            width: 120,
            dataIndex: 'targetName',
            render: (text, record) => {
                return (
                    <Link
                        to={`/eventcenter/eventmanage/target/list/update/${record?.targetId}/look`}
                    >
                        {text}
                    </Link>
                );
            },
        },
        {
            title: '目标事件用途',
            width: 120,
            dataIndex: 'targetTypeName',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createdTime',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'targetStateName',
        },
    ];
    return (
        <>
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <Form.Item label="目标事件名称" name="targetName">
                            <Input allowClear placeholder="请输入目标事件名称" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="目标事件ID" name="targetId">
                            <Input allowClear placeholder="请输入目标事件ID" />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="id"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </>
    );
};

export default AssociateTargetEventsTable;
