import { usePagination } from 'ahooks';
import { Col, Form, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, history } from 'umi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { getActEquityAssemblyPage } from '@/services/Marketing/DistributeBenefitsApi';

const AssociatedComponentsTable: React.FC = (props) => {
    const { id } = props;
    const [form] = Form.useForm();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getActEquityAssemblyPage({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, []);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };

        params.actId = id;
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const columns: ColumnsType = [
        {
            title: '组件模块ID',
            width: 120,
            dataIndex: 'moduleId',
            fixed: 'left',
            render: (text, record) => {
                return <Link to={`/marketing/component/module/detail/${text}`}>{text}</Link>;
            },
        },

        {
            title: '组件模块名称',
            width: 120,
            dataIndex: 'moduleName',
            render: (text, record) => {
                return (
                    <Link to={`/marketing/component/module/detail/${record?.moduleId}`}>
                        {text}
                    </Link>
                );
            },
        },
        {
            title: '组件模块类型',
            width: 120,
            dataIndex: 'relaActTypeName',
        },
        {
            title: '组件模块数量',
            width: 120,
            dataIndex: 'relaNum',
        },
        {
            title: '关联组件',
            width: 120,
            dataIndex: 'assembly',
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createdTime',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'moduleStatusName',
        },
    ];
    return (
        <>
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <Form.Item label="组件模块名称" name="moduleName">
                            <Input allowClear placeholder="请输入组件模块名称" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="组件模块ID" name="moduleId">
                            <Input allowClear placeholder="请输入组件模块ID" />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="id"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </>
    );
};

export default AssociatedComponentsTable;
