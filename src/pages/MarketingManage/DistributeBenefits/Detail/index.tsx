import { Space, Tabs, Typography } from 'antd';

import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { LeftOutlined } from '@ant-design/icons';
import { history, useLocation } from 'umi';
import ProCard from '@ant-design/pro-card';
import DetailInfo from './detailInfo';
import ClaimRecordTable from './claimRecordTable';
import AssociateTargetEventsTable from './associateTargetEventsTable';
import AssociatedComponentsTable from './associatedComponentsTable';

const DetailPage = () => {
    const { query = {} } = useLocation();
    const { id } = query;

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title">
                    <Space>
                        <LeftOutlined onClick={() => history.goBack()} />
                        <Typography.Title level={4} style={{ marginBottom: 0 }}>
                            查看发放权益活动
                        </Typography.Title>
                    </Space>
                </div>
            }
        >
            <ProCard>
                <Tabs>
                    <Tabs.TabPane tab="活动详情" key="1">
                        <DetailInfo id={id} />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="领取记录" key="2">
                        <ClaimRecordTable id={id} />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="关联列表" key="3">
                        <Tabs>
                            <Tabs.TabPane tab="关联目标事件" key="1">
                                <AssociateTargetEventsTable id={id} />
                            </Tabs.TabPane>
                            <Tabs.TabPane tab="关联组件" key="2">
                                <AssociatedComponentsTable id={id} />
                            </Tabs.TabPane>
                        </Tabs>
                    </Tabs.TabPane>
                </Tabs>
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default DetailPage;
