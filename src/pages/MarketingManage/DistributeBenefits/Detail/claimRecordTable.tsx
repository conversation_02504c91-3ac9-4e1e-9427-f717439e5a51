import { usePagination, useRequest } from 'ahooks';
import { Col, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link } from 'umi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import {
    exportReceiveRecord,
    getReceiveRecordPage,
} from '@/services/Marketing/DistributeBenefitsApi';

const ClaimRecordTable: React.FC = (props) => {
    const { id } = props;
    const [form] = Form.useForm();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getReceiveRecordPage({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, []);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };

        params.actId = id;
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const columns: ColumnsType = [
        {
            title: '序号 ',
            width: 60,
            fixed: 'left',
            render(text, record, index) {
                return <span title={index + 1}>{index + 1}</span>;
            },
        },
        {
            title: '领取时间',
            width: 200,
            dataIndex: 'getTime',
            fixed: 'left',
        },
        {
            title: '领取人',
            width: 120,
            dataIndex: 'mobile',
            fixed: 'left',
            render: (text, record) => {
                return <Link to={`/userCenter/userManage/detail/view?mobile=${text}`}>{text}</Link>;
            },
        },

        {
            title: '奖品名称',
            width: 120,
            dataIndex: 'equityName',
            render: (text, record) => {
                return (
                    <Link to={`/marketing/order-discounts/detail?id=${record?.equityId}`}>
                        {text}
                    </Link>
                );
            },
        },
        {
            title: '奖品类型',
            width: 120,
            dataIndex: 'equityPrizeType',
        },
        {
            title: '奖品总额',
            width: 120,
            dataIndex: 'discountValue',
        },
        {
            title: '支付金额',
            width: 120,
            dataIndex: 'payAmt',
        },
        {
            title: '已优惠金额',
            width: 120,
            dataIndex: 'discountAmt',
        },
    ];
    const { run: exportApi, loading: exportLoading } = useRequest(
        (params) => {
            return exportReceiveRecord(params);
        },
        {
            manual: true,
            onSuccess: () => {
                message.success('导出成功');
            },
        },
    );
    const onExportForm = () => {
        exportApi(getParams());
    };
    return (
        <>
            <Form form={form} onFinish={onFinish}>
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <Form.Item label="领取人" name="mobile">
                            <Input maxLength={16} allowClear placeholder="请输入领取人手机号" />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="id"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </>
    );
};

export default ClaimRecordTable;
