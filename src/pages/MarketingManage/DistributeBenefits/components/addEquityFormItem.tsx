import { Button, Modal, Space, Table, Typography } from 'antd';
import { useState } from 'react';
import AddEquityModal from './addEquityModal';
import { Link } from 'umi';
import { isEmpty } from 'lodash';

const AddEquityFormItem = (props: { value: any; onChange: any }) => {
    const { value = [], onChange, disabled } = props;
    const [visible, setVisible] = useState(false);

    const onDelete = (id) => {
        const newData = value.filter((item) => item?.equityId !== id);
        onChange(newData);
    };
    const columns = [
        {
            title: '权益名称',
            width: 200,
            dataIndex: 'equityName',
            fixed: 'left',
            render: (text, record) => {
                return (
                    <Link to={`/marketing/order-discounts/detail?id=${record?.equityId}`}>
                        {`${record?.equityTypeName} | ${record?.equityId} | ${record?.equityName}`}^
                    </Link>
                );
            },
        },
        {
            title: '优惠方式',
            width: 120,
            dataIndex: 'discountTypeName',
        },
        {
            title: '优惠力度',
            width: 120,
            dataIndex: 'discountValue',
        },

        {
            title: '创建时间',
            width: 120,
            dataIndex: 'created',
        },
        {
            title: '出资方',
            width: 120,
            dataIndex: 'contributePartyName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creator',
        },
        {
            title: '操作',
            width: 120,
            dataIndex: 'equityId',
            render: (text) => {
                return (
                    <Space>
                        <Link to={`/marketing/order-discounts/detail?id=${text}`}>详情</Link>

                        {!disabled && (
                            <Typography.Link onClick={() => onDelete(text)}>删除</Typography.Link>
                        )}
                    </Space>
                );
            },
        },
    ];
    return (
        <>
            <Button type="primary" onClick={() => setVisible(true)} disabled={disabled}>
                添加权益
            </Button>
            {value && !isEmpty(value) && (
                <Table
                    style={{ marginTop: 20, minWidth: 1200 }}
                    columns={columns}
                    dataSource={value}
                    pagination={false}
                />
            )}
            {visible && (
                <AddEquityModal
                    visible={visible}
                    onClose={() => setVisible(false)}
                    selectValue={value}
                    onSelect={(values: any) => onChange(values)}
                />
            )}
        </>
    );
};
export default AddEquityFormItem;
