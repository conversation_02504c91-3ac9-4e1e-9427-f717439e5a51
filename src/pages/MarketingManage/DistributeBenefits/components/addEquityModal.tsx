import { Alert, Modal, Tabs, Form, Input, Col } from 'antd';
import { usePagination, useRequest } from 'ahooks';
import { getOrderDiscountList } from '@/services/Marketing/OrderDiscountsApi';
import { useEffect, useState } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import type { ColumnsType } from 'antd/lib/table';
import TablePro from '@/components/TablePro';

const AddEquityModal = (props) => {
    const { visible, onClose, selectValue = [], onSelect } = props;
    const [selectedRow, setSelectedRow] = useState(selectValue);
    const [form] = Form.useForm();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getOrderDiscountList({
                ...params,
                pageSize,
                pageIndex: current,
                equityStatus: '01',
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );
    useEffect(() => {
        searchList(pagination, getParams());
    }, []);
    const onFinish = () => {
        searchList(pagination, getParams());
    };
    const onOk = () => {
        onSelect(selectedRow);
        onClose();
    };
    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };
    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        return params;
    };
    const columns: ColumnsType = [
        {
            title: '权益ID',
            width: 120,
            dataIndex: 'equityId',
            fixed: 'left',
        },
        {
            title: '权益名称',
            width: 200,
            dataIndex: 'equityName',
            fixed: 'left',
        },
        {
            title: '优惠方式',
            width: 120,
            dataIndex: 'discountTypeName',
        },
        {
            title: '优惠力度',
            width: 120,
            dataIndex: 'discountValue',
        },

        {
            title: '创建时间',
            width: 120,
            dataIndex: 'created',
        },
        {
            title: '出资方',
            width: 120,
            dataIndex: 'contributePartyName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'creator',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'equityStatusName',
        },
    ];
    const rowSelection = {
        selectedRowKeys: selectedRow.map((item: any) => item.equityId),
        onChange: (selectedRowKeys, selectedRows) => {
            // 筛选出非当前页的勾选项，不予处理
            const otherCpns = selectedRow.filter(
                (x) => listData?.list.filter((now) => now.equityId === x.equityId).length == 0,
            );
            setSelectedRow([...otherCpns, ...selectedRows]);
        },
        getCheckboxProps: (record) => ({
            disabled: selectValue.filter((item) => item.equityId === record.equityId).length > 0,
            name: record.equityId,
        }),
    };
    return (
        <Modal
            title="添加权益"
            visible={visible}
            onCancel={onClose}
            width={1200}
            onOk={() => onOk()}
        >
            <Tabs>
                <Tabs.TabPane key="1" tab="订单优惠">
                    <Alert
                        style={{ marginBottom: 20 }}
                        description={
                            <>
                                <div>
                                    「订单优惠」仅在用户有未完成订单时有效，若无未完成订单，优惠将不可用，且优惠权益将被核销
                                </div>
                                <div>
                                    &ensp;用户享受的所有「订单优惠」按「订单优惠」权益的获取时间，按先后顺序依次叠加计算优惠金额
                                </div>
                            </>
                        }
                        type="warning"
                    />
                    <Form form={form} onFinish={onFinish}>
                        <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                            <Col span={8}>
                                <Form.Item label="权益名称" name="equityName">
                                    <Input maxLength={16} allowClear placeholder="请输入权益名称" />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="权益ID" name="equityId">
                                    <Input maxLength={16} allowClear placeholder="请输入权益ID" />
                                </Form.Item>
                            </Col>
                        </SearchOptionsBar>
                    </Form>
                    <TablePro
                        loading={listLoading}
                        scroll={{ x: 'max-content' }}
                        rowKey="equityId"
                        rowSelection={{
                            type: 'checkbox',
                            ...rowSelection,
                        }}
                        dataSource={listData?.list}
                        columns={columns}
                        onChange={(pages: any) => {
                            pagination.onChange(pages?.current, pages?.pageSize);
                        }}
                        pagination={{
                            current: pagination.current,
                            total: pagination.total,
                            pageSize: pagination.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: number) => `共 ${total || 0} 条`,
                        }}
                    />
                </Tabs.TabPane>
            </Tabs>
        </Modal>
    );
};

export default AddEquityModal;
