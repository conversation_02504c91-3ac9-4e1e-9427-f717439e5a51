import { setCreateTargetTask } from '@/services/Marketing/DistributeBenefitsApi';
import { useRequest } from 'ahooks';
import { Form, Input, Modal, message } from 'antd';
const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};
const TargetEventsModal = (props) => {
    const { visible, onClose, id } = props;
    const [form] = Form.useForm();
    const { run, loading } = useRequest(
        (params) => {
            return setCreateTargetTask(params);
        },
        {
            manual: true,
            onSuccess: () => {
                message.success('保存成功');
                onClose();
            },
        },
    );
    const onFinish = (values) => {
        run(values);
    };
    const onOk = () => {
        form.submit();
    };
    return (
        <Modal
            title="生成目标事件"
            visible={visible}
            onCancel={onClose}
            width={600}
            confirmLoading={loading}
            onOk={() => onOk()}
        >
            <Form form={form} scrollToFirstError onFinish={onFinish} {...formItemLayout}>
                <Form.Item label="活动类型" name="rewardType" initialValue={'61'} hidden />
                <Form.Item label="活动类型" name="targetState" initialValue={'01'} hidden />
                <Form.Item label="目标事件名称" name="targetName" rules={[{ required: true }]}>
                    <Input placeholder="请输入目标事件名称" autoComplete="off" />
                </Form.Item>
                <Form.Item
                    label="目标事件用途"
                    name="targetType"
                    rules={[{ required: true }]}
                    initialValue={'01'}
                >
                    触发活动发奖
                </Form.Item>
                <Form.Item
                    label="发送目标类型"
                    name="targetSendType"
                    rules={[{ required: true }]}
                    initialValue={'MQ'}
                >
                    MQ
                </Form.Item>
                <Form.Item
                    label="配置Topic"
                    name="targetUrl"
                    rules={[{ required: true }]}
                    initialValue={'equityActReceive'}
                >
                    equityActReceive
                </Form.Item>
                <Form.Item
                    label="发送目标标识"
                    name="rewardId"
                    rules={[{ required: true }]}
                    initialValue={id}
                >
                    发放权益 | {id}
                </Form.Item>
                <Form.Item label="目标事件描述" name="targetRemarks" rules={[{ required: true }]}>
                    <Input.TextArea placeholder="请输入目标事件描述" />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default TargetEventsModal;
