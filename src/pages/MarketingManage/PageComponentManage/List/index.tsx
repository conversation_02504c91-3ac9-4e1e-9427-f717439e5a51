// 页面组件管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Modal,
    Popconfirm,
    Row,
    Select,
    Space,
    Switch,
    Tabs,
    Typography,
    message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, connect, history, request } from 'umi';

import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import {
    ComponentAssemblyStatusEnum,
    ComponentAssemblyStatusTabOptions,
    ComponentAssemblyTypeOptions,
    AssemblyTypeOptions,
    AssemblyTypeEnum,
} from '@/constants/componentManage';
import {
    deleteAssembly,
    getAssemblyPageList,
    updateAssemblyStatus,
} from '@/services/Marketing/ComponentManageApi';

const ListPage: React.FC<{
    dispatch?: any;
    global?: { codeInfo: Record<string, any> };
}> = ({ dispatch, global }) => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string | number>(
        ComponentAssemblyStatusTabOptions[0].key,
    );
    const [showModal, setShowModal] = useState<boolean>(false);
    const [addForm] = Form.useForm();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getAssemblyPageList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: deleteRequest, loading: deleteLoading } = useRequest(
        (id) => {
            return deleteAssembly(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('删除成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
            onError: () => {
                message.error('删除失败');
            },
        },
    );

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (id) => {
            return updateAssemblyStatus(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize });
    }, []);

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        if (currentTab && currentTab !== ComponentAssemblyStatusTabOptions[0].key) {
            params.assemblyStatus = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    const refreshList = () => {
        searchList(pagination, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const confirmDelete = (id: number) => {
        deleteRequest(id);
    };

    const confirmChangeStatus = (id: number, status: number) => {
        changeStatusRequest(id);
    };

    const showAddModal = () => {
        setShowModal(true);
    };

    const columns: ColumnsType<API.AssemblyQueryVo> = [
        {
            title: '组件ID',
            width: 120,
            dataIndex: 'assemblyId',
            fixed: 'left',
        },
        {
            title: '页面组件名称',
            width: 160,
            dataIndex: 'assemblyName',
            fixed: 'left',
        },

        {
            title: '页面组件类型',
            width: 160,
            dataIndex: 'customParamName',
        },
        {
            title: '页面组件模块类型',
            width: 160,
            dataIndex: 'assemblyTypeName',
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTime',
        },
        {
            title: '更新时间',
            width: 200,
            dataIndex: 'updatedTime',
        },
        {
            title: '创建来源',
            width: 120,
            dataIndex: 'createSourceName',
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
        },
        {
            title: '所属平台',
            width: 120,
            dataIndex: 'cooperationPlatformName',
        },
        {
            title: '使用方',
            width: 120,
            dataIndex: 'userTypeName',
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'assemblyStatus',
            render(value: number, record: API.AssemblyQueryVo) {
                return (
                    <Switch
                        checked={value === ComponentAssemblyStatusEnum.ENABLED}
                        checkedChildren="启动"
                        unCheckedChildren="关闭"
                        loading={changeStatusLoading}
                        onChange={() => {
                            confirmChangeStatus(
                                record?.assemblyId as number,
                                value === ComponentAssemblyStatusEnum.ENABLED
                                    ? ComponentAssemblyStatusEnum.DISABLED
                                    : ComponentAssemblyStatusEnum.ENABLED,
                            );
                        }}
                    />
                );
            },
        },
        {
            title: '操作',
            width: 200,
            dataIndex: 'assemblyId',
            sorter: false,
            fixed: 'right',
            render: (id, record: API.AssemblyQueryVo) => {
                let editUrl = `/marketing/component/pagecomponent/edit/${id}`;

                if (record.customParam == AssemblyTypeEnum.VIP_GUIDE) {
                    editUrl = `/marketing/component/pagecomponent/editVipGuide/${id}`;
                }
                return (
                    <Space>
                        <span
                            className={styles['table-btn']}
                            onClick={() =>
                                window.open(
                                    'https://digital.bangdao-tech.com/smartube-app/hudong/PJ1626859726002/put/standNew',
                                )
                            }
                        >
                            投放
                        </span>
                        <Link to={editUrl}>编辑</Link>
                        {record?.assemblyStatus === ComponentAssemblyStatusEnum.DISABLED && (
                            <Popconfirm
                                title="是否确认删除组件?"
                                onConfirm={() => confirmDelete(id)}
                                okButtonProps={{ loading: deleteLoading }}
                            >
                                <Typography.Link type="danger">删除</Typography.Link>
                            </Popconfirm>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 1}>
                        <Col span={6}>
                            <Form.Item label="组件名称" name="assemblyName">
                                <Input maxLength={16} allowClear placeholder="请输入组件名称" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label="组件ID" name="assemblyId">
                                <Input maxLength={32} allowClear placeholder="请输入组件ID" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label="组件类型" name="customParam">
                                <Select
                                    options={AssemblyTypeOptions}
                                    // fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    allowClear
                                    placeholder="请选择组件类型"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label="组件模板类型" name="assemblyType">
                                <Select
                                    options={ComponentAssemblyTypeOptions}
                                    // fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                    allowClear
                                    placeholder="请选择组件模板类型"
                                />
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Button
                            type="primary"
                            icon="+"
                            onClick={() => {
                                // history.push(`/marketing/component/pagecomponent/add`);
                                showAddModal();
                            }}
                        >
                            添加页面组件
                        </Button>
                    </Space>
                </div>
                <Tabs
                    defaultActiveKey={ComponentAssemblyStatusTabOptions[0].key as string}
                    onChange={changeTabType}
                >
                    {ComponentAssemblyStatusTabOptions.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="assemblyId"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
            <Modal
                title="新增组件"
                visible={showModal}
                onCancel={() => {
                    addForm.resetFields();
                    setShowModal(false);
                }}
                destroyOnClose
                footer={false}
                width={420}
            >
                <Form
                    form={addForm}
                    onFinish={(data) => {
                        if (data.type == AssemblyTypeEnum.CARD) {
                            history.push(`/marketing/component/pagecomponent/add`);
                        } else if (data.type == AssemblyTypeEnum.VIP_GUIDE) {
                            history.push(`/marketing/component/pagecomponent/addVipGuide`);
                        }
                    }}
                    labelCol={{
                        flex: '0 0 110px',
                    }}
                >
                    <Form.Item
                        label="添加组件类型"
                        name="type"
                        required
                        rules={[{ required: true, message: '请选择组件类型' }]}
                        initialValue={AssemblyTypeEnum.CARD}
                    >
                        <Select
                            options={AssemblyTypeOptions}
                            allowClear
                            showArrow
                            placeholder="请选择组件类型"
                        />
                    </Form.Item>
                    <Form.Item>
                        <Row style={{ width: '100%', marginTop: '24px' }} justify="center">
                            <Button htmlType="submit" type="primary">
                                添加
                            </Button>
                        </Row>
                    </Form.Item>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global }: any) => ({ global }))(ListPage);
