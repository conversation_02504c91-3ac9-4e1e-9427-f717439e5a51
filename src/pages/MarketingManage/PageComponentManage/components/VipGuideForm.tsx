import {
    Button,
    Form,
    Input,
    Radio,
    Row,
    Space,
    Typography,
    Checkbox,
    Collapse,
    Card,
    message,
    Drawer,
    Tabs,
} from 'antd';
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Link, history, useModel } from 'umi';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Radio';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

import SelectColor from '@/components/SelectColor/Item';

import CollapseFormListItem from '@/components/CollapseFormListItem/index';
import type { MATERIAL_ITEM_VO } from './MaterialItem';
import MaterialItem, { checkItem } from './MaterialItem';
import type { MATERIAL_LIST_VO } from './MaterialList';
import MaterialList from './MaterialList';

import {
    ComponentAssemblyStatusEnum,
    ComponentAssemblyStatusTabOptions,
    ComponentAssemblyTypeOptions,
    AssemblyTypeOptions,
    AssemblyTypeEnum,
    MATERIAL_BIZ_TYPES,
    MATERIAL_BIZ_TYPE_NAMES,
} from '@/constants/componentManage';
import { addVipAssembly } from '@/services/Marketing/ComponentManageApi';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { useRequest } from 'ahooks';

import LayoutPreview from '@/components/LayoutPreview';

interface Props {
    //表单模式，新增或编辑
    method: 'ADD' | 'EDIT';
    //组件id
    id?: string;
    //初始化数据，编辑或者有复制功能使用
    initialValue?: any;
    callback?: () => void;
}

const MATERIAL_TYPES = {
    DEFAULT: 'defaultVos', //兜底素材
    HIT: 'policyVos', //命中策略素材
};

const VIP_USER_TYPE = {
    NORMAL: '21', //普通用户
    ON_THE_WAY: '22', //在途会员
    EXPIRED: '23', //过期会员
};

const VIP_USER_TYPE_NAMES = {
    [VIP_USER_TYPE.NORMAL]: '用户类型：非会员',
    [VIP_USER_TYPE.ON_THE_WAY]: '用户类型：在途会员',
    [VIP_USER_TYPE.EXPIRED]: '用户类型：过期会员',
};

const MATERIAL_INITIAL: MATERIAL_LIST_VO[] = [
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.NORMAL],
        contentUserType: VIP_USER_TYPE.NORMAL,
        contentList: [
            {
                title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.NORMAL],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.ON_THE_WAY],
        contentUserType: VIP_USER_TYPE.ON_THE_WAY,
        contentList: [
            {
                title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.ON_THE_WAY],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.EXPIRED],
        contentUserType: VIP_USER_TYPE.EXPIRED,
        contentList: [
            {
                title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.EXPIRED],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
];

const MATERIAL_HIT_INITIAL: MATERIAL_LIST_VO[] = [
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.NORMAL],
        contentUserType: VIP_USER_TYPE.NORMAL,
        contentList: [
            {
                bizType: MATERIAL_BIZ_TYPES.OPEN,
                title: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.OPEN],
                contentDefaultColor: '#0C0E0C',
            },
            {
                bizType: MATERIAL_BIZ_TYPES.TRIAL,
                title: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.TRIAL],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.ON_THE_WAY],
        contentUserType: VIP_USER_TYPE.ON_THE_WAY,

        contentList: [
            {
                bizType: MATERIAL_BIZ_TYPES.RENEW,
                title: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.RENEW],
                contentDefaultColor: '#0C0E0C',
            },
            {
                bizType: MATERIAL_BIZ_TYPES.UPGRADE,
                title: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.UPGRADE],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
    {
        title: VIP_USER_TYPE_NAMES[VIP_USER_TYPE.EXPIRED],
        contentUserType: VIP_USER_TYPE.EXPIRED,
        contentList: [
            {
                bizType: MATERIAL_BIZ_TYPES.RECOMMEND,
                title: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.RECOMMEND],
                contentDefaultColor: '#0C0E0C',
            },
        ],
    },
];

const VipGuideForm = (props: Props) => {
    const [form] = Form.useForm();

    const { method = 'ADD', id, initialValue, callback } = props;

    const [curMaterialType, updateCurMaterialType] = useState(MATERIAL_TYPES.DEFAULT);

    const { codeInfo, initCode } = useModel('codeState');

    useEffect(() => {
        if (isEmpty(codeInfo?.contentCfgType)) {
            initCode('contentCfgType');
        }
    }, []);

    useEffect(() => {
        if (initialValue) {
            const options = {
                ...initialValue,
            };
            options.defaultVos = initFormmatVos(options.defaultVos);
            options.policyVos = initFormmatVos(options.policyVos);

            form.setFieldsValue(options);
        }
    }, [initialValue, codeInfo?.contentCfgType]);

    const formatContent = (content: string, type = 'def') => {
        const reg = /{[^{]*}/g;
        const matchs = content.match(reg);
        if (matchs instanceof Array) {
            for (const element of matchs) {
                const result = element.replace(/[{}]/g, '');
                const contentCfgTypeItem = codeInfo?.contentCfgType?.find(
                    (ele) => ele.codeName === result,
                );
                if (contentCfgTypeItem) {
                    if (type == 'preview') {
                        content = content.replace(element, `xxx`);
                    } else {
                        content = content.replace(element, `{${contentCfgTypeItem.codeValue}}`);
                    }
                }
            }
        }

        return content;
    };

    const reverseFormatContent = (content: string) => {
        const reg = /{[^{]*}/g;
        const matchs = content.match(reg);
        if (matchs instanceof Array) {
            for (const element of matchs) {
                const result = element.replace(/[{}]/g, '');
                const contentCfgTypeItem = codeInfo?.contentCfgType?.find(
                    (ele) => ele.codeValue === result,
                );
                if (contentCfgTypeItem) {
                    content = content.replace(element, `{${contentCfgTypeItem.codeName}}`);
                }
            }
        }

        return content;
    };

    const initFormmatVos = (list: MATERIAL_LIST_VO[], type = 'def') => {
        const newList = list;

        for (const item of newList) {
            const title = VIP_USER_TYPE_NAMES[item.contentUserType];

            item.title = title;
            if (item.contentList instanceof Array) {
                for (const contentItem of item.contentList) {
                    if (contentItem.bizType) {
                        contentItem.title = MATERIAL_BIZ_TYPE_NAMES[contentItem.bizType];
                    } else {
                        contentItem.title = title;
                    }
                    let formatEvent = reverseFormatContent;
                    if (type == 'submit' || type == 'preview') {
                        formatEvent = formatContent;
                    }
                    if (contentItem.mainContent) {
                        contentItem.mainContent = formatEvent(contentItem.mainContent, type);
                    }
                    if (contentItem.subContent) {
                        contentItem.subContent = formatEvent(contentItem.subContent, type);
                    }
                    if (contentItem.btnContent) {
                        contentItem.btnContent = formatEvent(contentItem.btnContent, type);
                    }
                }
            }
        }
        return newList;
    };

    const { loading: submitLoading, run: submitForm } = useRequest(
        async (values: any) => {
            try {
                const params = {
                    ...values,
                };
                params.defaultVos = initFormmatVos(params.defaultVos, 'submit');
                params.policyVos = initFormmatVos(params.policyVos, 'submit');

                await addVipAssembly(params);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                message.success('保存成功');
                callback && callback();
            },
        },
    );

    const policyDynamicsOptionInfo = useMemo(() => {
        return {
            [VIP_USER_TYPE.NORMAL]: {
                [MATERIAL_BIZ_TYPES.OPEN]: codeInfo?.contentCfgType?.filter((ele) =>
                    [
                        'monthlyCardPay',
                        'quarterlyCardPay',
                        'annualCardPay',
                        'continuousMonthlyPay',
                        'memberBenefitValue',
                        'memberUintPirce',
                    ].includes(ele.codeValue),
                ),
                [MATERIAL_BIZ_TYPES.TRIAL]: codeInfo?.contentCfgType?.filter((ele) =>
                    ['memberBenefitValue', 'memberUintPirce'].includes(ele.codeValue),
                ),
            },
            [VIP_USER_TYPE.ON_THE_WAY]: {
                [MATERIAL_BIZ_TYPES.RENEW]: codeInfo?.contentCfgType?.filter((ele) =>
                    ['memberAccumSaved', 'memberRemainingDays'].includes(ele.codeValue),
                ),
                [MATERIAL_BIZ_TYPES.UPGRADE]: codeInfo?.contentCfgType?.filter((ele) =>
                    [
                        'monthlyCardPay',
                        'quarterlyCardPay',
                        'annualCardPay',
                        'continuousMonthlyPay',
                        'memberBenefitValue',
                        'memberUintPirce',
                    ].includes(ele.codeValue),
                ),
            },
            [VIP_USER_TYPE.EXPIRED]: {
                [MATERIAL_BIZ_TYPES.RECOMMEND]: codeInfo?.contentCfgType?.filter((ele) =>
                    [
                        'memberAccumSaved',
                        'monthlyCardPay',
                        'quarterlyCardPay',
                        'annualCardPay',
                        'continuousMonthlyPay',
                        'memberBenefitValue',
                        'memberUintPirce',
                    ].includes(ele.codeValue),
                ),
            },
        };
    }, [codeInfo?.contentCfgType]);
    const defaultDynamicsOptionInfo = useMemo(() => {
        return {
            [VIP_USER_TYPE.NORMAL]: codeInfo?.contentCfgType?.filter((ele) =>
                [
                    'monthlyCardPay',
                    'quarterlyCardPay',
                    'annualCardPay',
                    'continuousMonthlyPay',
                    'memberBenefitValue',
                    'memberUintPirce',
                ].includes(ele.codeValue),
            ),
            [VIP_USER_TYPE.ON_THE_WAY]: codeInfo?.contentCfgType?.filter((ele) =>
                ['memberAccumSaved', 'memberRemainingDays'].includes(ele.codeValue),
            ),
            [VIP_USER_TYPE.EXPIRED]: codeInfo?.contentCfgType?.filter((ele) =>
                [
                    'memberAccumSaved',
                    'monthlyCardPay',
                    'quarterlyCardPay',
                    'annualCardPay',
                    'continuousMonthlyPay',
                    'memberBenefitValue',
                    'memberUintPirce',
                ].includes(ele.codeValue),
            ),
        };
    }, [codeInfo?.contentCfgType]);

    return (
        <>
            <Form
                form={form}
                onFinish={submitForm}
                wrapperCol={{ span: 16 }}
                labelCol={{ flex: '0 0 120px' }}
                labelAlign="right"
            >
                <Typography.Title level={4}>基础信息</Typography.Title>

                {method === 'EDIT' && (
                    <Form.Item
                        name="assemblyId"
                        label="组件ID"
                        required={method === 'EDIT'}
                        wrapperCol={{ span: 8 }}
                    >
                        <Input disabled />
                    </Form.Item>
                )}
                <Form.Item name="assemblyType" initialValue={'01'} hidden></Form.Item>
                <Form.Item
                    name="assemblyName"
                    label="组件名称"
                    required
                    rules={[{ required: true, whitespace: true, message: '请输入组件名称' }]}
                    wrapperCol={{ span: 8 }}
                >
                    <Input maxLength={16} showCount allowClear placeholder="请输入组件名称" />
                </Form.Item>
                <Form.Item
                    name="customParam"
                    label="组件类型"
                    required
                    initialValue={AssemblyTypeEnum.VIP_GUIDE}
                >
                    会员导购
                </Form.Item>
                <Form.Item
                    name="cooperationPlatform"
                    hidden
                    initialValue={COOPERATION_PLATFORM_TYPES.XDT}
                ></Form.Item>

                <Typography.Title level={4}>展示配置</Typography.Title>
                <Typography.Title level={5}>背景和图片配置</Typography.Title>

                <Form.Item
                    label="背景图片"
                    name="bgImg"
                    wrapperCol={{ span: 24 }}
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请配置图片',
                        },
                    ]}
                >
                    <UpLoadImgItem
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'community',
                            relaTable: 'e_wechat_community',
                        }}
                        sizeInfo={{
                            size: 200,
                            width: 1420,
                            height: 360,
                            suggest: true,
                        }}
                    ></UpLoadImgItem>
                </Form.Item>
                <Form.Item
                    label="icon图片"
                    name="littleIcon"
                    wrapperCol={{ span: 24 }}
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请配置图片',
                        },
                    ]}
                >
                    <UpLoadImgItem
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'community',
                            relaTable: 'e_wechat_community',
                        }}
                        sizeInfo={{
                            size: 100,
                            width: 100,
                            height: 100,
                            suggest: true,
                        }}
                    ></UpLoadImgItem>
                </Form.Item>
                <Form.Item
                    label="按钮图片"
                    name="btnImg"
                    wrapperCol={{ span: 24 }}
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请配置图片',
                        },
                    ]}
                >
                    <UpLoadImgItem
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'community',
                            relaTable: 'e_wechat_community',
                        }}
                        sizeInfo={{
                            size: 200,
                            width: 130,
                            height: 48,
                            suggest: true,
                        }}
                    ></UpLoadImgItem>
                </Form.Item>

                <Form.Item
                    label="按钮文案颜色"
                    name="btnColor"
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请选择颜色',
                        },
                    ]}
                    wrapperCol={{ span: 8 }}
                >
                    <SelectColor></SelectColor>
                </Form.Item>

                <Typography.Title level={5}>文案和按钮配置</Typography.Title>
                <p>
                    <Radio.Group
                        buttonStyle="solid"
                        value={curMaterialType}
                        onChange={(event) => {
                            const {
                                target: { value },
                            } = event;
                            updateCurMaterialType(value);
                        }}
                    >
                        <Radio.Button value={MATERIAL_TYPES.DEFAULT}>兜底素材配置</Radio.Button>
                        <Radio.Button value={MATERIAL_TYPES.HIT}>命中策略素材配置</Radio.Button>
                    </Radio.Group>
                </p>

                <Form.Item noStyle hidden={curMaterialType == MATERIAL_TYPES.HIT}>
                    <Form.Item
                        name={MATERIAL_TYPES.DEFAULT}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请配置兜底素材');
                                    }

                                    for (const item of value) {
                                        for (const element of item.contentList) {
                                            const errMsg = checkItem(element);
                                            if (errMsg) {
                                                return Promise.reject();
                                                return Promise.reject(`${element.title}-${errMsg}`);
                                            }
                                        }
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        initialValue={MATERIAL_INITIAL}
                        wrapperCol={{
                            span: 24,
                        }}
                    >
                        <CollapseFormListItem hideAdd hideDel hideSort hideLayout>
                            <MaterialList dynamicsOptionInfo={defaultDynamicsOptionInfo} />
                        </CollapseFormListItem>
                    </Form.Item>
                </Form.Item>

                <Form.Item noStyle hidden={curMaterialType == MATERIAL_TYPES.DEFAULT}>
                    <Form.Item
                        name={MATERIAL_TYPES.HIT}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请配置命中策略素材');
                                    }

                                    for (const item of value) {
                                        for (const element of item.contentList) {
                                            const errMsg = checkItem(element);
                                            if (errMsg) {
                                                return Promise.reject();
                                                return Promise.reject(
                                                    `${item.title}-${element.title}-${errMsg}`,
                                                );
                                            }
                                        }
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        initialValue={MATERIAL_HIT_INITIAL}
                        wrapperCol={{
                            span: 24,
                        }}
                    >
                        <CollapseFormListItem hideAdd hideDel hideSort>
                            <MaterialList dynamicsOptionInfo={policyDynamicsOptionInfo} />
                        </CollapseFormListItem>
                    </Form.Item>
                </Form.Item>

                <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.bgImg !== curValues.bgImg ||
                        prevValues.littleIcon !== curValues.littleIcon ||
                        prevValues.btnImg !== curValues.btnImg ||
                        prevValues.btnColor !== curValues.btnColor ||
                        prevValues.defaultVos !== curValues.defaultVos ||
                        prevValues.policyVos !== curValues.policyVos
                    }
                >
                    {({ getFieldsValue }) => {
                        const formValues = getFieldsValue();

                        const vosList = [];

                        if (!isEmpty(formValues.defaultVos)) {
                            const defaultVos = initFormmatVos(
                                copyObjectCommon(formValues.defaultVos),
                                'preview',
                            );
                            console.log(54545, defaultVos);
                            for (const element of defaultVos) {
                                if (!isEmpty(element.contentList)) {
                                    for (const contentItem of element.contentList) {
                                        const guideInfo = { ...formValues, ...contentItem };
                                        delete guideInfo.defaultVos;
                                        delete guideInfo.policyVos;
                                        vosList.push(guideInfo);
                                    }
                                }
                            }
                        }

                        if (!isEmpty(formValues.policyVos)) {
                            const policyVos = initFormmatVos(
                                copyObjectCommon(formValues.policyVos),
                                'preview',
                            );

                            for (const element of policyVos) {
                                if (!isEmpty(element.contentList)) {
                                    for (const contentItem of element.contentList) {
                                        const guideInfo = { ...formValues, ...contentItem };
                                        delete guideInfo.defaultVos;
                                        delete guideInfo.policyVos;
                                        vosList.push(guideInfo);
                                    }
                                }
                            }
                        }

                        console.log(4324, vosList);

                        window.sessionStorage.setItem('xdt_vipGuide', JSON.stringify(vosList));

                        return (
                            <LayoutPreview
                                layouts={[
                                    <iframe
                                        key="index"
                                        src={`${
                                            window.location.origin
                                        }${PUBLIC_PATH}preview/index.html#/pagesActive/publicTask/vipGuideDemo?timestamp=${new Date().getTime()}`}
                                        style={{
                                            border: 0,
                                            width: '445px',
                                            minHeight: '466px',
                                            backgroundColor: '#efefef',
                                        }}
                                    ></iframe>,
                                ]}
                            ></LayoutPreview>
                        );
                    }}
                </Form.Item>

                <Form.Item>
                    <Space style={{ marginTop: '40px', display: 'flex', justifyContent: 'end' }}>
                        <Button
                            onClick={() => {
                                callback && callback();
                            }}
                            type="default"
                            size="large"
                        >
                            取消
                        </Button>

                        <Button
                            type="primary"
                            size="large"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            确定
                        </Button>
                    </Space>
                </Form.Item>
            </Form>
        </>
    );
};

export default VipGuideForm;
