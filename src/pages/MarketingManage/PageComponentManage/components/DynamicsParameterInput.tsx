import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Input, Typography, Space, Tooltip } from 'antd';
import ParameterModal from './ParameterModal';
import { InfoCircleOutlined } from '@ant-design/icons';

const DynamicsParameterInput = (
    props: API.CommonFormItem<string> & {
        placeholder?: string;
        maxLength?: number;
        options?: { codeValue: string; codeName: string }[];
        filedProps?: Record<string, any>;
        addonAfterExtend?: React.ReactNode;
    },
    ref: any,
) => {
    const {
        value,
        onChange,
        disabled = false,
        placeholder = '请输入',
        maxLength,
        options = [],
        filedProps = {},
        addonAfterExtend,
        ...extendProps
    } = props;
    const parameterModalRef = useRef();
    useImperativeHandle(ref, () => {
        return {};
    });
    const updateFormItem = (newVal: string) => {
        onChange && onChange(newVal);
    };
    return (
        <>
            <Input
                value={value}
                onInput={(event) => {
                    const {
                        target: { value: newVal },
                    } = event;
                    for (const optionItem of options) {
                        const index = value?.indexOf(optionItem.codeValue);
                        const resultIndex = newVal?.indexOf(optionItem.codeValue);
                        if (index >= 0 && resultIndex < 0) {
                            updateFormItem(value?.replace(optionItem.codeValue, '') || '');

                            return;
                        }
                    }
                    updateFormItem(newVal);
                }}
                disabled={disabled}
                placeholder={placeholder}
                autoComplete="off"
                // showCount
                maxLength={maxLength}
                addonAfter={
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                parameterModalRef?.current?.open();
                            }}
                        >
                            +添加动态参数
                        </Typography.Link>
                        <Tooltip title="想要高亮的部分用英文[]中括号包起来（例：优惠金额[0.5元]）">
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                        {addonAfterExtend}
                    </Space>
                }
                {...filedProps}
            />
            <ParameterModal
                ref={parameterModalRef}
                options={options}
                onConfirm={(val: string) => {
                    updateFormItem(`${value || ''}${val}`);
                }}
            ></ParameterModal>
        </>
    );
};

export default forwardRef(DynamicsParameterInput);
