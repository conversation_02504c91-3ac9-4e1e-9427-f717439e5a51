import { forwardRef, useImperativeHandle, useState, useRef, useEffect, useMemo } from 'react';
import SelectColor from '@/components/SelectColor/Item';
import { Form, Input, Typography, Select, Space } from 'antd';
import { isEmpty, isObjectEqual } from '@/utils/utils';
import DynamicsParameterInput from './DynamicsParameterInput';

import { MATERIAL_BIZ_TYPES } from '@/constants/componentManage';

const layoutConfig = {
    labelCol: { flex: '0 0 140px' },
};

const useCheckItem = () => {
    const [startCheckInfo, changeStartCheckInfo] = useState({});
    const checkStartByKey = (key: string) => {
        return startCheckInfo[key];
    };
    const updateStartCheckInfo = (info: Record<string, any>) => {
        changeStartCheckInfo({
            ...startCheckInfo,
            ...info,
        });
    };
    return {
        updateStartCheckInfo,
        checkStartByKey,
    };
};
export const checkItem = (info: MATERIAL_ITEM_VO) => {
    let errMsg = '';
    const checkKeys = [
        'mainContent',
        'contentDefaultColor',
        'contentHighColor',
        'btnContent',
        // 'btnContentColor',
        'btnPageType',
    ];
    for (const item of checkKeys) {
        errMsg = checkItemByKey(item, info);

        if (errMsg) {
            break;
        }
    }
    return errMsg;
};
export interface MATERIAL_ITEM_VO {
    title?: string;
    bizType?: string;
    mainContent?: string;

    contentDefaultColor?: string;
    contentHighColor?: string;

    subContent?: string;
    subContentDefaultColor?: string;
    subContentHighColor?: string;

    // btnContentColor?: string;
    btnContent?: string;
    btnPageType?: string;
}

const checkItemByKey = (key: string, info: MATERIAL_ITEM_VO) => {
    let errMsg = '';
    if (isEmpty(info[key])) {
        switch (key) {
            case 'mainContent':
                errMsg = '请输入主文案';
                break;
            case 'contentDefaultColor':
                errMsg = '请选择主文案颜色';
                break;
            // case 'contentHighColor':
            //     errMsg = '请选择主文案高亮颜色';
            //     break;
            case 'btnContent':
                errMsg = '请输入按钮文案';
                break;
            // case 'btnContentColor':
            //     errMsg = '请输入按钮文案颜色';
            //     break;

            case 'btnPageType':
                errMsg = '请选择按钮跳转页面';
                break;
        }
    }
    return errMsg;
};

const MaterialItem = (
    props: API.CommonFormItem<MATERIAL_ITEM_VO> & {
        dynamicsOptionInfo?: any[] | Record<string, any>;
    },
    ref: any,
) => {
    const { value, onChange, disabled = false, dynamicsOptionInfo } = props;

    const { updateStartCheckInfo, checkStartByKey } = useCheckItem();

    useImperativeHandle(ref, () => {
        return {};
    });

    const updateInfo = (newVal: MATERIAL_ITEM_VO) => {
        const info = {
            ...value,
            ...newVal,
        };
        updateFormItem(info);

        const checkInfo = {};
        for (const key in newVal) {
            checkInfo[key] = true;
        }
        updateStartCheckInfo(checkInfo);
    };
    const updateFormItem = (newVal: MATERIAL_ITEM_VO) => {
        onChange && onChange(newVal);
    };

    // const mainOptions = [
    //     {
    //         codeValue: '{站点价格}',
    //         codeName: '站点价格',
    //     },
    //     {
    //         codeValue: '{会员权益优惠金额}',
    //         codeName: '会员权益优惠金额',
    //     },
    // ];

    const checkStatusByKey = (key: string) => {
        return checkItemByKey(key, value) ? 'error' : '';
    };

    const dynamicsOptions = useMemo(() => {
        let list = [];
        if (dynamicsOptionInfo) {
            if (dynamicsOptionInfo instanceof Array) {
                //如果传进来是数组就取数组
                list = dynamicsOptionInfo;
            } else if (dynamicsOptionInfo instanceof Object) {
                //如果传进来是对象就按bizType
                if (value?.bizType && dynamicsOptionInfo[value?.bizType]) {
                    list = dynamicsOptionInfo[value?.bizType];
                } else {
                    for (const key in dynamicsOptionInfo) {
                        const element = dynamicsOptionInfo[key];
                        if (element instanceof Array) {
                            list = list.concat(element);
                        }
                    }
                }
            }
        }
        return list;
    }, [dynamicsOptionInfo, value]);

    return (
        <>
            <Form.Item
                label="主文案"
                required
                help={
                    checkItemByKey('mainContent', value) ||
                    checkItemByKey('contentDefaultColor', value)
                }
                validateStatus={
                    checkStatusByKey('mainContent') || checkStatusByKey('contentDefaultColor')
                }
                {...layoutConfig}
            >
                <DynamicsParameterInput
                    value={value?.mainContent}
                    onChange={(newVal: string) => {
                        updateInfo({
                            mainContent: newVal,
                        });
                    }}
                    disabled={disabled}
                    placeholder="请输入主文案"
                    options={dynamicsOptions}
                    addonAfterExtend={
                        <>
                            <span>
                                <Typography.Text type="danger">*</Typography.Text>文案颜色
                            </span>
                            <SelectColor
                                value={value?.contentDefaultColor}
                                onChange={(newVal: string) => {
                                    updateInfo({
                                        contentDefaultColor: newVal,
                                    });
                                }}
                                disabled={disabled}
                                placeholder="请选择颜色"
                                small
                            />
                            <span>高亮颜色</span>
                            <SelectColor
                                value={value?.contentHighColor}
                                onChange={(newVal: string) => {
                                    updateInfo({
                                        contentHighColor: newVal,
                                    });
                                }}
                                disabled={disabled}
                                placeholder="请选择颜色"
                                small
                            />
                        </>
                    }
                ></DynamicsParameterInput>
            </Form.Item>

            <Form.Item label="副文案" {...layoutConfig}>
                <DynamicsParameterInput
                    value={value?.subContent}
                    onChange={(newVal: string) => {
                        updateInfo({
                            subContent: newVal,
                        });
                    }}
                    disabled={disabled}
                    placeholder="请输入副文案"
                    options={dynamicsOptions}
                    addonAfterExtend={
                        <>
                            <span>
                                <Typography.Text type="danger">*</Typography.Text>文案颜色
                            </span>
                            <SelectColor
                                value={value?.subContentDefaultColor}
                                onChange={(newVal: string) => {
                                    updateInfo({
                                        subContentDefaultColor: newVal,
                                    });
                                }}
                                disabled={disabled}
                                placeholder="请选择颜色"
                                small
                            />
                            <span>高亮颜色</span>
                            <SelectColor
                                value={value?.subContentHighColor}
                                onChange={(newVal: string) => {
                                    updateInfo({
                                        subContentHighColor: newVal,
                                    });
                                }}
                                disabled={disabled}
                                placeholder="请选择颜色"
                                small
                            />
                        </>
                    }
                ></DynamicsParameterInput>
            </Form.Item>

            <Form.Item
                label="按钮文案"
                required
                help={checkItemByKey('btnContent', value)}
                validateStatus={checkStatusByKey('btnContent')}
                {...layoutConfig}
            >
                <DynamicsParameterInput
                    value={value?.btnContent}
                    onChange={(newVal: string) => {
                        updateInfo({
                            btnContent: newVal,
                        });
                    }}
                    disabled={disabled}
                    placeholder="请输入按钮文案"
                    options={dynamicsOptions}
                ></DynamicsParameterInput>
            </Form.Item>

            {/* <Form.Item
                label="按钮文案颜色"
                required
                help={
                    checkStartByKey('btnContentColor') && checkItemByKey('btnContentColor', value)
                }
                validateStatus={
                    checkStartByKey('btnContentColor') && checkStatusByKey('btnContentColor')
                }
                {...layoutConfig}
            >
                <SelectColor
                    value={value?.btnContentColor}
                    onChange={(newVal: string) => {
                        updateInfo({
                            btnContentColor: newVal,
                        });
                    }}
                    disabled={disabled}
                    placeholder="请选择颜色"
                />
            </Form.Item> */}

            <Form.Item
                label="按钮跳转页面"
                required
                help={checkItemByKey('btnPageType', value)}
                validateStatus={checkStatusByKey('btnPageType')}
                {...layoutConfig}
            >
                <Select
                    value={value?.btnPageType}
                    onChange={(newVal: string) => {
                        updateInfo({
                            btnPageType: newVal,
                        });
                    }}
                    placeholder="请选择"
                    disabled={disabled}
                >
                    <Select.Option value="01">会员开通页</Select.Option>
                    <Select.Option value="02">会员中心页</Select.Option>
                    {value?.bizType == MATERIAL_BIZ_TYPES.TRIAL ? (
                        <Select.Option value="03">体验会员开通页</Select.Option>
                    ) : undefined}
                </Select>
            </Form.Item>
        </>
    );
};
export default forwardRef(MaterialItem);
