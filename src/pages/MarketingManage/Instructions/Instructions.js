// 使用说明管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Cascader,
    message,
    Image,
    Tooltip,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { ExclamationCircleOutlined, LeftOutlined } from '@ant-design/icons';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import {
    functionDirectionDeleteApi,
    functionDirectionAddApi,
} from '@/services/Marketing/MarketingInstructionsApi';
import styles from '@/assets/styles/common.less';
import QuillRichInput from '@/components/QuillRichInput/index';
import { isEmpty } from '@/utils/utils';

import SearchOptionsBar from '@/components/SearchOptionsBar';

const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const formItemLayout = {};

const formItemEditLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { dispatch, listLoading, form, onSubmit, onReset, history, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem label="版本号:" name="versionsName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="dateTime" label="创建时间:" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ManagePage = (props) => {
    const {
        dispatch,
        history,
        route,
        InstructionsModel: { instructionsList, instructionsListTotal, info },
        global: { pageInit, channelOptions = [] },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [editForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    const [copyStatus, setCopyStatus] = useState(false);
    const [showEdit, changeShowEdit] = useState(false);
    const [editInfo, updateEditInfo] = useState(null);

    useEffect(() => {
        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (info) {
            openEditEvent(info);
        }
    }, [info]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: (data.dateTime && data.dateTime[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dateTime && data.dateTime[1].format('YYYY-MM-DD')) || '',
            versionsName: data?.versionsName ?? '',
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'InstructionsModel/getFunctionDirectionList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '版本号',
            width: 200,
            dataIndex: 'versionsName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                const editBtn = (
                    <span
                        key="edit"
                        className={styles['table-btn']}
                        onClick={() => getInfo(record)}
                    >
                        编辑
                    </span>
                );
                const deleteBtn = (
                    <span
                        key="del"
                        className={styles['table-btn']}
                        onClick={() => delEditEvent(record)}
                        style={{ color: 'red' }}
                    >
                        删除
                    </span>
                );
                const copyBtn = (
                    <span
                        key="copy"
                        className={styles['table-btn']}
                        onClick={() => getInfo(record, 'copy')}
                    >
                        复制
                    </span>
                );

                btnList.push(editBtn);

                btnList.push(deleteBtn);

                btnList.push(copyBtn);

                return <Space>{btnList}</Space>;
            },
        },
    ];

    const openAddEvent = () => {
        changeShowEdit(true);
    };

    const getInfo = (item, type) => {
        setCopyStatus(type ? true : false);
        let params = {
            id: item.id,
        };
        dispatch({
            type: 'InstructionsModel/getFunctionDirectionInfo',
            options: params,
        });
    };
    const openEditEvent = (item) => {
        changeShowEdit(true);

        editForm.setFieldsValue({
            id: copyStatus ? '' : item.id,
            versionsName: item.versionsName,
            content: item.content,
        });
        updateEditInfo(item);
    };
    const closeEditEvent = () => {
        changeShowEdit(false);
        editForm.setFieldsValue({
            versionsName: null,
            content: null,
        });
        updateEditInfo(null);
    };

    const delEditEvent = async (item) => {
        confirm({
            title: `确定删除${item.versionsName}?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await functionDirectionDeleteApi({ id: item.id });
                    searchData();
                    message.success('删除成功');
                    return;
                } catch (error) {
                    return Promise.reject(error);
                }
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const submitSaveEvent = async (values) => {
        try {
            let params = {
                versionsName: values.versionsName,
                content: values.content,
            };
            if (editInfo?.id && !copyStatus) {
                params.id = editInfo.id;
            }
            await functionDirectionAddApi(params);
            message.success('保存成功');
            await searchData();
            closeEditEvent();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const goBack = () => {
        history.replace('/marketing/couponCenter/cpnManage/list');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={openAddEvent}>
                        + 新建
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={instructionsList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: instructionsListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={`${editInfo && !copyStatus ? '编辑' : '新建'}使用说明`}
                destroyOnClose
                width={650}
                visible={showEdit}
                onCancel={() => {
                    closeEditEvent();
                }}
                footer={null}
                maskClosable={false}
            >
                <Form
                    form={editForm}
                    {...formItemEditLayout}
                    wrapperCol={{ span: 24 }}
                    onFinish={submitSaveEvent}
                >
                    <FormItem
                        label="版本号"
                        name="versionsName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="如：V1，限10字"
                            autoComplete="off"
                            maxLength={10}
                            allowClear
                            showCount
                        ></Input>
                    </FormItem>
                    <QuillRichInput
                        label="正文"
                        name="content"
                        rules={[
                            { required: true, message: '请填写' },
                            () => ({
                                validator(rule, value) {
                                    if (value == '<p><br></p>') {
                                        return Promise.reject('请填写');
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        {...formItemEditLayout}
                        wrapperCol={{
                            span: 24,
                        }}
                        placeholder="请填写"
                    ></QuillRichInput>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEditEvent}>取消</Button>
                    </Space>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, InstructionsModel, loading }) => ({
    global,
    InstructionsModel,
    listLoading: loading.effects['InstructionsModel/getFunctionDirectionList'],
}))(ManagePage);
