import {
    functionDirectionInfoApi,
    functionDirectionPageApi,
} from '@/services/Marketing/MarketingInstructionsApi';

const InstructionsModel = {
    namespace: 'InstructionsModel',
    state: {
        info: null, // 详情
        instructionsList: [], // 列表
        instructionsListTotal: 0, // 列表记录数
    },
    effects: {
        *getFunctionDirectionInfo({ options }, { call, put, select }) {
            try {
                const { data = {} } = yield call(functionDirectionInfoApi, options);
                yield put({
                    type: 'updateInfo',
                    info: data,
                });
            } catch (error) {}
        },
        *getFunctionDirectionList({ options }, { call, put }) {
            try {
                const response = yield call(functionDirectionPageApi, options);
                yield put({
                    type: 'updateState',
                    payload: {
                        instructionsList: response.data.records,
                        instructionsListTotal: response.data.total,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
    },
    reducers: {
        updateInfo(state, { info }) {
            return {
                ...state,
                info,
            };
        },
        updateState(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
    },
};
export default InstructionsModel;
