import {
    getCouponDetail<PERSON>pi,
    getCouponListApi,
    getCodeStoreListApi,
    getCodeListApi,
    getCouponActListApi,
} from '@/services/Marketing/MarketingNewCouponApi';
import { qryXdtOperatorInfosApi } from '@/services/OperationMng/OperationMngApi.js';

const cpnManageModel = {
    namespace: 'cpnManageModel',
    state: {
        conponInfo: null,
        operationList: [], // 运营商管理列表
        operationListTotal: 0, // 运营商管理列表记录数

        codeManageList: [], //码管理列表
        codeManageListTotal: 0, //码管理列表数量

        codeStoreList: [], //码库列表
        codeStoreListTotal: 0, //码库列表数量

        linkList: [], //码管理列表
        linkListTotal: 0, //码管理列表数量
    },
    effects: {
        *getCouponInfo({ options }, { call, put, select }) {
            try {
                const { data = {} } = yield call(getCouponDetailApi, options);
                yield put({
                    type: 'updateCouponInfo',
                    info: data,
                });
            } catch (error) {}
        },
        *getOperationList({ payload }, { call, put }) {
            try {
                const response = yield call(qryXdtOperatorInfosApi, payload);
                yield put({
                    type: 'updateState',
                    payload: {
                        operationList: response.data.list,
                        operationListTotal: response.data.total,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *getCodeMangeList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCodeListApi, options);
                yield put({
                    type: 'updateState',
                    payload: {
                        codeManageList: records,
                        codeManageListTotal: total,
                    },
                });
            } catch (error) {}
        },
        *getCodeStoreList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCodeStoreListApi, options);
                yield put({
                    type: 'updateState',
                    payload: {
                        codeStoreList: records,
                        codeStoreListTotal: total,
                    },
                });
            } catch (error) {}
        },
        *getLinkList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCouponActListApi, options);
                yield put({
                    type: 'updateState',
                    payload: {
                        linkList: records,
                        linkListTotal: total,
                    },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateState(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        updateCouponInfo(state, { info }) {
            return {
                ...state,
                conponInfo: info,
            };
        },
    },
};
export default cpnManageModel;
