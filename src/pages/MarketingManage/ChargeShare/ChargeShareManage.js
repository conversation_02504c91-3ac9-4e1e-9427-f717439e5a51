import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    Modal,
    DatePicker,
    Input,
    Select,
    Popconfirm,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

import {
    updateCardShareStateApi,
    saveCardShareInfoApi,
} from '@/services/Marketing/MarketingChargeApi';
import { useImperativeHandle } from 'react';

const FormItem = Form.Item;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} open>
                <Col span={8}>
                    <FormItem label="共享人手机" name="mobile">
                        <Input placeholder="请填写" autoComplete="off" allowClear />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="被共享人手机" name="sharedMobile">
                        <Input placeholder="请填写" autoComplete="off" allowClear />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="状态" name="whiteState">
                        <Select placeholder="请选择" allowClear>
                            {[
                                { codeName: '开启', codeValue: '1' },
                                { codeName: '停止', codeValue: '0' },
                            ].map((ele) => {
                                return (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const AddModal = (props) => {
    const { initRef, onFinish } = props;

    const [visible, updateVisible] = useState(false);
    const [form] = Form.useForm();
    useImperativeHandle(initRef, () => ({
        show: () => {
            updateVisible(true);
            form.resetFields();
        },
        close,
    }));

    const close = () => {
        updateVisible(false);
    };

    const [isWaiting, updateWaiting] = useState(false);
    const submitFormEvent = () => {
        form.validateFields().then(async (value) => {
            try {
                await saveCardShareInfoApi(value);
                updateWaiting(true);
                onFinish?.();
                close();
            } catch (error) {
                return Promise.reject(error);
            } finally {
                updateWaiting(false);
            }
        });
    };

    return (
        <Modal
            title={'添加共享用户'}
            destroyOnClose
            width={500}
            visible={visible}
            onCancel={() => {
                close();
            }}
            onOk={submitFormEvent}
            zIndex={10}
            okButtonProps={{ loading: isWaiting }}
        >
            <Form {...formItemLayout} form={form}>
                <FormItem name="whiteState" initialValue={'1'} noStyle />
                <FormItem
                    label="企业名称"
                    name="companyName"
                    rules={[{ required: true, message: '请输入企业名称' }]}
                >
                    <Input autoComplete="off" placeholder="请输入" showCount />
                </FormItem>
                <FormItem
                    label="用户手机号"
                    name="mobile"
                    rules={[{ required: true, message: '请输入手机号' }]}
                >
                    <Input autoComplete="off" placeholder="请输入" />
                </FormItem>
            </Form>
        </Modal>
    );
};

export const ChargeShareManage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        chargeShareModel: { chargeShareList, chargeShareTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const addRef = useRef();
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }

        return () => {
            dispatch({
                type: 'chargeShareModel/setProperty',
                params: {
                    chargeShareList: [],
                    chargeShareTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'chargeShareModel/getCardShareList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '开通时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '企业名称',
            width: 140,
            dataIndex: 'companyName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '共享人手机',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电卡数量',
            width: 140,
            dataIndex: 'cardCount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电卡总余额',
            width: 140,
            dataIndex: 'cardAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '共享人数',
            width: 140,
            dataIndex: 'sharedCount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'whiteStateName',
            render(text, record) {
                let color = '#000000';
                if (record.whiteState == '1') {
                    // 进行中
                    // 绿色
                    color = '#87d068';
                } else if (record.whiteState == '0') {
                    // 已停止
                    // 灰色
                    color = '#cccccc';
                }
                return (
                    <span title={text} style={{ color: color }}>
                        {text}
                    </span>
                );
            },
        },

        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (text, record) => {
                const btns = [
                    <a
                        key="detail"
                        onClick={() => {
                            history.push(
                                `/marketing/card/share/detail?id=${record.cardShareWhiteId}`,
                            );
                        }}
                        type="link"
                    >
                        查看
                    </a>,
                ];
                // 1 开启 0停止
                btns.push(
                    <Popconfirm
                        key="oper"
                        type="link"
                        onConfirm={async () => {
                            await updateCardShareStateApi({
                                cardShareWhiteId: record.cardShareWhiteId,
                                whiteState: record.whiteState == '1' ? '0' : '1',
                            });
                            message.success('操作成功');
                            searchData();
                        }}
                        title={`确定${record.whiteState == '1' ? '停止' : '开启'}?`}
                    >
                        <a>{record.whiteState == '1' ? '停止' : '开启'}</a>
                    </Popconfirm>,
                );
                return (
                    <div style={{ display: 'flex', gap: '12px' }}>
                        {btns.map((ele, index) => {
                            return (
                                <div key={index} style={{ whiteSpace: 'nowrap' }}>
                                    {ele}
                                </div>
                            );
                        })}
                    </div>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    {...props}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button
                        type="primary"
                        onClick={() => {
                            addRef.current.show();
                        }}
                    >
                        新增
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.workOrderId}
                    dataSource={chargeShareList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: chargeShareTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>

            <AddModal initRef={addRef} onFinish={() => searchData()} {...props}></AddModal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, chargeShareModel, loading, user }) => ({
    global,
    chargeShareModel,
    listLoading: loading.effects['chargeShareModel/getCardShareList'],
    currentUser: user.currentUser,
}))(ChargeShareManage);
