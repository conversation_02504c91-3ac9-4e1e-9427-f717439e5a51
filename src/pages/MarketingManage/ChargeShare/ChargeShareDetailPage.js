import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Descriptions } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

export const ChargeShareDetailPage = (props) => {
    const {
        dispatch,
        history,
        route,
        chargeShareModel: { chargeShareInfo },
        listLoading,
    } = props;

    const {
        location: { query },
    } = history;

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        return () => {
            dispatch({
                type: 'chargeShareModel/setProperty',
                params: { chargeShareInfo: undefined },
            });
        };
    }, []);

    useEffect(() => {
        initData();
    }, [pageInfo]);

    const initData = async () => {
        try {
            const params = {
                cardShareWhiteId: query?.id,
                pageSize: pageInfo.pageSize,
                pageIndex: pageInfo.pageIndex,
            };

            dispatch({
                type: 'chargeShareModel/getCardShareDetail',
                options: params,
            });
        } catch (error) {}
    };

    const columns = [
        {
            title: '共享时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '被共享手机',
            width: 140,
            dataIndex: 'sharedMobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '名称',
            width: 140,
            dataIndex: 'sharedCustName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '已消费金额',
            width: 140,
            dataIndex: 'discountAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '共享总额度',
            width: 140,
            dataIndex: 'sharedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '剩余共享金额',
            width: 140,
            dataIndex: 'surplusAmt',
            render(text, record) {
                let result = Number(record.sharedAmt) - Number(record.discountAmt);

                return <span title={result}>{result}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'recordStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };

    const whiteStateName = useMemo(() => {
        let name = '-';
        if (chargeShareInfo?.whiteState == '1') {
            name = '进行中';
        } else if (chargeShareInfo?.whiteState == '0') {
            name = '已停止';
        }
        return name;
    }, [chargeShareInfo]);

    const cardShareRecordQueryVoPage = useMemo(() => {
        let total = 0;
        let list = [];
        if (chargeShareInfo && chargeShareInfo.cardShareRecordQueryVoPage) {
            total = chargeShareInfo.cardShareRecordQueryVoPage?.total;
            list = chargeShareInfo.cardShareRecordQueryVoPage?.records;
        }
        return {
            list,
            total,
        };
    }, [chargeShareInfo]);

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card title="共享人信息">
                <Descriptions>
                    {[
                        { title: '共享人手机', value: chargeShareInfo?.mobile || '-' },
                        { title: '企业名称', value: chargeShareInfo?.companyName || '-' },
                        { title: '开通时间', value: chargeShareInfo?.createdTime || '-' },
                        { title: '共享状态', value: whiteStateName },
                    ]?.map((ele, index) => {
                        return (
                            <Descriptions.Item label={ele.title} key={index}>
                                {ele.value}
                            </Descriptions.Item>
                        );
                    })}
                </Descriptions>
            </Card>
            <br />
            <Card title="共享记录">
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.id}
                    dataSource={cardShareRecordQueryVoPage?.list}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: cardShareRecordQueryVoPage?.total,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, chargeShareModel, loading }) => ({
    global,
    chargeShareModel,
    listLoading: loading.effects['chargeShareModel/getCardShareDetail'],
}))(ChargeShareDetailPage);
