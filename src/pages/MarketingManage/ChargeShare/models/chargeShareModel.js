import {
    getCardShareListApi,
    getCardShareDetailApi,
} from '@/services/Marketing/MarketingChargeApi';

const ChargeShareModel = {
    namespace: 'chargeShareModel',
    state: {
        chargeShareList: [],
        chargeShareTotal: 0,

        chargeShareInfo: undefined,
    },
    effects: {
        *getCardShareList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getCardShareListApi, options);

                yield put({
                    type: 'setProperty',
                    params: { chargeShareList: list, chargeShareTotal: total },
                });
            } catch (error) {}
        },
        *getCardShareDetail({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getCardShareDetailApi, options);

                yield put({
                    type: 'setProperty',
                    params: { chargeShareInfo: data },
                });
            } catch (error) {}
        },
    },
    reducers: {
        setProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default ChargeShareModel;
