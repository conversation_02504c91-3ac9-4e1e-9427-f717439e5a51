import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { Button, Form, Radio, Select, Table } from 'antd';
import { Fragment, useImperativeHandle, useMemo, useRef, useState } from 'react';
import {
    CDP_COLUMNS,
    CDP_CROWD_COLUMNS,
    CROWD_TYPES,
    CrowdSelectModal,
} from '../../Membership/components/PriceCrowdItem';
import { MinusCircleOutlined } from '@ant-design/icons';
import { copyObjectCommon } from '@/utils/utils';

import { ActivitiesPatternEnum } from '../AdvertisementUpdatePage';

export const VIP_POLICY_TYPES = {
    TRY: '01', // 体验会员
    OPEN: '02', //开通会员
    UPDATE: '03', // 升级会员
    CONTINUE: '04', // 续费会员
    OVERDUE: '05', // 过期续费
};

const ModalDelegate = (props) => {
    const { initRef, form, applyType = '' } = props;

    const [visible, updateVisible] = useState(false);
    const [type, updateType] = useState();
    const [key, updateKey] = useState('');
    const [title, updateTitle] = useState('');
    const [defaultSelects, updateDefaultSelects] = useState([]);
    useImperativeHandle(initRef, () => ({
        show: ({ type: _type }) => {
            updateVisible(true);
            updateType(_type);
            let key;
            let title;
            switch (_type) {
                case CROWD_TYPES.CDP:
                    key = 'crowd_CDP_pic_list';
                    title = '选择CDP画像';
                    break;
                case CROWD_TYPES.CDP_CROWD:
                    key = 'crowd_CDP_group_list';
                    title = '选择CDP人群';
                    break;

                default:
                    break;
            }
            updateKey(key);
            updateTitle(title);
            updateDefaultSelects(form.getFieldValue(key) || []);
        },
        onClose,
    }));

    const onClose = () => {
        updateVisible(false);
        updateType(undefined);
        updateKey('');
        updateTitle('');
    };

    const finishSelectCrowdEvent = (selectList) => {
        for (let index = selectList.length - 1; index >= 0; index--) {
            const element = selectList[index];
            if (element.children) {
                selectList.splice(index, 1);
            }
        }

        form.setFieldsValue({ [key]: selectList });
        onClose();
    };

    return (
        <CrowdSelectModal
            applyType={applyType}
            title={title}
            crowdType={type}
            defaultSelects={defaultSelects}
            visible={visible}
            onClose={onClose}
            onFinish={finishSelectCrowdEvent}
            // limitLabel={limitLabel}
            // allXdtLabel={allXdtLabel}
            // disabledIds={disabledIds}
        />
    );
};

export const CrowdSelectItem = (props) => {
    const {
        applyType = '',
        form,
        formItemLayout,
        formItemFixedWidthLayout,
        hasAll,
        // 仅新电途人群用，
        defaultCheckedAll,
        isEdit,
        isCopy,
        changeCrowdTypeCallback, // 监听值变化容易出错，直接回调事件，针对事件做处理
        changePolicySceneCallback, // 监听值变化容易出错，直接回调事件，针对事件做处理
    } = props;

    const policyOptions = useMemo(() => {
        let options = [
            { codeName: '推荐体验会员', codeValue: VIP_POLICY_TYPES.TRY },
            { codeName: '推荐开通会员', codeValue: VIP_POLICY_TYPES.OPEN },
            { codeName: '推荐升级会员', codeValue: VIP_POLICY_TYPES.UPDATE },
            { codeName: '推荐续费会员', codeValue: VIP_POLICY_TYPES.CONTINUE },
            { codeName: '推荐过期续费会员', codeValue: VIP_POLICY_TYPES.OVERDUE },
        ];
        return options.map((ele) => {
            return (
                <Select.Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Select.Option>
            );
        });
    });
    const putSceneType = Form.useWatch('putSceneType', form);
    const groupTypeOptions = useMemo(() => {
        let options = [
            hasAll && { codeName: '全部', codeValue: CROWD_TYPES.ALL },
            { codeName: '新电途标签', codeValue: CROWD_TYPES.XDT },
            { codeName: 'CDP画像', codeValue: CROWD_TYPES.CDP },
            { codeName: 'CDP人群', codeValue: CROWD_TYPES.CDP_CROWD },
        ];

        const noDisableds = [CROWD_TYPES.ALL, CROWD_TYPES.XDT];

        return options.map((ele) => {
            return (
                ele && (
                    <Radio
                        value={ele.codeValue}
                        key={ele.codeValue}
                        disabled={putSceneType === '02' && !noDisableds.includes(ele.codeValue)}
                    >
                        {ele.codeName}
                    </Radio>
                )
            );
        });
    }, [putSceneType]);

    const cdpGroupColumns = ({ type, list }) => {
        let columnList = [];
        if (type == CROWD_TYPES.CDP) {
            columnList.push(...CDP_COLUMNS);
        } else if (type == CROWD_TYPES.CDP_CROWD) {
            columnList.push(...CDP_CROWD_COLUMNS);
        }
        const editColumn = {
            title: '操作',
            width: '80px',
            fixed: 'right',
            render(text, record, index) {
                return (
                    <MinusCircleOutlined
                        style={{ fontSize: '24px', color: 'red' }}
                        onClick={() => {
                            const newList = copyObjectCommon(list);
                            newList.splice(index, 1);
                            if (type == CROWD_TYPES.CDP) {
                                form.setFieldsValue({ crowd_CDP_pic_list: newList });
                            } else if (type == CROWD_TYPES.CDP_CROWD) {
                                form.setFieldsValue({ crowd_CDP_group_list: newList });
                            }
                        }}
                    />
                );
            },
        };
        if (isEdit || isCopy) {
            // 编辑页、复制页中CDP人群/画像，需要支持里面的画像被删除
            columnList.push(editColumn);
        }
        return columnList;
    };

    const modalRef = useRef();

    return (
        <Fragment>
            <Form.Item
                name="crowdType"
                label="人群类型"
                rules={[{ required: true, message: '请选择' }]}
                {...formItemLayout}
                initialValue={CROWD_TYPES.XDT}
            >
                <Radio.Group disabled={isEdit} onChange={changeCrowdTypeCallback}>
                    {groupTypeOptions}
                </Radio.Group>
            </Form.Item>

            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.crowdType !== curValues.crowdType ||
                    prevValues.crowd_CDP_pic_list != curValues.crowd_CDP_pic_list ||
                    prevValues.crowd_CDP_group_list != curValues.crowd_CDP_group_list ||
                    prevValues.putSceneType != curValues.putSceneType ||
                    prevValues.activitiesPattern !== curValues.activitiesPattern
                }
            >
                {({ getFieldValue }) => {
                    const crowdType = getFieldValue('crowdType');
                    const crowd_CDP_pic_list = getFieldValue('crowd_CDP_pic_list');
                    const crowd_CDP_group_list = getFieldValue('crowd_CDP_group_list');
                    const putSceneType = getFieldValue('putSceneType');
                    const activitiesPattern = getFieldValue('activitiesPattern');
                    return (
                        (crowdType == CROWD_TYPES.ALL &&
                            activitiesPattern == ActivitiesPatternEnum.BANNER_VIP && (
                                <Form.Item
                                    name="policyScene"
                                    label="推荐策略场景"
                                    rules={[{ required: true, message: '请选择' }]}
                                    {...formItemFixedWidthLayout}
                                >
                                    <Select
                                        placeholder="请选择"
                                        onChange={changePolicySceneCallback}
                                    >
                                        {policyOptions}
                                    </Select>
                                </Form.Item>
                            )) ||
                        (crowdType == CROWD_TYPES.XDT && (
                            <ActiveCrowdCheckTree
                                form={form}
                                required
                                valueType="select"
                                defaultCheckedAll={defaultCheckedAll}
                                rules={[{ required: true, message: '请选择' }]}
                                {...formItemFixedWidthLayout}
                                disabled={putSceneType == '02'}
                            />
                        )) ||
                        (crowdType == CROWD_TYPES.CDP && (
                            <Form.Item
                                name="crowd_CDP_pic_list"
                                label="活动人群"
                                rules={[{ required: true, message: '请选择' }]}
                                {...formItemLayout}
                            >
                                <Button
                                    type="primary"
                                    disabled={putSceneType == '02'}
                                    onClick={() => {
                                        modalRef.current.show({
                                            type: CROWD_TYPES.CDP,
                                        });
                                    }}
                                >
                                    {isEdit ? '编辑' : '选择'}CDP画像
                                </Button>
                                {(crowd_CDP_pic_list?.length && (
                                    <div className="mg-t-20">
                                        <Table
                                            scroll={{ x: 'max-content', y: 300 }}
                                            columns={cdpGroupColumns({
                                                type: crowdType,
                                                list: crowd_CDP_pic_list,
                                            })}
                                            rowKey={(record, index) => index}
                                            dataSource={crowd_CDP_pic_list}
                                            pagination={false}
                                        ></Table>
                                    </div>
                                )) ||
                                    null}
                            </Form.Item>
                        )) ||
                        (crowdType == CROWD_TYPES.CDP_CROWD && (
                            <Form.Item
                                name="crowd_CDP_group_list"
                                label="活动人群"
                                rules={[{ required: true, message: '请选择' }]}
                                {...formItemLayout}
                            >
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        modalRef.current.show({ type: CROWD_TYPES.CDP_CROWD });
                                    }}
                                    disabled={putSceneType == '02'}
                                >
                                    {isEdit ? '编辑' : '选择'}CDP人群
                                </Button>
                                {(crowd_CDP_group_list?.length && (
                                    <div className="mg-t-20">
                                        <Table
                                            scroll={{ x: 'max-content', y: 300 }}
                                            columns={cdpGroupColumns({
                                                type: crowdType,
                                                list: crowd_CDP_group_list,
                                            })}
                                            rowKey={(record, index) => index}
                                            dataSource={crowd_CDP_group_list}
                                            pagination={false}
                                        ></Table>
                                    </div>
                                )) ||
                                    null}
                            </Form.Item>
                        )) ||
                        null
                    );
                }}
            </Form.Item>

            <ModalDelegate applyType={applyType} initRef={modalRef} form={form} />
        </Fragment>
    );
};
