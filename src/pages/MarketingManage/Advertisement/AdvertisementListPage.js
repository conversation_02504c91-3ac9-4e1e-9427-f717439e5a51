import { Page<PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Cascader,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    deleteAdvertisementApi,
    stopAdvertisementApi,
} from '@/services/Marketing/MarketingAdvertisementMngApi';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { NEW_STATUS_TYPES } from '@/config/declare';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import advertisementStyles from './Advertisement.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import { getCityListApi } from '@/services/CommonApi';
import CitysSelect from '@/components/CitysSelect/index.js';
import { ActivitiesPatternEnum } from './AdvertisementUpdatePage';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        history,
        onExportForm,
        advertisementModel: { advertiseStateList, advertiseTypeList },
        global: { channelOptions = [] },
        route,
    } = props;

    const isBanner = useMemo(() => {
        if (route.path.indexOf('/banner/') >= 0) {
            return true;
        }
        return false;
    }, [route.path]);
    const [cityTreeData, changeCityTreeData] = useState([]);

    useEffect(() => {
        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
            });
        }
        initCityTree();
    }, []);

    const initCityTree = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = [];
            for (const item of areaList) {
                list.push({
                    key: item.areaCode,
                    label: item.areaName,
                    value: item.areaCode,
                    children: item.cityList ? formatCityItem(item.cityList) : [],
                });
            }
            changeCityTreeData(list);
            return;
        } catch (error) {
            console.log(88888, error);
            return Promise.reject(error);
        }
    };

    const formatCityItem = (data) => {
        const list = [];
        for (const item of data) {
            list.push({
                key: item.areaCode,
                label: item.areaName,
                value: item.areaCode,
            });
        }
        return list;
    };

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [null, null],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="活动名称:" name="titleName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem
                        label="活动样式"
                        name="activitiesPattern"
                        {...formItemLayout}
                        initialValue={(isBanner && ActivitiesPatternEnum.BANNER_VIP) || undefined}
                    >
                        <Select
                            placeholder="请选择"
                            allowClear={!isBanner}
                            onChange={() => {
                                form.setFieldsValue({ activitiesLaunch: undefined });
                            }}
                        >
                            {advertiseTypeList?.map((ele) => {
                                return (
                                    ((([
                                        ActivitiesPatternEnum.BANNER_VIP,
                                        ActivitiesPatternEnum.CAR_AUTH,
                                    ].includes(ele.activitiesPattern) &&
                                        isBanner) ||
                                        (![
                                            ActivitiesPatternEnum.BANNER_VIP,
                                            ActivitiesPatternEnum.CAR_AUTH,
                                        ].includes(ele.activitiesPattern) &&
                                            !isBanner)) && (
                                        <Option
                                            value={ele.activitiesPattern}
                                            key={ele.activitiesPattern}
                                        >
                                            {ele.activitiesPatternName}
                                        </Option>
                                    )) ||
                                    null
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                        {({ getFieldValue }) => {
                            const activitiesPattern = getFieldValue('activitiesPattern');
                            const location =
                                activitiesPattern &&
                                advertiseTypeList?.filter(
                                    (ele) => ele.activitiesPattern == activitiesPattern,
                                );
                            const { activitiesLaunchList } = (location && location[0]) || {};
                            return (
                                <FormItem
                                    label="投放位置:"
                                    name="activitiesLaunch"
                                    {...formItemLayout}
                                >
                                    <Select
                                        placeholder={
                                            (activitiesPattern && '全部') || '请选择活动样式'
                                        }
                                        disabled={activitiesPattern == undefined}
                                        allowClear
                                    >
                                        {activitiesLaunchList?.map((ele) => (
                                            <Option value={ele.codeValue} key={ele.codeValue}>
                                                {ele.codeName}
                                            </Option>
                                        ))}
                                    </Select>
                                </FormItem>
                            );
                        }}
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="投放日期" name="effTime">
                        <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="channel" label="投放渠道">
                        <Select placeholder="请选择" allowClear>
                            {channelOptions?.map((ele) => (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <CitysSelect
                        label="投放城市"
                        name="cityCode"
                        placeholder="请选择"
                        showArrow
                        allowClear
                        provinceSelectable
                        rules={[]}
                        // onChange={(citylist) => {
                        //     //切换会员城市时讲没有选择的会员日城市去掉
                        //     const vipDayList = form.getFieldValue('cityCode');
                        //     let list = [];
                        //     for (const item of vipDayList) {
                        //         if (citylist.includes(item)) {
                        //             list.push(item);
                        //         }
                        //     }

                        //     form.setFieldsValue({
                        //         cityCode: list,
                        //     });
                        // }}
                    />
                </Col>

                {/* <Col span={8}>
                    <ActiveCrowdCheckTree
                        label="活动人群"
                        form={form}
                        mode="single"
                        allowClear
                        valueType="select"
                        showTableSearch
                        rules={[]}
                        disableIds={['user', 'vip', 'notvip', 'expvip', 'effvip', 'custom']}
                    />
                </Col> */}

                <Col span={8}>
                    <FormItem label="投放状态:" name="advertiseState" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {advertiseStateList?.map((ele) => (
                                <Option value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        {/* <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button> */}
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const AdvertisementListPage = (props) => {
    const {
        dispatch,
        history,
        advertisementModel: { advertisementList, advertisementListTotal },
        global: { pageInit },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;
    const isBanner = useMemo(() => {
        if (pathname?.indexOf('/banner/') >= 0) {
            return true;
        }
        return false;
    }, [pathname]);

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: NEW_STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        dispatch({
            type: 'advertisementModel/initOptions',
        });

        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            titleName: data.titleName || '',
            advertiseState: data.advertiseState || '',
            activitiesPattern: data.activitiesPattern || '',
            activitiesLaunch: data.activitiesLaunch || '',
            effTime: data.effTime?.format('YYYY-MM-DD') || '',
            channel: data.channel || undefined,
            cityCode: data.cityCode?.length ? data.cityCode.join(',') : undefined,
            areaType: data.cityCode?.length ? '0' : undefined, // 根据投放城市来判断是全部还是部分
            activeCrowd: data.activeCrowd || '',
            queryType: isBanner ? '02' : '01', // 查询类型 01：图文广告 02：文本广告
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'advertisementModel/getAdvertisementList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const stopAdvertisementEvent = async (item) => {
        confirm({
            title: `确定停止${item.titleName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await stopAdvertisementApi({ advertiseId: item.advertiseId });
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteAdvertisementEvent = async (item) => {
        confirm({
            title: `确定删除${item.titleName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await deleteAdvertisementApi({ advertiseId: item.advertiseId });
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // //导出
    // const exportFormEvent = () => {
    //     const data = form.getFieldsValue();
    //     const params = {
    //         pageIndex: pageInfo.pageIndex,
    //         pageSize,
    //         actName: data.actName,
    //         actId: data.actId,
    //         actSubType: data.actSubType,
    //         beginDate:
    //             (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
    //         endDate:
    //             (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
    //     };
    //     if (tabType !== NEW_STATUS_TYPES.ALL) {
    //         params.advertiseState = tabType;
    //     }
    //     let columnsStrs = [];
    //     for (const item of columns) {
    //         if (item.dataIndex) {
    //             columnsStrs.push({
    //                 key: item.dataIndex,
    //                 value: item.title,
    //             });
    //         }
    //     }
    //     exportTableByParams({
    //         methodUrl: '/bil/coupon/couponPutList',
    //         options: params,
    //         columnsStr: columnsStrs,
    //     });
    // };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push(`/marketing/advertisement/${isBanner ? 'banner/' : ''}list/add`);
    };

    const editAdvertiseEvent = (item) => {
        history.push(
            `/marketing/advertisement/${isBanner ? 'banner/' : ''}list/update/${item.advertiseId}`,
        );
    };

    const columns = [
        {
            title: '活动标题',
            width: 240,
            render(text, record) {
                return (
                    <div className={advertisementStyles['title-view']} title={record.titleName}>
                        {record.iconUrl ? (
                            <img
                                className={advertisementStyles['title-icon']}
                                src={record.iconUrl}
                            />
                        ) : null}
                        <div>
                            <h1 className={advertisementStyles.title}>{record.titleName}</h1>
                            {record.subTitle ? (
                                <p className={advertisementStyles['sub-title']}>
                                    {record.subTitle}
                                </p>
                            ) : null}
                        </div>
                    </div>
                );
            },
        },
        {
            title: '投放时间段',
            width: 400,
            dataIndex: 'advertiseDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动样式',
            width: 140,
            dataIndex: 'activitiesPatternName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '投放位置',
            width: 180,
            dataIndex: 'activitiesLaunchName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '投放渠道',
            width: 200,
            dataIndex: 'channelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '投放区域',
            width: 140,
            dataIndex: 'putCity',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '人群类型',
            width: 140,
            dataIndex: 'crowdTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '投放人群',
            width: 180,
            dataIndex: 'activeCrowdName',
            render(text, record) {
                return (
                    <span
                        style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '5',
                        }}
                        title={text}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'advertiseStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        // {
        //     title: '展现/点击量',
        //     width: 140,
        //     dataIndex: 'showNum',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                const editBtn = (
                    <span
                        key="edit"
                        className={styles['table-btn']}
                        onClick={() => editAdvertiseEvent(record)}
                    >
                        编辑
                    </span>
                );
                const deleteBtn = (
                    <span
                        key="del"
                        className={styles['table-btn']}
                        onClick={() => deleteAdvertisementEvent(record)}
                    >
                        删除
                    </span>
                );

                const stopBtn = (
                    <span
                        key="stop"
                        className={styles['table-btn']}
                        onClick={() => stopAdvertisementEvent(record)}
                    >
                        停止
                    </span>
                );
                const copyPath = `/marketing/advertisement/${isBanner ? 'banner/' : ''}list/copy/${
                    record.advertiseId
                }`;
                const copyBtn = (
                    <Link to={copyPath} target="_blank">
                        复制
                    </Link>
                );
                btnList.push(editBtn);
                if (
                    record.advertiseState == NEW_STATUS_TYPES.DRAFT ||
                    record.advertiseState == NEW_STATUS_TYPES.NOSTART
                ) {
                    btnList.push(deleteBtn);
                }
                if (record.advertiseState == NEW_STATUS_TYPES.NOSTART) {
                }
                if (record.advertiseState == NEW_STATUS_TYPES.DOING) {
                    btnList.push(stopBtn);
                }
                if (record.advertiseState == NEW_STATUS_TYPES.END) {
                }
                btnList.push(copyBtn);
                return <Space>{btnList}</Space>;
            },
        },
    ];

    return (
        <PageHeaderWrapper title={isBanner ? '文本广告' : '图文广告'}>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    // onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        + 新建
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={advertisementList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: advertisementListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, advertisementModel, loading }) => ({
    global,
    advertisementModel,
    listLoading: loading.effects['advertisementModel/getAdvertisementList'],
}))(AdvertisementListPage);
