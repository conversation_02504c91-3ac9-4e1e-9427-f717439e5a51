import {
    LeftOutlined,
    MinusCircleOutlined,
    PlusOutlined,
    QuestionCircleOutlined,
} from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    message,
    Row,
    DatePicker,
    Radio,
    Checkbox,
    Input,
    InputNumber,
    Spin,
    Select,
    Space,
    Tooltip,
} from 'antd';
import moment from 'moment';
import { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { connect } from 'umi';

import styles from './Advertisement.less';
import { isFullUrl } from '@/utils/verify';
import { saveAdvertisementApi } from '@/services/Marketing/MarketingAdvertisementMngApi';

import CitysSelect from '@/components/CitysSelect/index.js';
import UpLoadImg from '@/components/UpLoadImg/index.js';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import { WEEK_TYPES } from '@/constants/options';
import { CrowdSelectItem, VIP_POLICY_TYPES } from './components/CrowdSelectItem';
import { CROWD_TYPES } from '../Membership/components/PriceCrowdItem';
import { copyObjectCommon } from '@/utils/utils';

const { TextArea } = Input;

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

const LINK_TYPES = {
    NONE: '00',
    ALIPAY: '01',
    WEB: '02',
    APPLET: '03',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 160px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

export const ActivitiesPatternEnum = {
    //腰部广告
    WAIST: '11',
    NAVIGATION_CORNER: '13', // 导航栏角标
    BANNER_VIP: '12', // 会员导购banner
    CAR_AUTH: '14', //车辆认证
};

const textBanner = [ActivitiesPatternEnum.BANNER_VIP, ActivitiesPatternEnum.CAR_AUTH];
const defaultBannerOptions = [
    {
        key: 'firstCopywrite',
        placeholder: '请填写第一行文案',
        args: [],
        help: undefined, // 用于提示误编辑关键字的场景
    },
];
const AdvertisementUpdatePage = (props) => {
    const {
        dispatch,
        history,
        match,
        advertisementModel: { editActInfo, advertiseTypeList, advertiseChannelList, policyList },
        route,
        infoLoading,
    } = props;

    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [linkTypeList, changeLinkTypeList] = useState([]);

    const advertiseId = match.params.advertiseId || null;
    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy') >= 0) {
            return true;
        }
        return false;
    }, [route.path]);
    const isBanner = useMemo(() => {
        if (route.path.indexOf('/banner/') >= 0) {
            return true;
        }
        return false;
    }, [route.path]);

    const iconRef = useRef();

    useEffect(() => {
        const params = {};
        if (advertiseId) {
            params.advertiseId = advertiseId;
        }
        dispatch({
            type: 'advertisementModel/initEditActInfo',
            ...params,
        });
        if (!advertiseTypeList?.length) {
            dispatch({
                type: 'advertisementModel/initOptions',
            });
        }
        if (isBanner && !policyList?.length) {
            dispatch({
                type: 'advertisementModel/initPolicyCopywriteConfig',
            });
        }

        return () => {
            dispatch({
                type: 'advertisementModel/updateEditActInfo',
                info: undefined,
            });
        };
    }, []);

    useEffect(() => {
        if (!editActInfo) {
            return;
        }
        editActInfo.activitiesLaunch =
            (editActInfo?.activitiesLaunch?.split && editActInfo.activitiesLaunch.split(',')) ||
            editActInfo?.activitiesLaunch;
        const {
            effTime,
            expTime,
            citys,
            iconUrl,
            linkTypeList: linkList,
            advertiseType,
            titleName,
            subTitle,
            effWeekFlag,
            effWeek,
            activeCrowdFlag,
            activeCrowd,
            areaType,
            bgColor,
            linkType,
            linkUrl,
            loginFlag,
            sn,
            appId,
            extend,
            channel,
            activitiesPattern,
            putSceneType,
            activitiesLaunch,
            adContent,
            crowdType,
            policyScene,
        } = editActInfo;

        if (linkList) {
            changeLinkTypeList(linkList);
        }
        if (iconUrl) {
            setTimeout(() => {
                iconRef.current?.init(iconUrl);
            }, 0);
        }

        const extendData = [];
        if (extend) {
            const extendInfo = JSON.parse(extend);
            for (const key in extendInfo) {
                if (extendInfo.hasOwnProperty(key)) {
                    const element = extendInfo[key];
                    extendData.push({
                        name: key,
                        value: element,
                    });
                }
            }
        }
        let activeCrowdList = [];
        if (activeCrowd) {
            activeCrowdList = activeCrowd.split(',');
        }
        let channelvalue = [];
        if (channel) {
            channelvalue = channel.split(',');
        }

        const params = {
            advertiseType,
            titleName,
            subTitle,
            effWeekFlag: effWeekFlag ?? '0',
            effWeek: effWeek?.split(','),
            areaType,
            bgColor,
            linkType,
            linkUrl,
            loginFlag,
            sn,
            appId,
            dateTime: [effTime ? moment(effTime) : null, expTime ? moment(expTime) : null],
            extend: extendData,
            channel: channelvalue,
            activeCrowdFlag,
            activeCrowd: activeCrowdList,
            activitiesPattern: isBanner
                ? activitiesPattern || ActivitiesPatternEnum.BANNER_VIP
                : activitiesPattern || '01',
            putSceneType,
            activitiesLaunch,
            iconUrl,
            adContent,
            citys,
            crowdType,
            policyScene,
        };
        if (editActInfo.crowdType == CROWD_TYPES.ALL) {
            params.policyScene = editActInfo.policyScene;
        } else if (editActInfo.crowdType == CROWD_TYPES.CDP) {
            params.crowd_CDP_pic_list = editActInfo.custLabelList;
        } else if (editActInfo.crowdType == CROWD_TYPES.CDP_CROWD) {
            params.crowd_CDP_group_list = editActInfo.custLabelList;
        }
        if (textBanner.includes(editActInfo.activitiesPattern)) {
            params.disPriceFlag = editActInfo.disPriceFlag || '0';
            params.firstCopywrite = editActInfo.firstCopywrite;
            params.sencodCopywrite = editActInfo.sencodCopywrite;
            params.btnCopywrite = editActInfo.btnCopywrite;
        }

        form.setFieldsValue(params);
    }, [editActInfo]);

    // 投放样式
    const advertiseTypeOptions = useMemo(() => {
        let options = [];

        advertiseTypeList.forEach((ele) => {
            if (ele.activitiesPattern == '05' && !advertiseId) {
                // 编辑时，单独开放运营位配置
            } else {
                if (isBanner && !textBanner.includes(ele.activitiesPattern)) {
                } else if (!isBanner && textBanner.includes(ele.activitiesPattern)) {
                } else {
                    options.push(
                        <Radio
                            value={ele.activitiesPattern}
                            key={ele.activitiesPattern}
                            disabled={ele.activitiesPattern == '05'}
                        >
                            {ele.activitiesPatternName}
                        </Radio>,
                    );
                }
            }
        });
        return options;
    }, [advertiseTypeList, advertiseId]);

    // 投放位置
    const advertiseSubTypeOptions = (type) => {
        let options = [];
        advertiseTypeList?.forEach((ele) => {
            if (ele.activitiesPattern == type) {
                ele.activitiesLaunchList?.forEach((subEle) => {
                    options.push({
                        value: subEle.codeValue,
                        label: subEle.codeName,
                    });
                });
            }
        });
        return options;
    };

    const policyScene = Form.useWatch('policyScene', form);
    const crowdType = Form.useWatch('crowdType', form);
    const activitiesPattern = Form.useWatch('activitiesPattern', form);
    const [bannerOptions, updateBannerOptions] = useState([
        ...defaultBannerOptions,
        {
            key: 'sencodCopywrite',
            placeholder: '请填写第二行文案',
            args: [],
            help: undefined,
        },
    ]);

    const activitiesLaunch = Form.useWatch('activitiesLaunch', form);
    const channel = Form.useWatch('channel', form);
    const launchTip = useMemo(() => {
        if (
            channel?.includes('01') &&
            activitiesLaunch?.includes('0801') &&
            activitiesPattern == '08'
        ) {
            return 'APP首页不生效浮窗效果，与AI小助手位置冲突';
        }
        return null;
    }, [channel, activitiesLaunch, activitiesPattern]);

    useEffect(() => {
        if (!isBanner) {
            return;
        }
        // if (activitiesPattern === ActivitiesPatternEnum.CAR_AUTH) {
        //     updateBannerOptions(defaultBannerOptions);
        //     return;
        // }
        if (policyScene?.length && policyList?.length && crowdType == CROWD_TYPES.ALL) {
            const policyObj = policyList.find((ele) => ele.policyScene == policyScene) || {};
            for (const item of bannerOptions) {
                item.args = copyObjectCommon(policyObj.vipPolicyCopywriteConfigQueryVos) || [];
            }
            updateBannerOptions([...bannerOptions]);
        } else if (crowdType != CROWD_TYPES.ALL) {
            for (const item of bannerOptions) {
                item.args = [];
            }
            updateBannerOptions([...bannerOptions]);
        }
    }, [policyList, policyScene, crowdType, isBanner, activitiesPattern]);

    const clearBannerInfo = () => {
        if (!isBanner) {
            return;
        }
        // 一旦变动，刷掉已填写的文案
        const params = {};
        for (const item of bannerOptions) {
            params[item.key] = undefined;
        }
        form.setFieldsValue(params);
    };

    const formatBannerOptions = useMemo(() => {
        if (activitiesPattern === ActivitiesPatternEnum.CAR_AUTH) {
            return bannerOptions.filter((ele) => ele.key == 'firstCopywrite');
        }
        return bannerOptions;
    }, [bannerOptions, activitiesPattern]);

    const bannerItems = (
        <FormItem
            name="front_end_banner"
            label="banner文案"
            {...formItemLayout}
            required
            rules={[
                ({ getFieldValue }) => ({
                    validator(rule, value) {
                        const policyScene = getFieldValue('policyScene');
                        let hasContent = false;
                        const policySceneItem = policyList.find(
                            (argEle) => argEle.policyScene == policyScene,
                        );
                        const argInfo =
                            (policySceneItem &&
                                policySceneItem.vipPolicyCopywriteConfigQueryVos?.find(
                                    (argEle) => argEle.referParamField == 'recovery_text',
                                )) ||
                            undefined;
                        for (const item of formatBannerOptions) {
                            const { key } = item;
                            const content = getFieldValue(key);
                            if (content?.length) {
                                hasContent = true;

                                if (argInfo && content.indexOf(argInfo.referParam) >= 0) {
                                    if (
                                        activitiesLaunch.some(
                                            (ele) => !['1203', '1204'].includes(ele),
                                        )
                                    ) {
                                        return Promise.reject(
                                            `${argInfo.referParam}仅可配置投放在下单页和充电中`,
                                        );
                                    }
                                }
                            }
                        }

                        if (!hasContent) {
                            return Promise.reject('至少任选以上一行填写文案');
                        }
                        return Promise.resolve();
                    },
                }),
            ]}
        >
            <Space direction="vertical" style={{ width: '100%' }}>
                {formatBannerOptions.map((ele, index) => {
                    return (
                        <Row gutter={12} key={index}>
                            <Col span={12}>
                                <FormItem
                                    name={ele.key}
                                    noStyle={ele.help == undefined}
                                    validateStatus={ele.help ? 'warning' : undefined}
                                    help={ele.help}
                                >
                                    <Input
                                        id={`front_end_banner_${index}`}
                                        maxLength={ele.args?.length ? 50 : 30}
                                        showCount={ele.args?.length == 0}
                                        placeholder={ele.placeholder}
                                        autoComplete="off"
                                        style={{
                                            width: '100%',
                                        }}
                                        onBlur={(e) => {
                                            const content = e.target.value;
                                            const targetEle = bannerOptions[index];
                                            if (content?.length) {
                                                const res = new RegExp(
                                                    /{[\u4E00-\u9FA5A-Za-z0-9\s]*}/g,
                                                );
                                                const matches = content.match(res) || [];
                                                const helper = [];
                                                for (const char of matches) {
                                                    const c = char.substring(1, char.length - 1);

                                                    if (
                                                        !targetEle.args.some(
                                                            (argEle) => argEle.referParam == c,
                                                        )
                                                    ) {
                                                        helper.push(char);
                                                    }
                                                }
                                                if (helper?.length) {
                                                    targetEle.help = `文本未匹配参数：${helper.join(
                                                        '、',
                                                    )}，提交前请注意辨别`;
                                                } else {
                                                    targetEle.help = undefined;
                                                }
                                            } else {
                                                targetEle.help = undefined;
                                            }
                                            updateBannerOptions([...bannerOptions]);
                                        }}
                                    />
                                </FormItem>
                            </Col>
                            {(activitiesPattern !== ActivitiesPatternEnum.CAR_AUTH &&
                                ele.args?.length && (
                                    <Col>
                                        <div
                                            style={{
                                                height: '32px',
                                                display: 'flex',
                                                alignContent: 'center',
                                                flexWrap: 'wrap',
                                            }}
                                        >
                                            引用参数：
                                            {ele.args?.map((arguEle, arguIndex) => {
                                                return (
                                                    <div key={arguIndex}>
                                                        <a
                                                            id={`front_end_banner_${index}_`} // 传参用，避免onClick取不到index属性
                                                            onClick={(e) => {
                                                                let key = e.target.id;
                                                                key = key.substring(
                                                                    0,
                                                                    key.length - 1,
                                                                );
                                                                const element =
                                                                    document.getElementById(key);
                                                                const value =
                                                                    form.getFieldValue(ele.key) ||
                                                                    '';
                                                                const selectionStart =
                                                                    element.selectionStart;
                                                                form.setFieldsValue({
                                                                    [ele.key]:
                                                                        value.slice(
                                                                            0,
                                                                            selectionStart,
                                                                        ) +
                                                                        `{${arguEle.referParam}}` +
                                                                        value.slice(selectionStart),
                                                                });
                                                                element.focus();
                                                                // focus() 异步，所以加了 setTimeout
                                                                setTimeout(() => {
                                                                    const nextSelection =
                                                                        selectionStart +
                                                                        arguEle.referParam?.length +
                                                                        2;
                                                                    element.setSelectionRange(
                                                                        selectionStart,
                                                                        nextSelection,
                                                                    );
                                                                }, 0);
                                                                // 为防止手抖，加个禁用后2秒延时开放的交互
                                                                arguEle.disabled = true;
                                                                updateBannerOptions([
                                                                    ...bannerOptions,
                                                                ]);
                                                                setTimeout(() => {
                                                                    arguEle.disabled = false;
                                                                    updateBannerOptions([
                                                                        ...bannerOptions,
                                                                    ]);
                                                                }, 2000);
                                                            }}
                                                            disabled={arguEle.disabled}
                                                        >
                                                            [{arguEle.referParam}]
                                                        </a>
                                                        {arguEle.referParamField ==
                                                            'dis_copy_show' && (
                                                            <Tooltip
                                                                title={
                                                                    <div>
                                                                        <div>示例：</div>
                                                                        <div>
                                                                            ①您有10元会员月卡抵扣券，23:59后失效
                                                                        </div>
                                                                        <div>
                                                                            ②您有10元会员月卡抵扣券，限时使用
                                                                        </div>
                                                                        <div>
                                                                            ③限时特价，每月9.9元
                                                                        </div>
                                                                    </div>
                                                                }
                                                            >
                                                                <QuestionCircleOutlined />
                                                            </Tooltip>
                                                        )}

                                                        {arguEle.referParamField ==
                                                            'recovery_text' && (
                                                            <Tooltip
                                                                title={
                                                                    <div>
                                                                        <div>
                                                                            {`1. 文案示例：会员1.5400/度，本单充20度立即回本`}
                                                                        </div>
                                                                        <div>
                                                                            {`2. 场站享受会员价&场站不限高或者场站享受会员价&场站限高时&开通金额<=限高金额，设定40度的回本阈值，充Y度=会员开通金额/(普通用户充电活动价 - 会员充电专享价 )`}
                                                                        </div>
                                                                        <div>
                                                                            {`  ①当Y度<=40度&场站享受会员价时，文案展示为：会员1.5400/度，本单充Y度立即回本`}
                                                                        </div>
                                                                        <div>
                                                                            {`  ②当Y度>40度时&场站享受会员价时，文案展示为：开通会员享1.5400/度`}
                                                                        </div>
                                                                        <div>
                                                                            {`3. 场站不享受会员价时，文案展示逻辑：按照人群标签取默认文案`}
                                                                        </div>
                                                                    </div>
                                                                }
                                                            >
                                                                <QuestionCircleOutlined />
                                                            </Tooltip>
                                                        )}
                                                        {arguIndex == ele.args.length - 1
                                                            ? ''
                                                            : `，`}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </Col>
                                )) ||
                                null}
                        </Row>
                    );
                })}
            </Space>
        </FormItem>
    );

    const cornerLinkTypeName = (type) => {
        switch (type) {
            case VIP_POLICY_TYPES.TRY:
                return '体验会员页';
                break;
            case VIP_POLICY_TYPES.OPEN:
                return '开通会员页';
                break;
            case VIP_POLICY_TYPES.UPDATE:
                return '升级会员页';
                break;
            case VIP_POLICY_TYPES.CONTINUE:
            case VIP_POLICY_TYPES.OVERDUE:
                return '续费会员页';
                break;

            default:
                break;
        }
        return '';
    };

    const cornerButtonName = (type) => {
        switch (type) {
            case VIP_POLICY_TYPES.OVERDUE:
            case VIP_POLICY_TYPES.OPEN:
                return '开通';
            case VIP_POLICY_TYPES.CONTINUE:
                return '续费';

            default:
                break;
        }
        return '';
    };

    const isUrl = (str) => {
        const v = new RegExp(
            '^(?!mailto:)(?:(?:http|https|ftp|alipays)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
            'i',
        );
        return v.test(str);
    };

    /**
     * 保存
     */
    const saveAdvertisementEvent = async (values) => {
        if (submitLoading) {
            return;
        }
        try {
            const params = {
                activitiesPattern: values.activitiesPattern,
                activitiesLaunch:
                    (values.activitiesLaunch?.join && values.activitiesLaunch.join(',')) ||
                    values.activitiesLaunch ||
                    '',
                titleName: values.titleName,
                subTitle: values.subTitle,
                effWeekFlag: values.effWeekFlag,
                effWeek: values.effWeekFlag === '1' ? values.effWeek?.join(',') : undefined,
                activeCrowdFlag: values.activeCrowdFlag,
                activeCrowd: values.activeCrowd && values.activeCrowd.join(','),
                areaType: values.areaType,
                iconUrl: values.iconUrl || '',
                bgColor: values.bgColor,
                linkType: values.linkType,
                sn: values.sn,
                appId: values.appId || '',
                advertiseNo: editActInfo?.advertiseNo,
                advertiseState: '02',
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                channel: values.channel && values.channel.join(','),
                adContent: values.adContent,
                //仅腰部广告有这个字段
                putSceneType:
                    values.activitiesPattern === ActivitiesPatternEnum.WAIST
                        ? values.putSceneType
                        : undefined,
                crowdType: values.crowdType,
            };

            if (values.activitiesPattern === ActivitiesPatternEnum.WAIST && false) {
                params.linkUrl = '/pagesPaymember/paymember/flashkill';
            } else {
                let formatLinkUrl = values.linkUrl?.replace(/[\r\n\s]/g, '');
                if (params.linkType == LINK_TYPES.WEB) {
                    if (isUrl(formatLinkUrl)) {
                        params.linkUrl = formatLinkUrl || '';
                    } else if (!/^\//.test(formatLinkUrl)) {
                        params.linkUrl = `/${formatLinkUrl || ''}`;
                    } else {
                        params.linkUrl = formatLinkUrl || '';
                    }
                    params.loginFlag = values.loginFlag;
                } else if (params.linkType == LINK_TYPES.APPLET) {
                    if (/^\//g.test(formatLinkUrl)) {
                        params.linkUrl = formatLinkUrl.replace(/^[\/]*/g, '');
                    } else {
                        params.linkUrl = formatLinkUrl || '';
                    }
                } else {
                    params.linkUrl = formatLinkUrl || '';
                }

                if (values.crowdType == CROWD_TYPES.ALL) {
                    params.policyScene = values.policyScene;
                } else if (values.crowdType == CROWD_TYPES.CDP) {
                    params.custLabelList = values.crowd_CDP_pic_list;
                } else if (values.crowdType == CROWD_TYPES.CDP_CROWD) {
                    params.custLabelList = values.crowd_CDP_group_list;
                }
                if (textBanner.includes(values.activitiesPattern)) {
                    params.disPriceFlag = values.disPriceFlag || '0';
                    let firstCopywrite = values.firstCopywrite;
                    let sencodCopywrite = values.sencodCopywrite;
                    // bannerOptions[0].args?.map((ele) => {
                    //     firstCopywrite =
                    //         firstCopywrite?.replace(ele.referParam, ele.referParamField) || '';
                    //     sencodCopywrite =
                    //         sencodCopywrite?.replace(ele.referParam, ele.referParamField) || '';
                    // });
                    params.firstCopywrite = firstCopywrite;
                    params.sencodCopywrite = sencodCopywrite;
                    params.btnCopywrite = values.btnCopywrite;

                    if (params.disPriceFlag == '1') {
                        params.btnCopywrite = `￥9.9${cornerButtonName(policyScene)}`;
                    }
                }
            }

            if (advertiseId && !isCopy) {
                params.advertiseId = advertiseId;
            }

            if (values.citys) {
                params.citys = values.citys;
            }
            if (values.extend) {
                const extend = {};
                for (const item of values.extend) {
                    extend[item.name] = item.value;
                }
                params.extend = JSON.stringify(extend);
            }

            await saveAdvertisementApi(params);

            message.success('提交成功');
            goBack();
        } catch (error) {
            console.log(666666, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const goBack = () => {
        history.replace(`/marketing/advertisement/${isBanner ? 'banner/' : ''}list`);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Spin spinning={infoLoading || false}>
                    <Form
                        form={form}
                        initialValues={{
                            linkType: '00',
                            areaType: '1',
                            channel: advertiseChannelList?.map((ele) => ele.codeValue),
                            activitiesPattern: isBanner ? ActivitiesPatternEnum.BANNER_VIP : '01',
                            effWeekFlag: '0',
                        }}
                        onFinish={saveAdvertisementEvent}
                        scrollToFirstError
                    >
                        <div className={styles.formTitle}>基础信息</div>
                        <FormItem
                            name="titleName"
                            label="主标题"
                            {...formItemFixedWidthLayout}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value || value.length == 0) {
                                            return Promise.reject('请输入');
                                        }

                                        const wordReg = /(?=·|~|`|\^|\s|&|\||\\|\[|\]|{|})/g;
                                        if (wordReg.test(value)) {
                                            return Promise.reject('请检查是否有特殊字符');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            required
                        >
                            <Input
                                maxLength={15}
                                placeholder="请输入主标题,最多15个字符"
                                autoComplete="off"
                            />
                        </FormItem>

                        <FormItem
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.activitiesPattern !== curValues.activitiesPattern
                            }
                        >
                            {({ getFieldValue }) => {
                                const activitiesPattern = getFieldValue('activitiesPattern');
                                // activitiesPattern=02 是站点列表公告，如果是列表公告，那副标题 长度不要限制，然后下面的icon 图片不必填
                                return (
                                    <FormItem
                                        name="subTitle"
                                        label="副标题"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value || value.length == 0) {
                                                        return Promise.reject('请输入');
                                                    }

                                                    const wordReg =
                                                        /(?=·|~|`|\^|\s|&|\||\\|\[|\]|{|})/g;
                                                    if (wordReg.test(value)) {
                                                        return Promise.reject(
                                                            '请检查是否有特殊字符',
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        required
                                        {...formItemFixedWidthLayout}
                                    >
                                        <Input
                                            maxLength={activitiesPattern == '02' ? 50 : 15}
                                            placeholder={`请输入副标题,最多15个字符`}
                                            autoComplete="off"
                                        />
                                    </FormItem>
                                );
                            }}
                        </FormItem>

                        <FormItem
                            label="投放时段:"
                            name="dateTime"
                            {...formItemLayout}
                            rules={[
                                { required: true, message: '请选择投放时段' },
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择投放开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择投放结束日期');
                                        }
                                        if (value[1]) {
                                            const nowTime = +new Date();
                                            const sendEndTime = +new Date(value[1]);

                                            if (sendEndTime < nowTime) {
                                                return Promise.reject(
                                                    '投放结束日期不能早于当前时间',
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            {...formItemFixedWidthLayout}
                        >
                            <RangePicker
                                showTime={{
                                    format: 'HH:mm:ss',
                                    defaultValue: [
                                        moment('00:00:00', 'HH:mm:ss'),
                                        moment('23:59:59', 'HH:mm:ss'),
                                    ],
                                }}
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD HH:mm:ss"
                            />
                        </FormItem>
                        <FormItem
                            label="是否限制生效星期:"
                            name="effWeekFlag"
                            {...formItemLayout}
                            {...formItemFixedWidthLayout}
                            required
                        >
                            <Radio.Group
                                options={[
                                    {
                                        label: '否',
                                        value: '0',
                                    },
                                    {
                                        label: '是',
                                        value: '1',
                                    },
                                ]}
                            />
                        </FormItem>
                        <FormItem
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.effWeekFlag !== curValues.effWeekFlag
                            }
                        >
                            {({ getFieldValue }) => {
                                const effWeekFlag = getFieldValue('effWeekFlag');
                                return (
                                    effWeekFlag === '1' && (
                                        <FormItem
                                            label="生效星期:"
                                            name="effWeek"
                                            {...formItemLayout}
                                            {...formItemFixedWidthLayout}
                                            required
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择生效星期',
                                                },
                                            ]}
                                        >
                                            <Checkbox.Group options={WEEK_TYPES} />
                                        </FormItem>
                                    )
                                );
                            }}
                        </FormItem>
                        <CheckBoxGroup
                            label="投放渠道"
                            name={'channel'}
                            form={form}
                            selectList={advertiseChannelList}
                            required
                            wrapperCol={{ span: 8 }}
                            rules={[{ required: true, message: '请选择投放渠道' }]}
                            valueType="select"
                            {...formItemLayout}
                        ></CheckBoxGroup>
                        {isBanner && (
                            <FormItem
                                name="activitiesPattern"
                                label="投放样式"
                                rules={[{ required: true, message: '请选择' }]}
                                {...formItemFixedWidthLayout}
                            >
                                <Select placeholder="请选择">{advertiseTypeOptions}</Select>
                            </FormItem>
                        )}

                        <CrowdSelectItem
                            applyType={isBanner ? '02' : '03'}
                            form={form}
                            formItemLayout={formItemLayout}
                            formItemFixedWidthLayout={formItemFixedWidthLayout}
                            defaultCheckedAll={editActInfo?.activeCrowdFlag == '1'}
                            hasAll={isBanner}
                            isEdit={props?.location?.pathname?.indexOf('/update/') >= 0}
                            isCopy={isCopy}
                            changeCrowdTypeCallback={clearBannerInfo}
                            changePolicySceneCallback={clearBannerInfo}
                        />

                        <div className={styles.formTitle}>展示信息</div>

                        {!isBanner && (
                            <FormItem
                                name="activitiesPattern"
                                label="投放样式"
                                rules={[{ required: true, message: '请选择' }]}
                                {...formItemLayout}
                            >
                                <Radio.Group
                                    onChange={(e) => {
                                        const values = {
                                            activitiesLaunch: undefined,
                                            adContent: undefined,
                                            iconUrl: undefined,
                                        };
                                        iconRef?.current?.rest();
                                        if (e.target.value == '02' || e.target.value == '09') {
                                            values.linkType = LINK_TYPES.NONE;
                                        }
                                        const launchTypes = advertiseSubTypeOptions(e.target.value);
                                        if (launchTypes?.length == 1) {
                                            values.activitiesLaunch = launchTypes[0].value;
                                        }
                                        form.setFieldsValue(values);
                                    }}
                                >
                                    {advertiseTypeOptions}
                                </Radio.Group>
                            </FormItem>
                        )}

                        <FormItem
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.activitiesPattern !== curValues.activitiesPattern ||
                                prevValues.policyScene !== curValues.policyScene ||
                                prevValues.disPriceFlag !== curValues.disPriceFlag ||
                                prevValues.crowdType !== curValues.crowdType
                            }
                        >
                            {({ getFieldValue }) => {
                                const activitiesPattern = getFieldValue('activitiesPattern');
                                const policyScene = getFieldValue('policyScene');
                                const crowdType = getFieldValue('crowdType');
                                const disPriceFlag = getFieldValue('disPriceFlag');
                                // activitiesPattern=02 是站点列表公告，如果是列表公告，那副标题 长度不要限制，然后下面的icon 图片不必填
                                // 构造限制条件
                                const sizeInfo = {
                                    size: 200, // 尺寸
                                    suggest: true,
                                };

                                let acceptType;
                                switch (activitiesPattern) {
                                    case '06':
                                        // 大banner
                                        sizeInfo.width = 704;
                                        sizeInfo.height = 172;
                                        break;
                                    case '05':
                                        // 运营位
                                        break;
                                    case '04':
                                        // 小banner
                                        sizeInfo.width = 690;
                                        sizeInfo.height = 200;
                                        break;
                                    case '03':
                                        // icon
                                        sizeInfo.width = 120;
                                        sizeInfo.height = 120;
                                        break;
                                    case '02':
                                        // 公告
                                        break;
                                    case '01':
                                        // 弹窗
                                        sizeInfo.width = 600;
                                        sizeInfo.height = 800;
                                        sizeInfo.size = 200;
                                        acceptType = ['png', 'jpg', 'jpeg', 'gif'];
                                        break;

                                    case '07':
                                        // 金刚区
                                        sizeInfo.width = 94;
                                        sizeInfo.height = 94;
                                        acceptType = ['png', 'jpg', 'jpeg', 'gif'];
                                        break;
                                    case '08':
                                        // 浮窗
                                        sizeInfo.width = 130;
                                        sizeInfo.height = 130;
                                        acceptType = ['png', 'jpg', 'jpeg', 'gif'];
                                        break;
                                    case '09':
                                        // 文字链
                                        break;
                                    case '10':
                                        // 场站banner
                                        sizeInfo.width = 702;
                                        sizeInfo.height = 144;
                                        break;
                                    case ActivitiesPatternEnum.WAIST:
                                        // 腰部-会员限时秒杀
                                        sizeInfo.width = 721;
                                        sizeInfo.height = 151;
                                        break;
                                    case ActivitiesPatternEnum.NAVIGATION_CORNER:
                                        // 导航栏角标
                                        sizeInfo.width = 76;
                                        sizeInfo.height = 28;
                                        acceptType = ['png', 'jpg', 'jpeg', 'gif'];
                                        sizeInfo.size = 200;
                                        break;
                                    default:
                                        break;
                                }

                                return (
                                    <>
                                        {activitiesPattern === ActivitiesPatternEnum.WAIST && (
                                            <FormItem
                                                name="putSceneType"
                                                label="投放场景"
                                                rules={[{ required: true, message: '请选择' }]}
                                                {...formItemLayout}
                                            >
                                                <Radio.Group
                                                    options={[
                                                        {
                                                            label: '限时秒杀',
                                                            value: '01',
                                                        },
                                                        {
                                                            label: '会员日',
                                                            value: '02',
                                                        },
                                                    ]}
                                                    onChange={(event) => {
                                                        const {
                                                            target: { value },
                                                        } = event;
                                                        if (value === '02') {
                                                            form.setFieldsValue({
                                                                crowdType: CROWD_TYPES.XDT,
                                                                activeCrowdFlag: '1',
                                                            });
                                                        }
                                                    }}
                                                />
                                            </FormItem>
                                        )}
                                        <FormItem
                                            name="activitiesLaunch"
                                            label="投放位置"
                                            rules={[{ required: true, message: '请选择' }]}
                                            {...formItemLayout}
                                            validateStatus={launchTip ? 'error' : 'success'}
                                            help={launchTip}
                                        >
                                            <Checkbox.Group
                                                options={advertiseSubTypeOptions(activitiesPattern)}
                                            />
                                        </FormItem>

                                        <FormItem
                                            name="areaType"
                                            label="投放范围"
                                            rules={[{ required: true, message: '请选择' }]}
                                            {...formItemFixedWidthLayout}
                                        >
                                            <Radio.Group>
                                                <Radio value="1">全平台</Radio>
                                                <Radio value="0">选择区域</Radio>
                                            </Radio.Group>
                                        </FormItem>
                                        <FormItem
                                            noStyle
                                            shouldUpdate={(prevValues, curValues) =>
                                                prevValues.areaType !== curValues.areaType
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                const areaType = getFieldValue('areaType');

                                                return areaType === '0' ? (
                                                    <CitysSelect
                                                        // treeDefaultExpandAll={true}
                                                        name="citys"
                                                        treeDataSimpleMode={false}
                                                        loadData={null}
                                                        formItemLayout={formItemFixedWidthLayout}
                                                        form={form}
                                                        provinceSelectable
                                                    />
                                                ) : null;
                                            }}
                                        </FormItem>

                                        {/* 公告/文字链无图片 */}
                                        {activitiesPattern == '02' ||
                                        activitiesPattern == '09' ||
                                        textBanner.includes(activitiesPattern) ? undefined : (
                                            <UpLoadImg
                                                form={form}
                                                ref={iconRef}
                                                formItemLayout={formItemLayout}
                                                label="icon图片"
                                                name="iconUrl"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请上传图片',
                                                    },
                                                ]}
                                                uploadData={{
                                                    contentType: '02',
                                                    contRemrk: 'advertise',
                                                    relaTable: 'advertise',
                                                }}
                                                sizeInfo={sizeInfo}
                                                acceptType={acceptType}
                                                required
                                            />
                                        )}

                                        {(textBanner.includes(activitiesPattern) && bannerItems) ||
                                            null}

                                        {(activitiesPattern === ActivitiesPatternEnum.BANNER_VIP &&
                                            crowdType == CROWD_TYPES.ALL &&
                                            (policyScene == VIP_POLICY_TYPES.OPEN ||
                                                policyScene == VIP_POLICY_TYPES.CONTINUE ||
                                                policyScene == VIP_POLICY_TYPES.OVERDUE) && (
                                                <FormItem
                                                    name="disPriceFlag"
                                                    label="按钮是否展示会员方案价格"
                                                    rules={[{ required: true, message: '请选择' }]}
                                                    // {...formItemLayout}
                                                    initialValue={'0'}
                                                >
                                                    <Radio.Group>
                                                        <Radio value="0">否</Radio>
                                                        <Radio value="1">是</Radio>
                                                    </Radio.Group>
                                                </FormItem>
                                            )) ||
                                            null}

                                        {(textBanner.includes(activitiesPattern) && (
                                            <FormItem
                                                name="btnCopywrite"
                                                label="按钮文案"
                                                {...formItemFixedWidthLayout}
                                                rules={[
                                                    () => ({
                                                        validator(rule, value) {
                                                            if (
                                                                (policyScene ==
                                                                    VIP_POLICY_TYPES.OPEN ||
                                                                    policyScene ==
                                                                        VIP_POLICY_TYPES.CONTINUE ||
                                                                    policyScene ==
                                                                        VIP_POLICY_TYPES.OVERDUE) &&
                                                                disPriceFlag == '1'
                                                            ) {
                                                                return Promise.resolve();
                                                            }
                                                            if (!value || value.length == 0) {
                                                                return Promise.reject('请输入');
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                required
                                            >
                                                {(policyScene == VIP_POLICY_TYPES.OPEN ||
                                                    policyScene == VIP_POLICY_TYPES.CONTINUE ||
                                                    policyScene == VIP_POLICY_TYPES.OVERDUE) &&
                                                disPriceFlag == '1' ? (
                                                    `￥9.9${cornerButtonName(policyScene)}`
                                                ) : (
                                                    <Input
                                                        maxLength={6}
                                                        placeholder="请填写按钮文案，最多6个字"
                                                        autoComplete="off"
                                                        showCount
                                                    />
                                                )}
                                            </FormItem>
                                        )) ||
                                            null}

                                        {/* 金刚区有标签文案 */}
                                        {(activitiesPattern == '07' && (
                                            <FormItem
                                                name="adContent"
                                                label="标签文案"
                                                {...formItemFixedWidthLayout}
                                            >
                                                <Input
                                                    maxLength={4}
                                                    placeholder="请填写标签文案，最多4个字"
                                                    autoComplete="off"
                                                    showCount
                                                />
                                            </FormItem>
                                        )) ||
                                            null}

                                        {/* 文字链有文案内容 */}
                                        {(activitiesPattern == '09' && (
                                            <FormItem
                                                name="adContent"
                                                label="文案内容"
                                                {...formItemFixedWidthLayout}
                                                rules={[{ required: true, message: '请输入' }]}
                                            >
                                                <Input
                                                    maxLength={20}
                                                    placeholder="请填写标签文案，最多20个字"
                                                    autoComplete="off"
                                                    showCount
                                                />
                                            </FormItem>
                                        )) ||
                                            null}

                                        {/* 公告类型有文案内容 */}
                                        {(activitiesPattern == '02' && (
                                            <FormItem
                                                name="adContent"
                                                label="文案内容"
                                                {...formItemFixedWidthLayout}
                                                rules={[{ required: true, message: '请输入' }]}
                                            >
                                                <TextArea
                                                    maxLength={100}
                                                    placeholder="请输入文案，最多100字"
                                                    autoComplete="off"
                                                    showCount
                                                />
                                            </FormItem>
                                        )) ||
                                            null}

                                        {/* 导航栏角标类型无连接 */}
                                        {(activitiesPattern !==
                                            ActivitiesPatternEnum.NAVIGATION_CORNER &&
                                            activitiesPattern !== ActivitiesPatternEnum.CAR_AUTH &&
                                            (activitiesPattern ==
                                                ActivitiesPatternEnum.BANNER_VIP &&
                                            crowdType == CROWD_TYPES.ALL ? (
                                                <FormItem
                                                    label="跳转链接"
                                                    required
                                                    {...formItemLayout}
                                                >
                                                    {cornerLinkTypeName(policyScene)}
                                                </FormItem>
                                            ) : (
                                                <FormItem
                                                    name="linkType"
                                                    label="跳转类型"
                                                    rules={[{ required: true, message: '请选择' }]}
                                                    {...formItemLayout}
                                                >
                                                    <Radio.Group>
                                                        <Radio value={LINK_TYPES.NONE}>
                                                            无链接
                                                        </Radio>
                                                        {activitiesPattern != '02' &&
                                                            linkTypeList.map((ele) => {
                                                                // 公告类型仅支持无连接
                                                                return (
                                                                    <Radio
                                                                        key={ele.codeValue}
                                                                        value={ele.codeValue}
                                                                    >
                                                                        {ele.codeName}
                                                                    </Radio>
                                                                );
                                                            })}
                                                    </Radio.Group>
                                                </FormItem>
                                            ))) ||
                                            null}
                                    </>
                                );
                            }}
                        </FormItem>

                        <FormItem
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.linkType !== curValues.linkType ||
                                prevValues.crowdType !== curValues.crowdType ||
                                prevValues.activitiesPattern !== curValues.activitiesPattern
                            }
                        >
                            {({ getFieldValue }) => {
                                const linkType = getFieldValue('linkType');
                                const crowdType = getFieldValue('crowdType');
                                if (crowdType == CROWD_TYPES.ALL) {
                                    return null;
                                }

                                const activitiesPattern = getFieldValue('activitiesPattern');
                                if (
                                    activitiesPattern == '02' ||
                                    activitiesPattern == ActivitiesPatternEnum.NAVIGATION_CORNER
                                ) {
                                    return null;
                                }

                                if (linkType == LINK_TYPES.APPLET) {
                                    return (
                                        <Fragment>
                                            <FormItem
                                                name="appId"
                                                label="小程序appId"
                                                rules={[{ required: true, message: '请填写' }]}
                                                {...formItemFixedWidthLayout}
                                            >
                                                <Input placeholder="请输入" autoComplete="off" />
                                            </FormItem>
                                            <FormItem
                                                name="linkUrl"
                                                label="小程序页面地址"
                                                {...formItemFixedWidthLayout}
                                                rules={[
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            if (!value || value.length == 0) {
                                                                return Promise.resolve();
                                                            }
                                                            if (value.length < 3) {
                                                                return Promise.reject(
                                                                    '请检查链接长度',
                                                                );
                                                            }

                                                            // 校验H5链接地址
                                                            const h5Reg =
                                                                /^[\/]?([A-Za-z\/]+)(?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)(#\/)?(?:[\w]*)?$/g;
                                                            if (
                                                                !h5Reg.test(value) &&
                                                                !isFullUrl(value)
                                                            ) {
                                                                return Promise.reject(
                                                                    '链接内容有误',
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <Input placeholder="请输入" autoComplete="off" />
                                            </FormItem>
                                            <FormItem label="例" {...formItemLayout}>
                                                <div
                                                    className="text-line"
                                                    style={{ whiteSpace: 'pre' }}
                                                >
                                                    /pages/index/index (不填默认首页)
                                                </div>
                                            </FormItem>

                                            <FormItem
                                                name="extend"
                                                {...formItemLayout}
                                                {...{
                                                    wrapperCol: {
                                                        span: 16,
                                                    },
                                                }}
                                                label="额外参数"
                                            >
                                                <Form.List name="extend">
                                                    {(fields, { add, remove }) => (
                                                        <Fragment>
                                                            {fields.map((field, index) => {
                                                                return (
                                                                    <Row
                                                                        key={field.key}
                                                                        gutter={{
                                                                            md: 8,
                                                                            lg: 24,
                                                                            xl: 48,
                                                                        }}
                                                                    >
                                                                        <Col flex="1">
                                                                            <FormItem
                                                                                validateTrigger={[
                                                                                    'onChange',
                                                                                ]}
                                                                                label="字段名"
                                                                                name={[
                                                                                    index,
                                                                                    'name',
                                                                                ]}
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        whitespace: true,
                                                                                        message:
                                                                                            '请输入字段名',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Input
                                                                                    placeholder="请输入字段名"
                                                                                    autoComplete="off"
                                                                                />
                                                                            </FormItem>
                                                                        </Col>
                                                                        <Col flex="1">
                                                                            <FormItem
                                                                                validateTrigger={[
                                                                                    'onChange',
                                                                                ]}
                                                                                name={[
                                                                                    index,
                                                                                    'value',
                                                                                ]}
                                                                                label="字段值"
                                                                                rules={[
                                                                                    {
                                                                                        required: true,
                                                                                        whitespace: true,
                                                                                        message:
                                                                                            '请输入字段值',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Input
                                                                                    placeholder="请输入字段值"
                                                                                    autoComplete="off"
                                                                                />
                                                                            </FormItem>
                                                                        </Col>
                                                                        <Col>
                                                                            <MinusCircleOutlined
                                                                                className={
                                                                                    styles[
                                                                                        'dynamic-delete-button'
                                                                                    ]
                                                                                }
                                                                                style={{
                                                                                    margin: '0 8px',
                                                                                }}
                                                                                onClick={() => {
                                                                                    remove(
                                                                                        field.name,
                                                                                    );
                                                                                }}
                                                                            />
                                                                        </Col>
                                                                    </Row>
                                                                );
                                                            })}

                                                            <FormItem label="">
                                                                <Button
                                                                    type="dashed"
                                                                    onClick={() => {
                                                                        add();
                                                                    }}
                                                                    style={{ width: '60%' }}
                                                                >
                                                                    <PlusOutlined />
                                                                    添加参数
                                                                </Button>
                                                            </FormItem>
                                                        </Fragment>
                                                    )}
                                                </Form.List>
                                            </FormItem>
                                        </Fragment>
                                    );
                                }
                                if (linkType == LINK_TYPES.ALIPAY || linkType == LINK_TYPES.WEB) {
                                    let linkMode = '';
                                    if (linkType == LINK_TYPES.WEB) {
                                        linkMode = '/pages/home/<USER>//www.evshine.cn';
                                    } else {
                                        linkMode =
                                            '生活号文章地址https://render.alipay.com/p/s/i/?scheme=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FappId%3D2018121712345678%26page%3Dpages%2Findex%2Findex%26query%3Dchannel%253d1';
                                    }
                                    return (
                                        <Fragment>
                                            {linkType == LINK_TYPES.WEB ? (
                                                <FormItem
                                                    name="loginFlag"
                                                    label="是否登陆"
                                                    {...formItemFixedWidthLayout}
                                                    initialValue="0"
                                                >
                                                    <Radio.Group>
                                                        <Radio value={'0'}>否</Radio>
                                                        <Radio value={'1'}>是</Radio>
                                                    </Radio.Group>
                                                </FormItem>
                                            ) : null}
                                            <FormItem
                                                label="链接地址"
                                                required
                                                name="linkUrl"
                                                {...formItemFixedWidthLayout}
                                                rules={[
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            if (!value) {
                                                                return Promise.reject('请输入');
                                                            }

                                                            if (linkType == LINK_TYPES.ALIPAY) {
                                                                // 校验生活号链接地址
                                                                if (value.length < 6) {
                                                                    return Promise.reject(
                                                                        '请检查链接长度',
                                                                    );
                                                                }
                                                                if (!isFullUrl(value)) {
                                                                    return Promise.reject(
                                                                        '链接内容有误',
                                                                    );
                                                                }
                                                            } else {
                                                                // 校验H5链接地址
                                                                if (value.length < 3) {
                                                                    return Promise.reject(
                                                                        '请检查链接长度',
                                                                    );
                                                                }
                                                                const h5Reg =
                                                                    /^[\/]?([A-Za-z\/]+)(?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)(#\/)?(?:[\w]*)?$/g;
                                                                if (
                                                                    !h5Reg.test(value) &&
                                                                    !isFullUrl(value)
                                                                ) {
                                                                    return Promise.reject(
                                                                        '链接内容有误',
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <Input placeholder="请输入" autoComplete="off" />
                                            </FormItem>
                                            <FormItem label="例" {...formItemLayout}>
                                                <div
                                                    className="text-line"
                                                    style={{ whiteSpace: 'pre' }}
                                                    title={linkMode}
                                                >
                                                    {linkMode}
                                                </div>
                                            </FormItem>
                                        </Fragment>
                                    );
                                }
                            }}
                        </FormItem>

                        <FormItem
                            name="sn"
                            label="投放排序"
                            rules={[{ required: true, message: '请填写' }]}
                            {...formItemFixedWidthLayout}
                        >
                            <InputNumber placeholder="请输入" min={1} precision={0} />
                        </FormItem>
                        <div className={styles['form-submit']}>
                            <Button
                                className={styles['form-btn']}
                                type="primary"
                                loading={submitLoading}
                                htmlType="submit"
                            >
                                提交
                            </Button>

                            <Button className={styles['form-btn']} onClick={goBack}>
                                取消
                            </Button>
                        </div>
                    </Form>
                </Spin>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, advertisementModel, loading }) => ({
    global,
    advertisementModel,
    infoLoading: loading.effects['advertisementModel/initEditActInfo'],
}))(AdvertisementUpdatePage);
