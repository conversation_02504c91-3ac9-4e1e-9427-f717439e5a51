import {
    getAdvertisementListApi,
    getAdvertisementDetailApi,
    getAdvertisementConditionApi,
    getAdvertisementCondition2Api,
    getPolicyCopywriteConfigApi,
} from '@/services/Marketing/MarketingAdvertisementMngApi';

const advertisementModel = {
    namespace: 'advertisementModel',
    state: {
        advertisementList: [], // 翻牌列表
        advertisementListTotal: 0,
        editActInfo: undefined, // 当前详情信息
        advertiseStateList: [], // 投放状态列表
        advertiseTypeList: [], // 投放类型列表
        advertiseChannelList: [], // 投放渠道列表

        policyList: [], // 引用参数数据
    },
    effects: {
        /**
         * 翻牌列表
         */
        *getAdvertisementList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getAdvertisementListApi, options);

                yield put({
                    type: 'updateAdvertisementList',
                    advertisementList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ advertiseId }, { call, put, select }) {
            try {
                const { data } = yield call(getAdvertisementDetailApi, advertiseId);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
        *initPolicyCopywriteConfig({}, { call, put, select }) {
            try {
                const { data } = yield call(getPolicyCopywriteConfigApi);
                yield put({
                    type: 'updateProperty',
                    params: { policyList: data },
                });
            } catch (error) {}
        },
        *initOptions(_, { call, put, select }) {
            try {
                const {
                    data: { advertiseStateList, actChannelList },
                } = yield call(getAdvertisementConditionApi);

                const { data: advertiseTypeList } = yield call(getAdvertisementCondition2Api);

                yield put({
                    type: 'updateProperty',
                    params: {
                        advertiseStateList,
                        advertiseTypeList,
                        advertiseChannelList: actChannelList,
                    },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateAdvertisementList(state, { advertisementList, total }) {
            return {
                ...state,
                advertisementList,
                advertisementListTotal: total,
            };
        },
        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
        updateStateList(state, { list }) {
            return {
                ...state,
                advertiseStateList: list,
            };
        },
        updateTypeList(state, { list }) {
            return {
                ...state,
                advertiseTypeList: list,
            };
        },
    },
};
export default advertisementModel;
