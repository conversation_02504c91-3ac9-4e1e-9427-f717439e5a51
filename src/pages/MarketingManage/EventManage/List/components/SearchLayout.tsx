import { Col, Form, Input, Select } from 'antd';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import { EventSourceOptions } from '../../EventManageEnum';

const FormItem = Form.Item;

const SearchLayout = (props: any) => {
    const { form, onSubmit, onReset, listLoading } = props;

    const onFinish = (values: any) => {
        onSubmit({
            eventName: values?.eventName?.trim(),
            eventCode: values?.eventCode?.trim(),
            eventSource: values?.eventSource,
        });
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };
    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} open minSpan={24}>
                <Col span={6}>
                    <FormItem label="事件名称" name="eventName">
                        <Input placeholder="请填写" autoComplete="off" allowClear maxLength={30} />
                    </FormItem>
                </Col>
                <Col span={6}>
                    <FormItem label="事件标识符" name="eventCode">
                        <Input placeholder="请填写" autoComplete="off" allowClear maxLength={30} />
                    </FormItem>
                </Col>
                <Col span={6}>
                    <FormItem label="事件来源" name="eventSource">
                        <Select
                            placeholder="请选择事件来源"
                            options={EventSourceOptions}
                            allowClear
                        />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
