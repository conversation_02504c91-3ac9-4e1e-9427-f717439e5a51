// 事件管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Popconfirm, Space, Spin, Typography, message, Switch } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { connect, Link } from 'umi';

import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import { usePagination, useRequest } from 'ahooks';
import type { ColumnType } from 'antd/lib/table';
import { queryEventList, eventEditStatusApi } from '@/services/Marketing/EventManageApi';
import moment from 'moment';
import {
    EventStatusEnum,
    TaskBelongTypeEnum,
    TaskBelongSubTypeEnum,
    TaskBelongTypeOptions,
} from '@/constants/eventManage';

const EventManageList = (props: any) => {
    const { dispatch, history, global } = props;
    const { pageInit } = global;
    const [form] = Form.useForm();

    const {
        location: { pathname },
    } = history;
    const cacheName = `${pathname}`;

    const { run, data, loading, pagination } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryEventList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
            cacheKey: cacheName,
        },
    );
    const refreshList = () => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
            run(
                {
                    current: pageInit[cacheName]?.page?.current ?? 1,
                    pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
                },
                pageInit[cacheName].form,
            );
        } else {
            run({
                current: 1,
                pageSize: 10,
            });
        }
    };

    useEffect(() => {
        refreshList();
    }, []);

    const resetData = () => {
        form.resetFields();
        run({
            current: pageInit[cacheName]?.page?.current ?? 1,
            pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
        });
    };

    const searchData = (formData: any) => {
        const params = { ...formData };
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: formData,
                page: { current: 1, pageSize: pagination?.pageSize },
            },
        });
        run({ current: 1, pageSize: pagination?.pageSize }, params);
    };

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (params) => {
            return eventEditStatusApi({ ...params });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    const confirmChangeStatus = (eventId: number, eventCode: string, eventState: string) => {
        const params = {
            eventId,
            eventCode,
            eventState,
        };
        changeStatusRequest(params);
    };

    const columns: ColumnType<API.EventListVo>[] = [
        {
            title: '事件标识符',
            dataIndex: 'eventCode',
            width: 120,
        },
        {
            title: '事件名称',
            dataIndex: 'eventName',
            width: 150,
        },
        {
            title: '事件数据来源',
            dataIndex: 'eventSourceName',
            width: 140,
        },
        {
            title: '说明',
            dataIndex: 'eventRemarks',
            width: 180,
        },
        {
            title: '事件归属',
            dataIndex: 'eventRemarks',
            width: 120,
            render(value: string, record: API.EventListVo) {
                return TaskBelongTypeOptions?.map((item: { label: string; value: string }) => {
                    if (record.belongType === item.value) {
                        return item.label;
                    } else if (record.belongSubType === item.value) {
                        return item.label;
                    } else {
                        return '';
                    }
                });
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            width: 150,
            render: (value: string) => {
                return (value && moment(value).format('YYYY-MM-DD HH:mm:ss')) || '-';
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
        },
        {
            title: '状态',
            dataIndex: 'eventState',
            width: 100,
            render(value: string, record: API.EventListVo) {
                if (record.createdBy === 'SYSADMIN') {
                    return (
                        (record?.eventStateName && (
                            <text style={{ color: '#81DE8A' }}>{record?.eventStateName}</text>
                        )) ||
                        '-'
                    );
                } else {
                    return (
                        <Switch
                            checked={value === EventStatusEnum.ENABLED}
                            checkedChildren="启动"
                            unCheckedChildren="关闭"
                            loading={changeStatusLoading}
                            onChange={() => {
                                confirmChangeStatus(
                                    record?.eventId as number,
                                    record?.eventCode as string,
                                    value === EventStatusEnum.ENABLED
                                        ? EventStatusEnum.DISABLED
                                        : EventStatusEnum.ENABLED,
                                );
                            }}
                        />
                    );
                }
            },
        },
        {
            title: '操作',
            width: 160,
            dataIndex: 'eventId',
            fixed: 'right',
            render: (text: string, record: API.EventListVo) => {
                if (record.createdBy === 'SYSADMIN') {
                    // 系统默认事件不可编辑
                    return <Space></Space>;
                } else {
                    return (
                        <Space>
                            <Link to={`/eventcenter/eventmanage/list/record?id=${text}&mode=EDIT`}>
                                编辑事件
                            </Link>
                            <Link
                                to={`/eventcenter/eventmanage/list/record?eventCode=${record?.eventCode}&pageType=params`}
                            >
                                配置参数
                            </Link>
                        </Space>
                    );
                }
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    onSubmit={searchData}
                    onReset={resetData}
                    listLoading={loading}
                />
                <Space size="large" style={{ marginBottom: '12px' }}>
                    <Button type="primary">
                        <Link to={`/eventcenter/eventmanage/list/record?mode=ADD`}>新增事件</Link>
                    </Button>
                    <Button type="primary">
                        <Link to={`/eventcenter/eventmanage/common/param`}>公共参数管理</Link>
                    </Button>
                </Space>
                <br></br>
                <TablePro
                    loading={loading}
                    scroll={{ x: 'max-content' }}
                    rowKey="eventId"
                    dataSource={data?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        dispatch({
                            type: 'global/setPageInit',
                            pathname,
                            info: {
                                form: pageInit[pathname]?.form,
                                page: { current: pages?.current, pageSize: pages?.pageSize },
                            },
                        });
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
}))(EventManageList);
