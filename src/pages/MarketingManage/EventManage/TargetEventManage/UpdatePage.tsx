// 目标事件管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { LeftOutlined } from '@ant-design/icons';

import {
    Button,
    Card,
    Form,
    Popconfirm,
    Space,
    Spin,
    Typography,
    message,
    Input,
    Select,
    TreeSelect,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { connect, Link, history, useRouteMatch } from 'umi';

import { useRequest } from 'ahooks';
import {
    getAllActTypeApi,
    getTargetEventDetailApi,
    submitTargetEventDetailApi,
    getEquityDownApi,
} from '@/services/Marketing/EventManageApi';
import commonStyles from '@/assets/styles/common.less';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    wrapperCol: {
        span: 8,
    },
    labelWrap: true,
};

const TargetEventUpdatePage = (props: any) => {
    const {
        dispatch,
        global: { codeInfo = {} },
    } = props;
    const { params = {} } = useRouteMatch();

    const { targetId, look } = params as any;

    const [form] = Form.useForm();
    const {
        location: { pathname },
    } = history;

    const {
        run: runEquityDown,
        data: equityDown,
        loading: equityDownLoading,
    } = useRequest(
        async () => {
            const { data } = await getEquityDownApi();
            return data;
        },
        {
            manual: true,
        },
    );
    const {
        run,
        data: eventInfo,
        loading,
    } = useRequest(
        async (id: string) => {
            const { data } = await getTargetEventDetailApi(id);
            // if (!data?.rewardSubType && data?.rewardType) {
            //     data.rewardSubType = data.rewardType;
            // }
            return data;
        },
        {
            manual: true,
        },
    );

    const { run: submit, loading: submitLoading } = useRequest(
        async (params?: any) => {
            const { data } = await submitTargetEventDetailApi({
                ...params,
            });
            return data;
        },
        {
            manual: true,
        },
    );

    const { data: actType } = useRequest(async () => {
        const {
            data: { list },
        } = await getAllActTypeApi();
        return list;
    });

    useEffect(() => {
        runEquityDown();
        if (targetId) {
            initData(targetId);
        }
    }, []);

    useEffect(() => {
        if (eventInfo) {
            form.setFieldsValue(eventInfo);
        }
    }, [eventInfo]);

    const initData = (id: string) => {
        run(id);
    };

    const onFinish = async (values: any) => {
        try {
            const params = { ...values };
            if (targetId) {
                params.targetId = targetId;
            }
            params.targetState = '01';
            // const rewardSubType = params.rewardSubType;
            // // 捞一遍目标标识的层级关系
            // actType?.map((ele: any) => {
            //     if (ele.codeValue == rewardSubType) {
            //         params.rewardType = ele.codeValue;
            //         delete params.rewardSubType;
            //     } else if (ele.children?.length) {
            //         ele.children?.map((childrenEle: any) => {
            //             if (childrenEle.codeValue == rewardSubType) {
            //                 params.rewardType = ele.codeValue;
            //             }
            //         });
            //     }
            // });
            await submit(params);
            message.success('保存成功');
            goBack();
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const goBack = () => {
        history.go(-1);
    };
    // 标识类型仅保留发奖活动
    const actTypeOptions: {
        value?: string;
        title?: string;
    }[] = [
        {
            value: '1410',
            title: '发奖活动',
        },
        {
            value: '61',
            title: '发放权益',
        },
    ];
    // const actTypeOptions = useMemo(() => {
    //     const loop = (
    //         list: {
    //             codeValue?: string;
    //             codeName?: string;
    //             children?: any;
    //             selectable: boolean;
    //         }[],
    //     ) => {
    //         return list?.map((ele) => {
    //             if (ele.children?.length) {
    //                 ele.selectable = false;
    //                 ele.children = loop(ele.children);
    //             } else {
    //                 ele.selectable = true;
    //             }
    //             return {
    //                 ...ele,
    //                 value: ele.codeValue,
    //                 title: ele.codeName,
    //             };
    //         });
    //     };
    //     return loop(actType) || [];
    // }, [actType]);

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {look ? '查看' : targetId ? '编辑' : '新增'}目标事件
                </div>
            }
        >
            <Card loading={loading}>
                <div className={commonStyles['form-title']}>添加目标事件</div>

                <Form
                    form={form}
                    initialValues={{ targetType: '01' }}
                    onFinish={onFinish}
                    {...formItemLayout}
                >
                    <Form.Item noStyle name="rewardType"></Form.Item>
                    <Form.Item noStyle name="rewardSubType"></Form.Item>
                    <Form.Item noStyle name="relaFlag"></Form.Item>
                    <Form.Item noStyle name="imageFlag"></Form.Item>
                    <Form.Item noStyle name="rewardUnit"></Form.Item>
                    <Form.Item noStyle name="equityUnitName"></Form.Item>
                    <Form.Item noStyle name="targetSendType"></Form.Item>
                    <Form.Item noStyle name="targetUrl"></Form.Item>
                    <Form.Item
                        label="目标事件名称"
                        name="targetName"
                        rules={[
                            {
                                required: true,
                                message: '请填写目标事件名称',
                            },
                        ]}
                    >
                        <Input
                            disabled={look}
                            placeholder="请填写目标事件名称"
                            maxLength={20}
                            autoComplete="off"
                            showCount
                        ></Input>
                    </Form.Item>
                    <Form.Item
                        label="目标事件用途"
                        name="targetType"
                        rules={[
                            {
                                required: true,
                                message: '请选择目标事件用途',
                            },
                        ]}
                    >
                        <Select
                            disabled={look}
                            placeholder="请选择目标事件用途"
                            onChange={() => {
                                form.setFieldsValue({
                                    equityCode: undefined,
                                    rewardId: undefined,
                                    rewardValue: undefined,
                                    targetImg: undefined,
                                    rewardType: undefined,
                                    rewardSubType: undefined,
                                    relaFlag: undefined,
                                    imageFlag: undefined,
                                    rewardUnit: undefined,
                                    equityUnitName: undefined,
                                    targetUrl: undefined,
                                    targetSendType: undefined,
                                });
                            }}
                        >
                            <Select.Option value="01">触发活动发奖</Select.Option>
                            <Select.Option value="02" disabled>
                                触发告警
                            </Select.Option>
                            <Select.Option value="03">触发页面跳转</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(pre, after) =>
                            pre.targetType !== after.targetType ||
                            pre.relaFlag !== after.relaFlag ||
                            pre.imageFlag !== after.imageFlag ||
                            pre.equityUnitName !== after.equityUnitName ||
                            pre.targetUrl !== after.targetUrl
                        }
                    >
                        {({ getFieldValue }) => {
                            const targetType = getFieldValue('targetType');
                            const relaFlag = getFieldValue('relaFlag');
                            const imageFlag = getFieldValue('imageFlag');
                            const equityUnitName = getFieldValue('equityUnitName');
                            const targetUrl = getFieldValue('targetUrl');
                            if (targetType === '01') {
                                return (
                                    <>
                                        <Form.Item label="事件内容" required>
                                            <Form.Item
                                                name="equityCode"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择权益类型',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    disabled={look}
                                                    placeholder="请选择权益类型"
                                                    allowClear
                                                    onChange={(value: any) => {
                                                        const equityDownItem = equityDown?.find(
                                                            (item: any) =>
                                                                item.equityCode === value,
                                                        );
                                                        form.setFieldsValue({
                                                            rewardType: equityDownItem?.relaType,
                                                            rewardSubType:
                                                                equityDownItem?.relaSubType,
                                                            relaFlag: equityDownItem?.relaFlag,
                                                            imageFlag: equityDownItem?.imageFlag,
                                                            rewardUnit: equityDownItem?.equityUnit,
                                                            equityUnitName:
                                                                equityDownItem?.equityUnitName,
                                                            targetUrl: equityDownItem?.equityUrl,
                                                            targetSendType:
                                                                equityDownItem?.equitySendType,
                                                        });
                                                    }}
                                                >
                                                    {equityDown?.map((item: any, index: number) => (
                                                        <Select.Option
                                                            key={index}
                                                            value={item?.equityCode}
                                                        >
                                                            {item?.equityName}
                                                        </Select.Option>
                                                    ))}
                                                </Select>
                                                {/* <TreeSelect
                                                disabled={look}
                                                showSearch
                                                style={{ width: '100%' }}
                                                placeholder="请选择权益类型"
                                                allowClear
                                                treeDefaultExpandAll
                                                treeData={actTypeOptions}
                                            /> */}
                                            </Form.Item>
                                            {relaFlag === '1' && (
                                                <Form.Item
                                                    label="权益ID"
                                                    style={{ width: '100%' }}
                                                    name="rewardId"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请填写权益ID',
                                                        },
                                                    ]}
                                                >
                                                    <Input
                                                        disabled={look}
                                                        style={{ width: '100%' }}
                                                        placeholder="请填写权益ID"
                                                        autoComplete="off"
                                                    ></Input>
                                                </Form.Item>
                                            )}
                                            {equityUnitName && (
                                                <Space style={{ width: '100%' }} align="baseline">
                                                    <Form.Item
                                                        style={{ width: '100%' }}
                                                        label="发放单位"
                                                        name="rewardValue"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message: '请填写发放单位',
                                                            },
                                                        ]}
                                                    >
                                                        <Input
                                                            disabled={look}
                                                            style={{ width: '100%' }}
                                                            placeholder="请填写发放单位"
                                                            autoComplete="off"
                                                        ></Input>
                                                    </Form.Item>
                                                    <text>{equityUnitName || ''}</text>
                                                </Space>
                                            )}
                                            {imageFlag === '1' && (
                                                <Form.Item
                                                    label={'配置图片'}
                                                    name="targetImg"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请选择配置图片',
                                                        },
                                                    ]}
                                                >
                                                    <UpLoadImgItem
                                                        disabled={look}
                                                        uploadData={{
                                                            contentType: '02',
                                                            contRemrk: 'ev_target',
                                                            relaTable: 'eventCenterTargetImgUrl',
                                                        }}
                                                        sizeInfo={{
                                                            size: 1024 * 5,
                                                            // width: 800,
                                                            // height: 600,
                                                        }}
                                                    ></UpLoadImgItem>
                                                </Form.Item>
                                            )}
                                        </Form.Item>
                                        {targetUrl && (
                                            <Form.Item
                                                label="配置Topic"
                                                rules={[
                                                    {
                                                        required: true,
                                                    },
                                                ]}
                                            >
                                                {targetUrl || ''}
                                            </Form.Item>
                                        )}
                                    </>
                                );
                            } else if (targetType === '02') {
                                return (
                                    <Form.Item
                                        label="事件内容"
                                        name="rewardId"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请填写告警ID',
                                            },
                                        ]}
                                    >
                                        <Input
                                            disabled={look}
                                            style={{ width: '100%' }}
                                            placeholder="请填写告警ID"
                                            autoComplete="off"
                                        ></Input>
                                    </Form.Item>
                                );
                            } else if (targetType === '03') {
                                return (
                                    <Form.Item
                                        label="事件内容"
                                        name="rewardId"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请填写跳转的内部URL',
                                            },
                                        ]}
                                    >
                                        <Input
                                            disabled={look}
                                            style={{ width: '100%' }}
                                            placeholder="请填写跳转的内部URL"
                                            autoComplete="off"
                                        ></Input>
                                    </Form.Item>
                                );
                            }
                            return;
                        }}
                    </Form.Item>
                    <Form.Item
                        label="目标事件描述"
                        name="targetRemarks"
                        rules={[
                            {
                                required: true,
                                message: '请填写目标事件描述',
                            },
                        ]}
                    >
                        <TextArea
                            disabled={look}
                            placeholder="请填写目标事件描述"
                            rows={6}
                            autoComplete="off"
                            maxLength={1000}
                            showCount
                        ></TextArea>
                    </Form.Item>
                    <Space
                        align="end"
                        className="mg-t-20"
                        style={{ display: 'flex', justifyContent: 'center' }}
                    >
                        <Button onClick={goBack}>取消</Button>
                        {!look && (
                            <Button
                                loading={submitLoading}
                                type="primary"
                                onClick={() => {
                                    form?.submit();
                                }}
                            >
                                提交
                            </Button>
                        )}
                    </Space>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
}))(TargetEventUpdatePage);
