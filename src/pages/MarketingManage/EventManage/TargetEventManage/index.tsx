// 目标事件管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Popconfirm, Space, Spin, Typography, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { connect, Link } from 'umi';

import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import { usePagination, useRequest } from 'ahooks';
import type { ColumnType } from 'antd/lib/table';
import { queryTargetEventList } from '@/services/Marketing/EventManageApi';
import moment from 'moment';
import { formatActivePage } from '@/utils/utils';

const TargetEventManageList = (props: any) => {
    const { dispatch, history, global } = props;
    const { pageInit } = global;
    const [form] = Form.useForm();
    const {
        location: { pathname },
    } = history;
    const cacheName = `${pathname}`;

    const { run, data, loading, pagination } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryTargetEventList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
            cacheKey: cacheName,
        },
    );

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
            run(
                {
                    current: pageInit[cacheName]?.page?.current ?? 1,
                    pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
                },
                pageInit[cacheName].form,
            );
        } else {
            run({
                current: 1,
                pageSize: 10,
            });
        }
    }, []);

    const resetData = () => {
        form.resetFields();
        run({
            current: pageInit[cacheName]?.page?.current ?? 1,
            pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
        });
    };

    const searchData = (formData: any) => {
        const params = { ...formData };
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: formData,
                page: { current: 1, pageSize: pagination?.pageSize },
            },
        });
        run({ current: 1, pageSize: pagination?.pageSize }, params);
    };

    const columns: ColumnType<API.CustOrderReplaceQueryVo>[] = [
        {
            title: '目标事件ID',
            dataIndex: 'targetId',
            width: 120,
        },
        {
            title: '目标事件名称',
            dataIndex: 'targetName',
            width: 180,
        },
        {
            title: '目标事件用途',
            dataIndex: 'targetTypeName',
            width: 140,
        },
        {
            title: '目标事件描述',
            dataIndex: 'targetRemarks',
            width: 180,
        },
        {
            title: '配置Topic',
            dataIndex: 'targetUrl',
            width: 180,
        },
        {
            title: '关联ID',
            dataIndex: 'rewardId',
            width: 180,
            render(text: string, record: any) {
                const type = record.rewardType;
                const actVersion = record.actVersion || 2;
                const actSubType = record.rewardSubType || 1410;
                const params: any = {
                    type,
                    actId: text,
                    parentActFlag: undefined,
                    actVersion,
                    actSubType,
                };
                const path = formatActivePage(params);
                return (
                    (text && type && (
                        <Link to={path} target="_blank">
                            {record?.rewardDesc}
                        </Link>
                    )) ||
                    record?.rewardDesc ||
                    '-'
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            width: 180,
            render: (value: string) => {
                return (value && moment(value).format('YYYY-MM-DD HH:mm:ss')) || '-';
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
        },
        {
            title: '状态',
            dataIndex: 'targetStateName',
            width: 120,
            render(text: string) {
                return (text && <text style={{ color: '#81DE8A' }}>{text}</text>) || '-';
            },
        },
        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Space>
                        <Typography.Link
                            href="javascript:viod(0)"
                            onClick={() => goEditPage(record)}
                        >
                            编辑
                        </Typography.Link>
                    </Space>
                );
            },
        },
    ];

    const goEditPage = (record: any) => {
        history.push(`/eventcenter/eventmanage/target/list/update/${record.targetId}`);
    };
    const addTragetEvent = () => {
        history.push(`/eventcenter/eventmanage/target/list/add`);
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    onSubmit={searchData}
                    onReset={resetData}
                    listLoading={loading}
                />
                <Button style={{ marginBottom: '20px' }} onClick={addTragetEvent} type="primary">
                    添加目标事件
                </Button>
                <br></br>
                <TablePro
                    loading={loading}
                    scroll={{ x: 'max-content' }}
                    rowKey="targetId"
                    dataSource={data?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        dispatch({
                            type: 'global/setPageInit',
                            pathname,
                            info: {
                                form: pageInit[pathname]?.form,
                                page: { current: pages?.current, pageSize: pages?.pageSize },
                            },
                        });
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    offsetHeader={false}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
}))(TargetEventManageList);
