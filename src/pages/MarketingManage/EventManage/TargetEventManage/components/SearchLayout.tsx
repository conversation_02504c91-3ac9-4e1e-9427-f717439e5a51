import { Col, Form, Input, Select } from 'antd';

import SearchOptionsBar from '@/components/SearchOptionsBar';

const FormItem = Form.Item;

const SearchLayout = (props: any) => {
    const { form, onSubmit, onReset, listLoading } = props;

    const onFinish = (values: any) => {
        onSubmit({
            targetName: values?.targetName?.trim(),
            targetId: values?.targetId?.trim(),
        });
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };
    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} open minSpan={24}>
                <Col span={6}>
                    <FormItem label="目标事件名称" name="targetName">
                        <Input
                            placeholder="请填写目标事件名称"
                            autoComplete="off"
                            allowClear
                            maxLength={30}
                        />
                    </FormItem>
                </Col>
                <Col span={6}>
                    <FormItem label="目标事件ID" name="targetId">
                        <Input
                            placeholder="请填写目标事件ID"
                            autoComplete="off"
                            allowClear
                            maxLength={30}
                        />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
