import { Modal, Form, Space, Button, Row, Radio, Input, Divider, message, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

import { useRequest } from 'ahooks';
import { ruleSaveApi } from '@/services/Marketing/EventManageApi';

const { TextArea } = Input;
const { Option } = Select;
const EditForm: React.FC<{
    visible: boolean;
    eventCode?: string;
    onCancel: (refresh: boolean) => void;
    mode: 'ADD' | 'EDIT' | 'COPY';
    initValues?: any;
}> = ({ visible, eventCode, onCancel, mode, initValues }) => {
    const [form] = Form.useForm();

    useEffect(() => {
        if (visible) {
            form.setFieldsValue({ ...initValues });
        }
    }, [visible, mode, initValues, form]);

    const cancel = (refresh: boolean = false) => {
        form.resetFields();
        onCancel(refresh);
    };

    const { run: updateReq, loading: updateLoading } = useRequest(
        (params) => {
            return ruleSaveApi(params);
        },
        {
            manual: true,
            onSuccess: (result) => {
                if (result?.ret === 200) {
                    message.success('操作成功');
                    cancel(true);
                } else {
                    message.error(result?.msg || '操作失败');
                }
            },
        },
    );
    const chatGroupChannel = Form.useWatch('chatGroupChannel', form);
    const submit = (formData: any) => {
        const params: API.RuleSaveParamsVo = {
            ...formData,
            ruleType: '02',
            eventCode,
        };
        if (mode === 'EDIT') {
            params.ruleId = initValues.ruleId;
        } else {
            params.ruleState = '01';
        }
        console.log(params);
        updateReq(params);
    };
    return (
        <>
            <Modal
                visible={visible}
                onCancel={() => {
                    cancel(false);
                }}
                title={mode === 'EDIT' ? '编辑参数' : '新增参数'}
                footer={null}
                destroyOnClose
                zIndex={99}
            >
                <Form form={form} onFinish={submit} wrapperCol={{ span: 16 }}>
                    <Form.Item
                        label="参数标识符"
                        name="ruleCode"
                        required
                        rules={[
                            {
                                required: true,
                                message: '请填写参数标识符',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                    >
                        <Input
                            placeholder="请填写参数标识符"
                            allowClear
                            disabled={mode === 'EDIT'}
                        />
                    </Form.Item>
                    <Form.Item
                        label="参数名称"
                        name="ruleName"
                        required
                        rules={[
                            {
                                required: true,
                                message: '请填写参数名称',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                    >
                        <Input placeholder="请填写参数名称" allowClear />
                    </Form.Item>
                    <Form.Item
                        label="参数计算格式"
                        name="dataType"
                        required
                        rules={[
                            {
                                required: true,
                                message: '请选择参数计算格式',
                                transform: (v) => v?.trim(),
                            },
                        ]}
                    >
                        <Select allowClear placeholder="请选择参数计算格式">
                            <Option value="1">数值</Option>
                            <Option value="2">字符串</Option>
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="ruleRemarks"
                        label="参数描述"
                        rules={[
                            {
                                required: true,
                                whitespace: true,
                                message: '请输入参数描述',
                            },
                        ]}
                    >
                        <TextArea placeholder="请输入参数描述" rows={4} showCount></TextArea>
                    </Form.Item>
                    <Form.Item wrapperCol={{ span: 24 }}>
                        <Row justify="center">
                            <Space>
                                <Button type="primary" htmlType="submit" loading={updateLoading}>
                                    提交
                                </Button>
                                <Button htmlType="reset" onClick={() => cancel(false)}>
                                    取消
                                </Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default React.memo(EditForm);
