// 事件管理-编辑事件
import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Card,
    Descriptions,
    Space,
    Form,
    Input,
    Tabs,
    Radio,
    Row,
    Col,
    Select,
    Switch,
    message,
    Button,
    Typography,
} from 'antd';
import type { ColumnType } from 'antd/lib/table';
import qs from 'qs';
import { history, Link } from 'umi';

import TablePro from '@/components/TablePro';
import {
    queryEventDetail,
    queryRuleList,
    eventSaveApi,
    ruleEditStatusApi,
} from '@/services/Marketing/EventManageApi';
import { useEffect, useState, useMemo } from 'react';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import {
    EventStatusEnum,
    TaskBelongTypeEnum,
    TaskBelongSubTypeEnum,
} from '@/constants/eventManage';
import EditForm from './components/EditForm';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';
const { TabPane } = Tabs;
const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    wrapperCol: {
        span: 8,
    },
    labelWrap: true,
};
const EventManageRecord = () => {
    const [form] = Form.useForm();
    const goBack = () => {
        history.goBack();
    };

    const [tabType, setTabType] = useState<string>('01');
    const [showForm, setShowForm] = useState<boolean>(false);
    const [initValues, setInitValues] = useState<API.RuleListVo>();
    const [formMode, setFormMode] = useState<'ADD' | 'EDIT' | 'COPY'>('ADD'); // 参数新增或编辑

    const { id, pageType, eventCode, mode } =
        qs.parse(history.location.search, { ignoreQueryPrefix: true }) ?? {};

    const {
        run: queryTable,
        data: tableData,
        loading: tableLoading,
        pagination,
    } = usePagination(
        async ({ current, pageSize, type }) => {
            const response = await queryRuleList({
                eventCode: type === '02' ? eventCode : undefined,
                pageSize,
                pageIndex: current,
                ruleType: type || '01',
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const {
        run: queryDetail,
        data: detailData,
        loading: detailLoading,
    } = useRequest(
        (id: string) => {
            return queryEventDetail({ eventId: id });
        },
        {
            manual: true,
        },
    );

    const cancel = (refresh: boolean = false) => {
        form.resetFields();
        goBack();
    };

    useEffect(() => {
        if (detailData) {
            form.setFieldsValue({ ...detailData.data });
        }
    }, [detailData]);

    const { run: updateReq, loading: updateLoading } = useRequest(
        (params) => {
            return eventSaveApi(params);
        },
        {
            manual: true,
            onSuccess: (result) => {
                if (result?.ret === 200) {
                    message.success('操作成功');
                    cancel(true);
                } else {
                    message.error(result?.msg || '操作失败');
                }
            },
        },
    );
    const submit = (formData: any) => {
        const params = {
            ...formData,
        };
        if (mode === 'EDIT') {
            params.eventId = id;
        } else {
            params.eventState = '01';
        }
        console.log(params);
        updateReq(params);
    };

    useEffect(() => {
        if (id) {
            queryDetail(id as string);
        }
    }, [id]);

    useEffect(() => {
        if (eventCode) {
            queryTable({ pageSize: 10, current: 1, type: tabType });
        }
    }, [eventCode]);

    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (params) => {
            return ruleEditStatusApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    queryTable({ pageSize: 10, current: 1, type: tabType });
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );

    const confirmChangeStatus = (ruleId: number, ruleCode: string, ruleState: string) => {
        const params = {
            ruleId,
            ruleCode,
            ruleState,
        };
        changeStatusRequest(params);
    };

    const columns = useMemo(() => {
        const columnsBase: ColumnType<API.RuleListVo>[] = [
            {
                title: '参数标识符',
                dataIndex: 'ruleCode',
                width: 150,
            },
            {
                title: '参数名称',
                dataIndex: 'ruleName',
                width: 150,
            },
            {
                title: '说明',
                dataIndex: 'ruleRemarks',
                width: 180,
            },
            {
                title: '创建时间',
                dataIndex: 'createdTime',
                width: 180,
                render: (value: string) => {
                    return (value && moment(value).format('YYYY-MM-DD HH:mm:ss')) || '-';
                },
            },
            {
                title: '创建人',
                dataIndex: 'createdBy',
                width: 120,
            },
        ];
        let columnsDiff: ColumnType<API.RuleListVo>[] = [];
        if (tabType === '02') {
            columnsDiff = [
                {
                    title: '状态',
                    dataIndex: 'ruleState',
                    width: 100,
                    render(value: string, record: API.RuleListVo) {
                        return (
                            <Switch
                                checked={value === EventStatusEnum.ENABLED}
                                checkedChildren="启动"
                                unCheckedChildren="关闭"
                                loading={changeStatusLoading}
                                onChange={() => {
                                    confirmChangeStatus(
                                        record?.ruleId as number,
                                        record?.ruleCode as string,
                                        value === EventStatusEnum.ENABLED
                                            ? EventStatusEnum.DISABLED
                                            : EventStatusEnum.ENABLED,
                                    );
                                }}
                            />
                        );
                    },
                },
                {
                    title: '操作',
                    width: 120,
                    dataIndex: 'ruleId',
                    fixed: 'right',
                    render: (text: string, record: API.RuleListVo) => {
                        return (
                            <Space>
                                <Typography.Link
                                    onClick={() => {
                                        setShowForm(true);
                                        setFormMode('EDIT');
                                        setInitValues(record);
                                    }}
                                >
                                    编辑
                                </Typography.Link>
                            </Space>
                        );
                    },
                },
            ];
        } else {
            columnsDiff = [
                {
                    title: '状态',
                    dataIndex: 'ruleStateName',
                    width: 120,
                    render(text: string) {
                        return (text && <text style={{ color: '#81DE8A' }}>{text}</text>) || '-';
                    },
                },
            ];
        }
        return [...columnsBase, ...columnsDiff];
    }, [tabType]);

    const changeTabTypeEvent = (type: string) => {
        // changePageInfo({ tabType: type, pageIndex: 1 });
        setTabType(type);

        queryTable({ pageSize: 10, current: 1, type });
    };

    const onModalCancel = (refresh: boolean) => {
        setShowForm(false);
        setFormMode('ADD');
        setInitValues(undefined);
        if (refresh) {
            queryTable({ pageSize: 10, current: 1, type: tabType });
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {pageType === 'params' ? (
                        <text>配置参数</text>
                    ) : (
                        <text>{mode === 'ADD' ? '新增事件' : '编辑事件'}</text>
                    )}
                </div>
            }
            // loading={detailLoading}
        >
            {pageType === 'params' ? (
                <Card>
                    <Tabs defaultActiveKey={'01'} onChange={changeTabTypeEvent} activeKey={tabType}>
                        <TabPane tab="公共参数" key={'01'} />
                        <TabPane tab="自定义参数" key={'02'} />
                    </Tabs>
                    {tabType === '02' && (
                        <>
                            <Space size="large" style={{ marginBottom: '12px' }}>
                                <Button type="primary">
                                    <Typography.Link
                                        onClick={() => {
                                            setShowForm(true);
                                            setFormMode('ADD');
                                            setInitValues(undefined);
                                        }}
                                    >
                                        新增自定义参数
                                    </Typography.Link>
                                </Button>
                            </Space>
                            <br></br>
                        </>
                    )}

                    <TablePro
                        loading={tableLoading}
                        scroll={{ x: 'max-content' }}
                        rowKey="id"
                        dataSource={tableData?.list}
                        columns={columns}
                        onChange={(pages: any) => {
                            pagination.onChange(pages?.current, pages?.pageSize);
                        }}
                        pagination={{
                            current: pagination.current,
                            total: pagination.total,
                            pageSize: pagination.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: number) => `共 ${total || 0} 条`,
                        }}
                        offsetHeader={false}
                    />
                </Card>
            ) : (
                <Card loading={detailLoading}>
                    <div className={commonStyles['form-title']}>基本信息</div>
                    <Form
                        form={form}
                        onFinish={submit}
                        {...formItemLayout}
                        initialValues={{ eventSourceName: '01' }}
                    >
                        <Form.Item
                            label="事件标识符"
                            name="eventCode"
                            rules={[
                                {
                                    required: true,
                                    message: '请填写事件标识符',
                                },
                            ]}
                        >
                            <Input
                                placeholder="请填写事件标识符"
                                maxLength={50}
                                autoComplete="off"
                                showCount
                                disabled={id ? true : false}
                            ></Input>
                        </Form.Item>
                        <Form.Item
                            label="事件名称"
                            name="eventName"
                            rules={[
                                {
                                    required: true,
                                    message: '请填写事件名称',
                                },
                            ]}
                        >
                            <Input
                                placeholder="请填写事件名称"
                                maxLength={20}
                                autoComplete="off"
                                showCount
                            ></Input>
                        </Form.Item>
                        <Form.Item
                            label="数据来源"
                            name="eventSource"
                            rules={[
                                {
                                    required: true,
                                    message: '请选择事件数据来源',
                                },
                            ]}
                        >
                            <Radio.Group disabled={id ? true : false}>
                                <Radio value={'01'}>新电途</Radio>
                                <Radio value={'02'}>小磨盘</Radio>
                                <Radio value={'03'}>第三方</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prev, cur) =>
                                prev.belongType !== cur.belongType ||
                                prev.belongSubType !== cur.belongSubType
                            }
                        >
                            {({ getFieldValue }) => {
                                const belongType = getFieldValue('belongType');
                                const belongSubType = getFieldValue('belongSubType');
                                return (
                                    <Form.Item
                                        label="事件所属方"
                                        name="belongType"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择事件所属方',
                                            },
                                        ]}
                                    >
                                        <Radio.Group disabled={id ? true : false}>
                                            <Radio value={TaskBelongTypeEnum.XDT}>新电途</Radio>
                                            <Radio value={TaskBelongTypeEnum.CAR}>车生态</Radio>
                                            {belongType === TaskBelongTypeEnum.CAR ? (
                                                <Form.Item
                                                    name="belongSubType"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请选择车生态',
                                                        },
                                                    ]}
                                                    style={{
                                                        position: 'relative',
                                                        top: '30px',
                                                        left: '82px',
                                                        width: '500px',
                                                        paddingBottom: '10px',
                                                    }}
                                                >
                                                    <Radio.Group disabled={id ? true : false}>
                                                        <Radio
                                                            value={TaskBelongSubTypeEnum.CAR_PLAT}
                                                        >
                                                            车生态平台使用
                                                        </Radio>
                                                        <Radio
                                                            value={
                                                                TaskBelongSubTypeEnum.CAR_ENTERPRISE
                                                            }
                                                        >
                                                            车生态商户使用
                                                        </Radio>
                                                        {belongSubType ===
                                                        TaskBelongSubTypeEnum.CAR_ENTERPRISE ? (
                                                            <Space
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '0',
                                                                    left: '280px',
                                                                    width: '200px',
                                                                }}
                                                            >
                                                                <EnterpriseSelectItem
                                                                    label="商户"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message: '请选择商户',
                                                                        },
                                                                    ]}
                                                                    form={form}
                                                                    labelAlign={'right'}
                                                                    disabled={id ? true : false}
                                                                />
                                                            </Space>
                                                        ) : null}
                                                    </Radio.Group>
                                                </Form.Item>
                                            ) : null}
                                        </Radio.Group>
                                    </Form.Item>
                                );
                            }}
                        </Form.Item>
                        <Form.Item
                            name="eventRemarks"
                            label="目标事件描述"
                            rules={[
                                {
                                    required: false,
                                    whitespace: true,
                                    message: '请输入目标事件描述',
                                },
                            ]}
                        >
                            <TextArea
                                style={{ width: '600px' }}
                                // defaultValue={detailData?.data?.eventRemarks}
                                placeholder="请输入目标事件描述"
                                maxLength={32}
                                rows={4}
                                showCount
                            ></TextArea>
                        </Form.Item>
                        <Form.Item wrapperCol={{ span: 24 }}>
                            <Row justify="center">
                                <Space>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        loading={updateLoading}
                                    >
                                        提交
                                    </Button>
                                    <Button htmlType="reset" onClick={() => cancel(false)}>
                                        取消
                                    </Button>
                                </Space>
                            </Row>
                        </Form.Item>
                    </Form>
                </Card>
            )}

            <EditForm
                visible={showForm}
                onCancel={onModalCancel}
                mode={formMode}
                initValues={initValues}
                eventCode={eventCode}
            />
        </PageHeaderWrapper>
    );
};

export default EventManageRecord;
