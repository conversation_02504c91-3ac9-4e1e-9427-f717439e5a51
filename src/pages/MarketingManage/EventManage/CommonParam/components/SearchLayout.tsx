import { Col, Form, Input, Select } from 'antd';

import SearchOptionsBar from '@/components/SearchOptionsBar';

const FormItem = Form.Item;

const SearchLayout = (props: any) => {
    const { form, onSubmit, onReset, listLoading } = props;

    const onFinish = (values: any) => {
        onSubmit({
            ruleName: values?.ruleName?.trim(),
            ruleCode: values?.ruleCode?.trim(),
        });
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };
    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} open minSpan={24}>
                <Col span={6}>
                    <FormItem label="参数名称" name="ruleName">
                        <Input placeholder="请填写" autoComplete="off" allowClear maxLength={30} />
                    </FormItem>
                </Col>
                <Col span={6}>
                    <FormItem label="参数标识符" name="ruleCode">
                        <Input placeholder="请填写" autoComplete="off" allowClear maxLength={30} />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
