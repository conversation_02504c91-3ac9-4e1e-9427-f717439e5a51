import { addCustOrderReplace } from '@/services/MngCustApi';
import { useRequest } from 'ahooks';
import { Form, Input, Modal, message } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

const AddModal = (props: any, ref: any) => {
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();
    const { callback } = props;

    useImperativeHandle(ref, () => {
        return {
            show: () => {
                setVisible(true);
            },
        };
    });

    const { run, loading } = useRequest(
        (params: any) => {
            return addCustOrderReplace(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res.ret === 200 && res?.data?.success) {
                    message.success('开通成功');
                    callback && callback();
                    onClose();
                } else if (res?.data?.message) {
                    message.error(res.data.message);
                }
            },
        },
    );

    const onClose = () => {
        form.resetFields();
        setVisible(false);
    };

    const onSubmit = async () => {
        const formData = await form.validateFields();
        run({ mobile: formData?.mobile?.trim() });
    };

    return (
        <Modal
            visible={visible}
            title="开通代充"
            destroyOnClose
            onCancel={onClose}
            onOk={onSubmit}
            okButtonProps={{ loading: loading }}
        >
            <Form form={form} wrapperCol={{ span: 14 }} labelCol={{ span: 6 }}>
                <Form.Item
                    name="mobile"
                    label="选择用户"
                    rules={[
                        { required: true, whitespace: true, message: '请输入用户手机号' },
                        {
                            pattern: /^1[3456789]\d{9}$/,
                            message: '请输入正确的用户手机号',
                        },
                    ]}
                >
                    <Input placeholder="请输入用户手机号" allowClear maxLength={11} showCount />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(AddModal);
