// 事件管理-公共参数管理
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Popconfirm, Space, Spin, Typography, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { connect, Link } from 'umi';

import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import { usePagination, useRequest } from 'ahooks';
import type { ColumnType } from 'antd/lib/table';
import AddModal from './components/AddModal';
import { queryRuleList } from '@/services/Marketing/EventManageApi';
import moment from 'moment';
import { LeftOutlined } from '@ant-design/icons';

const CommonParamList = (props: any) => {
    const { dispatch, history, global } = props;
    const modalRef = useRef<any>();
    const { pageInit } = global;
    const [form] = Form.useForm();
    const [updateId, setUpdateId] = useState<string>('');

    const {
        location: { pathname },
    } = history;
    const cacheName = `${pathname}`;

    const { run, data, loading, pagination, refresh } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryRuleList({
                ...params,
                ruleType: '01', // 规则类型 01公共 02业务
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
            cacheKey: cacheName,
        },
    );

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
            run(
                {
                    current: pageInit[cacheName]?.page?.current ?? 1,
                    pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
                },
                pageInit[cacheName].form,
            );
        } else {
            run({
                current: 1,
                pageSize: 10,
            });
        }
    }, []);

    const resetData = () => {
        form.resetFields();
        run({
            current: pageInit[cacheName]?.page?.current ?? 1,
            pageSize: pageInit[cacheName]?.page?.pageSize ?? 10,
        });
    };

    const searchData = (formData: any) => {
        const params = { ...formData };
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: formData,
                page: { current: 1, pageSize: pagination?.pageSize },
            },
        });
        run({ current: 1, pageSize: pagination?.pageSize }, params);
    };

    const goBack = () => {
        history.goBack();
    };
    const showAdd = () => {
        modalRef.current?.show();
    };

    const columns: ColumnType<API.CustOrderReplaceQueryVo>[] = [
        {
            title: '参数标识符',
            dataIndex: 'ruleCode',
            width: 150,
        },
        {
            title: '参数名称',
            dataIndex: 'ruleName',
            width: 150,
        },
        {
            title: '参数描述',
            dataIndex: 'ruleRemarks',
            width: 180,
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            width: 180,
            render: (value: string) => {
                return (value && moment(value).format('YYYY-MM-DD HH:mm:ss')) || '-';
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
        },
        {
            title: '状态',
            dataIndex: 'ruleStateName',
            width: 120,
            render(text: string) {
                return (text && <text style={{ color: '#81DE8A' }}>{text}</text>) || '-';
            },
        },
        // {
        //     title: '操作',
        //     width: 160,
        //     dataIndex: 'id',
        //     fixed: 'right',
        //     render: (text: string, record: API.CustOrderReplaceQueryVo) => {
        //         return (
        //             <Space>
        //                 <text>编辑</text>
        //             </Space>
        //         );
        //     },
        // },
    ];

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    公共参数管理
                </div>
            }
        >
            <Card>
                <SearchLayout
                    form={form}
                    onSubmit={searchData}
                    onReset={resetData}
                    listLoading={loading}
                />
                <Space direction="vertical" size="large">
                    <Button disabled type="primary">
                        添加公共参数
                    </Button>
                    <TablePro
                        loading={loading}
                        scroll={{ x: 'max-content' }}
                        rowKey="id"
                        dataSource={data?.list}
                        columns={columns}
                        onChange={(pages: any) => {
                            dispatch({
                                type: 'global/setPageInit',
                                pathname,
                                info: {
                                    form: pageInit[pathname]?.form,
                                    page: { current: pages?.current, pageSize: pages?.pageSize },
                                },
                            });
                            pagination.onChange(pages?.current, pages?.pageSize);
                        }}
                        pagination={{
                            current: pagination.current,
                            total: pagination.total,
                            pageSize: pagination.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: number) => `共 ${total || 0} 条`,
                        }}
                        offsetHeader={false}
                    />
                </Space>
                <AddModal ref={modalRef} callback={refresh} />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
}))(CommonParamList);
