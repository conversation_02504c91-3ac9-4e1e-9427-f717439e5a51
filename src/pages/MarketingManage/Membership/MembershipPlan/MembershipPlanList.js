import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Radio,
    Alert,
    Typography,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    stopMembershipPlanApi,
    deleteMembershipPlanApi,
} from '@/services/Marketing/MarketingMembershipApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { VIP_TYPES } from '../MembershipRights/RightsConfig';
import EditPlanMaterialDrawer from './components/EditPlanMaterialDrawer';

const { TabPane } = Tabs;
const { Option } = Select;

const FormItem = Form.Item;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <FormItem label="方案ID" name="actId">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="方案名称" name="actName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="方案类型" name="actSubType">
                        <Select placeholder="请选择" allowClear>
                            <Option key={VIP_TYPES.VIP_2_0} value={VIP_TYPES.VIP_2_0}>
                                付费会员
                            </Option>
                            {/* <Option key={VIP_TYPES.VIP_1_0} value={VIP_TYPES.VIP_1_0}>
                                会员方案1.0
                            </Option> */}
                            <Option key={VIP_TYPES.VIP_TRY} value={VIP_TYPES.VIP_TRY}>
                                体验会员
                            </Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const MembershipPlanList = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {} },
        membershipModel: { membershipPlanList, membershipPlanTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;
    const { actState: actStateList } = codeInfo;

    const [form] = Form.useForm();

    const materialDrawerRef = useRef();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: '-1',
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        if (!actStateList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'actState',
            });
        }

        return () => {
            dispatch({
                type: 'membershipModel/updateMembershipProperty',
                params: {
                    membershipPlanList: [],
                    membershipPlanTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };
                if (pageInfo.tabType !== '-1') {
                    params.actState = pageInfo.tabType;
                }

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'membershipModel/getMembershipPlanList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const actStateOptions = useMemo(() => {
        if (actStateList) {
            return actStateList.map((ele) => {
                if (ele.codeValue == 5) return null;
                return <TabPane tab={ele.codeName} key={ele.codeValue} />;
            });
        }
        return [];
    }, [actStateList]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const changeToState = (item, targetState) => {
        let name = '';
        if (targetState == 3) {
            // 结束
            name = '停止';
        } else if (targetState == 5) {
            // 删除
            name = '删除';
        }

        confirm({
            title: `确定${name}该活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    targetState == 5
                        ? await deleteMembershipPlanApi({
                              actId: item.actId,
                          })
                        : await stopMembershipPlanApi({
                              actId: item.actId,
                          });
                    message.success(`${name}成功`);
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const editEvent = (item) => {
        if (item.actSubType == VIP_TYPES.VIP_1_0) {
            history.push(`/userCenter/membership/plan/list/update/${item.actId}`);
        } else {
            history.push(`/userCenter/membership/plan/list/update-vip/${item.actId}`);
        }
    };

    const lookEvent = (item) => {
        if (item.actSubType == VIP_TYPES.VIP_1_0) {
            history.push(`/userCenter/membership/plan/list/detail/${item.actId}`);
        } else {
            history.push(`/userCenter/membership/plan/list/detail-vip/${item.actId}`);
        }
    };

    const columns = [
        {
            title: '有效日期',
            width: 130,
            dataIndex: 'creTime',
            render(text, record) {
                return (
                    <span title={text} style={{ lineBreak: 'anywhere' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '会员方案名称',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '会员方案ID',
            width: 140,
            dataIndex: 'actId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '方案类型',
            width: 120,
            dataIndex: 'actSubTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '月卡',
            width: 90,
            dataIndex: 'monthCard',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '季卡',
            width: 90,
            dataIndex: 'seasonCard',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '年卡',
            width: 90,
            dataIndex: 'yearCard',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '连续包月',
            width: 120,
            dataIndex: 'monthCards',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '连续包季',
            width: 120,
            dataIndex: 'seasonCards',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '连续包年',
            width: 120,
            dataIndex: 'yearCards',
            render(text, record) {
                if (!text?.length) {
                    text = '-';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            fixed: 'right',
            render: (text, record) => {
                const materialBtn = [VIP_TYPES.VIP_2_0, VIP_TYPES.VIP_TRY].includes(
                    record.actSubType,
                ) && (
                    <Typography.Link
                        onClick={() => {
                            materialDrawerRef?.current?.open(record.actId);
                        }}
                    >
                        配置素材
                    </Typography.Link>
                );
                const deleteBtn = (
                    <span className={styles['table-btn']} onClick={() => changeToState(record, 5)}>
                        删除
                    </span>
                );
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );
                const finishBtn = (
                    <span className={styles['table-btn']} onClick={() => changeToState(record, 3)}>
                        停止
                    </span>
                );
                const checkBtn = (
                    <span className={styles['table-btn']} onClick={() => lookEvent(record)}>
                        查看
                    </span>
                );

                let copyPath = ``;
                if (record.actSubType == VIP_TYPES.VIP_1_0) {
                    copyPath = `/userCenter/membership/plan/list/copy/${record.actId}`;
                } else {
                    copyPath = `/userCenter/membership/plan/list/copy-vip/${record.actId}`;
                }
                const copyBtn = (
                    <Link to={copyPath} target="_blank">
                        复制
                    </Link>
                );

                let btns = [];
                if (record.actState == 0) {
                    // 草稿
                    btns.push(materialBtn);
                    btns.push(editBtn);
                    btns.push(deleteBtn);
                } else if (record.actState == 1) {
                    // 待开始
                    btns.push(materialBtn);

                    btns.push(editBtn);
                    btns.push(finishBtn);
                    // btns.push(deleteBtn);
                } else if (record.actState == 2) {
                    // 进行中
                    btns.push(materialBtn);

                    btns.push(checkBtn);
                    btns.push(editBtn);
                    btns.push(finishBtn);
                } else {
                    // 已结束
                    btns.push(checkBtn);
                }
                btns.push(copyBtn);
                return <Space>{btns}</Space>;
            },
        },
    ];

    const [createVisible, updateVisible] = useState(false);
    const [createType, updateCreateType] = useState(VIP_TYPES.VIP_2_0);
    // 跳转新增页面
    const gotoAddEvent = () => {
        if (createType == VIP_TYPES.VIP_1_0) {
            history.push('/userCenter/membership/plan/list/add');
        } else {
            history.push('/userCenter/membership/plan/list/add-vip', { actSubType: createType });
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <p>
                    <Alert
                        message={
                            <Space>
                                配置会员方案和会员方案权益
                                <Typography.Link
                                    onClick={() => {
                                        window?.open(
                                            'https://alidocs.dingtalk.com/i/nodes/ZgpG2NdyVXrGGozXiQaAPNLo8MwvDqPk?utm_scene=person_space',
                                        );
                                    }}
                                >
                                    查看详情
                                </Typography.Link>
                            </Space>
                        }
                    ></Alert>
                </p>
                <SearchLayout
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={() => updateVisible(true)}>
                        新建
                    </Button>
                </div>
                <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key="-1" />
                    {actStateOptions}
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={membershipPlanList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: membershipPlanTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>

            <Modal
                title="选择会员方案类型"
                visible={createVisible}
                width={260}
                onOk={gotoAddEvent}
                onCancel={() => updateVisible(false)}
                centered
            >
                <div>
                    <Radio.Group
                        onChange={(e) => {
                            updateCreateType(e.target.value);
                        }}
                        value={createType}
                    >
                        <Space direction="vertical">
                            <Radio value={VIP_TYPES.VIP_2_0}>付费会员</Radio>
                            {/* <Radio value={VIP_TYPES.VIP_1_0}>会员方案1.0</Radio> */}
                            <Radio value={VIP_TYPES.VIP_TRY}>体验会员</Radio>
                        </Space>
                    </Radio.Group>
                </div>
            </Modal>
            <EditPlanMaterialDrawer
                ref={materialDrawerRef}
                onConfirm={() => {
                    searchData();
                }}
            ></EditPlanMaterialDrawer>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, membershipModel, loading }) => ({
    global,
    membershipModel,
    listLoading: loading.effects['membershipModel/getMembershipPlanList'],
}))(MembershipPlanList);
