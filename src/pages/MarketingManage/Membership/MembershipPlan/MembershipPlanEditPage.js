import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import { Card } from 'antd';

import { LeftOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout';

import { isEmpty } from '@/utils/utils';
import { VIP_TYPES, RIGHTS_TYPES } from '../MembershipRights/RightsConfig';

import PlanForm from './components/PlanForm';

const MembershipPlanEditPage = (props) => {
    const {
        dispatch,
        history,
        route,
        match,
        membershipModel: { membershipPlanInfo },
    } = props;

    const actId = match.params.id || null;

    const actSubType = props?.location?.state?.actSubType;

    const subTypeName = useMemo(() => {
        const { actSubType: infoActSubType } = membershipPlanInfo ?? {};
        let type = infoActSubType || actSubType;
        let name = '';
        switch (type) {
            case VIP_TYPES.VIP_TRY:
                name = '体验版';
                break;
            case VIP_TYPES.VIP_LEVEL:
                name = '等级会员';
                break;

            default:
                name = '付费会员';
                break;
        }
        return (actId ? '编辑' : '新增') + name;
    }, [actSubType, membershipPlanInfo, actId]);

    const isLock = useMemo(() => {
        if (route.path.indexOf('/detail') >= 0) {
            return true;
        }
        return false;
    }, [route]); // 是否可编辑

    const isCopy = useMemo(() => {
        if (route.path.indexOf('/copy-vip') >= 0) {
            return true;
        }
        return false;
    }, [route]); // 是否可编辑

    // 初始化
    useEffect(() => {
        loadData();
        return () => {
            dispatch({
                type: 'membershipModel/updateMembershipProperty',
                params: { membershipPlanInfo: undefined },
            });
        };
    }, []);

    const loadData = () => {
        if (actId) {
            if (actSubType == VIP_TYPES.VIP_LEVEL) {
                dispatch({
                    type: 'membershipModel/getVipLevelDetail',
                    options: { actId },
                });
            } else {
                dispatch({
                    type: 'membershipModel/getVipPlanDetail',
                    options: { actId },
                });
            }
        } else {
            // initAddInfo();
        }
    };

    const initialValues = useMemo(() => {
        let params = {};
        if (props?.location?.state) {
            const type = props?.location?.state?.actSubType;

            params.actSubType = type;

            if (type == VIP_TYPES.VIP_TRY) {
                params.openMethods = [
                    {
                        openType: '07',
                        openAmt: 0,
                    },
                ];
                // 体验版
            } else if (type == VIP_TYPES.VIP_LEVEL) {
                params.actName = '等级会员';
            }
        }
        if (membershipPlanInfo) {
            params = {
                ...params,
                ...membershipPlanInfo,
            };

            if (membershipPlanInfo.upgradeTipFlag) {
                params.upgradeTipFlag = membershipPlanInfo.upgradeTipFlag === '1' ? true : false;
            }

            if (membershipPlanInfo.stopTipFlag) {
                params.stopTipFlag = membershipPlanInfo.stopTipFlag === '1' ? true : false;
            }
            if (membershipPlanInfo.showLabel) {
                params.showLabel = membershipPlanInfo.showLabel === '1' ? true : false;
            }
            if (membershipPlanInfo.showBottomDesc) {
                params.showBottomDesc = membershipPlanInfo.showBottomDesc === '1' ? true : false;
            }

            params.vipExpireSendMsgFlag = membershipPlanInfo.vipExpireSendMsgFlag === '1';

            if (membershipPlanInfo.banners) {
                params.banners = JSON.parse(membershipPlanInfo.banners);
            }
            if (membershipPlanInfo.citys) {
                params.citys = JSON.parse(membershipPlanInfo.citys);
            }
            if (membershipPlanInfo.gains) {
                params.gains = JSON.parse(membershipPlanInfo.gains);
            }
            if (membershipPlanInfo.openMethods) {
                params.openMethods = JSON.parse(membershipPlanInfo.openMethods);
            }

            if (membershipPlanInfo.effTime && membershipPlanInfo.expTime) {
                params.dateTime = [
                    moment(membershipPlanInfo.effTime, 'YYYY-MM-DD HH:mm:ss'),
                    moment(membershipPlanInfo.expTime, 'YYYY-MM-DD HH:mm:ss'),
                ];
                params.effTime = undefined;
                params.expTime = undefined;
            }

            if (membershipPlanInfo.vipDay?.indexOf(',') > -1) {
                params.vipDay = membershipPlanInfo.vipDay.split(',');
            }
            if (!membershipPlanInfo.vipDayCityList) {
                params.vipDayCityList = [];
            }

            if (membershipPlanInfo.addPrizeList) {
                const list = membershipPlanInfo.addPrizeList.map((ele) => {
                    const options = { ...ele };
                    return options;
                });
                if (!list?.length) {
                    list.push({ addPrizeSn: '1' });
                }
                const two = list.findIndex((ele) => ele.addPrizeSn == '2');
                if (two >= 0) {
                    list[two].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '2' });
                }
                const three = list.findIndex((ele) => ele.addPrizeSn == '3');
                if (three >= 0) {
                    list[three].useInvite = true;
                } else {
                    list.push({ addPrizeSn: '3' });
                }
                params.addPrizeList = list;
            }

            if (membershipPlanInfo.vipGroupList) {
                const list = membershipPlanInfo.vipGroupList.map((ele) => {
                    const options = { ...ele, city: ele?.city?.split(',') || [] };
                    return options;
                });

                params.vipGroupList = list;
            }

            if (membershipPlanInfo.banners) {
                params.banners = JSON.parse(membershipPlanInfo.banners);
            }

            if (membershipPlanInfo.citys) {
                params.citys = JSON.parse(membershipPlanInfo.citys);
            }

            if (membershipPlanInfo.gains) {
                params.gains = JSON.parse(membershipPlanInfo.gains);
            }

            if (membershipPlanInfo.openMethods) {
                const openMethodsArr = JSON.parse(membershipPlanInfo.openMethods);
                params.openMethods = openMethodsArr?.map((ele) => {
                    ele.showLabel = ele.showLabel === '1' ? true : false;
                    ele.showBottomDesc = ele.showBottomDesc === '1' ? true : false;
                    return ele;
                });
            }

            if (membershipPlanInfo.activeCrowd?.length) {
                params.activeCrowd =
                    membershipPlanInfo.activeCrowd.split?.(',') || membershipPlanInfo.activeCrowd;
            }

            if (membershipPlanInfo.activeCrowdFlag == '0') {
                const keys = ['labelList', 'cdpList', 'custLabelList'];
                const activeCrowdConfig = {};
                for (const key of keys) {
                    activeCrowdConfig[key] = params[key];
                }
                params.activeCrowdConfig = activeCrowdConfig;
            }

            params.releIds = membershipPlanInfo.releIds || [];
            if (!isEmpty(params.releIds)) {
                params.releIdsFlag = true;
            }

            if (params.actGetLimitNum > 0) {
                params.actGetLimitFlag = true;
            }
        }
        return params;
    }, [membershipPlanInfo]);

    const goBack = () => {
        if (props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL) {
            history.replace('/userCenter/membership/vip-level/list');
        } else {
            history.replace('/userCenter/membership/plan/list');
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {subTypeName}
                </div>
            }
        >
            <Card>
                <PlanForm
                    loadData={loadData}
                    goBack={goBack}
                    isLock={isLock}
                    isCopy={isCopy}
                    initialValues={initialValues}
                    membershipPlanInfo={membershipPlanInfo}
                ></PlanForm>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, membershipModel, couponModel }) => ({
    global,
    membershipModel,
    couponModel,
}))(MembershipPlanEditPage);
