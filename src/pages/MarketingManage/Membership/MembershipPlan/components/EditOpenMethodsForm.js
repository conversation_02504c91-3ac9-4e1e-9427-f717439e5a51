import React, {
    Fragment,
    useState,
    useEffect,
    useMemo,
    useRef,
    forwardRef,
    useImperativeHandle,
    useContext,
} from 'react';
import { connect } from 'umi';
import {
    InputNumber,
    Form,
    Space,
    Divider,
    Button,
    Row,
    Col,
    Input,
    Select,
    message,
    Popconfirm,
    Table,
    Tooltip,
    Checkbox,
} from 'antd';
import commonStyles from '@/assets/styles/common.less';
import {
    MinusCircleOutlined,
    PlusCircleOutlined,
    InfoCircleOutlined,
    DownCircleOutlined,
    UpCircleOutlined,
} from '@ant-design/icons';
import styles from '../../Membership.less';
import { VIP_TYPES } from '../../MembershipRights/RightsConfig';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import CustTooltip from '@/components/CustTooltip/index';

const FormItem = Form.Item;
const { Option } = Select;

const openChannelOptionItems = [
    { label: '月卡', value: '01' },
    { label: '季卡', value: '02' },
    { label: '年卡', value: '03' },
    { label: '连续包月', value: '04' },
    { label: '连续包季', value: '05' },
    { label: '连续包年', value: '06' },
    { label: '体验周卡', value: '07' },
];
const bottomDescOptionItems = [
    { label: '固定文案', value: '01' },
    { label: '动态取值', value: '02' },
];
const bottomDescCalcOptionItems = [
    { label: '次月', value: '01' },
    { label: '次季', value: '02' },
    { label: '次年', value: '03' },
    { label: '日均', value: '04' },
];

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    actSubType,
    index,
    resultList,
    disabled,
    readOnly,
    ...restProps
}) => {
    const form = useContext(EditableContext);

    const tooltipRef = useRef();

    useEffect(() => {
        if (record) {
            if (!isEmpty(record[dataIndex])) {
                form.setFieldsValue({
                    // [dataIndex]: record[dataIndex],
                    ...record,
                });
            } else {
                form.setFieldsValue({
                    [dataIndex]: null,
                });
            }
        }
    }, [record]);

    const otherCodes = useMemo(() => {
        if (resultList instanceof Array) {
            return resultList
                ?.map((ele = {}, otherIndex) => {
                    if (index >= 0 && otherIndex !== index) {
                        return ele.openType;
                    }
                })
                .filter((ele) => ele);
        }

        return [];
    }, [resultList]);

    const save = async () => {
        try {
            const values = form.getFieldsValue();
            handleSave &&
                handleSave(index, {
                    ...record,
                    ...values,
                });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };
    let childNode = children;
    switch (dataIndex) {
        case 'openType':
            childNode = (
                <Fragment>
                    <Form.Item
                        style={{
                            margin: 0,
                        }}
                        required
                    >
                        <Space>
                            <Form.Item
                                name={dataIndex}
                                rules={[{ required: true, message: '请选择开通方式' }]}
                                noStyle
                            >
                                <Select
                                    placeholder="请选择开通方式"
                                    disabled={
                                        actSubType == VIP_TYPES.VIP_TRY || disabled || readOnly
                                        // || isExit(openType)
                                    }
                                    allowClear
                                    onChange={(newVal) => {
                                        if (newVal == '04') {
                                            tooltipRef?.current?.open();
                                        }
                                        save();
                                    }}
                                    onBlur={save}
                                    style={{ minWidth: '140px' }}
                                >
                                    {openChannelOptionItems.map((ele) => (
                                        <Option
                                            key={ele.value}
                                            value={ele.value}
                                            disabled={
                                                otherCodes.findIndex((code) => code == ele.value) >
                                                    -1 ||
                                                (actSubType != VIP_TYPES.VIP_TRY &&
                                                    ele.value == '07')
                                            }
                                        >
                                            {ele.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <CustTooltip
                                ref={tooltipRef}
                                title={
                                    '因微信关闭代扣业务，则进行中的会员方案，微信小程序不展示连续包月，仅支付宝小程序和APP展示连续包月并支持使用签约代扣服务。'
                                }
                                hidden={record[dataIndex] != '04'}
                            ></CustTooltip>
                        </Space>
                    </Form.Item>
                </Fragment>
            );
            break;
        case 'openAmt':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    name={dataIndex}
                    rules={[{ required: true, message: '请输入开通金额' }]}
                >
                    <InputNumber
                        disabled={disabled || readOnly}
                        style={{ width: '100%' }}
                        placeholder="请输入开通金额"
                        step={0.01}
                        min={0}
                        precision={2}
                        onPressEnter={save}
                        onBlur={save}
                    />
                </Form.Item>
            );
            break;
        case 'showLabel':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    // name={dataIndex}
                >
                    <Space>
                        <Form.Item
                            name="showLabel"
                            noStyle
                            valuePropName="checked"
                            rules={[{ required: true, message: '请选择左上角标签' }]}
                        >
                            <Checkbox
                                style={{ width: '150px' }}
                                onChange={save}
                                disabled={disabled || readOnly}
                                // checked={form.getFieldValue('showLabel') === '1'}
                            >
                                展示左上角标签
                            </Checkbox>
                        </Form.Item>
                        <Form.Item
                            name="methodLabel"
                            noStyle
                            rules={[{ required: true, message: '请输入文案' }]}
                        >
                            <Input
                                placeholder="请输入文案"
                                style={{ width: '150px' }}
                                onChange={save}
                                onBlur={save}
                                disabled={disabled || readOnly}
                            />
                        </Form.Item>
                    </Space>
                </Form.Item>
            );
            break;
        case 'showBottomDesc':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    // name={dataIndex}
                >
                    <Space>
                        <Form.Item
                            name="showBottomDesc"
                            noStyle
                            valuePropName="checked"
                            rules={[{ required: true, message: '请输入底部描述' }]}
                        >
                            <Checkbox
                                style={{ width: '150px' }}
                                onChange={save}
                                disabled={disabled || readOnly}
                                // checked={form.getFieldValue('showBottomDesc') === '1'}
                            >
                                展示底部文案
                            </Checkbox>
                        </Form.Item>
                        <Form.Item
                            name="bottomDescType"
                            rules={[{ required: true, message: '请选择底部文案类型' }]}
                            noStyle
                        >
                            <Select
                                placeholder="请选择底部文案类型"
                                disabled={disabled || readOnly}
                                allowClear
                                onChange={(newVal) => {}}
                                onBlur={save}
                                style={{ minWidth: '140px' }}
                            >
                                {bottomDescOptionItems.map((ele) => (
                                    <Option
                                        key={ele.value}
                                        value={ele.value}
                                        disabled={record?.openType == '07' && ele.value == '02'}
                                    >
                                        {ele.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        {form.getFieldValue('bottomDescType') === '01' && (
                            <Form.Item
                                name="bottomDesc"
                                noStyle
                                rules={[{ required: true, message: '请输入文案' }]}
                            >
                                <Input
                                    placeholder="请输入文案"
                                    style={{ width: '150px' }}
                                    onChange={save}
                                    onBlur={save}
                                    disabled={disabled || readOnly}
                                />
                            </Form.Item>
                        )}
                        {form.getFieldValue('bottomDescType') === '02' && (
                            <Form.Item
                                name="bottomDescCalcType"
                                rules={[{ required: true, message: '请选择底部文案取值类型' }]}
                                noStyle
                            >
                                <Select
                                    placeholder="请选择底部文案取值类型"
                                    disabled={disabled || readOnly}
                                    allowClear
                                    onChange={(newVal) => save()}
                                    onBlur={save}
                                    style={{ minWidth: '140px' }}
                                >
                                    {bottomDescCalcOptionItems.map((ele) => (
                                        <Option key={ele.value} value={ele.value}>
                                            {ele.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        )}
                    </Space>
                </Form.Item>
            );
            break;

        default:
            break;
    }
    return <td {...restProps}>{childNode}</td>;
};

const EditOpenMethodsForm = (props, ref) => {
    const {
        value,
        onChange,
        disabled,
        readOnly,
        actSubType,
        hideAction = false,
        onlyBasic = false, //仅展示基础配置
        onlyMaterial = false, //仅展示素材配置
    } = props;

    useImperativeHandle(ref, () => {
        return {};
    });

    useEffect(() => {
        if (value instanceof Array) {
            updateResultList(value);
        }
    }, [value]);

    const [resultList, updateResultList] = useState([{}]);
    const addEvent = () => {
        const newList = copyObjectCommon(resultList);
        newList.push({});
        updateResultList(newList);
        updateFormItem(newList);
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const removeResultIndex = (index) => {
        const newList = copyObjectCommon(resultList);
        newList.splice(index, 1);
        updateResultList(newList);
        updateFormItem(newList);
    };

    const downOpenTypeSort = (index) => {
        const newList = copyObjectCommon(resultList);
        if (index < newList.length - 1) {
            let temp = newList[index + 1];
            newList[index + 1] = newList[index];
            newList[index] = temp;
            updateResultList(newList);
            updateFormItem(newList);
        }
    };
    const upOpenTypeSort = (index) => {
        const newList = copyObjectCommon(resultList);
        if (index > 0) {
            let temp = newList[index - 1];
            newList[index - 1] = newList[index];
            newList[index] = temp;
            updateResultList(newList);
            updateFormItem(newList);
        }
    };

    const handleSave = async (index, row) => {
        try {
            const newData = copyObjectCommon(resultList);

            const item = newData[index];

            const newItem = {
                ...item,
                ...row,
            };

            newData.splice(index, 1, newItem);

            updateResultList(newData);
            updateFormItem(newData);
        } catch (error) {}
    };

    const updateFormItem = (newData) => {
        onChange && onChange(newData);
    };

    const columns = [
        {
            title: '排序',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '开通方式 ',
            dataIndex: 'openType',
            width: 200,
            onCell: (record, index) => ({
                record,
                index: index,
                dataIndex: 'openType',
                title: '开通方式',
                handleSave,
                actSubType,
                resultList: resultList,
                disabled,
                readOnly: readOnly || onlyMaterial,
            }),
        },
        {
            title: '开通金额',
            dataIndex: 'openAmt',
            width: 200,
            onCell: (record, index) => ({
                record,
                index: index,
                dataIndex: 'openAmt',
                title: '开通金额',
                handleSave,
                actSubType,
                disabled,
                readOnly: readOnly || onlyMaterial,
            }),
        },
        // {
        //     title: '左上角标签',
        //     align: 'center',
        //     width: 200,
        //     dataIndex: '',
        //     render(text, record, index) {
        //         return (
        //             <Form.Item
        //                 style={{
        //                     margin: 0,
        //                 }}
        //                 key={index}
        //             >
        //                 <Space>
        //                     <Form.Item
        //                         name={[index, 'showLabel']}
        //                         noStyle
        //                         valuePropName="checked"
        //                         rules={[{ required: true, message: '请选择左上角标签' }]}
        //                     >
        //                         <Checkbox
        //                             style={{ width: '150px' }}
        //                             onChange={(e) => {
        //                                 const newRecord = copyObjectCommon(record);
        //                                 newRecord.showLabel = e?.target?.checked ? '1' : '0';
        //                                 handleSave(index, newRecord);
        //                             }}
        //                             disabled={disabled}
        //                             checked={record?.showLabel === '1'}
        //                         >
        //                             展示左上角标签
        //                         </Checkbox>
        //                     </Form.Item>
        //                     {/* <Form.Item
        //                         name={[index, 'methodLabel']}
        //                         noStyle
        //                         rules={[{ required: true, message: '请输入文案' }]}
        //                     >
        //                         <Input
        //                             placeholder="请输入文案"
        //                             style={{ width: '150px' }}
        //                             onChange={(e) => {
        //                                 console.log(e);
        //                                 const newRecord = copyObjectCommon(record);
        //                                 newRecord.showLabel = e?.target?.value;
        //                                 handleSave(index, newRecord);
        //                             }}
        //                             onBlur={(e) => {
        //                                 const newRecord = copyObjectCommon(record);
        //                                 newRecord.showLabel = e?.target?.value;
        //                                 handleSave(index, newRecord);
        //                             }}
        //                             disabled={disabled}
        //                         />
        //                     </Form.Item> */}
        //                 </Space>
        //             </Form.Item>
        //         );
        //     },
        // },
        ...(!onlyBasic
            ? [
                  {
                      title: '左上角标签',
                      dataIndex: 'showLabel',
                      width: 350,
                      onCell: (record, index) => ({
                          record,
                          index: index,
                          dataIndex: 'showLabel',
                          title: '左上角标签',
                          handleSave,
                          actSubType,
                          disabled,
                          readOnly,
                      }),
                  },
                  {
                      title: '底部描述',
                      dataIndex: 'showBottomDesc',
                      width: 350,
                      onCell: (record, index) => ({
                          record,
                          index: index,
                          dataIndex: 'showBottomDesc',
                          title: '左上角标签',
                          handleSave,
                          actSubType,
                          disabled,
                          readOnly,
                      }),
                  },
              ]
            : []),
        ...(!hideAction
            ? [
                  {
                      title: '操作 ',
                      width: 120,
                      render(text, record, index) {
                          const upItem = (
                              <Button
                                  size="small"
                                  type="text"
                                  style={{ color: '#ff9901' }}
                                  onClick={() => {
                                      upOpenTypeSort(index);
                                  }}
                              >
                                  上移
                              </Button>
                          );
                          const downItem = (
                              <Button
                                  size="small"
                                  type="text"
                                  style={{ color: '#ff9901' }}
                                  onClick={() => {
                                      downOpenTypeSort(index);
                                  }}
                              >
                                  下移
                              </Button>
                          );
                          const delItem = (
                              <Popconfirm
                                  title="确定删除吗？"
                                  onConfirm={() => {
                                      removeResultIndex(index);
                                  }}
                              >
                                  <Button size="small" type="text" style={{ color: '#ff9901' }}>
                                      删除
                                  </Button>
                              </Popconfirm>
                          );

                          const btnList = [];
                          if (index > 0 && !readOnly) {
                              btnList.push(upItem);
                          }
                          if (index < resultList.length - 1 && !readOnly) {
                              btnList.push(downItem);
                          }

                          if (!disabled && index > 0) {
                              btnList.push(delItem);
                          }

                          return <Space>{btnList}</Space>;
                      },
                  },
              ]
            : []),
    ];

    return (
        <Fragment>
            {!(disabled || readOnly || hideAction) && (
                <Fragment>
                    <Button
                        type="primary"
                        disabled={resultList.length >= 6}
                        onClick={() => {
                            if (resultList.length >= 6) {
                                message.error('已超出可配置的最大数量');
                                return;
                            }
                            addEvent();
                        }}
                    >
                        新增
                    </Button>
                    <div className="mg-t-10"></div>
                </Fragment>
            )}

            <Table
                dataSource={resultList}
                rowKey={(record, index) => index}
                columns={columns}
                pagination={false}
                components={components}
                style={{ width: '100%' }}
                scroll={{ x: 'max-content', y: 450 }}
            ></Table>
        </Fragment>
    );
};

export default forwardRef(EditOpenMethodsForm);
