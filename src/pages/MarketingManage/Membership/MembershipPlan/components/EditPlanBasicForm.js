import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Input,
    Select,
    Form,
    DatePicker,
    message,
    Divider,
    Space,
    Modal,
    Switch,
    Tooltip,
    Checkbox,
    InputNumber,
    Steps,
} from 'antd';

import commonStyles from '@/assets/styles/common.less';
import CityTransferModal from '@/components/CityTransferModal';

import EditActInfoForm from './EditActInfoForm';
import EditPrizeForm from './EditPrizeForm';
import EditTryVipForm from './EditTryVipForm';
import EditConfigureForm from './EditConfigureForm';
import EditOpenMethodsForm from './EditOpenMethodsForm';
import EditActPageForm from './EditActPageForm';
import EditPlanRightItem from './EditPlanRightItem';
import EditOtherForm from './EditOtherForm';
import ActLinkItem from '@/components/ActLinkItem/index';

import moment from 'moment';

import { isEmpty } from '@/utils/utils';
import { VIP_TYPES, RIGHTS_TYPES } from '../../MembershipRights/RightsConfig';
import { getVipPlanEquityCityApi } from '@/services/Marketing/MarketingMembershipApi';
import {
    ACTSUBTYPES,
    ACT_LINK_TYPES,
    ACT_SUB_STATUS,
    ACT_SUB_TYPE,
    APPLET_ACT_TYPES,
} from '@/config/declare';
import { createQrcodeCommon } from '@/utils/utils';
import EditLevelForm from './EditLevelForm';

const { RangePicker } = DatePicker;

const { Option } = Select;
const { confirm } = Modal;

const FormItem = Form.Item;

// 跳转方式
const LINK_TYPES = {
    HOME: '01', // 首页
    CARD: '02', // 我的卡包
    OTHER: '03', // 自定义
    GIFT: '04', // 默认礼包领取页面
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};
const EditPlanBasicForm = (props) => {
    const { form, loadData, isLock, isCopy, membershipPlanInfo, actId } = props;
    const cityModalRef = useRef();
    const methodsRef = useRef();

    const actSubType = Form.useWatch('actSubType', form);

    const actSubTypeName = useMemo(() => {
        let name = '';
        switch (actSubType) {
            case VIP_TYPES.VIP_LEVEL:
                name = '等级会员';
                break;
            case VIP_TYPES.VIP_2_0:
                name = '付费会员';
                break;
            case VIP_TYPES.VIP_TRY:
                name = '体验会员';
                break;
            default:
                break;
        }
        return name;
    }, [actSubType]);
    return (
        <>
            <h1 className={commonStyles['form-title']}>基本信息</h1>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actSubType !== curValues.actSubType ||
                    prevValues.actSuactNamebType !== curValues.actName ||
                    prevValues.unionVipFlag !== curValues.unionVipFlag
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    const actName = getFieldValue('actName');
                    const unionVipFlag = getFieldValue('unionVipFlag');
                    return actSubType == VIP_TYPES.VIP_LEVEL ? (
                        <FormItem
                            label="方案名称"
                            name="actName"
                            {...formItemFixedWidthLayout}
                            rules={[{ required: true, message: '请填写活动名称' }]}
                        >
                            {actName}
                        </FormItem>
                    ) : (
                        <Fragment>
                            <FormItem
                                label="方案名称"
                                name="actName"
                                {...formItemFixedWidthLayout}
                                rules={[{ required: true, message: '请填写活动名称' }]}
                            >
                                <Input
                                    disabled={
                                        !isCopy && (membershipPlanInfo?.actState > 2 || isLock)
                                    }
                                    placeholder="请输入"
                                    autoComplete="off"
                                    maxLength={10}
                                    showCount
                                />
                            </FormItem>

                            <FormItem
                                label="副标题"
                                name="actResume"
                                {...formItemFixedWidthLayout}
                                rules={[{ required: true, message: '请填写副标题' }]}
                            >
                                <Input
                                    disabled={
                                        !isCopy && (membershipPlanInfo?.actState > 2 || isLock)
                                    }
                                    placeholder="请输入"
                                    autoComplete="off"
                                    maxLength={10}
                                    showCount
                                />
                            </FormItem>

                            <FormItem
                                label="方案有效期"
                                {...formItemFixedWidthLayout}
                                required
                                name="dateTime"
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (!value) {
                                                return Promise.reject('请选择活动有效期');
                                            }
                                            if (!value[0]) {
                                                return Promise.reject('请选择活动开始日期');
                                            }
                                            if (!value[1]) {
                                                return Promise.reject('请选择活动失效日期');
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <RangePicker
                                    disabled={[
                                        !isCopy && (membershipPlanInfo?.actState >= 2 || isLock),
                                        !isCopy && (membershipPlanInfo?.actState > 2 || isLock),
                                    ]}
                                    format="YYYY-MM-DD HH:mm:ss"
                                    showTime={{
                                        format: 'HH:mm:ss',
                                        defaultValue: [
                                            moment('00:00:00', 'HH:mm:ss'),
                                            moment('23:59:59', 'HH:mm:ss'),
                                        ],
                                    }}
                                />
                            </FormItem>

                            <FormItem
                                label="方案类型"
                                name="actSubType"
                                {...formItemFixedWidthLayout}
                                rules={[{ required: true, message: '请选择方案类型' }]}
                            >
                                {actSubTypeName}
                                {/* {props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL ? (
                    '等级会员'
                ) : (
                    <Select
                        placeholder="请选择"
                        onChange={(value) => {
                            const openMethods = form.getFieldValue('openMethods');

                            if (value == VIP_TYPES.VIP_TRY) {
                                // 体验版
                                form.setFieldsValue({
                                    openMethods: [
                                        {
                                            openType: '07',
                                            openAmt: 0,
                                        },
                                    ],
                                });
                            } else if (value == VIP_TYPES.VIP_LEVEL) {
                                form.setFieldsValue({ actName: '等级会员' });
                            } else if (
                                openMethods instanceof Array &&
                                openMethods?.some((ele) => ele.openType == '07')
                            ) {
                                openMethods.splice(
                                    openMethods.indexOf(
                                        openMethods.findIndex((ele) => ele.openType == '07'),
                                    ),
                                );
                                if (!openMethods?.length) {
                                    openMethods.push({});
                                }
                                form.setFieldsValue({ openMethods });
                            }
                        }}
                        disabled={!isCopy && (membershipPlanInfo?.actState >= 2 || isLock)}
                    >
                        <Option key={VIP_TYPES.VIP_2_0} value={VIP_TYPES.VIP_2_0}>
                            会员方案2.0
                        </Option>
                        <Option key={VIP_TYPES.VIP_TRY} value={VIP_TYPES.VIP_TRY}>
                            体验版方案
                        </Option>
                    </Select>
                )} */}
                            </FormItem>
                            <Form.Item label={'合作方类型'}>
                                <Space direction="vertical" style={{ width: '100%' }}>
                                    <Form.Item noStyle>
                                        <Space style={{ lineHeight: '32px' }}>
                                            <Form.Item
                                                name="unionVipFlag"
                                                valuePropName="checked"
                                                noStyle
                                            >
                                                <Checkbox
                                                    checked={unionVipFlag}
                                                    onChange={(e) => {
                                                        if (!e.target.checked) {
                                                            const gains =
                                                                form.getFieldValue('gains');
                                                            form.setFieldValue(
                                                                'gains',
                                                                gains.filter(
                                                                    (v) =>
                                                                        v.gainType !==
                                                                        RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                ),
                                                            );
                                                            form.setFieldValue('actLbl', undefined);
                                                        }
                                                    }}
                                                ></Checkbox>
                                            </Form.Item>
                                            {unionVipFlag && (
                                                <Form.Item
                                                    name={'actLbl'}
                                                    noStyle
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请选择联合会员类型',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        style={{ width: '200px' }}
                                                        allowClear
                                                        placeholder="请选择"
                                                        disabled={
                                                            !isCopy &&
                                                            (membershipPlanInfo?.actState >= 2 ||
                                                                isLock)
                                                        }
                                                        onChange={(value) => {
                                                            const gains =
                                                                form.getFieldValue('gains');
                                                            if (value == '03') {
                                                                if (
                                                                    gains &&
                                                                    !gains.find(
                                                                        (v) =>
                                                                            v.gainType ===
                                                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                    )
                                                                ) {
                                                                    gains.push({
                                                                        gainType:
                                                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                    });
                                                                }
                                                                form.setFieldValue(
                                                                    'gains',
                                                                    gains.filter(
                                                                        (v) =>
                                                                            v.gainType !==
                                                                                RIGHTS_TYPES.MEMBER_PROPR &&
                                                                            !isEmpty(v),
                                                                    ),
                                                                );
                                                            } else {
                                                                form.setFieldValue(
                                                                    'gains',
                                                                    gains.filter(
                                                                        (v) =>
                                                                            v.gainType !==
                                                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                                    ),
                                                                );
                                                            }
                                                        }}
                                                    >
                                                        <Option key={'03'} value={'03'}>
                                                            支付宝联合会员
                                                        </Option>
                                                    </Select>
                                                </Form.Item>
                                            )}
                                        </Space>
                                    </Form.Item>
                                </Space>
                            </Form.Item>
                        </Fragment>
                    );
                }}
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.citys !== curValues.citys ||
                    prevValues.actSubType !== curValues.actSubType
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    if (actSubType == VIP_TYPES.VIP_LEVEL) {
                        return null;
                    }
                    const citys = getFieldValue('citys');
                    return (
                        <FormItem
                            label="开通城市"
                            name="citys"
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!citys?.length) {
                                            return Promise.reject('请选择开通城市');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            {(citys?.length && (
                                <Space size="small">
                                    {((isCopy ||
                                        !membershipPlanInfo ||
                                        membershipPlanInfo?.actState <= 2) &&
                                        !isLock && (
                                            <Button
                                                type="link"
                                                onClick={() => {
                                                    cityModalRef?.current?.show({
                                                        defaultKeys: citys?.map(
                                                            (ele) => ele.areaCode,
                                                        ),
                                                    });
                                                }}
                                            >
                                                编辑
                                            </Button>
                                        )) ||
                                        null}
                                    {((isCopy ||
                                        !membershipPlanInfo ||
                                        membershipPlanInfo?.actState <= 2) &&
                                        !isLock && (
                                            <Divider type="vertical" style={{ margin: 0 }} />
                                        )) ||
                                        null}
                                    <Button
                                        type="link"
                                        onClick={() =>
                                            cityModalRef?.current?.show({
                                                defaultKeys: citys?.map((ele) => ele.areaCode),
                                                disabled: true,
                                            })
                                        }
                                    >
                                        查看
                                    </Button>
                                </Space>
                            )) ||
                                ((!membershipPlanInfo || membershipPlanInfo?.actState <= 2) && (
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            cityModalRef?.current?.show();
                                        }}
                                        disabled={isLock}
                                    >
                                        城市
                                    </Button>
                                )) ||
                                null}
                        </FormItem>
                    );
                }}
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.actSubType !== curValues.actSubType ||
                    prevValues.openMethods !== curValues.openMethods ||
                    prevValues.unionVipFlag !== curValues.unionVipFlag
                }
            >
                {({ getFieldValue }) => {
                    const actSubType = getFieldValue('actSubType');
                    const openMethods = getFieldValue('openMethods');
                    const unionVipFlag = getFieldValue('unionVipFlag');

                    const paymemberQrcodePath = 'https://act.xdtev.com/paymember-pay';

                    if (actSubType == VIP_TYPES.VIP_1_0) {
                        return (
                            <Fragment>
                                <EditActInfoForm
                                    {...props}
                                    form={form}
                                    isLock={isLock}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                />

                                <EditPrizeForm
                                    {...props}
                                    form={form}
                                    isLock={isLock}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    onRefresh={loadData}
                                />

                                <EditConfigureForm
                                    {...props}
                                    form={form}
                                    isLock={isLock}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    equityNo={RIGHTS_TYPES.MEMBER_DAY}
                                />

                                <Divider />
                                <h1 className={commonStyles['form-title']}>活动页面</h1>
                                <ActLinkItem
                                    actId={actId}
                                    actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                    disableds={[ACT_LINK_TYPES.BAIDU, ACT_LINK_TYPES.BAIDU_MAP]}
                                    path="/pagesPaymember/paymember/pay"
                                    wrapperCol={{
                                        span: 24,
                                    }}
                                    readOnly={isLock}
                                />
                            </Fragment>
                        );
                    } else if (actSubType == VIP_TYPES.VIP_2_0) {
                        return (
                            <Fragment>
                                <h1 className={commonStyles['form-title']}>配置开通方式</h1>
                                <FormItem
                                    name="openMethods"
                                    label="开通方式"
                                    wrapperCol={{
                                        span: 24,
                                    }}
                                    rules={[{ required: true, message: '请配置开通方式' }]}
                                    initialValue={[{}]}
                                >
                                    <EditOpenMethodsForm
                                        ref={methodsRef}
                                        disabled={
                                            !isCopy && (membershipPlanInfo?.actState >= 2 || isLock)
                                        }
                                        readOnly={isLock}
                                        onlyBasic
                                    ></EditOpenMethodsForm>
                                </FormItem>

                                {/* <FormItem
                                        label={
                                            <Tooltip title="开启后在途连续包月用户展示升级提醒">
                                                升级提醒
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        }
                                        name="upgradeTipFlag"
                                        valuePropName="checked"
                                    >
                                        <Switch
                                            disabled={membershipPlanInfo?.actState > 2 || isLock}
                                        ></Switch>
                                    </FormItem> */}

                                {/* 草稿和新建支持编辑，未开始、进行中，如果是关闭状态支持开启 */}
                                {/* <FormItem
                                        labelCol={{ flex: '0 0 200px' }}
                                        label={
                                            <Tooltip title="开启时，用户拒绝升级时，用户原方案到期时终止续费">
                                                拒绝升级时终止原方案续费
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        }
                                        name="stopTipFlag"
                                        valuePropName="checked"
                                    >

                                        <Switch disabled={isLock}></Switch>
                                    </FormItem> */}

                                {/* {openMethods?.some(
                                        (ele) => ele.openType == '04' || ele.openType == '05',
                                    ) ? (
                                        <FormItem
                                            labelCol={{ flex: '0 0 200px' }}
                                            label={
                                                <Tooltip title="仅对连续包月、连续包季&超过80天内没有充电记录的会员用户在代扣前5天下发短信代扣提醒">
                                                    自动续费短信提醒
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            }
                                            name="vipExpireSendMsgFlag"
                                            valuePropName="checked"
                                        >

                                            <Switch disabled={isLock}></Switch>
                                        </FormItem>
                                    ) : null} */}
                                <Divider />
                                <h1 className={commonStyles['form-title']}>配置权益</h1>

                                <FormItem
                                    label="权益配置"
                                    name="gains"
                                    rules={[{ required: true, message: '请配置权益' }]}
                                    initialValue={[{}]}
                                >
                                    <EditPlanRightItem
                                        form={form}
                                        disabled={isLock}
                                        unionVipFlag={unionVipFlag}
                                        onlyGainType
                                    />
                                </FormItem>

                                <EditOtherForm
                                    {...props}
                                    form={form}
                                    isLock={isLock}
                                    formItemLayout={formItemLayout}
                                ></EditOtherForm>

                                <ActLinkItem
                                    actId={actId}
                                    actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                    mergePath={paymemberQrcodePath}
                                    mergeLabel={'支付宝&微信外部投放'}
                                    disableds={[ACT_LINK_TYPES.BAIDU, ACT_LINK_TYPES.BAIDU_MAP]}
                                    path="/pagesPaymember/paymember/pay"
                                    wrapperCol={{
                                        span: 24,
                                    }}
                                    readOnly={isLock}
                                />
                            </Fragment>
                        );
                    } else if (actSubType == VIP_TYPES.VIP_TRY) {
                        return (
                            <Fragment>
                                <FormItem
                                    name="openMethods"
                                    label="开通方式"
                                    wrapperCol={{
                                        span: 16,
                                    }}
                                    rules={[{ required: true, message: '请配置开通方式' }]}
                                    initialValue={[
                                        {
                                            openType: '07',
                                            openAmt: 0,
                                        },
                                    ]}
                                >
                                    <EditOpenMethodsForm
                                        actSubType={actSubType}
                                        readOnly={isLock}
                                        onlyBasic
                                        hideAction
                                    ></EditOpenMethodsForm>
                                </FormItem>

                                <h1 className={commonStyles['form-title']}>配置权益</h1>

                                <FormItem
                                    label="权益配置"
                                    name="gains"
                                    rules={[
                                        { required: true, message: '请配置权益' },
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!isEmpty(value)) {
                                                    const priceRight = value.find((ele) =>
                                                        [
                                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                            RIGHTS_TYPES.MEMBER_PROPR,
                                                        ].includes(ele.gainType),
                                                    );
                                                    if (!priceRight) {
                                                        return Promise.reject(
                                                            '会员专享价或者pro会员价必须配置一个',
                                                        );
                                                    }

                                                    const priceRightList = value.filter((ele) =>
                                                        [
                                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                                            RIGHTS_TYPES.MEMBER_PROPR,
                                                        ].includes(ele.gainType),
                                                    );
                                                    if (priceRightList.length > 1) {
                                                        return Promise.reject(
                                                            '会员专享价或者pro会员价只能配置一个',
                                                        );
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={[{}]}
                                >
                                    <EditPlanRightItem
                                        form={form}
                                        disabled={isLock}
                                        unionVipFlag={unionVipFlag}
                                        onlyGainType
                                        onlyRights={[
                                            RIGHTS_TYPES.ALIPAY_EXCLUSIVE_DISCOUNT,
                                            RIGHTS_TYPES.MEMBER_DAY,
                                            RIGHTS_TYPES.MEMBER_PROPR,
                                        ]}
                                    />
                                </FormItem>

                                <EditTryVipForm
                                    {...props}
                                    form={form}
                                    isLock={isLock}
                                    formItemLayout={formItemLayout}
                                    formItemFixedWidthLayout={formItemFixedWidthLayout}
                                    showTitle
                                />

                                <Divider />
                                <h1 className={commonStyles['form-title']}>其他配置</h1>

                                <FormItem
                                    label="权益价值"
                                    name="equityAmt"
                                    rules={[{ required: true, message: '请配置权益价值' }]}
                                >
                                    <InputNumber
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        placeholder="请填写"
                                        disabled={isLock}
                                        addonAfter="元"
                                    ></InputNumber>
                                </FormItem>
                                <ActLinkItem
                                    actId={actId}
                                    actType={APPLET_ACT_TYPES.MEMBER_PLAN}
                                    disableds={[ACT_LINK_TYPES.BAIDU, ACT_LINK_TYPES.BAIDU_MAP]}
                                    path={`/pagesPaymember/paymember/pay?actId=${actId}&experience=1`}
                                    wrapperCol={{
                                        span: 24,
                                    }}
                                    readOnly={isLock}
                                />
                            </Fragment>
                        );
                    } else if (actSubType == VIP_TYPES.VIP_LEVEL) {
                        return <EditLevelForm isLock={isLock} form={form} />;
                    }
                    return null;
                }}
            </FormItem>
            <CityTransferModal
                ref={cityModalRef}
                onFinish={(citys) => {
                    form.setFieldsValue({ citys });
                }}
                request={async (params = {}) => {
                    try {
                        const {
                            data: { areaList },
                        } = await getVipPlanEquityCityApi(params);

                        return areaList;
                    } catch (error) {
                        return [];
                    }
                }}
                deleteEnabled={false}
            />
        </>
    );
};
export default EditPlanBasicForm;
