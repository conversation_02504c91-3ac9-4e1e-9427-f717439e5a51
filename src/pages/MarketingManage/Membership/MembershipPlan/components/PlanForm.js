import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Input,
    Select,
    Form,
    DatePicker,
    message,
    Divider,
    Space,
    Modal,
    Switch,
    Tooltip,
    Checkbox,
    Steps,
} from 'antd';

import { LeftOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

import {
    saveVipLevelPlanApi,
    saveVipPlanApi,
    checkVipPlanApi,
} from '@/services/Marketing/MarketingMembershipApi';

import styles from '../../Membership.less';

import { isEmpty } from '@/utils/utils';
import { VIP_TYPES, RIGHTS_TYPES } from '../../MembershipRights/RightsConfig';
import EditPlanBasicForm from './EditPlanBasicForm';
import EditPlanMaterialForm from './EditPlanMaterialForm';

const { RangePicker } = DatePicker;

const { Option } = Select;
const { confirm } = Modal;

const FormItem = Form.Item;

// 跳转方式
const LINK_TYPES = {
    HOME: '01', // 首页
    CARD: '02', // 我的卡包
    OTHER: '03', // 自定义
    GIFT: '04', // 默认礼包领取页面
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const MembershipPlanEditPage = (props) => {
    const {
        loadData,
        goBack,
        isLock,
        isCopy,
        initialValues,
        membershipPlanInfo,
        hideStep = false,
        defStep = 0,
    } = props;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);

    const actId = Form.useWatch('actId', form);

    useEffect(() => {
        if (initialValues) {
            form.setFieldsValue(initialValues);
        }
    }, [initialValues]);

    /**
     * 保存
     * type  save/send
     */
    const saveGiftEvent = (type) => {
        if (submitLoading) {
            return;
        }
        form.validateFields()
            .then(async (values) => {
                const params = {
                    ...values,
                    effTime: values?.dateTime?.[0]?.format?.('YYYY-MM-DD HH:mm:ss'),
                    expTime: values?.dateTime?.[1]?.format?.('YYYY-MM-DD HH:mm:ss'),
                };
                delete params.dateTime;

                params.upgradeTipFlag = values.upgradeTipFlag ? '1' : '0';
                params.stopTipFlag = values.stopTipFlag ? '1' : '0';
                params.vipExpireSendMsgFlag = values.vipExpireSendMsgFlag ? '1' : '0';

                if (values?.citys?.length) {
                    params.citys = JSON.stringify(values.citys);
                }
                if (values?.openMethods?.length) {
                    const openMethods = values?.openMethods?.map((ele) => {
                        ele.showLabel = ele?.showLabel ? '1' : '0';
                        ele.showBottomDesc = ele?.showBottomDesc ? '1' : '0';
                        return ele;
                    });
                    params.openMethods = JSON.stringify(openMethods);
                }
                if (values?.banners?.length) {
                    const banners = [];
                    values.banners.forEach((ele) => {
                        banners.push({
                            bannerType: ele.bannerType,
                            bannerImg: ele.bannerImg,
                        });
                    });
                    params.banners = JSON.stringify(banners);
                }
                if (values?.gains?.length) {
                    const gains = [];
                    values.gains.forEach((ele) => {
                        gains.push({
                            equityId: ele.equityId,
                            sortId: ele.sortId,
                            gainType: ele.gainType,
                            gainName: ele.gainName,
                            gainTitle: ele.gainTitle,
                            gainIcon: ele.gainIcon,
                            gainDetail: ele.gainDetail,
                            equityImg: ele.equityImg,
                            equityLabelImg: ele.equityLabelImg,
                            equityImgNew: ele.equityImgNew,
                            equityImgNewLinkType: ele.equityImgNewLinkType,
                            equityImgNewUrl: ele.equityImgNewUrl,
                        });
                    });
                    params.gains = JSON.stringify(gains);
                }
                if (values.actSubType == VIP_TYPES.VIP_LEVEL) {
                    if (values.levelDefineBos) {
                        // params.levelDefineParams = values.levelDefineBos;
                        delete params.levelDefineBos;
                    }
                    if (values.levelEquityBos) {
                        params.levelEquityParams = values.levelEquityBos;
                        delete params.levelEquityBos;
                    }
                    if (values.levelBos) {
                        // params.levelParams = values.levelBos;
                        delete params.levelBos;
                    }
                    if (values.levelTaskBos) {
                        // params.levelTaskParams = values.levelTaskBos;
                        delete params.levelTaskBos;
                    }
                }

                // if (values?.activeCrowd?.length) {
                //     params.activeCrowd = values?.activeCrowd?.join?.(',') || values?.activeCrowd;
                // }

                if (values.activeCrowdFlag == '0') {
                    const keys = Object.keys(values.activeCrowdConfig || {});
                    for (const key of keys) {
                        if (values.activeCrowdConfig[key]) {
                            params[key] = values.activeCrowdConfig[key];
                        }
                    }
                    delete params.activeCrowdConfig;
                }

                if (type == 'save') {
                    params.submitFlag = false;
                } else if (type == 'submit' || type == 'checkSubmit') {
                    params.submitFlag = true;
                }
                if (isCopy) {
                    delete params.actId;
                }
                if (isCopy) {
                    delete params.actState;
                }
                console.log(params, values);
                // return;
                changeSubmitLoading(true);

                if (values.actSubType !== VIP_TYPES.VIP_LEVEL && type == 'checkSubmit') {
                    const {
                        data: { remark: checkError },
                    } = await checkVipPlanApi({
                        actId: params?.actId,
                        citys: params?.citys,
                    });
                    if (checkError) {
                        confirm({
                            title: '确认提交?',
                            icon: <ExclamationCircleOutlined />,
                            content: checkError,
                            onOk() {
                                changeSubmitLoading(false);

                                saveGiftEvent('submit');
                            },
                            onCancel() {
                                console.log('Cancel');
                                changeSubmitLoading(false);
                            },
                        });
                        return;
                    }
                }

                const {
                    data: { actId: id },
                } = await (values.actSubType == VIP_TYPES.VIP_LEVEL
                    ? saveVipLevelPlanApi(params)
                    : saveVipPlanApi(params));
                changeSubmitLoading(false);

                form?.setFieldsValue({ actId: id });

                if (type == 'save') {
                    message.success('保存成功');
                    if (isCopy || !actId || hideStep) {
                        goBack && goBack();
                    }
                } else if (type == 'submit' || type == 'checkSubmit') {
                    message.success('提交成功');
                    goBack && goBack();
                }
            })
            .catch((e) => {
                console.log(e);
                changeSubmitLoading(false);
            });
    };

    const [curStep, updateCurStep] = useState(defStep);

    return (
        <>
            <Form
                form={form}
                {...formItemLayout}
                scrollToFirstError
                initialValues={{ vipExpireSendMsgFlag: true }}
                onFieldsChange={(values, allFields) => {
                    form.getFieldsValue();
                }}
            >
                <FormItem name="actId" noStyle hidden />

                <FormItem name="actState" noStyle />

                {!hideStep && (
                    <FormItem wrapperCol={{ span: 18 }}>
                        <Steps current={curStep} onChange={updateCurStep}>
                            <Steps.Step title="编辑会员方案"></Steps.Step>
                            <Steps.Step title="配置素材"></Steps.Step>
                        </Steps>
                    </FormItem>
                )}

                <FormItem noStyle hidden={curStep != 0}>
                    <EditPlanBasicForm form={form} actId={actId} {...props}></EditPlanBasicForm>
                </FormItem>
                <FormItem noStyle hidden={curStep != 1}>
                    <EditPlanMaterialForm
                        form={form}
                        actId={actId}
                        {...props}
                        novalidate={curStep != 1}
                    ></EditPlanMaterialForm>
                </FormItem>

                <div className={styles['form-submit']}>
                    <Fragment>
                        <Button className={styles['form-btn']} onClick={goBack}>
                            取消
                        </Button>
                        {curStep == 0 && !hideStep && (
                            <>
                                <Button
                                    className={styles['form-btn']}
                                    type="primary"
                                    onClick={() => {
                                        form.validateFields().then((values) => {
                                            updateCurStep(1);
                                        });
                                    }}
                                >
                                    下一步
                                </Button>
                            </>
                        )}

                        {curStep == 1 && (
                            <>
                                {!hideStep && (
                                    <Button
                                        className={styles['form-btn']}
                                        type="primary"
                                        onClick={() => {
                                            updateCurStep(0);
                                        }}
                                    >
                                        上一步
                                    </Button>
                                )}

                                {((isCopy ||
                                    !membershipPlanInfo ||
                                    membershipPlanInfo?.actState == 0) &&
                                    props?.location?.state?.actSubType != VIP_TYPES.VIP_LEVEL && (
                                        <Button
                                            className={styles['form-btn-left']}
                                            type="primary"
                                            loading={submitLoading}
                                            onClick={() => {
                                                saveGiftEvent('save');
                                            }}
                                        >
                                            保存
                                        </Button>
                                    )) ||
                                    null}
                                {(isCopy ||
                                    !membershipPlanInfo ||
                                    membershipPlanInfo?.actState <= 2 ||
                                    props?.location?.state?.actSubType == VIP_TYPES.VIP_LEVEL) &&
                                    !isLock && (
                                        <Button
                                            className={styles['form-btn']}
                                            type="primary"
                                            loading={submitLoading}
                                            onClick={async () => {
                                                form.validateFields().then(() => {
                                                    confirm({
                                                        title: `您确认要提交吗？`,
                                                        content: actId
                                                            ? form.getFieldValue('actSubType') ==
                                                              VIP_TYPES.VIP_2_0
                                                                ? '修改内容将于生效时间生效，如您新增了城市，请根据实际需要，在生效日期前完善加量包、天天领红包权益的配置！'
                                                                : ''
                                                            : '提交后方案不可删除',
                                                        okText: '确定',
                                                        cancelText: '取消',
                                                        onOk() {
                                                            saveGiftEvent('checkSubmit');
                                                        },
                                                    });
                                                });
                                            }}
                                        >
                                            提交
                                        </Button>
                                    )}
                            </>
                        )}
                    </Fragment>
                </div>
            </Form>
        </>
    );
};

export default connect(({ global, membershipModel, couponModel }) => ({
    global,
    membershipModel,
    couponModel,
}))(MembershipPlanEditPage);
