import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    InputNumber,
    Form,
    Space,
    Switch,
    Descriptions,
    Divider,
    Radio,
    Select,
    Checkbox,
} from 'antd';

import commonStyles from '@/assets/styles/common.less';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import { isEmpty } from '@/utils/utils';
import PriceCrowdItem, { validatorCrowdItem } from '../../components/PriceCrowdItem';
import { useRequest } from 'ahooks';
import {
    // 会员2.0方案
    getVipPlanListApi,
} from '@/services/Marketing/MarketingMembershipApi';

const FormItem = Form.Item;

// 体验会员
export const EditTryVipForm = (props) => {
    const {
        dispatch,
        form,
        isLock,
        global: { custLabelTypeList },
        membershipPlanInfo,
        formItemLayout,
        formItemFixedWidthLayout,
    } = props;
    useEffect(() => {
        if (!(custLabelTypeList instanceof Array) || custLabelTypeList?.length === 0) {
            dispatch({
                type: 'global/initCustLabelTypeList',
            });
        }
    }, []);

    const actGetLimitFlag = Form.useWatch('actGetLimitFlag', form);
    const releIdsFlag = Form.useWatch('releIdsFlag', form);

    const { data: planList, loading } = useRequest(async () => {
        try {
            const params = {
                pageIndex: 1,
                pageSize: 99,
                actSubType: '2700',
                actState: 2,
            };
            const {
                data: { records, total },
            } = await getVipPlanListApi(params);
            return records;
        } catch (error) {
            return Promise.reject(error);
        }
    });

    return (
        <Fragment>
            <Divider />
            <h1 className={commonStyles['form-title']}>规则配置</h1>

            <Form.Item
                label={'目标用户'}
                rules={[
                    {
                        required: true,
                        message: '请选择',
                    },
                ]}
                name={'activeCrowdFlag'}
                initialValue={'1'}
            >
                <Radio.Group disabled={isLock}>
                    <Radio value={'1'}>所有用户</Radio>
                    <Radio value={'0'}>部分用户</Radio>
                </Radio.Group>
            </Form.Item>

            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues?.activeCrowdFlag != curValues?.activeCrowdFlag
                }
            >
                {({ getFieldValue, setFieldsValue }) => {
                    const activeCrowdFlag = getFieldValue('activeCrowdFlag');
                    return (
                        activeCrowdFlag == '0' && (
                            <Fragment>
                                <Form.Item
                                    name="activeCrowdConfig"
                                    label=" "
                                    colon={false}
                                    wrapperCol={{ span: 24 }}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                const custLabelList =
                                                    getFieldValue('custLabelList');
                                                const labelList = getFieldValue('labelList');
                                                const cdpList = getFieldValue('cdpList');
                                                const res = validatorCrowdItem({
                                                    custLabelList,
                                                    labelList,
                                                    cdpList,
                                                });
                                                if (res?.length) {
                                                    return Promise.reject(res);
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <PriceCrowdItem
                                        onChange={(newCrowdInfo) => {
                                            setFieldsValue(newCrowdInfo);
                                        }}
                                        disabled={isLock}
                                        singleMode
                                    ></PriceCrowdItem>
                                </Form.Item>

                                <Form.Item name={'custLabelList'} noStyle />
                                <Form.Item name={'labelList'} noStyle />
                                <Form.Item name={'cdpList'} noStyle />
                            </Fragment>
                        )
                    );
                }}
            </Form.Item>

            {/* <FormItem label="体验周期" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="experienceCycle"
                        rules={[
                            { required: true, message: '请输入天数' },
                            () => ({
                                validator(rule, value) {
                                    if (!isEmpty(value) && Number(value) < 1) {
                                        return Promise.reject('周期最少配置1天');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        noStyle
                    >
                        <InputNumber
                            precision={0}
                            step={1}
                            min={0}
                            disabled={membershipPlanInfo?.actState > 2 || isLock}
                            style={{ width: '100%' }}
                            placeholder="请输入天数"
                            onChange={() => {
                                form?.validateFields(['receiveCycle']);
                            }}
                        />
                    </FormItem>
                    <span>天</span>
                </Space>
            </FormItem> */}

            {/* 体验只有周卡固定写死 */}
            <FormItem name="experienceCycle" hidden initialValue={7}></FormItem>

            <FormItem label="每个用户最多体验" {...formItemFixedWidthLayout}>
                <>
                    <p>
                        <Space>
                            <Form.Item name="actGetLimitFlag" valuePropName="checked" noStyle>
                                <Checkbox
                                    checked={actGetLimitFlag}
                                    onChange={(e) => {
                                        if (!e.target.checked) {
                                            form.setFieldValue('actGetLimitNum', undefined);
                                        }
                                    }}
                                ></Checkbox>
                            </Form.Item>
                            {actGetLimitFlag && (
                                <FormItem
                                    name="actGetLimitNum"
                                    // rules={[
                                    //     { required: true, message: '请输入次数' },
                                    // () => ({
                                    //     validator(rule, value) {
                                    //         const experienceCycle = form.getFieldValue('experienceCycle');
                                    //         if (!isEmpty(value)) {
                                    //             if (!isEmpty(experienceCycle)) {
                                    //                 if (Number(experienceCycle) < Number(value)) {
                                    //                     return Promise.reject(
                                    //                         `领取周期不能大于${experienceCycle}天`,
                                    //                     );
                                    //                 }
                                    //             }
                                    //             if (Number(value) < 1) {
                                    //                 return Promise.reject('周期最少配置1天');
                                    //             }
                                    //         }
                                    //         return Promise.resolve();
                                    //     },
                                    // }),
                                    // ]}
                                    noStyle
                                >
                                    <InputNumber
                                        precision={0}
                                        step={1}
                                        min={0}
                                        disabled={membershipPlanInfo?.actState > 2 || isLock}
                                        style={{ width: '100%' }}
                                        placeholder="请输入次数"
                                        // onChange={() => {
                                        //     form?.validateFields(['experienceCycle']);
                                        // }}
                                        addonAfter="次"
                                    />
                                </FormItem>
                            )}
                        </Space>
                    </p>
                    {actGetLimitFlag && (
                        <>
                            <FormItem label="关联其他方案" {...formItemFixedWidthLayout}>
                                <Space>
                                    <Form.Item name="releIdsFlag" valuePropName="checked" noStyle>
                                        <Checkbox
                                            checked={releIdsFlag}
                                            onChange={(e) => {
                                                if (!e.target.checked) {
                                                    form.setFieldValue('releIds', []);
                                                }
                                            }}
                                        ></Checkbox>
                                    </Form.Item>
                                    {releIdsFlag && (
                                        <FormItem name="releIds" noStyle>
                                            <Select
                                                placeholder="请选择"
                                                showSearch
                                                optionFilterProp="actName"
                                                fieldNames={{
                                                    label: 'actName',
                                                    value: 'actId',
                                                }}
                                                disabled={
                                                    membershipPlanInfo?.actState > 2 || isLock
                                                }
                                                options={planList || []}
                                                allowClear
                                                mode="multiple"
                                                maxTagCount={5}
                                                style={{ minWidth: '200px' }}
                                            ></Select>
                                        </FormItem>
                                    )}
                                </Space>
                            </FormItem>
                        </>
                    )}
                </>
            </FormItem>

            {/* <FormItem label="日优惠上限" {...formItemFixedWidthLayout} required>
                <Space>
                    <FormItem
                        name="dayDctLimit"
                        rules={[
                            { required: true, message: '请输入金额' },
                            () => ({
                                validator(rule, value) {
                                    if (!isEmpty(value) && Number(value) < 0.01) {
                                        return Promise.reject('日优惠上限最少配置0.01');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        noStyle
                    >
                        <InputNumber
                            precision={2}
                            step={0.01}
                            min={0}
                            max={999.99}
                            disabled={membershipPlanInfo?.actState > 2 || isLock}
                            style={{ width: '100%' }}
                            placeholder="请输入金额"
                        />
                    </FormItem>
                    <span>元</span>
                </Space>
            </FormItem> */}
        </Fragment>
    );
};

export default connect(({ global, couponModel }) => ({
    global,
    couponModel,
}))(EditTryVipForm);
