import React, { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import {
    Button,
    Card,
    Input,
    Select,
    Form,
    DatePicker,
    message,
    Divider,
    Space,
    Modal,
    Switch,
    Tooltip,
    Checkbox,
    Steps,
    Typography,
} from 'antd';
import { VIP_TYPES, RIGHTS_TYPES } from '../../MembershipRights/RightsConfig';
import EditOpenMethodsForm from './EditOpenMethodsForm';
import EditPlanRightItem from './EditPlanRightItem';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';

const FormItem = Form.Item;
const EditPlanMaterialForm = (props) => {
    const { form, loadData, isLock, isCopy, membershipPlanInfo, actId, novalidate = false } = props;
    const actSubType = Form.useWatch('actSubType', form);
    return (
        <>
            <Typography.Title level={5}>配置开通方式栏素材</Typography.Title>
            <FormItem
                name="openMethods"
                wrapperCol={{
                    span: 24,
                }}
                rules={[{ required: true, message: '请配置开通方式' }]}
            >
                <EditOpenMethodsForm
                    disabled={!isCopy && (membershipPlanInfo?.actState >= 2 || isLock)}
                    readOnly={isLock}
                    onlyMaterial
                    hideAction
                ></EditOpenMethodsForm>
            </FormItem>
            <Typography.Title level={5}>配置权益素材</Typography.Title>
            <FormItem
                name="gains"
                wrapperCol={{
                    span: 24,
                }}
                rules={[{ required: true, message: '请配置权益' }]}
            >
                <EditPlanRightItem
                    form={form}
                    disabled={isLock}
                    hideAction
                    novalidate={novalidate}
                />
            </FormItem>
            {actSubType == VIP_TYPES.VIP_TRY && (
                <Form.Item
                    label="到期弹窗图片"
                    name="popIcon"
                    wrapperCol={{ span: 24 }}
                    rules={
                        !novalidate
                            ? [
                                  {
                                      required: true,
                                      whitespace: true,
                                      message: '请配置图片',
                                  },
                              ]
                            : undefined
                    }
                >
                    <UpLoadImgItem
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'community',
                            relaTable: 'e_wechat_community',
                        }}
                        sizeInfo={{
                            size: 100,
                            width: 760,
                            height: 210,
                            suggest: true,
                        }}
                    ></UpLoadImgItem>
                </Form.Item>
            )}
        </>
    );
};

export default EditPlanMaterialForm;
