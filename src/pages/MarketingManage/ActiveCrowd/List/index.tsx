import { <PERSON><PERSON><PERSON>er<PERSON>rapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import { Button, Card, Form, Popconfirm, Space, Spin, Typography, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { useEffect, useRef, useState } from 'react';

import SearchLayout from './components/SearchLayout';
import TablePro from '@/components/TablePro';
import EditModal from './components/EditModal';
import { queryCrowdTagInfo, updateCrowdActConfig } from '@/services/Marketing/ActiveCrowdApi';
import { CROWD_STATUS, CrowdStatusTitle, CrowdTypeTitle } from '@/constants/crowd';

const IndexPage = () => {
    const [form] = Form.useForm();
    const [taskId, setTaskId] = useState<string>('');
    const modalRef = useRef<any>();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        refresh,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryCrowdTagInfo({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
        },
    );

    const onFinish = (formData?: API.QrCodeTemplateRequest) => {
        searchList({ pageSize: pagination.pageSize, current: 1 }, { ...formData });
    };

    useEffect(() => {
        searchList({ pageSize: pagination.pageSize, current: 1 });
    }, []);

    const resetData = () => {
        form.resetFields();
        searchList({ pageSize: pagination.pageSize, current: 1 });
    };

    const goAddEvent = () => {
        modalRef.current?.show();
    };

    const { run: changeStatus, loading: changeLoading } = useRequest(
        (params: any) => {
            setTaskId(params?.operId);
            return updateCrowdActConfig(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('操作成功');
                    refresh();
                }
                setTaskId('');
            },
            onError: () => {
                setTaskId('');
            },
        },
    );
    const onModalCallback = () => {
        refresh();
    };

    const showEditModal = (record: API.CustNatureTagQueryVo) => {
        modalRef.current?.show(record);
    };
    const changeEnable = (record: API.CustNatureTagQueryVo) => {
        changeStatus({
            id: record?.id,
            status: CROWD_STATUS.ENABLE,
        });
    };
    const changeDisable = (record: API.CustNatureTagQueryVo) => {
        changeStatus({
            id: record?.id,
            status: CROWD_STATUS.DISABLE,
        });
    };

    const columns: ColumnsType<API.CustNatureTagQueryVo> = [
        {
            title: '人群来源',
            width: 160,
            dataIndex: 'sourceType',
            render: (value: string) => {
                return CrowdTypeTitle[value] || '-';
            },
        },
        {
            title: '活动人群',
            width: 220,
            dataIndex: 'crowdName',
        },
        {
            title: '小程序展示标签',
            width: 180,
            dataIndex: 'appShowTagName',
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'status',
            render: (value: string) => {
                return CrowdStatusTitle[value] || '-';
            },
        },
        {
            title: '操作',
            width: 160,
            dataIndex: 'crowdId',
            fixed: 'right',
            render: (value: string, record: API.CustNatureTagQueryVo) => {
                return (
                    <Space>
                        <Typography.Link onClick={() => showEditModal(record)}>
                            编辑
                        </Typography.Link>
                        {record?.status === CROWD_STATUS.DISABLE && (
                            <Spin spinning={changeLoading && taskId === record?.id}>
                                <Popconfirm
                                    title="确认启用？"
                                    okText="确认"
                                    onConfirm={() => changeEnable(record)}
                                >
                                    <Typography.Link>启用</Typography.Link>
                                </Popconfirm>
                            </Spin>
                        )}
                        {record?.status === CROWD_STATUS.ENABLE && (
                            <Spin spinning={changeLoading && taskId === record?.id}>
                                <Popconfirm
                                    title="确认禁用？"
                                    okText="确认"
                                    onConfirm={() => changeDisable(record)}
                                >
                                    <Typography.Link>禁用</Typography.Link>
                                </Popconfirm>
                            </Spin>
                        )}
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    listLoading={listLoading}
                    form={form}
                    onSubmit={onFinish}
                    onReset={resetData}
                ></SearchLayout>
                <Space>
                    <Button type="primary" onClick={goAddEvent}>
                        新建
                    </Button>
                </Space>
                <div className="mg-t-20"></div>
                <TablePro
                    name="activecrowdlist"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    offsetHeader={false}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
                <EditModal ref={modalRef} callback={onModalCallback} />
            </Card>
        </PageHeaderWrapper>
    );
};

export default IndexPage;
