import { Select, Form, Input, Modal, Radio, Row, Space, Button, message, Tag } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { useRequest } from 'ahooks';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';

import { CROWD_TYPE, CrowdTypeOptions } from '@/constants/crowd';
import ActiveCrowdCheckTree from '@/components/ActiveCrowdCheckTree';
import {
    createCrowdActConfig,
    queryCdpCrowdInfo,
    queryCrowdTagExistList,
    updateCrowdActConfig,
} from '@/services/Marketing/ActiveCrowdApi';

const EditModal = (props: any, ref: any) => {
    const [action, setAction] = useState<'ADD' | 'EDIT'>('ADD');
    const [editRecord, setEditRecord] = useState<API.CustNatureTagQueryVo>();
    const [visible, setVisible] = useState<boolean>(false);
    const { callback } = props;
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => {
        return {
            show: (record?: API.CustNatureTagQueryVo) => {
                if (record) {
                    form.setFieldsValue({ ...record });
                    setAction('EDIT');
                    setEditRecord(record);
                } else {
                    form.setFieldsValue({
                        sourceType: CROWD_TYPE.CDP,
                        crowdId: undefined,
                        appShowTagName: undefined,
                    });
                    setAction('ADD');
                    setEditRecord(undefined);
                }
                setVisible(true);
            },
        };
    });

    const onClose = () => {
        form.resetFields();
        setEditRecord(undefined);
        setVisible(false);
    };

    const crondTypeChange = (e: RadioChangeEvent) => {
        form.setFieldsValue({
            crowdId: undefined,
        });
    };

    const {
        run: queryCdp,
        data: cdpData,
        loading: cdpLoading,
    } = useRequest(
        () => {
            return queryCdpCrowdInfo();
        },
        {
            manual: true,
        },
    );
    const {
        run: queryCrondConfig,
        data: crondData,
        loading: crondLoading,
    } = useRequest(
        () => {
            return queryCrowdTagExistList();
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (action === 'ADD' && visible) {
            queryCrondConfig();
            queryCdp();
        }
    }, [visible, action]);
    const cronIdList = useMemo(() => {
        return crondData?.data?.map((v) => v?.custCrowd) ?? [];
    }, [crondData?.data]);

    const cdpOptions = useMemo(() => {
        if (cdpData?.data && cronIdList) {
            return cdpData.data.map((v) => {
                return {
                    title: v?.cdpCrowdName,
                    label: v?.cdpCrowdName,
                    value: v?.cdpCrowdId,
                    disabled: cronIdList?.includes(v?.cdpCrowdId),
                };
            });
        } else {
            return [];
        }
    }, [cdpData?.data, cronIdList]);

    const { run: updateCrond, loading: updateLoading } = useRequest(
        (params: API.CreateCrowdActConfigRequest) => {
            return updateCrowdActConfig(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('保存成功');
                    callback && callback();
                    onClose();
                }
            },
        },
    );

    const { run: addCrond, loading: addLoading } = useRequest(
        (params: API.CreateCrowdActConfigRequest) => {
            return createCrowdActConfig(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('保存成功');
                    callback && callback();
                    onClose();
                }
            },
        },
    );

    const onFinish = (formData: API.CreateCrowdActConfigRequest) => {
        if (action === 'ADD') {
            addCrond({ ...formData, appShowTagName: formData.appShowTagName?.trim() });
        } else {
            updateCrond({
                id: editRecord?.id,
                ...formData,
                appShowTagName: formData.appShowTagName?.trim(),
            });
        }
    };

    return (
        <Modal
            title={action === 'EDIT' ? '编辑' : '新建'}
            visible={visible}
            footer={null}
            width={600}
            onCancel={onClose}
            destroyOnClose
        >
            <Form
                form={form}
                onFinish={onFinish}
                labelCol={{ flex: '0 0 120px' }}
                wrapperCol={{ span: 16 }}
            >
                {action === 'ADD' && (
                    <Form.Item
                        label="人群来源"
                        name="sourceType"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                    >
                        <Radio.Group options={CrowdTypeOptions} onChange={crondTypeChange} />
                    </Form.Item>
                )}
                {action === 'ADD' ? (
                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.sourceType !== curValues.sourceType
                        }
                    >
                        {({ getFieldValue }) => {
                            const sourceType = getFieldValue('sourceType');
                            return sourceType === CROWD_TYPE.XDT ? (
                                <ActiveCrowdCheckTree
                                    label="活动人群"
                                    name="crowdId"
                                    form={form}
                                    maxTagCount="responsive"
                                    disableIds={cronIdList}
                                    allowClear
                                    required
                                    hideAll
                                    labelCol={{ flex: '0 0 120px' }}
                                    wrapperCol={{ span: 16 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择活动人群',
                                        },
                                    ]}
                                />
                            ) : (
                                <Form.Item
                                    name="crowdId"
                                    label="活动人群"
                                    rules={[{ required: true, message: '请选择活动人群' }]}
                                    required
                                >
                                    <Select
                                        options={cdpOptions}
                                        allowClear
                                        showSearch
                                        showArrow
                                        mode="multiple"
                                        loading={cdpLoading || crondLoading}
                                        placeholder="请选择"
                                        filterOption={(input, option) =>
                                            (option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase()) ||
                                            (option?.value ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                    />
                                </Form.Item>
                            );
                        }}
                    </Form.Item>
                ) : (
                    <Form.Item label="活动人群">
                        <Tag title={editRecord?.crowdName} color="default">
                            {editRecord?.crowdName}
                        </Tag>
                    </Form.Item>
                )}
                <Form.Item
                    label="小程序展示标签"
                    name="appShowTagName"
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请填写小程序展示标签',
                        },
                    ]}
                >
                    <Input allowClear placeholder="请填写，最多6个字" maxLength={6} showCount />
                </Form.Item>
                <Form.Item wrapperCol={{ span: 24 }}>
                    <Row justify="center" style={{ width: '100%' }}>
                        <Space>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={addLoading || updateLoading}
                            >
                                保存
                            </Button>
                            <Button
                                type="default"
                                htmlType="reset"
                                onClick={onClose}
                                loading={addLoading || updateLoading}
                            >
                                取消
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(EditModal);
