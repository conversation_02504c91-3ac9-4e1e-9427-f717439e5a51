import { Col, Form, Input, Select } from 'antd';
import type { FormInstance } from 'antd';
import React from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import { CrowdStatusOptions, CrowdTypeOptions } from '@/constants/crowd';
import ActiveCrowdCheckTree from '../../../BusinessActive/ModuleActive/components/ModuleCrowdCheckTree';

const SearchLayout: React.FC<{
    form: FormInstance;
    listLoading: boolean;
    onSubmit: (formData: any) => void;
    onReset: () => void;
}> = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;
    const onFinish = (values: any) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };
    return (
        <Form
            form={form}
            onFinish={onFinish}
            labelAlign="right"
            labelCol={{ flex: '0 0 80px' }}
            wrapperCol={{ span: 22 }}
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={48}>
                <Col span={8}>
                    <Form.Item label="人群来源" name="sourceType">
                        <Select options={CrowdTypeOptions} allowClear placeholder="请选择" />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.sourceType !== curValues.sourceType
                        }
                    >
                        {({ getFieldValue }) => {
                            const sourceType = getFieldValue('sourceType');
                            return (
                                <Form.Item label="活动人群" name="crowdId">
                                    <ActiveCrowdCheckTree
                                        sourceType={sourceType}
                                        mode={''}
                                        noDisabledOption
                                    />
                                </Form.Item>
                            );
                        }}
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="展示标签" name="appShowTagName">
                        <Input maxLength={30} allowClear placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="状态" name="status">
                        <Select options={CrowdStatusOptions} allowClear placeholder="请选择" />
                    </Form.Item>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
