import React, { useRef, useState, forwardRef, useImperativeHandle, useMemo, Fragment } from 'react';

import { Modal, Card, Table, Form, Input, message } from 'antd';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import { useRequest } from 'ahooks';
import { saveImageManagePage } from '@/services/MngBilApi';

const AddModal: React.FC<any> = forwardRef((props, ref) => {
    const { callback } = props;
    const [visible, updateVisible] = useState(false);

    const [form] = Form.useForm();

    useImperativeHandle(ref, () => {
        return {
            open: (record?: Record<string, any>) => {
                if (typeof record == 'object') {
                    form?.setFieldsValue(record);
                }
                openModal();
            },
            close: closeModal,
        };
    });
    const openModal = () => {
        updateVisible(true);
    };
    const closeModal = () => {
        updateVisible(false);
        form?.resetFields();
    };

    const { run: submitEvent, loading: submitLoading } = useRequest(
        async () => {
            try {
                const values = await form.validateFields();

                await saveImageManagePage(values);
                message.success('保存成功');
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess: () => {
                closeModal();
                callback && callback();
            },
        },
    );

    return (
        <Fragment>
            <Modal
                title="新增图片"
                width={600}
                visible={visible}
                onCancel={closeModal}
                onOk={submitEvent}
                destroyOnClose
                okButtonProps={{
                    loading: submitLoading,
                }}
            >
                <Card bordered={false}>
                    <Form form={form} labelCol={{ flex: '0 0 110px' }}>
                        <Form.Item name="id" noStyle></Form.Item>
                        <Form.Item
                            name="imgName"
                            label="图片名称"
                            rules={[{ required: true, message: '请输入图片名称' }]}
                        >
                            <Input placeholder="请输入图片名称" />
                        </Form.Item>
                        <Form.Item
                            label="图片"
                            name="imgUrl"
                            wrapperCol={{ span: 24 }}
                            rules={[
                                {
                                    required: true,
                                    whitespace: true,
                                    message: '请配置图片',
                                },
                            ]}
                        >
                            <UpLoadImgItem
                                uploadData={{
                                    contentType: '02',
                                    contRemrk: 'community',
                                    relaTable: 'e_wechat_community',
                                }}
                                sizeInfo={{
                                    size: 200,
                                }}
                            ></UpLoadImgItem>
                        </Form.Item>
                    </Form>
                </Card>
            </Modal>
        </Fragment>
    );
});

export default AddModal;
