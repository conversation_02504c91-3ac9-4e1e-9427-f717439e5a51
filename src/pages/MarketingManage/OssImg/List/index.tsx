import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Card, message, Button, Typography, Popconfirm } from 'antd';
import XdtProTable from '@/components/XdtProTable/index';
import { useRef } from 'react';
import Thumbnail from '@/components/ShowPicture/Thumbnail';
import { useRequest } from 'ahooks';
import AddModal from './components/AddModal';
import { getImageManageListPage, deleteImageManagePage } from '@/services/MngBilApi';

const OssImgListPage = (props: any) => {
    const actionRef = useRef();
    const addRef = useRef();

    const { run: deleteEvent, loading: delLoading } = useRequest(
        async (id) => {
            try {
                await deleteImageManagePage({ id });
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );
    const columns = [
        {
            title: '图片名称',
            dataIndex: 'imgName',
        },
        {
            title: '图片',
            dataIndex: 'imgUrl',
            hideInSearch: true,
            render: (_, record) => {
                return (
                    <div style={{ width: '100px', height: '100px' }}>
                        <Thumbnail url={record?.imgUrl} disabled />
                    </div>
                );
            },
        },
        {
            title: '图片oss地址',
            dataIndex: 'imgUrl',
            hideInSearch: true,
            ellipsis: 2,
        },

        {
            title: '操作',
            hideInSearch: true,

            render: (_, record) => {
                return (
                    <Popconfirm
                        title={`确定删除？`}
                        okText="是"
                        cancelText="否"
                        onConfirm={() => deleteEvent(record.id)}
                        disabled={delLoading}
                    >
                        <Typography.Link type="danger" onClick={() => {}}>
                            删除
                        </Typography.Link>
                    </Popconfirm>
                );
            },
        },
    ];
    return (
        <>
            <PageHeaderWrapper>
                <Card>
                    <XdtProTable
                        actionRef={actionRef}
                        rowKey={(record: any) => record?.id}
                        requestApi={getImageManageListPage}
                        columns={columns}
                        hasSort
                        toolbar={{
                            title: [
                                <Button
                                    key="add"
                                    type="primary"
                                    onClick={() => {
                                        addRef?.current?.open();
                                    }}
                                >
                                    上传图片
                                </Button>,
                            ],
                        }}
                    ></XdtProTable>
                </Card>
                <AddModal
                    ref={addRef}
                    callback={() => {
                        actionRef?.current?.reload();
                    }}
                ></AddModal>
            </PageHeaderWrapper>
        </>
    );
};

export default OssImgListPage;
