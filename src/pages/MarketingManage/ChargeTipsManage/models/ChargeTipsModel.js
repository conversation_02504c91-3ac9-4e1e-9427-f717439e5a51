import { getTipsListApi } from '@/services/Marketing/MarketingTipsApi';

const tipsModel = {
    namespace: 'tipsModel',
    state: {
        tipsList: [], // 小提示列表
        tipsListTotal: 0,
    },
    effects: {
        /**
         * 小提示列表
         */
        *getTipsList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getTipsListApi, options);

                yield put({
                    type: 'updateTipsList',
                    tipsList: records,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateTipsList(state, { tipsList, total }) {
            return {
                ...state,
                tipsList,
                tipsListTotal: total,
            };
        },
    },
};
export default tipsModel;
