import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Cascader,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { exportTableByParams, formatExportColumns } from '@/utils/utils';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import {
    deleteTipsApi,
    saveTipsApi,
    getTipsListApiPath,
} from '@/services/Marketing/MarketingTipsApi';
import styles from '@/assets/styles/common.less';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import { isEmpty } from '@/utils/utils';

import SearchOptionsBar from '@/components/SearchOptionsBar';

const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const formItemLayout = {};

const formItemEditLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { dispatch, listLoading, form, onSubmit, onReset, history, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem
                        label="创建时间:"
                        name="dates"
                        // rules={[
                        //     ({ getFieldValue }) => ({
                        //         validator(rule, value) {
                        //             if (!value) {
                        //                 return Promise.reject('请选择日期');
                        //             }
                        //             if (!value[0]) {
                        //                 return Promise.reject('请选择开始日期');
                        //             }
                        //             if (!value[1]) {
                        //                 return Promise.reject('请选择结束日期');
                        //             }
                        //             if (value[0] && value[1]) {
                        //                 const startTime = +new Date(value[0]);
                        //                 const endTime = +new Date(value[1]);
                        //                 const dest = 60 * 1000 * 60 * 24 * 60;

                        //                 if (Math.abs(startTime - endTime) > dest) {
                        //                     return Promise.reject('选取范围最大不超过60天');
                        //                 }
                        //             }
                        //             return Promise.resolve();
                        //         },
                        //     }),
                        // ]}
                    >
                        <RangePicker format="YYYY-MM-DD" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="标题:" name="hintTitle" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ManagePage = (props) => {
    const {
        dispatch,
        history,
        tipsModel: { tipsList, tipsListTotal },
        global: { pageInit, channelOptions = [] },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [editForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    const [showEdit, changeShowEdit] = useState(false);
    const [editInfo, updateEditInfo] = useState(null);

    useEffect(() => {
        if (!channelOptions || channelOptions.length == 0) {
            dispatch({
                type: 'global/initChannelOptions',
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            hintTitle: data?.hintTitle || '',
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'tipsModel/getTipsList',
            options: params,
        });
    };

    // 导出
    const exportFormEvent = async () => {
        const data = form.getFieldsValue();
        const params = {
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            hintTitle: data?.hintTitle || '',
        };
        await getTipsListApiPath(params);
        // const columnsStrs = formatExportColumns(columns);
        // exportTableByParams({
        //     methodUrl: getTipsListApiPath,
        //     options: params,
        //     columnsStr: columnsStrs,
        // });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '标题',
            width: 200,
            dataIndex: 'hintTitle',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '内容',
            width: 400,
            dataIndex: 'hintContent',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '投放渠道',
            width: 200,
            dataIndex: 'hintChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                const editBtn = (
                    <span
                        key="edit"
                        className={styles['table-btn']}
                        onClick={() => openEditEvent(record)}
                    >
                        编辑
                    </span>
                );
                const deleteBtn = (
                    <span
                        key="del"
                        className={styles['table-btn']}
                        onClick={() => delEditEvent(record)}
                    >
                        删除
                    </span>
                );

                btnList.push(editBtn);

                btnList.push(deleteBtn);

                return <Space>{btnList}</Space>;
            },
        },
    ];

    const openAddEvent = () => {
        changeShowEdit(true);
    };

    const openEditEvent = (item) => {
        changeShowEdit(true);

        editForm.setFieldsValue({
            hintNo: item.hintNo,
            hintTitle: item.hintTitle,
            hintContent: item.hintContent,
            hintChannel: !isEmpty(item.hintChannel) ? item.hintChannel.split(',') : [],
        });
        updateEditInfo(item);
    };
    const closeEditEvent = () => {
        changeShowEdit(false);
        editForm.resetFields();
        updateEditInfo(null);
    };

    const delEditEvent = async (item) => {
        confirm({
            title: `确定删除${item.hintTitle}?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await deleteTipsApi({ hintNo: item.hintNo });
                    searchData();
                    message.success('删除成功');
                    return;
                } catch (error) {
                    return Promise.reject(error);
                }
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const submitSaveEvent = async (values) => {
        try {
            let params = {
                hintTitle: values.hintTitle,
                hintContent: values.hintContent,
                hintChannel: values.hintChannel.join(','),
            };
            if (editInfo?.hintNo) {
                params.hintNo = editInfo.hintNo;
            }
            await saveTipsApi(params);
            message.success('保存成功');
            await searchData();
            closeEditEvent();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={openAddEvent}>
                        + 新建
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={tipsList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: tipsListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={`${editInfo ? '编辑' : '新增'}提示`}
                destroyOnClose
                width={550}
                visible={showEdit}
                onCancel={() => {
                    closeEditEvent();
                }}
                footer={null}
                maskClosable={false}
            >
                <Form
                    form={editForm}
                    {...formItemEditLayout}
                    wrapperCol={{ span: 12 }}
                    onFinish={submitSaveEvent}
                >
                    <FormItem
                        label="标题"
                        name="hintTitle"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="请填写"
                            autoComplete="off"
                            maxLength={25}
                            showCount
                        ></Input>
                    </FormItem>

                    <CheckBoxGroup
                        label="投放渠道"
                        name={'hintChannel'}
                        form={editForm}
                        selectList={channelOptions}
                        required
                        rules={[{ required: true, message: '请选择投放渠道' }]}
                        valueType="select"
                    ></CheckBoxGroup>

                    <FormItem
                        label="内容"
                        name="hintContent"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <TextArea
                            placeholder="请填写"
                            autoComplete="off"
                            rows={6}
                            maxLength={40}
                            showCount
                        ></TextArea>
                    </FormItem>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEditEvent}>取消</Button>
                    </Space>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, tipsModel, loading }) => ({
    global,
    tipsModel,
    listLoading: loading.effects['tipsModel/getTipsList'],
}))(ManagePage);
