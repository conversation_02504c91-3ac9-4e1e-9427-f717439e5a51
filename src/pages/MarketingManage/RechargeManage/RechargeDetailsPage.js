import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useContext,
    forwardRef,
    useImperativeHandle,
} from 'react';
import { connect, Link } from 'umi';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import { saveRechargeApi, getRechargeRatioApi } from '@/services/Marketing/MarketingRechargeApi';
import {
    LeftOutlined,
    LoadingOutlined,
    PlusOutlined,
    InfoCircleOutlined,
    MinusCircleOutlined,
} from '@ant-design/icons';
import { uploadFile } from '@/services/CommonApi';
import Thumbnail from '@/components/ShowPicture/Thumbnail';
import SelectColor from '@/components/SelectColor';
import { RANGE_TYPES } from '@/config/declare';
import OperStationSearchList from '@/components/OperStationSearchList/SearchList';
import OperCitySelectItem from '@/components/OperCitySelectItem/index';

import { RECHARGE_STATUS } from './RechargeConfig';

import { isEmpty } from '@/utils/utils';
import {
    Button,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    TimePicker,
    Popconfirm,
    TreeSelect,
    Table,
    Upload,
} from 'antd';

import OperSelectItem from '@/components/OperSelectItem';
import StationListByCityForOper from '@/components/StationListByCityForOper';
import styles from './RechargeDetailsPage.less';
import { isRegExp } from 'lodash';
import { SELECT_TYPES } from '@/config/declare';

const EditableContext = React.createContext();

const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

const EditableCell = (props) => {
    const {
        title,
        editable,
        children,
        dataIndex,
        record,
        handleSave,
        parentForm,
        ratio,
        index,
        planList,
        changePlanList,
        childFailInfo,
        changeChildFailInfo,
        ...restProps
    } = props;

    const [editing, setEditing] = useState(false);
    const inputRef = useRef();
    const form = useContext(EditableContext);
    useEffect(() => {
        if (editing) {
            inputRef.current.focus();
        }
    }, [editing]);

    const nowValue = useMemo(() => {
        if (index >= 0) {
            return (planList[index] && planList[index][dataIndex]) || '';
        }
        return '';
    }, [planList, index]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({
            [dataIndex]: record[dataIndex],
        });
    };

    const save = async (e) => {
        try {
            const values = await form.validateFields();
            toggleEdit();
            handleSave({ ...record, ...values });
            const tableFailInfo = childFailInfo;
            delete tableFailInfo[index];
            changeChildFailInfo(tableFailInfo);
        } catch (errInfo) {
            console.log('Save failed:', errInfo, index);
            changeChildFailInfo({ [index]: true });
        }
    };

    let childNode = children;

    if (editable) {
        childNode = editing ? (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                required
                rules={[
                    // {
                    //     required: true,
                    //     message: `请填写`,
                    // },

                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            const rechargeSchemeDetails =
                                parentForm.getFieldValue('rechargeSchemeDetails');
                            // console.log(3333333333, value, record.key, props);
                            if (value !== 0 && !value) {
                                return Promise.reject('请填写');
                            }

                            const reg = /^([0]|([1-9][0-9]*)|(([0]\.\d{1,}|[1-9][0-9]*\.\d{1,})))$/;
                            if (!reg.test(value)) {
                                return Promise.reject('输入值必须大于0');
                            }

                            if (dataIndex == 'principal') {
                                for (let i = 0; i < rechargeSchemeDetails.length; i++) {
                                    const element = rechargeSchemeDetails[i];

                                    if (element[dataIndex] == value) {
                                        if (i != record.key - 1) {
                                            if (record.schemeDetailId && element.schemeDetailId) {
                                                if (
                                                    record.schemeDetailId != element.schemeDetailId
                                                ) {
                                                    return Promise.reject('已配置对应充值方案');
                                                }
                                            } else {
                                                return Promise.reject('已配置对应充值方案');
                                            }
                                        }
                                    }
                                    // if (i == record.key - 1 && element.benefit > value) {
                                    //     return form.validateFields();
                                    // }
                                }
                                if (Number(value) == 0) {
                                    return Promise.reject('输入值必须大于0');
                                }
                            }

                            // console.log(555555, record, dataIndex, value);
                            if (dataIndex == 'principal') {
                                if (!value) {
                                    return Promise.reject('请先配置本金');
                                }
                                if (record.benefit > value * ratio) {
                                    return Promise.reject(`馈赠金配置比例不得大于本金的${ratio}倍`);
                                }
                            } else if (dataIndex == 'benefit') {
                                if (record.principal > 0 && record.principal * ratio < value) {
                                    return Promise.reject(`馈赠金配置比例不得大于本金的${ratio}倍`);
                                }
                            }

                            // reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                            // if (!reg.test(value)) {
                            //     return Promise.reject('请保留2位小数');
                            // }

                            if (dataIndex == 'principal') {
                                const newPlanList = planList;
                                const nowIndex = index;
                                if (newPlanList[nowIndex]) {
                                    newPlanList[nowIndex].principal = value;

                                    changePlanList(newPlanList);
                                }
                            } else if (dataIndex == 'benefit') {
                                const newPlanList = planList;
                                const nowIndex = index;
                                if (newPlanList[nowIndex]) {
                                    newPlanList[nowIndex].benefit = value;

                                    changePlanList(newPlanList);
                                }
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <InputNumber
                    value={nowValue}
                    max={5000}
                    precision={2}
                    step={0.01}
                    min={0}
                    ref={inputRef}
                    onPressEnter={save}
                    onBlur={save}
                />
            </Form.Item>
        ) : (
            <div
                className="editable-cell-value-wrap"
                style={{
                    paddingRight: 24,
                }}
                onClick={toggleEdit}
            >
                {children}
            </div>
        );
    }

    return <td {...restProps}>{childNode}</td>;
};

const { Option } = Select;

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;

// 上传图片按钮
const UploadButton = ({ uploading }) => (
    <div>
        {uploading ? <LoadingOutlined /> : <PlusOutlined />}
        <div className="ant-upload-text">上传</div>
    </div>
);

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
    wrapperCol: {
        span: 12,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    PART: '2', //部分
};
const PrizeLayout = forwardRef((props, ref) => {
    const {
        rechargeManageModel: {
            rechargeDetails: { rechargeSchemeDetails },
        },
        childFailInfo,
        changeChildFailInfo,
        form,
        listLoading,
        ratio,
        isLock,
    } = props;

    const [showPrizeView, togglePrizeView] = useState(false); // 选择奖品弹窗
    const [planList, changePlanList] = useState([]); // table编辑错误信息汇总
    const [editCpnInfo, changeEditCpnInfo] = useState(null);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            changePlanList([]);
        },
    }));

    useEffect(() => {
        if (rechargeSchemeDetails) {
            changePlanList(rechargeSchemeDetails);
        }
    }, [rechargeSchemeDetails]);

    useEffect(() => {
        const list = planList.map((ele) => ({
            promotionPlanId: ele.promotionPlanId,
            schemeDetailId: ele.schemeDetailId,
            schemeId: ele.schemeId,
            principal: ele.principal,
            benefit: ele.benefit,
        }));
        form.setFieldsValue({ rechargeSchemeDetails: list });
    }, [planList]);

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    // 新增
    const addRechargeEvent = () => {
        if (planList.length >= 10) {
            message.error('最多配置10条充值方案!');
            return;
        }
        const newData = {
            principal: ``,
            benefit: '',
            key: planList.length + 1,
        };
        changePlanList([...planList, newData]);
    };

    const handleDelete = (index) => {
        const dataSource = [...planList];
        dataSource.splice(index, 1);

        changePlanList(dataSource.map((ele, eleIndex) => ({ ...ele, key: eleIndex })));
    };

    const handleSave = (row) => {
        // const newData = [...row];
        const newData = [...planList];
        const index = newData.findIndex(
            (item) =>
                (row.schemeDetailId && row.schemeDetailId === item.schemeDetailId) ||
                (row.key && row.key == item.key),
        );

        if (index >= 0) {
            newData.splice(index, 1, { ...newData[index], ...row });
        }

        changePlanList(newData);
    };

    const prizeColumns = [
        {
            title: '序号 ',
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '本金',
            dataIndex: 'principal',
            editable: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '馈赠金',
            dataIndex: 'benefit',
            editable: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    if (!isLock) {
        prizeColumns.push({
            title: '操作',
            render(text, record, index) {
                return (
                    <Popconfirm title="确认删除?" onConfirm={() => handleDelete(index)}>
                        <a>删除</a>
                    </Popconfirm>
                );
            },
        });
    }

    const columns = prizeColumns.map((col) => {
        if (!col.editable) {
            return col;
        }

        return {
            ...col,
            onCell: (record, index) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave,
                parentForm: form,
                planList,
                changePlanList,
                childFailInfo,
                changeChildFailInfo,
                index,
                ratio,
            }),
        };
    });

    return (
        <Fragment>
            <FormItem
                name="rechargeSchemeDetails"
                label="方案明细:"
                {...formItemLayout}
                required
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value || value.length == 0) {
                                return Promise.reject('请配置方案');
                            }
                            for (const item of value) {
                                for (const key in item) {
                                    if (item.hasOwnProperty(key)) {
                                        const element = item[key];

                                        if (key == 'principal') {
                                            if (!element) {
                                                return Promise.reject('请配置方案');
                                            }
                                        } else if (key == 'benefit') {
                                            const benefit = Number(element);
                                            if (typeof benefit != 'number') {
                                                return Promise.reject('请配置方案');
                                            }
                                        }

                                        if (Number(item.benefit) > Number(item.principal) * ratio) {
                                            return Promise.reject(
                                                `馈赠金配置比例不得大于本金的${ratio}倍`,
                                            );
                                        }
                                    }
                                }
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Button disabled={isLock} type="primary" onClick={addRechargeEvent}>
                    +新增
                </Button>
            </FormItem>
            <FormItem
                {...{
                    wrapperCol: {
                        span: 24,
                    },
                }}
            >
                <Table
                    loading={listLoading}
                    scroll={null}
                    dataSource={planList}
                    pagination={false}
                    columns={columns}
                    {...(isLock ? {} : { components })}
                    rowClassName={() => 'editable-row'}
                    bordered
                />
            </FormItem>
        </Fragment>
    );
});

const ResultModalView = (props) => {
    const { visible, actName, onClose, resultList } = props;
    const columns = [
        {
            title: '冲突活动',
            dataIndex: 'oldSchemeName',
            editable: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本次活动',
            dataIndex: 'actTypeName',
            editable: true,
            render(text, record) {
                return <span title={actName}>{actName}</span>;
            },
        },
        {
            title: '活动类型',
            dataIndex: 'actTypeName',
            editable: true,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '冲突场站',
            dataIndex: 'conflictStation',
            editable: true,
            render(text, record) {
                return (
                    <Tooltip title={text}>
                        <div
                            style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: '5',
                                whiteSpace: 'pre',
                            }}
                        >
                            {text}
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: '操作',
            render(text, record) {
                return (
                    <Link
                        to={`/marketing/rechargeManage/rechargeActive/list/update/${record.oldSchemeId}`}
                        target="_blank"
                    >
                        立即解决
                    </Link>
                );
            },
        },
    ];
    return (
        <Modal
            title={'活动冲突提醒'}
            destroyOnClose
            width={1000}
            visible={visible}
            onCancel={() => {
                onClose && onClose();
            }}
            footer={null}
            maskClosable={false}
        >
            <h3>本次活动与已有活动存在冲突，请优先处理</h3>
            <Space direction="vertical" size={5} style={{ color: 'gray' }}>
                <span>解决方法：</span>
                <span>1.将冲突场站从本次活动或冲突活动中剔除，使场站只在一场活动中存在</span>
                <span>2.调整活动时间，让本次活动与冲突活动在时间上错开</span>
            </Space>
            <br></br>

            <Table
                scroll={null}
                dataSource={resultList}
                pagination={false}
                columns={columns}
                rowClassName={() => 'editable-row'}
            />
        </Modal>
    );
};

// 子编辑页面
const RechargeDetailsPage = (props) => {
    const {
        dispatch,
        match,
        history,
        location,
        route,
        rechargeManageModel: { rechargeDetails },
        global: { operatorList },
        user: { currentUser },
    } = props;
    const [isLock, changeLock] = useState(() => {
        if (route.path.indexOf('/marketing/rechargeManage/rechargeActive/list/details') >= 0) {
            return true;
        }
        return false;
    }); // 是否可编辑
    const isActDoingLock = useMemo(() => {
        return (
            (rechargeDetails &&
                (rechargeDetails.state === RECHARGE_STATUS.USING ||
                    rechargeDetails.state === RECHARGE_STATUS.INVALID)) ||
            isLock
        );
    }, [rechargeDetails, isLock]);
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [schemeId, changeSchemeId] = useState(match.params.schemeId || null);

    const [showResultModal, toggleResultModal] = useState(false);

    const [imageUrl, changeImageUrl] = useState(''); // 上传文件
    const [uploading, changeUploading] = useState(false); // 上传加载

    const [resultList, changeResultList] = useState([]); //冲突结果列表

    const [ratio, changeRatio] = useState(1); // 本金馈赠金比例

    const [childFailInfo, changeChildFailInfo] = useState({});

    const [form] = Form.useForm();

    const schemeName = Form.useWatch('schemeName', form);

    const operIdValue = Form.useWatch('operId', form);

    const prizeRef = useRef();

    const operSelectList = useRef();

    const operStationRef = useRef();

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    const pageType = useMemo(() => {
        if (match.path == '/marketing/rechargeManage/rechargeActive/list/update/:schemeId') {
            return 'update';
        }
        if (match.path == '/marketing/rechargeManage/rechargeActive/list/add') {
            return 'add';
        }
        if (match.path == '/marketing/rechargeManage/rechargeActive/list/details/:schemeId') {
            return 'details';
        }
        return '';
    }, [match.path]);

    const pageTitle = useMemo(() => {
        if (pageType == 'update') {
            return '编辑充值活动';
        }
        if (pageType == 'add') {
            return '新增充值活动';
        }
        if (pageType == 'details') {
            return '充值活动详情';
        }
        return '';
    }, [pageType]);

    useEffect(() => {
        if (schemeId) {
            dispatch({
                type: 'rechargeManageModel/initRechargeDetails',
                schemeId,
            });
        }

        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }

        return () => {
            dispatch({
                type: 'rechargeManageModel/updateRechargeDetails',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        if (rechargeDetails) {
            initEditInfo(rechargeDetails);
        }
    }, [rechargeDetails]);

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const params = {};
            if (info.schemeName) {
                params.schemeName = info.schemeName;
            }
            if (info.cardSubtitle) {
                params.cardSubtitle = info.cardSubtitle;
            }

            if (info.cardColor) {
                params.cardColor = info.cardColor;
            }

            if (info.operId) {
                params.operId = info.operId;
                changeRatioByOper(info.operId);
            }
            if (info.cardLogo) {
                params.cardLogo = info.cardLogo;
                changeImageUrl(info.cardLogo);
            }
            if (info.effTime && info.expTime) {
                params.dateTime = [moment(info.effTime), moment(info.expTime)];
            }
            if (info.rechargeSchemeDetails) {
                params.rechargeSchemeDetails = info.rechargeSchemeDetails;
            }
            if (info.scopeType) {
                params.scopeType = info.scopeType;
                if (params.scopeType !== CAPITALTYPS.ALL) {
                    if (!isEmpty(info.actCitys)) {
                        params.actCitys = info.actCitys?.split(',') || [];
                    }
                    // if (!isEmpty(info.scopeInfo)) {
                    //     params.actStations = info.scopeInfo;
                    // }
                }
            }
            if (info.contributParty) {
                params.contributParty = info.contributParty?.split(',') || [];
            }
            if (info.platRate >= 0) {
                params.platRate = info.platRate;
            }
            form.setFieldsValue(params);
            return;
        } catch (error) {
            console.log(33333, error);
            return Promise.reject(error);
        }
    };

    /**
     * 保存优惠券
     * type  save/send
     */
    const saveCouponEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();

            const params = {
                saveType: type,
                schemeName: values.schemeName,

                cardSubtitle: values.cardSubtitle,
                cardColor: values.cardColor,
                // cardLogo: values.cardLogo,
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                operId: values.operId,
                scopeType: values.scopeType,
                contributParty: values.contributParty?.join(','),
            };

            if (params.scopeType !== CAPITALTYPS.ALL) {
                if (values.actCitys instanceof Array) {
                    params.actCitys = values.actCitys.join(',');
                }
                params.stationInfo = values.stationInfo || undefined;
                params.delScopeInfo = values.delScopeInfo || undefined;
                params.actStations = JSON.stringify(values.actStations);
            }
            if (values.contributParty?.includes('01') && values.contributParty?.includes('02')) {
                params.platRate = values.platRate;
            }

            const failSum = Object.keys(childFailInfo);
            if (failSum.length > 0) {
                message.error('方案填写有误,请修改后再提交');
                throw new Error();
            }
            if (values.rechargeSchemeDetails) {
                for (const item of values.rechargeSchemeDetails) {
                    if (isEmpty(item.principal)) {
                        message.error('本金填写有误');
                        throw new Error();
                    }
                    if (isEmpty(item.benefit)) {
                        message.error('馈赠金填写有误');
                        throw new Error();
                    }
                }
                params.rechargeSchemeDetails = JSON.stringify(values.rechargeSchemeDetails);
            }

            if (schemeId) {
                params.schemeId = schemeId;
            }
            if (rechargeDetails && rechargeDetails?.state === RECHARGE_STATUS.INVALID) {
                params.state = RECHARGE_STATUS.INVALID;
            }

            changeSubmitLoading(true);

            const { data } = await saveRechargeApi(params);

            const { conflictFlag, conflictList } = data;

            // if (newActId) {
            //     changeSchemeId(newActId);
            // }
            if (!conflictFlag) {
                message.success('提交成功');
                goBack();
            } else {
                openResultModal(conflictList);
            }
        } catch (error) {
            console.log(9999, error);
        } finally {
            changeSubmitLoading(false);
        }
    };
    const goBack = () => {
        history.go(-1);
    };

    const openResultModal = (list) => {
        changeResultList(list);
        toggleResultModal(true);
    };

    const closeResultModal = () => {
        toggleResultModal(false);
        changeResultList([]);
    };

    const changeOperEvent = async (operId) => {
        try {
            form.setFieldsValue({
                rechargeSchemeDetails: [],
            });
            prizeRef.current.rest();
            changeRatioByOper(operId);
        } catch (error) {
            console.log(9999, error);
        }
    };
    const changeRatioByOper = async (operId) => {
        try {
            if (!operId) return;
            const {
                data: { codeName },
            } = await getRechargeRatioApi({
                codeType: 'payprivateRechargeRatio',
                codeName: operId,
            });
            changeRatio(Number(codeName));
        } catch (error) {}
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('图片格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 / 1024 < 1;
            if (!isLt2M) {
                message.error('图片不大于 1mb!');
                rej();
                return;
            }

            const reader = new FileReader();
            reader.onload = function (e) {
                const data = e.target.result;
                // 加载图片获取图片真实宽度和高度
                const image = new Image();
                image.onload = function () {
                    const { width } = image;
                    const { height } = image;
                    const isAllow = width == 88 && height == 88;
                    if (!isAllow) {
                        message.error('背景图片尺寸必须为88*88');
                    }
                    if (isJpgOrPng && isLt2M && isAllow) {
                        res();
                    } else {
                        rej();
                    }
                };
                image.src = data;
            };
            reader.readAsDataURL(file);
        });

    const handleUploadChange = (imgUrl) => {
        changeImageUrl(imgUrl);
        form.setFieldsValue({ cardLogo: imgUrl });
    };

    const uploadProps = {
        name: 'avatar',
        className: styles['avatar-uploader'],
        showUploadList: false,
        // action: '/oper/uploadCertImage',
        data: {
            contentType: '02',
            contRemrk: 'recharge',
            relaTable: 'RECHARGE_SCHEME',
        },
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const params = {
                    ...data,
                };
                // if (schemeId) {
                //     params.relaId = schemeId;
                // }
                const formData = new FormData();
                if (params) {
                    Object.keys(params).forEach((key) => {
                        formData.append(key, params[key]);
                    });
                }
                formData.append(data.contRemrk, file);
                const {
                    data: { filePath, fileId },
                } = await uploadFile(formData);
                onSuccess(filePath);
            } catch (error) {}

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType: 'picture-card',
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };

    const actScopeRender = useMemo(() => {
        if (currentUser?.operId) {
            return (
                <FormItem
                    label="活动范围"
                    name="scopeType"
                    initialValue={RANGE_TYPES.ALL}
                    noStyle
                ></FormItem>
            );
        } else {
            return (
                <Fragment>
                    <h1 className={commonStyles['form-title']}>活动范围</h1>

                    <FormItem
                        label="活动范围"
                        name="scopeType"
                        initialValue={RANGE_TYPES.OTHER}
                        rules={[
                            { required: true, message: '请选择' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    const actCitys = getFieldValue('actCitys');
                                    const actStations = getFieldValue('actStations');
                                    if (value === RANGE_TYPES.OTHER) {
                                        if (isEmpty(actCitys) && isEmpty(actStations)) {
                                            return Promise.reject('请配置活动城市或指定场站');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <Radio.Group disabled={isLock}>
                            <Radio value={RANGE_TYPES.ALL}>全部</Radio>
                            <Radio value={RANGE_TYPES.OTHER} disabled={!operIdValue}>
                                选择区域
                            </Radio>
                        </Radio.Group>
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) => {
                            return (
                                prevValues.scopeType !== curValues.scopeType ||
                                prevValues.operId !== curValues.operId
                            );
                        }}
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const scopeType = getFieldValue('scopeType');
                            const operId = getFieldValue('operId');
                            return scopeType == RANGE_TYPES.OTHER ? (
                                <Fragment>
                                    <FormItem label="活动城市" name="actCitys">
                                        <OperCitySelectItem
                                            operId={operId}
                                            onChange={() => {
                                                form.validateFields(['scopeType']);
                                            }}
                                        ></OperCitySelectItem>
                                    </FormItem>

                                    <OperStationSearchList
                                        title="活动场站"
                                        keyName="actStations"
                                        form={form}
                                        stationList={rechargeDetails?.scopeInfo}
                                        disabled={isLock}
                                        currentUser={currentUser}
                                        limitOperIds={operId ? [{ operId }] : undefined}
                                        initRef={operStationRef}
                                        hideRange
                                        formItemLayout={{
                                            ...formItemLayout,
                                            wrapperCol: { span: 24 },
                                        }}
                                        requestInfo={
                                            schemeId && {
                                                recordParams: {
                                                    relateId: schemeId,
                                                    scene: 'stc_invest',
                                                },
                                            }
                                        }
                                    />
                                </Fragment>
                            ) : null;
                        }}
                    </FormItem>
                </Fragment>
            );
        }
    });

    return (
        <PageHeaderWrapper
            title={
                <Space>
                    <LeftOutlined onClick={goBack} />
                    {pageTitle}
                </Space>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    initialValues={{
                        scopeType: CAPITALTYPS.ALL,
                    }}
                    scrollToFirstError
                >
                    <h1 className={commonStyles['form-title']}>活动信息</h1>
                    <OperSelectItem
                        name="operId"
                        operatorList={operatorList}
                        disabled={isLock || schemeId}
                        rules={[{ required: true, message: '请选择运营商' }]}
                        onChange={changeOperEvent}
                        // purchase={SELECT_TYPES.EXCEPTBUY}
                    />
                    <FormItem
                        label={<span>活动名称</span>}
                        name="schemeName"
                        rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
                    >
                        <Input
                            disabled={isLock}
                            maxLength={20}
                            placeholder="名称用于区分活动"
                            autoComplete="off"
                        />
                    </FormItem>
                    <FormItem
                        name="dateTime"
                        label="活动有效期:"
                        rules={[
                            { required: true, message: '请选择活动有效期' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择活动开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择活动失效日期');
                                    }
                                    if (value[1]) {
                                        const nowTime = +new Date();
                                        const sendEndTime = +new Date(value[1]);

                                        if (sendEndTime < nowTime) {
                                            return Promise.reject('活动失效日期不能早于当前时间');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        // validateStatus={timeStatus}
                        // help={timeStatus === 'error' && '请填写生效时间'}
                    >
                        <RangePicker
                            disabled={[
                                rechargeDetails?.state === RECHARGE_STATUS.USING || isLock,
                                isLock,
                            ]}
                            disabledTime={disabledRangeTime}
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: [
                                    moment('00:00:00', 'HH:mm:ss'),
                                    moment('23:59:59', 'HH:mm:ss'),
                                ],
                            }}
                            format="YYYY-MM-DD HH:mm:ss"
                        />
                    </FormItem>

                    <PrizeLayout
                        ref={prizeRef}
                        form={form}
                        ratio={ratio}
                        childFailInfo={childFailInfo}
                        changeChildFailInfo={changeChildFailInfo}
                        {...props}
                        isLock={isLock}
                    />

                    {actScopeRender}

                    {/* <FormItem
                        shouldUpdate={(prevValues, curValues) => {
                            return prevValues.contributParty !== curValues.contributParty;
                        }}
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const contributParty = getFieldValue('contributParty');
                            return (
                                !(currentUser && currentUser.operId) && (
                                    <FormItem label="出资方" name="contributParty">
                                        <CheckboxGroup disabled={isActDoingLock}>
                                            <Checkbox value={'02'}>运营商</Checkbox>
                                            <Checkbox value={'01'}>
                                                <Space>
                                                    平台
                                                    <FormItem name="platRate" noStyle>
                                                        <InputNumber
                                                            max={100}
                                                            precision={2}
                                                            step={0.01}
                                                            min={0}
                                                            addonAfter="%"
                                                            disabled={
                                                                !(
                                                                    contributParty?.includes(
                                                                        '01',
                                                                    ) &&
                                                                    contributParty?.includes('02')
                                                                ) || isActDoingLock
                                                            }
                                                            placeholder="平台出资占比"
                                                        ></InputNumber>
                                                    </FormItem>
                                                </Space>
                                            </Checkbox>
                                        </CheckboxGroup>
                                    </FormItem>
                                )
                            );
                        }}
                    </FormItem> */}

                    <h1 className={commonStyles['form-title']}>对外展示</h1>
                    {/* <FormItem
                        label={
                            <span>
                                会员卡名称
                                <Tooltip title="在用户充值时展示在商家会员卡面">
                                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                </Tooltip>
                            </span>
                        }
                        name="cardSubtitle"
                    >
                        <Input
                            disabled={isLock}
                            maxLength={32}
                            placeholder="请填写会员卡名称"
                            autoComplete="off"
                        />
                    </FormItem> */}
                    <FormItem
                        label={
                            <span>
                                会员卡副标题
                                <Tooltip title="在用户充值时展示在商家会员卡面">
                                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                </Tooltip>
                            </span>
                        }
                        name="cardSubtitle"
                        rules={[{ required: true, message: '请填写副标题' }]}
                    >
                        <Input
                            disabled={isLock}
                            maxLength={50}
                            placeholder="例：多充多送，最高得200元馈赠金"
                            autoComplete="off"
                        />
                    </FormItem>

                    {/* <FormItem
                        {...formItemLayout}
                        label="会员卡LOGO"
                        name="cardLogo"
                        rules={[{ required: true, message: '请上传图片' }]}
                    >
                        <FormItem noStyle>
                            <Upload {...uploadProps}>
                                {imageUrl ? (
                                    // <img
                                    //     src={imageUrl}
                                    //     alt="avatar"
                                    //     style={{ width: '100%' }}
                                    // />
                                    <Thumbnail url={imageUrl} />
                                ) : (
                                    <UploadButton uploading={uploading} />
                                )}
                            </Upload>
                        </FormItem>
                        <span className={styles['mark-text']}>
                            尺寸为88*88,格式支持png、jpg、jpeg,大小不得超过1M
                        </span>
                    </FormItem> */}

                    {/* <SelectColor
                        form={form}
                        name="cardColor"
                        label="背景色值"
                        autoComplete="off"
                        maxLength={20}
                        placeholder="请输入RGB色值"
                        rules={[{ required: true, message: '请填写' }]}
                        disabled={isLock}
                        required
                    /> */}

                    <FormItem
                        label="背景色值"
                        name="cardColor"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Select disabled={isLock} placeholder="请选择颜色">
                            <Option value="#ED762F">
                                <span className={styles['color-tip']}>
                                    <span style={{ backgroundColor: '#ED762F' }} />
                                    橙色
                                </span>
                            </Option>
                            <Option value="#3A3430">
                                <span className={styles['color-tip']}>
                                    <span style={{ backgroundColor: '#3A3430' }} />
                                    黑色
                                </span>
                            </Option>
                            <Option value="#0A89FF">
                                <span className={styles['color-tip']}>
                                    <span style={{ backgroundColor: '#0A89FF' }} />
                                    蓝色
                                </span>
                            </Option>
                        </Select>
                    </FormItem>

                    <div className={commonStyles['form-submit']}>
                        {isLock ? (
                            <Fragment>
                                {/* <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    onClick={() => {
                                        changeLock(false);
                                    }}
                                >
                                    编辑
                                </Button> */}
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        ) : (
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('send');
                                    }}
                                >
                                    提交
                                </Button>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        )}
                    </div>
                </Form>
            </Card>
            <ResultModalView
                actName={schemeName}
                resultList={resultList}
                visible={showResultModal}
                onClose={closeResultModal}
            ></ResultModalView>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, rechargeManageModel, loading }) => ({
    global,
    user,
    rechargeManageModel,
    detalisLoading: loading.effects['rechargeManageModel/getCouponInfo'],
}))(RechargeDetailsPage);
