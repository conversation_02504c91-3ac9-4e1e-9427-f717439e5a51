import {
    getCouponActiveListApi,
    getCouponActiveDetailApi,
} from '@/services/Marketing/MarketingCouponActiveApi';

const couponActiveModel = {
    namespace: 'couponActiveModel',
    state: {
        couponActiveList: [], // 翻牌列表
        couponActiveListTotal: 0,
        couponActiveHistoryList: [], // 发奖记录列表
        couponActiveHistoryTotal: 0,
        editActInfo: {}, // 当前详情信息
    },
    effects: {
        /**
         * 翻牌列表
         */
        *getCouponActiveList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getCouponActiveListApi, options);

                yield put({
                    type: 'updateCouponActiveList',
                    couponActiveList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 开奖列表
         */
        *getCouponActiveHistoryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getCouponActiveListApi, options);

                yield put({
                    type: 'updateCouponActiveHistoryList',
                    couponActiveList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ actId }, { call, put, select }) {
            try {
                const { data } = yield call(getCouponActiveDetailApi, actId);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateCouponActiveList(state, { couponActiveList, total }) {
            return {
                ...state,
                couponActiveList,
                couponActiveListTotal: total,
            };
        },
        updateCouponActiveHistoryList(state, { list, total }) {
            return {
                ...state,
                couponActiveHistoryList: list,
                couponActiveHistoryListTotal: total,
            };
        },
        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default couponActiveModel;
