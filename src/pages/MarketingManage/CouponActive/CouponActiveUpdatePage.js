import { PageHeaderWrapper } from '@ant-design/pro-layout';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { connect } from 'umi';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import { STATUS_TYPES } from '@/config/declare';
import { saveCouponActiveApi, addCpnApi } from '@/services/Marketing/MarketingCouponActiveApi';
import {
    Button,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    TimePicker,
    TreeSelect,
} from 'antd';

import TablePro from '@/components/TablePro';

import {
    LeftOutlined,
    InfoCircleOutlined,
    MinusCircleOutlined,
    LoadingOutlined,
} from '@ant-design/icons';
import OperTreeSelectList from '@/components/OperTreeSelectList';
import styles from './CouponActiveUpdatePage.less';

const { Option } = Select;

const { TextArea } = Input;

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
    wrapperCol: {
        span: 8,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    OPERATOR: '2', // 运营商
    CITY: '3', // 城市
};
const PrizeLayout = (props) => {
    const {
        dispatch,
        couponActiveModel: { editActInfo },
        form,
        listLoading,
        isLock,
    } = props;
    const { putCpnList } = editActInfo;

    const [showPrizeView, togglePrizeView] = useState(false); // 选择奖品弹窗
    const [cpnList, changeCpnList] = useState([]);
    const [editCpnInfo, changeEditCpnInfo] = useState(null);

    useEffect(() => {
        if (putCpnList) {
            changeCpnList(putCpnList);
        }
    }, [putCpnList]);

    useEffect(() => {
        const list = cpnList.map((ele) => ({
            winningRate: ele.winningRate,
            cpnId: ele.cpnId,
            unGetNum: ele.unGetNum,
            pushPrizeId: ele.pushPrizeId,
        }));
        form.setFieldsValue({ cpnList: list });
    }, [cpnList]);

    const getMaxRate = useMemo(() => {
        let max = 100;
        const countPushPrizeIds = [];
        for (const item of cpnList) {
            const ishas = countPushPrizeIds.indexOf(item.pushPrizeId) >= 0; // 是否改奖品ID已计算概率
            if (
                !editCpnInfo ||
                (item.cpnId != editCpnInfo.cpnId &&
                    !ishas &&
                    item.pushPrizeId != editCpnInfo.pushPrizeId)
            ) {
                // 不同券 不同奖品ID的概率计算
                max -= Number(item.winningRate);
                if (!ishas) {
                    countPushPrizeIds.push(item.pushPrizeId);
                }
            }
        }
        return max;
    }, [cpnList, editCpnInfo]);

    const formmatList = useMemo(() => {
        // 格式化追加券
        const list = [];
        for (const item of cpnList) {
            let hasIndex = -1;
            for (let index = 0; index < list.length; index++) {
                const element = list[index];
                if (
                    element.pushPrizeId &&
                    item.pushPrizeId &&
                    element.pushPrizeId == item.pushPrizeId
                ) {
                    hasIndex = index;
                    break;
                }
            }
            if (hasIndex >= 0) {
                item.isChildren = true;
                if (!list[hasIndex].children) {
                    list[hasIndex].children = [item];
                } else {
                    list[hasIndex].children.push(item);
                }
            } else {
                list.push(item);
            }
        }
        return list;
    }, [cpnList]);

    const closePrizeViewEvent = () => {
        togglePrizeView(false);
        changeEditCpnInfo(null);
    };
    const addPrizeEvent = (params) => {
        const item = cpnList.find((ele) => ele.cpnId && params.cpnId && ele.cpnId == params.cpnId);
        if (item) {
            message.error('已添加此优惠券');
            return;
        }
        changeCpnList([...cpnList, params]);
        togglePrizeView(false);
        changeEditCpnInfo(null);
    };
    const editPrizeEvent = async (params) => {
        if (params.pushPrizeId) {
            // 追加券调用接口
            try {
                const options = {
                    ...params,
                    actId: editActInfo.actId,
                };
                await addCpnApi(options);
                dispatch({
                    type: 'couponActiveModel/initEditActInfo',
                    actId: editActInfo.actId,
                });
                togglePrizeView(false);
                changeEditCpnInfo(null);
            } catch (error) {}
        } else {
            for (let index = 0; index < cpnList.length; index++) {
                const element = cpnList[index];
                if (element.cpnId && params.cpnId && element.cpnId == params.cpnId) {
                    cpnList[index] = params;
                    break;
                }
            }
            changeCpnList([...cpnList]);
            togglePrizeView(false);
            changeEditCpnInfo(null);
        }
    };

    // 新增
    const addCpnEvent = () => {
        changeEditCpnInfo(null);
        togglePrizeView(true);
    };

    // 编辑
    const editCpnEvent = (item) => {
        changeEditCpnInfo(item);
        togglePrizeView(true);
    };

    // 追加
    const pushCpnEvent = (item) => {
        changeEditCpnInfo({
            ...item,
        });
        togglePrizeView(true);
    };
    // 删除
    const deleteCpnEvent = (item) => {
        const newList = [...cpnList];
        for (let index = cpnList.length - 1; index >= 0; index--) {
            const element = cpnList[index];
            if (element.cpnId == item.cpnId) {
                newList.splice(index, 1);
                changeCpnList(newList);
                return;
            }
        }
    };

    const prizeColumns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '优惠券名称',
            width: 140,
            dataIndex: 'cpnName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '剩余数量(张)',
            width: 140,
            dataIndex: 'unGetNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '中奖概率(%)',
            width: 160,
            dataIndex: 'winningRate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    if (!isLock) {
        prizeColumns.push({
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record) {
                const editList = [
                    <span
                        className={commonStyles['table-btn']}
                        key="edit"
                        onClick={() => {
                            editCpnEvent(record);
                        }}
                    >
                        修改
                    </span>,
                    <span
                        className={commonStyles['table-btn']}
                        key="del"
                        onClick={() => {
                            deleteCpnEvent(record);
                        }}
                    >
                        删除
                    </span>,
                ];
                if (
                    editActInfo &&
                    editActInfo.actState &&
                    (editActInfo.actState == STATUS_TYPES.NOSTART ||
                        editActInfo.actState == STATUS_TYPES.DOING)
                ) {
                    if (record.pushPrizeId && !record.isChildren) {
                        editList.push(
                            <span
                                className={commonStyles['table-btn']}
                                onClick={() => {
                                    pushCpnEvent(record);
                                }}
                            >
                                追加
                            </span>,
                        );
                    }
                }

                return <Space>{editList}</Space>;
            },
        });
    } else if (
        editActInfo &&
        editActInfo.actState &&
        (editActInfo.actState == STATUS_TYPES.NOSTART || editActInfo.actState == STATUS_TYPES.DOING)
    ) {
        prizeColumns.push({
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record) {
                return (
                    <Space>
                        {record.pushPrizeId && !record.isChildren ? (
                            <span
                                className={commonStyles['table-btn']}
                                onClick={() => {
                                    pushCpnEvent(record);
                                }}
                            >
                                追加
                            </span>
                        ) : null}
                    </Space>
                );
            },
        });
    }

    return (
        <Fragment>
            <FormItem
                name="cpnList"
                label="配置优惠券:"
                {...formItemLayout}
                required
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value || value.length == 0) {
                                return Promise.reject('请配置优惠券');
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <Button disabled={isLock} type="primary" onClick={addCpnEvent}>
                    +添加优惠券
                </Button>
            </FormItem>
            <FormItem
                {...{
                    wrapperCol: {
                        span: 24,
                    },
                }}
            >
                <TablePro
                    loading={listLoading}
                    scroll={null}
                    rowKey={(record) => record.cpnId}
                    dataSource={formmatList}
                    pagination={false}
                    expandable={{ defaultExpandAllRows: true, indentSize: 40 }}
                    columns={prizeColumns}
                />
            </FormItem>

            <PrizeModelLayout
                {...props}
                visible={showPrizeView}
                editCpnInfo={editCpnInfo}
                addEvent={addPrizeEvent}
                editEvent={editPrizeEvent}
                maxRate={getMaxRate}
                closeEvent={closePrizeViewEvent}
            />
        </Fragment>
    );
};
const prizeformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};
const PrizeModelLayout = (props) => {
    const {
        dispatch,
        listLoading,
        visible,
        addEvent,
        editEvent,
        closeEvent,
        editCpnInfo,
        isLock,
        maxRate = 100,
        global: { cpnList = [] },
    } = props;

    const [form] = Form.useForm();

    const [cpnInfo, changeCpnInfo] = useState(null);

    const [nowCoupons, changeNowCoupons] = useState([]);

    const isDisabled = useMemo(() => {
        if (editCpnInfo && !editCpnInfo.pushPrizeId) {
            return true;
        }
        return false;
    }, [editCpnInfo]);

    useEffect(() => {
        if (visible) {
            dispatch({
                type: 'global/getCpnList',
            });
        }
    }, [visible]);

    useEffect(() => {
        if (editCpnInfo) {
            changeCpnInfo(editCpnInfo);
            form.setFieldsValue({ ...editCpnInfo });
        }
    }, [editCpnInfo]);

    useEffect(() => {
        changeNowCoupons(cpnList);
    }, [cpnList]);

    const handleSearch = (value) => {
        if (value) {
            const list = [];
            for (const item of cpnList) {
                const name = item.cpnName;
                if (name.indexOf(value) >= 0) {
                    list.push(item);
                }
            }
            changeNowCoupons(list);
        } else {
            changeNowCoupons(cpnList);
        }
    };

    const handleFilter = (value, option) => true;

    const changeCpnEvent = (id) => {
        const item = cpnList.find((ele) => ele.cpnId == id);
        changeCpnInfo(item);
        form.setFieldsValue({ ...item });
    };

    const onFinish = (values) => {
        if (!cpnInfo) {
            message.error('请选择奖品');
            return;
        }
        if (cpnInfo.unGetNum == 0) {
            message.error('优惠券剩余数量为0');
            return;
        }
        const params = {
            ...cpnInfo,
            ...values,
            pushPrizeId: (editCpnInfo && editCpnInfo.pushPrizeId) || null,
        };

        if (editCpnInfo) {
            editEvent(params);
        } else {
            addEvent(params);
        }

        form.setFieldsValue({ winningRate: null });
    };
    const closeWindowEvent = () => {
        changeCpnInfo(null);
        form.resetFields();
        closeEvent();
    };

    return (
        <Fragment>
            <Modal
                visible={visible}
                title="添加优惠券"
                width={800}
                onCancel={closeWindowEvent}
                footer={null}
                maskClosable={false}
                destroyOnClose
            >
                <Form form={form} onFinish={onFinish} initialValues={{}} scrollToFirstError>
                    <FormItem name="cpnOwner" noStyle />
                    <FormItem name="cpnName" noStyle />
                    <FormItem name="pushPrizeId" noStyle />

                    <FormItem
                        label="选择优惠券:"
                        {...prizeformItemLayout}
                        wrapperCol={{ span: 16 }}
                        name="cpnId"
                    >
                        <Select
                            value={(cpnInfo && cpnInfo.cpnId) || null}
                            placeholder="请选择"
                            disabled={isDisabled || editCpnInfo?.cpnOwner == '03'}
                            showSearch
                            onSearch={handleSearch}
                            filterOption={handleFilter}
                            onChange={changeCpnEvent}
                        >
                            {nowCoupons.map((ele) => (
                                <Option
                                    key={ele.cpnId}
                                    value={ele.cpnId}
                                    disabled={ele.cpnOwner == '03'}
                                >
                                    {ele.cpnName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                    <FormItem label="剩余数量:" name="unGetNum" {...prizeformItemLayout}>
                        {(editCpnInfo?.cpnOwner == '03' && (
                            <InputNumber
                                min={1}
                                // 因平台券情况下，未发放数量会随着输入值变动，所以需要锚定初始值，其他不可编辑的情况可用优惠券本身的值
                                max={editCpnInfo?.unGetNum}
                                placeholder="请填写"
                            />
                        )) || <span>{(cpnInfo && cpnInfo.unGetNum) || ''}</span>}
                    </FormItem>
                    <FormItem label="中奖概率:" {...prizeformItemLayout} required>
                        <Space>
                            <FormItem
                                name="winningRate"
                                noStyle
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (!value) {
                                                return Promise.reject('请填写中奖概率');
                                            }
                                            if (Number(value) > maxRate) {
                                                return Promise.reject(`中奖概率总和不能大于100%`);
                                            }

                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <InputNumber
                                    min={1}
                                    max={maxRate}
                                    placeholder="请填写"
                                    disabled={editCpnInfo && editCpnInfo.pushPrizeId}
                                />
                            </FormItem>
                            <span>%</span>
                        </Space>
                    </FormItem>
                    <FormItem
                        {...{
                            wrapperCol: {
                                span: 8,
                                offset: 6,
                            },
                        }}
                    >
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeWindowEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Fragment>
    );
};

// 翻牌子编辑页面
const CouponActiveUpdatePage = (props) => {
    const {
        dispatch,
        match,
        history,
        location,
        route,
        currentUser,
        couponActiveModel: { editActInfo },
    } = props;
    const [isLock, changeLock] = useState(
        route.path == '/marketing/couponCenter/couponActive/list/look/:actId',
    ); // 是否可编辑
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [actId, changeActId] = useState(match.params.actId || null);
    const [isCopy, changeCopy] = useState(() => {
        if (route.path.indexOf('/marketing/couponCenter/couponActive/list/copy') >= 0) {
            return true;
        }
        return false;
    });

    const [form] = Form.useForm();

    const operSelectList = useRef();

    const disabledRangeTime = (_, type) => {
        if (type === 'start') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 1; index <= 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        if (type === 'end') {
            return {
                disabledSeconds: () => {
                    const disabledList = [];
                    for (let index = 0; index < 59; index++) {
                        disabledList.push(index);
                    }
                    return disabledList;
                },
            };
        }
        return null;
    };

    useEffect(() => {
        if (actId) {
            dispatch({
                type: 'couponActiveModel/initEditActInfo',
                actId,
            });
        }

        return () => {
            dispatch({
                type: 'couponActiveModel/updateEditActInfo',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
        }
    }, [editActInfo, currentUser]);

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const params = {};
            if (info.actName) {
                params.actName = info.actName;
            }
            if (info.actTitle) {
                params.actTitle = info.actTitle;
            }
            if (info.title) {
                params.title = info.title;
            }
            if (info.actMarks) {
                params.actMarks = info.actMarks;
            }
            if (info.actGetLimitNum) {
                params.actGetLimitNum = info.actGetLimitNum;
            }
            if (info.areaRangeType) {
                params.areaRangeType = info.areaRangeType;
            }
            if (currentUser.operId) {
                params.areaRangeType = CAPITALTYPS.OPERATOR;
            }
            if (info.effTime && info.expTime) {
                params.dateTime = [moment(info.effTime), moment(info.expTime)];
                // if (info.actState == '3') {
                //     changeLock(true);
                // let expDate = +new Date(info.expTime);
                // let nowDate = +new Date();
                // if (nowDate > expDate) {
                //     changeLock(true);
                // }
                // }
            }
            if (info.stationList) {
                params.stationList = info.stationList.map((ele) => ({
                    stationId: ele.stationIds.map((item) => Number(item)),
                    operId: ele.operId,
                }));
                if (info.areaRangeType == CAPITALTYPS.OPERATOR) {
                    setTimeout(() => {
                        operSelectList.current && operSelectList.current.init(params.stationList);
                    }, 0);
                }
            }
            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    const changeAreaType = (event) => {
        const {
            target: { value },
        } = event;

        if (value == CAPITALTYPS.OPERATOR) {
            setTimeout(() => {
                const stationsListInfo = form.getFieldValue('stationList');
                if (stationsListInfo) {
                    operSelectList.current && operSelectList.current.init(stationsListInfo);
                }
            }, 0);
        }
    };

    /**
     * 保存优惠券
     * type  save/send
     */
    const saveCouponEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();

            const params = {
                saveType: type,
                actName: values.actName,

                actTitle: values.actTitle,
                title: values.title || '',
                actMarks: values.actMarks,
                effTime:
                    (values.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                actGetLimitNum: values.actGetLimitNum,
                areaRangeType: values.areaRangeType,
                cpnList: JSON.stringify(values.cpnList || []),
            };
            if (values.stationList) {
                params.stationList = JSON.stringify(values.stationList);
            }

            if (!isCopy && actId) {
                params.actId = actId;
            }

            if (type == 'save') {
                params.actState = '0';
            } else if (type == 'send') {
                params.actState = '1';
            }

            changeSubmitLoading(true);

            const {
                data: { actId: newActId },
            } = await saveCouponActiveApi(params);

            changeCopy(false);

            if (newActId) {
                changeActId(newActId);
            }

            if (type == 'save') {
                message.success('保存成功');
                history.replace(`/marketing/couponCenter/couponActive/list/update/${newActId}`);
            } else if (type == 'send') {
                message.success('提交成功');
                goBack();
            }
        } catch (error) {
            console.log(9999, error);
        } finally {
            changeSubmitLoading(false);
        }
    };
    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    initialValues={{
                        areaRangeType: CAPITALTYPS.ALL,
                    }}
                    scrollToFirstError
                >
                    <Card
                        title={<span className={styles.formTitle}>活动信息</span>}
                        style={{ paddingTop: '20px' }}
                        bordered={false}
                    >
                        <FormItem
                            label={<span>活动名称</span>}
                            name="actName"
                            {...formItemLayout}
                            rules={[
                                { required: true, whitespace: true, message: '请填写活动名称' },
                            ]}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={20}
                                placeholder="名称用于区分活动"
                                autoComplete="off"
                            />
                        </FormItem>
                        {/* <FormItem
                            label={<span>活动标题</span>}
                            name="title"
                            {...formItemLayout}
                            rules={[
                                { required: true, whitespace: true, message: '请填写标题名称' },
                            ]}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={15}
                                placeholder="显示在翻牌页面的标题内容，最多15个字"
                                autoComplete="off"
                            />
                        </FormItem>
                        <FormItem
                            label={<span>活动副标题</span>}
                            name="actMarks"
                            {...formItemLayout}
                            rules={[
                                { required: true, whitespace: true, message: '请填写副标题名称' },
                            ]}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={15}
                                placeholder="显示在翻牌页面的副标题内容，最多15个字"
                                autoComplete="off"
                            />
                        </FormItem> */}
                        <FormItem
                            name="dateTime"
                            label="活动有效期:"
                            {...formItemLayout}
                            rules={[
                                { required: true, message: '请选择活动有效期' },
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择活动开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择活动失效日期');
                                        }
                                        if (value[1]) {
                                            const nowTime = +new Date();
                                            const sendEndTime = +new Date(value[1]);

                                            if (sendEndTime < nowTime) {
                                                return Promise.reject(
                                                    '活动失效日期不能早于当前时间',
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            // validateStatus={timeStatus}
                            // help={timeStatus === 'error' && '请填写生效时间'}
                        >
                            <RangePicker
                                disabled={isLock}
                                disabledTime={disabledRangeTime}
                                showTime={{
                                    format: 'HH:mm:ss',
                                    defaultValue: [
                                        moment('00:00:00', 'HH:mm:ss'),
                                        moment('23:59:59', 'HH:mm:ss'),
                                    ],
                                }}
                                format="YYYY-MM-DD HH:mm:ss"
                            />
                        </FormItem>
                        {/* <FormItem name="remark" label="活动说明:" {...formItemLayout}>
                        <TextArea disabled={isLock} placeholder={'活动的描述，用于内部识别'} />
                    </FormItem> */}
                        <FormItem label="说明" name="remark" {...formItemLayout}>
                            <TextArea
                                disabled={isLock}
                                rows={4}
                                maxLength={6}
                                placeholder=""
                                autoComplete="off"
                            />
                        </FormItem>
                        <FormItem
                            name="actGetLimitNum"
                            label="领取限制"
                            {...formItemLayout}
                            wrapperCol={{ span: 18 }}
                            required
                        >
                            <Radio.Group>
                                <Radio value={'0'}>无限制</Radio>
                                <Radio value={'1'}>
                                    每人限制
                                    <FormItem
                                        name="actGetLimitNum"
                                        {...formItemLayout}
                                        noStyle
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请填写');
                                                    }
                                                    if (value === 0) {
                                                        return Promise.reject(
                                                            '每日限制次数最小1次',
                                                        );
                                                    }

                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <InputNumber
                                            disabled={isLock}
                                            formatter={(num) => Math.round(num)}
                                            min={1}
                                            max={99999}
                                            placeholder="请填写"
                                        />
                                    </FormItem>
                                </Radio>
                            </Radio.Group>
                        </FormItem>
                    </Card>
                    <Card
                        title={<span className={styles.formTitle}>礼包信息</span>}
                        style={{ paddingTop: '20px' }}
                        bordered={false}
                    >
                        <FormItem
                            label="礼包总数"
                            name="count"
                            {...formItemLayout}
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <Input type="number" placeholder="请填写"></Input>
                        </FormItem>
                        <PrizeLayout form={form} {...props} isLock={isLock} />
                        <FormItem
                            label="活动范围"
                            name="areaRangeType"
                            {...formItemLayout}
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <Radio.Group
                                disabled={isLock || currentUser.operId}
                                onChange={changeAreaType}
                            >
                                <Radio value={CAPITALTYPS.ALL}>全部</Radio>
                                <Radio value={CAPITALTYPS.OPERATOR}>部分</Radio>
                                {/* <Radio value={CAPITALTYPS.CITY}>按城市</Radio> */}
                            </Radio.Group>
                        </FormItem>
                        <FormItem
                            shouldUpdate={
                                (prevValues, curValues) => true
                                // prevValues.areaRangeType !== curValues.areaRangeType
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const areaRangeType = getFieldValue('areaRangeType');
                                return areaRangeType == CAPITALTYPS.OPERATOR ? (
                                    <OperTreeSelectList
                                        form={form}
                                        initRef={operSelectList}
                                        name="stationList"
                                        disabled={isLock}
                                        formItemLayout={{
                                            labelCol: {
                                                flex: '0 0 140px',
                                            },
                                            labelAlign: 'left',
                                            wrapperCol: {
                                                span: 18,
                                            },
                                        }}
                                    />
                                ) : null;
                            }}
                        </FormItem>
                    </Card>

                    <div className={commonStyles['form-submit']}>
                        {isLock ? (
                            <Fragment>
                                {/* <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    onClick={() => {
                                        changeLock(false);
                                    }}
                                >
                                    编辑
                                </Button> */}
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        ) : (
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn-left']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('save');
                                    }}
                                >
                                    保存
                                </Button>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveCouponEvent('send');
                                    }}
                                >
                                    提交
                                </Button>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        )}
                    </div>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, couponActiveModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    couponActiveModel,
    detalisLoading: loading.effects['couponActiveModel/getCouponInfo'],
    operatorLoading: loading.effects['couponActiveModel/getOperationList'],
    // submitLoading: loading.effects['couponActiveModel/getCouponMangeList'],
}))(CouponActiveUpdatePage);
