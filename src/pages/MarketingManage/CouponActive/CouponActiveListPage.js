import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import {
    stopMiniCouponActiveApi,
    deleteMiniCouponActiveApi,
} from '@/services/Marketing/MarketingCouponActiveApi';
import { STATUS_TYPES } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './CouponActive.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(15, 'days'), moment().add(15, 'days')],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                // onExportForm={onExportForm}
                onReset={resetForm}
            >
                <Col span={8}>
                    <FormItem label="活动时间:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动名称:" name="name" {...formItemLayout}>
                        <Input placeholder="填写活动名称"></Input>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="活动类型:" name="type" {...formItemLayout}>
                        <Select placeholder="请选择"></Select>
                    </FormItem>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        {/* <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button> */}
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const SearchHistoryLayout = (props) => {
    const { historyLoading, form, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [null, null],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={historyLoading} onReset={resetForm}>
                <Col span={8}>
                    <FormItem label="手机号码:" name="activeName" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="兑换码:" name="activeStatus" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="状态:" name="activeStatus" {...formItemLayout}>
                        <Select>
                            <Option value="1">已核销</Option>
                            <Option value="2">未使用</Option>
                        </Select>
                    </FormItem>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        {/* <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button> */}
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};
const CouponActiveHistoryListPage = (props) => {
    const {
        dispatch,
        history,
        couponActiveModel: { couponActiveList, couponActiveListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            ...data,
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }
        dispatch({
            type: 'couponActiveModel/getCouponActiveList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '兑换码',
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '兑换时间',
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '兑换人',
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '参与状态',
            dataIndex: 'alreadyPutNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '使用订单',
            dataIndex: 'putPeopleNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销时间',
            dataIndex: 'surpluPutNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Card bordered={false}>
            <SearchHistoryLayout
                form={form}
                {...props}
                onSubmit={searchData}
                onReset={resetData}
                // onExportForm={exportFormEvent}
            />

            <TablePro
                name="history"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={couponActiveList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponActiveListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Card>
    );
};
const CouponActiveListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        couponActiveModel: { couponActiveList, couponActiveListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [showHistoryView, toggleHistoryView] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'couponActiveModel/getCouponActiveList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const stopCouponActiveEvent = async (item) => {
        confirm({
            title: `确定停止${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await stopMiniCouponActiveApi(item.actId);
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteCouponActiveEvent = async (item) => {
        confirm({
            title: `确定删除${item.actName}活动?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await deleteMiniCouponActiveApi(item.actId);
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const openHistoryViewEvent = (item) => {
        toggleHistoryView(true);
    };

    const closeHistoryViewEvent = () => {
        toggleHistoryView(false);
    };

    // //导出
    // const exportFormEvent = () => {
    //     const data = form.getFieldsValue();
    //     const params = {
    //         pageIndex: pageInfo.pageIndex,
    //         pageSize,
    //         actName: data.actName,
    //         actId: data.actId,
    //         actSubType: data.actSubType,
    //         beginDate:
    //             (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
    //         endDate:
    //             (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
    //     };
    //     if (tabType !== STATUS_TYPES.ALL) {
    //         params.actState = tabType;
    //     }
    //     let columnsStrs = [];
    //     for (const item of columns) {
    //         if (item.dataIndex) {
    //             columnsStrs.push({
    //                 key: item.dataIndex,
    //                 value: item.title,
    //             });
    //         }
    //     }
    //     exportTableByParams({
    //         methodUrl: '/bil/coupon/couponPutList',
    //         options: params,
    //         columnsStr: columnsStrs,
    //     });
    // };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push('/marketing/couponCenter/couponActive/list/add');
    };

    const editCouponActiveEvent = (item) => {
        history.push(`/marketing/couponCenter/couponActive/list/update/${item.actId}`);
    };

    const lookCouponActiveEvent = (item) => {
        history.push(`/marketing/couponCenter/couponActive/list/look/${item.actId}`);
    };

    const copyCouponActiveEvent = (item) => {
        history.push(`/marketing/couponCenter/couponActive/list/copy/${item.actId}`);
    };

    const columns = [
        {
            title: '活动时间',
            width: 340,
            dataIndex: 'actTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称/编号',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return (
                    <Fragment>
                        <div>
                            <span title={text}>{text}</span>
                        </div>
                        <div>
                            <span title={text}>{text}</span>
                        </div>
                    </Fragment>
                );
            },
        },
        {
            title: '活动范围',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '营销方式',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '礼包内容',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '人数/发放数',
            width: 140,
            dataIndex: 'prizeNum',
            render(text, record) {
                return <span title={text}>{text || 0}</span>;
            },
        },
        {
            title: '剩余数/总数',
            width: 140,
            dataIndex: 'drawNum',
            render(text, record) {
                return <span title={text}>{text || 0}</span>;
            },
        },

        {
            title: '活动状态',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 260,
            fixed: 'right',
            render: (text, record) => {
                if (record.actState == STATUS_TYPES.DRAFT) {
                    return (
                        <Space>
                            <span
                                className={styles['table-btn']}
                                onClick={() => editCouponActiveEvent(record)}
                            >
                                编辑
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookCouponActiveEvent(record)}
                            >
                                详情
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => deleteCouponActiveEvent(record)}
                            >
                                删除
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => copyCouponActiveEvent(record)}
                            >
                                复制
                            </span>
                        </Space>
                    );
                }
                if (record.actState == STATUS_TYPES.NOSTART) {
                    return (
                        <Space>
                            {/* <span
                                className={styles['table-btn']}
                                onClick={() => editCouponActiveEvent(record)}
                            >
                                编辑
                            </span> */}
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookCouponActiveEvent(record)}
                            >
                                详情
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => copyCouponActiveEvent(record)}
                            >
                                复制
                            </span>
                        </Space>
                    );
                }
                if (record.actState == STATUS_TYPES.DOING) {
                    return (
                        <Space>
                            {/* <span
                                className={styles['table-btn']}
                                onClick={() => updateRuleEvent(record)}
                            >
                                数据
                            </span> */}
                            {/* <span
                                className={styles['table-btn']}
                                onClick={() => editCouponActiveEvent(record)}
                            >
                                编辑
                            </span> */}
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookCouponActiveEvent(record)}
                            >
                                详情
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => stopCouponActiveEvent(record)}
                            >
                                停止
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => copyCouponActiveEvent(record)}
                            >
                                复制
                            </span>
                        </Space>
                    );
                }
                if (record.actState == STATUS_TYPES.END || record.actState == STATUS_TYPES.STOP) {
                    return (
                        <Space>
                            <span
                                className={styles['table-btn']}
                                onClick={() => lookCouponActiveEvent(record)}
                            >
                                详情
                            </span>
                            <span
                                className={styles['table-btn']}
                                onClick={() => copyCouponActiveEvent(record)}
                            >
                                复制
                            </span>
                        </Space>
                    );
                }
            },
        },
    ];

    return (
        <PageHeaderWrapper title="优惠券活动">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    // onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        新建
                    </Button>
                </div>

                <TablePro
                    name="list"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={couponActiveList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: couponActiveListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />

                <Modal
                    title="兑换记录"
                    width={1200}
                    visible={showHistoryView}
                    onCancel={closeHistoryViewEvent}
                    footer={null}
                    maskClosable={false}
                >
                    <CouponActiveHistoryListPage {...props} />
                </Modal>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, couponActiveModel, loading }) => ({
    global,
    couponActiveModel,
    listLoading: loading.effects['couponActiveModel/getCouponActiveList'],
    historyLoading: loading.effects['couponActiveModel/getCouponActiveList'],
}))(CouponActiveListPage);
