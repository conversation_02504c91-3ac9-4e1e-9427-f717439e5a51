import { Button, Modal, Form, Input, InputNumber, Select, message, Space } from 'antd';
import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import { handleAppealApi, updateAppealStatusApi } from '@/services/Merchant/AppealManagementApi';
import { AppealStatusEnum } from '@/constants/AppealManagement';

const { TextArea } = Input;

interface AppealActionsProps {
    record: API.AppealListItem;
    onSuccess: () => void;
}

const AppealActions: React.FC<AppealActionsProps> = ({ record, onSuccess }) => {
    const [handleModalVisible, setHandleModalVisible] = useState(false);
    const [statusModalVisible, setStatusModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [statusForm] = Form.useForm();

    const { run: handleAppeal, loading: handleLoading } = useRequest(handleAppealApi, {
        manual: true,
        onSuccess: (res) => {
            if (res.ret === 200) {
                message.success('处理成功');
                setHandleModalVisible(false);
                form.resetFields();
                onSuccess();
            } else {
                message.error(res.msg || '处理失败');
            }
        },
        onError: () => {
            message.error('处理失败');
        },
    });

    const { run: updateStatus, loading: statusLoading } = useRequest(updateAppealStatusApi, {
        manual: true,
        onSuccess: (res) => {
            if (res.ret === 200) {
                message.success('状态更新成功');
                setStatusModalVisible(false);
                statusForm.resetFields();
                onSuccess();
            } else {
                message.error(res.msg || '状态更新失败');
            }
        },
        onError: () => {
            message.error('状态更新失败');
        },
    });

    const handleSubmit = () => {
        form.validateFields().then((values) => {
            handleAppeal({
                appealNo: record.appealNo,
                ...values,
            });
        });
    };

    const handleStatusUpdate = () => {
        statusForm.validateFields().then((values) => {
            updateStatus({
                appealNo: record.appealNo,
                ...values,
            });
        });
    };

    const canHandle =
        record.appealStatus === AppealStatusEnum.PENDING ||
        record.appealStatus === AppealStatusEnum.PROCESSING;

    return (
        <Space>
            {canHandle && (
                <Button type="primary" size="small" onClick={() => setHandleModalVisible(true)}>
                    处理
                </Button>
            )}
            <Button size="small" onClick={() => setStatusModalVisible(true)}>
                更新状态
            </Button>

            {/* 处理申诉模态框 */}
            <Modal
                title="处理申诉"
                visible={handleModalVisible}
                onCancel={() => {
                    setHandleModalVisible(false);
                    form.resetFields();
                }}
                onOk={handleSubmit}
                confirmLoading={handleLoading}
                width={600}
            >
                <Form form={form} layout="vertical">
                    <Form.Item
                        label="处理结果"
                        name="handleResult"
                        rules={[{ required: true, message: '请选择处理结果' }]}
                    >
                        <Select placeholder="请选择处理结果">
                            <Select.Option value="accept">接受申诉</Select.Option>
                            <Select.Option value="reject">拒绝申诉</Select.Option>
                            <Select.Option value="partial">部分接受</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item
                        label="退款金额（元）"
                        name="refundAmount"
                        dependencies={['handleResult']}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(_, value) {
                                    const result = getFieldValue('handleResult');
                                    if ((result === 'accept' || result === 'partial') && !value) {
                                        return Promise.reject(new Error('请输入退款金额'));
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请输入退款金额"
                            min={0}
                            precision={2}
                        />
                    </Form.Item>
                    <Form.Item
                        label="处理说明"
                        name="handleRemark"
                        rules={[{ required: true, message: '请输入处理说明' }]}
                    >
                        <TextArea rows={4} placeholder="请输入处理说明" maxLength={500} showCount />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 更新状态模态框 */}
            <Modal
                title="更新申诉状态"
                visible={statusModalVisible}
                onCancel={() => {
                    setStatusModalVisible(false);
                    statusForm.resetFields();
                }}
                onOk={handleStatusUpdate}
                confirmLoading={statusLoading}
            >
                <Form form={statusForm} layout="vertical">
                    <Form.Item
                        label="状态"
                        name="status"
                        rules={[{ required: true, message: '请选择状态' }]}
                    >
                        <Select placeholder="请选择状态">
                            <Select.Option value={AppealStatusEnum.PROCESSING}>
                                处理中
                            </Select.Option>
                            <Select.Option value={AppealStatusEnum.PROCESSED}>已处理</Select.Option>
                            <Select.Option value={AppealStatusEnum.REJECTED}>已拒绝</Select.Option>
                            <Select.Option value={AppealStatusEnum.CLOSED}>已关闭</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item label="备注" name="remark">
                        <TextArea rows={3} placeholder="请输入备注" maxLength={200} showCount />
                    </Form.Item>
                </Form>
            </Modal>
        </Space>
    );
};

export default AppealActions;
