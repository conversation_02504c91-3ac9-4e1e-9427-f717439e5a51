import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { useLocation, history } from 'umi';

import { getAppealDetailApi } from '@/services/Merchant/AppealManagementApi';
import AppealDetailContent from './components/AppealDetailContent';
import { mockAppealDetail } from './mockData';
import './index.less';

const AppealDetailPage = () => {
    const location = useLocation();
    const query = new URLSearchParams(location.search);
    const appealNo = query.get('appealNo');

    const goBack = () => {
        history.goBack();
    };

    const {
        data: appealDetail,
        loading,
        refresh,
    } = useRequest(
        async () => {
            try {
                if (appealNo) {
                    return await getAppealDetailApi(appealNo);
                }
                return { data: mockAppealDetail };
            } catch (error) {
                console.warn('API调用失败，使用mock数据:', error);
                return { data: mockAppealDetail };
            }
        },
        {
            ready: true, // 总是准备好，因为我们有fallback数据
            onError: (error) => {
                console.warn('获取申诉详情失败，使用mock数据:', error);
                // 不显示错误消息，因为我们有fallback数据
            },
        },
    );

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    申诉详情
                </div>
            }
        >
            <AppealDetailContent
                appealDetail={appealDetail?.data}
                loading={loading}
                onRefresh={refresh}
            />
        </PageHeaderWrapper>
    );
};

export default AppealDetailPage;
