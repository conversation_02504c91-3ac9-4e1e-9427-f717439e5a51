// 测试页面 - 用于验证申诉详情页面功能
import React from 'react';
import { Button, Space } from 'antd';
import { history } from 'umi';

const TestPage: React.FC = () => {
    const testUrls = [
        {
            name: '申诉详情页面 - 有申诉单号',
            url: '/merchant/appeal-management/detail?appealNo=AP202312150001'
        },
        {
            name: '申诉详情页面 - 无申诉单号',
            url: '/merchant/appeal-management/detail'
        }
    ];

    const handleNavigate = (url: string) => {
        history.push(url);
    };

    return (
        <div style={{ padding: '24px' }}>
            <h2>申诉管理详情页面测试</h2>
            <p>点击下面的按钮测试不同的页面场景：</p>
            
            <Space direction="vertical" size="middle">
                {testUrls.map((item, index) => (
                    <Button 
                        key={index}
                        type="primary" 
                        onClick={() => handleNavigate(item.url)}
                        style={{ width: '300px' }}
                    >
                        {item.name}
                    </Button>
                ))}
            </Space>

            <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
                <h3>说明：</h3>
                <ul>
                    <li>页面使用了mock数据，即使API调用失败也能正常显示</li>
                    <li>包含完整的申诉详情信息展示</li>
                    <li>右侧有操作按钮和指引信息</li>
                    <li>支持图片附件预览</li>
                    <li>响应式布局，适配不同屏幕尺寸</li>
                </ul>
            </div>
        </div>
    );
};

export default TestPage;
