import { PlusOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import type { ActionType, ProColumnType, ProFormInstance } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Button, message, Popconfirm, Space, Spin, Typography } from 'antd';
import React, { useRef } from 'react';
import { Link } from 'umi';

import XdtProTable from '@/components/XdtProTable';
import { queryPileListApi, updatePileStatusApi } from '@/services/Merchant/PileMarketApi';
import { PileMarketAttributeEnum, PileMarketProductStatusEnum } from '@/constants/ServiceMarket';

const ListPage = () => {
    const actionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();

    const { run, loading } = useRequest(updatePileStatusApi, {
        manual: true,
        onSuccess(res) {
            if (res.ret === 200) {
                message.success('下架成功');
                actionRef.current?.reload();
            }
        },
    });

    const updateStatus = (serviceNo: any) => {
        run(serviceNo);
    };

    const columns: ProColumnType<API.PileMarketRecordVo>[] = [
        {
            title: '商品编码',
            dataIndex: 'id',
            hideInSearch: true,
            width: 120,
        },
        {
            title: '桩企名称',
            dataIndex: 'pileName',
            fieldProps: {
                placeholder: '请输入桩企名称',
                maxLength: 100,
            },
            width: 160,
        },
        {
            title: '商品描述',
            dataIndex: 'productDescription',
            fieldProps: {
                placeholder: '请输入商品描述',
                maxLength: 200,
            },
            width: 240,
        },
        {
            title: '电桩属性',
            dataIndex: 'pileAttribute',
            hideInSearch: true,
            valueType: 'select',
            valueEnum: {
                [PileMarketAttributeEnum.AC]: '交流',
                [PileMarketAttributeEnum.DC]: '直流',
            },
            width: 120,
        },
        {
            title: '商品价格',
            dataIndex: 'productPrice',
            hideInSearch: true,
            valueType: 'money',
            width: 120,
        },
        {
            title: '状态',
            dataIndex: 'productStatus',
            renderText(text, record) {
                return record?.productStatusName ?? '-';
            },
            valueType: 'select',
            valueEnum: {
                [PileMarketProductStatusEnum.NOT_ON_SHELVES]: '未上架',
                [PileMarketProductStatusEnum.ON_SHELVES]: '已上架',
                [PileMarketProductStatusEnum.OFF_SHELVES]: '已下架',
            },
            width: 90,
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            hideInSearch: true,
            valueType: 'dateTime',
            width: 160,
        },
        {
            title: '操作',
            key: 'option',
            valueType: 'option',
            hideInSearch: true,
            fixed: 'right',
            renderText(_, record: any) {
                return (
                    <Space>
                        <Link to={`/merchant/service-market/pile/list/detail?id=${record.id}`}>
                            详情
                        </Link>
                        <Link to={`/merchant/service-market/pile/list/edit?id=${record.id}`}>
                            编辑
                        </Link>
                        {record.productStatus !== PileMarketProductStatusEnum.ON_SHELVES && (
                            <Spin spinning={loading}>
                                <Popconfirm
                                    title="确定要上架吗？"
                                    onConfirm={() => {
                                        updateStatus(record.id);
                                    }}
                                >
                                    <Typography.Link>上架</Typography.Link>
                                </Popconfirm>
                            </Spin>
                        )}
                        {record.productStatus === PileMarketProductStatusEnum.ON_SHELVES && (
                            <Spin spinning={loading}>
                                <Popconfirm
                                    title="确定要下架吗？"
                                    onConfirm={() => {
                                        updateStatus(record.id);
                                    }}
                                >
                                    <Typography.Link>下架</Typography.Link>
                                </Popconfirm>
                            </Spin>
                        )}
                    </Space>
                );
            },
            width: 120,
        },
    ];

    return (
        <PageHeaderWrapper>
            <XdtProTable
                actionRef={actionRef}
                columns={columns}
                formRef={formRef}
                prefixKey="merchant-pile-market-list"
                requestApi={queryPileListApi}
                rowKey="id"
                search={{
                    span: 6,
                }}
                toolButtons={[
                    <Link key="add" to="/merchant/service-market/pile/list/add">
                        <Button type="primary" icon={<PlusOutlined />}>
                            创建商品
                        </Button>
                    </Link>,
                ]}
            />
        </PageHeaderWrapper>
    );
};

export default ListPage;
