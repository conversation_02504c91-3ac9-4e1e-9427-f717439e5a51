import {
    // 会员
    getCardListApi,
    getEnterpriseCardNumApi,
} from '@/services/Customer/CustomerCardApi';

const cardModel = {
    namespace: 'cardModel',
    state: {
        cardList: [], // 列表
        cardListTotal: 0, // 条数

        sendEnabledCardNum: 0, // 大客户可发放充电卡数量
    },
    effects: {
        /**
         *
         */
        *getCardList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: cardList, total: cardListTotal },
                } = yield call(getCardListApi, options);

                yield put({
                    type: 'updateManageList',
                    list: cardList,
                    total: cardListTotal,
                });
                return cardList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *getEnterpriseCardNum({ options }, { call, put, select }) {
            try {
                const {
                    data: { cardNum },
                } = yield call(getEnterpriseCardNumApi, options);

                yield put({
                    type: 'updateCardModelProperty',
                    params: { sendEnabledCardNum: cardNum },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateCardModelProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateManageList(state, { list, total }) {
            return {
                ...state,
                cardList: list,
                cardListTotal: total,
            };
        },
    },
};
export default cardModel;
