import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    But<PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    InputNumber,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SendCardModal from '../../MarketingManage/WeekCard/components/SendCardModal';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';
import { isEmpty } from '@/utils/utils';
import { sendEnterpriseCardApi } from '@/services/Customer/CustomerCardApi';

const { TextArea } = Input;

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <EnterpriseSelectItem form={form} />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const CardManagePage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {}, enterpriseChildList },
        cardModel: { cardList, cardListTotal, sendEnabledCardNum },
        customerSystemModel: { searchParams },
        listLoading,
        currentUser,
    } = props;

    const {
        location: { pathname, query },
    } = history;
    const { actState: actStateList } = codeInfo;

    const [form] = Form.useForm();

    const enterpriseId = Form.useWatch('enterpriseId', form);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        if (!actStateList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'actState',
            });
        }

        if (searchParams && searchParams?.enterpriseId) {
            form.setFieldsValue({ enterpriseId: searchParams?.enterpriseId });
        }

        if (currentUser?.childEnterpriseId?.length) {
            form.setFieldsValue({ childEnterpriseId: currentUser?.childEnterpriseId });
        }

        // if (query?.enterpriseId) {
        //     form.setFieldsValue({ enterpriseId: query?.enterpriseId });
        // }

        return () => {
            dispatch({
                type: 'cardModel/updateManageList',
                params: {
                    cardList: [],
                    cardListTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };
                if (data.childEnterpriseId) {
                    params.enterpriseId = data.childEnterpriseId;
                    params.childEnterpriseId = undefined;
                }
                if (pageInfo.tabType !== '-1') {
                    params.actState = pageInfo.tabType;
                }

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'cardModel/getCardList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    // 直接发券
    const sendRef = useRef();
    const [sendOriginalItem, updateSendOriginalItem] = useState();
    const sendCardEvent = (record) => {
        sendRef.current.show(record.actId);
        updateSendOriginalItem(record);

        dispatch({
            type: 'cardModel/updateCardModelProperty',
            params: { sendEnabledCardNum: 0 },
        });
        dispatch({
            type: 'cardModel/getEnterpriseCardNum',
            options: { actId: record.actId },
        });
    };

    const columns = [
        {
            title: '活动时间',
            width: 200,
            dataIndex: 'actTime',
            render(text, record) {
                const time = `${record.effTime}~${record.expTime}`;
                return (
                    <div>
                        <div title={time}>{record.effTime}</div>
                        <div title={time}>{record.expTime}</div>
                    </div>
                );
            },
        },
        {
            title: '客户名称',
            width: 200,
            dataIndex: 'actResume',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动编号',
            width: 200,
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '充电卡名称',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '适用范围',
            width: 200,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '充电卡周期',
            width: 120,
            dataIndex: 'actCycle',
            render(text, record) {
                if (isEmpty(text)) {
                    return <span>-</span>;
                } else {
                    return <span title={text}>{text}天</span>;
                }
            },
        },
        {
            title: '充电卡额度',
            width: 120,
            dataIndex: 'discountLimitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已发金额',
            width: 120,
            dataIndex: 'totalAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已发数量',
            width: 120,
            dataIndex: 'buyNum',
            render(text, record) {
                return (
                    <span
                        className={styles['table-btn']}
                        title={text}
                        onClick={() => {
                            jumpMarketingEvent(record);
                        }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '活动状态',
            width: 120,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 80,
            fixed: 'right',
            render: (text, record) => {
                return (
                    (record?.sendCardFlag == '1' && (
                        <a onClick={() => sendCardEvent(record)}>发卡</a>
                    )) ||
                    null
                );
            },
        },
    ];

    const jumpMarketingEvent = (item) => {
        dispatch({
            type: 'customerSystemModel/updateSearchParams',
            params: {
                tabType: '02',
                enterpriseId: enterpriseId,
                // actNo: item.actNo,
                childEnterpriseId:
                    (enterpriseId != item.enterpriseId && item.enterpriseId) || undefined,
            },
        });
        history.push('/sellerCenter/customer/account/list');
    };

    return (
        <PageHeaderWrapper
            content={
                // <SearchLayout
                //     form={form}
                //     onSubmit={() => {
                //         changePageInfo((state) => ({
                //             ...state,
                //             pageIndex: 1,
                //         }));
                //     }}
                //     onReset={resetData}
                // />
                <Form
                    {...formItemLayout}
                    form={form}
                    initialValues={{ childEnterpriseId: currentUser?.childEnterpriseId }}
                >
                    <Row gutter={24}>
                        <Col span={8}>
                            <EnterpriseSelectItem
                                rules={[{ required: true, message: '请选择客户' }]}
                                onChange={() => {
                                    form.setFieldsValue({ childEnterpriseId: undefined });
                                    changePageInfo((state) => ({
                                        ...state,
                                        pageIndex: 1,
                                    }));
                                }}
                            />
                        </Col>
                        <Col span={8}>
                            <FormItem label="子公司" name="childEnterpriseId">
                                <Select
                                    placeholder="请选择"
                                    autoComplete="off"
                                    disabled={currentUser?.childEnterpriseId?.length > 0}
                                    onChange={() => {
                                        changePageInfo((state) => ({
                                            ...state,
                                            pageIndex: 1,
                                        }));
                                    }}
                                    allowClear
                                >
                                    {enterpriseChildList?.map((item) => {
                                        return (
                                            <Option
                                                value={item.enterpriseId}
                                                key={item.enterpriseId}
                                            >
                                                {item.enterpriseName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            }
        >
            <Card>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={cardList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: cardListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>

            <SendCardModal
                sendRef={sendRef}
                {...props}
                sendEnableNum={sendEnabledCardNum}
                sendCardEvent={async ({ actId, allUsers }) => {
                    // 自定义发卡调用
                    const params = {
                        custInfoList: allUsers,
                        actId,
                        enterpriseId: sendOriginalItem?.enterpriseId,
                    };
                    await sendEnterpriseCardApi(params);
                    searchData();
                    return Promise.resolve();
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, cardModel, customerSystemModel, loading, user }) => ({
    global,
    cardModel,
    customerSystemModel,
    listLoading: loading.effects['cardModel/getCardList'],
    currentUser: user.currentUser,
}))(CardManagePage);
