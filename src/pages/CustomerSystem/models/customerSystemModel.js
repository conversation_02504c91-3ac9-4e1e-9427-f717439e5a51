const customerSystemModel = {
    namespace: 'customerSystemModel',
    state: {
        searchParams: undefined,
    },
    effects: {},
    reducers: {
        clearSearchParams(state, { params }) {
            return {
                ...state,
                searchParams: undefined,
            };
        },
        updateSearchParams(state, { params }) {
            return {
                ...state,
                searchParams: params,
            };
        },
    },
};
export default customerSystemModel;
