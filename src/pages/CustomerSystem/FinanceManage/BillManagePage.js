import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    InputNumber,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';
import moment from 'moment';
import CacheAreaView from '@/components/CacheAreaView';

const { TextArea } = Input;

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <EnterpriseSelectItem form={form} />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const BillManagePage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {}, enterpriseChildList },
        billModel: { billList, billListTotal },
        listLoading,
        currentUser,
    } = props;

    const {
        location: { pathname },
    } = history;
    const { actState: actStateList } = codeInfo;
    const cacheRef = useRef();

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const [bizType, changeBizType] = useState();

    const enterpriseId = Form.useWatch('enterpriseId', form);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        if (!actStateList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'actState',
            });
        }

        if (currentUser?.childEnterpriseId?.length) {
            form.setFieldsValue({ childEnterpriseId: currentUser?.childEnterpriseId });
        }

        return () => {
            dispatch({
                type: 'billModel/updateManageList',
                params: {
                    billList: [],
                    billListTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };
                if (data.childEnterpriseId) {
                    params.enterpriseId = data.childEnterpriseId;
                    params.childEnterpriseId = undefined;
                }
                if (pageInfo.tabType !== '-1') {
                    params.actState = pageInfo.tabType;
                }

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'billModel/getBillList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const actStateOptions = useMemo(() => {
        if (actStateList) {
            return actStateList.map((ele) => {
                if (ele.codeValue == 5) return null;
                return <TabPane tab={ele.codeName} key={ele.codeValue} />;
            });
        }
        return [];
    }, [actStateList]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const lookEvent = (item) => {
        history.push(`/sellerCenter/customer/finance/bill/list/detail/${item.actId}`);
    };

    const downLoadEvent = async (item) => {
        try {
            let params = {
                enterpriseId: item.enterpriseId,
                billDate: item.billDate,
            };
            if (item.childEnterpriseId) {
                params.enterpriseId = item.childEnterpriseId;
                params.childEnterpriseId = undefined;
            }
            cacheRef.current?.apply(params).then(() => cacheRef.current.count());
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const columns = [
        {
            title: '账单周期',
            width: 200,
            dataIndex: 'billDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '客户名称',
            width: 200,
            dataIndex: 'enterpriseName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单总数',
            width: 200,
            dataIndex: 'orderNum',
            render(text, record) {
                return (
                    <span
                        title={text}
                        // className={styles['table-btn']}
                        // onClick={() => {
                        //     jumpMarketingEvent(record);
                        // }}
                    >
                        {text}
                    </span>
                );
            },
        },

        {
            title: '充电订单总金额',
            width: 160,
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '平台费用',
        //     width: 140,
        //     dataIndex: 'serviceAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '结算总额',
            width: 200,
            dataIndex: 'settleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            fixed: 'right',
            width: 140,
            dataIndex: 'oper',
            render: (text, record) => (
                <Space>
                    {/* <span
                        className={styles['table-btn']}
                        onClick={() => {
                            lookEvent(record);
                        }}
                    >
                        详情
                    </span> */}
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            downLoadEvent(record);
                        }}
                    >
                        下载账单
                    </span>
                </Space>
            ),
        },
    ];

    const jumpMarketingEvent = (item) => {
        let startDate = moment(item.billDate).startOf('month').format('YYYY-MM-DD');
        let endDate = moment(item.billDate).endOf('month').format('YYYY-MM-DD');
        const linkPath = `/sellerCenter/customer/account/list`;

        dispatch({
            type: 'customerSystemModel/updateSearchParams',
            params: {
                enterpriseId: enterpriseId,
                tabType: '03',
                startDate: startDate,
                endDate: endDate,
                childEnterpriseId:
                    (enterpriseId != item.enterpriseId && item.enterpriseId) || undefined,
            },
        });
        history.push(linkPath);
    };

    return (
        <PageHeaderWrapper
            content={
                // <SearchLayout
                //     form={form}
                //     onSubmit={() => {
                //         changePageInfo((state) => ({
                //             ...state,
                //             pageIndex: 1,
                //         }));
                //     }}
                //     onReset={resetData}
                // />
                <Form
                    {...formItemLayout}
                    form={form}
                    initialValues={{ childEnterpriseId: currentUser?.childEnterpriseId }}
                >
                    <Row gutter={24}>
                        <Col span={8}>
                            <EnterpriseSelectItem
                                rules={[{ required: true, message: '请选择客户' }]}
                                onChange={(value, foundItem) => {
                                    switch (foundItem?.reconciliationMethod) {
                                        case '01':
                                            changeBizType('enterpriseIssueBill');
                                            break;
                                        case '02':
                                            changeBizType('enterpriseUsedBill');
                                            break;
                                        default:
                                            return '';
                                    }
                                    form.setFieldsValue({ childEnterpriseId: undefined });
                                    changePageInfo((state) => ({
                                        ...state,
                                        pageIndex: 1,
                                    }));
                                }}
                            />
                        </Col>
                        <Col span={8}>
                            <FormItem label="子公司" name="childEnterpriseId">
                                <Select
                                    placeholder="请选择"
                                    autoComplete="off"
                                    disabled={currentUser?.childEnterpriseId?.length > 0}
                                    onChange={() => {
                                        changePageInfo((state) => ({
                                            ...state,
                                            pageIndex: 1,
                                        }));
                                    }}
                                    allowClear
                                >
                                    {enterpriseChildList?.map((item) => {
                                        return (
                                            <Option
                                                value={item.enterpriseId}
                                                key={item.enterpriseId}
                                            >
                                                {item.enterpriseName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            }
            extra={
                <CacheAreaView
                    bizType={bizType}
                    bizTypeAll="enterpriseIssueBill,enterpriseUsedBill"
                    initRef={cacheRef}
                    isMng
                />
            }
        >
            <Card>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={billList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: billListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, billModel, customerSystemModel, loading, user }) => ({
    global,
    billModel,
    customerSystemModel,
    listLoading: loading.effects['billModel/getBillList'],
    currentUser: user.currentUser,
}))(BillManagePage);
