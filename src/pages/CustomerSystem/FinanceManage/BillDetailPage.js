import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Descriptions,
    InputNumber,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';

const { TextArea } = Input;

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const BillDetailPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {} },
        billModel: { billList, billListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;
    const { actState: actStateList } = codeInfo;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        if (!actStateList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'actState',
            });
        }

        return () => {
            dispatch({
                type: 'billModel/updateManageList',
                params: {
                    billList: [],
                    billListTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };
                if (pageInfo.tabType !== '-1') {
                    params.actState = pageInfo.tabType;
                }

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'billModel/getBillList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const actStateOptions = useMemo(() => {
        if (actStateList) {
            return actStateList.map((ele) => {
                if (ele.codeValue == 5) return null;
                return <TabPane tab={ele.codeName} key={ele.codeValue} />;
            });
        }
        return [];
    }, [actStateList]);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const lookEvent = (item) => {
        history.push(`/sellerCenter/customer/finance/bill/list/detail/${item.actId}`);
    };

    const infoColumns = [
        {
            title: '账单周期',
            width: 200,
            dataIndex: 'billDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单数量',
            width: 200,
            dataIndex: 'orderNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '核销总金额',
            width: 140,
            dataIndex: 'settleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台服务金额',
            width: 140,
            dataIndex: 'serviceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算总额',
            width: 200,
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const recordColumns = [
        {
            title: '订单时间',
            width: 200,
            dataIndex: 'actTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单编号',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '用户账号',
            width: 140,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电卡名称',
            width: 140,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电卡编号',
            width: 200,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 140,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销金额',
            width: 140,
            dataIndex: 'monthAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper content={'每月3日初生成上月账单'}>
            <Card title="基础信息">
                <Descriptions>
                    <Descriptions.Item label="客户名称"></Descriptions.Item>
                    <Descriptions.Item label="客户编号"></Descriptions.Item>
                    <Descriptions.Item label="合同编号"></Descriptions.Item>
                    <Descriptions.Item label="账单周期"></Descriptions.Item>
                    <Descriptions.Item label="结算金额"></Descriptions.Item>
                </Descriptions>
            </Card>
            <br></br>
            <Card title="账单信息">
                <TablePro
                    name="info"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={billList}
                    columns={infoColumns}
                    pagination={false}
                />
            </Card>
            <br></br>

            <Card title="账单明细">
                <TablePro
                    name="detail"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={billList}
                    columns={recordColumns}
                    pagination={false}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, billModel, loading }) => ({
    global,
    billModel,
    listLoading: loading.effects['billModel/getBillList'],
}))(BillDetailPage);
