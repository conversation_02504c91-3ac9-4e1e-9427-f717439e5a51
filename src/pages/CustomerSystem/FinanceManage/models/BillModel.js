import {
    // 会员
    getBillListApi,
} from '@/services/Customer/CustomerBillApi';

const billModel = {
    namespace: 'billModel',
    state: {
        billList: [], // 列表
        billListTotal: 0, // 条数
    },
    effects: {
        /**
         *
         */
        *getBillList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: billList, total: billListTotal },
                } = yield call(getBillListApi, options);

                yield put({
                    type: 'updateManageList',
                    list: billList,
                    total: billListTotal,
                });
                return billList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateManageList(state, { list, total }) {
            return {
                ...state,
                billList: list,
                billListTotal: total,
            };
        },
    },
};
export default billModel;
