import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Divider,
    InputNumber,
    Radio,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { RangePicker } = DatePicker;

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 6,
};

const PAGE_TAB_TYPES = {
    CHARGE: '01', //充值
    SEND: '02', //发放
    CHECK: '03', //核销
};

const DATE_TYPES = {
    WEEK: '01', //七天
    MONTH: '02', //30天
    MORE: '03', //自定义
};

const { TextArea } = Input;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    let formItems = [];
    const mobile = (
        <Col key="mobile" span={8}>
            <FormItem name="mobile" label="用户账号">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );

    formItems.push(mobile);

    return (
        <Form {...formItemLayout} form={form} onFinish={onSubmit}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                {formItems}
            </SearchOptionsBar>
        </Form>
    );
};

const ChargeTabPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        accountCenterModel: { chargeList, chargeListTotal },
        listLoading,
        searchParams: cacheSearchParams,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const cashName = pathname + '_charge';

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: PAGE_TAB_TYPES.CHARGE,
        },
        props,
        cashName,
    );

    useEffect(() => {
        if (pageInit[cashName]) {
            form.setFieldsValue(pageInit[cashName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (cacheSearchParams) {
            refreshEvent();
        }
    }, [cacheSearchParams]);
    // 调用搜索接口
    const searchData = async () => {
        try {
            const params = {
                ...cacheSearchParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                dateTime: undefined,
            };

            if (cacheSearchParams?.dateTime?.length > 0) {
                params.beginDate = cacheSearchParams?.dateTime?.[0].format('YYYY-MM-DD') || '';
                params.endDate = cacheSearchParams?.dateTime?.[1].format('YYYY-MM-DD') || '';
            } else {
                return;
            }

            dispatch({
                type: 'global/setPageInit',
                cashName,
                info: {
                    form: {},
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'accountCenterModel/getPaymentOrderList',
                options: params,
            });
        } catch (error) {
            console.log(6666, error);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const refreshEvent = () => {
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    const lookEvent = (item) => {
        history.push(`/marketing/account/detail/${item.actId}`);
    };

    const tableColumns = [
        {
            title: '充值时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '客户简称',
            width: 200,
            dataIndex: 'enterpriseName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充值说明',
            width: 200,
            dataIndex: 'remark',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作人员',
            width: 140,
            dataIndex: 'creEmp',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充值金额',
            width: 140,
            dataIndex: 'payAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={chargeList}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: chargeListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};

export default ChargeTabPage;
