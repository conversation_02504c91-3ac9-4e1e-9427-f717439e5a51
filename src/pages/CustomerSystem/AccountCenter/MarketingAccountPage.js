import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Divider,
    InputNumber,
    Radio,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';
import ChartCard from '@/components/Charts/ChartCard';

import Trend from '@/components/Trend';
import Yuan from '@/utils/Yuan';
import classnames from 'classnames';
import pageStyles from './MarketingAccountPage.less';
import { isEmpty } from '@/utils/utils';
import SendTabPage from './SendTabPage';
import CheckTabPage from './CheckTabPage';
import ChargeTabPage from './ChargeTabPage';
import CouponTabPage from './CouponTabPage';
import CacheAreaView from '@/components/CacheAreaView';

import moment from 'moment';

const { RangePicker } = DatePicker;

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 6,
};

const PAGE_TAB_TYPES = {
    CHARGE: '01', //充值
    SEND: '02', //发放
    CHECK: '03', //核销
    COUPON: '04', // 优惠券
};

const DATE_TYPES = {
    WEEK: '01', //七天
    MONTH: '02', //30天
    MORE: '03', //自定义
};

const { TextArea } = Input;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const TabPageView = (props) => {
    const {
        tabType,
        searchParams,
        weekLoading,
        checkLoading,
        chargeLoading,
        couponLoading,
        changeTabType,
        cacheRef,
    } = props;

    const [searchBuyNo, updateBuyNo] = useState();

    if (tabType === PAGE_TAB_TYPES.SEND) {
        return (
            <SendTabPage
                {...props}
                listLoading={weekLoading}
                searchParams={searchParams}
                changeTabType={(info) => {
                    updateBuyNo(info.buyNo);
                    changeTabType(PAGE_TAB_TYPES.CHECK);
                }}
                cacheRef={cacheRef}
            />
        );
    } else if (tabType === PAGE_TAB_TYPES.CHECK) {
        return (
            <CheckTabPage
                {...props}
                listLoading={checkLoading}
                searchParams={searchParams}
                buyNo={searchBuyNo || undefined}
                clearBuyNo={() => {
                    updateBuyNo(undefined);
                }}
                cacheRef={cacheRef}
            />
        );
    } else if (tabType === PAGE_TAB_TYPES.COUPON) {
        return (
            <CouponTabPage
                {...props}
                listLoading={couponLoading}
                searchParams={searchParams}
                buyNo={searchBuyNo || undefined}
                clearBuyNo={() => {
                    updateBuyNo(undefined);
                }}
                cacheRef={cacheRef}
            />
        );
    } else if (tabType === PAGE_TAB_TYPES.CHARGE) {
        return <ChargeTabPage {...props} listLoading={chargeLoading} searchParams={searchParams} />;
    }
};

// 数据类型  1电量 2金额 3人数 4单枪电量
const IntroduceRow = ({ loading, info = {} }) => {
    const formatRate = (rate) => {
        const rateStr = String(rate || 0);
        return rateStr.replace(/-/g, '');
    };
    return (
        <Card
            bordered={false}
            // title={<span className={pageStyles.rankingTitle}>昨日运营状况</span>}
            bodyStyle={{ padding: 0 }}
            // style={{
            //     marginBottom: 24,
            // }}
        >
            <Row type="flex">
                <Col {...topColResponsiveProps}>
                    <div className={pageStyles['card-item']}>
                        <div className={pageStyles['card-title']}>累计充值金额</div>
                        <div className={pageStyles['card-content']}>
                            <Yuan key="yuan">{info?.payAmt || 0}</Yuan>
                            <span key="unit" className={pageStyles['chart-unit']}>
                                元
                            </span>
                        </div>
                    </div>
                </Col>
                <Col {...topColResponsiveProps}>
                    <div className={pageStyles['card-item']}>
                        <div className={pageStyles['card-title']}>账户余额</div>
                        <div className={pageStyles['card-content']}>
                            <Yuan>{info?.balanceAmt || 0}</Yuan>
                            <span className={pageStyles['chart-unit']}>元</span>
                        </div>
                    </div>
                </Col>
                <Col {...topColResponsiveProps}>
                    <div className={pageStyles['card-item']}>
                        <div className={pageStyles['card-title']}>累计发放金额</div>
                        <div className={pageStyles['card-content']}>
                            <span key="amt">{info?.issueAmt || 0}</span>
                            <span key="amt-unit" className={pageStyles['chart-unit']}>
                                元
                            </span>
                        </div>
                    </div>
                </Col>
                <Col {...topColResponsiveProps}>
                    <div className={classnames(pageStyles['card-item'], pageStyles['last'])}>
                        <div className={pageStyles['card-title']}>累计核销金额</div>
                        <div className={pageStyles['card-content']}>
                            <Yuan key="pq">{info?.usedAmt || 0}</Yuan>
                            <span key="pq-unit" className={pageStyles['chart-unit']}>
                                元
                            </span>
                        </div>
                    </div>
                </Col>
            </Row>
        </Card>
    );
};

const MarketingAccountPage = (props) => {
    const {
        dispatch,
        history,
        accountCenterModel: { accountInfo },
        customerSystemModel: { searchParams },
        currentUser,
        global: { enterpriseChildList },
    } = props;

    const {
        location: {},
    } = history;

    const [enterpriseForm] = Form.useForm();
    const [dateForm] = Form.useForm();
    const cacheRef = useRef();

    const enterpriseId = Form.useWatch('enterpriseId', enterpriseForm);
    const dateTime = Form.useWatch('dateTime', dateForm);

    const [tabType, changeTabType] = useState(
        (searchParams && searchParams?.tabType) || PAGE_TAB_TYPES.CHARGE,
    );

    const bizType = useMemo(() => {
        switch (tabType) {
            case PAGE_TAB_TYPES.SEND:
                return 'weekRela';
            case PAGE_TAB_TYPES.CHECK:
                return 'orderRecord';
            case PAGE_TAB_TYPES.COUPON:
                return 'couponPutUse';
            default:
                return '';
        }
    }, [tabType]);

    useEffect(() => {
        if (searchParams) {
            if (searchParams?.enterpriseId) {
                enterpriseForm.setFieldsValue({ enterpriseId: searchParams?.enterpriseId });
            }
            if (searchParams?.childEnterpriseId) {
                enterpriseForm.setFieldsValue({
                    childEnterpriseId: searchParams?.childEnterpriseId,
                });
            }
            if (searchParams?.startDate && searchParams?.endDate) {
                dateForm.setFieldsValue({
                    dateType: DATE_TYPES.MORE,
                    dateTime: [moment(searchParams?.startDate), moment(searchParams?.endDate)],
                });
            }
        } else {
            if (currentUser?.enterpriseId) {
                enterpriseForm.setFieldsValue({ enterpriseId: currentUser?.enterpriseId });
            }
            if (currentUser?.childEnterpriseId?.length) {
                enterpriseForm.setFieldsValue({
                    childEnterpriseId: currentUser?.childEnterpriseId,
                });
            }
        }

        return () => {
            dispatch({
                type: 'accountCenterModel/updateAccountInfo',
                info: null,
            });
            dispatch({
                type: 'accountCenterModel/resetAccountAllList',
            });
        };
    }, []);

    useEffect(() => {
        if (enterpriseId) {
            searchData();
        }
    }, [enterpriseId]);

    const [cacheSearchParams, updateCacheSearchParams] = useState();
    // 调用搜索接口
    const searchData = async () => {
        enterpriseForm.validateFields().then((enterpriseValues) => {
            dateForm.validateFields().then((dateValues) => {
                const params = { ...enterpriseValues, ...dateValues };
                if (params.childEnterpriseId) {
                    params.enterpriseId = params.childEnterpriseId;
                    params.childEnterpriseId = undefined;
                }
                updateCacheSearchParams({ ...params });
                dispatch({
                    type: 'accountCenterModel/resetAccountAllList',
                });
                dispatch({
                    type: 'accountCenterModel/getEnterpriseNum',
                    options: { enterpriseId: params?.enterpriseId },
                });
            });
        });
    };

    const changeTabTypeEvent = (type) => {
        dispatch({
            type: 'customerSystemModel/clearSearchParams',
        });
        changeTabType(type);
    };

    return (
        <PageHeaderWrapper
            content={
                <Row gutter={16}>
                    <Col span={12}>
                        <Form
                            {...formItemLayout}
                            form={enterpriseForm}
                            initialValues={{ childEnterpriseId: currentUser?.childEnterpriseId }}
                        >
                            <Row gutter={16}>
                                <Col span={12}>
                                    <EnterpriseSelectItem
                                        rules={[{ required: true, message: '请选择客户' }]}
                                        onChange={() => {
                                            enterpriseForm.setFieldsValue({
                                                childEnterpriseId: undefined,
                                            });
                                            searchData();
                                        }}
                                    />
                                </Col>
                                <Col span={12}>
                                    <FormItem label="子公司" name="childEnterpriseId">
                                        <Select
                                            placeholder="请选择"
                                            autoComplete="off"
                                            disabled={currentUser?.childEnterpriseId?.length > 0}
                                            onChange={() => {
                                                searchData();
                                            }}
                                            allowClear
                                        >
                                            {enterpriseChildList?.map((item) => {
                                                return (
                                                    <Option
                                                        value={item.enterpriseId}
                                                        key={item.enterpriseId}
                                                    >
                                                        {item.enterpriseName}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </Col>
                    <Col span={12}>
                        <Form {...formItemLayout} form={dateForm}>
                            <Row gutter={12}>
                                <Col>
                                    <FormItem
                                        name="dateType"
                                        noStyle
                                        initialValue={DATE_TYPES.WEEK}
                                    >
                                        <Radio.Group
                                            style={{ marginBottom: '24px' }}
                                            optionType="button"
                                            onChange={(event) => {
                                                const {
                                                    target: { value },
                                                } = event;
                                                if (value === DATE_TYPES.WEEK) {
                                                    dateForm.setFieldsValue({
                                                        dateTime: [
                                                            moment()
                                                                .subtract(7, 'day')
                                                                .startOf('day'),
                                                            moment(),
                                                        ],
                                                    });
                                                } else if (value === DATE_TYPES.MONTH) {
                                                    dateForm.setFieldsValue({
                                                        dateTime: [
                                                            moment()
                                                                .subtract(30, 'day')
                                                                .startOf('day'),
                                                            moment(),
                                                        ],
                                                    });
                                                }
                                                searchData();
                                            }}
                                        >
                                            <Radio value={DATE_TYPES.WEEK}>近7天</Radio>
                                            <Radio value={DATE_TYPES.MONTH}>近30天</Radio>
                                            <Radio value={DATE_TYPES.MORE}>自定义</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                </Col>
                                <Col span={10}>
                                    <FormItem
                                        shouldUpdate={(pre, after) =>
                                            pre.dateType !== after.dateType
                                        }
                                    >
                                        {({ getFieldValue }) => {
                                            const dateType = getFieldValue('dateType');
                                            let timeDisable = false;
                                            if (
                                                dateType === DATE_TYPES.WEEK ||
                                                dateType === DATE_TYPES.MONTH
                                            ) {
                                                timeDisable = true;
                                            }
                                            return (
                                                <FormItem
                                                    noStyle
                                                    name="dateTime"
                                                    initialValue={[
                                                        moment().subtract(7, 'day').startOf('day'),
                                                        moment(),
                                                    ]}
                                                    rules={[
                                                        ({ getFieldValue }) => ({
                                                            validator(rule, value) {
                                                                if (!value) {
                                                                    return Promise.reject(
                                                                        '请选择查询日期',
                                                                    );
                                                                }
                                                                if (value) {
                                                                    if (!value[0]) {
                                                                        return Promise.reject(
                                                                            '请选择开始日期',
                                                                        );
                                                                    }
                                                                    if (!value[1]) {
                                                                        return Promise.reject(
                                                                            '请选择结束日期',
                                                                        );
                                                                    }
                                                                    // if (value[0] && value[1]) {
                                                                    //     const startTime = +new Date(value[0]);
                                                                    //     const endTime = +new Date(value[1]);
                                                                    //     const dest = 60 * 1000 * 60 * 24 * 90;

                                                                    //     if (Math.abs(startTime - endTime) > dest) {
                                                                    //         return Promise.reject(
                                                                    //             '选取范围最大不超过90天',
                                                                    //         );
                                                                    //     }
                                                                    // }
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <RangePicker
                                                        onChange={() => {
                                                            searchData();
                                                        }}
                                                        disabled={timeDisable}
                                                        format="YYYY-MM-DD"
                                                    />
                                                </FormItem>
                                            );
                                        }}
                                    </FormItem>
                                </Col>
                            </Row>
                        </Form>
                    </Col>
                </Row>
            }
            extra={<CacheAreaView bizType={bizType} initRef={cacheRef} isMng />}
        >
            <Card>
                <IntroduceRow info={accountInfo} />
            </Card>
            <br />

            <Card>
                <Tabs activeKey={tabType} onChange={changeTabTypeEvent}>
                    <TabPane tab="充值记录" key={PAGE_TAB_TYPES.CHARGE} />
                    <TabPane tab="充电卡发放" key={PAGE_TAB_TYPES.SEND} />
                    <TabPane tab="充电卡核销" key={PAGE_TAB_TYPES.CHECK} />
                    <TabPane tab="优惠券记录" key={PAGE_TAB_TYPES.COUPON} />
                </Tabs>

                <TabPageView
                    {...props}
                    searchParams={cacheSearchParams}
                    enterpriseId={enterpriseId}
                    tabType={tabType}
                    dateTime={dateTime}
                    changeTabType={changeTabType}
                    cacheRef={cacheRef}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, accountCenterModel, customerSystemModel, loading, user }) => ({
    global,
    accountCenterModel,
    customerSystemModel,
    user,
    currentUser: user.currentUser,
    chargeLoading: loading.effects['accountCenterModel/getPaymentOrderList'],
    weekLoading: loading.effects['accountCenterModel/getWeekRelaList'],
    checkLoading: loading.effects['accountCenterModel/getEnterpriseOrderRecord'],
    couponLoading: loading.effects['accountCenterModel/getEnterpriseCouponRecord'],
}))(MarketingAccountPage);
