import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Divider,
    InputNumber,
    Radio,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { isEmpty } from '@/utils/utils';

import moment from 'moment';

const { RangePicker } = DatePicker;

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 6,
};

const PAGE_TAB_TYPES = {
    CHARGE: '01', //充值
    SEND: '02', //发放
    CHECK: '03', //核销
};

const DATE_TYPES = {
    WEEK: '01', //七天
    MONTH: '02', //30天
    MORE: '03', //自定义
};

const { TextArea } = Input;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    let formItems = [];
    const mobile = (
        <Col key="mobile" span={8}>
            <FormItem name="mobile" label="用户账号">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );

    const buyNoItem = (
        <Col key="buyNo" span={8}>
            <FormItem name="buyNo" label="充电卡编号">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );

    formItems.push(mobile);
    formItems.push(buyNoItem);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={() => {
                onSubmit && onSubmit();
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                {formItems}
            </SearchOptionsBar>
        </Form>
    );
};

const CheckTabPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        accountCenterModel: { checkOrderList, checkOrderListTotal },
        listLoading,
        searchParams: cacheSearchParams,
        buyNo,
        clearBuyNo,
        cacheRef,
    } = props;

    const {
        location: { pathname, query },
    } = history;

    const [form] = Form.useForm();

    const cashName = pathname + '_check';

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: PAGE_TAB_TYPES.CHECK,
        },
        props,
        cashName,
    );

    useEffect(() => {
        if (pageInit[cashName]) {
            form.setFieldsValue(pageInit[cashName].form);
        }
    }, []);

    useEffect(() => {
        if (buyNo) {
            form.setFieldsValue({ buyNo: buyNo });
            searchData();
        }
    }, [buyNo]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (cacheSearchParams) {
            refreshEvent();
        }
    }, [cacheSearchParams]);

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            if (!cacheSearchParams) {
                return;
            }
            if (isDownload && !cacheSearchParams?.enterpriseId) {
                message.error('请选择查询客户');
                return;
            }
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                ...data,
                ...cacheSearchParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                dateTime: undefined,
                enterpriseRecordType: '01',
            };

            if (cacheSearchParams?.dateTime?.length > 0) {
                params.beginDate = cacheSearchParams?.dateTime?.[0].format('YYYY-MM-DD') || '';
                params.endDate = cacheSearchParams?.dateTime?.[1].format('YYYY-MM-DD') || '';
            } else {
                return;
            }

            if (isDownload) {
                // 下载
                cacheRef.current?.apply(params).then(() => cacheRef.current.count());
            } else {
                dispatch({
                    type: 'global/setPageInit',
                    cashName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'accountCenterModel/getEnterpriseOrderRecord',
                    options: params,
                });
            }
            return;
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        dispatch({
            type: 'customerSystemModel/clearSearchParams',
        });
        clearBuyNo && clearBuyNo();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const refreshEvent = () => {
        clearBuyNo && clearBuyNo();
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    const lookEvent = (item) => {
        history.push(`/marketing/account/detail/${item.actId}`);
    };

    const tableColumns = [
        {
            title: '订单时间',
            width: 200,
            dataIndex: 'orderTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单编号',
            width: 200,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户账号',
            width: 200,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '充电卡名称',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
                // return (
                //     <Link to={`/sellerCenter/customer/chargecard/list?enterpriseId=${enterpriseId}`}>
                //         {text}
                //     </Link>
                // );
            },
        },
        {
            title: '充电卡编号',
            width: 200,
            dataIndex: 'buyNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '城市',
            width: 120,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 120,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销金额',
            width: 120,
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                form={form}
                tabType={pageInfo.tabType}
                onSubmit={() => {
                    if (cacheSearchParams?.enterpriseId) {
                        refreshEvent();
                    } else {
                        message.error('请先选择客户');
                    }
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={checkOrderList}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: checkOrderListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};

export default CheckTabPage;
