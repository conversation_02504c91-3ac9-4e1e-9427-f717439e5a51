import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    Divider,
    InputNumber,
    Radio,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { isEmpty } from '@/utils/utils';

import moment from 'moment';

const { RangePicker } = DatePicker;

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 6,
};

const PAGE_TAB_TYPES = {
    CHARGE: '01', //充值
    SEND: '02', //发放
    CHECK: '03', //核销
};

const DATE_TYPES = {
    WEEK: '01', //七天
    MONTH: '02', //30天
    MORE: '03', //自定义
};

const { TextArea } = Input;

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    let formItems = [];
    const mobile = (
        <Col key="mobile" span={8}>
            <FormItem name="mobile" label="用户账号">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );
    const actName = (
        <Col key="actName" span={8}>
            <FormItem name="actName" label="充电卡名称">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );
    const actNoItem = (
        <Col key="actNo" span={8}>
            <FormItem name="actNo" label="活动编号">
                <Input placeholder="请输入" autoComplete="off" allowClear></Input>
            </FormItem>
        </Col>
    );
    const cardState = (
        <Col key="cardState" span={8}>
            <FormItem name="cardState" label="充电卡状态">
                <Select placeholder="请选择" allowClear>
                    <Option value="1">待开始</Option>
                    <Option value="2">进行中</Option>

                    <Option value="3">已超频</Option>

                    <Option value="4">已结束</Option>
                    <Option value="5">已退卡</Option>
                </Select>
            </FormItem>
        </Col>
    );

    formItems.push(mobile);
    formItems.push(actName);
    formItems.push(actNoItem);
    formItems.push(cardState);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={() => {
                onSubmit && onSubmit();
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                {formItems}
            </SearchOptionsBar>
        </Form>
    );
};

const SendTabPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        accountCenterModel: { weekRelaList, weekRelaListTotal },
        customerSystemModel: { searchParams },

        listLoading,
        searchParams: cacheSearchParams,
        changeTabType,
        cacheRef,
    } = props;

    const {
        location: { pathname, query },
    } = history;

    const [form] = Form.useForm();

    const cashName = pathname + '_send';

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: PAGE_TAB_TYPES.SEND,
        },
        props,
        cashName,
    );

    useEffect(() => {
        if (pageInit[cashName]) {
            form.setFieldsValue(pageInit[cashName].form);
        }
        if (searchParams && searchParams?.actNo) {
            form.setFieldsValue({ actNo: searchParams?.actNo });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (cacheSearchParams) {
            refreshEvent();
        }
    }, [cacheSearchParams]);

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            if (!cacheSearchParams) {
                return;
            }
            if (isDownload && !cacheSearchParams?.enterpriseId) {
                message.error('请选择查询客户');
                return;
            }
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                ...data,
                ...cacheSearchParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                dateTime: undefined,
            };

            if (cacheSearchParams?.dateTime?.length > 0) {
                params.beginDate = cacheSearchParams?.dateTime?.[0].format('YYYY-MM-DD') || '';
                params.endDate = cacheSearchParams?.dateTime?.[1].format('YYYY-MM-DD') || '';
            } else {
                return;
            }

            if (isDownload) {
                // 下载
                cacheRef.current?.apply(params).then(() => cacheRef.current.count());
            } else {
                dispatch({
                    type: 'global/setPageInit',
                    cashName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'accountCenterModel/getWeekRelaList',
                    options: params,
                });
            }
            return;
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();

        dispatch({
            type: 'customerSystemModel/clearSearchParams',
        });
        changePageInfo({ pageIndex: 1 });
    };

    const refreshEvent = () => {
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    const lookEvent = (item) => {
        history.push(`/marketing/account/detail/${item.actId}`);
    };

    const tableColumns = [
        {
            title: '发放时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '客户简称',
            width: 200,
            dataIndex: 'enterpriseName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户账号',
            width: 200,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '充电卡名称',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动编号',
            width: 140,
            dataIndex: 'actNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电卡ID',
            width: 140,
            dataIndex: 'buyNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '权益总额',
            width: 140,
            dataIndex: 'discountLimitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已优惠金额',
            width: 140,
            dataIndex: 'discountAmt',
            render(text, record) {
                return (
                    <span
                        className={styles['table-btn']}
                        title={text}
                        onClick={() => {
                            changeTabType && changeTabType(record);
                        }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '充电卡状态',
            width: 140,
            dataIndex: 'cardStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                form={form}
                tabType={pageInfo.tabType}
                onSubmit={() => {
                    if (cacheSearchParams?.enterpriseId) {
                        refreshEvent();
                    } else {
                        message.error('请先选择客户');
                    }
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={weekRelaList}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: weekRelaListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};

export default SendTabPage;
