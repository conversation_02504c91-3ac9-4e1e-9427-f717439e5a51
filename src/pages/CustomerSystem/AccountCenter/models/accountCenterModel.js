import {
    // 会员
    getPaymentOrderListApi,
    getWeekRelaListApi,
    getEnterpriseNumApi,
    getEnterpriseOrderRecordApi,
    getEnterpriseCouponRecordApi,
} from '@/services/Customer/CustomerAccountApi';

const accountCenterModel = {
    namespace: 'accountCenterModel',
    state: {
        accountInfo: null,

        chargeList: [], //充值记录
        chargeListTotal: 0, // 条数

        weekRelaList: [], // 发放列表
        weekRelaListTotal: 0, // 条数

        checkOrderList: [], //核销记录
        checkOrderListTotal: 0, // 条数

        couponOrderList: [], //优惠券记录
        couponOrderListTotal: 0, // 条数
    },
    effects: {
        /**
         *
         */
        *getPaymentOrderList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: accountList, total: accountListTotal },
                } = yield call(getPaymentOrderListApi, options);

                yield put({
                    type: 'updateChargeList',
                    list: accountList,
                    total: accountListTotal,
                });
                return accountList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         *
         */
        *getWeekRelaList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: accountList, total: accountListTotal },
                } = yield call(getWeekRelaListApi, options);

                yield put({
                    type: 'updateWeekRelaList',
                    list: accountList,
                    total: accountListTotal,
                });
                return accountList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *getEnterpriseOrderRecord({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: accountList, total: accountListTotal },
                } = yield call(getEnterpriseOrderRecordApi, options);

                yield put({
                    type: 'updateCheckOrderList',
                    list: accountList,
                    total: accountListTotal,
                });
                return accountList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *getEnterpriseCouponRecord({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: accountList, total: accountListTotal },
                } = yield call(getEnterpriseCouponRecordApi, options);

                yield put({
                    type: 'updateAccountProperty',
                    params: {
                        couponOrderList: accountList,
                        couponOrderListTotal: accountListTotal,
                    },
                });
                return accountList;
            } catch (error) {
                return Promise.reject(error);
            }
        },

        /**
         *
         */
        *getEnterpriseNum({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getEnterpriseNumApi, options);

                yield put({
                    type: 'updateAccountInfo',
                    info: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        resetAccountAllList(state) {
            return {
                ...state,
                chargeList: [],
                chargeListTotal: 0,
                weekRelaList: [],
                weekRelaListTotal: 0,
                checkOrderList: [],
                checkOrderListTotal: 0,
                couponOrderList: [], //优惠券记录
                couponOrderListTotal: 0, // 条数
            };
        },
        updateAccountProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateChargeList(state, { list, total }) {
            return {
                ...state,
                chargeList: list,
                chargeListTotal: total,
            };
        },

        updateWeekRelaList(state, { list, total }) {
            return {
                ...state,
                weekRelaList: list,
                weekRelaListTotal: total,
            };
        },
        updateCheckOrderList(state, { list, total }) {
            return {
                ...state,
                checkOrderList: list,
                checkOrderListTotal: total,
            };
        },
        updateAccountInfo(state, { info }) {
            return {
                ...state,
                accountInfo: info,
            };
        },
    },
};
export default accountCenterModel;
