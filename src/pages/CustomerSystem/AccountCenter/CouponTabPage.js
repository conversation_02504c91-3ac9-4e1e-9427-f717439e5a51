import { Col, Form, message, Input, Select, AutoComplete } from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { isEmpty } from '@/utils/utils';
import { getCouponListApi } from '@/services/Marketing/MarketingCouponApi';

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
        user: { currentUser },
        global: { codeInfo },
    } = props;

    const { cpnUseStatus } = codeInfo;
    useEffect(() => {
        initOptions();
        if (!cpnUseStatus || cpnUseStatus.length == 0) {
            dispatch({
                type: 'global/initCode',
                code: 'cpnUseStatus',
            });
        }
        return () => updateCpnNames([]);
    }, []);

    const cpnOwnerOptionsView = useMemo(
        () =>
            cpnUseStatus?.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            }),
        [cpnUseStatus],
    );

    const [serchStr, updateSerchStr] = useState('');
    const [cpnNames, updateCpnNames] = useState([]);

    const initOptions = async () => {
        try {
            const {
                data: { records = [] },
            } = await getCouponListApi({
                contributParty: '05',
                enterpriseId: currentUser.enterpriseId,
            });
            if (records instanceof Array) {
                updateCpnNames(
                    records.map((ele) => {
                        return {
                            label: ele.cpnName,
                            value: ele.cpnName,
                        };
                    }),
                );
            }
            return records;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const filterOptions = useMemo(() => {
        if (serchStr === '') {
            return [];
        }
        return cpnNames.filter((ele) => ele.value?.indexOf(serchStr) >= 0);
    }, [cpnNames, serchStr]);

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={() => {
                onSubmit && onSubmit();
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem name="mobile" label="用户账号">
                        <Input placeholder="请输入" autoComplete="off" allowClear></Input>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="cpnName" label="优惠券名称">
                        <AutoComplete
                            options={filterOptions}
                            onSearch={async (value) => {
                                updateSerchStr(value);
                            }}
                            placeholder="请输入"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="优惠券状态" name="useStatus" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {cpnOwnerOptionsView}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const CouponTabPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        accountCenterModel: { couponOrderList, couponOrderListTotal },
        listLoading,
        searchParams: cacheSearchParams,
        clearBuyNo,
        cacheRef,
    } = props;

    const {
        location: { pathname, query },
    } = history;

    const [form] = Form.useForm();

    const cashName = pathname + '_coupon';

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cashName);

    useEffect(() => {
        if (pageInit[cashName]) {
            form.setFieldsValue(pageInit[cashName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (cacheSearchParams) {
            refreshEvent();
        }
    }, [cacheSearchParams]);

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            if (!cacheSearchParams) {
                return;
            }
            if (isDownload && !cacheSearchParams?.enterpriseId) {
                message.error('请选择查询客户');
                return;
            }
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                ...data,
                ...cacheSearchParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                dateTime: undefined,
            };

            if (cacheSearchParams?.dateTime?.length > 0) {
                params.beginDate = cacheSearchParams?.dateTime?.[0].format('YYYY-MM-DD') || '';
                params.endDate = cacheSearchParams?.dateTime?.[1].format('YYYY-MM-DD') || '';
            } else {
                return;
            }

            if (isDownload) {
                // 下载
                cacheRef.current?.apply(params).then(() => cacheRef.current.count());
            } else {
                dispatch({
                    type: 'global/setPageInit',
                    cashName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'accountCenterModel/getEnterpriseCouponRecord',
                    options: params,
                });
            }
            return;
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        dispatch({
            type: 'customerSystemModel/clearSearchParams',
        });
        clearBuyNo && clearBuyNo();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const refreshEvent = () => {
        clearBuyNo && clearBuyNo();
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
    };

    const lookEvent = (item) => {
        history.push(`/marketing/account/detail/${item.actId}`);
    };

    const tableColumns = [
        {
            title: '发放时间',
            width: 200,
            dataIndex: 'putTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '核销时间',
            width: 200,
            dataIndex: 'usedTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单编号',
            width: 200,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券面额',
            width: 120,
            dataIndex: 'cpnAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户账号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '优惠券名称',
            width: 160,
            dataIndex: 'cpnName',
            render(text, record) {
                if (record.cpnId) {
                    return (
                        <Link
                            key={'01'}
                            to={`/marketing/couponCenter/coupon/list/look/${record.cpnId}`}
                            target="_blank"
                        >
                            {text}
                        </Link>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券组编号',
            width: 140,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '城市',
            width: 120,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 120,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已优惠金额',
            width: 120,
            dataIndex: 'usedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                {...props}
                form={form}
                tabType={pageInfo.tabType}
                onSubmit={() => {
                    if (cacheSearchParams?.enterpriseId) {
                        refreshEvent();
                    } else {
                        message.error('请先选择客户');
                    }
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={couponOrderList}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponOrderListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};

export default CouponTabPage;
