import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    InputNumber,
    Radio,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { saveCustomerApi, getCustomerDetailApi } from '@/services/Customer/CustomerManageApi';
import NumericInput from '@/components/NumericInput/index';
import { isEmpty } from '@/utils/utils';

const { TextArea } = Input;

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    wrapperCol: {
        span: 8,
    },
};

const inputConfig = {
    showCount: true,
    autoComplete: 'off',
};

const UpdatePage = (props) => {
    const {
        dispatch,
        history,
        match,
        global: { enterpriseList },
        customerModel: { customerList, customerListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname, state },
    } = history;

    const [enterpriseId, changeEnterpriseId] = useState(state?.enterpriseId || null);

    const [editForm] = Form.useForm();

    const goBack = () => {
        history.go(-1);
    };

    useEffect(() => {
        dispatch({
            type: 'global/getEnterpriseList',
        });
    }, []);

    useEffect(() => {
        if (enterpriseId) {
            initCustomerDetails(enterpriseId);
        }
    }, [enterpriseId]);

    const initCustomerDetails = async (id) => {
        try {
            const { data } = await getCustomerDetailApi({ enterpriseId: id });
            initFormData(data);
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const initFormData = (info) => {
        if (info) {
            editForm.setFieldsValue(info);
        }
    };

    const submitSaveEvent = async (values) => {
        try {
            let params = {
                ...values,
                enterpriseId,
            };
            await saveCustomerApi(params);
            message.success('提交成功');
            goBack();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Form
                    form={editForm}
                    onFinish={submitSaveEvent}
                    initialValues={{}}
                    scrollToFirstError
                    {...formItemLayout}
                >
                    <FormItem label="客户类型" name="enterpriseType" initialValue={'01'} required>
                        <Radio.Group placeholder="请选择" disabled={enterpriseId != undefined}>
                            <Radio value={'01'}>总公司</Radio>
                            <Radio value={'02'}>子公司</Radio>
                        </Radio.Group>
                    </FormItem>

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.enterpriseType !== curValues.enterpriseType
                        }
                    >
                        {({ getFieldValue }) => {
                            const enterpriseType = getFieldValue('enterpriseType');
                            return (
                                (enterpriseType == '02' && (
                                    <FormItem
                                        label="上级组织"
                                        name="parentEnterpriseId"
                                        rules={[{ required: true, message: '请选择' }]}
                                    >
                                        <Select
                                            placeholder="请选择"
                                            showSearch
                                            filterOption={(input, option) =>
                                                option.children
                                                    .toLowerCase()
                                                    .indexOf(input.toLowerCase()) >= 0
                                            }
                                            allowClear
                                            disabled={enterpriseId != undefined}
                                        >
                                            {enterpriseList.map((d) => (
                                                <Option key={d.enterpriseId}>
                                                    {d.enterpriseName}
                                                </Option>
                                            ))}
                                        </Select>
                                    </FormItem>
                                )) ||
                                null
                            );
                        }}
                    </FormItem>
                    {/* <FormItem label="客户编号" rules={[{ required: true, message: '请填写' }]}>
                        <Input placeholder="请填写" maxLength={25} {...inputConfig}></Input>
                    </FormItem> */}
                    <FormItem
                        label="客户名称"
                        name="enterpriseName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input placeholder="请填写" maxLength={25} {...inputConfig}></Input>
                    </FormItem>
                    <FormItem
                        label="企业全称"
                        name="enterpriseFullName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input placeholder="请填写" maxLength={50} {...inputConfig}></Input>
                    </FormItem>
                    <FormItem
                        label="合同编号"
                        name="contractNo"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input placeholder="请填写" maxLength={30} {...inputConfig}></Input>
                    </FormItem>
                    <FormItem
                        label="对账方式"
                        name="reconciliationMethod"
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'01'}
                    >
                        <Radio.Group placeholder="请选择">
                            <Radio value={'01'}>按发放金额</Radio>
                            <Radio value={'02'}>按核销金额</Radio>
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        label="溢价率"
                        name="premiumRate"
                        initialValue={0}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请填写溢价率');
                                    }
                                    const result = Number(value);
                                    if (result > 100 || result < -100) {
                                        return Promise.reject('溢价率值必须在-100~100之间');
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请填写服务费比例"
                            type="number"
                            min={-100}
                            step={0.01}
                            max={100}
                            precision={2}
                            addonAfter={'%'}
                        />
                    </FormItem>

                    <FormItem
                        label="联系人"
                        name="contact"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input placeholder="请填写" maxLength={25} {...inputConfig}></Input>
                    </FormItem>
                    <FormItem
                        label="联系人手机"
                        name="mobile"
                        rules={[
                            { required: true, message: '请填写' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (value?.length != 11) {
                                        return Promise.reject('请填写11位手机号');
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <NumericInput
                            placeholder="请填写"
                            maxLength={11}
                            autoComplete="off"
                            isHide
                            {...inputConfig}
                        ></NumericInput>
                    </FormItem>
                    <FormItem style={{ marginLeft: '140px' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button
                                onClick={() => {
                                    goBack();
                                }}
                            >
                                取消
                            </Button>
                        </Space>
                    </FormItem>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, customerModel, loading, weekCardModel }) => ({
    global,
    customerModel,
    weekCardModel,
    listLoading: loading.effects['customerModel/getCustomerDetail'],
}))(UpdatePage);
