import {
    // 会员
    getCustomerListApi,
    getCustomerDetailApi,
} from '@/services/Customer/CustomerManageApi';

const customerModel = {
    namespace: 'customerModel',
    state: {
        customerList: [], // 列表
        customerListTotal: 0, // 条数

        editActInfo: null, // 详情
    },
    effects: {
        /**
         *
         */
        *getCustomerList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: customerList, total: customerListTotal },
                } = yield call(getCustomerListApi, options);

                yield put({
                    type: 'updateManageList',
                    list: customerList,
                    total: customerListTotal,
                });
                return customerList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *getCustomerDetail({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getCustomerDetailApi, options);

                yield put({
                    type: 'updateCustomerInfo',
                    customerInfo: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateManageList(state, { list, total }) {
            return {
                ...state,
                customerList: list,
                customerListTotal: total,
            };
        },
        updateCustomerInfo(state, { customerInfo }) {
            return {
                ...state,
                editActInfo: customerInfo,
            };
        },
    },
};
export default customerModel;
