import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    Tabs,
    message,
    Input,
    Select,
    InputNumber,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    getCustomerListApiPath,
    saveEnterprisePaymentOrderApi,
} from '@/services/Customer/CustomerManageApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem/index';
import { exportTableByParams, isEmpty } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import CacheAreaView from '@/components/CacheAreaView';

const { TextArea } = Input;

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        form,
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
        currentUser,
        global: { enterpriseChildList },
    } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{ childEnterpriseId: currentUser?.childEnterpriseId }}
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                exportName="导出至暂存区"
            >
                <Col span={8}>
                    <EnterpriseSelectItem
                        form={form}
                        onChange={() => {
                            form.setFieldsValue({ childEnterpriseId: undefined });
                        }}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="子公司" name="childEnterpriseId">
                        <Select
                            placeholder="请选择"
                            autoComplete="off"
                            disabled={currentUser?.childEnterpriseId?.length > 0}
                            allowClear
                        >
                            {enterpriseChildList?.map((item) => {
                                return (
                                    <Option value={item.enterpriseId} key={item.enterpriseId}>
                                        {item.enterpriseName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="对账方式" name="reconciliationMethod">
                        <Select placeholder="请选择" autoComplete="off">
                            <Option value="01">按发放</Option>
                            <Option value="02">按核销</Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ManagePage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo = {} },
        customerModel: { customerList, customerListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;
    const { actState: actStateList } = codeInfo;

    const [form] = Form.useForm();

    const [editForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    const [chargeVisible, updateChargeVisible] = useState(false);

    const [curSelectItem, updateCurSelectItem] = useState();

    const cacheRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        if (!actStateList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'actState',
            });
        }

        return () => {
            dispatch({
                type: 'customerModel/updateManageList',
                params: {
                    customerList: [],
                    customerListTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const formatList = useMemo(() => {
        const list = [];
        customerList?.map((ele) => {
            list.push({
                ...ele,
                children: (ele?.childList?.length && ele?.childList) || undefined,
                childList: undefined,
            });
        });
        return list;
    }, [customerList]);

    // 调用搜索接口
    const searchData = (isDownload = false) => {
        form.validateFields().then((data) => {
            try {
                const data = form.getFieldsValue();
                const params = {
                    ...data,
                    pageIndex: (isDownload == false && pageInfo.pageIndex) || undefined,
                    pageSize: (isDownload == false && pageInfo.pageSize) || undefined,
                    enterpriseType: '01',
                };
                if (data.childEnterpriseId) {
                    params.enterpriseId = data.childEnterpriseId;
                    params.childEnterpriseId = undefined;
                    params.enterpriseType = '02';
                }

                if (isDownload) {
                    // 下载
                    cacheRef.current?.apply(params).then(() => cacheRef.current.count());
                } else {
                    dispatch({
                        type: 'global/setPageInit',
                        pathname,
                        info: {
                            form: data,
                            state: pageInfo,
                        },
                    });

                    dispatch({
                        type: 'customerModel/getCustomerList',
                        options: params,
                    });
                }
                return;
            } catch (error) {}
        });
    };

    // 导出
    // const exportFormEvent = () => {
    //     const data = form.getFieldsValue();
    //     const params = {
    //         ...data,
    //         pageIndex: pageInfo.pageIndex,
    //         pageSize: pageInfo.pageSize,
    //     };
    //     const columnsStrs = [];
    //     for (const item of columns) {
    //         if (item.dataIndex) {
    //             columnsStrs.push({
    //                 key: item.dataIndex,
    //                 value: item.name || item.title,
    //             });
    //         }
    //     }
    //     exportTableByParams({
    //         methodUrl: getCustomerListApiPath,
    //         options: params,
    //         columnsStr: columnsStrs,
    //     });
    // };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push('/sellerCenter/customer/manage/list/add');
    };

    const editEvent = (item) => {
        history.push(`/sellerCenter/customer/manage/list/update`, {
            enterpriseId: item.enterpriseId,
        });
    };

    const chargeEvent = (item) => {
        updateCurSelectItem(item);
        openChargeEvent();
    };

    const openChargeEvent = () => {
        updateChargeVisible(true);
    };

    const closeChargeEvent = () => {
        updateChargeVisible(false);
        updateCurSelectItem(undefined);
        editForm.resetFields();
    };

    const submitChargeEvent = async (values) => {
        try {
            let params = {
                ...values,
                enterpriseId: curSelectItem.enterpriseId,
            };
            await saveEnterprisePaymentOrderApi(params);
            message.success('提交成功');
            searchData();
            closeChargeEvent();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const columns = [
        {
            title: '创建时间',
            width: 260,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '客户编号',
            width: 200,
            dataIndex: 'enterpriseNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '客户简称',
            width: 200,
            dataIndex: 'enterpriseName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '企业全称',
            width: 140,
            dataIndex: 'enterpriseFullName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '合同编号',
            width: 120,
            dataIndex: 'contractNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '帐号余额',
            width: 120,
            dataIndex: 'balanceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '对账方式',
            width: 120,
            dataIndex: 'reconciliationMethodName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '溢价率',
            width: 120,
            dataIndex: 'premiumRate',
            render(text, record) {
                return <span title={text}>{(text?.length && `${text}%`) || ''}</span>;
            },
        },
        {
            title: '联系人',
            width: 120,
            dataIndex: 'contact',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '联系人手机',
            width: 120,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 120,

            fixed: 'right',
            render: (text, record) => {
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );
                const payBtn = (
                    <span className={styles['table-btn']} onClick={() => chargeEvent(record)}>
                        充值
                    </span>
                );

                let btns = [editBtn, payBtn];

                return <Space>{btns}</Space>;
            },
        },
    ];

    return (
        <PageHeaderWrapper
            extra={<CacheAreaView bizType={'enterprise'} initRef={cacheRef} isMng />}
        >
            <Card>
                <SearchLayout
                    {...props}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        <PlusOutlined />
                        新建
                    </Button>
                </div>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.enterpriseId}
                    dataSource={formatList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: customerListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                    expandable={{ defaultExpandAllRows: true }}
                />
            </Card>

            <Modal
                title="客户充值"
                width={500}
                visible={chargeVisible}
                onCancel={() => {
                    closeChargeEvent();
                }}
                footer={null}
                maskClosable={false}
                destroyOnClose
            >
                <Form
                    form={editForm}
                    onFinish={submitChargeEvent}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <FormItem label="客户全称" required>
                        {(curSelectItem && curSelectItem.enterpriseFullName) || ''}
                    </FormItem>
                    <FormItem label="客户简称" required>
                        {(curSelectItem && curSelectItem.enterpriseName) || ''}
                    </FormItem>

                    <FormItem
                        label="充值金额"
                        name="payAmt"
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请填写充值金额');
                                    }
                                    // const result = Number(value);
                                    // if (result <= 0) {
                                    //     return Promise.reject('充值金额必须大于0');
                                    // }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            placeholder="请填写充值金额"
                            type="number"
                            step={0.01}
                            precision={2}
                        />
                    </FormItem>

                    <FormItem
                        label="充值说明"
                        name="remark"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <TextArea
                            rows={6}
                            maxLength={200}
                            placeholder="请填写充值说明"
                            autoComplete="off"
                            showCount
                        />
                    </FormItem>
                    <FormItem wrapperCol={{ offset: '8' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button
                                onClick={() => {
                                    closeChargeEvent();
                                }}
                            >
                                取消
                            </Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, customerModel, loading, user }) => ({
    global,
    customerModel,
    listLoading: loading.effects['customerModel/getCustomerList'],
    currentUser: user.currentUser,
}))(ManagePage);
