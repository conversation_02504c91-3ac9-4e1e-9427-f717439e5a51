import SearchOptionsBar from '@/components/SearchOptionsBar';
import { useRequest } from 'ahooks';
import { Button, Col, DatePicker, Form, Input, Select, Space, Switch, message } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { Fragment, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { Link, useModel } from 'umi';
import { TASK_DETAIL_ACTIVE_KEY } from './TaskDetailLayout';
import moment from 'moment';
import TablePro from '@/components/TablePro';
import styles from '@/assets/styles/common.less';
import { exportQrcodeList } from '@/services/AssetCenter/QrcodeManageApi';
import { saveTaskStateApi } from '@/services/Marketing/TaskCenterApi';

const { RangePicker } = DatePicker;
const SearchLayout: React.FC<{
    initRef: any;
    showStyle: string;
}> = (props) => {
    const [form] = Form.useForm();
    const { showStyle } = props;
    const { listLoading, searchList, pagination, cacheRef } = useModel('taskManage.list');

    const { initRef } = props;
    useImperativeHandle(initRef, () => ({
        resetForm,
    }));

    const processParams = (formData: API.EventTaskListRequest) => {
        const params = {
            ...formData,
            beginTime:
                (formData?.dates && formData?.dates[0]?.format('YYYY-MM-DD 00:00:00')) || undefined,
            endTime:
                (formData?.dates && formData?.dates[1]?.format('YYYY-MM-DD 23:59:59')) || undefined,
            dates: undefined,
            taskId: (formData?.taskId?.length && formData?.taskId?.trim()) || undefined,
            taskName: (formData?.taskName?.length && formData?.taskName?.trim()) || undefined,
        };

        return params;
    };

    const onFinish = (formData: API.EventTaskListRequest) => {
        searchList(
            { current: pagination?.current, pageSize: pagination?.pageSize },
            processParams(formData),
        );
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination?.pageSize });
    };

    // const { run: exportFunc } = useRequest(
    //     (params) => {
    //         return exportQrcodeList(params);
    //     },
    //     {
    //         manual: true,
    //         onSuccess: (res: API.CommonBooleanResponse) => {
    //             if (res?.ret === 200) {
    //                 message.success('导出成功，请稍后到文件暂存区查看');
    //                 cacheRef?.current?.count();
    //             } else {
    //                 message.error(res?.msg || '导出失败');
    //             }
    //         },
    //     },
    // );

    // const onExportForm = () => {
    //     exportFunc(processParams(form?.getFieldsValue(true)));
    // };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError labelCol={{ span: 6 }} {...props}>
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                // onExportForm={showStyle == '1' && onExportForm}
                // exportName="导出至暂存区"
            >
                <Col span={8}>
                    <Form.Item label="任务ID" name="taskId">
                        <Input
                            maxLength={50}
                            allowClear
                            autoComplete="off"
                            placeholder="请填写任务ID"
                        />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="任务名称" name="taskName">
                        <Input
                            maxLength={50}
                            allowClear
                            autoComplete="off"
                            placeholder="请填写任务名称"
                        />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="创建时间" name="dates">
                        <RangePicker format="YYYY-MM-DD" allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="开关" name="taskState">
                        <Select placeholder="不限" allowClear>
                            <Select.Option value={'01'}>启用</Select.Option>
                            <Select.Option value={'09'}>停用</Select.Option>
                        </Select>
                    </Form.Item>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const TaskListTable: React.FC<{
    history: any;
    showStyle: '1' | '2'; // 1,普通战士列表；2，弹窗展示列表
    tableProps?: any; // 表格的附加属性，如果需要的话可传，直接投到tablePro组件
}> = ({ history, showStyle, tableProps }) => {
    const { listData, listLoading, pagination } = useModel('taskManage.list');

    const formRef = useRef<any>();

    useEffect(() => {
        formRef.current?.resetForm();
    }, []);
    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (taskId, taskState) => {
            return saveTaskStateApi({ taskId, taskState });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    formRef.current?.resetForm();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );
    const confirmChangeStatus = (id: number, status: string) => {
        changeStatusRequest(id, status);
    };
    const columns: ColumnsType<any> = [
        {
            title: '任务ID',
            width: 100,
            dataIndex: 'taskId',
        },
        {
            title: '任务名称',
            width: 180,
            dataIndex: 'taskName',
        },
        {
            title: '任务说明',
            width: 200,
            dataIndex: 'taskRemarks',
            ellipsis: true,
            render: (text) => (
                <div
                    style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        width: '200px',
                    }}
                >
                    {text}
                </div>
            ),
        },
        {
            title: '任务规则数量',
            width: 140,
            dataIndex: 'ruleNum',
        },
        {
            title: '关联任务包',
            width: 130,
            dataIndex: 'packageNum',
            render(text: string | undefined, record: API.EventTaskListVo) {
                return Number(record?.packageNum) > 0 ? (
                    <a
                        onClick={() => {
                            history.push(`/marketing/event/task/list/detail?id=${record?.taskId}`, {
                                defaultKey: TASK_DETAIL_ACTIVE_KEY.RELATE_RULE_PACKAGE,
                            });
                        }}
                        title={text}
                    >
                        {text || '-'}
                    </a>
                ) : (
                    '-'
                );
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span>{moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
            },
        },
        {
            title: '开关',
            width: 180,
            dataIndex: 'taskState',
            render(text, record) {
                return (
                    <Switch
                        checked={text === '01'}
                        checkedChildren="开启"
                        unCheckedChildren="关闭"
                        loading={changeStatusLoading}
                        onChange={() => {
                            confirmChangeStatus(record?.taskId, text === '01' ? '09' : '01');
                        }}
                    />
                );
            },
        },
        {
            title: '创建人',
            width: 200,
            dataIndex: 'createdBy',
        },
    ];

    const showColumns = useMemo(() => {
        if (showStyle == '1') {
            return [
                ...columns,
                {
                    title: '操作',
                    width: 160,
                    fixed: 'right',
                    render(text: string | undefined, record: API.EventTaskListVo) {
                        return (
                            <Space>
                                <Link
                                    to={`/marketing/event/task/list/edit?id=${record?.taskId}`}
                                    title={text}
                                >
                                    编辑
                                </Link>
                                <Link
                                    to={`/marketing/event/task/list/detail?id=${record?.taskId}`}
                                    title={text}
                                >
                                    详情
                                </Link>
                            </Space>
                        );
                    },
                },
            ];
        }
        return columns;
    }, [showStyle]);

    return (
        <Fragment>
            <SearchLayout initRef={formRef} showStyle={showStyle} />
            <div className={styles['btn-bar']}>
                <Space>
                    <Link
                        to="/marketing/event/task/list/add"
                        target={(showStyle == '2' && '_blank') || undefined}
                    >
                        <Button type="primary">新建</Button>
                    </Link>
                </Space>
            </div>
            {/* <Tabs
                onChange={(v) => {
                    setActiveStatus(v);
                }}
                activeKey={activeStatus}
            >
                <Tabs.TabPane key="binded" tab="已绑定" />
                <Tabs.TabPane key="discarded" tab="已废弃" />
            </Tabs> */}
            <TablePro
                {...(tableProps || {})}
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="taskId"
                dataSource={listData?.list}
                columns={showColumns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Fragment>
    );
};

export default React.memo(TaskListTable);
