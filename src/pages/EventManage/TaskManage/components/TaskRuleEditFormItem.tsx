import CitysSelect from '@/components/CitysSelect';
import { getCodesApi } from '@/services/CommonApi';
import {
    getEventRuleDownFromRuleCodeApi,
    getTaskRuleDownApi,
} from '@/services/Marketing/TaskCenterApi';
import { CloseOutlined, DeleteOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Card, Col, Form, Input, Popconfirm, Row, Select, Tooltip, TreeSelect } from 'antd';
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import baseStyles from '@/assets/styles/base.less';
import { getAllActTypeApi } from '@/services/Marketing/EventManageApi';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';

interface EventInfoItem {
    eventCode?: string; //	事件编码
    ruleCalculateType?: string; //	规则计算方式
    ruleCode?: string; //	规则标识符
    ruleId?: number; //	主键;主键，自增
    ruleName?: string; //	规则名称
    ruleRemarks?: string; //	描述
    ruleState?: string; //	规则状态
    ruleStateName?: string; //	规则状
    ruleType?: string; //	规则状态
    ruleValueType?: string; //	值类型（平均，最大，最小）
    updatedBy?: string; //	更新人
    updatedTime?: string; //	更新时间
}
interface EventInfo {
    eventName?: string;
    eventCode?: string;
    ruleCalculateType?: string;
    ruleId: number;
    ruleName?: string;
    ruleState?: string;
    ruleType?: string;
    ruleValueType?: string;
    // 事件详情，异步返回
    eventInfoLoading?: boolean;
    eventInfoList?: EventInfoItem[];
}

const ActTypeSelect: React.FC<any> = (props) => {
    const { suffixIcon } = props;
    const { data: actType } = useRequest(async () => {
        const {
            data: { list },
        } = await getAllActTypeApi();
        return list;
    });

    const actTypeOptions = useMemo(() => {
        const loop = (
            list: {
                codeValue?: string;
                codeName?: string;
                children?: any;
                selectable: boolean;
            }[],
        ) => {
            return list?.map((ele) => {
                if (ele.children?.length) {
                    ele.selectable = false;
                    ele.children = loop(ele.children);
                } else {
                    ele.selectable = true;
                }
                return {
                    ...ele,
                    value: ele.codeValue,
                    title: ele.codeName,
                };
            });
        };
        return loop(actType) || [];
    }, [actType]);

    return (
        <Form.Item noStyle {...props}>
            <TreeSelect
                showSearch
                placeholder="请选择"
                // allowClear
                treeDefaultExpandAll
                treeData={actTypeOptions}
                treeCheckable
                suffixIcon={suffixIcon}
                showArrow
            />
        </Form.Item>
    );
};

const TaskRuleEditFormItem: React.FC<{ form: any; formItemLayout: any }> = ({
    form,
    formItemLayout,
}) => {
    const [eventList, updateEventList] = useState<EventInfo[]>([]);

    const ruleList = Form.useWatch('ruleList', form);
    const relateFlag = Form.useWatch('relateFlag', form);

    // 初始化数据时，拉取规则下拉列表用
    const initEventRules = async (_eventList: EventInfo[]) => {
        try {
            for (const list in ruleList || []) {
                if (Object.prototype.hasOwnProperty.call(ruleList, list)) {
                    const element = ruleList[list];
                    for (const item in element.ruleDetList) {
                        if (Object.prototype.hasOwnProperty.call(element.ruleDetList, item)) {
                            const ruleElement = element.ruleDetList[item];
                            const targetObj = _eventList.find(
                                (ele: any) => ele?.eventCode == ruleElement.eventCode,
                            );
                            if (targetObj && !targetObj.eventInfoList) {
                                const { data } = await getTaskRuleDownApi(ruleElement.eventCode);
                                targetObj.eventInfoList = data;
                            }
                        }
                    }
                }
            }
            updateEventList([..._eventList]);
        } catch (error) {}
    };

    const [codes, updateCodes] = useState<{
        ruleValueType?: { codeName: string; codeValue: string }[];
        ruleCalculateType?: { codeName: string; codeValue: string }[];
        guideErrorCode?: { codeName: string; codeValue: string }[];
        stationServBusiStatus?: { codeName: string; codeValue: string }[];
    }>({});
    const initCode = async (code: string) => {
        try {
            const { data } = await getCodesApi(code);
            codes[code] = data?.list;
            updateCodes({ ...codes });
        } catch (error) {}
    };

    const { run: loadEventList, loading: loadEventListLoading } = useRequest(
        () => {
            return getEventRuleDownFromRuleCodeApi();
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    initEventRules(res.data || []);
                }
            },
        },
    );

    const { run: loadEventInfo, loading: loadEventInfoLoading } = useRequest(
        (params) => {
            return getTaskRuleDownApi(params.eventCode);
        },
        {
            manual: true,
            onSuccess: (res, params) => {
                if (res?.ret === 200) {
                    const ruleList = res.data;
                    const obj = eventList?.find(
                        (ele) => ele.eventCode == params?.[0]?.eventCode,
                    ) || {
                        eventInfoList: undefined,
                        eventInfoLoading: false,
                    };
                    obj.eventInfoLoading = false;
                    obj.eventInfoList = ruleList;
                    updateEventList([...(eventList || [])]);
                }
            },
        },
    );

    useEffect(() => {
        if (!eventList?.length) {
            loadEventList();
        }
        initCode('ruleValueType');
        initCode('ruleCalculateType');
        initCode('guideErrorCode');
        initCode('stationServBusiStatus');
    }, []);

    const ruleCodeFormItem = (key: any, info?: EventInfoItem) => {
        const ruleCode = info?.ruleCode;
        const eventCode = info?.eventCode;
        console.log(info);
        const tip =
            (info?.ruleRemarks?.length && (
                <Tooltip title={info?.ruleRemarks}>
                    <InfoCircleOutlined style={{ fontSize: '14px', color: 'black' }} />
                </Tooltip>
            )) ||
            undefined;
        // 当选择扫码结果或者下单异常，异常码最后使用选择框
        if (
            ruleCode == 'errorCode' &&
            (eventCode === 'QrCodeScanResult' || eventCode === 'CreateOrderResult')
        ) {
            return (
                <Form.Item
                    name={key}
                    rules={[
                        {
                            required: true,
                            message: '请配置',
                        },
                    ]}
                >
                    <Select
                        allowClear
                        mode="multiple"
                        placeholder="请选择"
                        options={codes?.guideErrorCode}
                        fieldNames={{ label: 'codeName', value: 'codeValue' }}
                    />
                </Form.Item>
            );
        }
        // 运营状态
        if (ruleCode == 'busiStatus') {
            return (
                <Form.Item
                    name={key}
                    rules={[
                        {
                            required: true,
                            message: '请配置',
                        },
                    ]}
                >
                    <Select
                        allowClear
                        placeholder="请选择"
                        options={codes?.stationServBusiStatus}
                        fieldNames={{ label: 'codeName', value: 'codeValue' }}
                    />
                </Form.Item>
            );
        }
        if (ruleCode == 'city') {
            return (
                <CitysSelect
                    label={''}
                    formItemLayout={{ colon: false }}
                    name={key}
                    rules={[
                        () => ({
                            validator(rule: any, value: any) {
                                if (!value?.length) {
                                    return Promise.reject('请配置');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                    showArrow
                    provinceSelectable
                    disabled={relateFlag}
                    suffixIcon={tip}
                    allowClear={false}
                />
            );
        } else if (ruleCode == 'actType') {
            return (
                <ActTypeSelect
                    name={key}
                    rules={[
                        () => ({
                            validator(rule: any, value: any) {
                                if (!value?.length) {
                                    return Promise.reject('请配置');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                    suffixIcon={tip}
                />
            );
        } else if (ruleCode == 'buildId') {
            return (
                <OperSelectTypeItem
                    name={key}
                    // rules={[{ message: '请选择运营商', required: true }]}
                    mode="multiple"
                    form={form}
                    // initialValue={requestInfo?.recordParams?.operId || undefined}
                    // disabled={requestInfo?.recordParams?.operId?.length > 0}
                    // disabledFilter={requestInfo?.recordParams?.operId?.length > 0}
                />
            );
        } else if (ruleCode == 'stationId') {
            return <AllStationSelect form={form} mode="multiple" label="活动场站" name={key} />;
        }
        return (
            <Form.Item
                name={key}
                rules={[
                    {
                        required: true,
                        message: '请配置',
                    },
                ]}
            >
                <Input placeholder="请输入" autoComplete="off" disabled={relateFlag} suffix={tip} />
            </Form.Item>
        );
    };

    return (
        <Form.Item noStyle shouldUpdate>
            {({ getFieldValue, setFieldsValue }) => {
                const ruleList = getFieldValue('ruleList');
                return (
                    (ruleList?.length && (
                        <Form.Item name={'ruleList'}>
                            <Form.List name="ruleList">
                                {(ruleFields, { add, remove: ruleRemove }) =>
                                    ruleFields.map((ruleField, ruleIndex) => (
                                        <Card
                                            key={ruleIndex}
                                            bordered
                                            style={{ marginBottom: '12px' }}
                                            bodyStyle={{ padding: '24px 0' }}
                                            className={baseStyles.antFormItemMb}
                                        >
                                            <Form.Item name={[ruleIndex, 'relationType']} noStyle />
                                            <Form.Item name={[ruleIndex, 'continueFlag']} noStyle />
                                            <Form.Item
                                                name={[ruleIndex, 'ruleDetList']}
                                                label={
                                                    <span style={{ fontWeight: 'bold' }}>
                                                        {`规则${ruleIndex + 1}`}
                                                    </span>
                                                }
                                                required
                                                {...formItemLayout}
                                            >
                                                <Form.List name={[ruleIndex, 'ruleDetList']}>
                                                    {(fields, { add, remove }) =>
                                                        fields.map((field, detIndex) => {
                                                            const eventCode = getFieldValue([
                                                                'ruleList',
                                                                ruleIndex,
                                                                'ruleDetList',
                                                                detIndex,
                                                                'eventCode',
                                                            ]);
                                                            const otherEventCodes: string[] = [];
                                                            ruleList.map(
                                                                (ele: any, tempIndex: number) => {
                                                                    if (tempIndex != ruleIndex) {
                                                                        const otherCode =
                                                                            ele.ruleDetList?.[0]
                                                                                ?.eventCode;
                                                                        if (otherCode) {
                                                                            otherEventCodes.push(
                                                                                otherCode,
                                                                            );
                                                                        }
                                                                    }
                                                                },
                                                            );
                                                            const ruleCode = getFieldValue([
                                                                'ruleList',
                                                                ruleIndex,
                                                                'ruleDetList',
                                                                detIndex,
                                                                'ruleCode',
                                                            ]);
                                                            const eventInfoList = eventList?.find(
                                                                (ele) => ele.eventCode == eventCode,
                                                            )?.eventInfoList;
                                                            const eventRuleInfo =
                                                                eventInfoList?.find(
                                                                    (ele) =>
                                                                        ele.ruleCode == ruleCode,
                                                                );
                                                            const ruleCalculateType =
                                                                eventRuleInfo?.ruleCalculateType;
                                                            const ruleCalculateTypeOptions =
                                                                ruleCalculateType?.split(',')?.map(
                                                                    (ele) =>
                                                                        codes.ruleCalculateType?.find(
                                                                            ({ codeValue }) =>
                                                                                codeValue == ele,
                                                                        ) || {
                                                                            codeValue: ele,
                                                                            codeName: ele,
                                                                        },
                                                                ) || [];
                                                            const ruleValueType =
                                                                eventRuleInfo?.ruleValueType;
                                                            const ruleValueTypeOptions =
                                                                ruleValueType?.split(',')?.map(
                                                                    (ele) =>
                                                                        codes.ruleValueType?.find(
                                                                            ({ codeValue }) =>
                                                                                codeValue == ele,
                                                                        ) || {
                                                                            codeValue: ele,
                                                                            codeName: ele,
                                                                        },
                                                                ) || [];
                                                            return (
                                                                <Row
                                                                    key={field.name}
                                                                    gutter={12}
                                                                    style={{ marginBottom: '6px' }}
                                                                >
                                                                    <Col span={6}>
                                                                        {/* 事件源编码 */}
                                                                        <Form.Item
                                                                            name={[
                                                                                detIndex,
                                                                                'eventCode',
                                                                            ]}
                                                                            rules={[
                                                                                {
                                                                                    required: true,
                                                                                    message:
                                                                                        '请配置',
                                                                                },
                                                                            ]}
                                                                        >
                                                                            {(detIndex == 0 && (
                                                                                <Select
                                                                                    disabled={
                                                                                        relateFlag
                                                                                    }
                                                                                    allowClear
                                                                                    placeholder="请选择"
                                                                                    loading={
                                                                                        loadEventListLoading
                                                                                    }
                                                                                    onChange={(
                                                                                        e,
                                                                                    ) => {
                                                                                        const obj =
                                                                                            eventList?.find(
                                                                                                (
                                                                                                    ele,
                                                                                                ) =>
                                                                                                    ele.eventCode ==
                                                                                                    e,
                                                                                            );
                                                                                        if (
                                                                                            !obj?.eventInfoList
                                                                                        ) {
                                                                                            loadEventInfo(
                                                                                                {
                                                                                                    eventCode:
                                                                                                        e,
                                                                                                },
                                                                                            );
                                                                                        }
                                                                                        // 清空后面所选项
                                                                                        const ruleList =
                                                                                            getFieldValue(
                                                                                                'ruleList',
                                                                                            );
                                                                                        ruleList[
                                                                                            ruleIndex
                                                                                        ].ruleDetList[
                                                                                            detIndex
                                                                                        ].ruleCode =
                                                                                            undefined;
                                                                                        if (
                                                                                            detIndex ==
                                                                                            0
                                                                                        ) {
                                                                                            // 删掉事件规则，当前card的所有过滤条件同时清空
                                                                                            const ruleDetList =
                                                                                                ruleList[
                                                                                                    ruleIndex
                                                                                                ]
                                                                                                    .ruleDetList;
                                                                                            ruleDetList.splice(
                                                                                                1,
                                                                                                ruleDetList.length -
                                                                                                    1,
                                                                                            );
                                                                                        }
                                                                                        setFieldsValue(
                                                                                            {
                                                                                                ruleList,
                                                                                            },
                                                                                        );
                                                                                    }}
                                                                                >
                                                                                    {eventList?.map(
                                                                                        (ele) => {
                                                                                            return (
                                                                                                <Select.Option
                                                                                                    value={
                                                                                                        ele.eventCode
                                                                                                    }
                                                                                                    key={
                                                                                                        ele.eventCode
                                                                                                    }
                                                                                                    disabled={otherEventCodes.some(
                                                                                                        (
                                                                                                            otherCode,
                                                                                                        ) =>
                                                                                                            otherCode ==
                                                                                                            ele.eventCode,
                                                                                                    )}
                                                                                                >
                                                                                                    {`${ele.eventName}(${ele?.eventCode})`}
                                                                                                </Select.Option>
                                                                                            );
                                                                                        },
                                                                                    )}
                                                                                </Select>
                                                                            )) ||
                                                                                `过滤条件${detIndex}`}
                                                                        </Form.Item>
                                                                    </Col>
                                                                    {(eventCode?.length && (
                                                                        <Col span={4}>
                                                                            {/* 规则名称 */}
                                                                            <Form.Item
                                                                                name={[
                                                                                    detIndex,
                                                                                    'ruleCode',
                                                                                ]}
                                                                                rules={[
                                                                                    {
                                                                                        required:
                                                                                            true,
                                                                                        message:
                                                                                            '请配置',
                                                                                    },
                                                                                ]}
                                                                            >
                                                                                <Select
                                                                                    disabled={
                                                                                        relateFlag
                                                                                    }
                                                                                    allowClear
                                                                                    placeholder="请选择"
                                                                                    loading={
                                                                                        loadEventInfoLoading
                                                                                    }
                                                                                    onChange={(
                                                                                        e,
                                                                                    ) => {
                                                                                        // 清空后面所选项
                                                                                        const ruleList =
                                                                                            getFieldValue(
                                                                                                'ruleList',
                                                                                            );
                                                                                        const currentItem =
                                                                                            ruleList[
                                                                                                ruleIndex
                                                                                            ]
                                                                                                .ruleDetList[
                                                                                                detIndex
                                                                                            ];

                                                                                        const eventRuleInfo =
                                                                                            eventInfoList?.find(
                                                                                                (
                                                                                                    ele,
                                                                                                ) =>
                                                                                                    ele.ruleCode ==
                                                                                                    e,
                                                                                            ) || {};
                                                                                        const keys =
                                                                                            Object.keys(
                                                                                                eventRuleInfo,
                                                                                            );
                                                                                        keys.forEach(
                                                                                            (ele) =>
                                                                                                (currentItem[
                                                                                                    ele
                                                                                                ] =
                                                                                                    eventRuleInfo[
                                                                                                        ele
                                                                                                    ]),
                                                                                        );
                                                                                        currentItem.ruleCalculateType =
                                                                                            undefined;
                                                                                        currentItem.ruleValueType =
                                                                                            undefined;
                                                                                        currentItem.ruleValue =
                                                                                            undefined;

                                                                                        setFieldsValue(
                                                                                            {
                                                                                                ruleList,
                                                                                            },
                                                                                        );
                                                                                    }}
                                                                                >
                                                                                    {eventInfoList?.map(
                                                                                        (ele) => {
                                                                                            return (
                                                                                                <Select.Option
                                                                                                    value={
                                                                                                        ele.ruleCode
                                                                                                    }
                                                                                                    key={
                                                                                                        ele.ruleCode
                                                                                                    }
                                                                                                >
                                                                                                    {
                                                                                                        ele.ruleName
                                                                                                    }
                                                                                                </Select.Option>
                                                                                            );
                                                                                        },
                                                                                    )}
                                                                                </Select>
                                                                            </Form.Item>
                                                                        </Col>
                                                                    )) ||
                                                                        null}
                                                                    {(eventRuleInfo && (
                                                                        <Fragment>
                                                                            <Col span={3}>
                                                                                <Form.Item
                                                                                    name={[
                                                                                        detIndex,
                                                                                        'ruleValueType',
                                                                                    ]}
                                                                                    rules={[
                                                                                        {
                                                                                            required:
                                                                                                true,
                                                                                            message:
                                                                                                '请配置',
                                                                                        },
                                                                                    ]}
                                                                                >
                                                                                    <Select
                                                                                        disabled={
                                                                                            relateFlag
                                                                                        }
                                                                                        allowClear={
                                                                                            ruleValueTypeOptions?.length >
                                                                                            1
                                                                                        }
                                                                                        placeholder="请选择"
                                                                                    >
                                                                                        {ruleValueTypeOptions?.map(
                                                                                            (
                                                                                                ele,
                                                                                            ) => {
                                                                                                return (
                                                                                                    (ele && (
                                                                                                        <Select.Option
                                                                                                            value={
                                                                                                                ele?.codeValue
                                                                                                            }
                                                                                                            key={
                                                                                                                ele?.codeValue
                                                                                                            }
                                                                                                        >
                                                                                                            {
                                                                                                                ele?.codeName
                                                                                                            }
                                                                                                        </Select.Option>
                                                                                                    )) ||
                                                                                                    null
                                                                                                );
                                                                                            },
                                                                                        )}
                                                                                    </Select>
                                                                                </Form.Item>
                                                                            </Col>
                                                                            <Col span={3}>
                                                                                {/* 计算方式（加，减，乘，除，大于，小于等） */}
                                                                                <Form.Item
                                                                                    name={[
                                                                                        detIndex,
                                                                                        'ruleCalculateType',
                                                                                    ]}
                                                                                    rules={[
                                                                                        {
                                                                                            required:
                                                                                                true,
                                                                                            message:
                                                                                                '请配置',
                                                                                        },
                                                                                    ]}
                                                                                >
                                                                                    <Select
                                                                                        disabled={
                                                                                            relateFlag
                                                                                        }
                                                                                        allowClear={
                                                                                            ruleCalculateTypeOptions?.length >
                                                                                            1
                                                                                        }
                                                                                        placeholder="请选择"
                                                                                    >
                                                                                        {ruleCalculateTypeOptions?.map(
                                                                                            (
                                                                                                ele,
                                                                                            ) => {
                                                                                                return (
                                                                                                    (ele && (
                                                                                                        <Select.Option
                                                                                                            value={
                                                                                                                ele?.codeValue
                                                                                                            }
                                                                                                            key={
                                                                                                                ele?.codeValue
                                                                                                            }
                                                                                                        >
                                                                                                            {
                                                                                                                ele?.codeName
                                                                                                            }
                                                                                                        </Select.Option>
                                                                                                    )) ||
                                                                                                    null
                                                                                                );
                                                                                            },
                                                                                        )}
                                                                                    </Select>
                                                                                </Form.Item>
                                                                            </Col>
                                                                            <Col span={6}>
                                                                                {/* 规则值 */}
                                                                                {ruleCodeFormItem(
                                                                                    [
                                                                                        detIndex,
                                                                                        'ruleValue',
                                                                                    ],
                                                                                    eventRuleInfo,
                                                                                )}
                                                                            </Col>
                                                                            <Form.Item
                                                                                name={[
                                                                                    detIndex,
                                                                                    'ruleId',
                                                                                ]}
                                                                                noStyle
                                                                            />
                                                                            <Form.Item
                                                                                name={[
                                                                                    detIndex,
                                                                                    'ruleName',
                                                                                ]}
                                                                                noStyle
                                                                            />
                                                                            <Form.Item
                                                                                name={[
                                                                                    detIndex,
                                                                                    'sn',
                                                                                ]}
                                                                                noStyle
                                                                            />
                                                                        </Fragment>
                                                                    )) ||
                                                                        null}
                                                                    {(!relateFlag && (
                                                                        <Col span={1}>
                                                                            <Popconfirm
                                                                                title={`确定删除当前${
                                                                                    detIndex == 0
                                                                                        ? '规则'
                                                                                        : '过滤条件'
                                                                                }？`}
                                                                                onConfirm={() => {
                                                                                    if (
                                                                                        detIndex ==
                                                                                        0
                                                                                    ) {
                                                                                        // 删除第一条，是删除规则
                                                                                        ruleRemove(
                                                                                            ruleIndex,
                                                                                        );
                                                                                    } else {
                                                                                        // 删除后面的，是删除过滤条件
                                                                                        remove(
                                                                                            detIndex,
                                                                                        );
                                                                                    }
                                                                                }}
                                                                            >
                                                                                {detIndex == 0 ? (
                                                                                    <DeleteOutlined
                                                                                        style={{
                                                                                            lineHeight:
                                                                                                '34px',
                                                                                        }}
                                                                                    />
                                                                                ) : (
                                                                                    <CloseOutlined
                                                                                        style={{
                                                                                            lineHeight:
                                                                                                '34px',
                                                                                        }}
                                                                                    />
                                                                                )}
                                                                            </Popconfirm>
                                                                        </Col>
                                                                    )) ||
                                                                        null}
                                                                </Row>
                                                            );
                                                        })
                                                    }
                                                </Form.List>
                                            </Form.Item>
                                            {(ruleList[ruleIndex]?.ruleDetList?.[0]?.eventCode &&
                                                !relateFlag && (
                                                    <Form.Item
                                                        label=" "
                                                        colon={false}
                                                        {...formItemLayout}
                                                    >
                                                        <a
                                                            onClick={() => {
                                                                const eventCode = getFieldValue([
                                                                    'ruleList',
                                                                    ruleIndex,
                                                                    'ruleDetList',
                                                                ])?.[0]?.eventCode;
                                                                ruleList[
                                                                    ruleIndex
                                                                ].ruleDetList.push({
                                                                    eventCode,
                                                                });
                                                                setFieldsValue({ ruleList });
                                                            }}
                                                        >
                                                            新增过滤条件
                                                        </a>
                                                    </Form.Item>
                                                )) ||
                                                null}
                                        </Card>
                                    ))
                                }
                            </Form.List>
                        </Form.Item>
                    )) || (
                        <Form.Item
                            name="failure_flag"
                            label=" "
                            colon={false}
                            rules={[
                                () => ({
                                    validator(rule: any, value: any) {
                                        return Promise.reject('请添加任务规则');
                                    },
                                }),
                            ]}
                        />
                    )
                );
            }}
        </Form.Item>
    );
};

export default React.memo(TaskRuleEditFormItem);
