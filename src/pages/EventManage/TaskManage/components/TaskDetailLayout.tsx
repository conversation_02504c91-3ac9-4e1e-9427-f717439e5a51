import { Card, Descriptions, Tabs, Tree } from 'antd';
import React, { useEffect, useState } from 'react';
import TablePro from '@/components/TablePro';
import { useRequest } from 'ahooks';
import { getTaskInfoApi } from '@/services/Marketing/TaskCenterApi';
import TaskPackageListTable from '../../TaskPackageManage/components/TaskPackageListTable';
import { CaretDownOutlined, DownOutlined } from '@ant-design/icons';
import { copyObjectCommon } from '@/utils/utils';

export const TASK_DETAIL_STYLE = {
    ALL: '0', // 显示全部
    ONLY_RULE: '1', // 只显示任务规则 - 任务包详情查看用
};

export const TASK_DETAIL_ACTIVE_KEY = {
    RULE: '01', //  任务规则
    RELATE_RULE_PACKAGE: '02', // 关联任务包
};

const TaskDetailLayout: React.FC<{
    showStyle: string;
    defaultKey?: string;
    id: string;
}> = ({ showStyle = TASK_DETAIL_STYLE.ALL, defaultKey = TASK_DETAIL_ACTIVE_KEY.RULE, id }) => {
    const [activeTabKey, setActiveTabKey] = useState<string>(defaultKey);
    const [info, updateInfo] = useState<any>();
    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    const { run, loading } = useRequest(
        (params) => {
            return getTaskInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    const tempInfo: any = res?.data;
                    const ruleList = tempInfo?.ruleList || [];
                    const _ruleList: any = []; // 构造一个新的ruleList，归类所有事件
                    ruleList.forEach((element: any) => {
                        const ruleDetList = element.ruleDetList;
                        ruleDetList.forEach((_element: any) => {
                            const target: any = _ruleList.find((ele: any) =>
                                ele.ruleDetList?.some(
                                    (detEle: any) => detEle.eventCode == _element.eventCode,
                                ),
                            );
                            if (!target) {
                                _ruleList.push({ ...element, ruleDetList: [_element] });
                            } else {
                                target.ruleDetList.push(_element);
                            }
                        });
                    });
                    tempInfo.ruleList = _ruleList;
                    updateInfo(tempInfo);
                }
            },
        },
    );

    return (
        <Card loading={loading}>
            <Descriptions column={1} labelStyle={{ fontWeight: 'bold' }}>
                <Descriptions.Item label="任务">{info?.taskName || '-'}</Descriptions.Item>
                <Descriptions.Item label="开关">
                    <span
                        style={info?.taskState == '01' ? { color: 'limegreen' } : { color: 'gray' }}
                    >
                        {info?.taskStateName || '-'}
                    </span>
                </Descriptions.Item>
            </Descriptions>

            <Tabs
                activeKey={activeTabKey}
                onChange={(key) => {
                    setActiveTabKey(key);
                }}
            >
                <Tabs.TabPane key={TASK_DETAIL_ACTIVE_KEY.RULE} tab="任务规则">
                    <TablePro
                        scroll={{ x: 'max-content' }}
                        rowKey="id"
                        dataSource={info?.ruleList}
                        columns={[
                            {
                                title: '序号',
                                width: 80,
                                render(text: string | undefined, record: any, index: number) {
                                    return <span>{`${index + 1}`}</span>;
                                },
                            },
                            {
                                title: '条件',
                                width: 280,
                                dataIndex: 'ruleDesc',
                                render(text: string | undefined, record: any) {
                                    const ruleDetList: any = copyObjectCommon(record.ruleDetList);
                                    const filterList =
                                        (ruleDetList?.length > 1 &&
                                            ruleDetList.splice(1, ruleDetList.length - 1)) ||
                                        [];
                                    return (
                                        (filterList?.length && (
                                            <Tree
                                                showIcon={false}
                                                showLine={{
                                                    showLeafIcon: false,
                                                }}
                                                // switcherIcon={<CaretDownOutlined />}
                                                treeData={[
                                                    {
                                                        title: (
                                                            <span>
                                                                {ruleDetList?.[0]?.ruleDesc}
                                                            </span>
                                                        ),
                                                        key: 'fixed_0',
                                                        children: filterList?.map(
                                                            (ele: any, index: number) => {
                                                                return {
                                                                    title: (
                                                                        <div>{ele?.ruleDesc}</div>
                                                                    ),
                                                                    key: ele.ruleId,
                                                                };
                                                            },
                                                        ),
                                                    },
                                                ]}
                                                selectable={false}
                                                defaultExpandAll
                                            />
                                        )) || <span>{ruleDetList?.[0]?.ruleDesc}</span>
                                    );
                                },
                            },
                        ]}
                        pagination={false}
                    />
                </Tabs.TabPane>
                {(showStyle == TASK_DETAIL_STYLE.ALL && (
                    <Tabs.TabPane key={TASK_DETAIL_ACTIVE_KEY.RELATE_RULE_PACKAGE} tab="关联任务包">
                        <TaskPackageListTable showStyle="2" otherParams={{ taskId: id }} />
                    </Tabs.TabPane>
                )) ||
                    null}
            </Tabs>
        </Card>
    );
};

export default React.memo(TaskDetailLayout);
