import { LeftOutlined } from '@ant-design/icons';
import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { <PERSON>ert, Button, Card, Form, Input, Radio, Row, Space, message, Checkbox } from 'antd';
import React, { useEffect } from 'react';
import { history, useLocation } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import { useRequest } from 'ahooks';
import { getTaskInfoApi, saveTaskInfoApi } from '@/services/Marketing/TaskCenterApi';
import TaskRuleEditFormItem from './components/TaskRuleEditFormItem';

const formItemLayout: any = {
    labelCol: { flex: '0 0 120px' },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const TaskManageEdit: React.FC<{
    route: { name: string };
}> = ({ route }) => {
    const [form] = Form.useForm();
    const location: any = useLocation();
    const {
        query: { id: taskId },
    }: { query: { id: string } } = location;

    useEffect(() => {
        if (taskId) {
            run(taskId);
        }
    }, []);

    const { run, loading } = useRequest(
        (params) => {
            return getTaskInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    const taskInfo: API.EventTaskListVo = res?.data || {};
                    const params: API.EventTaskListVo = { ...taskInfo };
                    const ruleList = params.ruleList || [];
                    const _ruleList: any = []; // 构造一个新的ruleList，归类所有事件
                    ruleList.forEach((element: any) => {
                        const ruleDetList = element.ruleDetList;
                        ruleDetList.forEach((_element: any) => {
                            const target: any = _ruleList.find((ele: any) =>
                                ele.ruleDetList?.some(
                                    (detEle: any) => detEle.eventCode == _element.eventCode,
                                ),
                            );
                            if (!target) {
                                _ruleList.push({ ...element, ruleDetList: [_element] });
                            } else {
                                target.ruleDetList.push(_element);
                            }
                        });
                    });
                    _ruleList.forEach((element: any) => {
                        for (const ruleElement of element?.ruleDetList || []) {
                            if (
                                (ruleElement.ruleCode == 'city' ||
                                    ruleElement.ruleCode == 'actType' ||
                                    // 当选择扫码结果或者下单异常，异常码最后使用选择框数据转换
                                    (ruleElement.ruleCode == 'errorCode' &&
                                        (ruleElement.eventCode === 'QrCodeScanResult' ||
                                            ruleElement.eventCode === 'CreateOrderResult'))) &&
                                typeof ruleElement.ruleValue == 'string'
                            ) {
                                ruleElement.ruleValue = ruleElement.ruleValue.split(',');
                            }
                        }
                    });
                    params.ruleList = _ruleList;
                    params.relateFlag = Number(params?.packageNum) > 0;
                    params.externalDimensionFlag =
                        params.externalDimensionFlag === '1' ? true : false;
                    params.noticeBusinessFlag = params.noticeBusinessFlag === '1' ? true : false;
                    form.setFieldsValue(params);
                }
            },
        },
    );
    // 已被组件关联的任务包除了开关、有效期其他字段不允许编辑
    const relateFlag = Form.useWatch('relateFlag', form);
    const { run: save, loading: saveLoading } = useRequest(
        (params) => {
            return saveTaskInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('保存成功');
                    goBack();
                } else {
                    message.success(res?.msg || '保存失败');
                }
            },
        },
    );

    const submitEvent = (values: any) => {
        const params = { ...values };
        params.externalDimensionFlag = params.externalDimensionFlag ? '1' : '0';
        params.noticeBusinessFlag = params.noticeBusinessFlag ? '1' : '0';
        const ruleList = params.ruleList || [];
        const _ruleList: any = []; // 构造一个新的ruleList，合并所有事件
        ruleList.forEach((element: any) => {
            if (!_ruleList.length) {
                _ruleList.push(element);
            } else {
                _ruleList[0].ruleDetList?.push(...element.ruleDetList);
            }
        });
        _ruleList.forEach((element: any) => {
            let index = 0;
            for (const ruleElement of element?.ruleDetList || []) {
                ruleElement.sn = ++index;
                if (Array.isArray(ruleElement.ruleValue)) {
                    ruleElement.ruleValue = ruleElement.ruleValue.join(',');
                }
            }
        });
        params.ruleList = _ruleList;
        delete params.activeCrowd_rule;
        save(params);
    };
    const goBack = () => {
        if (history?.length > 1) {
            // 临时处理是否有上一级页面的交互
            history.go(-1);
        } else {
            history.replace('/marketing/event/task/list');
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={loading}>
                <Form
                    form={form}
                    layout="horizontal"
                    onFinish={submitEvent}
                    {...formItemLayout}
                    initialValues={'01'}
                >
                    <Form.Item name={'relateFlag'} noStyle />
                    <Form.Item name={'relationType'} noStyle />
                    <div className={commonStyles['form-title']}>基础信息</div>
                    {(taskId && (
                        <Form.Item label="任务ID" name={'taskId'} {...formItemFixedWidthLayout}>
                            {taskId}
                        </Form.Item>
                    )) ||
                        null}
                    <Form.Item
                        label="任务名称"
                        name={'taskName'}
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, message: '请输入' }]}
                    >
                        <Input
                            disabled={relateFlag}
                            placeholder="如：分享五一活动，限10字，不可重复"
                            maxLength={20}
                            showCount
                            autoComplete="off"
                        />
                    </Form.Item>
                    <Form.Item
                        label="任务说明"
                        name={'taskRemarks'}
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, message: '请输入任务说明' }]}
                    >
                        <Input.TextArea
                            disabled={relateFlag}
                            placeholder="请输入任务说明"
                            autoComplete="off"
                        />
                    </Form.Item>
                    <Form.Item name={'taskState'} label="开关" initialValue={'09'} hidden>
                        <Radio.Group>
                            <Radio value="01">启用</Radio>
                            <Radio value="09">停用</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Alert
                        style={{ marginBottom: 20 }}
                        description={
                            <>
                                <div>规则之间为「且」关系，同时满足则视为任务条件完成;</div>
                                <div>
                                    规则内先计算「过滤条件」规则，再计算「规则条件」，多个「过滤条件」为「且」关系;
                                </div>
                            </>
                        }
                        type="warning"
                    />

                    <Space align="start" size={'large'}>
                        <div className={commonStyles['form-title']}>任务规则</div>
                        {(!relateFlag && (
                            <Button
                                onClick={() => {
                                    const ruleList = form.getFieldValue('ruleList') || [];
                                    ruleList.push({
                                        ruleDetList: [{}],
                                        relationType: '01',
                                        continueFlag: '0',
                                    });
                                    form.setFieldsValue({
                                        ruleList,
                                    });
                                }}
                            >
                                添加事件规则
                            </Button>
                        )) ||
                            null}
                    </Space>

                    <TaskRuleEditFormItem form={form} formItemLayout={formItemLayout} />

                    <Form.Item noStyle>
                        <Form.Item noStyle name="externalDimensionFlag" valuePropName="checked">
                            <Checkbox value="1">扩展规则</Checkbox>
                        </Form.Item>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.externalDimensionFlag !== curValues.externalDimensionFlag
                            }
                        >
                            {({ getFieldValue }) => {
                                const externalDimensionFlag =
                                    getFieldValue('externalDimensionFlag');
                                return (
                                    externalDimensionFlag && (
                                        <Form.Item noStyle>
                                            <Radio.Group disabled>
                                                <Radio defaultChecked>通过API传入条件</Radio>
                                            </Radio.Group>
                                        </Form.Item>
                                    )
                                );
                            }}
                        </Form.Item>
                    </Form.Item>
                    <Form.Item name="noticeBusinessFlag" valuePropName="checked">
                        <Checkbox value="1">通知业务方</Checkbox>
                    </Form.Item>
                    <Form.Item>
                        <Row justify="center" style={{ marginTop: '32px' }}>
                            <Space size="large">
                                <Button type="primary" htmlType="submit" loading={saveLoading}>
                                    提交
                                </Button>
                                <Button onClick={goBack}>返回</Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};
export default React.memo(TaskManageEdit);
