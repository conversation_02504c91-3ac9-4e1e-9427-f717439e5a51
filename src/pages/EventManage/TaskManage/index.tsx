import { Card } from 'antd';
import React from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import TaskListTable from './components/TaskListTable';

const TaskManageList: React.FC<{
    history: any;
}> = (props) => {
    return (
        <PageHeaderWrapper>
            <Card>
                <TaskListTable {...props} showStyle="1" />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(TaskManageList);
