import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import React from 'react';
import { history, useLocation } from 'umi';
import TaskDetailLayout, { TASK_DETAIL_STYLE } from './components/TaskDetailLayout';

const TaskManageDetailPage: React.FC<{
    route: { name: string };
    history: any;
}> = (props) => {
    const { route } = props;
    const goBack = () => {
        if (history?.length > 1) {
            // 临时处理是否有上一级页面的交互
            history.go(-1);
        } else {
            history.replace('/marketing/event/task/list');
        }
    };
    const location: any = useLocation();
    const {
        state,
        query: { id: taskId },
    }: { state: { defaultKey?: string }; query: { id: string } } = location;

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <TaskDetailLayout
                showStyle={TASK_DETAIL_STYLE.ALL}
                defaultKey={state?.defaultKey}
                id={taskId}
            />
        </PageHeaderWrapper>
    );
};
export default React.memo(TaskManageDetailPage);
