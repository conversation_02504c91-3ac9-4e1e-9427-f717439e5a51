import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import {
    Button,
    Card,
    DatePicker,
    Form,
    Input,
    InputNumber,
    Modal,
    Radio,
    Row,
    Select,
    Space,
    Typography,
    message,
} from 'antd';
import moment from 'moment';
import React, { Fragment, useEffect, useMemo } from 'react';
import { useLocation } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import TaskListForm from './components/TaskListForm';
import EventListForm from './components/EventListForm';
import PriceCrowdItem from '@/pages/MarketingManage/Membership/components/PriceCrowdItem';
import { validatorCrowdItem } from '@/pages/MarketingManage/Membership/components/ChildStrategyItem';
import { isEmpty } from '@/utils/utils';
import { TASK_PRIZE_TYPE } from './declare';
import { getTaskPkgInfoApi, saveTaskPkgInfoApi } from '@/services/Marketing/TaskPackageCenterApi';
import EnterpriseSelectItem from '@/components/EnterpriseSelectItem';

const { RangePicker } = DatePicker;
const formItemLayout: any = {
    labelCol: { flex: '0 0 120px' },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const TaskManageEdit: React.FC<{
    route: { name: string };
    history: any;
}> = (props) => {
    const { route, history } = props;
    const { query = {}, pathname } = history.location;

    const {} = query as any;

    const [form] = Form.useForm();

    const location: any = useLocation();
    const relateFlag = Form.useWatch('relateFlag', form);
    const belongType = Form.useWatch('belongType', form);
    const belongSubType = Form.useWatch('belongSubType', form);
    const {
        query: { id: pkgId },
    }: { query: { id: string } } = location;

    // 充电有奖
    const isChargePrize = useMemo(() => {
        return pathname?.indexOf('/charge-prize') > -1;
    }, [pathname]);

    useEffect(() => {
        if (pkgId) {
            run(pkgId);
        }
    }, []);

    const { run, loading } = useRequest(
        (params) => {
            return getTaskPkgInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    const taskInfo = res?.data || {};
                    const params = { ...taskInfo };
                    if (params.activeCrowdFlag == '0') {
                        const keys = ['labelList', 'cdpList', 'custLabelList'];
                        const activeCrowd_rule = {};
                        for (const key of keys) {
                            activeCrowd_rule[key] = params[key] = taskInfo[key];
                        }
                        params.activeCrowd_rule = activeCrowd_rule;
                    }
                    if (params.effTime && params.expTime) {
                        params.dateTime = [moment(params.effTime), moment(params.expTime)];
                    }
                    if (params.joinLimit != '-1') {
                        params.joinNum = params.joinLimit;
                        params.joinLimit = '1';
                    }
                    form.setFieldsValue(params);
                }
            },
        },
    );

    const { run: save, loading: saveLoading } = useRequest(
        (params) => {
            console.log(params);
            return saveTaskPkgInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res, [params]) => {
                if (res?.ret === 200) {
                    const data: any = res?.data;
                    if (data?.code == 10001) {
                        Modal.confirm({
                            title: data.msg || '任务包被关联，是否确定修改？',
                            onOk: () => {
                                params.check = true;
                                save(params);
                            },
                        });
                    } else {
                        message.success('保存成功');
                        goBack();
                    }
                } else {
                    message.success(res?.msg || '保存失败');
                }
            },
        },
    );

    // 失效时间只能延后
    const disabledDate = (current: any) => {
        const expTime = form.getFieldValue('dateTime')?.[1];
        return current && current < expTime;
    };
    const range = (start: number, end: number) => {
        const result = [];
        for (let i = start; i <= end; i++) {
            result.push(i);
        }
        return result;
    };
    const disabledRangeTime: any = (selectDate: any) => {
        const expTime = form.getFieldValue('dateTime')?.[1];
        if (selectDate > expTime) return;
        const currentDay = expTime.date();
        const currentHours = expTime.hours();
        const currentMinutes = expTime.minutes();
        const currentSecond = expTime.seconds();
        const settingHours = selectDate.hours();
        const settingDay = selectDate.date();
        const settinMinutes = selectDate.minutes();
        if (settingDay === currentDay) {
            const target: any = {
                disabledHours: () => range(0, currentHours - 1), //设置为当天这一小时，禁用该小时，该分钟之前的时间
            };
            if (settingHours === currentHours) {
                target.disabledMinutes = () => range(0, currentMinutes - 1);
            }
            if (settinMinutes === currentMinutes) {
                target.disabledSeconds = () => range(0, currentSecond - 1);
            }
            return target;
        }
        return {};
    };

    const submitEvent = (values: any) => {
        const params = {
            ...values,
            bizType: isChargePrize ? '02' : '01', //"业务类型（01-默认，02-充电任务）"
            effTime: values?.dateTime?.[0]?.format('YYYY-MM-DD HH:mm:ss') || undefined,
            expTime: values?.dateTime?.[1]?.format('YYYY-MM-DD HH:mm:ss') || undefined,
        };
        delete params.dateTime;
        if (values.activeCrowdFlag == '0') {
            const keys = Object.keys(values.activeCrowd_rule);
            for (const key of keys) {
                if (values.activeCrowd_rule[key]) {
                    params[key] = values.activeCrowd_rule[key];
                }
            }
        }

        if (values.joinLimit == '1') {
            params.joinLimit = values.joinNum;
        }
        delete params.activeCrowd_rule;
        delete params.joinNum;
        params.targetList?.forEach((ele: any, index: number) => (ele.sn = index + 1));
        params.taskList?.forEach((ele: any, index: number) => {
            ele.sn = index + 1;
            delete ele.temp_flag;
            ele?.targetList?.forEach(
                (targetEle: any, targetIndex: number) => (targetEle.sn = targetIndex + 1),
            );
        });
        save(params);
    };

    const goBack = () => {
        if (history?.length > 1) {
            // 临时处理是否有上一级页面的交互
            history.go(-1);
        } else {
            history.replace(
                `/marketing/event/${isChargePrize ? 'charge-prize' : 'task-package'}/list`,
            );
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form form={form} layout="horizontal" onFinish={submitEvent} {...formItemLayout}>
                    <Form.Item name={'relateFlag'} noStyle />
                    <div className={commonStyles['form-title']}>基础信息</div>
                    {(pkgId && (
                        <Form.Item
                            label={isChargePrize ? '活动ID' : '任务包ID'}
                            name={'packageId'}
                            {...formItemFixedWidthLayout}
                        >
                            {pkgId}
                        </Form.Item>
                    )) ||
                        null}
                    <Form.Item
                        name="packageName"
                        label={isChargePrize ? '活动名称' : '任务包名称'}
                        {...formItemFixedWidthLayout}
                        rules={[{ required: true, message: '请输入' }]}
                    >
                        <Input
                            placeholder={isChargePrize ? '请输入活动名称' : '请输入任务包名称'}
                            maxLength={20}
                            showCount
                            autoComplete="off"
                            disabled={relateFlag}
                        />
                    </Form.Item>
                    <Form.Item
                        label="有效期"
                        name="packageTimeType"
                        required
                        rules={[{ required: true, message: '请选择有效期' }]}
                    >
                        <Radio.Group disabled={relateFlag}>
                            <Radio value={'1'}>固定时间</Radio>
                            <Radio value={'2'}>动态时间</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(prev, next) => prev.packageTimeType !== next.packageTimeType}
                    >
                        {({ getFieldValue }) => {
                            const packageTimeType = `${getFieldValue('packageTimeType')}`;
                            return (
                                <>
                                    {packageTimeType !== '2' && (
                                        <Form.Item
                                            label=" "
                                            name="dateTime"
                                            {...formItemFixedWidthLayout}
                                            rules={[
                                                {
                                                    validator(rule, value) {
                                                        if (!value || isEmpty(value)) {
                                                            return Promise.reject('请选择活动时间');
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                },
                                            ]}
                                            colon={false}
                                            required={false}
                                            requiredMark={false}
                                        >
                                            <RangePicker
                                                showTime={{
                                                    format: 'HH:mm:ss',
                                                    defaultValue: [
                                                        moment('00:00:00', 'HH:mm:ss'),
                                                        moment('23:59:59', 'HH:mm:ss'),
                                                    ],
                                                }}
                                                format="YYYY-MM-DD HH:mm:ss"
                                                disabledDate={relateFlag && disabledDate}
                                                // 任务包被组件关联后任务包生效时间不可编辑，失效时间只能延后
                                                disabledTime={relateFlag && disabledRangeTime}
                                                disabled={[relateFlag, false]}
                                            />
                                        </Form.Item>
                                    )}
                                    {packageTimeType === '2' && (
                                        <Space
                                            size={16}
                                            align="baseline"
                                            style={{ marginLeft: '120px' }}
                                        >
                                            <Typography.Text>开始任务后</Typography.Text>
                                            <Form.Item
                                                name="endTimeDuration"
                                                rules={[
                                                    {
                                                        validator(rule, value) {
                                                            if (value && Number(value) > 0) {
                                                                return Promise.resolve();
                                                            } else {
                                                                return Promise.reject('请填写');
                                                            }
                                                        },
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    precision={0}
                                                    min={1}
                                                    max={360}
                                                    disabled={relateFlag}
                                                />
                                            </Form.Item>
                                            <Typography.Text>天内有效</Typography.Text>
                                        </Space>
                                    )}
                                </>
                            );
                        }}
                    </Form.Item>
                    <Form.Item
                        label="项目"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="usePlatform"
                    >
                        <Radio.Group>
                            <Radio value="01"> 新电途 </Radio>
                            <Radio value="02"> 商家平台 </Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        label="活动所属方"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        name="belongType"
                        initialValue={isChargePrize ? '01' : ''}
                    >
                        <Radio.Group disabled={isChargePrize}>
                            <Radio value="01"> 新电途 </Radio>
                            <Radio value="02"> 车生态 </Radio>
                        </Radio.Group>
                    </Form.Item>

                    {belongType === '02' && (
                        <Form.Item
                            rules={[
                                {
                                    required: true,
                                },
                            ]}
                            label={' '}
                            name="belongSubType"
                            required={false}
                            colon={false}
                        >
                            <Radio.Group>
                                <Radio value="0201"> 车生态平台使用 </Radio>
                                <Radio value="0202"> 车生态商户使用 </Radio>
                            </Radio.Group>
                        </Form.Item>
                    )}
                    {belongSubType === '0202' && (
                        <EnterpriseSelectItem
                            rules={[
                                {
                                    required: true,
                                },
                            ]}
                            formItemLayout={formItemFixedWidthLayout}
                            label={'商户'}
                            labelAlign="right"
                            form={form}
                        />
                    )}

                    <Form.Item
                        label={isChargePrize ? '活动说明' : '任务包说明'}
                        name={'packageRemarks'}
                        {...formItemFixedWidthLayout}
                        rules={[
                            {
                                required: true,
                                message: isChargePrize ? '请输入活动说明' : '请输入任务包说明',
                            },
                        ]}
                    >
                        <Input.TextArea
                            maxLength={100}
                            showCount
                            disabled={relateFlag}
                            placeholder={isChargePrize ? '请输入活动说明' : '请输入任务包说明'}
                            autoComplete="off"
                        />
                    </Form.Item>
                    <div className={commonStyles['form-title']}>规则配置</div>

                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues?.joinLimit != curValues?.joinLimit
                        }
                    >
                        {({ getFieldValue }) => {
                            const joinLimit = getFieldValue('joinLimit');
                            return (
                                <Form.Item
                                    name={'joinLimit'}
                                    label="完成条件"
                                    rules={[{ required: true, message: '请选择' }]}
                                    initialValue={isChargePrize ? '-1' : ''}
                                >
                                    <Radio.Group disabled={relateFlag || isChargePrize}>
                                        <Radio value="-1">完成所有任务后视为完成任务包</Radio>
                                        <Radio value="1">
                                            <Space size={'small'}>
                                                <span>完成一定任务数量后视为任务包完成</span>
                                                {(joinLimit == '1' && (
                                                    <Form.Item
                                                        noStyle
                                                        name={'joinNum'}
                                                        rules={[
                                                            ({ getFieldValue }) => ({
                                                                validator(rule, value) {
                                                                    const taskList =
                                                                        getFieldValue('taskList');
                                                                    if (!value) {
                                                                        return Promise.reject(
                                                                            '请输入限制参与数量',
                                                                        );
                                                                    }
                                                                    if (
                                                                        taskList?.length &&
                                                                        value > taskList?.length
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '限制参与数量不可超过已配置任务数量的上限',
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            min={1}
                                                            disabled={relateFlag}
                                                        />
                                                    </Form.Item>
                                                )) ||
                                                    null}
                                            </Space>
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            );
                        }}
                    </Form.Item>

                    <Form.Item
                        name={'openType'}
                        label="触发任务规则"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group disabled={relateFlag}>
                            <Radio value="01">用户手动触发</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        name={'rewardType'}
                        label="奖品发放方式"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group disabled={relateFlag}>
                            <Radio value={TASK_PRIZE_TYPE.SINGLE}>
                                单独领奖
                                <div style={{ opacity: 0.65 }}>
                                    每个任务单独配置奖品，完成任务时单独发奖
                                </div>
                            </Radio>
                            <Radio value={TASK_PRIZE_TYPE.MERGE}>
                                合并领奖
                                <div style={{ opacity: 0.65 }}>
                                    只对任务包配置奖品，完成任务时合并发奖
                                </div>
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        name={'obtainType'}
                        label="领奖方式"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group disabled={relateFlag}>
                            <Radio value="01">完成任务后，用户手动领取</Radio>
                            <Radio value="02">完成任务后，系统自动发放</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label={'目标用户'}
                        rules={[
                            {
                                required: true,
                                message: '请选择',
                            },
                        ]}
                        name={'activeCrowdFlag'}
                        initialValue={isChargePrize ? '1' : '0'}
                    >
                        <Radio.Group disabled={pkgId !== undefined || isChargePrize}>
                            <Radio value={'1'}>所有用户</Radio>
                            <Radio value={'0'}>部分用户</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues?.activeCrowdFlag != curValues?.activeCrowdFlag
                        }
                    >
                        {({ getFieldValue, setFieldsValue }) => {
                            const activeCrowdFlag = getFieldValue('activeCrowdFlag');
                            return (
                                activeCrowdFlag == '0' && (
                                    <Fragment>
                                        <Form.Item
                                            name="activeCrowd_rule"
                                            label=" "
                                            colon={false}
                                            wrapperCol={{ span: 24 }}
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        const custLabelList =
                                                            getFieldValue('custLabelList');
                                                        const labelList =
                                                            getFieldValue('labelList');
                                                        const cdpList = getFieldValue('cdpList');
                                                        const res = validatorCrowdItem({
                                                            custLabelList,
                                                            labelList,
                                                            cdpList,
                                                        });
                                                        if (res?.length) {
                                                            return Promise.reject(res);
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <PriceCrowdItem
                                                applyType={'04'}
                                                onChange={(newCrowdInfo: any) => {
                                                    setFieldsValue(newCrowdInfo);
                                                }}
                                                // disabled={
                                                //     isLock &&
                                                //     (curTaskType !==
                                                //         TASK_TYPES.VISIT_CHARGE ||
                                                //         prizeInfo.taskState > 2)
                                                // }
                                                singleMode
                                            ></PriceCrowdItem>
                                        </Form.Item>

                                        <Form.Item name={'custLabelList'} noStyle />
                                        <Form.Item name={'labelList'} noStyle />
                                        <Form.Item name={'cdpList'} noStyle />
                                    </Fragment>
                                )
                            );
                        }}
                    </Form.Item>

                    <Form.Item name={'packageState'} label="开关" initialValue={'09'} hidden>
                        <Radio.Group>
                            <Radio value="01">启用</Radio>
                            <Radio value="09">停用</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <TaskListForm
                        history={history}
                        disabled={relateFlag}
                        isChargePrize={isChargePrize}
                    />

                    <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues?.rewardType != curValues?.rewardType
                        }
                    >
                        {({ getFieldValue }) => {
                            const prizeType = getFieldValue('rewardType');
                            return (
                                (prizeType == TASK_PRIZE_TYPE.MERGE && (
                                    <Fragment>
                                        <div className={commonStyles['form-title']}>任务配置</div>
                                        <EventListForm history={history} disabled={relateFlag} />
                                    </Fragment>
                                )) ||
                                null
                            );
                        }}
                    </Form.Item>

                    <Form.Item>
                        <Row justify="center" style={{ marginTop: '32px' }}>
                            <Space size="large">
                                <Button
                                    type="primary"
                                    htmlType="submit"
                                    loading={loading || saveLoading}
                                >
                                    提交
                                </Button>
                                <Button onClick={goBack}>返回</Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};
export default React.memo(TaskManageEdit);
