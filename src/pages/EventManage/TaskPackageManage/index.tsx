import React, { useMemo, useRef, useState } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import TaskPackageListTable from './components/TaskPackageListTable';
import { Card } from 'antd';
import { Link, useModel, history } from 'umi';

const TaskManageList: React.FC = () => {
    const { query = {}, pathname } = history.location;
    // 充电有奖
    const isChargePrize = useMemo(() => {
        return pathname?.indexOf('/charge-prize') > -1;
    }, [pathname]);
    return (
        <PageHeaderWrapper>
            <Card>
                <TaskPackageListTable
                    showStyle="1"
                    otherParams={{
                        bizType: isChargePrize ? '02' : '01',
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(TaskManageList);
