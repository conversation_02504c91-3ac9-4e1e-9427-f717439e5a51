import { CaretDownOutlined, DownOutlined, LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Descriptions, Space, Tabs, Tree } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { Link, useLocation } from 'umi';
import moment from 'moment';
import { TASK_PRIZE_TYPE } from './declare';
import { useRequest } from 'ahooks';
import PriceCrowdItem from '@/pages/MarketingManage/Membership/components/PriceCrowdItem';
import { getTaskPkgInfoApi } from '@/services/Marketing/TaskPackageCenterApi';
import TablePro from '@/components/TablePro';

const TaskManageDetail: React.FC<{
    route: { name: string };
    history: any;
}> = (props) => {
    const { route, history } = props;
    const { query = {}, pathname } = history.location;

    const {} = query as any;

    const location: any = useLocation();
    const {
        query: { id: pkgId },
    }: { query: { id: string } } = location;

    // 充电有奖
    const isChargePrize = useMemo(() => {
        return pathname?.indexOf('/charge-prize') > -1;
    }, [pathname]);
    useEffect(() => {
        if (pkgId) {
            run(pkgId);
        }
    }, []);

    const [info, updateInfo] = useState<any>();
    const { run, loading } = useRequest(
        (params) => {
            return getTaskPkgInfoApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    const taskInfo = res?.data || {};
                    const params = { ...taskInfo };
                    if (params.activeCrowdFlag == '0') {
                        const keys = ['labelList', 'cdpList', 'custLabelList'];
                        const activeCrowd_rule = {};
                        for (const key of keys) {
                            activeCrowd_rule[key] = params[key] = taskInfo[key];
                        }
                        params.activeCrowd_rule = activeCrowd_rule;
                    }
                    if (params.joinLimit != '-1') {
                        params.joinNum = params.joinLimit;
                    }
                    updateInfo(params);
                }
            },
        },
    );

    const openTypeName = useMemo(() => {
        switch (info?.openType) {
            case '01':
                return '手动触发';

            default:
                break;
        }
        return '-';
    }, [info]);

    const obtainTypeName = useMemo(() => {
        switch (info?.obtainType) {
            case '01':
                return '手动触发';
            case '02':
                return '自动触发';

            default:
                break;
        }
        return '-';
    }, [info]);

    const rewardTypeName = useMemo(() => {
        switch (info?.rewardType) {
            case TASK_PRIZE_TYPE.SINGLE:
                return '单独领奖';
            case TASK_PRIZE_TYPE.MERGE:
                return '合并领奖';

            default:
                break;
        }
        return '-';
    }, [info]);

    const packageStateName = useMemo(() => {
        switch (info?.packageState) {
            case '01':
                return '启用';
            case '02':
                return '停用';

            default:
                break;
        }
        return '-';
    }, [info]);

    const goBack = () => {
        if (history?.length > 1) {
            // 临时处理是否有上一级页面的交互
            history.go(-1);
        } else {
            history.replace(
                `/marketing/event/${isChargePrize ? 'charge-prize' : 'task-package'}/list`,
            );
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={loading}>
                <Descriptions column={1} labelStyle={{ fontWeight: 'bold' }}>
                    <Descriptions.Item label={isChargePrize ? '活动' : '任务包'}>
                        {info?.packageName}
                        {` (${info?.packageId})`}
                    </Descriptions.Item>
                    <Descriptions.Item label={isChargePrize ? '可参与活动量' : '可参与任务量'}>
                        {info?.joinLimit == '-1' ? info?.taskList?.length : info?.joinLimit}
                    </Descriptions.Item>
                    <Descriptions.Item label={isChargePrize ? '活动开启触发' : '任务开启触发'}>
                        {openTypeName}
                    </Descriptions.Item>
                    <Descriptions.Item label={isChargePrize ? '活动完成触发' : '任务完成触发'}>
                        {obtainTypeName}
                    </Descriptions.Item>
                    <Descriptions.Item label="奖励规则">{rewardTypeName}</Descriptions.Item>
                    {`${info?.packageTimeType}` === '1' && (
                        <Descriptions.Item label="有效期">
                            <Space>
                                {moment(info?.effTime).format('YYYY-MM-DD HH:mm:ss')}
                                <span>~</span>
                                {moment(info?.expTime).format('YYYY-MM-DD HH:mm:ss')}
                            </Space>
                        </Descriptions.Item>
                    )}
                    {`${info?.packageTimeType}` === '2' && (
                        <Descriptions.Item label="有效期">
                            {`开始任务后${info?.endTimeDuration}天内有效`}
                        </Descriptions.Item>
                    )}
                    <Descriptions.Item label="适用人群">
                        <div style={{ display: 'block' }}>
                            {info?.activeCrowdFlag == '1' ? (
                                '不限制人群'
                            ) : (
                                <PriceCrowdItem value={info} disabled></PriceCrowdItem>
                            )}
                        </div>
                    </Descriptions.Item>
                    <Descriptions.Item label="开关">
                        <span
                            style={
                                info?.packageState == '01'
                                    ? { color: 'limegreen' }
                                    : { color: 'gray' }
                            }
                        >
                            {packageStateName}
                        </span>
                    </Descriptions.Item>
                </Descriptions>

                <Tabs>
                    <Tabs.TabPane key="0" tab="任务列表及目标事件">
                        <TablePro
                            scroll={{ x: 'max-content' }}
                            rowKey="id"
                            dataSource={info?.taskList}
                            columns={[
                                {
                                    title: '任务',
                                    width: 180,
                                    dataIndex: 'taskName',
                                    render(text: string | undefined, record: any) {
                                        return (
                                            <span>
                                                {text}
                                                <Link
                                                    to={`/marketing/event/task/list/detail?id=${record?.taskId}`}
                                                    target="_blank"
                                                    style={{ marginLeft: '6px' }}
                                                >
                                                    ({`${record.taskId}`})
                                                </Link>
                                            </span>
                                        );
                                    },
                                },
                                {
                                    title: '规则数量',
                                    width: 120,
                                    dataIndex: 'taskNum',
                                    render(text: string | undefined, record: any) {
                                        const rewardType = info?.rewardType;
                                        const taskNum =
                                            rewardType == TASK_PRIZE_TYPE.SINGLE
                                                ? record.targetList?.length
                                                : info?.targetList?.length;
                                        return <span>{taskNum}</span>;
                                    },
                                },
                                {
                                    title: '目标事件',
                                    width: 300,
                                    dataIndex: 'targetEvent',
                                    render(text: string | undefined, record: any) {
                                        const rewardType = info?.rewardType;
                                        const taskList =
                                            rewardType == TASK_PRIZE_TYPE.SINGLE
                                                ? record.targetList
                                                : info?.targetList;
                                        return (
                                            <Tree
                                                icon={<DownOutlined />}
                                                showIcon={false}
                                                showLine={{
                                                    showLeafIcon: false,
                                                }}
                                                switcherIcon={<CaretDownOutlined />}
                                                treeData={[
                                                    {
                                                        title: (
                                                            <span>
                                                                共
                                                                <span
                                                                    style={{
                                                                        color: 'red',
                                                                    }}
                                                                >
                                                                    {'1'}
                                                                </span>
                                                                条配置
                                                            </span>
                                                        ),
                                                        key: 'fixed_0',
                                                        children: taskList?.map(
                                                            (ele: any, index: number) => {
                                                                return {
                                                                    title: (
                                                                        <div>
                                                                            {`事件${index + 1}
                                                                                            ：`}
                                                                            {ele.targetName}
                                                                            {`(${ele?.targetId})`}
                                                                        </div>
                                                                    ),
                                                                    key: ele.targetId,
                                                                };
                                                            },
                                                        ),
                                                    },
                                                ]}
                                                selectable={false}
                                                defaultExpandAll
                                            />
                                        );
                                    },
                                    onCell: (_: any, index: number) => {
                                        const rewardType = info?.rewardType;
                                        // 合并领奖的展示效果
                                        if (rewardType == TASK_PRIZE_TYPE.MERGE) {
                                            if (index == 0) {
                                                return { rowSpan: info?.taskList.length };
                                            }
                                            return { rowSpan: 0 };
                                        }
                                        return {};
                                    },
                                },
                                {
                                    title: '操作',
                                    width: 100,
                                    fixed: 'right',
                                    render(text: string | undefined, record: API.EventTaskListVo) {
                                        return (
                                            <Link
                                                to={`/marketing/event/task/list/detail?id=${record?.taskId}`}
                                                title={text}
                                            >
                                                详情
                                            </Link>
                                        );
                                    },
                                },
                            ]}
                            pagination={false}
                        />
                    </Tabs.TabPane>
                </Tabs>
            </Card>
        </PageHeaderWrapper>
    );
};
export default React.memo(TaskManageDetail);
