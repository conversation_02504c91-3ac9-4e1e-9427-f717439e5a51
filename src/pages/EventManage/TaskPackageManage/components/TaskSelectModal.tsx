// 目标事件配置（单独配置，弹窗）

import { Form, Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import TaskListTable from '../../TaskManage/components/TaskListTable';
import { useModel } from 'umi';

const TaskSelectModal: React.FC<{
    initRef: any;
    history: any;
    onFinish: any;
}> = (props) => {
    const { initRef, onFinish } = props;
    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [disabledIds, updateDisabledIds] = useState<string[] | undefined>();
    const { listData } = useModel('taskManage.list');
    const [selectList, updateSelectList] = useState<any>([]); //
    useImperativeHandle(initRef, () => ({
        show: ({ disabledIds: _disabledIds }: { disabledIds?: string[] } = {}) => {
            updateVisible(true);
            updateDisabledIds(_disabledIds);
            form.resetFields();
            updateSelectList([]);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const rowSelection = {
        type: 'checkbox',
        checkStrictly: false,
        selectedRowKeys: selectList.map((item: any) => item.taskId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            const tableRoundList: any = [];
            for (const item of listData.list) {
                tableRoundList.push(item);
            }
            // 筛选出非当前页的勾选项，不予处理
            const otherCpns = selectList.filter(
                (x: any) => tableRoundList.filter((now: any) => now.taskId == x.taskId).length == 0,
            );
            updateSelectList([...otherCpns, ...selectedRows] as any);
        },
        getCheckboxProps: (record: any) => ({
            disabled: disabledIds && disabledIds.indexOf(`${record?.taskId}`) >= 0,
            name: record.taskId,
        }),
    };

    return (
        <Modal
            visible={visible}
            title="任务配置"
            onCancel={onClose}
            onOk={() => {
                onFinish?.(selectList);
                onClose();
            }}
            width={1100}
            okButtonProps={{ disabled: selectList?.length == 0 }}
        >
            <TaskListTable {...props} showStyle="2" tableProps={{ rowSelection }} />
        </Modal>
    );
};
export default React.memo(TaskSelectModal);
