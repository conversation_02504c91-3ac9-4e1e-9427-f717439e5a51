import React, { Fragment, useImperativeHandle, useMemo, useState } from 'react';
import TaskDetailLayout, { TASK_DETAIL_STYLE } from '../../TaskManage/components/TaskDetailLayout';
import { Modal } from 'antd';

const TaskDetailModal: React.FC<{
    initRef: any;
}> = ({ initRef }) => {
    const [visible, updateVisible] = useState(false);
    const [id, updateId] = useState<string>('');
    useImperativeHandle(initRef, () => ({
        show: ({ id: _id }: { id: string } = { id: '' }) => {
            updateVisible(true);
            updateId(_id);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    return (
        <Modal visible={visible} title="任务详情" onCancel={onClose} width={880} footer={null}>
            <div
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
            >
                <TaskDetailLayout showStyle={TASK_DETAIL_STYLE.ONLY_RULE} id={id} />
            </div>
        </Modal>
    );
};
export default React.memo(TaskDetailModal);
