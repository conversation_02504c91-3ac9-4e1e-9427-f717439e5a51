import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { useRequest } from 'ahooks';
import { Button, Col, DatePicker, Form, Input, Select, Space, Switch, message, Tabs } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import moment from 'moment';
import React, { Fragment, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Link, useModel, history } from 'umi';
import styles from '@/assets/styles/common.less';
import { updateTaskPkgStateApi } from '@/services/Marketing/TaskPackageCenterApi';

const { RangePicker } = DatePicker;
const SearchLayout = (props: any) => {
    // const [form] = Form.useForm();
    const { listLoading, searchList, pagination, cacheRef } = useModel('taskManage.pkgList');

    const { initRef, form, otherParams = {}, showStyle, isChargePrize } = props;
    useImperativeHandle(initRef, () => ({
        resetForm,
        onFinish,
    }));

    const processParams = (formData: any): any => {
        const params = {
            ...formData,
            ...otherParams,
            effTime:
                (formData?.dates && formData?.dates[0]?.format('YYYY-MM-DD 00:00:00')) || undefined,
            expTime:
                (formData?.dates && formData?.dates[1]?.format('YYYY-MM-DD 23:59:59')) || undefined,
            dates: undefined,
            beginTime:
                (formData?.createDates &&
                    formData?.createDates[0]?.format('YYYY-MM-DD 00:00:00')) ||
                undefined,
            endTime:
                (formData?.createDates &&
                    formData?.createDates[1]?.format('YYYY-MM-DD 23:59:59')) ||
                undefined,
            createDates: undefined,
            packageId: (formData?.packageId?.length && formData?.packageId?.trim()) || undefined,
            packageName:
                (formData?.packageName?.length && formData?.packageName?.trim()) || undefined,
        };
        return params;
    };

    const onFinish = (formData: API.QrCodeListRequst) => {
        searchList(
            { current: pagination?.current, pageSize: pagination?.pageSize },
            processParams(formData),
        );
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination?.pageSize }, otherParams);
    };

    // const { run: exportFunc } = useRequest(
    //     (params) => {
    //         return exportQrcodeList(params);
    //     },
    //     {
    //         manual: true,
    //         onSuccess: (res: API.CommonBooleanResponse) => {
    //             if (res?.ret === 200) {
    //                 message.success('导出成功，请稍后到文件暂存区查看');
    //                 cacheRef?.current?.count();
    //             } else {
    //                 message.error(res?.msg || '导出失败');
    //             }
    //         },
    //     },
    // );

    // const onExportForm = () => {
    //     exportFunc(processParams(form?.getFieldsValue(true)));
    // };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError labelCol={{ span: 6 }} {...props}>
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                // onExportForm={onExportForm}
                // exportName="导出至暂存区"
            >
                <Col span={8}>
                    <Form.Item label={`${isChargePrize ? '活动' : '任务包'}ID`} name="packageId">
                        <Input
                            maxLength={50}
                            allowClear
                            autoComplete="off"
                            placeholder={`请填写${isChargePrize ? '活动' : '任务包'}ID`}
                        />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item
                        label={`${isChargePrize ? '活动' : '任务包'}名称`}
                        name="packageName"
                    >
                        <Input
                            maxLength={50}
                            allowClear
                            autoComplete="off"
                            placeholder={`请填写${isChargePrize ? '活动' : '任务包'}名称`}
                        />
                    </Form.Item>
                </Col>
                {!isChargePrize && (
                    <>
                        <Col span={8}>
                            <Form.Item label="任务开启触发" name="openType" labelCol={{ span: 8 }}>
                                <Select placeholder="全部" allowClear>
                                    <Select.Option value={'01'}>手动触发</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="任务完成触发"
                                name="obtainType"
                                labelCol={{ span: 8 }}
                            >
                                <Select placeholder="全部" allowClear>
                                    <Select.Option value={'01'}>手动触发</Select.Option>
                                    <Select.Option value={'02'}>自动触发</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="奖励规则" name="rewardTypeList">
                                <Select placeholder="全部" allowClear>
                                    <Select.Option value={'01'}>单独领奖</Select.Option>
                                    <Select.Option value={'02'}>合并领奖</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        {(showStyle == '1' && (
                            <Col span={8}>
                                <Form.Item label="有效期" name="dates">
                                    <RangePicker format="YYYY-MM-DD" allowClear />
                                </Form.Item>
                            </Col>
                        )) ||
                            null}
                        <Col span={8}>
                            <Form.Item label="创建时间" name="createDates">
                                <RangePicker format="YYYY-MM-DD" allowClear />
                            </Form.Item>
                        </Col>
                        {(showStyle == '1' && (
                            <Col span={8}>
                                <Form.Item label="开关" name="packageState">
                                    <Select placeholder="不限" allowClear>
                                        <Select.Option value={'01'}>启用</Select.Option>
                                        <Select.Option value={'09'}>停用</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                        )) ||
                            null}
                    </>
                )}
            </SearchOptionsBar>
        </Form>
    );
};

const TaskPackageListTable: React.FC<{
    showStyle: '1' | '2'; // 1,普通展示列表；2，任务详情的展示列表
    otherParams?: any; // 额外入参
}> = ({ otherParams, showStyle }) => {
    const { query = {}, pathname } = history.location;

    const {} = query as any;
    const [form] = Form.useForm();
    const { listData, listLoading, searchList, pagination } = useModel('taskManage.pkgList');
    const formRef = useRef<any>();
    const [activeStatus, setActiveStatus] = useState<string>('');

    // 充电有奖
    const isChargePrize = useMemo(() => {
        return pathname?.indexOf('/charge-prize') > -1;
    }, [pathname]);
    useEffect(() => {
        formRef.current?.resetForm();
    }, []);
    const { run: changeStatusRequest, loading: changeStatusLoading } = useRequest(
        (packageId, packageState) => {
            return updateTaskPkgStateApi({ packageId, packageState });
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('修改状态成功');
                    formRef.current?.resetForm();
                } else {
                    message.error(res?.msg || '修改状态失败');
                }
            },
            onError: () => {
                message.error('修改状态失败');
            },
        },
    );
    const confirmChangeStatus = (id: number, status: string) => {
        changeStatusRequest(id, status);
    };
    const prizeColumns: ColumnsType<any> = [
        {
            title: '活动ID',
            width: 120,
            dataIndex: 'packageId',
        },
        {
            title: '活动名称',
            width: 140,
            dataIndex: 'packageName',
            render(text: string | undefined, record: any) {
                return (
                    (record.packageId && showStyle == '2' && (
                        <Link
                            to={`/marketing/event/${
                                isChargePrize ? 'charge-prize' : 'task-package'
                            }/list/detail?id=${record?.packageId}`}
                            target="_blank"
                            title={text}
                        >
                            {text}
                        </Link>
                    )) ||
                    text
                );
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span>{moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
            },
        },
        {
            title: '创建人',
            width: 200,
            dataIndex: 'createdBy',
        },
        {
            title: '活动所属方',
            width: 200,
            dataIndex: 'belongTypeName',
        },
        ...((showStyle == '1' && [
            {
                title: '状态',
                width: 100,
                dataIndex: 'packageState',
                render(text, record) {
                    return (
                        <Switch
                            checked={text === '01'}
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                            loading={changeStatusLoading}
                            onChange={() => {
                                confirmChangeStatus(record?.packageId, text === '01' ? '09' : '01');
                            }}
                        />
                    );
                },
            },
        ]) ||
            []),
    ];
    const columns: ColumnsType<any> = [
        {
            title: '任务包ID',
            width: 120,
            dataIndex: 'packageId',
        },
        {
            title: '任务包名称',
            width: 140,
            dataIndex: 'packageName',
            render(text: string | undefined, record: any) {
                return (
                    (record.packageId && showStyle == '2' && (
                        <Link
                            to={`/marketing/event/${
                                isChargePrize ? 'charge-prize' : 'task-package'
                            }/list/detail?id=${record?.packageId}`}
                            target="_blank"
                            title={text}
                        >
                            {text}
                        </Link>
                    )) ||
                    text
                );
            },
        },
        {
            title: '开启触发',
            width: 140,
            dataIndex: 'openTypeName',
        },
        {
            title: '完成触发',
            width: 140,
            dataIndex: 'obtainTypeName',
        },
        {
            title: '奖励规则',
            width: 140,
            dataIndex: 'rewardTypeName',
        },
        {
            title: '包含/可参与数量',
            width: 180,
            dataIndex: 'temp1',
            render(text, record) {
                const taskNum = record.taskNum;
                const joinLimit = record.joinLimit == '-1' ? taskNum : record.joinLimit;
                return (
                    <span>
                        {taskNum}/{joinLimit}
                    </span>
                );
            },
        },
        ...((showStyle == '1' && [
            {
                title: '有效期',
                width: 200,
                dataIndex: 'temp2',
                render(text: any, record: any) {
                    return `${record?.packageTimeType}` === '2' ? (
                        <span>{record?.packageTime}</span>
                    ) : (
                        <span>
                            {moment(record.effTime).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                            {moment(record.expTime).format('YYYY-MM-DD HH:mm:ss')}
                        </span>
                    );
                },
            },
        ]) ||
            []),
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span>{moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
            },
        },
        {
            title: '创建人',
            width: 200,
            dataIndex: 'createdBy',
        },
        {
            title: '活动所属方',
            width: 200,
            dataIndex: 'belongTypeName',
        },
        ...((showStyle == '1' && [
            {
                title: '开关',
                width: 100,
                dataIndex: 'packageState',
                render(text, record) {
                    return (
                        <Switch
                            checked={text === '01'}
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                            loading={changeStatusLoading}
                            onChange={() => {
                                confirmChangeStatus(record?.packageId, text === '01' ? '09' : '01');
                            }}
                        />
                    );
                },
            },
        ]) ||
            []),
    ];

    const showColumns = useMemo(() => {
        const newColumns = isChargePrize ? prizeColumns : columns;
        if (showStyle == '1') {
            return [
                ...newColumns,
                {
                    title: '操作',
                    width: 160,
                    fixed: 'right',
                    render(text: string | undefined, record: any) {
                        return (
                            <Space>
                                <Link
                                    to={`/marketing/event/${
                                        isChargePrize ? 'charge-prize' : 'task-package'
                                    }/list/edit?id=${record?.packageId}`}
                                    // target="_blank"
                                    title={text}
                                >
                                    编辑
                                </Link>
                                <Link
                                    to={`/marketing/event/${
                                        isChargePrize ? 'charge-prize' : 'task-package'
                                    }/list/detail?id=${record?.packageId}`}
                                    // target="_blank"
                                    title={text}
                                >
                                    详情
                                </Link>
                            </Space>
                        );
                    },
                },
            ];
        }
        return columns;
    }, [showStyle]);

    return (
        <Fragment>
            <SearchLayout
                otherParams={{
                    ...otherParams,
                }}
                showStyle={showStyle}
                isChargePrize={isChargePrize}
                initRef={formRef}
                form={form}
            />
            {(showStyle == '1' && (
                <div className={styles['btn-bar']}>
                    <Space>
                        <Link
                            to={`/marketing/event/${
                                isChargePrize ? 'charge-prize' : 'task-package'
                            }/list/add`}
                        >
                            <Button type="primary">新建</Button>
                        </Link>
                    </Space>
                </div>
            )) ||
                null}
            {isChargePrize && (
                <Tabs
                    onChange={(v) => {
                        setActiveStatus(v);
                        const formData = form?.getFieldsValue();
                        formData.packageState = v;
                        formRef.current?.onFinish(formData);
                    }}
                    activeKey={activeStatus}
                >
                    <Tabs.TabPane key="" tab="全部" />
                    <Tabs.TabPane key="01" tab="启动" />
                    <Tabs.TabPane key="09" tab="停止" />
                </Tabs>
            )}

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="packageId"
                dataSource={listData?.list}
                columns={showColumns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Fragment>
    );
};

export default React.memo(TaskPackageListTable);
