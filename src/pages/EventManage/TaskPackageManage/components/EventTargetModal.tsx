// 目标事件配置（单独配置，弹窗）

import { Form, Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import EventListForm from './EventListForm';

const EventTargetModal: React.FC<{
    initRef: any;
    history: any;
    onFinish: any;
}> = ({ initRef, history, onFinish }) => {
    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [editIndex, updateEditIndex] = useState<number>(0);
    useImperativeHandle(initRef, () => ({
        show: (
            { defaultValues, editIndex: _editIndex }: { defaultValues?: any; editIndex: number } = {
                defaultValues: undefined,
                editIndex: 0,
            },
        ) => {
            updateVisible(true);
            form.resetFields();
            updateEditIndex(_editIndex);
            if (defaultValues) {
                form.setFieldsValue({ targetList: defaultValues });
            }
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const targetList = Form.useWatch('targetList', form);
    return (
        <Modal
            visible={visible}
            title="目标事件配置"
            onCancel={onClose}
            onOk={() => {
                onFinish?.(editIndex, targetList);
                onClose();
            }}
        >
            <div
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
            >
                <Form form={form}>
                    <EventListForm history={history} />
                </Form>
            </div>
        </Modal>
    );
};
export default React.memo(EventTargetModal);
