// 目标事件配置（单独配置，弹窗）

import { Col, Form, Input, Modal, Select } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { Link, useModel } from 'umi';
import TablePro from '@/components/TablePro';
import moment from 'moment';
import type { ColumnsType } from 'antd/lib/table';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { formatActivePage } from '@/utils/utils';

const SearchLayout: React.FC<{
    initRef: any;
}> = (props) => {
    const [form] = Form.useForm();
    const { listLoading, searchList, pagination } = useModel('taskManage.eventList');

    const { initRef } = props;
    useImperativeHandle(initRef, () => ({
        resetForm,
    }));

    const processParams = (formData: API.EventTaskListRequest) => {
        const params = {
            ...formData,
            targetId: formData?.targetId?.trim(),
            targetName: formData?.targetName?.trim(),
        };
        return params;
    };

    const onFinish = (formData: API.EventTaskListRequest) => {
        searchList(
            { current: pagination?.current, pageSize: pagination?.pageSize },
            processParams(formData),
        );
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination?.pageSize });
    };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError labelCol={{ span: 10 }} {...props}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <Form.Item label="目标事件名称" name="targetName">
                        <Input maxLength={50} allowClear autoComplete="off" placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="目标事件ID" name="targetId">
                        <Input maxLength={50} allowClear autoComplete="off" placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="目标事件用途" name="targetType">
                        <Select
                            allowClear
                            placeholder="请选择"
                            options={[
                                { label: '触发活动发奖', value: '01' },
                                { label: '触发告警', value: '02' },
                            ]}
                        />
                    </Form.Item>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const EventSelectModal: React.FC<{
    initRef: any;
    history: any;
    onFinish: any;
}> = (props) => {
    const { initRef, onFinish, history } = props;
    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [disabledIds, updateDisabledIds] = useState<string[] | undefined>();
    const { listLoading, listData, pagination } = useModel('taskManage.eventList');
    const [selectList, updateSelectList] = useState<any>([]); //
    const formRef = useRef<any>();
    useImperativeHandle(initRef, () => ({
        show: ({ disabledIds: _disabledIds }: { disabledIds?: string[] } = {}) => {
            updateVisible(true);
            if (_disabledIds) {
                updateDisabledIds(_disabledIds);
            }
            form.resetFields();
            updateSelectList([]);
            setTimeout(() => {
                formRef.current?.resetForm();
            }, 200);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const rowSelection = {
        type: 'radio',
        fixed: 'left',
        selectedRowKeys: selectList.map((item: any) => item.targetId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            updateSelectList([...selectedRows] as any);
        },
        getCheckboxProps: (record: any) => ({
            disabled: disabledIds && disabledIds.indexOf(`${record?.targetId}`) >= 0,
            name: record.targetId,
        }),
    };

    const columns: ColumnsType<any> = [
        {
            title: '目标事件ID',
            dataIndex: 'targetId',
            width: 120,
            render: (text, record) => {
                return (
                    <Link
                        to={`/eventcenter/eventmanage/target/list/update/${record?.targetId}/look`}
                    >
                        {text}
                    </Link>
                );
            },
        },
        {
            title: '目标事件名称',
            dataIndex: 'targetName',
            width: 180,
            render: (text, record) => {
                return (
                    <Link
                        to={`/eventcenter/eventmanage/target/list/update/${record?.targetId}/look`}
                    >
                        {text}
                    </Link>
                );
            },
        },
        {
            title: '目标事件用途',
            dataIndex: 'targetTypeName',
            width: 140,
        },
        {
            title: '目标事件描述',
            dataIndex: 'targetRemarks',
            width: 180,
        },
        {
            title: '关联ID',
            dataIndex: 'rewardId',
            width: 180,
            render(text: string, record: any) {
                const type = record.rewardType;
                const actVersion = record.actVersion || 2;
                const actSubType = record.rewardSubType || 1410;
                const path = formatActivePage({
                    type,
                    actId: text,
                    parentActFlag: undefined,
                    actVersion,
                    actSubType,
                });
                return (
                    (text && type && record?.targetUrl && (
                        <Link to={path} target="_blank">
                            {record?.rewardDesc}
                        </Link>
                    )) ||
                    record?.rewardDesc ||
                    '-'
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            width: 180,
            render: (value: string) => {
                return (value && moment(value).format('YYYY-MM-DD HH:mm:ss')) || '-';
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
        },
        {
            title: '开关',
            width: 100,
            dataIndex: 'targetStateName',
            render(text, record) {
                return (
                    <span
                        style={
                            record.targetState == '01' ? { color: 'limegreen' } : { color: 'gray' }
                        }
                    >
                        {text}
                    </span>
                );
            },
        },
    ];

    return (
        <Modal
            visible={visible}
            title="任务配置"
            onCancel={onClose}
            onOk={() => {
                onFinish?.(selectList[0]);
                onClose();
            }}
            width={920}
            okButtonProps={{ disabled: selectList?.length == 0 }}
        >
            <SearchLayout initRef={formRef} />

            <TablePro
                rowSelection={rowSelection}
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="targetId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
        </Modal>
    );
};
export default React.memo(EventSelectModal);
