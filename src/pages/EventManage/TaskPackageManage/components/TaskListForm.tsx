// 任务配置列表

import { Badge, Button, Form, List, Popconfirm, Space, Tree, message, Typography } from 'antd';
import { useDrag, useDrop, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import React, { Fragment, useRef, useEffect } from 'react';
import commonStyles from '@/assets/styles/common.less';
import { CaretDownOutlined, DownOutlined, MenuOutlined } from '@ant-design/icons';
import EventTargetModal from './EventTargetModal';
import { TASK_PRIZE_TYPE } from '../declare';
import TaskDetailModal from './TaskDetailModal';
import TaskSelectModal from './TaskSelectModal';
import baseStyles from '@/assets/styles/base.less';
import { copyObjectCommon } from '@/utils/utils';

interface TaskItem {
    taskId: string;
    taskName: string;
    targetList?: any[];
}
interface TaskListFormItemProps {
    list: TaskItem[];
    disabled: boolean;
    targetEventSelectRef: React.RefObject<any>;
    taskDetailRef: React.RefObject<any>;
    remove: (index: number) => void;
    setFieldsValue: (values: any) => void;
}

const TaskListFormItem: React.FC<TaskListFormItemProps> = (props?: any) => {
    const [taskList, setTaskList] = React.useState<TaskItem[]>(props?.list);
    const [prizeType, setPrizeType] = React.useState<TASK_PRIZE_TYPE>(TASK_PRIZE_TYPE.SINGLE);
    const [disabled, setDisabled] = React.useState<boolean>(props?.disabled);

    useEffect(() => {
        if (props?.list) {
            setTaskList(props?.list);
        }
    }, [props?.list]);
    useEffect(() => {
        if (props?.prizeType) {
            setPrizeType(props?.prizeType);
        }
    }, [props?.prizeType]);
    useEffect(() => {
        if (props?.disabled) {
            setDisabled(props?.disabled);
        }
    }, [props?.disabled]);
    const moveTask = (dragIndex: number, hoverIndex: number) => {
        const dragTask = taskList[dragIndex];
        const newTaskList = [...taskList];
        newTaskList.splice(dragIndex, 1);
        newTaskList.splice(hoverIndex, 0, dragTask);
        setTaskList(newTaskList);

        props?.setFieldsValue({ taskList: newTaskList });
    };

    const TaskItemComponent: React.FC<{ task: TaskItem; index: number }> = ({ task, index }) => {
        const [{ isDragging }, drag] = useDrag({
            type: 'TASK',
            item: { index },
            collect: (monitor) => ({
                isDragging: monitor.isDragging(),
            }),
        });

        const [{ handlerId }, drop] = useDrop({
            accept: 'TASK',
            collect(monitor) {
                return {
                    handlerId: monitor.getHandlerId(),
                };
            },
            hover(item: { index: number }, monitor) {
                if (!ref.current) {
                    return;
                }
                const dragIndex = item.index;
                const hoverIndex = index;

                if (dragIndex === hoverIndex) {
                    return;
                }

                const hoverBoundingRect = ref.current.getBoundingClientRect();
                const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
                const clientOffset = monitor.getClientOffset();
                const hoverClientY = (clientOffset as { y: number }).y - hoverBoundingRect.top;

                if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                    return;
                }

                if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                    return;
                }

                moveTask(dragIndex, hoverIndex);
                item.index = hoverIndex;
            },
        });

        const opacity = isDragging ? 0 : 1;
        const ref = React.useRef<HTMLDivElement>(null);

        return (
            <div
                ref={(node) => (drag(drop(node)), (ref.current = node))}
                style={{ opacity }}
                data-handler-id={handlerId}
            >
                <List.Item key={task.taskId}>
                    <List.Item.Meta
                        title={
                            <div
                                style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                }}
                                className={baseStyles.antFormItemMb}
                            >
                                <span>
                                    <MenuOutlined style={{ marginRight: '8px' }} />
                                    {task.taskName}
                                </span>
                                <Form.Item
                                    name={[index, 'temp_flag']}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator() {
                                                const targetList = getFieldValue([
                                                    'taskList',
                                                    index,
                                                    'targetList',
                                                ]);
                                                if (
                                                    prizeType === TASK_PRIZE_TYPE.SINGLE &&
                                                    !targetList?.length
                                                ) {
                                                    return Promise.reject('请选择目标事件');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Space size={'small'}>
                                        {prizeType === TASK_PRIZE_TYPE.SINGLE && (
                                            <Button
                                                size="small"
                                                type="link"
                                                onClick={() => {
                                                    props?.targetEventSelectRef.current?.show({
                                                        defaultValues: task.targetList,
                                                        editIndex: index,
                                                    });
                                                }}
                                                disabled={disabled}
                                            >
                                                {task.targetList?.length ? (
                                                    <Badge color="limegreen" />
                                                ) : (
                                                    <Badge color="#f50" />
                                                )}
                                                目标事件配置
                                            </Button>
                                        )}
                                        <Button
                                            size="small"
                                            type="link"
                                            onClick={() => {
                                                props?.taskDetailRef.current?.show({
                                                    id: task.taskId,
                                                });
                                            }}
                                        >
                                            详情
                                        </Button>
                                        {!disabled && (
                                            <Popconfirm
                                                title="确定删除？"
                                                onConfirm={() => {
                                                    props?.remove(index);
                                                }}
                                            >
                                                <Button size="small" type="link" danger>
                                                    删除
                                                </Button>
                                            </Popconfirm>
                                        )}
                                    </Space>
                                </Form.Item>
                            </div>
                        }
                        description={
                            prizeType === TASK_PRIZE_TYPE.SINGLE &&
                            task.targetList?.length && (
                                <Tree
                                    icon={<DownOutlined />}
                                    showIcon={false}
                                    showLine={{
                                        showLeafIcon: false,
                                    }}
                                    switcherIcon={<CaretDownOutlined />}
                                    treeData={[
                                        {
                                            title: (
                                                <span>
                                                    共
                                                    <span
                                                        style={{
                                                            color: 'red',
                                                        }}
                                                    >
                                                        {'1'}
                                                    </span>
                                                    条配置
                                                </span>
                                            ),
                                            key: 'fixed_0',
                                            children: task.targetList.map((ele: any) => {
                                                return {
                                                    title: (
                                                        <div>
                                                            {`事件${index + 1}：`}
                                                            {ele.targetName}
                                                            {`(${ele?.targetId})`}
                                                        </div>
                                                    ),
                                                    key: ele.targetId,
                                                };
                                            }),
                                        },
                                    ]}
                                    selectable={false}
                                    defaultExpandAll
                                />
                            )
                        }
                    />
                </List.Item>
            </div>
        );
    };

    return (
        <DndProvider backend={HTML5Backend}>
            <List style={{ marginTop: '6px', marginLeft: '12px' }}>
                {taskList?.map((task, index) => (
                    <TaskItemComponent key={task.taskId} task={task} index={index} />
                ))}
            </List>
        </DndProvider>
    );
};
const TaskListForm: React.FC<{
    history: any;
    disabled: boolean;
    isChargePrize: boolean;
}> = ({ history, disabled, isChargePrize }) => {
    const taskSelectRef = useRef<any>();
    const taskDetailRef = useRef<any>();
    const targetEventSelectRef = useRef<any>();
    return (
        <Form.Item noStyle shouldUpdate>
            {({ getFieldValue, setFieldsValue, validateFields }) => {
                const prizeType = getFieldValue('rewardType');
                const taskList = getFieldValue('taskList');

                return (
                    <Fragment>
                        <Space size={'large'}>
                            <span className={commonStyles['form-title']}>任务列表</span>
                            {(!disabled && (
                                <Button
                                    disabled={isChargePrize && taskList?.length >= 5}
                                    onClick={() => {
                                        taskSelectRef.current?.show({
                                            disabledIds: taskList?.map(
                                                (ele: any) => `${ele.taskId}`,
                                            ),
                                        });
                                    }}
                                >
                                    添加任务
                                </Button>
                            )) ||
                                null}
                        </Space>
                        <Form.Item
                            name="taskList"
                            rules={[
                                () => ({
                                    validator(rule: any, value: any) {
                                        if (!value?.length) {
                                            return Promise.reject('请选择任务');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Form.List name="taskList" initialValue={[]}>
                                {(fields, { add, remove }) => (
                                    <TaskListFormItem
                                        list={taskList}
                                        prizeType={prizeType}
                                        disabled={disabled}
                                        targetEventSelectRef={targetEventSelectRef}
                                        taskDetailRef={taskDetailRef}
                                        remove={remove}
                                        setFieldsValue={setFieldsValue}
                                    />
                                )}
                            </Form.List>
                        </Form.Item>
                        <TaskSelectModal
                            history={history}
                            initRef={taskSelectRef}
                            onFinish={(values: any) => {
                                const taskList = getFieldValue('taskList') || [];
                                const taskListCopy: any = copyObjectCommon(taskList) || [];
                                taskListCopy?.push(...values);
                                if (isChargePrize && taskListCopy?.length > 5) {
                                    message.warning('充电有奖最多添加5个任务');
                                    return;
                                }
                                setFieldsValue({ taskList: taskListCopy });
                            }}
                        />
                        <EventTargetModal
                            initRef={targetEventSelectRef}
                            history={history}
                            onFinish={(editIndex: number, values: any) => {
                                const taskList = getFieldValue('taskList') || [];
                                taskList[editIndex].targetList = values;
                                setFieldsValue({ taskList });
                                validateFields();
                            }}
                        />

                        <TaskDetailModal initRef={taskDetailRef} />
                    </Fragment>
                );
            }}
        </Form.Item>
    );
};
export default React.memo(TaskListForm);
