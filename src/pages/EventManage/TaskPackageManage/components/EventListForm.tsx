// 目标事件配置

import { MinusCircleOutlined, MenuOutlined } from '@ant-design/icons';
import { Button, Form, Popconfirm, Space } from 'antd';
import React, { Fragment, useRef } from 'react';
import EventSelectModal from './EventSelectModal';
import { useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DndProvider } from 'react-dnd';

interface DraggableItemProps {
    index: number;
    id: string;
    text: string;
    moveItem: (dragIndex: number, hoverIndex: number) => void;
    remove: (index: number) => void;
    disabled: boolean;
    targetList: any[];
}

const DraggableItem: React.FC<DraggableItemProps> = ({
    index,
    id,
    text,
    moveItem,
    remove,
    disabled,
    targetList,
}) => {
    const ref = useRef<HTMLDivElement>(null);

    const [{ handlerId }, drop] = useDrop({
        accept: 'item',
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item: DraggableItemProps, monitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            // Don't replace items with themselves
            if (dragIndex === hoverIndex) {
                return;
            }

            // Determine rectangle on screen
            const hoverBoundingRect = ref.current.getBoundingClientRect();

            // Get vertical middle
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

            // Determine mouse position
            const clientOffset = monitor.getClientOffset();

            // Get pixels to the top
            const hoverClientY = (clientOffset as { y: number }).y - hoverBoundingRect.top;

            // Only perform the move when the mouse has crossed half of the items height
            // When dragging downwards, only move when the cursor is below 50%
            // When dragging upwards, only move when the cursor is above 50%

            // Dragging downwards
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }

            // Dragging upwards
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }

            // Time to actually perform the action
            moveItem(dragIndex, hoverIndex);

            // Note: we're mutating the monitor item here!
            // Generally it's better to avoid mutations,
            // but it's good here for the sake of performance
            // to avoid expensive index searches.
            item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag, preview] = useDrag({
        type: 'item',
        item: () => {
            return { id, index };
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const opacity = isDragging ? 0 : 1;
    drag(drop(ref));

    return (
        <div ref={ref} style={{ opacity }} data-handler-id={handlerId}>
            <Form.Item key={index}>
                <Space>
                    <MenuOutlined />
                    <div>
                        {`事件${index + 1}`}：{targetList[index]?.targetName}
                        {`(${targetList[index]?.targetId})`}
                    </div>
                    {(!disabled && (
                        <Popconfirm
                            title="确认删除？"
                            onConfirm={() => {
                                remove(index);
                            }}
                        >
                            <MinusCircleOutlined
                                style={{
                                    marginLeft: '6px',
                                    color: '#f50',
                                }}
                            />
                        </Popconfirm>
                    )) ||
                        null}
                </Space>
            </Form.Item>
        </div>
    );
};

const EventListFormItem: React.FC<{
    fields: any[];
    targetList: any[];
    remove: (index: number) => void;
    setFieldsValue: (params: any) => void;
    disabled: boolean;
}> = ({ fields, targetList, remove, setFieldsValue, disabled }) => {
    const moveItem = (dragIndex: number, hoverIndex: number) => {
        const dragField = fields[dragIndex];
        fields.splice(dragIndex, 1);
        fields.splice(hoverIndex, 0, dragField);

        const dragTargetList = targetList[dragIndex];
        targetList.splice(dragIndex, 1);
        targetList.splice(hoverIndex, 0, dragTargetList);

        setFieldsValue({ targetList });
    };

    return (
        <DndProvider backend={HTML5Backend}>
            {fields.map((field, index) => (
                <DraggableItem
                    text={targetList[index]?.targetName}
                    key={field.id}
                    id={field.id}
                    index={index}
                    moveItem={moveItem}
                    remove={remove}
                    disabled={disabled}
                    targetList={targetList}
                />
            ))}
        </DndProvider>
    );
};
const EventListForm: React.FC<{
    history: any;
    disabled: boolean;
}> = ({ history, disabled }) => {
    const eventSelectRef = useRef<any>();

    return (
        <Fragment>
            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.targetList != curValues.targetList
                }
            >
                {({ getFieldValue, setFieldsValue }) => {
                    const targetList = getFieldValue('targetList') || [];
                    return (
                        <Form.List name="targetList" initialValue={[]}>
                            {(fields, { add, remove }) => (
                                <Fragment>
                                    <Form.Item
                                        name={'temp_flag'}
                                        label={'任务完成事件配置'}
                                        colon={false}
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator() {
                                                    if (!targetList?.length) {
                                                        return Promise.reject('请选择目标事件');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        {(!disabled && (
                                            <Button
                                                onClick={() => {
                                                    eventSelectRef.current?.show({
                                                        disabledIds: targetList?.map(
                                                            (ele: any) => `${ele.targetId}`,
                                                        ),
                                                    });
                                                }}
                                                type="primary"
                                            >
                                                添加事件
                                            </Button>
                                        )) ||
                                            null}
                                    </Form.Item>
                                    <EventListFormItem
                                        fields={fields}
                                        targetList={targetList}
                                        remove={remove}
                                        disabled={disabled}
                                        setFieldsValue={setFieldsValue}
                                    />

                                    <EventSelectModal
                                        initRef={eventSelectRef}
                                        history={history}
                                        onFinish={(values: any) => {
                                            targetList.push(values);
                                            setFieldsValue({ targetList });
                                        }}
                                    />
                                </Fragment>
                            )}
                        </Form.List>
                    );
                }}
            </Form.Item>
        </Fragment>
    );
};

export default React.memo(EventListForm);
