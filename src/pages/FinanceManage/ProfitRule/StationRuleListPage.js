import { ExclamationCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Select, Modal, Popconfirm, Tabs, Space, message } from 'antd';
import React, { Fragment, useState, useEffect, useRef } from 'react';
import { connect, Link } from 'umi';

import styles from './ProfitRuleListPage.less';
import usePageState from '@/hooks/usePageState.js';
import {
    saveprofitruleApi,
    getStationRuleListPath,
    addStationTemplateApi,
} from '@/services/FinanceManage/ProfitRuleApi';
import { exportTableByParams } from '@/utils/utils';
import EditRulesModal from '../CityRule/EditRulesModal';
import { RULE_TYPES } from '@/config/declare';
import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import { TemplateListModal } from './ProfitRuleTemplateListPage';
import ImportHistoryModelLayout from './components/ImportHistoryModelLayout';
import ImportModelLayout from './components/ImportModelLayout';
import EditModelLayout from './components/EditModelLayout';
import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const { TabPane } = Tabs;
const { confirm } = Modal;
const STATUS_TYPES = {
    ALL: '00',
    CURRENCY: '01',
    STATION: '02',
    CITY: '03',
};

/**
 * 场站规则列表
 */
export const StationRuleListView = (props) => {
    const {
        dispatch,
        profitRuleModel: { stationRuleList, stationRuleListTotal },
        listLoading,
        getInfoLoading,
        operId,
        workorderEvent, //工单提交事件
    } = props;

    const [form] = Form.useForm();
    const [showEditView, toggleEditView] = useState(false); // 编辑弹窗状态
    const [showImportView, toggleImportView] = useState(false); // 导入弹窗状态
    const [showImportHistoryView, toggleImportHistoryView] = useState(false); // 导入历史弹窗状态
    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [editItems, changeEditItems] = useState([]); // 多个编辑项
    const [submitLoading, changeSubmitLoading] = useState(false);
    const templateRef = useRef();

    const { cooperationPlatform } = useCooperationPlatform();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: 50,
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            operId,
            stationName: data.stationName,
            ruleModel: data.ruleModel ? data.ruleModel.join(',') : '',
            templateIds: data.templateIds ? data.templateIds.join(',') : '',
            city: data.city?.join(',') || data.city,
            stationIdList: JSON.stringify(data.stationIdList) || '',
            effTimeBegin:
                (data.effTime && data.effTime[0] && data.effTime[0].format('YYYY-MM-DD')) || '',
            effTimeEnd:
                (data.effTime && data.effTime[1] && data.effTime[1].format('YYYY-MM-DD')) || '',
            expTimeBegin:
                (data.expTime && data.expTime[0] && data.expTime[0].format('YYYY-MM-DD')) || '',
            expTimeEnd:
                (data.expTime && data.expTime[1] && data.expTime[1].format('YYYY-MM-DD')) || '',
            cooperationType: '01',
            stationBusiStatus: data.stationBusiStatus,
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.profitRuleType = pageInfo.tabType;
        }
        changeSelectItems([]);
        dispatch({
            type: 'profitRuleModel/getStationRuleList',
            options: params,
        });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId,
            stationName: data.stationName,
            ruleModel: data.ruleModel ? data.ruleModel.join(',') : '',
            templateIds: data.templateIds ? data.templateIds.join(',') : '',
            city: data.city?.join(',') || data.city,
            stationIds: data.stationIdList?.join?.(',') || data.stationIdList || '',
            effTimeBegin:
                (data.effTime && data.effTime[0] && data.effTime[0].format('YYYY-MM-DD')) || '',
            effTimeEnd:
                (data.effTime && data.effTime[1] && data.effTime[1].format('YYYY-MM-DD')) || '',
            expTimeBegin:
                (data.expTime && data.expTime[0] && data.expTime[0].format('YYYY-MM-DD')) || '',
            expTimeEnd:
                (data.expTime && data.expTime[1] && data.expTime[1].format('YYYY-MM-DD')) || '',
            stationBusiStatus: data.stationBusiStatus,
            cooperationType: '01',
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.profitRuleType = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getStationRuleListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 批量重置
    const resetStationRuleByIds = async (items) => {
        try {
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId,
                stationName: data.stationName,
                city: data.city && JSON.stringify(data.city),
                stationIdList: JSON.stringify(data.stationIdList) || '',
                cooperationType: '01',
                cooperationPlatform,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.profitRuleType = pageInfo.tabType;
            }
            const stationIds = [];
            for (const item of items) {
                stationIds.push(item.stationId);
            }
            if (workorderEvent) {
                params.selectItems = items;
                workorderEvent('reset', params);
                return;
            }
            await dispatch({
                type: 'profitRuleModel/resetStationRuleByIds',
                stationIds: stationIds.join(','),
                options: params,
            });
            message.success('重置成功');
        } catch (error) {
            console.log(44444, error);
            return Promise.reject(error);
        }
    };

    // 关闭规则配置页面
    const closeEditViewEvent = () => {
        toggleEditView(false);
        changeEditItems([]);
        dispatch({
            type: 'profitRuleModel/updateProfitRuleInfo',
            editRuleInfo: {},
        });
        dispatch({
            type: 'profitRuleModel/updateSelectTemplateInfo',
            info: null,
        });
        searchData();
    };

    // 打开导入规则页面
    const openImportViewEvent = () => {
        toggleImportView(true);
    };

    // 打开导入历史记录页面
    const openImportHistoryViewEvent = () => {
        toggleImportHistoryView(true);
    };

    // 城市规则相关
    const editRef = useRef();

    const columns = [
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div
                        className="text-line"
                        style={{
                            width: '200px',
                            whiteSpace: 'normal',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '所属城市',
            width: 120,
            dataIndex: 'cityName',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '分润规则类型',
            width: 180,
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '分润方式',
            width: 460,
            dataIndex: 'ruleName',

            render(text, record) {
                return (
                    <div
                        style={{
                            whiteSpace: 'pre',
                            width: '320px',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '分润关系',
            width: 160,
            dataIndex: 'ruleRelName',

            render(text, record) {
                return (
                    <div
                        className="text-line"
                        style={{ width: '120px', color: record.dayexpflag == '1' ? 'red' : '' }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '溢价分成规则',
            width: 160,
            dataIndex: 'premiumString',
        },
        {
            title: '生效时间',
            width: 200,
            dataIndex: 'effTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '失效时间',
            width: 200,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {(text?.length && text) || record.maxExpTime}
                    </span>
                );
            },
        },
        {
            title: '所属模板',
            width: 160,
            dataIndex: 'templateName',
            render(text, record) {
                return (
                    <span
                        className={styles['table-btn']}
                        title={text}
                        onClick={() => {
                            templateRef.current?.show(record.templateId);
                        }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (text, record) => (
                <Fragment>
                    <span className={styles['table-btn']} onClick={() => editItemEvent(record)}>
                        编辑
                    </span>
                    {record.profitRuleType === '02' ? (
                        <Popconfirm
                            title="确认重置？"
                            okText="确认"
                            cancelText="取消"
                            onConfirm={() => resetStationRuleByIds([record])}
                        >
                            <a href="#">重置</a>
                        </Popconfirm>
                    ) : null}
                </Fragment>
            ),
        },
    ];

    const rowSelection = {
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        selectedRowKeys: selectItems?.map((ele) => ele.stationId),
        getCheckboxProps: (record) => ({
            name: record.stationId,
        }),
    };

    // 批量重置
    const batchRestEvent = () => {
        let filterList = selectItems.filter((ele) => ele.profitRuleType === '02');

        if (selectItems.length == 0) {
            message.error('请选择要重置的规则');
            return;
        }
        if (filterList?.length != selectItems?.length) {
            message.error('请不要勾选未配置的场站');
            return;
        }
        confirm({
            title: '确定要批量重置？',
            icon: <ExclamationCircleOutlined />,
            content: (
                <Space direction="vertical">
                    <span>重置后场站规则将还原为通用规则</span>
                    <span style={{ fontSize: 'small', color: 'gray' }}>
                        （若所选场站有城市规则，重置后将还原为城市规则）
                    </span>
                </Space>
            ),
            onOk() {
                resetStationRuleByIds(filterList);
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const resetEvent = (items) => {
        confirm({
            title: '确认重置场站规则？',
            icon: <ExclamationCircleOutlined />,
            content: (
                <Space direction="vertical">
                    <span>重置后场站规则将还原为通用规则</span>
                    <span style={{ fontSize: 'small', color: 'gray' }}>
                        （若所选场站有城市规则，重置后将还原为城市规则）
                    </span>
                </Space>
            ),
            async onOk() {
                try {
                    await resetStationRuleByIds(items);
                    closeEditViewEvent();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    // 批量编辑
    const batchEditEvent = () => {
        if (selectItems.length == 0) {
            message.error('请选择要编辑的规则');
            return;
        } else if (selectItems.length == 1) {
            editItemEvent(selectItems[0]);
            return;
        }
        toggleEditView(true);
        changeEditItems(selectItems);
    };

    // 编辑单个
    const editItemEvent = (item) => {
        const params = {
            ruleId: item.ruleId,
            operId,
            city: item.city,
            stationId: item.stationId,
            profitRuleType: item.profitRuleType,
            cooperationType: '01',
            cooperationPlatform,
        };
        if (item.profitRuleType === RULE_TYPES.STATION) {
            params.stationId = item.stationId;
        }
        dispatch({
            type: 'profitRuleModel/getRuleInfo',
            params,
        });

        changeEditItems([item]);

        toggleEditView(true);
    };

    const onEditFinish = async (params) => {
        if (submitLoading) {
            return;
        }
        try {
            changeSubmitLoading(true);

            if (params.templateId) {
                // 场站配置模板
                const ids = editItems.map((ele) => ele.stationId);
                params.stationIds = ids.join(',');
                params.operId = operId;
                params.cooperationPlatform = cooperationPlatform;

                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    closeEditViewEvent();

                    return;
                }

                await addStationTemplateApi(params);
            } else {
                // 场站配置规则

                params.operId = operId;
                params.ruleId = editItems[0].ruleId;
                params.cooperationType = '01'; // 抽成模式
                const ids = editItems.map((ele) => ele.stationId);
                params.stationIds = ids.join(',');
                params.cooperationPlatform = cooperationPlatform;

                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    closeEditViewEvent();

                    return;
                }

                await saveprofitruleApi(params);
            }

            message.success('保存成功');
            closeEditViewEvent();
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    return (
        <Fragment>
            <Card>
                <Tabs defaultActiveKey={pageInfo?.tabType || '1'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="场站" key={STATUS_TYPES.STATION} />
                    <TabPane tab="城市" key={STATUS_TYPES.CITY} />
                    <TabPane tab="通用" key={STATUS_TYPES.CURRENCY} />
                </Tabs>
                <SearchLayout
                    form={form}
                    operId={operId}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button className={styles['btn-item']} onClick={batchEditEvent}>
                        批量编辑
                    </Button>
                    <Button className={styles['btn-item']} onClick={batchRestEvent}>
                        批量重置
                    </Button>
                    <Button className={styles['btn-item']}>
                        <Link
                            to={`/sellerCenter/operatormanage/profitRule/list/templateManage`}
                            target="_blank"
                        >
                            模板管理
                        </Link>
                    </Button>
                    {!workorderEvent && (
                        <Fragment>
                            <Button className={styles['btn-item']} onClick={openImportViewEvent}>
                                导入规则
                            </Button>
                            <Button
                                className={styles['btn-item']}
                                onClick={openImportHistoryViewEvent}
                            >
                                导入历史
                            </Button>
                        </Fragment>
                    )}
                </div>
                <p style={{ margin: '10px 0' }}>{`已选${selectItems?.length || 0}条`}</p>
                <TablePro
                    name="list"
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={stationRuleList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationRuleListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [10, 50, 100, 500],
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title="规则配置"
                width={950}
                visible={showEditView}
                onCancel={closeEditViewEvent}
                footer={null}
                destroyOnClose
                maskClosable={false}
            >
                <EditModelLayout
                    {...props}
                    cooperationPlatform={cooperationPlatform}
                    operId={operId}
                    editItems={editItems}
                    multiple={editItems.length > 1}
                    loading={getInfoLoading}
                    submitLoading={submitLoading}
                    onFinish={onEditFinish}
                    onCancel={closeEditViewEvent}
                    resetEvent={resetEvent}
                    confirmCityEvent={async (editRuleInfo) => {
                        try {
                            const data = form.getFieldsValue();
                            const params = {
                                pageIndex: pageInfo.pageIndex,
                                pageSize: pageInfo.pageSize,
                                operId,
                                stationName: data.stationName,
                                city: JSON.stringify(data.city),
                                stationIdList: '',
                                cooperationType: '01',
                                cooperationPlatform,
                            };
                            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                                params.profitRuleType = pageInfo.tabType;
                            }

                            if (workorderEvent) {
                                params.selectItems = [editRuleInfo];
                                workorderEvent('reset', params);
                                closeEditViewEvent();
                                return;
                            }

                            await dispatch({
                                type: 'profitRuleModel/resetStationRuleByIds',
                                stationIds: editRuleInfo.stationId,
                                options: params,
                            });
                            closeEditViewEvent();
                            message.success('配置成功');
                        } catch (error) {}
                    }}
                    addCityRuleEvent={() => {
                        editRef?.current?.add({ operId, type: '03' });
                        closeEditViewEvent();
                    }}
                />
            </Modal>
            <ImportHistoryModelLayout
                cooperationPlatform={cooperationPlatform}
                show={showImportHistoryView}
                {...props}
                onCancel={() => {
                    toggleImportHistoryView(false);
                }}
            />
            <ImportModelLayout
                cooperationPlatform={cooperationPlatform}
                visible={showImportView}
                onRefresh={searchData}
                onCancel={() => {
                    toggleImportView(false);
                }}
                showHistory={() => {
                    toggleImportHistoryView(true);
                }}
            />
            <TemplateListModal initRef={templateRef} />
            <EditRulesModal path="profitRule" initRef={editRef} onEditFinish={searchData} />
        </Fragment>
    );
};

const StationRuleListPage = (props) => {
    const { history, route, match } = props;
    const { operId } = match.params;
    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <StationRuleListView {...props} closeEvent={goBack} operId={operId} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, profitRuleModel, loading }) => ({
    global,
    profitRuleModel,
    listLoading: loading.effects['profitRuleModel/getStationRuleList'],
    getInfoLoading: loading.effects['profitRuleModel/getRuleInfo'],
    importHistoryLoading: loading.effects['profitRuleModel/getImportHistoryList'],
    selectTemplateListLoading: loading.effects['profitRuleModel/getSelectTemplateInfo'],
}))(StationRuleListPage);
