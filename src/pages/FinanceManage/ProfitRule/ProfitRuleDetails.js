import { InfoCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Input,
    Form,
    DatePicker,
    Radio,
    message,
    Tooltip,
    Space,
    Modal,
    Switch,
} from 'antd';
import moment from 'moment';
import React, {
    Fragment,
    useState,
    useEffect,
    forwardRef,
    useImperativeHandle,
    useRef,
} from 'react';
import { connect } from 'umi';

import { RULE_TYPES, OPER_COOPERATION } from '@/config/declare';
import {
    saveprofitruleApi,
    addStationTemplateApi,
    getProfitruleExpireTimeFlag,
} from '@/services/FinanceManage/ProfitRuleApi';
import styles from './ProfitRuleListPage.less';
import { isEmpty, checkClosePremiumCommon } from '@/utils/utils';
import { initialRuleList } from './components/RuleModelFormItem';
import { childrenColumns } from './ProfitRuleTemplateListPage';
import TablePro from '@/components/TablePro';
import { RuleFormItem } from './components/RuleFormItem';
import SelectRuleModal from './components/SelectRuleModal';
import { isNumber } from 'lodash';
import PremiumSharingItem from './components/PremiumSharingItem';

import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const FormItem = Form.Item;
const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};
const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

export const ProftRuleDetailsView = forwardRef((props, ref) => {
    const {
        dispatch,
        history,
        getInfoLoading,
        selectTemplateListLoading,
        profitRuleModel,
        closeEvent,
        onWorkorderFinish, //工单提交事件
        ruleId,
        operName,
        operId,
    } = props;

    const { editRuleInfo, selectRuleInfo, selectTemplateInfo } = profitRuleModel;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const selectTemplateRef = useRef();
    const [canISetEffTime, changeCiSetEffTime] = useState(true);

    const { cooperationPlatform } = useCooperationPlatform();

    useImperativeHandle(ref, () => ({
        initData: initData,
    }));

    const initData = () => {
        if (operId || selectRuleInfo?.operId) {
            dispatch({
                type: 'profitRuleModel/getRuleInfo',
                params: {
                    operId: operId || (selectRuleInfo && selectRuleInfo.operId),
                    profitRuleType: RULE_TYPES.COMMON,
                    cooperationPlatform,
                },
            });
        }
    };

    useEffect(() => {
        return () => {
            dispatch({
                type: 'profitRuleModel/updateProfitRuleInfo',
                editRuleInfo: {},
            });
            dispatch({
                type: 'profitRuleModel/updateSelectTemplateInfo',
                info: undefined,
            });
        };
    }, []);

    useEffect(() => {
        initData();
    }, [selectRuleInfo]);

    const mainInfo = editRuleInfo?.list?.[0];
    useEffect(() => {
        if (mainInfo) {
            initEditRuleInfo(mainInfo);
        }
    }, [mainInfo]);

    const initEditRuleInfo = (info) => {
        const params = {};
        if (info) {
            const { effTime, expTime, profitRuleTypeList, ruleId, premiumFlag } = info;
            params.effTime = effTime ? moment(effTime) : moment().add(5, 'minutes');
            params.expTime = expTime ? moment(expTime) : null;
            if (expTime) {
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }
            params.ruleRel = info.ruleRel || '01';
            params.ruleFlag = info.ruleFlag || '0';
            params.ruleId = ruleId;
            params.useTemplate = info.templateId?.length ? '02' : '01';
            params.templateId = info.templateId;
            params.premiumFlag = premiumFlag;
            if (params.templateId) {
                changeSelectEvent(info.templateId);
            }

            if (profitRuleTypeList) {
                params.profitRuleList = initialRuleList.map((ele) => {
                    const findItem = profitRuleTypeList.find(
                        (item) => item.ruleModel === ele.ruleModel,
                    );
                    if (findItem) {
                        return {
                            ruleModel: [findItem.ruleModel],
                            ruleType: findItem.ruleType,
                            ruleValue: findItem.ruleValue,
                            agreeType: findItem.agreeType,
                            longRuleValue: findItem.longRuleValue,
                            shortRuleValue: findItem.shortRuleValue,
                        };
                    }
                    return null;
                });
            }
            if (premiumFlag === '1') {
                params.divideDTOList = info.divideList?.map((v) => ({
                    divideType: v.divideType,
                    divideValue: v.divideValue,
                }));
            }
            if (mainInfo.templateId) {
                // 如果是由使用模板切换到不使用模板，生效失效时间可以改
                changeCiSetEffTime(true);
            } else if (effTime) {
                if (expTime) {
                    if (moment().isBetween(moment(effTime), moment(expTime))) {
                        changeCiSetEffTime(false);
                    } else {
                        changeCiSetEffTime(true);
                    }
                } else if (moment(effTime).isBefore(moment())) {
                    changeCiSetEffTime(false);
                } else {
                    changeCiSetEffTime(true);
                }
            } else {
                changeCiSetEffTime(true);
            }
        }
        form.setFieldsValue(params);
    };

    useEffect(() => {
        loadSelectTemplateList();
    }, []);

    const loadSelectTemplateList = () => {
        dispatch({
            type: 'profitRuleModel/getSelectTemplateList',
            cooperationType: OPER_COOPERATION.TAKE,
        });
    };

    const changeSelectEvent = (value) => {
        dispatch({
            type: 'profitRuleModel/getSelectTemplateInfo',
            templateId: value,
        });
        form.setFieldValue('templateId', value);
        form.validateFields(['useTemplate']);
    };

    /**
     * 提交表单
     */
    const onFinish = async (values) => {
        if (submitLoading) {
            return;
        }
        changeSubmitLoading(true);
        const ruleFlag = form.getFieldValue('ruleFlag');
        const profitRuleList = form.getFieldValue('profitRuleList') || [];
        const filterRuleList = profitRuleList.filter((ele) => ele && !isEmpty(ele.ruleModel)) || [];
        const profitRuleTypes = filterRuleList.map((ele) => {
            let options = {
                ...ele,
                ruleModel: String(ele.ruleModel),
            };
            if (ele.agreeType instanceof Array && ele.agreeType.length === 1) {
                if (ele.agreeType.includes('01')) {
                    options.longRuleValue = ele.ruleValue;
                }
                if (ele.agreeType.includes('02')) {
                    options.shortRuleValue = ele.ruleValue;
                }
            }
            return options;
        });

        const params = {
            ruleRel: values.ruleRel,
            deducioActFlag: '0',
            effTime: values.effTime?.format?.('YYYY-MM-DD HH:mm:ss'),
            ruleFlag,
            ruleId: ruleId || '',
            operId: operId || mainInfo?.operId || selectRuleInfo?.operId,
            cooperationType: '01', // 抽成模式
            useTemplate: values.useTemplate,
            templateId: values.templateId,
            premiumFlag: values.premiumFlag,
            cooperationPlatform,
        };
        if (values.premiumFlag === '1') {
            params.divideDTOList = JSON.stringify(values.divideDTOList);
        }
        if (ruleFlag === '1') {
            params.profitRuleTypes = JSON.stringify(profitRuleTypes);
        }
        if (values.expTime) {
            params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
        }

        //判断没有有效期并且没有使用模板则是长期有效，不需要调用接口校验，否则需要调用接口校验
        if ((params.expTime || params.templateId) && values.premiumFlag === '1') {
            const verifyResult = await getProfitruleExpireTimeFlag({
                templateFlag: params.useTemplate,
                templateId: params.templateId,
                expTime: params.expTime,
                operId: params.operId,
                cooperationPlatform,
            });
            if (verifyResult?.data?.expireFlag !== '02') {
                Modal.confirm({
                    title: '平台定价',
                    type: 'warn',
                    content: '平台定价失效时间晚于规则失效时间的场站，平台定价将自动失效',
                    onOk: () => {
                        requestSaveApi(params);
                    },
                    onCancel: () => {
                        changeSubmitLoading(false);
                    },
                });
            } else {
                requestSaveApi(params);
            }
        } else {
            requestSaveApi(params);
        }
    };

    const requestSaveApi = async (params) => {
        if (onWorkorderFinish) {
            onWorkorderFinish(params);
            changeSubmitLoading(false);
            return;
        }

        try {
            if (params.templateId) {
                const templateParams = {
                    templateId: params.templateId,
                    operId: params.operId,
                    cooperationType: OPER_COOPERATION.TAKE,
                    premiumFlag: params.premiumFlag,
                    divideDTOList: params.divideDTOList,
                    cooperationPlatform,
                };
                await addStationTemplateApi(templateParams);
            } else {
                await saveprofitruleApi(params);
            }
            message.success('保存成功');
            history.go(-1);
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const changeExpFlagEvent = (event) => {
        const {
            target: { value },
        } = event;
        if (value === EXP_FLAG_TYPES.LONG) {
            form.setFieldsValue({ expTime: null });
        }
    };

    const disabledDate = (current) => {
        return current && current < moment().subtract(1, 'days').endOf('day');
    };

    return (
        <Card bordered={false}>
            <Form
                form={form}
                {...formItemLayout}
                onFinish={onFinish}
                initialValues={{
                    useTemplate: '01',
                    ruleRel: (mainInfo && mainInfo.ruleRel) || '01',
                    ruleFlag: (mainInfo && mainInfo.ruleFlag) || '0',
                    ruleTime: [
                        mainInfo && mainInfo.effTime
                            ? moment(mainInfo.effTime)
                            : moment().add(5, 'minutes'),
                        mainInfo && mainInfo.expTime ? moment(mainInfo.expTime) : null,
                    ],
                }}
                scrollToFirstError
            >
                <div className={styles.formTitle}>基础信息</div>
                <FormItem label="运营商名称:" {...formItemLayout}>
                    <Input
                        value={operName || (selectRuleInfo && selectRuleInfo.operName) || ''}
                        disabled
                    />
                </FormItem>
                <FormItem label="运营商编号:" {...formItemLayout}>
                    <Input
                        value={operId || (selectRuleInfo && selectRuleInfo.operId) || ''}
                        disabled
                    />
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues?.useTemplate !== curValues?.useTemplate
                    }
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            <FormItem
                                label={
                                    <span>
                                        使用模板
                                        <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                        </Tooltip>
                                    </span>
                                }
                                {...{
                                    labelCol: {
                                        span: 6,
                                    },
                                    wrapperCol: {
                                        span: 14,
                                    },
                                }}
                                name="useTemplate"
                                dependencies={['templateId']}
                                initialValue={'01'}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (value === '02') {
                                                const templateId = getFieldValue('templateId');
                                                if (!templateId) {
                                                    return Promise.reject('请选择模板');
                                                }
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <Space>
                                    <Radio.Group
                                        defaultValue={'01'}
                                        options={[
                                            {
                                                label: '否',
                                                value: '01',
                                            },
                                            {
                                                label: '是',
                                                value: '02',
                                            },
                                        ]}
                                    ></Radio.Group>
                                    {useTemplate === '02' && (
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                selectTemplateRef.current.show();
                                            }}
                                        >
                                            选择模板
                                        </Button>
                                    )}
                                </Space>
                            </FormItem>
                        );
                    }}
                </FormItem>
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues?.useTemplate !== curValues?.useTemplate
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            <>
                                {useTemplate === '02' && (
                                    <FormItem name="templateId" noStyle>
                                        <Input type="hidden" />
                                    </FormItem>
                                )}
                                <FormItem
                                    shouldUpdate={(prevValues, curValues) =>
                                        prevValues.useTemplate !== curValues.useTemplate ||
                                        prevValues._ruleType !== curValues._ruleType
                                    }
                                    noStyle
                                >
                                    {({ getFieldValue }) => {
                                        const useTemplate = getFieldValue('useTemplate');
                                        const _ruleType = getFieldValue('_ruleType');
                                        if (useTemplate === '02' && _ruleType != RULE_TYPES.CITY) {
                                            return (
                                                <TablePro
                                                    name="city"
                                                    loading={selectTemplateListLoading}
                                                    scroll={{ x: 'max-content' }}
                                                    rowKey="detailId"
                                                    dataSource={
                                                        selectTemplateInfo?.detailList || []
                                                    }
                                                    columns={childrenColumns}
                                                    pagination={false}
                                                    style={{ marginBottom: '16px' }}
                                                />
                                            );
                                        }
                                        return null;
                                    }}
                                </FormItem>
                            </>
                        );
                    }}
                </FormItem>
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        return useTemplate === '02' ? null : (
                            <Fragment>
                                <FormItem
                                    label="生效时间:"
                                    name="effTime"
                                    {...{
                                        labelCol: {
                                            span: 6,
                                        },
                                        wrapperCol: {
                                            span: 12,
                                        },
                                    }}
                                    rules={[
                                        { required: true, message: '请填写生效时间' },
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value) {
                                                    return Promise.reject('');
                                                }

                                                if (value && canISetEffTime) {
                                                    const nowTime = +new Date();
                                                    const sendEndTime = +new Date(value);

                                                    if (sendEndTime < nowTime) {
                                                        return Promise.reject(
                                                            '生效时间不能早于当前时间',
                                                        );
                                                    }
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <DatePicker
                                        disabled={!canISetEffTime}
                                        disabledDate={disabledDate}
                                        showTime={{
                                            format: 'HH:mm:ss',
                                            defaultValue: moment('00:00:00', 'HH:mm:ss'),
                                        }}
                                        format="YYYY-MM-DD HH:mm:ss"
                                        showNow={false}
                                        renderExtraFooter={(a) => (
                                            <Space>
                                                <Button
                                                    type="link"
                                                    onClick={() => {
                                                        const time = moment().add(10, 'seconds');
                                                        form.setFieldsValue({
                                                            effTime: time,
                                                        });
                                                    }}
                                                >
                                                    10秒之后
                                                </Button>
                                                <Button
                                                    type="link"
                                                    onClick={() => {
                                                        const effTime =
                                                            form.getFieldValue('effTime');
                                                        if (effTime) {
                                                            form.setFieldsValue({
                                                                effTime:
                                                                    moment(effTime).startOf('day'),
                                                            });
                                                        }
                                                    }}
                                                >
                                                    时间重置
                                                </Button>
                                            </Space>
                                        )}
                                    />
                                </FormItem>

                                <FormItem
                                    label="失效时间:"
                                    name="expFlag"
                                    initialValue="01"
                                    {...{
                                        labelCol: {
                                            span: 6,
                                        },
                                        wrapperCol: {
                                            span: 18,
                                        },
                                    }}
                                >
                                    <Radio.Group onChange={changeExpFlagEvent}>
                                        <Radio value={'01'}>长期</Radio>
                                        <Radio value={'02'}>
                                            <Space>
                                                指定时间
                                                <FormItem
                                                    shouldUpdate={(prevValues, curValues) =>
                                                        prevValues.expFlag !== curValues.expFlag ||
                                                        prevValues.effTime !== curValues.effTime
                                                    }
                                                    noStyle
                                                >
                                                    {({ getFieldValue }) => {
                                                        const expFlag = getFieldValue('expFlag');
                                                        const effTime = getFieldValue('effTime');

                                                        return (
                                                            <FormItem
                                                                name="expTime"
                                                                noStyle
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!effTime) {
                                                                                return Promise.reject(
                                                                                    '请选择生效开始时间',
                                                                                );
                                                                            }

                                                                            if (value) {
                                                                                const startTime =
                                                                                    +new Date(
                                                                                        effTime,
                                                                                    );
                                                                                const sendEndTime =
                                                                                    +new Date(
                                                                                        value,
                                                                                    );

                                                                                if (
                                                                                    sendEndTime <=
                                                                                    startTime
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        '失效时间不能早于生效时间',
                                                                                    );
                                                                                }
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <DatePicker
                                                                    disabledDate={disabledDate}
                                                                    disabled={expFlag != '02'}
                                                                    showTime={{
                                                                        format: 'HH:mm:ss',
                                                                        defaultValue: moment(
                                                                            '23:59:59',
                                                                            'HH:mm:ss',
                                                                        ),
                                                                    }}
                                                                    format="YYYY-MM-DD HH:mm:ss"
                                                                    showNow={false}
                                                                    renderExtraFooter={(a) => (
                                                                        <Space>
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() => {
                                                                                    const time =
                                                                                        moment().add(
                                                                                            10,
                                                                                            'seconds',
                                                                                        );
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            expTime:
                                                                                                time,
                                                                                        },
                                                                                    );
                                                                                }}
                                                                            >
                                                                                10秒之后
                                                                            </Button>
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() => {
                                                                                    const effTime =
                                                                                        form.getFieldValue(
                                                                                            'effTime',
                                                                                        );
                                                                                    if (effTime) {
                                                                                        form.setFieldsValue(
                                                                                            {
                                                                                                effTime:
                                                                                                    moment(
                                                                                                        effTime,
                                                                                                    ).startOf(
                                                                                                        'day',
                                                                                                    ),
                                                                                            },
                                                                                        );
                                                                                    }
                                                                                }}
                                                                            >
                                                                                时间重置
                                                                            </Button>
                                                                        </Space>
                                                                    )}
                                                                />
                                                            </FormItem>
                                                        );
                                                    }}
                                                </FormItem>
                                            </Space>
                                        </Radio>
                                    </Radio.Group>
                                </FormItem>
                                <RuleFormItem
                                    form={form}
                                    editRuleInfo={mainInfo}
                                    loading={getInfoLoading}
                                    commonRule
                                />
                            </Fragment>
                        );
                    }}
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues?.premiumFlag !== curValues?.premiumFlag
                    }
                >
                    {({ getFieldValue }) => {
                        const premiumFlag = getFieldValue('premiumFlag');
                        return (
                            <>
                                <FormItem name="premiumFlag" noStyle></FormItem>
                                <FormItem
                                    label="是否溢价"
                                    required
                                    tooltip="关闭后，不可配置平台定价，并且已设置的所有平台定价将自动失效"
                                >
                                    <Switch
                                        checkedChildren="开"
                                        unCheckedChildren="关"
                                        checked={premiumFlag === '1'}
                                        defaultChecked={false}
                                        onChange={(checked) => {
                                            if (!checked) {
                                                checkClosePremiumCommon(cooperationPlatform).then(
                                                    (result) => {
                                                        form.setFieldValue('premiumFlag', '0');
                                                    },
                                                );
                                            } else {
                                                form.setFieldValue('premiumFlag', '1');
                                            }
                                        }}
                                    />
                                </FormItem>
                                {premiumFlag === '1' && (
                                    <FormItem
                                        label=" "
                                        name="divideDTOList"
                                        wrapperCol={{ span: 16 }}
                                        colon={false}
                                        rules={[
                                            () => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject(`请选择溢价分成方式`);
                                                    }
                                                    for (const item of value) {
                                                        if (!isNumber(item?.divideValue)) {
                                                            return Promise.reject('请填写分成');
                                                        } else if (
                                                            Number(item?.divideValue) > 100 ||
                                                            Number(item?.divideValue) < 0
                                                        ) {
                                                            return Promise.reject(
                                                                '只能填写0.000～100.000之间的数字',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <PremiumSharingItem />
                                    </FormItem>
                                )}
                            </>
                        );
                    }}
                </FormItem>
                <FormItem noStyle name="ruleId"></FormItem>
                <div className={styles['form-submit']}>
                    <Button
                        className={styles['form-btn']}
                        type="primary"
                        htmlType="submit"
                        loading={submitLoading}
                    >
                        提交
                    </Button>
                    <Button className={styles['form-btn']} onClick={closeEvent}>
                        取消
                    </Button>
                </div>
            </Form>

            <SelectRuleModal onFinish={changeSelectEvent} ref={selectTemplateRef} />
        </Card>
    );
});

const ProftRuleDetailsPage = (props) => {
    const { history, location, route, match } = props;

    let ruleId = match.params.ruleId;
    let operName = location.query.operName;
    let operId = location.query.operId;

    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <ProftRuleDetailsView
                {...props}
                closeEvent={goBack}
                ruleId={ruleId}
                operName={operName}
                operId={operId}
            ></ProftRuleDetailsView>
        </PageHeaderWrapper>
    );
};
export default connect(({ profitRuleModel, loading }) => ({
    profitRuleModel,
    getInfoLoading: loading.effects['profitRuleModel/getRuleInfo'],
    selectTemplateListLoading: loading.effects['profitRuleModel/getSelectTemplateInfo'],
}))(ProftRuleDetailsPage);
