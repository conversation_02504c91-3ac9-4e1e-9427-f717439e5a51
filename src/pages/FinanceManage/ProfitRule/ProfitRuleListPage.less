@import '~antd/lib/style/themes/default.less';
.table-btn {
  margin: 0 10px;
  color: @primary-color;
  cursor: pointer;
}
.btnMargin {
  margin-left: 10px;
}
.btn-bar{
  margin-bottom:20px;
  .btn-item{
    margin-right:10px;
  }
}

.formTitle {
  margin-bottom:20px;
  color:#333;
  font-weight: bold;
  font-size: 20px;
}
.form-submit{
  text-align: center;
  .form-btn{
    margin:0 10px;
  }
}

.type-table{
  display: flex;
  align-items: center;
  width: 100%;
  margin:0;
  padding:0;
  border:1px solid #ccc;
  border-bottom: 0;
  &:last-child{
    border-bottom: 1px solid #ccc;
  }
  &>.type-table-item{
    border-right:1px solid #ccc !important;
  }
  .type-table-row{
      width:100%;

  }

  .type-table-item{
    display: flex;
    align-items: center;
    height: 40px;
    padding:10px 20px;
    // &:not(:last-child){
    //   border-right:1px solid #ccc;
    // }

    &.item-model{
        flex-shrink:0;
      width:220px;
    }
    :global{
      .ant-input-number{
        width: 100px;
        margin:0 10px;
      }
    }

  }

}
