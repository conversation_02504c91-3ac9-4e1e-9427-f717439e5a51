import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Col, Form, Row, Select, Space, Tabs } from 'antd';
import { useEffect, useRef, useMemo, useState } from 'react';
import { connect, Link } from 'umi';

import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import styles from './ProfitRuleListPage.less';
import { TemplateListModal } from './ProfitRuleTemplateListPage';
import { exportTableByParams } from '@/utils/utils';
import { getProfitRuleListPath } from '@/services/FinanceManage/ProfitRuleApi';
import SelectCooperationPlatform from '@/components/SelectCooperationPlatform/Radio';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { Option } = Select;

const STATUS_TYPES = {
    ALL: '00',
    NOTCONF: '01',
    USE: '02',
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        global: { codeInfo },
    } = props;

    const { ruleModel: ruleModelList } = codeInfo;

    useEffect(() => {
        if (!ruleModelList) {
            dispatch({
                type: 'global/initCode',
                code: 'ruleModel',
            });
        }
    }, []);

    const ruleModelListOptions = useMemo(() => {
        if (ruleModelList) {
            return ruleModelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [ruleModelList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <OperSelectTypeItem {...formItemLayout} form={form} />
                </Col>
                <Col span={8}>
                    <FormItem label="分润方式" name="ruleModel">
                        <Select mode="multiple" allowClear placeholder="请选择" {...formItemLayout}>
                            {ruleModelListOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="运营商状态" name="statusBackendCode" initialValue={'01'}>
                        <Select allowClear placeholder="请选择">
                            <Option value="01">正常</Option>
                            <Option value="02">废弃</Option>
                        </Select>
                    </FormItem>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const ProfitRuleListLayout = (props) => {
    const {
        dispatch,
        history,
        profitRuleModel: { profitRuleList, profitRuleListTotal },
        global: { pageInit },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const templateRef = useRef();

    const [pageInfo, changePageInfo, onTableChang] = usePageState(
        {
            pageSize: 50,
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            statusBackendCode: data.statusBackendCode,
            operId: data.operId || '',
            ruleModel: data.ruleModel ? data.ruleModel.join(',') : '',
            cooperationType: '01', // 抽成模式
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.ruleStatus = pageInfo.tabType;
        }

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'profitRuleModel/profitRuleList',
            options: params,
        });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            statusBackendCode: data.statusBackendCode,
            operId: data.operId || '',
            ruleModel: data.ruleModel ? data.ruleModel.join(',') : '',
            cooperationType: '01', // 抽成模式
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.ruleStatus = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getProfitRuleListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const updateRuleEvent = (item) => {
        dispatch({
            type: 'profitRuleModel/setSelectRuleInfo',
            info: item,
        });
        if (item.ruleId) {
            history.push(
                `/sellerCenter/operatormanage/profitRule/list/details/${item.ruleId}?cooperationPlatform=${cooperationPlatform}`,
                {
                    cooperationPlatform,
                },
            );
        } else {
            history.push(
                `/sellerCenter/operatormanage/profitRule/list/details?cooperationPlatform=${cooperationPlatform}`,
                {
                    cooperationPlatform,
                },
            );
        }
    };
    const editStationRuleEvent = (item) => {
        history.push(
            `/sellerCenter/operatormanage/profitRule/list/stationlist/${item.operId}?cooperationPlatform=${cooperationPlatform}`,
            {
                cooperationPlatform,
            },
        );
    };
    // 配置城市列表
    const editCityRuleEvent = (item) => {
        history.push(
            `/sellerCenter/operatormanage/profitRule/list/citylist/${item.operId}?cooperationPlatform=${cooperationPlatform}`,
            {
                cooperationPlatform,
            },
        );
    };

    const openEditHistoryEvent = () => {
        history.push(
            `/sellerCenter/operatormanage/profitRule/list/editHistory?cooperationPlatform=${cooperationPlatform}`,
            {
                cooperationPlatform,
            },
        );
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operNickName',
            render(text, record) {
                return (
                    <div
                        className="text-line"
                        title={text}
                        style={{ width: '140px', color: record.dayexpflag == '1' ? 'red' : '' }}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '平台',
            width: 140,
            dataIndex: 'cooperationPlatformName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分润方式',
            width: 440,
            dataIndex: 'ruleName',

            render(text, record) {
                return (
                    <div
                        style={{
                            whiteSpace: 'pre',
                            width: '240px',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '分润关系',
            width: 140,
            dataIndex: 'ruleRelName',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '溢价分成规则',
            width: 200,
            dataIndex: 'premiumString',
        },
        {
            title: '生效时间',
            width: 200,
            dataIndex: 'effTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '失效时间',
            width: 200,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {(text?.length && text) || record.maxExpTime}
                    </span>
                );
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'ruleStatusName',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '所属模板',
            width: 200,
            dataIndex: 'templateName',
            render(text = '-', record) {
                if (record.templateId) {
                    return (
                        <a
                            onClick={() => {
                                templateRef.current?.show(record.templateId);
                            }}
                        >
                            {text}
                        </a>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 260,
            render: (text, record) => (
                <Space>
                    <span className={styles['table-btn']} onClick={() => updateRuleEvent(record)}>
                        编辑
                    </span>
                    {record.ruleStatus == '02' ? (
                        <>
                            <span
                                className={styles['table-btn']}
                                onClick={() => editCityRuleEvent(record)}
                            >
                                配置城市
                            </span>

                            <span
                                className={styles['table-btn']}
                                onClick={() => editStationRuleEvent(record)}
                            >
                                配置场站
                            </span>
                        </>
                    ) : null}
                </Space>
            ),
        },
    ];

    const [cooperationPlatform, updateCooperationPlatform] = useState(() => {
        const oldVal = sessionStorage.getItem('cooperationPlatform');
        if (oldVal) {
            return oldVal;
        }
        return COOPERATION_PLATFORM_TYPES.XDT;
    });

    return (
        <Card>
            <SelectCooperationPlatform
                value={cooperationPlatform}
                onChange={(newVal) => {
                    updateCooperationPlatform(newVal);
                    sessionStorage.setItem('cooperationPlatform', newVal);
                    changePageInfo({ pageIndex: 1 });
                }}
                fieldProps={{
                    optionType: 'button',
                    buttonStyle: 'solid',
                }}
            ></SelectCooperationPlatform>
            <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                <TabPane tab="待配置" key={STATUS_TYPES.NOTCONF} />
                <TabPane tab="使用中" key={STATUS_TYPES.USE} />
            </Tabs>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />
            <Space className={styles['btn-bar']} style={{ marginTop: '12px' }}>
                <Button className={styles['btn-item']} onClick={openEditHistoryEvent}>
                    配置记录
                </Button>
                <Button>
                    <Link
                        to={`/sellerCenter/operatormanage/profitRule/list/templateManage`}
                        target="_blank"
                    >
                        模板管理
                    </Link>
                </Button>
            </Space>
            <br></br>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="operId"
                dataSource={profitRuleList}
                columns={columns}
                onChange={onTableChang}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: profitRuleListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                    pageSizeOptions: [50, 100],
                }}
                tabType={pageInfo.tabType}
            />

            <TemplateListModal initRef={templateRef} />
        </Card>
    );
};

const ProfitRulePage = (props) => {
    return (
        <PageHeaderWrapper>
            <ProfitRuleListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ profitRuleModel, global, loading }) => ({
    profitRuleModel,
    global,
    listLoading: loading.effects['profitRuleModel/profitRuleList'],
}))(ProfitRulePage);
