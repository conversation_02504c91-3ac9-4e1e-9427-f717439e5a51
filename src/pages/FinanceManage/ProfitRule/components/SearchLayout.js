import { Col, DatePicker, Form, Input, Select } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

import { getCityAndStationByOperIdApi } from '@/services/CommonApi';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { OPER_COOPERATION } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import useCooperationPlatform from '@/hooks/useCooperationPlatform';

export default (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        operId,
        global: { operatorList, codeInfo },
        profitRuleModel: { templateSelectList },
        listLoading,
    } = props;
    const [cityList, changeCityList] = useState([]);
    const [searchTxt, changeSearchTxt] = useState(null);

    const { cooperationPlatform } = useCooperationPlatform();

    const { ruleModel: ruleModelList } = codeInfo;

    useEffect(() => {
        initCityEvent();

        if (isEmpty(ruleModelList)) {
            dispatch({
                type: 'global/initCode',
                code: 'ruleModel',
            });
        }

        dispatch({
            type: 'profitRuleModel/getSelectTemplateList',
            cooperationType: OPER_COOPERATION.TAKE,
        });
    }, []);

    const ruleModelListOptions = useMemo(() => {
        if (ruleModelList) {
            return ruleModelList.map((ele) => (
                <Select.Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Select.Option>
            ));
        }
        return [];
    }, [ruleModelList]);

    const selectTemplateOptions = useMemo(() => {
        return templateSelectList.map((ele) => {
            return (
                <Select.Option value={ele.templateId} key={ele.templateId}>
                    {ele.name}
                </Select.Option>
            );
        });
    }, [templateSelectList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const operGroupRef = useRef();
    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const initCityEvent = async () => {
        try {
            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                operId,
                cooperationType: '01',
                cooperationPlatform,
            });
            changeCityList(cityAndStationList);
        } catch (error) {
        } finally {
        }
    };

    const handleSearch = (txt) => {
        changeSearchTxt(txt);
    };

    const handleFilter = (filterTxt, option) => true;

    const cityOptions = useMemo(() => {
        if (searchTxt) {
            const list = [];
            for (const item of cityList) {
                const searchName = item.title;
                if (searchName.indexOf(searchTxt) >= 0) {
                    list.push(item);
                }
            }
            return list.map((ele) => (
                <Select.Option key={ele.codeValue} value={ele.value}>
                    {ele.title}
                </Select.Option>
            ));
        }
        return cityList.map((ele) => (
            <Select.Option key={ele.codeValue} value={ele.value}>
                {ele.title}
            </Select.Option>
        ));
    }, [cityList, searchTxt]);

    return (
        <Form form={form} onFinish={onFinish} initialValues={{ isAbandon: '0' }} scrollToFirstError>
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                onSearchGroup={onSearchGroup}
                // open
            >
                <Col span={8}>
                    <Form.Item label="所选城市:" name="city">
                        <Select
                            showSearch
                            mode="multiple"
                            allowClear
                            onSearch={handleSearch}
                            filterOption={handleFilter}
                            maxTagCount={2}
                            placeholder="请选择"
                        >
                            {cityOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="场站名称:" name="stationName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="分润方式" name="ruleModel">
                        <Select mode="multiple" allowClear placeholder="请选择">
                            {ruleModelListOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="选择模板" name="templateIds">
                        <Select mode="multiple" allowClear placeholder="请选择">
                            {selectTemplateOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="生效时间" name="effTime">
                        <DatePicker.RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="失效时间" name="expTime">
                        <DatePicker.RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="是否废弃" name="stationBusiStatus">
                        <Select allowClear placeholder="请选择">
                            <Select.Option value="1">是</Select.Option>
                            <Select.Option value="0">否</Select.Option>
                        </Select>
                    </Form.Item>
                </Col>
                <Form.Item noStyle name="stationIdList" />
            </SearchOptionsBar>
            <OperGroupImportModal
                title="批量查询"
                buildId={operId}
                initRef={operGroupRef}
                onConfirm={(addStationList) => {
                    const list =
                        (addStationList?.length && addStationList.map((item) => item.stationId)) ||
                        [];
                    form.setFieldsValue({ stationIdList: list });
                    const values = form.getFieldsValue();
                    onSubmit(values);
                }}
            />
        </Form>
    );
};
