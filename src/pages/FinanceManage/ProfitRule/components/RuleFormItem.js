import React, { Fragment, useState, useEffect } from 'react';
import { Form, Radio, Switch } from 'antd';

import styles from '../ProfitRuleListPage.less';
import RuleModelFormItem from './RuleModelFormItem';

const FormItem = Form.Item;

export const RuleFormItem = (props) => {
    const {
        form,
        loading,
        editRuleInfo = {},
        disabled,
        formItemLayout,
        titleFormItemLayout,
        commonRule,
    } = props;

    const [hasProfit, changeHasProfit] = useState(true);

    useEffect(() => {
        if (editRuleInfo.ruleFlag == '1' || editRuleInfo?.ruleFlag === undefined) {
            changeHasProfitEvent(true);
        } else {
            changeHasProfitEvent(false);
        }
    }, [editRuleInfo.ruleFlag]);

    const options = [{ label: '多种方式共同分润', value: '01' }];
    const changeHasProfitEvent = (value) => {
        if (!value) {
            form.setFieldsValue({
                ruleFlag: '0',
                ruleRel: '',
                deducioActFlag: '0',
                profitRuleTypeList: [],
            });
        } else {
            form.setFieldsValue({ ruleFlag: '1', ruleRel: '01' });
        }

        changeHasProfit(value);
    };
    return (
        <Fragment>
            <FormItem
                shouldUpdate={(prevValues, curValues) => prevValues.ruleFlag !== curValues.ruleFlag}
                noStyle
            >
                {({ getFieldValue }) => {
                    const ruleFlag = getFieldValue('ruleFlag');
                    changeHasProfit(ruleFlag == '1');
                    return (
                        <FormItem
                            label="分润方式:"
                            {...(titleFormItemLayout || {
                                labelCol: {
                                    span: 6,
                                },
                                wrapperCol: {
                                    span: 16,
                                },
                            })}
                            required
                        >
                            <div className={styles['btn-bar']} style={{ marginTop: '6px' }}>
                                <Switch
                                    disabled={disabled}
                                    loading={loading}
                                    checked={ruleFlag == '1'}
                                    onChange={changeHasProfitEvent}
                                />
                            </div>
                        </FormItem>
                    );
                }}
            </FormItem>
            {hasProfit ? (
                <Fragment>
                    <RuleModelFormItem
                        disabled={disabled}
                        label=""
                        name="profitRuleList"
                        formItemLayout={formItemLayout || { wrapperCol: { span: 18, offset: 6 } }}
                        initialAgreeType={commonRule ? '01' : '02'}
                        form={form}
                    />
                    <FormItem
                        label="分润关系"
                        name="ruleRel"
                        {...(titleFormItemLayout || {
                            labelCol: {
                                span: 6,
                            },
                            wrapperCol: {
                                span: 16,
                            },
                        })}
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group disabled={disabled} options={options} />
                    </FormItem>
                </Fragment>
            ) : null}
        </Fragment>
    );
};
