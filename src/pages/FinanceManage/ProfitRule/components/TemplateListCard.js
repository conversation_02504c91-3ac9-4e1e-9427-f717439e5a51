import {
    Button,
    Card,
    Col,
    Form,
    message,
    Modal,
    Row,
    Input,
    Table,
    Tabs,
    Popconfirm,
    Typography,
    Space,
} from 'antd';
import moment from 'moment';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { connect } from 'umi';

import styles from '../ProfitRuleListPage.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import TemplateListFormItem from './TemplateListFormItem';
import { initialRuleList } from './RuleModelFormItem';
import { isEmpty } from '@/utils/utils';
import {
    deleteRuleTemplateApi,
    addRuleTemplateApi,
    updateRuleTemplateApi,
} from '@/services/FinanceManage/ProfitRuleApi';
import { OPER_COOPERATION } from '@/config/declare';
import { childrenColumns } from '../ProfitRuleTemplateListPage';

const { TabPane } = Tabs;
const FormItem = Form.Item;

const STATUS_TYPES = {
    ALL: '00',
    INEFFECTIVE: '01',
    USE: '02', // 使用中
    LOSE: '03', // 失效
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError>
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="模板名称:" name="name">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="模板id:" name="templateNo">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>

                <Col>
                    <Button type="primary" htmlType="submit">
                        查询
                    </Button>
                    <Button className={styles.btnMargin} onClick={resetForm}>
                        重置
                    </Button>
                </Col>
            </Row>
        </Form>
    );
};

const TemplateListCard = (props) => {
    const {
        dispatch,
        profitRuleModel: { ruleTemplateList, ruleTemplateListTotal, editTemplateInfo },
        listLoading,
        infoLoading,
        global: { codeInfo },
        canSelect = false,
        onSelect,
        pageSize = 50,
    } = props;

    const { templateStatus: templateStatusList } = codeInfo;

    const [form] = Form.useForm();

    const [templateForm] = Form.useForm();

    const [showEditView, toggleShowEditView] = useState(false);
    const [showAddView, toggleShowAddView] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: pageSize,
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        if (!templateStatusList) {
            dispatch({
                type: 'global/initCode',
                code: 'templateStatus',
            });
        }
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (editTemplateInfo && showEditView) {
            templateForm.setFieldsValue({
                templateId: editTemplateInfo.templateId,
                ...processRuleForm(editTemplateInfo),
            });
        } else if (editTemplateInfo && showAddView) {
            templateForm.setFieldsValue({
                ...processRuleForm(editTemplateInfo),
            });
        }
    }, [editTemplateInfo, showEditView, showAddView]);

    const processRuleForm = (record) => {
        const params = {
            name: record.name,
            detailList: record.detailList.map((ele) => {
                const { profitRuleTypeList } = ele;
                const item = {
                    effTime: moment(ele.effTime),
                    expTime: moment(ele.expTime),
                    ruleFlag: ele.ruleFlag === '1',
                    ruleRel: ele.ruleRel || '',
                    sn: ele.sn,
                };
                if (profitRuleTypeList) {
                    item.profitRuleList = initialRuleList.map((template) => {
                        const findItem = profitRuleTypeList.find(
                            (info) => info.ruleModel === template.ruleModel,
                        );
                        if (findItem) {
                            return {
                                ruleModel: [findItem.ruleModel],
                                ruleType: findItem.ruleType,
                                ruleValue: parseFloat(findItem.ruleValue),
                                agreeType: findItem.agreeType,
                                longRuleValue: parseFloat(findItem.longRuleValue),
                                shortRuleValue: parseFloat(findItem.shortRuleValue),
                            };
                        }
                        return null;
                    });
                }
                return item;
            }),
        };
        return params;
    };

    const templateStatusOptions = useMemo(() => {
        if (templateStatusList) {
            return templateStatusList.map((ele) => (
                <TabPane tab={ele.codeName} key={ele.codeValue} />
            ));
        }
        return [];
    }, [templateStatusList]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            name: data.name || '',
            templateNo: data.templateNo || '',
            cooperationType: OPER_COOPERATION.TAKE,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.status = pageInfo.tabType;
        }
        dispatch({
            type: 'profitRuleModel/getRuleTemplateList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };
    const openAddTemplateView = () => {
        templateForm.resetFields();
        toggleShowEditView(true);
    };

    const delTemplateById = async (item) => {
        try {
            await deleteRuleTemplateApi(item.templateId);
            searchData();
            message.success('删除成功');
        } catch (error) {
            message.error('删除失败');
            return Promise.reject(error);
        }
    };
    const editItemEvent = (item) => {
        openTemplateDetailsView(item);
        openEditView();
    };
    const openTemplateDetailsView = (item) => {
        dispatch({
            type: 'profitRuleModel/getTemplateInfo',
            templateId: item.templateId,
        });
        openEditView();
    };
    const openEditView = () => {
        toggleShowEditView(true);
    };
    const closeEditView = () => {
        toggleShowEditView(false);
        toggleShowAddView(false);
        dispatch({
            type: 'profitRuleModel/updateProfitTemplateInfo',
            editTemplateInfo: null,
        });
    };

    const onCopy = (item) => {
        templateForm.resetFields();
        dispatch({
            type: 'profitRuleModel/getTemplateInfo',
            templateId: item?.templateId,
        });
        toggleShowAddView(true);
    };

    const columns = [
        {
            title: '创建时间',
            width: 140,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板ID',
            width: 140,
            dataIndex: 'templateNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板名称',
            width: 140,
            dataIndex: 'name',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板生效时间',
            width: 140,
            dataIndex: 'effTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板失效时间',
            width: 140,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 180,
            render: (text, record) => {
                return (
                    <Space>
                        {canSelect && record.status == '01' && (
                            <Popconfirm
                                title="确认选择这个模板吗？"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => {
                                    onSelect && onSelect(record?.templateId);
                                }}
                            >
                                <Typography.Link>选择</Typography.Link>
                            </Popconfirm>
                        )}
                        {record.status == '01' && (
                            <Typography.Link onClick={() => editItemEvent(record)}>
                                编辑
                            </Typography.Link>
                        )}
                        {record.status == '01' && (
                            <Popconfirm
                                title="确认删除？"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => delTemplateById(record)}
                            >
                                <Typography.Link>删除</Typography.Link>
                            </Popconfirm>
                        )}
                        <Typography.Link
                            onClick={() => {
                                onCopy(record);
                            }}
                        >
                            复制
                        </Typography.Link>
                    </Space>
                );
            },
        },
    ];

    const expandedRowRender = useCallback(
        (childrenItem, childrenIndex, indent, expanded) => {
            return (
                <div style={{ padding: '0 170px' }}>
                    <Table
                        dataSource={(childrenItem && childrenItem.detailList) || []}
                        columns={childrenColumns}
                        pagination={false}
                    />
                </div>
            );
        },
        [ruleTemplateList],
    );

    const onFinish = async (values) => {
        try {
            const param = {
                name: values.name,
                cooperationType: OPER_COOPERATION.TAKE,
            };
            const detailList = values.detailList.map((ele, index) => {
                const profitRuleList = ele.profitRuleList || [];

                const filterRuleList =
                    profitRuleList.filter((item) => item && !isEmpty(item.ruleModel)) || [];

                const profitRuleTypes = filterRuleList.map((item) => {
                    let options = {
                        ...item,
                        ruleModel: String(item.ruleModel),
                    };
                    if (item.agreeType instanceof Array && item.agreeType.length === 1) {
                        if (item.agreeType.includes('01')) {
                            options.longRuleValue = item.ruleValue;
                        }
                        if (item.agreeType.includes('02')) {
                            options.shortRuleValue = item.ruleValue;
                        }
                    }

                    return options;
                });
                const options = {
                    effTime: (ele.effTime && ele.effTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    expTime: (ele.expTime && ele.expTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    ruleFlag: ele.ruleFlag ? '1' : '0',
                    sn: ele.sn || index + 1,
                    ruleRel: ele.ruleRel,
                };
                if (options.ruleFlag === '1') {
                    options.profitRuleTypes = profitRuleTypes;
                }
                return options;
            });
            param.detailJsonStr = JSON.stringify(detailList);
            if (editTemplateInfo && editTemplateInfo.templateId && showEditView) {
                param.templateId = editTemplateInfo.templateId;
                await updateRuleTemplateApi(param);
                message.success('编辑成功');
            } else {
                const result = await addRuleTemplateApi(param);
                message.success('新建成功');
                if (canSelect) {
                    onSelect && onSelect(result?.data?.templateId);
                }
            }

            searchData();
            closeEditView();
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <>
            <Card bordered={false}>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={openAddTemplateView}>
                        新建模板
                    </Button>
                </div>
                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    {templateStatusOptions}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.templateId}
                    expandable={{ defaultExpandAllRows: true, expandedRowRender }}
                    dataSource={ruleTemplateList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: ruleTemplateListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [10, 20, 50, 100],
                    }}
                    noSort
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={showAddView ? '新建模板' : '编辑模板'}
                width={1000}
                visible={showEditView || showAddView}
                onCancel={closeEditView}
                destroyOnClose
                footer={null}
                maskClosable={false}
            >
                <Card loading={infoLoading} bordered={false}>
                    <Form
                        form={templateForm}
                        onFinish={onFinish}
                        initialValues={{
                            detailList: [{}, {}],
                        }}
                        scrollToFirstError
                    >
                        <div
                            className="overscroll"
                            style={{
                                maxHeight: `${document.body.clientHeight - 270}px`,
                                overflowY: 'auto',
                            }}
                        >
                            <TemplateListFormItem
                                form={templateForm}
                                name="detailList"
                                templateInfo={editTemplateInfo}
                                cooperationType={OPER_COOPERATION.TAKE}
                                isEdit={!!editTemplateInfo && showEditView}
                                isAdd={showAddView}
                            />
                        </div>
                        <div className={styles['form-submit']}>
                            <Button className={styles['form-btn']} type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button className={styles['form-btn']} onClick={closeEditView}>
                                取消
                            </Button>
                        </div>
                    </Form>
                </Card>
            </Modal>
        </>
    );
};

export default connect(({ profitRuleModel, global, loading }) => ({
    profitRuleModel,
    global,
    listLoading: loading.effects['profitRuleModel/getRuleTemplateList'],
    infoLoading: loading.effects['profitRuleModel/getTemplateInfo'],
}))(TemplateListCard);
