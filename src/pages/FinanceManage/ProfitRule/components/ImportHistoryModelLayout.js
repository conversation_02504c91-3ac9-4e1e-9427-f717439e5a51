import { Form, Modal, Select } from 'antd';
import { Fragment, useEffect } from 'react';

import styles from '../ProfitRuleListPage.less';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState';
import { downloadProfitRuleApi } from '@/services/FinanceManage/ProfitRuleApi';

const IMPORT_RESULT_TYPES = {
    SUCCESS: '01', // 成功
    FAIL: '02', // 失败
};

export default (props) => {
    const {
        dispatch,

        importHistoryLoading,
        profitRuleModel: { importHistoryList, importHistoryListTotal },
        onCancel,
        show,
        cooperationPlatform,
    } = props;

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        importRet: IMPORT_RESULT_TYPES.FAIL,
    });

    const [form] = Form.useForm();

    useEffect(() => {
        if (pageInfo.importRet != IMPORT_RESULT_TYPES.FAIL) {
            changePageInfo({
                importRet: IMPORT_RESULT_TYPES.FAIL,
            });
        }
    }, [show]);

    useEffect(() => {
        if (show) {
            searchData();
        }
    }, [show, pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            importRet: pageInfo.importRet,
            cooperationPlatform,
        };

        dispatch({
            type: 'profitRuleModel/getImportHistoryList',
            options: params,
        });
    };

    const changeImportRetEvent = (type) => {
        changePageInfo({ importRet: type, pageIndex: 1 });
    };

    const downLoadFailFile = (item) => {
        downloadProfitRuleApi(item.importTime);
    };

    const columns = [
        {
            title: '导入时间',
            dataIndex: 'importTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '导入数量',
            width: 140,
            dataIndex: 'importCount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '导入结果',
            dataIndex: 'importRetName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '下载失败文件',
            width: 160,
            dataIndex: 'stationName',
            render(text, record) {
                return record.importRet == '02' ? (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            downLoadFailFile(record);
                        }}
                    >
                        下载
                    </span>
                ) : null;
            },
        },
    ];

    return (
        <Fragment>
            <Modal
                title="导入历史"
                width={800}
                visible={show}
                onCancel={onCancel}
                footer={null}
                zIndex={1005}
                maskClosable={false}
            >
                <Form form={form} layout="inline" style={{ marginBottom: '20px' }}>
                    <Form.Item label="导入结果">
                        <Select
                            value={pageInfo.importRet}
                            placeholder="请选择"
                            onChange={changeImportRetEvent}
                        >
                            <Select.Option value={IMPORT_RESULT_TYPES.SUCCESS}>成功</Select.Option>
                            <Select.Option value={IMPORT_RESULT_TYPES.FAIL}>失败</Select.Option>
                        </Select>
                    </Form.Item>
                </Form>
                <TablePro
                    name="import"
                    loading={importHistoryLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={importHistoryList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: importHistoryListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [50, 100],
                    }}
                    tabType={pageInfo.importRet}
                />
            </Modal>
        </Fragment>
    );
};
