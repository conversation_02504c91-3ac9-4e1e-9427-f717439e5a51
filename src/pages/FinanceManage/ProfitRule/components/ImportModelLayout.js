import { downloadProfitRuleApi, importProfitRuleApi } from '@/services/FinanceManage/ProfitRuleApi';
import { Button, Form, Modal, message, Upload, Result } from 'antd';
import { useEffect, useState } from 'react';
import styles from '../ProfitRuleListPage.less';

export default (props) => {
    const { visible, onCancel, onRefresh, cooperationPlatform } = props;
    const [uploadLoading, changeUploadLoading] = useState(false);
    const [showResult, changeShowResult] = useState(false); // 显示上传结果
    const [uploadStatus, changeUploadStatus] = useState(false); // 上传状态
    const [resultMsg, changeResultMsg] = useState(''); // 上传结果
    const [errorImportTime, setErrorImportTime] = useState(null); // 导入失败文件标识

    useEffect(
        () => () => {
            changeShowResult(false);
            changeResultMsg('');
        },
        [],
    );

    const downLoadFile = () => {
        const a = document.createElement('a');
        a.setAttribute('href', '/everest/excelTemplate/profitRuleImport.xlsx');
        a.setAttribute('id', 'ruleTemplate');
        // 防止反复添加
        if (document.getElementById('ruleTemplate')) {
            document.body.removeChild(document.getElementById('ruleTemplate'));
        }
        document.body.appendChild(a);
        a.click();
    };

    const downErrorFileEvent = () => {
        downloadProfitRuleApi(errorImportTime);
    };

    const closeModalEvent = () => {
        changeShowResult(false);
        onCancel();
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isXls =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
            if (!isXls) {
                message.error('只支持.xls或.xlsx文件!');
                rej();
                return;
            }

            res();
        });
    const handleUploadChange = (info) => {
        onRefresh();
    };
    const uploadOptions = {
        name: 'profitRule',
        showUploadList: false,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const formData = new FormData();

                formData.append(filename, file);
                formData.append('cooperationPlatform', cooperationPlatform);
                changeUploadLoading(true);
                const {
                    data: { msg, ret, importTime },
                } = await importProfitRuleApi(formData);
                changeUploadLoading(false);
                changeUploadStatus(ret !== 'false');
                setErrorImportTime(importTime);
                message.success(msg);

                onSuccess();
                changeShowResult(true);
                changeResultMsg(msg);
            } catch (error) {
                if (error && error.msg) {
                    message.error(error.msg);
                }
            } finally {
                changeUploadLoading(false);
            }
            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };
    return (
        <Modal
            title="导入配置规则"
            width={400}
            visible={visible}
            onCancel={closeModalEvent}
            footer={null}
            maskClosable={false}
        >
            {!showResult ? (
                <Form>
                    <Form.Item label="下载模板">
                        <Button type="primary" onClick={downLoadFile}>
                            下载导入模板
                        </Button>
                    </Form.Item>
                    <Form.Item label="上传文件">
                        <Upload {...uploadOptions}>
                            <Button loading={uploadLoading} type="primary">
                                上传文件
                            </Button>
                        </Upload>
                        <div>支持扩展名：.xls .xlsx</div>
                    </Form.Item>
                </Form>
            ) : uploadStatus ? (
                <Result status="success" title={resultMsg} />
            ) : (
                <Result
                    status="error"
                    title={
                        <div style={{ textAlign: 'center' }}>
                            <p>导入文件存在错误请</p>
                            <p>
                                <span className={styles['table-btn']} onClick={downErrorFileEvent}>
                                    下载失败文件
                                </span>
                            </p>
                        </div>
                    }
                />
            )}
        </Modal>
    );
};
