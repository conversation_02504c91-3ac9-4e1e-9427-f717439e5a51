import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Card, Form, Alert, Space, Tooltip, Radio, Input, Switch, Modal } from 'antd';
import { isNumber } from 'lodash';
import moment from 'moment';
import { Fragment, useState, useEffect, useMemo, useRef } from 'react';

import styles from '../ProfitRuleListPage.less';
import { RULE_TYPES, OPER_COOPERATION } from '@/config/declare';
import { isEmpty, checkClosePremiumCommon } from '@/utils/utils';
import { getStationCityProfitRuleApi } from '@/services/FinanceManage/CityRuleApi';
import TablePro from '@/components/TablePro';
import { RuleFormItem } from './RuleFormItem';
import { initialRuleList } from './RuleModelFormItem';
import EditRuleLayout from './EditRuleLayout';
import SelectRuleModal from './SelectRuleModal';
import PremiumSharingItem from './PremiumSharingItem';
import { queryProfitrulePremiumFlag, getRuleInfoApi } from '@/services/FinanceManage/ProfitRuleApi';

const FormItem = Form.Item;

const modelFormLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};

const templateColumns = [
    {
        title: '序号 ',
        width: 80,
        render(text, record, index) {
            return <span title={index}>{index + 1}</span>;
        },
    },
    {
        title: '分润方式',
        width: 460,
        dataIndex: 'ruleName',
        render(text, record) {
            return (
                <span
                    title={text}
                    style={{ whiteSpace: 'pre', color: record.dayexpflag == '1' ? 'red' : '' }}
                >
                    {text}
                </span>
            );
        },
    },
    {
        title: '生效时间',
        width: 200,
        dataIndex: 'effTime',
        render(text, record) {
            return (
                <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                    {text}
                </span>
            );
        },
    },
    {
        title: '失效时间',
        width: 200,
        dataIndex: 'expTime',
        render(text, record) {
            return (
                <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                    {(text?.length && text) || record.maxExpTime}
                </span>
            );
        },
    },
];

const EditModelLayout = (props) => {
    const {
        dispatch,
        operId,
        multiple,
        onFinish,
        loading,
        submitLoading,
        onCancel,
        selectTemplateListLoading,
        profitRuleModel = {},
        editItems,
        resetEvent,
        confirmCityEvent,
        addCityRuleEvent, // 新增城市
        cooperationPlatform,
    } = props;

    const { selectTemplateInfo, editRuleInfo } = profitRuleModel;
    const [editForm] = Form.useForm();
    const selectTemplateRef = useRef();
    const [editRuleType, changeEditRuleType] = useState(RULE_TYPES.COMMON);
    const [premiumFlagEnable, setPremiumFlagEnable] = useState(false); //是否可以编辑溢价分成

    useEffect(async () => {
        if (editRuleType === RULE_TYPES.STATION) {
            const params = {
                profitRuleType: '02',
                operId: editItems[0]?.operId || operId,
                stationIds: editItems?.map((v) => v.stationId)?.join(','),
                cooperationType: '01',
                cooperationPlatform,
            };
            const result = await queryProfitrulePremiumFlag(params);

            const {
                data: { list },
            } = await getRuleInfoApi({
                profitRuleType: '01',
                operId: editItems[0]?.operId || operId,
                cooperationPlatform,
            });
            if (result?.data?.premiumFlag === '01') {
                setPremiumFlagEnable(true);
                if (editItems.length > 1) {
                    //场站批量配置时 是否可以配置分润溢价取运营商通用是否开启开启就默认开

                    let options = {
                        premiumFlag: '1',
                    };
                    if (list[0]) {
                        options.divideDTOList = list[0].divideList?.map((v) => ({
                            divideType: v.divideType,
                            divideValue: v.divideValue,
                        }));
                    }
                    editForm.setFieldsValue(options);
                }
            } else {
                setPremiumFlagEnable(false);
            }
        }
    }, [editItems, editRuleType]);

    // 主要信息以editRuleInfo.list的首个元素为准
    const mainInfo = editRuleInfo?.list?.[0];

    // 规则场站
    const [allStations, updateAllStations] = useState([]);
    useEffect(() => {
        if (mainInfo?.ruleRange == '02' && editRuleInfo?.list?.length) {
            // 如果默认是部分，维护进规则场站列表
            updateAllStations([...editRuleInfo.list]);
        }
    }, [mainInfo?.ruleRange]);

    const isCommonRule = useMemo(() => {
        if (editRuleType === RULE_TYPES.COMMON) {
            return true;
        }
        return false;
    }, [editRuleType]);

    const selectTemplateDetailList = useMemo(() => {
        if (selectTemplateInfo) {
            return selectTemplateInfo.detailList;
        }
    }, [selectTemplateInfo]);

    const isEditStationRule = useMemo(() => {
        if (editItems && editItems[0] && editItems[0].profitRuleType) {
            if (editItems[0].profitRuleType === RULE_TYPES.STATION) {
                return true;
            }
        }
        return false;
    }, [editItems]);

    useEffect(() => {
        loadSelectTemplateList();
    }, []);

    const loadSelectTemplateList = () => {
        dispatch({
            type: 'profitRuleModel/getSelectTemplateList',
            cooperationType: OPER_COOPERATION.TAKE,
        });
    };

    useEffect(() => {
        if (editItems?.length > 1) {
            changeEditRuleType(RULE_TYPES.STATION);
            editForm.setFieldsValue({ _ruleType: RULE_TYPES.STATION });
            editItems[0].profitRuleType = RULE_TYPES.STATION;
        } else if (editItems?.[0]?.profitRuleType) {
            changeEditRuleType(RULE_TYPES.STATION);
        }
    }, [editItems]);

    useEffect(() => {
        const params = {};
        if (mainInfo) {
            const { effTime, expTime, profitRuleTypeList, profitRuleType, premiumFlag } = mainInfo;
            params.ruleRel = mainInfo.ruleRel || '';
            params.ruleFlag = mainInfo.ruleFlag || '0';
            params.premiumFlag = premiumFlag;
            if (premiumFlag === '1') {
                params.divideDTOList = mainInfo.divideList?.map((v) => ({
                    divideType: v.divideType,
                    divideValue: v.divideValue,
                }));
            }
            if (mainInfo?.profitRuleType == '03') {
                params.ruleRange = mainInfo.ruleRange;
                params.operId = mainInfo.operId;
                params.cityName = mainInfo.cityName;
                params.stationIds = mainInfo.stationIds;
            }

            params.effTime = effTime ? moment(effTime) : moment().add(5, 'minutes');
            params.expTime = expTime ? moment(expTime) : null;
            if (expTime) {
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }

            if (profitRuleTypeList) {
                params.profitRuleList = initialRuleList.map((ele) => {
                    const findItem = profitRuleTypeList.find(
                        (item) => item.ruleModel === ele.ruleModel,
                    );
                    if (findItem) {
                        return {
                            ruleModel: [findItem.ruleModel],
                            ruleType: findItem.ruleType,
                            ruleValue: parseFloat(findItem.ruleValue),
                            agreeType: findItem.agreeType,
                            longRuleValue: parseFloat(findItem.longRuleValue),
                            shortRuleValue: parseFloat(findItem.shortRuleValue),
                        };
                    }
                    return null;
                });
            }
            if (mainInfo.templateId) {
                params.useTemplate = '02';
                params.templateId = mainInfo.templateId;
                changeSelectEvent(mainInfo.templateId);
            }
            const _ruleType = editForm.getFieldValue('_ruleType');
            params._ruleType = profitRuleType || _ruleType;
            if (profitRuleType) {
                changeEditRuleType(profitRuleType);
                if (profitRuleType === RULE_TYPES.COMMON) {
                    editForm.setFieldsValue({
                        useTemplate: '01',
                    });
                } else if (profitRuleType === RULE_TYPES.STATION) {
                    if (mainInfo.templateId) {
                        editForm.setFieldsValue({
                            useTemplate: '02',
                            templateId: mainInfo.templateId,
                        });
                    }
                }
            }
        }
        editForm.setFieldsValue(params);
    }, [mainInfo]);

    // 读取当前规则是否配置了城市
    const [cityRuleFlag, updateCityRuleFlag] = useState(undefined);
    useEffect(() => {
        if (!editItems || editItems.length != 1) {
            updateCityRuleFlag(undefined);
        } else if (!cityRuleFlag) {
            // 读取城市规则是否有配置
            initCityFlag();
        }
    }, [editItems]);

    // 查询运营商站点是否配置了城市分润规则
    const initCityFlag = async () => {
        try {
            const {
                data: { flag },
            } = await getStationCityProfitRuleApi({
                operId,
                stationId: editItems[0].stationId,
                city: editItems[0].city,
                cooperationType: '01',
                cooperationPlatform,
            });
            updateCityRuleFlag(flag);
        } catch (error) {
            updateCityRuleFlag(undefined);
        }
    };

    const onEditFinish = (values) => {
        if (isCommonRule) {
            if (isEditStationRule) {
                resetEvent(editItems);
                return;
            }
            channleEvent();
            return;
        }

        if (values._ruleType == RULE_TYPES.CITY) {
            confirmCityEvent(mainInfo);
            return;
        }

        if (values.useTemplate === '02') {
            const options = {
                templateId: values.templateId,
                premiumFlag: values.premiumFlag,
            };
            if (values.premiumFlag === '1') {
                options.divideDTOList = JSON.stringify(values.divideDTOList);
            }
            onFinish(options);
            return;
        }

        const ruleFlag = editForm.getFieldValue('ruleFlag');

        const profitRuleList = editForm.getFieldValue('profitRuleList') || [];

        const filterRuleList = profitRuleList.filter((ele) => ele && !isEmpty(ele.ruleModel)) || [];

        const profitRuleTypes = filterRuleList.map((ele) => {
            let options = {
                ...ele,
                ruleModel: String(ele.ruleModel),
            };
            if (ele.agreeType instanceof Array && ele.agreeType.length === 1) {
                if (ele.agreeType.includes('01')) {
                    options.longRuleValue = ele.ruleValue;
                }
                if (ele.agreeType.includes('02')) {
                    options.shortRuleValue = ele.ruleValue;
                }
            }

            return options;
        });

        const params = {
            ruleRel: values.ruleRel,
            deducioActFlag: '0',
            effTime: values.effTime.format('YYYY-MM-DD HH:mm:ss'),
            ruleFlag,
            premiumFlag: values.premiumFlag, //溢价分成
        };
        if (ruleFlag === '1') {
            params.profitRuleTypes = JSON.stringify(profitRuleTypes);
        }
        if (values.expTime) {
            params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
        }
        if (values.premiumFlag === '1') {
            params.divideDTOList = JSON.stringify(values.divideDTOList);
        }
        params.profitRuleType = values._ruleType;

        onFinish(params);
    };

    // 取消
    const channleEvent = () => {
        onCancel();
    };

    const changeEditRuleTypeEvent = (e) => {
        const { value } = e.target;
        changeEditRuleType(value);
        toggleCommonAndStationEvent(value);
    };

    const toggleCommonAndStationEvent = (value) => {
        if (isEmpty(editItems) && isEmpty(editItems[0])) {
            return;
        }
        if (value === RULE_TYPES.COMMON) {
            editForm.setFieldsValue({
                useTemplate: '01',
            });
            dispatch({
                type: 'profitRuleModel/getRuleInfo',
                params: {
                    operId,
                    ruleId: editItems[0].ruleId,
                    profitRuleType: RULE_TYPES.COMMON,
                    cooperationType: '01',
                    cooperationPlatform,
                },
            });
        } else if (value === RULE_TYPES.CITY) {
            const params = {
                ruleId: editItems[0].ruleId,
                city: editItems[0].city,
                stationId: editItems[0].stationId,
                operId,
                profitRuleType: RULE_TYPES.CITY,
                cooperationType: '01',
                cooperationPlatform,
            };
            dispatch({
                type: 'profitRuleModel/getRuleInfo',
                params,
            });
        } else if (value === RULE_TYPES.STATION) {
            if (mainInfo.templateId) {
                editForm.setFieldsValue({
                    useTemplate: '02',
                    templateId: mainInfo.templateId,
                });
            }

            dispatch({
                type: 'profitRuleModel/getRuleInfo',
                params: {
                    operId,
                    ruleId: editItems[0].ruleId,
                    stationId: editItems[0].stationId,
                    profitRuleType: RULE_TYPES.STATION,
                    cooperationType: '01',
                    cooperationPlatform,
                },
            });
        }
    };

    const changeSelectEvent = (value) => {
        dispatch({
            type: 'profitRuleModel/getSelectTemplateInfo',
            templateId: value,
        });
        editForm.setFieldValue('templateId', value);
        editForm.validateFields(['useTemplate']);
    };

    return (
        <Card loading={loading} bordered={false} bodyStyle={{ padding: 0 }}>
            <Form
                form={editForm}
                {...modelFormLayout}
                onFinish={onEditFinish}
                initialValues={{
                    useTemplate: '01',
                    effTime:
                        mainInfo && mainInfo.effTime
                            ? moment(mainInfo.effTime)
                            : moment().add(5, 'minutes'),

                    expTime: '',
                }}
                scrollToFirstError
            >
                {multiple ? (
                    <Alert
                        message="提交后将直接替换所有选中场站的结算规则及模板"
                        type="warning"
                        showIcon
                    />
                ) : null}
                <FormItem
                    label="规则类型"
                    {...{
                        labelCol: {
                            span: 6,
                        },
                        wrapperCol: {
                            span: 14,
                        },
                    }}
                    initialValue={RULE_TYPES.COMMON}
                    name="_ruleType"
                >
                    <Radio.Group value={editRuleType} onChange={changeEditRuleTypeEvent}>
                        {editItems?.length > 1 ? (
                            <Tooltip title="批量编辑不支持选择通用规则">
                                <Radio value={RULE_TYPES.COMMON} disabled>
                                    通用规则
                                </Radio>
                            </Tooltip>
                        ) : (
                            <Radio value={RULE_TYPES.COMMON} disabled={cityRuleFlag == true}>
                                通用规则
                            </Radio>
                        )}
                        {editItems?.length > 1 ? (
                            <Tooltip title="批量编辑不支持选择城市规则">
                                <Radio value={RULE_TYPES.CITY} disabled>
                                    城市规则
                                </Radio>
                            </Tooltip>
                        ) : cityRuleFlag ? (
                            <Radio value={RULE_TYPES.CITY}>城市规则</Radio>
                        ) : (
                            <Tooltip
                                title={
                                    <>
                                        <span>未配置城市规则。</span>
                                        <Button
                                            type="link"
                                            onClick={() => addCityRuleEvent && addCityRuleEvent()}
                                        >
                                            现在去配？
                                        </Button>
                                    </>
                                }
                            >
                                <Radio value={RULE_TYPES.CITY} disabled>
                                    城市规则
                                </Radio>
                            </Tooltip>
                        )}
                        <Radio value={RULE_TYPES.STATION}>场站规则</Radio>
                    </Radio.Group>
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const _ruleType = getFieldValue('_ruleType');
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            (_ruleType == RULE_TYPES.CITY && (
                                <>
                                    <FormItem label="运营商名称">
                                        <span>{mainInfo?.operName || '-'}</span>
                                    </FormItem>

                                    <FormItem label="运营商编号:">
                                        <span>{mainInfo?.operId || '-'}</span>
                                    </FormItem>

                                    <FormItem label="城市">{mainInfo?.cityName || '-'}</FormItem>

                                    <FormItem
                                        label="规则范围"
                                        name="ruleRange"
                                        rules={[{ required: true, message: '请选择' }]}
                                    >
                                        <Radio.Group disabled>
                                            <Radio value="01">全部</Radio>
                                            <Radio value="02">部分</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                    <FormItem
                                        label={
                                            <span>
                                                使用模板
                                                <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        {...{
                                            labelCol: {
                                                span: 6,
                                            },
                                            wrapperCol: {
                                                span: 14,
                                            },
                                        }}
                                        name="useTemplate"
                                    >
                                        <Radio.Group disabled>
                                            <Radio value="01">否</Radio>
                                            <Radio value="02">是</Radio>
                                        </Radio.Group>
                                    </FormItem>

                                    {(useTemplate === '01' && (
                                        <Fragment>
                                            <FormItem label="生效时间">
                                                {mainInfo?.effTime || '-'}
                                            </FormItem>

                                            <FormItem label="失效时间">
                                                {(mainInfo?.expFlag == '01' && '长期') ||
                                                    mainInfo?.expTime ||
                                                    '-'}
                                            </FormItem>

                                            {(mainInfo?.ruleRange == 1 && (
                                                <RuleFormItem
                                                    loading={loading}
                                                    disabled
                                                    form={editForm}
                                                    editRuleInfo={mainInfo}
                                                />
                                            )) ||
                                                (mainInfo?.ruleRange == 2 && (
                                                    <FormItem
                                                        label="规则场站"
                                                        {...{
                                                            labelCol: {
                                                                span: 6,
                                                            },
                                                            wrapperCol: {
                                                                span: 24,
                                                            },
                                                        }}
                                                    >
                                                        <TablePro
                                                            name="rule"
                                                            scroll={{ x: 'max-content' }}
                                                            rowKey={(record) => record.stationId}
                                                            dataSource={allStations}
                                                            pagination={false}
                                                            columns={[
                                                                {
                                                                    title: '适用场站',
                                                                    width: 140,
                                                                    dataIndex: 'ruleRangeName',
                                                                    render(text, record) {
                                                                        return (
                                                                            <Tooltip
                                                                                title={
                                                                                    <div
                                                                                        style={{
                                                                                            maxWidth:
                                                                                                '620px',
                                                                                            maxHeight:
                                                                                                '320px',
                                                                                            overflowY:
                                                                                                'auto',
                                                                                        }}
                                                                                    >
                                                                                        {
                                                                                            record.stationNames
                                                                                        }
                                                                                    </div>
                                                                                }
                                                                            >
                                                                                {text}
                                                                            </Tooltip>
                                                                        );
                                                                    },
                                                                },
                                                                {
                                                                    title: '分润方式',
                                                                    dataIndex: 'ruleName',
                                                                    width: 460,
                                                                    render(text, record) {
                                                                        return (
                                                                            <span
                                                                                style={{
                                                                                    whiteSpace:
                                                                                        'pre',
                                                                                }}
                                                                                title={text}
                                                                            >
                                                                                {text}
                                                                            </span>
                                                                        );
                                                                    },
                                                                },
                                                                {
                                                                    title: '分润关系',
                                                                    dataIndex: 'ruleRelName',
                                                                    width: '120px',
                                                                    render(text, record) {
                                                                        return (
                                                                            <span title={text}>
                                                                                {text}
                                                                            </span>
                                                                        );
                                                                    },
                                                                },
                                                            ]}
                                                            sticky={{ offsetHeader: 64 }}
                                                        />
                                                    </FormItem>
                                                ))}
                                        </Fragment>
                                    )) ||
                                        null}

                                    <FormItem name="operName" noStyle />
                                    <FormItem name="operId" noStyle />
                                    <FormItem name="cityName" noStyle />
                                    <FormItem name="stationIds" noStyle />
                                    <FormItem name="effTime" noStyle />
                                    <FormItem name="expFlag" noStyle />
                                    <FormItem name="expTime" noStyle />
                                </>
                            )) ||
                            null
                        );
                    }}
                </FormItem>
                {!isCommonRule && mainInfo?.profitRuleType != RULE_TYPES.CITY && (
                    <Fragment>
                        <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                            {({ getFieldValue }) => {
                                const useTemplate = getFieldValue('useTemplate');
                                return (
                                    <>
                                        <FormItem
                                            label={
                                                <span>
                                                    使用模板
                                                    <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                        <InfoCircleOutlined
                                                            style={{ marginLeft: '6px' }}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            }
                                            {...{
                                                labelCol: {
                                                    span: 6,
                                                },
                                                wrapperCol: {
                                                    span: 14,
                                                },
                                            }}
                                            name="useTemplate"
                                        >
                                            <Space>
                                                <Radio.Group value={useTemplate}>
                                                    <Radio value="01">否</Radio>
                                                    <Radio value="02">是</Radio>
                                                </Radio.Group>
                                                {useTemplate === '02' && (
                                                    <Button
                                                        type="primary"
                                                        onClick={() => {
                                                            selectTemplateRef.current.show();
                                                        }}
                                                    >
                                                        选择模板
                                                    </Button>
                                                )}
                                            </Space>
                                        </FormItem>
                                    </>
                                );
                            }}
                        </FormItem>
                    </Fragment>
                )}
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        if (useTemplate === '02') {
                            return (
                                <>
                                    <FormItem noStyle name="templateId">
                                        <Input type="hidden" />
                                    </FormItem>
                                    <TablePro
                                        name="city"
                                        loading={selectTemplateListLoading}
                                        scroll={{ x: 'max-content' }}
                                        dataSource={selectTemplateDetailList}
                                        columns={templateColumns}
                                        pagination={false}
                                        style={{ marginBottom: '16px' }}
                                    />
                                </>
                            );
                        }
                        return null;
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        const _ruleType = getFieldValue('_ruleType');
                        if (useTemplate === '01' && _ruleType != RULE_TYPES.CITY) {
                            return (
                                <EditRuleLayout
                                    disabled={isCommonRule}
                                    editRuleInfo={mainInfo}
                                    form={editForm}
                                    {...props}
                                />
                            );
                        }
                        return null;
                    }}
                </FormItem>
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.premiumFlag !== curValues.premiumFlag ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const premiumFlag = getFieldValue('premiumFlag');
                        const ruleType = getFieldValue('_ruleType');
                        return (
                            <Fragment>
                                <FormItem name="premiumFlag" noStyle></FormItem>
                                <FormItem label="是否溢价" required>
                                    <Switch
                                        checkedChildren="开"
                                        unCheckedChildren="关"
                                        checked={premiumFlag === '1'}
                                        defaultChecked={false}
                                        disabled={
                                            isCommonRule ||
                                            ruleType === RULE_TYPES.CITY ||
                                            !premiumFlagEnable
                                        }
                                        onChange={(checked) => {
                                            if (!checked) {
                                                checkClosePremiumCommon(cooperationPlatform).then(
                                                    (result) => {
                                                        editForm.setFieldValue('premiumFlag', '0');
                                                    },
                                                );
                                            } else {
                                                editForm.setFieldValue('premiumFlag', '1');
                                            }
                                        }}
                                    />
                                </FormItem>
                                {premiumFlag === '1' && (
                                    <FormItem
                                        label=" "
                                        name="divideDTOList"
                                        wrapperCol={{ span: 16 }}
                                        colon={false}
                                        rules={[
                                            () => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject(`请选择溢价分成方式`);
                                                    }
                                                    for (const item of value) {
                                                        if (!isNumber(item?.divideValue)) {
                                                            return Promise.reject('请填写分成');
                                                        } else if (
                                                            Number(item?.divideValue) > 100 ||
                                                            Number(item?.divideValue) < 0
                                                        ) {
                                                            return Promise.reject(
                                                                '只能填写0～100之间的数字',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <PremiumSharingItem
                                            disabled={isCommonRule || ruleType === RULE_TYPES.CITY}
                                        />
                                    </FormItem>
                                )}
                            </Fragment>
                        );
                    }}
                </FormItem>
                <div className={styles['form-submit']}>
                    {!isCommonRule || (isCommonRule && isEditStationRule) ? (
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            提交
                        </Button>
                    ) : null}

                    <Button className={styles['form-btn']} onClick={channleEvent}>
                        取消
                    </Button>
                </div>
            </Form>
            <SelectRuleModal onFinish={changeSelectEvent} ref={selectTemplateRef} />
        </Card>
    );
};

export default EditModelLayout;
