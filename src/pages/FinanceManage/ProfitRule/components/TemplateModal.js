import { Button, Card, Form, message, Modal } from 'antd';
import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import moment from 'moment';
import styles from '../ProfitRuleListPage.less';
import TemplateListFormItem from './TemplateListFormItem';
import { initialRuleList } from './RuleModelFormItem';
import { isEmpty } from '@/utils/utils';

import { addRuleTemplateApi, updateRuleTemplateApi } from '@/services/FinanceManage/ProfitRuleApi';
import { OPER_COOPERATION } from '@/config/declare';

const formItemLayout = {};

const TemplateModal = (props, ref) => {
    const {
        dispatch,
        profitRuleModel: { editTemplateInfo },
        onFinish,
        initialAgreeType = '02',
    } = props;

    const [templateForm] = Form.useForm();
    const [infoLoading, updateInfoLoading] = useState(false);
    const [showEditView, toggleShowEditView] = useState(false);

    useImperativeHandle(ref, () => ({
        show: (record) => {
            templateForm.resetFields();
            toggleShowEditView(true);
            dispatch({
                type: 'profitRuleModel/updateProfitTemplateInfo',
                editTemplateInfo: undefined,
            });
            if (record?.templateId) {
                updateInfoLoading(true);
                dispatch({
                    type: 'profitRuleModel/getTemplateInfo',
                    templateId: record.templateId,
                }).then(() => {
                    updateInfoLoading(false);
                });
            }
        },
        close: () => {
            toggleShowEditView(false);
        },
    }));

    useEffect(() => {
        if (editTemplateInfo && showEditView) {
            const params = {
                name: editTemplateInfo.name,
                templateId: editTemplateInfo.templateId,
                detailList: editTemplateInfo.detailList.map((ele) => {
                    const { profitRuleTypeList } = ele;
                    const item = {
                        effTime: moment(ele.effTime),
                        expTime: moment(ele.expTime),
                        ruleFlag: ele.ruleFlag === '1',
                        ruleRel: ele.ruleRel || '',
                        sn: ele.sn,
                    };
                    if (profitRuleTypeList) {
                        item.profitRuleList = initialRuleList.map((template) => {
                            const findItem = profitRuleTypeList.find(
                                (info) => info.ruleModel === template.ruleModel,
                            );
                            if (findItem) {
                                return {
                                    ruleModel: [findItem.ruleModel],
                                    ruleType: findItem.ruleType,
                                    ruleValue: findItem.ruleValue,
                                    agreeType: findItem.agreeType,
                                    longRuleValue: findItem.longRuleValue,
                                    shortRuleValue: findItem.shortRuleValue,
                                };
                            }
                            return null;
                        });
                    }
                    return item;
                }),
            };
            templateForm.setFieldsValue(params);
        }
    }, [editTemplateInfo, showEditView]);

    const submitEvent = async (values) => {
        try {
            const param = {
                name: values.name,
                cooperationType: OPER_COOPERATION.TAKE,
            };
            const detailList = values.detailList.map((ele, index) => {
                const profitRuleList = ele.profitRuleList || [];

                const filterRuleList =
                    profitRuleList.filter((item) => item && !isEmpty(item.ruleModel)) || [];

                const profitRuleTypes = filterRuleList.map((item) => {
                    let options = {
                        ...item,
                        ruleModel: String(item.ruleModel),
                    };
                    if (item.agreeType instanceof Array && item.agreeType.length === 1) {
                        if (item.agreeType.includes('01')) {
                            options.longRuleValue = item.ruleValue;
                        }
                        if (item.agreeType.includes('02')) {
                            options.shortRuleValue = item.ruleValue;
                        }
                    }

                    return options;
                });
                const options = {
                    effTime: (ele.effTime && ele.effTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    expTime: (ele.expTime && ele.expTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    ruleFlag: ele.ruleFlag ? '1' : '0',
                    sn: ele.sn || index + 1,
                    ruleRel: ele.ruleRel,
                };
                if (options.ruleFlag === '1') {
                    options.profitRuleTypes = profitRuleTypes;
                }
                return options;
            });
            param.detailJsonStr = JSON.stringify(detailList);
            if (editTemplateInfo && editTemplateInfo.templateId) {
                param.templateId = editTemplateInfo.templateId;
                await updateRuleTemplateApi(param);
                message.success('编辑成功');
            } else {
                await addRuleTemplateApi(param);
                message.success('新建成功');
            }

            onFinish();
            ref?.current?.close();
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Modal
            title="配置模板"
            width={1200}
            visible={showEditView}
            onCancel={() => {
                ref?.current?.close();
            }}
            destroyOnClose
            footer={null}
            maskClosable={false}
        >
            <Card loading={infoLoading} bordered={false}>
                <Form
                    {...formItemLayout}
                    form={templateForm}
                    onFinish={submitEvent}
                    initialValues={{
                        detailList: [{}, {}],
                    }}
                    scrollToFirstError
                >
                    <div
                        className="overscroll"
                        style={{
                            maxHeight: `${document.body.clientHeight - 270}px`,
                            overflowY: 'auto',
                        }}
                    >
                        <TemplateListFormItem
                            form={templateForm}
                            name="detailList"
                            templateInfo={editTemplateInfo}
                            cooperationType={OPER_COOPERATION.TAKE}
                            isEdit={!!editTemplateInfo}
                            initialAgreeType={initialAgreeType}
                        />
                    </div>
                    <div className={styles['form-submit']}>
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            // loading={submitLoading}
                        >
                            提交
                        </Button>
                        <Button
                            className={styles['form-btn']}
                            onClick={() => {
                                ref?.current?.close();
                            }}
                        >
                            取消
                        </Button>
                    </div>
                </Form>
            </Card>
        </Modal>
    );
};

export default forwardRef(TemplateModal);
