import { InputNumber, Form, Checkbox, Radio, Space, Popover, Input } from 'antd';
import { isEmpty } from '@/utils/utils';
import Decimal from 'decimal.js';
import { Fragment, useRef } from 'react';

import styles from '../ProfitRuleListPage.less';

const FormItem = Form.Item;

export const initialRuleList = [
    {
        ruleModel: '01',
        ruleType: '01',
        ruleValue: 0,
    },
    {
        ruleModel: '02',
        ruleType: '01',
        ruleValue: 0,
    },
    {
        ruleModel: '03',
        ruleType: '01',
        ruleValue: 0,
    },
    {
        ruleModel: '04',
        ruleType: '01',
        ruleValue: 0,
    },
];

const getRuleModelName = (ruleModel) => {
    let name = '';
    switch (ruleModel) {
        case '01':
            name = '电量分润';
            break;
        case '02':
            name = '电费分润';
            break;
        case '03':
            name = '服务费分润';
            break;
        case '04':
            name = '附加费分润';
            break;

        default:
            break;
    }
    return name;
};

const RuleModelFormItem = (props) => {
    const {
        label = '分润方式',
        formItemLayout,
        name = 'profitRuleTypes',
        parentName,
        disabled,
        rules,
        initialAgreeType = '02',
        form,
    } = props;
    let formItemName = [];
    if (typeof name === 'string') {
        formItemName = [name];
    } else if (name instanceof Array) {
        formItemName = name;
    }
    let allName = [];
    if (typeof parentName === 'string') {
        allName = [parentName, ...formItemName];
    } else if (parentName instanceof Array) {
        allName = [...parentName, ...formItemName];
    } else {
        allName = formItemName;
    }
    return (
        <FormItem
            name={formItemName}
            label={label}
            {...formItemLayout}
            rules={
                rules || [
                    () => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请选择分润方式`);
                            }
                            if (value) {
                                const filterList = value.filter(
                                    (ele) => ele?.ruleModel && !isEmpty(ele.ruleModel),
                                );
                                if (!filterList || filterList.length == 0) {
                                    return Promise.reject(`请选择分润方式`);
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]
            }
        >
            <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                {({ getFieldValue, setFieldsValue, validateFields }) => {
                    return initialRuleList.map((ele, index) => {
                        const ruleModel = getFieldValue([...allName, index, 'ruleModel']);
                        const ruleType = getFieldValue([...allName, index, 'ruleType']);
                        const ruleValue = getFieldValue([...allName, index, 'ruleValue']);
                        const unit = ruleType === '01' ? '元' : '%';
                        let disabledItem = false;
                        if (!(ruleModel && ruleModel.includes(ele.ruleModel))) {
                            disabledItem = true;
                        }

                        const changeRuleValueEvent = (newVal) => {
                            const nowItemValues = getFieldValue([...allName, index]);

                            if (!nowItemValues.agreeType.includes('01')) {
                                nowItemValues.longRuleValue = null;
                                nowItemValues.shortRuleValue = newVal;
                            }
                            if (!nowItemValues.agreeType.includes('02')) {
                                nowItemValues.shortRuleValue = null;
                                nowItemValues.longRuleValue = newVal;
                            }

                            form?.setFieldValue([...allName, index], nowItemValues);
                        };
                        return (
                            <div className={styles['type-table']} key={index}>
                                <div
                                    className={`${styles['type-table-item']} ${styles['item-model']}`}
                                >
                                    <FormItem name={[...formItemName, index, 'ruleModel']} noStyle>
                                        <Checkbox.Group
                                            disabled={disabled}
                                            options={[
                                                {
                                                    label: getRuleModelName(ele.ruleModel),
                                                    value: ele.ruleModel,
                                                },
                                            ]}
                                        ></Checkbox.Group>
                                    </FormItem>
                                </div>
                                <div className={styles['type-table-row']}>
                                    <div className={styles['type-table-item']}>
                                        {ele.ruleModel == '01' ? (
                                            <FormItem
                                                name={[...formItemName, index, 'ruleType']}
                                                noStyle
                                                initialValue={'01'}
                                            >
                                                <Radio.Group disabled={disabled || disabledItem}>
                                                    <Radio value="01">
                                                        每度电平台抽成
                                                        {ruleType == '01' ? (
                                                            <Space>
                                                                <FormItem
                                                                    noStyle
                                                                    name={[
                                                                        ...formItemName,
                                                                        index,
                                                                        'ruleValue',
                                                                    ]}
                                                                    rules={[
                                                                        () => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    !disabledItem &&
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        `请填写${getRuleModelName(
                                                                                            ele.ruleModel,
                                                                                        )}抽成金额`,
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <InputNumber
                                                                        type="number"
                                                                        min={0}
                                                                        step={0.001}
                                                                        precision={3}
                                                                        disabled={
                                                                            disabled ||
                                                                            disabledItem ||
                                                                            ruleType != '01'
                                                                        }
                                                                        onChange={
                                                                            changeRuleValueEvent
                                                                        }
                                                                    />
                                                                </FormItem>
                                                                元
                                                            </Space>
                                                        ) : null}
                                                    </Radio>
                                                </Radio.Group>
                                            </FormItem>
                                        ) : (
                                            <FormItem
                                                name={[...formItemName, index, 'ruleType']}
                                                noStyle
                                                initialValue={'02'}
                                            >
                                                <Radio.Group disabled={disabled || disabledItem}>
                                                    {ele.ruleModel != '04' ? (
                                                        <Radio value="01">
                                                            平台抽成
                                                            {ruleType == '01' ? (
                                                                <Space>
                                                                    <FormItem
                                                                        name={[
                                                                            ...formItemName,
                                                                            index,
                                                                            'ruleValue',
                                                                        ]}
                                                                        noStyle
                                                                        rules={[
                                                                            () => ({
                                                                                validator(
                                                                                    rule,
                                                                                    value,
                                                                                ) {
                                                                                    if (
                                                                                        !disabledItem &&
                                                                                        isEmpty(
                                                                                            value,
                                                                                        )
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            `请填写${getRuleModelName(
                                                                                                ele.ruleModel,
                                                                                            )}抽成金额`,
                                                                                        );
                                                                                    }
                                                                                    return Promise.resolve();
                                                                                },
                                                                            }),
                                                                        ]}
                                                                    >
                                                                        <InputNumber
                                                                            disabled={
                                                                                disabled ||
                                                                                disabledItem ||
                                                                                ruleType != '01'
                                                                            }
                                                                            type="number"
                                                                            min={0}
                                                                            step={0.001}
                                                                            precision={3}
                                                                            onChange={
                                                                                changeRuleValueEvent
                                                                            }
                                                                        />
                                                                    </FormItem>
                                                                    元
                                                                </Space>
                                                            ) : null}
                                                        </Radio>
                                                    ) : null}
                                                    <Radio value="02">
                                                        平台抽成
                                                        {ruleType == '02' ? (
                                                            <Space>
                                                                <FormItem
                                                                    name={[
                                                                        ...formItemName,
                                                                        index,
                                                                        'ruleValue',
                                                                    ]}
                                                                    dependencies={[
                                                                        [
                                                                            ...formItemName,
                                                                            index,
                                                                            'longRuleValue',
                                                                        ],
                                                                        [
                                                                            ...formItemName,
                                                                            index,
                                                                            'shortRuleValue',
                                                                        ],
                                                                        [
                                                                            ...formItemName,
                                                                            index,
                                                                            'agreeType',
                                                                        ],
                                                                    ]}
                                                                    noStyle
                                                                    rules={[
                                                                        () => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    !disabledItem &&
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        `请填写${getRuleModelName(
                                                                                            ele.ruleModel,
                                                                                        )}抽成比例`,
                                                                                    );
                                                                                }
                                                                                if (value > 100) {
                                                                                    return Promise.reject(
                                                                                        '抽成配置不得大于100%',
                                                                                    );
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <InputNumber
                                                                        disabled={
                                                                            disabled ||
                                                                            disabledItem ||
                                                                            ruleType != '02'
                                                                        }
                                                                        type="number"
                                                                        min={0}
                                                                        step={0.001}
                                                                        precision={3}
                                                                        onChange={
                                                                            changeRuleValueEvent
                                                                        }
                                                                    />
                                                                </FormItem>
                                                                %
                                                            </Space>
                                                        ) : null}
                                                    </Radio>
                                                </Radio.Group>
                                            </FormItem>
                                        )}
                                    </div>
                                    {!disabledItem ? (
                                        <div className={styles['type-table-item']}>
                                            <FormItem noStyle>
                                                <FormItem
                                                    name={[...formItemName, index, 'agreeType']}
                                                    initialValue={[initialAgreeType]}
                                                    dependencies={[
                                                        [...formItemName, index, 'longRuleValue'],
                                                        [...formItemName, index, 'ruleValue'],
                                                        [...formItemName, index, 'shortRuleValue'],
                                                    ]}
                                                    rules={[
                                                        () => ({
                                                            validator(rule, value) {
                                                                if (!(value?.length > 0)) {
                                                                    return Promise.reject(
                                                                        `${getRuleModelName(
                                                                            ele.ruleModel,
                                                                        )}长协或短协至少选一个`,
                                                                    );
                                                                }
                                                                let shortRuleValue = 0;
                                                                let longRuleValue = 0;
                                                                if (
                                                                    value &&
                                                                    value instanceof Array
                                                                ) {
                                                                    if (value.includes('01')) {
                                                                        let longValue =
                                                                            getFieldValue([
                                                                                ...allName,
                                                                                index,
                                                                                'longRuleValue',
                                                                            ]);
                                                                        longRuleValue =
                                                                            Number(longValue);
                                                                    }
                                                                    if (value.includes('02')) {
                                                                        let shortValue =
                                                                            getFieldValue([
                                                                                ...allName,
                                                                                index,
                                                                                'shortRuleValue',
                                                                            ]);
                                                                        shortRuleValue =
                                                                            Number(shortValue);
                                                                    }

                                                                    let sumValue =
                                                                        Math.round(
                                                                            (longRuleValue +
                                                                                shortRuleValue) *
                                                                                1000,
                                                                        ) / 1000;

                                                                    if (
                                                                        value.length === 2 &&
                                                                        ruleValue != sumValue
                                                                    ) {
                                                                        return Promise.reject(
                                                                            `${getRuleModelName(
                                                                                ele.ruleModel,
                                                                            )}长协收入+短协收入必须等于抽成数值`,
                                                                        );
                                                                    }
                                                                }

                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]}
                                                    noStyle
                                                >
                                                    <Checkbox.Group
                                                        disabled={disabled || disabledItem}
                                                        onChange={(values) => {
                                                            const nowItemValues = getFieldValue([
                                                                ...allName,
                                                                index,
                                                            ]);

                                                            if (!values.includes('01')) {
                                                                nowItemValues.longRuleValue = null;
                                                                nowItemValues.shortRuleValue =
                                                                    ruleValue;
                                                            }
                                                            if (!values.includes('02')) {
                                                                nowItemValues.shortRuleValue = null;
                                                                nowItemValues.longRuleValue =
                                                                    ruleValue;
                                                            }

                                                            form?.setFieldValue(
                                                                [...allName, index],
                                                                nowItemValues,
                                                            );
                                                        }}
                                                    >
                                                        <FormItem
                                                            shouldUpdate={(prevValues, curValues) =>
                                                                true
                                                            }
                                                            noStyle
                                                        >
                                                            {(_) => {
                                                                let agreeType = getFieldValue([
                                                                    ...allName,
                                                                    index,
                                                                    'agreeType',
                                                                ]);
                                                                const longRuleValue = getFieldValue(
                                                                    [
                                                                        ...allName,
                                                                        index,
                                                                        'longRuleValue',
                                                                    ],
                                                                );
                                                                const subNumber = new Decimal(
                                                                    ruleValue ?? 0,
                                                                ).sub(
                                                                    new Decimal(longRuleValue ?? 0),
                                                                );
                                                                const shortRuleValue =
                                                                    isEmpty(ruleValue) ||
                                                                    isEmpty(longRuleValue) ||
                                                                    subNumber.lessThan(0)
                                                                        ? undefined
                                                                        : subNumber
                                                                              .toSignificantDigits(
                                                                                  6,
                                                                              )
                                                                              .toString();
                                                                console.log(
                                                                    43434,
                                                                    longRuleValue,
                                                                    shortRuleValue,
                                                                    subNumber,
                                                                );
                                                                if (
                                                                    form.getFieldValue([
                                                                        ...allName,
                                                                        index,
                                                                        'shortRuleValue',
                                                                    ]) !== shortRuleValue
                                                                ) {
                                                                    form?.setFieldValue(
                                                                        [
                                                                            ...allName,
                                                                            index,
                                                                            'shortRuleValue',
                                                                        ],
                                                                        shortRuleValue,
                                                                    );
                                                                }
                                                                return (
                                                                    <Space>
                                                                        <FormItem noStyle>
                                                                            <Checkbox value={'01'}>
                                                                                长协收入
                                                                            </Checkbox>
                                                                            {agreeType &&
                                                                            agreeType.includes(
                                                                                '01',
                                                                            ) &&
                                                                            agreeType.includes(
                                                                                '02',
                                                                            ) ? (
                                                                                <Fragment>
                                                                                    <FormItem
                                                                                        name={[
                                                                                            ...formItemName,
                                                                                            index,
                                                                                            'longRuleValue',
                                                                                        ]}
                                                                                        dependencies={[
                                                                                            [
                                                                                                ...formItemName,
                                                                                                index,
                                                                                                'ruleValue',
                                                                                            ],
                                                                                            [
                                                                                                ...formItemName,
                                                                                                index,
                                                                                                'shortRuleValue',
                                                                                            ],
                                                                                        ]}
                                                                                        noStyle
                                                                                        rules={[
                                                                                            () => ({
                                                                                                validator(
                                                                                                    rule,
                                                                                                    value,
                                                                                                ) {
                                                                                                    if (
                                                                                                        !disabledItem &&
                                                                                                        agreeType.includes(
                                                                                                            '01',
                                                                                                        )
                                                                                                    ) {
                                                                                                        if (
                                                                                                            isEmpty(
                                                                                                                value,
                                                                                                            )
                                                                                                        ) {
                                                                                                            return Promise.reject(
                                                                                                                `请填写${getRuleModelName(
                                                                                                                    ele.ruleModel,
                                                                                                                )}长协比例`,
                                                                                                            );
                                                                                                        }
                                                                                                    }

                                                                                                    return Promise.resolve();
                                                                                                },
                                                                                            }),
                                                                                        ]}
                                                                                    >
                                                                                        <InputNumber
                                                                                            disabled={
                                                                                                disabled ||
                                                                                                disabledItem ||
                                                                                                !agreeType ||
                                                                                                !agreeType.includes(
                                                                                                    '01',
                                                                                                )
                                                                                            }
                                                                                            type="number"
                                                                                            min={0}
                                                                                            step={
                                                                                                0.001
                                                                                            }
                                                                                            precision={
                                                                                                3
                                                                                            }
                                                                                            onChange={() => {
                                                                                                validateFields();
                                                                                            }}
                                                                                        />
                                                                                    </FormItem>
                                                                                    {unit}
                                                                                </Fragment>
                                                                            ) : null}
                                                                        </FormItem>

                                                                        <FormItem noStyle>
                                                                            <Checkbox value={'02'}>
                                                                                短协收入
                                                                            </Checkbox>
                                                                            {agreeType &&
                                                                            agreeType.includes(
                                                                                '01',
                                                                            ) &&
                                                                            agreeType.includes(
                                                                                '02',
                                                                            ) ? (
                                                                                <Fragment>
                                                                                    <FormItem
                                                                                        name={[
                                                                                            ...formItemName,
                                                                                            index,
                                                                                            'shortRuleValue',
                                                                                        ]}
                                                                                        noStyle
                                                                                    >
                                                                                        <span>
                                                                                            {shortRuleValue ||
                                                                                                ''}
                                                                                        </span>
                                                                                    </FormItem>
                                                                                    {unit}
                                                                                </Fragment>
                                                                            ) : null}
                                                                        </FormItem>
                                                                    </Space>
                                                                );
                                                            }}
                                                        </FormItem>
                                                    </Checkbox.Group>
                                                </FormItem>
                                            </FormItem>
                                        </div>
                                    ) : null}
                                </div>
                            </div>
                        );
                    });
                }}
            </FormItem>
        </FormItem>
    );
};

export default RuleModelFormItem;
