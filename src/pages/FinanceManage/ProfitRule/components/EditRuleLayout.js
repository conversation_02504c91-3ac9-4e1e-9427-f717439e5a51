import { Button, DatePicker, Form, Radio, Space } from 'antd';
import moment from 'moment';
import { Fragment, useEffect, useState } from 'react';

import { RuleFormItem } from './RuleFormItem';

const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

export default (props) => {
    const { loading, form, editRuleInfo = {}, disabled } = props;
    const [canISetEffTime, changeCiSetEffTime] = useState(true);
    const disabledDate = (current) => {
        // Can not select days before today and today
        return current && current < moment().subtract(1, 'days').endOf('day');
    };
    const changeExpFlagEvent = (event) => {
        const {
            target: { value },
        } = event;
        if (value === EXP_FLAG_TYPES.LONG) {
            form.setFieldsValue({ expTime: null });
        }
    };

    useEffect(() => {
        if (editRuleInfo) {
            const { effTime } = editRuleInfo;
            if (moment(effTime).isBefore(moment())) {
                changeCiSetEffTime(false);
            } else {
                changeCiSetEffTime(true);
            }
        }
    }, [editRuleInfo]);
    return (
        <Fragment>
            <Form.Item
                label="生效时间:"
                name="effTime"
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 12,
                    },
                }}
                rules={[
                    { required: true, message: '请填写生效时间' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value) {
                                return Promise.reject('');
                            }

                            if (value && canISetEffTime) {
                                const nowTime = +new Date();
                                const sendEndTime = +new Date(value);

                                if (sendEndTime < nowTime) {
                                    return Promise.reject('生效时间不能早于当前时间');
                                }
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <DatePicker
                    disabledDate={disabledDate}
                    showTime={{
                        format: 'HH:mm:ss',
                        defaultValue: moment('00:00:00', 'HH:mm:ss'),
                    }}
                    format="YYYY-MM-DD HH:mm:ss"
                    disabled={disabled}
                    showNow={false}
                    renderExtraFooter={(a) => (
                        <Space>
                            <Button
                                type="link"
                                onClick={() => {
                                    const time = moment().add(10, 'seconds');
                                    form.setFieldsValue({
                                        effTime: time,
                                    });
                                }}
                            >
                                10秒之后
                            </Button>
                            <Button
                                type="link"
                                onClick={() => {
                                    const effTime = form.getFieldValue('effTime');
                                    if (effTime) {
                                        form.setFieldsValue({
                                            effTime: moment(effTime).startOf('day'),
                                        });
                                    }
                                }}
                            >
                                时间重置
                            </Button>
                        </Space>
                    )}
                />
            </Form.Item>

            <Form.Item
                label="失效时间:"
                name="expFlag"
                initialValue="01"
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 18,
                    },
                }}
            >
                <Radio.Group onChange={changeExpFlagEvent} disabled={disabled}>
                    <Radio value={'01'}>长期</Radio>
                    <Radio value={'02'}>
                        <Space>
                            指定时间
                            <Form.Item
                                shouldUpdate={(prevValues, curValues) =>
                                    prevValues.expFlag !== curValues.expFlag ||
                                    prevValues.effTime !== curValues.effTime
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const expFlag = getFieldValue('expFlag');
                                    const effTime = getFieldValue('effTime');

                                    return (
                                        <Form.Item
                                            name="expTime"
                                            noStyle
                                            rules={[
                                                (_) => ({
                                                    validator(rule, value) {
                                                        if (!effTime) {
                                                            return Promise.reject(
                                                                '请选择生效开始时间',
                                                            );
                                                        }

                                                        if (value) {
                                                            const startTime = +new Date(effTime);
                                                            const sendEndTime = +new Date(value);

                                                            if (sendEndTime <= startTime) {
                                                                return Promise.reject(
                                                                    '失效时间不能早于生效时间',
                                                                );
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <DatePicker
                                                disabledDate={disabledDate}
                                                disabled={expFlag != '02' || disabled}
                                                showTime={{
                                                    format: 'HH:mm:ss',
                                                    defaultValue: moment('23:59:59', 'HH:mm:ss'),
                                                }}
                                                format="YYYY-MM-DD HH:mm:ss"
                                                showNow={false}
                                                renderExtraFooter={(a) => (
                                                    <Space>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const time = moment().add(
                                                                    10,
                                                                    'seconds',
                                                                );
                                                                form.setFieldsValue({
                                                                    expTime: time,
                                                                });
                                                            }}
                                                        >
                                                            10秒之后
                                                        </Button>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const effTime =
                                                                    form.getFieldValue('effTime');
                                                                if (effTime) {
                                                                    form.setFieldsValue({
                                                                        effTime:
                                                                            moment(effTime).startOf(
                                                                                'day',
                                                                            ),
                                                                    });
                                                                }
                                                            }}
                                                        >
                                                            时间重置
                                                        </Button>
                                                    </Space>
                                                )}
                                            />
                                        </Form.Item>
                                    );
                                }}
                            </Form.Item>
                        </Space>
                    </Radio>
                </Radio.Group>
            </Form.Item>
            <RuleFormItem
                loading={loading}
                disabled={disabled}
                form={form}
                editRuleInfo={editRuleInfo}
            />
        </Fragment>
    );
};
