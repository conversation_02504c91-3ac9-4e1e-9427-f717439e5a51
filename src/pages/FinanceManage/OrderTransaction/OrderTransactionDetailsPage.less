.details-table {
    .details-table-header {
        border: 1px solid #ebebeb;
    }
    tr {
        line-height: 50px;
        &:not(:last-child) {
            border-bottom: 1px solid #ebebeb;
        }
    }
    td,
    th {
        line-height: 50px;
        text-align: center;
    }
    th {
        background-color: #f8f8f8;
    }
    .bold {
        font-weight: bold;
    }
}
.order-details-page {
    :global {
        // .ant-descriptions-header {
        //     margin: 0;
        // }
        .ant-descriptions {
            margin-bottom: 30px;
        }
    }
}
.descriptions-table {
    :global {
        .ant-descriptions-item-content {
            width: 100%;
        }
    }
}
.status-view {
    .title {
        color: #999;
    }
    .status-name {
        color: #333;
        font-size: 26px;
    }
}
