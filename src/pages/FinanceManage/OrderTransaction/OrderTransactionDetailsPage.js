import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { LeftOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Descriptions, Card, Tooltip, Empty, Space, Typography } from 'antd';
import { Fragment, useEffect, useMemo } from 'react';
import { connect } from 'umi';
import styles from './OrderTransactionDetailsPage.less';
import TablePro from '@/components/TablePro';
import { CHANNEL_TYPES } from '@/config/declare';
import { isEmpty } from 'lodash';

// 交易明细列表
export const OrderTransactionDetailList = (props) => {
    const { dataSource, info: orderDetailMap } = props;
    const detailsColumns = [
        {
            title: '交易时间',
            dataIndex: 'transTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    交易类型
                    {/* <Tooltip title="预授权冻结与解结冻金额未实际入账，不进行汇总">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip> */}
                </span>
            ),
            dataIndex: 'transName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付方式',
            dataIndex: 'payTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易金额',
            dataIndex: 'allPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电交易金额',
            dataIndex: 'payAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '渠道优惠',
            dataIndex: 'couponAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '无资金优惠券',
            dataIndex: 'cpnAmt',
            render(value, record) {
                const text = (Number(value ?? 0) + Number(record?.fundCpnAmt ?? 0)).toFixed(2);
                return (
                    ((!!value || !!record?.fundCpnAmt) && (
                        <Space>
                            <Typography.Text>{text}</Typography.Text>
                            <Tooltip title={`支付宝券：${text}`}>
                                <InfoCircleOutlined />
                            </Tooltip>
                        </Space>
                    )) ||
                    '-'
                );
            },
        },
        {
            title: '馈赠金',
            dataIndex: 'giftAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户实付金额',
            dataIndex: 'allUserAmt',
            render(text, record) {
                let channelName = '支付宝';
                if (orderDetailMap && orderDetailMap.applyMode) {
                    if (orderDetailMap.applyMode == CHANNEL_TYPES.YSF) {
                        channelName = '银联';
                    } else if (
                        orderDetailMap.applyMode == CHANNEL_TYPES.WX ||
                        orderDetailMap.applyMode == '22' ||
                        orderDetailMap.applyMode == '0610'
                    ) {
                        channelName = '微信';
                    }
                }
                return (
                    <span title={text}>
                        {text}
                        <Tooltip
                            title={
                                <div>
                                    <p>会员卡: {record.principalAmt}</p>
                                    <p>
                                        {channelName}: {record.alipayAmt}
                                    </p>
                                </div>
                            }
                        >
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    </span>
                );
            },
        },
        {
            title: (
                <span>
                    手续费
                    <Tooltip title="手续费将在交易次日下午生成">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            dataIndex: 'servAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易状态',
            dataIndex: 'payStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方交易号',
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <TablePro
            name="OrderTransactionDetailList"
            style={{ width: '100%' }}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            locale={{
                emptyText: (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无支付信息" />
                ),
            }}
            dataSource={dataSource}
            columns={detailsColumns}
            pagination={false}
            sticky={false}
        />
    );
};

export const OrderTransactionPreAuthList = (props) => {
    const { dataSource } = props;

    const preAuthColumns = [
        {
            title: '交易时间',
            dataIndex: 'transTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易类型',
            dataIndex: 'transName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易金额',
            dataIndex: 'payAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '预授权流水号',
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <TablePro
            name="top"
            style={{ width: '100%' }}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            locale={{
                emptyText: (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无预授权记录" />
                ),
            }}
            dataSource={dataSource}
            columns={preAuthColumns}
            pagination={false}
            sticky={false}
        />
    );
};

export const OrderTransactionProfitruleInfo = (props) => {
    const { info: orderDetailMap, title = '分润结算' } = props;

    return (
        <Descriptions title={title} column={2}>
            {!orderDetailMap || orderDetailMap.ruleFlag == '0' ? (
                <Fragment>
                    <Descriptions.Item label="合作模式">
                        {(orderDetailMap && orderDetailMap.cooperationTypeName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="平台">
                        {(orderDetailMap && orderDetailMap.cooperationPlatformName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="分润规则">
                        <div
                            style={{
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-all',
                            }}
                            title={orderDetailMap && orderDetailMap.ruleName}
                        >
                            {(orderDetailMap && orderDetailMap.ruleName) || ''}
                        </div>
                    </Descriptions.Item>
                </Fragment>
            ) : (
                <Fragment>
                    <Descriptions.Item label="合作模式">
                        {(orderDetailMap && orderDetailMap.cooperationTypeName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="平台">
                        {(orderDetailMap && orderDetailMap.cooperationPlatformName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="分润规则">
                        <div
                            style={{
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-all',
                            }}
                            title={orderDetailMap && orderDetailMap.ruleName}
                        >
                            {(orderDetailMap && orderDetailMap.ruleName) || ''}
                        </div>
                    </Descriptions.Item>
                    <Descriptions.Item label="分润关系">
                        {(orderDetailMap && orderDetailMap.ruleRelName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="分润方式">
                        {(orderDetailMap && orderDetailMap.fenRunWayName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="新电途活动费用">
                        {(orderDetailMap && orderDetailMap.deducioActFlagName) || ''}
                    </Descriptions.Item>
                </Fragment>
            )}
        </Descriptions>
    );
};

export const OrderTransactionProfitruleList = (props) => {
    const { dataSource } = props;
    const ruleColumns = [
        {
            title: '分润时间',
            dataIndex: 'succeedTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分润类型',
            dataIndex: 'ruleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分润金额',
            dataIndex: 'disAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '分润状态',
            dataIndex: 'ruleStatus',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方交易号',
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <TablePro
            name="OrderTransactionProfitruleList"
            style={{ width: '100%' }}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            locale={{
                emptyText: (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无分润信息" />
                ),
            }}
            dataSource={dataSource}
            columns={ruleColumns}
            pagination={false}
            sticky={false}
        />
    );
};
export const OrderOccupyInfo = (props) => {
    const { info = {}, title = '占位数据' } = props;
    const { occupyOrder } = info;
    return (
        <>
            <Descriptions title={title} column={2}>
                <Descriptions.Item
                    label={
                        <span>
                            用户在平台占位次数
                            <Tooltip title="用户在新电途平台上产生占位费的次数">
                                <InfoCircleOutlined
                                    style={{ position: 'relative', marginLeft: 4, top: 2 }}
                                />
                            </Tooltip>
                        </span>
                    }
                >
                    {info?.totalTimes || '0'}次
                </Descriptions.Item>
                <Descriptions.Item
                    label={
                        <span>
                            用户在本站点占位次数
                            <Tooltip title="用户在本次充电站点上产生占位费的次数">
                                <InfoCircleOutlined
                                    style={{ position: 'relative', marginLeft: 4, top: 2 }}
                                />
                            </Tooltip>
                        </span>
                    }
                >
                    {info?.timesAtStation || '0'}次
                </Descriptions.Item>
            </Descriptions>
            {occupyOrder && (
                <>
                    <div>注：以下占位信息为系统判断结果</div>
                    <Descriptions>
                        <Descriptions.Item label="占位状态">
                            {occupyOrder?.statusName || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="占位时长">
                            {occupyOrder?.occupyDuration || '0'}分钟
                        </Descriptions.Item>
                        <Descriptions.Item label="占位开始时间">
                            {occupyOrder?.occupyStartTime || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="占位结束时间">
                            {occupyOrder?.occupyEndTime || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="充电开始时间">
                            {occupyOrder?.orderBeginTime || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="充电结束时间">
                            {occupyOrder?.orderEndTime || ''}
                        </Descriptions.Item>
                    </Descriptions>
                </>
            )}
        </>
    );
};
export const OrderOccupyList = (props) => {
    const { dataSource = [] } = props;
    const ruleColumns = [
        {
            title: '通知方式',
            dataIndex: 'messageTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            dataIndex: 'messageStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '失败原因',
            dataIndex: 'failReason',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '通知时间',
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return !isEmpty(dataSource) ? (
        <TablePro
            style={{ width: '100%' }}
            scroll={{ x: 'max-content' }}
            rowKey={(record, index) => index}
            locale={{
                emptyText: (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无占位信息" />
                ),
            }}
            dataSource={dataSource}
            columns={ruleColumns}
            pagination={false}
            sticky={false}
        />
    ) : null;
};
const OrderTransactionDetailsPage = (props) => {
    const {
        match,
        dispatch,
        infoLoading,
        history,
        orderTransactionModel: {
            orderDetails: {
                orderDetailMap = {},
                orderAmtItemList = [],
                orderBadDebtList = [],
                orderTransList = [],
                orderReFundList = [],
                orderProfitruleList = [],
                orderPartnerSettleList = [],
                orderBillSuccessDataList = [],
                orderPreAuthTransList = [],
                orderActList = [],
            },
        },
    } = props;
    const {
        params: { orderNo },
    } = match;

    useEffect(() => {
        dispatch({
            type: 'orderTransactionModel/initOrderDetails',
            orderNo,
        });
    }, []);
    const costColumns = [
        {
            title: '费用项',
            dataIndex: 'itemName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '原金额',
            dataIndex: 'originalAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商活动优惠',
            dataIndex: 'operDisAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '商家券抵扣',
            dataIndex: 'operCouponAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '新电途活动优惠',
            dataIndex: 'actDisAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台券抵扣',
            dataIndex: 'platCouponAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算金额',
            dataIndex: 'settleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '异常结算差额',
            dataIndex: 'abnoAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    const badColumns = [
        {
            title: '产生时间',
            dataIndex: 'orderEndTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '坏账类型',
            dataIndex: 'badDebtTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '坏账金额',
            dataIndex: 'arrAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途承担坏账',
            dataIndex: 'platformBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商承担坏账',
            dataIndex: 'operBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方交易号',
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const refundColumns = [
        {
            title: '退款时间',
            dataIndex: 'refundTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '退款原因',
            dataIndex: 'refundReason',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '退款金额',
            dataIndex: 'refundedAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途承担退款',
            dataIndex: 'platformBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商承担退款',
            dataIndex: 'operBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方交易号',
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    const cooperateColumns = [
        {
            title: '分帐时间',
            dataIndex: 'settleTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分帐合作伙伴',
            dataIndex: 'partnerName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分帐金额',
            dataIndex: 'settleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分帐规则',
            dataIndex: 'ruleName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分帐状态',
            dataIndex: 'settleStatus',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方交易号',
            dataIndex: 'outTradeNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const billSuccessColumns = [
        {
            title: '对账成功时间',
            dataIndex: 'cecBillTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电量',
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            dataIndex: 'elecAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            dataIndex: 'serviceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '附加费',
            dataIndex: 'incrementAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            dataIndex: 'chargeAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '对账批次号',
            dataIndex: 'cecBillNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台批次号',
            dataIndex: 'platBillNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const orderActColumns = [
        {
            title: '活动名称',
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠金额',
            dataIndex: 'discountAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    // title?: React.ReactNode | false;
    // content?: React.ReactNode;
    // extraContent?: React.ReactNode;
    // pageHeaderRender

    const orderInfo = useMemo(
        () => (
            <Descriptions column={3}>
                <Descriptions.Item label="运营商">
                    {(orderDetailMap && orderDetailMap.operName) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="付款方式">
                    {(orderDetailMap && orderDetailMap.traiffTypeName) || ''}
                </Descriptions.Item>
                <Descriptions.Item></Descriptions.Item>
                <Descriptions.Item label="城市">
                    {(orderDetailMap && orderDetailMap.cityName) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="订单渠道">
                    {(orderDetailMap && orderDetailMap.orderChannel) || ''}
                </Descriptions.Item>
                <Descriptions.Item></Descriptions.Item>
                <Descriptions.Item label="充电站">
                    {(orderDetailMap && orderDetailMap.stationName) || ''}
                </Descriptions.Item>
            </Descriptions>
        ),
        [orderDetailMap],
    );
    const orderStatus = useMemo(
        () => (
            <div className={styles['status-view']}>
                <p className={styles.title}>状态</p>
                <p className={styles['status-name']}>
                    {(orderDetailMap && orderDetailMap.orderStatusName) || ''}
                </p>
            </div>
        ),
        [orderDetailMap],
    );

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {`第三方订单号：${(orderDetailMap && orderDetailMap.orderNo) || ''}`}
                </div>
            }
            content={orderInfo}
            extraContent={orderStatus}
        >
            <Card loading={infoLoading} className={styles['order-details-page']}>
                {orderPreAuthTransList && orderPreAuthTransList.length > 0 ? (
                    <Descriptions title="预授权明细">
                        <Descriptions.Item className={styles['descriptions-table']}>
                            <OrderTransactionPreAuthList dataSource={orderPreAuthTransList} />
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
                <Descriptions title="支付明细">
                    <Descriptions.Item className={styles['descriptions-table']}>
                        <OrderTransactionDetailList
                            dataSource={orderTransList}
                            info={orderDetailMap}
                        />
                        {/* <table className={styles['details-table']}>
                            <tr className={styles['details-table-header']}>
                                {detailsColumns.map(ele => {
                                    return <th>{ele.title}</th>;
                                })}
                            </tr>
                        </table> */}
                    </Descriptions.Item>
                </Descriptions>
                <Descriptions title="订单费用">
                    <Descriptions.Item className={styles['descriptions-table']}>
                        <TablePro
                            name="detail"
                            style={{ width: '100%' }}
                            scroll={{ x: 'max-content' }}
                            rowKey={(record, index) => index}
                            dataSource={orderAmtItemList}
                            columns={costColumns}
                            pagination={false}
                            locale={{
                                emptyText: (
                                    <Empty
                                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        description="暂无费用信息"
                                    />
                                ),
                            }}
                            sticky={false}
                            // rowClassNam={(record, index) => {
                            //     if (record.itemName == '合计') {
                            //         return styles['bold'];
                            //     }
                            //     return '';
                            // }}
                        />
                    </Descriptions.Item>
                </Descriptions>

                <OrderTransactionProfitruleInfo info={orderDetailMap} />
                <Descriptions column={2}>
                    <Descriptions.Item span={3} className={styles['descriptions-table']}>
                        <OrderTransactionProfitruleList dataSource={orderProfitruleList} />
                    </Descriptions.Item>
                </Descriptions>

                {orderPartnerSettleList && orderPartnerSettleList.length > 0 ? (
                    <Descriptions title="合作伙伴分润" column={1}>
                        <Descriptions.Item span={3} className={styles['descriptions-table']}>
                            <TablePro
                                name="friends"
                                style={{ width: '100%' }}
                                scroll={{ x: 'max-content' }}
                                rowKey={(record, index) => index}
                                locale={{
                                    emptyText: (
                                        <Empty
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                            description="暂无合作伙伴分润"
                                        />
                                    ),
                                }}
                                dataSource={orderPartnerSettleList}
                                columns={cooperateColumns}
                                pagination={false}
                                sticky={false}
                            />
                            {/* <table className={styles['details-table']}>
                            <tr className={styles['details-table-header']}>
                                {cooperateColumns.map(ele => {
                                    return <th>{ele.title}</th>;
                                })}
                            </tr>
                        </table> */}
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}

                {orderReFundList && orderReFundList.length > 0 ? (
                    <Descriptions title="手工退款">
                        <Descriptions.Item className={styles['descriptions-table']}>
                            <TablePro
                                name="fund"
                                style={{ width: '100%' }}
                                scroll={{ x: 'max-content' }}
                                rowKey={(record, index) => index}
                                locale={{
                                    emptyText: (
                                        <Empty
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                            description="暂无手工退款"
                                        />
                                    ),
                                }}
                                dataSource={orderReFundList}
                                columns={refundColumns}
                                pagination={false}
                                sticky={false}
                            />
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}

                {orderBadDebtList && orderBadDebtList.length > 0 ? (
                    <Descriptions title="坏账记录">
                        <Descriptions.Item className={styles['descriptions-table']}>
                            <TablePro
                                name="record"
                                style={{ width: '100%' }}
                                scroll={{ x: 'max-content' }}
                                rowKey={(record, index) => index}
                                locale={{
                                    emptyText: (
                                        <Empty
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                            description="暂无坏账记录"
                                        />
                                    ),
                                }}
                                dataSource={orderBadDebtList}
                                columns={badColumns}
                                pagination={false}
                                sticky={false}
                            />
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}

                {orderBillSuccessDataList && orderBillSuccessDataList.length > 0 ? (
                    <Descriptions title="自动对账">
                        <Descriptions.Item className={styles['descriptions-table']}>
                            <TablePro
                                name="auto"
                                style={{ width: '100%' }}
                                scroll={{ x: 'max-content' }}
                                rowKey={(record, index) => index}
                                locale={{
                                    emptyText: (
                                        <Empty
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                            description="暂无自动对账"
                                        />
                                    ),
                                }}
                                dataSource={orderBillSuccessDataList}
                                columns={billSuccessColumns}
                                pagination={false}
                                sticky={false}
                            />
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
                {orderActList && orderActList.length > 0 ? (
                    <Descriptions title="活动优惠">
                        <Descriptions.Item className={styles['descriptions-table']}>
                            <TablePro
                                name="act"
                                style={{ width: '100%' }}
                                scroll={{ x: 'max-content' }}
                                rowKey={(record, index) => index}
                                locale={{
                                    emptyText: (
                                        <Empty
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                            description="暂无活动优惠"
                                        />
                                    ),
                                }}
                                dataSource={orderActList}
                                columns={orderActColumns}
                                pagination={false}
                                sticky={false}
                            />
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ orderTransactionModel, loading }) => ({
    orderTransactionModel,
    infoLoading: loading.effects['orderTransactionModel/initOrderDetails'],
}))(OrderTransactionDetailsPage);
