import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    Alert,
    // Divider,
    Spin,
    Space,
    Input,
    DatePicker,
    Tooltip,
    Tabs,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { InfoCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import { getProvinceAndCityApi, getStationListApi } from '@/services/CommonApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import { getUserOrderListPath } from '@/services/FinanceManage/FinanceManageApi';
import CitysSelect from '@/components/CitysSelect/index.js';
import debounce from 'lodash/debounce';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    ALL: '00',
    NOTPAID: '05', // 待支付
    ASSESS: '07', // 待评价
    END: '06', // 已完成
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        financeModel: { tariffTypeList, channelList },
        disabledContidion,
        searchCondition,
    } = props;

    const [stationList, changeStationList] = useState([]);
    const [fetching, changeFetching] = useState(false);

    useEffect(() => {
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const fetchStation = debounce(async (stationName) => {
        try {
            const operId = form.getFieldValue('operId');
            if (!operId) {
                return;
            }
            const params = {
                operId,
                stationName,
            };
            changeFetching(true);
            const {
                data: { stationList: list },
            } = await getStationListApi(params);
            changeStationList(list);
        } catch (error) {
        } finally {
            changeFetching(false);
        }
    }, 800);

    const tariffTypeOptions = useCallback(
        () =>
            tariffTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [tariffTypeList],
    );

    const channelOptions = useCallback(
        () =>
            channelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [channelList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    ...(searchCondition || {}),
                    dates: [moment(), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem
                            label="选择日期:"
                            name="dates"
                            {...formItemLayout}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        const operId = getFieldValue('operId');
                                        if (!value) {
                                            return Promise.reject('');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        const datas = operId ? 60 : 7;
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * datas;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject(
                                                    `选取范围最大不超过${datas}天`,
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem {...formItemLayout} form={form} />
                    </Col>
                    <Col span={8}>
                        <FormItem label="订单号:" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="订单渠道:" name="channel" {...formItemLayout}>
                            <Select placeholder="请选择">{channelOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="付款类型:" name="tariffType" {...formItemLayout}>
                            <Select placeholder="请选择">{tariffTypeOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            placeholder="请选择"
                            rules={[]}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="充&nbsp;&nbsp;电&nbsp;&nbsp;站:"
                            name="stationId"
                            {...formItemLayout}
                        >
                            <Select
                                showSearch
                                placeholder="请选择"
                                loading={fetching}
                                notFoundContent={fetching ? <Spin size="small" /> : null}
                                filterOption={false}
                                onSearch={fetchStation}
                                onFocus={() => {
                                    fetchStation('');
                                }}
                            >
                                {stationList.map((d) => (
                                    <Option key={d.stationId}>{d.stationName}</Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="用户手机号:" name="mobile" {...formItemLayout}>
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                disabled={disabledContidion?.indexOf('mobile') >= 0}
                            />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

export const OrderTransactionList = (props) => {
    const {
        dispatch,
        history,
        orderTransactionModel: {
            orderTransactionList,
            orderTransactionListTotal,
            orderTransactionStatistic,
        },
        global: { pageInit },
        listLoading,
        searchCondition,
        disabledContidion,
    } = props;

    const {
        location: { pathname: _pathName },
    } = history;

    const isUserCenter = _pathName?.indexOf('/userCenter/userManage/detail/view') >= 0;

    const pathname = `${_pathName}_order`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ASSESS,
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        if (searchCondition) {
            const currentConditions = form.getFieldsValue();
            let isEqual = true;
            Object.keys(searchCondition).map((ele) => {
                if (searchCondition?.[ele]?.length && !currentConditions?.[ele]) {
                    // 新增
                    isEqual = false;
                } else if (
                    currentConditions?.[ele] &&
                    currentConditions?.[ele] != searchCondition[ele]
                ) {
                    // 删除
                    isEqual = false;
                }
            });
            if (!isEqual) {
                form.setFieldsValue({ ...searchCondition });
                if (pageInfo.pageIndex == 1) {
                    searchData();
                } else {
                    changePageInfo({ pageIndex: 1 });
                }
            }
        }
    }, [searchCondition]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        if (listLoading) {
            return;
        }

        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId || '',
                orderNo: data.orderNo || '',
                channel: data.channel || '',
                tariffType: data.tariffType || '',
                stationId: data.stationId || '',
                mobile: data.mobile || '',
                bgnTime: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endTime: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };
            if (data.city) {
                params.city = JSON.stringify(data.city);
            }
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.orderStatus = pageInfo.tabType;
            }

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'orderTransactionModel/getOrderTransactionList',
                options: params,
            });
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = async () => {
        try {
            await form.validateFields();

            const data = form.getFieldsValue();
            const params = {
                operId: data.operId || '',
                orderNo: data.orderNo || '',
                channel: data.channel || '',
                tariffType: data.tariffType || '',
                stationId: data.stationId || '',
                mobile: data.mobile || '',
                bgnTime: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endTime: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };
            if (data.city) {
                params.city = JSON.stringify(data.city);
            }
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.orderStatus = pageInfo.tabType;
            }
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.name || item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: getUserOrderListPath,
                options: params,
                columnsStr: columnsStrs,
            });
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const lookDetailsEvent = (item) => {
        history.push(`/financemanage/orderTransaction/list/details/${item.orderNo}`);
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operNickname',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: (
                <span>
                    订单时间
                    <Tooltip title="下发订单启动命令时间">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '订单时间',
            width: 180,
            dataIndex: 'orderTime',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '订单号',
            width: 140,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            width: 140,
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            width: 140,
            align: 'right',
            dataIndex: 'chargeAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电费',
            width: 140,
            dataIndex: 'elecAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 140,
            dataIndex: 'serviceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '附加费',
            width: 140,
            dataIndex: 'incrementAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    运营商营销费用
                    <Tooltip title="运营商出资的所有营销活动费用">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '运营商营销费用',
            width: 200,
            align: 'right',
            dataIndex: 'operDiscountAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    新电途营销费用
                    <Tooltip title="平台出资所有营销活动费用">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '新电途营销费用',
            width: 200,
            align: 'right',
            dataIndex: 'platDiscountAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    异常结算金额
                    <Tooltip title="平台手工结算金额与运营商订单金额差额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '异常结算金额',
            width: 160,
            align: 'right',
            dataIndex: 'diffAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '付款类型',
            width: 140,

            dataIndex: 'tariffTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    用户实付金额
                    <Tooltip title="用户通过线上渠道实际支付金额；不包含平台优惠券或资金券">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '用户实付金额',
            width: 160,
            align: 'right',
            dataIndex: 'realPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    未结清金额
                    <Tooltip title="用户欠款">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '未结清金额',
            width: 160,
            align: 'right',
            dataIndex: 'unPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    手工退款金额
                    <Tooltip title="订单手工退款金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '手工退款金额',
            width: 160,
            align: 'right',
            dataIndex: 'refundAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单状态',
            width: 140,
            dataIndex: 'orderStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 200,
            dataIndex: 'applyModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 220,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '220px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '是否开票',
            width: 140,
            dataIndex: 'invoiceFlagName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '渠道单号',
            width: 140,
            dataIndex: 'startChargeNo',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Link
                        to={`/financemanage/orderTransaction/list/details/${record.orderNo}`}
                        key="detail"
                        target={isUserCenter ? '_blank' : undefined}
                    >
                        <a>详情</a>
                    </Link>
                );
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />
            {orderTransactionStatistic ? (
                <Alert
                    style={{ margin: '10px 0' }}
                    message={`
                订单数：${orderTransactionStatistic.orderNum || '-'}个；充电量${
                        orderTransactionStatistic.chargePq || '-'
                    }KWH；订单金额：${orderTransactionStatistic.chargeAmt || '-'}元；充电费：${
                        orderTransactionStatistic.elecAmt || '-'
                    }元；服务费：${orderTransactionStatistic.serviceAmt || '-'}元；附加费：${
                        orderTransactionStatistic.incrementAmt || '-'
                    }元；用户实付金额：${
                        orderTransactionStatistic.realPayAmt || '-'
                    }元；未结清金额：${
                        orderTransactionStatistic.unPayAmt || '-'
                    }元；人工退款金额：${
                        orderTransactionStatistic.refundAmt || '-'
                    }元；运营商营销费用：${
                        orderTransactionStatistic.operDiscountAmt || '-'
                    }元；新电途营销费用：${orderTransactionStatistic.platDiscountAmt || '-'}元`}
                    type="info"
                    showIcon
                />
            ) : null}

            <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                <TabPane tab="待支付" key={STATUS_TYPES.NOTPAID} />
                <TabPane tab="待评价" key={STATUS_TYPES.ASSESS} />
                <TabPane tab="已完成" key={STATUS_TYPES.END} />
            </Tabs>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={orderTransactionList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: orderTransactionListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Card>
    );
};

const OrderTransactionListPage = (props) => {
    const {
        dispatch,
        history,
        orderTransactionModel: {
            orderTransactionList,
            orderTransactionListTotal,
            orderTransactionStatistic,
        },
        global: { pageInit },
        listLoading,
    } = props;

    return (
        <PageHeaderWrapper>
            <OrderTransactionList {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, orderTransactionModel, global, loading }) => ({
    financeModel,
    orderTransactionModel,
    global,
    listLoading: loading.effects['orderTransactionModel/getOrderTransactionList'],
}))(OrderTransactionListPage);
