import {
    getUserOrderListApi,
    getDcrUserOrderListApi,
    getUserOrderDataApi,
    getDcrUserOrderDataApi,
    getOrderDetailsApi,
    getDcrOrderDetailsApi,
} from '@/services/FinanceManage/FinanceManageApi';

const OrderTransactionModel = {
    namespace: 'orderTransactionModel',
    state: {
        orderTransactionList: [], // 用户订单交易列表
        orderTransactionListTotal: 0, // 用户订单交易列表总条数
        orderTransactionStatistic: null, // 统计数据
        orderDetails: {
            orderDetailMap: {},
            orderAmtItemList: [],
            orderBadDebtList: [],
            orderTransList: [],
            orderReFundList: [],
            orderProfitruleList: [],
            orderPartnerSettleList: [],
            orderPreAuthTransList: [],
            orderBillSuccessDataList: [],
            orderActList: [],
        }, // 订单详情
    },
    effects: {
        /**
         * 用户订单交易列表
         */
        *getOrderTransactionList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getUserOrderListApi, options);

                yield put({
                    type: 'updateOrderTransactionList',
                    list,
                    total,
                });

                const { data } = yield call(getUserOrderDataApi, options);

                yield put({
                    type: 'updateOrderTransactionStatistic',
                    info: data,
                });
            } catch (error) {}
        },
        // 数据治理改造后调用的接口
        *getOrderTransactionAdbList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getDcrUserOrderListApi, options);

                yield put({
                    type: 'updateOrderTransactionList',
                    list,
                    total,
                });

                const { data } = yield call(getDcrUserOrderDataApi, options);

                yield put({
                    type: 'updateOrderTransactionStatistic',
                    info: data,
                });
            } catch (error) {}
        },
        /**
         * 获取订单详情
         */
        *initOrderDetails({ orderNo }, { call, put, select }) {
            try {
                const { data } = yield call(getOrderDetailsApi, orderNo);

                yield put({
                    type: 'updateOrderDetails',
                    info: data,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateOrderTransactionList(state, { list, total }) {
            return {
                ...state,
                orderTransactionList: list,
                orderTransactionListTotal: total,
            };
        },
        updateOrderTransactionStatistic(state, { info }) {
            return {
                ...state,
                orderTransactionStatistic: info,
            };
        },
        updateOrderDetails(state, { info }) {
            return {
                ...state,
                orderDetails: info,
            };
        },
    },
};
export default OrderTransactionModel;
