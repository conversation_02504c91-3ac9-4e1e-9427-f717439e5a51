import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Tooltip,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { stopMiniGiftApi, deleteMiniGiftApi } from '@/services/Marketing/MarketingGiftApi';
import { ACTSUBTYPES } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    ALL: '00',
    NOSTART: '1', // 未开始
    DOING: '2', // 进行中
    END: '3', // 已结束
    DRAFT: '0', // 草稿
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [null, null],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="创建日期:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="结算日期:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[dayjs('00:00', 'HH:mm'), dayjs('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem {...formItemLayout} form={form} />
                </Col>
                <Col span={8}>
                    <FormItem label="订单号:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="充电站:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="付款类型:" name="actName" {...formItemLayout}>
                        <Select placeholder="请选择" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单渠道:" name="actName" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const OrderSettleListPage = (props) => {
    const {
        dispatch,
        history,
        financeModel: { TransactionList, TransactionListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            actName: data.actName,
            actNo: data.actNo,
            actSubType: data.actSubType,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }
        dispatch({
            type: 'financeModel/getPartnersList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            actName: data.actName,
            actId: data.actId,
            actSubType: data.actSubType,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.actState = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.name || item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: '/bil/coupon/couponPutList',
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 240,
            dataIndex: 'creTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单创建时间',
            width: 240,
            dataIndex: 'creTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单号',
            width: 140,
            dataIndex: 'actSubTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商营销费用',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    线上收款金额
                    <Tooltip title="用户线上实付金额（未扣除手续费、分润）">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '线上收款金额',
            width: 200,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    线下收款金额
                    <Tooltip title="平台活动及平台优惠券减免金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '线下收款金额',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台分润方式',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return (
                    <span style={{ whiteSpace: 'pre' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '分账时间',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    平台分润金额
                    <Tooltip title="已结算状态的实际分润金额或线下结算状态的平台应分润金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '平台分润金额',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手续费支出',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    合作伙伴分账金额
                    <Tooltip title="已分账状态的实际分润金额或线下分账状态的应分账金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            name: '合作伙伴分账金额',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单状态',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '付款类型',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算状态',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分润时间',
            width: 140,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <Tabs defaultActiveKey="00" onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="未结算" key={STATUS_TYPES.NOSTART} />
                    <TabPane tab="已结算" key={STATUS_TYPES.DOING} />
                    <TabPane tab="线下结算" key={STATUS_TYPES.END} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={TransactionList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: TransactionListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, global, loading }) => ({
    financeModel,
    global,
    listLoading: loading.effects['financeModel/getPartnersList'],
}))(OrderSettleListPage);
