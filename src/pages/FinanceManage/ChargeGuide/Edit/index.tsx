import { PageHeaderWrapper } from '@ant-design/pro-layout';
import EditView from './components/EditView';
import LookView from './components/LookView';
import { useRequest } from 'ahooks';
import { Drawer, Card } from 'antd';
import { useParams } from 'umi';

import {
    getOperGuideConfigInfoApi,
    saveOperGuideConfigInfoApi,
} from '@/services/AssetCenter/GuideApi';
import { useEffect, useState, useImperativeHandle, forwardRef, useMemo } from 'react';
const EditPage = (props: any, ref: any) => {
    const { onConfirm } = props;
    const [visible, toggleVisible] = useState(false);
    const [defaultConfig, updateDefaultConfig] = useState();

    useImperativeHandle(ref, () => {
        return {
            open: (params?: { type?: string; record: API.OperGuideInfoVo }) => {
                if (params) {
                    updateDefaultConfig(params);
                }
                openEvent();
            },
        };
    });
    useEffect(() => {
        if (defaultConfig?.record) {
            initData();
        }
    }, [defaultConfig]);
    const openEvent = () => {
        toggleVisible(true);
    };
    const closeEvent = () => {
        toggleVisible(false);
        mutate(undefined);
        updateDefaultConfig(undefined);
    };

    const confirmEvent = () => {
        onConfirm && onConfirm();
        closeEvent();
    };

    const {
        data: initialValues,
        run: initData,
        loading,
        mutate,
    } = useRequest(
        async () => {
            try {
                const { record } = defaultConfig;
                const { data } = await getOperGuideConfigInfoApi(record?.configId);
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    const title = useMemo(() => {
        if (defaultConfig) {
            const { type } = defaultConfig;
            if (type == 'edit') {
                return '编辑引导';
            } else if (type == 'look') {
                return '查看引导';
            }
        }
        return '新增引导';
    }, [defaultConfig]);

    return (
        <Drawer title={title} width={600} visible={visible} onClose={closeEvent} destroyOnClose>
            <Card loading={loading} bordered={false} bodyStyle={{ padding: '0' }}>
                {defaultConfig?.type == 'look' ? (
                    <LookView initialValues={initialValues} goBack={closeEvent}></LookView>
                ) : (
                    <EditView
                        initialValues={initialValues}
                        goBack={closeEvent}
                        onConfirm={confirmEvent}
                    ></EditView>
                )}
            </Card>
        </Drawer>
    );
};
export default forwardRef(EditPage);
