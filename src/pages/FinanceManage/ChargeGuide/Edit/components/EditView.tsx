import ProForm, {
    ProFormDateTimeRangePicker,
    ProFormDependency,
    ProFormTimePicker,
    ProFormSelect,
    ProFormText,
    ProFormDateTimePicker,
    ProFormSwitch,
} from '@ant-design/pro-form';
import SelectChannelItem from '@/components/SelectChannelItem/index';
import CitysSelect from '@/components/CitysSelect/index.js';

import { Form, Row, Col, Space, Button, message } from 'antd';
import { useEffect } from 'react';
import OperSelectItem from '@/components/OperSelectItem/index';
import { getOperatorListApi } from '@/services/OperationMng/OperationMngApi';
import { isEmpty } from '@/utils/utils';
import { saveOperGuideConfigInfoApi } from '@/services/AssetCenter/GuideApi';
import { useHistory, useModel } from 'umi';
import { useRequest } from 'ahooks';
import { ProCard } from '@ant-design/pro-components';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
const EditView = (props: any) => {
    const { method, initialValues, onConfirm, goBack } = props;
    const { initialState } = useModel('@@initialState');
    const { codeInfo, initCode } = useModel('codeState');
    const { currentUser } = initialState || {};
    const history = useHistory();
    const [form] = Form.useForm();

    const configId = Form.useWatch('configId', form);

    useEffect(() => {
        if (isEmpty(codeInfo.channel)) {
            initCode('channel');
        }
        return () => {
            form.resetFields();
        };
    }, []);
    useEffect(() => {
        if (initialValues) {
            formatValueToLocal(initialValues);
        }
    }, [initialValues]);

    const formatValueToLocal = (info: API.OperGuideInfoVo) => {
        const params = {
            ...info,
        };
        if (params.configStatus == 1) {
            params.configStatus = true;
        } else {
            params.configStatus = false;
        }
        form.setFieldsValue(params);
    };

    const { data: operatorList } = useRequest(async () => {
        try {
            const { data } = await getOperatorListApi({});
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    });

    const { run: submitForm, loading: submitLoading } = useRequest(
        async (params) => {
            try {
                const options = {
                    ...params,
                };
                if (params.configStatus) {
                    options.configStatus = 1;
                } else {
                    options.configStatus = 0;
                }
                const { data } = await saveOperGuideConfigInfoApi(options);
                message.success('保存成功');
                onConfirm && onConfirm();
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    return (
        <ProCard>
            <ProForm<{
                name: string;
                company: string;
            }>
                layout="horizontal"
                wrapperCol={{ span: 24 }}
                form={form}
                submitter={{
                    render: (props, doms) => {
                        return (
                            <Row>
                                <Space style={{ marginLeft: 'auto' }}>
                                    <Button
                                        type="default"
                                        onClick={() => {
                                            props.form?.resetFields();
                                            goBack && goBack();
                                        }}
                                        key="cancel"
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={() => props.form?.submit?.()}
                                        key="next"
                                        loading={submitLoading}
                                        disabled={submitLoading}
                                    >
                                        提交
                                    </Button>
                                </Space>
                            </Row>
                        );
                    },
                }}
                onFinish={async (values) => {
                    submitForm(values);
                }}
                initialValues={{
                    operIdList: [],
                }}
                validateTrigger="onBlur"
            >
                <ProFormText
                    name="configName"
                    label="配置名称"
                    fieldProps={{
                        placeholder: '请输入配置名称',
                    }}
                    required
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请输入配置名称',
                        },
                    ]}
                ></ProFormText>

                <Form.Item
                    label="配置ID"
                    name="configId"
                    required
                    hidden={!initialValues?.configId}
                >
                    {configId}
                </Form.Item>

                <OperSelectItem
                    placeholder="请选择"
                    label="运营商"
                    name="operIdList"
                    validateTrigger={['onChange', 'onBlur']}
                    operatorList={operatorList}
                    mode="multiple"
                    required
                    rules={[
                        {
                            required: true,

                            message: '请输入配置运营商',
                        },
                    ]}
                />
                <ProForm.Item
                    label="投放渠道"
                    name="channelList"
                    rules={[
                        {
                            required: true,
                            message: '请选择渠道',
                        },
                    ]}
                >
                    <SelectChannelItem
                        optionList={codeInfo?.channel || []}
                        filterKeys={['新电途']}
                    ></SelectChannelItem>
                </ProForm.Item>
                <CitysSelect
                    form={form}
                    label="投放城市"
                    name="cityList"
                    placeholder="请选择"
                    formItemLayout={{ labelAlign: 'right' }}
                    showArrow
                    provinceSelectable
                    required
                />
                <ProFormText
                    name="guideDesc"
                    label="引导文案"
                    fieldProps={{
                        placeholder: '请输入引导文案',
                    }}
                    required
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请输入引导文案',
                        },
                    ]}
                ></ProFormText>

                <Form.Item
                    label="图片"
                    name="guideImgUrl"
                    wrapperCol={{ span: 24 }}
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请配置图片',
                        },
                    ]}
                >
                    <UpLoadImgItem
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'community',
                            relaTable: 'e_wechat_community',
                        }}
                        sizeInfo={{
                            size: 100,
                        }}
                    ></UpLoadImgItem>
                </Form.Item>
                <ProFormSwitch
                    name="configStatus"
                    label="状态"
                    rules={[
                        {
                            required: true,
                            message: '请选择状态',
                        },
                    ]}
                    initialValue
                    fieldProps={{
                        checkedChildren: '开启',
                        unCheckedChildren: '关闭',
                    }}
                ></ProFormSwitch>
            </ProForm>
        </ProCard>
    );
};
export default EditView;
