import { ProCard } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import Thumbnail from '@/components/ShowPicture/Thumbnail';
import { Button, Typography, Space, Tooltip, Tag } from 'antd';
import baseStyles from '@/assets/styles/base.less';

const LookView = (props: any) => {
    const { initialValues, goBack } = props;
    const columns = [
        {
            title: '配置名称',
            key: 'text',
            dataIndex: 'configName',
        },
        {
            title: '配置ID',
            key: 'text',
            dataIndex: 'configId',
        },
        {
            title: '运营商',
            key: 'text',
            dataIndex: 'operIdList',
            renderText(text: string, record: API.OperGuideInfoVo) {
                const operatorVoList = record?.operatorVoList || [];
                const operStr = operatorVoList?.map((ele) => ele.buildName)?.join('、');
                return (
                    <Space>
                        <div style={{ width: '300px' }}>{operStr}</div>
                        <Tag>共{operatorVoList?.length}个</Tag>
                    </Space>
                );
            },
        },
        {
            title: '投放渠道',
            key: 'channelList',
            dataIndex: 'channelList',
            renderText(text: string, record: API.OperGuideInfoVo) {
                const chnanelList = record.channelNameList || [];
                const channelStr = chnanelList?.join('、');
                return (
                    <Space>
                        <div style={{ width: '300px' }}>{channelStr}</div>
                        <Tag>共{chnanelList?.length}个</Tag>
                    </Space>
                );
            },
        },
        {
            title: '投放城市',
            key: 'cityList',
            dataIndex: 'cityList',
            renderText(text: string, record: API.OperGuideInfoVo) {
                const cityNameList = record.cityNameList || [];
                const cityStr = cityNameList?.join('、');

                return (
                    <Space>
                        <div style={{ width: '300px' }}>{cityStr}</div>
                        <Tag>共{cityNameList?.length}个</Tag>
                    </Space>
                );
            },
        },
        {
            title: '引导文案',
            key: 'guideDesc',
            dataIndex: 'guideDesc',
        },

        {
            title: '图片',
            key: 'guideImgUrl',
            dataIndex: 'guideImgUrl',
            renderText(text: string, record: API.OperGuideInfoVo) {
                return (
                    <div style={{ width: '100px', height: '100px' }}>
                        <Thumbnail url={record.guideImgUrl} disabled></Thumbnail>
                    </div>
                );
            },
        },
        {
            title: '状态',
            dataIndex: 'configStatus',
            valueType: 'select',
            valueEnum: {
                '0': '关闭',
                '1': '开启',
            },
            renderText(text: string, record: API.OperGuideInfoVo) {
                let statusType = 'danger';
                if (record.configStatus == 1) {
                    statusType = 'success';
                }
                return (
                    <Typography.Text type={statusType}>
                        {record.configStatusName || ''}
                    </Typography.Text>
                );
            },
        },
    ];
    return (
        <ProCard>
            <ProDescriptions
                dataSource={initialValues}
                column={1}
                columns={columns}
            ></ProDescriptions>
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                }}
            >
                <Button onClick={goBack}>返回</Button>
            </div>
        </ProCard>
    );
};
export default LookView;
