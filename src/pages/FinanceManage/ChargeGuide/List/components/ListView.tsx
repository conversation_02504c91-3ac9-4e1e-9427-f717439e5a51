import { <PERSON>, Button, Space, Typography, Tooltip, Tag, Popconfirm, message } from 'antd';
import { useRef, useEffect } from 'react';
import XdtProTable from '@/components/XdtProTable/index';
import {
    getOperGuideConfigListApi,
    delOperGuideConfigInfoApi,
} from '@/services/AssetCenter/GuideApi';
import { useHistory, useModel } from 'umi';
import { renderTableDataIndexText, isEmpty } from '@/utils/utils';
import SelectChannelItem from '@/components/SelectChannelItem/index';
import CitysSelect from '@/components/CitysSelect/index.js';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import baseStyles from '@/assets/styles/base.less';
import Thumbnail from '@/components/ShowPicture/Thumbnail';

import EditDrawer from '../../Edit/index';
import { useRequest } from 'ahooks';
import { element } from 'prop-types';

const ListView = (props: any) => {
    const { codeInfo, initCode } = useModel('codeState');

    const actionRef = useRef();
    const formRef = useRef();
    const editRef = useRef();

    console.log(4343, codeInfo.channel);

    useEffect(() => {
        if (isEmpty(codeInfo.channel)) {
            initCode('channel');
        }
    }, []);

    const { run: delEvent, loading: delLoading } = useRequest(
        async (record) => {
            try {
                await delOperGuideConfigInfoApi(record?.configId);
                actionRef?.current?.reload();
                message.success('删除成功');

                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    const columns = [
        {
            title: '配置名称',
            dataIndex: 'configName',
            width: 140,
            renderText(text: string, record: any) {
                return (
                    <Space direction="vertical" align="center">
                        <div>{record?.configName}</div>
                        <Typography.Text type="secondary">{record?.configId}</Typography.Text>
                    </Space>
                );
            },
        },

        {
            title: '运营商',
            dataIndex: 'operIdList',
            width: 240,
            renderText(text: string, record: any) {
                const operatorVoList = record?.operatorVoList || [];

                return (
                    <Tooltip
                        title={
                            <div
                                style={{
                                    maxHeight: '500px',
                                    overflowY: 'auto',
                                    overflowX: 'hidden',
                                }}
                            >
                                {operatorVoList?.map((ele, index: number) => (
                                    <p key={index}>
                                        <Space>
                                            <div className={'text-line'} style={{ width: '200px' }}>
                                                {ele.buildName}
                                            </div>
                                            {ele.operId}
                                        </Space>
                                    </p>
                                ))}
                            </div>
                        }
                    >
                        <Space>
                            <div
                                className={baseStyles['text-three']}
                                style={{ width: '200px', textAlign: 'center' }}
                            >
                                <p>{operatorVoList?.[0]?.buildName}</p>
                                <p>
                                    <Typography.Text type="secondary">
                                        {operatorVoList?.[0]?.operId}
                                    </Typography.Text>
                                </p>
                            </div>
                            <Tag>共{operatorVoList?.length}个</Tag>
                        </Space>
                    </Tooltip>
                );
            },
            renderFormItem() {
                return <OperSelectItem name="operIdList" mode={'multiple'} label="" />;
            },
        },
        {
            title: '投放渠道',
            dataIndex: 'channelList',
            width: 200,
            ellipsis: true,
            renderFormItem() {
                console.log(4343, codeInfo.channel);
                return (
                    <SelectChannelItem
                        optionList={codeInfo.channel}
                        filterKeys={['新电途']}
                    ></SelectChannelItem>
                );
            },
            renderText(text: string, record: any) {
                const chnanelList = record.channelNameList || [];
                const channelStr = chnanelList?.join('、');
                return (
                    <Tooltip
                        title={
                            <div
                                style={{
                                    width: '200px',
                                    maxHeight: '500px',
                                    overflowY: 'auto',
                                }}
                            >
                                {chnanelList?.map((ele, index: number) => (
                                    <p key={index}>{ele}</p>
                                ))}
                            </div>
                        }
                    >
                        <Space>
                            <div className={baseStyles['text-three']} style={{ width: '200px' }}>
                                {channelStr}
                            </div>
                            <Tag>共{chnanelList?.length}个</Tag>
                        </Space>
                    </Tooltip>
                );
            },
        },
        {
            title: '投放城市',
            dataIndex: 'cityList',
            width: 200,
            ellipsis: true,

            renderFormItem() {
                return (
                    <CitysSelect
                        label=""
                        name="cityList"
                        placeholder="请选择"
                        showArrow
                        allowClear
                        provinceSelectable
                        rules={[]}
                    />
                );
            },
            renderText(text: string, record: any) {
                const cityNameList = record.cityNameList || [];
                const cityStr = cityNameList?.join('、');

                return (
                    <Tooltip
                        title={
                            <div
                                style={{
                                    width: '200px',
                                    maxHeight: '500px',
                                    overflowY: 'auto',
                                }}
                            >
                                {cityNameList?.map((ele, index: number) => (
                                    <p key={index}>{ele}</p>
                                ))}
                            </div>
                        }
                    >
                        <Space>
                            <div className={baseStyles['text-three']} style={{ width: '200px' }}>
                                {cityStr}
                            </div>
                            <Tag>共{cityNameList?.length}个</Tag>
                        </Space>
                    </Tooltip>
                );
            },
        },
        {
            title: '图片及文案',
            dataIndex: 'guideImgUrl',
            width: 120,
            hideInSearch: true,
            renderText(text: string, record: any) {
                return (
                    <Space direction="vertical" align="center">
                        <div style={{ width: '100px', height: '100px' }}>
                            <Thumbnail url={record.guideImgUrl} disabled></Thumbnail>
                        </div>
                        <Typography.Text type="secondary">
                            {record?.guideDesc || ''}
                        </Typography.Text>
                    </Space>
                );
            },
        },
        {
            title: '状态',
            dataIndex: 'configStatus',
            valueType: 'select',
            valueEnum: {
                '0': '关闭',
                '1': '开启',
            },
            width: 120,
            renderText(text: string, record: any) {
                let statusType = 'danger';
                if (record.configStatus == 1) {
                    statusType = 'success';
                }
                return (
                    <Typography.Text type={statusType}>
                        {record.configStatusName || ''}
                    </Typography.Text>
                );
            },
        },
        {
            title: '操作时间',
            dataIndex: 'updateTime',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '操作人',
            dataIndex: 'updateBy',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '操作',

            fixed: 'right',
            width: 120,
            hideInSearch: true,
            renderText: (text: string, record: API.KeeperItemVo, index: number) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                editRef?.current?.open({ type: 'look', record });
                            }}
                        >
                            查看
                        </Typography.Link>
                        <Typography.Link
                            onClick={() => {
                                editRef?.current?.open({ type: 'edit', record });
                            }}
                        >
                            编辑
                        </Typography.Link>

                        <Popconfirm
                            disabled={delLoading}
                            title={`确定删除`}
                            okText="确定"
                            cancelText="取消"
                            onConfirm={async () => {
                                delEvent(record);
                            }}
                            okButtonProps={{
                                loading: delLoading,
                            }}
                        >
                            <Typography.Text type="danger">删除</Typography.Text>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];
    return (
        <Card>
            <XdtProTable
                actionRef={actionRef}
                formRef={formRef}
                requestApi={getOperGuideConfigListApi}
                columns={columns}
                rowKey={'configId'}
                hasSort
                toolButtons={[
                    <Button
                        key="add"
                        type="primary"
                        onClick={() => {
                            editRef?.current?.open({
                                type: 'add',
                            });
                        }}
                    >
                        + 新增配置
                    </Button>,
                ]}
            ></XdtProTable>
            <EditDrawer
                ref={editRef}
                onConfirm={() => {
                    actionRef?.current?.reload();
                }}
            ></EditDrawer>
        </Card>
    );
};

export default ListView;
