import {
    Button,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    message,
    Input,
    Select,
    Tag,
    Tooltip,
    Typography,
    Popconfirm,
    Table,
} from 'antd';
import {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import moment from 'moment';
import { usePagination } from 'ahooks';
import { renderTableDataIndexText } from '@/utils/utils';
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
import { batchJoinPoolApi, getJoinPoolRecordApi } from '@/services/FinanceManage/InvoiceManageApi';

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};
const RecordModal = forwardRef((props, ref) => {
    const [visible, updateVisible] = useState(false);

    useImperativeHandle(ref, () => ({
        show: (item = {}) => {
            console.log(1121321);
            updateVisible(true);
            searchData({ pageSize: pagination.pageSize, current: 1 });
        },
    }));
    const {
        run: searchData,
        loading: isWaiting,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }) => {
            const params = {};
            console.log(222222);
            const response = await getJoinPoolRecordApi({
                ...params,

                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                };
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
        },
    );
    const onClose = () => {
        updateVisible(false);
    };

    const columns = [
        {
            title: '下单时间',
            width: 200,
            dataIndex: 'orderTime',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'statusName',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '入池完成时间',
            width: 200,
            dataIndex: 'poolTime',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '入池订单数',
            width: 160,
            dataIndex: 'successOrderNum',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '入池金额',
            width: 140,
            dataIndex: 'poolMoney',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '操作人',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '操作时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return renderTableDataIndexText({ text });
            },
        },
    ];
    return (
        <Modal
            zIndex={11}
            visible={visible}
            title={'操作记录'}
            onCancel={onClose}
            width={1000}
            footer={null}
        >
            <Table
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total || 0} 条`,
                }}
            ></Table>
        </Modal>
    );
});
const BatchPoolModal = (props, ref) => {
    const { onFinish } = props;

    const [visible, updateVisible] = useState(false);
    const [isWaiting, updateWaiting] = useState(false);

    const [form] = Form.useForm();

    const recordModalRef = useRef();

    useImperativeHandle(ref, () => ({
        show: (item = {}) => {
            updateVisible(true);
            form.resetFields();
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const submitFormEvent = () => {
        form.validateFields().then(async (values) => {
            try {
                updateWaiting(true);
                const params = {};
                if (values.dates) {
                    params.startTime = values?.dates[0]?.format('YYYY-MM-DD HH:mm:ss');
                    params.endTime = values?.dates[1]?.format('YYYY-MM-DD HH:mm:ss');

                    delete params.dates;
                }
                await batchJoinPoolApi(params);
                onClose();
                message.success('操作成功');
                onFinish();
            } catch (error) {
                return Promise.reject(error);
            } finally {
                updateWaiting(false);
            }
        });
    };

    const disabledDate = (current) => {
        return current && current.isSameOrAfter(moment().subtract(1, 'year'));
    };

    return (
        <Fragment>
            <Modal
                visible={visible}
                title={
                    <Space>
                        <span>票池用户</span>
                        <Typography.Link
                            onClick={() => {
                                recordModalRef?.current?.show();
                            }}
                        >
                            操作记录
                        </Typography.Link>
                    </Space>
                }
                onCancel={onClose}
                width={550}
                footer={null}
                destroyOnClose
                zIndex={10}
            >
                <Typography.Text type="danger">
                    在下单时间范围内，所有未开票的购电订单，均会被纳入发票池。订单入池后不可逆，请谨慎操作。
                </Typography.Text>
                <br></br>
                <br></br>

                <Form {...formItemLayout} form={form} onFinish={submitFormEvent}>
                    <FormItem
                        label="申请日期"
                        name="dates"
                        // initialValue={[
                        //     moment().subtract(1.5, 'year'),
                        //     moment().subtract(1, 'year').subtract(1, 'day'),
                        // ]}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择结束日期');
                                    }
                                    if (value[0] && value[1]) {
                                        const dest = 6;
                                        if (value[1].diff(value[0], 'month') >= dest) {
                                            return Promise.reject(`选取范围最大不超过半年`);
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                    >
                        <RangePicker
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: [
                                    moment('00:00:00', 'HH:mm:ss'),
                                    moment('23:59:59', 'HH:mm:ss'),
                                ],
                            }}
                            disabledDate={disabledDate}
                            format="YYYY-MM-DD HH:mm:ss"
                            allowClear
                        />
                    </FormItem>
                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Popconfirm
                                title={'确认下单时间填写无误！'}
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => {
                                    form?.submit();
                                }}
                            >
                                <Button type="primary" loading={isWaiting}>
                                    提交
                                </Button>
                            </Popconfirm>

                            <Button onClick={onClose}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
            <RecordModal ref={recordModalRef}></RecordModal>
        </Fragment>
    );
};

export default forwardRef(BatchPoolModal);
