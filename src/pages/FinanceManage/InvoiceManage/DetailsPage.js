import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { connect, useLocation } from 'umi';
import { Fragment, useEffect, useState, useMemo } from 'react';
import {
    Descriptions,
    Card,
    Tooltip,
    Divider,
    Empty,
    Space,
    Alert,
    Button,
    Modal,
    message,
    Form,
    Input,
} from 'antd';
import { LeftOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './style.less';
import TablePro from '@/components/TablePro';
import Yuan from '@/utils/Yuan';
import usePageState from '@/hooks/usePageState.js';
import { INVIOCE_STATUS } from '@/config/declare';
import {
    invalidInvoiceApi,
    changeInvoiceApi,
    sendInvoiceEmailApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import InvoiceModal from './InvoiceModal';
import WorkOrderHXTool, { WORK_TYPES } from '../../MarketingManage/WorkOrder/WorkOrderHXTool';

import { FormOutlined, MailOutlined } from '@ant-design/icons';
import { emailReg } from '@/utils/verify';

import moment from 'moment';
import { INVOICE_OPEN_TYPE, InvoiceOpenVIPColumns } from './components/InvoiceChooseOrderModal';
import { CallRemindButton } from './components/CallRemindButton';
import { GenerateWorkOrderButton } from './components/GenerateWorkOrderButton';
const FormItem = Form.Item;
const { confirm } = Modal;

const DetailsPage = (props) => {
    const {
        match,
        dispatch,
        infoLoading,
        history,
        invoiceManageModel: {
            editActInfo: { invoiceDetail = {}, remindInvoiceVo = {}, sumInvoiceApplist = {} },
            invoiceDetailList = [],
            invoiceDetailListTotal,

            cardDetailList = [], // 卡券详情列表
            cardDetailListTotal,

            vipDetailList, // 会员购买明细列表
            vipDetailListTotal,

            operServiceDetailList,
            operServiceDetailListTotal,
        },
        user,
        global,
    } = props;
    const {
        params: { invoiceAppNo },
    } = match;

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    const [invoiceform] = Form.useForm();

    const [emailform] = Form.useForm();

    const [showEditWindow, changeShowEditWindow] = useState(false);

    const [showEmailWindow, changeShowEmailWindow] = useState(false);

    const [submitLoading, changeSubmitloading] = useState(false); // 上传加载
    const [fileList, updateFileList] = useState([]);

    useEffect(() => {
        dispatch({
            type: 'invoiceManageModel/initEditActInfo',
            invoiceAppNo,
        });
        return () => {
            dispatch({
                type: 'invoiceManageModel/updateInvoiceProperty',
                params: {
                    cardDetailList: [],
                    cardDetailListTotal: 0,
                    editActInfo: {},
                    vipDetailList: [],
                    vipDetailListTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo, invoiceDetail]);

    const curInvoice = useMemo(() => {
        const options = {
            invoiceAppNo: invoiceDetail?.invoiceAppNo,

            invoiceTime: invoiceDetail?.invoiceTime ? moment(invoiceDetail?.invoiceTime) : moment(),

            invoiceCode: invoiceDetail?.invoiceCode,

            invoiceNum: invoiceDetail?.invoiceNum,

            courierCompany: invoiceDetail?.courierCompany,

            courierNo: invoiceDetail?.courierNo,

            remark: invoiceDetail?.remark,
            invoiceMedia: invoiceDetail?.invoiceMedia,
        };
        return options;
    }, [invoiceDetail]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                invoiceAppNo,
            };

            if (
                invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER
            ) {
                // 充电订单
                // 订单列表
                dispatch({
                    type: 'invoiceManageModel/getInvoiceDetailList',
                    options: params,
                });
            } else if (
                invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.DISCOUNT_CARD ||
                invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_CARD
            ) {
                // 卡券列表
                dispatch({
                    type: 'invoiceManageModel/getCardInvoiceDetailList',
                    options: params,
                });
            } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.VIP) {
                // 会员
                dispatch({
                    type: 'invoiceManageModel/getVIPInvoiceDetailList',
                    options: params,
                });
            } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.OPER_SERVICE) {
                // 商家服务
                dispatch({
                    type: 'invoiceManageModel/getOperServiceInvoiceDetailList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const closeEditInvoiceEvent = () => {
        changeShowEditWindow(false);
    };

    const closeEmailInvoiceEvent = () => {
        changeShowEmailWindow(false);
    };

    const onFinish = async (values) => {
        try {
            const params = {
                invoiceStatus: invoiceDetail?.invoiceStatus,
                invoiceAppNo: curInvoice.invoiceAppNo,
            };

            params.remark = values.remark;
            params.invoiceTime = values.invoiceTime.format('YYYY-MM-DD HH:mm:ss');

            params.invoiceCode = values.invoiceCode;

            params.invoiceNum = values.invoiceNum;

            params.courierCompany = values.courierCompany;

            params.courierNo = values.courierNo;
            params.invoiceFileUrls = fileList?.map((ele) => ele.uid) || [];

            changeSubmitloading(true);
            await changeInvoiceApi(params);
            closeEditInvoiceEvent();
            dispatch({
                type: 'invoiceManageModel/initEditActInfo',
                invoiceAppNo,
            });
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    const onFinishEmail = async (values) => {
        try {
            const params = {
                invoiceAppNo: invoiceDetail?.invoiceAppNo,
                email: values.email,
            };

            changeSubmitloading(true);
            const { msg } = await sendInvoiceEmailApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            closeEmailInvoiceEvent();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    const detailsTableInfo = useMemo(() => {
        if (
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER
        ) {
            // 订单列表
            return {
                columns: [
                    {
                        title: '订单号',
                        width: 180,
                        dataIndex: 'thirdAppNo',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '结算时间',
                        width: 180,
                        dataIndex: 'successTime',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '运营商',
                        width: 180,
                        dataIndex: 'operatorName',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },

                    {
                        title: '场站名称',
                        width: 180,
                        dataIndex: 'stationName',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '开票总电量',
                        width: 180,
                        dataIndex: 'chargePq',
                        align: 'right',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '订单总金额',
                        width: 180,
                        dataIndex: 'amt',
                        align: 'right',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '开票总金额',
                        width: 180,
                        dataIndex: 'invoiceAmt',
                        align: 'right',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '开票电费金额',
                        width: 180,
                        dataIndex: 'elecAmt',
                        align: 'right',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '开票服务费金额',
                        width: 180,
                        dataIndex: 'serviceAmt',
                        align: 'right',
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                ],
                list: invoiceDetailList,
                total: invoiceDetailListTotal,
                title: '开票订单明细',
            };
        } else if (
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.DISCOUNT_CARD ||
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_CARD
        ) {
            // 卡券列表
            return {
                columns: [
                    {
                        title: '权益卡卡号',
                        dataIndex: 'buyNo',
                        width: 160,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '支付时间',
                        dataIndex: 'createTime',
                        width: 220,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '开票方',
                        dataIndex: 'operName',
                        width: 160,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '权益卡方案名称',
                        dataIndex: 'actName',
                        width: 160,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '权益卡类型',
                        dataIndex: 'actTypeName',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '权益卡总额度',
                        dataIndex: 'discountLimitAmt',
                        width: 140,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '权益卡销售金额',
                        dataIndex: 'sellAmt',
                        width: 160,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '权益卡开票金额',
                        dataIndex: 'invoiceAmt',
                        width: 160,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                ],
                list: cardDetailList,
                total: cardDetailListTotal,
                title: '开票卡券明细',
            };
        } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.VIP) {
            // 会员
            return {
                columns: InvoiceOpenVIPColumns,
                list: vipDetailList,
                total: vipDetailListTotal,
                title: '会员购买明细',
            };
        } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.OPER_SERVICE) {
            return {
                columns: [
                    {
                        title: '运营商',
                        dataIndex: 'operName',
                        width: 120,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '流水号',
                        dataIndex: 'recordNo',
                        width: 120,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '流水日',
                        dataIndex: 'recordDate',
                        width: 120,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '流水发生时间',
                        dataIndex: 'recordTime',
                        width: 180,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '服务名称',
                        dataIndex: 'serviceName',
                        width: 120,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '收费标准',
                        dataIndex: 'settleRule',
                        width: 120,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '服务明细',
                        dataIndex: 'serviceDetail',
                        width: 200,
                        render: (text) => <span>{text}</span>,
                    },
                    {
                        title: '消费金额（元）',
                        dataIndex: 'consumeAmt',
                        width: 160,
                        render: (text) => <span>{text}</span>,
                    },
                ],
                list: operServiceDetailList,
                total: operServiceDetailListTotal,
                title: '开票流水明细',
            };
        }
    }, [
        invoiceDetail,
        invoiceDetailList,
        invoiceDetailListTotal,
        cardDetailList,
        cardDetailListTotal,
        vipDetailList,
        vipDetailListTotal,
        operServiceDetailList,
        operServiceDetailListTotal,
    ]);

    const orderInfo = useMemo(
        () => (
            <Descriptions column={3}>
                <Descriptions.Item label="申请人">
                    {(invoiceDetail && invoiceDetail?.mobile) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="申请时间">
                    {(invoiceDetail && invoiceDetail?.appTime) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="开票项目">
                    {(invoiceDetail && invoiceDetail?.invoiceBusinessTypeName) || ''}
                </Descriptions.Item>

                <Descriptions.Item label="开票方式">
                    {(invoiceDetail && invoiceDetail?.invoiceAccessModeName) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="发票类型">
                    {(invoiceDetail && invoiceDetail?.invoiceMediaName) || ''}
                </Descriptions.Item>
                <Descriptions.Item label="发票种类">
                    {(invoiceDetail && invoiceDetail.invoiceTypeName) || ''}
                </Descriptions.Item>
                {(!invoiceDetail?.isCompanyAccount && (
                    <Descriptions.Item label="开票渠道">
                        {(invoiceDetail && invoiceDetail?.applyModeName) || ''}
                    </Descriptions.Item>
                )) ||
                    null}
                {(!user?.currentUser?.operId?.length && !invoiceDetail?.isCompanyAccount && (
                    <Descriptions.Item label="票池开票">
                        {(invoiceDetail && invoiceDetail?.invoicePoolFlagName) || ''}
                    </Descriptions.Item>
                )) ||
                    null}
                <Descriptions.Item label="运营商">
                    {(invoiceDetail && invoiceDetail?.operatorName) || ''}
                </Descriptions.Item>
                {(!invoiceDetail?.isCompanyAccount && (
                    <Descriptions.Item label="项目编号" span={2}>
                        {(invoiceDetail && invoiceDetail?.projectNo) || ''}
                    </Descriptions.Item>
                )) ||
                    null}
                {(!user?.currentUser?.operId?.length && (
                    <Descriptions.Item label="操作人">
                        {invoiceDetail?.operateStaff || ''}
                    </Descriptions.Item>
                )) ||
                    null}
                {invoiceDetail &&
                (invoiceDetail?.invoiceHandleFailReason || invoiceDetail?.invoiceFailReason) ? (
                    <Descriptions.Item label="开票失败原因" span={3}>
                        <Space>
                            <span>{`${invoiceDetail?.invoiceHandleFailReason || ''}`}</span>
                            <span>{`${invoiceDetail?.invoiceFailReason || ''}`}</span>
                        </Space>
                    </Descriptions.Item>
                ) : null}
                {invoiceDetail && invoiceDetail?.noticeKingdeeStatus == 3 && (
                    <Descriptions.Item label="同步失败原因" span={3}>
                        <Space>
                            <span>{`${invoiceDetail?.noticeKingdeeFailReason || ''}`}</span>
                        </Space>
                    </Descriptions.Item>
                )}
            </Descriptions>
        ),
        [invoiceDetail],
    );
    const orderStatus = useMemo(
        () => (
            <Fragment>
                <div style={{ marginBottom: '10px' }}>
                    <Space>
                        {((invoiceDetail?.invoiceAccessMode != '02' &&
                            invoiceDetail?.invoiceAccessMode != '03') ||
                            invoiceDetail?.redInvoiceApiFlag == 1) &&
                        invoiceDetail?.invoiceStatus == INVIOCE_STATUS.SUCCESS &&
                        !invoiceDetail?.isCompanyAccount ? (
                            <Button
                                className={styles['table-btn']}
                                onClick={() => {
                                    invalidInvoiceEvent();
                                }}
                            >
                                作废
                            </Button>
                        ) : null}
                        {/* {invoiceDetail?.invoicePdf ? (
                            <Button
                                type="primary"
                                onClick={() => {
                                    downLoadByUrl(invoiceDetail?.invoicePdf);
                                }}
                            >
                                下载发票
                            </Button>
                        ) : null} */}
                    </Space>
                </div>
                <Space>
                    <div className={styles['status-view']}>
                        <p className={styles.title}>状态</p>
                        <p className={styles['status-name']}>
                            {(invoiceDetail && invoiceDetail?.invoiceStatusName) || ''}
                        </p>
                    </div>
                    <div className={styles['status-view']}>
                        <p className={styles.title}>开票金额</p>
                        <p className={styles['status-name']}>
                            ￥<Yuan>{invoiceDetail && invoiceDetail?.invoiceAmt}</Yuan>
                        </p>
                    </div>
                </Space>
            </Fragment>
        ),
        [invoiceDetail],
    );

    const tableBar = useMemo(() => {
        if (
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
            invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER
        ) {
            return `订单数：${(sumInvoiceApplist && sumInvoiceApplist.orderNum) || ''}个；充电量${
                (sumInvoiceApplist && sumInvoiceApplist.chargePq) || ''
            }KWH；开票总金额：${
                (sumInvoiceApplist && sumInvoiceApplist.invoiceAmt) || ''
            }元；开票电费：${
                (sumInvoiceApplist && sumInvoiceApplist.elecAmt) || ''
            }元；开票服务费（含附加费）：${
                (sumInvoiceApplist && sumInvoiceApplist.serviceAmt) || ''
            }元；订单总金额：${(sumInvoiceApplist && sumInvoiceApplist.amt) || ''}元`;
        } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.VIP) {
            return `订单数：${
                (sumInvoiceApplist && sumInvoiceApplist.orderNum) || ''
            }个；开票总金额：${(sumInvoiceApplist && sumInvoiceApplist.invoiceAmt) || ''}元；`;
        } else if (invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.OPER_SERVICE) {
            return `流水数：${
                (sumInvoiceApplist && sumInvoiceApplist.orderNum) || ''
            }个；开票总金额：${(sumInvoiceApplist && sumInvoiceApplist.invoiceAmt) || ''}元；`;
        } else {
            return `订单数：${
                (sumInvoiceApplist && sumInvoiceApplist.orderNum) || ''
            }个；订单总金额：${(sumInvoiceApplist && sumInvoiceApplist.amt) || ''}元；开票总金额：${
                (sumInvoiceApplist && sumInvoiceApplist.invoiceAmt) || ''
            }元；`;
        }
    }, [sumInvoiceApplist]);

    const invalidInvoiceEvent = () => {
        confirm({
            title: `确定作废该发票?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (
                        invoiceDetail?.invoiceAccessMode == '02' ||
                        invoiceDetail?.invoiceAccessMode == '03'
                    ) {
                        message.error('线下开票、接口开票模式不支持作废');
                        return;
                    }
                    await invalidInvoiceApi(invoiceAppNo);
                    await dispatch({
                        type: 'invoiceManageModel/initEditActInfo',
                        invoiceAppNo,
                    });
                    await searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const goBack = () => {
        history.go(-1);
    };

    const invoiceItem = (url, index) => (
        <a
            href={url}
            onClick={(event) => {
                event.stopPropagation();
            }}
            target="_blank"
            rel="noopener noreferrer"
        >
            附件{index + 1}
        </a>
    );

    const fileShow = useMemo(() => {
        const urlList = [];
        if (invoiceDetail?.invoiceFileUrls) {
            invoiceDetail.invoiceFileUrls?.map((ele, index) => {
                urlList.push(invoiceItem(ele, index));
            });
        }

        return urlList;
    }, [invoiceDetail]);

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    申请单号:{(invoiceDetail && invoiceDetail?.invoiceAppNo) || ''}
                </div>
            }
            content={orderInfo}
            extraContent={orderStatus}
            extra={
                JSON.stringify(invoiceDetail) !== '{}' &&
                !invoiceDetail?.isCompanyAccount && (
                    <WorkOrderHXTool
                        inParams={invoiceDetail}
                        user={user}
                        dispatch={dispatch}
                        global={global}
                        type={WORK_TYPES.INVOICE}
                    />
                )
            }
        >
            <Card loading={infoLoading} className={styles['order-details-page']}>
                <Descriptions title="申请信息">
                    <Descriptions.Item label="发票抬头">
                        {(invoiceDetail && invoiceDetail?.invoiceTitle) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="税号">
                        {(invoiceDetail && invoiceDetail?.taxNum) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="公司地址">
                        {(invoiceDetail && invoiceDetail?.registeredAddr) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="公司电话">
                        {(invoiceDetail && invoiceDetail?.registeredMobile) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="开户银行">
                        {(invoiceDetail && invoiceDetail?.bankName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="银行账号">
                        {(invoiceDetail && invoiceDetail?.bankAccount) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="备注说明">
                        {(invoiceDetail && invoiceDetail?.invoiceRemark) || ''}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                {invoiceDetail?.invoiceStatus == INVIOCE_STATUS.FAIL ? null : (
                    <Fragment>
                        <Descriptions
                            title={
                                invoiceDetail?.isCompanyAccount ? (
                                    '发票信息'
                                ) : (
                                    <Space>
                                        发票信息
                                        {(invoiceDetail?.invoiceAccessMode == '03' &&
                                            invoiceDetail?.invoiceStatus ==
                                                INVIOCE_STATUS.SUCCESS) ||
                                        true ? (
                                            <FormOutlined
                                                onClick={() => {
                                                    changeShowEditWindow(true);
                                                }}
                                            />
                                        ) : null}
                                        {invoiceDetail?.invoiceFileUrls?.length ? (
                                            <Tooltip title="给用户发送发票邮件">
                                                <MailOutlined
                                                    onClick={() => {
                                                        emailform.setFieldsValue({
                                                            email: invoiceDetail?.email,
                                                        });
                                                        changeShowEmailWindow(true);
                                                    }}
                                                />
                                            </Tooltip>
                                        ) : null}
                                    </Space>
                                )
                            }
                        >
                            <Descriptions.Item label="开票时间">
                                {(invoiceDetail && invoiceDetail?.invoiceTime) || ''}
                            </Descriptions.Item>
                            <Descriptions.Item label="发票代码">
                                {(invoiceDetail && invoiceDetail?.invoiceCode) || ''}
                            </Descriptions.Item>
                            <Descriptions.Item label="发票号码">
                                {(invoiceDetail && invoiceDetail?.invoiceNum) || ''}
                            </Descriptions.Item>
                            <Descriptions.Item label="同步金蝶">
                                {(invoiceDetail && invoiceDetail?.noticeKingdeeName) || ''}
                            </Descriptions.Item>
                            {invoiceDetail && invoiceDetail?.invoiceFileUrls?.length ? (
                                <Descriptions.Item label="附件">
                                    <Space wrap>{fileShow}</Space>
                                </Descriptions.Item>
                            ) : null}

                            <Descriptions.Item label={<span style={{ color: 'red' }}>红冲人</span>}>
                                <span style={{ color: 'red' }}>
                                    {(invoiceDetail && invoiceDetail?.discardAccount) || ''}
                                </span>
                            </Descriptions.Item>
                            <Descriptions.Item
                                label={<span style={{ color: 'red' }}>红冲提交时间</span>}
                            >
                                <span style={{ color: 'red' }}>
                                    {(invoiceDetail && invoiceDetail?.redInvoiceApplyTime) || ''}
                                </span>
                            </Descriptions.Item>
                        </Descriptions>

                        <Divider />
                    </Fragment>
                )}

                <Descriptions title="接收方式">
                    <Descriptions.Item label="发票类型">
                        {(invoiceDetail && invoiceDetail?.invoiceMediaName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="邮箱地址">
                        {(invoiceDetail && invoiceDetail?.email) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="收件人">
                        {(invoiceDetail && invoiceDetail?.recipients) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="收件人电话">
                        {(invoiceDetail && invoiceDetail?.phoneNo) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="收件地址">
                        {(invoiceDetail && invoiceDetail?.mailAddr) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="快递公司">
                        {(invoiceDetail && invoiceDetail?.courierCompany) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="快递单号">
                        {(invoiceDetail && invoiceDetail?.courierNo) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="邮费说明">
                        {(invoiceDetail && invoiceDetail?.freeShipRemark) || ''}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                <Fragment>
                    <Descriptions title="催票信息">
                        <Descriptions.Item label="用户催票">
                            {remindInvoiceVo?.custRemindFlagName || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="进线催票">
                            <CallRemindButton
                                invoiceAppNo={invoiceDetail?.invoiceAppNo}
                                record={remindInvoiceVo}
                                onFinish={() => {
                                    dispatch({
                                        type: 'invoiceManageModel/initEditActInfo',
                                        invoiceAppNo,
                                    });
                                }}
                            />
                        </Descriptions.Item>
                        <Descriptions.Item label="催票次数">
                            {(remindInvoiceVo && remindInvoiceVo?.remindCount) || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="催票结果">
                            <div>
                                <GenerateWorkOrderButton
                                    invoiceAppNo={invoiceDetail?.invoiceAppNo}
                                    record={remindInvoiceVo}
                                    onFinish={() => {
                                        dispatch({
                                            type: 'invoiceManageModel/initEditActInfo',
                                            invoiceAppNo,
                                        });
                                    }}
                                />
                            </div>
                        </Descriptions.Item>
                        <Descriptions.Item label="用户催票时间">
                            {(remindInvoiceVo && remindInvoiceVo?.custRemindTime) || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="进线催票时间">
                            {(remindInvoiceVo && remindInvoiceVo?.callRemindTime) || ''}
                        </Descriptions.Item>
                    </Descriptions>

                    <Divider />
                </Fragment>

                <Descriptions title={detailsTableInfo?.title} />
                <Alert message={tableBar} type="info" showIcon />
                <TablePro
                    style={{ width: '100%', marginTop: '20px' }}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.invoiceRelatId}
                    locale={{
                        emptyText: (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无配置信息"
                            />
                        ),
                    }}
                    columns={detailsTableInfo?.columns}
                    dataSource={detailsTableInfo?.list}
                    onChange={onTableChange}
                    pagination={
                        invoiceDetail?.invoiceBusinessType == INVOICE_OPEN_TYPE.VIP
                            ? false
                            : {
                                  current: pageInfo.pageIndex,
                                  total: detailsTableInfo?.total,
                                  pageSize: pageInfo.pageSize,
                                  showSizeChanger: true,
                                  showQuickJumper: true,
                                  showTotal: (total) => `共 ${total} 条`,
                              }
                    }
                />
                <InvoiceModal
                    form={invoiceform}
                    showEditWindow={showEditWindow}
                    closeEditInvoiceEvent={closeEditInvoiceEvent}
                    curInvoice={curInvoice}
                    onFinish={onFinish}
                    submitLoading={submitLoading}
                    fileList={fileList}
                    updateFileList={updateFileList}
                    // changeImageType={changeImageType}
                    // imageUrl={imageUrl}
                    // changeImageUrl={changeImageUrl}
                    isDetails
                ></InvoiceModal>
                <Modal
                    title="发送邮件"
                    visible={showEmailWindow}
                    footer={false}
                    onCancel={closeEmailInvoiceEvent}
                    maskClosable={false}
                >
                    <Form form={emailform} onFinish={onFinishEmail} scrollToFirstError>
                        <Fragment>
                            <FormItem
                                label="邮箱地址"
                                name="email"
                                rules={[
                                    { required: true, message: '请填写' },
                                    {
                                        pattern: emailReg,
                                        message: '邮箱格式不正确',
                                    },
                                ]}
                            >
                                <Input placeholder="请填写" />
                            </FormItem>
                        </Fragment>

                        <FormItem style={{ textAlign: 'center' }}>
                            <Space>
                                <Button type="primary" htmlType="submit" loading={submitLoading}>
                                    发送
                                </Button>
                                <Button onClick={closeEmailInvoiceEvent}>取消</Button>
                            </Space>
                        </FormItem>
                    </Form>
                </Modal>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ invoiceManageModel, loading, user, global }) => ({
    invoiceManageModel,
    infoLoading: loading.effects['invoiceManageModel/initEditActInfo'],
    user,
    global,
}))(DetailsPage);
