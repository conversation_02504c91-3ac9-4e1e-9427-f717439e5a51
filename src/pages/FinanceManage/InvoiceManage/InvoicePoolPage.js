import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { <PERSON>ton, Card, Col, Form, Tabs, Input, Alert, Space } from 'antd';
import { connect } from 'umi';
import { useEffect, useRef, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import InvoicePoolUsersModal from './InvoicePoolUsersModal';
import DateSearchFormItem from '@/components/DateSearchFormItem';
import BatchPoolModal from './BatchPoolModal';

const { TabPane } = Tabs;

const FormItem = Form.Item;

const formItemLayout = {};

const POOL_TYPE = {
    IN: '01',
    OUT: '02',
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };

    return (
        <Form {...formItemLayout} form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <DateSearchFormItem label="产生时间" name="dateTime" {...formItemLayout} />
                </Col>
                <Col span={8}>
                    <FormItem label="用户手机号" name="mobile">
                        <Input placeholder="请填写" autoComplete="off" allowClear />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const InvoicePoolPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        invoiceManageModel: { poolBlance, poolSummary, poolList, poolTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const userModalRef = useRef();
    const batchModalRef = useRef();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: POOL_TYPE.IN,
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'invoiceManageModel/updateInvoiceProperty',
                params: {
                    poolBlance: undefined,
                    poolSummary: undefined,
                    poolList: [],
                    poolTotal: 0,
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const initBalance = () => {
        dispatch({
            type: 'invoiceManageModel/getInvoicePoolBalance',
        });
    };

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    createTimeBeginTime: data.dateTime?.[0].format('YYYY-MM-DD'),
                    createTimeEndTime: data.dateTime?.[1].format('YYYY-MM-DD'),
                    dateTime: undefined,
                };
                params.type = pageInfo.tabType;

                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'invoiceManageModel/getInvoicePoolList',
                    options: params,
                });
                initBalance();
            } catch (error) {}
        });
    };

    const poolOptions = useMemo(() => {
        return [
            { codeValue: POOL_TYPE.IN, codeName: '入池' },
            { codeValue: POOL_TYPE.OUT, codeName: '核销' },
        ].map((ele) => {
            return <TabPane tab={ele.codeName} key={ele.codeValue} />;
        });
    }, []);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = useMemo(
        () => [
            {
                title: '状态',
                width: 80,
                dataIndex: 'typeName',
                render(text, record) {
                    let color = '#cccccc';
                    if (record.type == POOL_TYPE.IN) {
                        // 绿色
                        color = '#87d068';
                    } else if (record.type == POOL_TYPE.OUT) {
                        // 红色
                        color = '#f50000';
                    }
                    return (
                        <span title={text} style={{ color: color }}>
                            {text}
                        </span>
                    );
                },
            },
            {
                title: '说明',
                width: 120,
                dataIndex: 'subTypeName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: pageInfo.tabType == POOL_TYPE.OUT ? '发票单号' : '订单号',
                width: 200,
                dataIndex: 'appNo',
                render(text, record) {
                    return (
                        <a
                            onClick={() => {
                                if (pageInfo.tabType == POOL_TYPE.IN) {
                                    history.push(
                                        `/orderCenter/charge-order/detail?orderNo=${record.appNo}`,
                                    );
                                } else {
                                    history.push(
                                        `/financemanage/invoice/manage/details/${record.appNo}`,
                                    );
                                }
                            }}
                        >
                            {text}
                        </a>
                    );
                },
            },
            {
                title: '总金额（元）',
                width: 140,
                dataIndex: 'totalAmt',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '电费（元）',
                width: 140,
                dataIndex: 'elecAmt',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '服务费（元）',
                width: 140,
                dataIndex: 'serviceAmt',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '附加费（元）',
                width: 140,
                dataIndex: 'incrementAmt',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '用户手机号',
                width: 140,
                dataIndex: 'mobile',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: pageInfo.tabType == POOL_TYPE.OUT ? '操作人' : '姓名',
                width: 120,
                dataIndex: 'name',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },

            {
                title: pageInfo.tabType == POOL_TYPE.OUT ? '操作时间' : '产生时间',
                width: 200,
                dataIndex: 'createTime',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ],
        [pageInfo],
    );

    const alertMessage = useMemo(() => {
        if (!poolBlance) {
            return null;
        }
        return (
            <div>
                票池余额：
                <span style={{ color: '#1890ff' }}>
                    {poolBlance.balance === undefined ? '-' : poolBlance.balance}
                </span>
                元（电费：{poolBlance.elecBalance === undefined ? '-' : poolBlance.elecBalance}
                元&nbsp;&nbsp;&nbsp;&nbsp;服务费：
                {poolBlance.serviceBalance === undefined ? '-' : poolBlance.serviceBalance}
                元&nbsp;&nbsp;&nbsp;&nbsp;附加费：
                {poolBlance.incrementBalance === undefined ? '-' : poolBlance.incrementBalance}元）
            </div>
        );
    }, [poolBlance]);

    const summaryMessage = useMemo(() => {
        if (!poolSummary || listLoading) {
            return null;
        }
        return (
            <div>
                总
                {(pageInfo.tabType == POOL_TYPE.IN && '入池') ||
                    (pageInfo.tabType == POOL_TYPE.OUT && '核销') ||
                    ''}
                ：
                <span style={{ color: pageInfo.tabType == POOL_TYPE.OUT ? 'red' : 'green' }}>
                    {poolSummary.totalAmt === undefined ? '-' : poolSummary.totalAmt}
                </span>
                元（电费：{poolSummary.elecAmt === undefined ? '-' : poolSummary.elecAmt}
                元&nbsp;&nbsp;&nbsp;&nbsp;服务费：
                {poolSummary.serviceAmt === undefined ? '-' : poolSummary.serviceAmt}
                元&nbsp;&nbsp;&nbsp;&nbsp;附加费：
                {poolSummary.incrementAmt === undefined ? '-' : poolSummary.incrementAmt}元）
            </div>
        );
    }, [pageInfo, poolSummary, listLoading]);

    return (
        <PageHeaderWrapper>
            <Card>
                <Alert
                    message={alertMessage}
                    type="info"
                    showIcon
                    action={
                        <Space>
                            <Button
                                size="small"
                                type="primary"
                                onClick={() => userModalRef.current.show()}
                            >
                                票池用户
                            </Button>
                            <Button
                                size="small"
                                onClick={() => {
                                    batchModalRef?.current.show();
                                }}
                            >
                                批量入池
                            </Button>
                        </Space>
                    }
                    style={{ marginBottom: '20px' }}
                />

                <SearchLayout
                    {...props}
                    form={form}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                    {poolOptions}
                </Tabs>

                {summaryMessage}

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.workOrderId}
                    dataSource={poolList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: poolTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>

            <InvoicePoolUsersModal
                initRef={userModalRef}
                changeStateEvent={() => {
                    searchData();
                }}
            />
            <BatchPoolModal ref={batchModalRef} onFinish={searchData}></BatchPoolModal>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, loading, user, invoiceManageModel }) => ({
    global,
    listLoading: loading.effects['invoiceManageModel/getInvoicePoolList'],
    currentUser: user.currentUser,
    invoiceManageModel,
}))(InvoicePoolPage);
