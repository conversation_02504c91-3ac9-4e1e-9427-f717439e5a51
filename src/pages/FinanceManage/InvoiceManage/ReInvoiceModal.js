// 重新开票
import { Button, Form, Modal, Space, Input, Radio, Row, Col } from 'antd';
import React, { Fragment, useEffect } from 'react';
import { emailReg } from '@/utils/verify';
import { INVOICE_OPEN_TYPE } from './components/InvoiceChooseOrderModal';

const { TextArea } = Input;

const FormItem = Form.Item;

const inputOptions = {
    // maxLength: 25,
    autoComplete: 'off',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'right',
};

export const InvoiceInputForm = (props) => {
    const {
        isDetails,
        formItemLayout = {},
        /**
         * 1、发票种类：默认开具专票，提示“不支持开专票的运营商将开具普票”。提交申请后识别无法开具专票的运营商改开普票，可开具专票的运营商申请专票。
         * 2、因无法识别统一申请的开票专票纸质发票之类统一将企业开票信息必填，并记录该企业上次开票的申请信息。下次开票申请时自动带入申请信息，客户可以修改再次提交，提交后更新最后的开票信息记录待再次申请时填入。
         * 3、其他开票申请信息与现有功能【发票管理】-【开发票】操作一致。
         */
        fromCompanyFlag,
    } = props;
    return (
        <Fragment>
            <FormItem
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.invoiceType !== curValues.invoiceType
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    const invoiceType = getFieldValue('invoiceType');

                    return (
                        <FormItem
                            name="invoiceTitleType"
                            label="抬头类型"
                            rules={[{ required: true, message: '请选择' }]}
                            initialValue={'02'}
                            {...formItemLayout}
                        >
                            <Radio.Group
                                initialValues={'02'}
                                disabled={invoiceType === '02' || fromCompanyFlag ? true : false}
                            >
                                <Radio value={'02'}>企业单位</Radio>
                                {/* 需求变更，企业开票不允许选择个人类型 */}
                                {!fromCompanyFlag && <Radio value={'01'}>个人/非企业单位</Radio>}
                            </Radio.Group>
                        </FormItem>
                    );
                }}
            </FormItem>

            <Row gutter={24}>
                <Col span={12}>
                    <FormItem
                        label="发票抬头"
                        name="invoiceTitle"
                        rules={[{ required: true, message: '请填写' }]}
                        {...formItemLayout}
                    >
                        <Input
                            maxLength={50}
                            disabled={isDetails}
                            placeholder="请填写"
                            {...inputOptions}
                        />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.invoiceTitleType !== curValues.invoiceTitleType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const invoiceTitleType = getFieldValue('invoiceTitleType');

                            return invoiceTitleType === '02' ? (
                                <FormItem
                                    label="税号"
                                    name="taxNum"
                                    rules={[
                                        { required: true, message: '请填写' },
                                        () => ({
                                            validator(rule, value) {
                                                const taxNumReg = /^[A-Z|0-9]{15}$/g;
                                                const taxNumReg1 = /^[A-Z|0-9]{17}$/g;
                                                const taxNumReg2 = /^[A-Z|0-9]{18}$/g;
                                                const taxNumReg3 = /^[A-Z|0-9]{20}$/g;

                                                const formatTax = value?.toUpperCase();
                                                if (
                                                    !(
                                                        taxNumReg.test(formatTax) ||
                                                        taxNumReg1.test(formatTax) ||
                                                        taxNumReg2.test(formatTax) ||
                                                        taxNumReg3.test(formatTax)
                                                    )
                                                ) {
                                                    return Promise.reject(
                                                        '税号必须由15、17、18、20位大写字母或数字组成',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    {...formItemLayout}
                                >
                                    <Input
                                        disabled={isDetails}
                                        placeholder="请填写"
                                        {...inputOptions}
                                    />
                                </FormItem>
                            ) : null;
                        }}
                    </FormItem>
                </Col>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.invoiceType !== curValues.invoiceType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const invoiceType = getFieldValue('invoiceType');

                        return (
                            <Fragment>
                                <Col span={12}>
                                    <FormItem
                                        label="公司地址"
                                        name="registeredAddr"
                                        rules={[
                                            {
                                                required: invoiceType === '02' ? true : false,
                                                message: '请填写',
                                            },
                                        ]}
                                        {...formItemLayout}
                                    >
                                        <Input
                                            maxLength={50}
                                            disabled={isDetails}
                                            placeholder="请填写"
                                            {...inputOptions}
                                        />
                                    </FormItem>
                                </Col>
                                <Col span={12}>
                                    <FormItem
                                        label="公司电话"
                                        name="registeredMobile"
                                        rules={[
                                            {
                                                required: invoiceType === '02' ? true : false,
                                                message: '请填写',
                                            },
                                        ]}
                                        {...formItemLayout}
                                    >
                                        <Input
                                            maxLength={20}
                                            disabled={isDetails}
                                            placeholder="请填写"
                                            {...inputOptions}
                                        />
                                    </FormItem>
                                </Col>
                                <Col span={12}>
                                    <FormItem
                                        label="开户银行"
                                        name="bankName"
                                        rules={[
                                            {
                                                required: invoiceType === '02' ? true : false,
                                                message: '请填写',
                                            },
                                        ]}
                                        {...formItemLayout}
                                    >
                                        <Input
                                            maxLength={50}
                                            disabled={isDetails}
                                            placeholder="请填写"
                                            {...inputOptions}
                                        />
                                    </FormItem>
                                </Col>
                                <Col span={12}>
                                    <FormItem
                                        label="银行账号"
                                        name="bankAccount"
                                        rules={[
                                            {
                                                required: invoiceType === '02' ? true : false,
                                                message: '请填写',
                                            },
                                        ]}
                                        {...formItemLayout}
                                    >
                                        <Input
                                            maxLength={30}
                                            disabled={isDetails}
                                            placeholder="请填写"
                                            {...inputOptions}
                                        />
                                    </FormItem>
                                </Col>
                            </Fragment>
                        );
                    }}
                </FormItem>

                <Col span={24}>
                    <FormItem label="备注说明" name="invoiceRemark" {...formItemLayout}>
                        <TextArea rows={4} placeholder="请填写" {...inputOptions} maxLength={100} />
                    </FormItem>
                </Col>

                {/*
                    1.邮箱地址、收件信息：开票项目为充电订单（代付）、充电订单时，根据上面的开票方式显示隐藏字段；
                    ①若开票方式包含平台开票、平台对接、平台代开，只显示邮箱地址，不显示收件信息；
                    注：星星充电特殊，需要邮箱地址和开票信息都显示。
                    ②若开票方式包含线下开票，需要看他运营商配置的是纸质发票还是电子发票：
                    仅电子发票：显示邮箱地址，不显示收件信息；
                    仅纸质发票：显示收件信息，不显示邮箱地址；
                    电子和纸质都支持：显示邮箱地址，不显示收件信息；
                    注：若开票方式满足①也满足②，则取并集；
                    2.邮箱地址、收件信息：开票项目为充电卡、打折卡、会员时，仅显示邮箱地址字段；
                */}
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.invoiceBusinessType !== curValues.invoiceBusinessType ||
                        prevValues.splitList !== curValues.splitList
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const invoiceBusinessType = getFieldValue('invoiceBusinessType');
                        const splitList = getFieldValue('splitList');
                        const isXX = splitList?.some((ele) => ele.operId == 313744932);
                        const isYKC = splitList?.some((ele) => ele.operId == 'MA1MY0GF9'); // 云快充
                        // invoiceAccessMode    开票方式：01平台代开；02平台对接；03线下开票；04新电途开票
                        const hasUnderLine = splitList?.some(
                            (ele) => ele.invoiceAccessMode == '03',
                        );
                        // invoiceMedias    支持的发票介质，01-纸质发票，02-电子发票
                        const hasEmail = splitList?.some(
                            (ele) => ele.invoiceMedias?.indexOf('02') >= 0,
                        );
                        const hasReciver = splitList?.some(
                            (ele) => ele.invoiceMedias?.indexOf('01') >= 0,
                        );
                        const showEmail =
                            !invoiceBusinessType || isXX || isYKC || !hasUnderLine || hasEmail;
                        let showRevicer =
                            invoiceBusinessType &&
                            (isXX ||
                                isYKC ||
                                ((invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                                    invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                                    invoiceBusinessType == INVOICE_OPEN_TYPE.CHARGE_ORDER) &&
                                    hasUnderLine &&
                                    hasReciver));
                        if (!isXX && !isYKC && showEmail && showRevicer) {
                            // 电子和纸质都支持:显示邮箱地址，不显示收件信息;星星充电、云快充特殊，需要邮箱地址和收件信息都显示。
                            showRevicer = false;
                        }
                        return (
                            <Fragment>
                                {(showEmail && (
                                    <Fragment>
                                        <Col span={12}>
                                            <FormItem
                                                label="邮箱地址"
                                                name="email"
                                                rules={[
                                                    { required: true, message: '请填写' },
                                                    {
                                                        pattern: emailReg,
                                                        message: '邮箱格式不正确',
                                                    },
                                                ]}
                                                {...formItemLayout}
                                            >
                                                <Input
                                                    disabled={isDetails}
                                                    placeholder="请填写"
                                                    {...inputOptions}
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col span={12} />
                                    </Fragment>
                                )) ||
                                    null}

                                {(showRevicer && (
                                    <Fragment>
                                        <Col span={12}>
                                            <FormItem
                                                label="收件人"
                                                name="recipients"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请填写',
                                                    },
                                                ]}
                                                {...formItemLayout}
                                            >
                                                <Input
                                                    maxLength={50}
                                                    disabled={isDetails}
                                                    placeholder="请填写"
                                                    {...inputOptions}
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col span={12}>
                                            <FormItem
                                                label="收件人电话"
                                                name="phoneNo"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请填写',
                                                    },
                                                ]}
                                                {...formItemLayout}
                                            >
                                                <Input
                                                    maxLength={50}
                                                    disabled={isDetails}
                                                    placeholder="请填写"
                                                    {...inputOptions}
                                                />
                                            </FormItem>
                                        </Col>
                                        <Col span={24}>
                                            <FormItem
                                                label="收件地址"
                                                name="mailAddr"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请填写',
                                                    },
                                                ]}
                                                {...formItemLayout}
                                            >
                                                <TextArea
                                                    rows={4}
                                                    placeholder="请填写"
                                                    {...inputOptions}
                                                    maxLength={100}
                                                />
                                            </FormItem>
                                        </Col>
                                    </Fragment>
                                )) ||
                                    null}
                            </Fragment>
                        );
                    }}
                </FormItem>
            </Row>
        </Fragment>
    );
};

const ReInvoiceModal = (props) => {
    const {
        form,
        showEditWindow,
        closeEditInvoiceEvent,
        curInvoice,
        onFinish,
        isDetails = false,
        dispatch,
        invoiceManageModel: {
            editActInfo: { invoiceDetail = {}, sumInvoiceApplist = {} },
        },
    } = props;

    useEffect(() => {
        if (!curInvoice?.invoiceAppNo) return;
        dispatch({
            type: 'invoiceManageModel/initEditActInfo',
            invoiceAppNo: curInvoice?.invoiceAppNo,
        });
    }, [curInvoice]);

    useEffect(() => {
        if (invoiceDetail) {
            const params = { ...invoiceDetail };
            if (!params.invoiceType || params.invoiceType.indexOf(',') >= 0) {
                params.invoiceType = '01';
            }
            form?.resetFields();
        }
    }, [invoiceDetail]);

    // 切换发票种类处理
    const onChangeInvoiceType = (item) => {
        if (item.target.value === '02') {
            form.setFieldsValue({ invoiceTitleType: '02' });
        }
    };

    return (
        <Modal
            title="重开发票"
            visible={showEditWindow}
            footer={false}
            onCancel={closeEditInvoiceEvent}
            maskClosable={false}
            width={800}
            destroyOnClose
        >
            <Form
                {...formItemLayout}
                initialValues={{ ...invoiceDetail, invoiceType: '01' }}
                form={form}
                onFinish={onFinish}
                scrollToFirstError
            >
                <Fragment>
                    <FormItem label="发票金额" name="invoiceTime">
                        <div style={{ color: 'red' }}>{curInvoice?.invoiceAmt}</div>
                    </FormItem>
                    <FormItem
                        name="invoiceType"
                        label="发票种类"
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={'01'}
                    >
                        <Radio.Group onChange={onChangeInvoiceType}>
                            <Radio value={'01'}>普通发票</Radio>
                            <Radio value={'02'} disabled={invoiceDetail?.supportTicketFlag !== '1'}>
                                专用发票
                            </Radio>
                        </Radio.Group>
                    </FormItem>

                    <InvoiceInputForm isDetails={isDetails} formItemLayout={formItemLayout} />
                </Fragment>

                <FormItem style={{ textAlign: 'center' }}>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEditInvoiceEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default ReInvoiceModal;
