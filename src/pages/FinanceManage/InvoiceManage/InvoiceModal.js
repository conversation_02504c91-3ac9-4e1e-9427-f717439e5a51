import { Button, Form, Modal, Space, Input, DatePicker, Upload, message, Spin } from 'antd';
import { Fragment, useState, useMemo } from 'react';

import { uploadFile } from '@/services/CommonApi';
import moment from 'moment';
import styles from '@/assets/styles/common.less';
import { PlusOutlined } from '@ant-design/icons';

const { TextArea } = Input;

const FormItem = Form.Item;

const inputOptions = {
    maxLength: 25,
    autoComplete: 'off',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'right',
};

const InvoiceModal = (props) => {
    const {
        form,
        showEditWindow,
        closeEditInvoiceEvent,
        curInvoice,
        onFinish,
        submitLoading,
        fileList,
        updateFileList,
        isDetails = false,
    } = props;
    const [uploadLoading, changeUploading] = useState(false); // 上传加载

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg' ||
                file.type === 'application/pdf';
            if (!isJpgOrPng) {
                message.error('文件格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 / 1024 < 1;
            if (!isLt2M) {
                message.error(`文件不大于 1mb!`);
                rej();
                return;
            }

            res();
        });

    const uploadProps = useMemo(() => {
        const options = {
            contentType: '02',
        };
        if (curInvoice && curInvoice.invoiceAppNo) {
            options.invoiceAppNo = curInvoice.invoiceAppNo;
        }
        return {
            name: 'avatar',
            className: styles['avatar-uploader'],
            accept: '.jpeg,.png,.jpg,.pdf',
            fileList,
            data: {
                contRemrk: 'btnFile',
                relaTable: 'd_account_invoice_app',
                ...options,
            },
            customRequest: async ({
                // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
                action,
                data,
                file,
                filename,
                headers,
                onError,
                onProgress,
                onSuccess,
                withCredentials,
            }) => {
                try {
                    const params = {
                        ...data,
                    };

                    const isJpgOrPng =
                        file.type === 'image/jpg' ||
                        file.type === 'image/png' ||
                        file.type === 'image/jpeg';
                    if (isJpgOrPng) {
                        params.contentType = '02';
                    } else {
                        params.contentType = '01';
                    }

                    if (params.invoiceAppNo) {
                        params.relaId = params.invoiceAppNo;
                    }

                    const formData = new FormData();
                    if (params) {
                        Object.keys(params).forEach((key) => {
                            formData.append(key, params[key]);
                        });
                    }
                    formData.append(data.contRemrk, file);

                    changeUploading(true);
                    const {
                        data: { filePath, fileId, relativePath },
                    } = await uploadFile(formData);
                    onSuccess(filePath, params.contentType);
                    fileList.push({
                        uid: relativePath,
                        name: filePath,
                        status: 'done',
                        url: filePath,
                        thumbUrl: filePath,
                    });
                    updateFileList([...fileList]);
                } catch (error) {
                } finally {
                    changeUploading(false);
                }

                return {
                    abort() {
                        console.log('upload progress is aborted.');
                    },
                };
            },
            onRemove: (file) => {
                const removeIndex = fileList.findIndex((ele) => ele.uid == file.uid);
                fileList.splice(removeIndex, 1);
                updateFileList([...fileList]);
                return true;
            },
            listType: 'picture-card',
            // onSuccess: handleUploadChange,
            // onChange: handleUploadChange,
            beforeUpload,
        };
    }, [curInvoice, fileList]);
    return (
        <Modal
            title="开票处理"
            visible={showEditWindow}
            footer={false}
            onCancel={closeEditInvoiceEvent}
            maskClosable={false}
        >
            <Form
                {...formItemLayout}
                initialValues={{ ...curInvoice }}
                form={form}
                onFinish={onFinish}
                scrollToFirstError
            >
                <Fragment>
                    <FormItem
                        label="开票日期"
                        name="invoiceTime"
                        initialValue={moment()}
                        rules={[{ required: true, message: '请选择日期' }]}
                    >
                        <DatePicker
                            disabled={isDetails}
                            style={{ width: '100%' }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                    <FormItem label="发票代码" name="invoiceCode">
                        <Input disabled={isDetails} placeholder="请填写" {...inputOptions} />
                    </FormItem>
                    <FormItem label="发票号码" name="invoiceNum">
                        <Input disabled={isDetails} placeholder="请填写" {...inputOptions} />
                    </FormItem>

                    <FormItem
                        label="备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注"
                        name="remark"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <TextArea rows={4} placeholder="请填写" {...inputOptions} maxLength={50} />
                    </FormItem>

                    {curInvoice.invoiceMedia != '02' ? (
                        <Fragment>
                            <FormItem
                                label="快递公司"
                                name="courierCompany"
                                // rules={[{ required: true, message: '请填写' }]}
                            >
                                <Input placeholder="请填写" {...inputOptions} />
                            </FormItem>
                            <FormItem
                                label="快递单号"
                                name="courierNo"
                                // rules={[{ required: true, message: '请填写' }]}
                            >
                                <Input placeholder="请填写" {...inputOptions} />
                            </FormItem>
                        </Fragment>
                    ) : null}

                    <FormItem label="发票附件&nbsp;&nbsp;">
                        <Fragment>
                            <Upload {...uploadProps}>
                                {fileList.length < 10 ? (
                                    <Spin spinning={uploadLoading}>上传附件</Spin>
                                ) : null}
                            </Upload>
                            <div>支持jpg、jpeg、png、pdf格式</div>
                        </Fragment>
                    </FormItem>
                </Fragment>

                <FormItem>
                    <Space>
                        <Button type="primary" htmlType="submit" loading={submitLoading}>
                            提交
                        </Button>
                        <Button onClick={closeEditInvoiceEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default InvoiceModal;
