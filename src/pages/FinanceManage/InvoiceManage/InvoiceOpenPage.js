import TablePro from '@/components/TablePro';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ert,
    Button,
    Card,
    Form,
    Input,
    Popconfirm,
    Radio,
    Select,
    Space,
    Spin,
    Tooltip,
    message,
} from 'antd';
import { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { connect, useLocation } from 'umi';
import InvoiceChooseOrderModal, {
    INVOICE_OPEN_TYPE,
    invoiceOpenColumns,
} from './components/InvoiceChooseOrderModal';
import commonStyles from '@/assets/styles/common.less';
import { InvoiceInputForm } from './ReInvoiceModal';
import { InfoCircleOutlined, LeftOutlined, LoadingOutlined } from '@ant-design/icons';
import { getUserViewApi } from '@/services/UserManage/UserApi';
import { isEmpty } from '@/utils/utils';
import { isPhoneNumber } from '@/utils/verify';
import {
    applyInvoiceApi,
    getInvoiceSplitResultApi,
    getInvoiceTotalAmtApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import { getOperCompanyListApi } from '@/services/Enterprise/EnterpriseManageApi';
import ChannelSelect from '@/components/FormItem/ChannelSelect';
import {
    getEnterpriseCompanyInvoiceListApi,
    lastEnterpriseCompanyInvoiceApi,
    submitEnterpriseCompanyInvoiceApi,
} from '@/services/Enterprise/EnterpriseOrderApi';

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'right',
};
const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const InvoiceOpenPage = (props) => {
    const {
        dispatch,
        global: { codeInfo = {} },
    } = props;
    const { query = {} } = useLocation();
    const { companyId, month } = query;
    const [form] = Form.useForm();
    const [chooseStations, updateChooseStations] = useState([]);
    const selectOrderModal = useRef();
    const openType = Form.useWatch('invoiceBusinessType', form);
    const rowSelection = {
        fixed: true,
        type: 'checkbox',
        selectedRowKeys: chooseStations.map((item) => item.orderNo || item.buyNo),
        onChange: (selectedRowKeys, selectedRows) => {
            updateChooseStations(selectedRows);
        },
    };

    const { invoiceBusinessType } = codeInfo;

    // 手机号交互
    const [mobileLoading, updateMoblieLoading] = useState(false);
    const [mobileErrorTip, updateMobileErrorTip] = useState(undefined); // undefined表示没问题
    const checkMobileEvent = async (input_mobile) => {
        try {
            const mobile = form.getFieldValue('mobile');
            if (mobile != input_mobile) {
                console.log(mobile, input_mobile);
                form.setFieldsValue({
                    mobile: undefined,
                    orderList: [],
                });
            }
            if (isEmpty(input_mobile)) {
                updateMobileErrorTip('请输入申请人电话');
                return;
            }
            if (!isPhoneNumber(input_mobile)) {
                updateMobileErrorTip('请检查申请人电话格式');
                return;
            }
            updateMoblieLoading(true);
            const { data } = await getUserViewApi({
                mobile: input_mobile,
            });
            const current_input_mobile = form.getFieldValue('input_mobile');
            if (input_mobile != current_input_mobile) {
                // 已经不是当前查的手机号，不予处理
                return;
            }
            if (data?.userState == 1) {
                // 已注册电话
                form.setFieldsValue({
                    mobile: input_mobile,
                });
                updateMobileErrorTip(undefined);
            } else {
                // 未注册电话
                updateMobileErrorTip('申请人未注册');
            }
        } catch (error) {
            updateMobileErrorTip('异常');
        } finally {
            updateMoblieLoading(false);
        }
    };

    const [companyList, updateCompanyList] = useState([]);
    useEffect(() => {
        if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP && !companyList?.length && !companyId) {
            initCompany();
        }
    }, [openType, companyList]);
    useEffect(() => {
        if (companyId) {
            loadCompanyOrders();
            loadLastCompanyInvoiceInfo();
        }

        if (!invoiceBusinessType?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'invoiceBusinessType',
            });
        }
    }, []);
    const initCompany = async (operId) => {
        try {
            const {
                data: { operCompanyList },
            } = await getOperCompanyListApi({ operId });
            updateCompanyList(operCompanyList);
        } catch (error) {}
    };
    const loadCompanyOrders = async () => {
        try {
            const {
                data: { details: splitList, total },
            } = await getEnterpriseCompanyInvoiceListApi({ companyId, month });
            form.setFieldsValue({ splitList, total });
        } catch (error) {}
    };
    const loadLastCompanyInvoiceInfo = async () => {
        try {
            const { data = {} } = await lastEnterpriseCompanyInvoiceApi({ companyId });
            form.setFieldsValue(data);
        } catch (error) {}
    };

    const enterpriseDropDownOptions = useMemo(() => {
        if (companyList) {
            return companyList.map((ele) => (
                <Select.Option key={ele.companyId} value={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Select.Option>
            ));
        }
        return [];
    }, [companyList]);

    // 订单变动后，要触发查看发票金额事件
    const orderListValue = Form.useWatch('orderList', form);
    useEffect(() => {
        getInvoiceInfo();
        form.setFieldsValue({ num: orderListValue?.length || 0 });
    }, [orderListValue, openType]);

    // 发票信息
    const getInvoiceInfo = async () => {
        try {
            const { splitList, mobile, invoiceTotalAmt } = form.getFieldsValue();
            if (orderListValue?.length) {
                const params = {
                    appNos: orderListValue?.map((ele) => ele.orderNo || ele.buyNo),
                    invoiceBusinessType: openType,
                    mobile,
                };
                if (
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER
                ) {
                    if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP) {
                        const companyId = form.getFieldValue('companyId');
                        params.companyId = companyId;
                        delete params.mobile;
                    } else if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL) {
                        const applyMode = form.getFieldValue('applyMode');
                        params.applyMode = applyMode;
                        delete params.mobile;
                    }
                    form.setFieldsValue({ splitList: [] });
                    const { data = [] } = await getInvoiceSplitResultApi(params);
                    const current_invoiceBusinessType = form.getFieldValue('invoiceBusinessType');
                    if (openType != current_invoiceBusinessType) {
                        return;
                    }
                    const fieldParams = { splitList: data };
                    if (data.length && data.some((ele) => ele.invoiceAccessMode !== '04')) {
                        // 当开票项目为充电订单（代付）、充电订单时，若包含平台开票以外的，则专用发票选项禁用；
                        // invoiceAccessMode    开票方式：01平台代开；02平台对接；03线下开票；04新电途开票
                        fieldParams.invoiceType = '01';
                    }
                    form.setFieldsValue(fieldParams);
                } else if (
                    openType == INVOICE_OPEN_TYPE.CHARGE_CARD ||
                    openType == INVOICE_OPEN_TYPE.DISCOUNT_CARD ||
                    openType == INVOICE_OPEN_TYPE.VIP // 会员的发票金额先删了，后端卡壳
                ) {
                    form.setFieldsValue({ invoiceTotalAmt: undefined });
                    const { data } = await getInvoiceTotalAmtApi(params);
                    const current_invoiceBusinessType = form.getFieldValue('invoiceBusinessType');
                    if (openType != current_invoiceBusinessType) {
                        return;
                    }
                    form.setFieldsValue({ invoiceTotalAmt: data });
                }
            } else if (splitList?.length) {
                form.setFieldsValue({ splitList: [] });
            } else if (invoiceTotalAmt !== undefined) {
                form.setFieldsValue({ invoiceTotalAmt: undefined });
            }
        } catch (error) {}
    };

    const submitEvent = async (values) => {
        try {
            const params = {
                ...values,
                appNos: values.orderList?.map((ele) => ele.orderNo || ele.buyNo),
            };
            delete params.orderList;
            delete params.splitList;
            delete params.invoiceTotalAmt;
            delete params.input_mobile;
            delete params.temp_list_2;
            if (companyId) {
                delete params.total;
                delete params.appNos;
                await submitEnterpriseCompanyInvoiceApi(params);
            } else {
                await applyInvoiceApi(params);
            }
            message.success('操作成功');
            goBack();
        } catch (error) {}
    };

    const goBack = () => {
        if (companyId) {
            props.history.replace('/userCenter/enterprise/order/list');
        } else {
            props.history.replace('/financemanage/invoice/manage');
        }
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {props.route?.title}
                </div>
            }
        >
            <Card>
                <Form form={form} onFinish={submitEvent} {...formItemLayout}>
                    <div className={commonStyles['form-title']}>订单信息</div>
                    {companyId ? (
                        <Fragment>
                            <Form.Item
                                name="invoiceBusinessType"
                                initialValue={INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP}
                                noStyle
                            />
                            <Form.Item name="companyId" initialValue={companyId} noStyle />
                            <Form.Item name="month" initialValue={month} noStyle />
                            <Form.Item name="splitList" noStyle />
                            <Form.Item name="total" noStyle />

                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.splitList != cur.splitList || pre.total != cur.total
                                }
                                noStyle
                            >
                                {({ getFieldValue, setFieldsValue }) => {
                                    const splitList = getFieldValue('splitList') || [];
                                    const total = getFieldValue('total');
                                    const tableBar =
                                        total &&
                                        `订单数：${total?.orderNum || ''}个；充电量${
                                            total?.chargePq || ''
                                        }KWH；开票总金额：${total?.invoiceAmt || ''}元；开票电费：${
                                            total?.invoiceElecAmt || ''
                                        }元；开票服务费（含附加费）：${
                                            total?.invoiceServiceIncrementAmt || ''
                                        }元；订单总金额：${total?.orderAmt || ''}元`;
                                    return (
                                        <Fragment>
                                            {(total && (
                                                <Alert message={tableBar} type="info" showIcon />
                                            )) ||
                                                null}
                                            <TablePro
                                                scroll={{ x: 'max-content', y: 400 }}
                                                dataSource={splitList}
                                                columns={[
                                                    {
                                                        title: '运营商',
                                                        width: 180,
                                                        dataIndex: 'operName',
                                                        render(text, record) {
                                                            return <span title={text}>{text}</span>;
                                                        },
                                                    },

                                                    {
                                                        title: '开票方式',
                                                        width: 180,
                                                        dataIndex: 'invoiceAccessModeName',
                                                        render(text, record) {
                                                            return <span title={text}>{text}</span>;
                                                        },
                                                    },
                                                    {
                                                        title: '开票总金额',
                                                        width: 140,
                                                        dataIndex: 'invoiceAmt',
                                                        align: 'right',
                                                        render(text, record) {
                                                            return (
                                                                <span
                                                                    style={{ color: '#f50' }}
                                                                    title={text}
                                                                >
                                                                    {text}
                                                                </span>
                                                            );
                                                        },
                                                    },
                                                    {
                                                        title: '电费金额',
                                                        width: 160,
                                                        dataIndex: 'invoiceElecAmt',
                                                        align: 'right',
                                                        render(text, record) {
                                                            return (
                                                                <span
                                                                    style={{ color: '#f50' }}
                                                                    title={text}
                                                                >
                                                                    {text}
                                                                </span>
                                                            );
                                                        },
                                                    },
                                                    {
                                                        title: '服务费金额',
                                                        width: 180,
                                                        dataIndex: 'invoiceServiceAmt',
                                                        align: 'right',
                                                        render(text, record) {
                                                            return (
                                                                <span
                                                                    style={{ color: '#f50' }}
                                                                    title={text}
                                                                >
                                                                    {text}
                                                                </span>
                                                            );
                                                        },
                                                    },
                                                    {
                                                        title: '占位费金额',
                                                        width: 180,
                                                        dataIndex: 'invoiceIncrementAmt',
                                                        align: 'right',
                                                        render(text, record) {
                                                            return (
                                                                <span
                                                                    style={{ color: '#f50' }}
                                                                    title={text}
                                                                >
                                                                    {text}
                                                                </span>
                                                            );
                                                        },
                                                    },
                                                ]}
                                                noSort
                                                pagination={false}
                                            />
                                        </Fragment>
                                    );
                                }}
                            </Form.Item>
                            <br />
                        </Fragment>
                    ) : (
                        <Fragment>
                            <Form.Item
                                label="开票项目"
                                name="invoiceBusinessType"
                                initialValue={
                                    companyId
                                        ? INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                        : INVOICE_OPEN_TYPE.CHARGE_ORDER
                                }
                                required
                            >
                                <Radio.Group
                                    onChange={() => {
                                        form.setFieldsValue({
                                            mobile: undefined,
                                            companyId: undefined,
                                            input_mobile: undefined,
                                            orderList: undefined,
                                        });
                                    }}
                                >
                                    {invoiceBusinessType?.map((ele) => (
                                        <Radio key={ele.codeValue} value={ele.codeValue}>
                                            {ele.codeName}
                                        </Radio>
                                    ))}
                                    {/* <Radio value={INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL}>
                                        充电订单（渠道）
                                    </Radio>
                                    <Radio value={INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP}>
                                        充电订单（代付）
                                    </Radio>
                                    <Radio value={INVOICE_OPEN_TYPE.CHARGE_ORDER}>
                                        充电订单（含安心充）
                                    </Radio>
                                    <Radio value={INVOICE_OPEN_TYPE.DISCOUNT_CARD}>打折卡</Radio>
                                    <Radio value={INVOICE_OPEN_TYPE.CHARGE_CARD}>充电卡</Radio>
                                    <Radio value={INVOICE_OPEN_TYPE.VIP}>会员</Radio> */}
                                </Radio.Group>
                            </Form.Item>

                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.invoiceBusinessType != cur.invoiceBusinessType
                                }
                                noStyle
                            >
                                {({ getFieldValue, setFieldsValue }) => {
                                    const invoiceBusinessType =
                                        getFieldValue('invoiceBusinessType');
                                    return (
                                        <>
                                            {invoiceBusinessType !=
                                                INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP &&
                                                invoiceBusinessType !=
                                                    INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL && (
                                                    <Fragment>
                                                        <Form.Item
                                                            label="申请人电话"
                                                            name={'input_mobile'}
                                                            required
                                                            {...formItemFixedWidthLayout}
                                                            help={mobileErrorTip}
                                                            validateStatus={
                                                                mobileErrorTip
                                                                    ? mobileErrorTip?.length
                                                                        ? 'error'
                                                                        : 'success'
                                                                    : undefined
                                                            }
                                                            rules={[
                                                                () => ({
                                                                    validator(rule, value) {
                                                                        if (
                                                                            mobileErrorTip?.length
                                                                        ) {
                                                                            return Promise.reject(
                                                                                mobileErrorTip,
                                                                            );
                                                                        }
                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                        >
                                                            <Input
                                                                placeholder="请填写"
                                                                autoComplete="off"
                                                                onBlur={(e) =>
                                                                    checkMobileEvent(e.target.value)
                                                                }
                                                                onChange={() => {
                                                                    form.setFieldsValue({
                                                                        mobile: undefined,
                                                                        orderList: [],
                                                                    });
                                                                    updateMobileErrorTip(undefined);
                                                                }}
                                                                suffix={
                                                                    <Spin
                                                                        spinning={mobileLoading}
                                                                        indicator={
                                                                            <LoadingOutlined />
                                                                        }
                                                                    />
                                                                }
                                                            />
                                                        </Form.Item>

                                                        <Form.Item name="mobile" noStyle />
                                                    </Fragment>
                                                )}
                                            {invoiceBusinessType ==
                                                INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP && (
                                                <Form.Item
                                                    label="选择企业"
                                                    name="companyId"
                                                    {...formItemFixedWidthLayout}
                                                    rules={[{ required: true, message: '请选择' }]}
                                                >
                                                    <Select
                                                        placeholder="请选择企业"
                                                        showSearch
                                                        filterOption={(input, option) =>
                                                            option.children
                                                                ?.toLowerCase()
                                                                .indexOf(input?.toLowerCase()) >= 0
                                                        }
                                                        allowClear
                                                        onChange={(value) => {
                                                            const selectObj =
                                                                value &&
                                                                companyList?.find(
                                                                    (ele) => ele.companyId == value,
                                                                );
                                                            setFieldsValue({
                                                                input_mobile:
                                                                    selectObj?.mobile || undefined,
                                                                mobile:
                                                                    selectObj?.mobile || undefined,
                                                                orderList: [],
                                                            });
                                                        }}
                                                    >
                                                        {enterpriseDropDownOptions}
                                                    </Select>
                                                </Form.Item>
                                            )}
                                            {invoiceBusinessType ==
                                                INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL && (
                                                <Form.Item
                                                    label="选择渠道"
                                                    name="applyMode"
                                                    {...formItemFixedWidthLayout}
                                                    rules={[{ required: true, message: '请选择' }]}
                                                >
                                                    <ChannelSelect
                                                        onChangeOption={(option) => {
                                                            setFieldsValue({
                                                                orderList: [],
                                                                mobile: option?.mobile,
                                                            });
                                                        }}
                                                    />
                                                </Form.Item>
                                            )}
                                        </>
                                    );
                                }}
                            </Form.Item>

                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.invoiceBusinessType != cur.invoiceBusinessType ||
                                    pre.orderList != cur.orderList ||
                                    pre.mobile != cur.mobile ||
                                    pre.companyId != cur.companyId ||
                                    pre.applyMode != cur.applyMode
                                }
                                noStyle
                            >
                                {({ getFieldValue, setFieldsValue }) => {
                                    const invoiceBusinessType =
                                        getFieldValue('invoiceBusinessType');
                                    const orderList = getFieldValue('orderList') || [];
                                    const mobile = getFieldValue('mobile');
                                    const companyId = getFieldValue('companyId');
                                    const applyMode = getFieldValue('applyMode');
                                    return (
                                        <Fragment>
                                            <Form.Item
                                                label="选择订单"
                                                name="num"
                                                required
                                                rules={[
                                                    () => ({
                                                        validator(rule, value) {
                                                            if (!value) {
                                                                return Promise.reject('请选择订单');
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <Button
                                                    type="primary"
                                                    onClick={() => {
                                                        const params = {};
                                                        if (
                                                            invoiceBusinessType ==
                                                            INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                                        ) {
                                                            const companyId =
                                                                form.getFieldValue('companyId');
                                                            params.companyId = companyId;
                                                        } else if (
                                                            invoiceBusinessType ==
                                                            INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL
                                                        ) {
                                                            const applyMode =
                                                                form.getFieldValue('applyMode');
                                                            params.applyMode = applyMode;
                                                        } else {
                                                            const mobile =
                                                                form.getFieldValue('mobile');
                                                            params.mobile = mobile;
                                                        }
                                                        selectOrderModal.current?.show({
                                                            disabledOrders: orderList,
                                                            defaultList: orderList,
                                                            params,
                                                        });
                                                    }}
                                                    disabled={
                                                        invoiceBusinessType ==
                                                        INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                                            ? !companyId
                                                            : invoiceBusinessType ==
                                                              INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL
                                                            ? !applyMode
                                                            : !isPhoneNumber(mobile)
                                                    }
                                                >
                                                    选择
                                                </Button>
                                            </Form.Item>
                                            <Form.Item
                                                label=" "
                                                name="orderList"
                                                colon={false}
                                                noStyle
                                            />
                                            {(orderList?.length && (
                                                <Form.Item label=" " colon={false}>
                                                    <Popconfirm
                                                        title={'确定批量移除？'}
                                                        onConfirm={() => {
                                                            for (const item of chooseStations) {
                                                                const index =
                                                                    orderList.indexOf(item);
                                                                orderList.splice(index, 1);
                                                                setFieldsValue({
                                                                    orderList: orderList,
                                                                });
                                                                updateChooseStations([]);
                                                            }
                                                        }}
                                                    >
                                                        <Button
                                                            style={{ marginBottom: '8px' }}
                                                            disabled={chooseStations?.length == 0}
                                                        >
                                                            批量移除
                                                        </Button>
                                                    </Popconfirm>
                                                    <TablePro
                                                        rowSelection={rowSelection}
                                                        scroll={{ x: 'max-content', y: 400 }}
                                                        rowKey={(record, index) =>
                                                            record.orderNo || record.buyNo
                                                        }
                                                        dataSource={orderList}
                                                        offsetHeader={false}
                                                        columns={[
                                                            ...(invoiceOpenColumns(
                                                                invoiceBusinessType,
                                                            ) || []),
                                                            {
                                                                title: '操作',
                                                                width: 140,
                                                                fixed: 'right',
                                                                render(text, record) {
                                                                    return (
                                                                        <Popconfirm
                                                                            title={'确定移除？'}
                                                                            onConfirm={() => {
                                                                                orderList.splice(
                                                                                    orderList.indexOf(
                                                                                        record,
                                                                                    ),
                                                                                    1,
                                                                                );
                                                                                setFieldsValue({
                                                                                    orderList:
                                                                                        orderList,
                                                                                });
                                                                                if (
                                                                                    chooseStations.some(
                                                                                        (ele) =>
                                                                                            ele.equipId ==
                                                                                            record.equipId,
                                                                                    )
                                                                                ) {
                                                                                    const index =
                                                                                        chooseStations.findIndex(
                                                                                            (ele) =>
                                                                                                ele.equipId ==
                                                                                                record.equipId,
                                                                                        );
                                                                                    chooseStations.splice(
                                                                                        index,
                                                                                        1,
                                                                                    );
                                                                                    updateChooseStations(
                                                                                        [
                                                                                            ...chooseStations,
                                                                                        ],
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            }}
                                                                        >
                                                                            <Button type="link">
                                                                                移除
                                                                            </Button>
                                                                        </Popconfirm>
                                                                    );
                                                                },
                                                            },
                                                        ]}
                                                        noSort
                                                        pagination={false}
                                                    />
                                                </Form.Item>
                                            )) ||
                                                null}
                                            <InvoiceChooseOrderModal
                                                initRef={selectOrderModal}
                                                openType={invoiceBusinessType}
                                                onFinish={(values) => {
                                                    let newOrderList = [...values];

                                                    // 按结算时间降序重排
                                                    newOrderList.sort((a, b) => {
                                                        if (
                                                            openType ==
                                                                INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                                                            openType ==
                                                                INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                                                            openType ==
                                                                INVOICE_OPEN_TYPE.CHARGE_ORDER
                                                        ) {
                                                            return b.settleTime > a.settleTime
                                                                ? 1
                                                                : -1;
                                                        } else {
                                                            return b.buyTime > a.buyTime ? 1 : -1;
                                                        }
                                                    });
                                                    // if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER) {
                                                    //     //只能选一种渠道的订单 不能交叉选择
                                                    //     let cooperationPlatforms = [];
                                                    //     for (const item of newOrderList) {
                                                    //         cooperationPlatforms.push(
                                                    //             item.cooperationPlatform,
                                                    //         );
                                                    //         cooperationPlatforms = [
                                                    //             ...new Set(cooperationPlatforms),
                                                    //         ];
                                                    //         if (cooperationPlatforms.length > 1) {
                                                    //             message.error(
                                                    //                 '只能选择同一种一级渠道的订单开票，请重新选择',
                                                    //             );
                                                    //             return;
                                                    //         }
                                                    //     }
                                                    // }
                                                    setFieldsValue({ orderList: newOrderList });
                                                }}
                                            />
                                        </Fragment>
                                    );
                                }}
                            </Form.Item>

                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.invoiceBusinessType != cur.invoiceBusinessType
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const invoiceBusinessType =
                                        getFieldValue('invoiceBusinessType');
                                    return (
                                        [
                                            INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP,
                                            INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL,
                                        ].includes(invoiceBusinessType) && (
                                            <Form.Item
                                                label="申请人电话"
                                                name={'mobile'}
                                                required
                                                {...formItemFixedWidthLayout}
                                                rules={[
                                                    () => ({
                                                        validator(rule, value) {
                                                            if (!isPhoneNumber(value)) {
                                                                return Promise.reject(
                                                                    '请检查电话格式',
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <Input placeholder="请填写" autoComplete="off" />
                                            </Form.Item>
                                        )
                                    );
                                }}
                            </Form.Item>
                        </Fragment>
                    )}

                    <div className={commonStyles['form-title']}>发票信息</div>

                    {(!companyId && openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP) ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER ? (
                        <Form.Item
                            shouldUpdate={(pre, cur) => pre.splitList != cur.splitList}
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const splitList = getFieldValue('splitList');
                                return (
                                    (splitList?.length && (
                                        <Form.Item
                                            label=""
                                            name="splitList"
                                            colon={false}
                                            wrapperCol={{ span: 16 }}
                                        >
                                            <TablePro
                                                rowKey={(record, index) =>
                                                    `${record.operId}_${record.invoiceAmt}`
                                                }
                                                dataSource={splitList}
                                                offsetHeader={false}
                                                columns={[
                                                    {
                                                        title: '运营商',
                                                        width: 140,
                                                        dataIndex: 'operName',
                                                    },
                                                    {
                                                        title: '开票方式',
                                                        width: 140,
                                                        dataIndex: 'invoiceAccessModeName',
                                                    },
                                                    {
                                                        title: '发票金额',
                                                        width: 140,
                                                        dataIndex: 'invoiceAmt',
                                                        render(text, record) {
                                                            return (
                                                                <span
                                                                    style={{
                                                                        color: '#f50',
                                                                        fontWeight: '500',
                                                                    }}
                                                                    title={text}
                                                                >
                                                                    {text}
                                                                </span>
                                                            );
                                                        },
                                                    },
                                                ]}
                                                noSort
                                                pagination={false}
                                            />
                                        </Form.Item>
                                    )) ||
                                    null
                                );
                            }}
                        </Form.Item>
                    ) : (
                        !companyId && (
                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.invoiceTotalAmt != cur.invoiceTotalAmt
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const invoiceTotalAmt = getFieldValue('invoiceTotalAmt');
                                    return (
                                        <Form.Item label="发票金额" name="invoiceTotalAmt">
                                            {isEmpty(invoiceTotalAmt) ? (
                                                '-'
                                            ) : (
                                                <span style={{ color: '#f50', fontWeight: '500' }}>
                                                    {`${invoiceTotalAmt}元`}
                                                </span>
                                            )}
                                        </Form.Item>
                                    );
                                }}
                            </Form.Item>
                        )
                    )}

                    <Form.Item
                        shouldUpdate={(pre, cur) =>
                            pre.splitList != cur.splitList ||
                            pre.invoiceBusinessType != cur.invoiceBusinessType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const splitList = getFieldValue('splitList');
                            const invoiceBusinessType = getFieldValue('invoiceBusinessType');
                            return (
                                <Form.Item
                                    name="invoiceType"
                                    label="发票种类"
                                    rules={[{ required: true, message: '请选择' }]}
                                    initialValue={companyId ? '02' : '01'}
                                >
                                    {/* // invoiceAccessMode    开票方式：01平台代开；02平台对接；03线下开票；04新电途开票 */}
                                    <Radio.Group
                                        onChange={(item) => {
                                            if (item.target.value === '02') {
                                                form.setFieldsValue({ invoiceTitleType: '02' });
                                            }
                                        }}
                                    >
                                        {(!companyId && <Radio value={'01'}>普通发票</Radio>) ||
                                            null}
                                        <Radio
                                            value={'02'}
                                            disabled={
                                                (invoiceBusinessType ==
                                                    INVOICE_OPEN_TYPE.CHARGE_ORDER ||
                                                    invoiceBusinessType ==
                                                        INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                                                    invoiceBusinessType ==
                                                        INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL) &&
                                                splitList?.length &&
                                                splitList.some(
                                                    (ele) => ele.supportTicketFlag !== '1',
                                                )
                                            }
                                        >
                                            {companyId ? (
                                                <div>
                                                    申请专票
                                                    <span
                                                        style={{ color: '#f50', marginLeft: '6px' }}
                                                    >
                                                        {`(不支持开专票的运营商将开具普票)`}
                                                    </span>
                                                </div>
                                            ) : (
                                                <div>
                                                    专用发票
                                                    <Tooltip title="禁用时表示所选订单包含不支持开专票的运营商">
                                                        <InfoCircleOutlined
                                                            style={{ marginLeft: '6px' }}
                                                        />
                                                    </Tooltip>
                                                </div>
                                            )}
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            );
                        }}
                    </Form.Item>

                    <Form.Item label="" name="temp_list_2" colon={false} wrapperCol={{ span: 16 }}>
                        <InvoiceInputForm
                            isDetails={false}
                            formItemLayout={formItemLayout}
                            fromCompanyFlag={companyId !== undefined}
                        />
                    </Form.Item>

                    <Form.Item style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={goBack}>取消</Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ user, financeModel, invoiceManageModel, global, loading }) => ({
    financeModel,
    invoiceManageModel,
    global,
    user,
    applylistLoading: loading.effects['invoiceManageModel/getInvoiceApplyList'],
    statisticslistLoading: loading.effects['invoiceManageModel/getInvoiceStatisticsList'],
    stocklistLoading: loading.effects['invoiceManageModel/getInvoiceStockList'],
}))(InvoiceOpenPage);
