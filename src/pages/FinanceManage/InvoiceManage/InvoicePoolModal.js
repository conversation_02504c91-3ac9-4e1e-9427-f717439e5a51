// 重新开票
import { Button, Form, message, Modal, Space } from 'antd';
import { useState, useMemo } from 'react';
import { useImperativeHandle } from 'react';
import { InvoiceInputForm } from './ReInvoiceModal';
import { connect } from 'umi';
import { submitInvoicePoolApi } from '@/services/FinanceManage/InvoiceManageApi';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'right',
};

const InvoicePoolModal = (props) => {
    const {
        initRef,
        onFinish,
        dispatch,
        invoiceManageModel: { poolBlance },
    } = props;
    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [editItem, updateEditItem] = useState();
    const [isWaiting, updateWaiting] = useState(false);
    useImperativeHandle(initRef, () => ({
        show: (item) => {
            updateVisible(true);
            updateEditItem(item);
            form.resetFields();
            form.setFieldsValue({ invoiceType: '01', ...item });

            dispatch({
                type: 'invoiceManageModel/getInvoicePoolBalance',
                callback: (data) => {
                    compareBalances(item, data);
                },
            });
        },
    }));

    const compareBalances = (currentItem, totalBalanceItem) => {
        if (
            parseFloat(totalBalanceItem?.elecBalance) < parseFloat(currentItem?.elecAmt) ||
            parseFloat(totalBalanceItem?.serviceBalance) <
                parseFloat(currentItem?.orderServiceAmt) ||
            parseFloat(totalBalanceItem?.incrementBalance) < parseFloat(currentItem?.incrementAmt)
        ) {
            message.error('票池余额不足，无法开票');
            return false;
        }
        return true;
    };

    const onClose = () => {
        updateEditItem(undefined);
        updateVisible(false);
    };

    const onSubmit = () => {
        if (!compareBalances(editItem, poolBlance)) {
            return;
        }
        form.validateFields().then(async (values) => {
            updateWaiting(true);
            try {
                await submitInvoicePoolApi(values);
                onFinish();
                onClose();
                message.success('操作成功');
            } catch (error) {
            } finally {
                updateWaiting(false);
            }
        });
    };

    // 开票金额
    const invoiceMessage = useMemo(() => {
        if (!editItem) {
            return null;
        }
        return (
            <div>
                {editItem.invoiceAmt === undefined ? '-' : editItem.invoiceAmt}
                元（电费：{editItem.elecAmt === undefined ? '-' : editItem.elecAmt}
                元&nbsp;&nbsp;&nbsp;&nbsp;服务费：
                {editItem.orderServiceAmt === undefined ? '-' : editItem.orderServiceAmt}
                元&nbsp;&nbsp;&nbsp;&nbsp;附加费：
                {editItem.incrementAmt === undefined ? '-' : editItem.incrementAmt}元）
            </div>
        );
    }, [editItem]);

    const summaryMessage = useMemo(() => {
        if (!poolBlance || !editItem) {
            return null;
        }
        return (
            <div>
                {poolBlance.balance === undefined ? '-' : poolBlance.balance}
                元（电费：
                <span
                    style={{
                        color:
                            parseFloat(poolBlance.elecBalance) < parseFloat(editItem.elecAmt)
                                ? 'red'
                                : undefined,
                    }}
                >
                    {poolBlance.elecBalance === undefined ? '-' : poolBlance.elecBalance}
                </span>
                元&nbsp;&nbsp;&nbsp;&nbsp;服务费：
                <span
                    style={{
                        color:
                            parseFloat(poolBlance.serviceBalance) <
                            parseFloat(editItem.orderServiceAmt)
                                ? 'red'
                                : undefined,
                    }}
                >
                    {poolBlance.serviceBalance === undefined ? '-' : poolBlance.serviceBalance}
                </span>
                元&nbsp;&nbsp;&nbsp;&nbsp;附加费：
                <span
                    style={{
                        color:
                            parseFloat(poolBlance.incrementBalance) <
                            parseFloat(editItem.incrementAmt)
                                ? 'red'
                                : undefined,
                    }}
                >
                    {poolBlance.incrementBalance === undefined ? '-' : poolBlance.incrementBalance}
                </span>
                元）
            </div>
        );
    }, [poolBlance, editItem]);

    return (
        <Modal
            title="票池开票"
            visible={visible}
            footer={false}
            onCancel={onClose}
            maskClosable={false}
            width={800}
        >
            <Form
                {...formItemLayout}
                initialValues={{ ...(editItem || {}) }}
                form={form}
                onFinish={onSubmit}
                scrollToFirstError
            >
                <FormItem name="invoiceAppId" noStyle />

                <FormItem label="开票金额">{invoiceMessage}</FormItem>
                <FormItem label="票池余额">{summaryMessage}</FormItem>

                <InvoiceInputForm />

                <FormItem style={{ textAlign: 'center' }}>
                    <Space>
                        <Button
                            type="primary"
                            htmlType="submit"
                            disabled={!poolBlance || !editItem}
                            loading={isWaiting}
                        >
                            提交
                        </Button>
                        <Button onClick={onClose}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};

export default connect(({ user, global, invoiceManageModel }) => ({
    global,
    user,
    invoiceManageModel,
}))(InvoicePoolModal);
