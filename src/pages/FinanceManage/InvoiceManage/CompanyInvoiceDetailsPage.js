import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { connect, useLocation } from 'umi';
import { Fragment, useEffect, useState, useMemo } from 'react';
import { Descriptions, Card, Divider, Empty, Alert } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import styles from './style.less';
import TablePro from '@/components/TablePro';
import Yuan from '@/utils/Yuan';
import usePageState from '@/hooks/usePageState.js';
import commonStyles from '@/assets/styles/common.less';
import { INVOICE_OPEN_TYPE } from './components/InvoiceChooseOrderModal';

const CompanyInvoiceDetailsPage = (props) => {
    const {
        dispatch,
        infoLoading,
        history,
        invoiceManageModel: { companyInvoiceInfo },
    } = props;

    const { query = {} } = useLocation();
    const { invoiceApplyId } = query;

    const [pageInfo, changePageInfo, onTableChange] = usePageState();
    useEffect(() => {
        dispatch({
            type: 'invoiceManageModel/initCompanyInvoiceInfo',
            invoiceApplyId,
        });
        return () => {
            dispatch({
                type: 'invoiceManageModel/updateInvoiceProperty',
                params: {
                    companyInvoiceInfo: undefined,
                },
            });
        };
    }, []);

    const invoiceDetail = useMemo(() => {
        return companyInvoiceInfo?.applyParam;
    }, [companyInvoiceInfo]);

    const invoiceSummary = useMemo(() => {
        return companyInvoiceInfo?.summary;
    }, [companyInvoiceInfo]);

    const detailsTableInfo = useMemo(() => {
        // 订单列表
        return {
            columns: [
                {
                    title: '运营商',
                    width: 180,
                    dataIndex: 'operName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },

                {
                    title: '开票方式',
                    width: 140,
                    dataIndex: 'invoiceAccessModeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '开票总金额',
                    width: 120,
                    dataIndex: 'invoiceAmt',
                    align: 'right',
                    render(text, record) {
                        return (
                            <span style={{ color: '#f50' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
                {
                    title: '电费金额',
                    width: 120,
                    dataIndex: 'invoiceElecAmt',
                    align: 'right',
                    render(text, record) {
                        return (
                            <span style={{ color: '#f50' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
                {
                    title: '服务费金额',
                    width: 120,
                    dataIndex: 'invoiceServiceAmt',
                    align: 'right',
                    render(text, record) {
                        return (
                            <span style={{ color: '#f50' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
                {
                    title: '占位费金额',
                    width: 120,
                    dataIndex: 'invoiceIncrementAmt',
                    align: 'right',
                    render(text, record) {
                        return (
                            <span style={{ color: '#f50' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
                {
                    title: '申请单号',
                    width: 180,
                    dataIndex: 'invoiceAppNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '发票类型',
                    width: 120,
                    dataIndex: 'invoiceMediaName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '发票种类',
                    width: 120,
                    dataIndex: 'invoiceTypeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '开票状态',
                    width: 120,
                    dataIndex: 'invoiceStatusName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '开具时间',
                    width: 200,
                    dataIndex: 'invoiceTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '发票号码',
                    width: 180,
                    dataIndex: 'invoiceNum',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '操作',
                    width: 120,
                    fixed: 'right',
                    render: (text, record) => {
                        return (
                            <a
                                onClick={() => {
                                    history.push(
                                        `/financemanage/invoice/manage/details/${
                                            record?.invoiceAppNo
                                        }?companyId=${true}`,
                                    );
                                }}
                            >
                                详情
                            </a>
                        );
                    },
                },
            ],
            list: companyInvoiceInfo?.items || [],
            total: companyInvoiceInfo?.items?.length || 0,
            title: '开票订单',
        };
    }, [companyInvoiceInfo]);

    const tableBar = useMemo(() => {
        return `订单数：${invoiceSummary?.orderNum || ''}个；开票总金额：${
            invoiceSummary?.invoiceAmt || ''
        }元；开票电费：${invoiceSummary?.invoiceElecAmt || ''}元；开票服务费（含附加费）：${
            invoiceSummary?.invoiceServiceIncrementAmt || ''
        }元`;
    }, [invoiceSummary]);

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    开票详情
                </div>
            }
        >
            <Card loading={infoLoading}>
                <Descriptions title={<div className={commonStyles['form-title']}>发票信息</div>}>
                    <Descriptions.Item label="申请时间">
                        {invoiceDetail?.applyTime || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="申请人">
                        {invoiceDetail?.applicantMobile || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="发票种类">
                        {invoiceDetail?.invoiceTypeName || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="抬头类型">
                        {invoiceDetail?.invoiceTitleTypeName || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="发票抬头">
                        {invoiceDetail?.invoiceTitle || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="税号">
                        {invoiceDetail?.taxNum || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="公司地址">
                        {invoiceDetail?.registeredAddr || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="公司电话">
                        {invoiceDetail?.registeredMobile || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="开户银行">
                        {invoiceDetail?.bankName || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="银行账号">
                        {invoiceDetail?.bankAccount || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="邮箱地址">
                        {invoiceDetail?.email || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="备注">
                        {invoiceDetail?.invoiceRemark || ''}
                    </Descriptions.Item>
                </Descriptions>

                <Divider />

                <Descriptions title={detailsTableInfo?.title} />
                <Alert message={tableBar} type="info" showIcon />
                <TablePro
                    style={{ width: '100%', marginTop: '20px' }}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.invoiceRelatId}
                    locale={{
                        emptyText: (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无配置信息"
                            />
                        ),
                    }}
                    columns={detailsTableInfo?.columns}
                    dataSource={detailsTableInfo?.list}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: detailsTableInfo?.total,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ invoiceManageModel, loading, user, global }) => ({
    invoiceManageModel,
    infoLoading: loading.effects['invoiceManageModel/initEditActInfo'],
    user,
    global,
}))(CompanyInvoiceDetailsPage);
