import {
    Button,
    Row,
    Col,
    Form,
    Modal,
    Space,
    DatePicker,
    message,
    Input,
    Select,
    Tag,
    Tooltip,
    InputNumber,
} from 'antd';
import { connect } from 'umi';
import { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { InfoCircleOutlined } from '@ant-design/icons';

import moment from 'moment';

const FormItem = Form.Item;
const { confirm } = Modal;

const { Option } = Select;
const { TextArea } = Input;

import {
    updateInvoicePoolUserApi,
    updateInvoicePoolUserStateApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import { isPhoneNumber } from '@/utils/verify';

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const AddUserModal = (props) => {
    const { initRef, onFinish } = props;
    const [visible, updateVisible] = useState(false);
    const [isWaiting, updateWaiting] = useState(false);
    const [editItem, updateEditItem] = useState({});
    useImperativeHandle(initRef, () => ({
        show: (item = {}) => {
            updateVisible(true);
            updateEditItem(item);
            form.resetFields();
            const params = item;
            // if (item?.enableTime) {
            //     params.time = moment(item.enableTime, 'YYYY-MM-DD');
            // }
            if (!params.enableTime) {
                params.time = moment();
            }
            form.setFieldsValue(params);
        },
    }));

    const onClose = () => {
        updateEditItem({});
        updateVisible(false);
    };

    const [form] = Form.useForm();
    const [dateOpenFlag, updateDateOpenFlag] = useState(false);

    const submitFormEvent = () => {
        form.validateFields().then(async (values) => {
            try {
                updateWaiting(true);
                const params = { ...values };
                if (params.time) {
                    params.enableTime = params.time.format('YYYY-MM-DD');
                    delete params.time;
                }
                await updateInvoicePoolUserApi(params);
                onClose();
                message.success('操作成功');
                onFinish();
            } catch (error) {
                return Promise.reject(error);
            } finally {
                updateWaiting(false);
            }
        });
    };

    return (
        <Modal
            title={editItem?.id ? '编辑用户' : '新增用户'}
            destroyOnClose
            width={500}
            visible={visible}
            onCancel={onClose}
            onOk={() => {
                submitFormEvent();
            }}
            okButtonProps={{ loading: isWaiting }}
        >
            <Form {...formItemLayout} form={form} onFinish={onFinish}>
                <FormItem name="id" noStyle />
                <FormItem name="enableTime" noStyle />
                <FormItem label="姓名" name="name" rules={[{ required: true, whitespace: true }]}>
                    <Input placeholder="请填写姓名" autoComplete="off" maxLength={20} allowClear />
                </FormItem>

                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) => prevValues.id != curValues.id}
                >
                    {({ getFieldValue }) => {
                        const id = getFieldValue('id');
                        const enableTime = getFieldValue('enableTime');
                        const mobile = getFieldValue('mobile');
                        return (
                            <Fragment>
                                <FormItem
                                    label="手机号:"
                                    name="mobile"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value === null) {
                                                    return Promise.reject('请输入手机号');
                                                }
                                                if (!isPhoneNumber(`${value}`)) {
                                                    return Promise.reject('请检查手机号');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required={id ? false : true}
                                >
                                    {id ? (
                                        mobile
                                    ) : (
                                        <InputNumber
                                            controls={false}
                                            style={{ width: '100%' }}
                                            maxLength={11}
                                            placeholder="请填写"
                                        />
                                    )}
                                </FormItem>
                                <FormItem
                                    name="time"
                                    label={
                                        <Tooltip
                                            title={
                                                '启用时间至未来所产生的购电运营商订单都将纳入发票池'
                                            }
                                        >
                                            启用时间&nbsp;
                                            <InfoCircleOutlined />
                                        </Tooltip>
                                    }
                                    rules={[
                                        {
                                            required: id ? false : true,
                                            message: '请选择启用时间',
                                        },
                                    ]}
                                    required={id ? false : true}
                                >
                                    {id ? (
                                        enableTime
                                    ) : (
                                        <DatePicker
                                            format="YYYY-MM-DD"
                                            placeholder="请选择"
                                            style={{ width: '100%' }}
                                            renderExtraFooter={() => {
                                                const pointerStyle = {
                                                    cursor: 'pointer',
                                                };
                                                return (
                                                    <Row>
                                                        {[
                                                            '三个月前',
                                                            '半年前',
                                                            '一年前',
                                                            '两年前',
                                                        ].map((ele, index) => {
                                                            return (
                                                                <Col key={index}>
                                                                    <Tag
                                                                        color="processing"
                                                                        onClick={() => {
                                                                            switch (index) {
                                                                                case 0:
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            time: moment().subtract(
                                                                                                3,
                                                                                                'months',
                                                                                            ),
                                                                                        },
                                                                                    );
                                                                                    break;
                                                                                case 1:
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            time: moment().subtract(
                                                                                                6,
                                                                                                'months',
                                                                                            ),
                                                                                        },
                                                                                    );
                                                                                    break;
                                                                                case 2:
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            time: moment().subtract(
                                                                                                1,
                                                                                                'years',
                                                                                            ),
                                                                                        },
                                                                                    );
                                                                                    break;
                                                                                case 3:
                                                                                    form.setFieldsValue(
                                                                                        {
                                                                                            time: moment().subtract(
                                                                                                2,
                                                                                                'years',
                                                                                            ),
                                                                                        },
                                                                                    );
                                                                                    break;

                                                                                default:
                                                                                    break;
                                                                            }
                                                                            updateDateOpenFlag(
                                                                                false,
                                                                            );
                                                                        }}
                                                                    >
                                                                        <span style={pointerStyle}>
                                                                            {ele}
                                                                        </span>
                                                                    </Tag>
                                                                </Col>
                                                            );
                                                        })}
                                                    </Row>
                                                );
                                            }}
                                            showToday={false}
                                            onOpenChange={(open) => {
                                                updateDateOpenFlag(open);
                                            }}
                                            open={dateOpenFlag}
                                        />
                                    )}
                                </FormItem>
                            </Fragment>
                        );
                    }}
                </FormItem>

                <FormItem label="备注">
                    <Space direction="vertical" style={{ width: '100%' }}>
                        <FormItem name="remark" noStyle>
                            <TextArea placeholder="请填写" maxLength={100} showCount allowClear />
                        </FormItem>
                        <div>
                            <Row>
                                {['内部员工', '亲朋好友'].map((ele, index) => {
                                    return (
                                        <Col span={6} key={index}>
                                            <a
                                                onClick={() => {
                                                    let remark =
                                                        (form.getFieldValue('remark') || '') + ele;
                                                    if (remark.length > 100) {
                                                        remark = remark.substring(0, 100);
                                                    }
                                                    form.setFieldsValue({
                                                        remark,
                                                    });
                                                }}
                                            >
                                                {ele}
                                            </a>
                                        </Col>
                                    );
                                })}
                            </Row>
                        </div>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        onReset();
    };
    const options = useMemo(() => {
        const channels = [
            { codeValue: '1', codeName: '正常' },
            { codeValue: '0', codeName: '停用' },
        ];
        const list = [];
        channels.forEach((ele) => {
            list.push(
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>,
            );
        });
        return list;
    }, []);

    return (
        <Form form={form} onFinish={onFinish}>
            <SearchOptionsBar loading={listLoading} onReset={resetForm} open>
                <Col span={8}>
                    <FormItem label="姓名" name="name">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="手机号" name="mobile">
                        <InputNumber
                            controls={false}
                            style={{ width: '100%' }}
                            placeholder="请填写"
                            maxLength={11}
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="状态" name="state">
                        <Select placeholder="请选择" allowClear>
                            {options}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const InvoicePoolUsersModal = (props) => {
    const {
        initRef,
        dispatch,
        invoiceManageModel: { poolUserList, poolUserTotal },
        listLoading,
        changeStateEvent,
    } = props;

    const [visible, updateVisible] = useState(false);
    useImperativeHandle(initRef, () => ({
        show: () => {
            updateVisible(true);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const [form] = Form.useForm();
    const addRef = useRef();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (visible) {
            searchData();
        }
    }, [pageInfo, visible]);

    // 调用搜索接口
    const searchData = () => {
        form.validateFields().then((data) => {
            try {
                const params = {
                    ...data,
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                };

                dispatch({
                    type: 'invoiceManageModel/getInvoicePoolUserList',
                    options: params,
                });
            } catch (error) {}
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '姓名',
            width: 100,
            dataIndex: 'name',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手机号',
            width: 120,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <Tooltip title={'启用时间至未来所产生的购电运营商订单都将纳入发票池'}>
                    启用时间&nbsp;
                    <InfoCircleOutlined />
                </Tooltip>
            ),
            width: 140,
            dataIndex: 'enableTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '停用时间',
            width: 140,
            dataIndex: 'disableTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 80,
            dataIndex: 'stateName',
            render(text, record) {
                let color = '#cccccc';
                if (record.state == '1') {
                    // 黑色
                    color = '#000000';
                } else if (record.state == '0') {
                    // 红色
                    color = '#f50000';
                }
                return (
                    <span title={text} style={{ color: color }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '备注',
            width: 140,
            dataIndex: 'remark',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            // align: 'center',
            render(text, record) {
                return (
                    (record.state == '1' && (
                        <Space>
                            <a
                                onClick={() => {
                                    addRef.current.show(record);
                                }}
                            >
                                编辑
                            </a>
                            <a
                                onClick={() => {
                                    confirm({
                                        title: '账号停用',
                                        content: '停用后，该用户产生的订单将不再纳入发票池中',
                                        onOk: async () => {
                                            try {
                                                await updateInvoicePoolUserStateApi({
                                                    ...record,
                                                    state: '0',
                                                });
                                                message.success('操作成功');
                                                searchData();
                                            } catch (error) {}
                                        },
                                    });
                                }}
                            >
                                停用
                            </a>
                        </Space>
                    )) || (
                        <a
                            onClick={() => {
                                confirm({
                                    title: '账号恢复',
                                    content: '恢复后，该用户停用期间产生的订单也将纳入发票池中',
                                    onOk: async () => {
                                        try {
                                            await updateInvoicePoolUserStateApi({
                                                ...record,
                                                state: '1',
                                            });
                                            message.success('操作成功');
                                            searchData();
                                            changeStateEvent?.();
                                        } catch (error) {}
                                    },
                                });
                            }}
                        >
                            恢复
                        </a>
                    )
                );
            },
        },
    ];

    return (
        <Modal visible={visible} title="票池用户" onCancel={onClose} width={880} footer={null}>
            <SearchLayout
                {...props}
                form={form}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />

            <Button
                onClick={() => {
                    addRef.current.show();
                }}
                style={{ marginBottom: '12px' }}
                type="primary"
            >
                新增用户
            </Button>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content', y: `${document.body.clientHeight - 520}px` }}
                rowKey={(record) => record.id}
                dataSource={poolUserList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: poolUserTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <AddUserModal
                initRef={addRef}
                onFinish={() => {
                    resetData();
                }}
            />
        </Modal>
    );
};

export default connect(({ global, invoiceManageModel, loading, user }) => ({
    global,
    invoiceManageModel,
    listLoading: loading.effects['invoiceManageModel/getInvoicePoolUserList'],
    currentUser: user.currentUser,
}))(InvoicePoolUsersModal);
