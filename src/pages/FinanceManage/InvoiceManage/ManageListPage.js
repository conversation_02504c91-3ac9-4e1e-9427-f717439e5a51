import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    message,
    Tooltip,
    Upload,
    Radio,
    Spin,
    Result,
    Popconfirm,
    Alert,
    InputNumber,
    Popover,
} from 'antd';
import { connect } from 'umi';
import { Fragment, useEffect, useState, useMemo, useRef } from 'react';
import { exportTableByParams, isEmpty } from '@/utils/utils';
import { InfoCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import CitysSelect from '@/components/CitysSelect/index.js';
import AllStationSelect from '@/components/AllStationSelect';
import CacheAreaView from '@/components/CacheAreaView';
import styles from '@/assets/styles/common.less';
import WorkOrderHXTool, { WORK_TYPES } from '../../MarketingManage/WorkOrder/WorkOrderHXTool';
import { CallRemindButton } from './components/CallRemindButton';
import { GenerateWorkOrderButton } from './components/GenerateWorkOrderButton';
import { Link } from 'umi';
import { MNG_DEFRAY_URL } from '@/config/global';
import { useRequest } from 'ahooks';
import SelectChannelItem from '@/components/SelectChannelItem/index';

import {
    updateInvoiceRemarkApi,
    invoiceSummaryListApiPath,
    invoiceStatisticsListApiPath,
    invalidInvoiceApi,
    retryInvoiceApi,
    retryInvoiceAgainApi,
    changeInvoiceApi,
    downloadInvoiceImportFailExportApi,
    importInvoiceApi,
    invoiceNoticeKingDeeApi,
    batchRetryInvoiceApi,
    nnInvoiceReopenApi,
    queryInvoiceStatusApi,
    callRemindInvoiceApi,
    submitWorkOrderApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import { uploadFile } from '@/services/CommonApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { INVIOCE_STATUS, INVIOCE_MODE } from '@/config/declare';
import { useImperativeHandle } from 'react';
import ReInvoiceModal from './ReInvoiceModal';
import InvoicePoolModal from './InvoicePoolModal';
import InvoicingWhiteModal from './components/InvoicingWhiteModal';
import GuideCompanyModal from './components/GuideCompanyModal';
import { IMG_URL } from '@/config/global';
import CopyComponent from '@/components/CopyComponent';
// import InvoiceModal from './InvoiceModal';
import BatchSyncjinDie from './components/batchSyncjinDie';
const { TextArea } = Input;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const PAGE_TYPES = {
    APPLY: '01',
    STATISTICS: '02',
    SUMMARY: '03',
};

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
    labelAlign: 'right',
};

const inputOptions = {
    maxLength: 25,
    autoComplete: 'off',
};

const ApplySearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        global: {
            invoiceAccessModeOptions, // 开票方式
            invoiceTypeCodeOptions, // 发票类型=
            codeInfo = {},
        },
        applylistLoading,
        user,
        tabType,
        disabledContidion = [],
        searchCondition,
    } = props;

    const {
        openInvoiceChannel: openInvoiceChannelList,
        invoiceType: invoiceTypeOptions,
        invoiceBusinessType,
        remindInvoiceResult,
    } = codeInfo;

    useEffect(() => {
        if (invoiceAccessModeOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceAccessModeList',
                options: {},
            });
        }
        if (invoiceTypeCodeOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceTypeCodeList',
                options: {},
            });
        }
        if (!openInvoiceChannelList?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'openInvoiceChannel',
            });
        }
        if (!invoiceTypeOptions?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'invoiceType',
            });
        }
        if (!invoiceBusinessType?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'invoiceBusinessType',
            });
        }
        if (!remindInvoiceResult?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'remindInvoiceResult',
            });
        }
    }, []);

    const invoiceAccessMode = useMemo(
        () =>
            invoiceAccessModeOptions.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [invoiceAccessModeOptions],
    );

    const invoiceTypeCode = useMemo(
        () =>
            invoiceTypeCodeOptions.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [invoiceTypeCodeOptions],
    );

    const invoiceKindCode = useMemo(
        () =>
            invoiceTypeOptions?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || [],
        [invoiceTypeOptions],
    );

    const openInvoiceChannel = useMemo(
        () =>
            openInvoiceChannelList?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [openInvoiceChannelList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    ...(searchCondition || {}),
                    timeType: '02',
                    dates: [moment().subtract(1, 'month').add(1, 'day'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={applylistLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    exportName="导出至暂存区"
                >
                    <Col span={8}>
                        <FormItem
                            label="申请日期"
                            name="dates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        const invoiceAppNo = getFieldValue('invoiceAppNo');
                                        console.log(invoiceAppNo);
                                        // 当“申请单号”有值，查询时忽略其他的所有查询条件，只按申请单号查结果
                                        if (invoiceAppNo) return Promise.resolve();
                                        if (!value) {
                                            return Promise.reject('请选择日期');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const dest = 1;
                                            if (value[1].diff(value[0], 'year') >= dest) {
                                                return Promise.reject(
                                                    `选取范围最大不超过${dest}年`,
                                                );
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            required
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            formItemLayout={formItemLayout}
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <AllStationSelect form={form} label="场站名称" />
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            placeholder="请选择"
                            rules={[]}
                            formItemLayout={formItemLayout}
                            showArrow
                            allowClear
                            provinceSelectable
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="开票方式:" name="invoiceAccessMode" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                {invoiceAccessMode}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="申请单号:" name="invoiceAppNo" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="发票类型:" name="invoiceMedia" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                {invoiceTypeCode}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="发票种类" name="invoiceType" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                {invoiceKindCode}
                            </Select>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="项目编号:" name="projectNo" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="发票抬头:" name="invoiceTitle" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="税&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号:"
                            name="taxNum"
                            {...formItemLayout}
                        >
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="发票代码:" name="invoiceCode" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="发票号码:" name="invoiceSerialNum" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="手&nbsp;&nbsp;机&nbsp;&nbsp;号:"
                            name="mobile"
                            {...formItemLayout}
                        >
                            <Input
                                placeholder="请填写"
                                allowClear
                                {...inputOptions}
                                disabled={disabledContidion?.indexOf('mobile') >= 0}
                            />
                        </FormItem>
                    </Col>
                    {/* <Col span={8}>
                        <FormItem label="用户类型:" name="bearObject" {...formItemLayout}>
                            <Select placeholder="请选择">{bearObjectOptions}</Select>
                        </FormItem>
                    </Col> */}
                    <Col span={8}>
                        <FormItem label="开具日期" name="invoiceTime">
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    {/* <Col span={8}>
                        <FormItem label="坏账类型:" name="badDebtType" {...formItemLayout}>
                            <Select placeholder="请选择">{tariffTypeOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="承担方:" name="bearObject" {...formItemLayout}>
                            <Select placeholder="请选择">{bearObjectOptions}</Select>
                        </FormItem>
                    </Col> */}
                    <Col span={8}>
                        <FormItem label="同步金蝶" name="noticeKingdeeStatus" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                <Option key="0" value="0">
                                    无须同步
                                </Option>
                                <Option key="1" value="1">
                                    未同步
                                </Option>
                                <Option key="2" value="2">
                                    已同步
                                </Option>
                                <Option key="3" value="3">
                                    同步失败
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="开票项目" name="invoiceBusinessType" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                {invoiceBusinessType?.map((ele) => (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="开票渠道:" name="applyMode" {...formItemLayout}>
                            <SelectChannelItem
                                optionList={openInvoiceChannelList}
                                multiple={false}
                            ></SelectChannelItem>
                        </FormItem>
                    </Col>
                    {/* 开具中TAB查询条件增加“申请超过”，默认0，最小0，最大365； */}
                    {(!user?.currentUser?.operId && tabType == '04' && (
                        <Col span={8}>
                            <FormItem
                                label="申请超过"
                                name="greaterDay"
                                {...formItemLayout}
                                initialValue="0"
                            >
                                <Row gutter={24}>
                                    <Col>
                                        <FormItem name="greaterDay" noStyle>
                                            <InputNumber
                                                min={0}
                                                max={365}
                                                precision={0}
                                                placeholder="请填写"
                                            />
                                        </FormItem>
                                    </Col>
                                    <Col style={{ display: 'flex', alignItems: 'center' }}>
                                        <span>天以上</span>
                                    </Col>
                                </Row>
                            </FormItem>
                        </Col>
                    )) ||
                        null}
                    {/* 开具失败TAB查询条件增加“用户状态”； */}
                    {(!user?.currentUser?.operId && tabType == '03' && (
                        <Col span={8}>
                            <FormItem label="用户状态" name="invoiceStatus" {...formItemLayout}>
                                <Select placeholder="请选择" allowClear>
                                    <Option key="0" value="04">
                                        开具中
                                    </Option>
                                    <Option key="1" value="03">
                                        开具失败
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                    )) ||
                        null}
                    {/* 开具中、开具失败TAB查询条件增加“预计开具时间” */}
                    {(!user?.currentUser?.operId && (tabType == '03' || tabType == '04') && (
                        <Col span={8}>
                            <FormItem label="预计开具时间" name="isOverdue" {...formItemLayout}>
                                <Select placeholder="请选择" allowClear>
                                    <Option key="0" value="0">
                                        已逾期
                                    </Option>
                                    <Option key="1" value="1">
                                        未逾期
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                    )) ||
                        null}
                    {(!user?.currentUser?.operId && (
                        <Col span={8}>
                            <FormItem label="票池开票" name="invoicePoolFlag" {...formItemLayout}>
                                <Select placeholder="请选择" allowClear>
                                    <Option key="1" value="1">
                                        是
                                    </Option>
                                    <Option key="0" value="0">
                                        否
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                    )) ||
                        null}
                    <Col span={8}>
                        <FormItem label="订单号" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="第三方订单号" name="thirdOrderNo" {...formItemLayout}>
                            <Input placeholder="请填写" allowClear autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="用户催票" name="custRemindFlag" {...formItemLayout}>
                            <Select
                                placeholder="请选择"
                                allowClear
                                options={[
                                    {
                                        label: '是',
                                        value: 1,
                                    },
                                    {
                                        label: '否',
                                        value: 0,
                                    },
                                ]}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="进线催票" name="callRemindFlag" {...formItemLayout}>
                            <Select
                                placeholder="请选择"
                                allowClear
                                options={[
                                    {
                                        label: '是',
                                        value: 1,
                                    },
                                    {
                                        label: '否',
                                        value: 0,
                                    },
                                ]}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="催票结果" name="remindResultList" {...formItemLayout}>
                            <Select placeholder="请选择，支持多选" mode="multiple" allowClear>
                                {remindInvoiceResult?.map((ele) => (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="用户催票时间" name="custRemindTime" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="进线催票时间" name="callRemindTime" {...formItemLayout}>
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const ApplyRemarkModal = (props) => {
    const {
        dispatch,
        initRef,
        invoiceManageModel: { invoiceApplyRemarkList },
        successCallback,
    } = props;

    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [editItem, updteEditItem] = useState();

    useEffect(() => {
        if (!editItem) {
            dispatch({
                type: 'invoiceManageModel/updateInvoiceProperty',
                params: { invoiceApplyRemarkList: [] },
            });
            return;
        }
        dispatch({
            type: 'invoiceManageModel/getInvoiceRemarkRecordList',
            options: { invoiceAppNo: editItem.invoiceAppNo },
        });
    }, [editItem]);

    useImperativeHandle(initRef, () => ({
        show: (item) => {
            updteEditItem({ ...item });
            updateVisible(true);
        },
    }));

    const onClose = () => {
        updateVisible(false);
        updteEditItem(undefined);
        form.resetFields();
    };

    const onFinish = () => {
        form.validateFields().then(async (values) => {
            const params = {
                invoiceAppNo: editItem.invoiceAppNo,
                ...values,
                predictTime: values?.predictTime?.format('YYYY-MM-DD'),
            };
            try {
                await updateInvoiceRemarkApi(params);
                message.success('操作成功');
                onClose();
                successCallback?.();
            } catch (error) {}
        });
    };

    return (
        <Modal
            title="备注"
            destroyOnClose
            width={800}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            onOk={onFinish}
        >
            <div style={{ color: 'red', marginLeft: '28px', marginBottom: '20px' }}>
                用户催票后，备注内容将会在前端展示给用户，请注意填写规范
            </div>
            <Form form={form} scrollToFirstError labelCol={{ span: 4 }}>
                <FormItem label="预计开具时间" name="predictTime">
                    <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                </FormItem>
                <FormItem label="备注" name="remark">
                    <TextArea rows={4} placeholder="请填写" maxLength={50} />
                </FormItem>
                <FormItem label="历史">
                    <Space direction="vertical" style={{ marginTop: '4px' }}>
                        {invoiceApplyRemarkList?.map(
                            (ele) =>
                                ele &&
                                `${ele.createTime} ${ele.operationName}：预计开具时间：${
                                    ele.predictTime || '-'
                                }；备注：${ele.remark || '-'}`,
                        )}
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};

export const ApplyListPage = (props) => {
    const {
        dispatch,
        history,
        invoiceManageModel: { invoiceApplyList, invoiceApplyListTotal },
        applylistLoading,
        global: { pageInit, invoiceStatusOptions },
        user,
        searchCondition,
        disabledContidion,
        global,
        cacheRef,
    } = props;
    const batchSyncRef = useRef();
    const {
        location: { pathname },
    } = history;
    const isUserCenter = pathname?.indexOf('/userCenter/userManage/detail/view') >= 0;

    const cacheName = `${pathname}apply`;

    const [form] = Form.useForm();
    const [openform] = Form.useForm();
    const [openReform] = Form.useForm();
    const [callForm] = Form.useForm();

    const [reInvoice, setReInvoice] = useState(null);
    const [curInvoice, setCurInvoice] = useState(null);
    const [showReEditWindow, changeShowReEditWindow] = useState(false);
    const [showEditWindow, changeShowEditWindow] = useState(false);
    const [batchReOpen, changeBatchReOpen] = useState(false); // 批量开票处理

    const poolModalRef = useRef();

    const [showImportView, toggleImportView] = useState(false); // 导入弹窗状态
    const [showInvoiceStockView, toggleInvoiceStockView] = useState(false); // 余票查询弹窗状态

    const [submitLoading, changeSubmitloading] = useState(false); // 上传加载

    const [selectItems, changeSelectItems] = useState([]); // 选中项

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: INVIOCE_STATUS.ALL,
        },
        props,
        cacheName,
    );

    useEffect(() => {
        if (searchCondition) {
            const currentConditions = form.getFieldsValue();
            let isEqual = true;
            Object.keys(searchCondition).map((ele) => {
                if (searchCondition?.[ele]?.length && !currentConditions?.[ele]) {
                    // 新增
                    isEqual = false;
                } else if (
                    currentConditions?.[ele] &&
                    currentConditions?.[ele] != searchCondition[ele]
                ) {
                    // 删除
                    isEqual = false;
                }
            });
            if (!isEqual) {
                form.setFieldsValue({ ...searchCondition });
                changePageInfo({ tabType: INVIOCE_STATUS.ALL });
                changeSelectItems([]);
                if (pageInfo.pageIndex == 1) {
                    searchData();
                } else {
                    changePageInfo({ pageIndex: 1 });
                }
            }
        }
    }, [searchCondition]);

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
        if (invoiceStatusOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceStatusList',
                options: {},
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const invoiceStatus = useMemo(
        () =>
            invoiceStatusOptions.map((ele) =>
                ele.codeValue == INVIOCE_STATUS.NOTISSUED ? null : (
                    <TabPane tab={ele.codeName} key={ele.codeValue} />
                ),
            ),
        [invoiceStatusOptions],
    );

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            const data = await form.validateFields();
            let params = {};
            if (data.invoiceAppNo) {
                params = {
                    invoiceAppNo: data.invoiceAppNo,
                };
            } else {
                params = {
                    operId: data.operId,
                    invoiceAccessMode: data.invoiceAccessMode,
                    invoiceAppNo: data.invoiceAppNo,
                    invoiceMedia: data.invoiceMedia,
                    invoiceType: data.invoiceType,
                    projectNo: data.projectNo,
                    invoiceTitle: data.invoiceTitle,
                    taxNum: data.taxNum,
                    invoiceCode: data.invoiceCode,
                    invoiceSerialNum: data.invoiceSerialNum,
                    mobile: data.mobile,
                    beginDate:
                        (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                    endDate:
                        (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                    invoiceTimeBgn:
                        (data.invoiceTime &&
                            data.invoiceTime[0] &&
                            data.invoiceTime[0].format('YYYY-MM-DD')) ||
                        '',
                    invoiceTimeEnd:
                        (data.invoiceTime &&
                            data.invoiceTime[1] &&
                            data.invoiceTime[1].format('YYYY-MM-DD')) ||
                        '',
                    noticeKingdeeStatus: data.noticeKingdeeStatus,
                    invoiceBusinessType: data.invoiceBusinessType,
                    greaterDay: data.greaterDay,
                    isInvoiceHandle: data.isInvoiceHandle,
                    isOverdue: data.isOverdue,
                    invoiceStatus: data.invoiceStatus,
                    city: data.city?.join?.(',') || data.city,
                    stationId: data.stationId,
                    applyMode: data.applyMode,
                    invoicePoolFlag: data.invoicePoolFlag,
                    orderNo: data.orderNo,
                    thirdOrderNo: data.thirdOrderNo,
                    custRemindFlag: data.custRemindFlag,
                    callRemindFlag: data.callRemindFlag,
                    remindResultList: data.remindResultList,
                    custRemindStartTime: data.custRemindTime?.[0].format('YYYY-MM-DD') || '',
                    custRemindEndTime: data.custRemindTime?.[1].format('YYYY-MM-DD') || '',
                    custRemindTime: undefined,
                    callRemindStartTime: data.callRemindTime?.[0].format('YYYY-MM-DD') || '',
                    callRemindEndTime: data.callRemindTime?.[1].format('YYYY-MM-DD') || '',
                    callRemindTime: undefined,
                };
            }

            if (pageInfo.tabType != INVIOCE_STATUS.ALL) {
                params.platInvoiceStatus = pageInfo.tabType;
            }

            if (isDownload) {
                // 下载
                cacheRef?.current?.apply(params).then(() => {
                    cacheRef?.current?.count();
                });
            } else {
                (params.pageIndex = pageInfo.pageIndex || undefined),
                    (params.pageSize = pageInfo.pageSize);

                dispatch({
                    type: 'global/setPageInit',
                    pathname: cacheName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'invoiceManageModel/getInvoiceApplyList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changeSelectItems([]);
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        if (type !== pageInfo.tabType) {
            changeSelectItems([]);
        }
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const invalidInvoiceEvent = (item) => {
        const canTime = moment().subtract(3, 'minutes').valueOf();
        const startTime = moment(item.invoiceTime).valueOf();
        if (canTime < startTime) {
            message.info('开具时间3分钟后才能作废');
            return;
        }
        confirm({
            title: `确定作废该发票?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (
                        (item.invoiceAccessMode == '02' || item.invoiceAccessMode == '03') &&
                        item.redInvoiceApiFlag != 1
                    ) {
                        message.error('线下开票、接口开票模式不支持作废');
                        return;
                    }
                    await invalidInvoiceApi(item.invoiceAppNo);
                    await searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // 打开导入规则页面
    const openImportViewEvent = () => {
        toggleImportView(true);
    };
    // 重开发票
    const openReInvoiceEvent = (values) => {
        const options = {
            invoiceAppNo: values.invoiceAppNo,
            invoiceAmt: values.invoiceAmt,
            invoiceType:
                !values.invoiceType || values.invoiceType.indexOf(',') >= 0
                    ? '01'
                    : values.invoiceType,
            invoiceTitleType: values.invoiceTitleType,
            invoiceTitle: values.invoiceTitle,
            taxNum: values.taxNum,
            registeredAddr: values.registeredAddr,
            registeredMobile: values.registeredMobile,
            bankName: values.bankName,
            bankAccount: values.bankAccount,
            invoiceRemark: values.invoiceRemark,
            email: values.email,
        };

        setReInvoice(options);
        openReform.setFieldsValue(options);
        changeShowReEditWindow(true);
    };
    const openInvoiceEvent = (item) => {
        const options = {
            invoiceAppNo: item.invoiceAppNo,

            invoiceTime: item.invoiceTime ? moment(item.invoiceTime) : moment(),

            invoiceCode: item.invoiceCode,

            invoiceNum: item.invoiceNum,

            courierCompany: item.courierCompany,

            courierNo: item.courierNo,

            remark: item.remark,
            invoiceMedia: item.invoiceMedia,
            invoiceAccessMode: item.invoiceAccessMode,
            platInvoiceStatus: item.platInvoiceStatus,
        };

        setCurInvoice(options);

        openform.setFieldsValue(options);
        changeShowEditWindow(true);
    };

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.invoiceAppNo),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            disabled:
                pageInfo.tabType === INVIOCE_STATUS.FAIL &&
                (record.invoiceStatus == INVIOCE_STATUS.FAIL || record.invoiceAccessMode === '03'), // Column configuration not to be checked
            name: record.stationId,
        }),
    };

    const retryInvoiceEvent = async (item) => {
        try {
            await retryInvoiceApi(item.invoiceAppNo);
            message.success('发起成功');
            await searchData();
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const batchRetryInvoiceEvent = async () => {
        if (selectItems.length == 0) {
            message.error('请选择要重试的订单');
            return;
        }
        confirm({
            title: '确认重试开票？',
            icon: <ExclamationCircleOutlined />,
            content: '',
            onOk: async () => {
                try {
                    const invoiceAppNos = selectItems?.map((ele) => ele.invoiceAppNo)?.join(',');
                    await batchRetryInvoiceApi(invoiceAppNos);
                    message.success('发起成功');
                    await searchData();
                    changeSelectItems([]);
                } catch (error) {
                    return Promise.reject(error);
                }
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const retryInvoiceAgainEvent = async (item) => {
        try {
            await retryInvoiceAgainApi(item.invoiceAppNo);
            message.success('发起成功');
            await searchData();
        } catch (error) {}
    };
    // 批量处理 开票处理
    const batchReOpenInvoiceEvent = async () => {
        const errorArr = [];
        if (selectItems.length == 0) {
            message.error('请选择要批量处理的订单');
            return;
        }
        selectItems?.forEach((item, index) => {
            if (
                !(
                    item.platInvoiceStatus == INVIOCE_STATUS.OPENING &&
                    item.invoiceStatus == INVIOCE_STATUS.OPENING &&
                    item.invoiceAccessMode == '03'
                )
            ) {
                errorArr.push(item);
            }
        });
        if (errorArr.length > 0) {
            message.error('仅开具中的线下开票发票支持批量处理，请重新选择');
            return;
        } else {
            changeBatchReOpen(true);
            changeShowEditWindow(true);
        }
    };

    const closeReEditInvoiceEvent = () => {
        changeShowReEditWindow(false);
        setReInvoice(null);
        openReform.resetFields();
    };

    const closeEditInvoiceEvent = () => {
        changeBatchReOpen(false);
        changeShowEditWindow(false);
        setCurInvoice(null);
        updateFileList([]);
        openform.resetFields();
    };
    // 诺诺发票重开
    const nnInvoiceReopen = async (item) => {
        try {
            await openReform.validateFields();
            const values = openReform.getFieldsValue();

            const params = {
                invoiceAppNo: reInvoice.invoiceAppNo,
                invoiceType: values.invoiceType,
                invoiceTitleType: values.invoiceTitleType,
                invoiceTitle: values.invoiceTitle,
                taxNum: values.invoiceTitleType === '02' ? values.taxNum : undefined,
                registeredAddr: values.registeredAddr,
                registeredMobile: values.registeredMobile,
                bankName: values.bankName,
                bankAccount: values.bankAccount,
                invoiceRemark: values.invoiceRemark,
                email: values.email,
            };

            const { msg } = await nnInvoiceReopenApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            closeReEditInvoiceEvent();
            await searchData();
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onFinish = async (values) => {
        try {
            const invoicePathList = [];
            const invoiceAppNos =
                batchReOpen && selectItems?.map((ele) => ele.invoiceAppNo)?.join(',');
            fileList.map((ele) => {
                if (ele.uid) {
                    invoicePathList.push(ele.uid);
                }
            });
            const params = {
                invoiceStatus: values.invoiceStatus,
                invoiceAppNo: batchReOpen ? invoiceAppNos : curInvoice?.invoiceAppNo,
                isInvoiceHandle: '01',
                invoiceFileUrls: invoicePathList,
            };
            if (![INVIOCE_STATUS.OPENING, INVIOCE_STATUS.CANCEL].includes(params.invoiceStatus)) {
                if (params.invoiceStatus == INVIOCE_STATUS.FAIL) {
                    params.invoiceHandleFailReason = values.invoiceHandleFailReason;
                } else if (batchReOpen) {
                    // 批量 开票处理
                    params.invoiceFileUrls = undefined;
                    params.invoiceTime = values.invoiceTime.format('YYYY-MM-DD HH:mm:ss');
                } else {
                    params.invoiceTime = values.invoiceTime.format('YYYY-MM-DD HH:mm:ss');

                    params.invoiceCode = values.invoiceCode;

                    params.invoiceNum = values.invoiceNum;

                    params.courierCompany = values.courierCompany;

                    params.courierNo = values.courierNo;
                }
            }

            changeSubmitloading(true);
            const { msg } = await changeInvoiceApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            closeEditInvoiceEvent();
            if (batchReOpen) {
                changeSelectItems([]);
            }
            await searchData();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    const columns = [
        {
            title: '申请时间',
            width: 200,
            dataIndex: 'appTime',
            textWrap: 'word-break',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...((user?.currentUser?.operId && []) || [
            {
                title: '用户状态',
                width: 120,
                dataIndex: 'invoiceStatusName',
                textWrap: 'word-break',
                render(text, record) {
                    const styleOptions = {};
                    if (record.invoiceStatus == INVIOCE_STATUS.FAIL && text?.length) {
                        styleOptions.color = 'red';
                    }

                    return (
                        <span style={{ ...styleOptions }} title={text}>
                            {text || '-'}
                        </span>
                    );
                },
            },
        ]),

        {
            title: '开具状态',
            width: 140,
            dataIndex: 'platInvoiceStatusName',
            render(text, record) {
                const styleOptions = {};
                if (record.platInvoiceStatus == INVIOCE_STATUS.FAIL && text?.length) {
                    styleOptions.color = 'red';
                }

                return (
                    <span style={{ ...styleOptions }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '开票渠道',
            width: 140,
            dataIndex: 'applyModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '申请人电话',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '抬头类型',
            width: 140,
            dataIndex: 'invoiceTitleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发票抬头',
            width: 140,
            dataIndex: 'invoiceTitle',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '税号',
            width: 140,
            dataIndex: 'taxNum',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '开票总金额',
            width: 140,
            align: 'right',
            dataIndex: 'invoiceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票电费金额',
            width: 140,
            align: 'right',
            dataIndex: 'elecAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    开票服务费金额
                    <Tooltip title="开票服务费包含附加费">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 180,
            align: 'right',
            dataIndex: 'serviceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票总电量',
            width: 140,
            align: 'right',
            dataIndex: 'chargePq',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '发票类型',
            width: 140,
            dataIndex: 'invoiceMediaName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发票种类',
            width: 140,
            dataIndex: 'invoiceTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票方式',
            width: 140,
            dataIndex: 'invoiceAccessModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票项目',
            width: 140,
            dataIndex: 'invoiceBusinessTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '申请单号',
            width: 140,
            dataIndex: 'invoiceAppNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户催票',
            dataIndex: 'custRemindFlagName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '进线催票',
            dataIndex: 'callRemindFlagName',
            width: 140,
            render(text, record) {
                return (
                    <CallRemindButton
                        invoiceAppNo={record?.invoiceAppNo}
                        record={record}
                        onFinish={() => {
                            searchData();
                        }}
                    />
                );
            },
        },
        {
            title: '催票次数',
            dataIndex: 'remindCount',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户催票时间',
            dataIndex: 'custRemindTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '进线催票时间',
            dataIndex: 'callRemindTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '催票结果',
            dataIndex: 'remindResult',
            width: 300,
            render(text, record) {
                return (
                    <GenerateWorkOrderButton
                        invoiceAppNo={record?.invoiceAppNo}
                        record={record}
                        onFinish={() => {
                            searchData();
                        }}
                    />
                );
            },
        },
        {
            title: '开票失败原因',
            width: 230,
            dataIndex: 'invoiceFailReason',
            render(text, record) {
                const tips = [];
                if (record?.invoiceHandleFailReason?.length) {
                    tips.push(record?.invoiceHandleFailReason);
                }
                if (text?.length) {
                    tips.push(text);
                }
                const tip = tips?.join?.('\n');
                return (
                    <Space size="small" direction="vertical">
                        {(record?.invoiceHandleFailReason && (
                            <span
                                style={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitBoxOrient: 'vertical',
                                    WebkitLineClamp: '1',
                                }}
                                title={tip}
                            >
                                {record?.invoiceHandleFailReason}
                            </span>
                        )) ||
                            null}
                        {(text && (
                            <span
                                style={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitBoxOrient: 'vertical',
                                    WebkitLineClamp: '1',
                                }}
                                title={tip}
                            >
                                {text}
                            </span>
                        )) ||
                            null}
                    </Space>
                );
            },
        },
        {
            title: '开具时间',
            width: 200,
            dataIndex: 'invoiceTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '发票代码',
            width: 140,
            dataIndex: 'invoiceCode',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发票号码',
            width: 140,
            dataIndex: 'invoiceNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '红冲人',
            width: 140,
            dataIndex: 'discardAccount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '红冲提交时间',
            width: 140,
            dataIndex: 'redInvoiceApplyTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '项目编号',
            width: 160,
            dataIndex: 'projectNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '同步金蝶',
            width: 140,
            dataIndex: 'noticeKingdeeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '同步失败原因',
            width: 140,
            dataIndex: 'noticeKingdeeFailReason',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...((user?.currentUser?.operId && []) || [
            {
                title: (
                    <span>
                        票池开票
                        <Tooltip title="平台使用发票池额度为用户开票">
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    </span>
                ),
                width: 140,
                dataIndex: 'invoicePoolFlagName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '预计开具时间',
                width: 140,
                dataIndex: 'predictTime',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '备注',
                width: 230,
                dataIndex: 'remark',
                render(text, record) {
                    return (
                        <span
                            style={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: '2',
                            }}
                            title={text}
                        >
                            {text}
                        </span>
                    );
                },
            },
        ]),

        {
            title: '操作',
            fixed: 'right',
            width: 240,
            render: (text, record, index) => {
                // 不应超过指定标题长度，超过就缩略
                let totalLength = 0;
                let showCount = 3;
                const editItem = [
                    <Link
                        to={`/financemanage/invoice/manage/details/${record.invoiceAppNo}`}
                        key="detail"
                        target={isUserCenter ? '_blank' : undefined}
                    >
                        <Button type="link" size="small" key="detail">
                            详情
                        </Button>
                    </Link>,
                ];
                if (
                    record.platInvoiceStatus == INVIOCE_STATUS.SUCCESS &&
                    record.invoiceAccessMode === '04' &&
                    // record.invoiceType !== '04' && // 注释说明：平台开票-专用发票：已出具的专票，支持作废、重开发票。
                    record.invoicePoolFlag != '1' &&
                    record.invoiceBusinessType != 7 // 为减少测试工作量，商家服务的重开发票入口暂时屏蔽
                ) {
                    // 平台开票-已开具状态，增加“重开发票”按钮，票池开票不允许重开
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                openReInvoiceEvent(record);
                            }}
                        >
                            重开发票
                        </Button>,
                    );
                }
                if (
                    ((record.invoiceAccessMode != '02' && record.invoiceAccessMode != '03') ||
                        record.redInvoiceApiFlag == 1) &&
                    // record.invoiceType !== '04' && // 注释说明：平台开票-专用发票：已出具的专票，支持作废、重开发票。
                    record.platInvoiceStatus == INVIOCE_STATUS.SUCCESS
                ) {
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                invalidInvoiceEvent(record);
                            }}
                        >
                            作废
                        </Button>,
                    );
                } else if (
                    (record.platInvoiceStatus == INVIOCE_STATUS.FAIL ||
                        record.platInvoiceStatus == INVIOCE_STATUS.RED_FAIL) &&
                    !record.invoiceStatus != INVIOCE_STATUS.FAIL &&
                    ![INVIOCE_MODE.OFFLINE].includes(record.invoiceAccessMode)
                ) {
                    // 开具状态开具失败，且用户状态也是失败的，隐藏重试按钮
                    // 除了线下开屏都能失败重试
                    // 如果开具失败但是有流水号的，仍然显示【重试】操作按钮，如果开具失败但是没有流水号的，显示【重开】
                    if (record.platInvoiceStatus == INVIOCE_STATUS.RED_FAIL) {
                        if (!(!isEmpty(record.redBillStatus) && record.redBillStatus != '01')) {
                            editItem.push(
                                <Popconfirm
                                    title="重新发起一次开票？"
                                    okText="确认"
                                    cancelText="取消"
                                    onConfirm={() => {
                                        if (record.invoiceSerialNum?.length == 0) {
                                            retryInvoiceAgainEvent(record);
                                        } else {
                                            retryInvoiceEvent(record);
                                        }
                                    }}
                                >
                                    <Button type="link" size="small">
                                        {record.invoiceSerialNum?.length == 0 ? '重开' : `重试`}
                                    </Button>
                                </Popconfirm>,
                            );
                        }
                    } else {
                        editItem.push(
                            <Popconfirm
                                title="重新发起一次开票？"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => {
                                    if (record.invoiceSerialNum?.length == 0) {
                                        retryInvoiceAgainEvent(record);
                                    } else {
                                        retryInvoiceEvent(record);
                                    }
                                }}
                            >
                                <Button type="link" size="small">
                                    {record.invoiceSerialNum?.length == 0 ? '重开' : `重试`}
                                </Button>
                            </Popconfirm>,
                        );
                    }
                }
                if (
                    (record.platInvoiceStatus == INVIOCE_STATUS.OPENING ||
                        record.platInvoiceStatus == INVIOCE_STATUS.SUCCESS) &&
                    [INVIOCE_MODE.PLAT_JOINT, INVIOCE_MODE.OFFLINE].includes(
                        record.invoiceAccessMode,
                    )
                ) {
                    // 线下并且开具中
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                openInvoiceEvent(record);
                            }}
                        >
                            处理
                        </Button>,
                    );
                }
                // 操作增加“备注”，开具失败、开具中状态有，点击弹窗；运营商账号不显示；
                if (
                    !user?.currentUser?.operId &&
                    (record.platInvoiceStatus == INVIOCE_STATUS.FAIL ||
                        record.platInvoiceStatus == INVIOCE_STATUS.OPENING)
                ) {
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={async () => {
                                remarkRef?.current?.show(record);
                            }}
                        >
                            备注
                        </Button>,
                    );
                }
                if (record.platInvoiceStatus == INVIOCE_STATUS.FAIL) {
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                openInvoiceEvent(record);
                            }}
                        >
                            手工开票
                        </Button>,
                    );
                }
                // 同步金蝶标识 0 无须同步 1 未同步 2 已同步  3 同步失败
                if (
                    record.platInvoiceStatus !== INVIOCE_STATUS.RED_FAIL &&
                    (record.noticeKingdeeStatus == 1 || record.noticeKingdeeStatus == 3) &&
                    record.invoiceAccessMode != INVIOCE_MODE.SAFE_CHARGE
                ) {
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={async () => {
                                try {
                                    await invoiceNoticeKingDeeApi({ ...record });
                                    message.success('同步完成');
                                    await searchData();
                                } catch (error) {}
                            }}
                        >
                            同步金蝶
                        </Button>,
                    );
                }

                if (
                    !user?.currentUser?.operId?.length &&
                    (record.invoiceAccessMode == INVIOCE_MODE.OFFLINE ||
                        record.invoiceAccessMode == INVIOCE_MODE.PLAT_JOINT) &&
                    (record.platInvoiceStatus == INVIOCE_STATUS.FAIL ||
                        record.platInvoiceStatus == INVIOCE_STATUS.OPENING) &&
                    record.invoicePoolFlag != '1'
                ) {
                    // 开票方式为线下开票或平台对接，开具状态为开具失败或开具中，票池开票状态否，显示票池开票按钮；运营商账号不显示票池开票按钮；
                    editItem.push(
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                poolModalRef.current.show(record);
                            }}
                        >
                            票池开票
                        </Button>,
                    );
                }

                if (user.currentUser?.relaFlag) {
                    editItem.push(
                        <WorkOrderHXTool
                            buttonProps={{ type: 'link', size: 'small' }}
                            key="tool"
                            inParams={record}
                            user={user}
                            global={global}
                            dispatch={dispatch}
                            type={WORK_TYPES.INVOICE}
                        />,
                    );
                    totalLength += 4;
                }

                const loop = (target) => {
                    if (typeof target?.props?.children == 'string') {
                        return target?.props?.children;
                    }
                    if (target?.props?.children) {
                        return loop(target?.props?.children);
                    }
                    return '';
                };
                editItem.map((ele, index) => {
                    totalLength += loop(ele)?.length || 0;
                });

                return (
                    <>
                        {totalLength < 10 || editItem?.length <= showCount ? (
                            editItem
                        ) : (
                            <Fragment>
                                {editItem.slice(0, showCount - 1)}
                                <Popover
                                    content={
                                        <Space direction="vertical">
                                            {editItem.slice(showCount - 1, editItem.length)}
                                        </Space>
                                    }
                                    trigger="hover"
                                >
                                    <Button type="link" size="small">
                                        ...
                                    </Button>
                                </Popover>
                            </Fragment>
                        )}

                        {/* {record.invoicePdf ? (
                            <span
                                className={styles['table-btn']}
                                onClick={() => {
                                    downLoadByUrl(record.invoicePdf);
                                }}
                            >
                                下载发票
                            </span>
                        ) : null} */}

                        {/* <span className={styles['table-btn']} onClick={() => {}}>
                        下载发票
                    </span> */}
                    </>
                );
            },
        },
    ];

    const [fileList, updateFileList] = useState([]);
    const [uploadLoading, changeUploading] = useState(false); // 上传加载

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg' ||
                file.type === 'application/pdf';
            if (!isJpgOrPng) {
                message.error('文件格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 / 1024 < 1;
            if (!isLt2M) {
                message.error(`文件不大于 1mb!`);
                rej();
                return;
            }

            res();
        });

    const uploadProps = useMemo(() => {
        const options = {
            contentType: '02',
        };
        if (curInvoice && curInvoice.invoiceAppNo) {
            options.invoiceAppNo = curInvoice.invoiceAppNo;
        }
        return {
            name: 'avatar',
            className: styles['avatar-uploader'],
            accept: '.jpeg,.png,.jpg,.pdf',
            fileList,
            data: {
                contRemrk: 'btnFile',
                relaTable: 'd_account_invoice_app',
                ...options,
            },
            customRequest: async ({
                // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
                action,
                data,
                file,
                filename,
                headers,
                onError,
                onProgress,
                onSuccess,
                withCredentials,
            }) => {
                try {
                    const params = {
                        ...data,
                    };

                    const isJpgOrPng =
                        file.type === 'image/jpg' ||
                        file.type === 'image/png' ||
                        file.type === 'image/jpeg';
                    if (isJpgOrPng) {
                        params.contentType = '02';
                    } else {
                        params.contentType = '01';
                    }

                    if (params.invoiceAppNo) {
                        params.relaId = params.invoiceAppNo;
                    }

                    const formData = new FormData();
                    if (params) {
                        Object.keys(params).forEach((key) => {
                            formData.append(key, params[key]);
                        });
                    }
                    formData.append(data.contRemrk, file);

                    changeUploading(true);
                    const {
                        data: { filePath, fileId, relativePath },
                    } = await uploadFile(formData);
                    fileList.push({
                        uid: relativePath,
                        name: filePath,
                        status: 'done',
                        url: filePath,
                        thumbUrl: filePath,
                    });
                    updateFileList([...fileList]);
                } catch (error) {
                } finally {
                    changeUploading(false);
                }

                return {
                    abort() {
                        console.log('upload progress is aborted.');
                    },
                };
            },
            listType: 'picture-card',
            onRemove: (file) => {
                const removeIndex = fileList.findIndex((ele) => ele.uid == file.uid);
                fileList.splice(removeIndex, 1);
                updateFileList([...fileList]);
                return true;
            },
            // onSuccess: handleUploadChange,
            // onChange: handleUploadChange,
            beforeUpload,
        };
    }, [curInvoice, fileList]);

    const remarkRef = useRef();

    const { run: asyncInvoiceStatus, loading: asyncLoading } = useRequest(
        () => {
            return queryInvoiceStatusApi();
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    Modal.success({
                        title: '提交成功',
                        content: '请稍后刷新页面查看开票结果',
                    });
                } else {
                    message.error(res?.msg || '查询失败');
                }
            },
        },
    );
    return (
        <Fragment>
            <ApplySearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
                tabType={pageInfo.tabType}
            />
            <div className={styles['btn-bar']}>
                <Space>
                    {(user?.currentUser?.operId?.length == 0 && (
                        <Button
                            onClick={() => {
                                history.push('/financemanage/invoice/open');
                            }}
                            type="primary"
                        >
                            开发票
                        </Button>
                    )) ||
                        null}

                    {pageInfo.tabType === INVIOCE_STATUS.FAIL ? (
                        <Button onClick={batchRetryInvoiceEvent}>批量重试</Button>
                    ) : null}

                    <Button onClick={batchReOpenInvoiceEvent} disabled={selectItems?.length === 0}>
                        批量处理
                    </Button>
                    <Button onClick={openImportViewEvent}>导入处理</Button>
                    <Button
                        onClick={() => {
                            toggleInvoiceStockView(true);
                        }}
                    >
                        余票查询
                    </Button>
                    {/* sysadmin以及新耀管理单位才显示，其他的都不显示。 */}
                    {(user?.currentUser?.operId?.length == 0 && (
                        <Fragment>
                            <Button
                                onClick={() => {
                                    asyncInvoiceStatus();
                                }}
                                loading={asyncLoading}
                            >
                                开票结果查询
                            </Button>
                            <InvoicingWhiteModal></InvoicingWhiteModal>
                        </Fragment>
                    )) ||
                        null}
                    <Button onClick={() => batchSyncRef?.current?.show()}>批量同步金蝶</Button>
                    <BatchSyncjinDie initRef={batchSyncRef} />
                </Space>
            </div>

            <Tabs activeKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={INVIOCE_STATUS.ALL} />
                {/* <TabPane tab="开具中" key={INVIOCE_STATUS.OPENING} />
                <TabPane tab="已开具" key={INVIOCE_STATUS.SUCCESS} />
                <TabPane tab="开具失败" key={INVIOCE_STATUS.FAIL} />
                <TabPane tab="已作废" key={INVIOCE_STATUS.CANCEL} /> */}
                {invoiceStatus}
            </Tabs>

            <TablePro
                name="list"
                rowSelection={
                    invoiceApplyList?.length && {
                        type: 'checkbox',
                        ...rowSelection,
                    }
                }
                rowKey={(record) => record.invoiceAppNo}
                loading={applylistLoading}
                scroll={{ x: 'max-content' }}
                dataSource={invoiceApplyList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: invoiceApplyListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
            <ReInvoiceModal
                {...props}
                form={openReform}
                showEditWindow={showReEditWindow}
                closeEditInvoiceEvent={closeReEditInvoiceEvent}
                curInvoice={reInvoice}
                onFinish={nnInvoiceReopen}
            ></ReInvoiceModal>
            {/* <InvoiceModal
                form={openform}
                showEditWindow={showEditWindow}
                closeEditInvoiceEvent={closeEditInvoiceEvent}
                curInvoice={curInvoice}
                onFinish={onFinish}
                submitLoading={submitLoading}
                changeImageType={changeImageType}
                imageUrl={imageUrl}
                changeImageUrl={changeImageUrl}
            ></InvoiceModal> */}
            <Modal
                title="开票处理"
                visible={showEditWindow}
                footer={false}
                onCancel={closeEditInvoiceEvent}
                maskClosable={false}
            >
                <Form
                    {...formItemLayout}
                    initialValues={{ ...curInvoice, invoiceStatus: INVIOCE_STATUS.SUCCESS }}
                    form={openform}
                    onFinish={onFinish}
                    scrollToFirstError
                >
                    <FormItem
                        label="开票结果"
                        name="invoiceStatus"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group>
                            <Radio value={INVIOCE_STATUS.SUCCESS}>已开具</Radio>
                            <Radio value={INVIOCE_STATUS.FAIL}>开具失败</Radio>
                            {curInvoice?.platInvoiceStatus == INVIOCE_STATUS.SUCCESS &&
                            [INVIOCE_MODE.PLAT_JOINT, INVIOCE_MODE.OFFLINE].includes(
                                curInvoice?.invoiceAccessMode,
                            ) ? (
                                <Fragment>
                                    <Radio value={INVIOCE_STATUS.OPENING}>开具中</Radio>
                                    <Radio value={INVIOCE_STATUS.CANCEL}>已红冲</Radio>
                                </Fragment>
                            ) : null}
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.invoiceStatus != curValues.invoiceStatus
                        }
                    >
                        {({ getFieldValue }) => {
                            const invoiceStatusForm = getFieldValue('invoiceStatus');
                            if (
                                [INVIOCE_STATUS.OPENING, INVIOCE_STATUS.CANCEL].includes(
                                    invoiceStatusForm,
                                )
                            ) {
                                return undefined;
                            }
                            if (invoiceStatusForm == INVIOCE_STATUS.FAIL) {
                                return (
                                    <FormItem
                                        label="开票失败原因"
                                        name="invoiceHandleFailReason"
                                        rules={[{ required: true, message: '请填写' }]}
                                    >
                                        <TextArea
                                            rows={4}
                                            placeholder="请填写"
                                            {...inputOptions}
                                            maxLength={50}
                                        />
                                    </FormItem>
                                );
                            }
                            if (batchReOpen) {
                                return (
                                    <FormItem
                                        label="开票日期"
                                        name="invoiceTime"
                                        initialValue={moment()}
                                        rules={[{ required: true, message: '请选择日期' }]}
                                    >
                                        <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                                    </FormItem>
                                );
                            }
                            return (
                                <Fragment>
                                    <FormItem
                                        label="开票日期"
                                        name="invoiceTime"
                                        initialValue={moment()}
                                        rules={[{ required: true, message: '请选择日期' }]}
                                    >
                                        <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                                    </FormItem>
                                    <FormItem label="发票代码" name="invoiceNum">
                                        <Input placeholder="请填写" {...inputOptions} />
                                    </FormItem>
                                    <FormItem label="发票号码" name="invoiceCode">
                                        <Input placeholder="请填写" {...inputOptions} />
                                    </FormItem>

                                    {curInvoice?.invoiceMedia != '02' ? (
                                        <Fragment>
                                            <FormItem label="快递公司" name="courierCompany">
                                                <Input placeholder="请填写" {...inputOptions} />
                                            </FormItem>
                                            <FormItem label="快递单号" name="courierNo">
                                                <Input placeholder="请填写" {...inputOptions} />
                                            </FormItem>
                                        </Fragment>
                                    ) : null}

                                    <FormItem label="发票附件">
                                        <Fragment>
                                            <Upload {...uploadProps}>
                                                {fileList.length < 10 ? (
                                                    <Spin spinning={uploadLoading}>上传附件</Spin>
                                                ) : null}
                                            </Upload>
                                            <div>支持jpg、jpeg、png、pdf格式</div>
                                        </Fragment>
                                    </FormItem>
                                </Fragment>
                            );
                        }}
                    </FormItem>

                    <FormItem>
                        <Space>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                提交
                            </Button>
                            <Button onClick={closeEditInvoiceEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
            <ImportModelLayout
                visible={showImportView}
                onRefresh={searchData}
                onCancel={() => {
                    toggleImportView(false);
                }}
            />
            <InvoiceStockModelLayout
                show={showInvoiceStockView}
                {...props}
                onCancel={() => {
                    toggleInvoiceStockView(false);
                }}
            />
            <ApplyRemarkModal {...props} initRef={remarkRef} successCallback={searchData} />
            <InvoicePoolModal
                initRef={poolModalRef}
                onFinish={() => {
                    searchData();
                }}
            />
        </Fragment>
    );
};

/**
 * 余票弹窗
 */
const InvoiceStockModelLayout = (props) => {
    const {
        show,
        dispatch,
        stocklistLoading,
        invoiceManageModel: { invoiceStockList, invoiceStockListTotal },
        global: {
            invoiceAccessModeOptions, // 开票方式
        },
        onCancel,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});

    useEffect(() => {
        if (invoiceAccessModeOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceAccessModeList',
                options: {},
            });
        }
    }, []);

    useEffect(() => {
        if (show) {
            searchData();
        }
    }, [show, pageInfo]);

    const invoiceAccessMode = useMemo(
        () =>
            invoiceAccessModeOptions.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [invoiceAccessModeOptions],
    );

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            invoiceAccessMode: data.invoiceAccessMode || '',
            operId: data.operId || '',
        };

        dispatch({
            type: 'invoiceManageModel/getInvoiceStockList',
            options: params,
        });
    };

    const finishEvent = (values) => {
        changePageInfo({ pageIndex: 1 });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '开票方式',
            dataIndex: 'invoiceAccessModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '剩余数量',
            dataIndex: 'remainNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '起始发票号码',
            dataIndex: 'invoiceNumStart',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '终止发票号码',
            dataIndex: 'invoiceNumEnd',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <Fragment>
            <Modal
                title="余票查询"
                width={800}
                visible={show}
                onCancel={onCancel}
                footer={null}
                zIndex={1005}
                maskClosable={false}
            >
                <Form form={form} onFinish={finishEvent} className="mg-b" scrollToFirstError>
                    <SearchOptionsBar loading={stocklistLoading} onReset={resetData}>
                        <Col span={8}>
                            <OperSelectTypeItem
                                // rules={[{ message: '请选择运营商', required: true }]}
                                {...formItemLayout}
                                form={form}
                            />
                        </Col>
                        <Col span={8}>
                            <FormItem
                                label="开票方式:"
                                name="invoiceAccessMode"
                                {...formItemLayout}
                            >
                                <Select placeholder="请选择">{invoiceAccessMode}</Select>
                            </FormItem>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <TablePro
                    name="tickets"
                    loading={stocklistLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.operId}
                    dataSource={invoiceStockList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: invoiceStockListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.importRet}
                />
            </Modal>
        </Fragment>
    );
};

/**
 * 导入模板弹窗
 */
const ImportModelLayout = (props) => {
    const { visible, onCancel, onRefresh } = props;
    const [uploadLoading, changeUploadLoading] = useState(false);
    const [showResult, changeShowResult] = useState(false); // 显示上传结果
    const [uploadStatus, changeUploadStatus] = useState(false); // 上传状态
    const [resultMsg, changeResultMsg] = useState(''); // 上传结果

    const [errorImportKey, setErrorImportKey] = useState(null); // 导入失败文件标识

    useEffect(
        () => () => {
            changeShowResult(false);
            changeResultMsg('');
        },
        [],
    );

    const downLoadFile = () => {
        const a = document.createElement('a');
        a.setAttribute('href', `${IMG_URL}/static/excel/员工导入模板.xlsx`);
        a.setAttribute('id', 'ruleTemplate');
        // 防止反复添加
        if (document.getElementById('ruleTemplate')) {
            document.body.removeChild(document.getElementById('ruleTemplate'));
        }
        document.body.appendChild(a);
        a.click();
    };

    const downErrorFileEvent = () => {
        downloadInvoiceImportFailExportApi(errorImportKey);
    };

    const closeModalEvent = () => {
        changeShowResult(false);
        onCancel();
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isXls =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
            if (!isXls) {
                message.error('只支持.xls或.xlsx文件!');
                rej();
                return;
            }

            res();
        });
    const handleUploadChange = (info) => {
        onRefresh();
    };
    const uploadOptions = {
        name: 'file',
        showUploadList: false,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const formData = new FormData();

                formData.append(filename, file);
                changeUploadLoading(true);
                const {
                    data: { msg, ret, invoiceFailExportKey },
                } = await importInvoiceApi(formData);
                changeUploadLoading(false);
                changeUploadStatus(ret !== 'false');
                setErrorImportKey(invoiceFailExportKey);
                message.success(msg);

                onSuccess();
                changeShowResult(true);
                changeResultMsg(msg);
            } catch (error) {
                if (error && error.msg) {
                    message.error(error.msg);
                }
            } finally {
                changeUploadLoading(false);
            }

            // const formData = new FormData();
            // if (data) {
            //     Object.keys(data).forEach(key => {
            //         formData.append(key, data[key]);
            //     });
            // }
            // formData.append(filename, file);

            // axios
            //     .post(action, formData, {
            //         withCredentials,
            //         headers,
            //         onUploadProgress: ({ total, loaded }) => {
            //             onProgress(
            //                 { percent: Number(Math.round((loaded / total) * 100).toFixed(2)) },
            //                 file,
            //             );
            //         },
            //     })
            //     .then(({ data: response }) => {
            //         // this.setState({ legalImgOssKey: response.data.imageId });
            //         onSuccess(response, file);
            //     })
            //     .catch(onError);

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };
    return (
        <Modal
            title="导入发票处理结果"
            width={500}
            visible={visible}
            onCancel={closeModalEvent}
            footer={null}
            maskClosable={false}
        >
            {!showResult ? (
                <Form>
                    {/* <FormItem label="下载模板">
                        <Button type="primary" onClick={downLoadFile}>
                            下载导入模板
                        </Button>
                        <div>请在模板内补充开票时间、发票号码、发票代码</div>
                    </FormItem> */}
                    <FormItem label="上传文件">
                        <Upload {...uploadOptions}>
                            <Button loading={uploadLoading} type="primary">
                                上传文件
                            </Button>
                        </Upload>
                        <div>支持扩展名: .xls .xlsx</div>
                    </FormItem>
                    <FormItem>
                        <div style={{ color: 'red' }}>
                            请在发票列表导出【开具中】的发票申请，在导出的表格中补充开票时间、发票号码、发票代码后上传；纸质发票请额外补充快递公司、快递单号
                        </div>
                    </FormItem>
                </Form>
            ) : uploadStatus ? (
                <Result status="success" title={resultMsg} />
            ) : (
                <Result
                    status="error"
                    title={
                        <div style={{ textAlign: 'center' }}>
                            <p>导入文件存在错误请</p>
                            <p>
                                <span className={styles['table-btn']} onClick={downErrorFileEvent}>
                                    下载失败文件
                                </span>
                            </p>
                        </div>
                    }
                />
            )}
        </Modal>
    );
};

const StatisticsSearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        statisticslistLoading,
        global: {
            invoiceAccessModeOptions, // 开票方式
        },
        user,
    } = props;

    useEffect(() => {
        if (invoiceAccessModeOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceAccessModeList',
                options: {},
            });
        }
    }, []);

    const invoiceAccessMode = useMemo(
        () =>
            invoiceAccessModeOptions.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [invoiceAccessModeOptions],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    dates: [moment().subtract(1, 'month'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={statisticslistLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    exportName="导出至暂存区"
                >
                    <Col span={8}>
                        <FormItem label="申请时间" name="dates">
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            {...formItemLayout}
                            form={form}
                        />
                    </Col>
                    {/* <Col span={8}>
                        <FormItem label="开票方式:" name="invoiceAccessMode" {...formItemLayout}>
                            <Select placeholder="请选择">{invoiceAccessMode}</Select>
                        </FormItem>
                    </Col> */}

                    <Col span={8}>
                        <FormItem label="开票方式:" name="invoiceAccessMode" {...formItemLayout}>
                            <Select placeholder="请选择">{invoiceAccessMode}</Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

// 开票统计
const StatisticsListPage = (props) => {
    const {
        dispatch,
        history,
        invoiceManageModel: {
            invoiceStatisticsList,
            invoiceStatisticsListTotal,
            invoiceStatisticsInfo,
        },
        statisticslistLoading,
        global: { pageInit },
        searchEvent,
        cacheRef,
    } = props;

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}statistics`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cacheName);

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                dates: undefined,
            };

            dispatch({
                type: 'global/setPageInit',
                pathname: cacheName,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'invoiceManageModel/getInvoiceStatisticsInfo',
                options: params,
            });

            params.pageIndex = pageInfo.pageIndex;
            params.pageSize = pageInfo.pageSize;
            dispatch({
                type: 'invoiceManageModel/getInvoiceStatisticsList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = async () => {
        try {
            const data = await form.getFieldsValue();
            const params = {
                ...data,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                dates: undefined,
            };

            cacheRef?.current?.apply(params).then(() => {
                cacheRef?.current?.count();
            });

            // const columnsStrs = [];
            // for (const item of columns) {
            //     if (item.dataIndex) {
            //         columnsStrs.push({
            //             key: item.dataIndex,
            //             value: item.title,
            //         });
            //     } else if (item.title == '开具中') {
            //         columnsStrs.push(
            //             ...[
            //                 {
            //                     key: 'issueIngNum',
            //                     value: '开具中(全部)',
            //                 },
            //                 {
            //                     key: 'issueIngSevenNum',
            //                     value: '开具中(7天以上未开)',
            //                 },
            //                 {
            //                     key: 'issueIngFifteenNum',
            //                     value: '开具中(15天以上未开)',
            //                 },
            //             ],
            //         );
            //     }
            // }

            // exportTableByParams({
            //     methodUrl: invoiceStatisticsListApiPath,
            //     options: params,
            //     columnsStr: columnsStrs,
            // });
        } catch (error) {
            console.log(error);
        }
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return (
                    <div>
                        <div className="text-line" style={{ width: '140px' }} title={text}>
                            {text}
                        </div>
                        <div
                            className="text-line"
                            style={{ width: '140px' }}
                            title={record.projectNo}
                        >
                            {record.projectNo}
                        </div>
                    </div>
                );
            },
        },
        {
            title: '开票方式',
            width: 140,
            dataIndex: 'invoiceAccessModeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票申请',
            width: 120,
            dataIndex: 'invoiceNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: INVIOCE_STATUS.ALL,
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '已开具',
            width: 100,
            dataIndex: 'issueAlreadyNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: '02',
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '开具中',
            children: [
                {
                    title: '全部',
                    width: 80,
                    dataIndex: 'issueIngNum',
                    render(text, record) {
                        return text > 0 ? (
                            <a
                                onClick={async () => {
                                    const condition = await form.getFieldsValue();
                                    searchEvent({
                                        ...condition,
                                        operId: record.operId,
                                        platInvoiceStatus: '04',
                                    });
                                }}
                            >
                                {text}
                            </a>
                        ) : (
                            <span title={text}>{text}</span>
                        );
                    },
                },
                {
                    title: '7天以上未开',
                    width: 140,
                    dataIndex: 'issueIngSevenNum',
                    render(text, record) {
                        return text > 0 ? (
                            <a
                                onClick={async () => {
                                    const condition = await form.getFieldsValue();
                                    searchEvent({
                                        ...condition,
                                        operId: record.operId,
                                        platInvoiceStatus: '04',
                                        greaterDay: 7,
                                    });
                                }}
                            >
                                {text}
                            </a>
                        ) : (
                            <span title={text}>{text}</span>
                        );
                    },
                },
                {
                    title: '15天以上未开',
                    width: 140,
                    dataIndex: 'issueIngFifteenNum',
                    render(text, record) {
                        return text > 0 ? (
                            <a
                                onClick={async () => {
                                    const condition = await form.getFieldsValue();
                                    searchEvent({
                                        ...condition,
                                        operId: record.operId,
                                        platInvoiceStatus: '04',
                                        greaterDay: 15,
                                    });
                                }}
                            >
                                {text}
                            </a>
                        ) : (
                            <span title={text}>{text}</span>
                        );
                    },
                },
            ],
        },
        {
            title: '开具失败',
            width: 120,
            dataIndex: 'issueFailNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: '03',
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '已红冲',
            width: 100,
            dataIndex: 'issueRedNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: '05',
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '红冲中',
            width: 100,
            dataIndex: 'issueRedIngNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: '06',
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '红冲失败',
            width: 100,
            dataIndex: 'issueRedFailNum',
            render(text, record) {
                return text > 0 ? (
                    <a
                        onClick={async () => {
                            const condition = await form.getFieldsValue();
                            searchEvent({
                                ...condition,
                                operId: record.operId,
                                platInvoiceStatus: '07',
                            });
                        }}
                    >
                        {text}
                    </a>
                ) : (
                    <span title={text}>{text}</span>
                );
            },
        },
    ];

    const getNum = (key) => {
        if (invoiceStatisticsInfo?.[key]?.length) {
            return invoiceStatisticsInfo[key];
        }
        return '-';
    };

    return (
        <Fragment>
            <StatisticsSearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <Alert
                message={
                    <Space size="large">
                        <span>
                            运营商：
                            {<span style={{ fontWeight: 'bold' }}>{getNum('operNum')}</span>}家
                        </span>
                        <span>
                            申请开票：
                            {<span style={{ fontWeight: 'bold' }}>{getNum('invoiceNum')}</span>}张
                        </span>
                        <span>
                            已开具：
                            {
                                <span style={{ fontWeight: 'bold' }}>
                                    {getNum('issueAlreadyNum')}
                                </span>
                            }
                            张
                        </span>
                        <span>
                            开具中：
                            {<span style={{ fontWeight: 'bold' }}>{getNum('issueIngNum')}</span>}
                            张（7天以上未开：
                            {invoiceStatisticsInfo?.issueIngSevenNum > 0 ? (
                                <span style={{ color: 'red', fontWeight: 'bold' }}>
                                    {getNum('issueIngSevenNum')}
                                </span>
                            ) : (
                                <span style={{ fontWeight: 'bold' }}>
                                    {getNum('issueIngSevenNum')}
                                </span>
                            )}
                            张
                        </span>
                        <span>
                            15天以上未开：
                            {invoiceStatisticsInfo?.issueIngFifteenNum > 0 ? (
                                <span style={{ color: 'red', fontWeight: 'bold' }}>
                                    {getNum('issueIngFifteenNum')}
                                </span>
                            ) : (
                                <span style={{ fontWeight: 'bold' }}>
                                    {getNum('issueIngFifteenNum')}
                                </span>
                            )}
                            张）
                        </span>
                        <span>
                            开具失败：
                            {invoiceStatisticsInfo?.issueFailNum > 0 ? (
                                <span style={{ color: 'red', fontWeight: 'bold' }}>
                                    {getNum('issueFailNum')}
                                </span>
                            ) : (
                                <span style={{ fontWeight: 'bold' }}>{getNum('issueFailNum')}</span>
                            )}
                            张
                        </span>
                        <span>
                            已红冲：
                            {<span style={{ fontWeight: 'bold' }}>{getNum('issueRedNum')}</span>}张
                        </span>
                        <span>
                            红冲中：
                            {<span style={{ fontWeight: 'bold' }}>{getNum('issueRedIngNum')}</span>}
                            张
                        </span>
                        <span>
                            红冲失败：
                            {
                                <span style={{ fontWeight: 'bold' }}>
                                    {getNum('issueRedFailNum')}
                                </span>
                            }
                            张
                        </span>
                    </Space>
                }
                type="info"
                showIcon
            />
            <br />
            <TablePro
                name="stastic"
                loading={statisticslistLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={invoiceStatisticsList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: invoiceStatisticsListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

const SummarySearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, statisticslistLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    beginDate: moment(),
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={statisticslistLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    exportName="导出至暂存区"
                >
                    <Col span={8}>
                        <FormItem label="开票月份" name="beginDate">
                            <DatePicker
                                style={{ width: '100%' }}
                                picker="month"
                                format="YYYY-MM"
                                allowClear={false}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            {...formItemLayout}
                            form={form}
                        />
                    </Col>
                    {/* <Col span={8}>
                        <FormItem label="开票方式:" name="invoiceAccessMode" {...formItemLayout}>
                            <Select placeholder="请选择">{invoiceAccessMode}</Select>
                        </FormItem>
                    </Col> */}

                    <Col span={8}>
                        <FormItem label="项目编号:" name="projectNo" {...formItemLayout}>
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const SummaryListPage = (props) => {
    const {
        dispatch,
        history,
        invoiceManageModel: { invoiceSummaryList, invoiceSummaryListTotal },
        statisticslistLoading,
        global: { pageInit },
        cacheRef,
    } = props;

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}statistics`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cacheName);

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                invoiceAccessMode: data.invoiceAccessMode,
                projectNo: data.projectNo,
                beginDate: data.beginDate?.format('YYYY-MM'),
            };

            dispatch({
                type: 'global/setPageInit',
                pathname: cacheName,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'invoiceManageModel/getInvoiceSummaryList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            invoiceAccessMode: data.invoiceAccessMode,
            projectNo: data.projectNo,
            beginDate: data.beginDate?.format('YYYY-MM'),
        };

        // const columnsStrs = [];
        // for (const item of columns) {
        //     if (item.dataIndex) {
        //         columnsStrs.push({
        //             key: item.dataIndex,
        //             value: item.title,
        //         });
        //     }
        // }
        // exportTableByParams({
        //     methodUrl: invoiceSummaryListApiPath,
        //     options: params,
        //     columnsStr: columnsStrs,
        // });
        // 下载明细批量导出
        cacheRef?.current?.apply(params).then(() => {
            cacheRef?.current?.count();
        });
    };

    const downLoadDetails = (item) => {
        const params = {
            operId: item.operId,
            invoiceAccessMode: item.invoiceAccessMode,
            projectNo: item.projectNo,
            beginDate: item.invoiceMonth,
            endDate: item.invoiceMonth,
        };
        // 下载明细批量导出
        cacheRef?.current?.apply(params).then(() => {
            cacheRef?.current?.count();
        });
        // invoiceStatisticsExportListApi(params);
    };

    const columns = [
        {
            title: '开票月份',
            width: 160,
            dataIndex: 'invoiceMonth',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        // {
        //     title: '开票方式',
        //     width: 140,
        //     dataIndex: 'invoiceAccessModeName',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '项目编号',
            width: 140,
            dataIndex: 'projectNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '开票张数',
            width: 120,
            align: 'right',
            dataIndex: 'invoiceNum',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: (
                <span>
                    开票金额
                    <Tooltip title="本月开出的发票总额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            align: 'right',

            dataIndex: 'invoiceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    开票税额
                    <Tooltip title="税额等于各费项分别乘以税率。即开票电费*13%+开票服务费*6%">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            align: 'right',

            dataIndex: 'invoiceRateAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本月订单开票金额',
            width: 180,
            align: 'right',

            dataIndex: 'currentMonthOrderInvoiceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '历史订单开票金额',
            width: 180,
            align: 'right',

            dataIndex: 'hisMonthOrderInvoiceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            render(text, record) {
                return (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            downLoadDetails(record);
                        }}
                    >
                        下载明细
                    </span>
                );
            },
        },
        // {
        //     title: (
        //         <span>
        //             未开票金额
        //             <Tooltip title="本月完成且未开票的订单收入 - 往期完成且在本月开票订单收入。订单收入为用户付金额（可开票金额">
        //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
        //             </Tooltip>
        //         </span>
        //     ),
        //     width: 180,
        //     align: 'right',

        //     dataIndex: 'noInvoiceAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: (
        //         <span>
        //             未开票税额
        //             <Tooltip title="未开票电费*13%+未开票服务费（含附加费）*6%">
        //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
        //             </Tooltip>
        //         </span>
        //     ),
        //     width: 180,
        //     align: 'right',

        //     dataIndex: 'noInvoiceRateAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
    ];

    return (
        <Fragment>
            <SummarySearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <TablePro
                name="summary"
                loading={statisticslistLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={invoiceSummaryList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: invoiceSummaryListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

const ManageListPage = (props) => {
    const {
        dispatch,
        history,
        user = {},
        global: { operatorList },
    } = props;
    const { currentUser } = user;

    const [pageType, changePageType] = useState(PAGE_TYPES.APPLY);
    // const [bizType, changeBizType] = useState(); // 导出至暂存区场景值
    const cacheRef = useRef();
    const guideRef = useRef();
    let bizType = '';

    useEffect(() => {
        if (user.currentUser?.operId?.length) {
            guideRef?.current?.open();
        }
    }, []);

    let radioOptions = [];
    if (currentUser.isOpenInvoiceCount == 1) {
        if (user.currentUser?.operId?.length) {
            radioOptions = [
                { label: '开票申请', value: PAGE_TYPES.APPLY },
                { label: '开票统计', value: PAGE_TYPES.STATISTICS },
            ];
        } else {
            radioOptions = [
                { label: '开票申请', value: PAGE_TYPES.APPLY },
                { label: '开票统计', value: PAGE_TYPES.STATISTICS },
                { label: '平台票汇总', value: PAGE_TYPES.SUMMARY },
            ];
        }
    }

    if (pageType == PAGE_TYPES.APPLY) {
        bizType = 'invoiceListExport';
    } else if (pageType == PAGE_TYPES.STATISTICS) {
        bizType = 'invoiceStatisticsList';
    } else if (pageType == PAGE_TYPES.SUMMARY) {
        bizType = 'invoicePlatformList';
    }

    const onPageChange = (e) => {
        changePageType(e.target.value);
    };

    const [searchCondition, updateSearchCondition] = useState();

    const pageShow = () => {
        if (pageType == PAGE_TYPES.APPLY) {
            return (
                <ApplyListPage {...props} cacheRef={cacheRef} searchCondition={searchCondition} />
            );
        }
        if (pageType == PAGE_TYPES.STATISTICS) {
            return (
                <StatisticsListPage
                    {...props}
                    cacheRef={cacheRef}
                    searchEvent={(condition) => {
                        changePageType(PAGE_TYPES.APPLY);
                        updateSearchCondition({ ...condition });
                    }}
                />
            );
        }
        if (pageType == PAGE_TYPES.SUMMARY) {
            return <SummaryListPage {...props} cacheRef={cacheRef} />;
        }
    };

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);
    const stylebottom = currentUser && currentUser.isOpenInvoiceCount == 1 ? '20px' : '0';
    return (
        <PageHeaderWrapper extra={<CacheAreaView bizType={bizType} initRef={cacheRef} isMng />}>
            <Card>
                <div
                    style={{
                        marginBottom: stylebottom,
                    }}
                >
                    <Radio.Group
                        value={pageType}
                        options={radioOptions}
                        onChange={onPageChange}
                        optionType="button"
                        buttonStyle="solid"
                    />
                </div>

                <div>{pageShow()}</div>
            </Card>
            <GuideCompanyModal ref={guideRef}></GuideCompanyModal>
        </PageHeaderWrapper>
    );
};

export default connect(({ user, financeModel, invoiceManageModel, global, loading }) => ({
    financeModel,
    invoiceManageModel,
    global,
    user,
    applylistLoading: loading.effects['invoiceManageModel/getInvoiceApplyList'],
    statisticslistLoading: loading.effects['invoiceManageModel/getInvoiceStatisticsList'],
    stocklistLoading: loading.effects['invoiceManageModel/getInvoiceStockList'],
}))(ManageListPage);
