import { getEnterpriseCompanyInvoiceInfoApi } from '@/services/Enterprise/EnterpriseOrderApi';
import {
    getInvoiceApplyListApi,
    getInvoiceSummaryListApi,
    getInvoiceDetailApi,
    getInvoiceDetailListApi,
    getCardInvoiceDetailListApi,
    getInvoiceStockListApi,
    getInvoiceStatisticsListApi,
    getInvoiceStatisticsInfoApi,
    getInvoiceRemarkRecordListApi,
    getInvoicePoolBalanceApi,
    getInvoicePoolListApi,
    getInvoicePoolUserListApi,
    getVIPInvoiceDetailListApi,
    getOperServiceInvoiceDetailListApi,
} from '@/services/FinanceManage/InvoiceManageApi';

const InvoiceManageModel = {
    namespace: 'invoiceManageModel',
    state: {
        invoiceApplyList: [], // 发票申请列表
        invoiceApplyListTotal: 0, // 发票申请列表总条数
        invoiceApplyRemarkList: [],

        invoiceSummaryList: [], // 发票汇总列表
        invoiceSummaryListTotal: 0, // 发票汇总列表总条数

        invoiceStatisticsInfo: undefined, // 总和
        invoiceStatisticsList: [], // 发票统计列表
        invoiceStatisticsListTotal: 0, // 发票汇总列表总条数

        invoiceStockList: [], // 余票列表
        invoiceStockListTotal: 0, // 余票列表总条数

        companyInvoiceInfo: undefined, // 企业开票详情
        editActInfo: {}, // 当前详情信息
        invoiceDetailList: [], // 订单详情列表
        invoiceDetailListTotal: 0,
        cardDetailList: [], // 卡券详情列表
        cardDetailListTotal: 0,
        vipDetailList: [], // 会员购买明细列表
        vipDetailListTotal: 0,
        operServiceDetailList: [], // 商家服务明细列表
        operServiceDetailListTotal: 0,

        poolBlance: undefined, // 票池信息
        poolSummary: undefined, // 入池/核销汇总
        poolList: [], // 票池列表
        poolTotal: 0, // 票池总数
        poolUserList: [], // 票池用户列表
        poolUserTotal: 0, // 票池用户总数
    },
    effects: {
        /**
         * 发票申请列表
         */
        *getInvoiceApplyList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoiceApplyListApi, options);

                yield put({
                    type: 'updateInvoiceApplyList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 备注历史
         */
        *getInvoiceRemarkRecordList({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getInvoiceRemarkRecordListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { invoiceApplyRemarkList: data },
                });
            } catch (error) {}
        },
        /**
         * 开票统计信息的总和信息
         */
        *getInvoiceStatisticsInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getInvoiceStatisticsInfoApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { invoiceStatisticsInfo: data },
                });
            } catch (error) {}
        },
        /**
         * 发票统计列表
         */
        *getInvoiceStatisticsList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoiceStatisticsListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { invoiceStatisticsList: list, invoiceStatisticsListTotal: total },
                });
            } catch (error) {}
        },
        /**
         * 发票汇总列表
         */
        *getInvoiceSummaryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoiceSummaryListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { invoiceSummaryList: list, invoiceSummaryListTotal: total },
                });
            } catch (error) {}
        },
        /**
         * 余票列表
         */
        *getInvoiceStockList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoiceStockListApi, options);

                yield put({
                    type: 'updateInvoiceStockList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ invoiceAppNo }, { call, put, select }) {
            try {
                const { data } = yield call(getInvoiceDetailApi, invoiceAppNo);
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {}
        },
        /**
         * 查询企业开票详情
         */
        *initCompanyInvoiceInfo({ invoiceApplyId }, { call, put, select }) {
            try {
                const { data } = yield call(getEnterpriseCompanyInvoiceInfoApi, invoiceApplyId);
                yield put({
                    type: 'updateInvoiceProperty',
                    params: { companyInvoiceInfo: data },
                });
            } catch (error) {}
        },
        /**
         * 开票详情列表
         */
        *getInvoiceDetailList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoiceDetailListApi, options);

                yield put({
                    type: 'updateInvoiceDetailList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 卡券详情列表
         */
        *getCardInvoiceDetailList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getCardInvoiceDetailListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { cardDetailList: list, cardDetailListTotal: total },
                });
            } catch (error) {}
        },
        /**
         * 会员购买明细列表列表
         */
        *getVIPInvoiceDetailList({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getVIPInvoiceDetailListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { vipDetailList: data, vipDetailListTotal: data?.length },
                });
            } catch (error) {}
        },
        // 发票管理-详情-商家服务
        *getOperServiceInvoiceDetailList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getOperServiceInvoiceDetailListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { operServiceDetailList: list, operServiceDetailListTotal: total },
                });
            } catch (error) {}
        },
        *getInvoicePoolBalance({ options, callback }, { call, put, select }) {
            try {
                const { data } = yield call(getInvoicePoolBalanceApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { poolBlance: data },
                });
                callback?.(data);
            } catch (error) {}
        },

        *getInvoicePoolList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total, summary },
                } = yield call(getInvoicePoolListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { poolList: list, poolTotal: total, poolSummary: summary },
                });
            } catch (error) {}
        },

        *getInvoicePoolUserList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getInvoicePoolUserListApi, options);

                yield put({
                    type: 'updateInvoiceProperty',
                    params: { poolUserList: list, poolUserTotal: total },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateInvoiceProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateInvoiceApplyList(state, { list, total }) {
            return {
                ...state,
                invoiceApplyList: list,
                invoiceApplyListTotal: total,
            };
        },
        updateInvoiceStockList(state, { list, total }) {
            return {
                ...state,
                invoiceStockList: list,
                invoiceStockListTotal: total,
            };
        },
        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
        updateInvoiceDetailList(state, { list, total }) {
            return {
                ...state,
                invoiceDetailList: list,
                invoiceDetailListTotal: total,
            };
        },
    },
};
export default InvoiceManageModel;
