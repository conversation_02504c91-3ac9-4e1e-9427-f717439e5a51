import { useImperativeHandle, forwardRef, useState, Fragment, useEffect, useMemo } from 'react';
import {
    Button,
    Modal,
    Alert,
    Table,
    Popconfirm,
    Form,
    Space,
    message,
    Typography,
    Row,
    Col,
} from 'antd';
import guide_company_img from '@/assets/images/guide_company.png';

const GuideCompanyModal = (props, ref) => {
    const {} = props;
    const [showModal, toggleShowModal] = useState(false);

    useImperativeHandle(ref, () => {
        return {
            open() {
                toggleShowModal(true);
            },
        };
    });

    const closeEvent = () => {
        toggleShowModal(false);
    };
    const gotoGuide = () => {
        const downloadElement = document.createElement('a');

        downloadElement.href = `https://www.xdtev.com/ui/company/financial/invoice-handle`;
        downloadElement.target = '_blank';

        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
    };
    return (
        <Modal
            width={800}
            visible={showModal}
            title="商家端支持管理用户发票啦"
            onCancel={() => {
                closeEvent();
            }}
            destroyOnClose
            footer={false}
        >
            <Typography.Title level={5}>
                用户发票功能已迁移至商家端，您可以直接在商家端中处理用户发票，无需在本系统和商家端之间来回切换使用！
            </Typography.Title>
            <p>
                <Typography.Text type="danger">
                    本系统将于<Typography.Link>2024年04月30日</Typography.Link>
                    关闭，关闭后用户发票只能在商家端中处理。
                    商家端账号密码以开通邮件为准，如有问题请和我们取得联系。
                </Typography.Text>
            </p>
            <Row>
                <Col>
                    <Typography.Title level={5}>商家端“用户发票”功能入口：</Typography.Title>
                </Col>
                <Col>
                    <img src={guide_company_img} style={{ width: '200px' }}></img>
                </Col>
            </Row>

            <Space
                align="end"
                className="mg-t-20"
                style={{ display: 'flex', justifyContent: 'center' }}
            >
                <Button onClick={closeEvent}>暂时留下来</Button>
                <Button
                    type="primary"
                    onClick={() => {
                        gotoGuide();
                    }}
                >
                    前往商家端
                </Button>
            </Space>
        </Modal>
    );
};
export default forwardRef(GuideCompanyModal);
