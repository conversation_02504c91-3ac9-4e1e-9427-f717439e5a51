import { useImperativeHandle, forwardRef, useState, Fragment, useEffect, useMemo } from 'react';
import { Button, Modal, Alert, Table, Popconfirm, Form, Space, message } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import commonStyles from '@/assets/styles/common.less';
import NumericInput from '@/components/NumericInput/index';
import { isEmpty } from '@/utils/utils';
import {
    getInvoiceWhiteListApi,
    delWhiteApi,
    addWhiteApi,
} from '@/services/FinanceManage/InvoiceManageApi';

const InvoicingWhiteModal = (props, ref) => {
    const {} = props;
    const [showModal, toggleShowModal] = useState(false);
    const [showEditModal, toggleShowEditModal] = useState(false);
    const [tableList, updateTableList] = useState([]);
    const [tableListTotal, updateTableListTotal] = useState(0);
    const [tableListLoading, updateTableListLoading] = useState(false);

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const openModal = () => {
        toggleShowModal(true);
    };

    useEffect(() => {
        if (showModal) {
            searchData();
        }
    }, [showModal, pageInfo]);

    const searchData = async () => {
        try {
            let params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };
            updateTableListLoading(true);
            const {
                data: { records, total },
            } = await getInvoiceWhiteListApi(params);
            updateTableList(records);
            updateTableListTotal(total);
            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateTableListLoading(false);
        }
    };

    const openAddModal = () => {
        form?.resetFields();

        toggleShowEditModal(true);
    };

    const closeEditModal = () => {
        toggleShowEditModal(false);

        form?.resetFields();
    };

    const columns = [
        {
            title: '用户手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 140,
            dataIndex: 'createByName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 120,
            render(text, record) {
                const delBtn = (
                    <Popconfirm
                        title="确认删除"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => {
                            delMobileEvent(record);
                        }}
                    >
                        <span className={commonStyles['table-btn']}>删除</span>
                    </Popconfirm>
                );
                const btnList = [delBtn];
                return <Space>{btnList}</Space>;
            },
        },
    ];

    const submitFormEvent = async (values) => {
        try {
            let params = {
                ...values,
            };

            await addWhiteApi(params);
            message.success('保存成功');
            searchData();
            closeEditModal();

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const delMobileEvent = async (item) => {
        try {
            await delWhiteApi(item.id);
            message.success('删除成功');
            searchData();

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    return (
        <Fragment>
            <Button
                onClick={() => {
                    openModal();
                }}
            >
                开票白名单
            </Button>
            <Modal
                width={800}
                visible={showModal}
                title="开票白名单"
                onCancel={() => {
                    toggleShowModal(false);
                }}
                destroyOnClose
                footer={false}
            >
                <Alert
                    message="用户默认只能选择近一年的订单申请开票，白名单用户没有限制"
                    type="warning"
                    showIcon
                />
                <div className="mg-tb-20">
                    <Button type="primary" onClick={openAddModal}>
                        新建
                    </Button>
                </div>

                <Table
                    loading={tableListLoading}
                    scroll={{ x: 'max-content' }}
                    dataSource={tableList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: tableListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                ></Table>
            </Modal>
            <Modal
                visible={showEditModal}
                onCancel={closeEditModal}
                title={'新建用户'}
                destroyOnClose
                footer={false}
            >
                <Form form={form} initialValues={{}} onFinish={submitFormEvent}>
                    <Form.Item
                        name="mobile"
                        label="用户手机号"
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject('请填写11位手机号');
                                    }
                                    if (value?.length != 11) {
                                        return Promise.reject('请填写11位手机号');
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                    >
                        <NumericInput
                            placeholder="请填写"
                            maxLength={11}
                            autoComplete="off"
                            isHide
                        ></NumericInput>
                    </Form.Item>
                    <Space
                        align="end"
                        className="mg-t-20"
                        style={{ display: 'flex', justifyContent: 'center' }}
                    >
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEditModal}>取消</Button>
                    </Space>
                </Form>
            </Modal>
        </Fragment>
    );
};
export default forwardRef(InvoicingWhiteModal);
