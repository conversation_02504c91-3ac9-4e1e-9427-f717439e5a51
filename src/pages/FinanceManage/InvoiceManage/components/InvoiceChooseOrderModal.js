// 重新开票
import { Form, Modal, Input, Col, DatePicker, message } from 'antd';
import React, { Fragment, useEffect, useImperativeHandle, useState } from 'react';
import { Link } from 'umi';

import usePageState from '@/hooks/usePageState';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';
import { WEEK_CARD_TYPES } from '@/config/declare';
import { VIP_TYPES } from '@/pages/MarketingManage/Membership/MembershipRights/RightsConfig';
import {
    getInvoiceApplicableChargeCardListApi,
    getInvoiceApplicableCompanyOrderListApi,
    getInvoiceApplicableDiscountCardListApi,
    getInvoiceApplicableOrderListApi,
    getInvoiceApplicableVIPListApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import { getChannelOrderPageListApi } from '@/services/MngDefApi';
import SelectChannelItem from '@/components/SelectChannelItem/index';

import { getCodesApi } from '@/services/CommonApi';

import { useRequest } from 'ahooks';

import moment from 'moment';

const { RangePicker } = DatePicker;

const formItemLayout = {
    labelCol: {
        flex: '0 0 70px',
    },
    labelAlign: 'left',
};

export const INVOICE_OPEN_TYPE = {
    CHARGE_ORDER_CHANNEL: '06', // 充电订单（渠道）
    CHARGE_ORDER_COMP: '05', // 充电订单（代付）
    CHARGE_ORDER: '01', // 充电订单
    DISCOUNT_CARD: '03', // 打折卡
    CHARGE_CARD: '02', // 充电卡
    VIP: '04', // 会员
    OPER_SERVICE: '07', // 商家服务-高德流量
};

// 折扣卡展示列
export const InvoiceOpenDiscountColumns = [
    {
        title: '商户流水号',
        width: 200,
        dataIndex: 'buyNo',
        render(text, record) {
            return (
                (record.custId && record.actId && record.buyNo && (
                    <Link
                        to={`/marketing/weekcard/manage/list/details/${record.actId}/${record.custId}/${record.buyNo}`}
                        key="detail"
                        target={'_blank'}
                    >
                        <div style={{ width: '140px' }} title={text}>
                            {text || ''}
                        </div>
                    </Link>
                )) || <span title={text}>{text || ''}</span>
            );
        },
    },
    {
        title: '购买时间',
        width: 200,
        dataIndex: 'buyTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '卡类型',
        width: 140,
        dataIndex: 'cardDisTypeName',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '卡方案',
        width: 140,
        dataIndex: 'actName',
        render(text, record) {
            return (
                (record.actId && (
                    <Link
                        to={
                            (record.cardSaleType == WEEK_CARD_TYPES.GROUP &&
                                `/marketing/weekcard/groupbuy/look/${record.actId}`) ||
                            `/marketing/weekcard/manage/list/plan/look/${record.actId}`
                        }
                        key="detail"
                        target={'_blank'}
                    >
                        <div style={{ width: '140px' }} title={text}>
                            {text || ''}
                        </div>
                    </Link>
                )) || <span title={text}>{text || ''}</span>
            );
        },
    },
    {
        title: '订单金额',
        width: 140,
        dataIndex: 'sellAmt',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '开票金额',
        dataIndex: 'buyAmt',
        width: 160,
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
];

// 充电卡展示列
export const InvoiceOpenChargeCardColumns = [
    {
        title: '商户流水号',
        width: 200,
        dataIndex: 'buyNo',
        render(text, record) {
            return (
                (record.custId && record.actId && record.buyNo && (
                    <Link
                        to={`/marketing/weekcard/manage/list/details/${record.actId}/${record.custId}/${record.buyNo}`}
                        key="detail"
                        target={'_blank'}
                    >
                        <div style={{ width: '140px' }} title={text}>
                            {text || ''}
                        </div>
                    </Link>
                )) || <span title={text}>{text || ''}</span>
            );
        },
    },
    {
        title: '购买时间',
        width: 200,
        dataIndex: 'buyTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '卡方案',
        width: 140,
        dataIndex: 'actName',
        render(text, record) {
            return (
                (record.actId && (
                    <Link
                        to={
                            (record.cardSaleType == WEEK_CARD_TYPES.GROUP &&
                                `/marketing/weekcard/groupbuy/look/${record.actId}`) ||
                            `/marketing/weekcard/manage/list/plan/look/${record.actId}`
                        }
                        key="detail"
                        target={'_blank'}
                    >
                        <div style={{ width: '140px' }} title={text}>
                            {text || ''}
                        </div>
                    </Link>
                )) || <span title={text}>{text || ''}</span>
            );
        },
    },
    {
        title: '订单金额',
        width: 140,
        dataIndex: 'sellAmt',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '开票金额',
        dataIndex: 'buyAmt',
        width: 160,
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
];

// 会员展示列
export const InvoiceOpenVIPColumns = [
    {
        title: '购买流水号',
        width: 200,
        dataIndex: 'buyNo',
        render(text, record) {
            text = record.orderNo || record.buyNo;
            return (
                (record.custId &&
                    record.actId &&
                    record.buyNo &&
                    record.actSubType &&
                    record.custVipId && (
                        <Link
                            to={`/userCenter/membership/manager/list/detail/${record.custId}/${record.actId}/${record.buyNo}/${record.actSubType}/${record.custVipId}`}
                            key="detail"
                            target={'_blank'}
                        >
                            <div style={{ width: '140px' }} title={text}>
                                {text || ''}
                            </div>
                        </Link>
                    )) || <span title={text}>{text || ''}</span>
            );
        },
    },
    {
        title: '购买时间',
        width: 200,
        dataIndex: 'buyTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '购买内容 ',
        width: 160,
        dataIndex: 'buyContent',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '开通方式 ',
        width: 160,
        dataIndex: 'vipNumName',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    // {
    //     title: '订单金额',
    //     width: 140,
    //     dataIndex: 'sellAmt',
    //     render(text, record) {
    //         return <span title={text}>{text}</span>;
    //     },
    // },
    {
        title: '开票金额',
        dataIndex: 'invoiceAmt',
        width: 160,
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '方案名称',
        width: 160,
        dataIndex: 'actName',
        render(text, record) {
            if (record.actId?.length) {
                return (
                    <Link
                        to={
                            record.actSubType == VIP_TYPES.VIP_1_0
                                ? `/userCenter/membership/plan/list/detail/${record.actId}`
                                : `/userCenter/membership/plan/list/detail-vip/${record.actId}`
                        }
                        key="detail"
                        target={'_blank'}
                    >
                        <a>{text}</a>
                    </Link>
                );
            }
            return <span title={text}>{text || ''}</span>;
        },
    },
];

export const invoiceOpenColumns = (openType) => {
    // 充电订单展示列
    const InvoiceOpenChargeOrderColumns = [
        ...(openType === INVOICE_OPEN_TYPE.CHARGE_ORDER
            ? [
                  {
                      title: '下单渠道',
                      width: 140,
                      dataIndex: 'channelName',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
              ]
            : []),

        {
            title: '订单号',
            dataIndex: 'orderNo',
            width: 140,
            render(text, record) {
                if (text?.length) {
                    return (
                        <Link
                            to={`/orderCenter/charge-order/detail?orderNo=${text}`}
                            key="detail"
                            target={'_blank'}
                        >
                            <a>{text}</a>
                        </Link>
                    );
                }
                return <span title={text}>{text || ''}</span>;
            },
        },
        ...(openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL
            ? [
                  {
                      title: '渠道订单号',
                      width: 180,
                      dataIndex: 'startChargeNo',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
              ]
            : [
                  {
                      title: '第三方订单号',
                      width: 180,
                      dataIndex: 'thirdAppNo',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
              ]),
        {
            title: '结算时间',
            width: 180,
            dataIndex: 'settleTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 180,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票总电量',
            width: 140,
            dataIndex: 'chargePq',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电订单总金额',
            width: 160,
            dataIndex: 'chargeAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票总金额',
            width: 140,
            dataIndex: 'invoiceAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...(openType === INVOICE_OPEN_TYPE.CHARGE_ORDER
            ? [
                  {
                      title: '安心充开票金额',
                      width: 160,
                      dataIndex: 'assureChargeInvoiceAmt',
                      align: 'right',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
              ]
            : []),
        {
            title: '开票电费金额',
            width: 160,
            dataIndex: 'invoiceElecAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票服务费金额',
            width: 180,
            dataIndex: 'invoiceServiceAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    switch (openType) {
        case INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP:
        case INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL:
        case INVOICE_OPEN_TYPE.CHARGE_ORDER:
            return InvoiceOpenChargeOrderColumns;
        case INVOICE_OPEN_TYPE.DISCOUNT_CARD:
            return InvoiceOpenDiscountColumns;
        case INVOICE_OPEN_TYPE.CHARGE_CARD:
            return InvoiceOpenChargeCardColumns;
        case INVOICE_OPEN_TYPE.VIP:
            return InvoiceOpenVIPColumns;
        default:
            break;
    }
    return [];
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, listLoading, openType } = props;
    const onFinish = async (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const { data: openInvoiceChannelList } = useRequest(async () => {
        try {
            const {
                data: { list },
            } = await getCodesApi('channel');
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    });

    const selectChannel = (
        <Col span={8}>
            <Form.Item label="下单渠道:" name="channels">
                <SelectChannelItem
                    optionList={openInvoiceChannelList}
                    transform={(list) => {
                        return list.join(',');
                    }}
                ></SelectChannelItem>
            </Form.Item>
        </Col>
    );

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
            {...formItemLayout}
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                {openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL && (
                    <>
                        <Col span={8}>
                            <Form.Item
                                label="订单号"
                                name="orderNo"
                                tooltip="支持批量查询（换行隔开）,最多50条"
                            >
                                <Input.TextArea
                                    placeholder="请输入"
                                    allowClear
                                    autoComplete="off"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="渠道订单号"
                                name="startChargeNo"
                                tooltip="支持批量查询（换行隔开）,最多50条"
                                labelCol={{
                                    labelCol: {
                                        flex: '0 0 100px',
                                    },
                                }}
                            >
                                <Input.TextArea
                                    placeholder="请输入"
                                    allowClear
                                    autoComplete="off"
                                />
                            </Form.Item>
                        </Col>
                    </>
                )}
                {((openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER) && (
                    <Fragment>
                        {selectChannel}

                        <Col span={8}>
                            <OperSelectTypeItem form={form} formItemLayout={formItemLayout} />
                        </Col>

                        <Col span={8}>
                            <AllStationSelect
                                form={form}
                                label="场站名称"
                                name="stationId"
                                formItemLayout={formItemLayout}
                            />
                        </Col>

                        <Col span={8}>
                            <Form.Item label="订单号" name="orderNo">
                                <Input placeholder="请输入" allowClear autoComplete="off" />
                            </Form.Item>
                        </Col>

                        {openType !== INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL && (
                            <Col span={8}>
                                <Form.Item
                                    label="第三方订单号"
                                    name="thirdOrderNo"
                                    labelCol={{
                                        labelCol: {
                                            flex: '0 0 100px',
                                        },
                                    }}
                                >
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                        )}
                        {openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL && (
                            <Col span={8}>
                                <Form.Item
                                    label="渠道订单号"
                                    name="startChargeNo"
                                    labelCol={{
                                        labelCol: {
                                            flex: '0 0 100px',
                                        },
                                    }}
                                >
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                        )}
                        <Col span={8}>
                            <Form.Item
                                label="结算时间:"
                                name="dates"
                                initialValue={
                                    openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL
                                        ? [moment().subtract(7, 'day'), moment()]
                                        : openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                        ? [moment().subtract(31, 'day'), moment()]
                                        : undefined
                                }
                                rules={
                                    openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                                    openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                        ? [
                                              {
                                                  validator(rule, value) {
                                                      const formData = form.getFieldsValue(true);
                                                      const [start, end] = value || [
                                                          undefined,
                                                          undefined,
                                                      ];
                                                      let limitDays = 7;

                                                      if (
                                                          openType ===
                                                          INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
                                                      ) {
                                                          limitDays = 31;
                                                      }
                                                      if (
                                                          !formData?.orderNo &&
                                                          !formData?.startChargeNo &&
                                                          !value
                                                      ) {
                                                          return Promise.reject('请选择结算时间');
                                                      } else if (
                                                          Math.abs(end.diff(start, 'days')) >
                                                          limitDays
                                                      ) {
                                                          return Promise.reject(
                                                              `选取范围最大不超过${limitDays}天`,
                                                          );
                                                      }
                                                      return Promise.resolve();
                                                  },
                                              },
                                          ]
                                        : undefined
                                }
                            >
                                <RangePicker format="YYYY-MM-DD" />
                            </Form.Item>
                        </Col>
                    </Fragment>
                )) ||
                    ((openType == INVOICE_OPEN_TYPE.DISCOUNT_CARD ||
                        openType == INVOICE_OPEN_TYPE.CHARGE_CARD) && (
                        <Fragment>
                            <Col span={8}>
                                <Form.Item
                                    label="商户流水号"
                                    name="buyNo"
                                    labelCol={{
                                        labelCol: {
                                            flex: '0 0 100px',
                                        },
                                    }}
                                >
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="卡方案" name="actName">
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="购买时间:" name="dates">
                                    <RangePicker format="YYYY-MM-DD" />
                                </Form.Item>
                            </Col>
                        </Fragment>
                    )) ||
                    (openType == INVOICE_OPEN_TYPE.VIP && (
                        <Fragment>
                            <Col span={8}>
                                <Form.Item
                                    label="购买流水号"
                                    name="buyNo"
                                    labelCol={{
                                        labelCol: {
                                            flex: '0 0 100px',
                                        },
                                    }}
                                >
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="方案名称" name="actName">
                                    <Input placeholder="请输入" allowClear autoComplete="off" />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="购买时间:" name="dates">
                                    <RangePicker format="YYYY-MM-DD" />
                                </Form.Item>
                            </Col>
                        </Fragment>
                    ))}
            </SearchOptionsBar>
        </Form>
    );
};

const InvoiceChooseOrderModal = (props) => {
    const { initRef, onFinish, openType } = props;

    const [visible, updateVisible] = useState(false);
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [disabledKeys, updateDisabledKeys] = useState([]);
    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [otherParams, updateOtherParams] = useState({}); // 额外的入参

    const [list, updateList] = useState([]);
    const [listTotal, updateListTotal] = useState(0);
    const [listLoading, updateListLoading] = useState(false);
    useImperativeHandle(initRef, () => ({
        show: ({ disabledOrders, defaultList, params = {} }) => {
            if (disabledOrders?.length) {
                updateDisabledKeys(disabledOrders?.map((ele) => ele.orderNo || ele.buyNo));
            }
            if (defaultList instanceof Array && defaultList.length > 0) {
                changeSelectItems(defaultList);
            }
            initData();
            updateOtherParams(params);
            // updateMobile('18459193769');
            updateVisible(true);
        },
    }));

    const onClose = () => {
        updateVisible(false);
        updateDisabledKeys([]);
        changeSelectItems([]);
    };

    const resetData = () => {
        initData();
        changePageInfo({ pageIndex: 1 });
    };

    const initData = () => {
        form.resetFields();
        updateList([]);
        updateListTotal(0);
    };

    const searchData = async () => {
        try {
            updateListLoading(true);
            await form.validateFields(['dates']);
            const data = form.getFieldsValue();
            const params = {
                ...data,
                ...otherParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };
            if (params.dates) {
                if (
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_ORDER
                ) {
                    params.settleTimeBeginTime = params.dates?.[0]?.format?.('YYYY-MM-DD');
                    params.settleTimeEndTime = params.dates?.[1]?.format?.('YYYY-MM-DD');
                } else if (
                    openType == INVOICE_OPEN_TYPE.DISCOUNT_CARD ||
                    openType == INVOICE_OPEN_TYPE.CHARGE_CARD ||
                    openType == INVOICE_OPEN_TYPE.VIP
                ) {
                    params.buyTimeBeginTime = params.dates?.[0]?.format?.('YYYY-MM-DD');
                    params.buyTimeEndTime = params.dates?.[1]?.format?.('YYYY-MM-DD');
                }
                delete params.dates;
            }
            const _list = [];
            let total = 0;
            if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP) {
                const {
                    data: { records = [], total: _total },
                } = await getInvoiceApplicableCompanyOrderListApi(params);
                total = _total;
                _list.push(...records);
            } else if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL) {
                const orderNoLength = params.orderNo?.split('\n') || [];
                const startChargeNoLength = params.orderNo?.split('\n') || [];
                if (orderNoLength.length > 50 || startChargeNoLength.length > 50) {
                    message.error('查询条数不能大于50条');
                    return false;
                }
                const { data: _data = [] } = await getChannelOrderPageListApi(params);
                total = data.length;
                _list.push(..._data);
            } else if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER) {
                const { data: _data = [] } = await getInvoiceApplicableOrderListApi(params);
                total = data.length;
                _list.push(..._data);
            } else if (openType == INVOICE_OPEN_TYPE.CHARGE_CARD) {
                const { data: _data = [] } = await getInvoiceApplicableChargeCardListApi(params);
                total = data.length;
                _list.push(..._data);
            } else if (openType == INVOICE_OPEN_TYPE.DISCOUNT_CARD) {
                const { data: _data = [] } = await getInvoiceApplicableDiscountCardListApi(params);
                total = data.length;
                _list.push(..._data);
            } else if (openType == INVOICE_OPEN_TYPE.VIP) {
                const { data: _data = [] } = await getInvoiceApplicableVIPListApi(params);
                total = data.length;
                _list.push(..._data);
            }
            updateList([..._list]);
            updateListTotal(total);
        } catch (error) {
        } finally {
            updateListLoading(false);
        }
    };
    useEffect(() => {
        if (
            visible &&
            !(
                openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_CHANNEL ||
                openType === INVOICE_OPEN_TYPE.CHARGE_ORDER_COMP
            )
        ) {
            searchData();
        }
    }, [visible]);
    useEffect(() => {
        if (visible) {
            searchData();
        }
    }, [pageInfo]);

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.orderNo || ele.buyNo),
        onChange: (selectedRowKeys, selectedRows) => {
            const tableRoundList = [];
            for (const item of list) {
                tableRoundList.push(item);
            }
            // 筛选出非当前页的勾选项，不予处理
            const otherCpns = selectItems.filter(
                (x) =>
                    tableRoundList.filter(
                        (now) =>
                            (now.orderNo && x.orderNo && now.orderNo == x.orderNo) ||
                            (now.buyNo && x.buyNo && now.buyNo == x.buyNo),
                    ).length == 0,
            );
            changeSelectItems([...otherCpns, ...selectedRows]);
        },
        getCheckboxProps: (record) => ({
            disabled: disabledKeys?.indexOf(record.orderNo || record.buyNo) >= 0,
        }),
    };

    return (
        <Modal
            title="可开票订单"
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            width={980}
            okButtonProps={{ disabled: selectItems?.length == 0 }}
            okText="提交"
            onOk={() => {
                if (openType == INVOICE_OPEN_TYPE.CHARGE_ORDER) {
                    let cooperationPlatforms = [];
                    for (const item of selectItems) {
                        cooperationPlatforms.push(item.cooperationPlatform);
                        cooperationPlatforms = [...new Set(cooperationPlatforms)];
                        if (cooperationPlatforms.length > 1) {
                            message.error('只能选择同一种一级渠道的订单开票，请重新选择');
                            return;
                        }
                    }
                }
                onFinish?.(selectItems);
                onClose();
            }}
            destroyOnClose
        >
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    if (pageInfo.pageIndex === 1) {
                        searchData();
                    } else {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }
                }}
                onReset={resetData}
            />
            <TablePro
                name={`choose_order_modal_${openType}`}
                rowSelection={
                    (list?.length && {
                        type: 'checkbox',
                        ...rowSelection,
                        fixed: true,
                    }) ||
                    null
                }
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.orderNo || record.buyNo}
                dataSource={list}
                columns={invoiceOpenColumns(openType)}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: listTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                    pageSizeOptions: [10, 50, 100, 200, 500],
                }}
            />
        </Modal>
    );
};

export default InvoiceChooseOrderModal;
