// 生成工单按钮
import { useRequest } from 'ahooks';
import { Form, Radio, Popconfirm } from 'antd';
import { Link } from 'umi';
import React, { Fragment, useImperativeHandle, useRef, useState } from 'react';
import { submitWorkOrderApi } from '@/services/FinanceManage/InvoiceManageApi';
import CopyComponent from '@/components/CopyComponent';

export const GenerateWorkOrderButton: React.FC<{
    onFinish: () => void;
    invoiceAppNo: string;
    record: any;
}> = ({ onFinish, invoiceAppNo, record }) => {
    const copyView = (
        <span className="text-clamp-2">
            （ID：{<CopyComponent text={record?.workOrderId}></CopyComponent>}）
        </span>
    );
    const generateWorkOrderBtn = (
        <Popconfirm
            title="确认生成工单"
            okText="确认"
            cancelText="取消"
            onConfirm={async (e) => {
                try {
                    await submitWorkOrderApi({ appNo: invoiceAppNo });
                    onFinish();
                    return;
                } catch (error) {
                    return Promise.reject(error);
                }
                // message.success('Click on Yes');
            }}
        >
            <a>生成工单</a>
        </Popconfirm>
    );
    if (record?.remindResultNameList?.length > 0) {
        return record?.remindResultNameList?.map((item: any, index: number) => {
            if (item.indexOf(record?.workOrderId) !== -1) {
                // 鼠标移入工单ID区域提示点击复制，点击复制工单ID；
                item = item.replace(`（ID：${record?.workOrderId}）`, '');
                return (
                    <div key={index}>
                        {item}
                        {copyView}
                    </div>
                );
            } else {
                return <div key={index}>{item}</div>;
            }
        });
    } else if (!record?.remindResultNameList || record?.remindResultNameList?.length === 0) {
        // 这个订单在催票结果中未生成工单时显示该按钮
        return generateWorkOrderBtn;
    } else {
        return '-';
    }
};
