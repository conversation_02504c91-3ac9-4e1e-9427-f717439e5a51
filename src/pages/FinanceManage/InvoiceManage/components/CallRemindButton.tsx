// 进线催票按钮
import { useRequest } from 'ahooks';
import { Form, Radio, Popconfirm } from 'antd';
import { Link } from 'umi';
import React, { Fragment, useImperativeHandle, useRef, useState } from 'react';
import { callRemindInvoiceApi } from '@/services/FinanceManage/InvoiceManageApi';

export const CallRemindButton: React.FC<{
    onFinish: () => void;
    invoiceAppNo: string;
    record: any;
}> = ({ onFinish, invoiceAppNo, record }) => {
    const [callForm] = Form.useForm();
    const popView = (
        <Popconfirm
            icon=""
            title={
                <Form
                    form={callForm}
                    style={{ width: '200px' }}
                    wrapperCol={{ span: 24 }}
                    labelCol={{ span: 8 }}
                >
                    <Form.Item
                        name="callRemindInvoiceFlag"
                        label="进线催票"
                        initialValue={record?.callRemindFlag}
                    >
                        <Radio.Group>
                            <Radio value="0">否</Radio>
                            <Radio value="1">是</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        noStyle
                        shouldUpdate={(pre, after) =>
                            pre.callRemindInvoiceFlag != after.callRemindInvoiceFlag
                        }
                    >
                        {({ getFieldValue }) => {
                            const callRemindInvoiceFlag = getFieldValue('callRemindInvoiceFlag');
                            return (
                                callRemindInvoiceFlag == '1' &&
                                !record?.workOrderStatus && (
                                    <Form.Item
                                        name="workOrderFlag"
                                        label="生成工单"
                                        initialValue={'1'}
                                    >
                                        <Radio.Group>
                                            <Radio value="0">否</Radio>
                                            <Radio value="1">是</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                )
                            );
                        }}
                    </Form.Item>
                </Form>
            }
            okText="确认"
            cancelText="取消"
            onConfirm={async (e) => {
                try {
                    const callData = callForm.getFieldsValue(true);
                    if (callData.callRemindInvoiceFlag != '1') return;
                    await callRemindInvoiceApi({
                        appNo: invoiceAppNo,
                        callRemindInvoiceFlag: callData.callRemindInvoiceFlag || '1',
                        workOrderFlag: callData.workOrderFlag,
                    });
                    callForm.resetFields();
                    onFinish();
                    return;
                } catch (error) {
                    return Promise.reject(error);
                }
            }}
            onCancel={() => callForm.resetFields()}
        >
            <Link to="">{record?.callRemindFlagName}</Link>
        </Popconfirm>
    );
    if (record?.callRemindFlag == '1') {
        return <span title={record?.callRemindFlagName}>{record?.callRemindFlagName}</span>;
    } else {
        return popView;
    }
};
