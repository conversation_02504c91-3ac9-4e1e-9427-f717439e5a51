import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, Form, Input, Button, message, Typography, Table } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
    batchSyncValidationApi,
    saveBatchSyncApi,
} from '@/services/FinanceManage/InvoiceManageApi';

const { confirm } = Modal;
const BatchSyncModal = (props) => {
    const { initRef } = props;
    const [visible, updateVisible] = useState(false);
    const [stepIndex, setStepIndex] = useState(0);
    const [tableList, setTableList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    useImperativeHandle(initRef, () => ({
        show: () => {
            updateVisible(true);
        },
    }));
    const onClose = () => {
        form.resetFields();
        setStepIndex(0);
        updateVisible(false);
    };
    const onFinish = async (values) => {
        const { invoiceNums = '' } = values;
        try {
            const { data = [] } = await batchSyncValidationApi({
                invoiceNums: invoiceNums.split('\n').filter((item) => item),
            });
            setTableList(data);
            setStepIndex(1);
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    };
    const onSubmit = async () => {
        try {
            const invoiceNums = form.getFieldValue('invoiceNums');
            await saveBatchSyncApi({
                invoiceNums: invoiceNums.split('\n').filter((item) => item),
            });
            message.success('操作成功');
            setLoading(false);
            onClose();
        } catch (error) {
            setLoading(false);
        }
    };
    const columns = [
        {
            title: '发票号码',
            dataIndex: 'invoiceNum',
        },
        {
            title: '结果',
            dataIndex: 'result',
            render: (text, record) => {
                return (
                    <div style={{ color: record?.allowable ? '#25C696' : '#F92929' }}>{text}</div>
                );
            },
        },
    ];
    // 自定义校验函数
    const validateInvoiceNums = (rule, value, callback) => {
        const invoiceArray = value
            .split('\n')
            .map((num) => num.trim())
            .filter((num) => num); // 去除空行和前后空格
        if (invoiceArray.length > 500) {
            callback('发票号码数量不能超过500条');
        } else {
            callback();
        }
    };
    const succuessNum = tableList.filter((item) => item.allowable)?.length || 0;
    const errorNum = tableList.filter((item) => !item.allowable)?.length || 0;
    return (
        <Modal
            title="批量同步金蝶"
            visible={visible}
            onCancel={() => onClose()}
            width={800}
            footer={[
                stepIndex === 0 && (
                    <Button
                        key="next"
                        type="primary"
                        loading={loading}
                        onClick={() => form.submit()}
                    >
                        下一步
                    </Button>
                ),
                stepIndex === 1 && (
                    <Button key="prev" onClick={() => setStepIndex(0)}>
                        上一步
                    </Button>
                ),
                stepIndex === 1 && (
                    <Button
                        type="primary"
                        key="submit"
                        loading={loading}
                        onClick={() => {
                            confirm({
                                title: '确认同步金蝶',
                                icon: <ExclamationCircleOutlined />,
                                content:
                                    errorNum > 0 ? (
                                        <>
                                            <span style={{ color: '#F92929' }}>{errorNum}</span>
                                            个校验失败的发票将自动忽略
                                        </>
                                    ) : (
                                        ''
                                    ),
                                onOk() {
                                    onSubmit();
                                },
                                onCancel() {
                                    console.log('Cancel');
                                },
                            });
                        }}
                    >
                        提交
                    </Button>
                ),
            ]}
        >
            {stepIndex === 0 && (
                <>
                    <p style={{ color: 'red' }}>多张发票回车间隔，每次同步上限500张发票</p>
                    <Form form={form} name="batch-sync-modal" onFinish={onFinish}>
                        <Form.Item
                            label="发票号码"
                            name="invoiceNums"
                            rules={[
                                { required: true, message: '请输入发票号码!' },
                                { validator: validateInvoiceNums },
                            ]}
                        >
                            <Input.TextArea placeholder="请填写" rows={4} />
                        </Form.Item>
                    </Form>
                </>
            )}
            {stepIndex === 1 && (
                <>
                    <Typography.Paragraph>
                        可同步<span style={{ color: '#25C696' }}>{succuessNum}</span>张，不可同步
                        <span style={{ color: '#F92929' }}>{errorNum}</span>
                        张，请核对数据是否正确，提交后将自动忽略不可同步的发票。
                    </Typography.Paragraph>
                    <Table columns={columns} dataSource={tableList} pagination={{showSizeChanger:true}} />
                </>
            )}
        </Modal>
    );
};

export default forwardRef(BatchSyncModal);
