import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import { orderBillBaddebtApiPath } from '@/services/FinanceManage/BillManageApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const refundOptions = [
    { codeName: '订单坏账', codeValue: '0101' },
    { codeName: '坏账回收', codeValue: '0102' },
];

const timeTypes = [
    { codeName: '订单确认时间', codeValue: '01' },
    { codeName: '发生时间', codeValue: '02' },
];

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { bearOptions },
        financeModel: { channelList },
    } = props;

    useEffect(() => {
        if (bearOptions.length == 0) {
            dispatch({
                type: 'global/initBearOptions',
                options: {},
            });
        }
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const timeTypeOptions = () =>
        timeTypes.map((ele) => (
            <Option key={ele.codeValue} value={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));

    const tariffTypeOptions = () =>
        refundOptions.map((ele) => (
            <Option key={ele.codeValue} value={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));

    const bearObjectOptions = useMemo(
        () =>
            bearOptions.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [bearOptions],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem label="发生时间:">
                            <Input.Group>
                                <Row>
                                    <Col flex="0 0 auto">
                                        <FormItem noStyle name="timeType">
                                            <Select placeholder="请选择">
                                                {timeTypeOptions()}
                                            </Select>
                                        </FormItem>
                                    </Col>
                                    <Col flex="1">
                                        <FormItem
                                            noStyle
                                            name="dates"
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('请选择日期');
                                                        }
                                                        if (!value[0]) {
                                                            return Promise.reject('请选择开始日期');
                                                        }
                                                        if (!value[1]) {
                                                            return Promise.reject('请选择结束日期');
                                                        }
                                                        if (value[0] && value[1]) {
                                                            const startTime = +new Date(value[0]);
                                                            const endTime = +new Date(value[1]);
                                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                                            if (
                                                                Math.abs(startTime - endTime) > dest
                                                            ) {
                                                                return Promise.reject(
                                                                    '选取范围最大不超过60天',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <RangePicker format="YYYY-MM-DD" />
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Input.Group>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            onChange={onSubmit}
                            {...formItemLayout}
                            rules={[{ required: true, message: '请选择运营商' }]}
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="第三方订单号:" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="坏账类型:" name="badDebtType" {...formItemLayout}>
                            <Select placeholder="请选择">{tariffTypeOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="承担方:" name="bearObject" {...formItemLayout}>
                            <Select placeholder="请选择">{bearObjectOptions}</Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const RefundListPage = (props) => {
    const {
        dispatch,
        history,
        refundModel: { refundList, refundListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                orderNo: data.orderNo,
                bearObject: data.bearObject,
                badDebtType: data.badDebtType,
                timeType: data.timeType,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };

            dispatch({
                type: 'refundModel/getRefundList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            orderNo: data.orderNo,
            bearObject: data.bearObject,
            badDebtType: data.badDebtType,
            timeType: data.timeType,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };

        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: orderBillBaddebtApiPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '发生时间',
            width: 240,
            dataIndex: 'succeedTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '第三方订单号',
            width: 140,
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '坏账类型',
            width: 140,
            dataIndex: 'orderBadDebtTypeName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '金额',
            width: 140,
            dataIndex: 'badDebtAmt',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '手续费',
            width: 140,
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '承担方',
            width: 140,
            dataIndex: 'bearObjectName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '交易号',
            width: 200,
            dataIndex: 'transNo',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '订单确定时间',
            width: 140,
            dataIndex: 'orderBgnTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={refundList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: refundListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, refundModel, global, loading }) => ({
    financeModel,
    refundModel,
    global,
    listLoading: loading.effects['refundModel/getRefundList'],
}))(RefundListPage);
