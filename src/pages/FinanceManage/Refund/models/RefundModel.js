import { getOrderBillBaddebtApi } from '@/services/FinanceManage/BillManageApi';

const RefundModel = {
    namespace: 'refundModel',
    state: {
        refundList: [], // 坏账列表
        refundListTotal: 0, // 坏账列表总条数
    },
    effects: {
        /**
         * 坏账列表
         */
        *getRefundList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getOrderBillBaddebtApi, options);

                yield put({
                    type: 'updateRefundList',
                    list,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateRefundList(state, { list, total }) {
            return {
                ...state,
                refundList: list,
                refundListTotal: total,
            };
        },
    },
};
export default RefundModel;
