import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    Radio,
    DatePicker,
    Tabs,
    Popconfirm,
    Descriptions,
    message,
    Spin,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectItem from '@/components/OperSelectItem';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { applyBillApi } from '@/services/FinanceManage/BillManageApi';
import { BILL_TYPES, BILL_STATUS } from '@/config/declare';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    // labelAlign: 'left',
    wrapperCol: {
        span: 24,
    },
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, listLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <FormItem label="申请日期" name="dates">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <OperSelectTypeItem
                        // rules={[
                        //     {
                        //         required: true,
                        //         message: '请选择运营商',
                        //     },
                        // ]}
                        {...formItemLayout}
                        onChange={onSubmit}
                        form={form}
                    />
                </Col>

                <Col span={8}>
                    <FormItem label="出账日期" name="dates2">
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="账单编号" name="billNo">
                        <Input placeholder="请填写账单编号" autoComplete="off" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const TempBillListPage = (props) => {
    const {
        dispatch,
        history,
        billManageModel: { tempBillList, tempBillTotal, tempBillDetailInfo, tempBillForecastDate },
        global: { operatorList },
        listLoading,
        detailLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        billType: BILL_TYPES.DAY,
        billStatus: '0',
    });

    useEffect(() => {
        if (!operatorList?.length) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
        return () => {
            dispatch({
                type: 'billManageModel/updateBillManageProperty',
                params: { tempBillList: [], tempBillTotal: 0 },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                billType: pageInfo.billType,
            };

            if (params.dates?.length == 2) {
                params.applyStartDate = params.dates[0].format('YYYY-MM-DD');
                params.applyEndDate = params.dates[1].format('YYYY-MM-DD');
                params.dates = undefined;
            }
            if (params.dates2?.length == 2) {
                params.forecastStartDate = params.dates2[0].format('YYYY-MM-DD');
                params.forecastEndDate = params.dates2[1].format('YYYY-MM-DD');
                params.dates2 = undefined;
            }

            if (pageInfo.billStatus != '0') {
                params.billStatus = pageInfo.billStatus;
            }

            dispatch({
                type: 'billManageModel/getTempBillList',
                options: params,
            });
        } catch (error) {
            console.log(444, error);
        }
    };

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ billStatus: type, pageIndex: 1 });
    };

    const gotoDetailsEvent = (item) => {
        try {
            const params = {
                id: item.id,
            };

            dispatch({
                type: 'billManageModel/getTempBillDetailInfo',
                options: params,
            });
            updateBillDetailVisiable(true);
        } catch (error) {
            console.log(3333, error);
        }
    };

    const columns = [
        {
            title: '申请日期',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商全称',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operNickname',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '账单编号',
            width: 200,
            dataIndex: 'billNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '预出账日期',
            width: 140,
            dataIndex: 'forecastDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '账单状态',
            width: 140,
            dataIndex: 'billStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 160,
            render: (text, record) => (
                <Space>
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            gotoDetailsEvent(record);
                        }}
                    >
                        详情
                    </span>

                    {(record.billStatus == 3 && record.billDownLoadUrl?.length && (
                        <a href={record.billDownLoadUrl} target="_blank" rel="noopener noreferrer">
                            下载账单
                        </a>
                    )) ||
                        null}
                </Space>
            ),
        },
    ];

    // 账单申请
    const [billApplyVisiable, updateBillApplyVisiable] = useState(false);
    const operatorSelectRef = useRef();
    const [applyForm] = Form.useForm();
    const [billApplySubmitState, updateBillApplySubmitState] = useState(false);
    const billDateSearch = () => {
        applyForm.validateFields().then((values) => {
            let params = { ...values };
            if (params.dates.length == 2) {
                params.startDate = params.dates[0].format('YYYY-MM-DD');
                params.endDate = params.dates[1].format('YYYY-MM-DD');
                params.dates = undefined;
            }
            params.remark = undefined;

            dispatch({
                type: 'billManageModel/getTempBillForecastDate',
                options: params,
            });
        });
    };
    const billDateCleaner = () => {
        // 清空预出日期
        dispatch({
            type: 'billManageModel/updateBillManageProperty',
            params: { tempBillForecastDate: undefined },
        });
    };
    const billApplyEvent = () => {
        // 账单申请
        applyForm.validateFields().then(async (values) => {
            let params = { ...values };
            if (params.dates.length == 2) {
                params.startDate = params.dates[0].format('YYYY-MM-DD');
                params.endDate = params.dates[1].format('YYYY-MM-DD');
                params.dates = undefined;
            }
            params.billType = '01';

            updateBillApplySubmitState(true);
            try {
                await applyBillApi(params);
                updateBillApplySubmitState(false);
                billApplyDismiss();
                message.success('提交成功');
                searchData();
            } catch {
                updateBillApplySubmitState(false);
            }
        });
    };
    const billApplyDismiss = () => {
        // 申请弹窗消失方法
        billDateCleaner();
        updateBillApplyVisiable(false);
        applyForm.resetFields();
    };

    // 账单详情
    const [billDetailVisiable, updateBillDetailVisiable] = useState(false);
    const billDetailDismiss = () => {
        updateBillDetailVisiable(false);
        dispatch({
            type: 'billManageModel/updateBillManageProperty',
            params: { tempBillDetailInfo: undefined },
        });
    };

    return (
        <PageHeaderWrapper title="临时账单">
            <Card>
                <SearchLayout
                    form={form}
                    billType={pageInfo.billType}
                    {...props}
                    onSubmit={resetData}
                    onReset={resetData}
                />

                <p>
                    <Button
                        type="primary"
                        onClick={() => {
                            operatorSelectRef?.current?.rest();
                            updateBillApplyVisiable(true);
                        }}
                    >
                        账单申请
                    </Button>
                </p>
                <Tabs defaultActiveKey={'0'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={'0'} />
                    <TabPane tab="待处理" key={'01'} />
                    <TabPane tab="生成中" key={'02'} />
                    <TabPane tab="已生成" key={'03'} />
                    <TabPane tab="生成失败" key={'04'} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.id}
                    dataSource={tempBillList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: tempBillTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.billType}
                />
            </Card>

            <Modal
                title="账单申请"
                visible={billApplyVisiable}
                onCancel={billApplyDismiss}
                footer={null}
                maskClosable={false}
            >
                <Form
                    form={applyForm}
                    {...{
                        ...formItemLayout,
                        labelCol: {
                            flex: '0 0 120px',
                        },
                    }}
                >
                    <Row>
                        <Col span={24}>
                            <OperSelectItem
                                name="operIds"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择运营商',
                                    },
                                ]}
                                initRef={operatorSelectRef}
                                operatorList={operatorList}
                                {...formItemLayout}
                                onChange={billDateCleaner}
                            />
                        </Col>
                        <Col span={20}>
                            <FormItem
                                label="选择账期"
                                name="dates"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择账期',
                                    },
                                ]}
                            >
                                <RangePicker
                                    // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                                    format="YYYY-MM-DD"
                                    onChange={billDateCleaner}
                                />
                            </FormItem>
                        </Col>
                        <Col span={4} style={{ textAlign: 'end' }}>
                            <Button type="primary" onClick={billDateSearch}>
                                查询
                            </Button>
                        </Col>
                        {(tempBillForecastDate && (
                            <>
                                <Col span={24}>
                                    <FormItem label="预出账日期">
                                        {tempBillForecastDate?.forecastDate || '-'}
                                    </FormItem>
                                </Col>
                                <Col span={24}>
                                    <FormItem label="订单渠道">
                                        {tempBillForecastDate?.orderChannelName || '-'}
                                    </FormItem>
                                </Col>
                                <Col span={12}>
                                    <FormItem label="订单结算周期">
                                        {(tempBillForecastDate?.settlePeriod &&
                                            `${tempBillForecastDate?.settlePeriod}小时`) ||
                                            '-'}
                                    </FormItem>
                                </Col>
                                <Col span={12}>
                                    <FormItem label="异常结算周期">
                                        {(tempBillForecastDate?.abnormalSettlePeriod &&
                                            `${tempBillForecastDate?.abnormalSettlePeriod}小时`) ||
                                            '-'}
                                    </FormItem>
                                </Col>
                            </>
                        )) ||
                            undefined}
                        <Col span={24}>
                            <FormItem label="备注" name="remark">
                                <TextArea placeholder="请填写" />
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button type="primary" onClick={billApplyEvent} loading={billApplySubmitState}>
                        提交
                    </Button>
                    <Button onClick={billApplyDismiss}>取消</Button>
                </Space>
            </Modal>

            <Modal
                title="账单详情"
                visible={billDetailVisiable}
                onCancel={billDetailDismiss}
                footer={null}
                maskClosable={false}
            >
                {(detailLoading && (
                    <div style={{ textAlign: 'center' }}>
                        <Spin />
                    </div>
                )) || (
                    <Descriptions
                        title={
                            tempBillDetailInfo?.billNo?.length
                                ? `编号：${tempBillDetailInfo?.billNo}`
                                : undefined
                        }
                        column={2}
                    >
                        <Descriptions.Item label="运营商全称">
                            {tempBillDetailInfo?.operName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="运营商简称">
                            {tempBillDetailInfo?.operNickname || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="账期" span={2}>
                            {`${
                                (tempBillDetailInfo?.startDate &&
                                    moment(tempBillDetailInfo?.startDate, 'YYYY-MM-DD').format(
                                        'YYYY年MM月DD日',
                                    )) ||
                                ''
                            } -
                                ${
                                    (tempBillDetailInfo?.endDate &&
                                        moment(tempBillDetailInfo?.endDate, 'YYYY-MM-DD').format(
                                            'YYYY年MM月DD日',
                                        )) ||
                                    '-'
                                }`}
                        </Descriptions.Item>
                        <Descriptions.Item label="预出账日期">
                            {tempBillDetailInfo?.forecastDate || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="订单渠道">
                            {tempBillDetailInfo?.orderChannelName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="异常结算周期">
                            {(tempBillDetailInfo?.abnormalSettlePeriod &&
                                `${tempBillDetailInfo?.abnormalSettlePeriod}小时`) ||
                                '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="订单结算周期" span={2}>
                            {(tempBillDetailInfo?.settlePeriod &&
                                `${tempBillDetailInfo?.settlePeriod}小时`) ||
                                '-'}
                        </Descriptions.Item>
                        {(tempBillDetailInfo?.remark?.length && (
                            <Descriptions.Item label="备注" span={2}>
                                {tempBillDetailInfo?.remark}
                            </Descriptions.Item>
                        )) ||
                            null}
                        {(tempBillDetailInfo?.billStatus == 4 &&
                            tempBillDetailInfo?.failReason?.length && (
                                <Descriptions.Item label="失败原因" span={2}>
                                    {tempBillDetailInfo?.failReason}
                                </Descriptions.Item>
                            )) ||
                            undefined}
                    </Descriptions>
                )}
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ billManageModel, global, loading }) => ({
    billManageModel,
    global,
    listLoading: loading.effects['billManageModel/getBillManageList'],
    detailLoading: loading.effects['billManageModel/getTempBillDetailInfo'],
}))(TempBillListPage);
