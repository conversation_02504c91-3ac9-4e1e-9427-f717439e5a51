import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo } from 'react';
import { Descriptions, Card, Tooltip, Button, Space } from 'antd';
import styles from './BillSummaryPage.less';
import { InfoCircleOutlined } from '@ant-design/icons';

import { BASE_URL } from '@/config/global';
import { BILL_TYPES } from '@/config/declare';

export const exportBillDetails = ({
    origin = window.location.origin,
    methodUrl = ``,
    options = {},
    columnsStr = [],
    fileName = 'tabel',
    fileType = 'xls',
}) => {
    const exportUrl = `${origin}${BASE_URL}/def/v0.1/order-bill-export`;
    const params = [];
    for (const key in options) {
        if (options.hasOwnProperty(key)) {
            const element = options[key];
            params.push({
                key,
                value: element,
            });
        }
    }
    const formatOptions = (optionList) => optionList.map((ele) => `${ele.key}=${ele.value}`);

    const url = `${exportUrl}?${formatOptions(params).join('&')}`;
    // console.log(333, options, url);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    // a.setAttribute('target', '_blank');
    a.setAttribute('id', 'startTelMedicine');
    // 防止反复添加
    if (document.getElementById('startTelMedicine')) {
        document.body.removeChild(document.getElementById('startTelMedicine'));
    }
    document.body.appendChild(a);
    a.click();
};
const BillSummaryPage = (props) => {
    const {
        dispatch,
        history,
        billManageModel: {
            selectFilterInfo,
            settleInfo: {
                orderBillNoSettleList = [],
                rptOrderBillSettleList = [],
                rtpOrderBillBadList = [],
                entryAmt,
            },
            entryInfo: {
                rptOrderBillEntryList = [],
                rptOrderBillNoEntryList = [],
                rptOrderBillEntryTotal = {},
            },
        },
        summaryLoading,
    } = props;
    useEffect(() => {
        const options = {
            billType: selectFilterInfo.billType,
            orderBillDate: selectFilterInfo.orderBillDate,
            operId: selectFilterInfo.operId,
        };

        dispatch({
            type: 'billManageModel/getBillSettleAndEntry',
            options,
        });
    }, []);

    const settleColumns = [
        {
            title: '订单类型',
            dataIndex: 'orderBillTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单数',
            dataIndex: 'orderBillNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            dataIndex: 'orderAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: (
        //         <span>
        //             订单应收
        //             <Tooltip title="订单应收=订单金额-订单应分润金额">
        //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
        //             </Tooltip>
        //         </span>
        //     ),
        //     dataIndex: 'orderReceiveAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },

        {
            title: '运营商承担费用',
            dataIndex: 'orderBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途承担费用',
            dataIndex: 'platformBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '新电途补贴金额',
        //     dataIndex: 'platformSubAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '本期新电途补贴金额',
            dataIndex: 'issuePlatformSubAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '历史新电途补贴金额',
            dataIndex: 'historyPlatformSubAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '用户实付金额',
        //     dataIndex: 'userPayAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '本期用户实付金额',
            dataIndex: 'issueUserPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '历史用户实付金额',
            dataIndex: 'historyUserPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途线上分润金额',
            dataIndex: 'platformProfitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途线下分润金额',
            dataIndex: 'offinleProfitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本期支付手续费',
            dataIndex: 'issueServiceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '历史支付手续费',
            dataIndex: 'historyServiceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '本期支付手续费',
        //     dataIndex: 'serviceCharge',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: '历史支付手续费',
        //     dataIndex: 'serviceCharge',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '用户欠款',
            dataIndex: 'userArrearsAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    const specialColumns = [
        {
            title: '订单类型',
            dataIndex: 'orderBillTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单数',
            dataIndex: 'orderBillNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '退款金额',
            dataIndex: 'refundAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '坏账金额',
            dataIndex: 'badDebtAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商承担金额',
            dataIndex: 'orderBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途承担金额',
            dataIndex: 'platformBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商回收金额',
            dataIndex: 'recoveryAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途回收金额',
            dataIndex: 'offinleProfitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本期用户实付金额',
            dataIndex: 'issueUserPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '本期支付手续费',
            dataIndex: 'issueServiceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const entryColumns = [
        {
            title: '订单类型',
            dataIndex: 'orderBillTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单数',
            dataIndex: 'orderBillNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            dataIndex: 'orderAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: (
        //         <span>
        //             订单应收
        //             <Tooltip title="订单应收=订单金额-订单应分润金额">
        //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
        //             </Tooltip>
        //         </span>
        //     ),
        //     dataIndex: 'orderReceiveAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '新电途补贴金额',
            dataIndex: 'platformSubAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户实付金额',
            dataIndex: 'userPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '本期用户实付金额',
        //     dataIndex: 'userPayAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: '历史用户实付金额',
        //     dataIndex: 'userPayAmt',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '新电途分润金额',
            dataIndex: 'platformProfitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付手续费',
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '本期支付手续费',
        //     dataIndex: 'serviceCharge',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        // {
        //     title: '历史支付手续费',
        //     dataIndex: 'serviceCharge',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
        {
            title: '汇总金额',
            dataIndex: 'totalAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const sumTotal = useMemo(() => {
        const result = [];

        entryColumns.forEach((col) => {
            if (
                col.dataIndex == 'platformProfitAmt' ||
                col.dataIndex == 'platformSubAmt' ||
                col.dataIndex == 'serviceCharge' ||
                col.dataIndex == 'totalAmt' ||
                col.dataIndex == 'userPayAmt'
            ) {
                result.push(
                    <td>
                        {(rptOrderBillEntryTotal && rptOrderBillEntryTotal[col.dataIndex]) || '0'}
                    </td>,
                );
            }
        });

        return result;
    }, [rptOrderBillEntryTotal]);

    const gotoDetails = () => {
        if (selectFilterInfo.billType == BILL_TYPES.DAY) {
            history.push(`/financemanage/operator/bill/billDetails/day`);
        } else if (selectFilterInfo.billType == BILL_TYPES.MONTH) {
            history.push(`/financemanage/operator/bill/billDetails/day/month`);
        }
    };

    const exportBillDetailsEvent = () => {
        const a = document.createElement('a');
        a.setAttribute('href', selectFilterInfo.billDownLoadUrl);
        // a.setAttribute('target', '_blank');
        a.setAttribute('id', 'startTelMedicine');
        // 防止反复添加
        if (document.getElementById('startTelMedicine')) {
            document.body.removeChild(document.getElementById('startTelMedicine'));
        }
        document.body.appendChild(a);
        a.click();
    };

    return (
        <PageHeaderWrapper content="每月5日前生成上月账单">
            <Card loading={summaryLoading}>
                <Descriptions column={4}>
                    <Descriptions.Item label="运营商">
                        {selectFilterInfo.operNickname}
                    </Descriptions.Item>
                    <Descriptions.Item label="账单周期">
                        {selectFilterInfo.orderBillDate}
                    </Descriptions.Item>
                    <Descriptions.Item label="入账金额">{entryAmt}</Descriptions.Item>
                    <Descriptions.Item label="运营商编号">
                        {selectFilterInfo.operId || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="合同编号">
                        {selectFilterInfo.operProjectCode || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item>
                        <Space>
                            <Button onClick={gotoDetails}>查看明细</Button>
                            <Button type="primary" onClick={exportBillDetailsEvent}>
                                导出账单及明细
                            </Button>
                        </Space>
                    </Descriptions.Item>
                </Descriptions>
                {rptOrderBillSettleList && rptOrderBillSettleList.length > 0 ? (
                    <Descriptions title="本期结算信息">
                        <Descriptions.Item>
                            <table className={styles['details-table']}>
                                <tr className={styles['details-table-header']}>
                                    <th colSpan={4}>基础信息</th>
                                    <th colSpan={6}>收入信息</th>
                                    <th colSpan={4}>支出信息</th>
                                    <th rowSpan={2}>用户欠款</th>
                                </tr>
                                <tr className={styles['details-table-header']}>
                                    {settleColumns.map((ele) =>
                                        ele.dataIndex == 'userArrearsAmt' ? null : (
                                            <th>{ele.title}</th>
                                        ),
                                    )}
                                </tr>
                                {rptOrderBillSettleList.map((ele, index) => {
                                    const result = [];
                                    // if (index == 0) {
                                    //     result.push(
                                    //         <td rowSpan={rptOrderBillSettleList.length}>已结算</td>,
                                    //     );
                                    // }
                                    const grayIndexs = [
                                        'issuePlatformSubAmt',
                                        'historyPlatformSubAmt',
                                        'platformProfitAmt',
                                        'offinleProfitAmt',
                                    ];
                                    settleColumns.forEach((col) => {
                                        if (grayIndexs.includes(col.dataIndex)) {
                                            result.push(
                                                <td
                                                    key={col.dataIndex}
                                                    style={{ backgroundColor: '#f8f8f8' }}
                                                >
                                                    {ele[col.dataIndex] || '0'}
                                                </td>,
                                            );
                                        } else {
                                            result.push(
                                                <td key={col.dataIndex}>
                                                    {ele[col.dataIndex] || '0'}
                                                </td>,
                                            );
                                        }
                                    });
                                    const name = ele.orderBillTypeName == '合计' ? styles.bold : '';

                                    return (
                                        <tr key={index} className={name}>
                                            {result}
                                        </tr>
                                    );
                                })}
                            </table>
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
                {orderBillNoSettleList && orderBillNoSettleList.length > 0 ? (
                    <Descriptions title="未结算信息">
                        <Descriptions.Item>
                            <table className={styles['details-table']}>
                                <tr className={styles['details-table-header']}>
                                    <th colSpan={4}>基础信息</th>
                                    <th colSpan={6}>收入信息</th>
                                    <th colSpan={4}>支出信息</th>
                                    <th rowSpan={2}>用户欠款</th>
                                </tr>
                                <tr className={styles['details-table-header']}>
                                    {settleColumns.map((ele) =>
                                        ele.dataIndex == 'userArrearsAmt' ? null : (
                                            <th>{ele.title}</th>
                                        ),
                                    )}
                                </tr>
                                {orderBillNoSettleList.map((ele, index) => {
                                    const result = [];
                                    // if (index == 0) {
                                    //     result.push(
                                    //         <td rowSpan={orderBillNoSettleList.length}>未结算</td>,
                                    //     );
                                    // }
                                    const grayIndexs = [
                                        'issuePlatformSubAmt',
                                        'historyPlatformSubAmt',
                                        'platformProfitAmt',
                                        'offinleProfitAmt',
                                    ];
                                    settleColumns.forEach((col) => {
                                        if (grayIndexs.includes(col.dataIndex)) {
                                            result.push(
                                                <td
                                                    key={col.dataIndex}
                                                    style={{ backgroundColor: '#f8f8f8' }}
                                                >
                                                    {ele[col.dataIndex] || '0'}
                                                </td>,
                                            );
                                        } else {
                                            result.push(
                                                <td key={col.dataIndex}>
                                                    {ele[col.dataIndex] || '0'}
                                                </td>,
                                            );
                                        }
                                    });

                                    const name = ele.orderBillTypeName == '合计' ? styles.bold : '';

                                    return (
                                        <tr key={index} className={name}>
                                            {result}
                                        </tr>
                                    );
                                })}
                            </table>
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
                {rtpOrderBillBadList && rtpOrderBillBadList.length > 0 ? (
                    <Descriptions title="特殊订单结算">
                        <Descriptions.Item>
                            <table className={styles['details-table']}>
                                <tr className={styles['details-table-header']}>
                                    {specialColumns.map((ele, index) =>
                                        ele.dataIndex == 'userArrearsAmt' ? null : (
                                            <th key={index}>{ele.title}</th>
                                        ),
                                    )}
                                </tr>
                                {rtpOrderBillBadList.map((ele, index) => {
                                    const result = [];
                                    // if (index == 0) {
                                    //     result.push(
                                    //         <td rowSpan={orderBillNoSettleList.length}>未结算</td>,
                                    //     );
                                    // }
                                    specialColumns.forEach((col) => {
                                        result.push(
                                            <td key={col.dataIndex}>
                                                {ele[col.dataIndex] || '0'}
                                            </td>,
                                        );
                                    });

                                    const name = ele.orderBillTypeName == '合计' ? styles.bold : '';

                                    return (
                                        <tr key={index} className={name}>
                                            {result}
                                        </tr>
                                    );
                                })}
                            </table>
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}

                {/* <Descriptions title="本期入账信息">
                    <Descriptions.Item>
                        <table className={styles['details-table']}>
                            <tr className={styles['details-table-header']}>
                                <th rowSpan={2}>结算状态</th>
                                <th colSpan={5}>基础信息</th>
                                <th colSpan={5}>线上入账</th>
                            </tr>
                            <tr className={styles['details-table-header']}>
                                {entryColumns.map(ele => (
                                    <th>{ele.title}</th>
                                ))}
                            </tr>
                            {rptOrderBillEntryList.map((ele, index) => {
                                const result = [];
                                if (index == 0) {
                                    result.push(
                                        <td rowSpan={rptOrderBillEntryList.length}>已结算</td>,
                                    );
                                }
                                entryColumns.forEach(col => {
                                    result.push(<td>{ele[col.dataIndex] || '0'}</td>);
                                });
                                return <tr>{result}</tr>;
                            })}
                            {rptOrderBillNoEntryList.map((ele, index) => {
                                const result = [];
                                if (index == 0) {
                                    result.push(
                                        <td rowSpan={rptOrderBillNoEntryList.length}>未结算</td>,
                                    );
                                }
                                entryColumns.forEach(col => {
                                    result.push(<td>{ele[col.dataIndex] || '0'}</td>);
                                });

                                return <tr>{result}</tr>;
                            })}
                            <tr className={styles.bold}>
                                <td colSpan={6}>合计</td>
                                {sumTotal}
                            </tr>
                        </table>
                    </Descriptions.Item>
                </Descriptions> */}
                <div style={{ color: 'red' }}>
                    注：支付宝实际入账金额=本期用户实付金额（已结算+未结算）合计-线上分润金额-本期支付手续费（已结算+未结算）合计+（如有）特殊订单本期用户实付-（如有）特殊订单本期支付手续费
                </div>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ billManageModel, global, loading }) => ({
    billManageModel,
    global,
    summaryLoading: loading.effects['billManageModel/getBillSettleAndEntry'],
}))(BillSummaryPage);
