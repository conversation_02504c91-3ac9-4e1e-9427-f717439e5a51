import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Tooltip,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { InfoCircleOutlined } from '@ant-design/icons';
import OperSelectItem from '@/components/OperSelectItem';
import styles from '@/assets/styles/common.less';
import { getUserOrderListPath } from '@/services/FinanceManage/FinanceManageApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    CURRENT: '0301', // 当期订单
    HISTORY: '0302', // 历史订单
    REFUND: '0303', // 退款明细
    BAD: '0304', // 坏账明细
    OFFLINE: '0305', // 线下结算
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="订单号:" name="orderNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const BillDetailsListPage = (props) => {
    const {
        dispatch,
        history,
        match,
        billDetailsModel: { billDetailsList, billDetailsListTotal },
        billManageModel: { selectFilterInfo },
        listLoading,
    } = props;

    const {
        params: { orderNo },
        path,
    } = match;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.CURRENT,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const searchBar = useCallback(() => {
        if (pageInfo.tabType == STATUS_TYPES.CURRENT || pageInfo.tabType == STATUS_TYPES.HISTORY) {
            return (
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
            );
        }
        return null;
    }, [pageInfo.tabType]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const options = {
            orderBillDate: selectFilterInfo.orderBillDate,
            billType: selectFilterInfo.billType,
            operId: selectFilterInfo.operId,
        };
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            orderNo: data.orderNo,
            orderBillDetailType: pageInfo.tabType,
            ...options,
        };

        dispatch({
            type: 'billDetailsModel/getBillDetailsList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabType = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = useMemo(() => {
        if (pageInfo.tabType == STATUS_TYPES.REFUND) {
            return [
                {
                    title: '退款时间',
                    width: 240,
                    dataIndex: 'orderBgnTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '订单号',
                    width: 240,
                    dataIndex: 'orderNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '第三方交易号',
                    width: 200,
                    dataIndex: 'tradeNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '退款原因',
                    width: 240,
                    dataIndex: 'remark',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '退款金额',
                    width: 140,
                    align: 'right',
                    dataIndex: 'orderAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '新电途承担退款',
                    width: 200,
                    align: 'right',
                    dataIndex: 'platformBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '运营商承担退款',
                    width: 200,
                    align: 'right',
                    dataIndex: 'operBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
            ];
        }
        if (pageInfo.tabType == STATUS_TYPES.BAD) {
            return [
                {
                    title: '产生时间',
                    width: 140,
                    dataIndex: 'orderBgnTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '订单号',
                    width: 240,
                    dataIndex: 'orderNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '坏账类型',
                    width: 240,
                    dataIndex: 'orderBadDebtTypeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '订单付款方式',
                    width: 140,
                    dataIndex: 'orderPayTypeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '坏账金额',
                    width: 200,
                    align: 'right',
                    dataIndex: 'orderAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '新电途承担坏账',
                    width: 200,
                    align: 'right',
                    dataIndex: 'platformBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '运营商承担坏账',
                    width: 200,
                    align: 'right',
                    dataIndex: 'operBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
            ];
        }
        if (
            pageInfo.tabType == STATUS_TYPES.CURRENT ||
            pageInfo.tabType == STATUS_TYPES.HISTORY ||
            pageInfo.tabType == STATUS_TYPES.OFFLINE
        ) {
            const badAmt =
                pageInfo.tabType == STATUS_TYPES.HISTORY
                    ? {
                          title: (
                              <span>
                                  坏账金额
                                  <Tooltip title="超过坏账周期仍未结清的用户欠款金额">
                                      <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                  </Tooltip>
                              </span>
                          ),
                          width: 200,
                          align: 'right',
                          dataIndex: 'badDebtAmt',
                          render(text, record) {
                              return <span title={text}>{text}</span>;
                          },
                      }
                    : { width: 0 };
            const orderChannel =
                pageInfo.tabType == STATUS_TYPES.CURRENT || pageInfo.tabType == STATUS_TYPES.OFFLINE
                    ? {
                          title: <span>订单渠道</span>,
                          width: 140,
                          align: 'right',
                          dataIndex: 'orderChannelName',
                          render(text, record) {
                              return <span title={text}>{text}</span>;
                          },
                      }
                    : { width: 0 };
            const stationName =
                pageInfo.tabType == STATUS_TYPES.CURRENT || pageInfo.tabType == STATUS_TYPES.OFFLINE
                    ? {
                          title: <span>站点</span>,
                          width: 140,
                          dataIndex: 'stationName',
                          render(text, record) {
                              return (
                                  <div
                                      className="text-line"
                                      style={{ width: '140px' }}
                                      title={text}
                                  >
                                      {text}
                                  </div>
                              );
                          },
                      }
                    : { width: 0 };
            return [
                {
                    title: '订单时间',
                    width: 240,
                    dataIndex: 'orderBgnTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                // {
                //     title: '订单号',
                //     width: 240,
                //     dataIndex: 'orderNo',
                //     render(text, record) {
                //         return <span title={text}>{text}</span>;
                //     },
                // },
                {
                    title: '第三方订单号',
                    width: 240,
                    dataIndex: 'partOrderNo',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '城市',
                    width: 140,
                    dataIndex: 'cityName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                stationName,
                {
                    title: '异常类型',
                    width: 140,
                    dataIndex: 'operAnboTypeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '充电量',
                    width: 140,
                    align: 'right',
                    dataIndex: 'chargePq',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '订单金额',
                    width: 140,
                    align: 'right',
                    dataIndex: 'orderAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: (
                        <span>
                            订单应收
                            <Tooltip title="订单应收=订单金额-订单应分润金额">
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        </span>
                    ),
                    width: 200,
                    align: 'right',
                    dataIndex: 'orderReceiveAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: (
                        <span>
                            运营商承担费用
                            <Tooltip title="运营商出资的营销活动费用">
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        </span>
                    ),
                    width: 240,
                    align: 'right',
                    dataIndex: 'operBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: (
                        <span>
                            新电途承担费用
                            <Tooltip title="新电途资金券抵扣金额，运营商资金未减少">
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        </span>
                    ),
                    width: 240,
                    align: 'right',
                    dataIndex: 'platformBearAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                // {
                //     title: (
                //         <span>
                //             新电途补贴金额
                //             <Tooltip title="新电途优惠券抵扣金额，运营商收款减少，需线下与新电途结算">
                //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                //             </Tooltip>
                //         </span>
                //     ),
                //     width: 200,
                //     dataIndex: 'platformSubAmt',
                //     render(text, record) {
                //         return <span title={text}>{text}</span>;
                //     },
                // },
                {
                    title: <span>本期新电途补贴金额</span>,
                    width: 220,
                    align: 'right',
                    dataIndex: 'issuePlatformSubAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: <span>历史新电途补贴金额</span>,
                    width: 220,
                    align: 'right',
                    dataIndex: 'historyPlatformSubAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                // {
                //     title: (
                //         <span>
                //             用户实付金额
                //             <Tooltip title="用户通过线上渠道实际支付金额；不包含新电途优惠券或资金券">
                //                 <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                //             </Tooltip>
                //         </span>
                //     ),
                //     width: 240,
                //     dataIndex: 'userPayAmt',
                //     render(text, record) {
                //         return <span title={text}>{text}</span>;
                //     },
                // },
                {
                    title: (
                        <span>
                            本期用户实付金额
                            <Tooltip title="用户通过线上渠道实际支付金额；不包含新电途优惠券或资金券">
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        </span>
                    ),
                    width: 260,
                    align: 'right',
                    dataIndex: 'issueUserPayAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: <span>历史用户实付金额</span>,
                    width: 260,
                    align: 'right',
                    dataIndex: 'historyUserPayAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: (
                        <span>
                            未结清金额
                            <Tooltip title="订单待补收或待退款金额">
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        </span>
                    ),
                    width: 160,
                    align: 'right',
                    dataIndex: 'noPayAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                badAmt,
                {
                    title: '新电途线上分润金额',
                    width: 220,
                    align: 'right',
                    dataIndex: 'platformProfitAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '新电途线下分润金额',
                    width: 220,
                    align: 'right',
                    dataIndex: 'offlineProfitAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                // {
                //     title: '手续费支出',
                //     width: 140,
                //     dataIndex: 'serviceCharge',
                //     render(text, record) {
                //         return <span title={text}>{text}</span>;
                //     },
                // },
                {
                    title: '本期支付手续费',
                    width: 200,
                    align: 'right',
                    dataIndex: 'issueServiceCharge',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '历史支付手续费',
                    width: 200,
                    align: 'right',
                    dataIndex: 'historyServiceCharge',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '平台分润方式',
                    width: 200,
                    dataIndex: 'profitRuleTypeName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '自动扣除平台活动费用',
                    width: 200,
                    align: 'right',
                    dataIndex: 'deducioActFlagName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '结算状态',
                    width: 140,
                    dataIndex: 'orderSettleStatusName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                orderChannel,
                {
                    title: '充电费',
                    width: 140,
                    align: 'right',
                    dataIndex: 'elecAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '服务费',
                    width: 140,
                    align: 'right',
                    dataIndex: 'serviceAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '附加费',
                    width: 140,
                    align: 'right',
                    dataIndex: 'incrementAmt',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '结算日期',
                    width: 140,
                    dataIndex: 'orderSettleDate',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '分润账号',
                    width: 140,
                    dataIndex: 'profitAccount',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '操作',
                    fixed: 'right',
                    width: 100,
                    render: (text, record) => (
                        <Fragment>
                            <span
                                className={styles['table-btn']}
                                onClick={() => {
                                    gotoOrderDetails(record);
                                }}
                            >
                                详情
                            </span>
                        </Fragment>
                    ),
                },
            ];
        }
    }, [pageInfo.tabType]);

    const gotoOrderDetails = (item) => {
        history.push(`/financemanage/orderTransaction/list/details/${item.orderNo}`);
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Tabs defaultActiveKey="00" onChange={changeTabType}>
                    <TabPane tab="当期订单" key={STATUS_TYPES.CURRENT} />
                    <TabPane tab="历史订单" key={STATUS_TYPES.HISTORY} />
                    <TabPane tab="退款明细" key={STATUS_TYPES.REFUND} />
                    <TabPane tab="坏账明细" key={STATUS_TYPES.BAD} />
                    {/* <TabPane tab="线下结算" key={STATUS_TYPES.OFFLINE} /> */}
                </Tabs>
                {searchBar()}

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={billDetailsList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: billDetailsListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ billManageModel, billDetailsModel, loading }) => ({
    billManageModel,
    billDetailsModel,
    listLoading: loading.effects['billDetailsModel/getBillDetailsList'],
}))(BillDetailsListPage);
