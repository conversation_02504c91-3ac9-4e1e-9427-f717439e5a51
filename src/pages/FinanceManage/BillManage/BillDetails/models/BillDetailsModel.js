import { getBillDayListApi } from '@/services/FinanceManage/BillManageApi';

const BillDetailsModel = {
    namespace: 'billDetailsModel',
    state: {
        billDetailsList: [], // 用户订单交易列表
        billDetailsListTotal: 0, // 用户订单交易列表总条数
    },
    effects: {
        /**
         * 用户订单交易列表
         */
        *getBillDetailsList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBillDayListApi, options);

                yield put({
                    type: 'updateBillDetailsList',
                    list,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateBillDetailsList(state, { list, total }) {
            return {
                ...state,
                billDetailsList: list,
                billDetailsListTotal: total,
            };
        },
    },
};
export default BillDetailsModel;
