import {
    getBillManageListApi,
    getBillSettleInfoApi,
    getBillEntryInfoApi,
    getBillListApi,
    detailInfoApi,
    queryForecastDateApi,
} from '@/services/FinanceManage/BillManageApi';
import { SessionStorage } from '@/utils/SessionStorage/index';

const BillManageModel = {
    namespace: 'billManageModel',
    state: {
        billManageList: [], // 用户订单交易列表
        billManageListTotal: 0, // 用户订单交易列表总条数
        selectFilterInfo: SessionStorage.getItem('selectFilterInfo'),
        settleInfo: {
            orderBillNoSettleList: [],
            entryAmt: '',
            rptOrderBillSettleList: [],
        }, // 本期结算数据
        entryInfo: {
            rptOrderBillEntryList: [],
            rptOrderBillNoEntryList: [],
            rptOrderBillEntryTotal: {},
        }, // 本期入账数据

        // 临时账单
        tempBillList: [], // 账单列表
        tempBillTotal: 0, // 账单列表条数
        tempBillForecastDate: undefined, // 账单预出日期
        tempBillDetailInfo: undefined, // 账单详情
    },
    effects: {
        /**
         * 用户订单交易列表
         */
        *getBillManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBillManageListApi, options);

                yield put({
                    type: 'updateBillManageList',
                    list,
                    total,
                });
            } catch (error) {}
        },

        *getBillSettleAndEntry({ options }, { call, put, select }) {
            try {
                const { data: settleInfo } = yield call(getBillSettleInfoApi, options);

                yield put({
                    type: 'updateSettleInfo',
                    info: settleInfo,
                });

                // const { data: entryInfo } = yield call(getBillEntryInfoApi, options);

                // yield put({
                //     type: 'updateEntryInfo',
                //     info: entryInfo,
                // });
            } catch (error) {}
        },
        *getTempBillList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBillListApi, options);

                yield put({
                    type: 'updateBillManageProperty',
                    params: { tempBillList: list, tempBillTotal: total },
                });
            } catch (error) {}
        },
        *getTempBillDetailInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(detailInfoApi, options);

                yield put({
                    type: 'updateBillManageProperty',
                    params: { tempBillDetailInfo: data },
                });
            } catch (error) {}
        },
        *getTempBillForecastDate({ options }, { call, put, select }) {
            try {
                const { data } = yield call(queryForecastDateApi, options);

                yield put({
                    type: 'updateBillManageProperty',
                    params: { tempBillForecastDate: data },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateBillManageProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateBillManageList(state, { list, total }) {
            return {
                ...state,
                billManageList: list,
                billManageListTotal: total,
            };
        },
        resetSelectFilterInfo(state, { info }) {
            SessionStorage.removeItem('selectFilterInfo');
            return {
                ...state,
                selectFilterInfo: null,
            };
        },
        updateSelectFilterInfo(state, { info }) {
            SessionStorage.setItem('selectFilterInfo', info);
            return {
                ...state,
                selectFilterInfo: info,
            };
        },
        updateSettleInfo(state, { info }) {
            return {
                ...state,
                settleInfo: info,
            };
        },
        updateEntryInfo(state, { info }) {
            return {
                ...state,
                entryInfo: info,
            };
        },
    },
};
export default BillManageModel;
