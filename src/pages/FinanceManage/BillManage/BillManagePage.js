import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    Radio,
    DatePicker,
    Tabs,
    Popconfirm,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import styles from '@/assets/styles/common.less';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { confirmBillApi } from '@/services/FinanceManage/BillManageApi';
import { BILL_TYPES, BILL_STATUS } from '@/config/declare';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { MonthPicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, billType, onChangeBillType, listLoading } = props;

    const DataSelect = useMemo(() => {
        if (billType == BILL_TYPES.DAY) {
            return (
                <FormItem label="选择日期:" name="orderBillDate" {...formItemLayout}>
                    <DatePicker
                        // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                        format="YYYY-MM-DD"
                        onChange={onSubmit}
                    />
                </FormItem>
            );
        }
        if (billType == BILL_TYPES.MONTH) {
            return (
                <FormItem label="选择日期:" name="orderBillDate" {...formItemLayout}>
                    <MonthPicker
                        picker="month"
                        // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                        format="YYYY-MM"
                        onChange={onSubmit}
                    />
                </FormItem>
            );
        }
    }, [billType]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const changeBillTypeEvent = (e) => {
        const {
            target: { value },
        } = e;
        onChangeBillType(value);
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} hideOper>
                <Col>
                    <Radio.Group
                        value={billType}
                        onChange={changeBillTypeEvent}
                        buttonStyle="solid"
                    >
                        <Radio.Button value={BILL_TYPES.DAY}>日账单</Radio.Button>
                        <Radio.Button value={BILL_TYPES.MONTH}>月账单</Radio.Button>
                    </Radio.Group>
                </Col>
                <Col span={8}>{DataSelect}</Col>

                <Col span={8}>
                    <OperSelectTypeItem
                        rules={[{ message: '请选择运营商', required: true }]}
                        {...formItemLayout}
                        onChange={onSubmit}
                        form={form}
                    />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const BillManageListPage = (props) => {
    const {
        dispatch,
        history,
        billManageModel: { billManageList, billManageListTotal },
        global: { operatorList2 },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        billType: BILL_TYPES.DAY,
        billStatus: BILL_STATUS.ALL,
    });

    useEffect(
        () => () => {
            dispatch({
                type: 'billManageModel/updateBillManageList',
                list: [],
                total: 0,
            });
        },
        [],
    );

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            let dateType = 'YYYY-MM-DD';
            if (pageInfo.billType == BILL_TYPES.MONTH) {
                dateType = 'YYYY-MM';
            }
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,

                billType: pageInfo.billType,
            };
            if (pageInfo.billStatus != BILL_STATUS.ALL) {
                params.billStatus = pageInfo.billStatus;
            }
            if (data.orderBillDate) {
                params.orderBillDate = data.orderBillDate.format(dateType);
            }

            dispatch({
                type: 'billManageModel/getBillManageList',
                options: params,
            });
        } catch (error) {
            console.log(444, error);
        }
    };

    const confirmBillEvent = async (item) => {
        try {
            await confirmBillApi({
                operId: item.operId,
                orderBillDate: item.orderBillDate,
            });
            searchData();
        } catch (error) {}
    };

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const changeBillType = (type) => {
        changePageInfo({ billType: type, pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ billStatus: type, pageIndex: 1 });
    };

    const gotoDetailsEvent = (item) => {
        try {
            const params = {
                billType: pageInfo.billType,
                orderBillDate: item.orderBillDate,
                billDownLoadUrl: item.billDownLoadUrl,
                operProjectCode: item.operProjectCode,
            };

            for (const ele of operatorList2) {
                if (item.operId == ele.operId) {
                    params.operId = ele.operId;
                    params.operNickname = ele.operNickname;
                    break;
                }
            }

            dispatch({
                type: 'billManageModel/updateSelectFilterInfo',
                info: params,
            });
            if (pageInfo.billType == BILL_TYPES.MONTH) {
                history.push(`/financemanage/operator/bill/billSummary/month`);
            } else {
                history.push(`/financemanage/operator/bill/billSummary/day`);
            }
        } catch (error) {
            console.log(3333, error);
        }
    };

    const columns = [
        {
            title: '账单周期',
            width: 140,
            fixed: 'left',
            dataIndex: 'orderBillDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '已结算订单',
            width: 140,
            dataIndex: 'settleOrderNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            width: 140,
            dataIndex: 'orderAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商承担费用',
            width: 200,
            dataIndex: 'operBearAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途分润金额',
            width: 200,
            dataIndex: 'platformProfitAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手续费支出',
            width: 140,
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '待结算订单',
            width: 200,
            dataIndex: 'noSettleOrderNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '用户待结清金额',
            width: 160,
            dataIndex: 'noPayAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单状态',
            width: 140,
            dataIndex: 'billStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 200,
            render: (text, record) => (
                <Space>
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            gotoDetailsEvent(record);
                        }}
                    >
                        详情
                    </span>

                    <a href={record.billDownLoadUrl} target="_blank" rel="noopener noreferrer">
                        下载账单
                    </a>
                    {pageInfo.billType == BILL_TYPES.MONTH &&
                    record.billStatus == BILL_STATUS.UNCONFIRMED ? (
                        <Popconfirm
                            title="已与运营商确认该账单无误?"
                            onConfirm={() => {
                                confirmBillEvent(record);
                            }}
                            okText="确定"
                            cancelText="取消"
                        >
                            <span className={styles['table-btn']}>确认</span>
                        </Popconfirm>
                    ) : null}
                </Space>
            ),
        },
    ];

    return (
        <PageHeaderWrapper title="账单管理">
            <Card>
                <SearchLayout
                    form={form}
                    billType={pageInfo.billType}
                    onChangeBillType={changeBillType}
                    {...props}
                    onSubmit={resetData}
                    onReset={resetData}
                />

                <Tabs defaultActiveKey={BILL_STATUS.ALL} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={BILL_STATUS.ALL} />
                    <TabPane tab="未确认" key={BILL_STATUS.UNCONFIRMED} />
                    <TabPane tab="已确认" key={BILL_STATUS.CONFIRMED} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record}
                    dataSource={billManageList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: billManageListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.billType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ billManageModel, global, loading }) => ({
    billManageModel,
    global,
    listLoading: loading.effects['billManageModel/getBillManageList'],
}))(BillManageListPage);
