import { getOperPartnerListApi } from '@/services/FinanceManage/FinanceManageApi';

const SeparateAccountsModel = {
    namespace: 'separateAccountsModel',
    state: {
        separateAccountsList: [], // 合作伙伴分账列表
        separateAccountsListTotal: 0, // 合作伙伴分账列表总条数
    },
    effects: {
        /**
         * 合作伙伴分账列表
         */
        *getSeparateAccountsList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getOperPartnerListApi, options);

                yield put({
                    type: 'updateSeparateAccountsList',
                    list,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateSeparateAccountsList(state, { list, total }) {
            return {
                ...state,
                separateAccountsList: list,
                separateAccountsListTotal: total,
            };
        },
    },
};
export default SeparateAccountsModel;
