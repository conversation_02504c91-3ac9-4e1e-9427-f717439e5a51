import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMome, useCallback } from 'react';
import { getOperPartnerListPath } from '@/services/FinanceManage/FinanceManageApi';
import { exportTableByParams } from '@/utils/utils';
import { PARTNER_STATUS_TYPES } from '@/config/declare';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        financeModel: { tariffTypeList, channelList },
    } = props;

    useEffect(() => {
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const tariffTypeOptions = useCallback(
        () =>
            tariffTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [tariffTypeList],
    );

    const channelOptions = useCallback(
        () =>
            channelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [channelList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(7, 'days'), moment()],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem
                        label="创建日期:"
                        name="dates"
                        {...formItemLayout}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择结束日期');
                                    }
                                    if (value[0] && value[1]) {
                                        const startTime = +new Date(value[0]);
                                        const endTime = +new Date(value[1]);
                                        const dest = 60 * 1000 * 60 * 24 * 60;

                                        if (Math.abs(startTime - endTime) > dest) {
                                            return Promise.reject('选取范围最大不超过60天');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem {...formItemLayout} form={form} />
                </Col>
                <Col span={8}>
                    <FormItem label="订单号:" name="orderNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="订单渠道:" name="channel" {...formItemLayout}>
                        <Select placeholder="请选择">{channelOptions()}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="付款类型:" name="tariffType" {...formItemLayout}>
                        <Select placeholder="请选择">{tariffTypeOptions()}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="充电站:" name="stationName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const SeparateAccountsListPage = (props) => {
    const {
        dispatch,
        history,
        separateAccountsModel: { separateAccountsList, separateAccountsListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: PARTNER_STATUS_TYPES.ALL,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            operId: data.operId,
            orderNo: data.orderNo,
            channel: data.channel,
            tariffType: data.tariffType,
            stationName: data.stationName,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };
        if (pageInfo.tabType !== PARTNER_STATUS_TYPES.ALL) {
            params.settleStatus = pageInfo.tabType;
        }
        dispatch({
            type: 'separateAccountsModel/getSeparateAccountsList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            orderNo: data.orderNo,
            channel: data.channel,
            tariffType: data.tariffType,
            stationName: data.stationName,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };
        if (pageInfo.tabType !== PARTNER_STATUS_TYPES.ALL) {
            params.settleStatus = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getOperPartnerListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 240,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '合作伙伴',
            width: 240,
            dataIndex: 'partnerName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单创建时间',
            width: 240,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单号',
            width: 140,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            width: 140,
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            width: 200,
            dataIndex: 'elecAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 140,
            dataIndex: 'servAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            width: 140,
            dataIndex: 'chargeAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分账时间',
            width: 140,
            dataIndex: 'settleTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分账金额',
            width: 140,
            dataIndex: 'settleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分账状态',
            width: 140,
            dataIndex: 'settleStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 140,
            dataIndex: 'channelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '付款类型',
            width: 140,
            dataIndex: 'tariffTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={PARTNER_STATUS_TYPES.ALL} />
                    <TabPane tab="未分账" key={PARTNER_STATUS_TYPES.NOTSELLTE} />
                    <TabPane tab="已分账" key={PARTNER_STATUS_TYPES.SELLTED} />
                    <TabPane tab="线下分账" key={PARTNER_STATUS_TYPES.OFFLINE} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={separateAccountsList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: separateAccountsListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, separateAccountsModel, global, loading }) => ({
    financeModel,
    separateAccountsModel,
    global,
    listLoading: loading.effects['separateAccountsModel/getSeparateAccountsList'],
}))(SeparateAccountsListPage);
