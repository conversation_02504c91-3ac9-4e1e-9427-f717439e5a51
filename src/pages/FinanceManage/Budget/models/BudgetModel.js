import {
    getBudgetYear<PERSON>ist<PERSON><PERSON>,
    getBudgetYearDetailsApi,
    getBudgetMonthListApi,
    getProjectListApi,
} from '@/services/FinanceManage/BudgetApi';
import { copyObjectCommon } from '@/utils/utils';

const findItemDelete = (list, id) => {
    for (let index = 0; index < list.length; index++) {
        const element = list[index];
        if (element.projectId === id) {
            list[index] = null;
            list.splice(index, 1);
            break;
        }
        if (element?.children?.length) {
            findItemDelete(list[index].children, id);
        }
    }
};

const budgetModel = {
    namespace: 'budget',
    state: {
        budgetYearList: [], // 年度账单列表
        budgetYearListTotal: 0,
        editYearInfo: null, //详情

        budgetMonthList: [], // 年度账单列表
        projectTree: [], //项目树
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getBudgetYearList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBudgetYearListApi, options);

                yield put({
                    type: 'updateBudgetYearListList',
                    budgetYearList: list,
                    total,
                });
                return list;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 企业信息列表
         */
        *getBudgetYearInfo({ budgetId }, { call, put, select }) {
            try {
                const {
                    data: { data },
                } = yield call(getBudgetYearDetailsApi, budgetId);

                yield put({
                    type: 'updateBudgetYearInfo',
                    info: data,
                });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 企业信息列表
         */
        *getBudgetMonthList({ budgetId }, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getBudgetMonthListApi, budgetId);

                yield put({
                    type: 'updateBudgetMonthListList',
                    budgetMonthList: list,
                });
                return list;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *initProjectTree({ options }, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getProjectListApi);

                yield put({
                    type: 'updateProjectTree',
                    list: list.map((ele) => {
                        return { ...ele, children: [] };
                    }),
                });
                return list;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateBudgetYearListList(state, { budgetYearList, total }) {
            return {
                ...state,
                budgetYearList,
                budgetYearListTotal: total,
            };
        },
        updateBudgetYearInfo(state, { info }) {
            return {
                ...state,
                editYearInfo: info,
            };
        },
        updateBudgetMonthListList(state, { budgetMonthList }) {
            return {
                ...state,
                budgetMonthList,
            };
        },
        delectProjectTreeById(state, { projectId }) {
            let projectTree = state.projectTree;

            findItemDelete(projectTree, projectId);

            return {
                ...state,
                projectTree: copyObjectCommon(projectTree),
            };
        },
        updateProjectTree(state, { list, parentId }) {
            let projectTree = state.projectTree;
            if (parentId) {
                const findIndex = projectTree.findIndex((ele) => ele.projectId === parentId);
                if (findIndex >= 0) {
                    const childList = projectTree[findIndex].children;

                    for (const item of list) {
                        const childIndex = childList.findIndex(
                            (ele) => ele.projectId === item.projectId,
                        );
                        if (childIndex >= 0) {
                            childList[childIndex] = { ...childList[childIndex], ...item };
                        } else {
                            childList.push({ ...item });
                        }
                    }
                }
            } else {
                for (const item of list) {
                    const findIndex = projectTree.findIndex(
                        (ele) => ele.projectId === item.projectId,
                    );
                    if (findIndex >= 0) {
                        projectTree[findIndex] = { ...projectTree[findIndex], ...item };
                    } else {
                        projectTree.push({ ...item, children: [] });
                    }
                }
            }
            return {
                ...state,
                projectTree: copyObjectCommon(projectTree),
            };
        },
    },
};
export default budgetModel;
