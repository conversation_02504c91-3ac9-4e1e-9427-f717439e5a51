import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    Cascader,
    Popconfirm,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import {
    getBudgetYearListPath,
    updateBudgetYearApi,
    addBudgetYearApi,
    deleteBudgetYearApi,
} from '@/services/FinanceManage/BudgetApi';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import NumericInput from '@/components/NumericInput/index';
import { getCityListApi } from '@/services/CommonApi';
import { throttle } from 'lodash';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const STATUS_TYPES = {
    ALL: '00',
    DOING: '01',
    SIGN: '02',
    END: '03',
    DEL: '04',
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const [cityTreeData, changeCityTreeData] = useState([]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem label="预算名称:" name="budgetName" {...formItemLayout}>
                        <Input placeholder="请填写" maxLength={11} autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="预算编号:" name="budgetId" {...formItemLayout}>
                        <Input placeholder="请填写" maxLength={11} autoComplete="off" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const YearListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo },
        budget: { budgetYearList, budgetYearListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            budgetName: data.budgetName,
            budgetId: data.budgetId,
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'budget/getBudgetYearList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // //导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            budgetName: data.budgetName,
            budgetId: data.budgetId,
        };

        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getBudgetYearListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const addYearEvent = () => {
        history.push('/financemanage/budget/year/add');
    };

    const lookMonthBudgetEvent = (item) => {
        history.push(`/financemanage/budget/year/month/${item.budgetId}`);
    };

    const editYearEvent = (item) => {
        history.push(`/financemanage/budget/year/update/${item.budgetId}`);
    };
    const deleteYearEvent = throttle(async (item) => {
        try {
            await deleteBudgetYearApi(item.budgetId);
            message.success('删除成功');
            searchData();
        } catch (error) {
            return Promise.reject(error);
        }
    }, 500);

    const columns = [
        {
            title: '预算名称',
            width: 200,
            dataIndex: 'budgetName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '预算编号',
            width: 140,
            dataIndex: 'budgetId',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '部门',
            width: 200,
            dataIndex: 'departmentName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '项目',
            width: 200,
            dataIndex: 'projectName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '年度计划配置金额',
            width: 160,
            dataIndex: 'configurePrice',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '年度预计核销金额',
            width: 160,
            dataIndex: 'predictUsePrice',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '160px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '预算年',
            width: 100,
            dataIndex: 'budgetYear',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '100px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '生效时间',
            width: 360,
            dataIndex: 'timeRange',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '360px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '负责人',
            width: 140,
            dataIndex: 'principalName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '负责运营',
            width: 140,
            dataIndex: 'operationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '创建时间',
            width: 140,
            dataIndex: 'creTime',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text || ''}
                    </div>
                );
            },
        },

        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Space>
                        <span
                            className={styles['table-btn']}
                            onClick={() => lookMonthBudgetEvent(record)}
                        >
                            月度预算
                        </span>
                        <span className={styles['table-btn']} onClick={() => editYearEvent(record)}>
                            编辑
                        </span>
                        <Popconfirm
                            title="确认删除？"
                            okText="删除"
                            onConfirm={() => deleteYearEvent(record)}
                        >
                            <span className={styles['table-btn']}>删除</span>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper title="企业申请">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    // onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button className={styles['btn-item']} type="primary" onClick={addYearEvent}>
                        新增预算
                    </Button>
                </div>
                <br></br>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.budgetId}
                    dataSource={budgetYearList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: budgetYearListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, budget, loading }) => ({
    global,
    budget,
    listLoading: loading.effects['budget/getBudgetYearList'],
}))(YearListPage);
