import { useEffect, useMemo, useState } from 'react';
import { connect } from 'umi';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Card,
    Form,
    Button,
    Input,
    Space,
    DatePicker,
    InputNumber,
    Select,
    Row,
    Col,
    message,
    Modal,
    Table,
    Popconfirm,
} from 'antd';
import styles from '@/assets/styles/common.less';
import InputItem from '@/components/InputItem/index';
import TablePro from '@/components/TablePro';

import { InfoCircleOutlined, LeftOutlined, DownOutlined } from '@ant-design/icons';
import moment from 'moment';
import {
    getDepartmentListApi,
    getAccountListApi,
    getProjectListApi,
    addBudgetYearApi,
    updateBudgetYearApi,
    addProjectApi,
    deleteProjectApi,
    updateProjectApi,
} from '@/services/FinanceManage/BudgetApi';

const { RangePicker, YearPicker } = DatePicker;
const FormItem = Form.Item;

const { TextArea } = Input;

const formItemLayout = {
    labelCol: {
        lg: {
            span: 8,
        },
    },
    wrapperCol: {
        md: {
            span: 16,
        },
        lg: {
            span: 8,
        },
    },
};
const EditProjectModal = (props) => {
    const { visible, firstProjectList, editInfo, updataProjectEvent, closeEvent } = props;
    const [form] = Form.useForm();

    useEffect(() => {
        if (editInfo) {
            let params = {
                projectName: editInfo.projectName,
                parentId: editInfo.parentId,
            };
            if (params.parentId === 0) {
                delete params.parentId;
            }
            form.setFieldsValue(params);
        }
    }, []);

    const firstProjectOptions = useMemo(() => {
        return firstProjectList.map((ele) => {
            return (
                <Select.Option
                    // disabled={ele.deleted === '1'}
                    style={{ display: ele.deleted === '1' ? 'none' : 'block' }}
                    value={ele.projectId}
                    key={ele.projectId}
                >
                    {ele.projectName}
                </Select.Option>
            );
        });
    }, [firstProjectList]);

    const onFinish = async (values) => {
        try {
            await form.validateFields();
            let params = {
                projectName: values.projectName,
                parentId: values?.parentId || '',
            };
            if (editInfo) {
                params.projectId = editInfo.projectId;
                delete params.parentId;
                await updateProjectApi(params);
                updataProjectEvent(editInfo.parentId);
            } else {
                if (params.parentId) {
                    params.projectLevel = '2';
                } else {
                    params.projectLevel = '1';
                }
                await addProjectApi(params);
                updataProjectEvent(params.parentId);
            }

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    return (
        <Modal
            title="新增项目"
            visible={visible}
            width={500}
            destroyOnClose
            footer={false}
            onCancel={closeEvent}
        >
            <Form form={form} onFinish={onFinish} scrollToFirstError>
                <FormItem
                    label="项目名称"
                    name="projectName"
                    rules={[{ required: true, message: '请填写项目名称' }]}
                >
                    <Input placeholder="请填写项目名称" autoComplete="off"></Input>
                </FormItem>
                {!editInfo ? (
                    <FormItem label="上级项目" name="parentId">
                        <Select
                            showSearch
                            optionFilterProp="projectName"
                            filterOption={(input, option) => option?.children?.indexOf(input) >= 0}
                            placeholder="请选择项目大项"
                            allowClear
                            disabled={editInfo}
                        >
                            {firstProjectOptions}
                        </Select>
                    </FormItem>
                ) : null}

                <div className={styles['form-submit']}>
                    <Button className={styles['form-btn']} type="primary" htmlType="submit">
                        提交
                    </Button>
                    <Button className={styles['form-btn']} onClick={closeEvent}>
                        取消
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};

const ProjectManageModal = (props) => {
    const {
        dispatch,
        visible,
        closeEvent,
        budget: { projectTree },
    } = props;

    const [showEditProject, changeShowEditProject] = useState(false);
    const [editInfo, changeEditInfo] = useState(null);

    const initProjectList = async (parentId) => {
        try {
            const {
                data: { list },
            } = await getProjectListApi(parentId);
            dispatch({ type: 'budget/updateProjectTree', list, parentId });

            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const formatDeleteTree = useMemo(() => {
        if (!projectTree?.length) {
            return [];
        }
        let filterFirst = projectTree?.filter((ele) => ele.deleted !== '1');
        let list = filterFirst.map((ele) => {
            return {
                ...ele,
                children: ele?.children?.filter((child) => child.deleted !== '1'),
            };
        });
        // console.log(3333, projectTree, list);
        return list;
    }, [projectTree]);

    const columns = [
        {
            title: '一级项目',
            dataIndex: 'projectName',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '操作',
            width: 140,
            render: (text, record) => {
                return (
                    <Space>
                        <span
                            className={styles['table-btn']}
                            onClick={() => editProjectEvent(record)}
                        >
                            编辑
                        </span>
                        <Popconfirm
                            title="确认删除？"
                            okText="删除"
                            onConfirm={() => delProjectEvent(record)}
                        >
                            <span className={styles['table-btn']}>删除</span>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];

    const childColumns = [
        {
            title: '二级项目',
            dataIndex: 'projectName',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '操作',
            width: 140,
            render: (text, record) => {
                return (
                    <Space>
                        <span
                            className={styles['table-btn']}
                            onClick={() => editProjectEvent(record)}
                        >
                            编辑
                        </span>
                        <Popconfirm
                            title="确认删除？"
                            okText="删除"
                            onConfirm={() => delProjectEvent(record)}
                        >
                            <span className={styles['table-btn']}>删除</span>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];

    const updateTableEvent = (parentId) => {
        initProjectList(parentId);
        closeShowEditProject();
    };

    const closeShowEditProject = () => {
        changeShowEditProject(false);

        changeEditInfo(null);
    };

    const editProjectEvent = (item) => {
        changeEditInfo(item);
        changeShowEditProject(true);
    };
    const delProjectEvent = async (item) => {
        try {
            if (item?.children && item.children.length > 0) {
                message.error('有子项目的不能删除');
                return;
            }
            await deleteProjectApi({ projectId: item.projectId });
            dispatch({ type: 'budget/delectProjectTreeById', projectId: item.projectId });
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const expandedRowRender = (record) => {
        return (
            <Table columns={childColumns} dataSource={record.children || []} pagination={false} />
        );
    };

    return (
        <Modal
            title="项目管理"
            visible={visible}
            width={1200}
            footer={false}
            onCancel={closeEvent}
            destroyOnClose
        >
            <div className={styles['btn-bar']}>
                <Button
                    className={styles['btn-item']}
                    type="primary"
                    onClick={() => {
                        changeShowEditProject(true);
                    }}
                >
                    新增项目
                </Button>
            </div>
            <br></br>
            <div className="overscroll" style={{ maxHeight: '450px' }}>
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.projectId}
                    dataSource={formatDeleteTree}
                    columns={columns}
                    pagination={false}
                    noSort
                    offsetHeader={0}
                    expandable={{
                        childrenColumnName: 'childList',
                        expandedRowRender: expandedRowRender,
                        rowExpandable: (record) => record.projectLevel === '1',
                        onExpand: (expanded, record) => {
                            if (expanded) {
                                initProjectList(record.projectId);
                            }
                        },
                    }}
                />

                {showEditProject ? (
                    <EditProjectModal
                        visible={showEditProject}
                        firstProjectList={projectTree}
                        updataProjectEvent={(parentId) => {
                            initProjectList(parentId);
                            closeShowEditProject();
                        }}
                        editInfo={editInfo}
                        closeEvent={() => {
                            closeShowEditProject();
                        }}
                    ></EditProjectModal>
                ) : null}
            </div>
        </Modal>
    );
};
const UpdateYearPage = (props) => {
    const {
        dispatch,
        history,
        route,
        match,
        infoLoading,
        budget: { editYearInfo, projectTree },
    } = props;

    const { budgetId } = match.params;

    const [form] = Form.useForm();

    const [submitLoading, updateSubmitLoading] = useState(false);
    const [firstDepartmentList, changedFirstDepartmentList] = useState([]);
    const [secondDepartmentList, changedSecondDepartmentList] = useState([]);

    const [accountList, changedAccountList] = useState([]);

    const [showManageProject, changeShowManageProject] = useState(false);

    const firstDepartmentOptions = useMemo(() => {
        return firstDepartmentList.map((ele) => {
            return (
                <Select.Option value={ele.departmentId} key={ele.departmentId}>
                    {ele.departmentName}
                </Select.Option>
            );
        });
    }, [firstDepartmentList]);
    const secondDepartmentOptions = useMemo(() => {
        return secondDepartmentList.map((ele) => {
            return (
                <Select.Option value={ele.departmentId} key={ele.departmentId}>
                    {ele.departmentName}
                </Select.Option>
            );
        });
    }, [secondDepartmentList]);

    const firstProjectOptions = useMemo(() => {
        return projectTree.map((ele) => {
            return (
                <Select.Option
                    style={{ display: ele.deleted === '1' ? 'none' : 'block' }}
                    // disabled={ele.deleted === '1'}
                    value={ele.projectId}
                    key={ele.projectId}
                >
                    {ele.projectName}
                </Select.Option>
            );
        });
    }, [projectTree]);

    const accountListOptions = useMemo(() => {
        return accountList.map((ele) => {
            return (
                <Select.Option value={ele.accountId} key={ele.accountId}>
                    {ele.accountName}
                </Select.Option>
            );
        });
    }, [accountList]);

    useEffect(() => {
        if (budgetId) {
            dispatch({
                type: 'budget/getBudgetYearInfo',
                budgetId: budgetId,
            });
        }
        dispatch({ type: 'budget/initProjectTree' });

        initDepartmentList();
        initAccountList();
        return () => {
            dispatch({
                type: 'budget/updateBudgetYearInfo',
                info: null,
            });
        };
    }, []);

    useEffect(() => {
        if (editYearInfo) {
            let params = {
                budgetName: editYearInfo.budgetName,
                budgetYear: moment().year(editYearInfo?.budgetYear),
                configurePrice: editYearInfo.configurePrice,

                dates: [moment(editYearInfo?.effTime), moment(editYearInfo?.expTime)],
                departmentId: editYearInfo.departmentId,
                operation: editYearInfo?.operation.split(',').map((ele) => Number(ele)) || [],
                predictUsePrice: editYearInfo.predictUsePrice,
                principal: editYearInfo?.principal.split(',').map((ele) => Number(ele)) || [],
                projectCategory: editYearInfo.projectCategory,
                remark: editYearInfo.remark,
                secondCategory: editYearInfo.secondCategory,
                secondDepartmentId: editYearInfo.secondDepartmentId,
            };
            if (editYearInfo?.departmentId) {
                initDepartmentList(editYearInfo.departmentId);
            }
            if (editYearInfo?.projectCategory) {
                initProjectList(editYearInfo.projectCategory);
            }
            form.setFieldsValue(params);
        }
    }, [editYearInfo]);

    const initDepartmentList = async (parentId) => {
        try {
            const {
                data: { list },
            } = await getDepartmentListApi(parentId);
            if (!parentId) {
                changedFirstDepartmentList(list);
            } else {
                changedSecondDepartmentList(list);
            }
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const initProjectList = async (parentId) => {
        try {
            const {
                data: { list },
            } = await getProjectListApi(parentId);
            dispatch({ type: 'budget/updateProjectTree', list, parentId });

            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const initAccountList = async () => {
        try {
            const {
                data: { list },
            } = await getAccountListApi();
            changedAccountList(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onFinish = async (values) => {
        try {
            updateSubmitLoading(true);
            await form.validateFields();
            let params = {
                budgetName: values.budgetName,
                budgetYear: values.budgetYear.format('YYYY'),
                configurePrice: values.configurePrice,
                effTime:
                    (values.dates &&
                        values.dates[0] &&
                        values.dates[0].format('YYYY-MM-DD HH:mm:ss')) ||
                    '',
                expTime:
                    (values.dates &&
                        values.dates[1] &&
                        values.dates[1].format('YYYY-MM-DD HH:mm:ss')) ||
                    '',
                departmentId: values.departmentId,
                operation: (values.operation?.length && values.operation.join(',')) || '',
                predictUsePrice: values.predictUsePrice,
                principal: (values.principal?.length && values.principal.join(',')) || '',
                projectCategory: values.projectCategory,
                remark: values.remark,
                secondCategory: values.secondCategory,
                secondDepartmentId: values.secondDepartmentId,
            };

            if (budgetId) {
                params.budgetId = budgetId;
                await updateBudgetYearApi(params);
                message.success('保存成功');
            } else {
                await addBudgetYearApi(params);
                message.success('新增成功');
            }
            goBack();

            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateSubmitLoading(false);
        }
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form form={form} {...formItemLayout} onFinish={onFinish} scrollToFirstError>
                    <FormItem
                        label="预算名称:"
                        name="budgetName"
                        rules={[{ required: true, message: '请填写预算名称' }]}
                    >
                        <Input
                            showCount
                            placeholder="预算名称限制24个字"
                            maxLength={24}
                            autoComplete="off"
                        />
                    </FormItem>
                    <FormItem label="归属部门:" required>
                        <Row>
                            <Col span={12}>
                                <FormItem
                                    noStyle
                                    name="departmentId"
                                    rules={[{ required: true, message: '请选择一级部门' }]}
                                >
                                    <Select
                                        placeholder="请选择一级部门"
                                        showSearch
                                        allowClear
                                        optionFilterProp="departmentName"
                                        filterOption={(input, option) =>
                                            option?.children?.indexOf(input) >= 0
                                        }
                                        onChange={(value) => {
                                            form.setFieldsValue({
                                                secondDepartmentId: '',
                                            });
                                            initDepartmentList(value);
                                        }}
                                    >
                                        {firstDepartmentOptions}
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={12}>
                                <FormItem noStyle name="secondDepartmentId">
                                    <Select
                                        showSearch
                                        allowClear
                                        optionFilterProp="departmentName"
                                        filterOption={(input, option) =>
                                            option?.children?.indexOf(input) >= 0
                                        }
                                        placeholder="请选择二级部门"
                                    >
                                        {secondDepartmentOptions}
                                    </Select>
                                </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                    <FormItem label="项目:" required>
                        <Row>
                            <Col span={12}>
                                <FormItem
                                    noStyle
                                    name="projectCategory"
                                    rules={[{ required: true, message: '请选择大项项目' }]}
                                >
                                    <Select
                                        showSearch
                                        allowClear
                                        optionFilterProp="projectName"
                                        filterOption={(input, option) =>
                                            option?.children?.indexOf(input) >= 0
                                        }
                                        placeholder="请选择项目大项"
                                        onChange={(value) => {
                                            form.setFieldsValue({
                                                secondCategory: '',
                                            });
                                            initProjectList(value);
                                        }}
                                    >
                                        {firstProjectOptions}
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={12}>
                                <FormItem
                                    shouldUpdate={(prevValues, curValues) =>
                                        prevValues.projectCategory !== curValues.projectCategory
                                    }
                                    noStyle
                                >
                                    {({ getFieldValue }) => {
                                        const projectCategory = getFieldValue('projectCategory');

                                        const secondProjectList = projectTree.find(
                                            (ele) => ele.projectId === projectCategory,
                                        );

                                        const secondProjectOptions =
                                            secondProjectList?.children?.map((ele) => {
                                                return (
                                                    <Select.Option
                                                        // disabled={ele.deleted === '1'}
                                                        style={{
                                                            display:
                                                                ele.deleted === '1'
                                                                    ? 'none'
                                                                    : 'block',
                                                        }}
                                                        value={ele.projectId}
                                                        key={ele.projectId}
                                                    >
                                                        {ele.projectName}
                                                    </Select.Option>
                                                );
                                            }) || [];
                                        return (
                                            <FormItem noStyle name="secondCategory">
                                                <Select
                                                    showSearch
                                                    allowClear
                                                    optionFilterProp="projectName"
                                                    filterOption={(input, option) =>
                                                        option?.children?.indexOf(input) >= 0
                                                    }
                                                    placeholder="请选择项目小项"
                                                >
                                                    {secondProjectOptions}
                                                </Select>
                                            </FormItem>
                                        );
                                    }}
                                </FormItem>
                            </Col>
                        </Row>
                        <br></br>
                        <Row>
                            <Col>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        changeShowManageProject(true);
                                    }}
                                >
                                    管理
                                </Button>
                            </Col>
                        </Row>
                    </FormItem>
                    <FormItem
                        label="年度计划配置金额:"
                        name="configurePrice"
                        rules={[{ required: true, message: '请填写计划配置金额' }]}
                    >
                        <InputNumber
                            placeholder="请填写"
                            style={{ width: '100%' }}
                            precision={2}
                            step={0.01}
                            min={0}
                        />
                    </FormItem>
                    <FormItem
                        label="年度预计核销金额:"
                        name="predictUsePrice"
                        rules={[{ required: true, message: '请填写预计核销金额' }]}
                    >
                        <InputNumber
                            placeholder="请填写"
                            style={{ width: '100%' }}
                            precision={2}
                            step={0.01}
                            min={0}
                        />
                    </FormItem>
                    <FormItem
                        label="预算年:"
                        name="budgetYear"
                        initialValue={moment()}
                        rules={[{ required: true, message: '请选择预算年' }]}
                    >
                        <YearPicker
                            disabledDate={(current) => {
                                return current && current < moment().startOf('year');
                            }}
                            format="YYYY"
                            placeholder="请选择年份"
                            style={{ width: '100%' }}
                            onChange={(value) => {
                                form.setFieldsValue({
                                    dates: [
                                        moment(value).startOf('year'),
                                        moment(value).endOf('year'),
                                    ],
                                });
                            }}
                        />
                    </FormItem>
                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.budgetYear !== curValues.budgetYear
                        }
                    >
                        {({ getFieldValue }) => {
                            const budgetYear = getFieldValue('budgetYear');
                            const disabledDate = (current) => {
                                // Can not select days before today and today
                                return current && current < moment(budgetYear).startOf('year');
                            };

                            return (
                                <FormItem
                                    label="生效时间:"
                                    name="dates"
                                    initialValue={[
                                        moment().startOf('year'),
                                        moment().endOf('year'),
                                    ]}
                                    rules={[
                                        { required: true, message: '请填写生效时间' },
                                        // ({ getFieldValue }) => ({
                                        //     validator(rule, value) {
                                        //         if (!value) {
                                        //             return Promise.reject('');
                                        //         }

                                        //         if (value) {
                                        //             const nowTime = +new Date();
                                        //             const sendEndTime = +new Date(value);

                                        //             if (sendEndTime < nowTime) {
                                        //                 return Promise.reject(
                                        //                     '生效时间不能早于当前时间',
                                        //                 );
                                        //             }
                                        //         }

                                        //         return Promise.resolve();
                                        //     },
                                        // }),
                                    ]}
                                >
                                    <RangePicker
                                        disabledDate={disabledDate}
                                        showTime={{
                                            format: 'HH:mm:ss',
                                            defaultValue: moment('00:00:00', 'HH:mm:ss'),
                                        }}
                                        format="YYYY-MM-DD HH:mm:ss"
                                        showNow={false}
                                    />
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <FormItem
                        label="负责人:"
                        name="principal"
                        rules={[{ required: true, message: '请选择负责人' }]}
                    >
                        <Select
                            showSearch
                            mode="multiple"
                            allowClear
                            optionFilterProp="accountName"
                            filterOption={(input, option, ...options) => {
                                return option?.children?.indexOf(input) >= 0;
                            }}
                            placeholder="请选择"
                        >
                            {accountListOptions}
                        </Select>
                    </FormItem>
                    <FormItem
                        label="负责运营:"
                        name="operation"
                        rules={[{ required: true, message: '请选择负责运营' }]}
                    >
                        <Select
                            mode="multiple"
                            allowClear
                            showSearch
                            optionFilterProp="accountName"
                            filterOption={(input, option) => option?.children?.indexOf(input) >= 0}
                            placeholder="请选择"
                        >
                            {accountListOptions}
                        </Select>
                    </FormItem>
                    <FormItem label="备注:" name="remark">
                        <TextArea rows={5} showCount maxLength={100} placeholder="请填写" />
                    </FormItem>

                    <div className={styles['form-submit']}>
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            提交
                        </Button>
                        <Button className={styles['form-btn']} onClick={goBack}>
                            取消
                        </Button>
                    </div>
                </Form>
                <ProjectManageModal
                    {...props}
                    visible={showManageProject}
                    closeEvent={() => {
                        changeShowManageProject(false);
                    }}
                ></ProjectManageModal>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, budget, loading }) => ({
    global,
    budget,
    infoLoading: loading.effects['budget/getBudgetYearInfo'],
}))(UpdateYearPage);
