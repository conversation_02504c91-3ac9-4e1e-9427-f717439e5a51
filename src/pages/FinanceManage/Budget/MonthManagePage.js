import { connect } from 'umi';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Form, Button, Input, Space, Descriptions, Modal, InputNumber } from 'antd';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';

import { InfoCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { useEffect, useState, useMemo } from 'react';
import { updateBudgetMonthApi } from '@/services/FinanceManage/BudgetApi';

const { TextArea } = Input;

const FormItem = Form.Item;

const formItemLayout = {};
const UpdateYearPage = (props) => {
    const {
        dispatch,
        history,
        match,
        infoLoading,
        route,
        budget: { editYearInfo, budgetMonthList },
    } = props;

    const { budgetId } = match.params;

    const [form] = Form.useForm();

    const [editVisible, updateEditVisible] = useState(false);
    const [curMonthIndex, updateCurMonthIndex] = useState(null);
    const [submitLoading, updateSubmitLoading] = useState(false);

    const curMonthInfo = useMemo(() => {
        let info = null;
        if (curMonthIndex >= 0) {
            info = budgetMonthList[curMonthIndex];

            form.setFieldsValue(info);
        }
        return info;
    }, [curMonthIndex]);
    const curEditMonth = useMemo(() => {
        if (curMonthInfo) {
            return `${curMonthInfo.budgetMonth}月`;
        }
        return '';
    }, [budgetMonthList, curMonthInfo, curMonthIndex]);

    const allConfigurePrice = useMemo(() => {
        let max = 0;
        budgetMonthList.forEach((element, index) => {
            max += element.configurePrice >= 0 ? Number(element.configurePrice) : 0;
        });
        return Math.round(max * 100) / 100;
    }, [budgetMonthList]);

    const allPredictUsePrice = useMemo(() => {
        let max = 0;
        budgetMonthList.forEach((element, index) => {
            max += element.predictUsePrice >= 0 ? Number(element.predictUsePrice) : 0;
        });
        return Math.round(max * 100) / 100;
    }, [budgetMonthList]);

    const curMaxMonthConfigurePrice = useMemo(() => {
        let max = 0;
        if (editYearInfo) {
            max = editYearInfo.configurePrice;
        }
        let configureAmt = 0;

        budgetMonthList.forEach((element, index) => {
            if (index !== curMonthIndex) {
                configureAmt += element.configurePrice >= 0 ? Number(element.configurePrice) : 0;
            }
        });

        max -= configureAmt;

        if (max < 0) {
            max = 0;
        }
        return Math.round(max * 100) / 100;
    }, [budgetMonthList, editYearInfo, curMonthIndex]);

    const curMaxMonthPredictUsePrice = useMemo(() => {
        let max = 0;
        if (editYearInfo) {
            max += editYearInfo.predictUsePrice;
        }

        let predictUseAmt = 0;

        budgetMonthList.forEach((element, index) => {
            if (index !== curMonthIndex) {
                predictUseAmt += element.predictUsePrice >= 0 ? Number(element.predictUsePrice) : 0;
            }
        });

        max -= predictUseAmt;

        if (max < 0) {
            max = 0;
        }
        return Math.round(max * 100) / 100;
    }, [budgetMonthList, editYearInfo, curMonthIndex]);

    useEffect(() => {
        if (budgetId) {
            dispatch({
                type: 'budget/getBudgetYearInfo',
                budgetId: budgetId,
            });
        }
        return () => {
            dispatch({
                type: 'budget/updateBudgetYearInfo',
                info: null,
            });
        };
    }, []);

    useEffect(() => {
        if (editYearInfo) {
            if (editYearInfo.budgetId) {
                dispatch({ type: 'budget/getBudgetMonthList', budgetId: budgetId });
            }
        } else {
            dispatch({ type: 'budget/updateBudgetMonthListList', budgetMonthList: [] });
        }
    }, [editYearInfo]);

    const goBack = () => {
        history.go(-1);
    };

    const columns = [
        {
            title: '月份',
            dataIndex: 'budgetMonth',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '月度计划配置金额',
            dataIndex: 'configurePrice',
            render(text, record) {
                return <span title={text}>{text || '0'}</span>;
            },
        },
        {
            title: '月度预计核销金额',
            dataIndex: 'predictUsePrice',
            render(text, record) {
                return <span title={text}>{text || '0'}</span>;
            },
        },
        {
            title: '备注',
            dataIndex: 'remark',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            render: (text, record, index) => {
                return (
                    <span className={styles['table-btn']} onClick={() => editMonthEvent(index)}>
                        编辑
                    </span>
                );
            },
        },
    ];

    const editMonthEvent = (index) => {
        updateCurMonthIndex(index);
        openEditView();
    };

    const openEditView = () => {
        updateEditVisible(true);
    };

    const closeEditView = () => {
        updateEditVisible(false);
        updateCurMonthIndex(-1);
        form.resetFields();
    };

    const onMonthFinish = async (values) => {
        try {
            updateSubmitLoading(true);
            let params = {
                ...values,
                budgetMonthId: curMonthInfo.budgetMonthId,
                budgetId: curMonthInfo.budgetId,
            };
            await updateBudgetMonthApi(params);
            if (editYearInfo.budgetId) {
                dispatch({ type: 'budget/getBudgetMonthList', budgetId: budgetId });
            }
            dispatch({
                type: 'budget/getBudgetYearInfo',
                budgetId: budgetId,
            });
            closeEditView();
            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateSubmitLoading(false);
        }
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={infoLoading}>
                <Descriptions>
                    <Descriptions.Item label="预算名称">
                        {(editYearInfo && editYearInfo.budgetName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="预算编号">
                        {(editYearInfo && editYearInfo.budgetId) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="项目">
                        {(editYearInfo && editYearInfo.projectName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="年度计划配置金额">
                        {(editYearInfo && editYearInfo.configurePrice) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="年度预计核销金额">
                        {(editYearInfo && editYearInfo.predictUsePrice) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="预算年">
                        {(editYearInfo && editYearInfo.budgetYear) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="月度计划配置总和">
                        {allConfigurePrice}
                    </Descriptions.Item>
                    <Descriptions.Item label="月度预计核销总和">
                        {allPredictUsePrice}
                    </Descriptions.Item>

                    <Descriptions.Item label="部门">
                        {(editYearInfo && editYearInfo.departmentName) || ''}
                    </Descriptions.Item>
                </Descriptions>
                <br></br>
                <TablePro
                    loading={infoLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.budgetMonthId}
                    dataSource={budgetMonthList}
                    columns={columns}
                    pagination={false}
                />
            </Card>
            <Modal
                title={`编辑${curEditMonth}预算`}
                visible={editVisible}
                footer={false}
                onCancel={closeEditView}
                maskClosable={false}
                destroyOnClose
            >
                <Form form={form} onFinish={onMonthFinish} scrollToFirstError>
                    <FormItem
                        label="月度计划配置金额"
                        name="configurePrice"
                        rules={[
                            { required: true, message: '请填写月度计划配置金额' },
                            () => ({
                                validator(rule, value) {
                                    if (value) {
                                        if (Number(value) > Number(editYearInfo.configurePrice)) {
                                            return Promise.reject(`月度计划总和不能大于年度计划`);
                                        }
                                        if (Number(value) > curMaxMonthConfigurePrice) {
                                            return Promise.reject(
                                                `月度计划不能大于已分配的月度计划配置预算`,
                                            );
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            placeholder="请填写"
                            style={{ width: '100%' }}
                            precision={2}
                            step={0.01}
                            min={0}
                        />
                    </FormItem>
                    <FormItem
                        label="月度预计核销金额"
                        name="predictUsePrice"
                        rules={[
                            { required: true, message: '请填写月度预计核销金额' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (value) {
                                        const configurePrice = getFieldValue('configurePrice');
                                        if (Number(value) > Number(configurePrice)) {
                                            return Promise.reject(`月度核销不能大于计划配置预算`);
                                        }
                                        if (Number(value) > curMaxMonthPredictUsePrice) {
                                            return Promise.reject(
                                                `月度核销不能大于已分配的计划核销预算`,
                                            );
                                        }
                                        if (Number(value) > Number(editYearInfo.predictUsePrice)) {
                                            return Promise.reject(`月度核销总和不能大于年度核销`);
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            placeholder="请填写"
                            style={{ width: '100%' }}
                            precision={2}
                            step={0.01}
                            min={0}
                        />
                    </FormItem>
                    <FormItem label="备注" name="remark">
                        <TextArea rows={5} showCount maxLength={100} placeholder="请填写" />
                    </FormItem>
                    <div className={styles['form-submit']}>
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            提交
                        </Button>
                        <Button className={styles['form-btn']} onClick={closeEditView}>
                            取消
                        </Button>
                    </div>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, budget, loading }) => ({
    global,
    budget,
    infoLoading: loading.effects['budget/getBudgetYearInfo'],
}))(UpdateYearPage);
