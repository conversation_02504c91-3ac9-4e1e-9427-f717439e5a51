import { getSettlementListApi } from '@/services/FinanceManage/FinanceManageApi';

const settlementModel = {
    namespace: 'settlementModel',
    state: {
        settlementList: [], // 结算列表
        settlementTotal: 0, // 结算条数
    },
    effects: {
        /**
         *
         */
        *getSettlementList({ params, callback }, { call, put, select }) {
            try {
                const {
                    data: { records: settlementList, total: settlementTotal },
                } = yield call(getSettlementListApi, params);

                if (callback) {
                    callback(settlementList);
                } else {
                    yield put({
                        type: 'updateSettlementProperty',
                        params: { settlementList, settlementTotal },
                    });
                }
            } catch (error) {}
        },
    },
    reducers: {
        updateSettlementProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default settlementModel;
