import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Input, message } from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { exportTableByParams } from '@/utils/utils';
import { LocalStorage } from '@/utils/LocalStorage';
import { exportSettlementListApi } from '@/services/FinanceManage/FinanceManageApi';
import CacheAreaView from '@/components/CacheAreaView';
import { useRequest } from 'ahooks';

const { TextArea } = Input;

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'left',
    // wrapperCol: {
    //     span: 24,
    // },
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, listLoading, onExportForm, exportLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                exportLoading={exportLoading}
            >
                <Col span={8}>
                    <FormItem
                        label="第三方订单号"
                        name="startChargeSeq"
                        rules={[
                            {
                                required: true,
                                message: '请填写第三方订单号',
                            },
                        ]}
                    >
                        <TextArea placeholder="可输入多个订单编号，换行符或者空格隔开" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const SettlementListPage = (props) => {
    const {
        dispatch,
        listLoading,
        settlementModel: { settlementList, settlementTotal },
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});

    const cacheRef = useRef();

    useEffect(
        () => () => {
            dispatch({
                type: 'settlementModel/updateSettlementProperty',
                params: { settlementList: [], settlementTotal: 0 },
            });
        },
        [],
    );

    useEffect(() => {
        const partOrderNo = LocalStorage.getItem('partOrderNo');
        if (partOrderNo?.length && form) {
            form.setFieldsValue({ startChargeSeq: partOrderNo });
            LocalStorage.setItem('partOrderNo', undefined);
        }
    }, [form]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const { run: exportList, loading: exportLoading } = useRequest(
        async (params) => {
            try {
                await exportSettlementListApi(params);
                cacheRef?.current?.count();
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    // 调用搜索接口
    const searchData = (isDownload) => {
        form.validateFields().then((data) => {
            try {
                if (data.startChargeSeq?.replaceAll) {
                    data.startChargeSeq = data.startChargeSeq.replaceAll('\n', ',');
                }
                const params = {
                    ...data,
                    pageIndex: isDownload ? undefined : pageInfo.pageIndex,
                    pageSize: isDownload ? undefined : pageInfo.pageSize,
                };

                if (isDownload) {
                    exportList(params);
                } else {
                    dispatch({
                        type: 'settlementModel/getSettlementList',
                        params,
                    });
                }
            } catch (error) {
                console.log(444, error);
            }
        });
    };

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '第三方订单号',
            width: 200,
            dataIndex: 'startChargeSeq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operNickname',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算报文上报时间',
            width: 200,
            dataIndex: 'dataTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商异常结算配置时长',
            width: 140,
            dataIndex: 'abnoSettleTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算报文和下单时间间隔小时数',
            width: 180,
            dataIndex: 'orderInterval',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            width: 100,
            dataIndex: 'totalElecMoney',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 100,
            dataIndex: 'totalSeviceMoney',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '总费用',
            width: 100,
            dataIndex: 'totalMoney',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电量',
            width: 100,
            dataIndex: 'totalPower',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '原始报文',
            width: 280,
            dataIndex: 'requestOriginData',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper
            title="结算报文"
            extra={<CacheAreaView bizType={'hlhSettleMsg'} initRef={cacheRef} />}
        >
            <Card>
                <SearchLayout
                    form={form}
                    billType={pageInfo.billType}
                    {...props}
                    onSubmit={resetData}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                    exportLoading={exportLoading}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record}
                    dataSource={settlementList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: settlementTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.billType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ settlementModel, global, loading }) => ({
    settlementModel,
    global,
    listLoading: loading.effects['settlementModel/getSettlementList'],
}))(SettlementListPage);
