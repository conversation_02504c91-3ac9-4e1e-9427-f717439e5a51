import { getPersonOrderBillApi } from '@/services/FinanceManage/BillManageApi';

const PersonalBillModel = {
    namespace: 'personalBillModel',
    state: {
        personalBillList: [], // 用户订单交易列表
        personalBillListTotal: 0, // 用户订单交易列表总条数
    },
    effects: {
        /**
         * 用户订单交易列表
         */
        *getPersonalBillList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getPersonOrderBillApi, options);

                yield put({
                    type: 'updatePersonalBillList',
                    list,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updatePersonalBillList(state, { list, total }) {
            return {
                ...state,
                personalBillList: list,
                personalBillListTotal: total,
            };
        },
    },
};
export default PersonalBillModel;
