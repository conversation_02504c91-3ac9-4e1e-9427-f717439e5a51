import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Divider,
    Spin,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import { personOrderBillApiPath } from '@/services/FinanceManage/BillManageApi';
import { getProvinceAndCityApi, getStationListApi } from '@/services/CommonApi';
import CitysSelect from '@/components/CitysSelect/index.js';
import debounce from 'lodash/debounce';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { InfoCircleOutlined } from '@ant-design/icons';
import { SELECT_TYPES } from '@/config/declare';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 订单状态
const STATUS_TYPES = {
    ALL: '00',
    UNPAID: '05', // 待支付
    UNEVALUATE: '07', // 待评价
};

const formItemLayout = {
    // labelAlign: 'left',
    // labelCol: {
    //     flex: '0 0 100px',
    // },
};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { billTransChannelList },
        financeModel: { tariffTypeList, channelList, orderAmtTotal },
    } = props;

    const [stationList, changeStationList] = useState([]);
    const [fetching, changeFetching] = useState(false);

    useEffect(() => {
        if (billTransChannelList.length == 0) {
            dispatch({
                type: 'global/getBillTransChannelList',
                options: {},
            });
        }
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const tariffTypeOptions = useCallback(
        () =>
            tariffTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [tariffTypeList],
    );

    const channelOptions = useCallback(
        () =>
            channelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [channelList],
    );

    const getbillTransChannelOptions = useMemo(() => {
        return billTransChannelList.map((ele) => (
            <Option key={ele.codeValue} value={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));
    }, [billTransChannelList]);

    const fetchStation = debounce(async (stationName) => {
        try {
            const operId = form.getFieldValue('operId');
            if (!operId) {
                return;
            }
            const params = {
                operId,
                stationName,
            };
            changeFetching(true);
            const {
                data: { stationList: list },
            } = await getStationListApi(params);
            changeStationList(list);
        } catch (error) {
        } finally {
            changeFetching(false);
        }
    }, 800);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    city: [],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <OperSelectTypeItem
                            {...formItemLayout}
                            rules={[{ message: '请选择运营商', required: true }]}
                            purchase={SELECT_TYPES.ONLYBUY}
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="订单日期"
                            name="orderDates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        // if (!value) {
                                        //     return Promise.reject('请选择日期');
                                        // }
                                        // if (!value[0]) {
                                        //     return Promise.reject('请选择开始日期');
                                        // }
                                        // if (!value[1]) {
                                        //     return Promise.reject('请选择结束日期');
                                        // }
                                        if (value && value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="未结算金额:" name="unsettleFlag" {...formItemLayout}>
                            <Select placeholder="请选择">
                                <Option value="1">是</Option>
                                <Option value="0">否</Option>
                            </Select>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="营销活动:" name="actFlag" {...formItemLayout}>
                            <Select placeholder="请选择">
                                <Option value="1">是</Option>
                                <Option value="0">否</Option>
                            </Select>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem
                            label="开票日期"
                            name="openDates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        // if (!value) {
                                        //     return Promise.reject('请选择日期');
                                        // }
                                        // if (!value[0]) {
                                        //     return Promise.reject('请选择开始日期');
                                        // }
                                        // if (!value[1]) {
                                        //     return Promise.reject('请选择结束日期');
                                        // }
                                        if (value && value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="结算日期"
                            name="settleDates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        // if (!value) {
                                        //     return Promise.reject('请选择日期');
                                        // }
                                        // if (!value[0]) {
                                        //     return Promise.reject('请选择开始日期');
                                        // }
                                        // if (!value[1]) {
                                        //     return Promise.reject('请选择结束日期');
                                        // }
                                        if (value && value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="订单号:" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="订单渠道:" name="orderChannel" {...formItemLayout}>
                            <Select placeholder="请选择">{channelOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="支付渠道:" name="payChannel" {...formItemLayout}>
                            <Select placeholder="请选择">{getbillTransChannelOptions}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            placeholder="请选择"
                            rules={[]}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="充&nbsp;&nbsp;电&nbsp;&nbsp;站:"
                            name="stationId"
                            {...formItemLayout}
                        >
                            <Select
                                showSearch
                                placeholder="请选择"
                                loading={fetching}
                                notFoundContent={fetching ? <Spin size="small" /> : null}
                                filterOption={false}
                                onSearch={fetchStation}
                                onFocus={() => {
                                    fetchStation('');
                                }}
                            >
                                {stationList.map((d) => (
                                    <Option key={d.stationId}>{d.stationName}</Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const PersonalBillListPage = (props) => {
    const {
        dispatch,
        history,
        personalBillModel: { personalBillList, personalBillListTotal, orderAmtTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                orderNo: data.orderNo,
                orderChannel: data.orderChannel,
                payChannel: data.payChannel,
                unsettleFlag: data.unsettleFlag,
                actFlag: data.actFlag,
                stationId: data.stationId,
                beginTime:
                    (data.orderDates &&
                        data.orderDates[0] &&
                        data.orderDates[0].format('YYYY-MM-DD')) ||
                    '',
                endTime:
                    (data.orderDates &&
                        data.orderDates[1] &&
                        data.orderDates[1].format('YYYY-MM-DD')) ||
                    '',
                beginDate:
                    (data.settleDates &&
                        data.settleDates[0] &&
                        data.settleDates[0].format('YYYY-MM-DD')) ||
                    '',
                endDate:
                    (data.settleDates &&
                        data.settleDates[1] &&
                        data.settleDates[1].format('YYYY-MM-DD')) ||
                    '',
                invoiceBeginTime:
                    (data.openDates &&
                        data.openDates[0] &&
                        data.openDates[0].format('YYYY-MM-DD')) ||
                    '',
                invoiceEndTime:
                    (data.openDates &&
                        data.openDates[1] &&
                        data.openDates[1].format('YYYY-MM-DD')) ||
                    '',
            };
            if (data.city && data.city.length > 0) {
                params.city = JSON.stringify(data.city);
            }
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.orderStatus = pageInfo.tabType;
            }
            dispatch({
                type: 'personalBillModel/getPersonalBillList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            orderNo: data.orderNo,
            orderChannel: data.orderChannel,
            payChannel: data.payChannel,
            unsettleFlag: data.unsettleFlag,
            actFlag: data.actFlag,
            stationId: data.stationId,
            beginTime:
                (data.orderDates &&
                    data.orderDates[0] &&
                    data.orderDates[0].format('YYYY-MM-DD')) ||
                '',
            endTime:
                (data.orderDates &&
                    data.orderDates[1] &&
                    data.orderDates[1].format('YYYY-MM-DD')) ||
                '',
            beginDate:
                (data.settleDates &&
                    data.settleDates[0] &&
                    data.settleDates[0].format('YYYY-MM-DD')) ||
                '',
            endDate:
                (data.settleDates &&
                    data.settleDates[1] &&
                    data.settleDates[1].format('YYYY-MM-DD')) ||
                '',
            invoiceBeginTime:
                (data.openDates && data.openDates[0] && data.openDates[0].format('YYYY-MM-DD')) ||
                '',
            invoiceEndTime:
                (data.openDates && data.openDates[1] && data.openDates[1].format('YYYY-MM-DD')) ||
                '',
        };
        if (data.city && data.city.length > 0) {
            params.city = JSON.stringify(data.city);
        }
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.orderStatus = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: personOrderBillApiPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const lookDetailsEvent = (item) => {
        history.push(`/financemanage/orderTransaction/list/details/${item.orderNo}`);
    };

    const columns = [
        {
            title: '订单时间',
            width: 140,
            dataIndex: 'orderBgnTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '结清时间',
            width: 140,
            dataIndex: 'orderEndTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },

        {
            title: '订单号',
            width: 140,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单状态',
            width: 200,
            align: 'right',
            dataIndex: 'orderStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 200,
            align: 'right',
            dataIndex: 'orderChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '支付渠道',
            width: 140,
            dataIndex: 'payChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易内容',
            width: 140,
            dataIndex: 'orderPayTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电量',
            width: 200,
            dataIndex: 'chargePq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单金额',
            width: 140,
            dataIndex: 'orderAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            width: 140,
            dataIndex: 'elecAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 140,
            dataIndex: 'serviceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '附加费',
            width: 140,
            dataIndex: 'incrementAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '是否有未结算金额',
            width: 180,
            dataIndex: 'unsettleFlag',
            render(text, record) {
                return <span>{record.unsettleFlag == '1' ? '是' : '否'}</span>;
            },
        },
        {
            title: '未结算金额结算日期',
            width: 200,
            dataIndex: 'settleTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '营销活动（是/否）',
            width: 180,
            dataIndex: 'actFlag',
            render(text, record) {
                return <span title={text}>{record.actFlag == '1' ? '是' : '否'}</span>;
            },
        },
        {
            title: '优惠金额（营销费用）',
            width: 200,
            dataIndex: 'actAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '商家优惠',
            width: 120,
            dataIndex: 'operDisAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单收入金额',
            width: 140,
            dataIndex: 'orderReceiveAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付手续费',
            width: 140,
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手工退款金额',
            width: 140,
            dataIndex: 'refundAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手工退款手续费',
            width: 160,
            dataIndex: 'refundServiceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    可提现金额
                    <Tooltip title="订单收入金额-手工退款金额+支付手续费+手工退款手续费">
                        <InfoCircleOutlined />
                    </Tooltip>
                </span>
            ),
            width: 140,
            dataIndex: 'withdrawAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '开票日期',
            width: 140,
            dataIndex: 'invoiceTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发票代码',
            width: 140,
            dataIndex: 'invoiceCode',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '发票号码',
            width: 140,
            dataIndex: 'invoiceNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '购方名称',
            width: 140,
            dataIndex: 'invoiceTitle',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '购方税号',
            width: 140,
            dataIndex: 'taxNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '销售发票金额（不含税）',
            width: 140,
            dataIndex: 'invoiceAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '销项税额',
            width: 140,
            dataIndex: 'taxAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '销售发票金额（含税）',
            width: 140,
            dataIndex: 'taxExcludedAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record) {
                return (
                    <span className={styles['table-btn']} onClick={() => lookDetailsEvent(record)}>
                        详情
                    </span>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="待支付" key={STATUS_TYPES.UNPAID} />
                    <TabPane tab="待评价" key={STATUS_TYPES.UNEVALUATE} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={personalBillList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: personalBillListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, personalBillModel, global, loading }) => ({
    financeModel,
    personalBillModel,
    global,
    listLoading: loading.effects['personalBillModel/getPersonalBillList'],
}))(PersonalBillListPage);
