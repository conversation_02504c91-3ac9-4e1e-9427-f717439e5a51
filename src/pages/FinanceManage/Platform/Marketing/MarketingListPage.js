import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    Alert,
    // Divider,
    Spin,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import { orderBillAmtApiPath } from '@/services/FinanceManage/BillManageApi';
import { getProvinceAndCityApi, getStationListApi } from '@/services/CommonApi';
import CitysSelect from '@/components/CitysSelect/index.js';
import debounce from 'lodash/debounce';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    ALL: '00',
    DONE: '01', // 已结算
    UNDONE: '02', // 待结算
};

const formItemLayout = {
    // labelAlign: 'left',
    // labelCol: {
    //     flex: '0 0 100px',
    // },
};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        financeModel: { tariffTypeList, channelList, orderAmtTotal },
    } = props;

    const [stationList, changeStationList] = useState([]);
    const [fetching, changeFetching] = useState(false);

    useEffect(() => {
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const tariffTypeOptions = useCallback(
        () =>
            tariffTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [tariffTypeList],
    );

    const channelOptions = useCallback(
        () =>
            channelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [channelList],
    );

    const profitOptions = () => [
        <Option key="0" value="0">
            订单分润
        </Option>,
        <Option key="1" value="1">
            坏账回收
        </Option>,
    ];

    const timeTypeOptions = () => [
        <Option key="01" value="01">
            结算日期
        </Option>,
        <Option key="1" value="02">
            订单日期
        </Option>,
    ];

    const fetchStation = debounce(async (stationName) => {
        try {
            const operId = form.getFieldValue('operId');
            if (!operId) {
                return;
            }
            const params = {
                operId,
                stationName,
            };
            changeFetching(true);
            const {
                data: { stationList: list },
            } = await getStationListApi(params);
            changeStationList(list);
        } catch (error) {
        } finally {
            changeFetching(false);
        }
    }, 800);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                    city: [],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem label="选择日期:">
                            <Input.Group>
                                <Row>
                                    <Col flex="0 0 auto">
                                        <FormItem noStyle name="timeType">
                                            <Select placeholder="请选择">
                                                {timeTypeOptions()}
                                            </Select>
                                        </FormItem>
                                    </Col>
                                    <Col flex="1">
                                        <FormItem
                                            noStyle
                                            name="dates"
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('请选择日期');
                                                        }
                                                        if (!value[0]) {
                                                            return Promise.reject('请选择开始日期');
                                                        }
                                                        if (!value[1]) {
                                                            return Promise.reject('请选择结束日期');
                                                        }
                                                        if (value[0] && value[1]) {
                                                            const startTime = +new Date(value[0]);
                                                            const endTime = +new Date(value[1]);
                                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                                            if (
                                                                Math.abs(startTime - endTime) > dest
                                                            ) {
                                                                return Promise.reject(
                                                                    '选取范围最大不超过60天',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <RangePicker format="YYYY-MM-DD" />
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Input.Group>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            {...formItemLayout}
                            rules={[{ message: '请选择运营商', required: true }]}
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="第三方订单号:" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            placeholder="请选择"
                            rules={[]}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="充&nbsp;&nbsp;电&nbsp;&nbsp;站:"
                            name="stationId"
                            {...formItemLayout}
                        >
                            <Select
                                showSearch
                                placeholder="请选择"
                                loading={fetching}
                                notFoundContent={fetching ? <Spin size="small" /> : null}
                                filterOption={false}
                                onSearch={fetchStation}
                                onFocus={() => {
                                    fetchStation('');
                                }}
                            >
                                {stationList.map((d) => (
                                    <Option key={d.stationId}>{d.stationName}</Option>
                                ))}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="付款类型:" name="orderPayType" {...formItemLayout}>
                            <Select placeholder="请选择">{tariffTypeOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="分润类型:" name="profitType" {...formItemLayout}>
                            <Select placeholder="请选择">{profitOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="订单渠道:" name="orderChannel" {...formItemLayout}>
                            <Select placeholder="请选择">{channelOptions()}</Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const MarketingListPage = (props) => {
    const {
        dispatch,
        history,
        marketingModel: { marketingList, marketingListTotal, orderAmtTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [loadPage, changeLoadPage] = useState(true);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        if (loadPage) {
            changeLoadPage(false);
            return;
        }
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                orderNo: data.orderNo,
                orderChannel: data.orderChannel,
                profitType: data.profitType,
                orderPayType: data.orderPayType,
                stationId: data.stationId,
                city: JSON.stringify(data.city),
                timeType: data.timeType,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.orderStatus = pageInfo.tabType;
            }
            dispatch({
                type: 'marketingModel/getMarketingList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            orderNo: data.orderNo,
            orderChannel: data.orderChannel,
            profitType: data.profitType,
            orderPayType: data.orderPayType,
            stationId: data.stationId,
            city: JSON.stringify(data.city),
            timeType: data.timeType,
            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };
        if (pageInfo.tabType !== STATUS_TYPES.ALL) {
            params.orderStatus = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: orderBillAmtApiPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const lookDetailsEvent = (item) => {
        history.push(`/financemanage/orderTransaction/list/details/${item.orderNo}`);
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '订单时间',
            width: 140,
            dataIndex: 'succeedTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '第三方订单号',
            width: 140,
            dataIndex: 'thirdOrderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '资金券抵扣',
            width: 200,
            align: 'right',
            dataIndex: 'funCpnBal',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付券抵扣',
            width: 200,
            align: 'right',
            dataIndex: 'cpnBal',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台券抵扣',
            width: 200,
            align: 'right',
            dataIndex: 'orderPlatCouponAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途活动金额',
            width: 200,
            align: 'right',
            dataIndex: 'actBalTotal',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '新电途营销费用合计',
            width: 200,
            align: 'right',
            dataIndex: 'marketActAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电站',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '订单渠道',
            width: 140,
            dataIndex: 'orderChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '付款类型',
            width: 140,
            dataIndex: 'orderPayTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单结算时间',
            width: 200,
            dataIndex: 'successTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '结算状态',
            width: 140,
            dataIndex: 'settleStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record) {
                return (
                    <span className={styles['table-btn']} onClick={() => lookDetailsEvent(record)}>
                        详情
                    </span>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <Alert
                    style={{ marginTop: '20px' }}
                    message={`订单数：${orderAmtTotal.orderNum || 0}个；平台补贴金额：${
                        orderAmtTotal.funCpnBal || 0
                    }元；平台优惠券金额：${orderAmtTotal.cpnBal || 0}元；平台活动金额：${
                        orderAmtTotal.actBalTotal || 0
                    }元；平台营销费用合计：${orderAmtTotal.marketActAmt || 0}元`}
                    type="info"
                    showIcon
                />

                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="已结算" key={STATUS_TYPES.DONE} />
                    <TabPane tab="待结算" key={STATUS_TYPES.UNDONE} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={marketingList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: marketingListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, marketingModel, global, loading }) => ({
    financeModel,
    marketingModel,
    global,
    listLoading: loading.effects['marketingModel/getMarketingList'],
}))(MarketingListPage);
