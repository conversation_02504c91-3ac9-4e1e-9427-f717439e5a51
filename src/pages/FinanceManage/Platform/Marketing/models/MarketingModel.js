import { getOrderBillAmtApi } from '@/services/FinanceManage/BillManageApi';

const MarketingModel = {
    namespace: 'marketingModel',
    state: {
        marketingList: [], // 用户订单交易列表
        marketingListTotal: 0, // 用户订单交易列表总条数
        orderAmtTotal: {},
    },
    effects: {
        /**
         * 用户订单交易列表
         */
        *getMarketingList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total, orderAmtTotal },
                } = yield call(getOrderBillAmtApi, options);

                yield put({
                    type: 'updateMarketingList',
                    list,
                    total,
                    orderAmtTotal,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateMarketingList(state, { list, total, orderAmtTotal }) {
            return {
                ...state,
                marketingList: list,
                marketingListTotal: total,
                orderAmtTotal,
            };
        },
    },
};
export default MarketingModel;
