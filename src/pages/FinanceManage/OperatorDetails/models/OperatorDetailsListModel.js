import {
    getOrderBillTransApi,
    getOrderBillTransTotalApi,
} from '@/services/FinanceManage/BillManageApi';

const BillTransModel = {
    namespace: 'billTransListModel',
    state: {
        billTransList: [], // 收支明细列表
        billTransListTotal: 0, // 收支明细列表总条数
        billTransTotalList: [], // 收支明细汇总列表
        billTransTotalListTotal: 0, // 收支明细汇总列表总条数
    },
    effects: {
        /**
         * 收支明细列表
         */
        *getBillTransList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getOrderBillTransApi, options);

                yield put({
                    type: 'updateBillTransList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 收支明细汇总列表
         */
        *getBillTransTotalList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getOrderBillTransTotalApi, options);

                yield put({
                    type: 'updateBillTransTotalList',
                    list,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateBillTransList(state, { list, total }) {
            return {
                ...state,
                billTransList: list,
                billTransListTotal: total,
            };
        },
        updateBillTransTotalList(state, { list, total }) {
            return {
                ...state,
                billTransTotalList: list,
                billTransTotalListTotal: total,
            };
        },
    },
};
export default BillTransModel;
