import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    Divider,
    Radio,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { stopMiniGiftApi, deleteMiniGiftApi } from '@/services/Marketing/MarketingGiftApi';
import { ACTSUBTYPES } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import {
    orderBillTransApiPath,
    orderBillTransTotalApiPath,
} from '@/services/FinanceManage/BillManageApi';
import styles from '@/assets/styles/common.less';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 类型
const STATUS_TYPES = {
    DETAILS: '01', // 收支明细
    TOTAL: '02', // 收支汇总
};

const transObjectOptions = () => [
    <Option key="01" value="01">
        平台
    </Option>,
    <Option key="02" value="02">
        用户
    </Option>,
    <Option key="03" value="03">
        合作伙伴
    </Option>,
];

const timeTypeOptions = () => [
    <Option key="01" value="01">
        订单日期
    </Option>,
    <Option key="1" value="02">
        交易日期
    </Option>,
];

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { billTransChannelList, billTransTypeList },
        financeModel: { tariffTypeList, channelList },
    } = props;

    // const operatorSelectRef = useRef();

    useEffect(() => {
        if (billTransChannelList.length == 0) {
            dispatch({
                type: 'global/getBillTransChannelList',
                options: {},
            });
        }
        if (billTransTypeList.length == 0) {
            dispatch({
                type: 'global/getBillTransTypeList',
                options: {},
            });
        }
        dispatch({
            type: 'financeModel/initFinanceOptions',
            options: {},
        });
    }, []);

    const getbillTransChannelOptions = useMemo(() => {
        return billTransChannelList.map((ele) => (
            <Option key={ele.codeValue} value={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));
    }, [billTransChannelList]);
    const billTransTypeOptions = useMemo(
        () =>
            billTransTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [billTransTypeList],
    );

    const tariffTypeOptions = useCallback(
        () =>
            tariffTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [tariffTypeList],
    );

    const channelOptions = useCallback(
        () =>
            channelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [channelList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // operatorSelectRef.current.rest();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                timeType: '02',
                dates: [moment().subtract(7, 'days'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                {/* <Col span={8}>
                    <OperSelectItem
                        name="operId"
                        initRef={operatorSelectRef}
                        operatorList={operatorList}
                        {...formItemLayout}
                    />
                </Col> */}
                <Col span={10}>
                    <FormItem label="交易日期:" {...formItemLayout}>
                        <Input.Group>
                            <Row>
                                <Col flex="0 0 auto">
                                    <FormItem noStyle name="timeType">
                                        <Select placeholder="请选择">{timeTypeOptions()}</Select>
                                    </FormItem>
                                </Col>
                                <Col flex="1">
                                    <FormItem
                                        noStyle
                                        name="dates"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请选择日期');
                                                    }
                                                    if (!value[0]) {
                                                        return Promise.reject('请选择开始日期');
                                                    }
                                                    if (!value[1]) {
                                                        return Promise.reject('请选择结束日期');
                                                    }
                                                    if (value[0] && value[1]) {
                                                        const startTime = +new Date(value[0]);
                                                        const endTime = +new Date(value[1]);
                                                        const dest = 60 * 1000 * 60 * 24 * 60;

                                                        if (Math.abs(startTime - endTime) > dest) {
                                                            return Promise.reject(
                                                                '选取范围最大不超过60天',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <RangePicker format="YYYY-MM-DD" />
                                    </FormItem>
                                </Col>
                            </Row>
                        </Input.Group>
                    </FormItem>
                </Col>
                <Col span={6}>
                    <FormItem
                        label="订&nbsp;&nbsp;单&nbsp;&nbsp;号:"
                        name="orderNo"
                        {...formItemLayout}
                    >
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem
                        label="交&nbsp;&nbsp;易&nbsp;&nbsp;号:"
                        name="transNo"
                        {...formItemLayout}
                    >
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="交易渠道:" name="transChannel" {...formItemLayout}>
                        <Select placeholder="请选择">{getbillTransChannelOptions}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="交易类型:" name="transType" {...formItemLayout}>
                        <Select mode="multiple" allowClear maxTagCount={2} placeholder="请选择">
                            {billTransTypeOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="交易对象:" name="transObject" {...formItemLayout}>
                        <Select placeholder="请选择">{transObjectOptions()}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单渠道:" name="orderChannel" {...formItemLayout}>
                        <Select placeholder="请选择">{channelOptions()}</Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const CollectSearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        global: { billTransChannelList },
    } = props;

    // const operatorSelectRef = useRef();

    useEffect(() => {
        if (billTransChannelList.length == 0) {
            dispatch({
                type: 'global/getBillTransChannelList',
                options: {},
            });
        }
    }, []);

    const getbillTransChannelOptions = useMemo(
        () =>
            billTransChannelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [billTransChannelList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // operatorSelectRef.current.rest();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(7, 'days'), moment()],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                {/* <Col span={8}>
                    <OperSelectItem
                        name="operId"
                        initRef={operatorSelectRef}
                        operatorList={operatorList}
                        {...formItemLayout}
                    />
                </Col> */}
                <Col span={10}>
                    <FormItem label="交易日期:" name="dates" {...formItemLayout}>
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('日期');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="交易渠道:" name="transChannel" {...formItemLayout}>
                        <Select placeholder="请选择">{getbillTransChannelOptions}</Select>
                    </FormItem>
                </Col>

                <Col>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button type="primary" onClick={onExportForm}>
                            导出
                        </Button>
                        <Button onClick={resetForm}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

const OrderSettleListPage = (props) => {
    const {
        dispatch,
        history,
        billTransListModel: {
            billTransList,
            billTransListTotal,
            billTransTotalList,
            billTransTotalListTotal,
        },
        listLoading,
        totalListLoading,
        user: { currentUser },
    } = props;

    const [form] = Form.useForm();
    const [operForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.DETAILS,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        operForm.validateFields().then((operData) => {
            const data = form.getFieldsValue();
            let params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,

                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                operId: operData.operId,
            };

            if (pageInfo.tabType == STATUS_TYPES.DETAILS) {
                const entends = {
                    timeType: data.timeType,
                    orderNo: data.orderNo,
                    transNo: data.transNo,
                    transChannel: data.transChannel,
                    transType: JSON.stringify(data.transType),
                    transObject: data.transObject,
                    orderChannel: data.orderChannel,
                };
                params = {
                    ...params,
                    ...entends,
                };
                dispatch({
                    type: 'billTransListModel/getBillTransList',
                    options: params,
                });
            } else if (pageInfo.tabType == STATUS_TYPES.TOTAL) {
                params.transChannel = data.transChannel;
                dispatch({
                    type: 'billTransListModel/getBillTransTotalList',
                    options: params,
                });
            }
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeOperEvent = (value) => {
        if (pageInfo.pageIndex == 1) {
            searchData();
        } else changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        operForm.validateFields().then((operData) => {
            const data = form.getFieldsValue();
            try {
                const params = {
                    timeType: data.timeType,
                    orderNo: data.orderNo,
                    transNo: data.transNo,
                    transChannel: data.transChannel,
                    transType: JSON.stringify(data.transType),
                    transObject: data.transObject,
                    orderChannel: data.orderChannel,
                    beginDate:
                        (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                    endDate:
                        (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                    operId: operData.operId,
                };

                const columnsStrs = [];
                for (const item of columns) {
                    if (item.dataIndex) {
                        columnsStrs.push({
                            key: item.dataIndex,
                            value: item.title,
                        });
                    }
                }
                exportTableByParams({
                    methodUrl: orderBillTransApiPath,
                    options: params,
                    columnsStr: columnsStrs,
                });
            } catch (error) {
                console.log(5555, error);
            }
        });
    };

    const exportTotalFormEvent = () => {
        operForm.validateFields().then((operData) => {
            const data = form.getFieldsValue();
            try {
                const params = {
                    transChannel: data.transChannel,
                    beginDate:
                        (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                    endDate:
                        (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                    operId: operData.operId,
                };
                const columnsStrs = [];
                for (const item of collectColumns) {
                    if (item.dataIndex) {
                        columnsStrs.push({
                            key: item.dataIndex,
                            value: item.title,
                        });
                    }
                }
                exportTableByParams({
                    methodUrl: orderBillTransTotalApiPath,
                    options: params,
                    columnsStr: columnsStrs,
                });
            } catch (error) {
                console.log(5555, error);
            }
        });
    };

    const columns = [
        {
            title: '交易时间',
            width: 140,
            dataIndex: 'transTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '交易渠道',
            width: 140,
            dataIndex: 'transChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易类型',
            width: 140,
            dataIndex: 'transTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易对象',
            width: 140,
            dataIndex: 'transObjectName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易号',
            width: 200,
            dataIndex: 'transNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易金额',
            width: 140,
            align: 'right',
            dataIndex: 'transAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付券抵扣金额',
            width: 160,
            align: 'right',
            dataIndex: 'transCpnAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易实收金额',
            width: 140,
            align: 'right',
            dataIndex: 'transIncomeAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手续费',
            width: 140,
            align: 'right',
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 140,
            dataIndex: 'orderChannelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单确认时间',
            width: 200,
            dataIndex: 'orderBgnTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单号',
            width: 140,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '分润账号',
            width: 140,
            align: 'right',
            dataIndex: 'profitAccount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const collectColumns = [
        {
            title: '交易日期',
            width: 140,
            dataIndex: 'transTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易金额',
            width: 140,
            align: 'right',
            dataIndex: 'transAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '优惠券抵扣金额',
            width: 160,
            align: 'right',
            dataIndex: 'transCpnAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易实收金额',
            width: 160,
            align: 'right',
            dataIndex: 'transIncomeAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手续费支出',
            width: 140,
            align: 'right',
            dataIndex: 'serviceCharge',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '入账金额',
            width: 140,
            align: 'right',
            dataIndex: 'transEntryAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '分润账号',
        //     width: 140,
        //     align: 'right',
        //     dataIndex: 'profitAccount',
        //     render(text, record) {
        //         return <span title={text}>{text}</span>;
        //     },
        // },
    ];
    const changeTabTypeEvent = (e) => {
        const {
            target: { value },
        } = e;

        changePageInfo({ tabType: value, pageIndex: 1 });
    };

    const renderPage = () => {
        if (pageInfo.tabType == STATUS_TYPES.DETAILS) {
            return (
                <Fragment>
                    <SearchLayout
                        form={form}
                        {...props}
                        onSubmit={() => {
                            changePageInfo((state) => ({
                                ...state,
                                pageIndex: 1,
                            }));
                        }}
                        onReset={resetData}
                        onExportForm={exportFormEvent}
                    />

                    <TablePro
                        name="detail"
                        loading={listLoading}
                        scroll={{ x: 'max-content' }}
                        rowKey={(record, index) => index}
                        dataSource={billTransList}
                        columns={columns}
                        onChange={onTableChange}
                        pagination={{
                            current: pageInfo.pageIndex,
                            total: billTransListTotal,
                            pageSize: pageInfo.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total) => `共 ${total} 条`,
                        }}
                        tabType={pageInfo.tabType}
                    />
                </Fragment>
            );
        }
        if (pageInfo.tabType == STATUS_TYPES.TOTAL) {
            return (
                <Fragment>
                    <CollectSearchLayout
                        form={form}
                        {...props}
                        onSubmit={searchData}
                        onReset={resetData}
                        onExportForm={exportTotalFormEvent}
                    />

                    <TablePro
                        name="total"
                        loading={totalListLoading}
                        scroll={{ x: 'max-content' }}
                        rowKey={(record, index) => index}
                        dataSource={billTransTotalList}
                        columns={collectColumns}
                        onChange={onTableChange}
                        pagination={{
                            current: pageInfo.pageIndex,
                            total: billTransTotalListTotal,
                            pageSize: pageInfo.pageSize,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total) => `共 ${total} 条`,
                        }}
                        tabType={pageInfo.tabType}
                    />
                </Fragment>
            );
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Row wrap={false}>
                    <Col span={8}>
                        <Radio.Group
                            value={pageInfo.tabType}
                            onChange={changeTabTypeEvent}
                            buttonStyle="solid"
                        >
                            <Radio.Button value={STATUS_TYPES.DETAILS}>收支明细</Radio.Button>
                            <Radio.Button value={STATUS_TYPES.TOTAL}>收支汇总</Radio.Button>
                        </Radio.Group>
                    </Col>
                    <Col offset={8} span={8}>
                        <Form {...formItemLayout} form={operForm} scrollToFirstError>
                            <OperSelectTypeItem
                                {...formItemLayout}
                                onChange={changeOperEvent}
                                rules={[{ message: '请选择运营商', required: true }]}
                                form={operForm}
                            />
                        </Form>
                    </Col>
                </Row>

                <Divider style={{ marginTop: '0' }} />
                {renderPage()}
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ financeModel, billTransListModel, global, user, loading }) => ({
    financeModel,
    billTransListModel,
    global,
    user,
    listLoading: loading.effects['billTransListModel/getBillTransList'],
    totalListLoading: loading.effects['billTransListModel/getBillTransTotalList'],
}))(OrderSettleListPage);
