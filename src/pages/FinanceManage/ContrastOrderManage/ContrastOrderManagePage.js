import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Radio } from 'antd';
import { connect } from 'umi';
import React, { Fragment, useState } from 'react';
import BalancePage from './BalancePage';
import RecordPage from './RecordPage';

const PAGE_TUPES = {
    BALANCE: '01', // 平账处理
    RECORD: '02', // 对账记录
};
const ContrastOrderManagePage = (props) => {
    const [pageType, changePageType] = useState(PAGE_TUPES.BALANCE);

    const onChangePageTypeEvent = (e) => {
        changePageType(e.target.value);
    };

    const toggleBlanceView = () => {
        changePageType(PAGE_TUPES.BALANCE);
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Radio.Group value={pageType} onChange={onChangePageTypeEvent} buttonStyle="solid">
                    <Radio.Button value={PAGE_TUPES.BALANCE}>平账处理</Radio.Button>
                    <Radio.Button value={PAGE_TUPES.RECORD}>对账记录</Radio.Button>
                </Radio.Group>

                <Card
                    bodyStyle={{
                        marginTop: '20px',
                        padding: '0',
                    }}
                    bordered={false}
                >
                    {pageType === PAGE_TUPES.BALANCE ? <BalancePage {...props} /> : null}
                    {pageType === PAGE_TUPES.RECORD ? (
                        <RecordPage hideView={toggleBlanceView} {...props} />
                    ) : null}
                </Card>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ contrastOrderManageModel, global, loading }) => ({
    contrastOrderManageModel,
    global,
    listLoading: loading.effects['contrastOrderManageModel/getContrastOrderManageList'],
}))(ContrastOrderManagePage);
