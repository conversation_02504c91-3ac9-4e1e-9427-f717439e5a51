import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import {
    getRecordListPath,
    exportCecBillRecordApi,
} from '@/services/FinanceManage/ContrastOrderManageApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import lodash from 'lodash';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, listLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem
                            label="发生时间:"
                            name="dates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('请选择日期');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            onChange={onSubmit}
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="平台批次号:" name="platBillNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const RecordPage = (props) => {
    const {
        dispatch,
        history,
        contrastOrderManageModel: { cecBillRecordList, cecBillRecordListTotal },
        listLoading,
        hideView,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                platBillNo: data.platBillNo,

                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };

            dispatch({
                type: 'contrastOrderManageModel/getCecBillRecordList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = lodash.throttle(() => {
        const data = form.getFieldsValue();
        const params = {
            operId: data.operId,
            platBillNo: data.platBillNo,

            beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
            endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
        };

        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getRecordListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    }, 1500);

    const downloadDetailsEvent = lodash.throttle(async (item) => {
        try {
            const params = {
                platBillNo: item.platBillNo,

                operId: item.operId,
            };
            exportCecBillRecordApi(params);
        } catch (error) {}
    }, 1500);

    const balanceDetailsEvent = (item) => {
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: {
                    operId: item.operatorId,
                    platBillNo: item.platBillNo,
                },
            },
        });
        hideView && hideView();
    };

    const columns = [
        {
            title: '对账时间',
            width: 240,
            dataIndex: 'cecBillTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },

        {
            title: '平台批次号',
            width: 140,
            dataIndex: 'platBillNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '对账批次号',
            width: 140,
            dataIndex: 'cecBillNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '核对成功订单数',
            width: 140,
            dataIndex: 'checkSuccessNum',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '核对成功订单金额',
            width: 140,
            dataIndex: 'checkSuccessAmt',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '核对失败订单数',
            width: 140,
            dataIndex: 'checkFailNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 180,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Space>
                        {record.checkFailNum > 0 ? (
                            <span
                                className={styles['table-btn']}
                                onClick={() => balanceDetailsEvent(record)}
                            >
                                差异处理
                            </span>
                        ) : null}

                        <span
                            className={styles['table-btn']}
                            onClick={() => downloadDetailsEvent(record)}
                        >
                            下载明细
                        </span>
                    </Space>
                );
            },
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={cecBillRecordList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: cecBillRecordListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

export default connect(({ contrastOrderManageModel, global, loading }) => ({
    contrastOrderManageModel,
    global,
    listLoading: loading.effects['contrastOrderManageModel/getCecBillRecordList'],
}))(RecordPage);
