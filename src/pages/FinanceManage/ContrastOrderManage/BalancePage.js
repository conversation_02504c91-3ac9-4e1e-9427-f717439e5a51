import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Radio,
    Modal,
    Row,
    Select,
    Alert,
    Divider,
    Space,
    Input,
    InputNumber,
    DatePicker,
    Tabs,
    Tooltip,
    Collapse,
    Steps,
    Descriptions,
    message,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import { getBalanceListPath } from '@/services/FinanceManage/ContrastOrderManageApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { InfoCircleOutlined } from '@ant-design/icons';
import pageStyles from './BalancePage.less';
import baseStyles from '@/assets/styles/base.less';
import { isEmpty } from '@/utils/utils';
import { sendHandleBillApi } from '@/services/FinanceManage/ContrastOrderManageApi';

const HANDLEWAY_TYPES = {
    BASE: '01',
    XDT: '02',
    CUSTOM: '03',
};

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const { TextArea } = Input;

const { Panel } = Collapse;

const { Step } = Steps;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const CONTRAST_STATUS = {
    ALL: '00',
    DONE: '1',
    WILL: '0',
};

const timeTypes = [
    { codeName: '对账时间', codeValue: '01' },
    { codeName: '处理时间', codeValue: '02' },
];

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, listLoading } = props;

    const timeTypeOptions = () =>
        timeTypes.map((ele) => (
            <Option key={ele.codeValue} value={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '01',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    minSpan={40}
                >
                    <Col span={12}>
                        <FormItem label="发生时间:">
                            <Input.Group>
                                <Row>
                                    <Col flex="0 0 auto">
                                        <FormItem noStyle name="timeType">
                                            <Select placeholder="请选择">
                                                {timeTypeOptions()}
                                            </Select>
                                        </FormItem>
                                    </Col>
                                    <Col flex="1">
                                        <FormItem
                                            noStyle
                                            name="dates"
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('请选择日期');
                                                        }
                                                        if (!value[0]) {
                                                            return Promise.reject('请选择开始日期');
                                                        }
                                                        if (!value[1]) {
                                                            return Promise.reject('请选择结束日期');
                                                        }
                                                        if (value[0] && value[1]) {
                                                            const startTime = +new Date(value[0]);
                                                            const endTime = +new Date(value[1]);
                                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                                            if (
                                                                Math.abs(startTime - endTime) > dest
                                                            ) {
                                                                return Promise.reject(
                                                                    '选取范围最大不超过60天',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <RangePicker format="YYYY-MM-DD" />
                                        </FormItem>
                                    </Col>
                                </Row>
                            </Input.Group>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <OperSelectTypeItem
                            onChange={onSubmit}
                            form={form}
                            // rules={[{ required: true, message: '请选择运营商' }]}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="订单号:" name="orderNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="平台批次号:" name="platBillNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="处理结果:" name="billHandleStatus">
                            <Select placeholder="请选择">
                                <Option value="01">以对方为准</Option>
                                <Option value="02">以新电途为准</Option>
                                <Option value="03">手工结算</Option>
                            </Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const WaitDetailView = (props) => {
    const { form, info } = props;

    const [curBillIndex, changeBillIndex] = useState(0);

    const billList = useMemo(() => {
        if (info) {
            return info.billList || [];
        }
        return [];
    }, [info]);

    const billListOption = useMemo(() => {
        return billList.map((ele, index) => {
            return (
                <Option key={index} value={index}>
                    {ele.cecBillTime}
                </Option>
            );
        });
    }, [billList]);

    const billInfo = useMemo(() => {
        if (billList && billList[curBillIndex]) {
            return billList[curBillIndex];
        }
        return {};
    }, [billList]);

    useEffect(() => {
        if (billInfo) {
            form.setFieldsValue({
                cecBillTime: billInfo.cecBillTime,
                cecBillDataId: billInfo.cecBillDataId,
            });
        }
    }, [billInfo]);

    const formData = form.getFieldsValue();

    const changeSettlementType = (type) => {
        const params = {
            baseSettlement: false,
            xdtSettlement: false,
            customSettlement: false,
        };
        params[type] = true;
        form.setFieldsValue(params);
    };

    const changeBillEvent = (value) => {
        changeBillIndex(value);
    };

    return (
        <Fragment>
            {billList.length > 1 ? (
                <Fragment>
                    <Alert
                        message={`该订单存在${billList.length}条待处理差异。选择其中一条处理后，剩余差异将同步更新为“已处理”`}
                        type="warning"
                        showIcon
                    />
                    <br></br>
                    <FormItem label="选择差异产生时间：">
                        <Select value={curBillIndex} onChange={changeBillEvent}>
                            {billListOption}
                        </Select>
                    </FormItem>
                </Fragment>
            ) : null}

            <table className={pageStyles['details-table']}>
                <tr>
                    <th>选择</th>
                    <th>平账方式</th>
                    <th>订单号</th>
                    <th>电量</th>
                    <th>电费</th>
                    <th>服务费</th>
                    <th>附加费</th>
                    <th>订单金额</th>
                </tr>
                <tr>
                    <td>
                        <FormItem noStyle name="baseSettlement" valuePropName="checked">
                            <Radio
                                onChange={(e) => {
                                    changeSettlementType('baseSettlement');
                                }}
                            ></Radio>
                        </FormItem>
                    </td>
                    <td>以对账方为准</td>
                    <td>{billInfo.baseEquipOrderNo || '-'}</td>
                    <td>{billInfo.baseEquipChargePq >= 0 ? billInfo.baseEquipChargePq : '-'}</td>
                    <td>{billInfo.baseEquipElecAmt >= 0 ? billInfo.baseEquipElecAmt : '-'}</td>
                    <td>
                        {billInfo.baseEquipServiceAmt >= 0 ? billInfo.baseEquipServiceAmt : '-'}
                    </td>
                    <td>
                        {billInfo.baseEquipIncrementAmt >= 0 ? billInfo.baseEquipIncrementAmt : '-'}
                    </td>
                    <td>{billInfo.baseEquipChargeAmt >= 0 ? billInfo.baseEquipChargeAmt : '-'}</td>
                </tr>
                <tr>
                    <td>
                        <FormItem noStyle name="xdtSettlement" valuePropName="checked">
                            <Radio
                                onChange={() => {
                                    changeSettlementType('xdtSettlement');
                                }}
                            ></Radio>
                        </FormItem>
                    </td>
                    <td>以新电途为准</td>
                    <td>-</td>
                    <td>
                        {billInfo.customerOperChargePq >= 0 ? billInfo.customerOperChargePq : '-'}
                    </td>
                    <td>
                        {billInfo.customerOperElecAmt >= 0 ? billInfo.customerOperElecAmt : '-'}
                    </td>
                    <td>
                        {billInfo.customerOperServiceAmt >= 0
                            ? billInfo.customerOperServiceAmt
                            : '-'}
                    </td>
                    <td>
                        {billInfo.customerOperIncrementAmt >= 0
                            ? billInfo.customerOperIncrementAmt
                            : '-'}
                    </td>
                    <td>
                        {billInfo.customerOperChargeAmt >= 0 ? billInfo.customerOperChargeAmt : '-'}
                    </td>
                </tr>
                <tr>
                    <td>
                        <FormItem noStyle name="customSettlement" valuePropName="checked">
                            <Radio
                                onChange={() => {
                                    changeSettlementType('customSettlement');
                                }}
                            ></Radio>
                        </FormItem>
                    </td>
                    <td>手工结算</td>
                    <td>-</td>
                    <td>
                        <FormItem noStyle name="chargePq">
                            <InputNumber
                                placeholder="请输入"
                                bordered={false}
                                type="number"
                                min={0}
                                precision={2}
                                step={0.01}
                            />
                        </FormItem>
                    </td>
                    <td>
                        <FormItem noStyle name="elecAmt">
                            <InputNumber
                                placeholder="请输入"
                                bordered={false}
                                type="number"
                                min={0}
                                precision={2}
                                step={0.01}
                            />
                        </FormItem>
                    </td>
                    <td>
                        <FormItem noStyle name="serviceAmt">
                            <InputNumber
                                placeholder="请输入"
                                bordered={false}
                                type="number"
                                min={0}
                                precision={2}
                                step={0.01}
                            />
                        </FormItem>
                    </td>
                    <td>
                        <FormItem noStyle name="incrementAmt">
                            <InputNumber
                                placeholder="请输入"
                                bordered={false}
                                type="number"
                                min={0}
                                precision={2}
                                step={0.01}
                            />
                        </FormItem>
                    </td>
                    <td>
                        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                            {({ getFieldValue }) => {
                                const elecAmt = getFieldValue('elecAmt');
                                const serviceAmt = getFieldValue('serviceAmt');
                                const incrementAmt = getFieldValue('incrementAmt');
                                let num = 0;
                                if (elecAmt) {
                                    num += parseFloat(elecAmt);
                                }
                                if (serviceAmt) {
                                    num += parseFloat(serviceAmt);
                                }
                                if (incrementAmt) {
                                    num += parseFloat(incrementAmt);
                                }
                                return Math.round(num * 100) / 100;
                            }}
                        </FormItem>
                    </td>
                </tr>
            </table>
            <br></br>
            <p>
                <FormItem label={<strong>处理说明</strong>} required>
                    <Space>
                        <span>
                            <FormItem
                                noStyle
                                name="handleRemark"
                                rules={[{ required: true, message: '请填写处理说明' }]}
                            >
                                <TextArea
                                    placeholder="请输入"
                                    rows={3}
                                    maxLength={255}
                                    style={{ width: '400px' }}
                                />
                            </FormItem>
                        </span>
                    </Space>
                </FormItem>
            </p>
            <FormItem noStyle name="cecBillTime"></FormItem>
            <FormItem noStyle name="cecBillDataId"></FormItem>
        </Fragment>
    );
};

const DetailView = (props) => {
    const { form, info } = props;

    return (
        <Fragment>
            <table className={pageStyles['details-table']}>
                <tr>
                    <th>选择</th>
                    <th>平账方式</th>
                    <th>订单号</th>
                    <th>电量</th>
                    <th>电费</th>
                    <th>服务费</th>
                    <th>附加费</th>
                    <th>订单金额</th>
                </tr>
                <tr>
                    <td>
                        <Radio checked={info.handleWay === HANDLEWAY_TYPES.BASE} disabled></Radio>
                    </td>
                    <td>以对账方为准</td>
                    <td>{info.baseEquipOrderNo || '-'}</td>
                    <td>{info.baseEquipChargePq >= 0 ? info.baseEquipChargePq : '-'}</td>
                    <td>{info.baseEquipElecAmt >= 0 ? info.baseEquipElecAmt : '-'}</td>
                    <td>{info.baseEquipServiceAmt >= 0 ? info.baseEquipServiceAmt : '-'}</td>
                    <td>{info.baseEquipIncrementAmt >= 0 ? info.baseEquipIncrementAmt : '-'}</td>
                    <td>{info.baseEquipChargeAmt >= 0 ? info.baseEquipChargeAmt : '-'}</td>
                </tr>
                <tr>
                    <td>
                        <Radio checked={info.handleWay === HANDLEWAY_TYPES.XDT} disabled></Radio>
                    </td>
                    <td>以新电途为准</td>
                    <td>-</td>
                    <td>{info.customerOperChargePq >= 0 ? info.customerOperChargePq : '-'}</td>
                    <td>{info.customerOperElecAmt >= 0 ? info.customerOperElecAmt : '-'}</td>
                    <td>{info.customerOperServiceAmt >= 0 ? info.customerOperServiceAmt : '-'}</td>
                    <td>
                        {info.customerOperIncrementAmt >= 0 ? info.customerOperIncrementAmt : '-'}
                    </td>
                    <td>{info.customerOperChargeAmt >= 0 ? info.customerOperChargeAmt : '-'}</td>
                </tr>
                <tr>
                    <td>
                        <Radio checked={info.handleWay === HANDLEWAY_TYPES.CUSTOM} disabled></Radio>
                    </td>
                    <td>手工结算</td>
                    <td>-</td>
                    <td>{info.chargePq >= 0 ? info.chargePq : '-'}</td>
                    <td>{info.elecAmt >= 0 ? info.elecAmt : '-'}</td>
                    <td>{info.serviceAmt >= 0 ? info.serviceAmt : '-'}</td>
                    <td>{info.incrementAmt >= 0 ? info.incrementAmt : '-'}</td>
                    <td>{info.orderAmt >= 0 ? info.orderAmt : '-'}</td>
                </tr>
            </table>
            <br></br>
            <p>
                <FormItem label={<strong>处理说明：</strong>}>
                    <Space>
                        <span>{info.handleRemark}</span>
                    </Space>
                </FormItem>
            </p>
        </Fragment>
    );
};

const ChargeJournalView = (props) => {
    const { info } = props;

    const operAbnoTypeName = useMemo(() => {
        if (info) {
            return info.operAbnoTypeName;
        }
        return '';
    }, [info]);

    const chargeList = useMemo(() => {
        if (info) {
            return info.chargeControlLogList;
        }
        return [];
    }, [info]);

    const stepList = useMemo(() => {
        if (isEmpty(chargeList)) {
            return [];
        }
        return chargeList.map((ele, index) => {
            const desc = [];
            if (ele.controlTerminalName) {
                desc.push(ele.controlTerminalName);
            }
            if (ele.chargeControlTypeName) {
                desc.push(ele.chargeControlTypeName);
            }
            if (ele.chargeControlMsg) {
                desc.push(ele.chargeControlMsg);
            }
            return (
                <Step
                    key={index}
                    title={
                        <Space>
                            <span>{ele.controlTime}</span>
                            <span>{desc.join('|')}</span>
                        </Space>
                    }
                    // description={`${ele.controlTerminalName || ''}${ele.chargeControlTypeName ||
                    //     ''}|${ele.chargeControlMsg || ''}`}
                />
            );
        });
    }, [chargeList]);
    return stepList.length > 0 ? (
        <Collapse defaultActiveKey={[]}>
            <Panel header="充电日志" key="1">
                <p>异常说明：{operAbnoTypeName}</p>
                <Steps progressDot current={chargeList.length} direction="vertical">
                    {stepList}
                </Steps>
            </Panel>
        </Collapse>
    ) : null;
};

const HandleRecordView = (props) => {
    const { info } = props;

    const recordList = useMemo(() => {
        if (info) {
            return info.billHandleRecordList;
        }
        return [];
    }, [info]);

    const renderRecordList = useMemo(() => {
        if (isEmpty(recordList)) {
            return [];
        }
        return recordList.map((ele, index) => {
            return (
                <Step
                    key={index}
                    title={`处理时间：${ele.handleTime}（本次共处理${ele.handleNum}条差异）`}
                    description={
                        <Card
                            bordered={false}
                            bodyStyle={{
                                padding: '10px 20px',
                            }}
                        >
                            <Descriptions column={2}>
                                <Descriptions.Item label="平账方式">
                                    {ele.handleWayName || ''}
                                </Descriptions.Item>
                                <Descriptions.Item label="对账时间">
                                    {ele.cecBillTime || ''}
                                </Descriptions.Item>
                                <Descriptions.Item label="平账订单电量">
                                    {ele.chargePq >= 0 ? ele.chargePq : ''}
                                </Descriptions.Item>
                                <Descriptions.Item label="对账批次号">
                                    {ele.cecBillNo || ''}
                                </Descriptions.Item>
                                <Descriptions.Item label="平账订单金额">
                                    {ele.orderAmt >= 0 ? ele.orderAmt : ''}
                                </Descriptions.Item>
                                <Descriptions.Item label="处理说明">
                                    {ele.handleRemark || ''}
                                </Descriptions.Item>
                            </Descriptions>
                        </Card>
                    }
                />
            );
        });
    }, [recordList]);
    return renderRecordList.length > 0 ? (
        <Collapse defaultActiveKey={[]}>
            <Panel header="历史处理记录" key="1">
                <Steps progressDot current={recordList.length} direction="vertical">
                    {renderRecordList}
                </Steps>
            </Panel>
        </Collapse>
    ) : null;
};

const DetailsModal = (props) => {
    const {
        dispatch,
        onClose,
        orderHandleLoading,
        detailsHandleLoading,
        contrastOrderManageModel: { waitHandleOrderInfo, handleOrderDetailInfo },
        onSubmit,
    } = props;
    const [form] = Form.useForm();

    useEffect(() => {
        if (waitHandleOrderInfo) {
            initFormData(waitHandleOrderInfo);
        }
    }, [waitHandleOrderInfo]);

    const initFormData = (info) => {
        const params = {};
        form.setFieldsValue(params);
    };

    const onFinish = (values) => {
        const params = {
            cecBillTime: values.cecBillTime,
            cecBillDataId: values.cecBillDataId,
            handleRemark: values.handleRemark || '',
        };

        if (values.baseSettlement) {
            params.handleWay = HANDLEWAY_TYPES.BASE;
        }
        if (values.xdtSettlement) {
            params.handleWay = HANDLEWAY_TYPES.XDT;
        }
        if (values.customSettlement) {
            params.handleWay = HANDLEWAY_TYPES.CUSTOM;
        }

        const { chargePq } = values;
        const { elecAmt } = values;
        const { serviceAmt } = values;
        const { incrementAmt } = values;

        if (params.handleWay === HANDLEWAY_TYPES.CUSTOM) {
            if (isEmpty(chargePq)) {
                message.error('请填写电量');
                return;
            }
            if (isEmpty(elecAmt)) {
                message.error('请填写电费');
                return;
            }
            if (isEmpty(serviceAmt)) {
                message.error('请填写服务费');
                return;
            }
            if (isEmpty(incrementAmt)) {
                message.error('请填写附加费');
                return;
            }

            params.chargePq = values.chargePq || '0';
            params.elecAmt = values.elecAmt || '0';
            params.serviceAmt = values.serviceAmt || '0';
            params.incrementAmt = values.incrementAmt || '0';
        }

        if (isEmpty(params.cecBillTime) || isEmpty(params.cecBillDataId)) {
            message.error('请选择差异产生时间');
            return;
        }

        // let num = 0;
        // if (chargePq) {
        //     num += parseFloat(chargePq);
        // }
        // if (elecAmt) {
        //     num += parseFloat(elecAmt);
        // }
        // if (serviceAmt) {
        //     num += parseFloat(serviceAmt);
        // }
        // if (incrementAmt) {
        //     num += parseFloat(incrementAmt);
        // }
        // params.orderAmt = Math.round(num * 100) / 100;

        onSubmit(params);
        // form.resetFields();
    };
    return (
        <Card
            bordered={false}
            bodyStyle={{
                padding: '0',
            }}
            loading={orderHandleLoading || detailsHandleLoading}
        >
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    baseSettlement: true,
                }}
                scrollToFirstError
            >
                {waitHandleOrderInfo ? (
                    <WaitDetailView info={waitHandleOrderInfo} form={form} />
                ) : (
                    <DetailView info={handleOrderDetailInfo} form={form} />
                )}
                <ChargeJournalView info={waitHandleOrderInfo || handleOrderDetailInfo} />

                <HandleRecordView info={waitHandleOrderInfo || handleOrderDetailInfo} />
                <br></br>
                {waitHandleOrderInfo ? (
                    <p className={baseStyles.textRight}>
                        <Space>
                            <Button onClick={onClose}>取消</Button>
                            <Button type="primary" htmlType="submit">
                                确认
                            </Button>
                        </Space>
                    </p>
                ) : null}
            </Form>
        </Card>
    );
};

const BalancePage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo },
        contrastOrderManageModel: { contrastOrderManageList, contrastOrderManageListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [showDetailsView, changeShowDetailsView] = useState(false);
    const [curEditItem, changeCurEditItem] = useState(null);

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: {},
                },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                orderNo: data.orderNo,
                platBillNo: data.platBillNo,
                handleWay: data.handleWay,
            };

            if (data.timeType === '01') {
                params.beginDate =
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '';
                params.endDate =
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '';
            }
            if (data.timeType === '02') {
                params.handleBeginDate =
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '';
                params.handleEndDate =
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '';
            }

            if (pageInfo.tabType !== CONTRAST_STATUS.ALL) {
                params.billHandleStatus = pageInfo.tabType;
            }

            dispatch({
                type: 'contrastOrderManageModel/getContrastOrderManageList',
                options: params,
            });
        } catch (error) {
            console.log(error);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                operId: data.operId,
                orderNo: data.orderNo,
                platBillNo: data.platBillNo,
                handleWay: data.handleWay,
            };

            if (data.timeType === '01') {
                params.beginDate =
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '';
                params.endDate =
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '';
            }
            if (data.timeType === '02') {
                params.handleBeginDate =
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '';
                params.handleEndDate =
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '';
            }

            if (pageInfo.tabType !== CONTRAST_STATUS.ALL) {
                params.billHandleStatus = pageInfo.tabType;
            }

            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: getBalanceListPath,
                options: params,
                columnsStr: columnsStrs,
            });
        } catch (error) {}
    };

    const closeDetailsView = () => {
        changeShowDetailsView(false);
        changeCurEditItem(null);
        dispatch({
            type: 'contrastOrderManageModel/updateOrderWaitHandleInfo',
            info: null,
        });
    };

    const openDetailsView = () => {
        changeShowDetailsView(true);
    };

    const handleOrderEvent = (item) => {
        changeCurEditItem(item);
        dispatch({
            type: 'contrastOrderManageModel/getOrderWaitHandleInfo',
            orderNo: item.orderNo,
        });

        openDetailsView();
    };

    const handleDetailsEvent = (item) => {
        changeCurEditItem(item);
        dispatch({
            type: 'contrastOrderManageModel/getHandleBillInfo',
            billHandleId: item.billHandleId,
        });

        openDetailsView();
    };

    const gotoOrderTransactionPage = (orderNo) => {
        history.push(`/financemanage/orderTransaction/list/details/${orderNo}`);
    };

    const columns = [
        {
            title: '对账时间',
            width: 240,
            dataIndex: 'cecBillTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '第三方订单号',
            width: 180,
            dataIndex: 'baseEquipOrderNo',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '180px' }} title={text}>
                        {text || '-'}
                    </div>
                );
                // return (
                //     <span
                //         className={styles['table-btn']}
                //         title={text}
                //         onClick={() => {
                //             gotoOrderTransactionPage(record.orderNo);
                //         }}
                //     >
                //         {text}
                //     </span>
                // );
            },
        },

        {
            title: (
                <span>
                    差异充电量
                    <Tooltip title="对账方电量 - 新电途电量">
                        <InfoCircleOutlined />
                    </Tooltip>
                </span>
            ),
            width: 140,
            dataIndex: 'pq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    差异订单金额
                    <Tooltip title="对账方订单金额 - 新电途订单金额">
                        <InfoCircleOutlined />
                    </Tooltip>
                </span>
            ),
            width: 160,
            dataIndex: 'orderAmt',
            render(text, record) {
                return <span title={text}>{text || '0'}</span>;
            },
        },
        {
            title: '平台批次号',
            width: 140,
            dataIndex: 'platBillNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '对账批次号',
            width: 140,
            dataIndex: 'cecBillNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '平账处理时间',
            width: 140,
            dataIndex: 'handleTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '处理结果',
            width: 140,
            dataIndex: 'handleWayName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '处理说明',
            width: 140,
            dataIndex: 'handleRemark',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '处理人',
            width: 140,
            dataIndex: 'handlePerson',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'billHandleStatusName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                if (record.billHandleStatus === '0') {
                    return (
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                handleOrderEvent(record);
                            }}
                        >
                            处理
                        </span>
                    );
                }

                return (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            handleDetailsEvent(record);
                        }}
                    >
                        详情
                    </span>
                );
            },
        },
    ];
    const submitHandleBillEvent = async (params) => {
        try {
            const options = {
                ...params,
                orderNo: curEditItem.orderNo,
            };

            await sendHandleBillApi(options);

            await searchData();
            message.success('处理成功');
            closeDetailsView();
        } catch (error) {}
    };

    return (
        <Fragment>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={CONTRAST_STATUS.ALL} />
                <TabPane tab="已处理" key={CONTRAST_STATUS.DONE} />
                <TabPane tab="待处理" key={CONTRAST_STATUS.WILL} />
            </Tabs>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={contrastOrderManageList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: contrastOrderManageListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <Modal
                title={'平账处理'}
                width={1050}
                visible={showDetailsView}
                onCancel={closeDetailsView}
                footer={null}
                destroyOnClose
                maskClosable={false}
            >
                <DetailsModal
                    onSubmit={submitHandleBillEvent}
                    {...props}
                    onClose={closeDetailsView}
                />
            </Modal>
        </Fragment>
    );
};

export default connect(({ contrastOrderManageModel, global, loading }) => ({
    contrastOrderManageModel,
    global,
    listLoading: loading.effects['contrastOrderManageModel/getContrastOrderManageList'],
    orderHandleLoading: loading.effects['contrastOrderManageModel/getOrderWaitHandleInfo'],
    detailsHandleLoading: loading.effects['contrastOrderManageModel/getHandleBillInfo'],
}))(BalancePage);
