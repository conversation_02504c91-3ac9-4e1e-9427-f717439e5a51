import {
    getBalanceListApi,
    getOrderWaitHandleBillApi,
    getHandleBillInfoApi,
    getCecBillRecordApi,
} from '@/services/FinanceManage/ContrastOrderManageApi';

const ContrastOrderManageModel = {
    namespace: 'contrastOrderManageModel',
    state: {
        contrastOrderManageList: [], // 平账列表
        contrastOrderManageListTotal: 0, // 平账列表总条数
        waitHandleOrderInfo: null, // 订单待平账记录信息
        handleOrderDetailInfo: null, // 订单待平账记录信息

        cecBillRecordList: [], // 记录列表
        cecBillRecordListTotal: 0, // 记录列表总条数
    },
    effects: {
        /**
         * 平账列表
         */
        *getContrastOrderManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBalanceListApi, options);

                yield put({
                    type: 'updateContrastOrderManageList',
                    list,
                    total,
                });
            } catch (error) {
                console.log(error);
            }
        },
        /**
         * 查询订单待平账记录
         */
        *getOrderWaitHandleInfo({ orderNo }, { call, put, select }) {
            try {
                const { data } = yield call(getOrderWaitHandleBillApi, orderNo);

                yield put({
                    type: 'updateOrderWaitHandleInfo',
                    info: data,
                });
            } catch (error) {
                console.log(error);
            }
        },
        /**
         * 查询平账记录详情
         */
        *getHandleBillInfo({ billHandleId }, { call, put, select }) {
            try {
                const { data } = yield call(getHandleBillInfoApi, billHandleId);

                yield put({
                    type: 'updateHandleOrderInfo',
                    info: data,
                });
            } catch (error) {
                console.log(error);
            }
        },
        /**
         * 平账列表
         */
        *getCecBillRecordList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getCecBillRecordApi, options);

                yield put({
                    type: 'updateCecBillRecordList',
                    list,
                    total,
                });
            } catch (error) {
                console.log(error);
            }
        },
    },
    reducers: {
        updateContrastOrderManageList(state, { list, total }) {
            return {
                ...state,
                contrastOrderManageList: list,
                contrastOrderManageListTotal: total,
            };
        },
        updateOrderWaitHandleInfo(state, { info }) {
            return {
                ...state,
                waitHandleOrderInfo: info,
            };
        },
        updateHandleOrderInfo(state, { info }) {
            return {
                ...state,
                handleOrderDetailInfo: info,
            };
        },
        updateCecBillRecordList(state, { list, total }) {
            return {
                ...state,
                cecBillRecordList: list,
                cecBillRecordListTotal: total,
            };
        },
    },
};
export default ContrastOrderManageModel;
