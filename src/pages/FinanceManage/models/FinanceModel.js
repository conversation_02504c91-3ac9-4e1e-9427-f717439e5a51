import { getPartnerOptionsApi } from '@/services/FinanceManage/FinanceManageApi';

const financeModel = {
    namespace: 'financeModel',
    state: {
        tariffTypeList: [], // 付费模式
        channelList: [], // 订单渠道
    },
    effects: {
        /**
         *
         */
        *initFinanceOptions(_, { call, put, select }) {
            const {
                data: { tariffTypeList, channelList },
            } = yield call(getPartnerOptionsApi);

            yield put({
                type: 'updateFinanceOptions',
                tariffTypeList,
                channelList,
            });
        },
    },
    reducers: {
        updateFinanceOptions(state, { tariffTypeList, channelList }) {
            return {
                ...state,
                tariffTypeList,
                channelList,
            };
        },
    },
};
export default financeModel;
