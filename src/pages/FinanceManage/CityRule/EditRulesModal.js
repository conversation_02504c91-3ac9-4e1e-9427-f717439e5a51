import { ExclamationCircleOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import {
    Button,
    Card,
    Form,
    Select,
    Modal,
    Alert,
    DatePicker,
    Space,
    message,
    Tooltip,
    Radio,
    Input,
    Popconfirm,
    Spin,
    Switch,
} from 'antd';
import { isNumber } from 'lodash';
import moment from 'moment';
import React, { Fragment, useState, useEffect, useMemo, useRef, useImperativeHandle } from 'react';
import { connect } from 'umi';

import styles from './style.less';
import { RuleFormItem as ProfitRuleFormItem } from '../ProfitRule/components/RuleFormItem';
import PurchaseRuleFormItem, { checkRuleInfo } from '../Purchase/components/PurchaseRuleFormItem';
import { MAX_PRECISION } from '../Purchase/PurchaseConfig';
import SlottingTooltip from '../Purchase/components/SlottingTooltip';
import SlottingRuleFormItem, {
    checkRuleValue,
    defaultRuleValue,
} from '../Purchase/components/SlottingRuleFormItem';
import { CollectTypes, PURCHASE_RULE_TYPES } from '../Purchase/PurchaseConfig';

import { initialRuleList } from '../ProfitRule/components/RuleModelFormItem';
import OperSelectItem from '@/components/OperSelectItem';

import {
    saveprofitruleApi,
    addStationTemplateApi,
    queryProfitrulePremiumFlag,
} from '@/services/FinanceManage/ProfitRuleApi';
import { resertStationRuleByIdsApi, getRuleInfoApi } from '@/services/FinanceManage/ProfitRuleApi';
import { getStationCityProfitRuleApi } from '@/services/FinanceManage/CityRuleApi';

import {
    getStationCityListApi,
    getOperCityRuleIdApi,
    saveCityProfitRuleApi,
    deleteCityProfitRuleApi,
} from '@/services/FinanceManage/CityRuleApi';
import { RULE_TYPES } from '@/config/declare';
import { isEmpty, checkClosePremiumCommon } from '@/utils/utils';
import TablePro from '@/components/TablePro';
import RuleStationList from './components/RuleStationList';
import RuleStationModal from './components/RuleStationModal';
import SelectRuleModal from '../ProfitRule/components/SelectRuleModal';
import SelectPurchaseTemplateModal from '../Purchase/components/SelectRuleModal';
import BuyPremiumItem from '../Purchase/components/PremiumSharingItem';
import SharePremiumItem from '../ProfitRule/components/PremiumSharingItem';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

const PAGE_TYPE = {
    PROFIT: '01', // 配置分润
    PURCHASE: '02', // 配置购电规则
    CITY: '03', // 配置城市
};

const EDIT_TYPE = {
    ADD: '0', // 新增
    EDIT: '1', // 编辑
    CHOOSE: '2', // 选择配置
    DETAIL: '3',
};

const modelFormLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};

export const getProfitRuleTypesFunction = (profitRuleList, isPurchse) => {
    const filterRuleList = profitRuleList.filter((ele) => ele && !isEmpty(ele.ruleModel)) || [];

    const profitRuleTypes = filterRuleList.map((ele) => {
        let options = {
            ...ele,
            ruleModel: String(ele.ruleModel),
        };
        if (ele.agreeType instanceof Array && ele.agreeType.length === 1) {
            if (isPurchse) {
                let maxValue = Number(ele.ruleValue);

                if (ele.ruleType === PURCHASE_RULE_TYPES.DISCOUNT) {
                    maxValue =
                        Math.round((10 - (Number(ele.ruleValue) || 0)) * 10 * 10 ** MAX_PRECISION) /
                        10 ** MAX_PRECISION;
                }
                const curLongRuleValue =
                    (ele.curLongRuleValue && Number(ele.curLongRuleValue)) || 0;
                const curShortRuleValue =
                    (ele.curShortRuleValue && Number(ele.curShortRuleValue)) || 0;
                const surplusValue =
                    Math.round(
                        (maxValue - curLongRuleValue - curShortRuleValue) * 10 ** MAX_PRECISION,
                    ) /
                    10 ** MAX_PRECISION;

                debugger;
                if (ele.agreeType.includes('01')) {
                    options.longRuleValue = surplusValue;
                    options.shortRuleValue = undefined;
                }
                if (ele.agreeType.includes('02')) {
                    options.shortRuleValue = surplusValue;
                    options.longRuleValue = undefined;
                }
            } else {
                if (ele.agreeType.includes('01')) {
                    options.longRuleValue = ele.ruleValue;
                    options.shortRuleValue = undefined;
                }

                if (ele.agreeType.includes('02')) {
                    options.shortRuleValue = ele.ruleValue;
                    options.longRuleValue = undefined;
                }
            }
        }

        return options;
    });

    return profitRuleTypes;
};

// 场站规则列表
const EditRulesModal = (props) => {
    const {
        dispatch,
        initRef,
        onEditFinish,
        selectTemplateListLoading,
        checkCityAlreadySetLoading, // 检查当前城市是否已配置规则，如果有，则提醒编辑
        profitRuleModel,
        global: {
            operatorList,
            codeInfo: { operationWay },
        },
        addCityRuleEvent,
        workorderEvent,
        path,
        cooperationPlatform,
    } = props;

    const [showEditView, toggleEditView] = useState(false); // 编辑弹窗状态
    const [editItems, changeEditItems] = useState([]); // 多个编辑项
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [infoLoading, changeInfoLoading] = useState(false);
    const [pageType, updatePageType] = useState(); // 当前页面所属方
    const [editType, updateEditType] = useState(EDIT_TYPE.DETAIL); // 编辑状态
    const [operId, updateOperId] = useState(); //运营商id
    const [premiumFlagEnable, setPremiumFlagEnable] = useState(false);

    const { templateSelectList, selectTemplateInfo, editRuleInfo } = profitRuleModel;
    const [editForm] = Form.useForm();
    const selectPurchaseTemplateRef = useRef();
    const selectTemplateRef = useRef();
    const stationRef = useRef();
    // 主要信息以editRuleInfo.list的首个元素为准
    const mainInfo = editRuleInfo?.list?.[0];
    const cityWatch = Form.useWatch('city', editForm);
    useEffect(async () => {
        if (showEditView && cityWatch) {
            let cooperationType;
            if (path == 'profitRule') {
                cooperationType = '01';
            } else if (path == 'purchase') {
                cooperationType = '02';
            }
            const params = {
                profitRuleType: '03',
                operId,
                city: cityWatch,
                cooperationType,
                cooperationPlatform,
            };
            const result = await queryProfitrulePremiumFlag(params);
            if (result?.data?.premiumFlag === '01') {
                setPremiumFlagEnable(true);
            } else {
                setPremiumFlagEnable(false);
            }
        }
    }, [showEditView, path, operId, cityWatch]);

    useEffect(() => {
        if (!operationWay) {
            dispatch({
                type: 'global/initCode',
                code: 'operationWay',
            });
        }
    }, []);

    useImperativeHandle(initRef, () => ({
        add: ({ operId, type }) => {
            // 新建
            updatePageType(type);
            editForm?.setFieldsValue({ operId });
            initSlottingData(operId);
            updateEditType(EDIT_TYPE.ADD);
            updateOperId(operId);
            toggleEditView(true);
        },
        edit: ({ item, operId, type }) => {
            // 开始编辑,传入编辑的item，可批量编辑
            if (Array.isArray(item)) {
                if (item.length == 0) {
                    message.error('请选择要编辑的规则');
                    return;
                }

                changeEditItems([...item]);
                if (item.length == 1) {
                    initData(item[0], operId, type);
                }
            } else {
                if (!item) {
                    message.error('请选择要编辑的规则');
                    return;
                }
                changeEditItems([item]);
                initData(item, operId, type);
            }
            updatePageType(type);
            editForm?.setFieldsValue({ operId });
            updateEditType(EDIT_TYPE.EDIT);
            updateOperId(operId);
            toggleEditView(true);
        },
        choose: ({ item, operId, type }) => {
            // 开始编辑,传入编辑的item，可批量编辑
            if (Array.isArray(item)) {
                if (item.length == 0) {
                    message.error('请选择要配置的规则');
                    return;
                }

                changeEditItems([...item]);
                if (item.length == 1) {
                    initData(item[0], operId, type);
                }
            } else {
                if (!item) {
                    message.error('请选择要配置的规则');
                    return;
                }
                changeEditItems([item]);
                initData(item, operId, type);
            }

            updatePageType(type);
            editForm?.setFieldsValue({ operId });
            updateEditType(EDIT_TYPE.CHOOSE);
            updateOperId(operId);
            toggleEditView(true);
        },
        close: () => {
            // 关闭规则配置页面
            toggleEditView(false);
            updateOperId(undefined);
            editForm.resetFields();
            changeEditItems([]);
            updateEditType(EDIT_TYPE.DETAIL);
            updatePageType(undefined);
            updateAllStations([]);

            setCityConfirmTitle('');
            changeCityConfirmVisible(false);
            setCityConfirmRuleId(undefined);

            dispatch({
                type: 'profitRuleModel/updateProfitRuleInfo',
                editRuleInfo: {},
            });
            dispatch({
                type: 'profitRuleModel/updateSelectTemplateInfo',
                info: null,
            });
        },
    }));

    const initData = async (item, operId, type) => {
        try {
            changeInfoLoading(true);

            let cooperationType;
            if (path == 'profitRule') {
                cooperationType = '01';
            } else if (path == 'purchase') {
                cooperationType = '02';
            }
            const params = {
                ruleId: item.ruleId,
                city: item.city,
                operId,
                profitRuleType: type,
                cooperationType,
                cooperationPlatform,
            };
            if (type === PAGE_TYPE.PURCHASE) {
                params.stationId = item.stationId;
            }
            await dispatch({
                type: 'profitRuleModel/getRuleInfo',
                params,
            });
            changeInfoLoading(false);
        } catch (error) {
            changeInfoLoading(false);
        }
    };

    const initSlottingData = async (operId) => {
        try {
            //初始化通用的通道配置

            if (path != 'purchase') {
                return;
            }
            const params = {
                operId,
                profitRuleType: '01',
                cooperationType: '02',
                cooperationPlatform,
            };

            const { data } = await getRuleInfoApi(params);
            const parentItem = data?.list?.[0];
            if (parentItem) {
                const { profitSlottingRuleList } = parentItem;
                if (profitSlottingRuleList instanceof Array) {
                    let profitSlottingRules = {
                        collectType: CollectTypes.DETAIL,
                        list: profitSlottingRuleList,
                    };
                    editForm.setFieldsValue({ profitSlottingRules });
                }

                return parentItem;
            }

            return null;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (showEditView && operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, [showEditView]);

    // 城市相关的属性
    const [cityList, changeCityList] = useState([]);
    const [cityLoading, changeCityLoading] = useState(false);
    const [cityConfirmVisible, changeCityConfirmVisible] = useState(false);
    const [cityConfirmTitle, setCityConfirmTitle] = useState('');
    const [cityConfirmRuleId, setCityConfirmRuleId] = useState(undefined);
    useEffect(() => {
        if (pageType == PAGE_TYPE.CITY && !cityList.length) {
            initCityEvent();
        }
    }, [pageType]);

    useEffect(() => {
        changeCityConfirmVisible(cityConfirmTitle?.length > 0);
    }, [cityConfirmTitle]);

    const initCityEvent = async () => {
        changeCityLoading(true);
        try {
            let cooperationType;
            if (path == 'profitRule') {
                cooperationType = '01';
            } else if (path == 'purchase') {
                cooperationType = '02';
            }
            const {
                data: { cityList: cityAndStationList },
            } = await getStationCityListApi({ operId, cooperationType, cooperationPlatform });
            changeCityList(cityAndStationList);
            changeCityLoading(false);
        } catch (error) {
        } finally {
            changeCityLoading(false);
        }
    };

    const cityOptions = useMemo(() => {
        const list =
            cityList?.map((ele) => (
                <Option key={ele.city} value={ele.city}>
                    {ele.cityName}
                </Option>
            )) || [];
        return list;
    }, [cityList]);

    // CHOOSE的场景
    // 读取当前规则是否配置了城市
    const [cityRuleFlag, updateCityRuleFlag] = useState(undefined);
    useEffect(() => {
        if (!editItems || editItems.length != 1) {
            updateCityRuleFlag(undefined);
        } else if (!cityRuleFlag) {
            // 读取城市规则是否有配置
            initCityFlag();
        }
    }, [editItems]);

    // 查询运营商站点是否配置了城市分润规则
    const initCityFlag = async () => {
        try {
            const {
                data: { flag },
            } = await getStationCityProfitRuleApi({
                operId,
                stationId: editItems[0].stationId,
                cooperationPlatform,
            });
            updateCityRuleFlag(flag);
        } catch (error) {
            updateCityRuleFlag(undefined);
        }
    };

    const resetEvent = (items) => {
        confirm({
            title: '确认重置场站规则？',
            icon: <ExclamationCircleOutlined />,
            content: '重置后场站规则将还原为通用规则',
            async onOk() {
                try {
                    const stationIds = [];
                    for (const item of items) {
                        stationIds.push(item.stationId);
                    }
                    await resertStationRuleByIdsApi({
                        stationIds: stationIds.join(','),
                        cooperationPlatform,
                    });
                    message.success('重置成功');
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const selectTemplateDetailList = useMemo(() => {
        if (selectTemplateInfo) {
            return selectTemplateInfo.detailList;
        }
    }, [selectTemplateInfo]);

    const selectTemplateOptions = useMemo(() => {
        return templateSelectList.map((ele) => {
            return (
                <Option value={ele.templateId} key={ele.templateId}>
                    {ele.name}
                </Option>
            );
        });
    }, [templateSelectList]);

    useEffect(() => {
        loadSelectTemplateList();
    }, []);

    const loadSelectTemplateList = () => {
        let cooperationType;
        if (path == 'profitRule') {
            cooperationType = '01';
        } else if (path == 'purchase') {
            cooperationType = '02';
        }
        dispatch({
            type: 'profitRuleModel/getSelectTemplateList',
            cooperationType,
        });
    };

    // 数据初始化
    useEffect(() => {
        const params = {};
        if (mainInfo) {
            const {
                effTime,
                expTime,
                profitRuleTypeList,
                profitSlottingRuleList,
                profitRuleType,
                premiumFlag,
            } = mainInfo;
            params.ruleRel = mainInfo.ruleRel || '';
            params.ruleFlag = mainInfo.ruleFlag || '0';
            params._city = params.city = mainInfo.city;
            params.cityRuleStationIds = mainInfo.cityRuleStationIds;
            params.ruleRange = mainInfo.ruleRange;
            params.useTemplate = mainInfo.useTemplate || '01';
            params.premiumFlag = premiumFlag;
            params.effTime = effTime ? moment(effTime) : moment().add(5, 'minutes');
            params.expTime = expTime ? moment(expTime) : null;
            if (expTime) {
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }
            if (premiumFlag === '1') {
                params.divideDTOList = mainInfo.divideList?.map((v) => ({
                    divideType: v.divideType,
                    divideValue: v.divideValue,
                }));
            }
            if (profitRuleTypeList) {
                if (path == 'profitRule') {
                    params.profitRuleList = initialRuleList.map((ele) => {
                        const findItem = profitRuleTypeList.find(
                            (item) => item.ruleModel === ele.ruleModel,
                        );

                        if (findItem) {
                            return {
                                ruleModel: [findItem.ruleModel],
                                ruleType: findItem.ruleType,
                                ruleValue: parseFloat(findItem.ruleValue),
                                agreeType: findItem.agreeType,
                                longRuleValue: parseFloat(findItem.longRuleValue),
                                shortRuleValue: parseFloat(findItem.shortRuleValue),
                            };
                        }
                        return null;
                    });
                } else if (path == 'purchase') {
                    params.profitRuleTypes = profitRuleTypeList;
                }
            }
            if (path == 'purchase' && profitSlottingRuleList instanceof Array) {
                params.profitSlottingRules = {
                    collectType: CollectTypes.DETAIL,
                    list: profitSlottingRuleList,
                };
            }
            if (mainInfo.templateId) {
                params.useTemplate = '02';
                params.templateId = mainInfo.templateId;
                changeSelectEvent(mainInfo.templateId);
            }

            if (profitRuleType) {
                params.editRuleType = profitRuleType;
                if (profitRuleType === RULE_TYPES.COMMON) {
                    params.useTemplate = '01';
                }
            }
            editForm.setFieldsValue(params);
        } else {
            editForm.resetFields();
            editForm.setFieldsValue({ operId });
        }
    }, [mainInfo]);

    // 数据提交
    const onFinish = async (values) => {
        if (
            (pageType == PAGE_TYPE.PROFIT || pageType == PAGE_TYPE.PURCHASE) &&
            values.editRuleType === RULE_TYPES.COMMON
        ) {
            if (editItems?.[0]?.profitRuleType === RULE_TYPES.STATION) {
                resetEvent(editItems);
                return;
            }
            initRef?.current?.close();
            return;
        }

        if (submitLoading) {
            return;
        }

        let params = {
            saveType: 'send',
            cooperationPlatform,
        };

        const ids = editItems.map((ele) => ele.stationId);
        params.stationIds = ids.join(',');
        params.operId = operId;
        params.city = values.city;
        params.ruleRange = values.ruleRange;
        params.useTemplate = values.useTemplate;
        params.templateId = values.templateId;
        params.premiumFlag = values.premiumFlag;
        if (values.premiumFlag === '1') {
            params.divideDTOList = JSON.stringify(values.divideDTOList);
        }
        // 以上都是公共参数，归档一下

        if (pageType == PAGE_TYPE.PROFIT) {
            params.cooperationType = '01';
        } else if (pageType == PAGE_TYPE.PURCHASE) {
            params.cooperationType = '02';
        } else if (pageType == PAGE_TYPE.CITY) {
            params.profitRuleType = '03'; // 03-城市
        }

        try {
            changeSubmitLoading(true);
            if (params.templateId && pageType != PAGE_TYPE.CITY) {
                //通道费收取方式
                const profitSlottingRuleInfo = values.profitSlottingRules;

                const profitSlottingRules =
                    profitSlottingRuleInfo?.list?.filter((ele) => ele.slottingFlag) || [];
                params.profitSlottingRules = JSON.stringify(profitSlottingRules);
                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    initRef?.current?.close();
                    return;
                }
                // 场站配置模板
                await addStationTemplateApi(params);
            } else {
                // 场站配置规则
                if (pageType == PAGE_TYPE.CITY) {
                    const profitBoList = [];
                    if (params.ruleRange == '02') {
                        // 部分
                        allStations.map((ele) => {
                            const resParams = {
                                ...params,
                                ruleRel: ele.ruleRel,
                                ruleFlag: ele.ruleFlag,
                                ruleId: ele.ruleId,
                                stationIds:
                                    ele.cityRuleStationIds
                                        ?.map((stationEle) => stationEle.stationId)
                                        ?.join(',') || ele.stationIds,
                                stationInfo: ele.stationInfo || undefined,
                                delScopeInfo: ele.delScopeInfo || undefined,
                            };
                            if (path == 'purchase' && ele.profitRuleTypes) {
                                resParams.ruleFlag = '1';
                                const profitRuleList =
                                    typeof ele.profitRuleTypes == 'string'
                                        ? JSON.parse(ele.profitRuleTypes)
                                        : ele.profitRuleTypes;
                                resParams.profitRuleTypes = getProfitRuleTypesFunction(
                                    profitRuleList,
                                    true,
                                );
                            } else if (ele.ruleFlag === '1') {
                                const profitRuleList =
                                    ele.profitRuleList || ele.profitRuleTypeList || [];
                                resParams.profitRuleTypes = getProfitRuleTypesFunction(
                                    profitRuleList,
                                    false,
                                );
                            }

                            if (path == 'purchase') {
                                const profitSlottingRuleInfo = ele.profitSlottingRules;

                                const profitSlottingRules =
                                    profitSlottingRuleInfo?.list?.filter(
                                        (element) =>
                                            element.slottingFlag ||
                                            element?.slottingFlag === undefined,
                                    ) || [];
                                resParams.profitSlottingRules = profitSlottingRules;
                            }

                            resParams.useTemplate = ele.useTemplate;
                            resParams.templateId = ele.templateId;
                            // 全部
                            resParams.effTime =
                                ele.effTime?.format?.('YYYY-MM-DD HH:mm:ss') || ele.effTime;
                            if (ele.expFlag === EXP_FLAG_TYPES.LONG) {
                                resParams.expTime = null;
                            } else if (ele.expTime) {
                                resParams.expTime =
                                    ele.expTime?.format?.('YYYY-MM-DD HH:mm:ss') || ele.expTime;
                            }
                            profitBoList.push(resParams);
                        });
                    } else {
                        // 全部，使用外层配置
                        params.useTemplate = values.useTemplate;
                        params.templateId = values.templateId;
                        // 全部
                        const ruleFlag = editForm.getFieldValue('ruleFlag');
                        params.ruleFlag = values.ruleFlag;
                        params.ruleRel = values.ruleRel;
                        params.effTime = values.effTime?.format('YYYY-MM-DD HH:mm:ss');
                        if (path == 'purchase' && values.profitRuleTypes) {
                            params.ruleFlag = '1';
                            params.profitRuleTypes = getProfitRuleTypesFunction(
                                values.profitRuleTypes,
                                true,
                            );
                        } else if (ruleFlag === '1') {
                            const profitRuleList = editForm.getFieldValue('profitRuleList') || [];
                            params.profitRuleTypes = getProfitRuleTypesFunction(
                                profitRuleList,
                                false,
                            );
                        }
                        if (path == 'purchase') {
                            const profitSlottingRuleInfo = values.profitSlottingRules;

                            const profitSlottingRules =
                                profitSlottingRuleInfo?.list?.filter(
                                    (element) => element.slottingFlag,
                                ) || [];
                            params.profitSlottingRules = profitSlottingRules;
                        }

                        if (values.expFlag === EXP_FLAG_TYPES.LONG) {
                            params.expTime = null;
                        } else if (values.expTime) {
                            params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
                        }
                        params.ruleId = editItems?.[0]?.ruleId;
                        profitBoList.push(params);
                    }
                    params = { profitBoList: JSON.stringify(profitBoList) };
                }
                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    initRef?.current?.close();
                    return;
                }
            }

            if (pageType == PAGE_TYPE.CITY) {
                await saveCityProfitRuleApi(params);
            } else {
                await saveprofitruleApi(params);
            }

            message.success('保存成功');
            initRef?.current?.close();
            onEditFinish && onEditFinish();
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    // 改变模板
    const changeSelectEvent = (value) => {
        dispatch({
            type: 'profitRuleModel/getSelectTemplateInfo',
            templateId: value,
        });
        editForm.setFieldValue('templateId', value);
        editForm.validateFields(['useTemplate']);
    };

    // 编辑页方法
    const disabledDate = (current) => {
        // Can not select days before today and today
        return current && current < moment().subtract(1, 'days').endOf('day');
    };

    // 规则场站
    const [allStations, updateAllStations] = useState([]);
    useEffect(() => {
        if (mainInfo?.ruleRange == '02' && editRuleInfo?.list?.length) {
            // 如果默认是部分，维护进规则场站列表
            let newList = [...editRuleInfo.list];

            newList.forEach((item) => {
                if (item?.profitRuleTypes) {
                    item.profitRuleTypes =
                        (typeof item.profitRuleTypes === 'string' &&
                            JSON.parse(item.profitRuleTypes)) ||
                        item.profitRuleTypes;
                } else if (item?.profitRuleTypeList) {
                    if (path == 'profitRule') {
                        item.profitRuleList = initialRuleList.map((ele) => {
                            const findItem = item.profitRuleTypeList.find(
                                (item) => item.ruleModel === ele.ruleModel,
                            );
                            if (findItem) {
                                return {
                                    ruleModel: [findItem.ruleModel],
                                    ruleType: findItem.ruleType,
                                    ruleValue: parseFloat(findItem.ruleValue),
                                    agreeType: findItem.agreeType,
                                    longRuleValue: parseFloat(findItem.longRuleValue),
                                    shortRuleValue: parseFloat(findItem.shortRuleValue),
                                };
                            }
                            return null;
                        });
                    } else if (path == 'purchase') {
                        item.profitRuleTypes = item.profitRuleTypeList;
                    }
                }
                if (path == 'purchase' && item.profitSlottingRuleList) {
                    item.profitSlottingRules = {
                        collectType: CollectTypes.DETAIL,
                        list: item.profitSlottingRuleList,
                    };
                }
            });

            updateAllStations(newList);
        }
    }, [mainInfo?.ruleRange]);

    const ruleStationRef = useRef();
    const [ruleStationEditItem, updateRuleStationEditItem] = useState(); // 记录当前的编辑项
    const addStationEvent = () => {
        // 所有已选场站不可被添加
        const disabledStationIds = [];
        allStations?.forEach(
            (ele) =>
                (ele?.cityRuleStationIds?.map &&
                    ele.cityRuleStationIds.map((cityEle) =>
                        disabledStationIds.push(cityEle.stationId),
                    )) ||
                (ele?.stationIds?.split &&
                    disabledStationIds.push(...ele?.stationIds?.split?.(','))),
        );
        updateRuleStationEditItem(undefined);
        ruleStationRef.current.udpateDisabledStationIds([...disabledStationIds]);
        ruleStationRef.current.show();
    };

    const editStationEvent = (item, index) => {
        updateRuleStationEditItem(item);
        const disabledStationIds = [];
        allStations?.forEach((ele, stationIndex) => {
            if (stationIndex != index) {
                (ele?.cityRuleStationIds?.map &&
                    ele.cityRuleStationIds.map((cityEle) =>
                        disabledStationIds.push(cityEle.stationId),
                    )) ||
                    (ele?.stationIds?.split &&
                        disabledStationIds.push(...ele?.stationIds?.split?.(',')));
            }
        });
        // 更新禁止选择的站点
        ruleStationRef.current.udpateDisabledStationIds([...disabledStationIds]);
        ruleStationRef.current.show(item);
    };

    const deleteStationEvent = async (item, index) => {
        const deleteEvent = (deleteIndex, shouldRefresh) => {
            allStations.splice(deleteIndex, 1);
            updateAllStations([...allStations]);
            message.success('删除成功');
            // 城市规则列表刷新
            shouldRefresh && onEditFinish?.();
        };
        // 如果有id，先调用删除接口，再本地删除
        if (item.ruleId) {
            if (workorderEvent) {
                deleteEvent(index, true);
            } else {
                confirm({
                    title: '确定要删除吗？',
                    icon: <ExclamationCircleOutlined />,
                    content: '本操作将会直接从城市规则中删除此规则，请确认！',
                    async onOk() {
                        try {
                            await deleteCityProfitRuleApi({ ruleId: item.ruleId });
                            deleteEvent(index, true);
                        } catch (error) {}
                    },
                    onCancel() {
                        console.log('Cancel');
                    },
                });
            }
        } else {
            deleteEvent(index, false);
        }
    };

    const canISetEffTime = useMemo(() => {
        if (mainInfo) {
            const { effTime } = mainInfo;
            if (moment(effTime).isBefore(moment())) {
                return false;
            }
        }
        return true;
    }, [mainInfo]);

    const templateColumns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: path == 'purchase' ? '购电结算方式' : '分润方式',
            width: 240,
            dataIndex: 'ruleName',
            render(text, record) {
                return (
                    <span
                        title={text}
                        style={{
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-all',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '生效时间',
            width: 160,
            dataIndex: 'effTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '失效时间',
            width: 160,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                        {(text?.length && text) || record.maxExpTime}
                    </span>
                );
            },
        },
    ];

    return (
        <Modal
            title="规则配置"
            width={1100}
            visible={showEditView}
            onCancel={() => {
                initRef?.current?.close();
            }}
            footer={null}
            destroyOnClose
            maskClosable={false}
        >
            <Card loading={infoLoading} bordered={false} bodyStyle={{ padding: 0 }}>
                <Form
                    form={editForm}
                    {...modelFormLayout}
                    onFinish={onFinish}
                    initialValues={{
                        useTemplate: '01',
                        effTime:
                            mainInfo && mainInfo.effTime
                                ? moment(mainInfo.effTime)
                                : moment().add(5, 'minutes'),

                        expTime: '',
                    }}
                    scrollToFirstError
                >
                    {editItems.length > 1 ? (
                        <Alert
                            message="提交后将直接替换所有选中场站的结算规则及模板"
                            type="warning"
                            showIcon
                        />
                    ) : null}

                    {((pageType == PAGE_TYPE.CITY || pageType == PAGE_TYPE.PURCHASE) && (
                        <>
                            <OperSelectItem
                                label="运营商名称"
                                name="operId"
                                validateTrigger={['onChange', 'onBlur']}
                                operatorList={operatorList}
                                disabled
                                formItemLayout={formItemLayout}
                            />

                            <FormItem
                                shouldUpdate={(prevValues, curValues) =>
                                    prevValues.operId !== curValues.operId
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const operId = getFieldValue('operId');
                                    return (
                                        <FormItem
                                            label="运营商编号:"
                                            name="operId"
                                            {...formItemFixedWidthLayout}
                                        >
                                            <span>{operId}</span>
                                        </FormItem>
                                    );
                                }}
                            </FormItem>
                        </>
                    )) ||
                        null}

                    {(pageType == PAGE_TYPE.CITY && (
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.city !== curValues.city ||
                                prevValues._city !== curValues._city ||
                                prevValues.ruleRange !== curValues.ruleRange
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const _city = getFieldValue('_city');
                                const ruleRange = getFieldValue('ruleRange');
                                return (
                                    <>
                                        <FormItem
                                            label="城市"
                                            name="_city"
                                            required
                                            rules={[
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        const city = getFieldValue('city');
                                                        if (!city) {
                                                            return Promise.reject('请选择生效城市');
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            {...formItemFixedWidthLayout}
                                        >
                                            <Popconfirm
                                                title={cityConfirmTitle}
                                                visible={cityConfirmVisible}
                                                placement="right"
                                                onConfirm={() => {
                                                    setCityConfirmTitle('');
                                                    changeCityConfirmVisible(false);
                                                    const _city = editForm.getFieldValue('_city');
                                                    editForm.setFieldsValue({ city: _city });
                                                    stationRef?.current?.reset();
                                                    if (cityConfirmRuleId) {
                                                        // 重新拉取详情，回填页面
                                                        const item = {
                                                            ...(editItems?.length && editItems[0]),
                                                            ruleId: cityConfirmRuleId,
                                                            city: _city,
                                                        };
                                                        initRef?.current?.edit({
                                                            item: item,
                                                            operId: operId,
                                                            type: pageType,
                                                        });
                                                        setCityConfirmRuleId(undefined);
                                                    }
                                                }}
                                                //   okButtonProps={{ loading: confirmLoading }}
                                                onCancel={() => {
                                                    setCityConfirmTitle('');
                                                    changeCityConfirmVisible(false);
                                                    setCityConfirmRuleId(undefined);
                                                    const city = editForm.getFieldValue('city');
                                                    editForm.setFieldsValue({
                                                        _city: city,
                                                    });
                                                }}
                                            >
                                                <Select
                                                    placeholder={
                                                        cityOptions?.length == 0
                                                            ? '无可配置城市'
                                                            : '请选择'
                                                    }
                                                    // allowClear
                                                    notFoundContent={
                                                        checkCityAlreadySetLoading ||
                                                        cityLoading ? (
                                                            <Spin size="small" />
                                                        ) : null
                                                    }
                                                    showSearch
                                                    showArrow
                                                    filterOption={(input, option) =>
                                                        option.children
                                                            .toLowerCase()
                                                            .indexOf(input.toLowerCase()) >= 0
                                                    }
                                                    onChange={async (value, option) => {
                                                        // 临时记录当前选择的城市，点ok的时候赋值给city，点cancel的时候还原为city
                                                        editForm.setFieldsValue({
                                                            _city: value,
                                                        });
                                                        updateAllStations([]);
                                                        // 切换城市时清空已选城市数据
                                                        if (!value) {
                                                            // 删除，提醒清除
                                                            console.log('提醒清除');
                                                        } else {
                                                            // 开始请求当前选中的城市是否已配置规则的接口
                                                            try {
                                                                let cooperationType;
                                                                if (path == 'profitRule') {
                                                                    cooperationType = '01';
                                                                } else if (path == 'purchase') {
                                                                    cooperationType = '02';
                                                                }

                                                                const {
                                                                    data: { ruleId },
                                                                } = await getOperCityRuleIdApi({
                                                                    cooperationType,
                                                                    city: value,
                                                                    operId,
                                                                    cooperationPlatform,
                                                                });
                                                                if (ruleId) {
                                                                    setCityConfirmRuleId(ruleId);
                                                                    setCityConfirmTitle(
                                                                        `${
                                                                            option?.children ||
                                                                            '该城市'
                                                                        }已设置过城市规则，是否继续修改${
                                                                            option?.children ||
                                                                            '该城市'
                                                                        }的城市规则？`,
                                                                    );
                                                                } else if (
                                                                    ruleRange != 1 &&
                                                                    stationRef?.current?.hasStations()
                                                                ) {
                                                                    // 选的不是全部，并且场站已选，才提醒
                                                                    setCityConfirmTitle(
                                                                        '切换城市需要重新选择规则场站，是否继续？',
                                                                    );
                                                                } else {
                                                                    // 可直接修改城市
                                                                    editForm.setFieldsValue({
                                                                        city: value,
                                                                    });
                                                                }
                                                            } catch (error) {}
                                                        }
                                                    }}
                                                    style={{ width: '100%' }}
                                                    value={_city}
                                                    disabled={
                                                        cityOptions?.length == 0 ||
                                                        editType == EDIT_TYPE.EDIT
                                                    }
                                                >
                                                    {cityOptions}
                                                </Select>
                                            </Popconfirm>
                                        </FormItem>
                                        <FormItem name="city" noStyle />
                                    </>
                                );
                            }}
                        </FormItem>
                    )) ||
                        null}

                    {(pageType == PAGE_TYPE.CITY && (
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.city !== curValues.city
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const city = getFieldValue('city');
                                if (!city) return null;
                                return (
                                    <FormItem
                                        label="规则范围"
                                        name="ruleRange"
                                        rules={[{ required: true, message: '请选择' }]}
                                        {...formItemFixedWidthLayout}
                                    >
                                        <Radio.Group disabled={editType == EDIT_TYPE.EDIT}>
                                            <Radio value="01">全部</Radio>
                                            <Radio value="02">部分</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                );
                            }}
                        </FormItem>
                    )) ||
                        null}

                    {(pageType == PAGE_TYPE.CITY && (
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.ruleRange !== curValues.ruleRange ||
                                prevValues.city !== curValues.city
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const city = getFieldValue('city');
                                if (!city) return null;

                                const ruleRange = getFieldValue('ruleRange');
                                return ruleRange == 2 ? (
                                    <>
                                        <FormItem
                                            label="规则场站"
                                            name="empty"
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (allStations?.length == 0) {
                                                            return Promise.reject('请选择规则场站');
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            required
                                            {...formItemLayout}
                                        >
                                            <Space>
                                                <Button type="primary" onClick={addStationEvent}>
                                                    <PlusOutlined />
                                                    添加
                                                </Button>
                                            </Space>
                                        </FormItem>

                                        <RuleStationList
                                            allStations={allStations}
                                            formItemLayout={{
                                                wrapperCol: {
                                                    offset: 2,
                                                    span: 20,
                                                },
                                            }}
                                            path={path}
                                            form={editForm}
                                            keyName={'ruleStations'}
                                            editStationEvent={editStationEvent}
                                            deleteStationEvent={deleteStationEvent}
                                        />

                                        <RuleStationModal
                                            {...props}
                                            path={path}
                                            initRef={ruleStationRef}
                                            operId={operId}
                                            city={city}
                                            ruleId={ruleStationEditItem?.ruleId}
                                            cooperationPlatform={cooperationPlatform}
                                            onFinish={(values) => {
                                                if (ruleStationEditItem) {
                                                    allStations.splice(
                                                        allStations.indexOf(ruleStationEditItem),
                                                        1,
                                                        {
                                                            ...ruleStationEditItem,
                                                            ...values,
                                                            ruleId: ruleStationEditItem.ruleId,
                                                        },
                                                    );
                                                } else {
                                                    allStations.push({ ...values });
                                                }
                                                updateAllStations([...allStations]);
                                            }}
                                            changeSelectEvent={changeSelectEvent}
                                            selectTemplateOptions={selectTemplateOptions}
                                            disabledDate={disabledDate}
                                            selectTemplateDetailList={selectTemplateDetailList}
                                            templateColumns={templateColumns}
                                        />
                                    </>
                                ) : null;
                            }}
                        </FormItem>
                    )) ||
                        null}

                    {(editType == EDIT_TYPE.CHOOSE && (
                        <FormItem
                            label="规则类型"
                            name="editRuleType"
                            {...{
                                labelCol: {
                                    span: 6,
                                },
                                wrapperCol: {
                                    span: 14,
                                },
                            }}
                            initialValue={RULE_TYPES.COMMON}
                            {...formItemFixedWidthLayout}
                        >
                            <Radio.Group
                                onChange={({ target: { value } }) => {
                                    // 如果是批量编辑，则忽略
                                    if (isEmpty(editItems) && isEmpty(editItems[0])) {
                                        return;
                                    }
                                    if (value === RULE_TYPES.COMMON) {
                                        editForm.setFieldsValue({
                                            useTemplate: '01',
                                        });
                                        dispatch({
                                            type: 'profitRuleModel/getRuleInfo',
                                            params: {
                                                operId,
                                                ruleId: editItems[0].ruleId,
                                                profitRuleType: pageType,
                                                cooperationPlatform,
                                            },
                                        });
                                    } else if (value === RULE_TYPES.STATION) {
                                        if (mainInfo.templateId) {
                                            editForm.setFieldsValue({
                                                useTemplate: '02',
                                                templateId: mainInfo.templateId,
                                            });
                                        }

                                        dispatch({
                                            type: 'profitRuleModel/getRuleInfo',
                                            params: {
                                                operId,
                                                ruleId: editItems[0].ruleId,
                                                stationId: editItems[0].stationId,
                                                profitRuleType: pageType,
                                                cooperationPlatform,
                                            },
                                        });
                                    }
                                }}
                            >
                                {editItems?.length > 1 ? (
                                    <Tooltip title="批量编辑不支持选择通用规则">
                                        <Radio value={RULE_TYPES.COMMON} disabled>
                                            通用规则
                                        </Radio>
                                    </Tooltip>
                                ) : (
                                    <Radio
                                        value={RULE_TYPES.COMMON}
                                        disabled={cityRuleFlag != undefined}
                                    >
                                        通用规则
                                    </Radio>
                                )}
                                {editItems?.length > 1 ? (
                                    <Tooltip title="批量编辑不支持选择城市规则">
                                        <Radio value={RULE_TYPES.CITY} disabled>
                                            城市规则
                                        </Radio>
                                    </Tooltip>
                                ) : cityRuleFlag ? (
                                    <Radio value={RULE_TYPES.CITY}>城市规则</Radio>
                                ) : (
                                    <Tooltip
                                        title={
                                            <>
                                                <span>未配置城市规则。</span>
                                                <Button
                                                    type="link"
                                                    onClick={() =>
                                                        addCityRuleEvent && addCityRuleEvent()
                                                    }
                                                >
                                                    现在去配？
                                                </Button>
                                            </>
                                        }
                                    >
                                        <Radio value={RULE_TYPES.CITY} disabled>
                                            城市规则
                                        </Radio>
                                    </Tooltip>
                                )}
                                <Radio value={RULE_TYPES.STATION}>场站规则</Radio>
                            </Radio.Group>
                        </FormItem>
                    )) ||
                        null}

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.city !== curValues.city ||
                            prevValues.editRuleType !== curValues.editRuleType ||
                            prevValues.ruleRange !== curValues.ruleRange
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const city = getFieldValue('city');
                            const ruleRange = getFieldValue('ruleRange');
                            if (ruleRange != '01' || !city) {
                                return null;
                            }
                            const editRuleType = getFieldValue('editRuleType');
                            if (editRuleType !== RULE_TYPES.COMMON) {
                                return (
                                    <Fragment>
                                        <FormItem
                                            noStyle
                                            shouldUpdate={(prevValues, curValues) =>
                                                prevValues?.useTemplate !== curValues?.useTemplate
                                            }
                                        >
                                            {({ getFieldValue }) => {
                                                const useTemplate = getFieldValue('useTemplate');
                                                return (
                                                    <FormItem
                                                        label={
                                                            <span>
                                                                使用模板
                                                                <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                                    <InfoCircleOutlined
                                                                        style={{
                                                                            marginLeft: '6px',
                                                                        }}
                                                                    />
                                                                </Tooltip>
                                                            </span>
                                                        }
                                                        {...{
                                                            labelCol: {
                                                                span: 6,
                                                            },
                                                            wrapperCol: {
                                                                span: 14,
                                                            },
                                                        }}
                                                        name="useTemplate"
                                                        {...formItemFixedWidthLayout}
                                                        rules={[
                                                            ({ getFieldValue }) => ({
                                                                validator(rule, value) {
                                                                    if (value === '02') {
                                                                        const templateId =
                                                                            getFieldValue(
                                                                                'templateId',
                                                                            );
                                                                        if (!templateId) {
                                                                            return Promise.reject(
                                                                                '请选择模板',
                                                                            );
                                                                        }
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <Space>
                                                            <Radio.Group value={useTemplate}>
                                                                <Radio value="01">否</Radio>
                                                                <Radio value="02">
                                                                    <Space>是</Space>
                                                                </Radio>
                                                            </Radio.Group>
                                                            {useTemplate === '02' && (
                                                                <Button
                                                                    type="primary"
                                                                    onClick={() => {
                                                                        if (path == 'purchase') {
                                                                            selectPurchaseTemplateRef.current.show();
                                                                        } else {
                                                                            selectTemplateRef.current.show();
                                                                        }
                                                                    }}
                                                                >
                                                                    选择模板
                                                                </Button>
                                                            )}
                                                        </Space>
                                                    </FormItem>
                                                );
                                            }}
                                        </FormItem>
                                        <FormItem
                                            shouldUpdate={(prevValues, curValues) => true}
                                            noStyle
                                        >
                                            {({ getFieldValue }) => {
                                                const useTemplate = getFieldValue('useTemplate');
                                                if (useTemplate === '02') {
                                                    return (
                                                        <FormItem name="templateId" noStyle>
                                                            <Input type="hidden" />
                                                        </FormItem>
                                                    );
                                                }
                                                return null;
                                            }}
                                        </FormItem>
                                    </Fragment>
                                );
                            }
                            return null;
                        }}
                    </FormItem>
                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.useTemplate !== curValues.useTemplate ||
                            prevValues.ruleRange !== curValues.ruleRange
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const useTemplate = getFieldValue('useTemplate');
                            const ruleRange = getFieldValue('ruleRange');
                            if (useTemplate === '02' && ruleRange == '01') {
                                return (
                                    <TablePro
                                        name="template"
                                        loading={selectTemplateListLoading}
                                        scroll={{ x: 'max-content' }}
                                        dataSource={selectTemplateDetailList}
                                        columns={templateColumns}
                                        pagination={false}
                                        style={{ marginBottom: '16px' }}
                                    />
                                );
                            }
                            return null;
                        }}
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.useTemplate !== curValues.useTemplate ||
                            prevValues.editRuleType !== curValues.editRuleType ||
                            prevValues.expFlag !== curValues.expFlag ||
                            prevValues.effTime !== curValues.effTime ||
                            prevValues.ruleRange !== curValues.ruleRange ||
                            prevValues.premiumFlag !== curValues.premiumFlag
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const useTemplate = getFieldValue('useTemplate');
                            const editRuleType = getFieldValue('editRuleType');
                            const expFlag = getFieldValue('expFlag');
                            const effTime = getFieldValue('effTime');
                            const ruleRange = getFieldValue('ruleRange');
                            const premiumFlag = getFieldValue('premiumFlag');

                            return (
                                <Fragment>
                                    {useTemplate === '01' && ruleRange == '01' && (
                                        <Fragment>
                                            <FormItem
                                                label="生效时间:"
                                                name="effTime"
                                                rules={[
                                                    { required: true, message: '请填写生效时间' },
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            if (!value) {
                                                                return Promise.reject('');
                                                            }

                                                            if (value && canISetEffTime) {
                                                                const nowTime = +new Date();
                                                                const sendEndTime = +new Date(
                                                                    value,
                                                                );

                                                                if (sendEndTime < nowTime) {
                                                                    return Promise.reject(
                                                                        '生效时间不能早于当前时间',
                                                                    );
                                                                }
                                                            }

                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                {...formItemFixedWidthLayout}
                                            >
                                                <DatePicker
                                                    disabledDate={disabledDate}
                                                    showTime={{
                                                        format: 'HH:mm:ss',
                                                        defaultValue: moment(
                                                            '00:00:00',
                                                            'HH:mm:ss',
                                                        ),
                                                    }}
                                                    format="YYYY-MM-DD HH:mm:ss"
                                                    disabled={
                                                        ((pageType == PAGE_TYPE.PROFIT ||
                                                            pageType == PAGE_TYPE.PURCHASE) &&
                                                            editRuleType === RULE_TYPES.COMMON) ||
                                                        !canISetEffTime
                                                    }
                                                    showNow={false}
                                                    renderExtraFooter={(a) => (
                                                        <Space>
                                                            <Button
                                                                type="link"
                                                                onClick={() => {
                                                                    const time = moment().add(
                                                                        10,
                                                                        'seconds',
                                                                    );
                                                                    editForm.setFieldsValue({
                                                                        effTime: time,
                                                                    });
                                                                }}
                                                            >
                                                                10秒之后
                                                            </Button>
                                                            <Button
                                                                type="link"
                                                                onClick={() => {
                                                                    const effTime =
                                                                        editForm.getFieldValue(
                                                                            'effTime',
                                                                        );
                                                                    if (effTime) {
                                                                        editForm.setFieldsValue({
                                                                            effTime:
                                                                                moment(
                                                                                    effTime,
                                                                                ).startOf('day'),
                                                                        });
                                                                    }
                                                                }}
                                                            >
                                                                时间重置
                                                            </Button>
                                                        </Space>
                                                    )}
                                                    style={{ width: '100%' }}
                                                />
                                            </FormItem>
                                            <FormItem
                                                label="失效时间:"
                                                name="expFlag"
                                                initialValue="01"
                                                {...{
                                                    labelCol: {
                                                        span: 6,
                                                    },
                                                    wrapperCol: {
                                                        span: 18,
                                                    },
                                                }}
                                                {...formItemLayout}
                                            >
                                                <Radio.Group
                                                    disabled={
                                                        (pageType == PAGE_TYPE.PROFIT ||
                                                            pageType == PAGE_TYPE.PURCHASE) &&
                                                        editRuleType === RULE_TYPES.COMMON
                                                    }
                                                >
                                                    <Radio value={EXP_FLAG_TYPES.LONG}>长期</Radio>
                                                    <Radio value={EXP_FLAG_TYPES.SET}>
                                                        <Space>
                                                            指定时间
                                                            <FormItem
                                                                name="expTime"
                                                                noStyle
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!effTime) {
                                                                                return Promise.reject(
                                                                                    '请选择生效开始时间',
                                                                                );
                                                                            }

                                                                            if (value) {
                                                                                const startTime =
                                                                                    +new Date(
                                                                                        effTime,
                                                                                    );
                                                                                const sendEndTime =
                                                                                    +new Date(
                                                                                        value,
                                                                                    );

                                                                                if (
                                                                                    sendEndTime <=
                                                                                    startTime
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        '失效时间不能早于生效时间',
                                                                                    );
                                                                                }
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <DatePicker
                                                                    disabledDate={disabledDate}
                                                                    disabled={
                                                                        expFlag != '02' ||
                                                                        editRuleType ===
                                                                            RULE_TYPES.COMMON
                                                                    }
                                                                    showTime={{
                                                                        format: 'HH:mm:ss',
                                                                        defaultValue: moment(
                                                                            '23:59:59',
                                                                            'HH:mm:ss',
                                                                        ),
                                                                    }}
                                                                    format="YYYY-MM-DD HH:mm:ss"
                                                                    showNow={false}
                                                                    renderExtraFooter={(a) => (
                                                                        <Space>
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() => {
                                                                                    const time =
                                                                                        moment().add(
                                                                                            10,
                                                                                            'seconds',
                                                                                        );
                                                                                    editForm.setFieldsValue(
                                                                                        {
                                                                                            expTime:
                                                                                                time,
                                                                                        },
                                                                                    );
                                                                                }}
                                                                            >
                                                                                10秒之后
                                                                            </Button>
                                                                            <Button
                                                                                type="link"
                                                                                onClick={() => {
                                                                                    const effTime =
                                                                                        editForm.getFieldValue(
                                                                                            'effTime',
                                                                                        );
                                                                                    if (effTime) {
                                                                                        editForm.setFieldsValue(
                                                                                            {
                                                                                                effTime:
                                                                                                    moment(
                                                                                                        effTime,
                                                                                                    ).startOf(
                                                                                                        'day',
                                                                                                    ),
                                                                                            },
                                                                                        );
                                                                                    }
                                                                                }}
                                                                            >
                                                                                时间重置
                                                                            </Button>
                                                                        </Space>
                                                                    )}
                                                                />
                                                            </FormItem>
                                                        </Space>
                                                    </Radio>
                                                </Radio.Group>
                                            </FormItem>
                                            {(path == 'profitRule' &&
                                                (pageType != PAGE_TYPE.CITY ||
                                                    ruleRange == '01') && (
                                                    <ProfitRuleFormItem
                                                        loading={infoLoading}
                                                        disabled={
                                                            (pageType == PAGE_TYPE.PROFIT ||
                                                                pageType == PAGE_TYPE.PURCHASE) &&
                                                            editRuleType === RULE_TYPES.COMMON
                                                        }
                                                        form={editForm}
                                                        editRuleInfo={mainInfo}
                                                        commonRule
                                                        titleFormItemLayout={formItemLayout}
                                                        formItemLayout={{
                                                            wrapperCol: { span: 22, offset: 2 },
                                                        }}
                                                    />
                                                )) ||
                                                null}
                                            {path == 'purchase' &&
                                                (pageType != PAGE_TYPE.CITY ||
                                                    ruleRange == '01') && (
                                                    <Fragment>
                                                        <FormItem
                                                            label="结算方式"
                                                            name="profitRuleTypes"
                                                            {...formItemLayout}
                                                            wrapperCol={{ span: 24 }}
                                                            required
                                                            rules={[
                                                                () => ({
                                                                    validator(rule, value) {
                                                                        if (isEmpty(value)) {
                                                                            return Promise.reject(
                                                                                `请选择分润方式`,
                                                                            );
                                                                        }
                                                                        for (const item of value) {
                                                                            const ruleErr =
                                                                                checkRuleInfo(item);
                                                                            if (ruleErr) {
                                                                                return Promise.reject(
                                                                                    ruleErr,
                                                                                );
                                                                            }
                                                                        }
                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                        >
                                                            <PurchaseRuleFormItem
                                                                initialAgreeType={['01']}
                                                                disabled={
                                                                    (pageType == PAGE_TYPE.PROFIT ||
                                                                        pageType ==
                                                                            PAGE_TYPE.PURCHASE) &&
                                                                    editRuleType ===
                                                                        RULE_TYPES.COMMON
                                                                }
                                                            ></PurchaseRuleFormItem>
                                                        </FormItem>
                                                    </Fragment>
                                                )}
                                        </Fragment>
                                    )}
                                    {path == 'purchase' && ruleRange == '01' && (
                                        <FormItem
                                            label={
                                                <span>
                                                    通道费收取方式
                                                    <SlottingTooltip></SlottingTooltip>
                                                </span>
                                            }
                                            {...{
                                                labelCol: {
                                                    flex: '0 0 140px',
                                                },
                                            }}
                                            name="profitSlottingRules"
                                            wrapperCol={{ span: 24 }}
                                            required
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (isEmpty(value)) {
                                                            return Promise.reject(
                                                                `请配置通道费收取方式`,
                                                            );
                                                        }
                                                        const errText = checkRuleValue(value);
                                                        if (errText) {
                                                            return Promise.reject(errText);
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            initialValue={defaultRuleValue}
                                        >
                                            <SlottingRuleFormItem></SlottingRuleFormItem>
                                        </FormItem>
                                    )}
                                    <FormItem name="premiumFlag" noStyle></FormItem>
                                    <FormItem
                                        label="是否溢价"
                                        required
                                        {...formItemLayout}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Switch
                                            checkedChildren="开"
                                            unCheckedChildren="关"
                                            checked={premiumFlag === '1'}
                                            defaultChecked={false}
                                            disabled={!premiumFlagEnable}
                                            onChange={(checked) => {
                                                if (!checked) {
                                                    checkClosePremiumCommon(
                                                        cooperationPlatform,
                                                    ).then((result) => {
                                                        editForm.setFieldValue('premiumFlag', '0');
                                                    });
                                                } else {
                                                    editForm.setFieldValue('premiumFlag', '1');
                                                }
                                            }}
                                        />
                                    </FormItem>
                                    {premiumFlag === '1' && path == 'purchase' && (
                                        <FormItem
                                            label=" "
                                            name="divideDTOList"
                                            {...formItemLayout}
                                            wrapperCol={{ span: 16 }}
                                            colon={false}
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (isEmpty(value)) {
                                                            return Promise.reject(
                                                                `请选择溢价分成方式`,
                                                            );
                                                        }
                                                        for (const item of value) {
                                                            if (!isNumber(item?.divideValue)) {
                                                                return Promise.reject('请填写分成');
                                                            } else if (
                                                                Number(item?.divideValue) > 10 ||
                                                                Number(item?.divideValue) < 0
                                                            ) {
                                                                return Promise.reject(
                                                                    '只能填写0.000～10.000之间的数字',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <BuyPremiumItem />
                                        </FormItem>
                                    )}
                                    {premiumFlag === '1' && path == 'profitRule' && (
                                        <FormItem
                                            label=" "
                                            name="divideDTOList"
                                            {...formItemLayout}
                                            wrapperCol={{ span: 16 }}
                                            colon={false}
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (isEmpty(value)) {
                                                            return Promise.reject(
                                                                `请选择溢价分成方式`,
                                                            );
                                                        }
                                                        for (const item of value) {
                                                            if (!isNumber(item?.divideValue)) {
                                                                return Promise.reject('请填写分成');
                                                            } else if (
                                                                Number(item?.divideValue) > 100 ||
                                                                Number(item?.divideValue) < 0
                                                            ) {
                                                                return Promise.reject(
                                                                    '只能填写0～100之间的数字',
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <SharePremiumItem />
                                        </FormItem>
                                    )}
                                </Fragment>
                            );
                        }}
                    </FormItem>

                    <FormItem name="ruleFlag" noStyle />
                    <FormItem name="ruleRel" noStyle />

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.editRuleType !== curValues.editRuleType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const editRuleType = getFieldValue('editRuleType');
                            return (
                                <div className={styles['form-submit']}>
                                    {editType == EDIT_TYPE.ADD ||
                                    editType == EDIT_TYPE.EDIT ||
                                    editRuleType !== RULE_TYPES.COMMON ||
                                    (editRuleType === RULE_TYPES.COMMON &&
                                        editItems?.[0]?.profitRuleType === RULE_TYPES.STATION) ? (
                                        <Button
                                            className={styles['form-btn']}
                                            type="primary"
                                            htmlType="submit"
                                            loading={submitLoading}
                                        >
                                            提交
                                        </Button>
                                    ) : null}

                                    <Button
                                        className={styles['form-btn']}
                                        onClick={() => initRef?.current?.close()}
                                    >
                                        取消
                                    </Button>
                                </div>
                            );
                        }}
                    </FormItem>
                </Form>
                <SelectPurchaseTemplateModal
                    onFinish={changeSelectEvent}
                    ref={selectPurchaseTemplateRef}
                />
                <SelectRuleModal onFinish={changeSelectEvent} ref={selectTemplateRef} />
            </Card>
        </Modal>
    );
};

export default connect(({ global, profitRuleModel, loading, user }) => ({
    global,
    user,
    profitRuleModel,
    listLoading: loading.effects['profitRuleModel/getStationRuleList'],
    selectTemplateListLoading: loading.effects['profitRuleModel/getSelectTemplateInfo'],
}))(EditRulesModal);
