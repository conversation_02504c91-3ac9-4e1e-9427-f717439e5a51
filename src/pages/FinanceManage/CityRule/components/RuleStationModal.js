import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Form, Modal, DatePicker, Space, Tooltip, Radio, Input } from 'antd';
import moment from 'moment';
import { Fragment, useState, useImperativeHandle, useRef } from 'react';

import { RuleFormItem as ProfitRuleFormItem } from '../../ProfitRule/components/RuleFormItem';
import PurchaseRuleFormItem, {
    checkRuleInfo,
} from '../../Purchase/components/PurchaseRuleFormItem';
import SlottingTooltip from '../../Purchase/components/SlottingTooltip';
import SlottingRuleFormItem, {
    checkRuleValue,
    defaultRuleValue,
} from '../../Purchase/components/SlottingRuleFormItem';
import OperStationSearchList from '@/components/OperStationSearchList/SearchList';
import { initialRuleList } from '../../ProfitRule/components/RuleModelFormItem';
import { getRuleTemplateDetilsApi, getRuleInfoApi } from '@/services/FinanceManage/ProfitRuleApi';
import { getRuleNameApi } from '@/services/FinanceManage/CityRuleApi';
import { RULE_TYPES } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import TablePro from '@/components/TablePro';
import { getProfitRuleTypesFunction } from '../EditRulesModal';
import SelectRuleModal from '../../ProfitRule/components/SelectRuleModal';
import SelectPurchaseTemplateModal from '../../Purchase/components/SelectRuleModal';
import { CollectTypes, PURCHASE_RULE_TYPES } from '../../Purchase/PurchaseConfig';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

// 规则场站弹窗
const RuleStationModal = (props = {}) => {
    const {
        initRef,
        path,
        currentUser,
        operId,
        city,
        onFinish,
        global: {
            codeInfo: { operationWay },
        },

        disabledDate,
        templateColumns,
        cooperationPlatform,
    } = props;

    const selectTemplateRef = useRef();
    const selectPurchaseTemplateRef = useRef();
    const [ruleStationForm] = Form.useForm();
    const [showState, updateShowState] = useState(false);
    const [editItem, updateEditItem] = useState(undefined);
    const [disabledStationIds, udpateDisabledStationIds] = useState([]); // 禁止选择的站点
    const [selectTemplateListLoading, updateSelectTemplateListLoading] = useState(false);
    const [selectTemplateDetailList, updateSelectTemplateDetailList] = useState([]);
    const [canISetEffTime, changeCiSetEffTime] = useState(true);
    const closeEvent = () => {
        updateShowState(false);
        udpateDisabledStationIds([]);
        updateEditItem(undefined);
        ruleStationForm.resetFields();
        updateSelectTemplateDetailList([]);
    };

    useImperativeHandle(initRef, () => ({
        show: (item) => {
            ruleStationForm.resetFields();

            if (item?.profitRuleTypes) {
                item.profitRuleTypes =
                    (typeof item.profitRuleTypes === 'string' &&
                        JSON.parse(item.profitRuleTypes)) ||
                    item.profitRuleTypes;
            } else if (item?.profitRuleTypeList) {
                if (path == 'profitRule') {
                    item.profitRuleList = initialRuleList.map((ele) => {
                        const findItem = item.profitRuleTypeList.find(
                            (item) => item.ruleModel === ele.ruleModel,
                        );
                        if (findItem) {
                            return {
                                ruleModel: [findItem.ruleModel],
                                ruleType: findItem.ruleType,
                                ruleValue: parseFloat(findItem.ruleValue),
                                agreeType: findItem.agreeType,
                                longRuleValue: parseFloat(findItem.longRuleValue),
                                shortRuleValue: parseFloat(findItem.shortRuleValue),
                            };
                        }
                        return null;
                    });
                } else if (path == 'purchase') {
                    item.profitRuleTypes = item.profitRuleTypeList;
                }
            }

            const params = (item && { ...item }) || { ruleFlag: '0' };
            if (params.effTime) {
                params.effTime = moment(params.effTime, 'YYYY-MM-DD HH:mm:ss');
            }
            if (params.expTime) {
                params.expTime = moment(params.expTime, 'YYYY-MM-DD HH:mm:ss');
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }
            if (params.templateId) {
                params.useTemplate = '02';
                loadSelectTemplateList(params.templateId);
            } else {
                params.useTemplate = '01';
            }
            if (params.templateId) {
                // 如果是由使用模板切换到不使用模板，生效失效时间可以改
                changeCiSetEffTime(true);
            } else if (params.effTime) {
                if (params.expTime) {
                    if (moment().isBetween(params.effTime, params.expTime)) {
                        changeCiSetEffTime(false);
                    } else {
                        changeCiSetEffTime(true);
                    }
                } else if (params.effTime.isBefore(moment())) {
                    changeCiSetEffTime(false);
                } else {
                    changeCiSetEffTime(true);
                }
            } else {
                changeCiSetEffTime(true);
            }
            if (!item) {
                initSlottingData();
            }
            ruleStationForm.setFieldsValue(params);
            updateShowState(true);
            updateEditItem(item);
        },
        udpateDisabledStationIds,
    }));

    const loadSelectTemplateList = async (id) => {
        try {
            updateSelectTemplateListLoading(true);
            const { data } = await getRuleTemplateDetilsApi(id);
            updateSelectTemplateDetailList(data?.detailList || []);
            ruleStationForm.setFieldValue('templateId', id);
            ruleStationForm.validateFields(['useTemplate']);
        } catch (error) {
        } finally {
            updateSelectTemplateListLoading(false);
        }
    };

    const initSlottingData = async () => {
        try {
            //初始化通用的通道配置
            if (path != 'purchase') {
                return;
            }
            const params = {
                operId,
                profitRuleType: '01',
                cooperationType: '02',
                cooperationPlatform,
            };

            const { data } = await getRuleInfoApi(params);
            const parentItem = data?.list?.[0];
            if (parentItem) {
                const { profitSlottingRuleList } = parentItem;
                if (profitSlottingRuleList instanceof Array) {
                    let profitSlottingRules = {
                        collectType: CollectTypes.DETAIL,
                        list: profitSlottingRuleList,
                    };
                    ruleStationForm.setFieldsValue({ profitSlottingRules });
                }

                return parentItem;
            }

            return null;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <>
            <Modal
                visible={showState}
                title="规则场站"
                onCancel={closeEvent}
                width={1000}
                onOk={() => {
                    ruleStationForm.validateFields().then(async (values) => {
                        try {
                            const params = {
                                ruleRel: values.ruleRel,
                                ruleFlag:
                                    (path == 'purchase' && values.profitRuleType && '1') ||
                                    values.ruleFlag ||
                                    '0',
                                stationIds: values.cityRuleStationIds
                                    .map((ele) => ele.stationId)
                                    .join(','),
                                stationInfo: values.stationInfo || undefined,
                                delScopeInfo: values.delScopeInfo || undefined,
                            };
                            if (path == 'purchase' && values.profitRuleTypes) {
                                params.ruleFlag = '1';
                                params.profitRuleTypes = getProfitRuleTypesFunction(
                                    values.profitRuleTypes,
                                    true,
                                );
                                //通道费收取方式
                            } else if (values.ruleFlag === '1') {
                                const profitRuleList = values.profitRuleList || [];
                                params.profitRuleTypes = getProfitRuleTypesFunction(
                                    profitRuleList,
                                    false,
                                );
                            }
                            if (path == 'purchase') {
                                const profitSlottingRuleInfo = values.profitSlottingRules;

                                const profitSlottingRules =
                                    profitSlottingRuleInfo?.list?.filter(
                                        (ele) => ele.slottingFlag,
                                    ) || [];
                                params.profitSlottingRules = profitSlottingRules;
                            }

                            let cooperationType;
                            if (path == 'profitRule') {
                                cooperationType = '01';
                            } else if (path == 'purchase') {
                                cooperationType = '02';
                            }
                            params.cooperationType = cooperationType;

                            params.useTemplate = values.useTemplate;
                            params.templateId = values.templateId;
                            // 全部
                            params.effTime = values.effTime?.format?.('YYYY-MM-DD HH:mm:ss');
                            if (values.expFlag === EXP_FLAG_TYPES.LONG) {
                                params.expTime = null;
                            } else if (values.expTime) {
                                params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
                            }

                            const { data } = await getRuleNameApi({
                                profitBoList: JSON.stringify([params]),
                            });

                            onFinish &&
                                onFinish({
                                    ...values,
                                    ...data,
                                    profitSlottingRules: values.profitSlottingRules,
                                    stationNames: values.cityRuleStationIds
                                        .map((ele) => ele.stationName)
                                        .join(','),
                                });
                            closeEvent();
                        } catch (error) {
                            console.log(error);
                        }
                    });
                }}
                okText="保存"
                destroyOnClose
            >
                <Form form={ruleStationForm}>
                    <OperStationSearchList
                        title="适用场站"
                        form={ruleStationForm}
                        formItemLayout={formItemLayout}
                        limitOperIds={[{ operId }]}
                        stationList={editItem?.cityRuleStationIds || []}
                        required
                        currentUser={currentUser}
                        operChannels={operationWay}
                        limitCityIds={[city]}
                        disabledStationIds={disabledStationIds}
                        keyName="cityRuleStationIds"
                        hideRange
                    />
                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues?.useTemplate !== curValues?.useTemplate
                        }
                    >
                        {({ getFieldValue }) => {
                            const useTemplate = getFieldValue('useTemplate');
                            return (
                                <FormItem
                                    label={
                                        <span>
                                            使用模板
                                            <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    name="useTemplate"
                                    {...formItemFixedWidthLayout}
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value === '02') {
                                                    const templateId = getFieldValue('templateId');
                                                    if (!templateId) {
                                                        return Promise.reject('请选择模板');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Space>
                                        <Radio.Group value={useTemplate}>
                                            <Radio value="01">否</Radio>
                                            <Radio value="02">
                                                <Space>是</Space>
                                            </Radio>
                                        </Radio.Group>
                                        {useTemplate === '02' && (
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    if (path == 'purchase') {
                                                        selectPurchaseTemplateRef.current.show();
                                                    } else {
                                                        selectTemplateRef.current.show();
                                                    }
                                                }}
                                            >
                                                选择模板
                                            </Button>
                                        )}
                                    </Space>
                                </FormItem>
                            );
                        }}
                    </FormItem>
                    <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                        {({ getFieldValue }) => {
                            const useTemplate = getFieldValue('useTemplate');
                            if (useTemplate === '02') {
                                return (
                                    <Fragment>
                                        <FormItem name="templateId" noStyle>
                                            <Input type="hidden" />
                                        </FormItem>
                                        <TablePro
                                            name="template"
                                            loading={selectTemplateListLoading}
                                            scroll={{ x: 'max-content' }}
                                            dataSource={selectTemplateDetailList}
                                            columns={templateColumns}
                                            pagination={false}
                                        />
                                    </Fragment>
                                );
                            }
                            return null;
                        }}
                    </FormItem>

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.useTemplate !== curValues.useTemplate ||
                            prevValues.editRuleType !== curValues.editRuleType ||
                            prevValues.expFlag !== curValues.expFlag ||
                            prevValues.effTime !== curValues.effTime ||
                            prevValues.premiumFlag !== curValues.premiumFlag
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const useTemplate = getFieldValue('useTemplate');
                            if (useTemplate != '01') {
                                return null;
                            }
                            const editRuleType = getFieldValue('editRuleType');
                            const expFlag = getFieldValue('expFlag');
                            const effTime = getFieldValue('effTime');
                            const premiumFlag = getFieldValue('premiumFlag');

                            return (
                                <Fragment>
                                    <FormItem
                                        label="生效时间:"
                                        name="effTime"
                                        rules={[
                                            { required: true, message: '请填写生效时间' },
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('');
                                                    }

                                                    if (value && canISetEffTime) {
                                                        const nowTime = +new Date();
                                                        const sendEndTime = +new Date(value);

                                                        if (sendEndTime < nowTime) {
                                                            return Promise.reject(
                                                                '生效时间不能早于当前时间',
                                                            );
                                                        }
                                                    }

                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        {...formItemFixedWidthLayout}
                                    >
                                        <DatePicker
                                            disabledDate={disabledDate}
                                            showTime={{
                                                format: 'HH:mm:ss',
                                                defaultValue: moment('00:00:00', 'HH:mm:ss'),
                                            }}
                                            format="YYYY-MM-DD HH:mm:ss"
                                            disabled={
                                                ((path == 'profitRule' || path == 'purchase') &&
                                                    editRuleType === RULE_TYPES.COMMON) ||
                                                !canISetEffTime
                                            }
                                            showNow={false}
                                            renderExtraFooter={(a) => (
                                                <Space>
                                                    <Button
                                                        type="link"
                                                        onClick={() => {
                                                            const time = moment().add(
                                                                10,
                                                                'seconds',
                                                            );
                                                            ruleStationForm.setFieldsValue({
                                                                effTime: time,
                                                            });
                                                        }}
                                                    >
                                                        10秒之后
                                                    </Button>
                                                    <Button
                                                        type="link"
                                                        onClick={() => {
                                                            const effTime =
                                                                ruleStationForm.getFieldValue(
                                                                    'effTime',
                                                                );
                                                            if (effTime) {
                                                                ruleStationForm.setFieldsValue({
                                                                    effTime:
                                                                        moment(effTime).startOf(
                                                                            'day',
                                                                        ),
                                                                });
                                                            }
                                                        }}
                                                    >
                                                        时间重置
                                                    </Button>
                                                </Space>
                                            )}
                                            style={{ width: '100%' }}
                                        />
                                    </FormItem>

                                    <FormItem
                                        label="失效时间:"
                                        name="expFlag"
                                        initialValue="01"
                                        {...formItemLayout}
                                    >
                                        <Radio.Group
                                            disabled={
                                                (path == 'profitRule' || path == 'purchase') &&
                                                editRuleType === RULE_TYPES.COMMON
                                            }
                                        >
                                            <Radio value={EXP_FLAG_TYPES.LONG}>长期</Radio>
                                            <Radio value={EXP_FLAG_TYPES.SET}>
                                                <Space>
                                                    指定时间
                                                    <FormItem
                                                        name="expTime"
                                                        noStyle
                                                        rules={[
                                                            (_) => ({
                                                                validator(rule, value) {
                                                                    if (!effTime) {
                                                                        return Promise.reject(
                                                                            '请选择生效开始时间',
                                                                        );
                                                                    }

                                                                    if (value) {
                                                                        const startTime = +new Date(
                                                                            effTime,
                                                                        );
                                                                        const sendEndTime =
                                                                            +new Date(value);

                                                                        if (
                                                                            sendEndTime <= startTime
                                                                        ) {
                                                                            return Promise.reject(
                                                                                '失效时间不能早于生效时间',
                                                                            );
                                                                        }
                                                                    }

                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <DatePicker
                                                            disabledDate={disabledDate}
                                                            disabled={
                                                                expFlag != '02' ||
                                                                editRuleType === RULE_TYPES.COMMON
                                                            }
                                                            showTime={{
                                                                format: 'HH:mm:ss',
                                                                defaultValue: moment(
                                                                    '23:59:59',
                                                                    'HH:mm:ss',
                                                                ),
                                                            }}
                                                            format="YYYY-MM-DD HH:mm:ss"
                                                            showNow={false}
                                                            renderExtraFooter={(a) => (
                                                                <Space>
                                                                    <Button
                                                                        type="link"
                                                                        onClick={() => {
                                                                            const time =
                                                                                moment().add(
                                                                                    10,
                                                                                    'seconds',
                                                                                );
                                                                            ruleStationForm.setFieldsValue(
                                                                                {
                                                                                    expTime: time,
                                                                                },
                                                                            );
                                                                        }}
                                                                    >
                                                                        10秒之后
                                                                    </Button>
                                                                    <Button
                                                                        type="link"
                                                                        onClick={() => {
                                                                            const effTime =
                                                                                ruleStationForm.getFieldValue(
                                                                                    'effTime',
                                                                                );
                                                                            if (effTime) {
                                                                                ruleStationForm.setFieldsValue(
                                                                                    {
                                                                                        effTime:
                                                                                            moment(
                                                                                                effTime,
                                                                                            ).startOf(
                                                                                                'day',
                                                                                            ),
                                                                                    },
                                                                                );
                                                                            }
                                                                        }}
                                                                    >
                                                                        时间重置
                                                                    </Button>
                                                                </Space>
                                                            )}
                                                        />
                                                    </FormItem>
                                                </Space>
                                            </Radio>
                                        </Radio.Group>
                                    </FormItem>

                                    {(path == 'profitRule' && (
                                        <ProfitRuleFormItem
                                            form={ruleStationForm}
                                            editRuleInfo={editItem}
                                            commonRule
                                            titleFormItemLayout={formItemLayout}
                                            formItemLayout={{
                                                wrapperCol: { span: 22, offset: 2 },
                                            }}
                                        />
                                    )) ||
                                        null}
                                    {path == 'purchase' && (
                                        <Fragment>
                                            <FormItem
                                                label="结算方式"
                                                name="profitRuleTypes"
                                                wrapperCol={{ span: 24 }}
                                                required
                                                rules={[
                                                    () => ({
                                                        validator(rule, value) {
                                                            if (isEmpty(value)) {
                                                                return Promise.reject(
                                                                    `请选择购电结算方式`,
                                                                );
                                                            }
                                                            for (const item of value) {
                                                                const ruleErr = checkRuleInfo(item);
                                                                if (ruleErr) {
                                                                    return Promise.reject(ruleErr);
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <PurchaseRuleFormItem
                                                    initialAgreeType={['01']}
                                                ></PurchaseRuleFormItem>
                                            </FormItem>
                                        </Fragment>
                                    )}
                                </Fragment>
                            );
                        }}
                    </FormItem>
                    {path == 'purchase' && (
                        <FormItem
                            label={
                                <span>
                                    通道费收取方式
                                    <SlottingTooltip></SlottingTooltip>
                                </span>
                            }
                            name="profitSlottingRules"
                            wrapperCol={{ span: 16 }}
                            required
                            rules={[
                                () => ({
                                    validator(rule, value) {
                                        if (isEmpty(value)) {
                                            return Promise.reject(`请配置通道费收取方式`);
                                        }
                                        const errText = checkRuleValue(value);
                                        if (errText) {
                                            return Promise.reject(errText);
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            initialValue={defaultRuleValue}
                        >
                            <SlottingRuleFormItem></SlottingRuleFormItem>
                        </FormItem>
                    )}
                    <FormItem name="ruleFlag" noStyle />
                    <FormItem name="ruleRel" noStyle />
                </Form>
            </Modal>
            <SelectRuleModal onFinish={loadSelectTemplateList} ref={selectTemplateRef} />
            <SelectPurchaseTemplateModal
                onFinish={loadSelectTemplateList}
                ref={selectPurchaseTemplateRef}
            />
        </>
    );
};

export default RuleStationModal;
