import { Form, Space, Tooltip, Popconfirm } from 'antd';
import { Fragment, useState, useEffect, useMemo, useRef } from 'react';

import TablePro from '@/components/TablePro';
import { TemplateListModal as ProfitTemplateListModal } from '../../ProfitRule/ProfitRuleTemplateListPage';
import { TemplateListModal as PurchaseTemplateListModal } from '../../Purchase/PurchaseTemplateListPage';

const FormItem = Form.Item;

const RuleStationList = ({
    formItemLayout,
    allStations = [],
    path,
    form,
    disabled,
    keyName,
    editStationEvent, // 编辑
    deleteStationEvent, // 删除
}) => {
    useEffect(() => {
        form.setFieldsValue({ [keyName]: allStations });
    }, [allStations, keyName]);

    const profitTemplateRef = useRef();
    const purchaseTemplateRef = useRef();

    const stationColumns = useMemo(() => {
        const columns =
            (path == 'profitRule' && [
                {
                    title: '适用场站',
                    width: 140,
                    dataIndex: 'ruleRangeName',
                    render(text, record) {
                        return (
                            <Tooltip
                                title={
                                    <div
                                        style={{
                                            maxWidth: '620px',
                                            maxHeight: '320px',
                                            overflowY: 'auto',
                                        }}
                                    >
                                        {record.stationNames}
                                    </div>
                                }
                            >
                                {text}
                            </Tooltip>
                        );
                    },
                },
                {
                    title: '分润方式',
                    dataIndex: 'ruleName',
                    width: '120px',
                    render(text, record) {
                        return (
                            <span style={{ whiteSpace: 'pre' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
            ]) ||
            (path == 'purchase' && [
                {
                    title: '适用场站',
                    width: 140,
                    dataIndex: 'ruleRangeName',
                    render(text, record) {
                        return (
                            <Tooltip
                                title={
                                    <div
                                        style={{
                                            maxWidth: '620px',
                                            maxHeight: '320px',
                                            overflowY: 'auto',
                                        }}
                                    >
                                        {record.stationNames}
                                    </div>
                                }
                            >
                                {text}
                            </Tooltip>
                        );
                    },
                },
                {
                    title: '购电结算方式',
                    dataIndex: 'ruleName',
                    width: 240,
                    render(text, record) {
                        return (
                            <span
                                style={{
                                    whiteSpace: 'pre-wrap',
                                    wordBreak: 'break-all',
                                }}
                                title={text}
                            >
                                {text}
                            </span>
                        );
                    },
                },
            ]) ||
            [];
        columns.push(
            ...[
                {
                    title: '溢价分成规则',
                    width: 220,
                    dataIndex: 'premiumString',
                },
                {
                    title: '生效时间',
                    width: 220,
                    dataIndex: 'effTime',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
                {
                    title: '失效时间',
                    width: 220,
                    dataIndex: 'expTime',
                    render(text, record) {
                        return (
                            <span title={text}> {(text?.length && text) || record.maxExpTime}</span>
                        );
                    },
                },
                {
                    title: '所属模板',
                    width: 200,
                    dataIndex: 'templateName',
                    render(text = '-', record) {
                        if (record.templateId) {
                            return (
                                <a
                                    onClick={() => {
                                        if (path == 'profitRule') {
                                            profitTemplateRef.current?.show(record.templateId);
                                        } else if (path == 'purchase') {
                                            purchaseTemplateRef.current?.show(record.templateId);
                                        }
                                    }}
                                >
                                    {text}
                                </a>
                            );
                        }
                        return <span title={text}>{text}</span>;
                    },
                },
            ],
        );
        if (disabled != true) {
            columns.push({
                title: '操作 ',
                fixed: 'right',
                width: 120,
                render(text, record, index) {
                    return (
                        <Space>
                            <a onClick={() => editStationEvent(record, index)}>编辑</a>
                            <Popconfirm
                                title={`确定要删除？`}
                                onConfirm={() => {
                                    deleteStationEvent && deleteStationEvent(record, index);
                                }}
                            >
                                <a>删除</a>
                            </Popconfirm>
                        </Space>
                    );
                },
            });
        }
        return columns;
    }, [allStations, path]);

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }
        changePageNum(page.current);
    };

    return (
        <Fragment>
            {(allStations?.length && (
                <FormItem {...formItemLayout}>
                    <FormItem noStyle name={keyName}>
                        <TablePro
                            name="list"
                            scroll={{ x: 'max-content' }}
                            rowKey={(record) => record.stationId}
                            dataSource={allStations}
                            onChange={changePageInfo}
                            pagination={{
                                current: pageNum,
                                total: allStations?.length,
                                pageSize: pageSize,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条`,
                            }}
                            columns={stationColumns}
                            sticky={{ offsetHeader: 64 }}
                        />
                    </FormItem>
                </FormItem>
            )) ||
                null}
            <ProfitTemplateListModal initRef={profitTemplateRef} />
            <PurchaseTemplateListModal initRef={purchaseTemplateRef} />
        </Fragment>
    );
};

export default RuleStationList;
