import { getCityProfitRuleListApi } from '@/services/FinanceManage/CityRuleApi';

const CityRuleModel = {
    namespace: 'cityRuleModel',
    state: {
        cityRuleList: [], // 分润规则列表
        cityRuleTotal: 0,
    },
    effects: {
        /**
         * 分润规则列表
         */
        *getCityRuleList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getCityProfitRuleListApi, options);

                yield put({
                    type: 'setCityRulesProperty',
                    params: { cityRuleList: list, cityRuleTotal: total },
                });
            } catch (error) {}
        },
    },
    reducers: {
        setCityRulesProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default CityRuleModel;
