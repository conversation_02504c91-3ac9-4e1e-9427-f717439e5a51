import { LeftOutlined, PlusOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, Select, Tabs, Space, Tooltip } from 'antd';
import { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect, Link } from 'umi';

import styles from './style.less';
import usePageState from '@/hooks/usePageState.js';
import { isEmpty } from '@/utils/utils';
import { getStationCityListApi } from '@/services/CommonApi';
import EditRulesModal from './EditRulesModal';
import { TemplateListModal as ProfitTemplateListModal } from '../ProfitRule/ProfitRuleTemplateListPage';
import { TemplateListModal as PurchaseTemplateListModal } from '../Purchase/PurchaseTemplateListPage';
import SearchOptionsBar from '@/components/SearchOptionsBar/index';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import CacheAreaView from '@/components/CacheAreaView';

import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const { TabPane } = Tabs;

const FormItem = Form.Item;
const { Option } = Select;

const STATUS_TYPES = {
    ALL: '00',
    CURRENCY: '01',
    STATION: '02',
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        operId,
        global: { codeInfo },
        cooperationType,
        listLoading,
        onExportForm,
    } = props;

    const operGroupRef = useRef();
    const [cityList, changeCityList] = useState([]);
    const { ruleModel: ruleModelList } = codeInfo;

    useEffect(() => {
        initCityEvent();

        if (isEmpty(ruleModelList)) {
            dispatch({
                type: 'global/initCode',
                code: 'ruleModel',
            });
        }
    }, []);

    const ruleModelListOptions = useMemo(() => {
        if (ruleModelList) {
            return ruleModelList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [ruleModelList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const initCityEvent = async () => {
        try {
            const {
                data: { cityList: cityAndStationList },
            } = await getStationCityListApi({ operId });
            changeCityList(cityAndStationList);
        } catch (error) {
        } finally {
        }
    };

    const cityOptions = useMemo(() => {
        return (
            cityList?.map((ele) => (
                <Option key={ele.city} value={ele.city}>
                    {ele.cityName}
                </Option>
            )) || []
        );
    }, [cityList]);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onSearchGroup={onSearchGroup}
                onExportForm={onExportForm}
                exportName="导出至暂存区"
            >
                <Col span={8}>
                    <FormItem label="所选城市:" name="city" {...formItemLayout}>
                        <Select
                            placeholder="请选择"
                            allowClear
                            showSearch
                            filterOption={(input, option) =>
                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                        >
                            {cityOptions}
                        </Select>
                    </FormItem>
                </Col>

                <Col span={8}>
                    <AllStationSelect form={form} label="场站名称" name="stationIds" />
                </Col>

                {(cooperationType === '01' && (
                    <Col span={8}>
                        <FormItem label="分润方式" name="ruleModel">
                            <Select
                                mode="multiple"
                                allowClear
                                placeholder="请选择"
                                {...formItemLayout}
                            >
                                {ruleModelListOptions}
                            </Select>
                        </FormItem>
                    </Col>
                )) ||
                    null}
                <FormItem noStyle name="stationIdList" />
            </SearchOptionsBar>

            <OperGroupImportModal
                title="批量查询"
                buildId={operId}
                initRef={operGroupRef}
                onConfirm={(addStationList) => {
                    const list =
                        (addStationList?.length && addStationList.map((item) => item.stationId)) ||
                        [];
                    form.setFieldsValue({ stationIdList: list });
                    const values = form.getFieldsValue();
                    onSubmit(values);
                }}
            />
        </Form>
    );
};

/**
 * 场站规则列表
 */
export const CityRuleListView = (props) => {
    const {
        dispatch,
        cityRuleModel: { cityRuleList, cityRuleTotal },
        listLoading,

        operId,

        workorderEvent, //工单提交事件
        cooperationType: cooperationTypeCode,
        cacheRef,
    } = props;

    const [form] = Form.useForm();
    const editRef = useRef();
    const profitTemplateRef = useRef();
    const purchaseTemplateRef = useRef();

    const { cooperationPlatform } = useCooperationPlatform();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: 50,
        tabType: '02',
    });

    const path = useMemo(() => {
        if (cooperationTypeCode) {
            if (cooperationTypeCode === '01') {
                return 'profitRule';
            } else if (cooperationTypeCode === '02') {
                return 'purchase';
            }
        }
        const href = window.location.href;
        let pathName = '';
        if (href.indexOf('profitRule') > -1) {
            // 分润
            pathName = 'profitRule';
        } else if (href.indexOf('purchase') > -1) {
            // 购电
            pathName = 'purchase';
        }
        return pathName;
    }, [cooperationTypeCode]);

    const cooperationType = useMemo(() => {
        if (cooperationTypeCode) {
            return cooperationTypeCode;
        }
        const href = window.location.href;
        let cooperationType = '';
        if (href.indexOf('profitRule') > -1) {
            // 分润
            cooperationType = '01';
        } else if (href.indexOf('purchase') > -1) {
            // 购电
            cooperationType = '02';
        }
        return cooperationType;
    }, [cooperationTypeCode]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = (isDownload) => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: (!isDownload && pageInfo.pageIndex) || undefined,
            pageSize: (!isDownload && pageInfo.pageSize) || undefined,
            operId,
            ruleModel: data.ruleModel ? data.ruleModel.join(',') : '',
            city: data.city || '',
            cooperationType: cooperationType,
            cooperationPlatform,
        };
        const stationList = [...(data.stationIdList || [])];
        if (data.stationIds && stationList?.indexOf(Number(data.stationIds)) == -1) {
            stationList.push(data.stationIds);
        }
        params.stationIds = stationList.join?.(',') || data.stationIds;

        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.ruleStatus = pageInfo.tabType;
        }
        if (isDownload) {
            cacheRef.current?.apply(params).then(() => cacheRef.current.count());
        } else {
            dispatch({
                type: 'cityRuleModel/getCityRuleList',
                options: params,
            });
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const otherMainColumns = useMemo(() => {
        if (cooperationType == '01') {
            return [
                {
                    title: '分润方式',
                    width: 240,
                    dataIndex: 'ruleName',
                    render(text, record) {
                        return (
                            <span style={{ whiteSpace: 'pre' }} title={text}>
                                {text}
                            </span>
                        );
                    },
                },
                {
                    title: '分润关系',
                    width: 120,
                    dataIndex: 'ruleRelName',
                    render(text, record) {
                        return <span title={text}>{text}</span>;
                    },
                },
            ];
        } else if (cooperationType == '02') {
            return [
                {
                    title: '购电结算方式',
                    width: 240,
                    dataIndex: 'ruleName',
                    render(text, record) {
                        return (
                            <span
                                style={{
                                    whiteSpace: 'pre-wrap',
                                    wordBreak: 'break-all',
                                }}
                                title={text}
                            >
                                {text}
                            </span>
                        );
                    },
                },
            ];
        }
        return [];
    }, [cooperationType]);

    const columns = [
        {
            title: '城市',
            width: 120,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...otherMainColumns,
        {
            title: '溢价分成规则',
            width: 140,
            dataIndex: 'premiumString',
        },
        {
            title: '生效时间',
            width: 220,
            dataIndex: 'effTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '失效时间',
            width: 220,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}> {(text?.length && text) || record.maxExpTime}</span>;
            },
        },
        {
            title: '规则范围',
            width: 140,
            dataIndex: 'ruleRangeName',
            render(text, record) {
                return (
                    (record.stationNames && (
                        <Tooltip
                            title={
                                <div
                                    style={{
                                        maxWidth: '620px',
                                        maxHeight: '320px',
                                        overflowY: 'auto',
                                    }}
                                >
                                    {record.stationNames}
                                </div>
                            }
                        >
                            {text}
                        </Tooltip>
                    )) || <span title={text}>{text}</span>
                );
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'ruleStatusName',
            render(text = '-', record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '所属模板',
            width: 200,
            dataIndex: 'templateName',
            render(text = '-', record) {
                if (record.templateId) {
                    return (
                        <a
                            onClick={() => {
                                if (cooperationType == '01') {
                                    profitTemplateRef.current?.show(record.templateId);
                                } else if (cooperationType == '02') {
                                    purchaseTemplateRef.current?.show(record.templateId);
                                }
                            }}
                        >
                            {text}
                        </a>
                    );
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (text, record) =>
                record?.ruleStatus != '03' && (
                    <Fragment>
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                editRef?.current?.edit({ item: record, operId, type: '03' });
                            }}
                        >
                            编辑
                        </span>
                    </Fragment>
                ),
        },
    ];

    return (
        <Fragment>
            <Card>
                <SearchLayout
                    form={form}
                    operId={operId}
                    cooperationType={cooperationType}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={cacheRef ? () => searchData(true) : undefined}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => {
                            editRef?.current?.add({ operId, type: '03' });
                        }}
                    >
                        <PlusOutlined />
                        新增
                    </Button>

                    <Button>
                        <Link
                            to={
                                window.location.href.indexOf('profitRule') > -1
                                    ? `/sellerCenter/operatormanage/profitRule/list/templateManage`
                                    : `/sellerCenter/operatormanage/purchase/list/templateManage`
                            }
                            target="_blank"
                        >
                            模板管理
                        </Link>
                    </Button>
                </Space>
                <Tabs defaultActiveKey={pageInfo?.tabType || '02'} onChange={changeTabTypeEvent}>
                    {/* <TabPane tab="全部" key={STATUS_TYPES.ALL} /> */}
                    <TabPane tab="使用中" key={'02'} />
                    <TabPane tab="已失效" key={'03'} />
                </Tabs>
                <TablePro
                    name="list"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(r) => `${r.ruleId}${r.city}`}
                    dataSource={cityRuleList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: cityRuleTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [10, 50, 100, 500],
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>

            <EditRulesModal
                cooperationPlatform={cooperationPlatform}
                initRef={editRef}
                path={path}
                onEditFinish={searchData}
                workorderEvent={workorderEvent || undefined}
            />

            <ProfitTemplateListModal initRef={profitTemplateRef} />
            <PurchaseTemplateListModal initRef={purchaseTemplateRef} />
        </Fragment>
    );
};

const CityRuleListPage = (props) => {
    const { history, route, match } = props;
    const { operId } = match.params;
    const goBack = () => {
        history.go(-1);
    };
    const cacheRef = useRef();
    const exportType = useMemo(() => {
        const href = window.location.href;
        let cooperationType = '';
        if (href.indexOf('profitRule') > -1) {
            // 分润
            cooperationType = 'profitCityRuleExport';
        } else if (href.indexOf('purchase') > -1) {
            // 购电
            cooperationType = 'buyPowerCityRuleExport';
        }
        return cooperationType;
    }, []);

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
            extra={<CacheAreaView bizType={exportType} initRef={cacheRef} />}
        >
            <CityRuleListView {...props} closeEvent={goBack} operId={operId} cacheRef={cacheRef} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, cityRuleModel, loading }) => ({
    global,
    cityRuleModel,
    listLoading: loading.effects['cityRuleModel/getCityRuleList'],
    getInfoLoading: loading.effects['cityRuleModel/getRuleInfo'],
    importHistoryLoading: loading.effects['cityRuleModel/getImportHistoryList'],
    selectTemplateListLoading: loading.effects['cityRuleModel/getSelectTemplateInfo'],
}))(CityRuleListPage);
