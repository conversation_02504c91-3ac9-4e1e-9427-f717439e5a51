import {
    qryDetailCheckChargeOrdersApi,
    getDetailCheckChargeOrdersApi,
} from '@/services/FinanceManage/OrderCheckApi';

const orderCheckModel = {
    namespace: 'orderCheckModel',
    state: {
        orderCheckList: [], // 自动对账
        orderCheckListTotal: 0,
        editActInfo: {}, // 自动对账详情信息
    },
    effects: {
        /**
         * 自动对账
         */
        *getOrderCheckList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(qryDetailCheckChargeOrdersApi, options);

                yield put({
                    type: 'updateOrderCheckList',
                    orderCheckList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ icnCheckOrderDetailId }, { call, put, select }) {
            try {
                const { data } = yield call(getDetailCheckChargeOrdersApi, {
                    icnCheckOrderDetailId,
                });
                yield put({
                    type: 'updateEditActInfo',
                    info: data,
                });
            } catch (error) {
                console.log(6666, error);
            }
        },
    },
    reducers: {
        updateOrderCheckList(state, { orderCheckList, total }) {
            return {
                ...state,
                orderCheckList,
                orderCheckListTotal: total,
            };
        },

        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default orderCheckModel;
