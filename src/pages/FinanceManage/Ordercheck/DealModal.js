import { Modal, Form, Radio, Input, InputNumber, Row, Col, Space, Button } from 'antd';
import { useState, useEffect, useMemo } from 'react';
import { listIcnHandleStatusApi } from '@/services/FinanceManage/OrderCheckApi';

const { TextArea } = Input;
const FormItem = Form.Item;
const formItemLayout = {
    labelCol: {
        span: 4,
    },
    wrapperCol: {
        span: 16,
    },
};

const DealModal = (props) => {
    const {
        orderCheckModel: { editActInfo },
        info,
        visible,
        onFinish,
        onClose,
    } = props;
    const [form] = Form.useForm();
    const finishEvent = (values) => {
        onFinish && onFinish({ icnCheckOrderDetailId: info.icnCheckOrderDetailId, ...values });
    };
    const closeEvent = () => {
        onClose && onClose();
        form.resetFields();
    };
    const [handleStatusList, changeHandleStatusList] = useState([]);
    useEffect(() => {
        initHandleOptions();
        return () => {};
    }, []);
    useEffect(() => {
        if (editActInfo) {
            const initialValues = {
                handleStatus: editActInfo.handleStatus,
                diyTotalPower: editActInfo.diyTotalPower,
                diyTotalCharge: editActInfo.diyTotalCharge,
                diyTotalService: editActInfo.diyTotalService,
                abnormalExplain: editActInfo.abnormalExplain,
            };
            form.setFieldsValue(initialValues);
        }
    }, [editActInfo]);
    const initHandleOptions = async () => {
        try {
            const {
                data: { list },
            } = await listIcnHandleStatusApi();
            changeHandleStatusList(list);
        } catch (error) {}
    };
    const handleOptions = useMemo(
        () =>
            // 自定义codeValue 04
            handleStatusList.map((ele) => (
                <Radio key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Radio>
            )),
        [handleStatusList],
    );

    return (
        <Modal
            visible={visible}
            title="差异处理"
            width={800}
            footer={false}
            onCancel={onClose}
            maskClosable={false}
        >
            <Form
                {...formItemLayout}
                form={form}
                onFinish={finishEvent}
                initialValues={{}}
                scrollToFirstError
            >
                <FormItem label="差异处理状态">{info.checkStatusName}</FormItem>
                <FormItem
                    label="处理方式"
                    name="handleStatus"
                    rules={[
                        {
                            required: true,
                            message: '请选择',
                        },
                    ]}
                >
                    <Radio.Group>{handleOptions}</Radio.Group>
                </FormItem>
                <FormItem
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.handleStatus !== curValues.handleStatus
                    }
                >
                    {({ getFieldValue }) => {
                        const handleStatus = getFieldValue('handleStatus');
                        return handleStatus == '04' ? (
                            <FormItem
                                {...{
                                    wrapperCol: {
                                        span: 16,
                                        offset: 4,
                                    },
                                }}
                            >
                                <Row>
                                    <Col span={8}>
                                        <FormItem label="电量">
                                            <FormItem
                                                name="diyTotalPower"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择',
                                                    },
                                                ]}
                                                noStyle
                                            >
                                                <InputNumber step={0.01} />
                                            </FormItem>
                                            Kwh
                                        </FormItem>
                                    </Col>
                                    <Col span={8}>
                                        <FormItem label="电费">
                                            <FormItem
                                                name="diyTotalCharge"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择',
                                                    },
                                                ]}
                                                noStyle
                                            >
                                                <InputNumber step={0.01} />
                                            </FormItem>
                                            元
                                        </FormItem>
                                    </Col>
                                    <Col span={8}>
                                        <FormItem label="服务费">
                                            <FormItem
                                                name="diyTotalService"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择',
                                                    },
                                                ]}
                                                noStyle
                                            >
                                                <InputNumber step={0.01} />
                                            </FormItem>
                                            元
                                        </FormItem>
                                    </Col>
                                </Row>
                            </FormItem>
                        ) : null;
                    }}
                </FormItem>

                <FormItem label="差异处理说明" name="abnormalExplain">
                    <TextArea row={4} placeholder="请填写" />
                </FormItem>
                <FormItem
                    {...{
                        wrapperCol: {
                            span: 16,
                            offset: 4,
                        },
                    }}
                >
                    <Space>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default DealModal;
