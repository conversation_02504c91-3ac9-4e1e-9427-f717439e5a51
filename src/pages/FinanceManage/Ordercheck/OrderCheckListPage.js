import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    Alert,
    // Divider,
    Spin,
    Space,
    Input,
    DatePicker,
    Tooltip,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { InfoCircleOutlined } from '@ant-design/icons';
import OperSelectItem from '@/components/OperSelectItem';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import {
    listIcnCheckTypeApi,
    listIcnCheckStatusApi,
    qryDetailCheckChargeOrdersPath,
    handleCheckChargeOrdersApi,
} from '@/services/FinanceManage/OrderCheckApi';
import debounce from 'lodash/debounce';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

import DealModal from './DealModal';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

// 活动状态
const STATUS_TYPES = {
    ALL: '00',
    SUCCESS: '1', // 对账成功
    FAIL: '0', // 对账失败
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, listLoading } = props;

    const [checkTypeList, changeCheckTypeList] = useState([]);

    const [checkStatusList, changeCheckStatusList] = useState([]);

    useEffect(() => {
        initOptions();
    }, []);

    const initOptions = async () => {
        try {
            const {
                data: { list: checkTypes },
            } = await listIcnCheckTypeApi();
            changeCheckTypeList(checkTypes);

            const {
                data: { list: checkStatus },
            } = await listIcnCheckStatusApi();
            changeCheckStatusList(checkStatus);
        } catch (error) {}
    };

    const checkTypeOptions = useCallback(
        () =>
            checkTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [checkTypeList],
    );

    const checkStatusOptions = useCallback(
        () =>
            checkStatusList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [checkStatusList],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{}}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    minSpan={40}
                >
                    <Col span={8}>
                        <FormItem
                            label="对账日期:"
                            name="orderCheckDate"
                            {...formItemLayout}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.resolve();
                                        }
                                        if (value) {
                                            if (!value[0]) {
                                                return Promise.reject('请选择开始日期');
                                            }
                                            if (!value[1]) {
                                                return Promise.reject('请选择结束日期');
                                            }
                                            if (value[0] && value[1]) {
                                                const startTime = +new Date(value[0]);
                                                const endTime = +new Date(value[1]);
                                                const dest = 60 * 1000 * 60 * 24 * 60;

                                                if (Math.abs(startTime - endTime) > dest) {
                                                    return Promise.reject('选取范围最大不超过60天');
                                                }
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="平台订单日期:"
                            name="platformOrderDate"
                            {...formItemLayout}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.resolve();
                                        }
                                        if (value) {
                                            if (!value[0]) {
                                                return Promise.reject('请选择开始日期');
                                            }
                                            if (!value[1]) {
                                                return Promise.reject('请选择结束日期');
                                            }
                                            if (value[0] && value[1]) {
                                                const startTime = +new Date(value[0]);
                                                const endTime = +new Date(value[1]);
                                                const dest = 60 * 1000 * 60 * 24 * 60;

                                                if (Math.abs(startTime - endTime) > dest) {
                                                    return Promise.reject('选取范围最大不超过60天');
                                                }
                                            }
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="运营商订单日期:"
                            name="operatorOrderDate"
                            {...formItemLayout}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.resolve();
                                        }
                                        if (value) {
                                            if (!value[0]) {
                                                return Promise.reject('请选择开始日期');
                                            }
                                            if (!value[1]) {
                                                return Promise.reject('请选择结束日期');
                                            }
                                            if (value[0] && value[1]) {
                                                const startTime = +new Date(value[0]);
                                                const endTime = +new Date(value[1]);
                                                const dest = 60 * 1000 * 60 * 24 * 60;

                                                if (Math.abs(startTime - endTime) > dest) {
                                                    return Promise.reject('选取范围最大不超过60天');
                                                }
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="对账类型:" name="checkType" {...formItemLayout}>
                            <Select placeholder="请选择">{checkTypeOptions()}</Select>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="平台订单号:" name="orderNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="运营商订单号:" name="startChargeSeq" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="异常处理状态:" name="checkStatus" {...formItemLayout}>
                            <Select placeholder="请选择">{checkStatusOptions()}</Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <OperSelectTypeItem {...formItemLayout} form={form} />
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const OrderTransactionListPage = (props) => {
    const {
        dispatch,
        history,
        orderCheckModel: { orderCheckList, orderCheckListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: STATUS_TYPES.ALL,
    });

    const [visibleModal, changeVisibleModal] = useState(false);

    const [modalInfo, changeModalInfo] = useState({
        icnCheckOrderDetailId: null,
        detailEditable: false,
        checkStatusName: '',
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId || '',
                orderNo: data.orderNo || '',

                checkDateBgn:
                    (data.orderCheckDate &&
                        data.orderCheckDate[0] &&
                        data.orderCheckDate[0].format('YYYY-MM-DD')) ||
                    '',
                checkDateEnd:
                    (data.orderCheckDate &&
                        data.orderCheckDate[1] &&
                        data.orderCheckDate[1].format('YYYY-MM-DD')) ||
                    '',
                platformOrderDateBgn:
                    (data.platformOrderDate &&
                        data.platformOrderDate[0] &&
                        data.platformOrderDate[0].format('YYYY-MM-DD')) ||
                    '',
                platformOrderDateEnd:
                    (data.platformOrderDate &&
                        data.platformOrderDate[1] &&
                        data.platformOrderDate[1].format('YYYY-MM-DD')) ||
                    '',
                operatorOrderDateBgn:
                    (data.operatorOrderDate &&
                        data.operatorOrderDate[0] &&
                        data.operatorOrderDate[0].format('YYYY-MM-DD')) ||
                    '',
                operatorOrderDateEnd:
                    (data.operatorOrderDate &&
                        data.operatorOrderDate[1] &&
                        data.operatorOrderDate[1].format('YYYY-MM-DD')) ||
                    '',

                checkType: data.checkType || '',
                startChargeSeq: data.startChargeSeq || '',
                checkStatus: data.checkStatus || '',
            };
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.checkResult = pageInfo.tabType;
            }
            dispatch({
                type: 'orderCheckModel/getOrderCheckList',
                options: params,
            });
        } catch (error) {}
    };

    const showDetail = (record) => {
        // this.setState({
        //     icnCheckOrderDetailId: record.icnCheckOrderDetailId,
        //     modalVisible: true,
        //     detailEditable: record.checkStatus == '01' ? true : false,
        //     checkStatusName: record.checkStatusName,
        // });
        changeModalInfo({
            icnCheckOrderDetailId: record.icnCheckOrderDetailId,
            detailEditable: record.checkStatus == '01',
            checkStatusName: record.checkStatusName,
        });
        // const { dispatch } = this.props;
        if (record.checkStatus == '02') {
            // 展示已处理的
            dispatch({
                type: 'orderCheckModel/initEditActInfo',
                icnCheckOrderDetailId: record.icnCheckOrderDetailId,
            });
        } else {
            // 未处理的
            dispatch({
                type: 'orderCheckModel/updateEditActInfo',
                info: {},
            });
        }
        changeVisibleModal(true);
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                operId: data.operId || '',
                orderNo: data.orderNo || '',

                checkDateBgn:
                    (data.orderCheckDate &&
                        data.orderCheckDate[0] &&
                        data.orderCheckDate[0].format('YYYY-MM-DD')) ||
                    '',
                checkDateEnd:
                    (data.orderCheckDate &&
                        data.orderCheckDate[1] &&
                        data.orderCheckDate[1].format('YYYY-MM-DD')) ||
                    '',
                platformOrderDateBgn:
                    (data.platformOrderDate &&
                        data.platformOrderDate[0] &&
                        data.platformOrderDate[0].format('YYYY-MM-DD')) ||
                    '',
                platformOrderDateEnd:
                    (data.platformOrderDate &&
                        data.platformOrderDate[1] &&
                        data.platformOrderDate[1].format('YYYY-MM-DD')) ||
                    '',
                operatorOrderDateBgn:
                    (data.operatorOrderDate &&
                        data.operatorOrderDate[0] &&
                        data.operatorOrderDate[0].format('YYYY-MM-DD')) ||
                    '',
                operatorOrderDateEnd:
                    (data.operatorOrderDate &&
                        data.operatorOrderDate[1] &&
                        data.operatorOrderDate[1].format('YYYY-MM-DD')) ||
                    '',

                checkType: data.checkType || '',
                startChargeSeq: data.startChargeSeq || '',
                checkStatus: data.checkStatus || '',
            };
            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.checkResult = pageInfo.tabType;
            }
            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.name || item.title,
                    });
                }
            }

            exportTableByParams({
                methodUrl: qryDetailCheckChargeOrdersPath,
                options: params,
                columnsStr: columnsStrs,
            });
            // dispatch({
            //     type: 'global/exportXml',
            //     methodUrl: qryDetailCheckChargeOrdersPath,
            //     options: params,
            //     columnsStr: columnsStrs,
            // });
        } catch (error) {}
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const modalFinish = async (values) => {
        try {
            await handleCheckChargeOrdersApi(values);
            changeVisibleModal(false);
            searchData();
        } catch (error) {}
    };

    const columns = [
        {
            title: '对账时间',
            width: 200,
            dataIndex: 'checkTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '对账类型',
            width: 200,
            dataIndex: 'checkTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商名称',
            width: 200,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台订单号',
            width: 200,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台订单日期',
            width: 200,
            dataIndex: 'orderNoDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台充电量',
            width: 150,
            dataIndex: 'myTotalPower',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台订单金额',
            width: 200,
            dataIndex: 'myTotalMoney',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '互联互通订单号',
            width: 200,
            dataIndex: 'startChargeSeq',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商订单日期',
            width: 150,
            dataIndex: 'startChargeSeqDate',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商充电量',
            width: 150,
            dataIndex: 'totalPower',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商订单金额',
            width: 150,
            dataIndex: 'totalMoney',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '对账结果',
            width: 150,
            dataIndex: 'checkResultName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '对账说明',
            width: 150,
            dataIndex: 'checkExplain',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '异常处理状态',
            width: 150,
            dataIndex: 'checkStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '处理方式',
            width: 150,
            dataIndex: 'handleStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 140,
            dataIndex: 'oper',
            render: (text, record) => (
                <Fragment>
                    {record.checkResult == '0' ? (
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                showDetail(record);
                            }}
                        >
                            异常处理
                        </span>
                    ) : null}
                </Fragment>
            ),
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="对账失败" key={STATUS_TYPES.FAIL} />
                    <TabPane tab="对账成功" key={STATUS_TYPES.SUCCESS} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.icnCheckOrderDetailId}
                    dataSource={orderCheckList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: orderCheckListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
                <DealModal
                    {...props}
                    info={modalInfo}
                    visible={visibleModal}
                    onFinish={modalFinish}
                    onClose={() => {
                        changeVisibleModal(false);
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ orderCheckModel, global, loading }) => ({
    orderCheckModel,
    global,
    listLoading: loading.effects['orderCheckModel/getOrderCheckList'],
}))(OrderTransactionListPage);
