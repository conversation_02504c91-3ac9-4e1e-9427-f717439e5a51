import {
    getProfitRuleListApi,
    getRuleInfoApi,
    getStationRuleListApi,
    resertStationRuleByIdsApi,
    getProfitRuleTemplateListApi,
    getRuleTemplateDetilsApi,
    getTemplateSelectListApi,
    getProfitRuleHistoryApi,
} from '@/services/FinanceManage/ProfitRuleApi';
import { SessionStorage } from '@/utils/SessionStorage/index';

const PurchaseModel = {
    namespace: 'purchaseModel',
    state: {
        purchaseList: [], // 分润规则列表
        purchaseListTotal: 0, // 分润规则列表总条数
        selectRuleInfo: SessionStorage.getItem('purchaseModel/selectRuleInfo') || {},
        editRuleInfo: {}, // 当前编辑规则信息
        stationRuleList: [], // 场站规则列表
        stationRuleListTotal: 0, // 场站规则列表总条数
        ruleTemplateList: [], // 编辑历史列表
        ruleTemplateListTotal: 0, // 编辑历史列表总条数
        editTemplateInfo: null, // 当前编辑模板信息

        editHistoryList: [], // 编辑历史列表
        editHistoryListTotal: 0, // 编辑历史列表总条数

        importHistoryList: [], // 导入历史列表
        importHistoryListTotal: 0, // 导入历史列表总条数

        templateSelectList: [], // 可用模板列表
        selectTemplateInfo: null, // 当前选择模板信息
    },
    effects: {
        /**
         * 分润规则列表
         */
        *purchaseList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getProfitRuleListApi, options);

                yield put({
                    type: 'updatePurchaseList',
                    purchaseList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 获取规则信息
         */
        *getRuleInfo({ params }, { call, put, select }) {
            try {
                const { data } = yield call(getRuleInfoApi, params);
                yield put({
                    type: 'updatePurchaseInfo',
                    editRuleInfo: data,
                });
            } catch (error) {}
        },
        /**
         * 场站列表
         */
        *getStationRuleList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getStationRuleListApi, options);
                yield put({
                    type: 'updateStationRuleList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 重置场站规则
         */
        *resetStationRuleByIds({ stationIds, options }, { call, put, select }) {
            try {
                yield call(resertStationRuleByIdsApi, { stationIds, ...options });
                const {
                    data: { list, total },
                } = yield call(getStationRuleListApi, options);
                yield put({
                    type: 'updateStationRuleList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 配置记录列表
         */
        *getEditHistoryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getProfitRuleHistoryApi, options);
                yield put({
                    type: 'updateEditHistoryList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 模板列表
         */
        *getRuleTemplateList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getProfitRuleTemplateListApi, options);
                yield put({
                    type: 'updateRuleTemplateList',
                    list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 获取模板信息
         */
        *getTemplateInfo({ templateId }, { call, put, select }) {
            try {
                const { data } = yield call(getRuleTemplateDetilsApi, templateId);
                yield put({
                    type: 'updatePurchaseTemplateInfo',
                    editTemplateInfo: data,
                });
            } catch (error) {}
        },

        /**
         * 获取下拉模板列表
         */
        *getSelectTemplateList({ cooperationType }, { call, put, select }) {
            try {
                const {
                    data: { templateList },
                } = yield call(getTemplateSelectListApi, { cooperationType });
                yield put({
                    type: 'updateTemplateSelectList',
                    list: templateList,
                });
            } catch (error) {}
        },
        /**
         * 获取模板信息
         */
        *getSelectTemplateInfo({ templateId }, { call, put, select }) {
            try {
                const { data } = yield call(getRuleTemplateDetilsApi, templateId);
                yield put({
                    type: 'updateSelectTemplateInfo',
                    info: data,
                });
            } catch (error) {}
        },
    },
    reducers: {
        setSelectRuleInfo(state, { info }) {
            SessionStorage.setItem('purchaseModel/selectRuleInfo', info);
            return {
                ...state,
                selectRuleInfo: info,
            };
        },
        updatePurchaseInfo(state, { editRuleInfo }) {
            return {
                ...state,
                editRuleInfo,
            };
        },
        updatePurchaseList(state, { purchaseList, total }) {
            return {
                ...state,
                purchaseList,
                purchaseListTotal: total,
            };
        },

        updateEditHistoryList(state, { list, total }) {
            return {
                ...state,
                editHistoryList: list,
                editHistoryListTotal: total,
            };
        },
        updateImportHistoryList(state, { list, total }) {
            return {
                ...state,
                importHistoryList: list,
                importHistoryListTotal: total,
            };
        },
        updateStationRuleList(state, { list, total }) {
            return {
                ...state,
                stationRuleList: list,
                stationRuleListTotal: total,
            };
        },
        updateRuleTemplateList(state, { list, total }) {
            return {
                ...state,
                ruleTemplateList: list,
                ruleTemplateListTotal: total,
            };
        },
        updatePurchaseTemplateInfo(state, { editTemplateInfo }) {
            return {
                ...state,
                editTemplateInfo,
            };
        },
        updateTemplateSelectList(state, { list }) {
            return {
                ...state,
                templateSelectList: list,
            };
        },
        updateSelectTemplateInfo(state, { info }) {
            return {
                ...state,
                selectTemplateInfo: info,
            };
        },
    },
};
export default PurchaseModel;
