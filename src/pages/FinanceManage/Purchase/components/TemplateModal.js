import { Button, Card, Form, message, Modal } from 'antd';
import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import moment from 'moment';
import styles from '../PurchaseListPage.less';
import TemplateListFormItem from './TemplateListFormItem';

import { addRuleTemplateApi, updateRuleTemplateApi } from '@/services/FinanceManage/ProfitRuleApi';
import { OPER_COOPERATION } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import { CollectTypes, computedLongShortValue } from '../PurchaseConfig';

const formItemLayout = {};

const TemplateModal = (props, ref) => {
    const {
        dispatch,
        profitRuleModel: { editTemplateInfo },
        onFinish,
        initialAgreeType = '02',
    } = props;

    const [templateForm] = Form.useForm();
    const [infoLoading, updateInfoLoading] = useState(false);
    const [showEditView, toggleShowEditView] = useState(false);

    useImperativeHandle(ref, () => ({
        show: (record) => {
            templateForm.resetFields();
            toggleShowEditView(true);
            dispatch({
                type: 'purchaseModel/updatePurchaseTemplateInfo',
                editTemplateInfo: undefined,
            });
            if (record?.templateId) {
                updateInfoLoading(true);
                dispatch({
                    type: 'purchaseModel/getTemplateInfo',
                    templateId: record.templateId,
                }).then(() => {
                    updateInfoLoading(false);
                });
            }
        },
        close: () => {
            toggleShowEditView(false);
        },
    }));

    useEffect(() => {
        if (editTemplateInfo && showEditView) {
            const params = {
                name: editTemplateInfo.name,
                templateId: editTemplateInfo.templateId,
                detailList: editTemplateInfo.detailList.map((ele) => {
                    let item = {
                        effTime: moment(ele.effTime),
                        expTime: moment(ele.expTime),
                    };
                    if (ele.profitRuleTypeList[0]) {
                        item = {
                            ...item,
                            ...ele.profitRuleTypeList[0],
                        };
                    }
                    return item;
                }),
            };
            templateForm.setFieldsValue(params);
        }
    }, [editTemplateInfo, showEditView]);

    const submitEvent = async (values) => {
        try {
            const param = {
                name: values.name,
                cooperationType: OPER_COOPERATION.BUY,
            };
            const detailList = values.detailList.map((ele) => {
                const profitRuleList = ele.profitRuleTypeList;

                const profitSlottingRuleInfo = ele.profitSlottingRules;

                const profitSlottingRules =
                    profitSlottingRuleInfo?.list?.filter((ele) => ele.slottingFlag) || [];

                const filterRuleList =
                    profitRuleList.filter((element) => element && !isEmpty(element.ruleModel)) ||
                    [];

                const profitRuleTypes = filterRuleList.map((element) => {
                    let options = {
                        ...element,
                        ruleModel: String(element.ruleModel),
                    };
                    if (element.agreeType instanceof Array && element.agreeType.length === 1) {
                        const ruleInfo = computedLongShortValue(element);
                        if (ruleInfo) {
                            options = {
                                ...options,
                                ...ruleInfo,
                            };
                        }
                    }

                    return options;
                });

                return {
                    effTime: (ele.effTime && ele.effTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    expTime: (ele.expTime && ele.expTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    ruleFlag: '1',
                    profitRuleTypes: profitRuleTypes,
                    profitSlottingRules: profitSlottingRules,
                    sn: ele.sn,
                    ruleRel: '',
                };
            });
            param.detailJsonStr = JSON.stringify(detailList);

            if (editTemplateInfo && editTemplateInfo.templateId) {
                param.templateId = editTemplateInfo.templateId;
                await updateRuleTemplateApi(param);
                message.success('编辑成功');
            } else {
                await addRuleTemplateApi(param);
                message.success('新建成功');
            }

            onFinish();
            ref?.current?.close();
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Modal
            title="配置模板"
            width={1200}
            visible={showEditView}
            onCancel={() => {
                ref?.current?.close();
            }}
            destroyOnClose
            footer={null}
            maskClosable={false}
        >
            <Card loading={infoLoading} bordered={false}>
                <Form
                    {...formItemLayout}
                    form={templateForm}
                    onFinish={submitEvent}
                    initialValues={{
                        detailList: [{}, {}],
                    }}
                    scrollToFirstError
                >
                    <div
                        className="overscroll"
                        style={{
                            maxHeight: `${document.body.clientHeight - 270}px`,
                            overflowY: 'auto',
                        }}
                    >
                        <TemplateListFormItem
                            form={templateForm}
                            name="detailList"
                            cooperationType={OPER_COOPERATION.BUY}
                            templateInfo={editTemplateInfo}
                            isEdit={!!editTemplateInfo}
                            initialAgreeType={initialAgreeType}
                        />
                    </div>
                    <div className={styles['form-submit']}>
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            // loading={submitLoading}
                        >
                            提交
                        </Button>
                        <Button
                            className={styles['form-btn']}
                            onClick={() => {
                                ref?.current?.close();
                            }}
                        >
                            取消
                        </Button>
                    </div>
                </Form>
            </Card>
        </Modal>
    );
};

export default forwardRef(TemplateModal);
