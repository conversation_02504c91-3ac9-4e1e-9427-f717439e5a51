import { Modal } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

import TemplateListCard from './TemplateListCard';

interface Props {
    //选择完毕确认回调事件
    onFinish?: (value: string) => void;
}
const SelectRuleModal = (props: Props, ref: any) => {
    const { onFinish } = props;

    const [visible, setVisible] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
        show: () => {
            setVisible(true);
        },
        close: () => {
            setVisible(false);
        },
    }));

    const onCancel = () => {
        setVisible(false);
    };

    const onSelect = (value: string) => {
        if (value) {
            onFinish && onFinish(value);
        }
        onCancel();
    };

    return (
        <>
            <Modal
                visible={visible}
                title="选择模板"
                footer={null}
                width={1100}
                onCancel={onCancel}
                destroyOnClose
            >
                <TemplateListCard canSelect onSelect={onSelect} pageSize={5} />
            </Modal>
        </>
    );
};

export default forwardRef(SelectRuleModal);
