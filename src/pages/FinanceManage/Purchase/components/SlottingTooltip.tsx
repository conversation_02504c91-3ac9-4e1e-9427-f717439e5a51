import { Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
const SlottingTooltip = () => {
    return (
        <Tooltip
            title={
                <div>
                    <p>
                        结算前、结算后计算通道费示例:
                        例如附加费10元，按9折与商家结算，通道费收取0.6%
                    </p>
                    <p>按结算前的通道费=10*0.6%</p>
                    <p>按结算后的通道费=10*90%*0.6%</p>
                </div>
            }
        >
            <InfoCircleOutlined
                style={{
                    marginLeft: '6px',
                }}
            />
        </Tooltip>
    );
};
export default SlottingTooltip;
