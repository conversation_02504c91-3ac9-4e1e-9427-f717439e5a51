import { Switch, Radio, InputNumber, Space, Typography } from 'antd';
import React from 'react';
import { Fragment, useEffect, useState } from 'react';

import { RULE_MODEL_TYPES } from '../PurchaseConfig';

import SlottingItem from './SlottingItem';
import { SlottingTypes, checkItemValue } from './SlottingItem';
import type { SlottingConfig } from './SlottingItem';
import { copyObjectCommon } from '@/utils/utils';

import { isEmpty } from '@/utils/utils';

type SlottingRule = {
    collectType: CollectTypes;
    list: SlottingConfig[];
};

export enum CollectTypes {
    DETAIL = '01',
    ORDER = '02',
}

interface Props {
    ruleModel: string;
    value: SlottingRule | undefined;
    onChange: (value: SlottingRule) => void;
    disabled?: boolean; //是否禁用编辑
}

export const defaultSlottingOrderInfo: SlottingConfig[] = [
    {
        ruleModel: RULE_MODEL_TYPES.ELEORDER,
        slottingFlag: false,
        slottingBaseType: SlottingTypes.Before,
        slottingRuleValue: 0.6,
    },
    {
        ruleModel: RULE_MODEL_TYPES.SERVICE,
        slottingFlag: false,
        slottingBaseType: SlottingTypes.Before,
        slottingRuleValue: 0.6,
    },
    {
        ruleModel: RULE_MODEL_TYPES.ADD,
        slottingFlag: true,
        slottingBaseType: SlottingTypes.Before,
        slottingRuleValue: 0.6,
    },
];
const defaultPriceInfo: SlottingConfig[] = [
    {
        ruleModel: RULE_MODEL_TYPES.ORDER,
        slottingFlag: true,
        slottingBaseType: SlottingTypes.Before,
        slottingRuleValue: 0.6,
    },
];

export const defaultRuleValue = {
    collectType: CollectTypes.DETAIL,
    list: defaultSlottingOrderInfo,
};

export const checkRuleValue = (ruleInfo: SlottingRule) => {
    for (let index = 0; index < ruleInfo.list.length; index++) {
        const element = ruleInfo.list[index];
        const errText = checkItemValue(element);
        if (errText) {
            return errText;
        }
    }
    return;
};

const SlottingRuleFormItem: React.FC<Props> = (props) => {
    const { value, onChange, disabled } = props;
    const [slottingRuleInfo, updateSlottingRuleValue] = useState<SlottingRule>(defaultRuleValue);

    const updateFormValue = (info: SlottingRule) => {
        updateSlottingRuleValue(info);
        if (JSON.stringify(slottingRuleInfo) != JSON.stringify(info)) {
            onChange && onChange(info);
        }
    };

    useEffect(() => {
        if (!isEmpty(value)) {
            initSlottingRule(value);
        }
    }, [value]);

    const initSlottingRule = (info: SlottingRule = defaultRuleValue) => {
        const { list } = info;
        const newList: SlottingConfig[] = [];

        for (const item of defaultRuleValue.list) {
            const ruleInfo = list.find((ele) => ele.ruleModel == item.ruleModel);

            if (ruleInfo) {
                const newItem: SlottingConfig = copyObjectCommon(ruleInfo);
                if (typeof newItem.slottingFlag === 'undefined') {
                    newItem.slottingFlag = true;
                }
                newList.push(newItem);
            } else {
                newList.push(item);
            }
        }

        const params = {
            collectType: info.collectType,
            list: newList,
        };
        updateFormValue(params);
    };

    return (
        <Fragment>
            <Radio.Group
                disabled={disabled}
                value={slottingRuleInfo?.collectType}
                onChange={(event) => {
                    const {
                        target: { value: newValue },
                    } = event;
                    updateFormValue({
                        ...slottingRuleInfo,
                        collectType: newValue,
                        list:
                            newValue === CollectTypes.ORDER
                                ? defaultPriceInfo
                                : defaultSlottingOrderInfo,
                    });
                }}
            >
                <Radio value={CollectTypes.DETAIL}>按订单细项</Radio>
                {/* <Radio value={CollectTypes.ORDER}>按订单总金额</Radio> */}
            </Radio.Group>
            {slottingRuleInfo?.list?.map((ele, index) => {
                return (
                    <div key={index}>
                        <SlottingItem
                            disabled={disabled}
                            ruleModel={ele.ruleModel}
                            value={ele}
                            onChange={(newValue) => {
                                const copyList: SlottingConfig[] = copyObjectCommon(
                                    slottingRuleInfo.list,
                                );
                                copyList[index] = newValue;
                                updateFormValue({
                                    ...slottingRuleInfo,
                                    list: copyList,
                                });
                            }}
                        ></SlottingItem>
                    </div>
                );
            })}
        </Fragment>
    );
};
export default SlottingRuleFormItem;
