/**
 * 根据运营商查询对应城市下的站点
 */

import { Fragment, useCallback } from 'react';
import { Form, Button, Row, Col, Input, DatePicker, Tooltip, Space } from 'antd';
import moment from 'moment';
import { InfoCircleOutlined } from '@ant-design/icons';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import { OPER_COOPERATION } from '@/config/declare';
import { copyObjectCommon, isEmpty } from '@/utils/utils';

import PurchaseRuleFormItem, { checkRuleInfo } from './PurchaseRuleFormItem';
import SlottingRuleFormItem, { checkRuleValue, defaultRuleValue } from './SlottingRuleFormItem';
import SlottingTooltip from './SlottingTooltip';

const FormItem = Form.Item;

const TemplateListFormItem = (props) => {
    const {
        formItemLayout,
        form,
        templateInfo,
        label = '',
        name = 'templateList',
        cooperationType = OPER_COOPERATION.BUY,
        disabled,
        isEdit,
        rules,
        initialAgreeType = '02',
        isAdd = false,
    } = props;

    const canIUpdateCheck = useCallback(
        (index) => {
            // 是否可以编辑生效和失效时间
            const info = (templateInfo && templateInfo[name] && templateInfo[name][index]) || null;
            let canIUpdateStart = true;
            let canIUpdateEnd = true;
            if (info && !isAdd) {
                const nowTime = moment();
                if (info.effTime) {
                    const effTime = moment(info.effTime).add(3, 'minutes');
                    if (effTime.isBefore(nowTime)) {
                        canIUpdateStart = false;
                    }
                }
                if (info && info.expTime) {
                    const expTime = moment(info.expTime);

                    if (expTime.isBefore(nowTime)) {
                        canIUpdateEnd = false;
                    }
                }
            }
            return { canIUpdateStart, canIUpdateEnd };
        },
        [templateInfo, isAdd],
    );

    const canIadd = () => {
        const hasAdd = true;

        return !disabled && hasAdd;
    };

    const disabledTime = () => {
        return {};
    };

    const changeEndDateEvent = (event, index) => {
        const parentData = form.getFieldValue(name);
        const formatNewDate = copyObjectCommon(parentData);
        if (formatNewDate && formatNewDate[index + 1]) {
            formatNewDate[index + 1].effTime = event.add(1, 'seconds');
            const formatDateList = formatNewDate.map((ele) => {
                return {
                    ...ele,
                    effTime: ele.effTime ? moment(ele.effTime) : null,
                    expTime: ele.expTime ? moment(ele.expTime) : null,
                };
            });
            form.setFieldsValue({ [name]: formatDateList });
        }
    };

    return (
        <Fragment>
            <FormItem
                {...{ labelCol: { flex: '80px' }, wrapperCol: { span: 12 } }}
                label="模板名称"
                name={'name'}
                rules={[
                    {
                        required: true,
                        message: '请填写',
                    },
                ]}
            >
                <Input placeholder="请填写" autoComplete="off" maxLength={20}></Input>
            </FormItem>
            <Form.Item name={name} {...formItemLayout} label={label} rules={rules}>
                <Form.List name={name}>
                    {(fields, { add, remove }) => (
                        <Fragment>
                            {fields.map((field, index) => (
                                <Fragment key={index}>
                                    <Row gutter={20}>
                                        <Col flex="1">
                                            <FormItem
                                                {...{ labelCol: { flex: '80px' } }}
                                                label={
                                                    <span>
                                                        规则{index + 1}
                                                        <Tooltip title="同一模板内的规则生效周期必须是连续的时间段">
                                                            <InfoCircleOutlined />
                                                        </Tooltip>
                                                    </span>
                                                }
                                            >
                                                <FormItem
                                                    shouldUpdate={(prevValues, curValues) => true}
                                                    noStyle
                                                >
                                                    {({ getFieldValue }) => {
                                                        const parentData = getFieldValue(name);

                                                        // console.log(1111111, parentData, index);
                                                        let statDate = null;
                                                        if (
                                                            parentData[index - 1] &&
                                                            parentData[index - 1].expTime
                                                        ) {
                                                            statDate = moment(
                                                                parentData[index - 1].expTime,
                                                            ).add(1, 'seconds');
                                                        }

                                                        let disabledEditEffTime = false;
                                                        let disabledEditExpTime = false;
                                                        const { canIUpdateStart, canIUpdateEnd } =
                                                            canIUpdateCheck(index);
                                                        if (isEdit) {
                                                            const nowTime = moment();
                                                            if (
                                                                parentData[index] &&
                                                                parentData[index].effTime
                                                            ) {
                                                                const effTime = moment(
                                                                    parentData[index].effTime,
                                                                ).add(3, 'minutes');
                                                                if (effTime.isBefore(nowTime)) {
                                                                    disabledEditEffTime = true;
                                                                }
                                                            }
                                                            if (
                                                                parentData[index] &&
                                                                parentData[index].expTime
                                                            ) {
                                                                const expTime = moment(
                                                                    parentData[index].expTime,
                                                                );

                                                                if (expTime.isBefore(nowTime)) {
                                                                    disabledEditExpTime = true;
                                                                }
                                                            }
                                                        }

                                                        const disabledDate = (current) => {
                                                            const nowDate = moment();
                                                            let disabledStart = nowDate;

                                                            if (statDate) {
                                                                if (statDate.isAfter(nowDate)) {
                                                                    disabledStart = statDate;
                                                                }
                                                            }

                                                            // Can not select days before today and today

                                                            return (
                                                                current && current < disabledStart
                                                            );
                                                        };

                                                        return (
                                                            <Fragment>
                                                                <FormItem
                                                                    label="生效时间:"
                                                                    fieldKey={field.fieldKey}
                                                                    name={[field.name, 'effTime']}
                                                                    initialValue={statDate}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请填写生效时间',
                                                                        },
                                                                        (_) => ({
                                                                            validator(rule, value) {
                                                                                if (!value) {
                                                                                    return Promise.reject(
                                                                                        '',
                                                                                    );
                                                                                }
                                                                                if (
                                                                                    !disabledEditEffTime
                                                                                ) {
                                                                                    if (
                                                                                        index ==
                                                                                            0 &&
                                                                                        value.isBefore(
                                                                                            moment().add(
                                                                                                0,
                                                                                                'minutes',
                                                                                            ),
                                                                                        )
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '生效时间不能早于当前时间',
                                                                                        );
                                                                                    }
                                                                                }
                                                                                for (
                                                                                    let i = 0;
                                                                                    i <
                                                                                    parentData.length;
                                                                                    i++
                                                                                ) {
                                                                                    const element =
                                                                                        parentData[
                                                                                            i
                                                                                        ];
                                                                                    if (
                                                                                        i !=
                                                                                            index &&
                                                                                        index > i &&
                                                                                        element &&
                                                                                        element.expTime
                                                                                    ) {
                                                                                        if (
                                                                                            value.isSameOrBefore(
                                                                                                element.expTime,
                                                                                            )
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `已被${
                                                                                                    i +
                                                                                                    1
                                                                                                }包含,请修改时间`,
                                                                                            );
                                                                                            break;
                                                                                        }
                                                                                    }
                                                                                }
                                                                                const preData =
                                                                                    parentData[
                                                                                        index - 1
                                                                                    ];
                                                                                if (
                                                                                    preData &&
                                                                                    preData.expTime
                                                                                ) {
                                                                                    if (
                                                                                        !value.isSame(
                                                                                            moment(
                                                                                                preData.expTime,
                                                                                            ).add(
                                                                                                1,
                                                                                                'seconds',
                                                                                            ),
                                                                                        )
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            `开始时间必须是前一条规则的结束时间+1秒`,
                                                                                        );
                                                                                    }
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        disabled={
                                                                            !canIUpdateStart ||
                                                                            disabled
                                                                        }
                                                                        defaultPickerValue={
                                                                            statDate
                                                                        }
                                                                        disabledDate={disabledDate}
                                                                        showTime={{
                                                                            format: 'HH:mm:ss',
                                                                            defaultValue: moment(
                                                                                '00:00:00',
                                                                                'HH:mm:ss',
                                                                            ),
                                                                        }}
                                                                        format="YYYY-MM-DD HH:mm:ss"
                                                                        showNow={false}
                                                                        renderExtraFooter={(a) => (
                                                                            <Space>
                                                                                <Button
                                                                                    type="link"
                                                                                    onClick={() => {
                                                                                        const time =
                                                                                            moment().add(
                                                                                                10,
                                                                                                'seconds',
                                                                                            );
                                                                                        const parentData =
                                                                                            form.getFieldValue(
                                                                                                name,
                                                                                            );
                                                                                        if (
                                                                                            parentData &&
                                                                                            parentData[
                                                                                                index
                                                                                            ]
                                                                                        ) {
                                                                                            parentData[
                                                                                                index
                                                                                            ].effTime =
                                                                                                time;
                                                                                            form.setFieldsValue(
                                                                                                {
                                                                                                    [name]: parentData,
                                                                                                },
                                                                                            );
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    10秒之后
                                                                                </Button>
                                                                                <Button
                                                                                    type="link"
                                                                                    onClick={() => {
                                                                                        const parentData =
                                                                                            form.getFieldValue(
                                                                                                name,
                                                                                            );
                                                                                        if (
                                                                                            parentData &&
                                                                                            parentData[
                                                                                                index
                                                                                            ]
                                                                                        ) {
                                                                                            if (
                                                                                                parentData[
                                                                                                    index
                                                                                                ]
                                                                                                    .effTime
                                                                                            ) {
                                                                                                parentData[
                                                                                                    index
                                                                                                ].effTime =
                                                                                                    moment(
                                                                                                        parentData[
                                                                                                            index
                                                                                                        ]
                                                                                                            .effTime,
                                                                                                    ).startOf(
                                                                                                        'day',
                                                                                                    );
                                                                                                form.setFieldsValue(
                                                                                                    {
                                                                                                        [name]: parentData,
                                                                                                    },
                                                                                                );
                                                                                            }
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    时间重置
                                                                                </Button>
                                                                            </Space>
                                                                        )}
                                                                    />
                                                                </FormItem>
                                                                <FormItem
                                                                    label="失效时间:"
                                                                    fieldKey={field.fieldKey}
                                                                    name={[field.name, 'expTime']}
                                                                    required
                                                                    rules={[
                                                                        (_) => ({
                                                                            validator(rule, value) {
                                                                                const effTime =
                                                                                    parentData &&
                                                                                    parentData[
                                                                                        index
                                                                                    ] &&
                                                                                    parentData[
                                                                                        index
                                                                                    ].effTime;
                                                                                if (!value) {
                                                                                    return Promise.reject(
                                                                                        '请填写失效时间',
                                                                                    );
                                                                                }
                                                                                if (!effTime) {
                                                                                    return Promise.reject(
                                                                                        '请选择生效开始时间',
                                                                                    );
                                                                                }

                                                                                if (value) {
                                                                                    const startTime =
                                                                                        +new Date(
                                                                                            effTime,
                                                                                        );
                                                                                    const sendEndTime =
                                                                                        +new Date(
                                                                                            value,
                                                                                        );

                                                                                    if (
                                                                                        sendEndTime <=
                                                                                        startTime
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            '失效时间不能早于生效时间',
                                                                                        );
                                                                                    }
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        disabled={
                                                                            !canIUpdateEnd ||
                                                                            disabled
                                                                        }
                                                                        defaultPickerValue={
                                                                            parentData?.[index]
                                                                                ?.expTime
                                                                        }
                                                                        disabledDate={disabledDate}
                                                                        disabledTime={disabledTime}
                                                                        showTime={{
                                                                            format: 'HH:mm:ss',
                                                                            hideDisabledOptions: true,
                                                                            defaultValue: moment(
                                                                                '23:59:59',
                                                                                'HH:mm:ss',
                                                                            ),
                                                                        }}
                                                                        format="YYYY-MM-DD HH:mm:ss"
                                                                        onChange={(e) => {
                                                                            changeEndDateEvent(
                                                                                e,
                                                                                index,
                                                                            );
                                                                        }}
                                                                        showNow={false}
                                                                        renderExtraFooter={(a) => (
                                                                            <Space>
                                                                                <Button
                                                                                    type="link"
                                                                                    onClick={() => {
                                                                                        const time =
                                                                                            moment().add(
                                                                                                10,
                                                                                                'seconds',
                                                                                            );
                                                                                        const parentData =
                                                                                            form.getFieldValue(
                                                                                                name,
                                                                                            );
                                                                                        if (
                                                                                            parentData &&
                                                                                            parentData[
                                                                                                index
                                                                                            ]
                                                                                        ) {
                                                                                            parentData[
                                                                                                index
                                                                                            ].expTime =
                                                                                                time;
                                                                                            form.setFieldsValue(
                                                                                                {
                                                                                                    [name]: parentData,
                                                                                                },
                                                                                            );
                                                                                            changeEndDateEvent(
                                                                                                time,
                                                                                                index,
                                                                                            );
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    10秒之后
                                                                                </Button>
                                                                                <Button
                                                                                    type="link"
                                                                                    onClick={() => {
                                                                                        const parentData =
                                                                                            form.getFieldValue(
                                                                                                name,
                                                                                            );
                                                                                        if (
                                                                                            parentData &&
                                                                                            parentData[
                                                                                                index
                                                                                            ]
                                                                                        ) {
                                                                                            if (
                                                                                                parentData[
                                                                                                    index
                                                                                                ]
                                                                                                    .effTime
                                                                                            ) {
                                                                                                parentData[
                                                                                                    index
                                                                                                ].effTime =
                                                                                                    moment(
                                                                                                        parentData[
                                                                                                            index
                                                                                                        ]
                                                                                                            .effTime,
                                                                                                    ).startOf(
                                                                                                        'day',
                                                                                                    );
                                                                                                form.setFieldsValue(
                                                                                                    {
                                                                                                        [name]: parentData,
                                                                                                    },
                                                                                                );
                                                                                            }
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    时间重置
                                                                                </Button>
                                                                            </Space>
                                                                        )}
                                                                    />
                                                                </FormItem>
                                                                {cooperationType === '02' && (
                                                                    <Fragment>
                                                                        <FormItem
                                                                            fieldKey={
                                                                                field.fieldKey
                                                                            }
                                                                            name={[
                                                                                field.name,
                                                                                'ruleType',
                                                                            ]}
                                                                            initialValue={'02'}
                                                                            noStyle
                                                                        ></FormItem>
                                                                        <FormItem
                                                                            fieldKey={
                                                                                field.fieldKey
                                                                            }
                                                                            name={[
                                                                                field.name,
                                                                                'sn',
                                                                            ]}
                                                                            initialValue={index + 1}
                                                                            noStyle
                                                                        ></FormItem>
                                                                    </Fragment>
                                                                )}

                                                                <FormItem
                                                                    label="结算方式"
                                                                    name={[
                                                                        field.name,
                                                                        'profitRuleTypeList',
                                                                    ]}
                                                                    wrapperCol={{ span: 16 }}
                                                                    required
                                                                    rules={[
                                                                        () => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        `请选择购电结算方式`,
                                                                                    );
                                                                                }
                                                                                for (const item of value) {
                                                                                    const ruleErr =
                                                                                        checkRuleInfo(
                                                                                            item,
                                                                                        );
                                                                                    if (ruleErr) {
                                                                                        return Promise.reject(
                                                                                            ruleErr,
                                                                                        );
                                                                                    }
                                                                                }
                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <PurchaseRuleFormItem
                                                                        disabled={
                                                                            disabledEditEffTime ||
                                                                            disabled
                                                                        }
                                                                        initialAgreeType={[
                                                                            initialAgreeType,
                                                                        ]}
                                                                    ></PurchaseRuleFormItem>
                                                                </FormItem>
                                                                {/* <FormItem
                                                                    label={
                                                                        <span>
                                                                            通道费收取方式
                                                                            <SlottingTooltip></SlottingTooltip>
                                                                        </span>
                                                                    }
                                                                    name={[
                                                                        field.name,
                                                                        'profitSlottingRules',
                                                                    ]}
                                                                    wrapperCol={{ span: 24 }}
                                                                    required
                                                                    rules={[
                                                                        () => ({
                                                                            validator(rule, value) {
                                                                                if (
                                                                                    isEmpty(value)
                                                                                ) {
                                                                                    return Promise.reject(
                                                                                        `请配置通道费收取方式`,
                                                                                    );
                                                                                }
                                                                                const errText =
                                                                                    checkRuleValue(
                                                                                        value,
                                                                                    );
                                                                                if (errText) {
                                                                                    return Promise.reject(
                                                                                        errText,
                                                                                    );
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                    initialValue={defaultRuleValue}
                                                                >
                                                                    <SlottingRuleFormItem
                                                                        disabled={
                                                                            disabledEditEffTime ||
                                                                            disabled
                                                                        }
                                                                    ></SlottingRuleFormItem>
                                                                </FormItem> */}
                                                            </Fragment>
                                                        );
                                                    }}
                                                </FormItem>
                                            </FormItem>
                                        </Col>
                                        <Col flex="80px">
                                            {disabled || index < 2 ? null : (
                                                <div>
                                                    <MinusCircleOutlined
                                                        className={styles['dynamic-delete-button']}
                                                        style={{ margin: '0 8px' }}
                                                        onClick={() => {
                                                            remove(field.name);
                                                        }}
                                                    />
                                                </div>
                                            )}
                                        </Col>
                                    </Row>
                                </Fragment>
                            ))}
                            <FormItem style={{ paddingLeft: '80px' }}>
                                <Button
                                    disabled={!canIadd()}
                                    type="dashed"
                                    onClick={() => {
                                        add();
                                    }}
                                    style={{ width: '200px' }}
                                >
                                    <PlusOutlined />
                                    新增规则
                                </Button>
                            </FormItem>
                        </Fragment>
                    )}
                </Form.List>
            </Form.Item>
        </Fragment>
    );
};
export default TemplateListFormItem;
