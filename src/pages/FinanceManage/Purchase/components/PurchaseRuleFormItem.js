import { Fragment, forwardRef, useEffect, useImperativeHandle, useState, useMemo } from 'react';
import { Radio, Space, InputNumber, Checkbox, Form, Tooltip } from 'antd';
import Decimal from 'decimal.js';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import purchaseStyles from '../PurchaseListPage.less';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
    RULE_MODEL_TYPES,
    getRuleModelName,
    PURCHASE_RULE_TYPES,
    MAX_PRECISION,
} from '../PurchaseConfig';

const RADIO_TYPES = {
    DETAIL: '01',
    ALL: '02',
};
const FormItem = Form.Item;
export const initialPurchaseDetailRuleList = [
    {
        ruleModel: RULE_MODEL_TYPES.ELEORDER,
        ruleType: '02',
        ruleValue: 0,
    },
    {
        ruleModel: RULE_MODEL_TYPES.SERVICE,
        ruleType: '02',
        ruleValue: 0,
    },
    {
        ruleModel: RULE_MODEL_TYPES.ELECTRIC,
        ruleType: '01',
        ruleValue: 0,
    },
    {
        ruleModel: RULE_MODEL_TYPES.ADD,
        ruleType: '02',
        ruleValue: 0,
    },
];

export const checkRuleInfo = (ruleInfo = {}, precision = MAX_PRECISION) => {
    if (ruleInfo) {
        const { ruleValue, ruleType } = ruleInfo;
        if (isEmpty(ruleValue)) {
            return '请配置结算折扣';
        }

        const ruleValueError = checkRuleValue(ruleInfo);
        if (ruleValueError) {
            return ruleValueError;
        }

        const formatRuleValue = Number(ruleValue);
        let endValue = formatRuleValue;
        if (ruleType === PURCHASE_RULE_TYPES.DISCOUNT) {
            endValue =
                Math.round((10 - (formatRuleValue || 0)) * 10 * 10 ** precision) / 10 ** precision;
        }
        const maxValue = endValue < 0 ? 0 : endValue;

        return checkAgreeValue(ruleInfo, maxValue, precision);
    } else {
        return '';
    }
};

const checkRuleValue = (ruleInfo = {}) => {
    if (ruleInfo) {
        const { ruleValue } = ruleInfo;

        if (isEmpty(ruleValue)) {
            if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ELECTRIC) {
                return '请配置优惠金额';
            } else {
                return '请配置结算折扣';
            }
        }
        const formatRuleValue = Number(ruleValue);
        if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ELECTRIC) {
            if (formatRuleValue <= 0) {
                return '金额必须大于0';
            }
        } else {
            if (formatRuleValue > 10) {
                return '请填写范围0.000-10.000';
            }
        }

        // if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ADD) {
        //     const curSlottingFee = ruleInfo?.slottingRuleValue;
        //     if (isEmpty(curSlottingFee)) {
        //         return '请填写附加费通道费';
        //     }
        //     if (curSlottingFee > 2) {
        //         return '附加费通道费填写范围0%~2%';
        //     }
        // } else if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ORDER) {
        //     const slottingRuleValue = ruleInfo?.slottingRuleValue;
        //     if (isEmpty(slottingRuleValue)) {
        //         return '请填写附加费通道费';
        //     }
        // } else if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ELECTRIC) {
        //     const slottingRuleValue = ruleInfo?.slottingRuleValue;
        //     if (isEmpty(slottingRuleValue)) {
        //         return '请填写附加费通道费';
        //     }
        // }
    }
    return '';
};

const checkAgreeValue = (agreeInfo, maxValue = 100, precision = MAX_PRECISION) => {
    let errMessage = '';
    if (agreeInfo) {
        const { agreeType, longRuleValue, shortRuleValue } = agreeInfo;
        if (isEmpty(agreeType)) {
            return '长协或短协至少选一个';
        }

        let shortValue = 0;
        let longValue = 0;
        if (agreeType && agreeType instanceof Array) {
            if (agreeType?.includes('01')) {
                longValue = longRuleValue ? Number(longRuleValue) : 0;
            }
            if (agreeType?.includes('02')) {
                shortValue = shortRuleValue ? Number(shortRuleValue) : 0;
            }

            let sumValue = Math.round((longValue + shortValue) * 10 ** precision) / 10 ** precision;

            if (agreeType.length === 2 && maxValue != sumValue) {
                errMessage = `长协收入+短协收入必须等于平台利润`;
            }
        }
    }

    return errMessage;
};

const AgreeFormItem = (props) => {
    const {
        value,
        ruleType,
        ruleModel,
        defaultValue = ['02'],
        onChange,
        disabled,
        precision = MAX_PRECISION,
    } = props;
    const unit = ruleType === PURCHASE_RULE_TYPES.MONEY ? '元' : '%';
    const [agreeInfo, updateAgreeInfo] = useState({
        agreeType: defaultValue || [],
        longRuleValue: null,
        shortRuleValue: null,
    });
    useEffect(() => {
        if (!isEmpty(value)) {
            updateAgreeInfo(value);
        }
    }, [value]);
    const maxValue = useMemo(() => {
        if (!isEmpty(value)) {
            const { ruleValue } = value;

            console.log(44324, value, ruleType);
            if (ruleType === PURCHASE_RULE_TYPES.MONEY) {
                return ruleValue;
            }
            const endValue =
                Math.round((10 - (ruleValue || 0)) * 10 * 10 ** precision) / 10 ** precision;
            return endValue < 0 ? 0 : endValue;
        }
        return '-';
    }, [value, ruleType]);

    const changeAgreeInfo = (info) => {
        //只抛出协议配置 防止污染分润其他配置值
        const newInfo = {
            agreeType: agreeInfo.agreeType,
            longRuleValue: agreeInfo.longRuleValue,
            shortRuleValue: agreeInfo.shortRuleValue,
            ...info,
        };
        updateAgreeInfo(newInfo);
        onChange && onChange(newInfo);
    };
    const { agreeType = [], longRuleValue, shortRuleValue } = agreeInfo;

    const longRuleValueError = useMemo(() => {
        if (agreeType instanceof Array && agreeType.includes('01')) {
            if (isEmpty(longRuleValue)) {
                return `请填写${getRuleModelName(ruleModel)}长协比例`;
            }
        }
        return '';
    }, [agreeType, ruleModel, longRuleValue, shortRuleValue]);

    const agreeTypeError = useMemo(() => {
        return checkAgreeValue(agreeInfo, maxValue, precision);
    }, [agreeInfo, maxValue]);

    useEffect(() => {
        if (maxValue) {
            const longRuleValue = agreeInfo.longRuleValue;
            if (longRuleValue && agreeInfo.agreeType.length === 2) {
                const shortValue = new Decimal(maxValue).sub(new Decimal(longRuleValue));
                changeAgreeInfo({
                    longRuleValue: longRuleValue,
                    shortRuleValue: shortValue.lessThan(0)
                        ? undefined
                        : shortValue.toSignificantDigits(6).toString(),
                });
            }
        }
    }, [maxValue]);

    const changeAgreeType = (newValue) => {
        let newInfo = {
            agreeType: newValue,
        };
        if (newValue?.length === 1) {
            if (newValue.includes('01')) {
                newInfo.longRuleValue = maxValue;
                newInfo.shortRuleValue = undefined;
            } else if (newValue.includes('02')) {
                newInfo.shortRuleValue = maxValue;
                newInfo.longRuleValue = undefined;
            }
        } else if (newValue.length === 2) {
            if (
                agreeInfo?.longRuleValue &&
                maxValue &&
                new Decimal(maxValue).greaterThanOrEqualTo(new Decimal(agreeInfo?.longRuleValue))
            ) {
                newInfo.shortRuleValue = new Decimal(maxValue)
                    .sub(agreeInfo?.longRuleValue)
                    .toSignificantDigits(6)
                    .toString();
            }
        }
        changeAgreeInfo(newInfo);
    };

    return (
        <FormItem help={agreeTypeError} validateStatus={(agreeTypeError && 'error') || ''}>
            <Space direction="vertical">
                {ruleType === PURCHASE_RULE_TYPES.DISCOUNT && <div>{`平台利润${maxValue}%`}</div>}

                <Checkbox.Group
                    value={agreeType}
                    style={{ marginTop: '-25px' }}
                    onChange={changeAgreeType}
                    disabled={disabled}
                >
                    <Space>
                        <Space>
                            <Checkbox value={'01'} className="text-line">
                                长协收入
                            </Checkbox>
                            {agreeType && agreeType.includes('01') && agreeType.includes('02') ? (
                                <Fragment>
                                    <FormItem
                                        style={{ marginTop: '25px' }}
                                        help={longRuleValueError}
                                        validateStatus={(longRuleValueError && 'error') || ''}
                                    >
                                        <InputNumber
                                            value={longRuleValue}
                                            disabled={
                                                disabled || !agreeType || !agreeType.includes('01')
                                            }
                                            type="number"
                                            min={0}
                                            max={100}
                                            step={
                                                ruleType === PURCHASE_RULE_TYPES.MONEY
                                                    ? 0.001
                                                    : 0.01
                                            }
                                            precision={
                                                ruleType === PURCHASE_RULE_TYPES.MONEY ? 3 : 2
                                            }
                                            onChange={(newValue) => {
                                                const shortValue = new Decimal(maxValue).sub(
                                                    new Decimal(newValue),
                                                );
                                                changeAgreeInfo({
                                                    longRuleValue: newValue,
                                                    shortRuleValue: shortValue.lessThan(0)
                                                        ? undefined
                                                        : shortValue
                                                              .toSignificantDigits(6)
                                                              .toString(),
                                                });
                                            }}
                                        />
                                    </FormItem>
                                    {unit}
                                </Fragment>
                            ) : null}
                        </Space>
                        <Space>
                            <Checkbox value={'02'} className="text-line">
                                短协收入
                            </Checkbox>
                            {agreeType && agreeType.includes('01') && agreeType.includes('02') ? (
                                <Fragment>
                                    <FormItem style={{ marginTop: '25px' }}>
                                        <span>{shortRuleValue}</span>
                                    </FormItem>
                                    {unit}
                                </Fragment>
                            ) : null}
                        </Space>
                    </Space>
                </Checkbox.Group>
            </Space>
        </FormItem>
    );
};

const PurchaseRuleFormItem = (props, ref) => {
    const { value, onChange, initialAgreeType = ['02'], disabled } = props;
    const initValue = [
        // {
        //     ruleModel: RULE_MODEL_TYPES.SERVICE,
        //     agreeType: ['02'],
        //     ruleType: '02',
        // },
        // {
        //     ruleModel: RULE_MODEL_TYPES.ADD,
        //     agreeType: ['01'],
        //     slottingRuleValue: 0.6,
        //     ruleValue: 10,
        //     ruleType: '02',
        // },
    ];
    const [listData, updateListData] = useState(initValue);
    const [radioValue, updateRadioValue] = useState(RADIO_TYPES.DETAIL);
    useEffect(() => {
        if (value instanceof Array) {
            const hasOrderType = value.find((ele) => ele.ruleModel === RULE_MODEL_TYPES.ORDER);
            if (hasOrderType) {
                updateRadioValue(RADIO_TYPES.ALL);
            }
            updateListData(value);
        }
    }, [value]);

    const filterDetailList = useMemo(() => {
        if (listData instanceof Array) {
            return listData.filter((ele) => {
                return ele.ruleModel !== RULE_MODEL_TYPES.ORDER;
            });
        }
        return [];
    }, [listData]);

    const filterOrderInfo = useMemo(() => {
        if (listData instanceof Array) {
            return listData.find((ele) => {
                return ele.ruleModel === RULE_MODEL_TYPES.ORDER;
            });
        }
        return null;
    }, [listData]);

    useImperativeHandle(ref, () => {
        return {};
    });

    const changeListIndexEvent = (options) => {
        const newList = copyObjectCommon(listData);
        let index = newList.findIndex((ele) => ele.ruleModel === options.ruleModel);
        if (index >= 0) {
            newList[index] = {
                ...newList[index],
                ...options,
            };
            changeListEvent(newList);
        }
    };

    const changeListEvent = (newList) => {
        updateListData(newList);
        updateFormItem(newList);
    };

    const changeRadioEvent = (event) => {
        const {
            target: { value: radioValue },
        } = event;
        updateRadioValue(radioValue);
        if (radioValue === RADIO_TYPES.DETAIL) {
            changeListEvent(initValue);
        } else {
            changeListEvent([
                {
                    ruleModel: RULE_MODEL_TYPES.ORDER,
                    ruleType: '02',
                    agreeType: initialAgreeType || [],
                    // slottingRuleValue: 0.6,
                },
            ]);
        }
    };

    const changeDetailModelEvent = (newModel) => {
        const newList = copyObjectCommon(filterDetailList);
        const delOldList = newList?.filter((ele) => newModel.includes(ele.ruleModel));
        for (const item of newModel) {
            const hasItemModel = delOldList.find((ele) => ele.ruleModel === item);
            if (!hasItemModel) {
                delOldList.push({
                    ruleModel: item,
                    ruleType: item == RULE_MODEL_TYPES.ELECTRIC ? '01' : '02',
                    agreeType: initialAgreeType || [],
                });
            }
        }
        changeListEvent(delOldList);
    };

    const updateFormItem = (newList) => {
        onChange && onChange(newList);
    };

    const valueOrderErrorObj = useMemo(() => {
        const obj = {};
        if (filterOrderInfo) {
            const { ruleValue, slottingRuleValue, ruleType } = filterOrderInfo;
            if (isEmpty(ruleValue)) {
                obj.valueError = '请填写';
            } else if (ruleType === PURCHASE_RULE_TYPES.DISCOUNT && Number(ruleValue) > 10) {
                obj.valueError = '请填写范围0.000-10.000';
            }
            // if (isEmpty(slottingRuleValue)) {
            //     obj.slottingValueError = '请填写附加费通道费';
            // }
        }
        return obj;
    }, [filterOrderInfo]);
    return (
        <Fragment>
            <Radio.Group value={radioValue} disabled={disabled} onChange={changeRadioEvent}>
                <Space direction="vertical">
                    <Radio value={RADIO_TYPES.DETAIL}>
                        <Space direction="vertical">
                            <span>按订单细项</span>

                            <Checkbox.Group
                                defaultValue={filterDetailList?.map((item) => item.ruleModel)}
                                value={filterDetailList?.map((item) => item.ruleModel)}
                                disabled={disabled}
                                onChange={changeDetailModelEvent}
                            >
                                {initialPurchaseDetailRuleList.map((ele, index) => {
                                    const ruleConfigData = filterDetailList.find(
                                        (item) => item.ruleModel === ele.ruleModel,
                                    );
                                    let disabledItem = false;
                                    let disabledCheckBox = false;
                                    if (!ruleConfigData) {
                                        disabledItem = true;
                                    }
                                    // if (radioValue === RADIO_TYPES.ALL) {
                                    //     disabledCheckBox = true;
                                    // } else if (ele.ruleModel == RULE_MODEL_TYPES.ADD) {
                                    //     disabledCheckBox = true;
                                    // }
                                    const curRuleValue = ruleConfigData?.ruleValue;

                                    let valueErrorMessage = '';
                                    let slottingErrorMessage = '';

                                    if (ruleConfigData) {
                                        if (isEmpty(curRuleValue)) {
                                            valueErrorMessage = '请填写';
                                        }
                                        if (
                                            ele?.ruleType === PURCHASE_RULE_TYPES.DISCOUNT &&
                                            Number(curRuleValue) > 10
                                        ) {
                                            valueErrorMessage = '请填写范围0.000-10.000';
                                        }

                                        if (ruleConfigData.ruleModel === RULE_MODEL_TYPES.ADD) {
                                            const curSlottingFee =
                                                ruleConfigData?.slottingRuleValue;
                                            if (isEmpty(curSlottingFee)) {
                                                slottingErrorMessage = '请填写附加费通道费';
                                            }
                                            if (curSlottingFee > 2) {
                                                slottingErrorMessage = '附加费通道费填写范围0%~2%';
                                            }
                                        }
                                    }

                                    return (
                                        <div className={purchaseStyles['type-table']} key={index}>
                                            <div
                                                className={`${purchaseStyles['type-table-label']} ${purchaseStyles['item-model']}`}
                                            >
                                                <Checkbox
                                                    value={ele.ruleModel}
                                                    disabled={disabled || disabledCheckBox}
                                                >
                                                    按{getRuleModelName(ele.ruleModel)}
                                                </Checkbox>
                                            </div>
                                            <div className={purchaseStyles['type-table-row']}>
                                                <div className={purchaseStyles['type-table-main']}>
                                                    <Space>
                                                        <FormItem
                                                            style={{ marginTop: '20px' }}
                                                            help={valueErrorMessage}
                                                            validateStatus={
                                                                (valueErrorMessage && 'error') || ''
                                                            }
                                                        >
                                                            <InputNumber
                                                                value={ruleConfigData?.ruleValue}
                                                                addonAfter={
                                                                    ele?.ruleModel ===
                                                                    RULE_MODEL_TYPES.ELECTRIC
                                                                        ? '元'
                                                                        : '折结算'
                                                                }
                                                                addonBefore={
                                                                    ele?.ruleModel ===
                                                                    RULE_MODEL_TYPES.ELECTRIC
                                                                        ? '每度电优惠'
                                                                        : ''
                                                                }
                                                                min={0}
                                                                step={0.001}
                                                                precision={MAX_PRECISION}
                                                                placeholder="请填写"
                                                                disabled={disabled || disabledItem}
                                                                onChange={(newValue) => {
                                                                    if (ruleConfigData) {
                                                                        const newItem =
                                                                            copyObjectCommon(
                                                                                ruleConfigData,
                                                                            );
                                                                        newItem.ruleValue =
                                                                            newValue;

                                                                        changeListIndexEvent(
                                                                            newItem,
                                                                        );
                                                                    }
                                                                }}
                                                            />
                                                        </FormItem>
                                                        {ele?.ruleType ===
                                                            PURCHASE_RULE_TYPES.DISCOUNT && (
                                                            <span>可填写范围0.000-10.000</span>
                                                        )}
                                                        {/* {ele?.ruleModel ===
                                                            RULE_MODEL_TYPES.ADD && (
                                                            <Fragment>
                                                                <span>
                                                                    , 附加费通道费
                                                                    <Tooltip title="当平台替运营商代收占位费时，平台需收取的占位订单通道费">
                                                                        <InfoCircleOutlined />
                                                                    </Tooltip>
                                                                </span>
                                                                <FormItem
                                                                    style={{
                                                                        marginTop: '20px',
                                                                    }}
                                                                    help={slottingErrorMessage}
                                                                    validateStatus={
                                                                        (slottingErrorMessage &&
                                                                            'error') ||
                                                                        ''
                                                                    }
                                                                >
                                                                    <InputNumber
                                                                        value={
                                                                            ruleConfigData?.slottingRuleValue
                                                                        }
                                                                        min={0}
                                                                        step={0.01}
                                                                        precision={2}
                                                                        placeholder="请填写"
                                                                        disabled={
                                                                            disabled || disabledItem
                                                                        }
                                                                        onChange={(newValue) => {
                                                                            if (ruleConfigData) {
                                                                                const newItem =
                                                                                    copyObjectCommon(
                                                                                        ruleConfigData,
                                                                                    );
                                                                                newItem.slottingRuleValue =
                                                                                    newValue;

                                                                                changeListIndexEvent(
                                                                                    newItem,
                                                                                );
                                                                            }
                                                                        }}
                                                                    />
                                                                </FormItem>
                                                                %
                                                            </Fragment>
                                                        )} */}
                                                    </Space>
                                                </div>
                                                {ruleConfigData && (
                                                    <div
                                                        className={
                                                            purchaseStyles['type-table-main']
                                                        }
                                                    >
                                                        <AgreeFormItem
                                                            defaultValue={initialAgreeType}
                                                            ruleModel={ele?.ruleModel}
                                                            ruleType={ele?.ruleType}
                                                            value={ruleConfigData}
                                                            onChange={(agreeInfo) => {
                                                                if (ruleConfigData) {
                                                                    const oldItem =
                                                                        copyObjectCommon(
                                                                            ruleConfigData,
                                                                        );
                                                                    const newItem = {
                                                                        ...oldItem,
                                                                        ...agreeInfo,
                                                                    };
                                                                    changeListIndexEvent(newItem);
                                                                }
                                                            }}
                                                            disabled={disabled}
                                                            precision={MAX_PRECISION}
                                                        ></AgreeFormItem>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </Checkbox.Group>
                        </Space>
                    </Radio>
                    <Radio value={RADIO_TYPES.ALL}>
                        <div>
                            <Space>
                                <span>按充电订单总金额</span>

                                <FormItem
                                    style={{ marginTop: '20px' }}
                                    help={valueOrderErrorObj?.valueError}
                                    validateStatus={
                                        (valueOrderErrorObj?.valueError && 'error') || ''
                                    }
                                >
                                    <InputNumber
                                        value={filterOrderInfo?.ruleValue}
                                        min={0}
                                        step={0.001}
                                        addonAfter={'折结算'}
                                        precision={MAX_PRECISION}
                                        placeholder="请填写"
                                        disabled={radioValue != RADIO_TYPES.ALL || disabled}
                                        onChange={(newValue) => {
                                            if (filterOrderInfo) {
                                                const newItem = copyObjectCommon(filterOrderInfo);
                                                newItem.ruleValue = newValue;
                                                changeListIndexEvent(newItem);
                                            }
                                        }}
                                    />
                                </FormItem>

                                <span>可填写范围0.000-10.000</span>
                                {/* <Fragment>
                                    <span>
                                        , 附加费通道费
                                        <Tooltip title="当平台替运营商代收占位费时，平台需收取的占位订单通道费">
                                            <InfoCircleOutlined />
                                        </Tooltip>
                                    </span>
                                    <FormItem
                                        style={{
                                            marginTop: '20px',
                                        }}
                                        help={valueOrderErrorObj?.slottingValueError}
                                        validateStatus={
                                            (valueOrderErrorObj?.slottingValueError && 'error') ||
                                            ''
                                        }
                                    >
                                        <InputNumber
                                            value={filterOrderInfo?.slottingRuleValue}
                                            min={0}
                                            step={0.01}
                                            precision={2}
                                            placeholder="请填写"
                                            disabled={radioValue != RADIO_TYPES.ALL || disabled}
                                            onChange={(newValue) => {
                                                if (filterOrderInfo) {
                                                    const newItem =
                                                        copyObjectCommon(filterOrderInfo);
                                                    newItem.slottingRuleValue = newValue;
                                                    changeListIndexEvent(newItem);
                                                }
                                            }}
                                        />
                                    </FormItem>
                                    %
                                </Fragment> */}
                            </Space>
                        </div>
                        {filterOrderInfo && (
                            <AgreeFormItem
                                defaultValue={initialAgreeType}
                                ruleModel={RULE_MODEL_TYPES.ORDER}
                                ruleType={filterOrderInfo.ruleType}
                                value={filterOrderInfo}
                                disabled={disabled}
                                onChange={(agreeInfo) => {
                                    if (filterOrderInfo) {
                                        const oldItem = copyObjectCommon(filterOrderInfo);
                                        const newItem = {
                                            ...oldItem,
                                            ...agreeInfo,
                                        };

                                        changeListIndexEvent(newItem);
                                    }
                                }}
                                precision={MAX_PRECISION}
                            ></AgreeFormItem>
                        )}
                    </Radio>
                </Space>
            </Radio.Group>
        </Fragment>
    );
};
export default forwardRef(PurchaseRuleFormItem);
