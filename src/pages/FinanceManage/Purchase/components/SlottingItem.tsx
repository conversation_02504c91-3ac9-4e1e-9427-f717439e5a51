import { Switch, Radio, InputNumber, Space, Typography } from 'antd';
import React from 'react';
import { Fragment, useEffect, useState } from 'react';

import { RULE_MODEL_TYPES, getRuleModelName } from '../PurchaseConfig';
import { isEmpty } from '@/utils/utils';

export type SlottingConfig = {
    ruleModel: string;
    slottingFlag?: boolean;
    slottingBaseType?: SlottingTypes;
    slottingRuleValue?: number | null;
};

export enum SlottingTypes {
    Before = '0',
    After = '1',
}

export const checkItemValue = (ruleInfo: SlottingConfig) => {
    const slottingRuleValue = ruleInfo?.slottingRuleValue;
    if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ADD) {
        if (isEmpty(slottingRuleValue)) {
            return '请填写附加费通道费';
        }
    } else if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ORDER) {
        if (isEmpty(slottingRuleValue)) {
            return '请填写费通道费';
        }
    } else if (ruleInfo.ruleModel === RULE_MODEL_TYPES.SERVICE) {
        if (isEmpty(slottingRuleValue)) {
            return '请填写服务费通道费';
        }
    } else if (ruleInfo.ruleModel === RULE_MODEL_TYPES.ELEORDER) {
        if (isEmpty(slottingRuleValue)) {
            return '请填写电费通道费';
        }
    }
    if (slottingRuleValue > 2) {
        return '通道费填写范围0%~2%';
    }
    return;
};

interface Props {
    ruleModel: string;
    value: SlottingConfig | undefined;
    onChange: (value: SlottingConfig) => void;
    disabled?: boolean; //是否禁用编辑
}

const SlottingItem: React.FC<Props> = (props) => {
    const { value, onChange, disabled, ruleModel } = props;
    const [slottingItemInfo, updateSlottingItemValue] = useState<SlottingConfig>({
        ruleModel,
        slottingFlag: true,
        slottingBaseType: SlottingTypes.Before,
    });
    const updateFormValue = (info: SlottingConfig) => {
        updateSlottingItemValue(info);
        onChange && onChange(info);
    };

    useEffect(() => {
        if (!isEmpty(value)) {
            updateSlottingItemValue(value);
        }
    }, [value]);
    return (
        <Fragment>
            {getRuleModelName(ruleModel)}通道费:
            <Switch
                disabled={disabled || ruleModel === RULE_MODEL_TYPES.ADD}
                checked={slottingItemInfo?.slottingFlag}
                onChange={(flag) => {
                    updateFormValue({
                        ...slottingItemInfo,
                        slottingFlag: flag,
                    });
                }}
            ></Switch>
            <Space>
                {slottingItemInfo.slottingFlag && (
                    <Fragment>
                        <span>平台按订单</span>
                        <Radio.Group
                            value={slottingItemInfo?.slottingBaseType}
                            onChange={(event) => {
                                const {
                                    target: { value: newValue },
                                } = event;
                                updateFormValue({
                                    ...slottingItemInfo,
                                    slottingBaseType: newValue,
                                });
                            }}
                            disabled={disabled}
                        >
                            <Radio value={SlottingTypes.Before}>结算前</Radio>
                            <Radio value={SlottingTypes.After}>
                                <span>结算后</span>
                            </Radio>
                        </Radio.Group>
                        <span>费用收取通道费</span>
                        <InputNumber
                            value={slottingItemInfo?.slottingRuleValue}
                            min={0}
                            step={0.01}
                            precision={2}
                            placeholder="请填写"
                            disabled={disabled}
                            addonAfter={'%'}
                            onChange={(newValue) => {
                                updateFormValue({
                                    ...slottingItemInfo,
                                    slottingRuleValue: newValue,
                                });
                            }}
                        />
                    </Fragment>
                )}
            </Space>
        </Fragment>
    );
};
export default SlottingItem;
