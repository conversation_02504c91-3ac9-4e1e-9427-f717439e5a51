import { Checkbox, InputNumber, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
//divideType :01电费 02服务费 ，divideValue 数值
type PremiumConfig = {
    divideType: DIVIDE_TYPE;
    divideValue: number | undefined;
};

enum DIVIDE_TYPE {
    ELEC = '01',
    SERVICE = '02',
}

interface Props {
    value: PremiumConfig[] | undefined;
    onChange: (value: PremiumConfig[]) => void;
    disabled?: boolean; //是否禁用编辑
}
const PremiumSharingItem: React.FC<Props> = (props) => {
    const { value, onChange, disabled = false } = props;

    const [elecChecked, setElecChecked] = useState<boolean>(false);
    const [serviceChecked, setServiceChecked] = useState<boolean>(false);

    const [elecNumber, setElecNumber] = useState<number | undefined>(undefined);
    const [serviceNumber, setServiceNumber] = useState<number | undefined>(undefined);

    //初始化表单值
    useEffect(() => {
        const elec = value?.find((v) => v.divideType === DIVIDE_TYPE.ELEC);
        const service = value?.find((v) => v.divideType === DIVIDE_TYPE.SERVICE);
        if (elec) {
            setElecChecked(true);
            setElecNumber(elec?.divideValue);
        } else {
            setElecChecked(false);
            setElecNumber(undefined);
        }
        if (service) {
            setServiceChecked(true);
            setServiceNumber(service?.divideValue);
        } else {
            setServiceChecked(false);
            setServiceNumber(undefined);
        }
    }, [value]);

    //数据变化响应到表单
    const dataChange = (type: DIVIDE_TYPE, num: number | undefined, oper: 'add' | 'remove') => {
        const result: PremiumConfig[] = [];
        if (oper === 'add') {
            if (value && value?.length > 0) {
                const elec = value?.find((v) => v.divideType === DIVIDE_TYPE.ELEC);
                const service = value?.find((v) => v.divideType === DIVIDE_TYPE.SERVICE);
                result.push({
                    divideType: type,
                    divideValue: num,
                });
                if (type === DIVIDE_TYPE.ELEC && service) {
                    result.push(service);
                }
                if (type === DIVIDE_TYPE.SERVICE && elec) {
                    result.push(elec);
                }
            } else {
                result.push({ divideType: type, divideValue: num });
            }
        } else {
            const leftValue = value?.filter((v) => v.divideType !== type);
            if (leftValue) {
                result.push(...leftValue);
            }
        }
        onChange && onChange(result);
    };

    return (
        <Space direction="vertical">
            <Space align="baseline">
                <Checkbox
                    value={DIVIDE_TYPE.ELEC}
                    checked={elecChecked}
                    disabled
                    onChange={(e) => {
                        if (e.target.checked) {
                            dataChange(e.target.value, undefined, 'add');
                        } else {
                            dataChange(e.target.value, undefined, 'remove');
                        }
                    }}
                />
                <Typography.Text type={elecChecked ? undefined : 'secondary'}>
                    按电费
                </Typography.Text>
                <InputNumber
                    placeholder="请填写"
                    min={0.0}
                    max={10.0}
                    precision={3}
                    step={0.001}
                    disabled
                    value={elecNumber}
                    onChange={(value) => {
                        dataChange(DIVIDE_TYPE.ELEC, value as number, 'add');
                    }}
                />
                <Typography.Text type={elecChecked ? undefined : 'secondary'}>
                    折结算，可填写范围0.000-10.000
                </Typography.Text>
            </Space>
            <Space align="baseline">
                <Checkbox
                    value={DIVIDE_TYPE.SERVICE}
                    checked={serviceChecked}
                    disabled={disabled}
                    onChange={(e) => {
                        if (e.target.checked) {
                            dataChange(e.target.value, undefined, 'add');
                        } else {
                            dataChange(e.target.value, undefined, 'remove');
                        }
                    }}
                />
                <Typography.Text type={serviceChecked ? undefined : 'secondary'}>
                    按服务费
                </Typography.Text>
                <InputNumber
                    placeholder="请填写"
                    min={0.0}
                    max={10.0}
                    precision={3}
                    step={0.001}
                    disabled={!serviceChecked || disabled}
                    value={serviceNumber}
                    onChange={(value) => {
                        dataChange(DIVIDE_TYPE.SERVICE, value as number, 'add');
                    }}
                />
                <Typography.Text type={serviceChecked ? undefined : 'secondary'}>
                    折结算，可填写范围0.000-10.000
                </Typography.Text>
            </Space>
        </Space>
    );
};

export default PremiumSharingItem;
