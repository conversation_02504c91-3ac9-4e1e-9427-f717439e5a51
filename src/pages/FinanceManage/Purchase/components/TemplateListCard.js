import {
    Button,
    Card,
    Col,
    Form,
    message,
    Modal,
    Row,
    Input,
    Table,
    Tabs,
    Popconfirm,
    Space,
    Typography,
} from 'antd';
import moment from 'moment';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { connect } from 'umi';

import styles from '../PurchaseListPage.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import TemplateListFormItem from './TemplateListFormItem';
import {
    deleteRuleTemplateApi,
    addRuleTemplateApi,
    updateRuleTemplateApi,
} from '@/services/FinanceManage/ProfitRuleApi';
import { OPER_COOPERATION } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import { childrenColumns } from '../PurchaseTemplateListPage';
import { CollectTypes, computedLongShortValue } from '../PurchaseConfig';

const { TabPane } = Tabs;

const FormItem = Form.Item;

const STATUS_TYPES = {
    ALL: '00',
    INEFFECTIVE: '01',
    USE: '02', // 使用中
    LOSE: '03', // 失效
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError>
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="模板名称:" name="name">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="模板id:" name="templateNo">
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>

                <Col>
                    <Button type="primary" htmlType="submit">
                        查询
                    </Button>
                    <Button className={styles.btnMargin} onClick={resetForm}>
                        重置
                    </Button>
                </Col>
            </Row>
        </Form>
    );
};
const TemplateListCard = (props) => {
    const {
        dispatch,
        purchaseModel: { ruleTemplateList, ruleTemplateListTotal, editTemplateInfo },
        listLoading,
        infoLoading,
        global: { codeInfo },
        canSelect = false,
        onSelect,
        pageSize = 50,
    } = props;

    const { templateStatus: templateStatusList } = codeInfo;

    const [form] = Form.useForm();
    const [templateForm] = Form.useForm();
    const [showEditView, toggleShowEditView] = useState(false);
    const [showAddView, toggleShowAddView] = useState(false);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: pageSize,
        tabType: STATUS_TYPES.ALL,
    });

    useEffect(() => {
        if (!templateStatusList) {
            dispatch({
                type: 'global/initCode',
                code: 'templateStatus',
            });
        }
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (editTemplateInfo && showEditView) {
            templateForm.setFieldsValue({
                templateId: editTemplateInfo.templateId,
                ...processFormValue(editTemplateInfo),
            });
        } else if (editTemplateInfo && showAddView) {
            templateForm.setFieldsValue({
                ...processFormValue(editTemplateInfo),
            });
        }
    }, [editTemplateInfo, showEditView, showAddView]);

    const processFormValue = (record) => {
        const params = {
            name: record.name,
            detailList: record.detailList.map((ele) => {
                const { profitSlottingRuleList } = ele;

                let item = {
                    effTime: moment(ele.effTime),
                    expTime: moment(ele.expTime),
                    profitRuleTypeList: ele.profitRuleTypeList,
                };
                if (profitSlottingRuleList instanceof Array) {
                    item.profitSlottingRules = {
                        collectType: CollectTypes.DETAIL,
                        list: profitSlottingRuleList,
                    };
                }
                return item;
            }),
        };
        return params;
    };

    const templateStatusOptions = useMemo(() => {
        if (templateStatusList) {
            return templateStatusList.map((ele) => (
                <TabPane tab={ele.codeName} key={ele.codeValue} />
            ));
        }
        return [];
    }, [templateStatusList]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            name: data.name || '',
            templateNo: data.templateNo || '',
            cooperationType: '02',
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.status = pageInfo.tabType;
        }
        dispatch({
            type: 'purchaseModel/getRuleTemplateList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };
    const openAddTemplateView = () => {
        templateForm.resetFields();
        openEditView();
    };

    const delTemplateById = async (item) => {
        try {
            await deleteRuleTemplateApi(item.templateId);
            searchData();
            message.success('删除成功');
        } catch (error) {
            message.error('删除失败');
            return Promise.reject(error);
        }
    };
    const editItemEvent = (item) => {
        openTemplateDetailsView(item);
        openEditView();
    };
    const openTemplateDetailsView = (item) => {
        dispatch({
            type: 'purchaseModel/getTemplateInfo',
            templateId: item.templateId,
        });
        openEditView();
    };
    const openEditView = () => {
        toggleShowEditView(true);
    };

    const closeEditView = () => {
        toggleShowEditView(false);
        toggleShowAddView(false);
        dispatch({
            type: 'purchaseModel/updatePurchaseTemplateInfo',
            editTemplateInfo: null,
        });
    };

    const onCopy = (item) => {
        templateForm.resetFields();
        dispatch({
            type: 'purchaseModel/getTemplateInfo',
            templateId: item?.templateId,
        });
        toggleShowAddView(true);
    };

    const columns = [
        {
            title: '创建时间',
            width: 140,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板ID',
            width: 140,
            dataIndex: 'templateNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板名称',
            width: 140,
            dataIndex: 'name',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板生效时间',
            width: 140,
            dataIndex: 'effTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '模板失效时间',
            width: 140,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 180,
            render: (text, record) => {
                return (
                    <Space>
                        {canSelect && record.status == '01' && (
                            <Popconfirm
                                title="确认选择这个模板吗？"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => {
                                    onSelect && onSelect(record?.templateId);
                                }}
                            >
                                <Typography.Link>选择</Typography.Link>
                            </Popconfirm>
                        )}
                        {record.status == '01' && (
                            <Typography.Link onClick={() => editItemEvent(record)}>
                                编辑
                            </Typography.Link>
                        )}
                        {record.status == '01' && (
                            <Popconfirm
                                title="确认删除？"
                                okText="确认"
                                cancelText="取消"
                                onConfirm={() => delTemplateById(record)}
                            >
                                <Typography.Link href="#">删除</Typography.Link>
                            </Popconfirm>
                        )}
                        <Typography.Link
                            onClick={() => {
                                onCopy(record);
                            }}
                        >
                            复制
                        </Typography.Link>
                    </Space>
                );
            },
        },
    ];

    const expandedRowRender = useCallback(
        (childrenItem, childrenIndex, indent, expanded) => {
            return (
                <div style={{ padding: '0 170px' }}>
                    <Table
                        dataSource={(childrenItem && childrenItem.detailList) || []}
                        columns={childrenColumns}
                        pagination={false}
                    />
                </div>
            );
        },
        [ruleTemplateList],
    );

    const onFinish = async (values) => {
        try {
            const param = {
                name: values.name,
                cooperationType: OPER_COOPERATION.BUY,
            };
            const detailList = values.detailList.map((ele) => {
                const profitRuleList = ele.profitRuleTypeList;

                const profitSlottingRuleInfo = ele.profitSlottingRules;

                const profitSlottingRules =
                    profitSlottingRuleInfo?.list?.filter((ele) => ele.slottingFlag) || [];

                const filterRuleList =
                    profitRuleList.filter((element) => element && !isEmpty(element.ruleModel)) ||
                    [];

                const profitRuleTypes = filterRuleList.map((element) => {
                    let options = {
                        ...element,
                        ruleModel: String(element.ruleModel),
                    };
                    if (element.agreeType instanceof Array && element.agreeType.length === 1) {
                        const ruleInfo = computedLongShortValue(element);
                        if (ruleInfo) {
                            options = {
                                ...options,
                                ...ruleInfo,
                            };
                        }
                    }

                    return options;
                });

                return {
                    effTime: (ele.effTime && ele.effTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    expTime: (ele.expTime && ele.expTime.format('YYYY-MM-DD HH:mm:ss')) || '',
                    ruleFlag: '1',
                    profitRuleTypes: profitRuleTypes,
                    profitSlottingRules: profitSlottingRules,
                    sn: ele.sn,
                    ruleRel: '',
                };
            });
            param.detailJsonStr = JSON.stringify(detailList);
            if (editTemplateInfo && editTemplateInfo.templateId && showEditView) {
                param.templateId = editTemplateInfo.templateId;
                await updateRuleTemplateApi(param);
                message.success('编辑成功');
            } else {
                const result = await addRuleTemplateApi(param);
                message.success('新建成功');
                if (canSelect) {
                    onSelect && onSelect(result?.data?.templateId);
                }
            }

            searchData();

            closeEditView();
        } catch (error) {
            return Promise.reject(error);
        }
    };
    return (
        <>
            <Card bordered={false}>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={openAddTemplateView}>
                        新建模板
                    </Button>
                </div>
                <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    {templateStatusOptions}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.templateId}
                    expandable={{ defaultExpandAllRows: true, expandedRowRender }}
                    dataSource={ruleTemplateList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: ruleTemplateListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [50, 100],
                    }}
                    noSort
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={showEditView ? '配置模板' : '新建模板'}
                width={1100}
                visible={showEditView || showAddView}
                onCancel={closeEditView}
                destroyOnClose
                footer={null}
                maskClosable={false}
            >
                <Card loading={infoLoading} bordered={false}>
                    <Form
                        form={templateForm}
                        onFinish={onFinish}
                        initialValues={{
                            detailList: [{}, {}],
                        }}
                        scrollToFirstError
                    >
                        <div
                            className="overscroll"
                            style={{
                                maxHeight: `${document.body.clientHeight - 270}px`,
                                overflowY: 'auto',
                            }}
                        >
                            <TemplateListFormItem
                                form={templateForm}
                                name="detailList"
                                cooperationType={OPER_COOPERATION.BUY}
                                templateInfo={editTemplateInfo}
                                isEdit={!!editTemplateInfo && showEditView}
                                isAdd={showAddView}
                            />
                        </div>
                        <div className={styles['form-submit']}>
                            <Button className={styles['form-btn']} type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button className={styles['form-btn']} onClick={closeEditView}>
                                取消
                            </Button>
                        </div>
                    </Form>
                </Card>
            </Modal>
        </>
    );
};

export default connect(({ purchaseModel, global, loading }) => ({
    purchaseModel,
    global,
    listLoading: loading.effects['purchaseModel/getRuleTemplateList'],
    infoLoading: loading.effects['purchaseModel/getTemplateInfo'],
}))(TemplateListCard);
