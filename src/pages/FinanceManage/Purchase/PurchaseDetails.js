import { InfoCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Input,
    Form,
    Space,
    DatePicker,
    Radio,
    message,
    Tooltip,
    Switch,
    Modal,
} from 'antd';
import { isNumber } from 'lodash';
import moment from 'moment';
import { Fragment, useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { connect } from 'umi';

import { OPER_COOPERATION, RULE_TYPES } from '@/config/declare';

import {
    saveprofitruleApi,
    addStationTemplateApi,
    getProfitruleExpireTimeFlag,
} from '@/services/FinanceManage/ProfitRuleApi';
import styles from './PurchaseListPage.less';
import { isEmpty, checkClosePremiumCommon, copyObjectCommon } from '@/utils/utils';

import PurchaseRuleFormItem, { checkRuleInfo } from './components/PurchaseRuleFormItem';
import SlottingRuleFormItem, {
    checkRuleValue,
    defaultRuleValue,
} from './components/SlottingRuleFormItem';

import { childrenColumns } from './PurchaseTemplateListPage';
import TablePro from '@/components/TablePro';
import SelectRuleModal from './components/SelectRuleModal';
import PremiumSharingItem from './components/PremiumSharingItem';
import SlottingTooltip from './components/SlottingTooltip';
import { CollectTypes, computedLongShortValue } from './PurchaseConfig';
import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};

const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

export const PurchaseDetailsView = forwardRef((props, ref) => {
    const {
        dispatch,
        history,
        getInfoLoading,
        selectTemplateListLoading,
        purchaseModel,
        closeEvent,
        onWorkorderFinish,
        operName,
        operId,
    } = props;
    const { editRuleInfo, selectRuleInfo, selectTemplateInfo } = purchaseModel;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(false);
    const selectTemplateRef = useRef();
    const [canISetEffTime, changeCiSetEffTime] = useState(true);

    const { cooperationPlatform } = useCooperationPlatform();

    useImperativeHandle(ref, () => ({
        initData: initData,
    }));

    const initData = () => {
        if (operId || selectRuleInfo?.operId) {
            dispatch({
                type: 'purchaseModel/getRuleInfo',
                params: {
                    operId: operId || (selectRuleInfo && selectRuleInfo.operId),
                    profitRuleType: RULE_TYPES.COMMON,
                    cooperationPlatform,
                },
            });
        }
    };

    useEffect(() => {
        return () => {
            dispatch({
                type: 'purchaseModel/updatePurchaseInfo',
                editRuleInfo: {},
            });
            dispatch({
                type: 'purchaseModel/updateSelectTemplateInfo',
                info: undefined,
            });
        };
    }, []);

    useEffect(() => {
        initData();
    }, [selectRuleInfo]);

    const mainInfo = editRuleInfo?.list?.[0];

    useEffect(() => {
        initEditInfoEvent(mainInfo);
    }, [mainInfo]);

    const initEditInfoEvent = (info) => {
        const params = {};
        if (info) {
            const {
                effTime,
                expTime,
                profitRuleTypeList,
                profitSlottingRuleList,
                ruleId,
                premiumFlag,
            } = info;
            params.effTime = effTime ? moment(effTime) : moment().add(5, 'minutes');
            params.expTime = expTime ? moment(expTime) : null;
            params.ruleId = ruleId;
            params.useTemplate = info.templateId?.length ? '02' : '01';
            params.templateId = info.templateId;
            params.premiumFlag = premiumFlag;
            if (params.templateId) {
                changeSelectEvent(info.templateId);
            }
            if (expTime) {
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }
            if (profitRuleTypeList && profitRuleTypeList[0]) {
                params.profitRuleTypeList = profitRuleTypeList;
            }
            if (premiumFlag === '1') {
                params.divideDTOList = info.divideList?.map((v) => ({
                    divideType: v.divideType,
                    divideValue: v.divideValue,
                }));
            }

            if (profitSlottingRuleList instanceof Array) {
                params.profitSlottingRules = {
                    collectType: CollectTypes.DETAIL,
                    list: profitSlottingRuleList,
                };
            }

            if (mainInfo.templateId) {
                // 如果是由使用模板切换到不使用模板，生效失效时间可以改
                changeCiSetEffTime(true);
            } else if (effTime) {
                if (expTime) {
                    if (moment().isBetween(moment(effTime), moment(expTime))) {
                        changeCiSetEffTime(false);
                    } else {
                        changeCiSetEffTime(true);
                    }
                } else if (moment(effTime).isBefore(moment())) {
                    changeCiSetEffTime(false);
                } else {
                    changeCiSetEffTime(true);
                }
            } else {
                changeCiSetEffTime(true);
            }
        }
        form.setFieldsValue(params);
    };

    useEffect(() => {
        loadSelectTemplateList();
    }, []);

    const loadSelectTemplateList = () => {
        dispatch({
            type: 'purchaseModel/getSelectTemplateList',
            cooperationType: OPER_COOPERATION.BUY,
        });
    };

    const changeSelectEvent = (value) => {
        dispatch({
            type: 'purchaseModel/getSelectTemplateInfo',
            templateId: value,
        });
        form.setFieldValue('templateId', value);
        form.validateFields(['useTemplate']);
    };

    /**
     * 提交表单
     */
    const onFinish = async (values) => {
        //通道费收取方式
        const profitSlottingRuleInfo = values.profitSlottingRules;

        const profitSlottingRules =
            profitSlottingRuleInfo?.list?.filter((ele) => ele.slottingFlag) || [];
        const copyRules = copyObjectCommon(profitSlottingRules);
        const newRules = copyRules?.map((item) => {
            delete item.slottingFlag;
            return item;
        });

        if (JSON.stringify(mainInfo?.profitSlottingRuleList) !== JSON.stringify(newRules)) {
            console.log('相等');
            Modal.confirm({
                title: '提示',
                content: '通道费配置发生改变，城市/场站规则是否同步变更？',
                onOk() {
                    formatParams(values, profitSlottingRules, 1);
                },
                onCancel() {
                    formatParams(values, profitSlottingRules, 0);
                },
                okText: '是',
                cancelText: '否',
            });
        } else {
            formatParams(values, profitSlottingRules, undefined);
        }
    };
    const formatParams = async (values, profitSlottingRules, changeCommonSlotting) => {
        if (submitLoading) {
            return;
        }
        changeSubmitLoading(true);
        const profitRuleList = values.profitRuleTypeList;

        const filterRuleList =
            profitRuleList?.filter((ele) => ele && !isEmpty(ele.ruleModel)) || [];

        const profitRuleTypes = filterRuleList?.map((ele) => {
            let options = {
                ...ele,
                ruleModel: String(ele.ruleModel),
            };
            if (ele.agreeType instanceof Array && ele.agreeType.length === 1) {
                const ruleInfo = computedLongShortValue(ele);
                if (ruleInfo) {
                    options = {
                        ...options,
                        ...ruleInfo,
                    };
                }
            }

            return options;
        });

        const params = {
            profitRuleTypes: JSON.stringify(profitRuleTypes),
            profitSlottingRules: JSON.stringify(profitSlottingRules),
            ruleFlag: '1',
            deducioActFlag: '0',
            effTime: values.effTime?.format?.('YYYY-MM-DD HH:mm:ss'),
            ruleId: mainInfo?.ruleId || '',
            operId: operId || mainInfo?.operId || selectRuleInfo?.operId,
            cooperationType: '02', // 购电模式
            premiumFlag: values.premiumFlag,
            useTemplate: values.useTemplate,
            templateId: values.templateId,
            changeCommonSlotting: changeCommonSlotting,
            cooperationPlatform,
        };
        if (values.premiumFlag === '1') {
            params.divideDTOList = JSON.stringify(values.divideDTOList);
        }
        if (values.expTime) {
            params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
        }

        //判断没有有效期并且没有使用模板则是长期有效，不需要调用接口校验，否则需要调用接口校验
        if ((params.expTime || params.templateId) && values.premiumFlag === '1') {
            const verifyResult = await getProfitruleExpireTimeFlag({
                templateFlag: params.useTemplate,
                templateId: params.templateId,
                expTime: params.expTime,
                operId: params.operId,
                cooperationPlatform,
            });
            if (verifyResult?.data?.expireFlag !== '02') {
                Modal.confirm({
                    title: '平台定价',
                    type: 'warn',
                    content: '平台定价失效时间晚于规则失效时间的场站，平台定价将自动失效',
                    onOk: () => {
                        requestSaveApi(params);
                    },
                    onCancel: () => {
                        changeSubmitLoading(false);
                    },
                });
            } else {
                requestSaveApi(params);
            }
        } else {
            requestSaveApi(params);
        }
    };

    const requestSaveApi = async (params) => {
        if (onWorkorderFinish) {
            onWorkorderFinish(params);
            changeSubmitLoading(false);
            return;
        }

        try {
            if (params.templateId) {
                const templateParams = {
                    templateId: params.templateId,
                    operId: params.operId,
                    cooperationType: OPER_COOPERATION.BUY,
                    premiumFlag: params.premiumFlag,
                    divideDTOList: params.divideDTOList,
                    profitSlottingRules: params?.profitSlottingRules,
                    changeCommonSlotting: params?.changeCommonSlotting,
                    cooperationPlatform,
                };
                await addStationTemplateApi(templateParams);
            } else {
                await saveprofitruleApi(params);
            }
            message.success('保存成功');
            history.go(-1);
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const changeExpFlagEvent = (event) => {
        const {
            target: { value },
        } = event;
        if (value === EXP_FLAG_TYPES.LONG) {
            form.setFieldsValue({ expTime: null });
        }
    };

    const disabledDate = (current) => {
        return current && current < moment().subtract(1, 'days').endOf('day');
    };

    return (
        <Card loading={getInfoLoading}>
            <Form
                form={form}
                {...formItemLayout}
                onFinish={onFinish}
                initialValues={{
                    useTemplate: '01',
                    effTime: moment().add(5, 'minutes'),

                    expTime: null,
                }}
                scrollToFirstError
            >
                <div className={styles.formTitle}>基础信息</div>
                <FormItem label="运营商名称:" {...formItemLayout}>
                    <Input
                        value={operName || (selectRuleInfo && selectRuleInfo.operName) || ''}
                        disabled
                    />
                </FormItem>
                <FormItem label="运营商编号:" {...formItemLayout}>
                    <Input
                        value={operId || (selectRuleInfo && selectRuleInfo.operId) || ''}
                        disabled
                    />
                </FormItem>
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            <>
                                <FormItem
                                    label={
                                        <span>
                                            使用模板
                                            <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    {...{
                                        labelCol: {
                                            span: 6,
                                        },
                                        wrapperCol: {
                                            span: 14,
                                        },
                                    }}
                                    name="useTemplate"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value === '02') {
                                                    const templateId = getFieldValue('templateId');
                                                    if (!templateId) {
                                                        return Promise.reject('请选择模板');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Space>
                                        <Radio.Group value={useTemplate}>
                                            <Radio value="01">否</Radio>
                                            <Radio value="02">是</Radio>
                                        </Radio.Group>
                                        {useTemplate === '02' && (
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    selectTemplateRef.current.show();
                                                }}
                                            >
                                                选择模板
                                            </Button>
                                        )}
                                    </Space>
                                </FormItem>
                            </>
                        );
                    }}
                </FormItem>
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        const premiumFlag = getFieldValue('premiumFlag');
                        return (
                            <Fragment>
                                {useTemplate === '02' && (
                                    <Fragment>
                                        <FormItem name="templateId" noStyle></FormItem>
                                        <FormItem
                                            shouldUpdate={(prevValues, curValues) =>
                                                prevValues.useTemplate !== curValues.useTemplate ||
                                                prevValues._ruleType !== curValues._ruleType
                                            }
                                            noStyle
                                        >
                                            {({ getFieldValue }) => {
                                                const useTemplate = getFieldValue('useTemplate');
                                                const _ruleType = getFieldValue('_ruleType');
                                                if (
                                                    useTemplate === '02' &&
                                                    _ruleType != RULE_TYPES.CITY
                                                ) {
                                                    return (
                                                        <TablePro
                                                            name="city"
                                                            loading={selectTemplateListLoading}
                                                            scroll={{ x: 'max-content' }}
                                                            dataSource={
                                                                selectTemplateInfo?.detailList || []
                                                            }
                                                            rowKey="detailId"
                                                            columns={childrenColumns}
                                                            pagination={false}
                                                            style={{ marginBottom: '16px' }}
                                                        />
                                                    );
                                                }
                                                return null;
                                            }}
                                        </FormItem>
                                    </Fragment>
                                )}
                                {useTemplate !== '02' && (
                                    <Fragment>
                                        <FormItem
                                            label="生效时间:"
                                            name="effTime"
                                            {...{
                                                labelCol: {
                                                    span: 6,
                                                },
                                                wrapperCol: {
                                                    span: 12,
                                                },
                                            }}
                                            rules={[
                                                { required: true, message: '请填写生效时间' },
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (!value) {
                                                            return Promise.reject('');
                                                        }

                                                        if (value && canISetEffTime) {
                                                            const nowTime = +new Date();
                                                            const sendEndTime = +new Date(value);

                                                            if (sendEndTime < nowTime) {
                                                                return Promise.reject(
                                                                    '生效时间不能早于当前时间',
                                                                );
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <DatePicker
                                                disabled={!canISetEffTime}
                                                disabledDate={disabledDate}
                                                showTime={{
                                                    format: 'HH:mm:ss',
                                                    defaultValue: moment('00:00:00', 'HH:mm:ss'),
                                                }}
                                                format="YYYY-MM-DD HH:mm:ss"
                                                showNow={false}
                                                renderExtraFooter={(a) => (
                                                    <Space>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const time = moment().add(
                                                                    10,
                                                                    'seconds',
                                                                );
                                                                form.setFieldsValue({
                                                                    effTime: time,
                                                                });
                                                            }}
                                                        >
                                                            10秒之后
                                                        </Button>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const effTime =
                                                                    form.getFieldValue('effTime');
                                                                if (effTime) {
                                                                    form.setFieldsValue({
                                                                        effTime:
                                                                            moment(effTime).startOf(
                                                                                'day',
                                                                            ),
                                                                    });
                                                                }
                                                            }}
                                                        >
                                                            时间重置
                                                        </Button>
                                                    </Space>
                                                )}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="失效时间:"
                                            name="expFlag"
                                            initialValue="01"
                                            {...{
                                                labelCol: {
                                                    span: 6,
                                                },
                                                wrapperCol: {
                                                    span: 18,
                                                },
                                            }}
                                        >
                                            <Radio.Group onChange={changeExpFlagEvent}>
                                                <Radio value={'01'}>长期</Radio>
                                                <Radio value={'02'}>
                                                    <Space>
                                                        指定时间
                                                        <FormItem
                                                            shouldUpdate={(prevValues, curValues) =>
                                                                prevValues.expFlag !==
                                                                    curValues.expFlag ||
                                                                prevValues.effTime !==
                                                                    curValues.effTime
                                                            }
                                                            noStyle
                                                        >
                                                            {({ getFieldValue }) => {
                                                                const expFlag =
                                                                    getFieldValue('expFlag');
                                                                const effTime =
                                                                    getFieldValue('effTime');

                                                                return (
                                                                    <FormItem
                                                                        name="expTime"
                                                                        noStyle
                                                                        rules={[
                                                                            (_) => ({
                                                                                validator(
                                                                                    rule,
                                                                                    value,
                                                                                ) {
                                                                                    if (!effTime) {
                                                                                        return Promise.reject(
                                                                                            '请选择生效开始时间',
                                                                                        );
                                                                                    }

                                                                                    if (value) {
                                                                                        const startTime =
                                                                                            +new Date(
                                                                                                effTime,
                                                                                            );
                                                                                        const sendEndTime =
                                                                                            +new Date(
                                                                                                value,
                                                                                            );

                                                                                        if (
                                                                                            sendEndTime <=
                                                                                            startTime
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                '失效时间不能早于生效时间',
                                                                                            );
                                                                                        }
                                                                                    }

                                                                                    return Promise.resolve();
                                                                                },
                                                                            }),
                                                                        ]}
                                                                    >
                                                                        <DatePicker
                                                                            disabledDate={
                                                                                disabledDate
                                                                            }
                                                                            disabled={
                                                                                expFlag != '02'
                                                                            }
                                                                            showTime={{
                                                                                format: 'HH:mm:ss',
                                                                                defaultValue:
                                                                                    moment(
                                                                                        '23:59:59',
                                                                                        'HH:mm:ss',
                                                                                    ),
                                                                            }}
                                                                            format="YYYY-MM-DD HH:mm:ss"
                                                                            showNow={false}
                                                                            renderExtraFooter={(
                                                                                a,
                                                                            ) => (
                                                                                <Space>
                                                                                    <Button
                                                                                        type="link"
                                                                                        onClick={() => {
                                                                                            const time =
                                                                                                moment().add(
                                                                                                    10,
                                                                                                    'seconds',
                                                                                                );
                                                                                            form.setFieldsValue(
                                                                                                {
                                                                                                    expTime:
                                                                                                        time,
                                                                                                },
                                                                                            );
                                                                                        }}
                                                                                    >
                                                                                        10秒之后
                                                                                    </Button>
                                                                                    <Button
                                                                                        type="link"
                                                                                        onClick={() => {
                                                                                            const effTime =
                                                                                                form.getFieldValue(
                                                                                                    'effTime',
                                                                                                );
                                                                                            if (
                                                                                                effTime
                                                                                            ) {
                                                                                                form.setFieldsValue(
                                                                                                    {
                                                                                                        effTime:
                                                                                                            moment(
                                                                                                                effTime,
                                                                                                            ).startOf(
                                                                                                                'day',
                                                                                                            ),
                                                                                                    },
                                                                                                );
                                                                                            }
                                                                                        }}
                                                                                    >
                                                                                        时间重置
                                                                                    </Button>
                                                                                </Space>
                                                                            )}
                                                                        />
                                                                    </FormItem>
                                                                );
                                                            }}
                                                        </FormItem>
                                                    </Space>
                                                </Radio>
                                            </Radio.Group>
                                        </FormItem>

                                        <FormItem
                                            label="结算方式"
                                            name="profitRuleTypeList"
                                            wrapperCol={{ span: 16 }}
                                            required
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (isEmpty(value)) {
                                                            return Promise.reject(
                                                                `请选择购电结算方式`,
                                                            );
                                                        }
                                                        for (const item of value) {
                                                            const ruleErr = checkRuleInfo(item);
                                                            if (ruleErr) {
                                                                return Promise.reject(ruleErr);
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <PurchaseRuleFormItem
                                                initialAgreeType={['01']}
                                            ></PurchaseRuleFormItem>
                                        </FormItem>
                                    </Fragment>
                                )}
                                <FormItem
                                    label={
                                        <span>
                                            通道费收取方式
                                            <SlottingTooltip></SlottingTooltip>
                                        </span>
                                    }
                                    name="profitSlottingRules"
                                    wrapperCol={{ span: 16 }}
                                    required
                                    rules={[
                                        () => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject(`请配置通道费收取方式`);
                                                }
                                                const errText = checkRuleValue(value);
                                                if (errText) {
                                                    return Promise.reject(errText);
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={defaultRuleValue}
                                >
                                    <SlottingRuleFormItem></SlottingRuleFormItem>
                                </FormItem>
                                <FormItem name="premiumFlag" noStyle></FormItem>
                                <FormItem label="是否溢价" required>
                                    <Switch
                                        checkedChildren="开"
                                        unCheckedChildren="关"
                                        checked={premiumFlag === '1'}
                                        defaultChecked={false}
                                        onChange={(checked) => {
                                            if (!checked) {
                                                checkClosePremiumCommon(cooperationPlatform).then(
                                                    (result) => {
                                                        form.setFieldValue('premiumFlag', '0');
                                                    },
                                                );
                                            } else {
                                                form.setFieldValue('premiumFlag', '1');
                                            }
                                        }}
                                    />
                                </FormItem>
                                {premiumFlag === '1' && (
                                    <FormItem
                                        label=" "
                                        name="divideDTOList"
                                        wrapperCol={{ span: 16 }}
                                        colon={false}
                                        rules={[
                                            () => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject(`请选择溢价分成方式`);
                                                    }
                                                    for (const item of value) {
                                                        if (!isNumber(item?.divideValue)) {
                                                            return Promise.reject('请填写分成');
                                                        } else if (
                                                            Number(item?.divideValue) > 10 ||
                                                            Number(item?.divideValue) < 0
                                                        ) {
                                                            return Promise.reject(
                                                                '只能填写0.000～10.000之间的数字',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <PremiumSharingItem />
                                    </FormItem>
                                )}
                            </Fragment>
                        );
                    }}
                </FormItem>
                <FormItem noStyle name="ruleId"></FormItem>
                <div className={styles['form-submit']}>
                    <Button
                        className={styles['form-btn']}
                        type="primary"
                        htmlType="submit"
                        loading={submitLoading}
                    >
                        提交
                    </Button>
                    <Button className={styles['form-btn']} onClick={closeEvent}>
                        取消
                    </Button>
                </div>
            </Form>
            <SelectRuleModal onFinish={changeSelectEvent} ref={selectTemplateRef} />
        </Card>
    );
});

const PurchaseDetails = (props) => {
    const { history, location, route, match } = props;

    let ruleId = match.params.ruleId;
    let operName = location.query.operName;
    let operId = location.query.operId;

    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <PurchaseDetailsView
                {...props}
                closeEvent={goBack}
                ruleId={ruleId}
                operName={operName}
                operId={operId}
            ></PurchaseDetailsView>
        </PageHeaderWrapper>
    );
};

export default connect(({ purchaseModel, loading }) => ({
    purchaseModel,
    getInfoLoading: loading.effects['purchaseModel/getRuleInfo'],
}))(PurchaseDetails);
