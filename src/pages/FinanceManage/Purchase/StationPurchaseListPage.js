import { ExclamationCircleOutlined, LeftOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Select,
    Modal,
    Popconfirm,
    Tabs,
    DatePicker,
    Space,
    message,
    Radio,
    Alert,
    Tooltip,
    Switch,
} from 'antd';
import { isNumber } from 'lodash';
import moment from 'moment';
import { Fragment, useState, useEffect, useMemo, useRef } from 'react';
import { connect, Link } from 'umi';

import styles from './PurchaseListPage.less';

import usePageState from '@/hooks/usePageState.js';
import {
    saveprofitruleApi,
    getStationRuleListPath,
    addStationTemplateApi,
    queryProfitrulePremiumFlag,
    getRuleInfoApi,
} from '@/services/FinanceManage/ProfitRuleApi';
import { getStationCityProfitRuleApi } from '@/services/FinanceManage/CityRuleApi';
import { getCityAndStationByOperIdApi } from '@/services/CommonApi';

import { exportTableByParams, isEmpty, checkClosePremiumCommon } from '@/utils/utils';
import { OPER_COOPERATION, RULE_TYPES } from '@/config/declare';

import TablePro from '@/components/TablePro';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import SearchOptionsBar from '@/components/SearchOptionsBar/index';
import EditRulesModal from '../CityRule/EditRulesModal';
import PurchaseRuleFormItem, { checkRuleInfo } from './components/PurchaseRuleFormItem';
import SlottingRuleFormItem, {
    checkRuleValue,
    defaultRuleValue,
} from './components/SlottingRuleFormItem';
import SelectRuleModal from './components/SelectRuleModal';
import PremiumSharingItem from './components/PremiumSharingItem';
import SlottingTooltip from './components/SlottingTooltip';
import { TemplateListModal } from './PurchaseTemplateListPage';
import { CollectTypes, computedLongShortValue } from './PurchaseConfig';

import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const EXP_FLAG_TYPES = {
    LONG: '01', // 长期
    SET: '02', // 指定
};

const STATUS_TYPES = {
    ALL: '00',
    CURRENCY: '01',
    STATION: '02',
    CITY: '03',
};

const formItemLayout = {};

const modelFormLayout = {
    labelCol: {
        flex: '0 0 150px',
    },
    wrapperCol: {
        span: 8,
    },
};

const templateColumns = [
    {
        title: '序号 ',
        width: 80,
        render(text, record, index) {
            return <span title={index}>{index + 1}</span>;
        },
    },
    {
        title: '购电结算方式',
        dataIndex: 'ruleName',
        with: 240,
        render(text, record) {
            return (
                <span
                    title={text}
                    style={{
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all',
                        color: record.dayexpflag == '1' ? 'red' : '',
                    }}
                >
                    {text}
                </span>
            );
        },
    },
    {
        title: '生效时间',
        dataIndex: 'effTime',
        render(text, record) {
            return (
                <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                    {text}
                </span>
            );
        },
    },
    {
        title: '失效时间',
        dataIndex: 'expTime',
        render(text, record) {
            return (
                <span title={text} style={{ color: record.dayexpflag == '1' ? 'red' : '' }}>
                    {(text?.length && text) || record.maxExpTime}
                </span>
            );
        },
    },
];

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        operId,
        purchaseModel: { templateSelectList },
        listLoading,
    } = props;
    const [cityList, changeCityList] = useState([]);
    const [searchTxt, changeSearchTxt] = useState(null);

    useEffect(() => {
        initCityEvent();

        dispatch({
            type: 'purchaseModel/getSelectTemplateList',
            cooperationType: '02',
        });
    }, []);

    const selectTemplateOptions = useMemo(() => {
        return templateSelectList.map((ele) => {
            return (
                <Option value={ele.templateId} key={ele.templateId}>
                    {ele.name}
                </Option>
            );
        });
    }, [templateSelectList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const operGroupRef = useRef();
    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const initCityEvent = async () => {
        try {
            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                operId,
                cooperationType: '02',
            });
            changeCityList(cityAndStationList);
        } catch (error) {
        } finally {
        }
    };

    const handleSearch = (txt) => {
        changeSearchTxt(txt);
    };

    const handleFilter = (filterTxt, option) => true;

    const cityOptions = useMemo(() => {
        if (searchTxt) {
            const list = [];
            for (const item of cityList) {
                const searchName = item.title;
                if (searchName.indexOf(searchTxt) >= 0) {
                    list.push(item);
                }
            }
            return list.map((ele) => (
                <Option key={ele.value} value={ele.value}>
                    {ele.title}
                </Option>
            ));
        }
        return cityList.map((ele) => (
            <Option key={ele.value} value={ele.value}>
                {ele.title}
            </Option>
        ));
    }, [cityList, searchTxt]);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{ isAbandon: '0' }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                onSearchGroup={onSearchGroup}
                // open
            >
                <Col span={8}>
                    <FormItem label="所选城市:" name="city">
                        <Select
                            showSearch
                            mode="multiple"
                            allowClear
                            onSearch={handleSearch}
                            filterOption={handleFilter}
                            maxTagCount={2}
                            placeholder="请选择"
                        >
                            {cityOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="场站名称:" name="stationName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="选择模板" name="templateIds">
                        <Select mode="multiple" allowClear placeholder="请选择">
                            {selectTemplateOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="生效时间" name="effTime">
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="失效时间" name="expTime">
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="是否废弃" name="stationBusiStatus">
                        <Select allowClear placeholder="请选择">
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
                <FormItem noStyle name="stationIdList" />
            </SearchOptionsBar>
            <OperGroupImportModal
                title="批量查询"
                initRef={operGroupRef}
                buildId={operId}
                onConfirm={(addStationList) => {
                    const list =
                        (addStationList?.length && addStationList.map((item) => item.stationId)) ||
                        [];
                    form.setFieldsValue({ stationIdList: list });
                    const values = form.getFieldsValue(true);
                    onSubmit(values);
                }}
            />
        </Form>
    );
};

const EditModelLayout = (props) => {
    const {
        dispatch,
        match,
        multiple,
        onFinish,
        loading,
        submitLoading,
        selectTemplateListLoading,
        onCancel,
        purchaseModel,
        editItems,
        resetEvent,
        confirmCityEvent,
        addCityRuleEvent, // 新增城市
        cooperationPlatform,
    } = props;

    const { operId } = match.params;
    const [editForm] = Form.useForm();
    const { templateSelectList, selectTemplateInfo, editRuleInfo } = purchaseModel || {};
    const selectTemplateRef = useRef();
    const [editRuleType, changeEditRuleType] = useState(RULE_TYPES.COMMON);
    const [premiumFlagEnable, setPremiumFlagEnable] = useState(false); //是否可以编辑溢价分成

    useEffect(async () => {
        if (editRuleType === RULE_TYPES.STATION) {
            const params = {
                profitRuleType: '02',
                operId: editItems[0]?.operId || operId,
                stationIds: editItems?.map((v) => v.stationId)?.join(','),
                cooperationType: '02',
                cooperationPlatform,
            };
            const result = await queryProfitrulePremiumFlag(params);

            const {
                data: { list },
            } = await getRuleInfoApi({
                profitRuleType: '01',
                operId: editItems[0]?.operId || operId,
                cooperationPlatform,
            });

            if (result?.data?.premiumFlag === '01') {
                setPremiumFlagEnable(true);
                if (editItems.length > 1) {
                    //场站批量配置时 是否可以配置分润溢价取运营商通用是否开启开启就默认开
                    let options = {
                        premiumFlag: '1',
                    };
                    if (list[0]) {
                        options.divideDTOList = list[0].divideList?.map((v) => ({
                            divideType: v.divideType,
                            divideValue: v.divideValue,
                        }));
                    }
                    editForm.setFieldsValue(options);
                }
            } else {
                setPremiumFlagEnable(false);
            }
            if (editItems.length > 1 && list[0]) {
                //批量时取通用的通道配置
                const { profitSlottingRuleList } = list[0];

                if (profitSlottingRuleList instanceof Array) {
                    editForm.setFieldsValue({
                        profitSlottingRules: {
                            collectType: CollectTypes.DETAIL,
                            list: profitSlottingRuleList,
                        },
                    });
                }
            }
        }
    }, [editItems, editRuleType]);

    // 主要信息以editRuleInfo.list的首个元素为准
    const mainInfo = editRuleInfo?.list?.[0];

    // 规则场站
    const [allStations, updateAllStations] = useState([]);
    useEffect(() => {
        if (mainInfo?.ruleRange == '02' && editRuleInfo?.list?.length) {
            // 如果默认是部分，维护进规则场站列表
            updateAllStations([...editRuleInfo.list]);
        }
    }, [mainInfo?.ruleRange]);

    const isCommonRule = useMemo(() => {
        if (editRuleType === RULE_TYPES.COMMON) {
            return true;
        }
        return false;
    }, [editRuleType]);

    const selectTemplateDetailList = useMemo(() => {
        if (selectTemplateInfo) {
            return selectTemplateInfo.detailList;
        }
    }, [selectTemplateInfo]);

    const isEditStationRule = useMemo(() => {
        if (editItems && editItems[0] && editItems[0].profitRuleType) {
            if (editItems[0].profitRuleType === RULE_TYPES.STATION) {
                return true;
            }
        }
        return false;
    }, [editItems]);

    const selectTemplateOptions = useMemo(() => {
        return templateSelectList.map((ele) => {
            return (
                <Option value={ele.templateId} key={ele.templateId}>
                    {ele.name}
                </Option>
            );
        });
    }, [templateSelectList]);

    useEffect(() => {
        loadSelectTemplateList();
    }, []);

    const loadSelectTemplateList = () => {
        dispatch({
            type: 'purchaseModel/getSelectTemplateList',
            cooperationType: OPER_COOPERATION.BUY,
        });
    };

    useEffect(() => {
        if (editItems?.length > 1) {
            changeEditRuleType(RULE_TYPES.STATION);
            editForm.setFieldsValue({
                _ruleType: RULE_TYPES.STATION,
            });
            editItems[0].profitRuleType = RULE_TYPES.STATION;
        } else if (editItems?.[0]?.profitRuleType) {
            changeEditRuleType(RULE_TYPES.STATION);
        }
    }, [editItems, editRuleInfo]);

    useEffect(() => {
        const params = {};
        if (mainInfo) {
            const {
                effTime,
                expTime,
                profitRuleTypeList,
                profitSlottingRuleList,
                profitRuleType,
                premiumFlag,
            } = mainInfo;

            if (mainInfo?.profitRuleType == '03') {
                params.ruleRange = mainInfo.ruleRange;
                params.operId = mainInfo.operId;
                params.cityName = mainInfo.cityName;
                params.stationIds = mainInfo.stationIds;
            }
            params.premiumFlag = premiumFlag;
            if (premiumFlag === '1') {
                params.divideDTOList = mainInfo.divideList?.map((v) => ({
                    divideType: v.divideType,
                    divideValue: v.divideValue,
                }));
            }
            params.effTime = effTime ? moment(effTime) : moment().add(5, 'minutes');
            params.expTime = expTime ? moment(expTime) : null;
            if (expTime) {
                params.expFlag = '02';
            } else {
                params.expFlag = '01';
            }
            if (profitRuleTypeList && profitRuleTypeList[0]) {
                params.profitRuleTypes = params.profitRuleTypeList = profitRuleTypeList;
            }
            if (mainInfo.templateId) {
                params.useTemplate = '02';
                params.templateId = mainInfo.templateId;
                changeSelectEvent(mainInfo.templateId);
            }

            if (profitSlottingRuleList instanceof Array) {
                params.profitSlottingRules = {
                    collectType: CollectTypes.DETAIL,
                    list: profitSlottingRuleList,
                };
            }

            if (mainInfo.profitRuleType !== RULE_TYPES.STATION) {
                initSlottingData();
            }

            const _ruleType = editForm.getFieldValue('_ruleType');
            params._ruleType = profitRuleType || _ruleType;
            if (profitRuleType) {
                changeEditRuleType(profitRuleType);
                if (profitRuleType === RULE_TYPES.COMMON) {
                    editForm.setFieldsValue({
                        useTemplate: '01',
                    });
                } else if (profitRuleType === RULE_TYPES.STATION) {
                    if (mainInfo.templateId) {
                        editForm.setFieldsValue({
                            useTemplate: '02',
                            templateId: mainInfo.templateId,
                        });
                    }
                }
            }
        }
        editForm.setFieldsValue(params);
    }, [mainInfo]);

    const initSlottingData = async (info) => {
        try {
            //初始化通用的通道配置

            const params = {
                operId: info.operId,
                profitRuleType: '01',
                cooperationType: '02',
            };

            const { data } = await getRuleInfoApi(params);
            const parentItem = data?.list?.[0];
            if (parentItem) {
                const { profitSlottingRuleList } = parentItem;
                if (profitSlottingRuleList instanceof Array) {
                    let profitSlottingRules = {
                        collectType: CollectTypes.DETAIL,
                        list: profitSlottingRuleList,
                    };
                    editForm.setFieldsValue({ profitSlottingRules });
                }

                return parentItem;
            }

            return null;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 读取当前规则是否配置了城市
    const [cityRuleFlag, updateCityRuleFlag] = useState(undefined);
    useEffect(() => {
        if (!editItems || editItems.length != 1) {
            updateCityRuleFlag(undefined);
        } else if (!cityRuleFlag) {
            // 读取城市规则是否有配置
            initCityFlag();
        }
    }, [editItems]);

    // 查询运营商站点是否配置了城市分润规则
    const initCityFlag = async () => {
        try {
            const {
                data: { flag },
            } = await getStationCityProfitRuleApi({
                operId,
                stationId: editItems[0].stationId,
                city: editItems[0].city,
                cooperationType: '02',
                cooperationPlatform,
            });
            updateCityRuleFlag(flag);
        } catch (error) {
            updateCityRuleFlag(undefined);
        }
    };

    const onEditFinish = (values) => {
        if (isCommonRule) {
            if (isEditStationRule) {
                resetEvent(editItems);
                return;
            }
            channleEvent();
            return;
        }

        if (values._ruleType == RULE_TYPES.CITY) {
            confirmCityEvent(mainInfo);
            return;
        }

        const profitSlottingRuleInfo = values.profitSlottingRules;

        const profitSlottingRules =
            profitSlottingRuleInfo?.list?.filter((ele) => ele.slottingFlag) || [];

        if (values.useTemplate === '02') {
            const options = {
                templateId: values.templateId,
                premiumFlag: values.premiumFlag,
                profitSlottingRules: JSON.stringify(profitSlottingRules),
                cooperationPlatform,
            };
            if (values.premiumFlag === '1') {
                options.divideDTOList = JSON.stringify(values.divideDTOList);
            }
            onFinish(options);
            return;
        }

        const profitRuleList = values.profitRuleTypeList;

        const filterRuleList = profitRuleList.filter((ele) => ele && !isEmpty(ele.ruleModel)) || [];

        const profitRuleTypes = filterRuleList.map((ele) => {
            let options = {
                ...ele,
                ruleModel: String(ele.ruleModel),
            };
            if (ele.agreeType instanceof Array && ele.agreeType.length === 1) {
                const ruleInfo = computedLongShortValue(ele);
                if (ruleInfo) {
                    options = {
                        ...options,
                        ...ruleInfo,
                    };
                }
            }

            return options;
        });

        const params = {
            profitRuleTypes: JSON.stringify(profitRuleTypes),
            profitSlottingRules: JSON.stringify(profitSlottingRules),

            ruleFlag: '1',
            deducioActFlag: '0',
            effTime: values.effTime.format('YYYY-MM-DD HH:mm:ss'),
            cooperationType: '02', // 购电模式
            premiumFlag: values.premiumFlag, //溢价分成
            cooperationPlatform,
        };
        if (values.expTime) {
            params.expTime = values.expTime.format('YYYY-MM-DD HH:mm:ss');
        }
        if (values.premiumFlag === '1') {
            params.divideDTOList = JSON.stringify(values.divideDTOList);
        }
        params.profitRuleType = values._ruleType;

        onFinish(params);
    };

    // 取消
    const channleEvent = () => {
        onCancel();
    };

    const changeEditRuleTypeEvent = (e) => {
        const { value } = e.target;
        changeEditRuleType(value);
        toggleCommonAndStationEvent(value);
    };

    const toggleCommonAndStationEvent = (value) => {
        if (isEmpty(editItems) && isEmpty(editItems[0])) {
            return;
        }
        if (value === RULE_TYPES.COMMON) {
            editForm.setFieldsValue({
                useTemplate: '01',
            });
            dispatch({
                type: 'purchaseModel/getRuleInfo',
                params: {
                    operId,
                    ruleId: editItems[0].ruleId,
                    profitRuleType: RULE_TYPES.COMMON,
                    cooperationType: '02',
                    cooperationPlatform,
                },
            });
        } else if (value === RULE_TYPES.CITY) {
            const params = {
                ruleId: editItems[0].ruleId,
                city: editItems[0].city,
                stationId: editItems[0].stationId,
                operId,
                profitRuleType: '03',
                cooperationType: '02',
                cooperationPlatform,
            };
            dispatch({
                type: 'purchaseModel/getRuleInfo',
                params,
            });
        } else if (value === RULE_TYPES.STATION) {
            if (mainInfo.templateId) {
                editForm.setFieldsValue({
                    useTemplate: '02',
                    templateId: mainInfo.templateId,
                });
            }

            dispatch({
                type: 'purchaseModel/getRuleInfo',
                params: {
                    operId,
                    ruleId: editItems[0].ruleId,
                    stationId: editItems[0].stationId,
                    profitRuleType: RULE_TYPES.STATION,
                    cooperationType: '02',
                    cooperationPlatform,
                },
            });
        }
    };

    const changeSelectEvent = (value) => {
        dispatch({
            type: 'purchaseModel/getSelectTemplateInfo',
            templateId: value,
        });
        editForm.setFieldValue('templateId', value);
        editForm.validateFields(['useTemplate']);
    };
    const templateSelectView = useMemo(() => {
        if (!isCommonRule && mainInfo?.profitRuleType != RULE_TYPES.CITY) {
            return (
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            <>
                                <FormItem
                                    label={
                                        <span>
                                            使用模板
                                            <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                        </span>
                                    }
                                    {...{
                                        wrapperCol: {
                                            span: 14,
                                        },
                                    }}
                                    name="useTemplate"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (value === '02') {
                                                    const templateId = getFieldValue('templateId');
                                                    if (!templateId) {
                                                        return Promise.reject('请选择模板');
                                                    }
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <Space>
                                        <Radio.Group value={useTemplate}>
                                            <Radio value="01">否</Radio>
                                            <Radio value="02">是</Radio>
                                        </Radio.Group>
                                        {useTemplate === '02' && (
                                            <Button
                                                type="primary"
                                                onClick={() => {
                                                    selectTemplateRef.current.show();
                                                }}
                                            >
                                                选择模板
                                            </Button>
                                        )}
                                    </Space>
                                </FormItem>
                                {useTemplate === '02' && (
                                    <FormItem name="templateId" noStyle></FormItem>
                                )}
                            </>
                        );
                    }}
                </FormItem>
            );
        }
        return null;
    }, [isCommonRule, mainInfo, selectTemplateOptions]);

    return (
        <Card loading={loading} bordered={false} bodyStyle={{ padding: 0 }}>
            <Form
                form={editForm}
                {...modelFormLayout}
                onFinish={onEditFinish}
                initialValues={{
                    useTemplate: '01',
                    effTime:
                        mainInfo && mainInfo.effTime
                            ? moment(mainInfo.effTime)
                            : moment().add(5, 'minutes'),

                    expTime: '',
                }}
                scrollToFirstError
            >
                {multiple ? (
                    <Alert
                        message="提交后将直接替换所有选中场站的结算规则及模板"
                        type="warning"
                        showIcon
                    />
                ) : null}
                <FormItem
                    label="规则类型"
                    {...{
                        wrapperCol: {
                            span: 14,
                        },
                    }}
                    initialValue={RULE_TYPES.COMMON}
                    name="_ruleType"
                >
                    <Radio.Group value={editRuleType} onChange={changeEditRuleTypeEvent}>
                        {editItems?.length > 1 ? (
                            <Tooltip title="批量编辑不支持选择通用规则">
                                <Radio value={RULE_TYPES.COMMON} disabled>
                                    通用规则
                                </Radio>
                            </Tooltip>
                        ) : (
                            <Radio value={RULE_TYPES.COMMON} disabled={cityRuleFlag == true}>
                                通用规则
                            </Radio>
                        )}
                        {editItems?.length > 1 ? (
                            <Tooltip title="批量编辑不支持选择城市规则">
                                <Radio value={RULE_TYPES.CITY} disabled>
                                    城市规则
                                </Radio>
                            </Tooltip>
                        ) : cityRuleFlag ? (
                            <Radio value={RULE_TYPES.CITY}>城市规则</Radio>
                        ) : (
                            <Tooltip
                                title={
                                    <>
                                        <span>未配置城市规则。</span>
                                        <Button
                                            type="link"
                                            onClick={() => addCityRuleEvent && addCityRuleEvent()}
                                        >
                                            现在去配？
                                        </Button>
                                    </>
                                }
                            >
                                <Radio value={RULE_TYPES.CITY} disabled>
                                    城市规则
                                </Radio>
                            </Tooltip>
                        )}
                        <Radio value={RULE_TYPES.STATION}>场站规则</Radio>
                    </Radio.Group>
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const _ruleType = getFieldValue('_ruleType');
                        const useTemplate = getFieldValue('useTemplate');
                        return (
                            (_ruleType == RULE_TYPES.CITY && (
                                <>
                                    <FormItem label="运营商名称">
                                        <span>{mainInfo?.operName || '-'}</span>
                                    </FormItem>

                                    <FormItem label="运营商编号:">
                                        <span>{mainInfo?.operId || '-'}</span>
                                    </FormItem>

                                    <FormItem label="城市">{mainInfo?.cityName || '-'}</FormItem>

                                    <FormItem
                                        label="规则范围"
                                        name="ruleRange"
                                        rules={[{ required: true, message: '请选择' }]}
                                    >
                                        <Radio.Group disabled>
                                            <Radio value="01">全部</Radio>
                                            <Radio value="02">部分</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                    <FormItem
                                        label={
                                            <span>
                                                使用模板
                                                <Tooltip title="模板支持同时配置多条规则，规则可按时间依次生效。">
                                                    <InfoCircleOutlined
                                                        style={{ marginLeft: '6px' }}
                                                    />
                                                </Tooltip>
                                            </span>
                                        }
                                        {...{
                                            wrapperCol: {
                                                span: 14,
                                            },
                                        }}
                                        name="useTemplate"
                                    >
                                        <Radio.Group disabled>
                                            <Radio value="01">否</Radio>
                                            <Radio value="02">是</Radio>
                                        </Radio.Group>
                                    </FormItem>

                                    {(useTemplate === '01' && (
                                        <Fragment>
                                            <FormItem label="生效时间">
                                                {mainInfo?.effTime || '-'}
                                            </FormItem>

                                            <FormItem label="失效时间">
                                                {(mainInfo?.expFlag == '01' && '长期') ||
                                                    mainInfo?.expTime ||
                                                    '-'}
                                            </FormItem>

                                            {(mainInfo?.ruleRange == 1 && (
                                                <Fragment>
                                                    <FormItem
                                                        label="结算方式"
                                                        name="profitRuleTypes"
                                                        wrapperCol={{ span: 16 }}
                                                        required
                                                        rules={[
                                                            () => ({
                                                                validator(rule, value) {
                                                                    if (isEmpty(value)) {
                                                                        return Promise.reject(
                                                                            `请选择购电结算方式`,
                                                                        );
                                                                    }
                                                                    for (const item of value) {
                                                                        const ruleErr =
                                                                            checkRuleInfo(item);
                                                                        if (ruleErr) {
                                                                            return Promise.reject(
                                                                                ruleErr,
                                                                            );
                                                                        }
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                    >
                                                        <PurchaseRuleFormItem
                                                            disabled
                                                        ></PurchaseRuleFormItem>
                                                    </FormItem>

                                                    <FormItem
                                                        label={
                                                            <span>
                                                                通道费收取方式
                                                                <SlottingTooltip></SlottingTooltip>
                                                            </span>
                                                        }
                                                        name="profitSlottingRules"
                                                        wrapperCol={{ span: 24 }}
                                                        required
                                                        rules={[
                                                            () => ({
                                                                validator(rule, value) {
                                                                    if (isEmpty(value)) {
                                                                        return Promise.reject(
                                                                            `请配置通道费收取方式`,
                                                                        );
                                                                    }
                                                                    const errText =
                                                                        checkRuleValue(value);
                                                                    if (errText) {
                                                                        return Promise.reject(
                                                                            errText,
                                                                        );
                                                                    }

                                                                    return Promise.resolve();
                                                                },
                                                            }),
                                                        ]}
                                                        initialValue={defaultRuleValue}
                                                    >
                                                        <SlottingRuleFormItem
                                                            disabled
                                                        ></SlottingRuleFormItem>
                                                    </FormItem>
                                                </Fragment>
                                            )) ||
                                                (mainInfo?.ruleRange == 2 && (
                                                    <FormItem
                                                        label="规则场站"
                                                        {...{
                                                            wrapperCol: {
                                                                span: 24,
                                                            },
                                                        }}
                                                    >
                                                        <TablePro
                                                            name="rule"
                                                            scroll={{ x: 'max-content' }}
                                                            rowKey={(record) => record.stationId}
                                                            dataSource={allStations}
                                                            pagination={false}
                                                            columns={[
                                                                {
                                                                    title: '适用场站',
                                                                    width: 140,
                                                                    dataIndex: 'ruleRangeName',
                                                                    render(text, record) {
                                                                        return (
                                                                            <Tooltip
                                                                                title={
                                                                                    <div
                                                                                        style={{
                                                                                            maxWidth:
                                                                                                '620px',
                                                                                            maxHeight:
                                                                                                '320px',
                                                                                            overflowY:
                                                                                                'auto',
                                                                                        }}
                                                                                    >
                                                                                        {
                                                                                            record.stationNames
                                                                                        }
                                                                                    </div>
                                                                                }
                                                                            >
                                                                                {text}
                                                                            </Tooltip>
                                                                        );
                                                                    },
                                                                },
                                                                {
                                                                    title: '购电结算方式',
                                                                    dataIndex: 'ruleName',
                                                                    width: 300,
                                                                    render(text, record) {
                                                                        return (
                                                                            <span
                                                                                style={{
                                                                                    whiteSpace:
                                                                                        'pre-wrap',
                                                                                    wordBreak:
                                                                                        'break-all',
                                                                                }}
                                                                                title={text}
                                                                            >
                                                                                {text}
                                                                            </span>
                                                                        );
                                                                    },
                                                                },
                                                            ]}
                                                            sticky={{ offsetHeader: 64 }}
                                                        />
                                                    </FormItem>
                                                )) ||
                                                null}
                                        </Fragment>
                                    )) ||
                                        null}

                                    <FormItem name="operName" noStyle />
                                    <FormItem name="operId" noStyle />
                                    <FormItem name="cityName" noStyle />
                                    <FormItem name="stationIds" noStyle />
                                    <FormItem name="effTime" noStyle />
                                    <FormItem name="expFlag" noStyle />
                                    <FormItem name="expTime" noStyle />
                                </>
                            )) ||
                            null
                        );
                    }}
                </FormItem>

                {templateSelectView}

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        const _ruleType = getFieldValue('_ruleType');
                        if (useTemplate === '01' && _ruleType !== RULE_TYPES.CITY) {
                            return (
                                <EditRuleLayout
                                    disabled={isCommonRule}
                                    form={editForm}
                                    {...props}
                                    editRuleInfo={mainInfo}
                                />
                            );
                        }
                        return null;
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        if (useTemplate === '02') {
                            return (
                                <TablePro
                                    name="city"
                                    loading={selectTemplateListLoading}
                                    scroll={{ x: 'max-content' }}
                                    dataSource={selectTemplateDetailList}
                                    columns={templateColumns}
                                    pagination={false}
                                    style={{ marginBottom: '16px' }}
                                />
                            );
                        }
                        return null;
                    }}
                </FormItem>
                {/* 通用规则、城市规则、场站规则，使用模板选择是，仍然有“通道费收取方式”字段，即模板中不配通道费，统一在相应的规则中配置。
                    配置城市/场站规则（包括批量编辑）时，需要自动带入通用规则的通道费配置。 */}
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.useTemplate !== curValues.useTemplate ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const useTemplate = getFieldValue('useTemplate');
                        const _ruleType = getFieldValue('_ruleType');
                        if (useTemplate === '02') {
                            return (
                                <FormItem
                                    label={
                                        <span>
                                            通道费收取方式
                                            <SlottingTooltip></SlottingTooltip>
                                        </span>
                                    }
                                    name="profitSlottingRules"
                                    wrapperCol={{ span: 16 }}
                                    required
                                    rules={[
                                        () => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject(`请配置通道费收取方式`);
                                                }
                                                const errText = checkRuleValue(value);
                                                if (errText) {
                                                    return Promise.reject(errText);
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={defaultRuleValue}
                                >
                                    <SlottingRuleFormItem
                                        disabled={props?.disabled}
                                    ></SlottingRuleFormItem>
                                </FormItem>
                            );
                        }
                        return null;
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.premiumFlag !== curValues.premiumFlag ||
                        prevValues._ruleType !== curValues._ruleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const premiumFlag = getFieldValue('premiumFlag');
                        const ruleType = getFieldValue('_ruleType');
                        return (
                            <Fragment>
                                <FormItem name="premiumFlag" noStyle></FormItem>
                                <FormItem label="是否溢价" required>
                                    <Switch
                                        checkedChildren="开"
                                        unCheckedChildren="关"
                                        checked={premiumFlag === '1'}
                                        defaultChecked
                                        disabled={
                                            isCommonRule ||
                                            ruleType === RULE_TYPES.CITY ||
                                            !premiumFlagEnable
                                        }
                                        onChange={(checked) => {
                                            if (!checked) {
                                                checkClosePremiumCommon(cooperationPlatform).then(
                                                    (result) => {
                                                        editForm.setFieldValue('premiumFlag', '0');
                                                    },
                                                );
                                            } else {
                                                editForm.setFieldValue('premiumFlag', '1');
                                            }
                                        }}
                                    />
                                </FormItem>
                                {premiumFlag === '1' && (
                                    <FormItem
                                        label=" "
                                        name="divideDTOList"
                                        wrapperCol={{ span: 16 }}
                                        colon={false}
                                        rules={[
                                            () => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject(`请选择溢价分成方式`);
                                                    }
                                                    for (const item of value) {
                                                        if (!isNumber(item?.divideValue)) {
                                                            return Promise.reject('请填写分成');
                                                        } else if (
                                                            Number(item?.divideValue) > 10 ||
                                                            Number(item?.divideValue) < 0
                                                        ) {
                                                            return Promise.reject(
                                                                '只能填写0.000～10.000之间的数字',
                                                            );
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <PremiumSharingItem
                                            disabled={isCommonRule || ruleType === RULE_TYPES.CITY}
                                        />
                                    </FormItem>
                                )}
                            </Fragment>
                        );
                    }}
                </FormItem>
                <div className={styles['form-submit']}>
                    {!isCommonRule || (isCommonRule && isEditStationRule) ? (
                        <Button
                            className={styles['form-btn']}
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            提交
                        </Button>
                    ) : null}

                    <Button className={styles['form-btn']} onClick={channleEvent}>
                        取消
                    </Button>
                </div>
            </Form>
            <SelectRuleModal onFinish={changeSelectEvent} ref={selectTemplateRef} />
        </Card>
    );
};

const EditRuleLayout = (props) => {
    const { form, editRuleInfo, disabled } = props;
    const [canISetEffTime, changeCiSetEffTime] = useState(true);
    const disabledDate = (current) => {
        return current && current < moment().subtract(1, 'days').endOf('day');
    };
    const changeExpFlagEvent = (event) => {
        const {
            target: { value },
        } = event;
        if (value === EXP_FLAG_TYPES.LONG) {
            form.setFieldsValue({ expTime: null });
        }
    };

    useEffect(() => {
        if (editRuleInfo) {
            const { effTime } = editRuleInfo;
            if (moment(effTime).isBefore(moment())) {
                changeCiSetEffTime(false);
            } else {
                changeCiSetEffTime(true);
            }
        }
    }, [editRuleInfo]);
    return (
        <Fragment>
            <FormItem
                label="生效时间:"
                name="effTime"
                {...{
                    wrapperCol: {
                        span: 12,
                    },
                }}
                rules={[
                    { required: true, message: '请填写生效时间' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (!value) {
                                return Promise.reject('');
                            }

                            if (value && canISetEffTime) {
                                const nowTime = +new Date();
                                const sendEndTime = +new Date(value);

                                if (sendEndTime < nowTime) {
                                    return Promise.reject('生效时间不能早于当前时间');
                                }
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <DatePicker
                    disabledDate={disabledDate}
                    showTime={{
                        format: 'HH:mm:ss',
                        defaultValue: moment('00:00:00', 'HH:mm:ss'),
                    }}
                    format="YYYY-MM-DD HH:mm:ss"
                    disabled={disabled}
                    showNow={false}
                    renderExtraFooter={(a) => (
                        <Space>
                            <Button
                                type="link"
                                onClick={() => {
                                    const time = moment().add(10, 'seconds');
                                    form.setFieldsValue({
                                        effTime: time,
                                    });
                                }}
                            >
                                10秒之后
                            </Button>
                            <Button
                                type="link"
                                onClick={() => {
                                    const effTime = form.getFieldValue('effTime');
                                    if (effTime) {
                                        form.setFieldsValue({
                                            effTime: moment(effTime).startOf('day'),
                                        });
                                    }
                                }}
                            >
                                时间重置
                            </Button>
                        </Space>
                    )}
                />
            </FormItem>

            <FormItem
                label="失效时间:"
                name="expFlag"
                initialValue="01"
                {...{
                    wrapperCol: {
                        span: 18,
                    },
                }}
            >
                <Radio.Group onChange={changeExpFlagEvent} disabled={disabled}>
                    <Radio value={'01'}>长期</Radio>
                    <Radio value={'02'}>
                        <Space>
                            指定时间
                            <FormItem
                                shouldUpdate={(prevValues, curValues) =>
                                    prevValues.expFlag !== curValues.expFlag ||
                                    prevValues.effTime !== curValues.effTime
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const expFlag = getFieldValue('expFlag');
                                    const effTime = getFieldValue('effTime');

                                    return (
                                        <FormItem
                                            name="expTime"
                                            noStyle
                                            rules={[
                                                (_) => ({
                                                    validator(rule, value) {
                                                        if (!effTime) {
                                                            return Promise.reject(
                                                                '请选择生效开始时间',
                                                            );
                                                        }

                                                        if (value) {
                                                            const startTime = +new Date(effTime);
                                                            const sendEndTime = +new Date(value);

                                                            if (sendEndTime <= startTime) {
                                                                return Promise.reject(
                                                                    '失效时间不能早于生效时间',
                                                                );
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <DatePicker
                                                disabledDate={disabledDate}
                                                disabled={expFlag != '02' || disabled}
                                                showTime={{
                                                    format: 'HH:mm:ss',
                                                    defaultValue: moment('23:59:59', 'HH:mm:ss'),
                                                }}
                                                format="YYYY-MM-DD HH:mm:ss"
                                                showNow={false}
                                                renderExtraFooter={(a) => (
                                                    <Space>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const time = moment().add(
                                                                    10,
                                                                    'seconds',
                                                                );
                                                                form.setFieldsValue({
                                                                    expTime: time,
                                                                });
                                                            }}
                                                        >
                                                            10秒之后
                                                        </Button>
                                                        <Button
                                                            type="link"
                                                            onClick={() => {
                                                                const effTime =
                                                                    form.getFieldValue('effTime');
                                                                if (effTime) {
                                                                    form.setFieldsValue({
                                                                        effTime:
                                                                            moment(effTime).startOf(
                                                                                'day',
                                                                            ),
                                                                    });
                                                                }
                                                            }}
                                                        >
                                                            时间重置
                                                        </Button>
                                                    </Space>
                                                )}
                                            />
                                        </FormItem>
                                    );
                                }}
                            </FormItem>
                        </Space>
                    </Radio>
                </Radio.Group>
            </FormItem>

            <FormItem
                label="结算方式"
                name="profitRuleTypeList"
                wrapperCol={{ span: 16 }}
                required
                rules={[
                    () => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请选择购电结算方式`);
                            }
                            for (const item of value) {
                                const ruleErr = checkRuleInfo(item);
                                if (ruleErr) {
                                    return Promise.reject(ruleErr);
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <PurchaseRuleFormItem disabled={disabled}></PurchaseRuleFormItem>
            </FormItem>
            <FormItem
                label={
                    <span>
                        通道费收取方式
                        <SlottingTooltip></SlottingTooltip>
                    </span>
                }
                name="profitSlottingRules"
                wrapperCol={{ span: 16 }}
                required
                rules={[
                    () => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请配置通道费收取方式`);
                            }
                            const errText = checkRuleValue(value);
                            if (errText) {
                                return Promise.reject(errText);
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
                initialValue={defaultRuleValue}
            >
                <SlottingRuleFormItem disabled={disabled}></SlottingRuleFormItem>
            </FormItem>
        </Fragment>
    );
};
/**
 * 场站规则列表
 */
export const StationPurchaseListView = (props) => {
    const {
        dispatch,
        history,
        match,
        route,
        purchaseModel: { stationRuleList, stationRuleListTotal, editRuleInfo },
        listLoading,
        getInfoLoading,

        operId,
        workorderEvent, //工单提交事件
    } = props;

    const [form] = Form.useForm();
    const [showEditView, toggleEditView] = useState(false); // 编辑弹窗状态
    const templateRef = useRef();
    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [editItems, changeEditItems] = useState([]); // 多个编辑项
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: 50,
        tabType: STATUS_TYPES.ALL,
    });

    const { cooperationPlatform } = useCooperationPlatform();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue(true);
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            operId,
            stationName: data.stationName,
            templateIds: data.templateIds ? data.templateIds.join(',') : '',
            city: data.city?.join(',') || data.city,
            stationIdList: JSON.stringify(data.stationIdList) || '',
            effTimeBegin:
                (data.effTime && data.effTime[0] && data.effTime[0].format('YYYY-MM-DD')) || '',
            effTimeEnd:
                (data.effTime && data.effTime[1] && data.effTime[1].format('YYYY-MM-DD')) || '',
            expTimeBegin:
                (data.expTime && data.expTime[0] && data.expTime[0].format('YYYY-MM-DD')) || '',
            expTimeEnd:
                (data.expTime && data.expTime[1] && data.expTime[1].format('YYYY-MM-DD')) || '',
            stationBusiStatus: data.stationBusiStatus,
            cooperationType: '02',
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.profitRuleType = pageInfo.tabType;
        }
        changeSelectItems([]);
        dispatch({
            type: 'purchaseModel/getStationRuleList',
            options: params,
        });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operId,
            stationName: data.stationName,
            templateIds: data.templateIds ? data.templateIds.join(',') : '',
            city: data.city?.join(',') || data.city,
            stationIds: data.stationIdList?.join?.(',') || data.stationIdList || '',
            effTimeBegin:
                (data.effTime && data.effTime[0] && data.effTime[0].format('YYYY-MM-DD')) || '',
            effTimeEnd:
                (data.effTime && data.effTime[1] && data.effTime[1].format('YYYY-MM-DD')) || '',
            expTimeBegin:
                (data.expTime && data.expTime[0] && data.expTime[0].format('YYYY-MM-DD')) || '',
            expTimeEnd:
                (data.expTime && data.expTime[1] && data.expTime[1].format('YYYY-MM-DD')) || '',
            stationBusiStatus: data.stationBusiStatus,
            cooperationType: '02',
            cooperationPlatform,
        };
        if (pageInfo.tabType != STATUS_TYPES.ALL) {
            params.profitRuleType = pageInfo.tabType;
        }
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: getStationRuleListPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 批量重置
    const resetStationRuleByIds = async (items) => {
        try {
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId,
                stationName: data?.stationName || '',
                city: (data.city && JSON.stringify(data.city)) || '',
                cooperationType: '02',
                stationIdList: JSON.stringify(data.stationIdList) || '',
                cooperationPlatform,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.profitRuleType = pageInfo.tabType;
            }
            const stationIds = [];
            for (const item of items) {
                stationIds.push(item.stationId);
            }
            if (workorderEvent) {
                params.selectItems = items;
                workorderEvent('reset', params);
                return;
            }
            await dispatch({
                type: 'purchaseModel/resetStationRuleByIds',
                stationIds: stationIds.join(','),
                options: params,
            });
            message.success('重置成功');
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 关闭规则配置页面
    const closeEditViewEvent = () => {
        toggleEditView(false);
        changeEditItems([]);
        dispatch({
            type: 'purchaseModel/updatePurchaseInfo',
            editRuleInfo: {},
        });
        dispatch({
            type: 'purchaseModel/updateSelectTemplateInfo',
            info: null,
        });
        searchData();
    };

    // 城市规则相关
    const editRef = useRef();

    const columns = [
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div
                        className="text-line"
                        style={{
                            width: '200px',
                            whiteSpace: 'normal',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '所属城市',
            width: 120,
            dataIndex: 'cityName',
            render(text, record) {
                return (
                    <span
                        title={text}
                        style={{ width: '120px', color: record.dayexpflag == '1' ? 'red' : '' }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '结算规则类型',
            width: 140,
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return (
                    <span
                        title={text}
                        style={{ width: '140px', color: record.dayexpflag == '1' ? 'red' : '' }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '当前购电结算方式',
            width: 460,
            dataIndex: 'ruleName',

            render(text, record) {
                return (
                    <div
                        style={{
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-all',
                            width: '460px',
                            color: record.dayexpflag == '1' ? 'red' : '',
                        }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '溢价分成规则',
            width: 160,
            dataIndex: 'premiumString',
        },
        {
            title: '当前规则生效时间',
            width: 200,
            dataIndex: 'effTime',
            render(text, record) {
                return (
                    <span
                        title={text}
                        style={{ width: '160px', color: record.dayexpflag == '1' ? 'red' : '' }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '当前规则失效时间',
            width: 200,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <span
                        title={text}
                        style={{ width: '160px', color: record.dayexpflag == '1' ? 'red' : '' }}
                    >
                        {text || '长期'}
                    </span>
                );
            },
        },
        {
            title: '所属模板',
            width: 160,
            dataIndex: 'templateName',
            render(text, record) {
                if (record.templateName) {
                    return (
                        <span
                            className={styles['table-btn']}
                            title={text}
                            onClick={() => {
                                templateRef.current?.show(record.templateId);
                            }}
                        >
                            {text}
                        </span>
                    );
                }
                return <span>-</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 120,
            render: (text, record) => (
                <Fragment>
                    {!record.children || record.children.length == 0 ? (
                        <span className={styles['table-btn']} onClick={() => editItemEvent(record)}>
                            编辑
                        </span>
                    ) : null}
                    {record.profitRuleType === RULE_TYPES.STATION ? (
                        <Popconfirm
                            title="确认重置？"
                            okText="确认"
                            cancelText="取消"
                            onConfirm={() => resetStationRuleByIds([record])}
                        >
                            <a href="#">重置</a>
                        </Popconfirm>
                    ) : null}
                </Fragment>
            ),
        },
    ];

    const rowSelection = {
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        selectedRowKeys: selectItems?.map((ele) => ele.stationId),
        getCheckboxProps: (record) => ({
            name: record.stationId,
        }),
    };

    // 批量重置
    const batchRestEvent = () => {
        let filterList = selectItems.filter((ele) => ele.profitRuleType === RULE_TYPES.STATION);

        if (selectItems.length == 0) {
            message.error('请选择要重置的规则');
            return;
        }
        if (filterList?.length != selectItems?.length) {
            message.error('请不要勾选未配置的场站');
            return;
        }
        confirm({
            title: '确认重置所选场站规则？',
            icon: <ExclamationCircleOutlined />,
            content: (
                <Space direction="vertical">
                    <span>重置后场站规则将还原为通用规则</span>
                    <span style={{ fontSize: 'small', color: 'gray' }}>
                        （若所选场站有城市规则，重置后将还原为城市规则）
                    </span>
                </Space>
            ),
            onOk() {
                resetStationRuleByIds(filterList);
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const resetEvent = (items) => {
        confirm({
            title: '确认重置场站规则？',
            icon: <ExclamationCircleOutlined />,
            content: (
                <Space direction="vertical">
                    <span>重置后场站规则将还原为通用规则</span>
                    <span style={{ fontSize: 'small', color: 'gray' }}>
                        （若所选场站有城市规则，重置后将还原为城市规则）
                    </span>
                </Space>
            ),
            async onOk() {
                try {
                    await resetStationRuleByIds(items);
                    closeEditViewEvent();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    // 批量编辑
    const batchEditEvent = () => {
        if (selectItems.length == 0) {
            message.error('请选择要编辑的规则');
            return;
        } else if (selectItems.length == 1) {
            editItemEvent(selectItems[0]);
            return;
        }
        toggleEditView(true);
        changeEditItems(selectItems);
    };

    // 编辑单个
    const editItemEvent = (item) => {
        const params = {
            ruleId: item.ruleId,
            operId,
            city: item.city,
            stationId: item.stationId,
            profitRuleType: item.profitRuleType,
            cooperationType: '02',
            cooperationPlatform,
        };
        if (item.profitRuleType === RULE_TYPES.STATION) {
            params.stationId = item.stationId;
        }
        dispatch({
            type: 'purchaseModel/getRuleInfo',
            params,
        });

        changeEditItems([item]);

        toggleEditView(true);
    };

    const onEditFinish = async (params) => {
        if (submitLoading) {
            return;
        }
        try {
            changeSubmitLoading(true);
            if (params.templateId) {
                // 场站配置模板
                const ids = editItems.map((ele) => ele.stationId);
                params.stationIds = ids.join(',');
                params.operId = operId;
                params.cooperationPlatform = cooperationPlatform;
                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    closeEditViewEvent();

                    return;
                }
                await addStationTemplateApi(params);
            } else {
                // 场站配置规则
                params.operId = operId;
                params.ruleId = editItems[0].ruleId;
                params.cooperationType = '02'; // 购电模式
                params.cooperationPlatform = cooperationPlatform;

                const ids = editItems.map((ele) => ele.stationId);
                params.stationIds = ids.join(',');

                if (workorderEvent) {
                    params.selectItems = editItems;
                    workorderEvent('update', params);
                    closeEditViewEvent();

                    return;
                }
                await saveprofitruleApi(params);
            }

            message.success('保存成功');
            closeEditViewEvent();
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    return (
        <Fragment>
            <Card>
                <Tabs defaultActiveKey={pageInfo?.tabType || '1'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="场站" key={STATUS_TYPES.STATION} />
                    <TabPane tab="城市" key={STATUS_TYPES.CITY} />
                    <TabPane tab="通用" key={STATUS_TYPES.CURRENCY} />
                </Tabs>
                <SearchLayout
                    form={form}
                    operId={operId}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button className={styles['btn-item']} onClick={batchEditEvent}>
                        批量编辑
                    </Button>
                    <Button className={styles['btn-item']} onClick={batchRestEvent}>
                        批量重置
                    </Button>

                    <Button className={styles['btn-item']}>
                        <Link
                            to={`/sellerCenter/operatormanage/purchase/list/templateManage`}
                            target="_blank"
                        >
                            模板管理
                        </Link>
                    </Button>
                </div>
                <p style={{ margin: '10px 0' }}>{`已选${selectItems?.length || 0}条`}</p>
                <TablePro
                    name="list"
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={stationRuleList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationRuleListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [10, 50, 100, 500],
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title="规则配置"
                width={1100}
                visible={showEditView}
                onCancel={closeEditViewEvent}
                footer={null}
                destroyOnClose
                maskClosable={false}
            >
                <EditModelLayout
                    {...props}
                    cooperationPlatform={cooperationPlatform}
                    editItems={editItems}
                    multiple={editItems.length > 1}
                    loading={getInfoLoading}
                    submitLoading={submitLoading}
                    onFinish={onEditFinish}
                    editRuleInfo={editRuleInfo}
                    onCancel={closeEditViewEvent}
                    resetEvent={resetEvent}
                    confirmCityEvent={async (editRuleInfo) => {
                        try {
                            const data = form.getFieldsValue();
                            const params = {
                                pageIndex: pageInfo.pageIndex,
                                pageSize: pageInfo.pageSize,
                                operId,
                                stationName: data.stationName,
                                city: JSON.stringify(data.city),
                                stationIdList: '',
                                cooperationType: '02',
                                cooperationPlatform,
                            };
                            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                                params.profitRuleType = pageInfo.tabType;
                            }

                            if (workorderEvent) {
                                params.selectItems = [editRuleInfo];
                                workorderEvent('reset', params);
                                closeEditViewEvent();
                                return;
                            }

                            await dispatch({
                                type: 'profitRuleModel/resetStationRuleByIds',
                                stationIds: editRuleInfo.stationId,
                                options: params,
                            });
                            closeEditViewEvent();
                            message.success('配置成功');
                        } catch (error) {}
                    }}
                    addCityRuleEvent={() => {
                        editRef?.current?.add({ operId, type: '03' });
                        closeEditViewEvent();
                    }}
                />
            </Modal>

            <TemplateListModal initRef={templateRef} />
            <EditRulesModal
                path="purchase"
                initRef={editRef}
                onEditFinish={searchData}
                cooperationPlatform={cooperationPlatform}
            />
        </Fragment>
    );
};

const StationPurchaseListPage = (props) => {
    const { history, route, match } = props;
    const { operId } = match.params;
    const goBack = () => {
        history.go(-1);
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <StationPurchaseListView {...props} closeEvent={goBack} operId={operId} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, purchaseModel, loading }) => ({
    global,
    purchaseModel,
    listLoading: loading.effects['purchaseModel/getStationRuleList'],
    getInfoLoading: loading.effects['purchaseModel/getRuleInfo'],
}))(StationPurchaseListPage);
