import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Modal } from 'antd';
import { useState, useImperativeHandle } from 'react';
import { connect } from 'umi';

import TablePro from '@/components/TablePro';
import { getRuleTemplateDetilsApi } from '@/services/FinanceManage/ProfitRuleApi';
import TemplateListCard from './components/TemplateListCard';

export const childrenColumns = [
    {
        title: '序号 ',
        width: 80,
        render(text, record, index) {
            return <span title={index}>{index + 1}</span>;
        },
    },
    {
        title: '购电结算方式',
        width: 300,
        dataIndex: 'ruleName',
        render(text, record) {
            return (
                <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }} title={text}>
                    {text}
                </span>
            );
        },
    },

    {
        title: '规则生效时间',
        width: 140,
        dataIndex: 'effTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '规则失效时间',
        width: 140,
        dataIndex: 'expTime',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
];

export const TemplateListModal = (props) => {
    const { initRef } = props;

    const [showTeamplateView, toggleTeamplateView] = useState(false); // 模板弹窗状态
    const [loading, updateLoading] = useState(false);
    const [detailList, updateDetailList] = useState([]);

    useImperativeHandle(initRef, () => ({
        show: async (id) => {
            // 所属模板弹出的关联项
            toggleTeamplateView(true);
            try {
                updateLoading(true);
                const {
                    data: { detailList: list = [] },
                } = await getRuleTemplateDetilsApi(id);
                updateDetailList([...list]);
            } catch (error) {
            } finally {
                updateLoading(false);
            }
        },
    }));

    // 关闭规则配置页面
    const closeTemplateViewEvent = () => {
        toggleTeamplateView(false);
        updateDetailList([]);
    };

    return (
        <Modal
            title="所属模板"
            width={740}
            visible={showTeamplateView}
            onCancel={closeTemplateViewEvent}
            footer={null}
            maskClosable={false}
        >
            <TablePro
                name="template"
                loading={loading}
                scroll={{ x: 'max-content' }}
                dataSource={detailList}
                columns={childrenColumns}
                pagination={false}
            />
        </Modal>
    );
};

const PurchaseTemplateListPage = (props) => {
    const { history, route } = props;

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <TemplateListCard />
        </PageHeaderWrapper>
    );
};

export default connect(({ loading }) => ({
    listLoading: loading.effects['purchaseModel/getRuleTemplateList'],
    infoLoading: loading.effects['purchaseModel/getTemplateInfo'],
}))(PurchaseTemplateListPage);
