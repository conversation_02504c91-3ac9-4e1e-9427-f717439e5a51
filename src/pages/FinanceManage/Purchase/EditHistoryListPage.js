import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import moment from 'moment';
import styles from './PurchaseListPage.less';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import CitysSelect from '@/components/CitysSelect/index.js';
import { getProvinceAndCityApi } from '@/services/CommonApi';
import CacheAreaView from '@/components/CacheAreaView';
import SearchOptionsBar from '@/components/SearchOptionsBar';

import useCooperationPlatform from '@/hooks/useCooperationPlatform';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const STATUS_TYPES = {
    ALL: '00',
    NOTCONF: '01',
    USE: '02',
};

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar
                onReset={resetForm}
                onExportForm={onExportForm}
                exportName="导出至暂存区"
            >
                <Col span={8}>
                    <FormItem
                        label="配置日期:"
                        name="dates"
                        {...formItemLayout}
                        initialValue={[
                            moment().startOf('day').subtract(6, 'days'),
                            moment().endOf('day'),
                        ]}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value) {
                                        return Promise.reject('请选择日期');
                                    }
                                    if (!value[0]) {
                                        return Promise.reject('请选择开始日期');
                                    }
                                    if (!value[1]) {
                                        return Promise.reject('请选择结束日期');
                                    }
                                    if (value[0] && value[1]) {
                                        const startTime = +new Date(value[0]);
                                        const endTime = +new Date(value[1]);
                                        const dest = 60 * 1000 * 60 * 24 * 60;

                                        if (Math.abs(startTime - endTime) > dest) {
                                            return Promise.reject('选取范围最大不超过60天');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <RangePicker
                            // showTime={{ format: 'HH:mm',defaultValue:[moment('00:00', 'HH:mm'), moment('11:59', 'HH:mm')] }}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem
                        {...formItemLayout}
                        form={form}
                        rules={[{ required: true, message: '请选择运营商' }]}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="场站名称:" name="stationName" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col>
                {/* <Col span={8}>
                    <FormItem label="场站编号:" name="stationNo" {...formItemLayout}>
                        <Input placeholder="请填写" />
                    </FormItem>
                </Col> */}
                <Col span={8}>
                    <FormItem label="规则类型:" name="profitRuleType" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="01">通用</Option>
                            <Option value="03">城市</Option>
                            <Option value="02">场站</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <CitysSelect
                        label="城&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;市:"
                        name="city"
                        placeholder="请选择"
                        rules={[]}
                    />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const EditHistoryList = (props) => {
    const {
        dispatch,
        history,
        purchaseModel: { editHistoryList, editHistoryListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();
    const cacheRef = useRef();

    const { cooperationPlatform } = useCooperationPlatform();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        pageSize: 50,
    });

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                pageIndex: (isDownload == false && pageInfo.pageIndex) || undefined,
                pageSize: (isDownload == false && pageInfo.pageSize) || undefined,
                operId: data.operId || '',
                stationName: data.stationName || '',
                stationNo: data.stationNo || '',
                profitRuleType: data.profitRuleType || '',
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                city: JSON.stringify(data.city),
                cooperationType: '02', // 购电模式
                cooperationPlatform,
            };
            if (isDownload) {
                // 下载
                cacheRef.current?.apply(params).then(() => cacheRef.current.count());
            } else {
                dispatch({
                    type: 'purchaseModel/getEditHistoryList',
                    options: params,
                });
            }
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '配置时间',
            width: 140,
            dataIndex: 'creatTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作人',
            width: 140,
            dataIndex: 'operator',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '规则类型',
            width: 140,
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站名称',
            width: 140,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '平台',
            width: 140,
            dataIndex: 'cooperationPlatformName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '购电结算方式',
            width: 300,
            dataIndex: 'ruleModel',
            render(text, record) {
                return (
                    <span style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }} title={text}>
                        {text}
                    </span>
                );
            },
        },

        {
            title: '生效时间',
            width: 140,
            dataIndex: 'effTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '失效时间',
            width: 140,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <PageHeaderWrapper
            extra={<CacheAreaView bizType={'buypowerRuleHistoryExport'} initRef={cacheRef} />}
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />
                <br></br>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={editHistoryList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: editHistoryListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        pageSizeOptions: [50, 100],
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ purchaseModel, global, loading }) => ({
    purchaseModel,
    global,
    listLoading: loading.effects['purchaseModel/getEditHistoryList'],
}))(EditHistoryList);
