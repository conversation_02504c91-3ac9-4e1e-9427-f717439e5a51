export const RULE_MODEL_TYPES = {
    SERVICE: '01', //按服务费
    ADD: '03', //按附加费
    ORDER: '02', //订单总金额
    ELECTRIC: '07', //按电量
    ELEORDER: '04', //按电费
};

export const CollectTypes = {
    DETAIL: '01',
    ORDER: '02',
};

export const PURCHASE_RULE_TYPES = {
    MONEY: '01', //金额
    DISCOUNT: '02', //折扣
};

export const getRuleModelName = (ruleType) => {
    let name = '';
    switch (ruleType) {
        case RULE_MODEL_TYPES.SERVICE:
            name = '服务费';
            break;
        case RULE_MODEL_TYPES.ELECTRIC:
            name = '电量';
            break;
        case RULE_MODEL_TYPES.ELEORDER:
            name = '电费';
            break;
        case RULE_MODEL_TYPES.ADD:
            name = '附加费';
            break;
        case RULE_MODEL_TYPES.ORDER:
            name = '订单总金额';
            break;

        default:
            break;
    }
    return name;
};

export const MAX_PRECISION = 3;

export const computedLongShortValue = (element, options) => {
    let maxValue = Number(element.ruleValue);

    if (element.ruleType === PURCHASE_RULE_TYPES.DISCOUNT) {
        maxValue =
            Math.round((10 - (Number(element.ruleValue) || 0)) * 10 * 10 ** MAX_PRECISION) /
            10 ** MAX_PRECISION;
    }
    const curLongRuleValue = (element.curLongRuleValue && Number(element.curLongRuleValue)) || 0;
    const curShortRuleValue = (element.curShortRuleValue && Number(element.curShortRuleValue)) || 0;
    const surplusValue =
        Math.round((maxValue - curLongRuleValue - curShortRuleValue) * 10 ** MAX_PRECISION) /
        10 ** MAX_PRECISION;
    if (element.agreeType.includes('01')) {
        return {
            longRuleValue: surplusValue,
            shortRuleValue: undefined,
        };
    }
    if (element.agreeType.includes('02')) {
        return {
            shortRuleValue: surplusValue,
            longRuleValue: undefined,
        };
    }
    return;
};
