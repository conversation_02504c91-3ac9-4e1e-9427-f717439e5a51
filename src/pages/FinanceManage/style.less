.btnMargin {
    margin-left: 10px;
}
.step {
    .stepCircle {
      float: left;
        width: 35px;
        height: 35px;
        background:white;
        border: 1px solid rgba(0,0,0,0.3);
        border-radius: 30px;
        transition: all .4s;
        span {
          padding-left: 12px;
            color: rgba(0,0,0,0.3);
            font-size: 19px;
            line-height: 32px;
        }
    }
    .stepWord {
      margin-left: 10px;
      color: rgba(0,0,0,0.3);
      font-weight: bold;
        font-size: 16px;
        line-height: 35px;
        transition: all .4s;
    }
}

.step:hover {
    cursor: pointer;
    .stepCircle {
        background: #1890ff;
        border: 1px solid #1890ff;
        span {
            color: white;
        }
    }
    .stepWord {
        color: black;
    }
}
.stepActive {
    .stepCircle {
      float: left;
        width: 35px;
        height: 35px;
        background:#1890ff;
        border: 1px solid #1890ff;
        border-radius: 30px;
        transition: all .4s;
        span {
          padding-left: 12px;
          color: white;
          font-size: 19px;
            line-height: 32px;
            
            
        }
    }
    .stepWord {
      margin-left: 10px;
      color: black;
      font-weight: bold;
        font-size: 20px;
        line-height: 35px;
        transition: all .4s;
    }
}

.verticalLine {
  height: 80px;
  margin-top: 4px;
    margin-left: 17px;
    border-left: 1px solid rgba(0,0,0,0.2);
}
.formTitle {
  padding-left: 5px;
  font-weight: bold;
    border-left: 5px solid #1890ff;
    
}

.descriptionsItemWrapper {
    :global(.ant-descriptions-view) {
        margin-left: 25px;
    }
}
.tableTitle {
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
    font-size: 16px;
    line-height: 1.5;
}