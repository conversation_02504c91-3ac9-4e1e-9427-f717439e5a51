@import '~antd/lib/style/themes/default.less';

.main {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-family: 'Microsoft Yahei', sans-serif;
    background: url('./images/bg.jpg') no-repeat center;
    background-size: cover;
    //@media screen and (max-width: @screen-sm) {
    //  width: 95%;
    //}

    :global {
        .ant-form-item-control {
            height: 63px;
        }
        .ant-form-item-control.has-error {
            input {
                color: #fff;
                background-color: rgba(255, 255, 255, 0.3);
                border: none;
                outline: none;
            }
            input:focus,
            input::-internal-autofill-selected {
                color: #fff !important;
                background-color: rgba(255, 255, 255, 0.3) !important;
                border: none !important;
                outline: none !important;
            }
            input::placeholder {
                color: #fff;
            }
        }
        .ant-form-item {
            margin-bottom: 0;
        }
        .ant-form-item-has-error .ant-input,
        .ant-form-item-has-error .ant-input-affix-wrapper,
        .ant-form-item-has-error .ant-input:hover,
        .ant-form-item-has-error .ant-input-affix-wrapper:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
    }

    .icon {
        margin-left: 16px;
        color: rgba(0, 0, 0, 0.2);
        font-size: 24px;
        vertical-align: middle;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
            color: @primary-color;
        }
    }

    .other {
        z-index: 99;
        margin-top: -15px;
        color: #999;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        a {
            color: #fff;
            text-decoration: underline;
        }

        /* .register {
      float: right;
    } */
    }
}

.loginDiv {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30%;
    max-width: 400px;
    padding-top: 100px;
    text-align: center;
    transform: translate(20%, -50%);
    input {
        width: 100%;
        padding: 0 40px !important;
        font-size: 14px;
        text-align: left;
    }
    .title {
        position: absolute;
        top: 0;
        left: 50%;
        color: #fff;
        white-space: nowrap;
        text-align: center;
        transform: translateX(-50%);
        h3 {
            color: #fff;
            font-weight: bold;
            font-size: 40px;
            line-height: 40px;
        }
        p {
            font-size: 14px;
            font-family: 'Microsoft Yahei', sans-serif;
        }
    }
}

.desc {
    position: relative;
    margin-bottom: 15px;
    margin-left: 10px;
    color: #1890ff;
    font-weight: 500;
    font-size: 18px;
    font-family: fantasy;
    text-indent: 20px;
    &::before {
        position: absolute;
        top: 5px;
        left: 0;
        display: block;
        width: 3px;
        height: 16px;
        background: rgba(24, 144, 255, 1);
        content: '';
    }
}

.radius-btn {
    overflow: hidden;
    background-color: rgba(240, 128, 37, 1);
    border: none;
    border-radius: 30px;
    &:hover {
        background-color: rgba(240, 128, 37, 0.8);
    }
    &:focus {
        background-color: rgba(240, 128, 37, 0.8);
    }
}
.radius-input {
    align-items: center;
    height: 40px;
    padding: 0;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.3);
    border: none;
    border-radius: 30px;
    outline: none;
    :global {
        .ant-form-item-has-error,
        .ant-input-affix-wrapper {
            background-color: transparent !important;
        }
        .ant-input-prefix {
            margin: 0 10px;
        }
    }

    input {
        height: 100%;
        color: #fff;
        background-color: rgba(255, 255, 255, 0.3);
        // background-color: transparent!important;
        border: none;
        outline: none;
    }
    input::placeholder {
        color: #fff;
    }
}

.ant-input:not(:first-child) {
    padding-left: 89px;
}
