import { useState, useEffect } from 'react';
import { connect, FormattedMessage, useModel } from 'umi';

import { Alert, Form } from 'antd';
import Login from '@/components/Login/index';
import styles from './LoginPage.less';
import { getPageQuery } from '@/utils/utils';
import securityMsg from '@/utils/securityMsg';

import { tranCurrentEnvironment } from '@/utils/commonUtils';

import usericon from './images/user-icon.png';
import passwordicon from './images/password-icon.png';

const { UserName, Password, Captcha, Submit } = Login;

const LoginPage = (props) => {
    const {
        dispatch,
        user: { currentEnvironment, currentPlatName },
        loading,
        login,
        submitting,
    } = props;
    const [form] = Form.useForm();
    const { isCheckVerifyCode } = login;

    const [type, changeType] = useState('account');
    const [autoLogin, changeAutoLoginStatus] = useState(true);
    const [errorMsg, changeErrorMsg] = useState(false);

    const { initialState, setInitialState } = useModel('@@initialState');

    useEffect(() => {
        setInitialState((s) => ({ ...s, currentUser: undefined }));
        dispatch({
            type: 'login/getLoginInfo',
            payload: {},
        });
        const urlParams = new URL(window.location.href);
        const params = getPageQuery();
        const value = `ENM_security_${params.error}`;
        if (securityMsg[value]) {
            changeErrorMsg[securityMsg[value]];
        }
    }, []);

    const onTabChange = (tabType) => {
        changeType(tabType);
    };

    const onGetCaptcha = () =>
        new Promise((resolve, reject) => {
            form.validateFields(['mobile'], {}, (err, values) => {
                if (err) {
                    reject(err);
                } else {
                    dispatch({
                        type: 'login/getCaptcha',
                        payload: values.mobile,
                    })
                        .then(resolve)
                        .catch(reject);
                }
            });
        });

    const handleSubmit = (values) => {
        dispatch({
            type: 'login/login',
            payload: {
                ...values,
                type,
            },
        });
    };

    const changeAutoLogin = (e) => {
        changeAutoLoginStatus(e.target.checked);
    };

    const renderMessage = (content) => (
        <Alert style={{ marginBottom: 24 }} message={content} type="error" showIcon />
    );

    const environmentCode = tranCurrentEnvironment(currentEnvironment);

    return (
        <div className={styles.main}>
            <Login
                defaultActiveKey={type}
                onTabChange={onTabChange}
                onSubmit={handleSubmit}
                form={form}
                {...props}
            >
                <div className={styles.loginDiv}>
                    <div className={styles.title}>
                        <h3>{currentPlatName}</h3>
                        {/* <p>New Energy Vehicle Testing and Regulation of charging facilities</p> */}
                    </div>
                    {errorMsg && renderMessage(errorMsg)}
                    <UserName
                        name="j_username"
                        prefix={<img style={{ width: '25px', height: '25px' }} src={usericon} />}
                        placeholder="请输入账号"
                        className={styles['radius-input']}
                    />
                    <Password
                        className={styles['radius-input']}
                        prefix={
                            <img style={{ width: '25px', height: '25px' }} src={passwordicon} />
                        }
                        name="j_password"
                        id="i_password"
                        placeholder="请输入密码"
                        onPressEnter={() => form.validateFields(handleSubmit)}
                    />
                    {/* {isCheckVerifyCode === '1' && (
                        <Captcha
                            name="j_verifycode"
                            countDown={120}
                            onGetCaptcha={onGetCaptcha}
                            getCaptchaButtonText={formatMessage({ id: 'form.captcha' })}
                            getCaptchaSecondText={formatMessage({ id: 'form.captcha.second' })}
                        />
                    )} */}
                    <Submit className={styles['radius-btn']} loading={submitting}>
                        登录
                    </Submit>
                </div>
                <div />
            </Login>
        </div>
    );
};

export default connect(({ login, loading }) => ({
    login,
    submitting: loading.effects['login/login'],
}))(LoginPage);
