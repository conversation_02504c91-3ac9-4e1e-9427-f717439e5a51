import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Tabs } from 'antd';
import React, { useState, useEffect } from 'react';
import { connect, useModel } from 'umi';

import JoinXdtPage from './JoinXdtListPage';
import ServMktPage from './ServMktListPage';
import PileMarketTable from './components/PileMarketTable';

const { TabPane } = Tabs;

const JoinUsManagePage = (props: any) => {
    const [tabType, updateTabType] = useState('1');
    const [isHasServMktPerm, updateHasServMktPerm] = useState<boolean>(false);
    const [hasPileMarketPermission, updateHasPileMarketPermission] = useState<boolean>(false);
    const { initialState } = useModel('@@initialState');
    const { permissionButtonList } = initialState || {};

    const changeTabType = (value: string) => {
        updateTabType(value);
    };

    useEffect(() => {
        if (permissionButtonList?.find((v: any) => v.code === 'cy:oper-apply:servMktTab')) {
            updateHasServMktPerm(true);
        }
        if (permissionButtonList?.find((v: any) => v.code === 'ner:service-market:pile-apply')) {
            updateHasPileMarketPermission(true);
        }
    }, [permissionButtonList]);

    return (
        <PageHeaderWrapper {...props}>
            <Card>
                <Tabs onChange={changeTabType} activeKey={tabType}>
                    <TabPane tab={`加入我们`} key={`1`}>
                        <JoinXdtPage {...props} />
                    </TabPane>
                    {(isHasServMktPerm && (
                        <TabPane tab={`服务市场`} key={`2`}>
                            <ServMktPage {...props} />
                        </TabPane>
                    )) ||
                        null}
                    {hasPileMarketPermission && (
                        <TabPane tab={`电桩商城`} key={`3`}>
                            <PileMarketTable />
                        </TabPane>
                    )}
                </Tabs>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ user }: any) => ({
    global,
    currentUser: user.currentUser,
}))(JoinUsManagePage);
