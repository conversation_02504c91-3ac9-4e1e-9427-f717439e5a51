import {
    Button,
    Card,
    Checkbox,
    DatePicker,
    Form,
    Input,
    Modal,
    Radio,
    Select,
    Space,
    Table,
    message,
} from 'antd';
import moment from 'moment';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { connect, history } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import ConflictModal from '@/pages/MarketingManage/BusinessActive/ModuleActive/components/ConflictModal';
import ActTimeAndDiscountFormItem from '@/pages/MarketingManage/BusinessActive/ActTimeAndDiscountFormItem';
import { SELECT_TYPES, ADJUST_PRICE_TYPES } from '@/config/declare';
import { ACT_TYPES } from '@/constants/channel';
import { ADJUST_TYPS } from '@/pages/MarketingManage/BusinessActive/BusinessConfig';
import { isEmpty } from '@/utils/utils';
import { getStationScopeListApi, saveStationScopeInfoApi } from '@/services/CommonApi';
import { saveBusinessActiveApi } from '@/services/SallerCenter/MarketingApi';
import DiscountFormItem from '@/pages/MarketingManage/BusinessActive/DiscountFormItem';
import SelectAndImportOperItem from '@/components/OperSelectItem/SelectAndImportOperItem';
import OperSearchList from '../../ChannelManage/OperSearchList/SearchList';

const { confirm } = Modal;

// 日期多选项
const dataOptions = [
    {
        label: '周一',
        value: '1',
    },
    {
        label: '周二',
        value: '2',
    },
    {
        label: '周三',
        value: '3',
    },
    {
        label: '周四',
        value: '4',
    },
    {
        label: '周五',
        value: '5',
    },
    {
        label: '周六',
        value: '6',
    },
    {
        label: '周日',
        value: '7',
    },
];

const MarketingForm: React.FC<any> = (props) => {
    const {
        currentUser,
        dispatch,
        editActInfo,
        sallerModel: { commonChannelList },
        formType,
    } = props;
    //新增接口返回的id
    const [actId, changeActId] = useState('');
    const [submitLoading, changeSubmitLoading] = useState(false);
    const conflictRef = useRef<any>();
    const budgetRef = useRef<any>();
    const [conflictParams, updateConflictParams] = useState<any>();
    const [form] = Form.useForm();

    useEffect(() => {
        dispatch({
            type: 'sallerModel/initIcnChannel',
        });
    }, []);

    const goBack = () => {
        history.replace('/sellerCenter/channel/marketing');
    };

    const checkOperConfirm = async (content = '') => {
        return new Promise((resolve, reject) => {
            confirm({
                title: `存在活动场站属于活动运营商，提交后将删除对应活动运营商，以活动场站为准，是否继续？`,
                content: content,
                okText: '确定',
                cancelText: '取消',
                onOk() {
                    resolve(true);
                },
                onCancel() {
                    reject();
                },
            });
        });
    };

    /**
     * 保存优惠券
     * type  save/send
     */

    const submitEvent = async (type: string, values: any, blank: boolean) => {
        console.error('submitEvent==>', { type, values, blank });
        try {
            const params: any = {
                actChannel: values.actChannel.join(','),
                actName: values.actName,
                actNo: values?.actNo,
                actState: values?.actState,
                actType: values?.actType,
                allDayFlag: values?.allDayFlag,
                effTime:
                    (values?.dateTime && values.dateTime[0].format('YYYY-MM-DD HH:mm:ss')) || '',
                expTime:
                    (values?.dateTime && values.dateTime[1].format('YYYY-MM-DD HH:mm:ss')) || '',
                dctValue: values.dctValue,
                areaRangeType: values.areaRangeType,
            };
            if (values.cycleType != '1') {
                params.cycleType = values.cycleType;
                params.cycleValue = values.cycleValue.join(',');
            }

            if (values.allDayFlag === '0') {
                const actTimeIntervalList = values.actTimeIntervalList.map((ele: any) => {
                    return {
                        dctValue: ele.dctValue,
                        beginTime: (ele.actTime && ele.actTime[0].format('HH:mm')) || '',
                        endTime: (ele.actTime && ele.actTime[1].format('HH:mm')) || '',
                    };
                });
                params.periodsList = actTimeIntervalList;
            }

            if (values.areaRangeType == '2') {
                const stationOptions: any = {};
                params.operIds = values.operIds;
                const {
                    addStations = [],
                    allStations = [],
                    delStations = [],
                } = values.stationConfig || {};

                if (!isEmpty(allStations)) {
                    stationOptions.addStationIds = addStations.map((ele: any) => ele.stationId);
                    stationOptions.submitStationIds = allStations.map((ele: any) => ele.stationId);
                    stationOptions.delStationIds = delStations.map((ele: any) => ele.stationId);
                    const {
                        data: { stationScopeKey },
                    } = await saveStationScopeInfoApi(stationOptions);

                    params.stationScopeKey = stationScopeKey;

                    const addStationOpers = addStations.filter((ele: any) =>
                        params.operIds?.includes(ele.buildId),
                    );
                    if (addStationOpers.length > 0) {
                        await checkOperConfirm(
                            [...new Set(addStationOpers?.map((ele) => ele.buildName))]?.join(','),
                        );
                        params.operIds = values.operIds?.filter(
                            (ele) => !addStationOpers.find((ele2) => ele2.buildId == ele),
                        );
                    }
                }
            }

            if (actId || formType === 'EDIT') {
                params.actId = actId || editActInfo?.actId;
            }

            if (values.actState != 2) {
                if (type == 'save') {
                    params.actState = '0';
                } else if (type == 'send') {
                    params.actState = '1';
                }
            }

            changeSubmitLoading(true);

            const {
                data: { actId: newActId, conflictActList },
            } = (await saveBusinessActiveApi(params)) as any;

            if (conflictActList?.length) {
                updateConflictParams({ type, values: { ...values, overlapFlag: 1 }, blank });
                conflictRef.current?.show(conflictActList);
                return;
            } else {
                if (conflictParams) {
                    updateConflictParams(undefined);
                }
                conflictRef.current?.onClose();
            }

            if (newActId) {
                changeActId?.(newActId);
            }

            if (type == 'save') {
                message.success('保存成功');
                if (values?.budgetId) {
                    const options = {
                        budgetId: values?.budgetId || '',
                        effTime: values.dateTime[0],
                        expTime: values.dateTime[1],
                        actId: newActId,
                    };
                    budgetRef?.current?.update(options);
                }
            } else if (type == 'send') {
                message.success('提交成功');
            }
            goBack();
            changeSubmitLoading(false);
            return;
        } catch (error) {
            console.error('submit error==>', error);
            if (budgetRef?.current?.update && values?.budgetId) {
                const options = {
                    budgetId: values.budgetId,
                    effTime: values.dateTime[0],
                    expTime: values.dateTime[1],
                    actId: actId || '',
                };
                budgetRef?.current?.update(options);
            }
            return Promise.reject(error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const saveCouponEvent = async (type: string, blank: boolean) => {
        if (submitLoading) {
            return;
        }
        console.error('saveCouponEvent==>', type, blank);
        try {
            await form.validateFields();
            const values = form.getFieldsValue();
            const confirmList = []; //提交确认提醒框内容数组
            console.error('values==>', values);
            //校验分时段或者未分时时
            if (values.allDayFlag == 0) {
                // 分时段
                values.actTimeIntervalList?.forEach((ele: any) => {
                    const confimOptions: any = {
                        title: '',
                        children: [],
                    };
                    const discountOptions: any = {
                        discountValue: ele.dctValue,
                        vipDiscountValue: ele.vipDiscountValue,
                    };

                    if (values.actType == ACT_TYPES.DISCOUNT) {
                        // 打折低于3折
                        if (ele.dctValue < 3 || ele.vipDiscountValue < 3) {
                            if (ele.dctValue < 3) {
                                discountOptions.discountFlag = true;
                            }
                            discountOptions.vipDiscountFlag = false;
                            discountOptions.discountType = '打折';
                            discountOptions.unit = '折';
                            confimOptions.children.push(discountOptions);
                        }
                    }
                    if (values.actType == ACT_TYPES.REDUCTION) {
                        // 立减大于2毛
                        if (ele.dctValue > 0.2 || ele.vipDiscountValue > 0.2) {
                            if (ele.dctValue > 0.2) {
                                discountOptions.discountFlag = true;
                            }
                            discountOptions.vipDiscountFlag = false;
                            discountOptions.discountType = '立减';
                            discountOptions.unit = '元/度';
                            confimOptions.children.push(discountOptions);
                        }
                    }

                    if (confimOptions?.children?.length > 0) {
                        const [startTime, endTime] = ele.actTime;
                        confimOptions.title = `时段（${startTime.format('HH:mm')}-${endTime.format(
                            'HH:mm',
                        )})`;
                        confirmList.push(confimOptions);
                    }
                });
            } else {
                const confimOptions: any = {
                    title: '',
                    children: [],
                };

                const discountOptions: any = {
                    discountValue: values.dctValue,
                    vipDiscountValue: values.vipDiscountValue,
                };
                if (values.actType == ACT_TYPES.DISCOUNT) {
                    // 打折低于3折
                    if (values.dctValue < 3) {
                        discountOptions.discountFlag = true;
                    }
                    discountOptions.discountType = '打折';
                    discountOptions.unit = '折';
                    if (discountOptions.discountFlag) {
                        confimOptions.children.push(discountOptions);
                    }
                } else if (values.actType == ACT_TYPES.REDUCTION) {
                    // 立减大于2毛
                    if (values.dctValue > 0.2) {
                        discountOptions.discountFlag = true;
                    }
                    discountOptions.discountType = '立减';
                    discountOptions.unit = '元/度';
                    if (discountOptions.discountFlag || discountOptions.vipDiscountFlag) {
                        confimOptions.children.push(discountOptions);
                    }
                }

                if (confimOptions?.children?.length > 0) {
                    confirmList.push(confimOptions);
                }
            }

            if (confirmList?.length > 0) {
                Modal.confirm({
                    title: '请确认以下配置',
                    width: 800,
                    content: (
                        <div style={{ height: '500px', overflowY: 'auto' }}>
                            {confirmList.map((ele, index) => {
                                return (
                                    <Card bordered={false} key={index} title={ele.title}>
                                        <Table
                                            columns={columns}
                                            dataSource={ele.children}
                                            pagination={false}
                                        ></Table>
                                    </Card>
                                );
                            })}
                        </div>
                    ),
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        submitEvent(type, values, blank);
                    },
                });
            } else {
                submitEvent(type, values, blank);
            }
        } catch (error) {
            console.log(9999, error);
            changeSubmitLoading(false);
        } finally {
            changeSubmitLoading(false);
        }
    };

    const columns = [
        {
            title: '类型',
            dataIndex: 'discountType',
        },
        {
            title: '活动配置',
            dataIndex: 'discountValue',
            render(text: string, record: any) {
                const result = record.discountValue >= 0 ? `${text}${record.unit}` : '-';
                let preText = '';
                if (record.adjustType === ADJUST_TYPS.DYNAMIC) {
                    const preUnit = record.adjustMethodType === '0' ? '-' : '+';
                    const beforeStr =
                        record.adjustPriceType === ADJUST_PRICE_TYPES.SETTLE
                            ? ''
                            : '（结算价/原价）';
                    preText = `${beforeStr}${preUnit}`;
                }
                return (
                    <Space>
                        <span title={preText}>{preText}</span>
                        <span style={record.discountFlag ? { color: 'red' } : {}} title={result}>
                            {result}
                        </span>
                    </Space>
                );
            },
        },
    ];

    useEffect(() => {
        if (editActInfo) {
            form.setFieldsValue({
                ...editActInfo,
                actChannel: editActInfo?.actChannel?.split(','),
                dateTime: [moment(editActInfo?.effTime), moment(editActInfo?.expTime)],
                actTimeIntervalList: editActInfo?.periodsList
                    ? editActInfo.periodsList.map((v: API.MktActPeriodsParam) => {
                          return {
                              dctValue: v?.dctValue,
                              actTime: [
                                  moment(`1970-01-01 ${v?.beginTime}:00`),
                                  moment(`1970-01-01 ${v?.endTime}:00`),
                              ],
                          };
                      })
                    : undefined,
                cycleType: editActInfo?.cycleType ? '0201' : '1',
                cycleValue: editActInfo?.cycleValue?.split?.(',') || editActInfo?.cycleValue,
                operIds: editActInfo?.buildList?.map((ele) => ele.operId) || [],
            });
        }
    }, [editActInfo]);

    const formDisabled = useMemo(() => {
        if (formType === 'ADD' || editActInfo?.actState === '0') {
            return false;
        }
        return true;
    }, [editActInfo, formType]);

    const actType = Form.useWatch('actType', form);
    return (
        <>
            <Form
                form={form}
                scrollToFirstError
                wrapperCol={{ span: 8 }}
                labelCol={{
                    flex: '0 0 110px',
                }}
                labelAlign="left"
            >
                <div className={commonStyles['form-title']}>渠道营销活动</div>
                <Form.Item
                    label="活动名称"
                    name="actName"
                    rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
                >
                    <Input
                        maxLength={20}
                        placeholder="请输入活动名称，最多20个字"
                        autoComplete="off"
                        showCount
                        disabled={formDisabled}
                    />
                </Form.Item>
                <Form.Item
                    name="dateTime"
                    label="活动有效期"
                    rules={[
                        { required: true, message: '请选择活动有效期' },
                        (_) => ({
                            validator(rule, value) {
                                if (!value) {
                                    return Promise.reject('');
                                }
                                if (!value[0]) {
                                    return Promise.reject('请选择活动开始日期');
                                }
                                if (!value[1]) {
                                    return Promise.reject('请选择活动失效日期');
                                }
                                if (value[1]) {
                                    const nowTime = +new Date();
                                    const sendEndTime = +new Date(value[1]);

                                    if (sendEndTime < nowTime) {
                                        return Promise.reject('活动失效日期不能早于当前时间');
                                    }
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <DatePicker.RangePicker
                        disabled={formDisabled}
                        showTime={{
                            format: 'HH:mm:ss',
                            defaultValue: [
                                moment('00:00:00', 'HH:mm:ss'),
                                moment('23:59:59', 'HH:mm:ss'),
                            ],
                        }}
                        format="YYYY-MM-DD HH:mm:ss"
                    />
                </Form.Item>
                <Form.Item
                    label="活动渠道"
                    name="actChannel"
                    required
                    rules={[{ required: true, message: '请选择活动渠道' }]}
                >
                    <Select
                        placeholder="请选择"
                        allowClear
                        showSearch
                        mode="multiple"
                        disabled={formDisabled}
                        filterOption={(input: string, option: any) =>
                            (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                    >
                        {commonChannelList?.map((item: any, index: number) => {
                            return (
                                <Select.Option value={item.applyMode} key={index}>
                                    {item.channelName}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </Form.Item>
                <Form.Item
                    label="营销方式"
                    name="actType"
                    initialValue={ACT_TYPES.DISCOUNT}
                    rules={[{ required: true, message: '请选择营销方式' }]}
                >
                    <Radio.Group disabled={formDisabled}>
                        <Radio value={ACT_TYPES.DISCOUNT}>打折</Radio>
                        <Radio value={ACT_TYPES.REDUCTION}>立减</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    label="活动周期"
                    name="cycleType"
                    rules={[{ required: true, message: '请选择' }]}
                    initialValue={'1'}
                >
                    <Radio.Group>
                        <Radio value="1">全部</Radio>
                        <Radio value="0201">部分</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.cycleType !== curValues.cycleType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const cycleType = getFieldValue('cycleType');
                        // 限制
                        return (
                            (cycleType == '0201' && (
                                <Form.Item
                                    label="周期配置"
                                    name="cycleValue"
                                    wrapperCol={{ span: 18 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择可用日期',
                                        },
                                    ]}
                                >
                                    <Checkbox.Group options={dataOptions} />
                                </Form.Item>
                            )) ||
                            null
                        );
                    }}
                </Form.Item>

                <Form.Item
                    name="allDayFlag"
                    label="活动时段"
                    rules={[{ required: true, message: '请选择' }]}
                    initialValue={'1'}
                >
                    <Radio.Group disabled={formDisabled}>
                        <Radio value={'1'}>全天</Radio>
                        <Radio value={'0'}>部分时段</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues?.allDayFlag !== curValues?.allDayFlag ||
                        prevValues?.actType !== curValues?.actType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const allDayFlag = getFieldValue('allDayFlag');
                        const type = getFieldValue('actType');
                        console.error('shouldUpdate==>', allDayFlag, type);
                        return allDayFlag === '0' ? (
                            <ActTimeAndDiscountFormItem
                                form={form}
                                name="actTimeIntervalList"
                                label="选择时段"
                                disabled={formDisabled}
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择活动时段',
                                    },
                                ]}
                                formItemLayout={{
                                    ...{
                                        wrapperCol: {
                                            span: 24,
                                        },
                                    },
                                }}
                                actType={type}
                            />
                        ) : (
                            <DiscountFormItem form={form} actType={type} disabled={formDisabled} />
                        );
                    }}
                </Form.Item>

                <Form.Item
                    label="活动范围"
                    name="areaRangeType"
                    rules={[{ required: true, message: '请选择' }]}
                    initialValue={'2'}
                >
                    <Radio.Group>
                        <Radio value={'1'}>全部</Radio>
                        <Radio value={'2'}>部分</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    shouldUpdate={(prevValues, curValues) => {
                        return (
                            prevValues.areaRangeType !== curValues.areaRangeType ||
                            prevValues.actType !== curValues.actType ||
                            prevValues.operIds !== curValues.operIds ||
                            prevValues.stationConfig !== curValues.stationConfig
                        );
                    }}
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const areaRangeType = getFieldValue('areaRangeType');
                        const nowActType = getFieldValue('actType');

                        return (
                            areaRangeType == '2' && (
                                <>
                                    {/* <Form.Item
                                        label="活动运营商"
                                        name="operIds"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    const stationConfig =
                                                        getFieldValue('stationConfig');

                                                    if (isEmpty(stationConfig) && isEmpty(value)) {
                                                        return Promise.reject('请配置活动运营商');
                                                    }

                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        wrapperCol={{
                                            span: 18,
                                        }}
                                        required
                                    >
                                        <SelectAndImportOperItem />
                                    </Form.Item> */}
                                    <OperSearchList
                                        label="开放运营商"
                                        keyName="operIds"
                                        form={form}
                                        // 非图层则必填
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    const stationConfig =
                                                        getFieldValue('stationConfig');

                                                    const { allStations } = stationConfig ?? {};

                                                    if (isEmpty(allStations) && isEmpty(value)) {
                                                        return Promise.reject('请配置活动运营商');
                                                    }

                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        formItemLayout={{
                                            wrapperCol: { span: 24 },
                                        }}
                                        operList={editActInfo?.buildList}
                                        callbackOrigin
                                        cooperationPlatform={'xdt'}
                                    />
                                    <Form.Item
                                        label="活动场站"
                                        name="stationConfig"
                                        wrapperCol={{ span: 18 }}
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    const operIds = getFieldValue('operIds');
                                                    if (isEmpty(operIds) && isEmpty(value)) {
                                                        return Promise.reject('请配置指定场站');
                                                    }
                                                    if (!isEmpty(value)) {
                                                        const { addStations, allStations } = value;
                                                        if (
                                                            isEmpty(operIds) &&
                                                            isEmpty(allStations)
                                                        ) {
                                                            return Promise.reject('请配置指定场站');
                                                        }
                                                        if (
                                                            addStations.length > 20000 ||
                                                            allStations.length > 20000
                                                        ) {
                                                            return Promise.reject(
                                                                '一次性最多配置2万个站点',
                                                            );
                                                        }
                                                    }

                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <SearchStationItem
                                            title="活动场站"
                                            required
                                            purchase={SELECT_TYPES.ALL}
                                            currentUser={currentUser}
                                            hasStastics
                                            requestInfo={
                                                (actId || !isEmpty(editActInfo)) && {
                                                    listApi: getStationScopeListApi,
                                                    params: {
                                                        scopeRelateId: actId || editActInfo?.actId,
                                                        scopeRelateType: nowActType,
                                                    },
                                                    recordParams: {
                                                        relateId: actId || editActInfo?.actId,
                                                        scene: 'stc_station_market',
                                                    },
                                                }
                                            }
                                        ></SearchStationItem>
                                    </Form.Item>
                                </>
                            )
                        );
                    }}
                </Form.Item>

                <div className={commonStyles['form-submit']}>
                    {(!editActInfo || editActInfo?.actState < 1 || formType === 'ADD') && (
                        <Button
                            className={commonStyles['form-btn-left']}
                            type="primary"
                            loading={submitLoading}
                            onClick={() => {
                                saveCouponEvent('save', false);
                            }}
                        >
                            保存
                        </Button>
                    )}
                    <Button className={commonStyles['form-btn']} onClick={goBack}>
                        取消
                    </Button>
                    <Button
                        className={commonStyles['form-btn']}
                        type="primary"
                        loading={submitLoading}
                        onClick={() => {
                            saveCouponEvent('send', false);
                        }}
                    >
                        提交
                    </Button>
                </div>
            </Form>
            <ConflictModal
                initRef={conflictRef}
                onFinish={() => {
                    submitEvent(
                        conflictParams?.type,
                        conflictParams?.values,
                        conflictParams?.blank,
                    );
                }}
            />
        </>
    );
};

export default connect(({ global, sallerModel, user }: any) => ({
    global,
    sallerModel,
    currentUser: user.currentUser,
}))(MarketingForm);
