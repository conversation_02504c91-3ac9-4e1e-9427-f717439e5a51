import { getEnterpriseWithholdingListApi } from '@/services/Enterprise/EnterpriseWithholdingApi';
import { getEnterpriseDropDownApi } from '@/services/Enterprise/EnterpriseManageApi';

const enterpriseWithholdingModel = {
    namespace: 'enterpriseWithholding',
    state: {
        enterpriseWithholdingList: [], // 企业订单列表
        enterpriseWithholdingListTotal: 0,
        enterpriseDropDownList: [], // 企业下拉列表
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getEnterpriseWithholdingList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getEnterpriseWithholdingListApi, options);

                yield put({
                    type: 'updateEnterpriseWithholdingList',
                    enterpriseWithholdingList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 企业下拉列表
         */
        *getEnterpriseDropDownList({ companyNickname }, { call, put, select }) {
            try {
                const {
                    data: { companyList },
                } = yield call(getEnterpriseDropDownApi, companyNickname);

                yield put({
                    type: 'updateEnterpriseDropDownList',
                    enterpriseDropDownList: companyList,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateEnterpriseWithholdingList(state, { enterpriseWithholdingList, total }) {
            return {
                ...state,
                enterpriseWithholdingList,
                enterpriseWithholdingListTotal: total,
            };
        },
        updateEnterpriseDropDownList(state, { enterpriseDropDownList }) {
            return {
                ...state,
                enterpriseDropDownList,
            };
        },
    },
};
export default enterpriseWithholdingModel;
