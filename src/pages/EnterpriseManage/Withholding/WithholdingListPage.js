import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Select, DatePicker } from 'antd';
import { connect } from 'umi';
import { useEffect, useState, useMemo } from 'react';
import { getEnterpriseWithholdingListApiPath } from '@/services/Enterprise/EnterpriseWithholdingApi';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        form,
        dispatch,
        currentUser,
        global: { codeInfo },
        enterpriseWithholding: { enterpriseDropDownList },
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
    } = props;

    const { channel: channelList } = codeInfo;

    const [formatCompanyList, changeFormatCompanyList] = useState([]);

    useEffect(() => {
        if (!channelList) {
            dispatch({
                type: 'global/initCode',
                code: 'channel',
            });
        }

        dispatch({
            type: 'enterpriseWithholding/getEnterpriseDropDownList',
            companyNickname: '',
        });
    }, []);

    useEffect(() => {
        changeFormatCompanyList(enterpriseDropDownList);
    }, [enterpriseDropDownList]);

    const handleSearch = (txt) => {
        if (txt) {
            const list = [];
            for (const item of enterpriseDropDownList) {
                const searchName = item.companyNickname || item.companyName || '';
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            changeFormatCompanyList(list);
        } else {
            changeFormatCompanyList(enterpriseDropDownList);
        }
    };

    const handleFilter = (filterTxt, option) => true;

    const companyChannelListOptions = useMemo(() => {
        if (channelList) {
            return channelList.map((ele) => (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [channelList]);

    const enterpriseDropDownOptions = useMemo(() => {
        if (formatCompanyList) {
            return formatCompanyList.map((ele) => (
                <Option value={ele.companyId} key={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Option>
            ));
        }
        return [];
    }, [formatCompanyList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(365, 'day'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem label="选择日期:" name="dates" {...formItemLayout}>
                        <RangePicker picker="month" format="YYYY-MM" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem
                        label="企业简称:"
                        rules={[{ required: true, message: '请选择企业' }]}
                        name="companyId"
                        {...formItemLayout}
                    >
                        <Select
                            placeholder="请选择企业"
                            showSearch
                            onSearch={handleSearch}
                            filterOption={handleFilter}
                        >
                            {enterpriseDropDownOptions}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem
                        label="运营商"
                        rules={[{ required: true, message: '请选择' }]}
                        form={form}
                    />
                </Col>

                {currentUser.operId ? null : (
                    <Col span={8}>
                        <OperSelectTypeItem
                            label="合作方"
                            extraOpers={[
                                {
                                    operNickname: '平台',
                                    operName: '平台',
                                    operProjectCode: '平台',
                                    operId: '000000',
                                },
                            ]}
                            form={form}
                            formItemLayout={formItemLayout}
                        />
                    </Col>
                )}
                <Col span={8}>
                    <FormItem
                        label="订&nbsp;&nbsp;单&nbsp;&nbsp;号:"
                        name="channel"
                        {...formItemLayout}
                    >
                        <Select placeholder="请选择">{companyChannelListOptions}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem
                        label="交&nbsp;&nbsp;易&nbsp;&nbsp;号:"
                        name="channel"
                        {...formItemLayout}
                    >
                        <Select placeholder="请选择">{companyChannelListOptions}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="交易类型:" name="channel" {...formItemLayout}>
                        <Select placeholder="请选择">{companyChannelListOptions}</Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单渠道:" name="channel" {...formItemLayout}>
                        <Select placeholder="请选择">{companyChannelListOptions}</Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const WithholdingListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        enterpriseWithholding: { enterpriseWithholdingList, enterpriseWithholdingListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                companyId: data.companyId,
                belongType: data.belongType,
                beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM')) || '',
            };

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'enterpriseWithholding/getEnterpriseWithholdingList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // //导出
    const exportFormEvent = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                companyId: data.companyId,
                belongType: data.belongType,
                beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM')) || '',
            };

            const columnsStrs = [];
            for (const item of columns) {
                if (item.dataIndex) {
                    columnsStrs.push({
                        key: item.dataIndex,
                        value: item.title,
                    });
                }
            }
            exportTableByParams({
                methodUrl: getEnterpriseWithholdingListApiPath,
                options: params,
                columnsStr: columnsStrs,
            });
        } catch (error) {}
    };

    const gotoDetails = (item) => {
        history.push(`/financemanage/orderTransaction/list/details/${item.orderNo}`);
    };

    const columns = [
        {
            title: '合作方',
            width: 200,
            dataIndex: 'belongTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '企业简称',
            width: 140,
            dataIndex: 'companyNickname',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '交易时间',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易类型',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易号',
            width: 200,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '交易金额',
            width: 140,
            dataIndex: 'chargePq',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 200,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单时间',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单号',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '场站',
            width: 140,
            dataIndex: 'withholdingTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <span className={styles['table-btn']} onClick={() => gotoDetails(record)}>
                        交易详情
                    </span>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper title="企业订单">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={enterpriseWithholdingList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: enterpriseWithholdingListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, enterpriseWithholding, loading }) => ({
    global,
    currentUser: user.currentUser,
    enterpriseWithholding,
    listLoading: loading.effects['enterpriseWithholding/getEnterpriseWithholdingList'],
}))(WithholdingListPage);
