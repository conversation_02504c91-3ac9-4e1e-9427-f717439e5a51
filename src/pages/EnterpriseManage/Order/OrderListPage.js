import { InfoCircleOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Select, Input, DatePicker, Tooltip, Row } from 'antd';
import { throttle } from 'lodash';

import moment from 'moment';
import React, { useEffect, useState, useMemo } from 'react';
import { connect } from 'umi';

import {
    getEnterpriseOrderListApiPath,
    exportEnterpriseOrderListApi,
    exportEnterpriseCompanyInvoiceListApi,
    downloadCompanyOrderApi,
} from '@/services/Enterprise/EnterpriseOrderApi';
import { getOperCompanyListApi } from '@/services/Enterprise/EnterpriseManageApi';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import { exportTableByParams } from '@/utils/utils';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        form,
        dispatch,
        global: { codeInfo },
        listLoading,
        onSubmit,
        onReset,
        onExportForm,
        currentUser,
        isCompany,
        initOperId, // 初始化时
    } = props;

    const { companyBelongType: companyBelongTypeList } = codeInfo;

    const [companyList, updateCompanyList] = useState([]);

    const [formatCompanyList, changeFormatCompanyList] = useState([]);

    useEffect(() => {
        if (!companyBelongTypeList && !isCompany) {
            dispatch({
                type: 'global/initCode',
                code: 'companyBelongType',
            });
        }

        dispatch({
            type: 'enterpriseOrder/getEnterpriseDropDownList',
            companyNickname: '',
        });
    }, [isCompany]);

    useEffect(() => {
        changeFormatCompanyList(companyList);
    }, [companyList]);

    useEffect(() => {
        if ((currentUser.operId && !isCompany) || initOperId) {
            initCompany(currentUser.operId || initOperId);
        }
    }, [currentUser, isCompany, initOperId]);

    const initCompany = async (operId) => {
        try {
            const {
                data: { operCompanyList },
            } = await getOperCompanyListApi({ operId });
            updateCompanyList(operCompanyList);
        } catch (error) {}
    };

    const handleSearch = (txt) => {
        if (txt) {
            const list = [];
            for (const item of companyList) {
                const searchName = item.companyNickname || item.companyName || '';
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            changeFormatCompanyList(list);
        } else {
            changeFormatCompanyList(companyList);
        }
    };

    const onClearEvent = () => {
        changeFormatCompanyList(companyList);
    };

    const handleFilter = (filterTxt, option) => true;

    const enterpriseDropDownOptions = useMemo(() => {
        if (formatCompanyList) {
            return formatCompanyList.map((ele) => (
                <Option key={ele.companyId} value={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Option>
            ));
        }
        return [];
    }, [formatCompanyList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(365, 'day'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                minSpan={40}
            >
                <Col span={8}>
                    <FormItem label="选择日期:" name="dates" {...formItemLayout}>
                        <RangePicker picker="month" format="YYYY-MM" />
                    </FormItem>
                </Col>
                {!isCompany && (
                    <Col span={8}>
                        <OperSelectTypeItem
                            label="合作方"
                            rules={[{ required: true, message: '请选择合作方' }]}
                            onChange={(value) => {
                                form.setFieldsValue({
                                    companyId: '',
                                });
                                initCompany(value);
                            }}
                            form={form}
                            isCompany
                            placeholder={'支持全称、简称模糊搜索'}
                        />
                    </Col>
                )}
                {!isCompany && (
                    <Col span={8}>
                        <FormItem
                            label="企业简称:"
                            rules={[{ required: true, message: '请选择企业' }]}
                            name="companyId"
                            {...formItemLayout}
                        >
                            <Select
                                placeholder="请选择企业"
                                showSearch
                                onSearch={handleSearch}
                                filterOption={handleFilter}
                                onClear={onClearEvent}
                                allowClear
                            >
                                {enterpriseDropDownOptions}
                            </Select>
                        </FormItem>
                    </Col>
                )}
                <Col span={8}>
                    <FormItem label="VIN:" name="vin" {...formItemLayout}>
                        <Input placeholder="请填写"></Input>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="欠款金额:" name="unPayAmtFlag" {...formItemLayout}>
                        <Select placeholder="请选择">
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const OrderListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit },
        enterpriseOrder: { enterpriseOrderList, enterpriseOrderListTotal },
        listLoading,
        currentUser,
    } = props;

    const isCompany = useMemo(() => {
        return currentUser?.roleList?.includes('company');
    }, [currentUser]);

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }

        return () => {
            dispatch({
                type: 'enterpriseOrder/updateEnterpriseOrderList',
                enterpriseOrderList: [],
                total: 0,
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                companyId: data.companyId,
                unPayAmtFlag: data.unPayAmtFlag,
                operId: data.operId,
                vin: data.vin,
                belongType: data.belongType,
                beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM')) || '',
            };

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'enterpriseOrder/getEnterpriseOrderList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // //导出
    const exportFormEvent = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                companyId: data.companyId,
                unPayAmtFlag: data.unPayAmtFlag,
                operId: data.operId,
                vin: data.vin,
                belongType: data.belongType,
                beginDate: (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM')) || '',
            };

            exportEnterpriseCompanyInvoiceListApi(params);
        } catch (error) {}
    };

    const downloadDetailsEvent = throttle(async (item) => {
        try {
            const params = {
                beginDate: item.orderTime,

                endDate: item.orderTime,

                companyId: item.companyId,
            };
            downloadCompanyOrderApi(params);
        } catch (error) {}
    }, 500);

    const columns = [
        ...(isCompany
            ? []
            : [
                  {
                      title: '合作方',
                      width: 200,
                      dataIndex: 'belongTypeName',
                      render(text, record) {
                          return <span title={text}>{text}</span>;
                      },
                  },
                  {
                      title: '企业简称',
                      width: 140,
                      dataIndex: 'companyNickname',
                      render(text, record) {
                          return (
                              <div className="text-line" style={{ width: '140px' }} title={text}>
                                  {text || ''}
                              </div>
                          );
                      },
                  },
              ]),
        {
            title: '订单月份',
            width: 140,
            dataIndex: 'orderTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    订单数
                    <Tooltip title="成功完成充电订单">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            dataIndex: 'orderNum',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '充电量',
            width: 140,
            dataIndex: 'chargePq',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    充电订单总金额
                    <Tooltip title="订单原始金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 170,
            dataIndex: 'orderAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    企业结算金额
                    <Tooltip title="订单原始金额扣减企业所享受的优惠金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 160,
            dataIndex: 'companySettleAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '个人支付金额',
            width: 140,
            dataIndex: 'personPayAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '代付金额',
            width: 140,
            dataIndex: 'companyPayAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '欠款金额',
            width: 140,
            dataIndex: 'unPayAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '手工退款金额',
            width: 140,
            dataIndex: 'refundAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    企业开票金额
                    <Tooltip title="由企业申请开票的金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 160,
            dataIndex: 'companyInvoiceAmt',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 160,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Row gutter={12}>
                        <Col>
                            <span
                                className={styles['table-btn']}
                                onClick={() => downloadDetailsEvent(record)}
                            >
                                下载明细
                            </span>
                        </Col>
                        {/* 开票状态，待申请-申请开票，已申请-查看开票
                        01-待申请，02-申请中，03-已申请',
                        */}
                        {(record?.invoiceApplyStatus == '01' && (
                            <Col>
                                <span
                                    className={styles['table-btn']}
                                    onClick={() => {
                                        history.push(
                                            `/userCenter/enterprise/order/list/invoice-add?companyId=${record?.companyId}&month=${record.orderTime}`,
                                        );
                                    }}
                                >
                                    申请开票
                                </span>
                            </Col>
                        )) ||
                            null}
                        {(record?.invoiceApplyStatus == '03' && (
                            <Col>
                                <span
                                    className={styles['table-btn']}
                                    onClick={() => {
                                        history.push(
                                            `/userCenter/enterprise/order/list/invoice-detail?invoiceApplyId=${record?.invoiceApplyId}`,
                                        );
                                    }}
                                >
                                    查看开票
                                </span>
                            </Col>
                        )) ||
                            null}
                    </Row>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper title="企业订单">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                    isCompany={isCompany}
                    initOperId={pageInit[pathname]?.form?.operId}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={enterpriseOrderList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: enterpriseOrderListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, enterpriseOrder, loading }) => ({
    global,
    currentUser: user.currentUser,
    enterpriseOrder,
    listLoading: loading.effects['enterpriseOrder/getEnterpriseOrderList'],
}))(OrderListPage);
