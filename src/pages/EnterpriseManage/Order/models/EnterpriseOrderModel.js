import {
    getEnterpriseOrderListApi,
    getEnterpriseOrderPageListApi,
} from '@/services/Enterprise/EnterpriseOrderApi';
import { getEnterpriseDropDownApi } from '@/services/Enterprise/EnterpriseManageApi';

const enterpriseOrderModel = {
    namespace: 'enterpriseOrder',
    state: {
        enterpriseOrderList: [], // 企业订单列表
        enterpriseOrderListTotal: 0,
        enterpriseDropDownList: [], // 企业下拉列表
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getEnterpriseOrderList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records: list, total },
                } = yield call(getEnterpriseOrderPageListApi, options);

                yield put({
                    type: 'updateEnterpriseOrderList',
                    enterpriseOrderList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 企业下拉列表
         */
        *getEnterpriseDropDownList({ companyNickname }, { call, put, select }) {
            try {
                const {
                    data: { companyList },
                } = yield call(getEnterpriseDropDownApi, companyNickname);

                yield put({
                    type: 'updateEnterpriseDropDownList',
                    enterpriseDropDownList: companyList,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateEnterpriseOrderList(state, { enterpriseOrderList, total }) {
            return {
                ...state,
                enterpriseOrderList,
                enterpriseOrderListTotal: total,
            };
        },
        updateEnterpriseDropDownList(state, { enterpriseDropDownList }) {
            return {
                ...state,
                enterpriseDropDownList,
            };
        },
    },
};
export default enterpriseOrderModel;
