import React, { Fragment } from 'react';
import { Modal, Form, Input, Select, Radio, Switch, Space, Button, message } from 'antd';
import { useEffect, useState, useMemo } from 'react';
import { useRequest } from 'ahooks';

import NumericInput from '@/components/NumericInput/index';

import { getOperCompanyListApi } from '@/services/Enterprise/EnterpriseManageApi';
import { getEnterpriseStaffListApi } from '@/services//Enterprise/EnterpriseCarApi';
import { isEmpty } from '@/utils/utils';

const { Option } = Select;

const CarUpdateModal: React.FC<{
    visible: boolean;
    carInfo?: any;
    onOk: (values: any) => void;
    onCancel: () => void;
    currentUser: any;
}> = ({ visible, carInfo, onOk, onCancel, currentUser }) => {
    const [form] = Form.useForm();

    const [companyList, updateCompanyList] = useState<any[]>([]);
    const [formatCompanyList, changeFormatCompanyList] = useState<any[]>([]);

    const companyId = Form.useWatch('companyId', form);
    const staffId = Form.useWatch('staffId', form);
    const startPlugCharge = Form.useWatch('startPlugCharge', form);

    const [staffList, updateStaffList] = useState<API.StaffInfo[]>([]);

    const { run: searchStaffList, loading: searchStaffLoading } = useRequest(
        (params) => {
            return getEnterpriseStaffListApi(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonResponse<API.StaffInfo[]>) => {
                const { data = [] } = res;
                updateStaffList(data);
            },
        },
    );

    useEffect(() => {
        if (companyId) {
            searchStaffList({ companyId });
        } else {
            form.setFieldsValue({ staffId: '' });
        }
    }, [companyId]);

    useEffect(() => {
        if (currentUser.operId) {
            initCompany(currentUser.operId);
        } else {
            initCompany();
        }
    }, [currentUser]);

    const initCompany = async (operId?: string) => {
        try {
            const {
                data: { operCompanyList },
            } = await getOperCompanyListApi({ operId });
            updateCompanyList(operCompanyList);
            changeFormatCompanyList(operCompanyList);
        } catch (error) {}
    };

    useEffect(() => {
        if (visible && carInfo) {
            const params = {
                ...carInfo,
                startPlugCharge: carInfo?.startPlugCharge === '1' ? true : false,
            };
            form.setFieldsValue(params);
        } else {
            form.resetFields();
        }
    }, [visible, carInfo]);

    const staffPayTypes = useMemo(() => {
        if (staffId) {
            const staffInfo: API.StaffInfo | undefined = staffList.find(
                (ele) => ele.staffId == staffId,
            );
            if (staffInfo && staffInfo?.payTypes instanceof Array) {
                return staffInfo?.payTypes?.map((ele) => (
                    <Radio value={ele.code} key={ele.code}>
                        {ele.name}
                    </Radio>
                ));
            }
        }
        return [];
    }, [staffId, staffList]);

    const modalTitle = useMemo(() => {
        let title = '新建车辆';
        if (carInfo) {
            title = '编辑车辆';
        }
        return title;
    }, [carInfo]);
    const submitSaveEvent = (values: any) => {
        onOk && onOk(values);
    };

    const handleSearch = (txt: string) => {
        if (txt) {
            const list: any[] = [];
            for (const item of companyList) {
                const searchName = item?.companyNickname || item?.companyName || '';
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            changeFormatCompanyList(list);
        } else {
            changeFormatCompanyList(companyList);
        }
    };

    const onClearEvent = () => {
        changeFormatCompanyList(companyList);
    };

    const handleFilter = (filterTxt: string, option: any) => true;

    const handleStaffFilter = (filterTxt: string, option: any) => {
        return option?.children?.indexOf(filterTxt) >= 0;
    };

    const enterpriseDropDownOptions = useMemo(() => {
        if (formatCompanyList) {
            return formatCompanyList.map((ele) => (
                <Option key={ele.companyId} value={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Option>
            ));
        }
        return [];
    }, [formatCompanyList]);

    const staffOptions = useMemo(() => {
        if (staffList instanceof Array) {
            return staffList.map((ele) => {
                return (
                    <Option key={ele.staffId} value={ele.staffId}>
                        {ele.staffName}
                    </Option>
                );
            });
        }
        return [];
    }, [staffList]);

    return (
        <Modal
            title={modalTitle}
            visible={visible}
            footer={false}
            destroyOnClose
            onCancel={onCancel}
            maskClosable={false}
        >
            <Form
                name="carInfo"
                form={form}
                onFinish={submitSaveEvent}
                labelCol={{ flex: '0 0 100px' }}
            >
                <Form.Item
                    label="所属企业"
                    name="companyId"
                    rules={[
                        {
                            required: true,
                            message: '请选择企业',
                        },
                    ]}
                >
                    <Select
                        placeholder="请选择企业"
                        showSearch
                        onSearch={handleSearch}
                        filterOption={handleFilter}
                        onClear={onClearEvent}
                        allowClear
                        onChange={(value) => {
                            handleSearch(''); // 已选中的情况下，清空筛选条件
                        }}
                    >
                        {enterpriseDropDownOptions}
                    </Select>
                </Form.Item>
                {companyId && (
                    <Form.Item
                        label="绑定员工"
                        name="staffId"
                        rules={[
                            {
                                required: true,
                                message: '请选择绑定员工',
                            },
                        ]}
                    >
                        <Select
                            loading={searchStaffLoading}
                            placeholder="请选择绑定员工"
                            showSearch
                            filterOption={handleStaffFilter}
                            allowClear
                        >
                            {staffOptions}
                        </Select>
                    </Form.Item>
                )}
                <Form.Item
                    label="车牌号"
                    name="licenseNo"
                    rules={[
                        {
                            required: true,
                            message: '请填写车牌号',
                        },
                    ]}
                >
                    <Input placeholder="请填写" autoComplete="off"></Input>
                </Form.Item>
                <Form.Item
                    label="VIN码"
                    name="vin"
                    required
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject('请填写vin');
                                }
                                if (value?.length != 17) {
                                    return Promise.reject('请填写17位数字格式vin码');
                                }

                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <NumericInput
                        placeholder="请填写"
                        maxLength={17}
                        autoComplete="off"
                        isHide
                    ></NumericInput>
                </Form.Item>

                <Form.Item label="车辆类型" name="carType">
                    <Input placeholder="请填写" autoComplete="off"></Input>
                </Form.Item>

                <Form.Item label="即插即充" required>
                    {staffPayTypes.length === 0 ? (
                        <Space>
                            <Form.Item noStyle>
                                <Switch checked={false} disabled />
                            </Form.Item>
                            员工无可用的代扣方式
                        </Space>
                    ) : (
                        <Space>
                            <Form.Item
                                noStyle
                                name="startPlugCharge"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch />
                            </Form.Item>
                        </Space>
                    )}
                </Form.Item>

                {startPlugCharge && (
                    <Form.Item
                        label="支付方式"
                        name="payType"
                        rules={[
                            {
                                required: true,
                                message: '请选择支付方式',
                            },
                        ]}
                        initialValue={'01'}
                    >
                        <Radio.Group>{staffPayTypes}</Radio.Group>
                    </Form.Item>
                )}

                <Space
                    align="end"
                    className="mg-t-20"
                    style={{ display: 'flex', justifyContent: 'center' }}
                >
                    <Button type="primary" htmlType="submit">
                        提交
                    </Button>
                    <Button onClick={onCancel}>取消</Button>
                </Space>
            </Form>
        </Modal>
    );
};

export default CarUpdateModal;
