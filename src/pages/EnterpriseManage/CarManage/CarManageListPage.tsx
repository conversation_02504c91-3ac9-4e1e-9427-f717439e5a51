import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Popconfirm,
    Select,
    Space,
    Tabs,
    Typography,
    message,
    Modal,
} from 'antd';
import styles from '@/assets/styles/common.less';

import React, { useEffect, useState, useMemo, useRef } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Link, connect } from 'umi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { CAR_STATUS } from './CarConfig';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import ImportModal from '@/components/ImportModal';

import {
    getCompanyOperListApi,
    getOperCompanyListApi,
} from '@/services/Enterprise/EnterpriseManageApi';

import {
    getEnterpriseCarListApiPath,
    getEnterpriseCarListApi,
    updateEnterpriseCar<PERSON>pi,
    getEnterpriseCarInfoApi,
    importEnterpriseCarApi,
    delEnterpriseCarApi,
    bindEnterpriseCarStatusApi,
    exportEnterpriseCarListApi,
} from '@/services/Enterprise/EnterpriseCarApi';

import CarUpdateModal from './CarUpdateModal';
import { exportTableByParams, formatExportColumns, renderTableDataIndexText } from '@/utils/utils';

const { Option } = Select;

const carStatusOptions = [
    {
        key: CAR_STATUS.ALL,
        label: '全部',
    },
    {
        key: CAR_STATUS.NORMAL,
        label: '正常',
    },
    {
        key: CAR_STATUS.UNBINDING,
        label: '已解绑',
    },
];

const CarManageListPage: React.FC<{
    dispatch?: any;
    global?: any;
    currentUser?: any;
}> = ({ dispatch, global, currentUser }) => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string>(CAR_STATUS.ALL);
    const [companyList, updateCompanyList] = useState<any[]>([]);

    const [formatCompanyList, changeFormatCompanyList] = useState<any[]>([]);

    const [carInfo, updateCarInfo] = useState<any>(null);
    const [showCarModal, toggleCarModal] = useState(false);

    const importModalRef = useRef<any>();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: API.CarManagePaginationRequest) => {
            const { data } = await getEnterpriseCarListApi({
                ...params,
                pageSize,
                pageIndex: current,
            });
            return {
                list: data?.records,
                current: data?.pageIndex,
                pageSize: data?.pageSize,
                total: data?.total,
            };
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const { run: exportSearchList, loading: exportListLoading } = useRequest(
        (params) => {
            return exportEnterpriseCarListApi(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {},
        },
    );

    const { run: delCarEvent, loading: delCarLoading } = useRequest(
        (params) => {
            return delEnterpriseCarApi(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                const { ret } = res;
                if (ret == 200) {
                    message.success('删除成功');
                }
                refreshList();
            },
        },
    );

    const { run: bindCarEvent, loading: bindCarLoading } = useRequest(
        (params) => {
            return bindEnterpriseCarStatusApi(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                const { ret } = res;
                if (ret == 200) {
                    message.success('操作成功');
                }
                refreshList();
            },
        },
    );

    const getParams = () => {
        const formData = form?.getFieldsValue();
        const params = { ...formData };
        if (currentTab && currentTab !== CAR_STATUS.ALL) {
            params.bindStatus = currentTab;
        }
        return params;
    };

    const onFinish = () => {
        searchList(pagination, getParams());
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    };

    // //导出
    const exportFormEvent = () => {
        const params = getParams();
        exportSearchList(params);
    };

    // useEffect(() => {
    //     searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    // }, []);

    useEffect(() => {
        if (currentUser.operId) {
            initCompany(currentUser.operId);
        }
    }, [currentUser]);

    useEffect(() => {
        changeFormatCompanyList(companyList);
    }, [companyList]);

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, getParams());
    }, [currentTab]);

    const refreshList = () => {
        searchList(pagination, getParams());
    };

    const changeTabType = (value: string) => {
        setCurrentTab(value);
    };

    const initCompany = async (operId: string) => {
        try {
            const {
                data: { operCompanyList },
            } = await getOperCompanyListApi({ operId });
            updateCompanyList(operCompanyList);
        } catch (error) {}
    };

    const handleSearch = (txt: string) => {
        if (txt) {
            const list: any[] = [];
            for (const item of companyList) {
                const searchName = item?.companyNickname || item?.companyName || '';
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            changeFormatCompanyList(list);
        } else {
            changeFormatCompanyList(companyList);
        }
    };

    const onClearEvent = () => {
        changeFormatCompanyList(companyList);
    };

    const handleFilter = (filterTxt: string, option: any) => true;

    const enterpriseDropDownOptions = useMemo(() => {
        if (formatCompanyList) {
            return formatCompanyList.map((ele) => (
                <Option key={ele.companyId} value={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Option>
            ));
        }
        return [];
    }, [formatCompanyList]);

    const columns: ColumnsType<API.CarManageListVo> = [
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '合作方',
            width: 120,
            dataIndex: 'operName',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '企业简称',
            width: 120,
            dataIndex: 'companyNickname',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '车牌号',
            width: 180,
            dataIndex: 'licenseNo',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: 'VIN码',
            width: 200,
            dataIndex: 'vin',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '绑定手机号',
            width: 160,
            dataIndex: 'mobile',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: 'VIN码充电',
            width: 120,
            dataIndex: 'startPlugChargeName',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '支付方式',
            width: 120,
            dataIndex: 'payTypeName',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '状态',
            width: 120,
            dataIndex: 'bindStatusName',
            render(text) {
                return renderTableDataIndexText({ text });
            },
        },
        {
            title: '操作',
            width: 160,
            fixed: 'right',
            render: (text, record) => {
                const btnList = [];
                const editBtn = (
                    <Typography.Link
                        onClick={() => {
                            editCarEvent(record);
                        }}
                    >
                        编辑
                    </Typography.Link>
                );
                const delBtn = (
                    <Popconfirm
                        title="确认要删除车辆吗？"
                        onConfirm={() => {
                            delCarEvent(record?.prefId);
                        }}
                        okButtonProps={{ loading: delCarLoading }}
                    >
                        <Typography.Link>删除</Typography.Link>
                    </Popconfirm>
                );

                const unBindBtn = (
                    <Popconfirm
                        title={
                            <div>
                                <h3>车辆解绑</h3>
                                <p>解绑后，车辆将不再享受企业优惠</p>
                            </div>
                        }
                        onConfirm={() => {
                            bindCarEvent({
                                id: record?.prefId,
                                bindStatus: CAR_STATUS.UNBINDING,
                            });
                        }}
                        okButtonProps={{ loading: bindCarLoading }}
                    >
                        <Typography.Link>解绑</Typography.Link>
                    </Popconfirm>
                );

                const restoreBtn = (
                    <Popconfirm
                        title="确认要恢复车辆吗？"
                        onConfirm={() => {
                            bindCarEvent({
                                id: record?.prefId,
                                bindStatus: CAR_STATUS.NORMAL,
                            });
                        }}
                        okButtonProps={{ loading: bindCarLoading }}
                    >
                        <Typography.Link>恢复</Typography.Link>
                    </Popconfirm>
                );
                btnList.push(editBtn);
                if (record?.bindStatus == '1') {
                    btnList.push(unBindBtn);
                } else if (record?.bindStatus == '0') {
                    // btnList.push(restoreBtn);
                }
                // btnList.push(delBtn);

                return <Space>{btnList}</Space>;
            },
        },
    ];
    const formDataExt = (formData: any) => {};

    const importSuccess = (list: any[]) => {};

    const { run: saveCarInfoEvent, loading: saveCarInfoLoading } = useRequest(
        (params) => {
            toggleCarModal(false);
            const options = {
                ...params,
                startPlugCharge: params.startPlugCharge ? '1' : '0',
            };

            if (carInfo) {
                options.id = carInfo.id;
            }

            return updateEnterpriseCarApi(options);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('保存成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '操作失败');
                }
            },
        },
    );

    const addCarEvent = () => {
        updateCarInfo(null);
        toggleCarModal(true);
    };
    const editCarEvent = async (item: any) => {
        try {
            const { data } = await getEnterpriseCarInfoApi(item.prefId);

            updateCarInfo(data);
            toggleCarModal(true);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchOptionsBar
                        loading={listLoading}
                        onReset={resetForm}
                        onExportForm={exportFormEvent}
                        minSpan={24 * 1}
                    >
                        <Col span={8}>
                            <Form.Item label="车辆号码" name="licenseNo">
                                <Input allowClear placeholder="请填写" autoComplete="off" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="VIN码" name="vin">
                                <Input allowClear placeholder="请填写" autoComplete="off" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="手机号" name="mobile">
                                <Input allowClear placeholder="请填写" autoComplete="off" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <OperSelectTypeItem
                                label="合作方"
                                // rules={[{ required: true, message: '请选择合作方' }]}
                                onChange={(value: string) => {
                                    form.setFieldsValue({
                                        companyId: '',
                                    });
                                    initCompany(value);
                                }}
                                form={form}
                                isCompany
                                placeholder={'支持全称、简称模糊搜索'}
                            />
                        </Col>
                        <Col span={8}>
                            <Form.Item
                                label="企业简称:"
                                // rules={[{ required: true, message: '请选择企业' }]}
                                name="companyId"
                            >
                                <Select
                                    placeholder="请选择企业"
                                    showSearch
                                    onSearch={handleSearch}
                                    filterOption={handleFilter}
                                    onClear={onClearEvent}
                                    allowClear
                                    onChange={(value) => {
                                        handleSearch('');
                                    }}
                                >
                                    {enterpriseDropDownOptions}
                                </Select>
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Button
                            type="primary"
                            onClick={() => {
                                addCarEvent();
                            }}
                        >
                            + 新增
                        </Button>

                        <Button
                            onClick={() => {
                                importModalRef?.current?.show();
                            }}
                        >
                            导入车辆
                        </Button>
                    </Space>
                </div>
                <Tabs defaultActiveKey={CAR_STATUS.ALL} onChange={changeTabType}>
                    {carStatusOptions.map((v) => (
                        <Tabs.TabPane tab={v.label} key={v.key} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="prefId"
                    dataSource={listData?.list}
                    columns={columns}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                    tabType={currentTab}
                />
            </Card>
            <CarUpdateModal
                visible={showCarModal}
                carInfo={carInfo}
                onOk={saveCarInfoEvent}
                onCancel={() => {
                    toggleCarModal(false);
                }}
                currentUser={currentUser}
            ></CarUpdateModal>
            <ImportModal
                title="导入车辆信息"
                downLoadPath="/aliMini/xdt/static/excel/企业车辆导入模板.xlsx"
                onUpload={async (formData, callback) => {
                    // formDataExt(formData);

                    const {
                        ret,
                        // data: { matchstationList },
                    } = await importEnterpriseCarApi(formData, currentUser?.operId);

                    callback &&
                        callback({
                            ret: ret == 200 ? 'suc' : 'false',
                            msg: ret == 200 ? '导入成功' : '导入失败',
                        });
                    // importSuccess(matchstationList);
                    refreshList();
                }}
                ref={importModalRef}
                surfix={['.xls']}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }) => ({ global, currentUser: user.currentUser }))(
    CarManageListPage,
);
