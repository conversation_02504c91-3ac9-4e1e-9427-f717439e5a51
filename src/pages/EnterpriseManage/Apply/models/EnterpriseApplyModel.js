import { getEnterpriseApplyListApi } from '@/services/Enterprise/EnterpriseApplyApi';

const enterpriseApplyModel = {
    namespace: 'enterpriseApply',
    state: {
        enterpriseApplyList: [], // 企业订单列表
        enterpriseApplyListTotal: 0,
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getEnterpriseApplyList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getEnterpriseApplyListApi, options);

                yield put({
                    type: 'updateEnterpriseApplyList',
                    enterpriseApplyList: records,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateEnterpriseApplyList(state, { enterpriseApplyList, total }) {
            return {
                ...state,
                enterpriseApplyList,
                enterpriseApplyListTotal: total,
            };
        },
    },
};
export default enterpriseApplyModel;
