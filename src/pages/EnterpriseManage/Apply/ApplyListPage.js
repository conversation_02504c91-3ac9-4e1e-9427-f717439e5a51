import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, message, Modal, Space, Input } from 'antd';
import { connect } from 'umi';
import { useEffect, useState } from 'react';
import {
    getEnterpriseApplyListApiPath,
    updateEnterpriseApplyApi,
    exportEnterpriseApplyListApi,
} from '@/services/Enterprise/EnterpriseApplyApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import NumericInput from '@/components/NumericInput/index';

import AreaCascader from '../../AssetCenter/StationManage/components/AreaCascader';

const { TextArea } = Input;
const FormItem = Form.Item;
const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExportForm}>
                <Col span={8}>
                    <FormItem label="手机号:" name="mobile" {...formItemLayout}>
                        <NumericInput placeholder="请填写" maxLength={11} autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <AreaCascader
                        form={form}
                        formItemLayout={formItemLayout}
                        label={'城市'}
                        partEnabled
                    />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const ApplyListPage = (props) => {
    const {
        dispatch,
        history,
        global: { pageInit, codeInfo },
        enterpriseApply: { enterpriseApplyList, enterpriseApplyListTotal },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [remarkForm] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    const [openEditView, toggleEditView] = useState(false);

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            mobile: data.mobile,
        };
        if (data.city) {
            params.city = data.city;
        }

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'enterpriseApply/getEnterpriseApplyList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    // //导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            ...data,
            mobile: data.mobile,
        };
        if (data.city) {
            params.city = (data.city && data.city.pop()) || '';
        }
        exportEnterpriseApplyListApi(params);
    };

    const editMarkEvent = (item) => {
        remarkForm.setFieldsValue({ remark: item.mark || '', companyApplyId: item.companyApplyId });
        toggleEditView(true);
    };

    const closeEditView = () => {
        toggleEditView(false);
        remarkForm.resetFields();
    };

    const onRemarkFinish = async (values) => {
        try {
            await updateEnterpriseApplyApi(values);
            message.success('修改成功');
            closeEditView();
            searchData();
        } catch (error) {}
    };

    const columns = [
        {
            title: '提交时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '企业名称',
            width: 160,
            dataIndex: 'companyName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '160px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '企业规模',
            width: 100,
            dataIndex: 'companyScale',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '100px' }} title={text}>
                        {text || '0'}人
                    </div>
                );
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '联系人',
            width: 140,
            dataIndex: 'contacts',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '联系人手机号',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '备注',
            width: 140,
            dataIndex: 'remark',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text || ''}
                    </div>
                );
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <span className={styles['table-btn']} onClick={() => editMarkEvent(record)}>
                        添加备注
                    </span>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper title="企业申请">
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.companyApplyId}
                    dataSource={enterpriseApplyList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: enterpriseApplyListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
                <Modal
                    title="添加备注"
                    visible={openEditView}
                    footer={false}
                    onCancel={closeEditView}
                    destroyOnClose
                    maskClosable={false}
                >
                    <Space direction="vertical" style={{ width: '100%' }}>
                        <Form
                            {...formItemLayout}
                            form={remarkForm}
                            onFinish={onRemarkFinish}
                            initialValues={{}}
                            scrollToFirstError
                        >
                            <FormItem
                                label="备注"
                                name="remark"
                                rules={[{ required: true, message: '请填写备注' }]}
                            >
                                <TextArea
                                    placeholder="请填写"
                                    rows={4}
                                    autofocus="autofocus"
                                    maxLength={200}
                                    showCount
                                />
                            </FormItem>
                            <FormItem noStyle name="companyApplyId"></FormItem>

                            <Space>
                                <Button type="primary" htmlType="submit">
                                    提交
                                </Button>
                                <Button onClick={closeEditView}>取消</Button>
                            </Space>
                        </Form>
                    </Space>
                </Modal>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, enterpriseApply, loading }) => ({
    global,
    enterpriseApply,
    listLoading: loading.effects['enterpriseApply/getEnterpriseApplyList'],
}))(ApplyListPage);
