import { getStaffListApi } from '@/services/Enterprise/StaffApi';
import { getEnterpriseDropDownApi } from '@/services/Enterprise/EnterpriseManageApi';

const StaffModel = {
    namespace: 'staffModel',
    state: {
        staffList: [], // 员工列表
        staffListTotal: 0,
        enterpriseDropDownList: [], // 企业下拉列表
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getStaffList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStaffListApi, options);

                yield put({
                    type: 'updateStaffList',
                    staffList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 企业下拉列表
         */
        *getEnterpriseDropDownList({ companyNickname }, { call, put, select }) {
            try {
                const {
                    data: { companyList },
                } = yield call(getEnterpriseDropDownApi, companyNickname);

                yield put({
                    type: 'updateEnterpriseDropDownList',
                    enterpriseDropDownList: companyList,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateStaffList(state, { staffList, total }) {
            return {
                ...state,
                staffList,
                staffListTotal: total,
            };
        },
        updateEnterpriseDropDownList(state, { enterpriseDropDownList }) {
            return {
                ...state,
                enterpriseDropDownList,
            };
        },
    },
};
export default StaffModel;
