import { Button, Modal, Space, Table, Upload, message } from 'antd';
import { useState } from 'react';
import { IMG_URL } from '@/config/global';

import { setImportStaffApi, savebatchImportStaffApi } from '@/services/Enterprise/StaffApi';

const BatchImportStaffModal = (props: { visible: any; onClose: any; onRefresh: any }) => {
    const { visible, onClose, onRefresh } = props;
    const [uploadDataKey, setUploadDataKey] = useState('');
    const [tableData, setTableData] = useState([]);
    const [uploadLoading, setUploadLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const customRequest = (info) => {
        const { file } = info;
        const formData = new FormData();
        formData.append('file', file);
        setUploadLoading(true);
        setImportStaffApi(formData)
            .then((res) => {
                const { data = {}, ret } = res;
                if (ret === 200) {
                    const { staffCheckoutRequestVoList = [], successDataKey } = data;
                    setTableData(staffCheckoutRequestVoList);
                    setUploadDataKey(successDataKey);
                    message.success('上传成功');
                } else {
                    message.error('上传失败');
                }
            })
            .catch(() => {
                message.error('上传失败');
            })
            .finally(() => setUploadLoading(false));
    };
    const beforeUpload = (file: any) =>
        new Promise((res, rej) => {
            const isXls =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
            if (!isXls) {
                message.error('只支持.xls或.xlsx文件!');
                rej();
                return;
            }

            res();
        });
    const onFinish = () => {
        if (uploadDataKey) {
            setLoading(true);
            savebatchImportStaffApi(uploadDataKey)
                .then((res) => {
                    message.success('操作成功');
                    onRefresh();
                    onClose();
                })
                .finally(() => setLoading(false));
        } else {
            message.error('请先上传文件');
        }
    };
    const upLoadProps = {
        name: 'file',
        accept: '.xlsx',
        fileList: [],
        customRequest: customRequest,
        beforeUpload: beforeUpload,
    };
    const downLoadFile = () => {
        const a = document.createElement('a');
        a.setAttribute('href', `${IMG_URL}/static/excel/员工导入模板.xlsx`);
        a.setAttribute('id', 'ruleTemplate');
        // 防止反复添加
        if (document.getElementById('ruleTemplate')) {
            document.body.removeChild(document.getElementById('ruleTemplate'));
        }
        document.body.appendChild(a);
        a.click();
    };
    const columns = [
        {
            title: '企业简称',
            dataIndex: 'companyName',
        },
        {
            title: '员工姓名',
            dataIndex: 'staffName',
        },
        {
            title: '员工手机',
            dataIndex: 'mobile',
        },
        {
            title: '员工分组',
            dataIndex: 'staffGroupName',
        },
        {
            title: '支付方式',
            dataIndex: 'companyPayTypeName',
        },
        {
            title: '支付宝账号',
            dataIndex: 'companyPayTypeName',
        },
        {
            title: '账户类型',
            dataIndex: 'companyPayTypeName',
        },
        {
            title: '支付限额',
            dataIndex: 'paymentLimitTypeName',
        },
        {
            title: '额度',
            dataIndex: 'staffAmt',
        },
        {
            title: '订单多充',
            dataIndex: 'multiChargeFlagName',
        },
        {
            title: '校验状态',
            dataIndex: 'checkoutFlag',
            render: (text) => {
                return text ? '校验成功' : '校验失败';
            },
        },
        {
            title: '失败原因',
            dataIndex: 'failReason',
        },
    ];
    return (
        <Modal
            title="批量导入"
            width={1200}
            visible={visible}
            onCancel={onClose}
            onOk={() => onFinish()}
            // maskClosable={false}
            confirmLoading={loading}
            destroyOnClose
        >
            <Space size={100} style={{ marginBottom: 20 }}>
                <div>
                    下载模板：
                    <Button type="primary" onClick={() => downLoadFile()}>
                        下载模板
                    </Button>
                </div>
                <Space>
                    <span>上传文件：</span>
                    <Upload {...upLoadProps}>
                        <Button type="primary" loading={uploadLoading}>
                            上传
                        </Button>
                    </Upload>
                    <span> 支持扩展名：.xls .xlsx</span>
                </Space>
            </Space>
            <Table
                columns={columns}
                dataSource={tableData}
                pagination={{ pageSize: 10, total: tableData.length }}
            />
        </Modal>
    );
};

export default BatchImportStaffModal;
