import { Col, Form, Input, Select } from 'antd';
import React, { useState, useEffect, useMemo } from 'react';

import { getStallGroupListApi, getCompanyDropdownlistApi } from '@/services/Enterprise/StaffApi.js';
import { getOperCompanyListApi } from '@/services/Enterprise/EnterpriseManageApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

const FormItem = Form.Item;
const { Option } = Select;

const SearchLayout = (props: any) => {
    const {
        dispatch,
        form,
        onSubmit,
        onExport,
        onReset,
        listLoading,
        global: { codeInfo, operatorList = [] },
        currentUser,
        isCompany,
    } = props;

    const [groupList, changeGroupList] = useState<any[]>([]);

    const { companyBelongType: companyBelongTypeList } = codeInfo;

    const [formatCompanyList, changeFormatCompanyList] = useState<any[]>([]);

    const [companyList, updateCompanyList] = useState<any[]>([]);

    useEffect(() => {
        if (!isCompany) {
            if (!companyBelongTypeList) {
                dispatch({
                    type: 'global/initCode',
                    code: 'companyBelongType',
                });
            }
            if (operatorList.length == 0) {
                dispatch({
                    type: 'global/getOperatorList',
                    options: {},
                });
            }
        }
    }, [isCompany]);

    const enterpriseDropDownOptions = useMemo(() => {
        if (formatCompanyList) {
            return formatCompanyList.map((ele: any) => (
                <Option value={ele.companyId} key={ele.companyId}>
                    {ele.companyNickname || ele.companyName || ''}
                </Option>
            ));
        }
        return [];
    }, [formatCompanyList]);

    const onFinish = (values: any) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    useEffect(() => {
        changeFormatCompanyList(companyList);
    }, [companyList]);

    useEffect(() => {
        if (currentUser.operId) {
            initCompany(currentUser.operId);
        } else {
            if (isCompany) {
                getCompanyDropdownlistApi({ accountType: '01' }).then((res) => {
                    if (res && res.data && res.data.companyList) {
                        changeCompanyEvent(res.data.companyList[0].companyId);
                    }
                });
            }
            initCompany();
        }
    }, [currentUser, isCompany]);

    const initCompany = async (operId?: any) => {
        try {
            const {
                data: { operCompanyList },
            } = await getOperCompanyListApi({ operId });
            updateCompanyList(operCompanyList);
        } catch (error) {}
    };

    const handleSearch = (txt: any) => {
        if (txt) {
            const list: any[] = [];
            for (const item of companyList) {
                const searchName = item.companyNickname || item.companyName || '';
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            changeFormatCompanyList(list);
        } else {
            changeFormatCompanyList(companyList);
        }
    };

    const onClearEvent = () => {
        changeFormatCompanyList(companyList);
    };

    const handleFilter = () => true;

    const changeCompanyEvent = (companyId: any) => {
        initGroupList(companyId);
    };

    const initGroupList = async (companyId: any) => {
        try {
            const {
                data: { groupList: list },
            } = await getStallGroupListApi(companyId);
            changeGroupList(list);
        } catch (error) {}
    };

    const groupOptions = useMemo(() => {
        return groupList.map((ele) => {
            return (
                <Option value={ele.staffGroupId} key={ele.staffGroupId}>
                    {ele.staffGroupName}
                </Option>
            );
        });
    }, [groupList]);

    return (
        <Form form={form} onFinish={onFinish} initialValues={{}} scrollToFirstError>
            <SearchOptionsBar
                exportName="导出至暂存区"
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExport}
                minSpan={40}
            >
                <Col span={8}>
                    <FormItem label="员工姓名:" name="staffName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                {!isCompany && (
                    <Col span={8}>
                        <OperSelectTypeItem
                            label="合&nbsp;&nbsp;作&nbsp;&nbsp;方"
                            // onChange={(value: any) => {
                            // form.setFieldsValue({
                            //     companyId: '',
                            //     staffGroupId: '',
                            // });
                            // initCompany(value);
                            // }}
                            form={form}
                            isCompany
                            placeholder={'支持全称、简称模糊搜索'}
                        />
                    </Col>
                )}
                {!isCompany && (
                    <Col span={8}>
                        <FormItem label="企业简称:" name="companyId">
                            <Select
                                placeholder="请选择企业"
                                showSearch
                                onSearch={handleSearch}
                                filterOption={handleFilter}
                                onClear={onClearEvent}
                                onChange={changeCompanyEvent}
                            >
                                {enterpriseDropDownOptions}
                            </Select>
                        </FormItem>
                    </Col>
                )}
                <Col span={8}>
                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.companyId !== curValues.companyId
                        }
                    >
                        {({ getFieldValue }) => {
                            const companyId = getFieldValue('companyId');
                            let disabled = false;
                            if (!companyId && !isCompany) {
                                disabled = true;
                            }
                            return (
                                <FormItem label="员工分组:" name="staffGroupId">
                                    <Select disabled={disabled} placeholder="请选择" allowClear>
                                        {groupOptions}
                                    </Select>
                                </FormItem>
                            );
                        }}
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="手&nbsp;&nbsp;机&nbsp;&nbsp;号:" name="mobile">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="是否欠款:" name="isNoPay">
                        <Select placeholder="请选择">
                            <Option value="1">是</Option>
                            <Option value="0">否</Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
