import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    Form,
    Input,
    Select,
    Radio,
    Space,
    message,
    Divider,
    Checkbox,
    InputNumber,
    Modal,
} from 'antd';
import React, { Fragment, useState, useEffect, useMemo } from 'react';

import styles from '@/assets/styles/common.less';
import { addStallGroupApi, getStallGroupListApi } from '@/services/Enterprise/StaffApi.js';
import { getEnterpriseManageDetailApi } from '@/services/Enterprise/EnterpriseManageApi';
import { isEmpty } from '@/utils/utils';

import NumericInput from '@/components/NumericInput';
import { ACCOUNT_TYPE_ENUM, PAY_LIMIT_TYPE_ENUM } from '@/constants/enterprice';

const FormItem = Form.Item;
const { Option } = Select;

const inputOptions = {
    autoComplete: 'off',
    maxLength: 20,
};

const modelFormLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};
const { confirm } = Modal;
const EditForm = (props: any) => {
    const { onFinish, submitLoading, onCancel, editStaffInfo = {}, companyOptions } = props;
    const [editForm] = Form.useForm();

    const [groupList, changeGroupList] = useState<any[]>([]);
    const [groupName, changeGroupName] = useState<string>('');
    const [hasWithhold, changeHasWithhold] = useState<boolean>(false); // 是否可以代扣
    const [hasOffline, changeHasOffline] = useState<boolean>(false); // 是否可以代扣
    const [nowCompanyInfo, updateNowCompanyInfo] = useState<any>(null);

    useEffect(() => {
        if (editStaffInfo) {
            const params = {
                ...editStaffInfo,
            };
            params.companyPayType =
                editStaffInfo.companyPayType == '1' && editStaffInfo.companyStatus == '01';
            editForm.setFieldsValue(params);
            if (editStaffInfo.companyId) {
                changeCompanyEvent(editStaffInfo.companyId);
            }
        }
        return () => {
            updateNowCompanyInfo(null);
        };
    }, [editStaffInfo]);

    const changeCompanyEvent = (companyId: any) => {
        initCompanyInfo(companyId);
        initGroupList(companyId);
    };

    const initGroupList = async (companyId: any) => {
        try {
            const {
                data: { groupList: list },
            } = await getStallGroupListApi(companyId);
            for (const item of list) {
                if (
                    !editStaffInfo ||
                    (editStaffInfo && !editStaffInfo.companyId && item.isDefault == '1')
                ) {
                    editForm.setFieldsValue({ staffGroupId: item.staffGroupId });
                    break;
                }
            }
            changeGroupList(list);
        } catch (error) {}
    };

    const initCompanyInfo = async (companyId: any) => {
        try {
            const {
                data: { companyDetail },
            } = await getEnterpriseManageDetailApi(companyId);
            updateNowCompanyInfo(companyDetail);
            if (companyDetail.companyPayType === '1' && companyDetail.companyStatus == '01') {
                changeHasWithhold(true);
                changeHasOffline(false);
                if (editForm.getFieldValue('companyPayType')) {
                    editForm.setFieldsValue({
                        accountType: companyDetail.accountType,
                    });
                }
            } else if (
                companyDetail.offlineBalancePayType === '1' &&
                companyDetail.companyStatus == '01'
            ) {
                changeHasOffline(true);
                changeHasWithhold(false);
            } else {
                editForm.setFieldsValue({
                    companyPayType: false,
                    offlineBalancePayType: false,
                    paymentLimitType: undefined,
                    accountType: undefined,
                });
                changeHasWithhold(false);
                changeHasOffline(false);
            }
        } catch (error) {}
    };

    const groupOptions = useMemo(() => {
        return groupList.map((ele: any) => {
            return (
                <Option value={ele.staffGroupId} key={ele.staffGroupId}>
                    {ele.staffGroupName}
                </Option>
            );
        });
    }, [groupList]);

    const onEditFinish = (values: any) => {
        const params: any = {
            companyId: values.companyId,
            staffName: values.staffName,
            mobile: values.mobile,
            // 支付配置内容
            paymentLimit: values.paymentLimit,
            staffAmt: values.staffAmt,
            personPayType: '1',
            companyPayType: values.companyPayType ? '1' : '0',
            multiChargeFlag: values.multiChargeFlag,
        };
        if (values.companyPayType && hasWithhold) {
            params.accountType = values.accountType;
            params.alipayAccount = values.alipayAccount;
            params.alipayAccountType = values.alipayAccountType;
        }
        if (values.companyPayType && hasOffline) {
            params.paymentLimitType = values.paymentLimitType;
        }
        if (values.staffGroupId) {
            params.staffGroupId = values.staffGroupId;
        }

        if (editStaffInfo && editStaffInfo.staffId) {
            params.staffId = editStaffInfo.staffId;
        }
        if (
            editStaffInfo?.paymentLimitType &&
            editStaffInfo?.paymentLimitType !== values.paymentLimitType
        ) {
            confirm({
                title: '提醒',
                icon: <ExclamationCircleOutlined />,
                content: '支付限额进行调整后，将重新计算企业员工的余额支付限额和当前可用额度',
                onOk() {
                    onFinish(params);
                },
                onCancel() {
                    console.log('Cancel');
                },
            });
            return false;
        }
        onFinish(params);
    };

    // 取消
    const channleEvent = () => {
        onCancel();
    };

    const onNameChange = (event: any) => {
        changeGroupName(event.target.value);
    };

    const addGroupEvent = async (name: any) => {
        try {
            const companyId = editForm.getFieldValue('companyId');
            if (!companyId) {
                message.error('请先选择企业');
                return;
            }
            const params = {
                companyId,
                staffGroupName: name,
            };
            await addStallGroupApi(params);
            changeCompanyEvent(companyId);
            changeGroupName('');
        } catch (error) {}
    };

    return (
        <Form
            form={editForm}
            {...modelFormLayout}
            onFinish={onEditFinish}
            initialValues={{
                personPayType: '1',
            }}
            scrollToFirstError
        >
            <FormItem
                label="所属企业:"
                name="companyId"
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 12,
                    },
                }}
                rules={[{ required: true, message: '请选择' }]}
            >
                <Select placeholder="请选择" disabled={editStaffInfo} onChange={changeCompanyEvent}>
                    {companyOptions}
                </Select>
            </FormItem>
            <FormItem
                label="姓名:"
                name="staffName"
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 12,
                    },
                }}
                rules={[{ required: true, message: '请填写' }]}
            >
                <Input placeholder="请填写" autoComplete="off" maxLength={16} showCount />
            </FormItem>

            <FormItem
                label="手机号:"
                name="mobile"
                {...{
                    labelCol: {
                        span: 6,
                    },
                    wrapperCol: {
                        span: 12,
                    },
                }}
                rules={[{ required: true, message: '请填写' }]}
            >
                <NumericInput
                    placeholder="请填写"
                    disabled={editStaffInfo && editStaffInfo.staffId}
                    autoComplete="off"
                />
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.companyId !== curValues.companyId
                }
            >
                {({ getFieldValue }) => {
                    const companyId = getFieldValue('companyId');
                    let disabled = false;
                    if (!companyId) {
                        disabled = true;
                    }
                    return (
                        <FormItem
                            label="员工分组:"
                            name="staffGroupId"
                            {...{
                                labelCol: {
                                    span: 6,
                                },
                                wrapperCol: {
                                    span: 12,
                                },
                            }}
                        >
                            <Select
                                placeholder="请选择"
                                disabled={disabled}
                                dropdownRender={(menu) => (
                                    <div>
                                        {menu}
                                        <Divider style={{ margin: '4px 0' }} />
                                        <div
                                            style={{
                                                display: 'flex',
                                                flexWrap: 'nowrap',
                                                padding: 8,
                                            }}
                                        >
                                            <Input
                                                style={{ flex: 'auto' }}
                                                value={groupName}
                                                onChange={onNameChange}
                                            />
                                            <a
                                                style={{
                                                    flex: 'none',
                                                    padding: '8px',
                                                    display: 'block',
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() => {
                                                    addGroupEvent(groupName);
                                                }}
                                            >
                                                <PlusOutlined /> 新增分组
                                            </a>
                                        </div>
                                    </div>
                                )}
                            >
                                {groupOptions}
                            </Select>
                        </FormItem>
                    );
                }}
            </FormItem>

            <FormItem
                label="支付方式"
                wrapperCol={{
                    span: 18,
                }}
            >
                <FormItem name="personPayType" noStyle valuePropName="checked">
                    <Checkbox disabled>个人支付</Checkbox>
                </FormItem>
                {hasWithhold ? (
                    <FormItem name="companyPayType" noStyle valuePropName="checked">
                        {/* 签约中禁用 */}
                        <Checkbox
                            disabled={editStaffInfo && editStaffInfo.companyStatus == '02'}
                            onChange={(newFlag) => {
                                if (newFlag) {
                                    const accountType = editForm.getFieldValue('accountType');
                                    if (isEmpty(accountType)) {
                                        editForm.setFieldsValue({
                                            accountType:
                                                nowCompanyInfo.accountType ||
                                                ACCOUNT_TYPE_ENUM.ALIPAY,
                                        });
                                    }
                                }
                            }}
                        >
                            因公代付
                        </Checkbox>
                    </FormItem>
                ) : null}
                {hasOffline ? (
                    <FormItem name="companyPayType" noStyle valuePropName="checked">
                        {/* 签约中禁用 */}
                        <Checkbox>线下结算</Checkbox>
                    </FormItem>
                ) : null}
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.companyPayType !== curValues.companyPayType ||
                    prevValues.accountType !== curValues.accountType ||
                    prevValues.payLimitType !== curValues.payLimitType ||
                    prevValues.paymentLimitType !== curValues.paymentLimitType
                }
            >
                {({ getFieldValue }) => {
                    const companyPayTypeFlag = getFieldValue('companyPayType');
                    const accountType = getFieldValue('accountType');
                    let companyPayInfoFormItems = null;
                    if (accountType === ACCOUNT_TYPE_ENUM.ALIPAY) {
                        companyPayInfoFormItems = (
                            <Fragment>
                                <FormItem
                                    label="支付宝账号"
                                    name="alipayAccount"
                                    rules={[{ required: true, message: '请填写' }]}
                                >
                                    <Input
                                        disabled={
                                            editStaffInfo && editStaffInfo.succCompanyPay === true
                                        }
                                        placeholder="请填写"
                                        {...inputOptions}
                                        maxLength={50}
                                    />
                                </FormItem>
                                <FormItem label="代扣限额" required>
                                    <Space>
                                        <FormItem
                                            noStyle
                                            name="paymentLimit"
                                            rules={[{ required: true, message: '请填写' }]}
                                        >
                                            <Input
                                                type="number"
                                                max={
                                                    (nowCompanyInfo &&
                                                        nowCompanyInfo.companyAmt &&
                                                        Number(nowCompanyInfo.companyAmt)) ||
                                                    9999999
                                                }
                                                min={1}
                                                placeholder="请填写"
                                                {...inputOptions}
                                            />
                                        </FormItem>
                                        元/每月
                                    </Space>
                                </FormItem>
                            </Fragment>
                        );
                    } else if (accountType === ACCOUNT_TYPE_ENUM.PLATFORM) {
                        companyPayInfoFormItems = (
                            <Fragment>
                                <FormItem label="代扣限额" required>
                                    <Space>
                                        <FormItem
                                            noStyle
                                            name="staffAmt"
                                            rules={[{ required: true, message: '请填写' }]}
                                        >
                                            <Input
                                                max={9999999}
                                                min={1}
                                                placeholder="请填写"
                                                {...inputOptions}
                                            />
                                        </FormItem>
                                        元
                                    </Space>
                                </FormItem>
                            </Fragment>
                        );
                    }

                    if (companyPayTypeFlag && hasWithhold) {
                        return (
                            <Fragment>
                                <FormItem
                                    label="账户类型"
                                    name="accountType"
                                    rules={[{ required: true, message: '请填写' }]}
                                    initialValue={ACCOUNT_TYPE_ENUM.ALIPAY}
                                >
                                    <Radio.Group disabled>
                                        <Radio value={ACCOUNT_TYPE_ENUM.PLATFORM}>平台账户</Radio>
                                        <Radio value={ACCOUNT_TYPE_ENUM.ALIPAY}>支付宝</Radio>
                                    </Radio.Group>
                                </FormItem>
                                {companyPayInfoFormItems}
                            </Fragment>
                        );
                    } else if (companyPayTypeFlag && hasOffline) {
                        const paymentLimitType = getFieldValue('paymentLimitType');
                        return (
                            <Fragment>
                                <FormItem label="支付限额" required>
                                    <FormItem
                                        noStyle
                                        name="paymentLimitType"
                                        rules={[{ required: true, message: '请选择' }]}
                                    >
                                        <Radio.Group
                                            options={[
                                                {
                                                    label: '不限额',
                                                    value: PAY_LIMIT_TYPE_ENUM.NOT_LIMIT,
                                                },
                                                {
                                                    label: '按总额度限',
                                                    value: PAY_LIMIT_TYPE_ENUM.TOTAL,
                                                },
                                                {
                                                    label: '按月限额',
                                                    value: PAY_LIMIT_TYPE_ENUM.MONTHLY,
                                                },
                                            ]}
                                        />
                                    </FormItem>
                                </FormItem>
                                {(paymentLimitType === PAY_LIMIT_TYPE_ENUM.TOTAL ||
                                    paymentLimitType === PAY_LIMIT_TYPE_ENUM.MONTHLY) && (
                                    <FormItem
                                        label={
                                            paymentLimitType === PAY_LIMIT_TYPE_ENUM.TOTAL
                                                ? '总额度'
                                                : '月额度'
                                        }
                                        name="staffAmt"
                                        rules={[{ required: true, message: '请填写支付限额' }]}
                                    >
                                        <InputNumber
                                            max={1000000}
                                            min={1}
                                            precision={2}
                                            placeholder="请填写"
                                            {...inputOptions}
                                            addonAfter="元"
                                        />
                                    </FormItem>
                                )}

                                {companyPayTypeFlag && (
                                    <FormItem
                                        label="订单多充"
                                        name="multiChargeFlag"
                                        rules={[{ required: true }]}
                                        tooltip="开通后企业员工最多可同时发起50笔充电订单"
                                        initialValue={'1'}
                                    >
                                        <Radio.Group
                                            options={[
                                                { label: '不开通', value: '0' },
                                                { label: '开通', value: '1' },
                                            ]}
                                        />
                                    </FormItem>
                                )}
                            </Fragment>
                        );
                    } else {
                        return null;
                    }
                }}
            </FormItem>

            <div className={styles['form-submit']}>
                <Button
                    className={styles['form-btn']}
                    type="primary"
                    htmlType="submit"
                    loading={submitLoading}
                >
                    提交
                </Button>
                <Button className={styles['form-btn']} onClick={channleEvent}>
                    取消
                </Button>
            </div>
        </Form>
    );
};

export default EditForm;
