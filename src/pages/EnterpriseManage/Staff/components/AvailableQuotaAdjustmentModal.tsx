// import { setCompanyStaffApi } from '@/services/company/staffApi';
import { saveStaffApi } from '@/services/Enterprise/StaffApi';
import { Form, InputNumber, Modal, message } from 'antd';
import React, { useState } from 'react';
const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const AvailableQuotaAdjustmentModal = (props) => {
    const { record = {}, visible, onClose, onSuccess } = props;
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    const onFinish = (values: any) => {
        const params = {
            staffId: record?.staffId,
            paymentLimitType: record?.paymentLimitType,
            companyId: record?.companyId,
            staffName: record?.staffName,
            mobile: record?.mobile,
            addAmt: values?.addAmt,
        };
        setLoading(true);
        saveStaffApi(params)
            .then((res) => {
                message.success('保存成功');
                onSuccess();
            })
            .finally(() => {
                setLoading(false);
            });
    };
    return (
        <Modal
            title="可用额度调整"
            visible={visible}
            onCancel={onClose}
            onOk={() => form.submit()}
            // maskClosable={false}
            confirmLoading={loading}
            destroyOnClose
        >
            <Form form={form} {...layout} onFinish={onFinish} labelAlign="right">
                <Form.Item label="支付限额">{record?.paymentLimitTypeName}</Form.Item>
                <Form.Item label="当前可用额度">{record?.staffBalance}</Form.Item>
                <Form.Item
                    label="增加额度"
                    name="addAmt"
                    rules={[
                        { required: true },
                        {
                            pattern: /^0\.([1-9]|\d[1-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/,
                            message: '请输入大于0的数字,并且只保留两位小数',
                        },
                    ]}
                >
                    <InputNumber step={0.01} precision={2} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default AvailableQuotaAdjustmentModal;
