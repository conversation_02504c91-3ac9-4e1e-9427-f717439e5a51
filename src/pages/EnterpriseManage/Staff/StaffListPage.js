import { ExclamationCircleOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Form,
    Select,
    Modal,
    Tabs,
    Space,
    message,
    Upload,
    Result,
    Typography,
} from 'antd';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { connect } from 'umi';
import CacheAreaView from '@/components/CacheAreaView';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import {
    deleteStaffApi,
    quitStaffApi,
    recoveryStaffApi,
    saveStaffApi,
    importStaffApi,
    getStallByIdApi,
    exportStaffListApi,
} from '@/services/Enterprise/StaffApi.js';
import { COMPANY_STATUS } from '@/config/declare';
import { IMG_URL } from '@/config/global';

import TablePro from '@/components/TablePro';
import EditForm from './components/EditForm';
import SearchLayout from './components/SearchLayout';
import AvailableQuotaAdjustmentModal from './components/AvailableQuotaAdjustmentModal';
import BatchImportStaffModal from './components/BatchImportStaffModal';

const { TabPane } = Tabs;
const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const STATUS_TYPES = {
    ALL: '00',
    NORMAL: '01',
    QUIT: '02',
};

/**
 * 场站规则列表
 */
const StaffListPage = (props) => {
    const {
        currentUser,
        dispatch,
        history,
        match,
        route,
        global: { pageInit, codeInfo },
        staffModel: { staffList, staffListTotal, enterpriseDropDownList },
        listLoading,
    } = props;

    const {
        location: { pathname },
    } = history;
    const cacheRef = useRef();
    const isCompany = useMemo(() => {
        return currentUser?.roleList?.includes('company');
    }, [currentUser]);

    const [form] = Form.useForm();
    const [showEditView, toggleEditView] = useState(false); // 编辑弹窗状态
    const [showImportView, toggleImportView] = useState(false); // 导入弹窗状态
    const [editItem, changeEditItem] = useState(null); // 选中项
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [adjustmentVisible, setAdjustmentVisible] = useState(false);
    const [record, setRecord] = useState({});
    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: STATUS_TYPES.ALL,
        },
        props,
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        dispatch({
            type: 'staffModel/getEnterpriseDropDownList',
            companyNickname: '',
        });

        return () => {
            dispatch({
                type: 'staffModel/updateStaffList',
                staffList: [],
                total: 0,
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const companyOptions = useMemo(() => {
        return enterpriseDropDownList.map((ele) => {
            return (
                <Option value={ele.companyId} key={ele.companyId}>
                    {ele.companyNickname}
                </Option>
            );
        });
    }, [enterpriseDropDownList]);
    // 调用搜索接口
    const searchData = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                staffName: data.staffName,
                mobile: data.mobile,
                staffGroupId: data.staffGroupId,
                operId: data.operId,
                companyId: data.companyId,
                belongType: data.belongType,
                isNoPay: data.isNoPay,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.staffStatus = pageInfo.tabType;
            }

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'staffModel/getStaffList',
                options: params,
            });
        } catch (error) {}
    };

    // //导出
    const exportFormEvent = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params = {
                staffName: data.staffName,
                mobile: data.mobile,
                staffGroupId: data.staffGroupId,

                companyId: data.companyId,
                operId: data.operId,

                belongType: data.belongType,

                isNoPay: data.isNoPay,
            };
            if (pageInfo.tabType != STATUS_TYPES.ALL) {
                params.staffStatus = pageInfo.tabType;
            }

            cacheRef?.current?.apply(params).then(() => {
                cacheRef?.current?.count();
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    // 打开导入规则页面
    const openImportViewEvent = () => {
        toggleImportView(true);
    };

    const deleteStaffEvent = (item) => {
        confirm({
            title: `确定删除该员工?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await deleteStaffApi(item.staffId);
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const quitStaffEvent = (item) => {
        confirm({
            title: `员工离职`,
            icon: <ExclamationCircleOutlined />,
            content: '离职后，员工将不再享受企业优惠',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    const {
                        data: { haveOrder },
                    } = await quitStaffApi(item.staffId);
                    if (haveOrder) {
                        message.error('该员工已存在下单记录，无法删除');
                        return;
                    }
                    message.success('离职成功');
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const recoveryStaffEvent = (item) => {
        confirm({
            title: `确定恢复该员工?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await recoveryStaffApi(item.staffId);
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const columns = [
        {
            title: '员工分组',
            width: 160,
            dataIndex: 'staffGroupName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        ...(isCompany
            ? []
            : [
                  {
                      title: '合作方',
                      width: 140,
                      dataIndex: 'belongTypeName',
                      render(text, record) {
                          return (
                              <div className="text-line" style={{ width: '140px' }} title={text}>
                                  {text || ''}
                              </div>
                          );
                      },
                  },
                  {
                      title: '企业简称',
                      width: 140,
                      dataIndex: 'companyNickname',
                      render(text, record) {
                          return (
                              <div className="text-line" style={{ width: '140px' }} title={text}>
                                  {text || ''}
                              </div>
                          );
                      },
                  },
              ]),
        {
            title: '员工姓名',
            width: 140,
            dataIndex: 'staffName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '手机号',
            width: 100,
            dataIndex: 'mobile',

            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '支付限额',
            width: 140,
            dataIndex: 'paymentLimitTypeName',
            align: 'right',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '可用额度',
            width: 140,
            dataIndex: 'staffBalance',
            align: 'right',
            render: (text, record) => {
                return (
                    <Space>
                        {text}
                        {(record?.paymentLimitType === '1' || record?.paymentLimitType === '2') && (
                            <Typography.Link
                                onClick={() => {
                                    setAdjustmentVisible(true);
                                    setRecord(record);
                                }}
                            >
                                调整
                            </Typography.Link>
                        )}
                    </Space>
                );
            },
        },
        {
            title: '订单多充',
            width: 140,
            dataIndex: 'multiChargeFlagName',
            align: 'right',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '欠款金额',
            width: 140,
            dataIndex: 'noPayAmt',
            align: 'right',
            render(text, record) {
                return (
                    <div className="text-line" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '状态',
            width: 160,
            dataIndex: 'staffStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            fixed: 'right',
            width: 200,
            render: (text, record) => (
                <Space>
                    <span className={styles['table-btn']} onClick={() => editItemEvent(record)}>
                        编辑
                    </span>
                    {record.staffStatus == STATUS_TYPES.QUIT &&
                    record.companyStatus != COMPANY_STATUS.END &&
                    record.companyStatus != COMPANY_STATUS.DEL ? (
                        <span
                            className={styles['table-btn']}
                            onClick={() => recoveryStaffEvent(record)}
                        >
                            复职
                        </span>
                    ) : null}

                    {record.staffStatus == STATUS_TYPES.NORMAL ? (
                        <span
                            className={styles['table-btn']}
                            onClick={() => quitStaffEvent(record)}
                        >
                            离职
                        </span>
                    ) : null}

                    <span className={styles['table-btn']} onClick={() => deleteStaffEvent(record)}>
                        删除
                    </span>
                </Space>
            ),
        },
    ];

    // 新建
    const openAddWindowEvent = () => {
        toggleEditView(true);
    };

    // 编辑单个
    const editItemEvent = async (item) => {
        try {
            const { data } = await getStallByIdApi(item.staffId);
            changeEditItem(data);
            toggleEditView(true);
        } catch (error) {
            console.log(666, error);
        }
    };

    // 关闭规则配置页面
    const closeEditViewEvent = () => {
        toggleEditView(false);
        changeEditItem(null);
    };

    const onEditFinish = async (params) => {
        if (submitLoading) {
            return;
        }
        try {
            changeSubmitLoading(true);
            await saveStaffApi(params);
            searchData();
            message.success('保存成功');
            closeEditViewEvent();
        } catch (error) {
            console.log(444, error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    return (
        <PageHeaderWrapper
            extra={<CacheAreaView bizType="companyStaff" initRef={cacheRef} isMng />}
        >
            {/* 可用额度调整 */}
            {adjustmentVisible && (
                <AvailableQuotaAdjustmentModal
                    visible={adjustmentVisible}
                    record={record}
                    onClose={() => {
                        setRecord({});
                        setAdjustmentVisible(false);
                    }}
                    onSuccess={() => {
                        searchData();
                        setRecord({});
                        setAdjustmentVisible(false);
                    }}
                />
            )}
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    isCompany={isCompany}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExport={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button
                        className={styles['btn-item']}
                        type="primary"
                        onClick={openAddWindowEvent}
                    >
                        +新建
                    </Button>

                    {/* <Button className={styles['btn-item']} onClick={openImportViewEvent}>
                        导入员工
                    </Button> */}
                </div>
                <Tabs defaultActiveKey="1" onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={STATUS_TYPES.ALL} />
                    <TabPane tab="正常" key={STATUS_TYPES.NORMAL} />
                    <TabPane tab="已离职" key={STATUS_TYPES.QUIT} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.staffId}
                    dataSource={staffList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: staffListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
            <Modal
                title={editItem ? '编辑员工' : '新增员工'}
                width={1200}
                visible={showEditView}
                destroyOnClose
                onCancel={closeEditViewEvent}
                footer={null}
                maskClosable={false}
            >
                <EditForm
                    companyOptions={companyOptions}
                    loading={false}
                    submitLoading={submitLoading}
                    onFinish={onEditFinish}
                    editStaffInfo={editItem}
                    onCancel={closeEditViewEvent}
                />
            </Modal>
            {showImportView && (
                <BatchImportStaffModal
                    visible={showImportView}
                    onRefresh={searchData}
                    onClose={() => {
                        toggleImportView(false);
                    }}
                />
            )}
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, staffModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    staffModel,
    listLoading: loading.effects['staffModel/getStaffList'],
    importHistoryLoading: loading.effects['staffModel/getImportHistoryList'],
}))(StaffListPage);
