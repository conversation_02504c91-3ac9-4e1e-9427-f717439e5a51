import { useEffect } from 'react';
import { connect, Link } from 'umi';
import { Card, Col, Form, Input, Tabs, DatePicker, Tooltip } from 'antd';

import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import moment from 'moment';
import usePageState from '@/hooks/usePageState.js';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { InfoCircleOutlined } from '@ant-design/icons';

import TablePro from '@/components/TablePro';
import { ORDER_STATUS } from '@/config/declare';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const FormItem = Form.Item;
const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onExport, onReset } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(7, 'day'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} onExportForm={onExport}>
                <Col span={8}>
                    <FormItem label="选择日期:" name="dates" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单号:" name="stationName" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="未结清订单:" name="city" {...formItemLayout} />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

/**
 * 员工订单列表
 */
const StaffOrderListPage = (props) => {
    const {
        dispatch,
        history,
        match,
        route,
        profitRuleModel: { stationRuleList, stationRuleListTotal, editRuleInfo },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({
        tabType: ORDER_STATUS.ALL,
    });

    const { operId } = match.params;

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            operId,
            stationName: data.stationName,
            city: JSON.stringify(data.city),
        };
        if (pageInfo.tabType != ORDER_STATUS.ALL) {
            params.profitRuleType = pageInfo.tabType;
        }
        dispatch({
            type: 'profitRuleModel/getStationRuleList',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" title={text} style={{ width: '140px' }}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '订单创建时间',
            width: 160,
            dataIndex: 'effTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单号',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" title={text} style={{ width: '180px' }}>
                        <Link>{text}</Link>
                    </div>
                );
            },
        },
        {
            title: '充电量',
            width: 120,
            dataIndex: 'cityName',
            align: 'right',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '电费',
            width: 140,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '服务费',
            width: 140,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '附加费',
            width: 140,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    订单金额
                    <Tooltip title="订单原始金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    运营商活动优惠
                    <Tooltip title="运营商服务费减免">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 200,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <span>
                    企业结算金额
                    <Tooltip title="订单原始金额扣减企业所享受的优惠金额">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 200,
            align: 'right',
            dataIndex: 'profitRuleTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '支付方式',
            width: 140,
            dataIndex: 'ruleName',

            render(text, record) {
                return <div title={text}>{text}</div>;
            },
        },
        {
            title: '券抵扣金额',
            width: 140,
            dataIndex: 'ruleRelName',
            align: 'right',
            render(text, record) {
                return <div title={text}>{text}</div>;
            },
        },
        {
            title: '用户实付金额',
            width: 140,
            dataIndex: 'ruleRelName',
            align: 'right',
            render(text, record) {
                return <div title={text}>{text}</div>;
            },
        },

        {
            title: '未结清金额',
            width: 140,
            dataIndex: 'ruleRelName',
            align: 'right',
            render(text, record) {
                return <div title={text}>{text}</div>;
            },
        },

        {
            title: '订单状态',
            width: 160,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '订单渠道',
            width: 160,
            dataIndex: 'expTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 160,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <div className="text-line" title={text} style={{ width: '160px' }}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '充电站',
            width: 160,
            dataIndex: 'expTime',
            render(text, record) {
                return (
                    <div className="text-line" title={text} style={{ width: '160px' }}>
                        {text}
                    </div>
                );
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <SearchLayout
                    form={form}
                    operId={operId}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                />
                <Tabs defaultActiveKey="1" onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={ORDER_STATUS.ALL} />
                    <TabPane tab="待支付" key={ORDER_STATUS.UNPAID} />
                    <TabPane tab="待评价" key={ORDER_STATUS.UNASSESS} />
                    <TabPane tab="已完成" key={ORDER_STATUS.DONE} />
                    <TabPane tab="已取消" key={ORDER_STATUS.CANCEL} />
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={stationRuleList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationRuleListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ profitRuleModel, loading }) => ({
    profitRuleModel,
    listLoading: loading.effects['profitRuleModel/getStationRuleList'],
    importHistoryLoading: loading.effects['profitRuleModel/getImportHistoryList'],
}))(StaffOrderListPage);
