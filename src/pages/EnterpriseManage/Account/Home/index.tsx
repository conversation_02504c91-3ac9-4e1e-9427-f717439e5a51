import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Row, Space, Statistic, Tabs, Tooltip, Typography } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'umi';

import CompanySelect from '@/components/CompanySelect';
import ChargeAmtTabPage from './components/ChargeAmtTabPage';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { getEnterpriseAccountTotalApi } from '@/services/Enterprise/EnterpriseAccountApi';

const IndexPage = (props: any) => {
    const { currentUser } = props;
    const [companyForm] = Form.useForm();
    const [tabType, changeTabType] = useState<'AMT' | 'ELEC'>('AMT');

    const isCompany = useMemo(() => {
        return currentUser?.roleList?.includes('company');
    }, [currentUser]);

    const changeTabTypeEvent = (type: string) => {
        changeTabType(type as 'AMT' | 'ELEC');
    };
    const companyId = Form.useWatch('companyId', companyForm);

    const { run, data, loading } = useRequest(
        (id) => {
            return getEnterpriseAccountTotalApi(id);
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (companyId) {
            run(companyId);
        }
    }, [companyId]);

    return (
        <PageHeaderWrapper
            content={
                <Row gutter={16}>
                    <Col span={12}>
                        <Form form={companyForm} wrapperCol={{ span: 12 }}>
                            <Form.Item label="企业" name="companyId">
                                <CompanySelect accountType={'01'} disabled={isCompany} />
                            </Form.Item>
                        </Form>
                    </Col>
                </Row>
            }
        >
            <Card style={{ marginBottom: '16px' }}>
                <Row gutter={16}>
                    <Col span={8}>
                        <Statistic
                            title="累计充值金额"
                            value={data?.data?.rechargeAmt ?? '-'}
                            precision={2}
                            suffix="元"
                            loading={loading}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title="账户余额"
                            value={data?.data?.balance ?? '-'}
                            precision={2}
                            suffix="元"
                            loading={loading}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title={
                                <Space>
                                    <Typography.Text type="secondary">累计充电金额</Typography.Text>
                                    <Tooltip title="截止昨日">
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </Space>
                            }
                            value={data?.data?.chcAmt ?? '-'}
                            precision={2}
                            suffix="元"
                            loading={loading}
                        />
                    </Col>
                </Row>
            </Card>
            <Card>
                <Tabs activeKey={tabType} onChange={changeTabTypeEvent}>
                    <Tabs.TabPane tab="充值记录" key={'AMT'}>
                        <ChargeAmtTabPage companyId={companyId} />
                    </Tabs.TabPane>
                </Tabs>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, sallerModel, customerSystemModel, loading, user }: any) => ({
    global,
    sallerModel,
    customerSystemModel,
    user,
    currentUser: user.currentUser,
    eleLoading: loading.effects['sallerModel/getIcnChannelChargeEleList'],
}))(IndexPage);
