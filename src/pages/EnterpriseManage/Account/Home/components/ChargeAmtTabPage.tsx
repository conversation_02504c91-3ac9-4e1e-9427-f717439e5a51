import React, { Fragment, useEffect } from 'react';

import TablePro from '@/components/TablePro';
import { usePagination } from 'ahooks';
import { getEnterpriseRechargeListApi } from '@/services/Enterprise/EnterpriseAccountApi';

const ChargeAmtTabPage = (props: any) => {
    const { companyId } = props;

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current = 1, pageSize = 10 }) => {
            const response = await getEnterpriseRechargeListApi({
                companyId,
                pageSize,
                pageIndex: current,
            });
            if (response?.ret === 200) {
                return {
                    list: response?.data?.records,
                    current,
                    pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const tableColumns = [
        {
            title: '充值时间',
            width: 200,
            dataIndex: 'rechargeDate',
        },
        {
            title: '企业名称',
            width: 200,
            dataIndex: 'companyName',
        },
        {
            title: '充值金额',
            width: 140,
            dataIndex: 'rechargeAmt',
        },
    ];

    useEffect(() => {
        if (companyId) {
            searchList({ pageSize: 10, current: 1 });
        }
    }, [companyId]);

    return (
        <Fragment>
            <TablePro
                name="AmtTable"
                scroll={{ x: 'max-content' }}
                rowKey="hisId"
                columns={tableColumns}
                dataSource={listData?.list}
                loading={listLoading}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

export default ChargeAmtTabPage;
