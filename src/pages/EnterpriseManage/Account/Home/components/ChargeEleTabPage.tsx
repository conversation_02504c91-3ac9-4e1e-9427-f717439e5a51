import { Form, message } from 'antd';
import React, { Fragment, useEffect } from 'react';

import TablePro from '@/components/TablePro';
import SearchLayout from './SearchLayout';

import usePageState from '@/hooks/usePageState.js';
import { exportIcnChannelChargeEleListApi } from '@/services/SallerCenter/IcnApi';

const NO_TYPE = {
    CHANNEL: '1',
    ORDER: '2',
};

const TIME_TYPE = {
    START: '1',
    STOP: '2',
    ORDER: '3',
    SETTLE: '4',
};

const ChargeEleTabPage = (props: any) => {
    const {
        dispatch,
        sallerModel: { chargeEleList, chargeEleTotal },
        eleLoading,
        companyId,
        cacheRef,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);

    useEffect(() => {
        if (companyId) {
            searchData();
        }
    }, [pageInfo, companyId]);

    useEffect(() => {
        dispatch({
            type: 'sallerModel/updateProperty',
            params: {
                chargeEleList: [],
                chargeEleTotal: 0,
            },
        });
    }, [companyId]);

    // 调用搜索接口
    const searchData = async (isDownload = false) => {
        try {
            if (!companyId) {
                message.error('请先选择公司');
                return;
            }
            const data = form.getFieldsValue();
            const params = {
                ...data,
                orderNo: data.noType == NO_TYPE.ORDER ? data.no : undefined,
                channelOrderNo: data.noType == NO_TYPE.CHANNEL ? data.no : undefined,
                companyId,
                chargeBeginTime:
                    data.timeType == TIME_TYPE.START
                        ? data.timeRange?.[0]?.format('YYYY-MM-DD')
                        : undefined,
                chargeEndTime:
                    data.timeType == TIME_TYPE.START
                        ? data.timeRange?.[1]?.format('YYYY-MM-DD')
                        : undefined,

                chargeFinishBeginTime:
                    data.timeType == TIME_TYPE.STOP
                        ? data.timeRange?.[0]?.format('YYYY-MM-DD')
                        : undefined,
                chargeFinishEndTime:
                    data.timeType == TIME_TYPE.STOP
                        ? data.timeRange?.[1]?.format('YYYY-MM-DD')
                        : undefined,

                applyBeginTime:
                    data.timeType == TIME_TYPE.ORDER
                        ? data.timeRange?.[0]?.format('YYYY-MM-DD')
                        : undefined,
                applyEndTime:
                    data.timeType == TIME_TYPE.ORDER
                        ? data.timeRange?.[1]?.format('YYYY-MM-DD')
                        : undefined,

                settleBeginTime:
                    data.timeType == TIME_TYPE.SETTLE
                        ? data.timeRange?.[0]?.format('YYYY-MM-DD')
                        : undefined,
                settleEndTime:
                    data.timeType == TIME_TYPE.SETTLE
                        ? data.timeRange?.[1]?.format('YYYY-MM-DD')
                        : undefined,

                pageIndex: (isDownload == false && pageInfo.pageIndex) || undefined,
                pageSize: (isDownload == false && pageInfo.pageSize) || undefined,
                noType: undefined,
                no: undefined,
                timeType: undefined,
                timeRange: undefined,
            };

            if (isDownload) {
                await exportIcnChannelChargeEleListApi(params);
                cacheRef?.current?.count();
                message.success('提交成功，请到文件暂存区查看');
            } else {
                dispatch({
                    type: 'sallerModel/getIcnChannelChargeEleList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const tableColumns = [
        {
            title: '订单时间',
            width: 200,
            dataIndex: 'applyTime',
        },
        {
            title: '结算时间',
            width: 200,
            dataIndex: 'settleTime',
        },
        {
            title: '城市',
            width: 120,
            dataIndex: 'cityName',
        },
        {
            title: '充电站名称',
            width: 160,
            dataIndex: 'stationName',
        },
        {
            title: '平台单号',
            width: 160,
            dataIndex: 'orderNo',
        },
        {
            title: '渠道单号',
            width: 160,
            dataIndex: 'channelOrderNo',
        },
        {
            title: '充电电量',
            width: 120,
            dataIndex: 'chargePq',
        },
        {
            title: '订单总金额',
            width: 160,
            dataIndex: 'chargeAmt',
        },
        {
            title: '电费金额',
            width: 120,
            dataIndex: 'elecAmt',
        },
        {
            title: '服务费金额',
            width: 120,
            dataIndex: 'serviceAmt',
        },
        {
            title: '附加费金额',
            width: 120,
            dataIndex: 'incrementAmt',
        },
        {
            title: '结算金额',
            width: 120,
            dataIndex: 'settleAmt',
        },
        {
            title: '启动时间',
            width: 200,
            dataIndex: 'bgnTime',
        },
        {
            title: '停止时间',
            width: 200,
            dataIndex: 'endTime',
        },
        {
            title: '订单状态',
            width: 140,
            dataIndex: 'orderStatusName',
        },
    ];

    return (
        <Fragment>
            <SearchLayout
                {...props}
                form={form}
                onSubmit={() => {
                    searchData(false);
                }}
                onReset={resetData}
                onExportForm={() => {
                    searchData(true);
                }}
            />
            <TablePro
                name="EleTable"
                loading={eleLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record: any, index: number) => index}
                dataSource={chargeEleList}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: chargeEleTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

export default ChargeEleTabPage;
