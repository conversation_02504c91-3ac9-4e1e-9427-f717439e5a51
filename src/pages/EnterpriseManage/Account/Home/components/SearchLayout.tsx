import { Col, Form, Input, Select, Row, DatePicker } from 'antd';
import moment, { Moment } from 'moment';
import React, { useEffect, useState } from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

const formItemLayout = {};

const NO_TYPE = {
    CHANNEL: '1',
    ORDER: '2',
};

const TIME_TYPE = {
    START: '1',
    STOP: '2',
    ORDER: '3',
    SETTLE: '4',
};

const SearchLayout = (props: any) => {
    const {
        form,
        eleLoading,
        onSubmit,
        onReset,
        onExportForm,
        sallerModel: { commonOrderStatusList },
    } = props;

    const [dates, setDates] = useState<(Moment | null)[] | null>(null);
    const initTimeRange = [moment().subtract(7, 'day'), moment()];
    const [hackValue, setHackValue] = useState<any>();
    const [value, setValue] = useState<any>();
    const disabledDate = (current: Moment) => {
        if (!dates || dates?.length === 0) {
            return false;
        }
        const days = 40;
        if (dates[0] != null) {
            const tooLate = dates[0] && current.diff(dates[0], 'days') >= days;
            return tooLate;
        } else if (dates[1] != null) {
            const tooLate = current && dates[1].diff(current, 'days') >= days;
            return tooLate;
        }
        return false;
    };

    useEffect(() => {
        form.setFieldsValue({
            timeRange: hackValue || value || initTimeRange,
        });
    }, [value, hackValue]);

    const onOpenChange = (open: boolean) => {
        if (open) {
            setHackValue([]);
            setDates([]);
        } else {
            setHackValue(undefined);
        }
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={() => {
                onSubmit && onSubmit();
            }}
            scrollToFirstError
            initialValues={{ timeRange: initTimeRange }}
        >
            <SearchOptionsBar
                loading={eleLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                exportName={'导出至暂存区'}
            >
                <Col span={8}>
                    <FormItem label="编号">
                        <Row>
                            <Col span={6}>
                                <FormItem noStyle name="noType" initialValue={NO_TYPE.CHANNEL}>
                                    <Select>
                                        <Option value={NO_TYPE.CHANNEL}>渠道</Option>
                                        <Option value={NO_TYPE.ORDER}>订单</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={18}>
                                <FormItem noStyle name="no">
                                    <Input
                                        placeholder="请输入"
                                        autoComplete="off"
                                        allowClear
                                    ></Input>
                                </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="时间">
                        <Row>
                            <Col span={6}>
                                <FormItem noStyle name="timeType" initialValue={TIME_TYPE.SETTLE}>
                                    <Select>
                                        <Option value={TIME_TYPE.ORDER}>订单</Option>
                                        <Option value={TIME_TYPE.SETTLE}>结算</Option>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col span={18}>
                                <FormItem noStyle name="timeRange">
                                    <RangePicker
                                        format="YYYY-MM-DD"
                                        allowClear={false}
                                        disabledDate={disabledDate}
                                        onCalendarChange={(val) => setDates(val)}
                                        onChange={(val) => setValue(val)}
                                        onOpenChange={onOpenChange}
                                    />
                                </FormItem>
                            </Col>
                        </Row>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="订单状态" name="status" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {commonOrderStatusList?.map((ele: any) => {
                                return (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
