import { ExclamationCircleOutlined } from '@ant-design/icons';
import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Form, Modal, Space, Tabs, Typography } from 'antd';
import { useEffect, useState, useMemo, useRef } from 'react';
import { connect } from 'umi';
import CacheAreaView from '@/components/CacheAreaView';
import manageStyles from '../style.less';
import styles from '@/assets/styles/common.less';
import {
    updateCompanyStatusApi,
    getCompanySignQrcodeApi,
    exportEnterpriseManageListApi,
} from '@/services/Enterprise/EnterpriseManageApi';
import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import RechargeModal from './components/RechargeModal';
import usePageState from '@/hooks/usePageState';
import { COMPANY_STATUS } from '@/config/declare';
import { ACCOUNT_TYPE_ENUM } from '@/constants/enterprice';

const { TabPane } = Tabs;
const { confirm } = Modal;

const ManageListPage = (props: any) => {
    const {
        dispatch,
        history,
        currentUser,
        global: { pageInit, codeInfo },
        enterpriseManage: { enterpriseManageList, enterpriseManageListTotal },
        listLoading,
    } = props;

    const { companyStatus: companyStatusList } = codeInfo;
    const cacheRef = useRef();

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();
    const [showSignWindow, toggleSignWindow] = useState<boolean>(false);
    const [curSignInfo, changeCurSignInfo] = useState<any>(null);
    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: COMPANY_STATUS.ALL,
        },
        props,
    );
    const rechargeRef = useRef<ActionRef.OpenModal>();

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }

        if (!companyStatusList) {
            dispatch({
                type: 'global/initCode',
                code: 'companyStatus',
            });
        }

        return () => {
            dispatch({
                type: 'enterpriseManage/updateEnterpriseManageList',
                enterpriseManageList: [],
                total: 0,
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const companyStatusOptions = useMemo(() => {
        if (companyStatusList) {
            return companyStatusList.map((ele: API.CodeType) => (
                <TabPane tab={ele.codeName} key={ele.codeValue} />
            ));
        }
        return [];
    }, [companyStatusList]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params: any = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                companyName: data.companyName,
                mobile: data.mobile,
                belongType: data.belongType,
                contacts: data.contacts,
                companyNickname: data.companyNickname,
                operId: data.operId,
                companyId: data.companyId,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                companyPayType: data.companyPayType,
                accountType: data.accountType,
            };
            if (params.accountType === ACCOUNT_TYPE_ENUM.OFFLINE) {
                params.offlineBalancePayType = '1';
                params.accountType = undefined;
            }
            if (pageInfo.tabType !== COMPANY_STATUS.ALL) {
                params.companyStatus = pageInfo.tabType;
            }

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'enterpriseManage/getEnterpriseManageList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type: string) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const openSignView = async (item: any) => {
        try {
            const {
                data: { signQrCode },
            } = await getCompanySignQrcodeApi(item.companyId);
            changeCurSignInfo(`data:image/jpeg;base64,${signQrCode}`);
            toggleSignWindow(true);
        } catch (error) {
            console.log(77777, error);
        }
    };

    const closeSignWindowEvent = () => {
        toggleSignWindow(false);
        changeCurSignInfo(null);
    };

    const startEnterpriseManageEvent = async (item: any) => {
        confirm({
            title: `确定启用该企业?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await updateCompanyStatusApi({
                        companyId: item.companyId,
                        companyStatus: COMPANY_STATUS.DOING,
                    });
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const stopEnterpriseManageEvent = async (item: any) => {
        confirm({
            title: `确定停止该企业?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await updateCompanyStatusApi({
                        companyId: item.companyId,
                        companyStatus: COMPANY_STATUS.END,
                    });
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const deleteEnterpriseManageEvent = async (item: any) => {
        confirm({
            title: `确定删除该企业?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await updateCompanyStatusApi({
                        companyId: item.companyId,
                        companyStatus: COMPANY_STATUS.DEL,
                    });
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // //导出
    const exportFormEvent = async () => {
        try {
            await form.validateFields();
            const data = form.getFieldsValue();
            const params: any = {
                companyName: data.companyName,
                mobile: data.mobile,
                belongType: data.belongType,
                contacts: data.contacts,
                companyNickname: data.companyNickname,
                operId: data.operId,
                companyId: data.companyId,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                companyPayType: data.companyPayType,
                accountType: data.accountType,
            };
            if (data.accountType === ACCOUNT_TYPE_ENUM.OFFLINE) {
                params.offlineBalancePayType = '1';
                params.accountType = undefined;
            }
            if (pageInfo.tabType !== COMPANY_STATUS.ALL) {
                params.companyStatus = pageInfo.tabType;
            }
            cacheRef?.current?.apply(params).then(() => {
                cacheRef?.current?.count();
            });
        } catch (error) {}
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        history.push('/userCenter/enterprise/manage/list/add');
    };

    const editEnterpriseManageEvent = (item: any) => {
        history.push(`/userCenter/enterprise/manage/list/update/${item.companyId}`);
    };

    const lookEnterpriseManageEvent = (item: any) => {
        history.push(`/userCenter/enterprise/manage/list/details/${item.companyId}`);
    };

    const showRechargeModal = (item: any) => {
        rechargeRef.current?.open(item);
    };

    const belongTypeColumns = [];

    if (!currentUser.operId) {
        belongTypeColumns.push({
            title: '合作方',
            width: 140,
            dataIndex: 'belongTypeName',
            render(text: string) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        });
    }

    const columns = [
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
        },
        ...belongTypeColumns,
        {
            title: '企业名称',
            width: 160,
            dataIndex: 'companyName',
            render(text: string) {
                return (
                    <div className="text-line" style={{ width: '160px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '企业简称',
            width: 140,
            dataIndex: 'companyNickname',
            render(text: string) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '累计充电量',
            width: 120,
            dataIndex: 'chargePqTotal',
        },
        {
            title: '累计充电费',
            width: 120,
            dataIndex: 'realElecAmtTotal',
        },
        {
            title: '累计服务费',
            width: 120,
            dataIndex: 'realServiceAmtTotal',
        },
        {
            title: '累计附加费',
            width: 120,
            dataIndex: 'realIncrementAmtTotal',
        },
        {
            title: '联系人',
            width: 140,
            dataIndex: 'contacts',
            render(text: string) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '联系人手机号',
            width: 200,
            dataIndex: 'mobile',
        },
        {
            title: '代扣账户',
            width: 200,
            dataIndex: 'accountType',
            render(text: string, record: any) {
                if (
                    record.companyPayType === '1' &&
                    record.accountType === ACCOUNT_TYPE_ENUM.ALIPAY
                ) {
                    return <span>支付宝</span>;
                } else if (
                    record.companyPayType === '1' &&
                    record.accountType === ACCOUNT_TYPE_ENUM.PLATFORM
                ) {
                    return <span>平台账户</span>;
                } else if (record.offlineBalancePayType === '1') {
                    return <span>线下结算</span>;
                }
                return '-';
            },
        },
        {
            title: '累计充值金额',
            dataIndex: 'companyAmt',
            width: 120,
        },
        {
            title: '累计消费金额',
            dataIndex: 'useAmt',
            width: 120,
        },
        {
            title: '账户余额',
            dataIndex: 'balance',
            render: (value: string, record: any) => {
                return record.offlineBalancePayType === '1' ||
                    (record.companyPayType === '1' &&
                        record.accountType === ACCOUNT_TYPE_ENUM.PLATFORM)
                    ? value
                    : '-';
            },
            width: 120,
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'companyStatusName',
            render(text: string, record: any) {
                return (
                    <span
                        title={text}
                        className={styles['table-btn']}
                        onClick={() => openSignView(record)}
                    >
                        {text}
                    </span>
                );
            },
        },

        {
            title: '操作',
            width: 240,
            fixed: 'right',
            render: (text: string, record: any) => {
                return (
                    <Space>
                        <Typography.Link onClick={() => lookEnterpriseManageEvent(record)}>
                            详情
                        </Typography.Link>
                        {record.companyStatus !== COMPANY_STATUS.DEL && (
                            <Typography.Link onClick={() => editEnterpriseManageEvent(record)}>
                                编辑
                            </Typography.Link>
                        )}
                        {record.companyStatus === COMPANY_STATUS.END && (
                            <Typography.Link onClick={() => startEnterpriseManageEvent(record)}>
                                启用
                            </Typography.Link>
                        )}
                        {record.companyStatus !== COMPANY_STATUS.DEL &&
                            record.companyStatus !== COMPANY_STATUS.END && (
                                <Typography.Link onClick={() => stopEnterpriseManageEvent(record)}>
                                    停用
                                </Typography.Link>
                            )}
                        {record.companyStatus !== COMPANY_STATUS.DEL && (
                            <Typography.Link onClick={() => deleteEnterpriseManageEvent(record)}>
                                删除
                            </Typography.Link>
                        )}
                        {record.companyStatus === COMPANY_STATUS.DOING &&
                            record.offlineBalancePayType === '1' && (
                                <Typography.Link onClick={() => showRechargeModal(record)}>
                                    充值
                                </Typography.Link>
                            )}
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper
            title="企业信息"
            extra={<CacheAreaView bizType="company" initRef={cacheRef} isMng />}
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state: any) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={exportFormEvent}
                />
                <div className={styles['btn-bar']}>
                    <Button type="primary" onClick={gotoAddEvent}>
                        新建
                    </Button>
                </div>
                <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={COMPANY_STATUS.ALL} />
                    {companyStatusOptions}
                </Tabs>

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="companyId"
                    dataSource={enterpriseManageList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: enterpriseManageListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
                <Modal
                    visible={showSignWindow}
                    title="签约流程"
                    width={750}
                    onCancel={closeSignWindowEvent}
                    footer={null}
                    maskClosable={false}
                >
                    <div className={manageStyles['sign-view']}>
                        {curSignInfo ? (
                            <div className={manageStyles['code-view']}>
                                <img src={curSignInfo}></img>
                            </div>
                        ) : null}
                        <div className={manageStyles['info-view']}>
                            <p className={manageStyles.info}>1.打开支付宝APP</p>
                            <p className={manageStyles.info}>2.使用代付支付宝账号登录</p>
                            <p className={manageStyles.info}>3.扫一扫二维码,按指示完成签约</p>
                            <br></br>
                            <p className={manageStyles.mark}>注意事项:</p>
                            <p className={manageStyles.mark}>·签约期间不可使用因公代付</p>
                        </div>
                    </div>
                </Modal>
            </Card>
            <RechargeModal
                ref={rechargeRef}
                callback={() => {
                    searchData();
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, enterpriseManage, loading }: any) => ({
    global,
    currentUser: user.currentUser,
    enterpriseManage,
    listLoading: loading.effects['enterpriseManage/getEnterpriseManageList'],
}))(ManageListPage);
