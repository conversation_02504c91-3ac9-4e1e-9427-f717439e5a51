import { companyRechargeApi } from '@/services/MngCustApi';
import { useRequest } from 'ahooks';
import { Form, Input, InputNumber, message, Modal, Radio, Space, Typography } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

const RechargeModal = (props: any, ref: any) => {
    const { callback } = props;
    const [currentEnterprice, setCurrentEnterprice] = useState<any>();
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        open: (record: any) => {
            setCurrentEnterprice(record);
            setVisible(true);
        },
    }));

    const onCancel = () => {
        form.resetFields();
        setCurrentEnterprice(undefined);
        setVisible(false);
    };

    const { run, loading } = useRequest(
        (params) => {
            return companyRechargeApi(params);
        },
        {
            manual: true,
            onSuccess(res: any) {
                if (res.ret === 200) {
                    message.success('充值成功');
                    callback?.();
                    onCancel();
                }
            },
        },
    );

    const onOk = async () => {
        try {
            await form.validateFields();
            const formData = form.getFieldsValue(true);
            Modal.confirm({
                title: `确定要给${currentEnterprice?.companyNickname}充值${formData.rechargeAmt}元${
                    formData.sendFlag ? `，并赠送${formData.giftAmt}元` : ''
                }吗？`,
                onOk() {
                    form.submit();
                },
            });
        } catch (error) {}
    };

    const onSubmit = (formData: any) => {
        run({ ...formData, companyId: currentEnterprice?.companyId, sendFlag: undefined });
    };

    return (
        <Modal
            visible={visible}
            onCancel={onCancel}
            title="企业充值"
            cancelText="取消"
            okText="确定"
            okButtonProps={{ loading }}
            onOk={onOk}
        >
            <Form
                form={form}
                onFinish={onSubmit}
                labelCol={{ flex: '0 0 110px' }}
                labelAlign="right"
            >
                <Form.Item label="企业全称">
                    <Typography.Text>{currentEnterprice?.companyName}</Typography.Text>
                </Form.Item>
                <Form.Item label="渠道简称">
                    <Typography.Text>{currentEnterprice?.companyNickname}</Typography.Text>
                </Form.Item>
                <Form.Item
                    label="充值金额"
                    name="rechargeAmt"
                    required
                    rules={[
                        {
                            required: true,
                            message: '请输入充值金额',
                        },
                    ]}
                    wrapperCol={{ span: 8 }}
                >
                    <InputNumber precision={2} min={-1000000} max={1000000} addonBefore="¥" />
                </Form.Item>
                <Form.Item label="本次赠送金额" name="sendFlag" initialValue={false}>
                    <Radio.Group
                        options={[
                            {
                                label: '无赠送',
                                value: false,
                            },
                            {
                                label: '有赠送',
                                value: true,
                            },
                        ]}
                    />
                </Form.Item>
                <Form.Item noStyle shouldUpdate={(pre, cur) => pre.sendFlag !== cur.sendFlag}>
                    {({ getFieldValue }) => {
                        const sendFlag = getFieldValue('sendFlag');
                        return (
                            sendFlag && (
                                <Form.Item
                                    label=" "
                                    colon={false}
                                    name="giftAmt"
                                    requiredMark={false}
                                    required={false}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入赠送金额',
                                        },
                                    ]}
                                    wrapperCol={{ span: 8 }}
                                >
                                    <InputNumber
                                        precision={2}
                                        min={0.01}
                                        max={1000000}
                                        addonAfter="元"
                                    />
                                </Form.Item>
                            )
                        );
                    }}
                </Form.Item>
                <Form.Item label="充值说明" name="remark">
                    <Input.TextArea
                        maxLength={100}
                        showCount
                        allowClear
                        rows={3}
                        placeholder="请输入充值说明"
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(RechargeModal);
