import { Col, Form, Select, Input, DatePicker } from 'antd';
import moment from 'moment';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import { ACCOUNT_TYPE_ENUM } from '@/constants/enterprice';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;

const SearchLayout = (props: any) => {
    const { form, listLoading, onSubmit, onReset, onExportForm } = props;

    const onFinish = (values: any) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(365, 'day'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                exportName="导出至暂存区"
                onReset={resetForm}
                onExportForm={onExportForm}
                minSpan={40}
            >
                <Col span={8}>
                    <FormItem label="企业名称:" name="companyName">
                        <Input placeholder="请填写企业名称" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem
                        label="合&nbsp;&nbsp;作&nbsp;&nbsp;方"
                        form={form}
                        isCompany
                        placeholder={'支持全称、简称模糊搜索'}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="手&nbsp;&nbsp;机&nbsp;&nbsp;号:" name="mobile">
                        <Input placeholder="请填写手机号" autoComplete="off" />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="联&nbsp;&nbsp;系&nbsp;&nbsp;人:" name="contacts">
                        <Input autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="企业简称:" name="companyNickname">
                        <Input placeholder="请填写企业简称" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="创建日期:" name="dates">
                        <RangePicker
                            placeholder={['请选择开始时间', '请选择结束时间']}
                            format="YYYY-MM-DD"
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="代扣账户:" name="accountType">
                        <Select placeholder="请选择" allowClear>
                            <Option value={ACCOUNT_TYPE_ENUM.PLATFORM}>平台账户</Option>
                            <Option value={ACCOUNT_TYPE_ENUM.ALIPAY}>支付宝</Option>
                            <Option value={ACCOUNT_TYPE_ENUM.OFFLINE}>线下结算</Option>
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
