import {
    LeftOutlined,
    InfoCircleOutlined,
    MinusCircleOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Row,
    Select,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    TreeSelect,
    Cascader,
    Spin,
    Switch,
} from 'antd';
import moment from 'moment';
import { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';
import { connect } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import styles from './style.less';
import { isEmail, isPhoneNumber } from '@/utils/verify';

import { saveEnterpriseManageApi } from '@/services/Enterprise/EnterpriseManageApi';
import {
    getCityAndStationByOperIdApi,
    getCityListApi,
    getAllProvinceAndCityApi,
} from '@/services/CommonApi';
import { initMainOperatorsApi, initMainOperator<PERSON>tation<PERSON><PERSON> } from '@/services/MngAstApi';
import { getOperatorListByCityApi } from '@/services/OperationMng/OperationMngApi';

import TablePro from '@/components/TablePro';
import OperSelectItem from '@/components/OperSelectItem';
import NumericInput from '@/components/NumericInput';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';

import { emailReg } from '@/utils/verify';
import { isEmpty } from '@/utils/utils';
import { ACCOUNT_TYPE_ENUM, COMPANY_BELONG_TYPE_ENUM } from '@/constants/enterprice';

const { Option } = Select;
const { SHOW_CHILD } = TreeSelect;
const FormItem = Form.Item;

const partnerCode = 'belongType';

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
    wrapperCol: {
        span: 8,
    },
};

const CAPITALTYPS = {
    ALL: '1', // 全部
    OPERATOR: '2', // 运营商
    CITY: '3', // 城市
};

const inputOptions = {
    autoComplete: 'off',
    maxLength: 20,
};

/**
 * 基本信息层
 */
const BasicInfoView = (props) => {
    const {
        form,
        companyId,
        dispatch,
        formDisabled,
        cityTreeData = [],
        enterpriseManage: {
            editActInfo: { companyDetail = {} },
        },

        currentUser,
        discountListRef,
        global: { codeInfo },
    } = props;
    const [addressOption, changeAddressOption] = useState([]); // 地址级联框数据

    const companyBelongType = 'companyBelongType';

    const { companyBelongType: companyBelongTypeList } = codeInfo;
    const [operatorList, changeOperatorList] = useState([]);

    const initOperatorList = async (city) => {
        try {
            const { data } = await initMainOperatorsApi({ city, filerXingXingOperator: '1' });
            let arr = data?.filter((value) => value.operId !== 'MA1MY0GF9'); // 当合作方为运营商时，运营商下拉框过滤掉云快充主ID
            changeOperatorList(arr);
        } catch (error) {}
    };

    useEffect(() => {
        if (!companyBelongTypeList) {
            dispatch({
                type: 'global/initCode',
                code: companyBelongType,
            });
        }

        if (operatorList.length == 0) {
            initOperatorList();
        }
    }, []);

    useEffect(() => {
        initAddressData();
    }, [companyDetail, cityTreeData]);

    const companyBelongTypeOptions = useMemo(() => {
        if (companyBelongTypeList) {
            return companyBelongTypeList.map((ele, index) => {
                if (ele.codeValue == '0') {
                    if (currentUser.operId) {
                        return null;
                    }
                    return (
                        <Radio value={ele.codeValue} key={index}>
                            {ele.codeName}
                            <Tooltip title="与平台合作的企业，订单中的企业优惠由平台承担">
                                <InfoCircleOutlined />
                            </Tooltip>
                        </Radio>
                    );
                }
                if (ele.codeValue == '1') {
                    return (
                        <Radio key={ele.codeValue} value={ele.codeValue}>
                            {ele.codeName}
                        </Radio>
                    );
                }
                return (
                    <Radio key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Radio>
                );
            });
        }
        return [];
    }, [companyBelongTypeList]);

    const initAddressData = async () => {
        if (cityTreeData?.length) {
            if (
                companyDetail?.companyProvince &&
                companyDetail?.companyCity &&
                companyDetail?.companyCounty
            ) {
                // 初始化级联框的省数据
                let provinceData = addressOption;
                if (!provinceData?.length) {
                    provinceData = cityTreeData.map((item, index) => ({
                        label: item.areaName,
                        value: item.areaCode,
                        isLeaf: false,
                    }));
                }
                // 经营地址级联框的数据
                // 市

                const {
                    data: { areaList: cityList },
                } = await getAllProvinceAndCityApi({
                    upAreaCode: companyDetail.companyProvince,
                });
                const cityData = cityList.map((item, index) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                const index = provinceData.findIndex(
                    (val) => companyDetail.companyProvince == val.value,
                );
                if (index > -1 && provinceData[index]) {
                    provinceData[index].children = cityData;
                }
                // 县
                const {
                    data: { areaList: countyList },
                } = await getAllProvinceAndCityApi({ upAreaCode: companyDetail.companyCity });
                const countyData = countyList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: true,
                }));
                const index2 = cityData.findIndex((val) => val.value == companyDetail.companyCity);
                if (index2 > -1 && provinceData[index] && provinceData[index]?.children[index2]) {
                    provinceData[index].children[index2].children = countyData;
                }
                if (provinceData.length != 0) {
                    changeAddressOption([...provinceData]);
                }
            } else if (!addressOption?.length) {
                // 初始化级联框的省数据
                const provinceData = cityTreeData.map((item, index) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                changeAddressOption(provinceData);
            }
        }
    };
    // 级联框懒加载数据
    const loadAddressData = async (selectedOptions) => {
        if (selectedOptions.length < 3) {
            const targetOption = selectedOptions[selectedOptions.length - 1];
            targetOption.loading = true;

            const {
                data: { areaList },
            } = await getAllProvinceAndCityApi({
                upAreaCode: targetOption.value,
            });
            targetOption.loading = false;
            const children = areaList;
            const isLeaf = selectedOptions.length == 2;
            const dealData = children.map((item, index) => ({
                label: item.areaName,
                value: item.areaCode,
                isLeaf,
            }));
            targetOption.children = dealData;

            changeAddressOption([...addressOption]);
        }
    };

    return (
        <Card
            title={
                <span>
                    <span className={styles.formTitle}>基础信息</span>
                </span>
            }
            style={{ paddingTop: '20px' }}
            id="baseInfo"
            bordered={false}
        >
            <FormItem
                label={<span>企业名称</span>}
                name="companyName"
                {...formItemLayout}
                rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
            >
                <Input
                    disabled={formDisabled}
                    maxLength={20}
                    placeholder="请填写"
                    autoComplete="off"
                    {...inputOptions}
                />
            </FormItem>
            <FormItem
                label={<span>企业简称</span>}
                name="companyNickname"
                {...formItemLayout}
                rules={[{ required: true, whitespace: true, message: '请填写标题名称' }]}
            >
                <Input
                    disabled={formDisabled}
                    placeholder="请填写"
                    autoComplete="off"
                    {...inputOptions}
                    maxLength={5}
                />
            </FormItem>
            <FormItem
                label={<span>合作方</span>}
                name={partnerCode}
                {...formItemLayout}
                rules={[{ required: true, whitespace: true, message: '请填写活动名称' }]}
            >
                <Radio.Group
                    placeholder="请选择"
                    disabled={companyId || formDisabled}
                    onChange={() => {
                        form.setFieldsValue({ scopRules: [] });
                        discountListRef.current.rest();
                    }}
                >
                    {companyBelongTypeOptions}
                </Radio.Group>
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues[partnerCode] !== curValues[partnerCode]
                }
            >
                {({ getFieldValue }) => {
                    const partnerValue = getFieldValue(partnerCode);
                    return partnerValue == '1' && !currentUser.operId ? (
                        <OperSelectItem
                            label="运营商"
                            name="operId"
                            rules={[
                                {
                                    required: true,
                                    message: '请选择运营商',
                                },
                            ]}
                            operatorList={operatorList}
                            disabled={companyId || formDisabled}
                        />
                    ) : null;
                }}
            </FormItem>
            <Form.Item name="address" label="经营地址：" {...formItemLayout}>
                <Cascader
                    options={addressOption}
                    loadData={loadAddressData}
                    disabled={formDisabled}
                    placeholder="请选择"
                />
            </Form.Item>
            <Form.Item name="companyAddress" label="地址详情：" {...formItemLayout}>
                <Input
                    placeholder="请填写地址详情"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={100}
                />
            </Form.Item>

            <Form.Item
                name="contacts"
                label="联系人："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请填写',
                    },
                ]}
            >
                <Input placeholder="请填写" disabled={formDisabled} {...inputOptions} />
            </Form.Item>
            <Form.Item
                name="mobile"
                label="联系人手机号："
                {...formItemLayout}
                rules={[
                    {
                        required: true,
                        message: '请填写',
                    },
                    {
                        pattern: /^\d{11}$/,
                        message: '手机号格式不正确',
                    },
                ]}
            >
                <NumericInput
                    placeholder="请填写"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={11}
                />
            </Form.Item>
            <Form.Item
                name="contactEmail"
                label="通知邮箱"
                rules={[
                    {
                        required: true,
                        message: '请填写',
                    },
                    {
                        pattern: emailReg,
                        message: '邮箱格式不正确',
                    },
                ]}
            >
                <Input
                    placeholder="请填写邮箱地址"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={32}
                />
            </Form.Item>
            <Form.Item label="管理员手机号" required>
                <Form.List
                    name="alipayManageAccount"
                    rules={[
                        { min: 1, type: 'array', message: '最少1条数据' },
                        { max: 20, type: 'array', message: '最多20条数据' },
                    ]}
                >
                    {(fields, { add, remove }, { errors }) => (
                        <>
                            {fields.map(({ key, name, ...field }) => (
                                <Row key={field.key} style={{ width: '100%' }} gutter={20}>
                                    <Col span={20}>
                                        <Form.Item
                                            {...field}
                                            name={[name]}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请填写',
                                                },
                                                {
                                                    pattern: /^\d{11}$/,
                                                    message: '手机号格式不正确',
                                                },
                                            ]}
                                        >
                                            <Input
                                                placeholder="请填写手机号"
                                                disabled={formDisabled}
                                                {...inputOptions}
                                                maxLength={11}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span={4}>
                                        <MinusCircleOutlined
                                            style={{ position: 'relative', top: 5 }}
                                            className={styles['dynamic-delete-button']}
                                            onClick={() => remove(name)}
                                        />
                                    </Col>
                                </Row>
                            ))}
                            <Form.Item>
                                <Button
                                    type="primary"
                                    onClick={() => add()}
                                    icon={<PlusOutlined />}
                                >
                                    新增一行
                                </Button>
                                <Form.ErrorList errors={errors} />
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form.Item>
        </Card>
    );
};

const PayLayout = (props) => {
    return (
        <Card
            title={
                <span>
                    <span className={styles.formTitle}>支付配置</span>
                </span>
            }
            style={{ paddingTop: '20px' }}
            id="payInfo"
            bordered={false}
        >
            <PayFormView {...props} />
        </Card>
    );
};

const PayFormView = (props) => {
    const {
        form,
        dispatch,
        global: { codeInfo },
    } = props;

    const { companyBelongType: companyBelongTypeList, actChannel } = codeInfo;

    useEffect(() => {
        if (!actChannel) {
            dispatch({
                type: 'global/initCode',
                code: 'actChannel',
            });
        }
    }, []);
    const channelOptionList = useMemo(
        () =>
            actChannel?.map((ele) => (
                <Checkbox key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Checkbox>
            )) || [],
        [actChannel],
    );
    return (
        <Fragment>
            <FormItem
                label="订单支付方式"
                wrapperCol={{
                    span: 18,
                }}
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.belongType !== curValues.belongType ||
                    prevValues.companyPayType !== curValues.companyPayType ||
                    prevValues.offlineBalancePayType !== curValues.offlineBalancePayType
                }
            >
                {({ getFieldValue }) => {
                    const belongType = getFieldValue('belongType');
                    const companyPayType = getFieldValue('companyPayType');
                    const offlineBalancePayType = getFieldValue('offlineBalancePayType');
                    return (
                        <Fragment>
                            <FormItem name="personPayType" noStyle valuePropName="checked">
                                <Checkbox disabled>个人支付</Checkbox>
                            </FormItem>
                            <FormItem name="companyPayType" noStyle>
                                <Checkbox
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            form.setFieldsValue({
                                                offlineBalancePayType: false,
                                                companyPayType: true,
                                            });
                                        } else {
                                            form.setFieldsValue({ companyPayType: false });
                                        }
                                    }}
                                    checked={companyPayType}
                                >
                                    因公代付
                                </Checkbox>
                            </FormItem>
                            {belongType === COMPANY_BELONG_TYPE_ENUM.OPER && (
                                <FormItem name="offlineBalancePayType" noStyle>
                                    <Checkbox
                                        checked={offlineBalancePayType}
                                        onChange={(e) => {
                                            if (e.target.checked) {
                                                form.setFieldsValue({
                                                    companyPayType: false,
                                                    offlineBalancePayType: true,
                                                });
                                            } else {
                                                form.setFieldsValue({
                                                    offlineBalancePayType: false,
                                                });
                                            }
                                        }}
                                    >
                                        线下结算
                                    </Checkbox>
                                </FormItem>
                            )}
                        </Fragment>
                    );
                }}
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.companyPayType !== curValues.companyPayType ||
                    prevValues.accountType !== curValues.accountType ||
                    prevValues.offlineBalancePayType !== curValues.offlineBalancePayType ||
                    prevValues.balWarnFlag !== curValues.balWarnFlag
                }
            >
                {({ getFieldValue }) => {
                    const companyPayType = getFieldValue('companyPayType');
                    const accountType = getFieldValue('accountType');
                    const belongType = getFieldValue('belongType');
                    const balWarnFlag = getFieldValue('balWarnFlag');

                    let companyPayInfoFormItems = null;
                    if (accountType === ACCOUNT_TYPE_ENUM.ALIPAY) {
                        companyPayInfoFormItems = (
                            <Fragment>
                                <FormItem
                                    label="代付支付宝账号"
                                    name="alipayWithholdAccount"
                                    rules={[{ required: true, message: '请填写' }]}
                                >
                                    <Input
                                        placeholder="请填写"
                                        {...inputOptions}
                                        maxLength={50}
                                    ></Input>
                                </FormItem>
                                <FormItem label="代付额度" required>
                                    <Space>
                                        <FormItem
                                            noStyle
                                            name="companyAmt"
                                            rules={[{ required: true, message: '请填写' }]}
                                        >
                                            <Input
                                                type="number"
                                                max={9999999}
                                                min={1}
                                                placeholder="请填写"
                                                {...inputOptions}
                                            ></Input>
                                        </FormItem>
                                        元/每月
                                    </Space>
                                </FormItem>
                                <FormItem label="透支额度" required>
                                    <Space>
                                        <FormItem
                                            noStyle
                                            name="overdraftQuota"
                                            rules={[{ required: true, message: '请填写' }]}
                                        >
                                            <InputNumber
                                                max={9999999}
                                                min={1}
                                                placeholder="请填写"
                                                {...inputOptions}
                                            />
                                        </FormItem>
                                        元
                                    </Space>
                                </FormItem>
                            </Fragment>
                        );
                    } else if (accountType === ACCOUNT_TYPE_ENUM.PLATFORM) {
                        companyPayInfoFormItems = (
                            <Fragment>
                                <FormItem
                                    label="银行账号"
                                    name="paymentBankAccount"
                                    rules={[{ required: true, message: '请填写' }]}
                                >
                                    <Input
                                        placeholder="请填写"
                                        {...inputOptions}
                                        maxLength={50}
                                    ></Input>
                                </FormItem>
                                <CheckBoxGroup
                                    label="代付渠道"
                                    name={'paymentChannel'}
                                    form={form}
                                    selectList={actChannel}
                                    required
                                    rules={[{ required: true, message: '请选择代付渠道' }]}
                                    valueType="select"
                                ></CheckBoxGroup>

                                <FormItem
                                    name="balWarnFlag"
                                    label="余额告警"
                                    valuePropName="checked"
                                >
                                    <Switch checkedChildren="开" unCheckedChildren="关" />
                                </FormItem>

                                {balWarnFlag && (
                                    <>
                                        <Row>
                                            <Col span={6}>
                                                <FormItem
                                                    name="warnBalance"
                                                    label={'告警值'}
                                                    {...formItemLayout}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请填写',
                                                        },
                                                    ]}
                                                >
                                                    <InputNumber
                                                        maxLength={15}
                                                        placeholder="请填写"
                                                        autoComplete="off"
                                                        precision={2}
                                                        max={10000}
                                                        min={0}
                                                        addonAfter={'万'}
                                                        style={{ width: '100%' }}
                                                    />
                                                </FormItem>
                                            </Col>
                                            <Col span={6}>
                                                <FormItem
                                                    name="limitBalance"
                                                    label={'极限值'}
                                                    {...formItemLayout}
                                                    dependencies={['warnBalance']}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请填写',
                                                        },
                                                        ({ getFieldValue }) => ({
                                                            validator(_, value) {
                                                                if (
                                                                    !value ||
                                                                    (getFieldValue('warnBalance') >
                                                                        value &&
                                                                        value >= 0)
                                                                ) {
                                                                    return Promise.resolve();
                                                                }
                                                                return Promise.reject(
                                                                    new Error(
                                                                        '极限值需小于告警值大于等于0',
                                                                    ),
                                                                );
                                                            },
                                                        }),
                                                    ]}
                                                >
                                                    <InputNumber
                                                        maxLength={15}
                                                        placeholder="请填写"
                                                        autoComplete="off"
                                                        precision={2}
                                                        max={10000}
                                                        min={0}
                                                        addonAfter={'万'}
                                                        style={{ width: '100%' }}
                                                    />
                                                </FormItem>
                                            </Col>
                                        </Row>
                                        <FormItem
                                            name="warnMail"
                                            label={'告警邮箱'}
                                            {...formItemLayout}
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (!value?.length) {
                                                            return Promise.reject('请输入告警邮箱');
                                                        }
                                                        const mails = value.split(',');
                                                        if (mails.length > 10) {
                                                            return Promise.reject(
                                                                '最多支持填写10个邮箱',
                                                            );
                                                        }
                                                        for (const mail of mails) {
                                                            if (!mail.length) {
                                                                return Promise.reject(
                                                                    `邮箱有空值，请检查`,
                                                                );
                                                            }
                                                            if (!isEmail(mail)) {
                                                                return Promise.reject(
                                                                    `邮箱账号${mail}校验失败，请检查邮箱格式`,
                                                                );
                                                            }
                                                        }
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            required
                                        >
                                            <Input
                                                placeholder="如有多个以英文逗号分隔，最多支持填写10个邮箱"
                                                autoComplete="off"
                                            />
                                        </FormItem>
                                        <FormItem
                                            name="copyMail"
                                            label={'抄送邮箱'}
                                            {...formItemLayout}
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (value?.length > 0) {
                                                            const mails = value.split(',');
                                                            if (mails.length > 10) {
                                                                return Promise.reject(
                                                                    '最多支持填写10个邮箱',
                                                                );
                                                            }
                                                            for (const mail of mails) {
                                                                if (!mail.length) {
                                                                    return Promise.reject(
                                                                        `邮箱有空值，请检查`,
                                                                    );
                                                                }
                                                                if (!isEmail(mail)) {
                                                                    return Promise.reject(
                                                                        `邮箱账号${mail}校验失败，请检查邮箱格式`,
                                                                    );
                                                                }
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input
                                                placeholder="如有多个以英文逗号分隔，最多支持填写10个邮箱"
                                                autoComplete="off"
                                            />
                                        </FormItem>
                                        <FormItem
                                            name="warnMobile"
                                            label={'告警手机'}
                                            {...formItemLayout}
                                            rules={[
                                                () => ({
                                                    validator(rule, value) {
                                                        if (value?.length > 0) {
                                                            const values = value.split(',');
                                                            if (values.length > 10) {
                                                                return Promise.reject(
                                                                    '最多支持填写10个手机号',
                                                                );
                                                            }
                                                            for (const i of values) {
                                                                if (!i.length) {
                                                                    return Promise.reject(
                                                                        `手机号有空值，请检查`,
                                                                    );
                                                                }
                                                                if (!isPhoneNumber(i)) {
                                                                    return Promise.reject(
                                                                        `手机号${i}校验失败，请检查手机格式`,
                                                                    );
                                                                }
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input
                                                placeholder="如有多个以英文逗号分隔，最多支持填写10个手机号"
                                                autoComplete="off"
                                            />
                                        </FormItem>
                                    </>
                                )}
                            </Fragment>
                        );
                    }
                    if (companyPayType) {
                        return (
                            <Fragment>
                                <FormItem
                                    label="账户类型"
                                    name="accountType"
                                    rules={[{ required: true, message: '请选择' }]}
                                >
                                    <Radio.Group>
                                        {belongType === COMPANY_BELONG_TYPE_ENUM.PLATFORM && (
                                            <Radio value={ACCOUNT_TYPE_ENUM.PLATFORM}>
                                                平台账户
                                            </Radio>
                                        )}
                                        <Radio value={ACCOUNT_TYPE_ENUM.ALIPAY}>支付宝</Radio>
                                    </Radio.Group>
                                </FormItem>
                                {companyPayInfoFormItems}
                            </Fragment>
                        );
                    }
                    return null;
                }}
            </FormItem>
        </Fragment>
    );
};

const EnterpriseView = (props) => {
    const { form, isLock, initRef } = props;
    return (
        <Card
            title={
                <span>
                    <span className={styles.formTitle}>企业配置</span>
                </span>
            }
            style={{ paddingTop: '20px' }}
            id="baseInfo"
            bordered={false}
        >
            <DiscountLayout initRef={initRef} {...props} />
        </Card>
    );
};

const InvoiceView = (props) => {
    const {
        dispatch,
        formDisabled,
        cityTreeData,
        enterpriseManage: {
            editActInfo: { companyDetail = {} },
        },
        global: { codeInfo },
    } = props;

    const [addressOption, changeAddressOption] = useState([]); // 地址级联框数据

    const taxPayerType = 'taxPayerType';

    const { taxPayerType: taxPayerTypeList } = codeInfo;

    useEffect(() => {
        if (!taxPayerTypeList) {
            dispatch({
                type: 'global/initCode',
                code: taxPayerType,
            });
        }
    }, []);

    const taxPayerTypeListOptions = useMemo(() => {
        if (taxPayerTypeList) {
            return taxPayerTypeList.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [taxPayerTypeList]);

    useEffect(() => {
        initAddressData();
    }, [companyDetail, cityTreeData]);

    const initAddressData = async () => {
        if (cityTreeData?.length) {
            // 收件人地址级联框的数据
            if (
                companyDetail.receiptProvince &&
                companyDetail.receiptCity &&
                companyDetail.receiptCounty
            ) {
                let provinceData = addressOption;
                if (!provinceData?.length) {
                    provinceData = cityTreeData.map((item, index) => ({
                        label: item.areaName,
                        value: item.areaCode,
                        isLeaf: false,
                    }));
                }
                // 市

                const {
                    data: { areaList: cityList },
                } = await getAllProvinceAndCityApi({ upAreaCode: companyDetail.receiptProvince });
                const cityData = cityList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                const index = provinceData.findIndex(
                    (val) => companyDetail.receiptProvince == val.value,
                );
                if (index > -1) {
                    provinceData[index].children = cityData;
                }
                // 县

                const {
                    data: { areaList: countyList },
                } = await getAllProvinceAndCityApi({ upAreaCode: companyDetail.receiptCity });
                const countyData = countyList.map((item, itemIndex) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: true,
                }));
                const index2 = cityData.findIndex((val) => val.value == companyDetail.receiptCity);
                if (index2 > -1 && provinceData[index]?.children[index2]) {
                    provinceData[index].children[index2].children = countyData;
                }
                if (provinceData.length != 0) {
                    changeAddressOption([...provinceData]);
                }
            } else if (!addressOption?.length) {
                // 初始化级联框的省数据
                const provinceData = cityTreeData.map((item, index) => ({
                    label: item.areaName,
                    value: item.areaCode,
                    isLeaf: false,
                }));
                changeAddressOption(provinceData);
            }
        }
    };

    // 级联框懒加载数据
    const loadAddressData = async (selectedOptions) => {
        if (selectedOptions.length < 3) {
            const targetOption = selectedOptions[selectedOptions.length - 1];
            targetOption.loading = true;

            const {
                data: { areaList },
            } = await getAllProvinceAndCityApi({
                upAreaCode: targetOption.value,
            });
            targetOption.loading = false;
            const children = areaList;
            const isLeaf = selectedOptions.length == 2;
            const dealData = children.map((item, index) => ({
                label: item.areaName,
                value: item.areaCode,
                isLeaf,
            }));
            targetOption.children = dealData;

            changeAddressOption([...addressOption]);
        }
    };
    return (
        <Card
            title={
                <span>
                    <span className={styles.formTitle}>开票资料</span>
                </span>
            }
            style={{ paddingTop: '20px' }}
            bordered={false}
        >
            <Form.Item
                name="taxpayerType"
                label={
                    <span>
                        纳税人资格种类{' '}
                        <Tooltip title="一般纳税人开专票，小规模纳税人开普票">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </span>
                }
                {...formItemLayout}
            >
                <Select placeholder="请选择" disabled={formDisabled}>
                    {taxPayerTypeListOptions}
                </Select>
            </Form.Item>
            <Form.Item name="qualificBeginTime" label="资格开始时间：" {...formItemLayout}>
                <DatePicker disabled={formDisabled} />
            </Form.Item>
            <Form.Item name="invoiceTitle" label="发票抬头：" {...formItemLayout}>
                <Input placeholder="请填写" disabled={formDisabled} {...inputOptions} />
            </Form.Item>
            <Form.Item name="taxNum" label="纳税人识别号：" {...formItemLayout}>
                <Input placeholder="请填写" disabled={formDisabled} {...inputOptions} />
            </Form.Item>
            <Form.Item name="bankName" label="开户行名称：" {...formItemLayout}>
                <Input placeholder="请填写" disabled={formDisabled} {...inputOptions} />
            </Form.Item>
            <Form.Item name="bankAccount" label="银行账号：" {...formItemLayout}>
                <NumericInput
                    placeholder="请填写"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={32}
                />
            </Form.Item>
            <Form.Item name="invoiceAddress" label="开票地址：" {...formItemLayout}>
                <Input
                    placeholder="请填写"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={50}
                />
            </Form.Item>
            <Form.Item
                name="invoicePhone"
                label="开票电话："
                {...formItemLayout}
                rules={[
                    {
                        pattern: /^[\d|-]{0,16}$/,
                        message: '格式不正确',
                    },
                ]}
            >
                <Input
                    placeholder="请填写"
                    maxLength={11}
                    disabled={formDisabled}
                    {...inputOptions}
                />
            </Form.Item>
            <Form.Item name="receiptName" label="收件人姓名：" {...formItemLayout}>
                <Input placeholder="请填写" disabled={formDisabled} {...inputOptions} />
            </Form.Item>
            <Form.Item name="receiverAddressList" label="收件人地址：" {...formItemLayout}>
                <Cascader
                    options={addressOption}
                    loadData={loadAddressData}
                    disabled={formDisabled}
                />
            </Form.Item>
            <Form.Item name="receiptAddress" label="地址详情" {...formItemLayout}>
                <Input
                    placeholder="请填写"
                    disabled={formDisabled}
                    {...inputOptions}
                    maxLength={100}
                />
            </Form.Item>
            <Form.Item name="receiptPhone" label="收件人电话：" {...formItemLayout}>
                <NumericInput
                    placeholder="请填写"
                    maxLength={11}
                    disabled={formDisabled}
                    {...inputOptions}
                />
            </Form.Item>
        </Card>
    );
};

const DiscountLayout = (props) => {
    const {
        enterpriseManage: {
            editActInfo: { scopRules },
        },
        currentUser,
        form,
        listLoading,
        isLock,
        initRef,
    } = props;

    const [showDiscountView, toggleDiscountView] = useState(false); // 选择奖品弹窗
    const [discountList, changeDiscountList] = useState([]); // 当前配置列表
    const [editIndex, changeEditIndex] = useState(-1); // 当前编辑项下标
    const [editEnterpriseInfo, changeEditEnterpriseInfo] = useState(null); // 当前编辑对象

    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            changeDiscountList([]);
        },
    }));

    useEffect(() => {
        if (scopRules) {
            // 接口返回后初始化列表
            const list = [];
            for (const item of scopRules) {
                const newItem = item;
                const ruleList = [];
                for (const ruleItem of item.rules) {
                    const newRuleItem = ruleItem;
                    newRuleItem.stationId = ruleItem.stationId || [];
                    newRuleItem.stationId = newRuleItem.stationId?.map((ele) => Number(ele));
                    if (newRuleItem.stationId.length == 0) {
                        newRuleItem.suitStation = '1';
                    } else {
                        newRuleItem.suitStation = '0';
                    }
                    ruleList.push(newRuleItem);
                }
                newItem.rules = ruleList;

                list.push(newItem);
            }
            changeDiscountList(list);
        }
    }, [scopRules]);

    useEffect(() => {
        // 列表变更后更新表单
        const list = discountList.map((ele) => ({
            discountFlag: ele.discountFlag,
            elecDiscount: ele.elecDiscount,
            serviceDiscount: ele.serviceDiscount,
            incrementDiscount: ele.incrementDiscount,
            suitCity: ele.suitCity,
            suitStation: ele.suitStation,
            city: ele.city,
            rules: ele.rules,
            ruleId: ele.ruleId,
        }));
        form.setFieldsValue({ scopRules: list });
    }, [discountList]);

    const closeDiscountViewEvent = () => {
        toggleDiscountView(false);
        changeEditEnterpriseInfo(null);
        changeEditIndex(-1);
    };
    const addDiscountEvent = (params) => {
        changeDiscountList([...discountList, params]);
        toggleDiscountView(false);
        changeEditEnterpriseInfo(null);
    };
    const editDiscountEvent = (params) => {
        for (let index = 0; index < discountList.length; index++) {
            const element = discountList[index];
            if (index == editIndex) {
                discountList[index] = params;
                break;
            }
        }
        changeDiscountList([...discountList]);
        toggleDiscountView(false);
        changeEditEnterpriseInfo(null);
        changeEditIndex(-1);
    };

    // 新增
    const openAddDiscountViewEvent = () => {
        changeEditEnterpriseInfo(null);
        toggleDiscountView(true);
    };

    // 编辑
    const openEditDiscountViewEvent = (item, index) => {
        changeEditEnterpriseInfo(item);
        changeEditIndex(index);
        toggleDiscountView(true);
    };
    // 删除
    const deleteCpnEvent = (index) => {
        const newList = [...discountList];
        newList.splice(index, 1);
        changeDiscountList(newList);
    };

    const companyDiscountType = Form.useWatch('companyDiscountType', form);
    const prizeColumns = [
        {
            title: '序号 ',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '可用场站',
            width: 140,
            render(text, record) {
                let stationText = '';
                const partnerValue = form.getFieldValue(partnerCode);
                if (partnerValue == '1') {
                    if (record.suitStation == '0') {
                        stationText = '部分场站';
                    } else {
                        stationText = '全部场站';
                    }
                } else {
                    stationText = '全部场站';
                    if (record.rules) {
                        const rulesItem = record.rules[0];
                        if (rulesItem && rulesItem.stationId && rulesItem.stationId.length > 0) {
                            stationText = '部分场站';
                        }
                    }
                }

                return <span title={stationText}>{stationText}</span>;
            },
        },

        {
            title: '享受优惠费率',
            width: 180,
            dataIndex: 'discountFlag',
            render(text, record) {
                return (
                    <span title={text}>
                        {companyDiscountType == '1'
                            ? '否'
                            : record.discountFlag == '1'
                            ? '是'
                            : '否'}
                    </span>
                );
            },
        },
        {
            title: '电费享受折扣',
            width: 180,
            dataIndex: 'elecDiscount',
            render(text, record) {
                return (
                    <span title={text}>
                        {companyDiscountType == '1'
                            ? '个人优惠'
                            : record.elecDiscount >= 0
                            ? `${record.elecDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
        {
            title: '服务费享受折扣',
            width: 180,
            dataIndex: 'serviceDiscount',
            render(text, record) {
                return (
                    <span title={text}>
                        {companyDiscountType == '1'
                            ? '个人优惠'
                            : record.serviceDiscount >= 0
                            ? `${record.serviceDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
        {
            title: '附加费享受折扣',
            width: 180,
            dataIndex: 'incrementDiscount',
            render(text, record) {
                return (
                    <span title={text}>
                        {companyDiscountType == '1'
                            ? '个人优惠'
                            : record.incrementDiscount >= 0
                            ? `${record.incrementDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
    ];
    if (!isLock) {
        prizeColumns.push({
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record, index) {
                return (
                    <Space>
                        <span
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                openEditDiscountViewEvent(record, index);
                            }}
                        >
                            修改
                        </span>
                        <span
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                deleteCpnEvent(index);
                            }}
                        >
                            删除
                        </span>
                    </Space>
                );
            },
        });
    }

    const belongType = form.getFieldValue('belongType');
    return (
        <Fragment>
            <FormItem label="优惠方式" name={'companyDiscountType'} initialValue="0" required>
                <Radio.Group
                    onChange={(e) => {
                        if (discountList?.length) {
                            discountList.map((ele) => {
                                if (e.target.value == '1') {
                                    ele.discountFlag = '0';
                                }
                                // 如果有切换，都删掉折扣值，有需要再重新配
                                delete ele.elecDiscount;
                                delete ele.incrementDiscount;
                                delete ele.serviceDiscount;
                            });
                            changeDiscountList(discountList);
                        }
                    }}
                >
                    <Radio value="0">企业折扣</Radio>
                    {belongType === COMPANY_BELONG_TYPE_ENUM.PLATFORM && (
                        <Radio value="1">个人优惠</Radio>
                    )}
                </Radio.Group>
            </FormItem>

            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const partnerValue = getFieldValue(partnerCode);
                    const operId = getFieldValue('operId');
                    return (
                        <FormItem
                            name="scopRules"
                            label="可用场站及优惠:"
                            {...formItemLayout}
                            required
                            rules={[
                                () => ({
                                    validator(rule, value) {
                                        if (!value || value.length == 0) {
                                            return Promise.reject('请配置');
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Button
                                disabled={
                                    isLock ||
                                    (partnerValue == '1' && !operId && !currentUser.operId)
                                }
                                type="primary"
                                onClick={openAddDiscountViewEvent}
                            >
                                +添加
                            </Button>
                        </FormItem>
                    );
                }}
            </FormItem>

            <FormItem
                {...{
                    wrapperCol: {
                        span: 24,
                    },
                }}
            >
                <TablePro
                    style={{ width: '100%' }}
                    scroll={{ x: 'max-content' }}
                    loading={listLoading}
                    rowKey={(record) => record.cpnId}
                    dataSource={discountList}
                    pagination={false}
                    columns={prizeColumns}
                />
            </FormItem>

            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const partnerValue = getFieldValue(partnerCode);
                    const operId = getFieldValue('operId');
                    return (
                        <DiscountModelLayout
                            {...props}
                            visible={showDiscountView}
                            editEnterpriseInfo={editEnterpriseInfo}
                            addEvent={addDiscountEvent}
                            editEvent={editDiscountEvent}
                            discountList={discountList}
                            type={partnerValue}
                            closeEvent={closeDiscountViewEvent}
                            operId={currentUser.operId || operId}
                            companyDiscountType={companyDiscountType}
                            belongType={belongType}
                        />
                    );
                }}
            </FormItem>
        </Fragment>
    );
};
const discountformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};

const OperatorsForm = (props) => {
    const { operId, discountList, editEnterpriseInfo, belongType } = props;

    const stationsTreeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        placeholder: '请选择',
        maxTagCount: 2,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 2}个</span>,
        style: {
            width: '300px',
        },
    };
    const [stationList, changeStationList] = useState([]);
    useEffect(() => {
        if (operId) {
            initStationList();
        }
    }, [operId]);
    const initStationList = async () => {
        if (!operId?.length) {
            return;
        }
        try {
            if (operId) {
                const { data: cityAndStationList } = await initMainOperatorStationApi({ operId });
                changeStationList(cityAndStationList);
            }
        } catch (error) {}
    };

    // 禁用列表  过滤已选中站点
    const disabledStations = useMemo(() => {
        let list = [];
        for (const item of discountList) {
            if (item.rules) {
                for (const ruleItem of item.rules) {
                    let isEqual = false;
                    if (ruleItem.stationId) {
                        if (editEnterpriseInfo && editEnterpriseInfo.rules) {
                            for (const curRuleItem of editEnterpriseInfo.rules) {
                                if (
                                    curRuleItem.stationId &&
                                    curRuleItem.stationId.length > 0 &&
                                    ruleItem.stationId &&
                                    ruleItem.stationId.length > 0 &&
                                    curRuleItem.stationId.sort().toString() ==
                                        ruleItem.stationId.sort().toString()
                                ) {
                                    isEqual = true;
                                    break;
                                }
                            }
                        }
                        if (!isEqual) {
                            list = [...list, ...ruleItem.stationId];
                        }
                    }
                }
            }
        }
        return [...new Set(list)];
    }, [discountList, editEnterpriseInfo]);

    // 格式化列表  去除已被禁用的站点
    const formatStationList = useMemo(() => {
        const list = [];

        // 过滤已选站点
        if (stationList) {
            for (const item of stationList) {
                const newItem = item;
                const hasList = [];
                for (const children of item.children || []) {
                    let isHas = false;

                    for (let i = 0; i < disabledStations.length; i++) {
                        const element = disabledStations[i];

                        if (element == children.value) {
                            isHas = true;

                            break;
                        }
                    }
                    if (!isHas) {
                        hasList.push(children);
                    }
                }
                if (hasList.length != item.children && hasList.length > 0) {
                    newItem.children = hasList;
                    list.push(newItem);
                }
            }
        }
        return list;
    }, [stationList, disabledStations]);
    return (
        <Fragment>
            {belongType === COMPANY_BELONG_TYPE_ENUM.OPER ? (
                <FormItem noStyle name={['rules', 0, 'suitStation']} initialValue="0"></FormItem>
            ) : (
                <FormItem
                    name={['rules', 0, 'suitStation']}
                    label="可用场站:"
                    {...discountformItemLayout}
                    initialValue="1"
                >
                    <Radio.Group placeholder="请选择">
                        <Radio value="1">全部</Radio>
                        <Radio value="0">部分</Radio>
                    </Radio.Group>
                </FormItem>
            )}
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues?.rules[0]?.suitStation !== curValues?.rules[0]?.suitStation
                }
            >
                {({ getFieldValue }) => {
                    const rules = getFieldValue('rules');
                    return rules && rules[0] && rules[0]?.suitStation == '0' ? (
                        <FormItem
                            label="请选择"
                            name={['rules', 0, 'stationId']}
                            initialValue={[]}
                            rules={[
                                () => ({
                                    validator(rule, value) {
                                        if (!value || value.length == 0) {
                                            return Promise.reject('请配置站点');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <TreeSelect treeData={formatStationList} {...stationsTreeProps} />
                        </FormItem>
                    ) : null;
                }}
            </FormItem>
            <FormItem noStyle name={['rules', 0, 'operId']} initialValue={operId} />
        </Fragment>
    );
};

const PlatformForm = (props) => {
    const { dispatch, discountList, form, currentUser, editEnterpriseInfo } = props;

    const name = 'rules';

    const treeProps = {
        showSearch: true,
        treeNodeFilterProp: 'title',
        showCheckedStrategy: SHOW_CHILD,
        placeholder: '请选择',
        treeDefaultExpandAll: true,
        style: {
            width: '100%',
        },
    };
    const stationsTreeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        placeholder: '请选择',
        maxTagCount: 2,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 2}个</span>,
        style: {
            width: '300px',
        },
    };
    const [cityList, changeCityList] = useState([]);

    const [stationList, changeStationList] = useState({});
    const [stationLoading, toggleStationLoading] = useState(false);

    const [operatorList, changeOperatorList] = useState([]);

    useEffect(() => {
        if (operatorList.length == 0) {
            initOperatorList();
        }
        if (cityList.length == 0) {
            initCityList();
        }
        return () => {};
    }, []);

    useEffect(() => {
        if (editEnterpriseInfo) {
            // 延迟初始化所在城市运营商站点数据
            setTimeout(() => {
                reloadStationEvent(editEnterpriseInfo.city);
            }, 10);
        }
    }, [editEnterpriseInfo]);

    const initOperatorList = async (city) => {
        try {
            const {
                data: { list },
            } = await getOperatorListByCityApi(city);
            changeOperatorList(list);
        } catch (error) {}
    };

    // 禁用运营商列表  过滤已选中运营商
    const disabledOpers = useMemo(() => {
        const list = [];
        for (const item of discountList) {
            if (item.suitStation == '1') {
                if (item.rules) {
                    for (const ruleItem of item.rules) {
                        let isEqual = false;
                        if (editEnterpriseInfo && editEnterpriseInfo.rules) {
                            for (const curRuleItem of editEnterpriseInfo.rules) {
                                if (curRuleItem.operId == ruleItem.operId) {
                                    isEqual = true;
                                    break;
                                }
                            }
                        }
                        if (!isEqual) {
                            list.push(ruleItem.operId);
                        }
                    }
                }
            }
        }
        return list;
    }, [discountList, editEnterpriseInfo]);

    // 格式化列表  去除已被禁用的站点
    const formatOperatorList = useMemo(() => {
        const list = [];
        for (const item of operatorList) {
            let isHas = false;
            for (let index = 0; index < disabledOpers.length; index++) {
                const element = disabledOpers[index];

                if (item.operId == element) {
                    isHas = true;
                    break;
                }
            }
            if (!isHas) {
                list.push(item);
            }
        }

        return list;
    }, [operatorList, disabledOpers]);

    // 禁用列表  过滤已选中站点
    const disabledStations = useMemo(() => {
        let list = [];
        for (const item of discountList) {
            if (item.rules) {
                for (const ruleItem of item.rules) {
                    let isEqual = false;
                    if (ruleItem.stationId) {
                        if (editEnterpriseInfo && editEnterpriseInfo.rules) {
                            for (const curRuleItem of editEnterpriseInfo.rules) {
                                if (
                                    curRuleItem.stationId &&
                                    curRuleItem.stationId.length > 0 &&
                                    ruleItem.stationId &&
                                    ruleItem.stationId.length > 0 &&
                                    curRuleItem.stationId.sort().toString() ==
                                        ruleItem.stationId.sort().toString()
                                ) {
                                    isEqual = true;
                                    break;
                                }
                            }
                        }
                        if (!isEqual) {
                            list = [...list, ...ruleItem.stationId];
                        }
                    }
                }
            }
        }
        return [...new Set(list)];
    }, [discountList, editEnterpriseInfo]);

    const reloadStationEvent = (city) => {
        try {
            const list = form.getFieldValue(name);
            Promise.all(list.map((ele) => initCityAndStationEvent(ele.operId, city))).then(
                (data) => {
                    changeStationList(data);
                },
            );
        } catch (error) {}
    };

    const initCityAndStationEvent = async (operId, city) => {
        try {
            if (!operId) {
                return [];
            }
            toggleStationLoading(true);
            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                city,
                operId,
            });
            return cityAndStationList;
        } catch (error) {
            console.log(4444, error);
        } finally {
            toggleStationLoading(false);
        }
    };

    const changeOperEvent = async (value, index) => {
        try {
            toggleStationLoading(true);

            const city = form.getFieldValue('city');

            // 清空选中站点
            if (currentUser.operId != value) {
                const formItemData = form.getFieldValue(name);
                formItemData[index].stationId = [];
                form.setFieldsValue({
                    [name]: formItemData,
                });
            }

            const rules = form.getFieldValue(name);

            rules?.forEach((element, key) => {
                if (key != index) {
                    if (element && element.operId == value) {
                        if (
                            element.suitStation == '0' &&
                            element.stationId &&
                            element.stationId.length > 0
                        ) {
                            rules[index].suitStation = '0';
                            form.setFieldsValue({
                                [name]: rules,
                            });
                        }
                    }
                }
            });

            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                city,
                operId: value,
            });
            changeStationList((oldList) => {
                oldList[index] = cityAndStationList;
                return oldList;
            });
        } catch (error) {
            console.log(4444, error);
        } finally {
            toggleStationLoading(false);
        }
    };
    const initCityList = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = areaList.map((ele) => ({
                title: ele.areaName,
                value: ele.areaCode,
                isLeaf: false,
                selectable: false,
                children: ele.cityList.map((item) => ({
                    title: item.areaName,
                    value: item.areaCode,
                    isLeaf: true,
                })),
            }));
            changeCityList(list);
        } catch (error) {}
    };
    return (
        <Fragment>
            <FormItem
                name="suitCity"
                label="适用城市:"
                {...discountformItemLayout}
                initialValue="1"
                wrapperCol={{ span: 16 }}
            >
                <Radio.Group
                    placeholder="请选择"
                    onChange={(value) => {
                        // 切换城市清空下方运营商站点配置
                        form.setFieldsValue({ city: '' });
                        initOperatorList('');
                        form.setFieldsValue({ [name]: [] });
                    }}
                >
                    <Radio value="1">全部</Radio>
                    <Radio value="0">部分</Radio>
                </Radio.Group>
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) => prevValues.suitCity != curValues.suitCity}
            >
                {({ getFieldValue }) => {
                    const suitCity = getFieldValue('suitCity');
                    return suitCity == '0' ? (
                        <FormItem
                            name="city"
                            wrapperCol={{
                                offset: 6,
                                span: 8,
                            }}
                        >
                            <TreeSelect
                                treeData={cityList}
                                {...treeProps}
                                onChange={(value) => {
                                    // 切换城市清空下方运营商站点配置
                                    initOperatorList(value);
                                    form.setFieldsValue({ [name]: [] });
                                }}
                            />
                        </FormItem>
                    ) : null;
                }}
            </FormItem>

            <FormItem
                name="suitStation"
                label="可用场站:"
                {...discountformItemLayout}
                initialValue="1"
            >
                <Radio.Group placeholder="请选择">
                    <Radio value="1">全部</Radio>
                    <Radio value="0">部分</Radio>
                </Radio.Group>
            </FormItem>

            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.suitStation !== curValues.suitStation
                }
            >
                {({ getFieldValue }) => {
                    const curSuitStation = getFieldValue('suitStation');
                    return curSuitStation == '0' ? (
                        <FormItem
                            wrapperCol={{
                                offset: 6,
                                span: 18,
                            }}
                        >
                            <Form.List name={name}>
                                {(fields, { add, remove }) => (
                                    <Fragment>
                                        {fields.map((field, index) => (
                                            <Fragment key={index}>
                                                <Row>
                                                    <Col flex="200px">
                                                        <OperSelectItem
                                                            label=""
                                                            fieldKey={field.fieldKey}
                                                            name={[field.name, 'operId']}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择运营商',
                                                                },
                                                                (_) => ({
                                                                    validator(rule, value) {
                                                                        return Promise.resolve();
                                                                    },
                                                                }),
                                                            ]}
                                                            onChange={(value) => {
                                                                changeOperEvent(value, index);
                                                            }}
                                                            operatorList={formatOperatorList}
                                                            // disabled={disabled}
                                                        />
                                                    </Col>
                                                    <Col flex="0 0 auto">
                                                        <FormItem
                                                            noStyle
                                                            shouldUpdate={(prevValues, curValues) =>
                                                                true
                                                            }
                                                        >
                                                            {(_) => {
                                                                // 已选该运营商并配置了站点  不能再选全部
                                                                const rules = getFieldValue(name);
                                                                let noAll = false;
                                                                const operName = '';
                                                                const { operId = null } =
                                                                    rules[field.name] || {};
                                                                rules.forEach((item, key) => {
                                                                    if (
                                                                        index != key &&
                                                                        item &&
                                                                        operId &&
                                                                        item.operId == operId
                                                                    ) {
                                                                        if (
                                                                            item.suitStation ==
                                                                                '0' &&
                                                                            item.stationId &&
                                                                            item.stationId.length >
                                                                                0
                                                                        ) {
                                                                            noAll = true;
                                                                        }
                                                                        if (
                                                                            item.suitStation == '1'
                                                                        ) {
                                                                            noAll = true;
                                                                        }
                                                                    }
                                                                });

                                                                return (
                                                                    <FormItem
                                                                        style={{ margin: '0 8px' }}
                                                                        fieldKey={field.fieldKey}
                                                                        name={[
                                                                            field.name,
                                                                            'suitStation',
                                                                        ]}
                                                                        validateTrigger={[
                                                                            'onChange',
                                                                        ]}
                                                                        initialValue="1"
                                                                        wrapperCol={{
                                                                            span: 24,
                                                                        }}
                                                                        rules={[
                                                                            () => ({
                                                                                validator(
                                                                                    rule,
                                                                                    value,
                                                                                ) {
                                                                                    if (
                                                                                        noAll &&
                                                                                        value == '1'
                                                                                    ) {
                                                                                        return Promise.reject(
                                                                                            `该运营商不可选择全部`,
                                                                                        );
                                                                                    }

                                                                                    return Promise.resolve();
                                                                                },
                                                                            }),
                                                                        ]}
                                                                    >
                                                                        <Radio.Group>
                                                                            <Radio
                                                                                value="1"
                                                                                disabled={noAll}
                                                                            >
                                                                                全部
                                                                            </Radio>
                                                                            <Radio value="0">
                                                                                部分
                                                                            </Radio>
                                                                        </Radio.Group>
                                                                    </FormItem>
                                                                );
                                                            }}
                                                        </FormItem>
                                                    </Col>
                                                    <Col flex="1">
                                                        <Spin spinning={stationLoading || false}>
                                                            <FormItem
                                                                noStyle
                                                                shouldUpdate={(
                                                                    prevValues,
                                                                    curValues,
                                                                ) => true}
                                                            >
                                                                {(_) => {
                                                                    const rules =
                                                                        getFieldValue(name);

                                                                    const { suitStation = '1' } =
                                                                        rules[field.name] || {};

                                                                    const disabled =
                                                                        suitStation &&
                                                                        suitStation == '1';

                                                                    const formatStationList = [];

                                                                    // 过滤已选站点
                                                                    if (stationList[index]) {
                                                                        for (const item of stationList[
                                                                            index
                                                                        ]) {
                                                                            const newItem = item;
                                                                            const hasList = [];
                                                                            for (const children of item?.children ||
                                                                                []) {
                                                                                let isHas = false;

                                                                                for (
                                                                                    let i = 0;
                                                                                    i <
                                                                                    disabledStations.length;
                                                                                    i++
                                                                                ) {
                                                                                    const element =
                                                                                        disabledStations[
                                                                                            i
                                                                                        ];

                                                                                    if (
                                                                                        element ==
                                                                                        children.value
                                                                                    ) {
                                                                                        isHas = true;

                                                                                        break;
                                                                                    }
                                                                                }
                                                                                if (!isHas) {
                                                                                    hasList.push(
                                                                                        children,
                                                                                    );
                                                                                }
                                                                            }
                                                                            if (
                                                                                hasList.length !=
                                                                                    item?.children &&
                                                                                hasList.length > 0
                                                                            ) {
                                                                                newItem.children =
                                                                                    hasList;
                                                                                formatStationList.push(
                                                                                    newItem,
                                                                                );
                                                                            }
                                                                        }
                                                                    }
                                                                    return (
                                                                        <FormItem
                                                                            style={{
                                                                                margin: '0 8px',
                                                                                display:
                                                                                    'inline-block',
                                                                            }}
                                                                            fieldKey={
                                                                                field.fieldKey
                                                                            }
                                                                            name={[
                                                                                field.name,
                                                                                'stationId',
                                                                            ]}
                                                                            validateTrigger={[
                                                                                'onChange',
                                                                                'onBlur',
                                                                            ]}
                                                                            rules={[
                                                                                () => ({
                                                                                    validator(
                                                                                        rule,
                                                                                        value,
                                                                                    ) {
                                                                                        if (
                                                                                            disabled
                                                                                        ) {
                                                                                            return Promise.resolve();
                                                                                        }
                                                                                        if (
                                                                                            !value ||
                                                                                            value.length ==
                                                                                                0
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                '请配置站点',
                                                                                            );
                                                                                        }
                                                                                        // 过滤选择相同站点
                                                                                        let haserror = false;
                                                                                        rules.forEach(
                                                                                            (
                                                                                                item,
                                                                                                key,
                                                                                            ) => {
                                                                                                if (
                                                                                                    key !=
                                                                                                        index &&
                                                                                                    item &&
                                                                                                    value &&
                                                                                                    item.stationId &&
                                                                                                    item
                                                                                                        .stationId
                                                                                                        .length >
                                                                                                        0 &&
                                                                                                    value.length >
                                                                                                        0 &&
                                                                                                    item.stationId
                                                                                                        .sort()
                                                                                                        .toString() ==
                                                                                                        value
                                                                                                            .sort()
                                                                                                            .toString()
                                                                                                ) {
                                                                                                    haserror = true;
                                                                                                }
                                                                                            },
                                                                                        );
                                                                                        if (
                                                                                            haserror
                                                                                        ) {
                                                                                            return Promise.reject(
                                                                                                `${
                                                                                                    index +
                                                                                                    1
                                                                                                }已配置相同站点`,
                                                                                            );
                                                                                        }
                                                                                        return Promise.resolve();
                                                                                    },
                                                                                }),
                                                                            ]}
                                                                            noStyle
                                                                        >
                                                                            <TreeSelect
                                                                                disabled={disabled}
                                                                                treeData={
                                                                                    formatStationList
                                                                                }
                                                                                {...stationsTreeProps}
                                                                            />
                                                                        </FormItem>
                                                                    );
                                                                }}
                                                            </FormItem>
                                                        </Spin>
                                                    </Col>
                                                    <Col>
                                                        <MinusCircleOutlined
                                                            className={
                                                                styles['dynamic-delete-button']
                                                            }
                                                            style={{ margin: '0 8px' }}
                                                            onClick={() => {
                                                                remove(field.name);
                                                            }}
                                                        />
                                                    </Col>
                                                </Row>
                                            </Fragment>
                                        ))}
                                        <FormItem label="">
                                            <Button
                                                type="dashed"
                                                onClick={() => {
                                                    add();
                                                }}
                                                style={{ width: '200px' }}
                                            >
                                                <PlusOutlined />
                                                添加运营商
                                            </Button>
                                        </FormItem>
                                    </Fragment>
                                )}
                            </Form.List>
                        </FormItem>
                    ) : null;
                }}
            </FormItem>
        </Fragment>
    );
};

const BasicForm = (props) => {
    const { companyDiscountType } = props;
    useEffect(() => {
        console.log(5555555, companyDiscountType);
    }, [companyDiscountType]);
    return (
        <Fragment>
            <FormItem
                name="discountFlag"
                label="享受优惠费率:"
                initialValue={companyDiscountType == '1' ? '0' : '1'}
                rules={[{ required: true, message: '请选择' }]}
            >
                <Radio.Group placeholder="请选择" disabled={companyDiscountType == '1'}>
                    <Radio value="1">是</Radio>
                    <Radio value="0">否</Radio>
                </Radio.Group>
            </FormItem>
            <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue }) => {
                    const discountFlag = getFieldValue('discountFlag');
                    return discountFlag == '1' ? (
                        <FormItem
                            label={
                                <span>
                                    享受费率{' '}
                                    <Tooltip title="服务费打8.5折，填写8.5">
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            }
                            rules={[
                                () => ({
                                    validator(rule, value) {
                                        if (!value || value.length == 0) {
                                            return Promise.reject('请选择优惠类型');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            name="discount"
                        >
                            <Checkbox.Group>
                                <div>
                                    <Checkbox value="01">
                                        <Space>
                                            <span>电费</span>
                                            <Space>
                                                <span>享受</span>
                                                <FormItem
                                                    noStyle
                                                    shouldUpdate={(prevValues, curValues) => true}
                                                >
                                                    {() => {
                                                        const discount = getFieldValue('discount');
                                                        const isEdit =
                                                            discount && discount.includes('01');
                                                        return (
                                                            <FormItem
                                                                name="elecDiscount"
                                                                noStyle
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!isEdit) {
                                                                                return Promise.resolve();
                                                                            }
                                                                            if (
                                                                                !value &&
                                                                                value != 0
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    '请填写折扣',
                                                                                );
                                                                            }
                                                                            if (
                                                                                Number(value) > 10
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    `折扣不能大于10`,
                                                                                );
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    min={0}
                                                                    max={10}
                                                                    precision={1}
                                                                    step={0.1}
                                                                    placeholder="请填写"
                                                                    disabled={!isEdit}
                                                                />
                                                            </FormItem>
                                                        );
                                                    }}
                                                </FormItem>

                                                <span>折</span>
                                            </Space>
                                        </Space>
                                    </Checkbox>
                                </div>
                                <div>
                                    <Checkbox value="02">
                                        <Space>
                                            <span>服务费</span>
                                            <Space>
                                                <span>享受</span>
                                                <FormItem
                                                    noStyle
                                                    shouldUpdate={(prevValues, curValues) => true}
                                                >
                                                    {() => {
                                                        const discount = getFieldValue('discount');
                                                        const isEdit =
                                                            discount && discount.includes('02');
                                                        return (
                                                            <FormItem
                                                                name="serviceDiscount"
                                                                noStyle
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!isEdit) {
                                                                                return Promise.resolve();
                                                                            }
                                                                            if (
                                                                                !value &&
                                                                                value != 0
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    '请填写折扣',
                                                                                );
                                                                            }
                                                                            if (
                                                                                Number(value) > 10
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    `折扣不能大于10`,
                                                                                );
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    min={0}
                                                                    max={10}
                                                                    precision={1}
                                                                    step={0.1}
                                                                    placeholder="请填写"
                                                                    disabled={!isEdit}
                                                                />
                                                            </FormItem>
                                                        );
                                                    }}
                                                </FormItem>

                                                <span>折</span>
                                            </Space>
                                        </Space>
                                    </Checkbox>
                                </div>
                                <div>
                                    <Checkbox value="03">
                                        <Space>
                                            <span>附加费</span>
                                            <Space>
                                                <span>享受</span>
                                                <FormItem
                                                    noStyle
                                                    shouldUpdate={(prevValues, curValues) => true}
                                                >
                                                    {() => {
                                                        const discount = getFieldValue('discount');
                                                        const isEdit =
                                                            discount && discount.includes('03');
                                                        return (
                                                            <FormItem
                                                                name="incrementDiscount"
                                                                noStyle
                                                                rules={[
                                                                    (_) => ({
                                                                        validator(rule, value) {
                                                                            if (!isEdit) {
                                                                                return Promise.resolve();
                                                                            }
                                                                            if (
                                                                                !value &&
                                                                                value != 0
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    '请填写折扣',
                                                                                );
                                                                            }
                                                                            if (
                                                                                Number(value) > 10
                                                                            ) {
                                                                                return Promise.reject(
                                                                                    `折扣不能大于10`,
                                                                                );
                                                                            }

                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                <InputNumber
                                                                    min={0}
                                                                    max={10}
                                                                    precision={1}
                                                                    step={0.1}
                                                                    placeholder="请填写"
                                                                    disabled={!isEdit}
                                                                />
                                                            </FormItem>
                                                        );
                                                    }}
                                                </FormItem>

                                                <span>折</span>
                                            </Space>
                                        </Space>
                                    </Checkbox>
                                </div>
                            </Checkbox.Group>
                        </FormItem>
                    ) : null;
                }}
            </FormItem>
        </Fragment>
    );
};

const DiscountModelLayout = (props) => {
    const {
        dispatch,
        listLoading,
        visible,
        addEvent,
        editEvent,
        closeEvent,
        editEnterpriseInfo,
        discountList,
        isLock,
        type,
        operId,
        companyDiscountType,
        belongType,
    } = props;

    const [form] = Form.useForm();

    useEffect(() => {
        if (visible) {
            form.resetFields();
        }
    }, [visible]);

    useEffect(() => {
        if (editEnterpriseInfo) {
            // 更新表单
            const options = {
                discountFlag: editEnterpriseInfo.discountFlag,
                suitStation: editEnterpriseInfo.suitStation,
                suitCity: editEnterpriseInfo.suitCity,
                city: editEnterpriseInfo.city,
                ruleId: editEnterpriseInfo.ruleId,
            };
            if (companyDiscountType == '1') {
                options.discountFlag = '0';
            }
            const discount = [];
            if (editEnterpriseInfo.discountFlag == '1') {
                if (editEnterpriseInfo.elecDiscount >= 0) {
                    options.elecDiscount = editEnterpriseInfo.elecDiscount;
                    discount.push('01');
                }
                if (editEnterpriseInfo.serviceDiscount >= 0) {
                    options.serviceDiscount = editEnterpriseInfo.serviceDiscount;
                    discount.push('02');
                }
                if (editEnterpriseInfo.incrementDiscount >= 0) {
                    options.incrementDiscount = editEnterpriseInfo.incrementDiscount;
                    discount.push('03');
                }
            }
            options.discount = discount;

            if (editEnterpriseInfo.rules) {
                options.rules = editEnterpriseInfo.rules;
            }
            form.setFieldsValue(options);
        } else {
            const options = {};
            if (companyDiscountType == '1') {
                // 新建场景
                options.discountFlag = '0';
            }
            form.setFieldsValue(options);
        }
    }, [editEnterpriseInfo, companyDiscountType]);

    const onFinish = (values) => {
        // 提交

        const params = {
            discountFlag: values.discountFlag,
            suitStation: values.suitStation,
            suitCity: values.suitCity,
            city: values.city,
        };

        if (editEnterpriseInfo && editEnterpriseInfo.ruleId) {
            params.ruleId = editEnterpriseInfo.ruleId;
        }
        if (values.discountFlag == '1') {
            if (values.discount) {
                if (values.discount.includes('01')) {
                    params.elecDiscount = values.elecDiscount;
                }
                if (values.discount.includes('02')) {
                    params.serviceDiscount = values.serviceDiscount;
                }
                if (values.discount.includes('03')) {
                    params.incrementDiscount = values.incrementDiscount;
                }
            }
        }

        if (values.rules) {
            params.rules = values.rules;
        }

        if (operId) {
            // 运营商账号登录默认为部分城市
            params.suitCity = '1';
            params.suitStation = values?.rules[0]?.suitStation;
            params.rules = values.rules;
        }

        // //判断站点是否重复

        let formatDiscoutList = [];

        if (discountList) {
            if (editEnterpriseInfo) {
                formatDiscoutList = discountList.filter((ele) => {
                    const isEqual = JSON.stringify(ele) == JSON.stringify(editEnterpriseInfo);
                    return !isEqual;
                });
            } else {
                formatDiscoutList = discountList;
            }
        }

        // 校验去重
        if (params.suitCity == '1') {
            // 当前选中全部城市
            if (params.suitStation == '1') {
                // 当前选中全部站点
                if (formatDiscoutList.length > 0) {
                    message.error('已添加了全部站点优惠规则');
                    return;
                }
            } else if (params.rules) {
                for (const curRuleItem of params.rules) {
                    for (const item of formatDiscoutList) {
                        if (item.suitCity == '0') {
                            if (item.suitStation == '1' && item.city == params.city) {
                                message.error('已配置了相同城市的全部站点优惠规则');
                                return;
                            }
                        }
                        if (item.rules) {
                            for (const ruleItem of item.rules) {
                                if (ruleItem.operId == curRuleItem.operId) {
                                    if (curRuleItem.suitStation == '1') {
                                        message.error('已配置了该运营商的所有站点，无法再配置');
                                        return;
                                    }
                                    let hasIncludes = false;
                                    for (
                                        let index = 0;
                                        index < ruleItem.stationId.length;
                                        index++
                                    ) {
                                        const element = ruleItem.stationId[index];
                                        if (curRuleItem?.stationId.includes(element)) {
                                            hasIncludes = true;
                                            break;
                                        }
                                    }
                                    if (hasIncludes) {
                                        message.error('已配置了相同站点优惠规则');
                                        return;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else if (params.suitStation == '1') {
            // 当前选中全部站点
            for (const item of formatDiscoutList) {
                if (item.suitCity == '0') {
                    // 部分城市
                    if (item.city == params.city) {
                        message.error('已配置了相同城市的站点优惠规则');
                        return;
                    }
                } else {
                    // 全部城市无法判断
                }
            }
        } else {
            for (const curRuleItem of params.rules) {
                for (const item of formatDiscoutList) {
                    if (item.suitCity == '0') {
                        if (item.suitStation == '1' && item.city == params.city) {
                            message.error('已配置了相同城市的全部站点优惠规则');
                            return;
                        }
                    }
                    if (item.rules) {
                        for (const ruleItem of item.rules) {
                            if (ruleItem.operId == curRuleItem.operId) {
                                if (curRuleItem.suitStation == '1') {
                                    message.error('已配置了该运营商的所有站点，无法再配置');
                                    return;
                                }
                                let hasIncludes = false;
                                for (let index = 0; index < ruleItem.stationId.length; index++) {
                                    const element = ruleItem.stationId[index];
                                    if (curRuleItem?.stationId.includes(element)) {
                                        hasIncludes = true;
                                        break;
                                    }
                                }
                                if (hasIncludes) {
                                    message.error('已配置了相同站点优惠规则');
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        if (editEnterpriseInfo) {
            editEvent(params);
        } else {
            addEvent(params);
        }

        form.resetFields();
    };
    const closeWindowEvent = () => {
        form.resetFields();
        closeEvent();
    };

    return (
        <Fragment>
            <Modal
                visible={visible}
                title="可用场站及优惠"
                width={1000}
                onCancel={closeWindowEvent}
                footer={null}
                destroyOnClose
                maskClosable={false}
            >
                <Form
                    form={form}
                    onFinish={onFinish}
                    initialValues={{}}
                    scrollToFirstError
                    {...discountformItemLayout}
                >
                    {/* 运营商账号配置 */}
                    {type == '1' ? (
                        <OperatorsForm
                            {...props}
                            operId={operId}
                            discountList={discountList}
                            editEnterpriseInfo={editEnterpriseInfo}
                            belongType={belongType}
                            form={form}
                        />
                    ) : (
                        // 平台账号配置
                        <PlatformForm
                            {...props}
                            discountList={discountList}
                            editEnterpriseInfo={editEnterpriseInfo}
                            form={form}
                        />
                    )}

                    {/* 费率配置 */}
                    <BasicForm {...props} />
                    <FormItem
                        {...{
                            wrapperCol: {
                                span: 8,
                                offset: 6,
                            },
                        }}
                    >
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeWindowEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Fragment>
    );
};

// 翻牌子编辑页面
const ManageUpdatePage = (props) => {
    const {
        dispatch,
        match,
        history,
        location,
        route,
        currentUser,
        enterpriseManage: { editActInfo },
    } = props;
    const [isLock, changeLock] = useState(() => {
        if (route.path.indexOf('/userCenter/enterprise/manage/list/look') >= 0) {
            return true;
        }
        return false;
    }); // 是否可编辑
    const [submitLoading, changeSubmitLoading] = useState(false);
    const [companyId, changeCompanyId] = useState(match.params.companyId || null);

    const { operId } = currentUser;

    const [form] = Form.useForm();

    const operSelectList = useRef();

    const discountListRef = useRef();

    const [cityTreeData, changeCityTreeData] = useState([]);

    const initCityTree = async () => {
        try {
            const {
                data: { areaList },
            } = await getAllProvinceAndCityApi({ upAreaCode: 1 });
            changeCityTreeData(areaList);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (companyId) {
            dispatch({
                type: 'enterpriseManage/initEditActInfo',
                companyId,
            });
        }

        if (cityTreeData.length == 0) {
            initCityTree();
        }

        return () => {
            dispatch({
                type: 'enterpriseManage/updateEditActInfo',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
        }
    }, [editActInfo, currentUser]);

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            const { companyDetail = {}, scopRules = [] } = info;
            const params = {
                ...companyDetail,
                companyDiscountType: companyDetail?.companyDiscountType || '0',
            };
            if (companyDetail.companyName) {
                params.companyName = companyDetail.companyName;
            }

            if (companyDetail.alipayManageAccount) {
                const { alipayManageAccount = '' } = companyDetail;
                params.alipayManageAccount = alipayManageAccount?.split(',') || [];
            }
            if (companyDetail.bankAccount) {
                params.bankAccount = companyDetail.bankAccount;
            }
            if (companyDetail.bankName) {
                params.bankName = companyDetail.bankName;
            }
            if (companyDetail.belongType) {
                params.belongType = companyDetail.belongType;
            }
            if (params.belongType == COMPANY_BELONG_TYPE_ENUM.OPER && !currentUser.operId) {
                params.operId = scopRules[0]?.rules[0].operId;
            }
            if (companyDetail.operId) {
                params.operId = companyDetail.operId;
            }
            if (companyDetail.companyAddress) {
                params.companyAddress = companyDetail.companyAddress;
            }
            if (companyDetail.companyNickname) {
                params.companyNickname = companyDetail.companyNickname;
            }
            if (companyDetail.contactEmail) {
                params.contactEmail = companyDetail.contactEmail;
            }
            if (companyDetail.contacts) {
                params.contacts = companyDetail.contacts;
            }
            if (companyDetail.invoiceAddress) {
                params.invoiceAddress = companyDetail.invoiceAddress;
            }
            if (companyDetail.invoicePhone) {
                params.invoicePhone = companyDetail.invoicePhone;
            }
            if (companyDetail.invoiceTitle) {
                params.invoiceTitle = companyDetail.invoiceTitle;
            }
            if (companyDetail.mobile) {
                params.mobile = companyDetail.mobile;
            }
            if (companyDetail.invoicePhone) {
                params.invoicePhone = companyDetail.invoicePhone;
            }
            if (companyDetail.receiptAddress) {
                params.receiptAddress = companyDetail.receiptAddress;
            }
            if (companyDetail.receiptName) {
                params.receiptName = companyDetail.receiptName;
            }
            if (companyDetail.receiptPhone) {
                params.receiptPhone = companyDetail.receiptPhone;
            }
            if (companyDetail.taxNum) {
                params.taxNum = companyDetail.taxNum;
            }
            if (companyDetail.taxpayerType) {
                params.taxpayerType = companyDetail.taxpayerType;
            }

            if (companyDetail.qualificBeginTime) {
                params.qualificBeginTime = moment(companyDetail.qualificBeginTime);
            }
            if (
                companyDetail.companyProvince &&
                companyDetail.companyCity &&
                companyDetail.companyCounty
            ) {
                params.address = [
                    companyDetail.companyProvince,
                    companyDetail.companyCity,
                    companyDetail.companyCounty,
                ];
            }
            if (
                companyDetail.receiptProvince &&
                companyDetail.receiptCity &&
                companyDetail.receiptCounty
            ) {
                params.receiverAddressList = [
                    companyDetail.receiptProvince,
                    companyDetail.receiptCity,
                    companyDetail.receiptCounty,
                ];
            }

            if (companyDetail.companyAmt) {
                params.companyAmt = companyDetail.companyAmt;
            }

            if (companyDetail.overdraftQuota) {
                params.overdraftQuota = companyDetail.overdraftQuota;
            }

            params.personPayType = true;
            params.companyPayType = companyDetail.companyPayType == '1';
            params.offlineBalancePayType = companyDetail.offlineBalancePayType == '1';
            params.accountType = companyDetail.accountType;

            if (companyDetail.alipayWithholdAccount) {
                params.alipayWithholdAccount = companyDetail.alipayWithholdAccount;
            }
            if (companyDetail.alipayAccountType) {
                params.alipayAccountType = companyDetail.alipayAccountType;
            }
            if (companyDetail.paymentBankAccount) {
                params.paymentBankAccount = companyDetail.paymentBankAccount;
            }
            if (companyDetail.paymentChannel) {
                params.paymentChannel = companyDetail.paymentChannel
                    .split(',')
                    .filter((ele) => !isEmpty(ele));
            }

            // if (info.stationList) {
            //     params.stationList = info.stationList.map(ele => ({
            //         stationId: ele.stationIds.map(item => Number(item)),
            //         operId: ele.operId,
            //     }));
            //     if (info.areaRangeType == CAPITALTYPS.OPERATOR) {
            //         setTimeout(() => {
            //             operSelectList.current && operSelectList.current.init(params.stationList);
            //         }, 0);
            //     }
            // }
            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    /**
     * 保存企业
     * type  save/send
     */
    const saveEnterpriseEvent = async () => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();
            const { alipayManageAccount = [] } = values;
            const params = {
                ...values,
                companyName: values.companyName,
                alipayManageAccount: alipayManageAccount.join(','),
                bankAccount: values.bankAccount,
                bankName: values.bankName,
                belongType: values.belongType,
                companyAddress: values.companyAddress,
                companyNickname: values.companyNickname,
                contactEmail: values.contactEmail,
                contacts: values.contacts,
                invoiceAddress: values.invoiceAddress,
                invoicePhone: values.invoicePhone,
                invoiceTitle: values.invoiceTitle,
                mobile: values.mobile,
                qualificBeginTime:
                    (values.qualificBeginTime && values.qualificBeginTime.format('YYYY-MM-DD')) ||
                    '',
                receiptAddress: values.receiptAddress,
                receiptName: values.receiptName,
                receiptPhone: values.receiptPhone,
                taxNum: values.taxNum,
                taxpayerType: values.taxpayerType,
                companyDiscountType: values.companyDiscountType,

                // 支付配置内容
                companyAmt:
                    values.accountType === ACCOUNT_TYPE_ENUM.PLATFORM
                        ? undefined
                        : values.companyAmt,
                overdraftQuota: values.overdraftQuota,
                personPayType: '1',
                companyPayType: values.companyPayType ? '1' : '0',
            };
            if (values.companyPayType) {
                params.accountType = values.accountType;
                if (values.accountType === ACCOUNT_TYPE_ENUM.ALIPAY) {
                    params.alipayWithholdAccount = values.alipayWithholdAccount;
                    params.alipayAccountType = values.alipayAccountType;
                } else if (values.accountType === ACCOUNT_TYPE_ENUM.PLATFORM) {
                    params.paymentBankAccount = values.paymentBankAccount;
                    params.paymentChannel = values.paymentChannel?.join(',');
                }
            }
            if (values.offlineBalancePayType) {
                params.offlineBalancePayType = '1';
            } else {
                params.offlineBalancePayType = '0';
            }
            if (values.address) {
                const [companyProvince, companyCity, companyCounty] = values.address;
                params.companyProvince = companyProvince;
                params.companyCity = companyCity;
                params.companyCounty = companyCounty;
            }
            if (values.receiverAddressList) {
                const [receiptProvince, receiptCity, receiptCounty] = values.receiverAddressList;
                params.receiptProvince = receiptProvince;
                params.receiptCity = receiptCity;
                params.receiptCounty = receiptCounty;
            }
            if (values.scopRules) {
                const rules = [...values.scopRules];
                if (params.companyDiscountType == '1') {
                    rules.map((ele) => {
                        ele.discountFlag = '0';
                        delete ele.elecDiscount;
                        delete ele.incrementDiscount;
                        delete ele.serviceDiscount;
                    });
                }
                params.scopRules = JSON.stringify(rules);
            }
            if (values.operId) {
                params.operId = values.operId;
            }

            if (companyId) {
                params.companyId = companyId;
            }

            if (operId) {
                params.operId = operId;
            }

            changeSubmitLoading(true);

            const {
                data: { companyId: newCompanyId },
            } = await saveEnterpriseManageApi(params);

            if (newCompanyId) {
                changeCompanyId(newCompanyId);
            }
            message.success('提交成功');
            goBack();
        } catch (error) {
            console.log(9999, error);
        } finally {
            changeSubmitLoading(false);
        }
    };
    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    {...formItemLayout}
                    initialValues={{
                        areaRangeType: CAPITALTYPS.ALL,
                        [partnerCode]: currentUser.operId ? '1' : '0',
                    }}
                    scrollToFirstError
                >
                    {/* 基础信息配置 */}
                    <BasicInfoView
                        form={form}
                        companyId={companyId}
                        cityTreeData={cityTreeData}
                        discountListRef={discountListRef}
                        {...props}
                    />

                    {/* 支付配置 */}
                    <PayLayout form={form} {...props} />

                    {/* 企业配置 */}
                    <EnterpriseView form={form} initRef={discountListRef} {...props} />

                    {/* 开票信息配置 */}
                    <InvoiceView form={form} cityTreeData={cityTreeData} {...props} />

                    <div className={commonStyles['form-submit']}>
                        {isLock ? (
                            <Fragment>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        ) : (
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={saveEnterpriseEvent}
                                >
                                    提交
                                </Button>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    返回
                                </Button>
                            </Fragment>
                        )}
                    </div>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, enterpriseManage, loading }) => ({
    global,
    currentUser: user.currentUser,
    enterpriseManage,
    detalisLoading: loading.effects['enterpriseManage/getCouponInfo'],
    operatorLoading: loading.effects['enterpriseManage/getOperationList'],
}))(ManageUpdatePage);
