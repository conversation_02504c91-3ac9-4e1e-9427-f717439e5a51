import {
    getEnterpriseManageListApi,
    getEnterpriseManageDetailApi,
} from '@/services/Enterprise/EnterpriseManageApi';

const enterpriseManageModel = {
    namespace: 'enterpriseManage',
    state: {
        enterpriseManageList: [], // 企业信息列表
        enterpriseManageListTotal: 0,
        enterpriseManageHistoryList: [], // 发奖记录列表
        enterpriseManageHistoryTotal: 0,
        editActInfo: {}, // 当前详情信息
    },
    effects: {
        /**
         * 企业信息列表
         */
        *getEnterpriseManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getEnterpriseManageListApi, options);

                yield put({
                    type: 'updateEnterpriseManageList',
                    enterpriseManageList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 开奖列表
         */
        *getEnterpriseManageHistoryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getEnterpriseManageListApi, options);

                yield put({
                    type: 'updateEnterpriseManageHistoryList',
                    enterpriseManageList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 查询详情
         */
        *initEditActInfo({ companyId }, { call, put, select }) {
            const { data } = yield call(getEnterpriseManageDetailApi, companyId);
            yield put({
                type: 'updateEditActInfo',
                info: data,
            });
        },
    },
    reducers: {
        updateEnterpriseManageList(state, { enterpriseManageList, total }) {
            return {
                ...state,
                enterpriseManageList,
                enterpriseManageListTotal: total,
            };
        },
        updateEnterpriseManageHistoryList(state, { list, total }) {
            return {
                ...state,
                enterpriseManageHistoryList: list,
                enterpriseManageHistoryListTotal: total,
            };
        },
        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },
    },
};
export default enterpriseManageModel;
