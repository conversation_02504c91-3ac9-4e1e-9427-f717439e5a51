import { Descriptions, Divider, Empty, Modal, Tree, Space, message, Spin } from 'antd';
import { Fragment, useEffect, useState, useMemo } from 'react';

import commonStyles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import { getCityAndStationByOperIdApi, getOperAndStationByCityApi } from '@/services/CommonApi';

const DetailsView = (props: any) => {
    const {
        dispatch,
        currentUser,
        global: { operatorList = [] },
        enterpriseManage: {
            editActInfo: { companyDetail = {}, scopRules = [] },
        },
    } = props;

    const [showStation, changeShowStation] = useState<boolean>(false);
    const [stationList, changeStation] = useState<any[]>([]);

    const [curRule, changeCurRule] = useState<any[]>([]);
    const [cityStationList, changeCityStationList] = useState<any[]>([]);

    const [lookStationLoading, toggleLookStationLoading] = useState<boolean>(false);

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);

    const treeProps = {
        defaultExpandAll: true,
    };

    const initStationsList = (rules: any) => {
        try {
            toggleLookStationLoading(true);
            Promise.all(rules?.map((ele: any) => initOperStationList(ele.operId)))
                .then((data) => {
                    const list = data.reduce((before, after) => [...before, ...after], []);

                    changeStation(list);
                    changeShowStation(true);
                    toggleLookStationLoading(false);
                })
                .catch((err) => {
                    toggleLookStationLoading(false);
                });
        } catch (error) {}
    };
    const initStationsListByCity = async (city: any) => {
        try {
            toggleLookStationLoading(true);
            const { data: cityAndStationList } = await getOperAndStationByCityApi({
                city,
            });

            const list = cityAndStationList;
            list.forEach((operItem: any) => {
                operItem.key = operItem.value;
                if (operItem.children) {
                    const childrenList: any[] = [];
                    operItem.children.forEach((stationItem: any) => {
                        childrenList.push({
                            ...stationItem,
                            key: stationItem.value,
                        });
                    });
                    operItem.children = childrenList;
                }
            });

            changeCityStationList(list);
            if (list && list.length > 0) {
                changeShowStation(true);
            } else {
                message.info('查无站点信息');
            }
        } catch (error) {
        } finally {
            toggleLookStationLoading(false);
        }
    };

    const formatStationList = useMemo(() => {
        const list: any[] = [];
        if (curRule.length == 0) {
            return cityStationList;
        }
        const operList = stationList;
        curRule.forEach((ruleItem) => {
            operList.forEach((operItem) => {
                if (ruleItem.operId == operItem.value) {
                    const item: any = {
                        key: operItem.key,
                        title: operItem.title,
                        value: operItem.value,
                    };
                    const cityList = operItem.children;
                    const childList: any[] = [];
                    if (cityList) {
                        cityList.forEach((cityItem: any) => {
                            const stations = cityItem.children;
                            const childStationList: any[] = [];
                            if (stations) {
                                stations.forEach((stationItem: any) => {
                                    if (
                                        ruleItem.operId == operItem.value &&
                                        (!ruleItem.stationId ||
                                            (ruleItem.stationId &&
                                                (ruleItem.stationId.length == 0 ||
                                                    ruleItem.stationId.includes(
                                                        stationItem.value,
                                                    ))))
                                    ) {
                                        stationItem.key = stationItem.value;
                                        childStationList.push(stationItem);
                                    }
                                });
                            }
                            if (childStationList.length > 0) {
                                cityItem.children = childStationList;
                                cityItem.key = cityItem.value;
                                childList.push(cityItem);
                            }
                        });
                    }
                    item.children = childList;
                    list.push(item);
                }
            });
        });

        const cityList = [...list];
        return cityList;
    }, [stationList, curRule, cityStationList]);

    const initOperStationList = async (operId: string) => {
        try {
            if (operId) {
                const { data: cityAndStationList } = await getCityAndStationByOperIdApi({ operId });
                let operName = '运营商';
                for (let index = 0; index < operatorList.length; index++) {
                    const element = operatorList[index];
                    if (element.operId == operId) {
                        operName = element.operNickname;
                        break;
                    }
                }

                const operItem = {
                    title: operName,
                    value: operId,
                    key: operId,
                    children: cityAndStationList,
                };
                return [operItem];
            }
        } catch (error) {}
        return undefined;
    };

    const closeShowStationEvent = () => {
        changeShowStation(false);
        changeStation([]);
        changeCurRule([]);
        changeCityStationList([]);
    };

    const detailsColumns = [
        {
            title: '可用场站',
            dataIndex: 'useStationName',
            render(text: string, record: any) {
                let stationText = '';
                const btnclass = record.rules ? commonStyles['table-btn'] : '';
                if (!currentUser.operId) {
                    stationText = record.useStationName;
                } else {
                    stationText = '全部场站';
                    if (record.rules) {
                        const rulesItem = record.rules[0];
                        if (rulesItem && rulesItem.stationId && rulesItem.stationId.length > 0) {
                            stationText = '部分场站';
                        }
                    }
                }
                return (
                    <Spin spinning={lookStationLoading}>
                        <span
                            className={btnclass}
                            title={stationText}
                            onClick={() => {
                                if (lookStationLoading) {
                                    return;
                                }
                                if (record.rules && record.rules.length > 0) {
                                    // 获取站点信息
                                    initStationsList(record.rules);
                                    changeCurRule(record.rules);
                                } else if (record.city) {
                                    initStationsListByCity(record.city);
                                }
                            }}
                        >
                            {stationText}
                        </span>
                    </Spin>
                );
            },
        },
        {
            title: '享受优惠费率',
            dataIndex: 'discountFlagName',
        },
        {
            title: '电费享受折扣',
            dataIndex: 'elecDiscount',
            render(text: any, record: any) {
                return (
                    <span title={text}>
                        {companyDetail?.companyDiscountType == '1'
                            ? '个人优惠'
                            : record.elecDiscount >= 0
                            ? `${record.elecDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
        {
            title: '服务费享受折扣',
            dataIndex: 'serviceDiscount',
            render(text: any, record: any) {
                return (
                    <span title={text}>
                        {companyDetail?.companyDiscountType == '1'
                            ? '个人优惠'
                            : record.serviceDiscount >= 0
                            ? `${record.serviceDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
        {
            title: '附加费享受折扣',
            dataIndex: 'incrementDiscount',
            render(text: any, record: any) {
                return (
                    <span title={text}>
                        {companyDetail?.companyDiscountType == '1'
                            ? '个人优惠'
                            : record.incrementDiscount >= 0
                            ? `${record.incrementDiscount}折`
                            : '正常计费'}
                    </span>
                );
            },
        },
    ];
    return (
        <Fragment>
            <Descriptions title="基础信息">
                <Descriptions.Item label="企业名称">
                    {companyDetail?.companyName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="企业简称">
                    {companyDetail?.companyNickname || ''}
                </Descriptions.Item>
                <Descriptions.Item label="合作方">
                    {companyDetail?.partnerName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="联系地址">
                    {companyDetail?.pageCompanyAddress || ''}
                </Descriptions.Item>
                <Descriptions.Item label="联系人">
                    {companyDetail?.contacts || ''}
                </Descriptions.Item>
                <Descriptions.Item label="联系人手机">
                    {companyDetail?.mobile || ''}
                </Descriptions.Item>
                <Descriptions.Item label="通知">
                    {companyDetail?.contactEmail || ''}
                </Descriptions.Item>
                <Descriptions.Item label="管理员手机号">
                    {companyDetail?.alipayManageAccount || ''}
                </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="支付配置">
                <Descriptions.Item label="订单支付方式">
                    <Space>
                        <text>个人支付</text>
                        <text> {companyDetail?.companyPayType == '1' ? '因公代付' : ''}</text>
                    </Space>
                </Descriptions.Item>
                {companyDetail?.accountType === '02' && (
                    <Fragment>
                        <Descriptions.Item label="代扣支付宝账号">
                            {companyDetail?.alipayWithholdAccount || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="订单额度">
                            {companyDetail?.companyAmt || '-'}
                        </Descriptions.Item>
                    </Fragment>
                )}
                {companyDetail?.accountType === '01' && (
                    <Fragment>
                        <Descriptions.Item label="银行账号">
                            {companyDetail?.paymentBankAccount || ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="代付额度">
                            {companyDetail?.companyAmt || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="代付渠道">
                            {companyDetail?.paymentChannelName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="余额告警">
                            {companyDetail?.balWarnFlag ? '开' : '关'}
                        </Descriptions.Item>
                        {companyDetail?.balWarnFlag && (
                            <>
                                <Descriptions.Item label="告警值">
                                    {companyDetail?.warnBalance
                                        ? `${companyDetail?.warnBalance}万`
                                        : '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="极限值">
                                    {companyDetail?.limitBalance
                                        ? `${companyDetail?.limitBalance}万`
                                        : '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="告警邮箱">
                                    {companyDetail?.warnMail || '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="抄送邮箱">
                                    {companyDetail?.copyMail || '-'}
                                </Descriptions.Item>
                                <Descriptions.Item label="告警手机">
                                    {companyDetail?.warnMobile || '-'}
                                </Descriptions.Item>
                            </>
                        )}
                    </Fragment>
                )}
            </Descriptions>

            <Divider />

            <Descriptions title="企业配置">
                <Descriptions.Item label="优惠方式">
                    {companyDetail?.companyDiscountType == '1' ? '个人优惠' : '企业优惠'}
                </Descriptions.Item>
            </Descriptions>

            <TablePro
                name="entirment"
                style={{ width: '100%' }}
                scroll={{ x: 'max-content' }}
                rowKey={(record: any, index: number) => index}
                locale={{
                    emptyText: (
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无配置信息" />
                    ),
                }}
                dataSource={scopRules}
                columns={detailsColumns}
                pagination={false}
                sticky={false}
            />

            <Divider />

            <Descriptions title="开票资料">
                {companyDetail?.invoiceTitle ? (
                    <Descriptions.Item label="发票抬头">
                        {companyDetail?.invoiceTitle || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.taxNum ? (
                    <Descriptions.Item label="纳税人识别号">
                        {companyDetail?.taxNum || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.taxpayerTypeName ? (
                    <Descriptions.Item label="纳税人资格种类">
                        {companyDetail?.taxpayerTypeName || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.bankAccount ? (
                    <Descriptions.Item label="银行账号">
                        {companyDetail?.bankAccount || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.bankName ? (
                    <Descriptions.Item label="开户行名称">
                        {companyDetail?.bankName || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.qualificBeginTime ? (
                    <Descriptions.Item label="资格开始时间">
                        {companyDetail?.qualificBeginTime || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.invoiceAddress ? (
                    <Descriptions.Item label="开票地址">
                        {companyDetail?.invoiceAddress || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.invoicePhone ? (
                    <Descriptions.Item label="开票电话">
                        {companyDetail?.invoicePhone || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.receiptName ? (
                    <Descriptions.Item label="收件姓名">
                        {companyDetail?.receiptName || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.pageInvoiceAddress ? (
                    <Descriptions.Item label="收件地址">
                        {companyDetail?.pageInvoiceAddress || ''}
                    </Descriptions.Item>
                ) : null}
                {companyDetail?.receiptPhone ? (
                    <Descriptions.Item label="收件电话">
                        {companyDetail?.receiptPhone || ''}
                    </Descriptions.Item>
                ) : null}
            </Descriptions>

            <Modal
                title="查看场站"
                visible={showStation}
                onCancel={closeShowStationEvent}
                footer={false}
                destroyOnClose
                maskClosable={false}
            >
                <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                    <Tree treeData={formatStationList} {...treeProps} />
                </div>
            </Modal>
        </Fragment>
    );
};

export default DetailsView;
