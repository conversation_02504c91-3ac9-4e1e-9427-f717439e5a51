import { ActionType, ProColumnType } from '@ant-design/pro-table';
import { Modal, Typography } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

import XdtProTable from '@/components/XdtProTable';
import { queryFrozenRecordPageApi } from '@/services/Enterprise/EnterpriseManageApi';

const FreezeRecords = (props: any, ref: any) => {
    const { companyId } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const actionRef = useRef<ActionType>();

    useImperativeHandle(ref, () => ({
        open: () => {
            actionRef?.current?.reloadAndRest?.();
            setVisible(true);
        },
    }));

    const onCancel = () => {
        setVisible(false);
    };

    const columns: ProColumnType<API.FrozenRecordVo>[] = [
        {
            title: '订单时间',
            dataIndex: 'createTime',
            width: 120,
        },
        {
            title: '充电订单号',
            dataIndex: 'orderNo',
            width: 120,
        },
        {
            title: '司机姓名',
            dataIndex: 'custName',
            width: 120,
        },
        {
            title: '司机手机',
            dataIndex: 'mobile',
            width: 120,
        },
        {
            title: '冻结金额',
            dataIndex: 'frozenAmt',
            valueType: 'money',
            width: 120,
        },
    ];
    return (
        <Modal
            title="冻结明细"
            cancelText="关闭"
            visible={visible}
            okButtonProps={{ hidden: true }}
            onCancel={onCancel}
            width={848}
        >
            <Typography.Text type="danger">
                注：司机下单时将进行余额冻结，订单完成结算时将解除冻结并转成消费记录
            </Typography.Text>
            <XdtProTable
                actionRef={actionRef}
                columns={columns}
                requestApi={queryFrozenRecordPageApi}
                prefixKey="freezeRecords"
                scroll={{ x: 'max-content', y: 400 }}
                rowKey="frozenId"
                otherParams={{ companyId }}
                search={false}
            />
        </Modal>
    );
};

export default forwardRef(FreezeRecords);
