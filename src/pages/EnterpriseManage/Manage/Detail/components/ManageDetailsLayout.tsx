import { Card, Tabs } from 'antd';
import { useEffect, useState } from 'react';

import styles from '../../style.less';

import DetailsView from './DetailsView';

const { TabPane } = Tabs;

const ManageDetailsLayout = (props: any) => {
    const { match, dispatch, infoLoading, searchCondition } = props;

    const {
        params: { companyId },
    } = match;

    const [conditionParams, updateConditionParams] = useState(undefined);
    useEffect(() => {
        if (searchCondition != conditionParams) {
            updateConditionParams(searchCondition);
        }
    }, [searchCondition]);

    useEffect(() => {
        let isEqual = true;
        searchCondition &&
            Object.keys(searchCondition).map((ele) => {
                if (searchCondition?.[ele]?.length && !conditionParams?.[ele]) {
                    // 新增
                    isEqual = false;
                } else if (
                    conditionParams?.[ele] &&
                    conditionParams?.[ele] != searchCondition[ele]
                ) {
                    // 删除
                    isEqual = false;
                }
            });
        if (!isEqual) {
            searchData(searchCondition.companyId);
        }
    }, [conditionParams]);

    const searchData = (_companyId: string) => {
        dispatch({
            type: 'enterpriseManage/initEditActInfo',
            companyId: _companyId,
        });
    };

    return (
        <Card loading={infoLoading} className={styles['order-details-page']}>
            <Tabs defaultActiveKey="1">
                <TabPane tab="详情" key="1">
                    <DetailsView {...props} />
                </TabPane>
            </Tabs>
        </Card>
    );
};

export default ManageDetailsLayout;
