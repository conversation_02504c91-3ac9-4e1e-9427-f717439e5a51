import { Col, DatePicker, Descriptions, Form, Select, Space, Typography } from 'antd';
import { usePagination } from 'ahooks';
import moment from 'moment';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { queryCompanyBalanceRecordPageApi } from '@/services/MngCustApi';
import { useEffect, useRef } from 'react';
import FreezeRecords from './FreezeRecords';

const RechargeHistory = (props: any) => {
    const { companyId, companyDetail } = props;
    const [form] = Form.useForm();
    const freezeRef = useRef<ActionRef.OpenModal>();
    const showFrozenModal = () => {
        freezeRef?.current?.open();
    };

    const {
        run: searchData,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params = {}) => {
            const response = await queryCompanyBalanceRecordPageApi({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records || response?.data?.list || [],
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const onFinish = (formData: API.QrCodeListRequst) => {
        const params = {
            ...formData,
            beginDate:
                formData?.operDates &&
                formData.operDates?.[0] &&
                moment(formData.operDates[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss'),
            endDate:
                formData?.operDates &&
                formData.operDates?.[1] &&
                moment(formData.operDates[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss'),
            operDates: undefined,
            companyId: companyId,
        };
        searchData({ current: pagination?.current, pageSize: pagination?.pageSize }, params);
    };

    const resetForm = () => {
        form.resetFields();
        searchData({ current: 1, pageSize: pagination?.pageSize }, { companyId });
    };

    useEffect(() => {
        if (companyId) {
            searchData({ current: 1, pageSize: pagination?.pageSize }, { companyId });
        }
    }, [companyId]);

    const columns = [
        {
            title: '操作时间',
            dataIndex: 'createTime',
            width: 160,
        },
        {
            title: '变动金额',
            dataIndex: 'amt',
            width: 120,
        },
        {
            title: '变动完可用余额',
            dataIndex: 'afterCompanyAmt',
            width: 160,
        },
        {
            title: '变动类型',
            dataIndex: 'typeName',
            width: 120,
        },
        {
            title: '变动说明',
            dataIndex: 'remark',
            width: 160,
        },
        {
            title: '操作员',
            dataIndex: 'operator',
            width: 120,
        },
    ];
    return (
        <Space direction="vertical">
            <Descriptions title="充值记录" column={4}>
                <Descriptions.Item label="车队可用余额">{companyDetail?.balance}</Descriptions.Item>
                <Descriptions.Item label="累计充值金额">
                    {companyDetail?.companyAmt}
                </Descriptions.Item>
                <Descriptions.Item label="累计消费金额">{companyDetail?.useAmt}</Descriptions.Item>
                <Descriptions.Item label="冻结中金额">
                    <Space>
                        {companyDetail?.frozenAmt}
                        {companyDetail?.frozenAmt > 0 && (
                            <Typography.Link onClick={showFrozenModal}>查看</Typography.Link>
                        )}
                    </Space>
                </Descriptions.Item>
            </Descriptions>
            <Form
                form={form}
                onFinish={onFinish}
                scrollToFirstError
                labelCol={{ flex: '0 0 90px' }}
            >
                <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 2}>
                    <Col span={8}>
                        <Form.Item label="变动类型" name="type">
                            <Select
                                allowClear
                                showSearch
                                options={[
                                    {
                                        value: '3',
                                        label: '充值',
                                    },
                                    {
                                        value: '1',
                                        label: '订单支付',
                                    },
                                    {
                                        value: '2',
                                        label: '订单补收',
                                    },
                                    {
                                        value: '4',
                                        label: '赠送',
                                    },
                                ]}
                                placeholder="请选择"
                            />
                        </Form.Item>
                    </Col>

                    <Col span={8}>
                        <Form.Item label="操作时间" name="operDates">
                            <DatePicker.RangePicker
                                format="YYYY-MM-DD"
                                allowClear
                                placeholder={['开始时间', '结束时间']}
                            />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="companyId"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
            />
            <FreezeRecords ref={freezeRef} companyId={companyId} />
        </Space>
    );
};

export default RechargeHistory;
