import { ExclamationCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Descriptions, Modal, Button, Space, Card } from 'antd';
import { Fragment, useEffect, useMemo } from 'react';
import { connect } from 'umi';

import styles from '../style.less';
import Yuan from '@/utils/Yuan';
import { updateCompanyStatusApi } from '@/services/Enterprise/EnterpriseManageApi';
import { COMPANY_STATUS } from '@/config/declare';
import ManageDetailsLayout from './components/ManageDetailsLayout';
import RechargeHistory from './components/RechargeHistory';

const { confirm } = Modal;

const ManageDetailsPage = (props: any) => {
    const {
        dispatch,
        history,
        enterpriseManage: {
            editActInfo: { companyDetail = {}, scopRules = [] },
        },
        match,
    } = props;
    const {
        params: { companyId },
    } = match;

    useEffect(() => {
        if (companyId) {
            dispatch({
                type: 'enterpriseManage/initEditActInfo',
                companyId,
            });
        }
        return () => {
            dispatch({
                type: 'enterpriseManage/updateEditActInfo',
                info: {},
            });
        };
    }, [companyId]);

    const orderInfo = useMemo(
        () => (
            <Fragment>
                <Descriptions column={3}>
                    <Descriptions.Item label="员工数量">
                        {companyDetail?.starffNum || '0'}人
                    </Descriptions.Item>
                    <Descriptions.Item label="累计消费金额">
                        <Yuan>{companyDetail?.totalAmt || 0}</Yuan> 元
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                        {companyDetail?.createTime || ''}
                    </Descriptions.Item>
                </Descriptions>
            </Fragment>
        ),
        [companyDetail],
    );
    const orderStatus = useMemo(
        () => (
            <div className={styles['status-view']}>
                <p>
                    <span className={styles.title}>状态</span>
                    <span className={styles['status-name']}>
                        {companyDetail?.companyStatusName || ''}
                    </span>
                </p>
                <p>
                    <Space>
                        {companyDetail?.companyStatus == COMPANY_STATUS.DOING ? (
                            <Button
                                onClick={() => {
                                    stopEnterpriseManageEvent();
                                }}
                            >
                                停用
                            </Button>
                        ) : null}

                        {companyDetail?.companyStatus == COMPANY_STATUS.END ? (
                            <Button
                                onClick={() => {
                                    startEnterpriseManageEvent();
                                }}
                            >
                                启用
                            </Button>
                        ) : null}

                        <Button type="primary" onClick={() => editEnterpriseManageEvent()}>
                            编辑
                        </Button>
                    </Space>
                </p>
            </div>
        ),
        [companyDetail],
    );

    const editEnterpriseManageEvent = () => {
        history.push(`/userCenter/enterprise/manage/list/update/${companyId}`);
    };

    const stopEnterpriseManageEvent = async () => {
        confirm({
            title: `确定停止该企业?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await updateCompanyStatusApi({
                        companyId,
                        companyStatus: COMPANY_STATUS.END,
                    });
                    dispatch({
                        type: 'enterpriseManage/initEditActInfo',
                        companyId,
                    });
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const startEnterpriseManageEvent = async () => {
        confirm({
            title: `确定启用该企业?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await updateCompanyStatusApi({
                        companyId,
                        companyStatus: COMPANY_STATUS.DOING,
                    });
                    dispatch({
                        type: 'enterpriseManage/initEditActInfo',
                        companyId,
                    });
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {companyDetail?.companyName || ''}
                </div>
            }
            content={orderInfo}
            extraContent={orderStatus}
        >
            <Space direction="vertical" size={24}>
                <ManageDetailsLayout {...props} />
                {companyDetail?.offlineBalancePayType === '1' && (
                    <div style={{ background: '#fff', padding: '24px' }}>
                        <RechargeHistory companyId={companyId} companyDetail={companyDetail} />
                    </div>
                )}
            </Space>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user, enterpriseManage, loading }: any) => ({
    global,
    currentUser: user.currentUser,
    enterpriseManage,
    infoLoading: loading.effects['enterpriseManage/initEditActInfo'],
}))(ManageDetailsPage);
