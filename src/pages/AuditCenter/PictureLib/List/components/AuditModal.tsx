import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Checkbox, message, Modal, Popover, Row, Space, Spin, Typography } from 'antd';
import React, { forwardRef, Fragment, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { PICTURE_CHECK_STATUS_ENUM } from '@/constants/auditPicture';
import {
    examineSingle<PERSON>pi,
    getImageSafeDetailApi,
    uploadImageSafeApi,
} from '@/services/AuditCenter/PictureApi';
import AuditDetail from './AuditDetail';
import NotPassedReason from './NotPassedReason';
import UploadImage from './UploadImage';

const shadowColor = {
    [PICTURE_CHECK_STATUS_ENUM.AUDITING]: '#3A92FE',
    [PICTURE_CHECK_STATUS_ENUM.PASSED]: '#37B200',
    [PICTURE_CHECK_STATUS_ENUM.NOT_PASSED]: '#FF0000',
    [PICTURE_CHECK_STATUS_ENUM.AT_RISK]: '#EE850B',
    [PICTURE_CHECK_STATUS_ENUM.FAILED]: '#FF0000',
};
export type PageInfo = {
    index: number;
    pageIndex: number;
    pageSize: number;
    total: number;
};

const AuditModal = (props: any, ref: any) => {
    const { goNext, goPre, callback } = props;
    const [pageInfo, setPageInfo] = useState<PageInfo | undefined>();
    const [visible, setVisible] = useState<boolean>(false);
    const [autoGotoNext, setAutoGotoNext] = useState<boolean>(true);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [showReason, setShowReason] = useState<boolean>(false);
    const uploadRef = useRef<ActionRef.OpenModal>();

    const { run, data, mutate, loading } = useRequest(
        (id) => {
            return getImageSafeDetailApi(id);
        },
        {
            manual: true,
        },
    );

    useImperativeHandle(ref, () => ({
        open(record: any) {
            setPageInfo({
                index: record?.index,
                pageIndex: record?.pageIndex,
                pageSize: record?.pageSize,
                total: record.total,
            });
            run(record?.id);
            setVisible(true);
        },
    }));

    const onCancel = () => {
        setPageInfo(undefined);
        mutate(undefined);
        setIsEdit(false);
        setVisible(false);
        callback?.();
    };

    const gotoPrev = async () => {
        const result = await goPre(pageInfo);
        if (result) {
            setPageInfo({
                ...result,
            });
            run(result?.id);
        } else if (!arrowDisabled.prev) {
            message.error('读取下一条失败');
        }
    };

    const gotoNext = async () => {
        const result = await goNext(pageInfo);
        if (result) {
            setPageInfo({
                ...result,
            });
            run(result?.id);
        } else if (!arrowDisabled.next) {
            message.error('读取下一条失败');
        }
    };

    const arrowDisabled = useMemo(() => {
        return {
            prev: pageInfo?.index === 0 && pageInfo.pageIndex === 1,
            next:
                pageInfo &&
                pageInfo?.pageSize * (pageInfo?.pageIndex - 1) + pageInfo?.index + 1 ===
                    pageInfo?.total,
        };
    }, [pageInfo]);

    const { run: singleSubmit, loading: singleLoading } = useRequest(
        (params) => {
            return examineSingleApi(params);
        },
        {
            manual: true,
            onSuccess(res) {
                if (res.ret === 200) {
                    message.success('审核成功');
                    if (isEdit) {
                        setIsEdit(false);
                    }
                    if (autoGotoNext && !arrowDisabled.next) {
                        gotoNext();
                    } else {
                        onCancel();
                    }
                }
            },
        },
    );

    const { run: uploadImageSafe, loading: uploadLoading } = useRequest(
        (params) => {
            return uploadImageSafeApi(params);
        },
        {
            manual: true,
            onSuccess(res) {
                if (res.ret === 200) {
                    message.success('上传成功');
                }
            },
        },
    );

    const submitPassed = () => {
        const params = {
            checkStatus: PICTURE_CHECK_STATUS_ENUM.PASSED,
            id: data?.data?.id || '',
        };
        singleSubmit(params);
    };

    const submitNotPassed = (reason: string) => {
        reasonHide();
        const params = {
            checkStatus: PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
            id: data?.data?.id || '',
            failReasonCode: reason,
        };
        singleSubmit(params);
    };

    const uploadImages = (files: any) => {
        const params: any = {
            fileParams: files?.map((v: any) => {
                return {
                    fileId: v?.uid,
                    relativePath: v?.relativePath,
                };
            }),
            id: data?.data?.id,
        };
        uploadImageSafe(params);
    };

    const reasonHide = () => {
        setShowReason(false);
    };

    const handleNotPassedClick = (open: boolean) => {
        setShowReason(open);
    };

    return (
        <Modal
            visible={visible}
            title="图片详情"
            footer={
                <Fragment>
                    <Row justify="center" style={{ marginTop: '12px' }}>
                        <Space size="large">
                            {([
                                PICTURE_CHECK_STATUS_ENUM.AT_RISK,
                                PICTURE_CHECK_STATUS_ENUM.AUDITING,
                                PICTURE_CHECK_STATUS_ENUM.FAILED,
                            ].includes(data?.data?.checkStatus as string) ||
                                (isEdit &&
                                    data?.data?.checkStatus ===
                                        PICTURE_CHECK_STATUS_ENUM.NOT_PASSED)) && (
                                <Button
                                    type="primary"
                                    onClick={submitPassed}
                                    loading={singleLoading}
                                >
                                    通过
                                </Button>
                            )}
                            {([
                                PICTURE_CHECK_STATUS_ENUM.AT_RISK,
                                PICTURE_CHECK_STATUS_ENUM.AUDITING,
                                PICTURE_CHECK_STATUS_ENUM.FAILED,
                            ].includes(data?.data?.checkStatus as string) ||
                                (isEdit &&
                                    data?.data?.checkStatus ===
                                        PICTURE_CHECK_STATUS_ENUM.PASSED)) && (
                                <Popover
                                    content={
                                        <NotPassedReason
                                            callback={submitNotPassed}
                                            hideFunc={reasonHide}
                                        />
                                    }
                                    trigger="click"
                                    visible={showReason}
                                    onVisibleChange={handleNotPassedClick}
                                >
                                    <Button
                                        type="default"
                                        style={{
                                            border: '1px solid #FF0000',
                                            color: '#FF0000',
                                        }}
                                        loading={singleLoading}
                                    >
                                        不通过
                                    </Button>
                                </Popover>
                            )}
                            {isEdit && (
                                <Button
                                    type="default"
                                    onClick={() => {
                                        setIsEdit(false);
                                    }}
                                >
                                    取消
                                </Button>
                            )}
                        </Space>
                        {!isEdit &&
                            [
                                PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                                PICTURE_CHECK_STATUS_ENUM.PASSED,
                            ].includes(data?.data?.checkStatus as string) && (
                                <Space size="large" style={{ marginBottom: '12px' }}>
                                    <Button
                                        type="default"
                                        onClick={() => {
                                            setIsEdit(true);
                                        }}
                                    >
                                        修改结果
                                    </Button>
                                    <Button
                                        type="default"
                                        onClick={() => {
                                            uploadRef.current?.open();
                                        }}
                                        loading={uploadLoading}
                                    >
                                        图片上传
                                    </Button>
                                </Space>
                            )}
                    </Row>
                    {(isEdit ||
                        [
                            PICTURE_CHECK_STATUS_ENUM.AT_RISK,
                            PICTURE_CHECK_STATUS_ENUM.AUDITING,
                            PICTURE_CHECK_STATUS_ENUM.FAILED,
                        ].includes(data?.data?.checkStatus as string)) && (
                        <Row justify="center" style={{ marginTop: '12px', marginBottom: '12px' }}>
                            <Space size="small">
                                <Checkbox
                                    checked={autoGotoNext}
                                    onClick={() => setAutoGotoNext(!autoGotoNext)}
                                    disabled={arrowDisabled.next}
                                >
                                    审核后自动进入下一张图片
                                </Checkbox>
                            </Space>
                        </Row>
                    )}
                </Fragment>
            }
            closable
            onCancel={onCancel}
            width={842}
        >
            <div style={{ position: 'relative', padding: '0 48px' }}>
                <Spin spinning={loading}>
                    <Space style={{ width: '100%' }} direction="vertical" align="center">
                        <Typography.Title
                            level={3}
                            style={{
                                textShadow: `0 2px 6px ${
                                    shadowColor[data?.data?.checkStatus as string] || 'blue'
                                }`,
                            }}
                        >
                            {data?.data?.checkStatusName}
                        </Typography.Title>
                        <div
                            style={{
                                maxHeight: 'calc(100vh - 380px)',
                                overflowY: 'scroll',
                                overflowX: 'hidden',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    flex: '0 0 48px',
                                    justifyContent: 'flex-start',
                                    position: 'absolute',
                                    top: '40%',
                                    left: '-48px',
                                }}
                            >
                                <LeftOutlined
                                    onClick={gotoPrev}
                                    style={{
                                        fontSize: '40px',
                                        cursor: arrowDisabled.prev ? 'not-allowed' : 'pointer',
                                        color: arrowDisabled.prev ? '#ccc' : undefined,
                                    }}
                                />
                            </div>
                            <AuditDetail dataSource={data?.data} />
                            <div
                                style={{
                                    display: 'flex',
                                    flex: '0 0 48px',
                                    justifyContent: 'flex-end',
                                    position: 'absolute',
                                    top: '40%',
                                    right: '-48px',
                                }}
                            >
                                <RightOutlined
                                    onClick={gotoNext}
                                    size={40}
                                    style={{
                                        fontSize: '40px',
                                        cursor: arrowDisabled.next ? 'not-allowed' : 'pointer',
                                        color: arrowDisabled.next ? '#ccc' : undefined,
                                    }}
                                />
                            </div>
                        </div>
                    </Space>
                </Spin>
            </div>
            <UploadImage ref={uploadRef} callback={uploadImages} />
        </Modal>
    );
};

export default React.memo(forwardRef(AuditModal));
