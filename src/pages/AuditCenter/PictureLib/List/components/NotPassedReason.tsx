import { Button, Form, Row, Space } from 'antd';
import React from 'react';
import ReasonForm from './ReasonForm';

const NotPassedReason: React.FC<{ callback: (reason: string) => void; hideFunc: () => void }> = ({
    callback,
    hideFunc,
}) => {
    const [form] = Form.useForm();

    const submit = async () => {
        try {
            await form.validateFields();
            const reason = form.getFieldValue('reason');
            if (reason) {
                callback?.(reason.pop());
            }
        } catch (error) {}
    };

    return (
        <div style={{ width: '368px', padding: '24px 12px 0px 12px' }}>
            <Form form={form}>
                <Form.Item
                    label="原因"
                    name="reason"
                    required
                    rules={[{ required: true, message: '请选择原因' }]}
                >
                    <ReasonForm />
                </Form.Item>
                <Form.Item>
                    <Row justify="end" style={{ marginTop: '8px' }}>
                        <Space>
                            <Button type="default" onClick={hideFunc}>
                                取消
                            </Button>
                            <Button type="primary" onClick={submit}>
                                确认
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </div>
    );
};

export default React.memo(NotPassedReason);
