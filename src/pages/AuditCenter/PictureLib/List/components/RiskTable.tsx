import { getImageSafeDetailApi } from '@/services/AuditCenter/PictureApi';
import { useRequest } from 'ahooks';
import { Modal, Table } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

const RiskTable = (props: any, ref: any) => {
    const [visible, setVisible] = useState<boolean>(false);
    useImperativeHandle(ref, () => ({
        open(id: string | number) {
            run(id);
            setVisible(true);
        },
    }));

    const onCancel = () => {
        mutate(undefined);
        setVisible(false);
    };

    const { run, data, mutate, loading } = useRequest(
        (id) => {
            return getImageSafeDetailApi(id);
        },
        {
            manual: true,
        },
    );

    return (
        <Modal
            title="风险内容"
            visible={visible}
            cancelText="确定"
            okButtonProps={{ hidden: true }}
            onCancel={onCancel}
            width={648}
            destroyOnClose
        >
            <Table
                pagination={false}
                dataSource={data?.data?.labelList}
                loading={loading}
                columns={[
                    {
                        title: '标签值',
                        dataIndex: 'label',
                        width: 21,
                    },
                    {
                        title: '中文含义',
                        dataIndex: 'labelName',
                        width: 260,
                    },
                    {
                        title: '置信分',
                        dataIndex: 'confidence',
                        width: 130,
                    },
                ]}
            />
        </Modal>
    );
};

export default forwardRef(RiskTable);
