import { useRequest } from 'ahooks';
import { Button, Form, message, Modal, Radio, Row, Space, Typography } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { PICTURE_CHECK_STATUS_ENUM } from '@/constants/auditPicture';
import { batchExamineSingleApi } from '@/services/AuditCenter/PictureApi';
import AuditDetailSimple from './AuditDetailSimple';
import ReasonForm from './ReasonForm';
import MultiReasonModal from './MultiReasonModal';

const shadowColor = {
    [PICTURE_CHECK_STATUS_ENUM.AUDITING]: '#3A92FE',
    [PICTURE_CHECK_STATUS_ENUM.PASSED]: '#37B200',
    [PICTURE_CHECK_STATUS_ENUM.NOT_PASSED]: '#FF0000',
    [PICTURE_CHECK_STATUS_ENUM.AT_RISK]: '#EE850B',
    [PICTURE_CHECK_STATUS_ENUM.FAILED]: '#FF0000',
};

const MultiAuditModal = (props: any, ref: any) => {
    const { callback } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [records, setRecords] = useState<MNG_ALY.ImageSafeQueryVo[]>([]);
    const [form] = Form.useForm();
    const [allExpanded, setAllExpanded] = useState<
        | {
              expanded: boolean;
              date: Date;
          }
        | undefined
    >(undefined);
    const reasonRef = useRef<ActionRef.OpenModal>();

    useImperativeHandle(ref, () => ({
        open(items: any) {
            setRecords(items);
            form.setFieldsValue({
                list: items?.map((v: MNG_ALY.ImageSafeQueryVo) => ({
                    data: v,
                    passed: PICTURE_CHECK_STATUS_ENUM.PASSED,
                })),
            });
            setVisible(true);
        },
    }));

    const showStatus = useMemo(() => {
        return records?.[0]?.checkStatus || '';
    }, [records]);

    const onCancel = () => {
        form.resetFields();
        setAllExpanded(undefined);
        setRecords([]);
        setVisible(false);
    };

    const { run: batchSubmit, loading: singleLoading } = useRequest(
        (params) => {
            return batchExamineSingleApi(params);
        },
        {
            manual: true,
            onSuccess(res) {
                if (res.ret === 200) {
                    message.success('审核成功');
                    onCancel();
                    callback?.();
                }
            },
        },
    );

    const submitPassed = async () => {
        try {
            await form.validateFields();
            const formData = form.getFieldsValue(true);
            Modal.confirm({
                title: '提交确认',
                content: '确认处理结果无误后提交',
                onOk() {
                    const params = {
                        imageSafeExamineParams: formData?.list?.map((v: any) => ({
                            checkStatus: v?.passed,
                            id: v?.data?.id,
                            failReasonCode: v?.reason?.pop(),
                        })),
                    };
                    batchSubmit(params);
                },
            });
        } catch (error) {}
    };

    const submitAllPassed = () => {
        Modal.confirm({
            title: '批量通过',
            content: '以上图片将批量审批通过',
            onOk() {
                batchSubmit({
                    imageSafeExamineParams: form.getFieldValue('list')?.map((v: any) => ({
                        checkStatus: PICTURE_CHECK_STATUS_ENUM.PASSED,
                        id: v?.data?.id,
                    })),
                });
            },
            okText: '确定',
            cancelText: '取消',
        });
    };
    const submitAllNotPassed = ({ finalReason }: any) => {
        batchSubmit({
            imageSafeExamineParams: form.getFieldValue('list')?.map((v: any) => ({
                checkStatus: PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                id: v?.data?.id,
                failReasonCode: finalReason,
            })),
        });
    };

    const showAllNotPassedReason = () => {
        reasonRef.current?.open({
            callback: submitAllNotPassed,
        });
    };

    const changeAllPassed = () => {
        form.setFieldsValue({
            list: form.getFieldValue('list')?.map((v: any) => ({
                data: v?.data,
                passed: PICTURE_CHECK_STATUS_ENUM.PASSED,
            })),
        });
    };

    const setAllFailReason = ({ reasons }: any) => {
        console.debug('changeAllNotPassed', reasons);
        form.setFieldsValue({
            list: form.getFieldValue('list')?.map((v: any) => ({
                data: v?.data,
                passed: PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                reason: reasons,
            })),
        });
    };

    const changeAllNotPassed = (e: any) => {
        e.stopPropagation();

        console.debug('▁ ▂ ▃ ▄ ▅ ▆ ▇ ▉ changeAllNotPassed open');
        reasonRef.current?.open({
            callback: setAllFailReason,
        });
    };

    return (
        <Modal
            visible={visible}
            title="批量处理"
            footer={
                <Row justify="center" style={{ marginTop: '12px' }}>
                    <Space size="large">
                        {(showStatus === PICTURE_CHECK_STATUS_ENUM.AT_RISK ||
                            showStatus === PICTURE_CHECK_STATUS_ENUM.FAILED) && (
                            <Button type="primary" onClick={submitPassed} loading={singleLoading}>
                                提交
                            </Button>
                        )}
                        {showStatus === PICTURE_CHECK_STATUS_ENUM.NOT_PASSED && (
                            <Button
                                type="primary"
                                onClick={submitAllPassed}
                                loading={singleLoading}
                            >
                                批量通过
                            </Button>
                        )}
                        {showStatus === PICTURE_CHECK_STATUS_ENUM.PASSED && (
                            <Button
                                type="default"
                                danger
                                onClick={showAllNotPassedReason}
                                loading={singleLoading}
                            >
                                批量不通过
                            </Button>
                        )}
                        <Button type="default" loading={singleLoading} onClick={onCancel}>
                            取消
                        </Button>
                    </Space>
                </Row>
            }
            closable
            onCancel={onCancel}
            width={980}
        >
            <div>
                <Space style={{ width: '100%' }} direction="vertical" align="center">
                    <Typography.Title
                        level={3}
                        style={{
                            textShadow: `0 2px 6px ${
                                shadowColor[records?.[0]?.checkStatus as string] || 'blue'
                            }`,
                        }}
                    >
                        {records?.[0]?.checkStatusName}
                    </Typography.Title>
                    <Row justify="start" style={{ width: '920px', marginBottom: '12px' }}>
                        <Space>
                            <Button
                                type="default"
                                onClick={() => {
                                    setAllExpanded({ expanded: true, date: new Date() });
                                }}
                            >
                                全部展开
                            </Button>
                            <Button
                                type="default"
                                onClick={() => {
                                    setAllExpanded({ expanded: false, date: new Date() });
                                }}
                            >
                                全部收起
                            </Button>
                        </Space>
                    </Row>
                    {(showStatus === PICTURE_CHECK_STATUS_ENUM.AT_RISK ||
                        showStatus === PICTURE_CHECK_STATUS_ENUM.FAILED) && (
                        <Row justify="start" style={{ width: '920px', marginBottom: '12px' }}>
                            <Space>
                                <Button type="primary" ghost onClick={changeAllPassed}>
                                    全部通过
                                </Button>
                                <Button type="default" danger onClick={changeAllNotPassed}>
                                    全部不通过
                                </Button>
                            </Space>
                        </Row>
                    )}
                    <div
                        style={{
                            maxHeight: 'calc(100vh - 460px)',
                            overflowY: 'scroll',
                            overflowX: 'hidden',
                        }}
                    >
                        <Form form={form} layout="inline">
                            <Form.List
                                name="list"
                                rules={[
                                    {
                                        validator(rule, value) {
                                            if (
                                                [
                                                    PICTURE_CHECK_STATUS_ENUM.PASSED,
                                                    PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                                                ].includes(showStatus)
                                            ) {
                                                return Promise.resolve();
                                            }
                                            for (let index = 0; index < value?.length; index++) {
                                                const element = value[index];
                                                if (!element?.passed) {
                                                    return Promise.reject('请选择处理结果');
                                                }
                                            }
                                            return Promise.resolve();
                                        },
                                    },
                                ]}
                            >
                                {(fields, operation) =>
                                    fields.map((field) => {
                                        const data = form.getFieldValue([
                                            'list',
                                            field.name,
                                            'data',
                                        ]);
                                        return (
                                            <div
                                                key={field.key}
                                                style={{
                                                    width: '432px',
                                                    height: 'auto',
                                                    background: '#fff',
                                                    borderRadius: '10px',
                                                    boxShadow:
                                                        '0px 2px 8px 4px rgba(0, 0, 0, 0.14)',
                                                    overflow: 'hidden',
                                                    marginLeft: '8px',
                                                    marginRight: '12px',
                                                    marginBottom: '12px',
                                                    padding: '8px 14px 14px 14px',
                                                }}
                                            >
                                                <Form.Item>
                                                    <AuditDetailSimple
                                                        dataSource={data}
                                                        expanded={allExpanded}
                                                    />
                                                </Form.Item>
                                                {[
                                                    PICTURE_CHECK_STATUS_ENUM.AT_RISK,
                                                    PICTURE_CHECK_STATUS_ENUM.AUDITING,
                                                    PICTURE_CHECK_STATUS_ENUM.FAILED,
                                                ].includes(showStatus) && (
                                                    <Form.Item
                                                        label={
                                                            <Typography.Text type="danger">
                                                                处理结果
                                                            </Typography.Text>
                                                        }
                                                        {...field}
                                                        required
                                                        requiredMark
                                                        rules={[
                                                            {
                                                                validator() {
                                                                    const passed =
                                                                        form.getFieldValue([
                                                                            'list',
                                                                            field.name,
                                                                            'passed',
                                                                        ]);
                                                                    const reason =
                                                                        form.getFieldValue([
                                                                            'list',
                                                                            field.name,
                                                                            'reason',
                                                                        ]);
                                                                    if (
                                                                        passed ===
                                                                            PICTURE_CHECK_STATUS_ENUM.NOT_PASSED &&
                                                                        !reason
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '请选择原因',
                                                                        );
                                                                    } else if (!passed) {
                                                                        return Promise.reject(
                                                                            '请选择是否通过',
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                },
                                                            },
                                                        ]}
                                                        shouldUpdate
                                                    >
                                                        <Form.Item
                                                            {...field}
                                                            name={[field.name, 'passed']}
                                                            noStyle
                                                            required
                                                        >
                                                            <Radio.Group
                                                                options={[
                                                                    {
                                                                        label: '通过',
                                                                        value: PICTURE_CHECK_STATUS_ENUM.PASSED,
                                                                    },
                                                                    {
                                                                        label: '不通过',
                                                                        value: PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                                                                    },
                                                                ]}
                                                            ></Radio.Group>
                                                        </Form.Item>
                                                        <Form.Item
                                                            noStyle
                                                            shouldUpdate={(cur, prev) =>
                                                                cur.passed !== prev.passed
                                                            }
                                                        >
                                                            {({ getFieldValue }) => {
                                                                const passed = getFieldValue([
                                                                    'list',
                                                                    field.name,
                                                                    'passed',
                                                                ]);
                                                                return (
                                                                    passed ===
                                                                        PICTURE_CHECK_STATUS_ENUM.NOT_PASSED && (
                                                                        <Form.Item
                                                                            {...field}
                                                                            name={[
                                                                                field.name,
                                                                                'reason',
                                                                            ]}
                                                                            noStyle
                                                                        >
                                                                            <ReasonForm
                                                                                style={{
                                                                                    width: '156px',
                                                                                }}
                                                                            />
                                                                        </Form.Item>
                                                                    )
                                                                );
                                                            }}
                                                        </Form.Item>
                                                    </Form.Item>
                                                )}
                                                {[
                                                    PICTURE_CHECK_STATUS_ENUM.PASSED,
                                                    PICTURE_CHECK_STATUS_ENUM.NOT_PASSED,
                                                ].includes(showStatus) && (
                                                    <Row style={{ width: '100%' }} justify="center">
                                                        <Button
                                                            type="default"
                                                            danger
                                                            onClick={() => {
                                                                operation.remove(field.name);
                                                            }}
                                                            size="small"
                                                        >
                                                            不处理
                                                        </Button>
                                                    </Row>
                                                )}
                                            </div>
                                        );
                                    })
                                }
                            </Form.List>
                        </Form>
                    </div>
                </Space>
            </div>
            <MultiReasonModal ref={reasonRef} />
        </Modal>
    );
};

export default React.memo(forwardRef(MultiAuditModal));
