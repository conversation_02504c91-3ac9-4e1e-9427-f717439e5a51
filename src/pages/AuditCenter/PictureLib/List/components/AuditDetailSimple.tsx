import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import { Image, Space, Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { Link } from 'umi';

const AuditDetailSimple: React.FC<{
    dataSource?: MNG_ALY.ImageSafeQueryVo;
    expanded?: {
        expanded: boolean;
        date: Date;
    };
}> = ({ dataSource, expanded }) => {
    const [tableExpanded, setTableExpaned] = useState<boolean>(false);

    useEffect(() => {
        if (expanded && expanded?.expanded !== tableExpanded) {
            setTableExpaned(expanded?.expanded);
        }
    }, [expanded]);

    const changeExpanded = () => {
        setTableExpaned(!tableExpanded);
    };

    const basicColumns: ProDescriptionsItemProps<MNG_ALY.ImageSafeQueryVo, any>[] = [
        {
            title: '场站',
            dataIndex: 'stationName',
            renderText(value: string, record) {
                return (
                    <Link
                        to={`/assetCenter/stationManage/list/detail/${record.bizId}`}
                        target="_blank"
                    >
                        {value || '-'}
                    </Link>
                );
            },
        },
        {
            title: '图片来源',
            dataIndex: 'bizTypeName',
        },
        {
            title: '最高风险置信分',
            dataIndex: 'maxConfidence',
            renderText(text, record) {
                return (
                    <Space>
                        <Typography.Text>{text}</Typography.Text>
                        {tableExpanded && (
                            <Typography.Link onClick={changeExpanded}>收起</Typography.Link>
                        )}
                        {!tableExpanded && (
                            <Typography.Link onClick={changeExpanded}>展开</Typography.Link>
                        )}
                    </Space>
                );
            },
        },
    ];
    const riskColumns: ColumnsType<MNG_ALY.ImageSafeLabelQueryVo> = [
        {
            title: '标签值',
            dataIndex: 'label',
            width: 120,
            render(value) {
                return <span style={{ wordBreak: 'break-all' }}>{value}</span>;
            },
        },
        {
            title: '中文含义',
            dataIndex: 'labelName',
            width: 180,
            render(value) {
                return <span style={{ wordBreak: 'break-all' }}>{value}</span>;
            },
        },
        {
            title: '置信分',
            dataIndex: 'confidence',
            width: 80,
        },
    ];
    return (
        <div>
            <div
                style={{
                    display: 'flex',
                    width: '100%',
                    height: '240px',
                    justifyContent: 'center',
                    background: '#eee',
                    overflow: 'hidden',
                    borderTopRightRadius: '10px',
                    borderTopLeftRadius: '10px',
                    marginBottom: '16px',
                }}
            >
                <Image
                    src={dataSource?.imageUrl}
                    style={{
                        maxHeight: '100%',
                        maxWidth: '100%',
                        width: 'inherit',
                        height: '240px',
                    }}
                />
            </div>
            <ProDescriptions columns={basicColumns} column={1} dataSource={dataSource} />
            {tableExpanded && (
                <Table
                    pagination={false}
                    columns={riskColumns}
                    dataSource={dataSource?.labelList}
                    scroll={{ x: 'max-content', y: 100 }}
                    rowKey="id"
                    locale={{
                        emptyText: '暂无数据',
                    }}
                />
            )}
        </div>
    );
};
export default React.memo(AuditDetailSimple);
