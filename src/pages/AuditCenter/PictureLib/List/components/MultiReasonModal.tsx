import { Form, Modal } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';

import ReasonForm from './ReasonForm';

const MultiReasonModal = (props: any, ref: any) => {
    const [visible, updateVisible] = useState<boolean>(false);
    const [params, updateParams] = useState<any>(null);
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
        open(params: any) {
            updateVisible(true);
            updateParams(params);
        },
    }));

    const onClose = () => {
        form.resetFields();
        updateVisible(false);
    };

    const submit = async () => {
        try {
            await form.validateFields();
            const reasons = form.getFieldValue('reason');
            const finalReason = [...reasons].pop();
            console.debug('reasons', reasons);
            console.debug('params', params);
            if (reasons) {
                params?.callback?.({ reasons, finalReason });
                onClose();
            }
        } catch (error) {}
    };

    return (
        <Modal
            visible={visible}
            title="批量不通过"
            onCancel={onClose}
            width={420}
            okText="确定"
            onOk={submit}
            destroyOnClose
        >
            <Form form={form} scrollToFirstError>
                <Form.Item
                    name="reason"
                    label="原因"
                    required
                    rules={[
                        {
                            required: true,
                            message: '请选择原因',
                        },
                    ]}
                >
                    <ReasonForm />
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(MultiReasonModal);
