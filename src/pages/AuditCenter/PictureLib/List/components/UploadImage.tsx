import { Button, Form, Modal, Row, Space } from 'antd';
import React, { useImperativeHandle, useState, forwardRef } from 'react';

import UpLoadImgCustom from '@/components/UpLoadImg/UpLoadImgCustom';

const UploadImage = (props: any, ref: any) => {
    const { callback } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        open() {
            setVisible(true);
        },
    }));
    const onCancel = () => {
        form.resetFields();
        setVisible(false);
    };

    const submit = async () => {
        try {
            await form.validateFields();
            const files = form.getFieldValue('file');
            callback?.(files);
            onCancel();
        } catch (error) {}
    };

    return (
        <Modal
            title="图片上传"
            width={480}
            footer={false}
            visible={visible}
            onCancel={onCancel}
            destroyOnClose
        >
            <Form form={form} scrollToFirstError>
                <Form.Item
                    label="图片上传"
                    name="file"
                    required
                    rules={[{ required: true, message: '请上传图片' }]}
                >
                    <UpLoadImgCustom
                        maxCount={10}
                        multiple
                        placeholder="格式支持png、jpg、jpeg"
                        returnOriginData
                        sizeInfo={{
                            size: 800,
                        }}
                        showPlaceholder
                        uploadData={{
                            contentType: 'imageSafe',
                            contRemrk: 'popIcon',
                            relaTable: 'imageSafe',
                            addWatermarkFlag: '0',
                            businessType: 'imageSafe',
                        }}
                    />
                </Form.Item>
                <Form.Item>
                    <Row justify="center" style={{ marginTop: '8px' }}>
                        <Space>
                            <Button type="primary" onClick={submit}>
                                提交
                            </Button>
                            <Button type="default" onClick={onCancel}>
                                取消
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default React.memo(forwardRef(UploadImage));
