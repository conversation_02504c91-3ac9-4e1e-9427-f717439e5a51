import { useRequest } from 'ahooks';
import { Cascader } from 'antd';
import React, { useEffect } from 'react';

import { getImageFailReasonPageListApi } from '@/services/AuditCenter/PictureApi';

const ReasonForm: React.FC<{ value?: any; onChange?: any; style?: any }> = ({
    value,
    onChange,
    style,
}) => {
    const { run, data, loading } = useRequest(
        () => {
            return getImageFailReasonPageListApi();
        },
        {
            manual: true,
            cacheKey: 'getImageFailReasonPageListApi',
            cacheTime: 60000,
        },
    );
    useEffect(() => {
        run();
    }, []);

    return (
        <Cascader
            loading={loading}
            fieldNames={{
                label: 'name',
                value: 'code',
            }}
            onChange={(v) => {
                onChange?.(v);
            }}
            options={data?.data}
            placeholder="请选择原因"
            showSearch
            value={value}
            style={style}
        />
    );
};

export default ReasonForm;
