.info {
    margin-bottom: 12px;
    .red {
        margin-left: 2px;
        font-weight: 500;
        cursor: pointer;
        color: #f50,
    }
    .blue {
        margin-left: 2px;
        font-weight: 500;
        cursor: pointer;
        color: #1890ff
    }
    :global {
        .ant-col {
            gap: 16px;
            display: flex;
        }
        .ant-descriptions-row > th, .ant-descriptions-row > td {
            padding-bottom: 0;
        }
    }
}

.row-dragging {
    background: #fafafa;
    border: 1px solid #ccc;
}

.row-dragging td {
    padding: 16px;
}

.row-dragging .drag-visible {
    visibility: visible;
}
