import ProDescriptions from '@ant-design/pro-descriptions';
import type { ProDescriptionsItemProps } from '@ant-design/pro-descriptions';
import { Image, Space, Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React from 'react';
import { Link } from 'umi';

import { PICTURE_CHECK_STATUS_ENUM } from '@/constants/auditPicture';

const AuditDetail: React.FC<{ dataSource?: MNG_ALY.ImageSafeQueryVo }> = ({ dataSource }) => {
    const basicColumns: ProDescriptionsItemProps<MNG_ALY.ImageSafeQueryVo, any>[] = [
        {
            title: '场站',
            dataIndex: 'stationName',
            renderText(value: string, record) {
                return (
                    <Link
                        to={`/assetCenter/stationManage/list/detail/${record.bizId}`}
                        target="_blank"
                    >
                        {value || '-'}
                    </Link>
                );
            },
        },
        {
            title: '运营商',
            dataIndex: 'operName',
        },
        {
            title: '城市',
            dataIndex: 'cityName',
        },
        {
            title: '图片来源',
            dataIndex: 'bizTypeName',
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
        },
    ];
    const auditRecordColumns: ColumnsType<MNG_ALY.ImageExamineRecordQueryVo> = [
        {
            title: '审核方',
            dataIndex: 'examineTypeName',
            width: 110,
        },
        {
            title: '审核结果',
            dataIndex: 'checkResultName',
            render(value, record) {
                let type: 'secondary' | 'success' | 'warning' | 'danger' | undefined = undefined;
                if (record.checkResult === PICTURE_CHECK_STATUS_ENUM.NOT_PASSED) {
                    type = 'danger';
                } else if (record.checkResult === PICTURE_CHECK_STATUS_ENUM.AT_RISK) {
                    type = 'warning';
                }
                return <Typography.Text type={type}>{value}</Typography.Text>;
            },
            width: 138,
        },
        {
            title: '原因',
            dataIndex: 'failReasonName',
            width: 152,
        },
        {
            title: '操作人',
            dataIndex: 'operatorName',
            width: 110,
        },
        {
            title: '发起时间',
            dataIndex: 'startExamineTime',
            width: 130,
        },
        {
            title: '处理时间',
            dataIndex: 'createdTime',
            width: 130,
        },
    ];
    const riskColumns: ColumnsType<MNG_ALY.ImageSafeLabelQueryVo> = [
        {
            title: '标签值',
            dataIndex: 'label',
            width: 210,
        },
        {
            title: '中文含义',
            dataIndex: 'labelName',
            width: 260,
        },
        {
            title: '置信分',
            dataIndex: 'confidence',
            width: 120,
        },
    ];
    return (
        <div style={{ display: 'flex', flex: '1' }}>
            <Space style={{ width: '100%' }} direction="vertical">
                <div
                    style={{
                        display: 'flex',
                        width: '100%',
                        height: '240px',
                        justifyContent: 'center',
                        background: '#eee',
                        overflow: 'hidden',
                    }}
                >
                    <Image
                        src={dataSource?.imageUrl}
                        style={{
                            maxHeight: '100%',
                            maxWidth: '100%',
                            width: 'inherit',
                            height: '240px',
                        }}
                    />
                </div>
                <ProDescriptions columns={basicColumns} column={2} dataSource={dataSource} />
                <Typography.Text>审核记录：</Typography.Text>
                <Table
                    pagination={false}
                    columns={auditRecordColumns}
                    dataSource={dataSource?.examineRecordList}
                    rowKey="id"
                    locale={{
                        emptyText: '暂无数据',
                    }}
                />
                <Typography.Text>风险内容：</Typography.Text>
                <Table
                    pagination={false}
                    columns={riskColumns}
                    dataSource={dataSource?.labelList}
                    rowKey="id"
                    locale={{
                        emptyText: '暂无数据',
                    }}
                />
            </Space>
        </div>
    );
};
export default React.memo(AuditDetail);
