import { usePagination } from 'ahooks';
import { Button, Form, Input, Modal, Popconfirm, Space, message } from 'antd';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import styles from './style.less';
import TablePro from '@/components/TablePro';
import {
    addImageFailReasonApi,
    deleteImageFailReasonApi,
    updateImageFailReasonApi,
    getImageFailReasonPageListApi,
} from '@/services/AuditCenter/PictureApi';

type EditType = 'add' | 'edit' | 'addNext';
const EDIT_TYPE = {
    ADD: 'add',
    EDIT: 'edit',
    ADD_NEXT: 'addNext',
};

type EditItemType = {
    id: string;
    name: string;
    parentId: string;
};
const EditModal: React.FC<{
    initRef: any;
    onFinish: () => void;
}> = ({ initRef, onFinish }) => {
    const [visible, updateVisible] = useState(false);
    const [isWaiting, updateWaiting] = useState(false);
    const [type, updateType] = useState<EditType>();
    const [editItem, updateItem] = useState<EditItemType | undefined>();
    const [form] = Form.useForm();
    useImperativeHandle(initRef, () => ({
        show: ({ type: _type = 'add', item }: { type: EditType; item?: EditItemType }) => {
            updateType(_type);
            updateVisible(true);
            updateItem(item);
            form.resetFields();
            if (item && _type == EDIT_TYPE.EDIT) {
                form.setFieldsValue(item);
            }
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const title = useMemo(() => {
        switch (type) {
            case EDIT_TYPE.ADD:
                return '新增';
            case EDIT_TYPE.ADD_NEXT:
                return '新增下级';

            default:
                return '编辑';
        }
    }, [type]);

    const name = Form.useWatch('name', form);
    return (
        <Modal
            visible={visible}
            title={title}
            onCancel={onClose}
            width={420}
            zIndex={91}
            maskClosable={false}
            okButtonProps={{ disabled: !name?.length, loading: isWaiting }}
            okText={'确定'}
            onOk={async () => {
                const params: {
                    name: string;
                    id?: string;
                    parentId?: string;
                } = {
                    name: name?.trim(),
                };
                if (type == EDIT_TYPE.ADD_NEXT) {
                    // 新增下级
                    params.parentId = editItem?.id;
                } else if (type == EDIT_TYPE.ADD) {
                    // 新增
                } else if (type == EDIT_TYPE.EDIT) {
                    // 编辑
                    params.id = editItem?.id;
                }
                try {
                    updateWaiting(true);
                    if (type === EDIT_TYPE.EDIT) {
                        await updateImageFailReasonApi(params);
                    } else {
                        await addImageFailReasonApi(params);
                    }
                    message.success('操作成功');
                    onFinish();
                    onClose();
                } catch (error) {
                } finally {
                    updateWaiting(false);
                }
            }}
            destroyOnClose
        >
            <Form form={form} scrollToFirstError>
                <Form.Item
                    name="name"
                    label="原因"
                    required
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请填写原因',
                        },
                    ]}
                >
                    <Input.TextArea
                        placeholder="请填写"
                        autoComplete="off"
                        allowClear
                        maxLength={30}
                        showCount
                        rows={2}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

const ReasonMgrModal: React.FC<{ initRef: any }> = ({ initRef }) => {
    const [visible, updateVisible] = useState(false);
    const [list, updateList] = useState<
        {
            id: number;
            name: string;
            level: number;
        }[]
    >([]);
    const [expandedRowKeys, updateExpandedRowKeys] = useState<string[]>([]);
    const editRef = useRef<any>();
    useImperativeHandle(initRef, () => ({
        show: () => {
            updateVisible(true);
            pagination.onChange(1, 999);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };
    const { run, refresh, loading, data, pagination } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getImageFailReasonPageListApi({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data,
                    current: 1,
                    pageSize: 999,
                    total: response?.data?.length,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 999,
        },
    );

    useEffect(() => {
        updateList([...(data?.list || [])]);
    }, [data]);

    return (
        <Modal
            visible={visible}
            title="原因维护"
            onCancel={onClose}
            width={660}
            zIndex={90}
            footer={null}
            maskClosable={false}
        >
            <Space style={{ marginBottom: '12px' }}>
                <Button
                    type="primary"
                    onClick={() => {
                        editRef.current?.show({ type: EDIT_TYPE.ADD });
                    }}
                >
                    新增
                </Button>
            </Space>
            <TablePro
                style={{ zIndex: 100 }}
                scroll={{ y: document.body.clientHeight - 400, x: 'max-content' }}
                loading={loading}
                rowKey="id"
                dataSource={list}
                columns={[
                    {
                        title: '原因',
                        dataIndex: 'name',
                        className: styles['drag-visible'],
                        width: 260,
                    },
                    {
                        title: '操作',
                        // width: 120,
                        className: styles['drag-visible'],
                        render: (id: string, record: any) => {
                            return (
                                <Space style={{ whiteSpace: 'nowrap' }}>
                                    <Button
                                        type="link"
                                        size="small"
                                        onClick={() => {
                                            editRef.current?.show({
                                                type: EDIT_TYPE.EDIT,
                                                item: record,
                                            });
                                        }}
                                    >
                                        编辑
                                    </Button>
                                    {(record.level < 5 && (
                                        <Button
                                            type="link"
                                            size="small"
                                            onClick={() => {
                                                editRef.current?.show({
                                                    type: EDIT_TYPE.ADD_NEXT,
                                                    item: record,
                                                });
                                            }}
                                        >
                                            新增下级
                                        </Button>
                                    )) ||
                                        null}
                                    <Popconfirm
                                        title="确认删除？"
                                        onConfirm={async () => {
                                            try {
                                                await deleteImageFailReasonApi(record?.id);
                                                message.success('操作成功');
                                                refresh();
                                            } catch (error) {}
                                        }}
                                    >
                                        <Button type="link" danger size="small">
                                            删除
                                        </Button>
                                    </Popconfirm>
                                </Space>
                            );
                        },
                    },
                ]}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={
                    false && {
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        pageSizeOptions: [5, 10, 20, 50],
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }
                }
                expandable={{
                    childrenColumnName: 'children',
                    expandedRowKeys,
                    onExpand: (expanded: boolean, record: any) => {
                        if (expanded) {
                            expandedRowKeys.push(record.id);
                        } else {
                            expandedRowKeys.splice(expandedRowKeys.indexOf(record.id), 1);
                        }
                        updateExpandedRowKeys([...expandedRowKeys]);
                    },
                }}
            />

            <EditModal
                initRef={editRef}
                onFinish={() => {
                    refresh();
                }}
            />
        </Modal>
    );
};

export default ReasonMgrModal;
