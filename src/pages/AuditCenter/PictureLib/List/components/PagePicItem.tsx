import { Checkbox, Space, Tag, Typography } from 'antd';
import React, { useRef } from 'react';

import { PICTURE_CHECK_STATUS_ENUM } from '@/constants/auditPicture';
import RiskTable from './RiskTable';

const shadowColor = {
    [PICTURE_CHECK_STATUS_ENUM.AUDITING]: '#3A92FE',
    [PICTURE_CHECK_STATUS_ENUM.PASSED]: '#37B200',
    [PICTURE_CHECK_STATUS_ENUM.NOT_PASSED]: '#FF0000',
    [PICTURE_CHECK_STATUS_ENUM.AT_RISK]: '#EE850B',
    [PICTURE_CHECK_STATUS_ENUM.FAILED]: '#FF0000',
};

const PagePicItem: React.FC<{
    dataSource: MNG_ALY.ImageSafeQueryVo;
    checked: boolean;
    onChange: (checked: boolean) => void;
    onClick: () => void;
    checkable?: boolean;
}> = (props) => {
    const { dataSource, checked, onChange, onClick, checkable = false } = props;
    const riskRef = useRef<ActionRef.OpenModal>();
    return (
        <div
            style={{
                width: '330px',
                height: '120px',
                position: 'relative',
                display: 'flex',
                flexDirection: 'row',
            }}
        >
            {checkable && (
                <Checkbox
                    checked={checked}
                    onChange={() => {
                        onChange && onChange(!checked);
                    }}
                    style={{ position: 'absolute', top: '4px', left: '4px' }}
                />
            )}
            <div
                style={{
                    width: '160px',
                    height: '120px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    display: 'flex',
                    background: '#efefef',
                    borderRadius: '2px',
                    overflow: 'hidden',
                    cursor: 'pointer',
                }}
                onClick={onClick}
            >
                <img src={dataSource.imageUrl} style={{ maxWidth: '186px', maxHeight: '123px' }} />
                <span
                    style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        fontSize: '12px',
                        display: 'inline-block',
                    }}
                >
                    <Tag color={shadowColor[dataSource.checkStatus as string] || 'blue'}>
                        {dataSource.checkStatusName}
                    </Tag>
                </span>
            </div>
            <div
                style={{ width: '160px', height: '120px', marginLeft: '10px', cursor: 'pointer' }}
                onClick={onClick}
            >
                <Space direction="vertical" size={8}>
                    <Typography.Paragraph
                        strong
                        style={{
                            fontSize: '14px',
                            marginBottom: '0',
                            height: '42px',
                            wordBreak: 'break-all',
                        }}
                        title={dataSource.stationName}
                        ellipsis={{ rows: 2 }}
                    >
                        {dataSource.stationName}
                    </Typography.Paragraph>
                    <Typography.Text style={{ fontSize: '12px' }}>
                        {dataSource.bizTypeName}
                    </Typography.Text>
                    {dataSource.examineType === '02' && (
                        <Typography.Paragraph
                            style={{ fontSize: '12px', marginBottom: '0', wordBreak: 'break-all' }}
                            title={`${dataSource.operatorName || '-'}`}
                            ellipsis={{ rows: 2 }}
                        >
                            人工介入：
                            {`${dataSource.operatorName || '-'}`}
                        </Typography.Paragraph>
                    )}
                    {dataSource.examineType === '01' && (
                        <Typography.Paragraph
                            style={{ fontSize: '12px', marginBottom: '0', wordBreak: 'break-all' }}
                            onClick={(e) => {
                                e?.stopPropagation();
                                e?.preventDefault();
                                if (
                                    dataSource.maxConfidence !== undefined &&
                                    dataSource.maxConfidence !== null
                                ) {
                                    riskRef.current?.open(dataSource?.id);
                                }
                            }}
                        >
                            机器审核
                            {dataSource.maxConfidence !== undefined && (
                                <>
                                    ：
                                    <Typography.Link>{`${
                                        dataSource.maxConfidence || '-'
                                    }分`}</Typography.Link>
                                </>
                            )}
                        </Typography.Paragraph>
                    )}
                </Space>
            </div>
            <RiskTable ref={riskRef} />
        </div>
    );
};

export default React.memo(PagePicItem);
