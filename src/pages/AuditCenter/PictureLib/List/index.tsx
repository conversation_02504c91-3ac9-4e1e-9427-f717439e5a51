import { ProList } from '@ant-design/pro-components';
import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Button,
    Card,
    Checkbox,
    Col,
    DatePicker,
    Form,
    List,
    message,
    Select,
    Space,
    Tabs,
    Typography,
} from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';
import CitysSelect from '@/components/CitysSelect';
import SystemAccountSelect from '@/components/SystemAccountSelect';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import SortColumns from '@/components/SortColumns';
import PagePicItem from './components/PagePicItem';
import ReasonMgrModal from './components/ReasonModal';
import type { PageInfo as PAGE_INFO } from './components/AuditModal';
import AuditModal from './components/AuditModal';
import MultiAuditModal from './components/MultiAuditModal';
import { getImageSafeConfigApi, getImageSafePageListApi } from '@/services/AuditCenter/PictureApi';
import { PICTURE_CHECK_STATUS_ENUM } from '@/constants/auditPicture';
import usePageState from '@/hooks/usePageState';
import { useRequest } from 'ahooks';

const formItemLayout: any = {
    labelCol: { flex: '0 0 100px' },
    labelAlign: 'right',
};

const sorItems = [
    {
        label: '创建时间',
        name: 'orderBy',
        ascValue: '1',
        descValue: '0',
    },
    {
        label: '置信分',
        name: 'orderBy',
        ascValue: '3',
        descValue: '2',
    },
];

const ListPage = () => {
    const [form] = Form.useForm();
    const [currentTab, setCurrentTab] = useState<string>('ALL');
    const [sortParams, setSortParams] = useState<any>();
    const [pageInfo, changePageInfo] = usePageState({ pageSize: 20 });
    const [selectKeys, setSelectKeys] = useState<string[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [allSelected, setAllSelected] = useState<boolean>(false);
    const [data, setData] = useState<any>();
    const auditRef = useRef<ActionRef.OpenModal>();
    const popRef = useRef<any>();
    const multiRef = useRef<ActionRef.OpenModal>();

    const searchData: (p?: any) => any = async ({ pageIndex, pageSize }) => {
        const formData = form.getFieldsValue(true);
        const params = {
            ...formData,
            examineStartTime:
                formData?.examineTime &&
                formData.examineTime[0] &&
                moment(formData.examineTime[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss'),
            examineEndTime:
                formData?.examineTime &&
                formData.examineTime[1] &&
                moment(formData.examineTime[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss'),
            createStartTime:
                formData?.createTime &&
                formData.createTime[0] &&
                moment(formData.createTime[0]).startOf('days').format('YYYY-MM-DD HH:mm:ss'),
            createEndTime:
                formData?.createTime &&
                formData.createTime[1] &&
                moment(formData.createTime[1]).endOf('days').format('YYYY-MM-DD HH:mm:ss'),
            examineTime: undefined,
            createTime: undefined,
            citys: formData?.citys?.join(','),
            ...sortParams,
            pageIndex: pageIndex,
            pageSize: pageSize,
        };
        if (currentTab !== 'ALL') {
            params.checkStatus = currentTab;
        }
        setAllSelected(false);
        setSelectKeys([]);
        setLoading(true);
        try {
            const result = await getImageSafePageListApi(params);
            setData(result);
            setLoading(false);
            return result;
        } catch (error) {
            setData(undefined);
            setLoading(false);
            return null;
        }
    };

    const { data: configData } = useRequest(
        () => {
            return getImageSafeConfigApi();
        },
        { manual: false },
    );

    const onSubmit = () => {
        changePageInfo((state: any) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData({ pageIndex: 1, pageSize: pageInfo?.pageSize });
    };

    const resetForm = () => {
        form.resetFields();
        changePageInfo((state: any) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData({ pageIndex: 1, pageSize: pageInfo?.pageSize });
    };

    useEffect(() => {
        changePageInfo((state: any) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData({ pageIndex: 1, pageSize: pageInfo?.pageSize });
    }, [sortParams, currentTab]);

    const onSortChange = useCallback((res: any) => {
        setSortParams(res);
    }, []);

    const dataSources = useMemo(() => {
        return data?.data?.records?.map((v: MNG_ALY.ImageSafeQueryVo) => {
            return {
                ...v,
                checked: selectKeys?.includes(`${v?.id}`),
            };
        });
    }, [data?.data, selectKeys]);

    const showMultiModal = () => {
        if (selectKeys?.length > 0) {
            multiRef.current?.open(
                data?.data?.records?.filter((v: MNG_ALY.ImageSafeQueryVo) =>
                    selectKeys.includes(`${v.id}`),
                ),
            );
        } else {
            message.error('请选择要批量处理的图片');
        }
    };

    const goNext = async (page: PAGE_INFO) => {
        if (data?.data?.records && page.index < data?.data?.records.length - 1) {
            const item = data?.data?.records?.[page.index + 1];
            return {
                ...page,
                index: page.index + 1,
                id: item.id,
            };
        } else if (page.pageIndex * page.pageSize < data?.data?.total) {
            changePageInfo((state: any) => ({
                ...state,
                pageIndex: page.pageIndex + 1,
            }));
            const result = await searchData({
                pageIndex: page.pageIndex + 1,
                pageSize: pageInfo.pageSize,
            });
            const item = result?.data?.records?.[0];
            return item
                ? {
                      ...page,
                      pageIndex: page.pageIndex + 1,
                      index: 0,
                      id: item.id,
                  }
                : null;
        }
        return null;
    };

    const goPre = async (page: PAGE_INFO) => {
        if (data?.data?.records && page.index > 0) {
            const item = data?.data?.records?.[page.index - 1];
            return {
                ...page,
                index: page.index - 1,
                id: item.id,
            };
        } else if (page.pageIndex > 1) {
            changePageInfo((state: any) => ({
                ...state,
                pageIndex: page.pageIndex - 1,
            }));
            const result = await searchData({
                pageIndex: page.pageIndex - 1,
                pageSize: pageInfo.pageSize,
            });
            const item = result?.data?.records?.[pageInfo.pageSize - 1];
            return item
                ? {
                      ...page,
                      pageIndex: page.pageIndex - 1,
                      index: pageInfo.pageSize - 1,
                      id: item.id,
                  }
                : null;
        }
        return null;
    };

    const openAudit = (index: number, id: number | string) => {
        auditRef.current?.open({
            id: id,
            index,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            total: data?.data?.total,
        });
    };

    const checkAllChange = (e: CheckboxChangeEvent) => {
        const checked = e.target.checked;
        setAllSelected(checked);
        if (checked) {
            setSelectKeys(dataSources?.map((v: MNG_ALY.ImageSafeQueryVo) => `${v?.id}`));
        } else {
            setSelectKeys([]);
        }
    };

    const operId = Form.useWatch('operId', form);

    return (
        <PageHeaderWrapper>
            <Card>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <Alert
                        type="info"
                        showIcon
                        message={
                            <Space direction="vertical" size={4}>
                                <Typography.Text>
                                    1.所有图片进入系统，都会由机器审核并给出风险项及置信分（0~100），置信分越高表示风险项的可信度越高，需要重点关注。
                                </Typography.Text>
                                {configData?.data && (
                                    <Typography.Text>
                                        {`2.置信分<${configData?.data?.minConfidence}审核通过，${configData?.data?.minConfidence}-${configData?.data?.maxConfidence}为疑似风险，>${configData?.data?.maxConfidence}审核不通过。疑似风险的图片需要人工介入审核。`}
                                    </Typography.Text>
                                )}
                            </Space>
                        }
                    />
                    <Form
                        form={form}
                        onFinish={() => {
                            onSubmit?.();
                        }}
                        {...formItemLayout}
                        scrollToFirstError
                    >
                        <SearchOptionsBar loading={loading} onReset={resetForm}>
                            <Col span={8}>
                                <OperSelectItem
                                    form={form}
                                    name="operId"
                                    label="运营商"
                                    formItemLayout={formItemLayout}
                                />
                            </Col>
                            <Col span={8}>
                                <AllStationSelect
                                    form={form}
                                    label="场站名称"
                                    name="stationId"
                                    operId={operId}
                                />
                            </Col>
                            <Col span={8}>
                                <CitysSelect
                                    label="城市"
                                    name="citys"
                                    placeholder="请选择"
                                    formItemLayout={formItemLayout}
                                    showArrow
                                    allowClear
                                    rules={null}
                                    provinceSelectable
                                    multiple
                                />
                            </Col>
                            <Col span={8}>
                                <Form.Item label="图片来源" name="bizType">
                                    <Select
                                        options={[
                                            {
                                                label: '场站图片（互联互通）',
                                                value: '01',
                                            },
                                            {
                                                label: '场站图片（后台上传）',
                                                value: '02',
                                            },
                                            {
                                                label: '路书（互联互通）',
                                                value: '03',
                                            },
                                            {
                                                label: '路书（后台上传）',
                                                value: '04',
                                            },
                                        ]}
                                        showArrow
                                        placeholder="请选择"
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="审核方" name="examineType">
                                    <Select
                                        options={[
                                            {
                                                label: '机器审核',
                                                value: '01',
                                            },
                                            {
                                                label: '人工介入',
                                                value: '02',
                                            },
                                        ]}
                                        showArrow
                                        placeholder="请选择"
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <SystemAccountSelect fieldName="operator" label="操作人" />
                            </Col>
                            <Col span={8}>
                                <Form.Item label="审核时间" name="examineTime">
                                    <DatePicker.RangePicker
                                        placeholder={['开始时间', '结束时间']}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="创建时间" name="createTime">
                                    <DatePicker.RangePicker
                                        placeholder={['开始时间', '结束时间']}
                                    />
                                </Form.Item>
                            </Col>
                        </SearchOptionsBar>
                    </Form>
                    <Space>
                        {currentTab !== 'ALL' && (
                            <Button type="primary" onClick={showMultiModal}>
                                批量处理
                            </Button>
                        )}
                        <Button
                            type="default"
                            onClick={() => {
                                popRef.current?.show();
                            }}
                        >
                            原因维护
                        </Button>
                    </Space>
                    <Tabs
                        activeKey={currentTab}
                        onChange={(e) => {
                            setCurrentTab(e);
                        }}
                    >
                        <Tabs.TabPane tab="全部" key="ALL" tabKey="ALL"></Tabs.TabPane>
                        <Tabs.TabPane
                            tab="审核失败"
                            key={PICTURE_CHECK_STATUS_ENUM.FAILED}
                            tabKey={PICTURE_CHECK_STATUS_ENUM.FAILED}
                        ></Tabs.TabPane>
                        <Tabs.TabPane
                            tab="疑似风险"
                            key={PICTURE_CHECK_STATUS_ENUM.AT_RISK}
                            tabKey={PICTURE_CHECK_STATUS_ENUM.AT_RISK}
                        ></Tabs.TabPane>
                        <Tabs.TabPane
                            tab="未通过"
                            key={PICTURE_CHECK_STATUS_ENUM.NOT_PASSED}
                            tabKey={PICTURE_CHECK_STATUS_ENUM.NOT_PASSED}
                        ></Tabs.TabPane>
                        <Tabs.TabPane
                            tab="已通过"
                            key={PICTURE_CHECK_STATUS_ENUM.PASSED}
                            tabKey={PICTURE_CHECK_STATUS_ENUM.PASSED}
                        ></Tabs.TabPane>
                    </Tabs>
                    <SortColumns items={sorItems} mutualExclusion onChange={onSortChange} />
                    <ProList
                        dataSource={dataSources}
                        loading={loading}
                        grid={{ gutter: 16 }}
                        tableLayout="fixed"
                        itemLayout="horizontal"
                        footer={
                            currentTab !== 'ALL' && (
                                <Checkbox checked={allSelected} onChange={checkAllChange}>
                                    本页全选
                                </Checkbox>
                            )
                        }
                        pagination={{
                            current: pageInfo.pageIndex,
                            total: data?.data?.total,
                            pageSize: pageInfo.pageSize,
                            showTotal: (total) => `共 ${total} 条`,
                            showSizeChanger: true,
                            defaultPageSize: 20,
                            pageSizeOptions: [20, 50, 100],
                            onChange(page, pageSize) {
                                changePageInfo((state: any) => ({
                                    ...state,
                                    pageIndex: page,
                                    pageSize: pageSize,
                                }));
                                searchData({ pageIndex: page, pageSize: pageSize });
                            },
                        }}
                        style={{
                            justifyItems: 'center',
                        }}
                        renderItem={(item, index) => {
                            return (
                                <List.Item key={item.id}>
                                    <PagePicItem
                                        dataSource={item}
                                        checked={item.checked}
                                        checkable={currentTab !== 'ALL'}
                                        onChange={(checked) => {
                                            if (checked && !selectKeys.includes(`${item.id}`)) {
                                                selectKeys.push(`${item.id}`);
                                            } else if (!checked) {
                                                const index = selectKeys.indexOf(`${item.id}`);
                                                selectKeys.splice(index, 1);
                                            }
                                            setSelectKeys([...selectKeys]);
                                        }}
                                        onClick={() => {
                                            openAudit(index, item.id);
                                        }}
                                    />
                                </List.Item>
                            );
                        }}
                        rowKey="id"
                    />
                </Space>
                <ReasonMgrModal initRef={popRef} />
                <AuditModal
                    ref={auditRef}
                    goNext={goNext}
                    goPre={goPre}
                    callback={() => {
                        searchData({
                            pageIndex: pageInfo.pageIndex,
                            pageSize: pageInfo?.pageSize,
                        });
                    }}
                />
                <MultiAuditModal
                    ref={multiRef}
                    callback={() => {
                        changePageInfo((state: any) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                        searchData({
                            pageIndex: 1,
                            pageSize: pageInfo?.pageSize,
                        });
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default ListPage;
