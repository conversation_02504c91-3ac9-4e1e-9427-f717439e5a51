import { useEffect, useState } from 'react';
import { useParams, useLocation } from 'umi';
import styles from './index.less';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { getBiPageUrlByCodeApi } from '@/services/CommonApi';
import { useRequest } from 'ahooks';

const IframePage = () => {
    const params = useParams();
    const { query } = useLocation();
    const { pageCode } = params;
    const [isFullScreen, setIsFullScreen] = useState(false);

    const {
        run,
        data: url,
        loading,
    } = useRequest(
        async (code) => {
            try {
                const { data } = await getBiPageUrlByCodeApi(code, { paramMap: query });

                return data?.url;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );
    useEffect(() => {
        if (pageCode) {
            run(pageCode);
        }
    }, [pageCode]);
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            // 或者使用 event.keyCode === 27
            setIsFullScreen(false);
        }
    });
    return (
        <Spin spinning={loading}>
            <div className={`${styles.biPage} ${isFullScreen && styles.fullbiPage}`}>
                <div className={styles.fullScreen} onClick={() => setIsFullScreen(!isFullScreen)}>
                    {isFullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                </div>
                <iframe className={styles.iframe} src={url} />
            </div>
        </Spin>
    );
};

export default IframePage;
