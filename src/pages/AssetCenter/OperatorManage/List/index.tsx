import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Card, Form } from 'antd';
import { connect, Link } from 'umi';
import React, { useEffect, useState, useRef } from 'react';
import usePageState from '@/hooks/usePageState.js';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';

import { getOperationListApi } from '@/services/AssetCenter/OperationApi';
import { debounce } from 'lodash';
import SearchLayout from './components/SearchLayout';

export const OperatorManageListPage = (props: any) => {
    const { history, global } = props;

    const stationRef = useRef<any>();
    const { pageInit } = global;
    const {
        location: { pathname: _pathname, query },
    } = history;

    const pathname = _pathname;
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});

    useEffect(() => {
        if (pageInit[pathname]) {
            const { stationId, stationName } = pageInit[pathname].form || {};
            if (stationId && stationName) {
                stationRef.current?.init?.([
                    {
                        stationName: stationName,
                        stationId: stationId,
                    },
                ]);
            }
            form.setFieldsValue(pageInit[pathname].form);
        } else if (query) {
            form.setFieldsValue(query);
            setTimeout(() => {
                searchData();
            }, 0);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const [operationDataSource, updateOperationDataSource] = useState<any>();
    const [listLoading, updateListLoading] = useState(false);
    const searchData = debounce((isDownload = false) => {
        form.validateFields().then(async (data) => {
            const params = {
                ...data,
                pageIndex: (isDownload == false && pageInfo.pageIndex) || undefined,
                pageSize: (isDownload == false && pageInfo.pageSize) || undefined,
                createBeginTime: data?.dates?.[0].format('YYYY-MM-DD') || '',
                createEndTime: data?.dates?.[1].format('YYYY-MM-DD') || '',
                dates: undefined,
            };

            if (isDownload) {
                // 下载
            } else {
                try {
                    updateListLoading(true);
                    const { data } = await getOperationListApi(params);
                    updateOperationDataSource(data);
                } catch (error) {
                } finally {
                    updateListLoading(false);
                }
            }
        });
    }, 100);

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '操作模块',
            dataIndex: 'moduleCodeName',
            width: 120,
        },
        {
            title: '操作端',
            dataIndex: 'operatingEndName',
            width: 120,
        },
        {
            title: '操作类型',
            dataIndex: 'actionCodeName',
            width: 120,
        },
        {
            title: '操作数据',
            dataIndex: 'data',
            width: 200,
            render(text: any, record: any) {
                if (record?.stationId) {
                    return (
                        <Link
                            to={`/assetCenter/stationManage/list/detail/${record.stationId}`}
                            key="detail"
                            target={'_blank'}
                        >
                            <a>{text}</a>
                        </Link>
                    );
                }
                if (record?.attrOne) {
                    if (record?.moduleCode == 'oper_manage') {
                        // 跳到运营商管理详情
                        return (
                            <Link
                                to={`/sellerCenter/operatormanage/operator/list/detail/${record.attrOne}`}
                                key="detail"
                                target={'_blank'}
                            >
                                <a>{text}</a>
                            </Link>
                        );
                    }
                    if (record?.moduleCode == 'contract_todo') {
                        // 跳到合同待办详情
                        return (
                            <Link
                                to={`/sellerCenter/contract/todo/list/detail/${record.attrOne}`}
                                key="detail"
                                target={'_blank'}
                            >
                                <a>{text}</a>
                            </Link>
                        );
                    }
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作内容',
            dataIndex: 'content',
            width: 180,
            render(text: any) {
                return (
                    <span
                        title={text}
                        style={{
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '5',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-all',
                            wordWrap: 'break-word',
                        }}
                    >
                        {text}
                    </span>
                );
            },
        },
        {
            title: '操作时间',
            dataIndex: 'createTime',
            width: 200,
        },
        {
            title: '操作人',
            dataIndex: 'operator',
            width: 120,
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state: any) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    stationRef={stationRef}
                />

                <TablePro
                    name="ListPage"
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record: any) => record.id}
                    dataSource={operationDataSource?.records}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: operationDataSource?.total,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
}))(OperatorManageListPage);
