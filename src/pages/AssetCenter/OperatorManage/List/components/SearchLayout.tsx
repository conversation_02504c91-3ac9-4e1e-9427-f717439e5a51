import { Col, Form, Select, Input, DatePicker } from 'antd';
import moment from 'moment';
import { Fragment, ReactNode, useEffect, useMemo, useState } from 'react';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';

import { initOperationApi } from '@/services/AssetCenter/OperationApi';
import { getCodesApi } from '@/services/CommonApi';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout: any = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'right',
};

const SearchLayout = (props: any) => {
    const { listLoading, form, onSubmit, onReset, stationRef } = props;

    const [enums, udateEnums] = useState<any>([]);
    const [contractRetryNodes, udateContractRetryNodes] = useState<any>([]);
    const initCode = async () => {
        try {
            const { data } = await initOperationApi();
            udateEnums(data);

            const {
                data: { list },
            } = await getCodesApi('contractRetryNode');
            udateContractRetryNodes(list);
        } catch (error) {}
    };
    useEffect(() => {
        if (!enums?.length) {
            initCode();
        }
    }, []);

    const onFinish = (values: any) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const moduleCode = Form.useWatch('moduleCode', form);
    const targetModes = enums?.opList?.find?.((ele: any) => ele.codeValue == moduleCode)?.children;
    const formatOptions = (codeList: any) => {
        return codeList?.map((ele: any) => (
            <Option value={ele.codeValue} key={ele.codeValue}>
                {ele.codeName}
            </Option>
        ));
    };
    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                dates: [moment().subtract(7, 'days'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <FormItem
                        label="操作时间"
                        name="dates"
                        {...formItemLayout}
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value?.[0] || !value?.[1]) {
                                        return Promise.reject(`请选择`);
                                    }
                                    if (value?.[0] && value?.[1]) {
                                        const dest = 60;
                                        if (value[1].diff(value[0], 'days') >= dest) {
                                            return Promise.reject(`选取范围最大不超过${dest}天`);
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                    >
                        <RangePicker format="YYYY-MM-DD" allowClear={false} />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem
                        label="操作模块"
                        name="moduleCode"
                        {...formItemLayout}
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Select
                            placeholder="请选择"
                            allowClear
                            onChange={() => {
                                form.setFieldsValue({
                                    actionCode: undefined,
                                    buildNo: undefined,
                                    stationId: undefined,
                                    stationNo: undefined,
                                    buildName: undefined,
                                    cityName: undefined,
                                    stationName: undefined,
                                    attrThree: undefined,
                                });
                            }}
                        >
                            {formatOptions(enums?.opList)}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="操作端" name="operatingEnd" {...formItemLayout}>
                        <Select
                            placeholder="请选择"
                            allowClear
                            options={[
                                {
                                    label: '新电途后台',
                                    value: '0',
                                },
                                { label: '运维管家小程序', value: '1' },
                            ]}
                        />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="操作类型" name="actionCode" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear disabled={!moduleCode}>
                            {formatOptions(targetModes)}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={moduleCode === 'station_manage' || moduleCode === 'oper_manage' ? 8 : 0}>
                    <OperSelectTypeItem
                        name={(moduleCode === 'station_manage' && 'buildNo') || 'attrOne'}
                        form={form}
                        formItemLayout={formItemLayout}
                    />
                </Col>
                <Col span={moduleCode === 'station_manage' ? 8 : 0}>
                    <AllStationSelect
                        form={form}
                        ref={stationRef}
                        label="场站名称"
                        formItemLayout={formItemLayout}
                    />
                </Col>
                <Col span={moduleCode === 'clue_manage' ? 8 : 0}>
                    <Form.Item label="线索ID" name="stationNo">
                        <Input placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={moduleCode === 'clue_manage' ? 8 : 0}>
                    <Form.Item label="运营商" name="buildName">
                        <Input placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={moduleCode === 'clue_manage' ? 8 : 0}>
                    <Form.Item label="城市" name="cityName">
                        <Input placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={moduleCode === 'clue_manage' ? 8 : 0}>
                    <Form.Item label="场站名称" name="stationName">
                        <Input placeholder="请填写" />
                    </Form.Item>
                </Col>
                <Col span={moduleCode === 'contract_todo' ? 8 : 0}>
                    <FormItem label="同步平台" name="attrThree" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear disabled={!moduleCode}>
                            {formatOptions(contractRetryNodes)}
                        </Select>
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default SearchLayout;
