import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Form, Space, Typography } from 'antd';
import React from 'react';
import { history } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import TemplateForm from '../components/TemplateForm';

const AddTemplate = () => {
    const [form] = Form.useForm();
    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回列表页" />
                    <Typography.Title level={4}>新建模板</Typography.Title>
                </Space>
            }
        >
            <Card>
                <TemplateForm form={form} type="ADD" />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(AddTemplate);
