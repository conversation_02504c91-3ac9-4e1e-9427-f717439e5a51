import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Form, Space, Typography } from 'antd';
import React, { useEffect } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import TemplateForm from '../components/TemplateForm';
import { useRequest } from 'ahooks';
import { getTemplateDetail } from '@/services/AssetCenter/QrcodeManageApi';

const EditTemplate = () => {
    const [form] = Form.useForm();

    const params: { id?: string } = useParams();
    const id = params?.id;

    const { run, data, loading } = useRequest(
        (id) => {
            return getTemplateDetail(id);
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (id) {
            run(id);
        }
    }, [id]);

    useEffect(() => {
        if (data?.data?.id) {
            form?.setFieldsValue({
                ...data?.data,
            });
        }
    }, [data]);

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回列表页" />
                    <Typography.Title level={4}>编辑模板</Typography.Title>
                </Space>
            }
        >
            <Card loading={loading}>
                <TemplateForm form={form} templateData={data?.data} type="EDIT" />
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(EditTemplate);
