import { LeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    Image,
    Input,
    Row,
    Space,
    Popconfirm,
    Modal,
    message,
    Spin,
    Typography,
    Select,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { usePagination, useRequest } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { connect, history, Link } from 'umi';

import { API_HOST } from '@/config/global';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import { deleteTemplate, queryTemplateList } from '@/services/AssetCenter/QrcodeManageApi';
import { QRCODE_MUDULE_TYPE } from '../components/TemplateForm';

const ListPage: React.FC<any> = (props) => {
    const { dispatch, global } = props;
    const { pageInit } = global;
    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();
    const [showModal, setShowModal] = useState<boolean>(false);
    const [previewImage, setPreviewImage] = useState<number>();
    const [deleteId, setDeleteId] = useState<number>(-1);
    const [imageLoading, setImageLoading] = useState<boolean>(true);
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        refresh,
        pagination,
        params,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryTemplateList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            onError: () => {},
            cacheKey: '/assetCenter/qrcode/template',
            refreshDeps: [pageInit[pathname]?.form],
        },
    );

    const onFinish = (formData?: API.QrCodeTemplateRequest) => {
        searchList({ pageSize: pagination.pageSize, current: 1 }, { ...formData });
    };

    const refreshList = () => {
        refresh();
    };

    useEffect(() => {
        if (pageInit[pathname]) {
            const para = pageInit[pathname];
            searchList(para[0] as any, para[1]);
            form.setFieldsValue({ ...para[1] });
        } else {
            searchList({ pageSize: pagination.pageSize, current: 1 });
        }
    }, []);

    useEffect(() => {
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: params,
        });
    }, [params]);

    const { run: delTemp } = useRequest(
        (id: number) => {
            return deleteTemplate(id);
        },
        {
            manual: true,
            onSuccess: (res) => {
                setDeleteId(-1);
                if (res?.ret === 200) {
                    message.success('删除模板成功');
                    refreshList();
                } else {
                    message.error(res?.msg || '删除模板失败');
                }
            },
            onError: () => {
                message.error('删除模板失败，请稍后再试');
                setDeleteId(-1);
            },
        },
    );

    const columns: ColumnsType<any> = [
        {
            title: '模板名称',
            width: 120,
            dataIndex: 'name',
            fixed: 'left',
        },
        {
            title: '模板类型',
            width: 120,
            dataIndex: 'templateTypeName',
        },
        {
            title: '底图',
            dataIndex: 'backgroundFileUrl',
            width: 160,
            render: (value: string) => {
                return <Image src={value} width={64} preview />;
            },
        },
        {
            title: 'logo',
            dataIndex: 'logoFileUrl',
            width: 160,
            render: (value: string) => {
                return <Image src={value} width={64} preview />;
            },
        },
        {
            title: '操作',
            width: 140,
            dataIndex: 'id',
            fixed: 'right',
            render: (value: number, record) => {
                return (
                    <Space size="small">
                        <Button
                            type="link"
                            onClick={() => {
                                setShowModal(true);
                                setPreviewImage(value);
                            }}
                        >
                            预览
                        </Button>
                        <Link to={`/assetCenter/qrcode/template/edit/${record?.id}`}>编辑</Link>
                        <Popconfirm
                            title="确认要删除吗？"
                            onConfirm={() => {
                                setDeleteId(value);
                                delTemp(value);
                            }}
                        >
                            <Button type="link" loading={value === deleteId}>
                                删除
                            </Button>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>模板管理</Typography.Title>
                </Space>
            }
        >
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <Row gutter={24}>
                        <Col span={8}>
                            <Form.Item label="模板名称" name="name">
                                <Input maxLength={50} />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="二维码类型" name="templateType">
                                <Select placeholder="请选择" allowClear>
                                    <Select.Option value={QRCODE_MUDULE_TYPE.BIND}>
                                        绑码版
                                    </Select.Option>
                                    <Select.Option value={QRCODE_MUDULE_TYPE.FIXED}>
                                        固定版
                                    </Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Space>
                                <Button type="primary" htmlType="submit">
                                    查询
                                </Button>
                                <Button
                                    htmlType="reset"
                                    onClick={() => {
                                        onFinish();
                                    }}
                                >
                                    重置
                                </Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
                <div className={styles['btn-bar']}>
                    <Space>
                        <Link to="/assetCenter/qrcode/template/add">
                            <Button type="primary">新增</Button>
                        </Link>
                    </Space>
                </div>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    offsetHeader={0}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
                <Modal
                    title="预览"
                    visible={showModal}
                    onCancel={() => {
                        setImageLoading(true);
                        setShowModal(false);
                    }}
                    footer={false}
                    destroyOnClose
                >
                    <Spin spinning={imageLoading}>
                        <Image
                            onLoad={() => {
                                setImageLoading(false);
                            }}
                            src={`${API_HOST}/mng-ast/openapi/qrcode/template/preview?id=${previewImage}`}
                            preview={false}
                            width="100%"
                        />
                    </Spin>
                </Modal>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global }: any) => ({
    global,
}))(ListPage);
