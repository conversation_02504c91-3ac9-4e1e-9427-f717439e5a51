import { usePagination, useRequest } from 'ahooks';
import {
    Button,
    Col,
    Form,
    Image,
    Row,
    Space,
    Typography,
    Card,
    Input,
    Modal,
    Popover,
    InputNumber,
    message,
    Spin,
    Select,
    Radio,
    Popconfirm,
    Checkbox,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { Fragment, useEffect, useState } from 'react';
import { history, request, useModel, Link } from 'umi';

import TablePro from '@/components/TablePro';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { createCode, queryTemplateList } from '@/services/AssetCenter/QrcodeManageApi';
import { getOrgsByAccount } from '@/services/loginApi';
import { API_HOST } from '@/config/global';
import SystemAccountSelect from '@/components/SystemAccountSelect';
import { LeftOutlined } from '@ant-design/icons';
import { QRCODE_MUDULE_TYPE } from '../components/TemplateForm';
import BindModal from '../../components/BindModal';
import { isEmpty } from '@/utils/utils';

const PREVIEW_IMAGE_URL = `${API_HOST}/mng-ast/openapi/qrcode/template/preview?id=`;

const formItemLayout: any = {
    labelCol: { flex: '0 0 120px' },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const EquipmentListKey = 'temp_list';
const FixedEquipmentFormItem = () => {
    const { setShowModal } = useModel('qrcodeManage.bind');

    const [chooseStations, updateChooseStations] = useState([]);
    const rowSelection = {
        fixed: true,
        type: 'checkbox',
        selectedRowKeys: chooseStations.map((item: any) => item.gunEquipId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            updateChooseStations(selectedRows);
        },
    };

    return (
        <Form.Item
            shouldUpdate={(pre, cur) => pre[EquipmentListKey] != cur[EquipmentListKey]}
            noStyle
        >
            {({ getFieldValue, setFieldsValue }) => {
                const list = getFieldValue(EquipmentListKey) || [];
                return (
                    <Fragment>
                        <Form.Item
                            label="生成数量"
                            name="num"
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!list?.length) {
                                            return Promise.reject('请选择设备');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Space>
                                {(list?.length && <span>{list.length}</span>) || null}
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        setShowModal(true);
                                    }}
                                >
                                    选择设备
                                </Button>
                            </Space>
                        </Form.Item>
                        {(list?.length && (
                            <Form.Item label=" " name={EquipmentListKey} colon={false}>
                                <Popconfirm
                                    title={'确定批量移除？'}
                                    onConfirm={() => {
                                        for (const item of chooseStations) {
                                            const index = list.indexOf(item);
                                            list.splice(index, 1);
                                            setFieldsValue({ [EquipmentListKey]: list });
                                            updateChooseStations([]);
                                        }
                                    }}
                                >
                                    <Button
                                        style={{ marginBottom: '8px' }}
                                        disabled={chooseStations?.length == 0}
                                    >
                                        批量移除
                                    </Button>
                                </Popconfirm>
                                <TablePro
                                    rowSelection={rowSelection}
                                    scroll={{ x: 'max-content', y: 400 }}
                                    rowKey="gunEquipId"
                                    dataSource={list}
                                    offsetHeader={false}
                                    columns={[
                                        {
                                            title: '运营商',
                                            width: 140,
                                            dataIndex: 'operName',
                                        },
                                        {
                                            title: '场站名称',
                                            width: 180,
                                            dataIndex: 'stationName',
                                            render(text: string | undefined, record: any) {
                                                return (
                                                    <Link
                                                        to={`/assetCenter/stationManage/list/detail/${record?.stationId}`}
                                                        target="_blank"
                                                        title={text}
                                                    >
                                                        {text || '-'}
                                                    </Link>
                                                );
                                            },
                                        },
                                        {
                                            title: '桩名称',
                                            width: 160,
                                            dataIndex: 'pileName',
                                            render(text: string | undefined, record: any) {
                                                return (
                                                    <Link
                                                        to={`/assetCenter/pileManage/list/detail?id=${record?.pileNo}`}
                                                        target="_blank"
                                                        title={text}
                                                    >
                                                        {text || '-'}
                                                    </Link>
                                                );
                                            },
                                        },
                                        {
                                            title: '桩编号',
                                            width: 200,
                                            dataIndex: 'pileNo',
                                        },
                                        {
                                            title: '枪名称',
                                            width: 220,
                                            dataIndex: 'gunName',
                                        },
                                        {
                                            title: '枪编号',
                                            width: 220,
                                            dataIndex: 'gunNo',
                                        },
                                        {
                                            title: '操作',
                                            width: 140,
                                            fixed: 'right',
                                            render: (_: any, record: any) => {
                                                return (
                                                    <Popconfirm
                                                        title={'确定移除？'}
                                                        onConfirm={() => {
                                                            list.splice(list.indexOf(record), 1);
                                                            setFieldsValue({
                                                                [EquipmentListKey]: list,
                                                            });
                                                            if (
                                                                chooseStations.some(
                                                                    (ele: any) =>
                                                                        ele.gunEquipId ==
                                                                        record.gunEquipId,
                                                                )
                                                            ) {
                                                                const index =
                                                                    chooseStations.findIndex(
                                                                        (ele: any) =>
                                                                            ele.gunEquipId ==
                                                                            record.gunEquipId,
                                                                    );
                                                                chooseStations.splice(index, 1);
                                                                updateChooseStations([
                                                                    ...chooseStations,
                                                                ]);
                                                            }
                                                            return Promise.resolve();
                                                        }}
                                                    >
                                                        <Button type="link">移除</Button>
                                                    </Popconfirm>
                                                );
                                            },
                                        },
                                    ]}
                                    noSort
                                    pagination={false}
                                />
                            </Form.Item>
                        )) ||
                            null}

                        <BindModal
                            chooseCallback={(values: any[] = []) => {
                                setShowModal(false);
                                list.push(...values);
                                setFieldsValue({
                                    [EquipmentListKey]: list,
                                    num: list?.length || 0,
                                });
                            }}
                            disabledKeys={list?.map((ele: any) => ele.gunEquipId)}
                        />
                    </Fragment>
                );
            }}
        </Form.Item>
    );
};

const TemplateForm = () => {
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();
    const [showModal, setShowModal] = useState<boolean>(false);
    const [selectTemplate, setSelectTemplate] = useState<any>({});
    const [previewLoading, setPreviewLoading] = useState<boolean>(false);
    const [deptOptions, setDeptOptions] = useState<any[]>([]);

    useEffect(() => {
        if (selectTemplate?.id) {
            setPreviewLoading(true);
        }
    }, [selectTemplate]);

    const { run, loading, data, pagination } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await queryTemplateList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 50,
        },
    );

    const searchTemplate = (formData?: any) => {
        const templateType = form.getFieldValue('templateType');
        run({ current: 1, pageSize: pagination.pageSize }, { ...formData, templateType });
    };

    useEffect(() => {
        if (showModal) {
            searchTemplate();
        }
    }, [showModal]);

    const { run: sunmitCreateCode, loading: createLoading } = useRequest(
        (params: API.QrCodeCreateParam) => {
            return createCode(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    Modal.confirm({
                        content: '提交成功，请等待生成成功后下载',
                        okText: '去下载',
                        cancelText: '继续',
                        onOk: () => {
                            history.push('/assetCenter/qrcode/list?tab=batch');
                        },
                        onCancel: () => {
                            form.resetFields();
                            setSelectTemplate(undefined);
                        },
                    });
                } else {
                    message.error(res?.msg || '生成二维码失败');
                }
            },
        },
    );

    const { run: queryDept, loading: deptLoading } = useRequest(
        (account) => {
            return getOrgsByAccount(account);
        },
        {
            manual: true,
            onSuccess: (result: any) => {
                if (result?.ret === 200 && result?.data) {
                    const options =
                        result?.data?.map((v: any) => {
                            return {
                                key: `${v.orgId}`,
                                label: v.orgName || v.abbreviationOrgName,
                                value: `${v.orgId}`,
                            };
                        }) || [];
                    setDeptOptions(options);
                    form.setFieldsValue({ applyDept: options[0] });
                }
            },
        },
    );

    const submit = (formData: any) => {
        const params = {
            ...formData,
            applyDeptId: formData?.applyDept?.value,
            applyDept: formData?.applyDept?.label,
            remark: formData?.remark?.trim(),
            wordContent: formData?.wordContent?.join(',') || '',
        };

        if (params.templateType == QRCODE_MUDULE_TYPE.FIXED) {
            params.equipIds = params[EquipmentListKey].map((ele: any) => ele.gunEquipId);
            delete params[EquipmentListKey];
        }
        sunmitCreateCode(params);
    };

    const applyBy = Form.useWatch('applyBy', form);

    useEffect(() => {
        form.setFieldsValue({ dept: '' });
        setDeptOptions([]);
        queryDept(applyBy);
    }, [applyBy]);

    const columns: ColumnsType<any> = [
        {
            title: '模板名称',
            width: 120,
            dataIndex: 'name',
            fixed: 'left',
        },
        {
            title: '底图',
            dataIndex: 'backgroundFileUrl',
            width: 80,
            render: (value: string) => {
                return <Image src={value} width={64} preview />;
            },
        },
        {
            title: 'logo',
            width: 80,
            dataIndex: 'logoFileUrl',
            render: (value: string) => {
                return <Image src={value} width={64} preview />;
            },
        },
        {
            title: '操作',
            width: 100,
            dataIndex: 'id',
            fixed: 'right',
            render: (_, record) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                if (selectTemplate?.id !== record?.id) {
                                    setSelectTemplate(record);
                                }
                                form.setFieldsValue({
                                    templateId: record?.id,
                                    wordContent: !isEmpty(record?.wordContent)
                                        ? record?.wordContent?.split(',').filter((ele: any) => ele)
                                        : [],
                                });
                                setShowModal(false);
                            }}
                        >
                            选择
                        </Typography.Link>
                        <Popover
                            trigger={['click']}
                            content={
                                <Image
                                    src={`${PREVIEW_IMAGE_URL}${record?.id}`}
                                    preview
                                    width={128}
                                    placeholder={
                                        <Row justify="center">
                                            <Spin spinning></Spin>
                                        </Row>
                                    }
                                />
                            }
                        >
                            <Typography.Link>预览</Typography.Link>
                        </Popover>
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>生成二维码</Typography.Title>
                </Space>
            }
        >
            <Card bodyStyle={{ backgroundColor: '#fff', padding: '30px 18px 60px 18px' }}>
                <Form form={form} layout="horizontal" colon onFinish={submit} {...formItemLayout}>
                    <Row>
                        <Col span={24}>
                            <Form.Item
                                label="二维码类型"
                                name="templateType"
                                initialValue={QRCODE_MUDULE_TYPE.BIND}
                                required
                            >
                                <Radio.Group
                                    onChange={() => {
                                        form.setFieldsValue({ wordContent: [] });
                                    }}
                                >
                                    <Radio value={QRCODE_MUDULE_TYPE.BIND}>绑码版</Radio>
                                    <Radio value={QRCODE_MUDULE_TYPE.FIXED}>固定版</Radio>
                                </Radio.Group>
                            </Form.Item>

                            <Form.Item
                                shouldUpdate={(pre, cur) => pre.templateType != cur.templateType}
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const templateType = getFieldValue('templateType');
                                    return (
                                        (templateType == QRCODE_MUDULE_TYPE.BIND && (
                                            <Form.Item
                                                label="生成数量"
                                                name="num"
                                                tooltip="请填写，每次最多1000个"
                                                required
                                                rules={[
                                                    {
                                                        required: true,
                                                        type: 'integer',
                                                        min: 1,
                                                        max: 1000,
                                                        message: '请输入1到1000之间的整数',
                                                        transform: (v) => (v ? Number(v) : v),
                                                    },
                                                ]}
                                                {...formItemFixedWidthLayout}
                                            >
                                                <InputNumber
                                                    max={1000}
                                                    min={1}
                                                    step={100}
                                                    precision={0}
                                                    maxLength={4}
                                                    placeholder="请填写，每次最多1000个"
                                                    style={{ width: '100%' }}
                                                />
                                            </Form.Item>
                                        )) || <FixedEquipmentFormItem />
                                    );
                                }}
                            </Form.Item>
                            <SystemAccountSelect
                                fieldName="applyBy"
                                label="申请人"
                                required
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择申请人',
                                    },
                                ]}
                                formItemLayout={formItemFixedWidthLayout}
                            />
                            <Form.Item
                                label="所属部门"
                                name="applyDept"
                                required
                                rules={[{ required: true, message: '请选择所属部门' }]}
                                {...formItemFixedWidthLayout}
                            >
                                <Select
                                    options={deptOptions}
                                    allowClear
                                    labelInValue
                                    loading={deptLoading}
                                />
                            </Form.Item>
                            <Form.Item label="备注说明" name="remark" {...formItemFixedWidthLayout}>
                                <Input maxLength={30} placeholder="请填写，最多30个字" />
                            </Form.Item>
                            <Form.Item
                                label="二维码样式"
                                name="templateId"
                                required
                                rules={[{ required: true, message: '请选择模板' }]}
                                wrapperCol={{ span: 18 }}
                            >
                                <Space>
                                    <Typography.Text>{selectTemplate?.name}</Typography.Text>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            setShowModal(true);
                                        }}
                                    >
                                        选择模板
                                    </Button>
                                </Space>
                            </Form.Item>
                            <Form.Item
                                shouldUpdate={(pre, cur) => pre.templateType != cur.templateType}
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const templateType = getFieldValue('templateType');
                                    if (templateType === QRCODE_MUDULE_TYPE.FIXED) {
                                        return (
                                            !isEmpty(selectTemplate) && (
                                                <Form.Item
                                                    label="文字内容"
                                                    name="wordContent"
                                                    wrapperCol={{ span: 18 }}
                                                >
                                                    <Checkbox.Group>
                                                        <Checkbox value={'1'}>场站名称</Checkbox>
                                                        <Checkbox value={'2'}>桩名称</Checkbox>
                                                        <Checkbox value={'3'}>枪名称</Checkbox>
                                                    </Checkbox.Group>
                                                </Form.Item>
                                            )
                                        );
                                    }
                                    return null;
                                }}
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12} offset={1}>
                            <Form.Item>
                                <Card
                                    bordered={false}
                                    bodyStyle={{
                                        backgroundColor: 'rgba(240, 240, 240, 59)',
                                        borderRadius: '12px',
                                        padding: '30px 40px',
                                        width: '100%',
                                    }}
                                >
                                    <Space direction="vertical">
                                        <Typography.Title
                                            style={{
                                                fontSize: '18px',
                                                color: 'rgba(182, 66, 66, 100)',
                                            }}
                                        >
                                            预览效果
                                        </Typography.Title>
                                        {selectTemplate?.id && (
                                            <Spin spinning={previewLoading}>
                                                <Image
                                                    onLoad={() => {
                                                        setPreviewLoading(false);
                                                    }}
                                                    src={`${PREVIEW_IMAGE_URL}${selectTemplate?.id}`}
                                                    preview={false}
                                                />
                                            </Spin>
                                        )}
                                    </Space>
                                </Card>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item>
                        <Row justify="center">
                            <Space size="large">
                                <Button type="primary" htmlType="submit" loading={createLoading}>
                                    提交
                                </Button>
                                <Button
                                    htmlType="reset"
                                    onClick={() => {
                                        form?.resetFields();
                                        setSelectTemplate(undefined);
                                        history.replace('/assetCenter/qrcode/list');
                                    }}
                                >
                                    取消
                                </Button>
                            </Space>
                        </Row>
                    </Form.Item>
                </Form>
            </Card>
            <Modal
                title="选择模板"
                width={880}
                visible={showModal}
                footer={null}
                onCancel={() => {
                    setShowModal(false);
                }}
                destroyOnClose
            >
                <Space direction="vertical" size="large">
                    <Form form={searchForm} onFinish={searchTemplate} layout="inline">
                        <Form.Item label="模板名称" name="name">
                            <Input maxLength={50} />
                        </Form.Item>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                查询
                            </Button>
                            <Button
                                onClick={() => {
                                    searchForm.resetFields();
                                    searchTemplate();
                                }}
                            >
                                重置
                            </Button>
                        </Space>
                    </Form>
                    <TablePro
                        loading={loading}
                        scroll={{ x: 'max-content', y: 400 }}
                        offsetHeader={false}
                        rowKey="id"
                        dataSource={data?.list}
                        columns={columns}
                        noSort
                        onChange={(pages: any) => {
                            pagination.onChange(pages?.current, pages?.pageSize);
                        }}
                        pagination={{
                            current: pagination.current,
                            total: pagination.total,
                            pageSize: pagination.pageSize,
                            pageSizeOptions: [5, 10, 20, 50],
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total: number) => `共 ${total || 0} 条`,
                        }}
                    />
                </Space>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default React.memo(TemplateForm);
