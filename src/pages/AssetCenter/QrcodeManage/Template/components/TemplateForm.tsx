import { useDebounceFn, useRequest } from 'ahooks';
import type { FormInstance } from 'antd';
import {
    Col,
    Form,
    Row,
    Space,
    Typography,
    Image,
    Card,
    Input,
    InputNumber,
    Button,
    message,
    Radio,
    Checkbox,
    Tooltip,
} from 'antd';
import qs from 'qs';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { history } from 'umi';

import { API_HOST } from '@/config/global';
import UpLoadImg from '@/components/UpLoadImg/UpLoadImgCustom';
import { addModifyTemplate } from '@/services/AssetCenter/QrcodeManageApi';
import { InfoCircleOutlined } from '@ant-design/icons';

// 模板类型
export const QRCODE_MUDULE_TYPE = {
    BIND: '01', // 绑码版
    FIXED: '02', // 固定版
};

// 二维码类型
export const QRCODE_TYPE = {
    LINK: '02', // 链接二维码
    HLHT: '03', // 互联互通二维码
    HLHT_LINK: '04', // 互联互通链接二维码
    THIRD: '05', // 第三方二维码
};

const DefaultQrcodeTemplateProps = {
    codePositionX: 0,
    codePositionY: 20,
    qrCodeHeight: 460,
    qrCodeWidth: 460,
    textPositionX: 0,
    textPositionY: 400,
    secondTextPositionX: 0,
    secondTextPositionY: 430,
    textWidth: 22,
    colorType: '01',
};

const MAX_POSITION = 5000;

const TemplateForm: React.FC<{
    form: FormInstance<API.QrCodeTemplateParam>;
    templateData?: API.QrCodeTemplateParam;
    type: 'ADD' | 'EDIT';
}> = ({ form, templateData, type }) => {
    const [bgImage, setBgImage] = useState<string>('');
    const [imageSize, setImageSize] = useState<{ width: number; height: number } | undefined>(
        undefined,
    );
    const [previewImageUrl, setPreviewImageUrl] = useState<string>('');
    const bgRef = useRef<any>();
    const logoRef = useRef<any>();

    useEffect(() => {
        if (type === 'ADD') {
            form.setFieldsValue({
                ...DefaultQrcodeTemplateProps,
            });
        }
    }, [type]);

    useEffect(() => {
        if (templateData?.backgroundFileId) {
            bgRef?.current?.init({
                url: templateData?.backgroundFileUrl,
                uid: templateData?.backgroundFileId,
            });
            const url = `${API_HOST}/mng-ast/openapi/qrcode/template/preview?id=${templateData?.id}`;
            setPreviewImageUrl(url);
        }
        if (templateData?.logoFileId) {
            logoRef?.current?.init({
                url: templateData?.logoFileUrl,
                uid: templateData?.logoFileId,
            });
        }
    }, [templateData]);

    const imageOnload = (e: any) => {
        setImageSize({
            width: e?.target?.naturalWidth || e?.target?.width,
            height: e?.target?.naturalHeight || e?.target?.height,
        });
    };

    const { run, loading } = useRequest(
        (params) => {
            return addModifyTemplate(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res?.ret === 200) {
                    message.success('提交模板成功');
                    history.replace('/assetCenter/qrcode/template');
                } else {
                    message.error(res?.msg || '提交模板失败');
                }
            },
        },
    );

    const { run: previewQrcode } = useDebounceFn(
        (allFields) => {
            const params = {
                backgroundFileId: allFields?.backgroundFileId,
                codePositionX: allFields?.codePositionX ?? DefaultQrcodeTemplateProps.codePositionX,
                codePositionY: allFields?.codePositionY ?? DefaultQrcodeTemplateProps.codePositionY,
                logoFileId: allFields?.logoFileId,
                qrCodeHeight: allFields?.qrCodeHeight ?? DefaultQrcodeTemplateProps.qrCodeHeight,
                qrCodeWidth: allFields?.qrCodeWidth ?? DefaultQrcodeTemplateProps.qrCodeWidth,
                textPositionX: allFields?.textPositionX ?? DefaultQrcodeTemplateProps.textPositionX,
                textPositionY: allFields?.textPositionY ?? DefaultQrcodeTemplateProps.textPositionY,
                textWidth: allFields?.textWidth ?? DefaultQrcodeTemplateProps.textWidth,
                colorType: allFields?.colorType ?? DefaultQrcodeTemplateProps.colorType,
                templateType: allFields?.templateType,
                codeType: allFields?.codeType,
                thirdCodeContent: allFields?.thirdCodeContent,
                wordContent:
                    (allFields?.wordContent?.length && allFields?.wordContent.join?.(',')) ||
                    allFields?.wordContent ||
                    '',
                secondTextPositionX:
                    allFields?.secondTextPositionX ??
                    DefaultQrcodeTemplateProps.secondTextPositionX,
                secondTextPositionY:
                    allFields?.secondTextPositionY ??
                    DefaultQrcodeTemplateProps.secondTextPositionY,
            };
            const url = `${API_HOST}/mng-ast/openapi/qrcode/template/preview?${qs.stringify(
                params,
            )}`;
            setPreviewImageUrl(url);
        },
        {
            wait: 800,
        },
    );
    const formChange = () => {
        setTimeout(() => {
            const allFields = form.getFieldsValue(true);
            if (allFields?.backgroundFileId) {
                setBgImage(allFields?.backgroundFile);
                previewQrcode(allFields);
            }
        }, 200);
    };

    const submit = (formData: API.QrCodeTemplateParam) => {
        const params = { ...formData, name: formData?.name?.trim() };
        if (type === 'EDIT') {
            params.id = templateData?.id;
        }
        if (Array.isArray(params.wordContent)) {
            params.wordContent = params.wordContent.join?.(',');
        }
        run(params);
    };
    return (
        <Form form={form} layout="horizontal" onFinish={submit} onFieldsChange={formChange}>
            <Row>
                <Col span={12}>
                    <Form.Item
                        label="模板名称"
                        name="name"
                        required
                        rules={[
                            {
                                required: true,
                                transform: (v) => v?.trim(),
                                message: '请填写，最多30个字',
                            },
                        ]}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 14 }}
                    >
                        <Input maxLength={30} placeholder="请填写，最多30个字" />
                    </Form.Item>
                </Col>
            </Row>
            <Row>
                <Col span={12}>
                    <Form.Item
                        label="底图"
                        name="backgroundFileId"
                        required
                        rules={[{ required: true, message: '请上传底图' }]}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 14 }}
                    >
                        <UpLoadImg
                            placeholder="格式支持png、jpg、jpeg，大小不得超过3MB"
                            maxCount={1}
                            sizeInfo={{
                                size: 1024 * 3,
                            }}
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'backgroundFileId',
                                relaTable: 'a_station_label',
                            }}
                            returnId
                            ref={bgRef}
                        />
                    </Form.Item>
                    <Form.Item
                        label="Logo图"
                        name="logoFileId"
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 14 }}
                    >
                        <UpLoadImg
                            placeholder="格式支持png、jpg、jpeg，大小不得超过500KB"
                            maxCount={1}
                            sizeInfo={{
                                size: 500,
                            }}
                            uploadData={{
                                contentType: '02',
                                contRemrk: 'logoFileId',
                                relaTable: 'a_station_label',
                            }}
                            relaId=""
                            returnId
                            ref={logoRef}
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Card style={{ backgroundColor: 'rgba(240, 240, 240, 59)' }}>
                <Row gutter={24}>
                    <Col flex={'0 1 500px'}>
                        <Space direction="vertical">
                            <Typography.Title
                                style={{ fontSize: '18px', color: 'rgba(182, 66, 66, 100)' }}
                            >
                                预览效果
                                {imageSize ? `（${imageSize.width} * ${imageSize.height}）` : ''}
                            </Typography.Title>
                            {(previewImageUrl || bgImage) && (
                                <Image
                                    src={previewImageUrl || bgImage}
                                    width={'100%'}
                                    alt="上传底图后才可以预览"
                                    onLoad={imageOnload}
                                    style={{ maxWidth: '500px', marginBottom: '24px' }}
                                />
                            )}
                        </Space>
                    </Col>
                    <Col span={12}>
                        <Row>
                            <Col span={24}>
                                <Form.Item
                                    label="模板类型"
                                    name="templateType"
                                    labelCol={{ span: 6 }}
                                    initialValue={QRCODE_MUDULE_TYPE.BIND}
                                >
                                    <Radio.Group>
                                        <Radio value={QRCODE_MUDULE_TYPE.BIND}>绑码版</Radio>
                                        <Radio value={QRCODE_MUDULE_TYPE.FIXED}>固定版</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>

                            <Form.Item
                                shouldUpdate={(pre, cur) => pre.templateType != cur.templateType}
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const templateType = getFieldValue('templateType');
                                    return (
                                        (templateType == QRCODE_MUDULE_TYPE.FIXED && (
                                            <Col span={24}>
                                                <Form.Item
                                                    label="二维码类型"
                                                    name="codeType"
                                                    required
                                                    labelCol={{ span: 6 }}
                                                    initialValue={QRCODE_TYPE.HLHT_LINK}
                                                >
                                                    <Radio.Group>
                                                        <Radio value={QRCODE_TYPE.LINK}>
                                                            链接二维码
                                                        </Radio>
                                                        <Radio value={QRCODE_TYPE.HLHT}>
                                                            互联互通二维码
                                                        </Radio>
                                                        <Radio value={QRCODE_TYPE.HLHT_LINK}>
                                                            互联互通链接二维码
                                                        </Radio>
                                                        <Radio value={QRCODE_TYPE.THIRD}>
                                                            第三方二维码
                                                        </Radio>
                                                    </Radio.Group>
                                                </Form.Item>
                                            </Col>
                                        )) ||
                                        null
                                    );
                                }}
                            </Form.Item>

                            <Col span={12}>
                                <Form.Item
                                    label="二维码宽高"
                                    name="qrCodeWidth"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入二维码宽度',
                                        },
                                        {
                                            type: 'integer',
                                            min: 1,
                                            max: MAX_POSITION,
                                            transform: (value) => {
                                                return value ? Number(value) : value;
                                            },
                                            message: `请输入1到${MAX_POSITION}之间的整数`,
                                        },
                                    ]}
                                    labelCol={{ span: 12 }}
                                    wrapperCol={{ span: 12 }}
                                >
                                    <InputNumber
                                        maxLength={4}
                                        min={1}
                                        max={MAX_POSITION}
                                        placeholder={`请输入1到${MAX_POSITION}之间的整数`}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label=""
                                    name="qrCodeHeight"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入二维码高度',
                                        },
                                        {
                                            type: 'integer',
                                            min: 1,
                                            max: MAX_POSITION,
                                            transform: (value) => {
                                                return value ? Number(value) : value;
                                            },
                                            message: `请输入1到${MAX_POSITION}之间的整数`,
                                        },
                                    ]}
                                    wrapperCol={{ span: 12, offset: 1 }}
                                >
                                    <InputNumber
                                        maxLength={5}
                                        placeholder={`请输入1到${MAX_POSITION}之间的整数`}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={12}>
                                <Form.Item
                                    label="二维码位置"
                                    name="codePositionX"
                                    tooltip="X/Y坐标，底图中心点为0，0，X越大越向右，Y越大越向下"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入二维码X轴位置',
                                        },
                                        {
                                            type: 'integer',
                                            min: -5000,
                                            max: MAX_POSITION,
                                            transform: (value) => {
                                                return value ? Number(value) : value;
                                            },
                                            message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                        },
                                    ]}
                                    labelCol={{ span: 12 }}
                                    wrapperCol={{ span: 12 }}
                                >
                                    <InputNumber
                                        maxLength={5}
                                        placeholder={`请输入-5000到${MAX_POSITION}之间的整数`}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label=""
                                    name="codePositionY"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入二维码Y轴位置',
                                        },
                                        {
                                            type: 'integer',
                                            min: -5000,
                                            max: MAX_POSITION,
                                            transform: (value) => {
                                                return value ? Number(value) : value;
                                            },
                                            message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                        },
                                    ]}
                                    wrapperCol={{ span: 12, offset: 1 }}
                                >
                                    <InputNumber
                                        maxLength={5}
                                        placeholder={`"请输入-5000到${MAX_POSITION}之间的整数"`}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Form.Item
                            shouldUpdate={(pre, cur) => pre.templateType != cur.templateType}
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const templateType = getFieldValue('templateType');
                                return (
                                    <Row>
                                        <Col span={12}>
                                            <Form.Item
                                                label={`${
                                                    templateType == QRCODE_MUDULE_TYPE.FIXED
                                                        ? '第一行'
                                                        : ''
                                                }文字位置`}
                                                name="textPositionX"
                                                tooltip="X/Y坐标，底图中心点为0，0，X越大越向右，Y越大越向下"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请输入文字X轴位置',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        min: -5000,
                                                        max: MAX_POSITION,
                                                        transform: (value) => {
                                                            return value ? Number(value) : value;
                                                        },
                                                        message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                                    },
                                                ]}
                                                labelCol={{ span: 12 }}
                                                wrapperCol={{ span: 12 }}
                                            >
                                                <InputNumber
                                                    maxLength={5}
                                                    placeholder={`"输入-5000到${MAX_POSITION}之间的整数`}
                                                />
                                            </Form.Item>
                                        </Col>
                                        <Col span={12}>
                                            <Form.Item
                                                label=""
                                                name="textPositionY"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请输入文字Y轴位置',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        min: -5000,
                                                        max: MAX_POSITION,
                                                        transform: (value) => {
                                                            return value ? Number(value) : value;
                                                        },
                                                        message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                                    },
                                                ]}
                                                wrapperCol={{ span: 12, offset: 1 }}
                                            >
                                                <InputNumber
                                                    maxLength={5}
                                                    placeholder={`"输入-5000到${MAX_POSITION}之间的整数`}
                                                />
                                            </Form.Item>
                                        </Col>
                                        {templateType == QRCODE_MUDULE_TYPE.FIXED && (
                                            <Col span={12}>
                                                <Form.Item
                                                    label={`第二行文字位置`}
                                                    name="secondTextPositionX"
                                                    tooltip="X/Y坐标，底图中心点为0，0，X越大越向右，Y越大越向下"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请输入第二行文字X轴位置',
                                                        },
                                                        {
                                                            type: 'integer',
                                                            min: -5000,
                                                            max: MAX_POSITION,
                                                            transform: (value) => {
                                                                return value
                                                                    ? Number(value)
                                                                    : value;
                                                            },
                                                            message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                                        },
                                                    ]}
                                                    labelCol={{ span: 12 }}
                                                    wrapperCol={{ span: 12 }}
                                                >
                                                    <InputNumber
                                                        maxLength={5}
                                                        placeholder={`"输入-5000到${MAX_POSITION}之间的整数`}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        )}
                                        {templateType == QRCODE_MUDULE_TYPE.FIXED && (
                                            <Col span={12}>
                                                <Form.Item
                                                    label=""
                                                    name="secondTextPositionY"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请输入第二行文字Y轴位置',
                                                        },
                                                        {
                                                            type: 'integer',
                                                            min: -5000,
                                                            max: MAX_POSITION,
                                                            transform: (value) => {
                                                                return value
                                                                    ? Number(value)
                                                                    : value;
                                                            },
                                                            message: `请输入-5000到${MAX_POSITION}之间的整数`,
                                                        },
                                                    ]}
                                                    wrapperCol={{ span: 12, offset: 1 }}
                                                >
                                                    <InputNumber
                                                        maxLength={5}
                                                        placeholder={`"输入-5000到${MAX_POSITION}之间的整数`}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        )}
                                    </Row>
                                );
                            }}
                        </Form.Item>
                        <Row>
                            <Col span={12}>
                                <Form.Item
                                    label="文字大小"
                                    name="textWidth"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入文字大小',
                                        },
                                        {
                                            type: 'integer',
                                            min: 1,
                                            max: 1000,
                                            transform: (value) => {
                                                return value ? Number(value) : value;
                                            },
                                            message: '请输入1到1000之间的整数',
                                        },
                                    ]}
                                    labelCol={{ span: 12 }}
                                    wrapperCol={{ span: 12 }}
                                >
                                    <InputNumber
                                        maxLength={3}
                                        placeholder="请输入1到1000之间的整数"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Form.Item
                                    label="文字颜色"
                                    name="colorType"
                                    required
                                    labelCol={{ span: 6 }}
                                    initialValue={'01'}
                                >
                                    <Radio.Group>
                                        <Radio value="01">白色</Radio>
                                        <Radio value="02">黑色</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>
                            <Form.Item
                                shouldUpdate={(pre, cur) =>
                                    pre.templateType != cur.templateType ||
                                    pre.codeType != cur.codeType
                                }
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    const templateType = getFieldValue('templateType');
                                    const codeType = getFieldValue('codeType');
                                    return (
                                        (templateType == QRCODE_MUDULE_TYPE.FIXED && (
                                            <Fragment>
                                                <Col span={24}>
                                                    <Form.Item
                                                        label="文字内容"
                                                        name="wordContent"
                                                        labelCol={{ span: 6 }}
                                                        initialValue={['1', '2', '3']}
                                                    >
                                                        <Checkbox.Group>
                                                            <Checkbox value={'1'}>
                                                                场站名称
                                                            </Checkbox>
                                                            <Checkbox value={'2'}>桩名称</Checkbox>
                                                            <Checkbox value={'3'}>枪名称</Checkbox>
                                                        </Checkbox.Group>
                                                    </Form.Item>
                                                </Col>
                                                {(codeType == QRCODE_TYPE.THIRD && (
                                                    <Col span={24}>
                                                        <Form.Item
                                                            label="二维码内容"
                                                            tooltip="变量字段：pileNo、gunNo、qrcode、connectorID"
                                                            name="thirdCodeContent"
                                                            labelCol={{ span: 6 }}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请输入',
                                                                },
                                                            ]}
                                                            extra={`示例：https://ndjt.evstyle.cn?1={pileNo}&2={gunNo}&2={qrcode}&4={connectorID}`}
                                                        >
                                                            <Input
                                                                maxLength={100}
                                                                placeholder="请填写"
                                                                autoComplete="off"
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                )) ||
                                                    null}
                                            </Fragment>
                                        )) ||
                                        null
                                    );
                                }}
                            </Form.Item>
                        </Row>
                    </Col>
                </Row>
            </Card>
            <Form.Item>
                <Row justify="center" style={{ marginTop: '32px' }}>
                    <Space size="large">
                        <Button type="primary" htmlType="submit" loading={loading}>
                            提交
                        </Button>
                        <Button
                            htmlType="reset"
                            onClick={() => {
                                bgRef?.current?.rest();
                                logoRef?.current?.rest();
                                history.replace('/assetCenter/qrcode/template');
                            }}
                        >
                            取消
                        </Button>
                    </Space>
                </Row>
            </Form.Item>
        </Form>
    );
};

export default React.memo(TemplateForm);
