import { useRequest } from 'ahooks';
import { Form, InputNumber, Popconfirm, Radio, Select, Space, Modal } from 'antd';
import { useEffect, useState } from 'react';

import { MakeStatusOptions } from '@/constants/qrcode';
import { updateRecordMakeStatusApi } from '@/services/AssetCenter/QrcodeManageApi';

interface Props {
    visible: boolean;
    record: API.QrCodeCreateRecordQueryVo;
    refresh?: () => void;
    materialQualityCodeList: any[];
    materialSizeList: any[];
    onClose: any;
}
const MakeStatusRadio = (props: Props) => {
    const { materialQualityCodeList, materialSizeList, record, refresh, visible, onClose } = props;
    const [form] = Form.useForm();
    const materialSizeCode = Form.useWatch('materialSizeCode', form);
    const { run, loading } = useRequest(
        (params) => {
            return updateRecordMakeStatusApi({ recordId: record?.id, ...params });
        },
        {
            manual: true,
            onSuccess(data) {
                if (data?.ret === 200) {
                    onClose();
                    refresh && refresh();
                }
            },
        },
    );
    const onFinish = (values: any) => {
        const { materialSizeCode, sizeValue1, sizeValue2 } = values;
        if (materialSizeCode) {
            if (materialSizeCode === '05') {
                values.materialSizeValue = `${sizeValue1 || 0}*${sizeValue2 || 0}`;
            } else {
                const name =
                    materialSizeList.filter((item) => item.codeValue === materialSizeCode)[0] || {};
                values.materialSizeValue = name?.codeName;
            }
        }
        delete values.sizeValue1;
        delete values.sizeValue2;
        run(values);
    };
    useEffect(() => {
        const params = { ...record };
        const ohterSizeValue = record?.materialSizeValue || '';
        if (ohterSizeValue && record?.materialSizeCode === '05') {
            params.sizeValue1 = ohterSizeValue?.split('*')?.[0];
            params.sizeValue2 = ohterSizeValue?.split('*')?.[1];
        }
        form.setFieldsValue(params);
    }, [record]);

    return (
        <>
            <Modal
                width={600}
                visible={visible}
                onOk={() => form.submit()}
                onCancel={onClose}
                destroyOnClose
                confirmLoading={loading}
            >
                <Form
                    style={{ width: 500 }}
                    form={form}
                    onFinish={onFinish}
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 20 }}
                >
                    <Form.Item label="物料制作" name="makeStatus">
                        <Radio.Group options={MakeStatusOptions} />
                    </Form.Item>
                    <Form.Item label="物料尺寸" name="materialSizeCode">
                        <Select
                            options={materialSizeList}
                            fieldNames={{ label: 'codeName', value: 'codeValue' }}
                            allowClear
                            placeholder="请选择"
                        />
                    </Form.Item>
                    {materialSizeCode === '05' && (
                        <Form.Item label=" " colon={false}>
                            <Space>
                                <Form.Item
                                    name="sizeValue1"
                                    noStyle
                                    rules={[{ required: true, message: '请输入' }]}
                                >
                                    <InputNumber
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        placeholder="请输入"
                                    />
                                </Form.Item>
                                <div>*</div>
                                <Form.Item
                                    name="sizeValue2"
                                    noStyle
                                    rules={[{ required: true, message: '请输入' }]}
                                >
                                    <InputNumber
                                        precision={2}
                                        step={0.01}
                                        min={0}
                                        placeholder="请输入"
                                    />
                                </Form.Item>
                            </Space>
                        </Form.Item>
                    )}

                    <Form.Item label="物料材质" name="materialQualityCode">
                        <Select
                            options={materialQualityCodeList}
                            fieldNames={{ label: 'codeName', value: 'codeValue' }}
                            allowClear
                            placeholder="请选择"
                        />
                    </Form.Item>
                    <div></div>
                </Form>
            </Modal>
        </>
    );
};

export default MakeStatusRadio;
