import { <PERSON><PERSON>, Space, Card, Typography, Tabs } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { Link, useModel } from 'umi';
import SearchLayout from './SearchLayout';
import TablePro from '@/components/TablePro';
import styles from '@/assets/styles/common.less';
import type { ColumnsType } from 'antd/es/table';
import ListOperation from '../../components/ListOperation';
import BindModal from '../../components/BindModal';
import { QRCodeTypeEnum } from '@/constants/qrcode';

const ListTab: React.FC<any> = (props: any) => {
    const { setActiveListTab } = props;
    const { searchList, listData, listLoading, pagination } = useModel('qrcodeManage.list');
    const formRef = useRef<any>();
    const [qrCodeType, setQrCodeType] = useState<string>(QRCodeTypeEnum.XDT);

    useEffect(() => {
        searchList({ current: 1, pageSize: pagination.pageSize }, { qrCodeType });
    }, [qrCodeType]);

    const refreshList = () => {
        const formData = formRef?.current?.getFieldsValue();
        searchList(
            { current: pagination.current, pageSize: pagination.pageSize },
            { ...formData, qrCodeType },
        );
    };

    const columns: ColumnsType<any> = [
        ...(qrCodeType === QRCodeTypeEnum.XDT
            ? [
                  {
                      title: '二维码编号',
                      width: 180,
                      dataIndex: 'qrCodeNum',
                      fixed: 'left',
                  },
              ]
            : ([
                  {
                      title: '二维码内容',
                      width: 180,
                      dataIndex: 'content',
                      fixed: 'left',
                      render(value: string) {
                          return (
                              <Typography.Paragraph
                                  ellipsis={{ rows: 3, expandable: true }}
                                  style={{ wordBreak: 'break-all' }}
                              >
                                  {value}
                              </Typography.Paragraph>
                          );
                      },
                  },
              ] as any)),
        ...(qrCodeType === QRCodeTypeEnum.XDT
            ? [
                  {
                      title: '模板名称',
                      width: 180,
                      dataIndex: 'templateName',
                  },
              ]
            : []),
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text: string | undefined) {
                return <Typography.Text title={text}>{text || '-'}</Typography.Text>;
            },
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text: string | undefined, record: any) {
                return (
                    <Link
                        to={`/assetCenter/stationManage/list/detail/${record?.stationId}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                );
            },
        },
        {
            title: '所在地区',
            width: 200,
            dataIndex: 'cityName',
            render(text: string | undefined) {
                return <Typography.Text title={text}>{text || '-'}</Typography.Text>;
            },
        },
        {
            title: '桩名称',
            width: 160,
            dataIndex: 'pileName',
            render(text: string | undefined, record: API.QrCodeQueryVo) {
                return (
                    <Link
                        to={`/assetCenter/pileManage/list/detail?id=${record?.pileId}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                );
            },
        },
        {
            title: '桩编号',
            width: 180,
            dataIndex: 'pileNo',
        },
        {
            title: '枪名称',
            width: 160,
            dataIndex: 'gunName',
        },
        {
            title: '枪编号',
            width: 180,
            dataIndex: 'gunNo',
        },
        {
            title: '绑定时间',
            width: 160,
            dataIndex: 'bindTime',
        },
        {
            title: '绑定人',
            width: 120,
            dataIndex: 'bindBy',
        },
        ...(qrCodeType === QRCodeTypeEnum.XDT
            ? [
                  {
                      title: '生成时间',
                      width: 160,
                      dataIndex: 'createdTime',
                  },
                  {
                      title: '生成人',
                      width: 120,
                      dataIndex: 'createdBy',
                  },
              ]
            : []),
        {
            title: '申请人',
            width: 120,
            dataIndex: 'applyBy',
        },
        {
            title: '操作',
            width: 180,
            dataIndex: 'id',
            fixed: 'right',
            render: (_, record) => {
                return (
                    <ListOperation
                        pos="list"
                        record={record}
                        refresh={refreshList}
                        qrCodeType={qrCodeType}
                    />
                );
            },
        },
    ];
    return (
        <Card>
            <SearchLayout ref={formRef} qrCodeType={qrCodeType} />
            <div className={styles['btn-bar']}>
                <Space>
                    <Link to="/assetCenter/qrcode/generate">
                        <Button type="primary">生成二维码</Button>
                    </Link>
                    <Link to="/assetCenter/qrcode/template" target="_blank">
                        <Button>模板管理</Button>
                    </Link>
                </Space>
            </div>
            <Tabs
                onChange={(v) => {
                    setActiveListTab?.(v);
                    setQrCodeType(v);
                }}
                activeKey={qrCodeType}
            >
                <Tabs.TabPane key={QRCodeTypeEnum.XDT} tab="新电途码" />
                <Tabs.TabPane key={QRCodeTypeEnum.THIRD} tab="第三方码" />
            </Tabs>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="id"
                dataSource={listData?.list}
                columns={columns}
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total || 0} 条`,
                }}
            />
            <BindModal refreshQrcodeList={refreshList} />
        </Card>
    );
};

export default React.memo(ListTab);
