import {
    Space,
    Card,
    Typography,
    Form,
    DatePicker,
    Spin,
    message,
    Popconfirm,
    Col,
    Select,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useRequest } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { connect, history, Link, useModel } from 'umi';

import TablePro from '@/components/TablePro';
import SystemAccountSelect from '@/components/SystemAccountSelect';
import DebounceSeachSelect from '@/components/DebounceSeachSelect/index';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { QRCODE_MUDULE_TYPE } from '../../Template/components/TemplateForm';
import MakeStatusRadio from './MakeStatusRadio';
import {
    exportQrcodeRecordList,
    retryGenerate,
    qrcodeRecordDelete,
    getOrgListByNameApi,
    getQrCodeTemplateList,
} from '@/services/AssetCenter/QrcodeManageApi';
import { MakeStatusOptions } from '@/constants/qrcode';
import { EditOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

const BatchListTab: React.FC<any> = (props) => {
    const { dispatch, global } = props;
    const { pageInit, codeInfo = {} } = global;
    const { materialSize: materialSizeList, materialQualityCode: materialQualityCodeList } =
        codeInfo;

    const {
        location: { pathname },
    } = history;
    const { cacheRef } = useModel('qrcodeManage.list');
    const { searchList, listData, listLoading, pagination, refresh } =
        useModel('qrcodeManage.batchList');
    const [form] = Form.useForm();
    const [retryId, setRetryId] = useState<number>(-1);
    const [visible, setVisible] = useState(false);
    const [record, setRecord] = useState({});

    const processParams = (formData: any) => {
        return {
            ...formData,
            createdStartTime:
                (formData?.dates &&
                    formData?.dates[0] &&
                    formData?.dates[0].format('YYYY-MM-DD 00:00:00')) ||
                undefined,
            createdEndTime:
                (formData?.dates &&
                    formData?.dates[1] &&
                    formData?.dates[1].format('YYYY-MM-DD 23:59:59')) ||
                undefined,
            dates: undefined,
        };
    };
    const {
        run: queryTemplate,
        data: templateData,
        loading: templateLoading,
    } = useRequest(
        () => {
            return getQrCodeTemplateList();
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue({ ...pageInit[pathname].form });
            searchList(pageInit[pathname].page, processParams(pageInit[pathname].form));
        } else {
            searchList({ current: 1, pageSize: pagination?.pageSize });
        }
        queryTemplate();
        if (!materialSizeList) {
            dispatch({
                type: 'global/initCode',
                code: 'materialSize',
            });
        }
        if (!materialQualityCodeList) {
            dispatch({
                type: 'global/initCode',
                code: 'materialQualityCode',
            });
        }
    }, []);

    const onFinish = (formData?: API.QrcodeRecordListRequest) => {
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: formData,
                page: { current: 1, pageSize: pagination?.pageSize },
            },
        });
        searchList({ current: 1, pageSize: pagination?.pageSize }, processParams(formData));
    };

    const { run: retry, loading } = useRequest(
        (id) => {
            return retryGenerate(id);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('重试成功');
                    searchList(
                        { current: pagination?.current, pageSize: pagination?.pageSize },
                        form?.getFieldsValue(true),
                    );
                } else {
                    message.error(res?.msg || '重试失败');
                }
            },
        },
    );

    const { run: exportFunc } = useRequest(
        (params) => {
            return exportQrcodeRecordList(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('导出成功，请稍后到文件暂存区查看');
                    cacheRef?.current?.count();
                } else {
                    message.error(res?.msg || '导出失败');
                }
            },
        },
    );
    const { run: deleteRecord } = useRequest(
        (id) => {
            return qrcodeRecordDelete(id);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('删除成功');
                    searchList(
                        { current: pagination?.current, pageSize: pagination?.pageSize },
                        form?.getFieldsValue(true),
                    );
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
        },
    );

    const onExportForm = () => {
        const formData = form.getFieldsValue(true);
        exportFunc(processParams(formData));
    };

    const resetForm = () => {
        form.resetFields();
        onFinish();
    };

    const retryGenerateFun = (id: number) => {
        setRetryId(id);
        retry(id);
    };
    const qrcodeDeleteRecord = (id: number) => {
        deleteRecord(id);
    };

    const refreshTable = () => {
        refresh();
    };

    const columns: ColumnsType<API.QrCodeCreateRecordQueryVo> = [
        {
            title: '生成人',
            width: 100,
            dataIndex: 'createdBy',
        },
        {
            title: '生成时间',
            width: 120,
            dataIndex: 'createdTime',
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'statusName',
            render: (value: string, record) => {
                return (
                    <Typography.Text type={record?.status === '03' ? 'danger' : undefined}>
                        {value}
                    </Typography.Text>
                );
            },
        },
        {
            title: '二维码类型',
            width: 120,
            dataIndex: 'templateTypeName',
        },
        {
            title: '生成数量',
            width: 100,
            dataIndex: 'num',
        },
        {
            title: '所属模板',
            width: 160,
            dataIndex: 'templateName',
        },
        {
            title: '申请人',
            width: 100,
            dataIndex: 'applyBy',
        },
        {
            title: '申请部门',
            width: 120,
            dataIndex: 'applyDept',
        },
        {
            title: '说明',
            width: 160,
            dataIndex: 'remark',
        },
        {
            title: '物料制作',
            dataIndex: 'makeStatusName',
            render(value, record) {
                return (
                    <Space>
                        {value}
                        {record.status === '02' && (
                            <EditOutlined
                                onClick={() => {
                                    setVisible(true);
                                    setRecord(record);
                                }}
                            />
                        )}
                    </Space>
                );
            },
            width: 160,
        },
        {
            title: '物料尺寸',
            width: 160,
            dataIndex: 'materialSizeValue',
        },
        {
            title: '物料材质',
            width: 160,
            dataIndex: 'materialQualityCodeName',
        },
        {
            title: '操作',
            width: 180,
            dataIndex: 'id',
            fixed: 'right',
            render: (value, record) => {
                return (
                    <Space>
                        {record?.status === '02' &&
                            record?.templateType !== QRCODE_MUDULE_TYPE.FIXED && (
                                <Link to={`/assetCenter/qrcode/list/batch/${record?.id}`}>
                                    查看
                                </Link>
                            )}
                        {record?.status === '03' && (
                            <Spin spinning={retryId === value && loading}>
                                <Popconfirm
                                    title="确认要重试吗？"
                                    onConfirm={() => {
                                        retryGenerateFun(value);
                                    }}
                                >
                                    <Typography.Link type="danger">重试</Typography.Link>
                                </Popconfirm>
                            </Spin>
                        )}
                        {record?.status === '02' && !!record?.fileDownloadUrl && (
                            <Popconfirm
                                title="确认要下载二维码吗？"
                                onConfirm={() => {
                                    window.location.href = record?.fileDownloadUrl as string;
                                }}
                            >
                                <Typography.Link>下载二维码</Typography.Link>
                            </Popconfirm>
                        )}
                        {record?.status === '02' && record?.canDelete === '1' && (
                            <Popconfirm
                                title="确认删除？"
                                onConfirm={() => {
                                    qrcodeDeleteRecord(value);
                                }}
                            >
                                <Typography.Link>删除</Typography.Link>
                            </Popconfirm>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <Card>
            {visible && (
                <MakeStatusRadio
                    visible={visible}
                    onClose={() => {
                        setVisible(false);
                        setRecord({});
                    }}
                    record={record}
                    materialSizeList={materialSizeList}
                    materialQualityCodeList={materialQualityCodeList}
                    refresh={refreshTable}
                />
            )}
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Form
                    form={form}
                    onFinish={onFinish}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                >
                    <SearchOptionsBar
                        loading={listLoading}
                        onReset={resetForm}
                        onExportForm={onExportForm}
                        exportName="导出至暂存区"
                        minSpan={24 * 2}
                    >
                        <Col span={8}>
                            <Form.Item noStyle>
                                <SystemAccountSelect label="申请人" fieldName="applyBy" />
                            </Form.Item>
                        </Col>

                        <Col span={8}>
                            <Form.Item label="申请部门" name="applyDeptId">
                                <DebounceSeachSelect
                                    labelKey="orgName"
                                    valueKey="orgId"
                                    fetchApi={getOrgListByNameApi}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="生成时间" name="dates">
                                <RangePicker format="YYYY-MM-DD" allowClear />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="物料制作" name="makeStatus">
                                <Select
                                    placeholder="请选择"
                                    options={MakeStatusOptions}
                                    allowClear
                                />
                            </Form.Item>
                        </Col>

                        <Col span={8}>
                            <Form.Item label="物料材质" name="materialQualityCode">
                                <Select
                                    placeholder="请选择"
                                    options={materialQualityCodeList}
                                    allowClear
                                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="物料尺寸" name="materialSizeCode">
                                <Select
                                    placeholder="请选择"
                                    options={materialSizeList}
                                    allowClear
                                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="模板名称" name="templateId">
                                <Select
                                    allowClear
                                    showSearch
                                    showArrow
                                    options={templateData?.data ?? []}
                                    loading={templateLoading}
                                    fieldNames={{ label: 'name', value: 'id' }}
                                    filterOption={(value, option: any) => {
                                        return (
                                            !value ||
                                            option?.name
                                                ?.toLowerCase()
                                                .indexOf(value.toLowerCase()) >= 0
                                        );
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item label="二维码类型" name="templateType">
                                <Select placeholder="请选择" allowClear>
                                    <Select.Option value={QRCODE_MUDULE_TYPE.BIND}>
                                        绑码版
                                    </Select.Option>
                                    <Select.Option value={QRCODE_MUDULE_TYPE.FIXED}>
                                        固定版
                                    </Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                    </SearchOptionsBar>
                </Form>
                <TablePro
                    loading={listLoading}
                    offsetHeader={false}
                    scroll={{ x: 'max-content' }}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    noSort
                    onChange={(pages: any) => {
                        dispatch({
                            type: 'global/setPageInit',
                            pathname,
                            info: {
                                form: pageInit[pathname]?.form,
                                page: { current: pages?.current, pageSize: pages?.pageSize },
                            },
                        });
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
            </Space>
        </Card>
    );
};

export default connect(({ global }: any) => ({
    global,
}))(BatchListTab);
