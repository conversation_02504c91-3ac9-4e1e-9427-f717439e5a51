import { Radio, Card } from 'antd';
import type { RadioChangeEvent } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { history, useModel } from 'umi';
import qs from 'qs';

import CacheAreaView from '@/components/CacheAreaView';
import ListTab from './components/ListTab';
import BatchListTab from './components/BatchListTab';
import { QRCodeTypeEnum } from '@/constants/qrcode';

const ListPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('list');
    const [activeListTab, setActiveListTab] = useState<string>(QRCodeTypeEnum.XDT);
    const { cacheRef } = useModel('qrcodeManage.list');

    const { tab } = qs.parse(location.search, { ignoreQueryPrefix: true }) || { tab: 'list' };

    useEffect(() => {
        if (['list', 'batch'].includes(tab as string) && activeTab !== tab) {
            setActiveTab(tab as string);
        }
    }, [tab]);

    const tabChange = (e: RadioChangeEvent) => {
        const target = e.target.value;
        history.push(`/assetCenter/qrcode/list?tab=${target}`);
    };
    const bizType = useMemo(() => {
        if (activeTab === 'batch') {
            return 'qrcodeRecord';
        } else if (activeListTab === QRCodeTypeEnum.THIRD) {
            return 'thirdPartyQrcode';
        } else {
            return 'qrcode';
        }
    }, [activeTab, activeListTab]);

    return (
        <PageHeaderWrapper extra={<CacheAreaView bizType={bizType} initRef={cacheRef} />}>
            <Card>
                <Radio.Group
                    onChange={tabChange}
                    value={activeTab}
                    buttonStyle="solid"
                    style={{ marginBottom: 24 }}
                >
                    <Radio.Button value="list" key="list">
                        贴码管理
                    </Radio.Button>
                    <Radio.Button value="batch" key="batch">
                        生成记录
                    </Radio.Button>
                </Radio.Group>
                {activeTab === 'list' && <ListTab setActiveListTab={setActiveListTab} />}
                {activeTab === 'batch' && <BatchListTab />}
            </Card>
        </PageHeaderWrapper>
    );
};

export default React.memo(ListPage);
