import QrCodeLookComponent from '@/components/QrCodeLookItem';
import { QRCodeTypeEnum } from '@/constants/qrcode';
import { unbindQrcode, discardQrcode, restoreQrcode } from '@/services/AssetCenter/QrcodeManageApi';
import { useRequest } from 'ahooks';
import { Modal, Popconfirm, Space, Spin, Typography, message, Image, Card } from 'antd';
import React, { Fragment, useState, useMemo } from 'react';
import { useModel } from 'umi';

const ListOperation: React.FC<{
    pos: string;
    record: API.QrCodeQueryVo;
    refresh: () => void;
    qrCodeType?: string;
}> = ({ pos, record, refresh, qrCodeType }) => {
    const [showPreviewModal, setShowPreviewModal] = useState<boolean>(false);
    const [previewImage, setPreviewImage] = useState<string>();
    const [imageLoading, setImageLoading] = useState<boolean>(true);

    const { setShowModal, setCurrentQrcode } = useModel('qrcodeManage.bind');

    const { run: unBind, loading: unbindLoading } = useRequest(
        (id: number) => {
            return unbindQrcode(id);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res.ret === 200) {
                    message.success('解绑成功');
                    refresh();
                } else {
                    message.error(res?.msg || '解绑失败');
                }
            },
        },
    );

    const { run: discard, loading: discardLoading } = useRequest(
        (params: API.QrDiscardEquipParam) => {
            return discardQrcode(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res.ret === 200) {
                    message.success('废弃成功');
                    refresh();
                } else {
                    message.error(res?.msg || '废弃失败');
                }
            },
        },
    );

    const { run: deleteReq, loading: deleteLoading } = useRequest(
        (params: API.QrDiscardEquipParam) => {
            return discardQrcode(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res.ret === 200) {
                    message.success('删除成功');
                    refresh();
                } else {
                    message.error(res?.msg || '删除失败');
                }
            },
        },
    );

    const { run: restore, loading: restoreLoading } = useRequest(
        (id: number) => {
            return restoreQrcode(id);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res.ret === 200) {
                    message.success('恢复成功');
                    refresh();
                } else {
                    message.error(res?.msg || '恢复失败');
                }
            },
        },
    );

    const editBtns = useMemo(() => {
        const btnList = [];
        const overviewBtn = (
            <Typography.Link
                onClick={() => {
                    setShowPreviewModal(true);
                    if (qrCodeType === QRCodeTypeEnum.XDT) {
                        setPreviewImage(record?.qrCodeImageFileUrl as string);
                    } else {
                        setPreviewImage(record?.content as string);
                    }
                }}
            >
                预览
            </Typography.Link>
        );
        const changeBtn = (
            <Typography.Link
                onClick={() => {
                    setShowModal(true);
                    setCurrentQrcode(record);
                }}
            >
                换绑
            </Typography.Link>
        );
        const unbindBtn = (
            <Popconfirm
                title="确认要解绑吗？"
                onConfirm={() => {
                    unBind(record?.id);
                }}
            >
                <Spin spinning={unbindLoading}>
                    <Typography.Link>解绑</Typography.Link>
                </Spin>
            </Popconfirm>
        );
        const bindBtn = (
            <Typography.Link
                onClick={() => {
                    setShowModal(true);
                    setCurrentQrcode(record);
                }}
            >
                绑定
            </Typography.Link>
        );

        const discardBtn = (
            <Popconfirm
                title="确认废弃？"
                onConfirm={() => {
                    discard({
                        codeId: record.id.toString(),
                    });
                }}
            >
                <Spin spinning={discardLoading}>
                    <Typography.Link>废弃</Typography.Link>
                </Spin>
            </Popconfirm>
        );

        const deleteBtn = (
            <Popconfirm
                title="确认删除二维码吗？"
                onConfirm={() => {
                    deleteReq({
                        codeId: record.id.toString(),
                    });
                }}
            >
                <Spin spinning={deleteLoading}>
                    <Typography.Link>删除</Typography.Link>
                </Spin>
            </Popconfirm>
        );

        const restoreBtn = (
            <Popconfirm
                title="确认恢复？"
                onConfirm={() => {
                    restore(record?.id);
                }}
            >
                <Spin spinning={restoreLoading}>
                    <Typography.Link>恢复</Typography.Link>
                </Spin>
            </Popconfirm>
        );

        btnList.push(overviewBtn);
        if (record?.status === '02') {
            btnList.push(changeBtn);
            if (qrCodeType === QRCodeTypeEnum.XDT) {
                btnList.push(unbindBtn);
            } else {
                btnList.push(deleteBtn);
            }
        }
        if (record?.status === '01' && qrCodeType === QRCodeTypeEnum.XDT) {
            btnList.push(bindBtn);
        }
        if (pos === 'list' && qrCodeType === QRCodeTypeEnum.XDT) {
            btnList.push(discardBtn);
        }
        if (pos === 'batch') {
            if (record?.status === '02') {
                btnList.push(discardBtn);
            } else if (record?.status === '03') {
                btnList.push(restoreBtn);
            }
        }

        return <Fragment>{btnList}</Fragment>;
    }, [pos, record]);

    return (
        <>
            <Space>{editBtns}</Space>
            <Modal
                title="预览"
                visible={showPreviewModal}
                onCancel={() => {
                    setImageLoading(true);
                    setShowPreviewModal(false);
                }}
                footer={false}
                destroyOnClose
            >
                {qrCodeType === QRCodeTypeEnum.XDT && (
                    <Spin spinning={imageLoading}>
                        <Image
                            onLoad={() => {
                                setImageLoading(false);
                            }}
                            src={previewImage}
                            preview={false}
                            width="100%"
                        />
                    </Spin>
                )}
                {qrCodeType === QRCodeTypeEnum.THIRD && (
                    <Card>
                        <QrCodeLookComponent url={previewImage} bigType withLogo={false} />
                    </Card>
                )}
            </Modal>
        </>
    );
};

export default React.memo(ListOperation);
