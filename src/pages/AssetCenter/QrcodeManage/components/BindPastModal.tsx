import { useModel, Link } from 'umi';
import {
    Modal,
    Form,
    Row,
    Col,
    Space,
    Radio,
    Button,
    Cascader,
    Input,
    Popconfirm,
    message,
    notification,
} from 'antd';
import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import commonStyles from '@/assets/styles/common.less';

import { useRequest } from 'ahooks';
import { discardQrcode } from '@/services/AssetCenter/QrcodeManageApi';
import type { SpaceSize } from 'antd/es/space';

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'left',
    wrapperCol: {
        span: 8,
    },
    labelWrap: true,
};

const BindPastModal: React.FC<{ onConfirm: () => void; refreshQrcodeList: () => void }> = ({
    onConfirm,
    refreshQrcodeList,
}) => {
    const { mutate, setShowModal, setCurrentQrcode } = useModel('qrcodeManage.bind');
    const { showPastModal, setShowPastModal, pastQrcode, setPastQrcode } =
        useModel('qrcodeManage.bindPast');
    const [form] = Form.useForm();
    const [open, setOpen] = useState<boolean>(true);

    const { run: discardReq, loading: submitLoading } = useRequest(
        (params: API.QrDiscardEquipParam) => {
            return discardQrcode(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('操作成功');
                    mutate(undefined);
                    form.resetFields();
                    setShowModal(false);
                    setCurrentQrcode(undefined);
                    setShowPastModal(false);
                    setPastQrcode(undefined);
                    refreshQrcodeList();
                    onConfirm && onConfirm();
                } else {
                    message.error(res?.msg || '操作失败');
                }
            },
        },
    );

    const confirmBind = () => {
        const values = form.getFieldsValue();
        const pastCodeList: any[] = [];
        let codeId = '';

        values.pastList.forEach((item: API.QrGunEquipPastVo) => {
            if (item.saveFlag === '0') {
                pastCodeList.push(item.id);
            }
        });
        codeId = pastCodeList.join(',');
        console.log(codeId);
        if (codeId) {
            discardReq({
                codeId: codeId as string,
            });
        } else {
            message.success('操作成功');
            mutate(undefined);
            form.resetFields();
            setShowModal(false);
            setCurrentQrcode(undefined);
            setShowPastModal(false);
            setPastQrcode(undefined);
            refreshQrcodeList();
            onConfirm && onConfirm();
        }
    };

    useEffect(() => {
        if (showPastModal) {
            initEditInfo();
        }
    }, [showPastModal]);

    const changeRadio = () => {
        const values = form.getFieldsValue();
        let openValue = false;

        values.pastList.forEach((item: API.QrGunEquipPastVo) => {
            if (item.saveFlag === '1') {
                openValue = true;
            }
        });
        setOpen(openValue);
    };

    // 初始化默认数据
    const initEditInfo = async () => {
        try {
            const infoList = pastQrcode?.gunEquipVoList,
                params = {
                    pastList: {},
                };
            params.pastList = infoList?.[pastQrcode?.index]?.qrCodeList ?? {};
            params.pastList = infoList?.[pastQrcode?.index]?.qrCodeList.map(
                (item: API.QrGunEquipPastVo) => {
                    return {
                        ...item,
                        saveFlag: '1',
                    };
                },
            );
            console.log(params);
            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    // 点击提交
    const saveFinishEvent = async () => {
        // if (submitLoading) {
        //     return;
        // }
        // try {
        //     await form.validateFields();
        //     const values = form.getFieldsValue();
        //     let params = {
        //         actName: values.actName,
        //         effTime:
        //             (values.distributionTime &&
        //                 values.distributionTime[0].format('YYYY-MM-DD HH:mm:ss')) ||
        //             '',
        //         expTime:
        //             (values.distributionTime &&
        //                 values.distributionTime[1].format('YYYY-MM-DD HH:mm:ss')) ||
        //             '',
        //         citys: values?.citys?.join(',') ?? '',
        //         settleCycle: 1, // 结算周期 1按天，2按月，3按年
        //         subActList: values.subActList,
        //     };
        //     let subArr = [];
        //     let openFlagArr = [];
        //     if (params.subActList && params.subActList.length > 0) {
        //         subArr = params.subActList.map((item, index) => {
        //             if (item?.openFlag) {
        //                 openFlagArr.push(item?.openFlag);
        //             }
        //             let vipPointCheckbox =
        //                 item?.vipPointCheckbox && item?.vipPointCheckbox instanceof Array
        //                     ? item?.vipPointCheckbox.join(',')
        //                     : item?.vipPointCheckbox;
        //             return {
        //                 ...item,
        //                 ruleChargePq: item?.ruleCharge === '01' ? item.ruleChargePq : '',
        //                 ruleChargeAmt: item?.ruleCharge === '02' ? item.ruleChargeAmt : '',
        //                 openFlag: item?.openFlag ? 1 : 0,
        //                 prizeFlag: item?.prizeFlag ? 1 : 0,
        //                 ruleFlag: item?.ruleFlag ? 1 : 0,
        //                 strategyFlag: item?.strategyFlag ? 1 : 0,
        //                 actDetailType: index === 0 ? ACT_SUB_TYPE.NEW : ACT_SUB_TYPE.VIP, // ACT_SUB_TYPE
        //                 vipPointCheckbox,
        //                 monthVipPoint:
        //                     vipPointCheckbox?.indexOf('1') !== -1 ? item?.monthVipPoint : '',
        //                 quarterVipPoint:
        //                     vipPointCheckbox?.indexOf('2') !== -1 ? item?.quarterVipPoint : '',
        //                 yearVipPoint:
        //                     vipPointCheckbox?.indexOf('3') !== -1 ? item?.yearVipPoint : '',
        //             };
        //         });
        //     }
        //     params = { ...params, subActList: subArr };
        //     params.actId = actId;
        //     console.log(321321, params);
        //     if (openFlagArr && openFlagArr.length > 0) {
        //         submitEvent(params);
        //     } else {
        //         message.warning('必须开启至少一项费用信息');
        //     }
        // } catch (error) {
        //     updateSubmitLoading(false);
        // } finally {
        //     updateSubmitLoading(false);
        // }
    };

    return (
        <Modal
            title={`${pastQrcode?.pileName}-绑定设备`}
            width={600}
            visible={showPastModal}
            destroyOnClose
            onOk={() => {
                confirmBind();
            }}
            onCancel={() => {
                mutate(undefined);
                setPastQrcode(undefined);
                setShowPastModal(false);
            }}
            footer={[
                <Button
                    key="back"
                    onClick={() => {
                        mutate(undefined);
                        setPastQrcode(undefined);
                        setShowPastModal(false);
                    }}
                >
                    取消
                </Button>,
                <Button
                    key="submit"
                    type="primary"
                    loading={submitLoading}
                    onClick={() => {
                        if (!open) {
                            confirmBind();
                        }
                    }}
                >
                    {open ? (
                        <Popconfirm
                            title={'确认一个设备同时存在多个码？'}
                            // open={open}
                            onConfirm={() => {
                                confirmBind();
                            }}
                        >
                            确定
                        </Popconfirm>
                    ) : (
                        '确定'
                    )}
                </Button>,
            ]}
        >
            <Row>
                <Col span={20} offset={1}>
                    <ExclamationCircleOutlined style={{ color: '#E29836' }} />
                    <text style={{ paddingLeft: '8px' }}>
                        设备即将与新码绑定，请选择旧码处理方式：
                    </text>
                </Col>
                <Col span={20} offset={1}>
                    <text style={{ color: '#F94A56', paddingLeft: '22px', marginBottom: '15px' }}>
                        旧码已丢失选废弃，仍要使用选保留
                    </text>
                </Col>
                <Col span={20} offset={1}>
                    <Form
                        form={form}
                        onFinish={saveFinishEvent}
                        // initialValues={{
                        //     cardEquityType: actId ? '02' : '01',
                        // }}
                        // {...formItemLayout}
                        // scrollToFirstError
                    >
                        <Form.List name="pastList" initialValue={[]}>
                            {(fields) =>
                                fields.map((field, index) => {
                                    return (
                                        <Fragment key={field.name}>
                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) =>
                                                    prevValues?.pastList?.[field.name].qrCodeNum !==
                                                    curValues?.pastList?.[field.name].qrCodeNum
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const pastList = getFieldValue('pastList');
                                                    const qrCodeNum =
                                                        pastList[field.name].qrCodeNum;
                                                    return (
                                                        <Form.Item
                                                            name={[field.name, 'saveFlag']}
                                                            label={`旧码${index + 1}: ${qrCodeNum}`}
                                                        >
                                                            <Radio.Group onChange={changeRadio}>
                                                                <Radio value={'1'}>保留</Radio>
                                                                <Radio value={'0'}>废弃</Radio>
                                                            </Radio.Group>
                                                        </Form.Item>
                                                    );
                                                }}
                                            </Form.Item>
                                        </Fragment>
                                    );
                                })
                            }
                        </Form.List>
                        {/* <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                            <Button
                                className={commonStyles['form-btn']}
                                type="primary"
                                loading={submitLoading}
                                onClick={() => {
                                    form.submit();
                                }}
                            >
                                提交
                            </Button>
                            <Button
                                className={commonStyles['form-btn']}
                                onClick={() => {
                                    mutate(undefined);
                                    setPastQrcode(undefined);
                                    setShowPastModal(false);
                                }}
                            >
                                取消
                            </Button>
                        </Space> */}
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};

export default React.memo(BindPastModal);
