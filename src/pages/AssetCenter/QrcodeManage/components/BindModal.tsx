import { useModel, Link } from 'umi';
import {
    Modal,
    Form,
    Row,
    Col,
    Space,
    Button,
    Cascader,
    Input,
    Popconfirm,
    message,
    notification,
    Select,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useMemo, useState } from 'react';

import AllStationSelect from '@/components/AllStationSelect';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import TablePro from '@/components/TablePro';
import { getCityListApi } from '@/services/CommonApi';
import { useRequest } from 'ahooks';
import { bindEquipQrcode } from '@/services/AssetCenter/QrcodeManageApi';
import BindPastModal from '../components/BindPastModal';

const { TextArea } = Input;
const onCell = (record: any) => {
    return { rowSpan: record?.rowSpan };
};
const onelClRow1 = () => {
    return { rowSpan: 1 };
};

const BindModal: React.FC<{
    refreshQrcodeList?: () => void;

    chooseCallback?: (values: any[]) => void;
    disabledKeys?: number[]; // 禁用设备标识
}> = ({
    refreshQrcodeList, // 实现后表明是绑定设备，具备单条绑定+单条枪编号搜索功能
    chooseCallback, // 实现后表明是选择是被，具备多条选择+多枪编号筛选功能
    disabledKeys,
}) => {
    const [cityTreeData, changeCityTreeData] = useState<any[]>([]);
    const [form] = Form.useForm();
    const {
        searchList,
        mutate,
        listData,
        listLoading,
        pagination,
        showModal,
        setShowModal,
        currentQrcode,
        setCurrentQrcode,
    } = useModel('qrcodeManage.bind');
    const { setShowPastModal, pastQrcode, setPastQrcode } = useModel('qrcodeManage.bindPast');

    // 因为sizeChanger是按实际显示的行数作为展示项的，业务上是显示桩维度的数量，桩下可能有多个枪，占用多行，每页条数不符合业务需求
    // 单独维护一个字段，用于控制每页的业务层面的数量，antd的size用于控制实际显示的行数
    const [xdtPageSize, updateXdtPageSize] = useState(10);

    const onFinish = (formData: API.getEquipPaginationRequest) => {
        if (!formData?.operId && !formData?.searchKey) {
            notification.error({
                message: '运营商和场站名称至少要选择一个',
            });
        } else {
            const params = { ...formData };
            if (formData.gunNos?.includes('\n')) {
                params.gunNos = formData.gunNos.replaceAll('\n', ',');
            }
            if (formData.gunNos?.length === 0) {
                delete params.gunNos;
            }
            if (formData.searchKey) {
                const searchKeyList = formData.searchKey.split('\n');
                const filterList = searchKeyList.filter((ele) => ele);
                params.stationNames = filterList?.join(',');
                params.searchKey = undefined;
            }
            searchList({ current: 1, pageSize: currentQrcode?.id ? 5 : xdtPageSize }, params);
        }
    };

    const formatCityItem = (data: any) => {
        const list = [];
        for (const item of data) {
            list.push({
                key: item.areaCode,
                label: item.areaName,
                value: item.areaCode,
            });
        }
        return list;
    };

    const initCityTree = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = [];
            for (const item of areaList) {
                list.push({
                    key: item.areaCode,
                    label: item.areaName,
                    value: item.areaCode,
                    children: item.cityList ? formatCityItem(item.cityList) : [],
                });
            }
            changeCityTreeData(list);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (showModal) {
            initCityTree();
        }
    }, [showModal]);

    const { run: bindReq, loading: bindLoading } = useRequest(
        (params: API.QrBindEquipParam) => {
            return bindEquipQrcode(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('绑定成功');
                    mutate(undefined);
                    form.resetFields();
                    setShowModal(false);
                    setCurrentQrcode(undefined);
                    refreshQrcodeList?.();
                } else {
                    message.error(res?.msg || '绑定失败');
                }
            },
        },
    );

    const confirmBind = (gunEquipId: number) => {
        bindReq({
            equipId: gunEquipId,
            codeId: currentQrcode?.id as number,
        });
    };

    const [chooseStations, updateChooseStations] = useState([]);
    const confirmChoose = () => {
        mutate(undefined);
        form.resetFields();
        setShowModal(false);
        setCurrentQrcode(undefined);
        updateChooseStations([]);
        chooseCallback?.(chooseStations);
    };
    const rowSelection = {
        fixed: true,
        type: 'checkbox',
        selectedRowKeys: chooseStations.map((item: any) => item.gunEquipId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            updateChooseStations(selectedRows);
        },
        getCheckboxProps: (record: API.QrEquipVo) => ({
            disabled: disabledKeys && disabledKeys.indexOf(record.gunEquipId || -1) >= 0,
        }),
    };

    const columns: ColumnsType<API.QrEquipVo> = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            onCell: onCell,
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text: string | undefined, record) {
                return (
                    <Link
                        to={`/assetCenter/stationManage/list/detail/${record?.stationId}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                );
            },
            onCell: onCell,
        },
        {
            title: '场站是否上线',
            width: 200,
            dataIndex: 'openStatusName',
            onCell: onCell,
        },
        {
            title: '城市',
            width: 200,
            dataIndex: 'cityName',
            onCell: onCell,
        },
        {
            title: '桩名称',
            width: 160,
            dataIndex: 'pileName',
            render(text: string | undefined, record: any) {
                return (
                    <Link
                        to={`/assetCenter/pileManage/list/detail?id=${record?.pileNo}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                );
            },
            onCell: onCell,
        },
        {
            title: '桩编号',
            width: 200,
            dataIndex: 'pileNo',
            onCell: onCell,
        },
        {
            title: '枪名称',
            width: 220,
            dataIndex: 'gunName',
            onCell: onelClRow1,
        },
        {
            title: '枪编号',
            width: 220,
            dataIndex: 'gunNo',
            onCell: onelClRow1,
        },
        {
            title: '是否绑定',
            width: 160,
            dataIndex: 'bindFlag',
            render: (value: string) => {
                return value === '1' ? '是' : '否';
            },
            onCell: onelClRow1,
        },
    ];

    const showColumns: any[] = [
        ...columns,
        ...(refreshQrcodeList
            ? [
                  {
                      title: '操作',
                      width: 140,
                      fixed: 'right',
                      render: (_: any, record: any) => {
                          return record?.bindFlag === '1' ? (
                              <Button
                                  type="link"
                                  onClick={() => {
                                      setPastQrcode(record);
                                      setShowPastModal(true);
                                  }}
                                  loading={bindLoading}
                              >
                                  绑定
                              </Button>
                          ) : (
                              <Popconfirm
                                  title={
                                      record?.bindFlag === '1'
                                          ? `该设备已与${record?.xdtQrCode}绑定，绑定新码同时解绑旧码`
                                          : '确认绑定该设备？'
                                  }
                                  onConfirm={() => {
                                      confirmBind(record?.gunEquipId as number);
                                  }}
                              >
                                  <Button type="link" loading={bindLoading}>
                                      绑定
                                  </Button>
                              </Popconfirm>
                          );
                      },
                      onCell: onelClRow1,
                  },
              ]
            : []),
    ];

    const operId = Form.useWatch('operId', form);

    const tableData = useMemo(() => {
        if (listData?.list) {
            pagination.pageSize = listData.list.length;
        }
        return listData?.list;
    }, [listData?.list]);

    return (
        <Modal
            title={currentQrcode?.id ? `${currentQrcode?.id}-绑定设备` : '选择设备'}
            width={1080}
            visible={showModal}
            destroyOnClose
            onCancel={() => {
                form.resetFields();
                mutate(undefined);
                setCurrentQrcode(undefined);
                setShowModal(false);
            }}
            footer={chooseCallback ? undefined : false}
            onOk={confirmChoose}
        >
            <Form form={form} onFinish={onFinish} labelCol={{ flex: '0 0 80px' }} name="modalForm">
                <Row gutter={24}>
                    <Col span={8}>
                        <OperSelectItem
                            form={form}
                            formItemLayout={{
                                labelCol: { flex: '0 0 80px', align: 'right' },
                            }}
                            rest={{
                                label: '运营商',
                                name: 'operId',
                            }}
                        />
                    </Col>
                    <Col span={8}>
                        {/* <AllStationSelect
                            form={form}
                            label="场站名称"
                            name="stationId"
                            operId={operId}
                        /> */}
                        <Form.Item
                            label="场站名称"
                            name="searchKey"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (value?.length) {
                                            const searchKeyList = value.split('\n');
                                            const filterList = searchKeyList.filter((ele) => ele);
                                            if (filterList?.length > 100) {
                                                return Promise.reject('场站名称限制100条');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <TextArea placeholder="多个场站使用回车间隔" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="城市" name="city">
                            <Cascader
                                options={cityTreeData}
                                placeholder="请选择"
                                multiple
                                showSearch
                                showCheckedStrategy="SHOW_CHILD"
                                allowClear
                            />
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={24}>
                    <Col span={8}>
                        <Form.Item label="桩编号" name="pileNo">
                            <Input maxLength={50} allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        {refreshQrcodeList ? (
                            <Form.Item label="枪编号" name="gunNo">
                                <Input maxLength={50} allowClear />
                            </Form.Item>
                        ) : (
                            (chooseCallback && (
                                <Form.Item label="枪编号" name="gunNos">
                                    <TextArea allowClear placeholder="多个枪使用回车间隔" />
                                </Form.Item>
                            )) ||
                            null
                        )}
                    </Col>
                    <Col span={8}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                查询
                            </Button>
                            <Button
                                onClick={() => {
                                    form.resetFields();
                                    form.validateFields().then((values) => {
                                        onFinish(values);
                                    });
                                }}
                            >
                                重置
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <TablePro
                rowSelection={chooseCallback && tableData?.length && rowSelection}
                loading={listLoading}
                scroll={{ x: 'max-content', y: 400 }}
                rowKey="gunEquipId"
                dataSource={tableData}
                offsetHeader={false}
                columns={showColumns}
                noSort
                onChange={(pages: any) => {
                    pagination.onChange(pages?.current, !currentQrcode?.id ? xdtPageSize : 5);
                }}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: false,
                    ...((!currentQrcode?.id && {
                        // showTotal: (total: number) => `共 ${listData?.dataTotal || 0} 条`,
                        showQuickJumper: true,
                        itemRender: (_: any, type: any, originalElement: any) => {
                            if (type == 'next') {
                                return (
                                    <Space
                                        onClick={(t) => {
                                            t.stopPropagation();
                                            t.preventDefault();
                                        }}
                                    >
                                        <span
                                            style={{ width: '32px', display: 'block' }}
                                            onClick={() => {
                                                if (
                                                    pagination?.current <
                                                    listData?.dataTotal / xdtPageSize
                                                ) {
                                                    pagination.onChange(
                                                        pagination?.current + 1,
                                                        xdtPageSize,
                                                    );
                                                }
                                            }}
                                        >
                                            {originalElement}
                                        </span>
                                        <Select
                                            style={{ width: '108px' }}
                                            value={xdtPageSize}
                                            onChange={(e) => {
                                                pagination.onChange(pagination?.current, e);
                                                updateXdtPageSize(e);
                                            }}
                                        >
                                            {[10, 50, 100, 300].map((ele) => (
                                                <Select.Option value={ele} key={ele}>
                                                    {ele} 条/页
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </Space>
                                );
                            }
                            return originalElement;
                        },
                    }) || {
                        showQuickJumper: false,
                    }),
                }}
            />
            {refreshQrcodeList && (
                <BindPastModal
                    onConfirm={() => {
                        confirmBind(pastQrcode?.gunEquipId as number);
                    }}
                    refreshQrcodeList={refreshQrcodeList}
                />
            )}
        </Modal>
    );
};

export default React.memo(BindModal);
