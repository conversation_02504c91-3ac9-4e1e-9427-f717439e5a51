import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Cascader, Col, DatePicker, Form, Input, Select, message } from 'antd';
import { getCityListApi } from '@/services/CommonApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { useModel } from 'umi';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';
import SystemAccountSelect from '@/components/SystemAccountSelect';
import { useRequest } from 'ahooks';
import { exportQrcodeList } from '@/services/AssetCenter/QrcodeManageApi';

const { RangePicker } = DatePicker;

const SearchLayout = (props: any, ref: any) => {
    const { record: recordId } = props;
    const [form] = Form.useForm();
    const [cityTreeData, changeCityTreeData] = useState<any[]>([]);
    const { listLoading, searchList, pagination, cacheRef } = useModel('qrcodeManage.batch');
    useImperativeHandle(ref, () => ({
        getFieldsValue: () => {
            return processParams(form?.getFieldsValue(true));
        },
    }));

    useEffect(() => {
        initCityTree();
    }, []);

    const processParams = (formData: API.QrCodeListRequst): API.QrCodeListRequst => {
        const params = {
            ...formData,
            bindStartTime:
                (formData?.bindDates && formData?.bindDates[0]?.format('YYYY-MM-DD 00:00:00')) ||
                undefined,
            bindEndTime:
                (formData?.bindDates && formData?.bindDates[1]?.format('YYYY-MM-DD 23:59:59')) ||
                undefined,
            bindDates: undefined,
            city: formData?.city
                ? formData?.city?.map((v: string[]) => v[v.length - 1]).join(',')
                : undefined,
            gunNo: formData?.gunNo?.trim(),
            pileNo: formData?.pileNo?.trim(),
            qrCodeNum: formData?.qrCodeNum?.trim(),
            stationName: undefined,
        };
        return params;
    };

    const onFinish = (formData: API.QrCodeListRequst) => {
        const params = {
            ...processParams(formData),
            recordId,
        };
        searchList({ current: pagination?.current, pageSize: pagination?.pageSize }, params);
    };

    const resetForm = () => {
        form.resetFields();
        searchList({ current: 1, pageSize: pagination?.pageSize }, { recordId });
    };

    const { run: exportFunc } = useRequest(
        (params) => {
            return exportQrcodeList(params);
        },
        {
            manual: true,
            onSuccess: (res: API.CommonBooleanResponse) => {
                if (res?.ret === 200) {
                    message.success('导出成功，请稍后到文件暂存区查看');
                    cacheRef?.current?.count();
                } else {
                    message.error(res?.msg || '导出失败');
                }
            },
        },
    );

    const onExportForm = () => {
        exportFunc({ ...processParams(form?.getFieldsValue(true)), recordId });
    };

    const initCityTree = async () => {
        try {
            const { data: areaList } = await getCityListApi();
            const list = [];
            for (const item of areaList) {
                list.push({
                    key: item.areaCode,
                    label: item.areaName,
                    value: item.areaCode,
                    children: item.cityList ? formatCityItem(item.cityList) : [],
                });
            }
            changeCityTreeData(list);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const formatCityItem = (data: any) => {
        const list = [];
        for (const item of data) {
            list.push({
                key: item.areaCode,
                label: item.areaName,
                value: item.areaCode,
            });
        }
        return list;
    };

    const operId = Form.useWatch('operId', form);

    return (
        <Form
            form={form}
            onFinish={onFinish}
            scrollToFirstError
            ref={ref}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            {...props}
        >
            <SearchOptionsBar
                loading={listLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
                exportName="导出至暂存区"
                minSpan={24 * 2}
            >
                <Col span={8}>
                    <Form.Item label="二维码编号" name="qrCodeNum">
                        <Input maxLength={50} allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <OperSelectItem
                        form={form}
                        rest={{ name: 'operId', label: '运营商' }}
                        formItemLayout={{ labelCol: { flex: '0 0 90px' }, labelAlign: 'right' }}
                    />
                </Col>
                <Col span={8}>
                    <AllStationSelect
                        form={form}
                        label="场站名称"
                        name="stationId"
                        operId={operId}
                    />
                </Col>
                <Col span={8}>
                    <Form.Item label="城市" name="city">
                        <Cascader
                            options={cityTreeData}
                            expandTrigger="hover"
                            multiple
                            showSearch
                            showCheckedStrategy="SHOW_CHILD"
                            placeholder="请选择"
                            allowClear
                        />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="桩编号" name="pileNo">
                        <Input maxLength={50} allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="枪编号" name="gunNo">
                        <Input maxLength={50} allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="绑定状态" name="status">
                        <Select
                            options={[
                                {
                                    label: '未绑定',
                                    value: '01',
                                },
                                {
                                    label: '已绑定',
                                    value: '02',
                                },
                                {
                                    label: '已废弃',
                                    value: '03',
                                },
                            ]}
                            allowClear
                        />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="绑定时间" name="bindDates">
                        <RangePicker format="YYYY-MM-DD" allowClear />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <SystemAccountSelect label="绑定人" fieldName="bindBy" />
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export default React.forwardRef(SearchLayout);
