import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Space, Card, Typography } from 'antd';
import React, { useEffect, useRef } from 'react';
import { history, useModel, Link, useParams } from 'umi';
import type { ColumnsType } from 'antd/es/table';
import { LeftOutlined } from '@ant-design/icons';
import ListOperation from '../components/ListOperation';
import BindModal from '../components/BindModal';
import SearchLayout from './components/SearchLayout';
import TablePro from '@/components/TablePro';
import CacheAreaView from '@/components/CacheAreaView';
import { QRCodeTypeEnum } from '@/constants/qrcode';

const STATUS_TITLE = {
    '01': '未绑定',
    '02': '已绑定',
    '03': '已废弃',
};
export default React.memo(() => {
    const { searchList, listData, listLoading, pagination, cacheRef } =
        useModel('qrcodeManage.batch');
    const formRef = useRef<any>();
    const params: { id?: string } = useParams();
    const id = params?.id;
    useEffect(() => {
        if (id) {
            searchList({ current: 1, pageSize: pagination?.pageSize }, { recordId: id });
        }
    }, [id]);

    const refreshList = () => {
        searchList(
            { current: pagination?.current, pageSize: pagination?.pageSize },
            { ...formRef?.current?.getFieldsValue(), recordId: id },
        );
    };

    const columns: ColumnsType<any> = [
        {
            title: '二维码编号',
            width: 180,
            dataIndex: 'qrCodeNum',
            fixed: 'left',
        },
        {
            title: '绑定状态',
            width: 180,
            dataIndex: 'status',
            render: (value: any) => {
                return (
                    <text style={{ color: value === '03' ? 'red' : '' }}>
                        {STATUS_TITLE[value] || '-'}
                    </text>
                );
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text: string | undefined) {
                return <Typography.Text title={text}>{text || '-'}</Typography.Text>;
            },
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text: string | undefined, record) {
                return text ? (
                    <Link
                        to={`/assetCenter/stationManage/list/detail/${record?.stationId}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                ) : (
                    '-'
                );
            },
        },
        {
            title: '城市',
            width: 200,
            dataIndex: 'cityName',
            render(text: string | undefined) {
                return <Typography.Text title={text}>{text || '-'}</Typography.Text>;
            },
        },
        {
            title: '桩名称',
            width: 160,
            dataIndex: 'pileName',
            render(text: string | undefined, record) {
                return text ? (
                    <Link
                        to={`/assetCenter/pileManage/list/detail?id=${record?.pileId}`}
                        target="_blank"
                        title={text}
                    >
                        {text || '-'}
                    </Link>
                ) : (
                    '-'
                );
            },
        },
        {
            title: '桩编号',
            width: 180,
            dataIndex: 'pileNo',
        },
        {
            title: '枪名称',
            width: 160,
            dataIndex: 'gunName',
        },
        {
            title: '枪编号',
            width: 180,
            dataIndex: 'gunNo',
        },
        {
            title: '绑定人',
            width: 120,
            dataIndex: 'bindBy',
        },
        {
            title: '绑定时间',
            width: 160,
            dataIndex: 'bindTime',
        },

        {
            title: '废弃时间',
            width: 160,
            dataIndex: 'discardTime',
        },
        {
            title: '废弃人',
            width: 120,
            dataIndex: 'discardBy',
        },
        {
            title: '操作',
            width: 180,
            fixed: 'right',
            render: (_, record) => {
                return (
                    <ListOperation
                        pos="batch"
                        record={record}
                        refresh={refreshList}
                        qrCodeType={QRCodeTypeEnum.XDT}
                    />
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回列表页" />
                    <Typography.Title level={4}>二维码绑定情况</Typography.Title>
                </Space>
            }
            extra={<CacheAreaView bizType="qrcodeBind" initRef={cacheRef} />}
        >
            <Card>
                <SearchLayout ref={formRef} record={id} />
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="qrCodeNum"
                    dataSource={listData?.list}
                    columns={columns}
                    offsetHeader={0}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    noSort
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
            </Card>
            <BindModal refreshQrcodeList={refreshList} />
        </PageHeaderWrapper>
    );
});
