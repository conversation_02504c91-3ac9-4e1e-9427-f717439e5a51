import {
    Form,
    Button,
    Row,
    Col,
    Space,
    Input,
    Select,
    Upload,
    Tooltip,
    Popconfirm,
    message,
    notification,
} from 'antd';
import React, { Fragment, useState, useEffect, useRef, useContext, useMemo } from 'react';
import TablePro from '@/components/TablePro';
import { connect } from 'umi';
import {
    PlusOutlined,
    MinusCircleOutlined,
    UpCircleOutlined,
    DownCircleOutlined,
    PlusCircleOutlined,
    CloseCircleOutlined,
    InfoCircleOutlined,
    LoadingOutlined,
} from '@ant-design/icons';
import { BASE_URL } from '@/config/global';
import commonStyles from '@/assets/styles/common.less';
import { getStationTagTreeApi } from '@/services/AssetCenter/TagManageApi';

const iconSize = 24;

const FormItem = Form.Item;

const { TextArea } = Input;
const { Option } = Select;

const UploadButton = ({ uploading }) => (
    <div>{uploading ? <LoadingOutlined /> : <PlusOutlined />}</div>
);

const UploadImageLayout = (props) => {
    const {
        form,
        name = 'stationLabelPictureList',
        dataIndex,
        isSmall,
        sizeInfo = { size: 100 },
        disabled,
    } = props;
    const [fileList, updateFileList] = useState([]);
    const changeFileListAndForm = (list) => {
        updateFileList([...list]);
        setFormValues(list);
    };
    useEffect(() => {
        const file = form.getFieldValue([name, dataIndex]) || [];
        // const name = `${(Array.isArray(filePath) && filePath[filePath.length - 1]) || filePath}`;
        // const tempList = [...(file?.[name] || [])];
        if (file.url || file.uid) {
            const tempList = [file];
            changeFileListAndForm(tempList);
        }
    }, [form, dataIndex]);

    const setFormValues = (values) => {
        const list = form.getFieldValue(name);
        if (values[0]) {
            list[dataIndex] = { ...list[dataIndex], ...values[0] };
        } else {
            list[dataIndex] = list[dataIndex]?.labelId ? { labelId: list[dataIndex]?.labelId } : {};
        }

        form.setFieldsValue({ [name]: list });
    };

    const handleChange = ({ fileList: newFileList }) => {
        const list = newFileList?.map((ele) => {
            if (ele.status != 'uploading') {
                if (ele.url) {
                } else if (ele.response?.ret == 401) {
                    const logout = window?.loginStore?.logout;
                    logout?.();
                    notification.error({
                        message: '登录失效，请重新登录',
                    });
                } else if (!ele.response?.ret || ele.response?.ret != 200) {
                    ele.response = ele.response?.msg || '上传失败';
                    ele.status = 'error';
                } else if (ele.response?.data?.filePath) {
                    ele.url = ele.response?.data?.filePath;
                    ele.filePath = ele.response?.data?.relativePath;
                    ele.fileId = ele.response?.data?.fileId;
                }
            }
            let params = {
                fileId: ele?.fileId,
                filePath: ele?.filePath,
                uid: ele?.uid,
                url: ele?.url,
            };
            return params;
            // return ele;
        });
        updateFileList([...list]);
        if (list?.[0]?.url || list?.length === 0) {
            setFormValues(list);
        }
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('图片格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 < sizeInfo.size;
            if (!isLt2M) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}mb`;
                } else {
                    sizeNum = `${sizeInfo.size}kb`;
                }
                message.error(`图片不大于 ${sizeNum}!`);
                rej();
                return;
            }

            if (sizeInfo.width || sizeInfo.height) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const data = e.target.result;
                    // 加载图片获取图片真实宽度和高度
                    const image = new Image();
                    image.onload = function () {
                        const { width } = image;
                        const { height } = image;
                        if (sizeInfo.width && sizeInfo.height) {
                            if (width != sizeInfo.width && height != sizeInfo.height) {
                                message.error(`图片尺寸必须为${sizeInfo.width}*${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.width) {
                            if (width != sizeInfo.width) {
                                message.error(`图片宽度必须为${sizeInfo.width}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.height) {
                            if (height != sizeInfo.height) {
                                message.error(`图片高度必须为${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        }
                        if (isJpgOrPng && isLt2M) {
                            res();
                        } else {
                            rej();
                        }
                    };
                    image.src = data;
                };
                reader.readAsDataURL(file);
            } else {
                res();
            }
        });

    const upLoadProps = {
        name: 'file',
        accept: '.jpg,.jpeg,.png',
        action: `${origin}${BASE_URL}/pub/upload`,
        data: {
            contentType: 'stationGuide',
            contRemrk: 'popIcon',
            relaTable: 'stationGuide',
            addWatermarkFlag: '1',
            businessType: 'stationGuideBk',
        },
        fileList: fileList,
        onChange: handleChange,
        listType: 'picture-card',
        beforeUpload,
        maxCount: 1,
    };

    return (
        <Upload
            className={(isSmall && commonStyles['upload-smaller']) || undefined}
            {...upLoadProps}
            disabled={disabled}
        >
            {fileList?.length >= 1 ? null : (!disabled && <UploadButton />) || null}
        </Upload>
    );
};

const LabelImageFormItem = (props) => {
    const {
        dispatch,
        global: { codeInfo = {} },
        name = 'stationLabelPictureList',
        form,
        label = '标签图片',
        rules,
        onChange,
        disabled,
        ...otherProps
    } = props;

    const [selectList, updateSelectList] = useState([]);
    useEffect(() => {
        initTreeData();
    }, []);

    const initTreeData = async () => {
        try {
            const {
                data: { list },
            } = await getStationTagTreeApi();

            updateSelectList(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const [rightTableUpadateFlag, setRightTableUpadateFlag] = useState(false);
    const rightTableRefresh = () => {
        setRightTableUpadateFlag(!rightTableUpadateFlag);
    };
    const configMoved = (_, index, isUpper) => {
        const rightDatas = form.getFieldValue(name);
        const item = rightDatas[index];
        rightDatas?.splice?.(index, 1);
        if (isUpper) {
            // 向上
            rightDatas?.splice?.(index - 1, 0, item);
        } else {
            // 向下
            rightDatas?.splice?.(index + 1, 0, item);
        }
        form.setFieldsValue({ temp_config: rightDatas });
    };

    const configColumns = useMemo(
        () => [
            {
                title: '标签',
                width: 120,
                editable: true,
                render(text, record, index) {
                    return (
                        <Form.Item
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues?.[name]?.[index] !== prevValues?.[name]?.[index] ||
                                prevValues?.labelList !== curValues?.labelList
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const stationLabelPictureList = getFieldValue(name);
                                const labelList = getFieldValue('labelList');
                                // 标签图片-标签选项列表格式化
                                let stationLabelPicture = [];
                                stationLabelPicture = labelList?.map((item, index) => {
                                    let params = {
                                        codeName: '',
                                        codeValue: item.toString(),
                                    };
                                    selectList?.forEach((select) => {
                                        if (select.id === parseInt(item)) {
                                            params.codeName = select.name;
                                        }
                                    });
                                    return params;
                                });
                                // 已选标签禁用
                                let otherCodes = [];
                                otherCodes = stationLabelPictureList?.map((ele, otherIndex) => {
                                    return ele?.labelId;
                                });

                                return (
                                    <div>
                                        <Form.Item name={[name, index, 'labelId']} noStyle />
                                        <Form.Item
                                            name={[name, index, 'labelId']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择标签',
                                                },
                                            ]}
                                        >
                                            <Select
                                                placeholder="请选择"
                                                onChange={rightTableRefresh}
                                                allowClear
                                                disabled={disabled}
                                            >
                                                {stationLabelPicture?.map((ele) => (
                                                    <Option
                                                        key={ele.codeValue}
                                                        value={ele.codeValue}
                                                        disabled={
                                                            otherCodes.findIndex(
                                                                (code) => code == ele.codeValue,
                                                            ) > -1
                                                        }
                                                    >
                                                        {ele.codeName}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </div>
                                );
                            }}
                        </Form.Item>
                    );
                },
            },
            {
                title: (
                    <span>
                        图片
                        <Tooltip title="格式支持png、jpg、jpeg，大小不得超过800kb">
                            <InfoCircleOutlined
                                style={{
                                    color: '#0A89FF',
                                    marginLeft: '6px',
                                }}
                            />
                        </Tooltip>
                    </span>
                ),
                // align: 'center',
                width: 240,
                dataIndex: 'url',
                render(text, record, index) {
                    return (
                        <FormItem
                            name={[name]}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (value?.length > 0) {
                                            if (!value?.[index]?.url) {
                                                return Promise.reject('图片未上传');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            style={{ marginBottom: 0 }}
                        >
                            <UploadImageLayout
                                form={form}
                                dataIndex={index}
                                isSmall
                                sizeInfo={{ size: 800 }}
                                disabled={disabled}
                            />
                        </FormItem>
                    );
                },
            },
            ...((disabled && []) || [
                {
                    title: '操作',
                    width: 120,
                    fixed: 'right',
                    render: (text, record, index) => {
                        const upperBtn = (
                            <UpCircleOutlined
                                style={{
                                    fontSize: `${iconSize}px`,
                                }}
                                onClick={() => configMoved(record, index, true)}
                            />
                        );
                        const downerBtn = (
                            <DownCircleOutlined
                                style={{
                                    fontSize: `${iconSize}px`,
                                }}
                                onClick={() => configMoved(record, index, false)}
                            />
                        );
                        const closeBtn = (
                            <Popconfirm
                                title="确定删除？"
                                okText="确定"
                                cancelText="取消"
                                onConfirm={() => {
                                    const rightDatas = form.getFieldValue(name);
                                    rightDatas?.splice?.(index, 1);
                                    form.setFieldsValue({ temp_config: rightDatas });
                                }}
                            >
                                <CloseCircleOutlined
                                    style={{
                                        fontSize: `${iconSize}px`,
                                    }}
                                />
                            </Popconfirm>
                        );
                        return (
                            <Space size="small">
                                {index != form.getFieldValue(name)?.length - 1 && downerBtn}
                                {index != 0 && upperBtn}
                                {closeBtn}
                            </Space>
                        );
                    },
                },
            ]),
        ],
        [rightTableUpadateFlag, selectList],
    );

    return (
        <Fragment>
            <Form.Item
                shouldUpdate={(prevValues, curValues) =>
                    prevValues?.[name] !== prevValues?.[curValues] ||
                    prevValues?.labelList !== curValues?.labelList
                }
                noStyle
            >
                {({ getFieldValue }) => {
                    let stationLabelPictureList = getFieldValue(name);
                    let labelList = getFieldValue('labelList');
                    if (!stationLabelPictureList?.length) {
                        stationLabelPictureList = [];
                    }
                    return labelList?.length > 0 ? (
                        <FormItem label={label} name={name} {...otherProps}>
                            {(!disabled &&
                                ((stationLabelPictureList.length < 50 && (
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            let stationLabelPictureList = getFieldValue(name);
                                            if (!stationLabelPictureList?.length) {
                                                stationLabelPictureList = [];
                                            }
                                            stationLabelPictureList.push({});
                                            form.setFieldsValue({
                                                [name]: stationLabelPictureList,
                                            });
                                        }}
                                        disabled={stationLabelPictureList.length >= 50}
                                    >
                                        添加
                                    </Button>
                                )) || (
                                    <Tooltip title="最多添加50个标签">
                                        <Button disabled type="primary">
                                            添加
                                        </Button>
                                    </Tooltip>
                                ))) ||
                                null}
                            {(stationLabelPictureList?.length && (
                                <>
                                    {(!disabled && (
                                        <>
                                            <br />
                                            <br />
                                        </>
                                    )) ||
                                        null}
                                    <FormItem name={name} noStyle>
                                        <TablePro
                                            scroll={{ x: 'max-content' }}
                                            rowKey={(record, index) => record?.labelId || index}
                                            dataSource={[...stationLabelPictureList]}
                                            columns={configColumns}
                                            pagination={false}
                                            noSort
                                        />
                                    </FormItem>
                                </>
                            )) ||
                                null}
                        </FormItem>
                    ) : null;
                }}
            </Form.Item>
        </Fragment>
    );
};

export default connect(({ global }) => ({
    global,
}))(LabelImageFormItem);
