import { useRef, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Modal, Row, Col, Input, Descriptions, Space, Button, Select, Form } from 'antd';
import AMapLoader from '@amap/amap-jsapi-loader';
import { IMG_URL } from '@/config/global';

const { Option } = Select;

let map = null;

let stationMarker = null;

const FormItem = Form.Item;

const isLng = (latStr) => {
    return new RegExp(
        /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{6}|180\.0{6})$/,
    ).test(latStr);
};

const isLat = (lngStr) => {
    return new RegExp(/^(\-|\+)?([0-8]?\d{1}\.\d{6}|90\.0{6}|[0-8]?\d{1}\.\d{6})$/).test(lngStr);
};

const SelectLocationModal = (props, ref) => {
    const { open, lon, lat, onClose, onOk } = props;

    const [locationForm] = Form.useForm();

    const AMapJs = useRef();

    useEffect(() => {
        return () => {
            unMap();
        };
    }, []);

    useEffect(() => {
        if (open) {
            loadMap();
        }
    }, [open]);

    useEffect(() => {
        if (lon && lat) {
            locationForm.setFieldsValue({
                lon,
                lat,
            });
        }
    }, [lon, lat]);

    const selectFinishEvent = (values) => {
        onOk && onOk(values);
    };

    const updateFormLocation = (lnglat) => {
        locationForm.setFieldsValue({
            lon: lnglat.lng,
            lat: lnglat.lat,
        });
    };

    const loadMap = async () => {
        window._AMapSecurityConfig = {
            securityJsCode: 'f1d96ff30ff169db2e5bf7c5f683113f',
        };
        return new Promise((resolve, reject) => {
            AMapLoader.load({
                key: 'f0ad238a3a4b229bcdd93c77278f6148', //需要设置您申请的key
                version: '2.0',
                plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.PlaceSearch', 'AMap.AutoComplete'],
                AMapUI: {
                    version: '1.1',
                    plugins: ['overlay/SimpleMarker'],
                },
                Loca: {
                    version: '2.0.0',
                },
            })
                .then((AMap) => {
                    AMapJs.current = AMap;

                    initMap();
                    resolve(AMap);
                })
                .catch((e) => {
                    reject(e);
                });
        });
    };

    const unMap = () => {
        if (map) {
            map.destroy();
            map = null;
            stationMarker = null;
        }
    };

    const initMap = () => {
        if (map) {
            map.destroy();
            map = null;
        }

        map = new AMapJs.current.Map('stationMap', {
            zoom: 13, // 级别
            zooms: [8, 18],
            // center: [100.4438444366, 37.9652031933], // 中心点坐标
            // pitch:45,
            resizeEnable: false,
            zoomEnable: true,

            // layers: [
            //   new AMapJs.current.TileLayer.RoadNet({
            //     zIndex: 20
            //   }),
            //   new AMapJs.current.TileLayer({
            //     zIndex: 6,
            //     opacity: 1,
            //     getTileUrl: 'https://t{1,2,3,4}.tianditu.gov.cn/DataServer?T=ter_w&x=[x]&y=[y]&l=[z]'
            //   })
            // ]
        });

        if (lon && lat) {
            map.setCenter([lon, lat]);
            addMapMarker({
                lng: lon,
                lat: lat,
            });
        }

        map.on('click', (ev) => {
            // 触发事件的对象
            let target = ev.target;
            // 触发事件的地理坐标，AMap.LngLat 类型
            let lnglat = ev.lnglat;
            // 触发事件的像素坐标，AMap.Pixel 类型
            let pixel = ev.pixel;
            // 触发事件类型
            let type = ev.type;

            addMapMarker(lnglat);

            updateFormLocation(lnglat);
        });

        AMapJs.current.plugin(
            ['AMap.ToolBar', 'AMap.Scale', 'AMap.PlaceSearch', 'AMap.AutoComplete'],
            () => {
                // 在图面添加工具条控件，工具条控件集成了缩放、平移、定位等功能按钮在内的组合控件
                map.addControl(new AMapJs.current.ToolBar());

                // 在图面添加比例尺控件，展示地图在当前层级和纬度下的比例尺
                map.addControl(new AMapJs.current.Scale());

                const autoOptions = {
                    input: 'tipinput',
                };
                let auto = new AMapJs.current.AutoComplete(autoOptions);
                let placeSearch = new AMapJs.current.PlaceSearch({
                    map: map,
                }); //构造地点查询类
                auto.on('select', (e) => {
                    placeSearch.setCity(e.poi.adcode);
                    placeSearch.search(e.poi.name, (status, result) => {
                        // 查询成功时，result即对应匹配的POI信息
                        map.clearMap();
                        let pois = result.poiList.pois;
                        for (let i = 0; i < pois.length; i++) {
                            let poi = pois[i];
                            let marker = [];
                            marker[i] = new AMapJs.current.Marker({
                                position: poi.location, // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
                                title: poi.name,
                            });

                            marker[i].on('click', (event) => {
                                const { lnglat } = event;
                                addMapMarker(lnglat);
                                updateFormLocation(lnglat);
                            });
                            // 将创建的点标记添加到已有的地图实例：
                            map.add(marker[i]);
                        }
                        map.setFitView();
                    }); //关键字查询查询
                }); //注册监听，当选中某条记录时会触发
            },
        );
    };

    const addMapMarker = (locationInfo) => {
        if (stationMarker) {
            map.remove(stationMarker);
            stationMarker = null;
        }
        const info = [locationInfo.lng, locationInfo.lat];
        const marker = new AMapJs.current.Marker({
            icon: new AMapJs.current.Icon({
                image: `${IMG_URL}/static/images/common/location.png`,
                imageSize: new AMapJs.current.Size(24, 37),
            }),
            position: info,
            offset: new AMapJs.current.Pixel(-12, -37),
        });
        marker.setMap(map);
        stationMarker = marker;
    };

    return (
        <Modal
            title={'场站地图'}
            width={800}
            visible={open}
            onCancel={onClose}
            footer={null}
            maskClosable={false}
            destroyOnClose
        >
            <Form
                name="select-location"
                form={locationForm}
                onFinish={selectFinishEvent}
                initialValues={{
                    lon,
                    lat,
                }}
                scrollToFirstError
            >
                <Input id="tipinput" placeholder="请输入场站位置" autoComplete="off"></Input>

                <div
                    className="mg-t-20"
                    id="stationMap"
                    style={{ width: '100%', height: '500px' }}
                ></div>
                <br></br>
                <Row gutter={20}>
                    <Col span={12}>
                        <FormItem
                            label="经度"
                            name="lon"
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('请配置经度');
                                        }
                                        if (!isLng(value)) {
                                            return Promise.reject('请配置正确经度格式');
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input></Input>
                        </FormItem>
                    </Col>
                    <Col span={12}>
                        <FormItem
                            label="纬度"
                            name="lat"
                            required
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('请配置纬度');
                                        }
                                        if (!isLat(value)) {
                                            return Promise.reject('请配置正确纬度格式');
                                        }

                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input></Input>
                        </FormItem>
                    </Col>
                </Row>
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button type="primary" htmlType="submit">
                        提交
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </Space>
            </Form>
        </Modal>
    );
};

export default forwardRef(SelectLocationModal);
