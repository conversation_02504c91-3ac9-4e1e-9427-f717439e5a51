import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, Input, Select, Space, message } from 'antd';
import { Fragment, useImperativeHandle, useState } from 'react';

import {
    checkStationManageAbandonApi,
    getStationManageOptionsApi,
} from '@/services/AssetCenter/StationManangeApi';
import { stationManageAbandon } from '@/services/MngAstApi';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;
const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
    labelAlign: 'right',
};

const StationAbandonModal = (props) => {
    const {
        initRef,
        onFinish,
        global: { operatorList2 },
    } = props;

    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [stationIds, updateStationIds] = useState();

    const showTipEvent = (ids, names) => {
        confirm({
            title: `场站废弃提醒`,
            icon: <ExclamationCircleOutlined />,
            content: (
                <div>
                    <div style={{ fontWeight: '300' }}>
                        以下场站近7天仍有新电途充电次数，请确认是否废弃
                    </div>
                    <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {names.map((ele, index) => (
                            <div key={index} style={{ fontWeight: '500' }}>
                                {ele}
                            </div>
                        ))}
                    </div>
                </div>
            ),
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                showSelfEvent(ids);
            },
        });
    };

    const showSelfEvent = (ids) => {
        updateVisible(true);
        updateStationIds(ids);
        form.resetFields();
        initEnum();
    };

    const [stationAbandonReasonList, updateStationAbandonReasonList] = useState([]);
    useImperativeHandle(initRef, () => ({
        show: async (ids) => {
            try {
                const {
                    data: { flag, stationNameList },
                } = await checkStationManageAbandonApi({ stationIds: ids.join(',') });
                if (!flag) {
                    message.error('系统异常');
                    return;
                }
                if (stationNameList?.length) {
                    showTipEvent(ids, stationNameList);
                } else {
                    showSelfEvent(ids);
                }
            } catch (error) {}
        },
        onClose,
    }));

    const onClose = () => {
        updateWaiting(false);
        updateVisible(false);
        updateStationIds(undefined);
    };

    const initEnum = async () => {
        if (!stationAbandonReasonList?.length) {
            try {
                const {
                    data: { stationAbandonReasonList: _stationAbandonReasonList },
                } = await getStationManageOptionsApi();
                updateStationAbandonReasonList(_stationAbandonReasonList);
                // form.setFieldsValue({ operateReason: '0' });
            } catch (error) {}
        }
    };
    const [isWaiting, updateWaiting] = useState(false);
    return (
        <Modal
            title={'废弃场站'}
            destroyOnClose
            width={600}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            onOk={() => {
                form.validateFields().then(async (values) => {
                    updateWaiting(true);
                    try {
                        const params = {
                            stationIds: stationIds?.join?.(',') || stationIds,
                            ...values,
                            operateDesc: values?.operateDesc
                                ? values.operateDesc
                                : stationAbandonReasonList?.filter(
                                      (v) => v?.codeValue === values?.operateReason,
                                  )?.[0]?.codeName,
                        };
                        const result = await stationManageAbandon(params);
                        onClose();
                        onFinish?.(result, {
                            request: stationManageAbandon,
                            params: params,
                        });
                    } catch (error) {
                    } finally {
                        updateWaiting(false);
                    }
                });
            }}
            okButtonProps={{ loading: isWaiting }}
        >
            <Space style={{ margin: '0 0 16px' }}>
                <ExclamationCircleOutlined />
                <div>废弃后，对应场站的互联互通同步数据将不再更新</div>
            </Space>
            <Form {...formItemLayout} form={form} scrollToFirstError>
                <Form.Item
                    label="原因"
                    rules={[{ required: true, message: '请选择废弃原因' }]}
                    name={'operateReason'}
                    // initialValue={stationAbandonReasonList?.length && '0'}
                >
                    <Select placeholder="请选择">
                        {stationAbandonReasonList?.map((ele, index) => (
                            <Option value={ele.codeValue} key={index}>
                                {ele.codeName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    noStyle
                    shouldUpdate={(pre, after) => {
                        return pre.operateReason != after.operateReason;
                    }}
                >
                    {({ getFieldValue }) => {
                        const operateReason = getFieldValue('operateReason');
                        return (
                            (operateReason == 8 && (
                                <Form.Item name="operateDesc">
                                    <TextArea placeholder="请填写" maxLength={200} showCount />
                                </Form.Item>
                            )) ||
                            (operateReason == 4 && (
                                // 切换为内部
                                <Fragment>
                                    <OperSelectTypeItem
                                        name="operateOperatorId"
                                        label="切换后平台"
                                        rules={[{ required: true, message: '请选择' }]}
                                        form={form}
                                        formItemLayout={formItemLayout}
                                        onChange={(value) => {
                                            const obj = operatorList2?.find(
                                                (ele) => ele.operId == value,
                                            );
                                            // 应后端要求，当operateReason=4时 选择的运营商编号字段传=operateOperatorId
                                            // 当operateReason=4或者13 则运营商简称和文案传=operateDesc
                                            form.setFieldsValue({ operateDesc: obj.operNickname });
                                        }}
                                    />
                                    <Form.Item name={'operateDesc'} noStyle />
                                </Fragment>
                            )) ||
                            (operateReason == 13 && (
                                // 切换为外部
                                <Form.Item
                                    name="operateDesc"
                                    label="切换后平台"
                                    rules={[{ required: true, message: '请填写' }]}
                                >
                                    <Input placeholder="请填写" maxLength={32} showCount />
                                </Form.Item>
                            )) ||
                            null
                        );
                    }}
                </Form.Item>
            </Form>
        </Modal>
    );
};
export default StationAbandonModal;
