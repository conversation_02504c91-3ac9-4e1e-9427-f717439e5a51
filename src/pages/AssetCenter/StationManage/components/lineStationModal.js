import { Button, Card, Col, Form, Modal, Radio, Space, DatePicker, Alert } from 'antd';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { SyncOutlined, ReloadOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';

const FormItem = Form.Item;
const STEP_TYPES = {
    FIRST: '1',
    SECOND: '2',
};
const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};
const FirstStepForm = (props) => {
    const { onClose, onFinish } = props;

    const [firstForm] = Form.useForm();

    return (
        <Form
            {...formItemLayout}
            form={firstForm}
            onFinish={(values) => {
                onFinish && onFinish(values);
            }}
            initialValues={{}}
            scrollToFirstError
        >
            <FormItem label="场站上线" name="lineType" initialValue={'01'}>
                <Radio.Group>
                    <Radio value={'01'}>立即生效</Radio>
                    <Radio value={'02'}>定时生效</Radio>
                </Radio.Group>
            </FormItem>
            <FormItem
                shouldUpdate={(preData, afterData) => {
                    return preData.lineType !== afterData.lineType;
                }}
                noStyle
            >
                {({ getFieldValue }) => {
                    const lineType = getFieldValue('lineType');

                    // 支付宝券、第三方券追加，需先增加券，追加时进行选择追加

                    return lineType === '02' ? (
                        <FormItem
                            label="生效时间"
                            name="datatime"
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <DatePicker placeholder="请选择"></DatePicker>
                        </FormItem>
                    ) : undefined;
                }}
            </FormItem>

            <FormItem>
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button type="primary" htmlType="submit">
                        下一步
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </Space>
            </FormItem>
        </Form>
    );
};
const SecondStepForm = (props) => {
    const { onFinish, onClose, onBack } = props;
    const columns = [
        {
            title: '场站',
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '类型',
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: (
                <Space>
                    规则
                    <ReloadOutlined />
                </Space>
            ),
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    return (
        <div>
            <h3>请确认立即上线的场站的分润/购电结算规则配置</h3>
            <Alert
                message={<Space>未配置分润/购电结算规则的场站将自动忽略，不会上线</Space>}
                type="info"
                showIcon
            ></Alert>
            <br></br>

            <TablePro
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={[]}
                columns={columns}
                pagination={false}
                noSort
            />
            <br></br>
            <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                <Button type="primary" onClick={onFinish}>
                    提交
                </Button>
                {/* <Button type="primary" onClick={onBack}>
                    上一步
                </Button> */}
                <Button onClick={onClose}>取消</Button>
            </Space>
        </div>
    );
};
const LineStationModal = (props) => {
    const { visible, onChange, onClose, onFinish } = props;

    const [step, changeStep] = useState(STEP_TYPES.SECOND);
    const modalWidth = useMemo(() => {
        if (step === STEP_TYPES.SECOND) {
            return 1200;
        }
        return 500;
    }, [step]);

    const onFirstFinish = (values) => {
        changeStep(STEP_TYPES.SECOND);
    };
    const onSecondFinish = (values) => {
        onFinish && onFinish(values);
    };

    const modalView = useMemo(() => {
        if (step === STEP_TYPES.FIRST) {
            return <FirstStepForm onClose={onClose} onFinish={onFirstFinish}></FirstStepForm>;
        } else if (step === STEP_TYPES.SECOND) {
            return (
                <SecondStepForm
                    onClose={onClose}
                    onBack={() => {
                        changeStep(STEP_TYPES.FIRST);
                    }}
                    onFinish={onSecondFinish}
                ></SecondStepForm>
            );
        }
    }, [step]);

    return (
        <Modal
            title={'场站上线'}
            destroyOnClose
            width={modalWidth}
            visible={visible}
            onCancel={() => onClose()}
            footer={null}
            maskClosable={false}
        >
            {modalView}
        </Modal>
    );
};
export default LineStationModal;
