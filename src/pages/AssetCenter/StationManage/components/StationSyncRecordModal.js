import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, Input, Select, Space, Typography } from 'antd';
import { useImperativeHandle, useState, forwardRef } from 'react';
import TablePro from '@/components/TablePro';

import { getSyncStationRecordApi } from '@/services/MngAstApi';

import { usePagination } from 'ahooks';

const StationSyncRecordModal = (props, ref) => {
    const { onFinish } = props;

    const [visible, updateVisible] = useState(false);

    useImperativeHandle(ref, () => ({
        show: () => {
            updateVisible(true);
            searchData({ current: 1, pageSize: pagination?.pageSize });
        },
        onClose,
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const {
        run: searchData,
        loading: listLoading,
        data: recordList,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params = {}) => {
            const response = await getSyncStationRecordApi({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records || response?.data?.list || [],
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                };
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '同步人',
            width: 140,
            dataIndex: 'synOperatorName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '同步时间',
            width: 200,
            dataIndex: 'synDateTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '同步状态',
            dataIndex: 'synStatusName',
            render(text, record) {
                if (record.synStatus === '2') {
                    return <Typography.Text type="danger">{text || '-'}</Typography.Text>;
                }
                return <span title={text}>{text || '-'}</span>;
            },
        },
    ];

    return (
        <Modal
            title={
                <Space>
                    <span>同步记录</span>
                </Space>
            }
            destroyOnClose
            width={1000}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            footer={false}
        >
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.stationId}
                dataSource={recordList?.list}
                columns={columns}
                onChange={(pages) => {
                    pagination.onChange(pages?.current, pages?.pageSize);
                }}
                pagination={{
                    current: pagination.pageIndex,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                filterHeader={false}
            />
        </Modal>
    );
};
export default forwardRef(StationSyncRecordModal);
