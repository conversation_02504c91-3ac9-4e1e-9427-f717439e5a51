import { Form, Modal, Space, Typography, message } from 'antd';
import { Fragment, useImperativeHandle, useState, useRef, forwardRef } from 'react';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import StationSyncRecordModal from './StationSyncRecordModal';
import AreaCascader from './AreaCascader';

import { syncStationApi } from '@/services/MngAstApi';

const formItemLayout = {
    labelCol: {
        flex: '0 0 60px',
    },
    labelAlign: 'left',
};

const StationSyncModal = (props, ref) => {
    const { onFinish } = props;

    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);

    const recordRef = useRef();

    useImperativeHandle(ref, () => ({
        show: () => {
            updateVisible(true);
            form.resetFields();
        },
        onClose,
    }));

    const onClose = () => {
        updateWaiting(false);
        updateVisible(false);
    };

    const [isWaiting, updateWaiting] = useState(false);
    return (
        <Fragment>
            <Modal
                title={
                    <Space>
                        <span>同步场站</span>
                        <Typography.Link
                            onClick={() => {
                                recordRef?.current?.show();
                            }}
                        >
                            同步记录
                        </Typography.Link>
                    </Space>
                }
                destroyOnClose
                width={500}
                visible={visible}
                onCancel={onClose}
                maskClosable={false}
                okText="同步站点"
                onOk={() => {
                    form.validateFields().then(async (values) => {
                        updateWaiting(true);
                        try {
                            const params = {
                                ...values,
                                city: values?.city || '',
                            };
                            const result = await syncStationApi(params);

                            message.success('发起成功，可在同步记录中查看进度');

                            onFinish?.(result);
                        } catch (error) {
                            console.log(543543, error);
                        } finally {
                            updateWaiting(false);
                        }
                    });
                }}
                okButtonProps={{ loading: isWaiting }}
            >
                <Form {...formItemLayout} form={form} scrollToFirstError>
                    <OperSelectTypeItem
                        name="operatorId"
                        form={form}
                        rules={[{ required: true, message: '请选择' }]}
                    ></OperSelectTypeItem>

                    {/* <CitysSelect
                        label="城市"
                        name="city"
                        placeholder="请选择"
                        // formItemLayout={{ labelAlign: 'right' }}
                        showArrow
                        provinceSelectable
                        rules={[{ required: true, message: '请选择' }]}
                        isNewDataPermission
                        multiple={false}
                    /> */}

                    <AreaCascader
                        rules={[{ required: true, message: '请选择' }]}
                        form={form}
                        label={'城市'}
                        partEnabled
                        areaLevel="03"
                    />
                </Form>
            </Modal>
            <StationSyncRecordModal ref={recordRef}></StationSyncRecordModal>
        </Fragment>
    );
};
export default forwardRef(StationSyncModal);
