// 钉钉审批场站弹窗

import TablePro from '@/components/TablePro';
import { AuditOperTitle } from '@/constants/station';
import { Modal, Typography, message } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import {
    stationHidden,
    stationManageRecover,
    stationOffLine,
    stationShow,
    stationUpLine,
    sendStationPriceMq,
    validStationCustody,
} from '@/services/MngAstApi';

export const DingTalkAuditModal: React.FC<{ initRef: any }> = (props) => {
    const { initRef } = props;
    const [visible, updateVisible] = useState(false);

    const [dataSource, updateDateSource] = useState<{
        recordTabBos: [];
        stationNum: number;
        type: string;
        handle: any;
    }>();
    useImperativeHandle(initRef, () => ({
        show: (data: { recordTabBos: []; stationNum: number; type: string; handle: any }) => {
            updateDateSource(data);
            updateVisible(true);
        },
    }));

    const onClose = () => {
        updateVisible(false);
        updateDateSource(undefined);
    };
    // 仍要发起
    const stillInitiate = async () => {
        const { request, params } = dataSource?.handle;
        try {
            // noVerifyReSubmit	不校验的时候传1，强制审批
            await request({ ...params, noVerifyReSubmit: '1' });
            onClose();
            message.success('操作成功，请到审批记录或钉钉查看审批进度');
        } catch {
            message.error('操作失败');
        }
    };

    return (
        <Modal
            title={'操作成功，请到审批记录或钉钉查看审批进度'}
            visible={visible}
            onCancel={onClose}
            okText="仍要发起"
            cancelText="不发起"
            width={720}
            onOk={() =>
                Modal.confirm({
                    title: '仍要发起',
                    content: '同个场站有多个审批时，以首个审批通过的执行操作',
                    onOk() {
                        stillInitiate();
                    },
                })
            }
        >
            <div style={{ paddingBottom: '12px' }}>
                <Typography.Text strong style={{ color: '#f50' }}>
                    以下{dataSource?.stationNum}个{AuditOperTitle[dataSource?.type || '']}
                    操作正在审批，，请确认是否仍要发起审批:
                </Typography.Text>
            </div>

            <TablePro
                size="small"
                scroll={{ x: 'max-content' }}
                rowKey={'instanceId'}
                dataSource={dataSource?.recordTabBos}
                filterHeader={false}
                columns={[
                    {
                        title: '运营商',
                        width: 120,
                        dataIndex: 'operatorName',
                    },
                    {
                        title: '场站',
                        width: 180,
                        dataIndex: 'stationName',
                    },
                    {
                        title: '发起人',
                        width: 80,
                        dataIndex: 'promoterName',
                    },
                    {
                        title: '操作',
                        width: 80,
                        fixed: 'right',
                        render: (text: any, records: any) => {
                            return (
                                (records?.url?.length && (
                                    <a
                                        onClick={() => {
                                            window.location.href = `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(
                                                records?.url || '',
                                            )}&pc_slide=true`;
                                        }}
                                    >
                                        审批进度
                                    </a>
                                )) ||
                                null
                            );
                        },
                    },
                ]}
                pagination={false}
            />
        </Modal>
    );
};
