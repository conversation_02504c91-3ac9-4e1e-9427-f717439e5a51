import {
    Form,
    Button,
    Row,
    Col,
    Space,
    Input,
    Select,
    Upload,
    Tooltip,
    Popconfirm,
    message,
    notification,
} from 'antd';
import React, { Fragment, useState, useEffect, useRef, useContext, useMemo } from 'react';
import TablePro from '@/components/TablePro';
import { connect } from 'umi';
import {
    PlusOutlined,
    MinusCircleOutlined,
    UpCircleOutlined,
    DownCircleOutlined,
    PlusCircleOutlined,
    CloseCircleOutlined,
    InfoCircleOutlined,
    LoadingOutlined,
} from '@ant-design/icons';
import { BASE_URL } from '@/config/global';
import commonStyles from '@/assets/styles/common.less';

const iconSize = 24;

const FormItem = Form.Item;

const { TextArea } = Input;
const { Option } = Select;

const UploadButton = ({ uploading }) => (
    <div>{uploading ? <LoadingOutlined /> : <PlusOutlined />}</div>
);

const UploadImageLayout = (props) => {
    const { form, dataIndex: filePath, isSmall, sizeInfo = { size: 100 }, disabled } = props;

    const [fileList, updateFileList] = useState([]);
    const changeFileListAndForm = (list) => {
        updateFileList([...list]);
        const name = `${(Array.isArray(filePath) && filePath[filePath.length - 1]) || filePath}`;
        setFormValues({ [name]: list });
    };
    useEffect(() => {
        const file =
            form.getFieldValue(
                (Array.isArray(filePath) && [...filePath.slice(0, -1)]) || filePath,
            ) || [];
        const name = `${(Array.isArray(filePath) && filePath[filePath.length - 1]) || filePath}`;
        const tempList = [...(file?.[name] || [])];
        changeFileListAndForm(tempList);
        updateFileList([...tempList]);
        setFormValues({ [name]: tempList });
    }, [form, filePath]);

    const setFormValues = (values) => {
        if (Array.isArray(filePath)) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length - 1) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }
            if (!obj) {
                obj = superObj;
            }
            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue({ superName: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    const handleChange = ({ fileList: newFileList }) => {
        const list = newFileList?.map((ele) => {
            if (ele.status != 'uploading') {
                if (ele.url) {
                } else if (ele.response?.ret == 401) {
                    const logout = window?.loginStore?.logout;
                    logout?.();
                    notification.error({
                        message: '登录失效，请重新登录',
                    });
                } else if (!ele.response?.ret || ele.response?.ret != 200) {
                    ele.response = ele.response?.msg || '上传失败';
                    ele.status = 'error';
                } else if (ele.response?.data?.filePath) {
                    ele.url = ele.response?.data?.filePath;
                    ele.fileId = ele.response?.data?.fileId;
                }
            }
            return ele;
        });
        updateFileList([...list]);
        const name = `${(Array.isArray(filePath) && filePath[filePath.length - 1]) || filePath}`;
        setFormValues({ [name]: list });
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('图片格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 < sizeInfo.size;
            if (!isLt2M) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}mb`;
                } else {
                    sizeNum = `${sizeInfo.size}kb`;
                }
                message.error(`图片不大于 ${sizeNum}!`);
                rej();
                return;
            }

            if (sizeInfo.width || sizeInfo.height) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const data = e.target.result;
                    // 加载图片获取图片真实宽度和高度
                    const image = new Image();
                    image.onload = function () {
                        const { width } = image;
                        const { height } = image;
                        if (sizeInfo.width && sizeInfo.height) {
                            if (width != sizeInfo.width && height != sizeInfo.height) {
                                message.error(`图片尺寸必须为${sizeInfo.width}*${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.width) {
                            if (width != sizeInfo.width) {
                                message.error(`图片宽度必须为${sizeInfo.width}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.height) {
                            if (height != sizeInfo.height) {
                                message.error(`图片高度必须为${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        }
                        if (isJpgOrPng && isLt2M) {
                            res();
                        } else {
                            rej();
                        }
                    };
                    image.src = data;
                };
                reader.readAsDataURL(file);
            } else {
                res();
            }
        });

    const upLoadProps = {
        name: 'file',
        accept: '.jpg,.jpeg,.png',
        action: `${origin}${BASE_URL}/pub/upload`,
        data: {
            contentType: 'stationGuide',
            contRemrk: 'popIcon',
            relaTable: 'stationGuide',
            addWatermarkFlag: '1',
            businessType: 'stationGuideBk',
        },
        fileList: fileList,
        onChange: handleChange,
        listType: 'picture-card',
        beforeUpload,
        maxCount: 5,
    };

    return (
        <Upload
            className={(isSmall && commonStyles['upload-smaller']) || undefined}
            {...upLoadProps}
            disabled={disabled}
        >
            {fileList?.length >= 5 ? null : (!disabled && <UploadButton />) || null}
        </Upload>
    );
};

const RoadBookFormItem = (props) => {
    const {
        dispatch,
        global: { codeInfo = {} },
        name = 'guideList',
        form,
        label = '路书配置',
        rules,
        onChange,
        disabled,
        ...otherProps
    } = props;

    const { stationGuide } = codeInfo;
    useEffect(() => {
        if (!stationGuide) {
            dispatch({
                type: 'global/initCode',
                code: 'stationGuide',
            });
        }
    }, []);

    const [rightTableUpadateFlag, setRightTableUpadateFlag] = useState(false);
    const rightTableRefresh = () => {
        setRightTableUpadateFlag(!rightTableUpadateFlag);
    };
    const configMoved = (_, index, isUpper) => {
        const rightDatas = form.getFieldValue(name);
        const item = rightDatas[index];
        rightDatas?.splice?.(index, 1);
        if (isUpper) {
            // 向上
            rightDatas?.splice?.(index - 1, 0, item);
        } else {
            // 向下
            rightDatas?.splice?.(index + 1, 0, item);
        }
        form.setFieldsValue({ temp_config: rightDatas });
    };

    const configColumns = useMemo(
        () => [
            {
                title: '指引',
                width: 120,
                editable: true,
                render(text, record, index) {
                    return (
                        <Form.Item
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues?.[name]?.[index] !== prevValues?.[name]?.[index]
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const guideList = getFieldValue(name);
                                const titleType = guideList[index]['titleType'];
                                const otherCodes = [];
                                // guideList?.map((ele, otherIndex) => {
                                //     if (otherIndex !== index) {
                                //         otherCodes.push(ele.titleType);
                                //     }
                                // });

                                return (
                                    <div>
                                        <Form.Item name={[name, index, 'id']} noStyle />
                                        <Form.Item
                                            name={[name, index, 'titleType']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择指引类型',
                                                },
                                            ]}
                                        >
                                            <Select
                                                placeholder="请选择"
                                                onChange={rightTableRefresh}
                                                allowClear
                                                disabled={disabled}
                                            >
                                                {stationGuide?.map((ele) => (
                                                    <Option
                                                        key={ele.codeValue}
                                                        value={ele.codeValue}
                                                        disabled={
                                                            otherCodes.findIndex(
                                                                (code) => code == ele.codeValue,
                                                            ) > -1
                                                        }
                                                    >
                                                        {ele.codeName}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                        {(titleType == '06' && (
                                            <Form.Item
                                                style={{
                                                    margin: 0,
                                                }}
                                                name={[name, index, 'title']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请填写',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    placeholder="请填写"
                                                    disabled={disabled}
                                                ></Input>
                                            </Form.Item>
                                        )) ||
                                            null}
                                    </div>
                                );
                            }}
                        </Form.Item>
                    );
                },
            },
            {
                title: '描述',
                width: 160,
                dataIndex: 'guideDesc',
                editable: true,
                render(text, record, index) {
                    return (
                        <Form.Item
                            style={{
                                margin: 0,
                            }}
                            name={[name, index, 'guideDesc']}
                        >
                            <TextArea rows={3} disabled={disabled} maxLength={60} showCount />
                        </Form.Item>
                    );
                },
            },
            {
                title: (
                    <span>
                        图片
                        <Tooltip title="格式支持png、jpg、jpeg，大小不得超过800kb">
                            <InfoCircleOutlined
                                style={{
                                    color: '#0A89FF',
                                    marginLeft: '6px',
                                }}
                            />
                        </Tooltip>
                    </span>
                ),
                // align: 'center',
                width: 240,
                dataIndex: 'guidePictureList',
                render(text, record, index) {
                    return (
                        <FormItem
                            name={[name, index, 'guidePictureList']}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value?.length) {
                                            return Promise.reject('图片未上传');
                                        } else {
                                            let err = false;
                                            value.map((ele) => {
                                                if (!ele?.fileId?.length) {
                                                    err = true;
                                                }
                                            });
                                            if (err) {
                                                return Promise.reject('有图片上传失败');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            style={{ marginBottom: 0 }}
                        >
                            <UploadImageLayout
                                form={form}
                                dataIndex={[name, index, 'guidePictureList']}
                                isSmall
                                sizeInfo={{ size: 800 }}
                                disabled={disabled}
                            />
                        </FormItem>
                    );
                },
            },
            ...((disabled && []) || [
                {
                    title: '操作',
                    width: 120,
                    fixed: 'right',
                    render: (text, record, index) => {
                        const upperBtn = (
                            <UpCircleOutlined
                                style={{
                                    fontSize: `${iconSize}px`,
                                }}
                                onClick={() => configMoved(record, index, true)}
                            />
                        );
                        const downerBtn = (
                            <DownCircleOutlined
                                style={{
                                    fontSize: `${iconSize}px`,
                                }}
                                onClick={() => configMoved(record, index, false)}
                            />
                        );
                        const closeBtn = (
                            <Popconfirm
                                title="确定删除？"
                                okText="确定"
                                cancelText="取消"
                                onConfirm={() => {
                                    const rightDatas = form.getFieldValue(name);
                                    rightDatas?.splice?.(index, 1);
                                    form.setFieldsValue({ temp_config: rightDatas });
                                }}
                            >
                                <CloseCircleOutlined
                                    style={{
                                        fontSize: `${iconSize}px`,
                                    }}
                                />
                            </Popconfirm>
                        );
                        return (
                            <Space size="small">
                                {index != form.getFieldValue(name)?.length - 1 && downerBtn}
                                {index != 0 && upperBtn}
                                {closeBtn}
                            </Space>
                        );
                    },
                },
            ]),
        ],
        [rightTableUpadateFlag, stationGuide],
    );

    return (
        <Fragment>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => prevValues?.[name] !== curValues?.[name]}
                noStyle
            >
                {({ getFieldValue }) => {
                    let guideList = getFieldValue(name);
                    if (!guideList?.length) {
                        guideList = [];
                    }
                    return (
                        <FormItem label={label} name={name} {...otherProps}>
                            {(!disabled &&
                                ((guideList.length < 8 && (
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            let guideList = getFieldValue(name);
                                            if (!guideList?.length) {
                                                guideList = [];
                                            }
                                            guideList.push({});
                                            form.setFieldsValue({ [name]: guideList });
                                        }}
                                        disabled={guideList.length >= 8}
                                    >
                                        添加
                                    </Button>
                                )) || (
                                    <Tooltip title="最多添加8个路书">
                                        <Button disabled type="primary">
                                            添加
                                        </Button>
                                    </Tooltip>
                                ))) ||
                                null}
                            {(guideList?.length && (
                                <>
                                    {(!disabled && (
                                        <>
                                            <br />
                                            <br />
                                        </>
                                    )) ||
                                        null}
                                    <FormItem name={name} noStyle>
                                        <TablePro
                                            scroll={{ x: 'max-content' }}
                                            rowKey={(record, index) => record.id}
                                            dataSource={[...guideList]}
                                            columns={configColumns}
                                            pagination={false}
                                            noSort
                                        />
                                    </FormItem>
                                </>
                            )) ||
                                null}
                        </FormItem>
                    );
                }}
            </Form.Item>
        </Fragment>
    );
};

export default connect(({ global }) => ({
    global,
}))(RoadBookFormItem);
