import { Button, Col, Form, Modal, Space, Select, Switch, Tooltip, Input } from 'antd';
import { Fragment, useEffect, useMemo } from 'react';
import { connect } from 'umi';
const FormItem = Form.Item;
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
const { Option } = Select;
const { TextArea } = Input;

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const ConfirmDownModal = (props) => {
    const { dispatch, global, visible, onClose, onFinish } = props;

    const { codeInfo = {} } = global;

    const { stationDownReason = [] } = codeInfo;

    const [form] = Form.useForm();
    const onFinishEvent = (values) => {
        values.tempDownFlag = values.tempDownFlag === true ? '1' : '0';
        values.operateDesc = values?.operateDesc
            ? values.operateDesc
            : stationDownReason?.filter((v) => v?.codeValue === values?.operateReason)?.[0]
                  ?.codeName;
        onFinish && onFinish(values);
    };

    const closeEvent = () => {
        onClose && onClose();
    };

    useEffect(() => {
        if (visible) {
            if (stationDownReason?.length === 0) {
                dispatch({
                    type: 'global/initCode',
                    code: 'stationDownReason',
                });
            }
        }
        if (!visible) {
            form.resetFields();
        }
    }, [visible]);

    const stationDownReasonOptions = useMemo(() => {
        if (stationDownReason instanceof Array) {
            return stationDownReason?.map((ele) => {
                return (
                    <Option value={ele.codeValue} key={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [stationDownReason]);

    return (
        <Modal
            title={'场站下线'}
            destroyOnClose
            visible={visible}
            onCancel={closeEvent}
            footer={null}
            maskClosable={false}
        >
            <Space style={{ marginBottom: '20px' }}>
                <ExclamationCircleOutlined style={{ color: '#FEAD15', fontSize: '22px' }} />
                <span>确认下线对应站点</span>
            </Space>

            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinishEvent}
                initialValues={{}}
                scrollToFirstError
            >
                <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                    {({ getFieldValue }) => {
                        const tempDownFlag = getFieldValue('tempDownFlag');
                        const operateReason = getFieldValue('operateReason');

                        const remarkItem =
                            (operateReason == '7' && (
                                <FormItem
                                    label="具体原因"
                                    rules={[{ required: true, message: '请填写' }]}
                                    name={'operateDesc'}
                                >
                                    <TextArea
                                        placeholder="请填写"
                                        maxLength={200}
                                        rows={6}
                                        showCount
                                    ></TextArea>
                                </FormItem>
                            )) ||
                            null;
                        return (
                            (!tempDownFlag && (
                                <Fragment>
                                    <FormItem
                                        label="下线原因"
                                        name={'operateReason'}
                                        rules={[{ required: true, message: '请选择' }]}
                                        initialValue={'0'}
                                    >
                                        <Select showSearch allowClear>
                                            {stationDownReasonOptions}
                                        </Select>
                                    </FormItem>
                                    {remarkItem}
                                </Fragment>
                            )) ||
                            null
                        );
                    }}
                </FormItem>

                <Space>
                    <Form.Item
                        label={
                            <div>
                                临时下线
                                <Tooltip title="开启后，当场站达到指定条件将自动恢复上线">
                                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                </Tooltip>
                            </div>
                        }
                        required
                        name="tempDownFlag"
                        valuePropName="checked"
                        style={{ width: '160px' }}
                    >
                        <Switch
                            onChange={(newFlag) => {
                                if (newFlag) {
                                    form.setFieldsValue({
                                        operateReason: '2',
                                    });
                                }
                            }}
                        />
                    </Form.Item>

                    <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
                        {({ getFieldValue }) => {
                            const tempDownFlag = getFieldValue('tempDownFlag');
                            return (
                                (tempDownFlag == true && (
                                    <Fragment>
                                        <FormItem
                                            name={'triggerFlag'}
                                            style={{ marginLeft: '12px' }}
                                            initialValue={'0'}
                                        >
                                            <Select style={{ width: '220px' }} showSearch>
                                                <Option key={0}>{'枪离线'}</Option>
                                                <Option key={1}>{'查询价格失败'}</Option>
                                                <Option key={2}>{'设备认证失败'}</Option>
                                            </Select>
                                        </FormItem>
                                        <FormItem
                                            name="operateReason"
                                            noStyle
                                            initialValue={'2'}
                                        ></FormItem>
                                    </Fragment>
                                )) ||
                                null
                            );
                        }}
                    </FormItem>
                </Space>

                <FormItem>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default connect(({ global }) => ({
    global,
}))(ConfirmDownModal);
