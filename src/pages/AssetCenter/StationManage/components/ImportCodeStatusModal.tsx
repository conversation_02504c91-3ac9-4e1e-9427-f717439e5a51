import { Button, Result, Form, Modal, Space, Upload, Radio, notification } from 'antd';
import { Fragment, useState } from 'react';
import { useMemo } from 'react';
import { MNG_AST_URL } from '@/config/global';

import { UploadOutlined } from '@ant-design/icons';
import {
    stationDownloadCodeStatusTemplateApi,
    stationDownloadCodeStatusFailRecordApi,
} from '@/services/AssetCenter/StationManangeApi';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const MODAL_VIEW_TYPES = {
    IMPORT: '01',
    RESULT: '02',
};

const UploadList = (props) => {
    const {
        accept = '.xlsx,.xls',
        api = '/station/manage/codeStatus/import',
        tip,
        children,
        uploadCallback,
    } = props;
    const [fileList, updateFileList] = useState([]);

    const handleChange = ({ fileList: newFileList }) => {
        const list = newFileList?.map((ele) => {
            if (ele.response) {
                uploadCallback?.(ele.response);
            }
            return ele;
        });
        updateFileList([...list]);
    };

    const upLoadProps = {
        name: 'file',
        accept,
        action: `${origin}${MNG_AST_URL}${api}`,
        fileList: fileList,
        onChange: handleChange,
        multiple: true,
    };

    return (
        <Upload {...upLoadProps}>
            <Space direction="vertical">
                <Button icon={<UploadOutlined />}>上传文件</Button>
                {(tip && <span style={{ color: 'gray' }}>{tip}</span>) || null}
                {children}
            </Space>
        </Upload>
    );
};

const ImportCodeStatusModal = (props) => {
    const { visible, onClose, onFinish } = props;

    const [form] = Form.useForm();
    const [modalType, changeModalType] = useState(MODAL_VIEW_TYPES.IMPORT);

    const [uploadResult, changeUploadResult] = useState(undefined); // 上传结果

    const closeEvent = () => {
        onClose && onClose();
        form.resetFields();
        changeModalType(MODAL_VIEW_TYPES.IMPORT);
        changeUploadResult(undefined);
    };

    const downErrorFileEvent = async () => {
        try {
            await stationDownloadCodeStatusFailRecordApi({ serialNo: uploadResult?.serialNo });
        } catch (error) {}
    };

    const modalTitle = useMemo(() => {
        let title = '批量导入贴码状态';
        if (modalType === MODAL_VIEW_TYPES.RESULT) {
            title = '导入情况';
        }
        return title;
    }, [modalType]);

    const modalView = useMemo(() => {
        if (modalType === MODAL_VIEW_TYPES.IMPORT) {
            return (
                <Form {...formItemLayout} form={form} initialValues={{}} scrollToFirstError>
                    <FormItem label={<strong>第一步</strong>}>
                        <Space>
                            下载导入模板，根据模板要求填写对应信息
                            <Button
                                type="primary"
                                onClick={async () => {
                                    try {
                                        await stationDownloadCodeStatusTemplateApi();
                                    } catch (error) {}
                                }}
                            >
                                下载导入模板
                            </Button>
                        </Space>
                    </FormItem>

                    <FormItem label={<strong>第二步</strong>}>
                        <Space align="start">
                            <div style={{ marginTop: '6px' }}>上传需要导入的文件</div>
                            <UploadList
                                tip="支持扩展名：xls,xlsx"
                                uploadCallback={(res = {}) => {
                                    if (res.ret == 401) {
                                        const logout = window?.loginStore?.logout;
                                        logout?.();
                                        notification.error({
                                            message: '登录失效，请重新登录',
                                        });
                                    } else {
                                        const { data } = res;
                                        changeModalType(MODAL_VIEW_TYPES.RESULT);
                                        changeUploadResult({ ...data });
                                        onFinish?.();
                                    }
                                }}
                            />
                        </Space>
                    </FormItem>
                </Form>
            );
        } else if (modalType === MODAL_VIEW_TYPES.RESULT) {
            return (
                <Fragment>
                    <Result
                        status={uploadResult?.failNum > 0 ? 'error' : 'success'}
                        subTitle={
                            <div style={{ textAlign: 'center', fontSize: '18px', color: 'black' }}>
                                <Space direction="vertical">
                                    <Space size={'large'}>
                                        <Space size={'small'}>
                                            <span>成功：</span>
                                            <span>{uploadResult?.successNum || '0'}</span>
                                            <span>条</span>
                                        </Space>
                                        <Space size={'small'}>
                                            <span>失败：</span>
                                            <span
                                                style={{
                                                    color:
                                                        uploadResult?.failNum > 0 ? 'red' : 'black',
                                                }}
                                            >
                                                {uploadResult?.failNum || '0'}
                                            </span>
                                            <span>条</span>
                                        </Space>
                                    </Space>
                                    {(uploadResult?.failNum > 0 && (
                                        <Button
                                            onClick={downErrorFileEvent}
                                            style={{ marginTop: '12px' }}
                                        >
                                            导出失败数据
                                        </Button>
                                    )) ||
                                        null}
                                </Space>
                            </div>
                        }
                    />
                </Fragment>
            );
        }
    }, [modalType, uploadResult]);

    return (
        <Modal
            title={modalTitle}
            destroyOnClose
            width={600}
            visible={visible}
            onCancel={closeEvent}
            footer={null}
            maskClosable={false}
        >
            {modalView}
        </Modal>
    );
};
export default ImportCodeStatusModal;
