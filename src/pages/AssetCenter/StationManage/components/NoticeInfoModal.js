import {
    DatePicker,
    Input,
    Form,
    Modal,
    Space,
    Radio,
    Row,
    Col,
    Checkbox,
    Button,
    Popconfirm,
    message,
    Select,
} from 'antd';
import { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;
import moment from 'moment';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import {
    PropertiesItem,
    PROPERTISE_TYPES,
} from '../../TagManage/components/StationTagPropertiesFormItem';
import {
    getStationNoticeListApi,
    saveStationNoticeTemplateApi,
    deleteStationNoticeTemplateApi,
    getStationNoticeTypeListApi,
} from '@/services/AssetCenter/StationManangeApi';
import { isEmpty } from '@/utils/utils';

// 公告信息
const NoticeInfoModal = (props) => {
    const { initRef, onFinish } = props;

    const [form] = Form.useForm();
    const [visible, changeVisible] = useState(false);
    const [isWaiting, updateWaiting] = useState(false);

    const onClose = () => {
        changeVisible(false);
    };

    useImperativeHandle(initRef, () => ({
        show: () => {
            form.resetFields();
            changeVisible(true);
        },
        onClose,
    }));

    return (
        <Modal
            visible={visible}
            title="公告设置"
            onOk={() => {
                form.validateFields().then((values) => {
                    const notices = noticeInfoFormatter(values.noticeList);
                    onFinish?.({ notices });
                });
            }}
            okText="提交"
            okButtonProps={{ loading: isWaiting }}
            onCancel={onClose}
            width={720}
        >
            <Form form={form}>
                <NoticeInfoLayout form={form} />
            </Form>
        </Modal>
    );
};

export const noticeInfoFormatter = (values) => {
    const res =
        values
            ?.map((ele) => ({
                ...ele,
                effTime:
                    (ele.always == '0' && ele.dateRange?.[0]?.format('YYYY-MM-DD HH:mm:ss')) ||
                    undefined,
                expTime:
                    (ele.always == '0' && ele.dateRange?.[1]?.format('YYYY-MM-DD HH:mm:ss')) ||
                    undefined,
                dateRange: undefined,
                // 只要勾选了使用模板，就不用手动输入的公告内容，如果勾选后没有选具体模板，传空就行
                noticeInfo: ele.useTemplateFlag ? ele.useTemplateNoticeInfo : ele.noticeInfo,
                templateId: ele.useTemplateFlag ? ele.templateId : undefined,
                useTemplateNoticeInfo: undefined,
                useTemplateFlag: undefined,
            }))
            ?.filter((v) => !isEmpty(v)) || [];
    return JSON.stringify(res);
};

export const NoticeInfoLayout = (props) => {
    const {
        form, // 主要用于模板选择后页面刷新
        name = 'noticeList',
        disabled,
        formItemLayout,
    } = props;
    const chooseRef = useRef();
    return (
        <Fragment>
            <Form.List
                name={name}
                initialValue={[{}, {}]}
                rules={[
                    {
                        validator(_, lists) {
                            for (let index = 0; index < lists.length; index++) {
                                const value = lists[index];
                                const noticeName = `公告信息${index + 1}`;
                                if (!isEmpty(value)) {
                                    if (
                                        value.noticeInfo?.trim() &&
                                        (!value.always || (value.always == '0' && !value.dateRange))
                                    ) {
                                        return Promise.reject(`请选择${noticeName}的生效时间`);
                                    }
                                }
                            }

                            return Promise.resolve();
                        },
                    },
                ]}
            >
                {(fields, { add, remove }, { errors }) => (
                    <Fragment>
                        {fields.map((field, index) => (
                            <Form.Item
                                label={`公告信息${index + 1}`}
                                key={index}
                                {...formItemLayout}
                            >
                                <Form.Item name={[[index], 'templateId']} noStyle />
                                <Form.Item
                                    label="生效时间"
                                    name={[[index], 'always']}
                                    initialValue={'1'}
                                >
                                    <Radio.Group disabled={disabled} style={{ width: '100%' }}>
                                        <Radio value={'1'}>永久有效</Radio>
                                        <Radio value={'0'}>
                                            <Space>
                                                指定时间
                                                <Form.Item shouldUpdate noStyle>
                                                    {({ getFieldValue }) => {
                                                        const always = getFieldValue([
                                                            name,
                                                            index,
                                                            'always',
                                                        ]);
                                                        const useTemplateFlag = getFieldValue([
                                                            name,
                                                            index,
                                                            'useTemplateFlag',
                                                        ]);
                                                        return (
                                                            (always == '0' && (
                                                                <Form.Item
                                                                    name={[[index], 'dateRange']}
                                                                    noStyle
                                                                    rules={[
                                                                        ({ getFieldValue }) => ({
                                                                            validator(rule, value) {
                                                                                if (!value?.[0]) {
                                                                                    return Promise.reject(
                                                                                        '请选择具体指定时间',
                                                                                    );
                                                                                }
                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <RangePicker
                                                                        showTime={{
                                                                            format: 'HH:mm:ss',
                                                                            defaultValue: moment(
                                                                                '00:00:00',
                                                                                'HH:mm:ss',
                                                                            ),
                                                                        }}
                                                                        format="YYYY-MM-DD HH:mm:ss"
                                                                        disabled={disabled}
                                                                    />
                                                                </Form.Item>
                                                            )) ||
                                                            null
                                                        );
                                                    }}
                                                </Form.Item>
                                            </Space>
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                                <Form.Item shouldUpdate noStyle>
                                    {({ getFieldValue }) => {
                                        const useTemplateObj = getFieldValue([name, index]);
                                        const useTemplateFlag = useTemplateObj?.useTemplateFlag;
                                        const useTemplateNoticeInfo =
                                            useTemplateObj?.useTemplateNoticeInfo;
                                        return (
                                            <Form.Item label={'公告内容'}>
                                                <Space
                                                    direction="vertical"
                                                    style={{ width: '100%' }}
                                                >
                                                    <Form.Item noStyle>
                                                        <Space style={{ lineHeight: '32px' }}>
                                                            <Form.Item
                                                                name={[[index], 'useTemplateFlag']}
                                                                valuePropName="checked"
                                                                noStyle
                                                            >
                                                                <Checkbox checked={useTemplateFlag}>
                                                                    使用模板
                                                                </Checkbox>
                                                            </Form.Item>
                                                            {useTemplateFlag && (
                                                                <Button
                                                                    type="primary"
                                                                    onClick={() => {
                                                                        chooseRef.current.show({
                                                                            index,
                                                                        });
                                                                    }}
                                                                >
                                                                    选择
                                                                </Button>
                                                            )}
                                                        </Space>
                                                    </Form.Item>
                                                    {(useTemplateFlag && (
                                                        <Form.Item
                                                            name={[
                                                                [index],
                                                                'useTemplateNoticeInfo',
                                                            ]}
                                                            noStyle
                                                        >
                                                            <div style={{ wordBreak: 'break-all' }}>
                                                                {useTemplateNoticeInfo}
                                                            </div>
                                                        </Form.Item>
                                                    )) || (
                                                        <Form.Item
                                                            name={[[index], 'noticeInfo']}
                                                            noStyle
                                                        >
                                                            <TextArea
                                                                rows={3}
                                                                placeholder="请填写"
                                                                disabled={disabled}
                                                                maxLength={255}
                                                                showCount
                                                                style={{ width: '420px' }}
                                                            />
                                                        </Form.Item>
                                                    )}
                                                </Space>
                                            </Form.Item>
                                        );
                                    }}
                                </Form.Item>
                            </Form.Item>
                        ))}
                        <Form.ErrorList errors={errors} />
                    </Fragment>
                )}
            </Form.List>

            <ModuleChooseModal
                initRef={chooseRef}
                onChooseFinish={(values, index) => {
                    if (form) {
                        const allValues = form?.getFieldsValue();
                        const targetItem = allValues?.[name]?.[index];
                        targetItem.useTemplateNoticeInfo = values.noticeName;
                        targetItem.templateId = values.id;
                        form.setFieldsValue(allValues);
                    }
                }}
                onEditFinish={(values) => {
                    // 如果编辑的是已选的，要回调刷新页面
                    if (form && values?.id) {
                        const allValues = form?.getFieldsValue();
                        const targetItem = allValues?.[name]?.find(
                            (ele) => ele.templateId == values.id,
                        );
                        if (targetItem) {
                            // 如果之前有选中，编辑的又是选中的，要更新
                            targetItem.useTemplateNoticeInfo = values.noticeName;
                            form.setFieldsValue(allValues);
                        }
                    }
                }}
            />
        </Fragment>
    );
};

const AddNoticeModal = (props) => {
    const { initRef, onEditFinish, onAddFinish } = props;

    const [moduleForm] = Form.useForm();
    const [visible, changeVisible] = useState(false);
    const [editInfo, updateEditInfo] = useState();
    const [isWaiting, updateWaiting] = useState(false);
    useImperativeHandle(initRef, () => ({
        show: (info) => {
            updateEditInfo(info);
            moduleForm.resetFields();
            moduleForm.setFieldsValue(info);
            changeVisible(true);
        },
    }));

    const onClose = () => {
        changeVisible(false);
    };

    return (
        <Modal
            title={editInfo ? '编辑公告' : '新建公告'}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            width={420}
            onOk={() => {
                moduleForm.validateFields().then(async (data) => {
                    updateWaiting(true);
                    try {
                        await saveStationNoticeTemplateApi({
                            ...data,
                            id: editInfo?.id || undefined,
                        });
                        if (editInfo) {
                            onEditFinish?.({ ...editInfo, ...data });
                        } else {
                            onAddFinish();
                        }
                        onClose();
                    } catch (error) {
                    } finally {
                        updateWaiting(false);
                    }
                });
            }}
            okButtonProps={{ loading: isWaiting }}
            destroyOnClose
        >
            <Form
                form={moduleForm}
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
                labelCol={{
                    flex: '0 0 80px',
                }}
                labelAlign={'right'}
            >
                <Form.Item
                    name="noticeName"
                    label="公告内容"
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请输入',
                        },
                    ]}
                >
                    <TextArea placeholder="请填写" maxLength={255} showCount />
                </Form.Item>

                <Form.Item
                    label="公告类型"
                    name="noticeType"
                    rules={[
                        {
                            required: true,
                            whitespace: true,
                            message: '请输入',
                        },
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (value?.length > 6) {
                                    return Promise.reject('最多6个字');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <PropertiesItem {...props} type={PROPERTISE_TYPES.STATION_NOTICE} />
                </Form.Item>
            </Form>
        </Modal>
    );
};

const ModuleChooseModal = (props) => {
    const {
        initRef,
        onChooseFinish,
        onEditFinish, // 如果编辑的是已选的，要回调刷新页面
    } = props;

    const addRef = useRef();
    const [moduleForm] = Form.useForm();
    const [visible, changeVisible] = useState(false);
    const [editIndex, updateEditIndex] = useState();
    const [pageInfo, changePageInfo, onTableChange] = usePageState();
    const [typeList, updateTypeList] = useState([]);
    useImperativeHandle(initRef, () => ({
        show: ({ index } = {}) => {
            resetData();
            changeVisible(true);
            updateEditIndex(index);
            loadTypeList();
        },
    }));

    useEffect(() => {
        if (visible) {
            searchData();
        }
    }, [pageInfo, visible]);

    const loadTypeList = async () => {
        if (typeList?.length) {
            return;
        }

        const {
            data: { list },
        } = await getStationNoticeTypeListApi();
        if (list instanceof Array) {
            updateTypeList(list);
        }
    };

    const resetData = () => {
        moduleForm.resetFields();
        changePageInfo({ pageIndex: 1 });
    };
    const [listLoading, updateListLoading] = useState(false);
    const [dataSource, updateDateSource] = useState();
    const searchData = () => {
        moduleForm.validateFields().then(async (values) => {
            updateListLoading(true);
            try {
                const params = {
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    ...values,
                };
                const { data } = await getStationNoticeListApi(params);
                updateDateSource(data);
            } catch (error) {
            } finally {
                updateListLoading(false);
            }
        });
    };

    const onClose = () => {
        changeVisible(false);
        updateDateSource(undefined);
    };

    return (
        <Modal
            title={'选择模板'}
            visible={visible}
            onCancel={onClose}
            width={720}
            footer={null}
            destroyOnClose
        >
            <Form form={moduleForm} onFinish={searchData}>
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={() => {
                        moduleForm.resetFields();
                        resetData();
                    }}
                >
                    <Col span={8}>
                        <Form.Item label="公告内容" name="noticeName">
                            <Input placeholder="请填写" autoComplete="off" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="公告类型" name="parentId">
                            <Select placeholder="请选择" allowClear>
                                {typeList?.map((ele, index) => (
                                    <Option value={ele.id} key={index}>
                                        {ele.noticeType}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <Button
                type="primary"
                onClick={() => {
                    addRef.current.show();
                }}
                style={{ marginBottom: '6px' }}
            >
                新建
            </Button>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.id}
                dataSource={dataSource?.list}
                columns={[
                    {
                        title: '公告内容',
                        width: 240,
                        dataIndex: 'noticeName',
                        render(text, record) {
                            return (
                                <div className="text-line" style={{ width: '240px' }} title={text}>
                                    {text || '-'}
                                </div>
                            );
                        },
                    },
                    {
                        title: '公告类型',
                        width: 120,
                        dataIndex: 'noticeType',
                        render(text, record) {
                            return <span title={text}>{text || '-'}</span>;
                        },
                    },
                    {
                        title: '操作',
                        width: 140,
                        fixed: 'right',
                        render: (text, record) => {
                            let btns = [];

                            const editBtn = (
                                <a
                                    onClick={() => {
                                        addRef.current.show(record);
                                    }}
                                >
                                    编辑
                                </a>
                            );
                            const deleteBtn = (
                                <Popconfirm
                                    title={'删除后，使用该模板的场站公告将自动失效'}
                                    onConfirm={async () => {
                                        try {
                                            await deleteStationNoticeTemplateApi({ id: record.id });
                                            message.success('操作成功');
                                            if (dataSource?.list?.length == 1) {
                                                // 删掉的是最后一项，重置查询到第一页
                                                resetData();
                                            } else {
                                                // 当前页直接查询
                                                searchData();
                                            }
                                        } catch (error) {}
                                    }}
                                >
                                    <a>删除</a>
                                </Popconfirm>
                            );
                            const chooseBtn = (
                                <a
                                    onClick={() => {
                                        onChooseFinish?.(record, editIndex);
                                        onClose();
                                    }}
                                >
                                    选择
                                </a>
                            );

                            btns.push(chooseBtn, editBtn, deleteBtn);

                            return (
                                <Row gutter={8}>
                                    {btns.map((ele, index) => (
                                        <Col key={index}>{ele}</Col>
                                    ))}
                                </Row>
                            );
                        },
                    },
                ]}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: dataSource?.total,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                filterHeader={false}
            />

            <AddNoticeModal
                initRef={addRef}
                onEditFinish={(value) => {
                    onEditFinish?.(value);
                    searchData();
                }}
                onAddFinish={resetData}
            />
        </Modal>
    );
};

export default NoticeInfoModal;
