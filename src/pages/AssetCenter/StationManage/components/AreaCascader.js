import React, { Fragment, useEffect, useState } from 'react';
import { getAllProvinceCityApi, getCountryListApi } from '@/services/CommonApi';
import { Form, Cascader } from 'antd';

// 区域级联选择
const AreaCascader = (props) => {
    const {
        form,
        provinceKey = 'province',
        cityKey = 'city',
        areaKey = 'country',
        label = '',
        disabled,
        rules = [],
        formItemLayout = {},
        initialProvince,
        initialCity,
        initialArea,
        partEnabled, // 是否支持选中一个就反显一个
        areaLevel, // 选到对应层级,目前只支持到市级
        getAnywhereValue, // 如果=true，数据缺省情况下，点击最末一级行政单位，会直接回调，不需要配满3级才回调
    } = props;

    const [cityTreeData, changeCityTreeData] = useState([]);
    useEffect(() => {
        if (cityTreeData.length == 0) {
            initCityTree();
        }
    }, []);

    const cascaderKey = 'area_key';
    const [addressOption, changeAddressOption] = useState([]); // 地址级联框数据

    const initCityTree = async () => {
        try {
            const { data: areaList } = await getAllProvinceCityApi({});
            areaList.map((ele) =>
                ele.childs?.map((cityEle) => (cityEle.isLeaf = areaLevel === '03' ? true : false)),
            );
            changeCityTreeData(areaList);

            if (!initialProvince?.length && !initialCity?.length) {
                // 初始化级联框的省数据
                changeAddressOption(areaList);
            }
        } catch (error) {}
    };

    useEffect(() => {
        initAddressData();
    }, [initialCity, initialArea, cityTreeData]);

    useEffect(() => {
        if (initialProvince && initialCity && form) {
            if (areaLevel === '03') {
                form.setFieldsValue({
                    [provinceKey]: initialProvince,
                    [cityKey]: initialCity,
                    [cascaderKey]: [initialProvince, initialCity],
                });
            } else {
                form.setFieldsValue({
                    [provinceKey]: initialProvince,
                    [cityKey]: initialCity,
                    [areaKey]: initialArea,
                    [cascaderKey]: [initialProvince, initialCity, initialArea],
                });
            }
        }
    }, [initialProvince, initialCity, initialArea, provinceKey, cityKey, areaKey, form]);

    const initAddressData = async () => {
        if (initialProvince && initialCity && cityTreeData?.length) {
            // 初始化级联框的省数据
            const provinceData = cityTreeData;
            // 经营地址级联框的数据
            // 市
            const cityData = provinceData.find((val) => initialProvince == val.areaCode);
            // 县
            if (areaLevel !== '03') {
                const {
                    data: { areaList: countyList },
                } = await getCountryListApi({ upCode: initialCity });
                const areaData = cityData?.childs?.find((val) => val.areaCode == initialCity);
                if (areaData) {
                    areaData.childs = countyList;
                }
            }

            if (provinceData.length != 0) {
                changeAddressOption([...provinceData]);
            }
        }
    };

    // 级联框懒加载数据
    const loadAddressData = async (selectedOptions) => {
        // if (areaLevel === '03') return;
        const targetOption = selectedOptions[selectedOptions.length - 1];
        if (!targetOption.childs) {
            targetOption.loading = true;

            const {
                data: { areaList },
            } = await getCountryListApi({
                upCode: targetOption.areaCode,
            });
            if (getAnywhereValue && !areaList?.length) {
                let province;
                let city;
                let area;
                selectedOptions.map((ele, index) => {
                    switch (index) {
                        case 0:
                            province = ele.areaCode;
                            break;
                        case 1:
                            city = ele.areaCode;
                            break;
                        case 2:
                            area = ele.areaCode;
                            break;

                        default:
                            break;
                    }
                });
                targetOption.loading = false;
                targetOption.isLeaf = true;
                changeAddressOption([...addressOption]);
                form.setFieldsValue({
                    [provinceKey]: province,
                    [cityKey]: city,
                    [areaKey]: area,
                    [cascaderKey]: selectedOptions.map((ele) => ele.areaCode),
                });
                return;
            }
            targetOption.loading = false;
            const children = areaList;
            const isLeaf = selectedOptions.length == 2;
            const dealData = children.map((item, index) => ({
                ...item,
                isLeaf,
            }));
            targetOption.childs = dealData;

            changeAddressOption([...addressOption]);
        }
    };

    return (
        <Fragment>
            <Form.Item name={cascaderKey} label={label} rules={rules} {...formItemLayout}>
                <Cascader
                    showSearch
                    placeholder={`请选择`}
                    options={addressOption}
                    loadData={loadAddressData}
                    disabled={disabled}
                    onChange={(e) => {
                        const [province, city, area] = e || [];
                        form.setFieldsValue({
                            [provinceKey]: province,
                            [cityKey]: city,
                            [areaKey]: area,
                        });
                    }}
                    changeOnSelect={partEnabled}
                    fieldNames={{ label: 'areaName', value: 'areaCode', children: 'childs' }}
                />
            </Form.Item>
            <Form.Item name={provinceKey} noStyle />
            <Form.Item name={cityKey} noStyle />
            <Form.Item name={areaKey} noStyle />
        </Fragment>
    );
};

export default AreaCascader;
