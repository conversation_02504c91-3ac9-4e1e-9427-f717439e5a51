import { Button, Col, Form, Modal, Space, Select } from 'antd';
import { useEffect } from 'react';
import StationTagSelectFormItem from './StationTagSelectFormItem';
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const TagSetModal = (props) => {
    const { visible, onClose, onFinish } = props;

    const [form] = Form.useForm();
    const onFinishEvent = (values) => {
        onFinish && onFinish(values);
    };

    const closeEvent = () => {
        onClose && onClose();
    };

    useEffect(() => {
        if (!visible) {
            form.resetFields();
        }
    }, [visible]);

    return (
        <Modal
            title={'标签设置'}
            destroyOnClose
            width={600}
            visible={visible}
            onCancel={closeEvent}
            footer={null}
            maskClosable={false}
        >
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinishEvent}
                initialValues={{}}
                scrollToFirstError
            >
                <StationTagSelectFormItem form={form}></StationTagSelectFormItem>

                <FormItem>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default TagSetModal;
