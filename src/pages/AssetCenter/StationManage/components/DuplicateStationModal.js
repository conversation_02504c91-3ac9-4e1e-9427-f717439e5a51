import { ExclamationCircleOutlined, CheckCircleFilled } from '@ant-design/icons';
import { Button, Form, Select, Modal, Space, Tag, Steps, message, Typography } from 'antd';
import { history, connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';

import commonStyles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import ConfirmModal from '../../TimerTaskManage/ConfirmModal';
import {
    getDucplicateStationListApi,
    handleDucplicateStationListApi,
} from '@/services/AssetCenter/StationDucplicateApi';
import { getStationManageOptionsApi } from '@/services/AssetCenter/StationManangeApi';
import { stationUpLine } from '@/services/MngAstApi';
import { isEmpty } from '@/utils/utils';
import { DingTalkAuditModal } from './DingTalkAuditModal';
import { AUDIT_OPER_TYPE } from '@/constants/station';

const { confirm, success } = Modal;
const { Option } = Select;

// 数据列表
export const DuplicateTableColumns = [
    {
        title: '创建时间',
        width: 210,
        dataIndex: 'createTime',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '最后互通时间',
        width: 200,
        dataIndex: 'newUpdateTime',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '运营商',
        width: 140,
        dataIndex: 'buildName',
    },
    {
        title: '场站名称',
        width: 180,
        dataIndex: 'stationName',
        render(text, record) {
            return (
                <span title={text}>
                    {(record?.stationId && (
                        <Link
                            to={`/assetCenter/stationManage/list/detail/${record?.stationId}`}
                            key="detail"
                            target={'_blank'}
                        >
                            {text}
                        </Link>
                    )) ||
                        text ||
                        '-'}
                    {(record.currentStation && (
                        <Tag color="error" style={{ marginLeft: '6px' }}>
                            当前场站
                        </Tag>
                    )) ||
                        null}
                </span>
            );
        },
    },
    {
        title: '场站编号',
        width: 140,
        dataIndex: 'stationNo',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '是否上线',
        width: 120,
        dataIndex: 'openFlagName',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '运营状态',
        width: 120,
        dataIndex: 'busiStatusName',
        render(text, record) {
            return <span title={text}> {text || '-'}</span>;
        },
    },
    {
        title: '所在地区',
        width: 140,
        dataIndex: 'area',
        render(text, record) {
            return <span title={text}>{text || '-'}</span>;
        },
    },
    {
        title: '地址',
        width: 200,
        dataIndex: 'stationAddr',
        render(text, record) {
            return (
                <div className="text-third" style={{ width: '200px' }} title={text}>
                    {text || '-'}
                </div>
            );
        },
    },
    {
        title: '交/直流桩数',
        width: 160,
        dataIndex: 'pileNum',
        render(text, record) {
            if (record.acEquipNum || record.dcEquipNum) {
                const keys = Object.keys(record);
                const values = keys.map((ele) => record[ele] && `${ele}=${record[ele]}`);
                const params = values.filter((ele) => ele?.length > 0);
                return (
                    <Link
                        to={`/assetCenter/pileManage/list?${params?.join?.('&')}`}
                        key="detail"
                        target={'_blank'}
                    >
                        {`${record.acEquipNum === undefined ? '-' : record.acEquipNum} / ${
                            record.dcEquipNum === undefined ? '-' : record.dcEquipNum
                        }`}
                    </Link>
                );
            }
            return <span>-</span>;
        },
    },
    {
        title: '充电枪数',
        width: 140,
        dataIndex: 'gunNum',
        render(text, record) {
            return <span title={text}> {text || '-'}</span>;
        },
    },
    {
        title: '运营方式',
        width: 140,
        dataIndex: 'operationWayName',
        render(text, record) {
            return <span title={text}> {text || '-'}</span>;
        },
    },
];

// 数据分组
export const DuplicateSeparteFunction = (data = []) => {
    // 先把当前场站挪到最前面
    const currentStationIndex = data.findIndex((stationEle) => stationEle.currentStation);
    if (currentStationIndex >= 0) {
        const cur = data[currentStationIndex];
        data.splice(currentStationIndex, 1);
        data.unshift(cur);
    }
    /* 数据分组逻辑：
    1、currentStation当前场站、折叠场站不加移除按钮
    2、根据stationOneId来实现分组，默认展示第一条，点开+展示分组场站
    */
    const dicList = []; // 先以stationOneId来实现分组，最后再抽离所有的value拼成一个展示的list，格式：[{stationOneId, list}]
    for (const item of data) {
        const stationOneId = item.stationOneId;
        const containedList =
            stationOneId && dicList.find((ele) => ele.stationOneId == stationOneId)?.list;
        if (containedList) {
            containedList.push(item);
        } else {
            dicList.push({ stationOneId, list: [item] });
        }
    }
    const list = dicList.map((ele) => {
        const oneList = ele.list;
        const res = {
            ...oneList.shift(),
            isParent: true,
            children: (oneList?.length && oneList) || undefined,
        };
        return res;
    });
    return list;
};

export const DuplicateSuccessModal = (props) => {
    const { onFinish, lineEvent, initRef } = props;

    const [visible, updateVisible] = useState(false);
    const [isAllPass, updateIsAllPass] = useState(true);
    useImperativeHandle(initRef, () => ({
        show: (pass) => {
            // 传入重复场站的站点id
            updateVisible(true);
            updateIsAllPass(pass);
        },
        onClose,
    }));

    const onClose = () => {
        updateVisible(false);
        updateIsAllPass(undefined);
    };

    return (
        <Modal
            title={
                <Space>
                    <CheckCircleFilled style={{ color: '#52c41a', fontSize: '22px' }} />
                    操作成功
                </Space>
            }
            onOk={() => {
                lineEvent?.();
                onClose();
                // settingEvent();
            }}
            onCancel={onClose}
            okText={'立即上线'}
            okButtonProps={isAllPass ? undefined : { style: { display: 'none' } }}
            cancelText={isAllPass ? '暂不上线' : '完成'}
            visible={visible}
            width={320}
        >
            {isAllPass ? '在用站未上线，是否立即上线' : '在用站存在未通过校验，暂无法上线'}
        </Modal>
    );
};

const DuplicateStationModal = (props) => {
    const { onFinish, initRef } = props;
    const auditModal = useRef();
    const [visible, updateVisible] = useState(false);
    const [stationId, updateStationId] = useState();
    const [listLoading, updateListLoading] = useState(false);
    const [confirmIndex, updateConfirmIndex] = useState();
    const [currentStep, updateCurrentStep] = useState(0);
    useImperativeHandle(initRef, () => ({
        show: (id, confirmIndex) => {
            // 传入重复场站的站点id
            updateDataSourceOri([]);
            updateDataSourceStep1([]);
            updateRemoveStations([]);

            updateStationId(id);
            if (stationId) {
                resetData();
            }
            updateCurrentStep(0);
            updateConfirmIndex(confirmIndex);
            reasonForm.resetFields();
            initEnum();
        },
        onClose,
    }));

    const onClose = () => {
        updateVisible(false);
        updateStationId(undefined);
    };

    const [dataSourceOri, updateDataSourceOri] = useState([]); // 原始数据
    const [dataSourceStep1, updateDataSourceStep1] = useState([]); // 第一步的数据
    const [removeStations, updateRemoveStations] = useState([]);
    const resetData = async () => {
        changeSelectItems([]);
        updateListLoading(true);
        try {
            const { data } = await getDucplicateStationListApi({ mainStationId: stationId });
            if (data && data?.length > 0) {
                updateDataSourceOri(data);
                const list = DuplicateSeparteFunction(data);
                updateDataSourceStep1(list);
                updateVisible(true);
            } else {
                message.warning('疑似重复站处理中');
                return;
            }
        } catch (error) {
        } finally {
            updateListLoading(false);
        }
    };

    useEffect(() => {
        if (stationId) {
            resetData();
        }
    }, [stationId]);

    const showList = useMemo(() => {
        if (currentStep == 0) {
            return dataSourceStep1;
        }
        if (currentStep == 1) {
            return dataSourceOri.filter(
                (ele) => removeStations.some((removeId) => removeId == ele.stationId) == false,
            );
        }
        return [];
    }, [currentStep, dataSourceStep1, dataSourceOri]);

    const columns = useMemo(() => {
        return [
            ...(DuplicateTableColumns || []),
            ...((currentStep == 0 && [
                {
                    title: '操作',
                    width: 100,
                    fixed: 'right',
                    render(text, record) {
                        return (
                            (!record.currentStation && record.isParent && (
                                <a
                                    onClick={() => {
                                        const tempRemoves = [...removeStations, record.stationId];
                                        if (dataSourceStep1.length <= 2) {
                                            // 当最后一个场站被移除后，自动关闭该弹窗，疑似重复场站校验置为通过；
                                            confirm({
                                                title: '移除场站',
                                                icon: <ExclamationCircleOutlined />,
                                                content:
                                                    '执行后，所有已移除场站将不再与当前场站校验重复',
                                                zIndex: '1008',
                                                onOk: async () => {
                                                    submitEvent({
                                                        removeStation: tempRemoves?.join(',') || '',
                                                        repeatReason: '',
                                                        confirmStation: '',
                                                        firstStepRemove: '1', // 如果是第一步全部移除的话，传1
                                                    });
                                                },
                                            });
                                        } else {
                                            const removeIndex = dataSourceStep1.findIndex(
                                                (ele) => ele.stationId == record.stationId,
                                            );
                                            dataSourceStep1.splice(removeIndex, 1);
                                            updateRemoveStations([...tempRemoves]);
                                            updateDataSourceStep1([...dataSourceStep1]);
                                        }
                                    }}
                                >
                                    移除
                                </a>
                            )) ||
                            null
                        );
                    },
                },
            ]) ||
                []),
        ];
    });

    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const rowSelection = {
        selectedRowKeys: selectItems,
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            name: record.stationId,
        }),
    };

    const [reasonForm] = Form.useForm();
    const successRef = useRef();
    const submitEvent = async (values) => {
        try {
            /* 传参字段逻辑
            confirmStation：取非当前场站分组的其他组别的所有stationId
            inUseStation：勾选了哪个，就传勾选的stationId
            */
            const params = {
                currentStation: stationId,
                inUseStation: selectItems.map?.((ele) => ele.stationId)?.join(',') || '',
                ...values,
            };
            const { data } = await handleDucplicateStationListApi(params);

            if (confirmIndex === 0 || confirmIndex > 0) {
                message.success('操作成功');
                onFinish?.(confirmIndex);
                onClose();
            } else {
                operationSuccessEvent(data);
            }
        } catch (error) {}
    };

    const [inUseStations, updateInUseStations] = useState([]);
    const operationSuccessEvent = (data = {}) => {
        const { scene, message: msg, stationId: stationIds } = data;
        if (stationIds?.length) {
            updateInUseStations(stationIds.split(','));
        } else {
            updateInUseStations([]);
        }
        switch (scene) {
            case '200':
                // 操作成功
                message.success(msg);
                onFinish?.();
                onClose();
                break;
            case '202':
                // 在用站存在未通过校验，暂无法上线
                successRef.current.show(false);
                onFinish?.();
                onClose();
                break;
            case '203':
                // 在用站未上线，是否立即上线
                successRef.current.show(true);
                onFinish?.();
                onClose();
                break;
            case '105':
                // 重复站逻辑处理异常
                message.error(msg);
                break;
            default:
                message.error(msg);
                break;
        }
    };

    useEffect(() => {
        if (currentStep == 0 && selectItems?.length) {
            changeSelectItems([]);
        }
    }, [currentStep]);

    const [stationRepeatReasonList, updateStationRepeatReasonList] = useState([]);
    const initEnum = async () => {
        if (!stationRepeatReasonList?.length) {
            try {
                const {
                    data: { stationRepeatReasonList: _stationRepeatReasonList },
                } = await getStationManageOptionsApi();
                updateStationRepeatReasonList(_stationRepeatReasonList);
            } catch (error) {}
        }
    };

    // 场站上线
    const [confirmUpVisible, toggleConfirmUpModal] = useState(false);

    return (
        <Fragment>
            <Modal
                title="疑似重复场站处理"
                width={1000}
                visible={visible}
                onCancel={onClose}
                maskClosable={false}
                zIndex={1007}
                footer={null}
            >
                <Steps current={currentStep}>
                    {[
                        { title: '确认重复场站' },
                        { title: '选择在用场站' },
                        { title: '设为重复站' },
                    ]?.map((ele, index) => (
                        <Steps.Step key={index} title={ele.title} />
                    ))}
                </Steps>
                <div style={{ margin: '26px 0 16px', fontSize: '14px' }}>
                    {(currentStep == 0 && '非重复场站请“移除”，确保留下的场站均为重复场站') ||
                        (currentStep == 1 &&
                            '请选择实际在用的场站，若均没有在用，则不选；未选中的场站将自动下线并废弃') ||
                        (currentStep == 2 && '设为重复站后，所有场站将增加关联关系')}
                </div>
                {(currentStep == 2 && (
                    <Form form={reasonForm}>
                        <Form.Item
                            label="重复原因"
                            rules={[{ required: true, message: '请选择重复原因' }]}
                            name={'repeatReason'}
                            initialValue={'4'}
                        >
                            <Select placeholder="请选择">
                                {stationRepeatReasonList?.map((ele, index) => (
                                    <Option value={ele.codeValue} key={index}>
                                        {ele.codeName}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </Form>
                )) || (
                    <TablePro
                        name="confirm"
                        loading={listLoading}
                        scroll={{
                            x: 'max-content',
                            y: `${document.body.clientHeight - 440}px`,
                        }}
                        rowKey={(record) => record}
                        dataSource={showList}
                        columns={columns}
                        offsetHeader={false}
                        rowSelection={
                            (currentStep == 1 &&
                                showList?.length && {
                                    type: 'checkbox',
                                    ...rowSelection,
                                    fixed: true,
                                }) ||
                            null
                        }
                        pagination={false}
                        style={{ marginBottom: '22px' }}
                    />
                )}
                <div style={{ textAlign: 'center', width: '100%' }}>
                    <Space>
                        <Button onClick={onClose}>取消</Button>
                        {currentStep != 0 && (
                            <Button
                                onClick={() => {
                                    updateCurrentStep(currentStep - 1);
                                }}
                            >
                                上一步
                            </Button>
                        )}
                        {(currentStep != 2 && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (currentStep == 1 && selectItems?.length == 0) {
                                        confirm({
                                            title: '设为重复站',
                                            icon: <ExclamationCircleOutlined />,
                                            content: '未选择在用站，是否继续？',
                                            zIndex: '1008',
                                            onOk() {
                                                updateCurrentStep(currentStep + 1);
                                            },
                                            okText: '继续',
                                        });
                                    } else {
                                        updateCurrentStep(currentStep + 1);
                                    }
                                }}
                            >
                                下一步
                            </Button>
                        )) || (
                            <Button
                                type="primary"
                                onClick={() => {
                                    reasonForm.validateFields().then((values) => {
                                        confirm({
                                            title: '提交',
                                            icon: <ExclamationCircleOutlined />,
                                            content: '执行后，所有场站将增加关联关系',
                                            zIndex: '1008',
                                            onOk: async () => {
                                                console.log(confirmIndex);
                                                const otherStations = [];
                                                for (const data of dataSourceStep1) {
                                                    if (!data.currentStation) {
                                                        otherStations.push(
                                                            data.stationId,
                                                            ...(data.children?.map(
                                                                (ele) => ele.stationId,
                                                            ) || []),
                                                        );
                                                    }
                                                }
                                                await submitEvent({
                                                    ...values,
                                                    confirmStation: otherStations?.join(',') || '',
                                                    removeStation: removeStations?.join(',') || '',
                                                });
                                            },
                                        });
                                    });
                                }}
                            >
                                提交
                            </Button>
                        )}
                    </Space>
                </div>
            </Modal>

            {/* 场站上线确认弹窗 */}
            <ConfirmModal
                confirmVisible={confirmUpVisible}
                // firstTimerContent={firstTimerContent}
                stationInfoList={
                    dataSourceOri?.length &&
                    inUseStations?.length &&
                    confirmUpVisible &&
                    JSON.stringify(
                        dataSourceOri.filter((ele) =>
                            inUseStations.some((useEleIds) => useEleIds == ele.stationId),
                        ),
                    )
                }
                onFinish={async (validProfitRuleStations) => {
                    toggleConfirmUpModal(false);
                    try {
                        const stationIds = validProfitRuleStations
                            ?.filter((v) => v?.hasRule)
                            ?.map((v) => v?.stationId);
                        if (isEmpty(stationIds)) {
                            message.error('没有可上线场站');
                            return;
                        }
                        const params = {
                            stationIds: stationIds?.join?.(',') || stationIds,
                        };
                        const { data } = await stationUpLine(params);
                        if (data) {
                            const handle = {
                                request: stationUpLine,
                                params: params,
                            };
                            auditModal.current.show({
                                ...data,
                                type: AUDIT_OPER_TYPE.ONLINE,
                                handle,
                            });
                        } else {
                            message.success('操作成功，请到审批记录或钉钉查看审批进度');
                        }
                        onFinish?.();
                    } catch (error) {}
                }}
                onClose={() => toggleConfirmUpModal(false)}
            />

            <DuplicateSuccessModal
                initRef={successRef}
                lineEvent={() => {
                    toggleConfirmUpModal(true);
                }}
            />
            <DingTalkAuditModal initRef={auditModal} />
        </Fragment>
    );
};

export default connect(({ global }) => ({
    global,
}))(DuplicateStationModal);
