import { Button, Col, Form, Modal, Space, Select, Radio, Input } from 'antd';
import { useEffect } from 'react';
import StationTagSelectFormItem from './StationTagSelectFormItem';
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const CodeStatusEditModal = (props) => {
    const { visible, onClose, onFinish } = props;

    const [form] = Form.useForm();
    const onFinishEvent = (values) => {
        onFinish && onFinish(values);
    };

    const closeEvent = () => {
        onClose && onClose();
    };

    useEffect(() => {
        if (!visible) {
            form.resetFields();
        }
    }, [visible]);

    return (
        <Modal
            title={'贴码状态'}
            destroyOnClose
            width={600}
            visible={visible}
            onCancel={closeEvent}
            footer={null}
            maskClosable={false}
        >
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinishEvent}
                initialValues={{}}
                scrollToFirstError
            >
                <FormItem
                    label="贴码状态"
                    name="codeStatus"
                    rules={[
                        {
                            required: true,
                            message: '请选择贴码状态',
                        },
                    ]}
                >
                    <Radio.Group>
                        <Radio value={'1'}>可贴码</Radio>
                        <Radio value={'0'}>不可贴码</Radio>
                    </Radio.Group>
                </FormItem>
                <FormItem
                    label="贴码情况说明"
                    name="codeDescription"
                    rules={[{ max: 255, message: '最大长度为255' }]}
                >
                    <Input placeholder="请填写" maxLength={255} />
                </FormItem>
                <FormItem>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={closeEvent}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};
export default CodeStatusEditModal;
