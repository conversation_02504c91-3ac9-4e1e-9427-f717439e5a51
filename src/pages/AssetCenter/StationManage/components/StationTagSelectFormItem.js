import { Form, TreeSelect } from 'antd';
import { useState, useEffect } from 'react';
import { getStationTagTreeApi } from '@/services/AssetCenter/TagManageApi';
import { useMemo } from 'react';
const { SHOW_CHILD } = TreeSelect;
const FormItem = Form.Item;

const StationTagSelectItem = (props) => {
    const { onChange, disabled, maxTagCount = 5, form, ...otherProps } = props;
    const [selectList, updateSelectList] = useState([]);
    useEffect(() => {
        initTreeData();
    }, []);

    const initTreeData = async () => {
        try {
            const {
                data: { list },
            } = await getStationTagTreeApi();

            updateSelectList(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const treeOptions = useMemo(() => {
        let list = [];
        if (selectList instanceof Array) {
            for (const item of selectList) {
                if (item.parentId === 0) {
                    const children = selectList.filter((ele) => ele.parentId === item.id);
                    if (children && children.length > 0) {
                        item.children = children;
                    }
                    list.push(item);
                }
            }
        }
        return list;
    }, [selectList]);
    const onTagChange = (values) => {
        let arr = [];
        let stationLabelPictureList = form.getFieldValue('stationLabelPictureList');
        stationLabelPictureList?.forEach((item, index) => {
            values?.forEach((value) => {
                if (value === parseInt(item.labelId)) {
                    arr.push(item);
                }
            });
        });
        form.setFieldsValue({ stationLabelPictureList: arr });
        onChange && onChange(values);
    };
    return (
        <TreeSelect
            treeData={treeOptions}
            showCheckedStrategy={SHOW_CHILD}
            treeCheckable
            showArrow
            treeDefaultExpandAll
            placeholder="请选择"
            onChange={onTagChange}
            disabled={disabled}
            maxTagCount={maxTagCount}
            allowClear={!disabled}
            treeNodeFilterProp="name"
            fieldNames={{ label: 'name', value: 'id', children: 'children' }}
            {...otherProps}
        />
    );
};

const StationTagSelectFormItem = (props) => {
    const {
        form,
        label = '场站标签',
        name = 'stationLables',
        disabled,
        formItemLayout,
        ...otherProps
    } = props;

    return (
        <FormItem label={label} name={name} {...otherProps}>
            <StationTagSelectItem disabled={disabled} form={form}></StationTagSelectItem>
        </FormItem>
    );
};

export default StationTagSelectFormItem;
