import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, Input, Select, Space, message } from 'antd';
import { useImperativeHandle, useState } from 'react';

import { getStationManageOptionsApi } from '@/services/AssetCenter/StationManangeApi';
import { checkPileManageAbandonApi, gunManageAbandonApi } from '@/services/AssetCenter/PileApi';
import { getCodesApi } from '@/services/CommonApi';

const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;
const formItemLayout = {
    labelCol: {
        flex: '0 0 60px',
    },
    labelAlign: 'left',
};

const GunAbandonModal = (props) => {
    const { initRef, onFinish } = props;

    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [gunIds, updateGunIds] = useState();

    const showTipEvent = (ids, names) => {
        confirm({
            title: `枪报废提醒`,
            icon: <ExclamationCircleOutlined />,
            content: (
                <div>
                    <div style={{ fontWeight: '300' }}>
                        以下枪近7天仍有新电途充电次数，请确认是否废弃
                    </div>
                    <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {names.map((ele, index) => (
                            <div key={index} style={{ fontWeight: '500' }}>
                                {ele}
                            </div>
                        ))}
                    </div>
                </div>
            ),
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            onOk: async () => {
                showSelfEvent(ids);
            },
        });
    };

    const showSelfEvent = (ids) => {
        updateVisible(true);
        updateGunIds(ids);
        form.resetFields();
        initEnum();
    };

    const [gunAbandonReasonList, updateGunAbandonReasonList] = useState([]);
    useImperativeHandle(initRef, () => ({
        show: async (ids) => {
            try {
                const { data: list } = await checkPileManageAbandonApi({ gunIds: ids.join(',') });
                // 回参List 如果判断是否空。空代表校验通过。
                if (list?.length) {
                    showTipEvent(ids, list);
                } else {
                    showSelfEvent(ids);
                }
            } catch (error) {}
        },
        onClose,
    }));

    const onClose = () => {
        updateWaiting(false);
        updateVisible(false);
        updateGunIds(undefined);
    };

    const initEnum = async () => {
        if (!gunAbandonReasonList?.length) {
            try {
                const {
                    data: { list },
                } = await getCodesApi('gunScrapReason');
                updateGunAbandonReasonList(list);
            } catch (error) {}
        }
    };
    const [isWaiting, updateWaiting] = useState(false);
    return (
        <Modal
            title={'枪报废提醒'}
            destroyOnClose
            width={600}
            visible={visible}
            onCancel={onClose}
            maskClosable={false}
            onOk={() => {
                form.validateFields().then(async (values) => {
                    updateWaiting(true);
                    try {
                        const params = {
                            gunIds: gunIds?.join?.(',') || gunIds,
                            ...values,
                        };
                        const result = await gunManageAbandonApi(params);
                        onClose();
                        onFinish?.(result);
                    } catch (error) {
                    } finally {
                        updateWaiting(false);
                    }
                });
            }}
            okButtonProps={{ loading: isWaiting }}
        >
            <Space style={{ margin: '0 0 16px' }}>
                <ExclamationCircleOutlined />
                <div>报废后的枪不会对外展示，并且无法启动充电</div>
            </Space>
            <Form {...formItemLayout} form={form} scrollToFirstError>
                <Form.Item
                    label="原因"
                    rules={[{ required: true, message: '请选择废弃原因' }]}
                    name={'scrapReasonNo'}
                    initialValue={'1'}
                >
                    <Select placeholder="请选择">
                        {gunAbandonReasonList?.map((ele, index) => (
                            <Option value={ele.codeValue} key={index}>
                                {ele.codeName}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    noStyle
                    shouldUpdate={(pre, after) => {
                        return pre.scrapReasonNo != after.scrapReasonNo;
                    }}
                >
                    {({ getFieldValue }) => {
                        const scrapReasonNo = getFieldValue('scrapReasonNo');
                        // 选择其他时，需要填写其他信息
                        return (
                            scrapReasonNo == '0' && (
                                <Form.Item name="scrapReason">
                                    <TextArea placeholder="请填写" maxLength={200} showCount />
                                </Form.Item>
                            )
                        );
                    }}
                </Form.Item>
            </Form>
        </Modal>
    );
};
export default GunAbandonModal;
