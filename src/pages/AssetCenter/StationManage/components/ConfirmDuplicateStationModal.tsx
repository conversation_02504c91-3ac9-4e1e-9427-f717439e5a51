// 场站疑似重复待确认弹窗

import { ExclamationCircleOutlined, CheckCircleFilled } from '@ant-design/icons';
import TablePro from '@/components/TablePro';
import { Modal, Typography, Button, Popconfirm } from 'antd';
import React, { useImperativeHandle, useState, useEffect } from 'react';
import { copyObjectCommon } from '@/utils/utils';

const { confirm, success } = Modal;
export const ConfirmDuplicateStationModal: React.FC<{
    initRef: any;
    duplicateStation: any;
    onFinish: any;
}> = (props) => {
    const { initRef, duplicateStation, onFinish } = props;
    const [visible, updateVisible] = useState(false);

    const [dataSource, updateDateSource] = useState<any>();
    // 确认上线弹窗-疑似列表index
    // const [confirmIndex, updateConfirmIndex] = useState();
    useImperativeHandle(initRef, () => ({
        show: (stationInfoList: any) => {
            updateDateSource(stationInfoList);
            updateVisible(true);
        },
        setIndex: (index: number) => {
            const newList: any = copyObjectCommon(dataSource);
            newList?.splice(index, 1);
            updateDateSource(newList);
            if (newList?.length === 0) {
                confirm({
                    title: '确认',
                    icon: <ExclamationCircleOutlined />,
                    content: '疑似重复站已全部处理，确认上线？',
                    zIndex: 1008,
                    onOk: async () => {
                        onClose();
                        onFinish?.();
                    },
                });
            }
        },
    }));

    const onClose = () => {
        updateVisible(false);
        updateDateSource(undefined);
    };

    return (
        <Modal
            title={'场站疑似重复待确认弹窗'}
            visible={visible}
            onCancel={onClose}
            // cancelButtonProps={{ style: { display: 'none' } }}
            okText="暂不处理，直接上线"
            width={720}
            zIndex={1006}
            // onOk={onClose}
            footer={[
                <Button key="back" onClick={onClose}>
                    取消
                </Button>,
                <Popconfirm
                    key="submit"
                    title={`${dataSource?.length}个即将上线场站疑似重复未处理，确认上线？`}
                    onConfirm={() => {
                        onClose();
                        onFinish?.();
                    }}
                >
                    <Button>暂不处理，直接上线</Button>
                </Popconfirm>,
            ]}
        >
            <div style={{ paddingBottom: '12px' }}>
                以下
                <Typography.Text style={{ color: 'red' }}>{dataSource?.length}</Typography.Text>
                个上线场站的疑似重复站需处理
            </div>

            <TablePro
                size="small"
                scroll={{ x: 'max-content' }}
                rowKey={'instanceId'}
                dataSource={dataSource}
                filterHeader={false}
                columns={[
                    {
                        title: '场站',
                        width: 180,
                        dataIndex: 'stationName',
                    },
                    {
                        title: '疑似重复',
                        width: 80,
                        dataIndex: 'repeatNum',
                    },
                    {
                        title: '操作',
                        width: 80,
                        fixed: 'right',
                        render: (text: any, records: any, index: any) => {
                            return (
                                <a
                                    onClick={() => {
                                        duplicateStation?.(records?.stationId, index);
                                    }}
                                >
                                    立即处理
                                </a>
                            );
                        },
                    },
                ]}
                pagination={false}
            />
        </Modal>
    );
};
