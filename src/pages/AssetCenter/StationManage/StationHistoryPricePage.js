import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Col, Form, Select, Space, Input } from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import moment from 'moment';
import { LeftOutlined } from '@ant-design/icons';
import SearchOptionsBar from '@/components/SearchOptionsBar';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import CopyComponent from '@/components/CopyComponent';
import { StationPriceColumns } from './StationManageDetailPage';
import DateSearchFormItem from '@/components/DateSearchFormItem';
import commonStyles from '@/assets/styles/common.less';

const FormItem = Form.Item;
const { Option } = Select;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, listLoading, stationRef } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
                }}
                scrollToFirstError
                {...formItemLayout}
            >
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <AllStationSelect
                            form={form}
                            ref={stationRef}
                            label="场站名称"
                            rules={[{ required: true, message: '请选择场站' }]}
                        />
                    </Col>
                    <Col span={8}>
                        <DateSearchFormItem
                            label="获取时间"
                            name="dates"
                            {...formItemLayout}
                            dateProps={{
                                placeholder: ['开始时间', '结束时间'],
                                showTime: {
                                    format: 'HH:mm:ss',
                                    defaultValue: [
                                        moment('00:00:00', 'HH:mm:ss'),
                                        moment('23:59:59', 'HH:mm:ss'),
                                    ],
                                },
                                format: 'YYYY-MM-DD HH:mm:ss',
                            }}
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪功率" name="chargingType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'03'}>超充</Option>
                                <Option value={'02'}>快充</Option>
                                <Option value={'01'}>慢充</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="桩编号" name="pileNo">
                            <Input placeholder="请填写" autoComplete="off" allowClear />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪编号" name="gunNo">
                            <Input placeholder="请填写" autoComplete="off" allowClear />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const StationHistoryPricePage = (props) => {
    const {
        dispatch,
        history,
        stationManageModel: { stationPriceHistoryList, stationPriceHistoryTotal },
        listLoading,
        route,
        global: { pageInit },
    } = props;

    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();

    const state = { ...(props.location?.query || {}), ...(props.location?.state || {}) };
    const [pageInfo, changePageInfo, onTableChange] = usePageState();
    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]?.state?.useForm) {
            // 如果是从详情回来的，需要重新载入筛选条件，否则以state来填充
            form.setFieldsValue(pageInit[pathname].form);
            const { stationId, stationName } = pageInit[pathname].form;

            if (stationId && stationName) {
                stationRef.current?.init?.([
                    {
                        stationName,
                        stationId,
                    },
                ]);
            }
        } else if (state) {
            form.setFieldsValue({
                ...state,
            });
            if (state.stationId && state.stationName) {
                stationRef.current?.init?.([
                    {
                        stationName: state.stationName,
                        stationId: state.stationId,
                    },
                ]);
            }
        }

        return () => {
            dispatch({
                type: 'stationManageModel/updateStationProperty',
                params: { stationPriceHistoryList: [], stationPriceHistoryTotal: 0 },
            });

            form.resetFields();
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                ...data,
                priceGetStartTime: data?.dates?.[0].format('YYYY-MM-DD HH:mm:ss') || '',
                priceGetEndTime: data?.dates?.[1].format('YYYY-MM-DD HH:mm:ss') || '',
                dates: undefined,
            };

            // dispatch({
            //     type: 'global/setPageInit',
            //     pathname,
            //     info: {
            //         form: data,
            //         state: pageInfo,
            //     },
            // });
            await dispatch({
                type: 'stationManageModel/getStationHistoryList',
                options: params,
            });
            // // 默认全部收起
            // updateExpandedRowKeys([]);
        } catch (error) {
            console.log(5555, error);
            return error;
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '桩编号',
            width: 160,
            dataIndex: 'pileNo',
            render(text, record) {
                return <CopyComponent text={text} />;
            },
        },
        {
            title: '枪编号',
            width: 160,
            dataIndex: 'gunNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '枪功率',
            width: 120,
            dataIndex: 'gunPower',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '价格变化时间',
            width: 200,
            dataIndex: 'createdTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '价格获取时间',
            width: 200,
            dataIndex: 'priceGetTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    stationRef={stationRef}
                    state={state}
                />

                <TablePro
                    filterHeader={false}
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.id}
                    dataSource={stationPriceHistoryList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationPriceHistoryTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    showToggleExpanded
                    expandable={{
                        expandedRowRender: (record) => (
                            <TablePro
                                filterHeader={false}
                                columns={StationPriceColumns()}
                                dataSource={record?.priceList}
                                pagination={false}
                                sticky={false}
                                size="small"
                                rowClassName={(record, index) => {
                                    return (record.showFlag && commonStyles['bg_red']) || '';
                                }}
                            />
                        ),
                        rowExpandable: (record) => record?.priceList?.length > 0,
                        fixed: true,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading }) => ({
    stationManageModel,
    global,
    listLoading: loading.effects['stationManageModel/getStationHistoryList'],
}))(StationHistoryPricePage);
