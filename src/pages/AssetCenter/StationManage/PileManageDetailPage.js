import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    // Alert,
    // Divider,
    Space,
    Descriptions,
    Alert,
    Spin,
    message,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { LeftOutlined, BellOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useParams } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import { getPileManageApi } from '@/services/AssetCenter/StationManangeApi';
import TablePro from '@/components/TablePro';
import QrCodeLookComponent from '@/components/QrCodeLookItem';
import GunAbandonModal from './components/GunAbandonModal';
import { BATCH_ACTIONS } from './StationManageConfig';
import { gunManageRecoverApi } from '@/services/AssetCenter/PileApi';

const { confirm } = Modal;
const PileManageDetailPage = (props) => {
    const { history, route } = props;

    const {
        query: { id: pileId },
        state: _pileInfo,
    } = props.location || {};
    const [pileInfo, updatePileInfo] = useState();

    useEffect(() => {
        if (pileId) {
            loadData();
        } else {
            updatePileInfo(_pileInfo);
        }
    }, []);

    const [isWaiting, updateWaiting] = useState(false);
    const loadData = async () => {
        try {
            updateWaiting(true);
            const {
                data: { records },
            } = await getPileManageApi({ pageIndex: 1, pageSize: 10, pileId });
            if (records.length == 1) {
                updatePileInfo(records[0]);
            } else {
                updatePileInfo(_pileInfo);
            }
        } catch (error) {
        } finally {
            updateWaiting(false);
        }
    };

    const columns = [
        {
            title: '枪编号',
            width: 120,
            dataIndex: 'gunNo',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪名称',
            width: 120,
            dataIndex: 'gunName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪运行状态',
            width: 140,
            dataIndex: 'gunCouplerRunStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪运营状态',
            width: 140,
            dataIndex: 'gunOperStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪外显状态',
            width: 140,
            dataIndex: 'showStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪功率',
            width: 180,
            dataIndex: 'powerName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '二维码',
            width: 140,
            dataIndex: 'qrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '链接二维码',
            width: 140,
            dataIndex: 'linkQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '互联互通二维码',
            width: 160,
            dataIndex: 'hlhtQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '互联互通连接二维码',
            width: 200,
            dataIndex: 'hlhtLinkQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '接口类型',
            width: 140,
            dataIndex: 'couplerTypeName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '设备接口ID',
            width: 140,
            dataIndex: 'connectorId',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '车位号',
            width: 120,
            dataIndex: 'parkNo',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '报废时间',
            width: 140,
            dataIndex: 'scrapTime',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '报废人',
            width: 140,
            dataIndex: 'scrapperName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '报废原因',
            width: 140,
            dataIndex: 'scrapReason',
            render(text, record) {
                return (
                    <span
                        style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '2',
                            maxWidth: '220px',
                        }}
                        title={text}
                    >
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                let btns = [];

                const discardBtn = (
                    <a
                        // className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.gunId], true);
                        }}
                    >
                        废弃
                    </a>
                );
                const cardBtn = (
                    <a
                        // className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.gunId], false);
                        }}
                    >
                        恢复
                    </a>
                );

                if (record.gunOperStatus == '05') {
                    // 已废弃，显示恢复按钮
                    btns.push(cardBtn);
                } else {
                    btns.push(discardBtn);
                }

                return (
                    <Row gutter={12}>
                        <Col>{btns}</Col>
                    </Row>
                );
            },
        },
    ];

    const goBack = () => {
        history.go(-1);
    };

    // 废弃场站
    const abandonRef = useRef();
    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [abandonChecking, updateAbandonChecking] = useState(false);
    const [curSelectItem, changeCurSelectItem] = useState(undefined);
    /**
     * 批量处理交互统一处理方法
     */
    const batchSubmitEvent = (type) => {
        if (!selectItems?.length) {
            message.info('请选择批量操作的枪');
            return;
        }

        switch (type) {
            case BATCH_ACTIONS.DISCARD:
                if (selectItems.find((ele) => ele.gunOperStatus == '05')) {
                    message.error('勾选项包含已废弃场站，请勿重复废弃');
                    return;
                }
                discardStationEvent(
                    selectItems.map((ele) => ele.gunId),
                    true,
                );
                break;
            case BATCH_ACTIONS.CARD:
                if (selectItems.find((ele) => ele.gunOperStatus !== '05')) {
                    message.error('勾选项包含未废弃场站，请勿重复恢复');
                    return;
                }
                discardStationEvent(selectItems.map((ele) => ele.gunId));
                break;
            default:
                break;
        }
    };

    const discardStationEvent = async (gunIds, isDiscard) => {
        if (isDiscard) {
            updateAbandonChecking(true);
            await abandonRef.current.show(gunIds);
            updateAbandonChecking(false);
        } else {
            confirm({
                title: `确认恢复枪`,
                icon: <ExclamationCircleOutlined />,
                content: '',
                okText: '确定',
                okType: 'danger',
                cancelText: '取消',
                onOk: async () => {
                    try {
                        const params = {
                            gunIds: gunIds?.join?.(',') || gunIds,
                        };
                        const result = await gunManageRecoverApi(params);
                        operateFinish(true);
                    } catch (error) {}
                },
                onCancel() {
                    operateFinish();
                },
            });
        }
    };

    const operateFinish = (isSuccess = false) => {
        if (isSuccess) {
            loadData();
            message.success('操作成功');
            changeCurSelectItem(undefined);
        } else if (curSelectItem) {
            changeCurSelectItem(undefined);
        }
    };

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.gunId),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            name: record.gunId,
        }),
    };
    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Spin spinning={isWaiting}>
                <Card>
                    <Alert
                        type="info"
                        message={
                            <div>
                                <BellOutlined style={{ marginRight: '6px' }} />
                                带* 为互联互通数据
                            </div>
                        }
                    ></Alert>
                    <br></br>
                    <div className={commonStyles['form-title']}>电桩信息</div>
                    <Descriptions>
                        <Descriptions.Item label="运营商">
                            {pileInfo?.operatorName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`场站*`}>
                            {pileInfo?.stationName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`城市*`}>
                            {pileInfo?.cityName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`运营状态*`}>
                            {pileInfo?.stationStatusName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`场站是否上线`}>
                            {pileInfo?.pileOpenFlagName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`电桩名称*`}>
                            {pileInfo?.pileName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`电桩编号*`}>
                            {pileInfo?.pileNo || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="电桩标识">
                            {pileInfo?.pileId || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`电桩型号*`}>
                            {pileInfo?.pileModelName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`电桩类型*`}>
                            {pileInfo?.subTypeName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`电桩功率*`}>
                            {pileInfo?.powerName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`生产日期*`}>
                            {pileInfo?.pileMadeDate || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="品牌">
                            {pileInfo?.pileBrandName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="附加费说明">
                            {pileInfo?.incrementExplain || '-'}
                        </Descriptions.Item>
                    </Descriptions>
                    <br />
                    <div className={commonStyles['form-title']}>充电枪信息</div>

                    {(pileInfo?.gunInfoList?.length && (
                        <Space style={{ marginBottom: '12px' }}>
                            <Button
                                type="primary"
                                onClick={() => batchSubmitEvent(BATCH_ACTIONS.DISCARD)}
                                loading={abandonChecking}
                            >
                                批量作废
                            </Button>
                            <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.CARD)}>
                                批量恢复
                            </Button>
                        </Space>
                    )) ||
                        null}
                    <TablePro
                        rowSelection={
                            (pileInfo?.gunInfoList?.length && {
                                type: 'checkbox',
                                ...rowSelection,
                                fixed: true,
                            }) ||
                            null
                        }
                        loading={isWaiting}
                        scroll={{ x: 'max-content' }}
                        rowKey={(record, index) => record.gunId}
                        dataSource={pileInfo?.gunInfoList || []}
                        columns={columns}
                        pagination={false}
                    />
                </Card>
            </Spin>
            <GunAbandonModal
                initRef={abandonRef}
                onFinish={(result) => {
                    operateFinish(result);
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading }) => ({
    stationManageModel,
    global,
}))(PileManageDetailPage);
