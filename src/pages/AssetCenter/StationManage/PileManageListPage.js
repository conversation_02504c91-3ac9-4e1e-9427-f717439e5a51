import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, Modal, Select, Space, Input, message, Checkbox } from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';

import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';

import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import QrCodeLookComponent from '@/components/QrCodeLookItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import CopyComponent from '@/components/CopyComponent';
import { BATCH_ACTIONS } from './StationManageConfig';
import GunAbandonModal from './components/GunAbandonModal';
import CacheAreaView from '@/components/CacheAreaView';
import { gunManageRecoverApi } from '@/services/AssetCenter/PileApi';
import { isEmpty } from '@/utils/utils';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const {
        dispatch,
        stationManageModel: {},
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { codeInfo },
        stationRef,
        state,
    } = props;

    const { gunCouplerRunStatus, gunOperStatus } = codeInfo;
    const operGroupRef = useRef();

    useEffect(() => {
        if (!gunCouplerRunStatus?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'gunCouplerRunStatus',
            });
        }
        if (!gunOperStatus?.length) {
            dispatch({
                type: 'global/initCode',
                code: 'gunOperStatus',
            });
        }
    }, []);

    const gunCouplerRunStatusOptions = useMemo(() => {
        if (gunCouplerRunStatus) {
            return gunCouplerRunStatus.map((ele) => (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [gunCouplerRunStatus]);

    const gunOperStatusOptions = useMemo(() => {
        if (gunOperStatus) {
            return gunOperStatus.map((ele) => (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [gunOperStatus]);

    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const keys = ['operatorId', 'stationId', 'pileNo', 'gunNo', 'qrCode'];
    const limitRules = () => {
        const values = form.getFieldsValue(keys);
        const isOk = keys.some((key) => !isEmpty(values[key]));
        return isOk;
    };
    return (
        <Fragment>
            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{}}
                scrollToFirstError
                {...formItemLayout}
            >
                <Form.Item name="stationIdList" noStyle />
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    minSpan={64}
                    onSearchGroup={onSearchGroup}
                    onExportForm={onExportForm}
                    exportName="导出至暂存区"
                >
                    <Col span={8}>
                        <OperSelectTypeItem
                            name="operatorId"
                            form={form}
                            initialValue={state?.buildId}
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!limitRules()) {
                                            return Promise.reject('请选择运营商');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            onChange={() => form.validateFields(keys)}
                        ></OperSelectTypeItem>
                    </Col>

                    <Col span={8}>
                        <AllStationSelect
                            form={form}
                            ref={stationRef}
                            label="场站名称"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!limitRules()) {
                                            return Promise.reject('请选择场站');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                            onChange={() => form.validateFields(keys)}
                        />
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城市"
                            name="cityList"
                            placeholder="请选择"
                            // formItemLayout={{ labelAlign: 'right' }}
                            showArrow
                            provinceSelectable
                            rules={null}
                            isNewDataPermission
                        />
                    </Col>
                    <Col span={8}>
                        <FormItem label="包含废弃站" name="discardFlag">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>是</Option>
                                <Option value={'0'}>否</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="电桩名称:" name="pileName">
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                allowClear
                                onBlur={() => form.validateFields(keys)}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="电桩编号:"
                            name="pileNo"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!limitRules()) {
                                            return Promise.reject('请输入');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                allowClear
                                onBlur={() => form.validateFields(keys)}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="电桩功率" name="pileChargingType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'03'}>超充</Option>
                                <Option value={'02'}>快充</Option>
                                <Option value={'01'}>慢充</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="电桩类型" name="subType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'02'}>直流</Option>
                                <Option value={'01'}>交流</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="枪编号"
                            name="gunNo"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!limitRules()) {
                                            return Promise.reject('请输入');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                allowClear
                                onBlur={() => form.validateFields(keys)}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem
                            label="二维码编号:"
                            name="qrCode"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!limitRules()) {
                                            return Promise.reject('请输入');
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                allowClear
                                onBlur={() => form.validateFields(keys)}
                            />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪运行状态:" name="gunCouplerRunStatus">
                            <Select placeholder="请选择" allowClear>
                                {gunCouplerRunStatusOptions}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪运营状态:" name="gunOperStatus">
                            <Select placeholder="请选择" allowClear>
                                {gunOperStatusOptions}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪外显状态" name="showStatus">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>空闲</Option>
                                <Option value={'2'}>插枪中</Option>
                                <Option value={'3'}>充电中</Option>
                                <Option value={'4'}>已充满</Option>
                                <Option value={'5'}>占用</Option>
                                <Option value={'6'}>检修中</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪功率" name="gunChargingType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'03'}>超充</Option>
                                <Option value={'02'}>快充</Option>
                                <Option value={'01'}>慢充</Option>
                            </Select>
                        </FormItem>
                    </Col>

                    {/* <Form.Item shouldUpdate={(cur, pre) => cur.stationId != pre.stationId} noStyle>
                        {({ getFieldValue }) => {
                            const stationId = getFieldValue('stationId');
                            // 前置条件为场站名称有值，才能查询枪外显状态；
                            return (
                                ((stationId?.length || true) && (
                                )) ||
                                null
                            );
                        }}
                    </Form.Item> */}
                </SearchOptionsBar>
                <OperGroupImportModal
                    title="批量查询"
                    initRef={operGroupRef}
                    onConfirm={(addStationList) => {
                        const list =
                            (addStationList?.length &&
                                addStationList.map((item) => item.stationId)) ||
                            [];
                        form.setFieldsValue({ stationIdList: list });
                        const values = form.getFieldsValue();
                        onSubmit(values);
                    }}
                />
            </Form>
        </Fragment>
    );
};

const PileManageListPage = (props) => {
    const {
        dispatch,
        history,
        stationManageModel: { pileManageList, pileManageListTotal },
        listLoading,
        route,
        global: { pageInit, codeInfo },
    } = props;

    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();

    const state = props.location?.query || props.location?.state || {};
    const [pageInfo, changePageInfo, onTableChange] = usePageState();
    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]?.state?.useForm) {
            // 如果是从详情回来的，需要重新载入筛选条件，否则以state来填充
            form.setFieldsValue(pageInit[pathname].form);
            const { stationId, stationName } = pageInit[pathname].form;

            if (stationId && stationName) {
                stationRef.current?.init?.([
                    {
                        stationName,
                        stationId,
                    },
                ]);
            }
        } else if (state) {
            form.setFieldsValue({
                ...state,
                // cityList: (state.city && [state.city]) || undefined,
                // stationIdList: (state.stationId && [state.stationId]) || undefined,
                // operatorId: state.buildId || undefined,
            });
            if (state.stationId && state.stationName) {
                stationRef.current?.init?.([
                    {
                        stationName: state.stationName,
                        stationId: state.stationId,
                    },
                ]);
            }
        }

        return () => {
            dispatch({
                type: 'stationManageModel/updateStationProperty',
                params: { pileManageList: [], pileManageListTotal: 0 },
            });

            form.resetFields();
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownLoad = false) => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                cityList: data.cityList?.join?.(',') || data.cityList,
            };
            if (data.discardFlag == '1') {
                // 是否报废 1=包含报废  1 不用传 不包含传0
                delete params.discardFlag;
            }

            if (data.stationIdList) {
                params.stationIdList = data.stationIdList?.join?.(',') || data.stationIdList;
            }
            if (isDownLoad) {
                // 下载
                cacheRef?.current?.apply(params).then(() => {
                    cacheRef?.current?.count();
                });
            } else {
                params.pageIndex = pageInfo.pageIndex || undefined;
                params.pageSize = pageInfo.pageSize;
                dispatch({
                    type: 'global/setPageInit',
                    pathname,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });
                dispatch({
                    type: 'stationManageModel/getPileManageList',
                    options: params,
                });
            }
        } catch (error) {
            console.log(5555, error);
            return error;
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '场站名称',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                if (record.stationId) {
                    return (
                        <Link
                            to={`/assetCenter/stationManage/list/detail/${record.stationId}`}
                            key="detail"
                            target={'_blank'}
                        >
                            {text}
                        </Link>
                    );
                }
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '城市',
            width: 180,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '运营状态',
            width: 120,
            dataIndex: 'stationStatusName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '是否上线',
            width: 120,
            dataIndex: 'stationOpenFlagName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩名称',
            width: 180,
            dataIndex: 'pileName',
            render(text, record) {
                return (
                    <span
                        style={{ width: '140px' }}
                        title={text}
                        className={styles['table-btn']}
                        onClick={() => goDetailEvent(record)}
                    >
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '电桩编号',
            width: 160,
            dataIndex: 'pileNo',
            render(text, record) {
                return <CopyComponent text={text} />;
            },
        },
        {
            title: '电桩标识',
            width: 140,
            dataIndex: 'pileId',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩型号',
            width: 200,
            dataIndex: 'pileModelName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩类型',
            width: 120,
            dataIndex: 'subTypeName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩功率',
            width: 160,
            dataIndex: 'powerName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },

        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                let btns = [];

                const detailBtn = (
                    <span className={styles['table-btn']} onClick={() => goDetailEvent(record)}>
                        详情
                    </span>
                );

                btns.push(detailBtn);

                return <Space>{btns}</Space>;
            },
        },
    ];

    const pileColumns = [
        {
            title: '枪编号',
            width: 120,
            dataIndex: 'gunNo',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪名称',
            width: 180,
            dataIndex: 'gunName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪运行状态',
            width: 140,
            dataIndex: 'gunCouplerRunStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪运营状态',
            width: 140,
            dataIndex: 'gunOperStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪外显状态',
            width: 140,
            dataIndex: 'showStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '枪功率',
            width: 140,
            dataIndex: 'powerName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '二维码',
            width: 140,
            dataIndex: 'qrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '链接二维码',
            width: 140,
            dataIndex: 'linkQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '互联互通二维码',
            width: 160,
            dataIndex: 'hlhtQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '互联互通连接二维码',
            width: 200,
            dataIndex: 'hlhtLinkQrCode',
            render(text, record) {
                return (
                    <QrCodeLookComponent url={text} qrCode={record.qrCode}></QrCodeLookComponent>
                );
            },
        },
        {
            title: '报废时间',
            width: 140,
            dataIndex: 'scrapTime',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '报废人',
            width: 140,
            dataIndex: 'scrapperName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '报废原因',
            width: '220px',
            dataIndex: 'scrapReason',
            render(text, record) {
                return (
                    <span
                        style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: '2',
                            maxWidth: '220px',
                        }}
                        title={text}
                    >
                        {text || '-'}
                    </span>
                );
            },
        },
        {
            title: '操作',
            width: 80,
            // fixed: 'right',
            render: (text, record) => {
                let btns = [];

                const discardBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.gunId], true);
                        }}
                    >
                        废弃
                    </span>
                );
                const cardBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={() => {
                            changeCurSelectItem(record);
                            discardStationEvent([record.gunId], false);
                        }}
                    >
                        恢复
                    </span>
                );

                if (record.gunOperStatus == '05') {
                    // 枪运营状态（05为报废）
                    btns.push(cardBtn);
                } else {
                    // 已废弃，显示恢复按钮
                    btns.push(discardBtn);
                }

                return <Space>{btns}</Space>;
            },
        },
    ];

    const goDetailEvent = (item) => {
        // 点详情返回的时候需要加载当前form的搜索条件，加个标识，用于区分下次进来是取state还是取pageinit
        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: pageInit[pathname].form,
                state: { ...(pageInit[pathname].state || {}), useForm: true },
            },
        });

        history.push(`/assetCenter/pileManage/list/detail?id=${item.pileId}`, { ...item });
    };

    // 废弃场站
    const abandonRef = useRef();
    const [abandonChecking, updateAbandonChecking] = useState(false);
    const [curSelectItem, changeCurSelectItem] = useState(undefined);
    /**
     * 批量处理交互统一处理方法
     */
    const batchSubmitEvent = (type) => {
        const selectItems = [];
        pileManageList
            ?.filter((ele) => ele.selectedRows?.length > 0)
            ?.map((ele) => {
                selectItems.push(...ele.selectedRows);
            });
        if (!selectItems?.length) {
            message.info('请选择批量操作的枪');
            return;
        }

        switch (type) {
            case BATCH_ACTIONS.DISCARD:
                if (selectItems.find((ele) => ele.gunOperStatus == '05')) {
                    message.error('勾选项包含已废弃场站，请勿重复废弃');
                    return;
                }
                discardStationEvent(
                    selectItems.map((ele) => ele.gunId),
                    true,
                );
                break;
            case BATCH_ACTIONS.CARD:
                if (selectItems.find((ele) => ele.gunOperStatus !== '05')) {
                    message.error('勾选项包含未废弃场站，请勿重复恢复');
                    return;
                }
                discardStationEvent(selectItems.map((ele) => ele.gunId));
                break;
            default:
                break;
        }
    };

    const discardStationEvent = async (gunIds, isDiscard) => {
        if (isDiscard) {
            updateAbandonChecking(true);
            await abandonRef.current.show(gunIds);
            updateAbandonChecking(false);
        } else {
            confirm({
                title: `确认恢复枪`,
                icon: <ExclamationCircleOutlined />,
                content: '',
                okText: '确定',
                okType: 'danger',
                cancelText: '取消',
                onOk: async () => {
                    try {
                        const params = {
                            gunIds: gunIds?.join?.(',') || gunIds,
                        };
                        const result = await gunManageRecoverApi(params);
                        operateFinish(true);
                    } catch (error) {}
                },
                onCancel() {
                    operateFinish();
                },
            });
        }
    };

    const operateFinish = (isSuccess = false) => {
        if (isSuccess) {
            searchData();
            message.success('操作成功');
            changeCurSelectItem(undefined);
        } else if (curSelectItem) {
            changeCurSelectItem(undefined);
        }
    };

    const cacheRef = useRef();
    const pileRowSelection = {
        hideSelectAll: true,
        selectedRowKeys: pileManageList
            ?.filter(
                (ele) =>
                    ele.gunInfoList?.length > 0 &&
                    ele.selectedRows?.length == ele.gunInfoList?.length,
            )
            ?.map((ele) => ele.pileId),
        onChange: (selectedRowKeys, selectedRows) => {
            if (selectedRows.length) {
                selectedRows.map((record) => {
                    record.selectedRows?.splice(0, record.selectedRows.length);
                    record.selectedRows?.push(...record.gunInfoList);
                });
            } else {
                pileManageList.map((record) => {
                    record.selectedRows?.splice(0, record.selectedRows.length);
                });
            }
            dispatch({
                type: 'stationManageModel/updateStationProperty',
                params: { pileManageList },
            });
        },
        renderCell: (checked, record, index, originNode) => {
            const curChecked =
                record.gunInfoList?.length &&
                record.selectedRows?.length == record.gunInfoList?.length;
            const disabled = !record.gunInfoList || record.gunInfoList.length == 0;
            const indeterminate =
                record.selectedRows?.length > 0 &&
                record.selectedRows?.length != record.gunInfoList?.length;
            return disabled ? null : (
                <Checkbox
                    checked={curChecked}
                    indeterminate={indeterminate}
                    onChange={(e) => {
                        record.selectedRows.splice(0, record.selectedRows.length);
                        if (e.target.checked) {
                            record.selectedRows.push(...record.gunInfoList);
                        }
                        dispatch({
                            type: 'stationManageModel/updateStationProperty',
                            params: { pileManageList },
                        });
                    }}
                />
            );
        },
    };
    const gunRowSelection = (items = []) => {
        return {
            selectedRowKeys: items.map((ele) => ele.gunId),
            onChange: (selectedRowKeys, selectedRows) => {
                items.splice(0, items.length);
                items.push(...selectedRows);
                dispatch({
                    type: 'stationManageModel/updateStationProperty',
                    params: { pileManageList },
                });
            },
        };
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                // <div className="page-title" onClick={goBack}>
                //     <LeftOutlined />
                // </div>
                route.name
            }
            extra={<CacheAreaView bizType="astPileExport" initRef={cacheRef} />}
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    stationRef={stationRef}
                    state={state}
                    onExportForm={() => searchData(true)}
                />

                {(pileManageList?.length && (
                    <div style={{ marginBottom: '12px' }}>
                        <Space>
                            <Button
                                type="primary"
                                onClick={() => batchSubmitEvent(BATCH_ACTIONS.DISCARD)}
                                loading={abandonChecking}
                            >
                                批量作废
                            </Button>
                            <Button onClick={() => batchSubmitEvent(BATCH_ACTIONS.CARD)}>
                                批量恢复
                            </Button>
                        </Space>
                    </div>
                )) ||
                    null}

                <TablePro
                    rowSelection={
                        (pileManageList?.length && {
                            type: 'checkbox',
                            ...pileRowSelection,
                            fixed: true,
                        }) ||
                        null
                    }
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.pileId}
                    dataSource={pileManageList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: pileManageListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    expandable={{
                        expandedRowRender: (record) => {
                            if (!record.selectedRows) {
                                record.selectedRows = [];
                            }
                            return (
                                <div style={{ position: 'relative' }}>
                                    <TablePro
                                        rowSelection={{
                                            type: 'checkbox',
                                            ...gunRowSelection(record.selectedRows),
                                            fixed: true,
                                        }}
                                        rowKey={(record, index) => record.gunId}
                                        className={styles['child-table']}
                                        filterHeader={false}
                                        columns={pileColumns}
                                        dataSource={record?.gunInfoList?.map((ele) => ({
                                            ...ele,
                                            superPileId: record.pileId,
                                        }))}
                                        pagination={false}
                                        sticky={false}
                                        size="small"
                                    />
                                </div>
                            );
                        },
                        defaultExpandAllRows: true,
                        rowExpandable: (record) => record?.gunInfoList?.length > 0,
                        fixed: true,
                    }}
                    showToggleExpanded
                    defaultToggle
                    className={styles['table-expanded-row-unfixed']}
                />

                <GunAbandonModal
                    initRef={abandonRef}
                    onFinish={(result) => {
                        operateFinish(result);
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading }) => ({
    stationManageModel,
    global,
    listLoading: loading.effects['stationManageModel/getPileManageList'],
}))(PileManageListPage);
