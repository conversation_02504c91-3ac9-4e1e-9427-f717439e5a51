import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Button, Card, Col, Form, Input, message, Popconfirm, Row, Space, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import XdtProTable from '@/components/XdtProTable/index';
import WhiteListDrawer from '../Edit/index';
import {
    stopChargeWhitelistList,
    stopChargeWhitelistDel,
} from '@/services/AssetCenter/StopChargeApi';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';

const ListPage = () => {
    const whiteRef = useRef();
    const formRef = useRef();
    const actionRef = useRef();

    const onDelete = (id?: string) => {
        stopChargeWhitelistDel({ operWhitelistId: id }).then(() => {
            message.success('删除成功');
            actionRef?.current?.reload();
        });
    };
    const columns: any = [
        {
            title: '运营商',
            width: 160,
            dataIndex: 'operIds',
            fieldProps: { placeholder: '请填写' },

            renderFormItem(value: any) {
                return <OperSelectItem name="operIds" label="" />;
            },
            order: 8,
            search: {
                transform: (v: any) => {
                    return { operIds: [v] };
                },
            },
            render: (value: any, record: any) => {
                return record?.operName;
            },
        },
        {
            title: '场站',
            dataIndex: 'stationIds',
            fieldProps: { placeholder: '请填写' },

            renderFormItem(schema, config, form) {
                return (
                    <AllStationSelect
                        form={form}
                        name="stationIds"
                        label=""
                        // operId={operId}
                    />
                );
            },
            search: {
                transform: (v: any) => {
                    return { stationIds: [v?.value] };
                },
            },
            order: 7,
            render: (value: any, record: any) => {
                return record?.stationDesc;
            },
        },
        {
            title: '创建人',
            dataIndex: 'creator',
            width: 160,
            hideInSearch: true,
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 160,
            hideInSearch: true,
            render(text: string) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            hideInSearch: true,
            width: 160,
            render: (text: string, record: any) => {
                return (
                    <Space>
                        <Typography.Link
                            onClick={() => {
                                whiteRef?.current?.show(record?.operWhitelistId);
                            }}
                        >
                            编辑
                        </Typography.Link>
                        <Popconfirm
                            title={`确定删除？`}
                            okText="是"
                            cancelText="否"
                            onConfirm={() => onDelete(record?.operWhitelistId)}
                        >
                            <Typography.Link>删除</Typography.Link>
                        </Popconfirm>
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper>
            <Card>
                <XdtProTable
                    actionRef={actionRef}
                    formRef={formRef}
                    rowKey={(record: any) => record?.operWhitelistId}
                    requestApi={stopChargeWhitelistList}
                    columns={columns}
                    hasSort
                    toolButtons={[
                        <Button
                            key="add"
                            type="primary"
                            onClick={() => {
                                whiteRef?.current?.show();
                            }}
                        >
                            新增
                        </Button>,
                    ]}
                ></XdtProTable>
            </Card>
            <WhiteListDrawer ref={whiteRef} onSuccess={() => actionRef?.current?.reload()} />
        </PageHeaderWrapper>
    );
};

export default ListPage;
