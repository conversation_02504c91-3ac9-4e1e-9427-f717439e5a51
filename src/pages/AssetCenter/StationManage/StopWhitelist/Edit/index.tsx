import { Button, Drawer, Form, Input, message, Modal, Radio, Select, Space } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import OperTreeSelectList from '@/components/OperTreeSelectList';
import { saveStationScopeInfoApi } from '@/services/CommonApi';
import {
    stopChargeWhitelistDetail,
    stopChargeWhitelistSave,
} from '@/services/AssetCenter/StopChargeApi';

import { isEmpty } from 'lodash';
const { confirm } = Modal;
const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'right',
};
const WhiteListDrawer = (props: any, ref: any) => {
    const { onSuccess } = props;
    const [form] = Form.useForm();
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [id, setId] = useState('');
    const operSelectList = useRef();
    useImperativeHandle(ref, () => ({
        show: (id?: string) => {
            if (id) {
                setId(id);
                stopChargeWhitelistDetail({ operWhitelistId: id }).then((res) => {
                    const { data = {} } = res;
                    const { scopeList = [] } = data;
                    const result = scopeList.flatMap((item) => item.stationIdList);
                    const arr = [
                        {
                            operId: data?.operId,
                            stationId: result?.map((v) => v?.toString()) || [],
                        },
                    ];
                    data.scopeList = arr;
                    operSelectList.current && operSelectList.current.init(arr);

                    form.setFieldsValue(data);
                });
            }
            setOpen(true);
        },
    }));
    const onClose = () => {
        setId('');
        form.resetFields();
        setOpen(false);
    };
    const onFinish = async (values) => {
        const { scopeList = [] } = values;
        const params = { ...values };
        const stationOptions: any = {};
        let allStations: any = [];
        scopeList.map((item) => {
            allStations = [...allStations, ...item.stationId];
            return item;
        });
        stationOptions.addStationIds = [];
        stationOptions.submitStationIds = allStations;
        stationOptions.delStationIds = [];
        setLoading(true);
        const {
            data: { stationScopeKey },
        } = await saveStationScopeInfoApi(stationOptions).catch(() => setLoading(false));
        if (stationScopeKey) {
            params.stationScopeKey = stationScopeKey;
            delete params.scopeList;
            stopChargeWhitelistSave(params)
                .then(() => {
                    message.success('保存成功');
                    onSuccess();
                    onClose();
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    };
    return (
        <Drawer
            title={id ? '编辑' : '新增'}
            width={800}
            onClose={onClose}
            visible={open}
            footer={[
                <Space key="1">
                    <Button onClick={() => onClose()}>取消</Button>
                    <Button
                        type="primary"
                        loading={loading}
                        onClick={() => {
                            form.validateFields().then((values) => {
                                const { scopeList = [] } = values;
                                let allStations: any = [];
                                scopeList.map((item) => {
                                    allStations = [...allStations, ...item.stationId];
                                    return item;
                                });
                                confirm({
                                    title: `已选的${allStations.length}个场站将支持自动停充功能，请确认`,
                                    okButtonProps: {
                                        loading: loading,
                                    },
                                    onOk() {
                                        form.submit();
                                    },
                                });
                            });
                        }}
                    >
                        提交
                    </Button>
                </Space>,
            ]}
        >
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
                <Form.Item name="operWhitelistId" hidden />
                <OperTreeSelectList
                    label="场站配置"
                    form={form}
                    initRef={operSelectList}
                    rules={[{ required: true, message: '请选择场站' }]}
                    name="scopeList"
                    mode="select"
                    formItemLayout={formItemLayout}
                    disabledOperSelect={id}
                />
            </Form>
        </Drawer>
    );
};

export default forwardRef(WhiteListDrawer);
