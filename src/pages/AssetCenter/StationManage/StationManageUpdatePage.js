import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Radio,
    Form,
    Select,
    InputNumber,
    Tooltip,
    Input,
    Space,
    Divider,
    message,
    Switch,
    Table,
    Checkbox,
    notification,
    Upload,
    List,
    Row,
    Col,
    Descriptions,
    Image,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo } from 'react';
import { LeftOutlined, InfoCircleOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useParams } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import StationTagSelectFormItem from './components/StationTagSelectFormItem';
import RoadBookFormItem from './components/RoadBookFormItem';
import LabelImageFormItem from './components/LabelImageFormItem';
import { NoticeInfoLayout, noticeInfoFormatter } from './components/NoticeInfoModal';
import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgItem';
import AreaCascader from './components/AreaCascader';
import { BASE_URL } from '@/config/global';

import moment from 'moment';
const FormItem = Form.Item;
const { TextArea } = Input;
const { Option } = Select;
const formItemLayout = {
    labelCol: {
        flex: '0 0 160px',
    },
    labelAlign: 'right',
    wrapperCol: {
        span: 12,
    },
};
const formItemFixedLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 6,
    },
};

export const DATA_TYPES = {
    BUSI_TIME_KEY: 'busiTimeCtrl', // 营业时间
    STATION_TYPE_KEY: 'stationTypeFlag', // 场站类型
    MAP_KEY: 'mapCtrl', // 场站地图
    ADDRESS_KEY: 'stationAddrCtrl', // 详细地址描述
    AREA_KEY: 'areaCtrl', // 所在地区
    PHONE_KEY: 'telCtrl', // 场站电话
    VECHICLE_KEY: 'applyCarModelDescCtrl', // 适用车型描述
    STATION_IMAGE_KEY: 'stationPictureCtrl', // 场站图片
};

const stationDataOptions = [
    {
        label: '营业时间',
        value: DATA_TYPES.BUSI_TIME_KEY,
        key: 'busiTime',
    },
    {
        label: '场站类型',
        value: DATA_TYPES.STATION_TYPE_KEY,
        key: 'stationType',
    },
    {
        label: '场站地图',
        value: DATA_TYPES.MAP_KEY,
        key: 'rule_map_flag',
    },
    {
        label: '详细地址描述',
        value: DATA_TYPES.ADDRESS_KEY,
        key: 'stationAddr',
    },
    {
        label: '所在地区',
        value: DATA_TYPES.AREA_KEY,
    },
    {
        label: '场站电话',
        value: DATA_TYPES.PHONE_KEY,
        key: 'tel',
    },
    {
        label: '适用车型描述',
        value: DATA_TYPES.VECHICLE_KEY,
        key: 'applyCarModelDesc',
    },
    {
        label: '场站图片',
        value: DATA_TYPES.STATION_IMAGE_KEY,
        key: 'stationPictureList',
    },
];
// 数据不同步字段 不展示场站图片勾选
const formatStationDataOptions = stationDataOptions.filter(
    (dataEle) => dataEle.value !== DATA_TYPES.STATION_IMAGE_KEY,
);

import { saveStationInfoApi } from '@/services/AssetCenter/StationManangeApi';
import { getSecondTypeCodesApi } from '@/services/CommonApi';
import SelectLocationModal from './components/SelectLocationModal';
import { max } from 'lodash';

const UploadButton = ({ uploading }) => (
    <div>{uploading ? <LoadingOutlined /> : <PlusOutlined />}</div>
);
const UploadImageLayout = (props) => {
    const {
        form,
        dataIndex: filePath,
        isSmall,
        sizeInfo = { size: 100 },
        disabled,
        stationPictureListLength,
    } = props;

    const [fileList, updateFileList] = useState([]);
    const changeFileListAndForm = (list) => {
        updateFileList([...list]);
        const name = `${filePath}`;
        setFormValues({ [name]: list });
    };
    useEffect(() => {
        const file = form.getFieldValue(filePath) || [];
        const tempList = file;
        changeFileListAndForm(tempList);
    }, [form, filePath, stationPictureListLength]);

    const setFormValues = (values) => {
        if (Array.isArray(filePath)) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath?.length - 1) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }
            if (!obj) {
                obj = superObj;
            }
            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue({ superName: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    const handleChange = ({ fileList: newFileList }) => {
        const list = newFileList?.map((ele) => {
            if (ele.status != 'uploading') {
                if (ele.url) {
                } else if (ele.response?.ret == 401) {
                    const logout = window?.loginStore?.logout;
                    logout?.();
                    notification.error({
                        message: '登录失效，请重新登录',
                    });
                } else if (!ele.response?.ret || ele.response?.ret != 200) {
                    ele.response = ele.response?.msg || '上传失败';
                    ele.status = 'error';
                } else if (ele.response?.data?.filePath) {
                    ele.url = ele.response?.data?.filePath;
                    ele.filePath = ele.response?.data?.relativePath;
                    ele.fileId = ele.response?.data?.fileId;
                }
            }
            let params = {
                fileId: ele?.fileId,
                filePath: ele?.filePath,
                uid: ele?.uid,
                url: ele?.url,
            };
            return params;
        });
        updateFileList([...list]);
        const name = `${filePath}`;
        setFormValues({ [name]: list });
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg';
            if (!isJpgOrPng) {
                message.error('图片格式错误!');
                rej();
                return;
            }
            const isLt2M = file.size / 1024 < sizeInfo.size;
            if (!isLt2M) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}mb`;
                } else {
                    sizeNum = `${sizeInfo.size}kb`;
                }
                message.error(`图片不大于 ${sizeNum}!`);
                rej();
                return;
            }

            if (sizeInfo.width || sizeInfo.height) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const data = e.target.result;
                    // 加载图片获取图片真实宽度和高度
                    const image = new Image();
                    image.onload = function () {
                        const { width } = image;
                        const { height } = image;
                        if (sizeInfo.width && sizeInfo.height) {
                            if (width != sizeInfo.width && height != sizeInfo.height) {
                                message.error(`图片尺寸必须为${sizeInfo.width}*${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.width) {
                            if (width != sizeInfo.width) {
                                message.error(`图片宽度必须为${sizeInfo.width}`);
                                rej();
                                return;
                            }
                        } else if (sizeInfo.height) {
                            if (height != sizeInfo.height) {
                                message.error(`图片高度必须为${sizeInfo.height}`);
                                rej();
                                return;
                            }
                        }
                        if (isJpgOrPng && isLt2M) {
                            res();
                        } else {
                            rej();
                        }
                    };
                    image.src = data;
                };
                reader.readAsDataURL(file);
            } else {
                res();
            }
        });

    const upLoadProps = {
        name: 'file',
        accept: '.jpg,.jpeg,.png',
        action: `${origin}${BASE_URL}/pub/upload`,
        data: {
            contentType: 'stationGuide',
            contRemrk: 'popIcon',
            relaTable: 'stationGuide',
            addWatermarkFlag: '1',
            businessType: 'stationGuideBk',
        },
        fileList: fileList,
        onChange: handleChange,
        listType: 'picture-card',
        beforeUpload,
        maxCount: 10,
    };

    return (
        <Upload
            className={(isSmall && commonStyles['upload-smaller']) || undefined}
            {...upLoadProps}
            disabled={disabled}
        >
            {fileList?.length >= 10 ? null : (!disabled && <UploadButton />) || null}
        </Upload>
    );
};
const NumericInput = (props) => {
    const { value, onChange } = props;
    const handleChange = (e) => {
        const { value: inputValue } = e.target;
        const reg = /^[-\d]*$/;
        if (reg.test(inputValue) || inputValue === '') {
            onChange(inputValue);
        }
    };

    const handleBlur = () => {
        let valueTemp = value;
        if (value === '-') {
            valueTemp = value.slice(0, -1);
        }
        onChange(valueTemp.replace(/0*(\d+)/, '$1'));
    };

    return <Input {...props} onChange={handleChange} onBlur={handleBlur} />;
};

const BasicView = (props) => {
    const {
        disabled,
        form,
        stationManageModel: { stationEditInfo },
    } = props;

    const [stationTypeList, updateStationTypeList] = useState([]);

    const lon = Form.useWatch('lon', form);
    const lat = Form.useWatch('lat', form);

    const [showMapModal, toggleShowMapModal] = useState(false);
    const closeMapModalEvent = () => {
        toggleShowMapModal(false);
    };

    useEffect(() => {
        initStationType();
    }, []);

    const updateFormLocation = ({ lon, lat }) => {
        form.setFieldsValue({
            lon,
            lat,
        });
        closeMapModalEvent();
    };

    const initStationType = async () => {
        try {
            const {
                data: { list },
            } = await getSecondTypeCodesApi('astBusiStationType', '02');
            updateStationTypeList(list || []);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const stationTypeOptions = useMemo(() => {
        if (stationTypeList instanceof Array) {
            return stationTypeList.map((ele) => (
                <Option value={ele.codeValue} key={ele.codeValue}>
                    {ele.codeName}
                </Option>
            ));
        }
        return [];
    }, [stationTypeList]);

    return (
        <Fragment>
            <FormItem
                label={
                    <Space>
                        数据不同步字段
                        {/* <Tooltip title="开启后，互联互通将不再同步场站类型数据">
                            <InfoCircleOutlined />
                        </Tooltip> */}
                    </Space>
                }
                name="stationTypes"
                wrapperCol={{ span: 24 }}
            >
                <Checkbox.Group disabled={disabled} options={formatStationDataOptions} />
            </FormItem>

            <Form.Item shouldUpdate={(pre, cur) => pre.stationTypes != cur.stationTypes} noStyle>
                {({ getFieldValue }) => {
                    let stationTypes = getFieldValue('stationTypes');
                    let stationPictureListLength = getFieldValue('stationPictureListLength') || 0;
                    let arr2 = ['stationPictureCtrl'];
                    const mergedArray = stationTypes?.concat(arr2);
                    stationTypes = [...new Set(mergedArray)];
                    return (
                        <Fragment>
                            {stationTypes?.map((ele) => {
                                const key = stationDataOptions.find(
                                    (dataEle) => dataEle.value == ele,
                                )?.key;
                                const label = stationDataOptions.find(
                                    (dataEle) => dataEle.value == ele,
                                )?.label;
                                return (
                                    (ele == DATA_TYPES.MAP_KEY && (
                                        <Fragment>
                                            <Form.Item name="lat" noStyle />
                                            <Form.Item name="lon" noStyle />
                                            <FormItem
                                                rules={[
                                                    ({ getFieldValue }) => ({
                                                        validator(rule, value) {
                                                            const lat = getFieldValue('lat');
                                                            const lon = getFieldValue('lon');
                                                            if (!lat && !lon) {
                                                                return Promise.reject(
                                                                    '请选择经纬度',
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                name={key || label}
                                                label={label}
                                                required
                                            >
                                                <span
                                                    className={commonStyles['table-btn']}
                                                    onClick={() => {
                                                        toggleShowMapModal(true);
                                                    }}
                                                >
                                                    点击设置
                                                </span>
                                            </FormItem>
                                        </Fragment>
                                    )) ||
                                    (ele == DATA_TYPES.AREA_KEY && (
                                        <AreaCascader
                                            form={form}
                                            formItemLayout={formItemFixedLayout}
                                            label={
                                                <Space>
                                                    所在地区
                                                    <Tooltip title="省市区数据完整时，互联互通不会再同步该字段数据">
                                                        <InfoCircleOutlined />
                                                    </Tooltip>
                                                </Space>
                                            }
                                            initialProvince={stationEditInfo?.province}
                                            initialCity={stationEditInfo?.city}
                                            initialArea={stationEditInfo?.country}
                                            rules={[{ required: true, message: '请选择所在地区' }]}
                                            getAnywhereValue
                                        />
                                    )) || (
                                        <FormItem
                                            key={ele}
                                            name={key || label}
                                            label={label}
                                            {...{
                                                ...formItemFixedLayout,
                                                wrapperCol: {
                                                    span:
                                                        ele == DATA_TYPES.STATION_IMAGE_KEY
                                                            ? 16
                                                            : 6,
                                                },
                                            }}
                                            rules={[
                                                {
                                                    required: ele !== DATA_TYPES.STATION_IMAGE_KEY,
                                                    message: '请配置',
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        if (ele == DATA_TYPES.STATION_IMAGE_KEY) {
                                                            if (!value?.length) {
                                                                return Promise.resolve();
                                                            } else {
                                                                let err = false;
                                                                value.map((ele) => {
                                                                    if (!ele?.fileId?.length) {
                                                                        err = true;
                                                                    }
                                                                });
                                                                if (err) {
                                                                    return Promise.reject(
                                                                        '有图片上传失败',
                                                                    );
                                                                }
                                                            }
                                                        }

                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                        >
                                            {(ele == DATA_TYPES.BUSI_TIME_KEY && (
                                                <Input
                                                    placeholder="请填写"
                                                    disabled={disabled}
                                                    maxLength={50}
                                                    autoComplete="off"
                                                ></Input>
                                            )) ||
                                                (ele == DATA_TYPES.PHONE_KEY && (
                                                    <NumericInput
                                                        placeholder="请填写"
                                                        disabled={disabled}
                                                        maxLength={16}
                                                        autoComplete="off"
                                                    ></NumericInput>
                                                )) ||
                                                (ele == DATA_TYPES.STATION_TYPE_KEY && (
                                                    <Select disabled={disabled}>
                                                        {stationTypeOptions}
                                                    </Select>
                                                )) ||
                                                ((ele == DATA_TYPES.ADDRESS_KEY ||
                                                    ele == DATA_TYPES.VECHICLE_KEY) && (
                                                    <TextArea
                                                        rows={3}
                                                        placeholder="请填写"
                                                        disabled={disabled}
                                                        maxLength={128}
                                                        showCount
                                                    ></TextArea>
                                                )) ||
                                                (ele == DATA_TYPES.STATION_IMAGE_KEY && (
                                                    // <UpLoadImgItem
                                                    //     uploadData={{
                                                    //         contentType: '02',
                                                    //         contRemrk: 'community',
                                                    //         relaTable: 'e_wechat_community',
                                                    //     }}
                                                    //     sizeInfo={{
                                                    //         size: 800,
                                                    //     }}
                                                    //     multiple
                                                    //     maxCount="10"
                                                    // ></UpLoadImgItem>
                                                    <UploadImageLayout
                                                        form={form}
                                                        dataIndex={key}
                                                        stationPictureListLength={
                                                            stationPictureListLength
                                                        }
                                                        isSmall
                                                        sizeInfo={{ size: 800 }}
                                                    />
                                                )) ||
                                                null}
                                        </FormItem>
                                    )
                                );
                            })}
                        </Fragment>
                    );
                }}
            </Form.Item>
            <Form.Item shouldUpdate noStyle>
                {({ getFieldValue }) => {
                    const stationUrl = getFieldValue('stationUrl');
                    let stationUrlArr = null;
                    if (stationUrl?.length > 0) {
                        stationUrlArr = stationUrl?.split(';');
                    }
                    return (
                        <FormItem label="场站图片（互联互通）">
                            <Image.PreviewGroup>
                                {stationUrlArr?.map((item, index) => (
                                    <div
                                        key={index}
                                        style={{
                                            width: '60px',
                                            height: '60px',
                                            lineHeight: '60px',
                                            display: 'inline-block',
                                            marginRight: '10px',
                                            border: '1px solid #d9d9d9',
                                        }}
                                    >
                                        <Image width={'100%'} src={item} />
                                    </div>
                                ))}
                            </Image.PreviewGroup>
                        </FormItem>
                    );
                }}
            </Form.Item>

            <SelectLocationModal
                open={showMapModal}
                onClose={closeMapModalEvent}
                onOk={updateFormLocation}
                lon={lon}
                lat={lat}
            ></SelectLocationModal>
        </Fragment>
    );
};

const ParkingView = (props) => {
    const {
        disabled,
        stationManageModel: { stationEditInfo },
        parkPriceLabelList,
    } = props;
    console.log(parkPriceLabelList);
    return (
        <Fragment>
            <FormItem
                label={
                    <Space>
                        是否录入车牌
                        <Tooltip title="设为是，用户下单时车牌号必填，否则非必填">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </Space>
                }
                name="forceCarNum"
                initialValue={'1'}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={'1'}>是</Radio>
                    <Radio value={'0'}>否</Radio>
                </Radio.Group>
            </FormItem>

            <FormItem label={'停车费说明（外显）'}>
                <FormItem name="parkPriceDesc" noStyle>
                    <TextArea
                        rows={3}
                        placeholder="请填写"
                        disabled={disabled}
                        maxLength={256}
                        showCount
                    ></TextArea>
                </FormItem>
                <div>停车费说明（互联互通）：{stationEditInfo?.parkPrice || '-'}</div>
            </FormItem>
            <FormItem label="停车标签" name="parkPriceLabelDisplay">
                <Select
                    placeholder="请选择"
                    allowClear
                    options={parkPriceLabelList}
                    fieldNames={{ label: 'codeName', value: 'codeValue' }}
                />
            </FormItem>

            <FormItem label={'占位开关'} name="incrementSwitch" initialValue={'1'}>
                {(stationEditInfo?.operIncrementSwitch == '0' && (
                    <Tooltip title="运营商未开启收取占位费">
                        <Radio.Group disabled>
                            <Radio value={'1'}>开启</Radio>
                            <Radio value={'0'}>关闭</Radio>
                        </Radio.Group>
                    </Tooltip>
                )) || (
                    <Radio.Group disabled={disabled}>
                        <Radio value={'1'}>开启</Radio>
                        <Radio value={'0'}>关闭</Radio>
                    </Radio.Group>
                )}
            </FormItem>

            {/* <FormItem
                label={
                    <Space>
                        占位费通知
                        <Tooltip title="含短信、支付宝通知">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </Space>
                }
                name="delayFeeFlag"
                initialValue={'1'}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={'1'}>开启</Radio>
                    <Radio value={'0'}>关闭</Radio>
                </Radio.Group>
            </FormItem> */}
        </Fragment>
    );
};

const TagView = (props) => {
    const {
        form,
        stationManageModel: { stationBestName, stationEditInfo },
        disabled,
    } = props;
    return (
        <Fragment>
            <StationTagSelectFormItem
                form={form}
                name="labelList"
                maxTagCount={'responsive'}
                disabled={disabled}
                label={
                    <Space>
                        <Tooltip title="没数据时，将会外显互联互通的数据">
                            <InfoCircleOutlined />
                        </Tooltip>
                        <span>场站标签</span>
                    </Space>
                }
            />

            <Form.Item shouldUpdate noStyle>
                {({ getFieldValue }) => {
                    const labelHlhtList = getFieldValue('labelHlhtList');
                    return (
                        <FormItem label="场站标签（互联互通）" name="labelHlhtList">
                            {labelHlhtList?.map((ele) => ele.labelName)?.join('、') || '-'}
                        </FormItem>
                    );
                }}
            </Form.Item>

            <LabelImageFormItem
                wrapperCol={{ span: 24 }}
                form={form}
                disabled={disabled}
            ></LabelImageFormItem>
            <FormItem label={`是否${stationBestName}`} name="goodFlag" initialValue={'0'}>
                <Radio.Group disabled={disabled}>
                    <Radio value={'1'}>是</Radio>
                    <Radio value={'0'}>否</Radio>
                </Radio.Group>
            </FormItem>

            <RoadBookFormItem
                wrapperCol={{ span: 24 }}
                form={form}
                disabled={disabled}
                label={
                    <Space>
                        <Tooltip title="没数据时，将会外显互联互通的数据">
                            <InfoCircleOutlined />
                        </Tooltip>
                        <span>路书配置</span>
                    </Space>
                }
            ></RoadBookFormItem>

            <FormItem label="路书（互联互通）" name="guideHlhtList">
                {stationEditInfo?.guideHlhtList?.map((item, i) => {
                    return (
                        <Descriptions column={2} key={i} style={{ marginTop: '5px' }}>
                            <Descriptions.Item label="描述">
                                {item?.guideDesc || '-'}
                            </Descriptions.Item>
                            {(item?.guidePictureList?.length && (
                                <Descriptions.Item label="图片">
                                    <Upload
                                        listType="picture-card"
                                        fileList={item?.guidePictureList?.map?.((ele) => ({
                                            url: ele.url,
                                        }))}
                                        disabled
                                    />
                                </Descriptions.Item>
                            )) ||
                                null}
                        </Descriptions>
                    );
                })}
            </FormItem>
        </Fragment>
    );
};

const OtherView = (props) => {
    const { disabled, form } = props;

    return (
        <Fragment>
            <FormItem label="贴码状态" name="codeStatus">
                <Radio.Group>
                    <Radio value={'1'}>可贴码</Radio>
                    <Radio value={'0'}>不可贴码</Radio>
                </Radio.Group>
            </FormItem>
            <FormItem
                label="贴码情况说明"
                name="codeDescription"
                rules={[{ max: 255, message: '最大长度为255' }]}
            >
                <Input placeholder="请填写" maxLength={255} />
            </FormItem>
            <FormItem
                label={
                    <Space>
                        价格基准
                        <Tooltip title="原商家优惠关闭，价格基准为原价；开启则为结算价">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </Space>
                }
                name="operDiscount"
                initialValue={'0'}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={'0'}>原价</Radio>
                    <Radio value={'1'}>结算价</Radio>
                </Radio.Group>
            </FormItem>

            {/* <FormItem label="实体类型" name="car" initialValue={'01'}>
                <Radio.Group>
                    <Radio value={'01'}>实体</Radio>
                    <Radio value={'02'}>虚拟</Radio>
                </Radio.Group>
            </FormItem> */}

            <FormItem
                label={
                    <Space>
                        站点加权
                        <Tooltip title="填写范围-100～100，仅整数">
                            <InfoCircleOutlined />
                        </Tooltip>
                    </Space>
                }
                name="stationWeight"
                initialValue={'01'}
            >
                <InputNumber
                    style={{ width: '100px' }}
                    step={1}
                    min={-100}
                    max={100}
                    precision={0}
                    placeholder="请填写"
                    disabled={disabled}
                ></InputNumber>
            </FormItem>

            <NoticeInfoLayout
                disabled={disabled}
                form={form}
                formItemLayout={{
                    ...formItemLayout,
                    wrapperCol: {
                        span: 24,
                    },
                }}
            />

            <FormItem name="localRemark" label={'备注（本地）'}>
                <TextArea
                    rows={3}
                    placeholder="请填写"
                    disabled={disabled}
                    maxLength={256}
                    showCount
                ></TextArea>
            </FormItem>
        </Fragment>
    );
};
const StationManageUpdatePage = (props) => {
    const {
        dispatch,
        history,
        route,
        location,
        stationManageModel: { stationEditInfo, stationBestName, partTimeStationPictureInfo },
        global: { codeInfo = {} },
    } = props;
    const {
        query: { partTimeJobId },
    } = location;
    const { parkPriceLabel: parkPriceLabelList } = codeInfo;

    const { stationId } = useParams();
    const [form] = Form.useForm();
    const isLock = false;

    useEffect(() => {
        if (!parkPriceLabelList) {
            dispatch({
                type: 'global/initCode',
                code: 'parkPriceLabel',
            });
        }
        initData();
        if (!stationBestName?.length) {
            dispatch({
                type: 'stationManageModel/getStationGoodFlagName',
                options: {},
            });
        }

        return () => {
            dispatch({
                type: 'stationManageModel/updateStationProperty',
                params: { stationEditInfo: undefined },
            });
        };
    }, []);

    // 调用搜索接口
    const initData = async () => {
        try {
            const params = {
                stationId: stationId,
            };

            dispatch({
                type: 'stationManageModel/getStationInfo',
                options: params,
            });
        } catch (error) {
            console.log(5555, error);
            return error;
        }
    };

    useEffect(() => {
        if (stationEditInfo) {
            if (partTimeJobId) {
                const params = {
                    partTimeJobId: partTimeJobId,
                    stationId: stationId,
                };
                dispatch({
                    type: 'stationManageModel/getPartTimeStationPictureInfo',
                    options: params,
                });
            }
            let params = {
                ...stationEditInfo,

                labelList: stationEditInfo.labelList?.map((ele) => Number(ele.labelId)),

                stationTypeFlag: stationEditInfo.stationTypeFlag === '1' ? true : false,
                busiTimeCtrl: stationEditInfo.busiTimeCtrl === '1' ? true : false,

                mapCtrl: stationEditInfo.mapCtrl === '1' ? true : false,
                stationPictureCtrl: true,
            };
            stationEditInfo.stationPictureCtrl = '1';
            const stationTypes = [];
            stationDataOptions.map((ele) => {
                if (stationEditInfo[ele.value] == '1') {
                    stationTypes.push(ele.value);
                }
            });
            params.stationTypes = stationTypes;
            if (stationEditInfo.noticeList?.length) {
                const noticeList = [{}, {}];
                stationEditInfo.noticeList?.map((ele) => {
                    const tempNotice = { ...ele };
                    if (ele.always == '0') {
                        tempNotice.dateRange = [
                            ele.effTime && moment(ele.effTime),
                            ele.expTime && moment(ele.expTime),
                        ];
                    }
                    if (ele.templateId) {
                        tempNotice.useTemplateFlag = true;
                        tempNotice.useTemplateNoticeInfo = ele.noticeInfo;
                        tempNotice.noticeInfo = undefined;
                    }
                    if (ele.sn == '1') {
                        noticeList[0] = tempNotice;
                    } else if (ele.sn == '2') {
                        noticeList[1] = tempNotice;
                    }
                });
                params.noticeList = noticeList;
            } else {
                params.noticeList = [{}, {}];
            }
            form.setFieldsValue({ ...params });
        }
    }, [stationEditInfo]);
    useEffect(() => {
        if (partTimeStationPictureInfo) {
            const { guideList, stationLabelPictureList, stationPictureList, labelList } =
                partTimeStationPictureInfo;
            let params = {
                stationId: stationId,
                stationPictureList: stationPictureList?.length > 0 ? stationPictureList : undefined,
                stationPictureListLength: stationPictureList?.length,
                stationLabelPictureList:
                    stationLabelPictureList?.length > 0 ? stationLabelPictureList : undefined,
                guideList: guideList?.length > 0 ? guideList : undefined,
                labelList: labelList?.map((ele) => Number(ele)),
            };
            form.setFieldsValue({ ...params });
        }
    }, [partTimeStationPictureInfo]);

    const [isWaiting, updateWaiting] = useState(false);
    const onFinish = async (values) => {
        const params = {
            ...values,
            stationWeight: values.stationWeight || '0',
            stationTypeFlag: values.stationTypeFlag ? '1' : '0',
            busiTimeCtrl: values.busiTimeCtrl ? '1' : '0',

            mapCtrl: values.mapCtrl ? '1' : '0',
        };
        stationDataOptions.map((ele) => {
            params[ele.value] = values.stationTypes?.some((typeEle) => typeEle == ele.value)
                ? '1'
                : '0';
        });
        delete params.area_key;
        delete params.stationTypes;

        const guideList = params.guideList?.map((ele) => ({
            titleType: ele.titleType,
            title: ele.title,
            fileIdList: ele.guidePictureList.map((picEle) => picEle.fileId),
            guideDesc: ele.guideDesc,
            id: ele.id,
        }));
        const labelList = params.labelList?.map((ele) => ({
            labelId: ele,
            id:
                stationEditInfo?.labelList?.find((labelEle) => labelEle.labelId == ele)?.id ||
                undefined,
        }));

        params.guideList = JSON.stringify(guideList);
        params.labelList = JSON.stringify(labelList);
        params.stationPictureList = JSON.stringify(values.stationPictureList);
        params.stationLabelPictureList = JSON.stringify(values.stationLabelPictureList);
        params.noticeList = noticeInfoFormatter(params.noticeList);

        console.log(params, values);
        // return;
        try {
            updateWaiting(true);
            await saveStationInfoApi(params);
            message.success('提交成功');
            goBack();
        } catch (error) {
        } finally {
            updateWaiting(false);
        }
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {stationEditInfo?.stationName?.length
                        ? `${stationEditInfo.stationName}修改`
                        : route.name}
                </div>
            }
        >
            <Card>
                <Form
                    form={form}
                    onFinish={onFinish}
                    initialValues={{}}
                    scrollToFirstError
                    {...formItemLayout}
                >
                    <Form.Item name="stationId" noStyle />
                    <Form.Item name="stationName" noStyle />

                    <div className={commonStyles['form-title']}>基础信息</div>
                    <BasicView {...props} form={form} disabled={isLock}></BasicView>
                    <Divider></Divider>
                    <div className={commonStyles['form-title']}>停车及附加费</div>
                    <ParkingView
                        {...props}
                        disabled={isLock}
                        parkPriceLabelList={parkPriceLabelList}
                    ></ParkingView>
                    <Divider></Divider>
                    <div className={commonStyles['form-title']}>标签及路书</div>
                    <TagView {...props} form={form} disabled={isLock}></TagView>
                    <Divider></Divider>

                    <div className={commonStyles['form-title']}>其他信息</div>
                    <OtherView {...props} disabled={isLock} form={form}></OtherView>

                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        {(!isLock && (
                            <Button type="primary" htmlType="submit" loading={isWaiting}>
                                提交
                            </Button>
                        )) ||
                            null}
                        <Button onClick={goBack}>取消</Button>
                    </Space>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading }) => ({
    stationManageModel,
    global,
    detailLoading: loading.effects['stationManageModel/getStationInfo'],
}))(StationManageUpdatePage);
