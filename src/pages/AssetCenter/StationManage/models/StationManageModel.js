import {
    getStationList<PERSON><PERSON>,
    getStationManageOptionsApi,
    getPileManageApi,
    getStationBaseDetailApi,
    getStationPriceFeeApi,
    getStationActivityApi,
    getStationParkDelayFeeApi,
    getStationLabelAndGuideApi,
    getStationInfoEditApi,
    getStationGoodFlagNameApi,
    getStationHistoryListApi,
    getStationMonitorStatApi,
    getStationPageApi,
} from '@/services/AssetCenter/StationManangeApi';
import { partTimeStationPictureInfoApi } from '@/services/PartTimeCenter/PartTimeApi';

const StationManageModel = {
    namespace: 'stationManageModel',
    state: {
        stationManageList: [], // 站点管理列表
        stationManageListTotal: 0, // 站点管理列表总条数

        stationManageEnum: [], // 查询枚举
        operationStatusList: [], // 运营状态
        openStatusList: [], // 是否上线
        goodFlagStatusList: [], // 是否精品站
        hiddenStatusList: [], // 是否隐藏
        gunOffLineStatusList: [], // 枪离线选项
        operationWayStatusList: [], // 运营方式

        pileManageList: [], // 电桩管理列表
        pileManageListTotal: 0, // 电桩管理列表总条数

        pileDetail: null,

        stationBaseDetail: undefined, // 基础信息
        stationPriceList: [], // 充电价格
        stationPriceTotal: 0, // 充电价格
        stationActivityList: [], // 站点活动
        stationActivityTotal: 0, // 站点活动
        stationParkingFeeInfo: undefined, // 停车及附加费
        stationLabelAndGuideInfo: undefined, // 标签与路书

        stationEditInfo: undefined,

        stationBestName: '',
        stationRepeatValidTypeList: [],

        stationPriceHistoryList: [],
        stationPriceHistoryTotal: [],

        stationMonitorStat: {}, // 场站监控指标
        partTimeStationPictureInfo: undefined, // 兼职任务站点图片表详情
    },
    effects: {
        /**
         * 站点管理列表
         */
        *getStationManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStationPageApi, options);

                yield put({
                    type: 'updateStationManageList',
                    records,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 查询枚举
        *getStationEnum({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationManageOptionsApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: {
                        ...data,
                        stationManageEnum: data,
                    },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },

        // 基础信息
        *getStationBaseDetail({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationBaseDetailApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationBaseDetail: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 充电价格
        *getStationPriceFee({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStationPriceFeeApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationPriceList: records, stationPriceTotal: total },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 【站点详情】站点活动
        *getStationActivity({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStationActivityApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationActivityList: records, stationActivityTotal: total },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 停车及附加费
        *getStationParkDelayFee({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationParkDelayFeeApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationParkingFeeInfo: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 标签与路书
        *getStationLabelAndGuide({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationLabelAndGuideApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationLabelAndGuideInfo: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 【站点编辑】编辑信息展示
        *getStationInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationInfoEditApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationEditInfo: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 站点电桩列表
         */
        *getPileManageList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getPileManageApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { pileManageList: records, pileManageListTotal: total },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 站点电桩列表
         */
        *getPileManageDetail({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationListApi, options);

                yield put({
                    type: 'updatePileManageDetail',
                    info: data,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },

        *getStationGoodFlagName({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationGoodFlagNameApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationBestName: data || '精品站' },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 枪价格获取历史记录分页列表
        *getStationHistoryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStationHistoryListApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationPriceHistoryList: records, stationPriceHistoryTotal: total },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 场站监控指标
        *getStationMonitorStat({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getStationMonitorStatApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { stationMonitorStat: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        // 兼职任务站点图片表详情
        *getPartTimeStationPictureInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(partTimeStationPictureInfoApi, options);

                yield put({
                    type: 'updateStationProperty',
                    params: { partTimeStationPictureInfo: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateStationProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updateStationManageList(state, { records, total }) {
            return {
                ...state,
                stationManageList: records,
                stationManageListTotal: total,
            };
        },
        updatePileManageDetail(state, { info }) {
            return {
                ...state,
                pileDetail: info,
            };
        },
    },
};
export default StationManageModel;
