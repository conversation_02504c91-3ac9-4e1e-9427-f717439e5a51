import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Modal, message, Space } from 'antd';

import { sendStationPriceMq, validStationPrice } from '@/services/MngAstApi';

const { confirm } = Modal;

export const confirmUpLineEvent = async (stationIds, stationList = []) => {
    return new Promise((resolve, reject) => {
        const allStationName = stationList?.map((ele) => ele.stationName);
        confirm({
            title: '上线失败',
            icon: <ExclamationCircleOutlined style={{ color: 'red' }} />,
            content: (
                <div>
                    <h3>
                        <strong>以下场站价格格式有误或为空，暂无法上线</strong>
                    </h3>
                    <p style={{ maxHeight: '400px', overflowY: 'auto', fontSize: '1.17em' }}>
                        <Space direction="vertical">
                            {allStationName.map((ele, index) => (
                                <span key={index}>{ele}</span>
                            ))}
                        </Space>
                    </p>
                </div>
            ),
            okText: '重新获取价格',
            onOk: async () => {
                try {
                    await sendStationPriceMq({ stationIds: stationIds.join(',') });
                    message.success('发起成功，此过程预计1～2分钟');
                    resolve();
                } catch (error) {}
            },
            onCancel() {
                reject();
            },
        });
    });
};

const findUniqueObjects = (arr1, arr2, key) => {
    return arr1
        .filter((item1) => !arr2.find((item2) => item2[key].toString() === item1[key].toString()))
        .concat(
            arr2.filter(
                (item2) => !arr1.find((item1) => item1[key].toString() === item2[key].toString()),
            ),
        );
};

export const checkStationPriceEvent = async (stationIds = [], selectItems) => {
    try {
        const { data } = await validStationPrice({ stationIds: stationIds.join(',') });
        if (data) {
            if (data instanceof Array && data?.length > 0) {
                if (data?.length === stationIds?.length) {
                    await confirmUpLineEvent(stationIds, data);
                    return await Promise.reject();
                } else {
                    const uniqueObjects = findUniqueObjects(data, selectItems, 'stationId');
                    return { partialStation: data, uniqueObjects: uniqueObjects };
                }
            }
        }
        return;
    } catch (error) {
        return Promise.reject(error);
    }
};
