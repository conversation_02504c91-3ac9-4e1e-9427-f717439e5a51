import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    message,
    Modal,
    Row,
    Anchor,
    Space,
    Descriptions,
    Alert,
    Upload,
    Tag,
    Tooltip,
    Radio,
    Select,
    Tabs,
    Typography,
    List as AntDList,
    Empty,
    Image,
} from 'antd';
import { connect, Link as PageLink } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import {
    LeftOutlined,
    BellOutlined,
    InfoCircleOutlined,
    ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useParams } from 'umi';

import commonStyles from '@/assets/styles/common.less';
import AMapLoader from '@amap/amap-jsapi-loader';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { formatActivePage, isEmpty } from '@/utils/utils';
import WorkOrderHXTool, { WORK_TYPES } from '../../MarketingManage/WorkOrder/WorkOrderHXTool';
import { DuplicateTableColumns, DuplicateSuccessModal } from './components/DuplicateStationModal';
import {
    getDucplicateStationComfirmListApi,
    removeDucplicateStationListApi,
    confirmDucplicateStationListApi,
} from '@/services/AssetCenter/StationDucplicateApi';
import ConfirmModal from '../TimerTaskManage/ConfirmModal';
import DuplicateStationModal from './components/DuplicateStationModal';
import {
    getStationManageOptionsApi,
    getStationPriceApi,
} from '@/services/AssetCenter/StationManangeApi';
import { useImperativeHandle } from 'react';
import { stationUpLine } from '@/services/MngAstApi';
import { DATA_TYPES } from './StationManageUpdatePage';
import { getStationTagTreeApi } from '@/services/AssetCenter/TagManageApi';
import { DingTalkAuditModal } from './components/DingTalkAuditModal';
import { AUDIT_OPER_TYPE } from '@/constants/station';

const { confirm } = Modal;
const { Link } = Anchor;
const { Option } = Select;
const { TabPane } = Tabs;

const TAB_TYPES = {
    DUPLICATE: '01',
    NOT: '02',
};

let map = null;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'left',
};

const BasicInfoView = (props) => {
    const {
        initRef,
        dispatch,
        stationManageModel: { stationBaseDetail },
        history,
    } = props;

    useImperativeHandle(initRef, () => ({
        loadData,
    }));

    const { stationId } = useParams();
    const AMapJs = useRef();
    const [showMapModal, toggleShowMapModal] = useState(false);
    const closeMapModalEvent = () => {
        toggleShowMapModal(false);
    };
    useEffect(() => {
        loadData();
        return () => {
            unMap();
        };
    }, []);
    useEffect(() => {
        if (showMapModal) {
            loadMap();
        }
    }, [showMapModal]);

    const loadData = async () => {
        dispatch({
            type: 'stationManageModel/getStationBaseDetail',
            options: { stationId },
        });
    };

    const loadMap = async () => {
        window._AMapSecurityConfig = {
            securityJsCode: 'f1d96ff30ff169db2e5bf7c5f683113f',
        };
        return new Promise((resolve, reject) => {
            AMapLoader.load({
                key: 'f0ad238a3a4b229bcdd93c77278f6148', //需要设置您申请的key
                version: '2.0',
                plugins: ['AMap.Scale', 'AMap.ToolBar'],
                AMapUI: {
                    version: '1.1',
                    plugins: ['overlay/SimpleMarker'],
                },
                Loca: {
                    version: '2.0.0',
                },
            })
                .then((AMap) => {
                    AMapJs.current = AMap;

                    initMap();
                    resolve(AMap);
                })
                .catch((e) => {
                    reject(e);
                });
        });
    };

    const unMap = () => {
        if (map) {
            map.destroy();
            map = null;
        }
    };

    const initMap = () => {
        if (map) {
            map.destroy();
            map = null;
        }

        map = new AMapJs.current.Map('stationMap', {
            zoom: 13, // 级别
            zooms: [8, 15],
            // center: [100.4438444366, 37.9652031933], // 中心点坐标
            // pitch:45,
            resizeEnable: true,
            zoomEnable: true,

            // layers: [
            //   new AMapJs.current.TileLayer.RoadNet({
            //     zIndex: 20
            //   }),
            //   new AMapJs.current.TileLayer({
            //     zIndex: 6,
            //     opacity: 1,
            //     getTileUrl: 'https://t{1,2,3,4}.tianditu.gov.cn/DataServer?T=ter_w&x=[x]&y=[y]&l=[z]'
            //   })
            // ]
        });

        AMapJs.current.plugin(['AMap.ToolBar', 'AMap.Scale'], () => {
            // 在图面添加工具条控件，工具条控件集成了缩放、平移、定位等功能按钮在内的组合控件
            map.addControl(new AMapJs.current.ToolBar());

            // 在图面添加比例尺控件，展示地图在当前层级和纬度下的比例尺
            map.addControl(new AMapJs.current.Scale());
        });

        setMapCenter({
            lon: stationBaseDetail?.lon,
            lat: stationBaseDetail?.lat,
        });
    };

    const setMapCenter = (locationInfo) => {
        map.clearMap();
        const info = [locationInfo.lon, locationInfo.lat];
        map.setCenter(info);
        const marker = new AMapJs.current.Marker({
            icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
            position: info,
            offset: new AMapJs.current.Pixel(-13, -30),
        });
        marker.setMap(map);
    };

    const pileParams = useMemo(() => {
        const record = { ...stationBaseDetail, stationId };
        delete record.pictureList;
        const keys = Object.keys(record);
        const values = keys.map((ele) => record[ele] && `${ele}=${record[ele]}`);
        const params = values.filter((ele) => ele?.length > 0);
        return params;
    }, [stationId, stationBaseDetail]);

    return (
        <Card
            bordered={false}
            title={
                <div className={commonStyles['form-title']} id="basicInfo">
                    基础信息
                </div>
            }
        >
            <Descriptions>
                <Descriptions.Item label="运营商">
                    {stationBaseDetail?.operatorName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label={`合作平台`}>
                    {stationBaseDetail?.cooperationPlatformName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label={`内部底层`}>
                    {stationBaseDetail?.internalChannelFlagName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label={`场站ID*`}>
                    {stationBaseDetail?.stationId || '-'}
                </Descriptions.Item>
                <Descriptions.Item label={`场站名称*`}>
                    {stationBaseDetail?.stationName || '-'}
                </Descriptions.Item>

                <Descriptions.Item label={`场站编号*`}>
                    {stationBaseDetail?.stationNo || '-'}
                </Descriptions.Item>

                <Descriptions.Item label={`所在地区*`}>
                    <Space>
                        {stationBaseDetail?.stationArea || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.AREA_KEY] === '1' && <Tag>不同步</Tag>) ||
                            null}
                    </Space>
                </Descriptions.Item>

                <Descriptions.Item label="营业时间*">
                    <Space>
                        {stationBaseDetail?.busiTime || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.BUSI_TIME_KEY] === '1' && (
                            <Tag>不同步</Tag>
                        )) ||
                            null}
                    </Space>
                </Descriptions.Item>

                <Descriptions.Item label={`场站电话*`}>
                    <Space>
                        {stationBaseDetail?.stationTel || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.PHONE_KEY] === '1' && <Tag>不同步</Tag>) ||
                            null}
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label={`服务电话*`}>
                    {stationBaseDetail?.serviceTel || '-'}
                </Descriptions.Item>

                <Descriptions.Item label={`场地属性*`}>
                    {stationBaseDetail?.constructionName || '-'}
                </Descriptions.Item>

                <Descriptions.Item label={`场站类型*`}>
                    <Space>
                        {stationBaseDetail?.stationTypeName || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.STATION_TYPE_KEY] === '1' && (
                            <Tag>不同步</Tag>
                        )) ||
                            null}
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="适用车型描述*">
                    <Space>
                        {stationBaseDetail?.applyCarModelDesc || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.VECHICLE_KEY] === '1' && (
                            <Tag>不同步</Tag>
                        )) ||
                            null}
                    </Space>
                </Descriptions.Item>

                <Descriptions.Item label="交/直流桩数*">
                    {(stationBaseDetail && (
                        <PageLink
                            to={`/assetCenter/pileManage/list?${pileParams?.join?.('&')}`}
                            key="detail"
                            target={'_blank'}
                        >
                            {`${
                                stationBaseDetail.acEquipNum === undefined
                                    ? '-'
                                    : stationBaseDetail.acEquipNum
                            } / ${
                                stationBaseDetail.dcEquipNum === undefined
                                    ? '-'
                                    : stationBaseDetail.dcEquipNum
                            }`}
                        </PageLink>
                    )) ||
                        '-'}
                </Descriptions.Item>

                <Descriptions.Item label="充电枪数*">
                    {stationBaseDetail?.gunNum || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="是否有地锁">
                    {stationBaseDetail?.floorLockFlagName || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="位置引导*">
                    {stationBaseDetail?.siteGuide || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="详细地址描述*" span={2}>
                    <Space>
                        {stationBaseDetail?.stationAddress || '-'}
                        {(stationBaseDetail?.[DATA_TYPES.ADDRESS_KEY] === '1' && (
                            <Tag>不同步</Tag>
                        )) ||
                            null}
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="场站地图*" span={3}>
                    <Space>
                        <a
                            href="javascript:void(0)"
                            onClick={() => {
                                toggleShowMapModal(true);
                            }}
                        >
                            点击查看
                        </a>
                        {(stationBaseDetail?.[DATA_TYPES.MAP_KEY] === '1' && <Tag>不同步</Tag>) ||
                            null}
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="场站图片" span={3}>
                    <Image.PreviewGroup>
                        {stationBaseDetail?.stationPictureList?.map((item, index) => (
                            <div
                                key={index}
                                style={{
                                    width: '60px',
                                    height: '60px',
                                    lineHeight: '60px',
                                    display: 'inline-block',
                                    marginRight: '10px',
                                    border: '1px solid #d9d9d9',
                                }}
                            >
                                <Image width={'100%'} height={'60px'} src={item.url} />
                            </div>
                        ))}
                    </Image.PreviewGroup>
                </Descriptions.Item>
                <Descriptions.Item label="场站图片(互联互通)" span={3}>
                    <Image.PreviewGroup>
                        {stationBaseDetail?.pictureList?.map((item, index) => (
                            <div
                                key={index}
                                style={{
                                    width: '60px',
                                    height: '60px',
                                    lineHeight: '60px',
                                    display: 'inline-block',
                                    marginRight: '10px',
                                    border: '1px solid #d9d9d9',
                                }}
                            >
                                <Image width={'100%'} height={'60px'} src={item} />
                            </div>
                        ))}
                    </Image.PreviewGroup>
                </Descriptions.Item>

                <Descriptions.Item label="创建时间">
                    {stationBaseDetail?.firstTime || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="首次上线时间">
                    {stationBaseDetail?.firstOpenTime || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="最新上线时间">
                    {stationBaseDetail?.newOpenTime || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="最后互通时间">
                    {stationBaseDetail?.newUpdateTime || '-'}
                </Descriptions.Item>

                <Descriptions.Item label="下线时间">
                    {stationBaseDetail?.newCloseTime || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="废弃原因">
                    {(stationBaseDetail?.abandonReason == 8 &&
                        stationBaseDetail?.abandonDesc?.length &&
                        (stationBaseDetail?.abandonDesc || '-')) ||
                        stationBaseDetail?.abandonReasonName ||
                        '-'}
                </Descriptions.Item>
                <Descriptions.Item label="下线原因">
                    {stationBaseDetail?.downReasonName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="暂停开放时间">
                    {stationBaseDetail?.tempCloseTime || '-'}
                </Descriptions.Item>
            </Descriptions>
            <Modal
                title={'场站地图'}
                width={800}
                visible={showMapModal}
                onCancel={closeMapModalEvent}
                footer={null}
                maskClosable={false}
            >
                <div id="stationMap" style={{ width: '100%', height: '500px' }}></div>
                <Descriptions>
                    <Descriptions.Item label="经度">
                        {stationBaseDetail?.lon || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="纬度">
                        {stationBaseDetail?.lat || '-'}
                    </Descriptions.Item>
                </Descriptions>
            </Modal>
        </Card>
    );
};

export const StationPriceColumns = (containPlat = false) => [
    {
        title: '时段',
        width: 140,
        dataIndex: 'timeInterval',
        render(text, record) {
            return (
                <Space className="text-line" title={text}>
                    {text || '-'}
                    {(record.showFlag && <Tag color="red">当前时段</Tag>) || null}
                </Space>
            );
        },
    },

    {
        title: '电费（元/度）',
        children: [
            ...((containPlat && [
                {
                    title: '平台价*',
                    dataIndex: 'platChargePrice',
                    width: 120,
                    render(text, record) {
                        return <span title={text}>{text || '-'}</span>;
                    },
                },
            ]) ||
                []),

            {
                title: '商家原价*',
                width: 120,
                dataIndex: 'originalElectricityFee',
                render(text, record) {
                    if (!text && record) {
                        text = record.chargePrice;
                    }
                    return <span title={text}> {text || '-'}</span>;
                },
            },
            {
                title: '商家结算价*',
                width: 120,
                dataIndex: 'electricityFee',
                render(text, record) {
                    if (!text && record) {
                        text = record.discountChargePrice;
                    }
                    return <span title={text}> {text || '-'}</span>;
                },
            },
        ],
    },
    {
        title: '服务费（元/度）',
        children: [
            ...((containPlat && [
                {
                    title: '平台价*',
                    dataIndex: 'platServicePrice',
                    width: 120,
                    render(text, record) {
                        return <span title={text}>{text || '-'}</span>;
                    },
                },
            ]) ||
                []),
            {
                title: '商家原价*',
                width: 120,
                dataIndex: 'originalServiceFee',
                render(text, record) {
                    if (!text && record) {
                        text = record.servicePrice;
                    }
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '商家结算价*',
                width: 120,
                dataIndex: 'serviceFee',
                render(text, record) {
                    if (!text && record) {
                        text = record.discountServicePrice;
                    }
                    return <span title={text}> {text || '-'}</span>;
                },
            },
        ],
    },
    {
        title: '总价（元/度）',
        children: [
            ...((containPlat && [
                {
                    title: '平台价*',
                    dataIndex: 'platTotalPrice',
                    width: 120,
                    render(text, record) {
                        return <span title={text}>{text || '-'}</span>;
                    },
                },
            ]) ||
                []),
            {
                title: '商家原价*',
                width: 120,
                dataIndex: 'originalTotalPrice',
                render(text, record) {
                    return <span title={text}> {text || '-'}</span>;
                },
            },
            {
                title: '商家结算价*',
                width: 120,
                dataIndex: 'totalPrice',
                render(text, record) {
                    if (!text && record) {
                        text = record.discountTotalPrice;
                    }
                    return <span title={text}> {text || '-'}</span>;
                },
            },
        ],
    },
];

const PriceView = (props) => {
    const {
        stationManageModel: { stationBaseDetail },
        detailLoading,
    } = props;

    const { stationId } = useParams();

    const [stationPriceType, updateStationPriceType] = useState();
    const [stationPriceInfo, updateStationPriceInfo] = useState();

    const stationPriceList = useMemo(() => {
        let list = [];
        if (stationPriceInfo) {
            const findTabItem = stationPriceInfo.find(
                (ele) => ele.chargingName === stationPriceType,
            );
            if (findTabItem && findTabItem.stationEquipPriceVo instanceof Array) {
                list = findTabItem.stationEquipPriceVo;
            }
        }
        return list;
    }, [stationPriceType, stationPriceInfo]);

    const priceTypeTabs = useMemo(() => {
        if (stationPriceInfo instanceof Array) {
            return stationPriceInfo.map((ele) => {
                return <TabPane tab={ele.chargingName} key={ele.chargingName}></TabPane>;
            });
        }
        return [];
    }, [stationPriceInfo]);

    useEffect(() => {
        searchData();
    }, []);

    const searchData = async () => {
        try {
            const { data } = await getStationPriceApi({ stationId });
            updateStationPriceInfo(data);
            if (data && data[0]) {
                updateStationPriceType(data[0].chargingName);
            }
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Card
            bordered={false}
            title={
                <div className={commonStyles['form-title']} id="price">
                    <Tooltip title={'小程序价格展示,同一时段若快充或慢充有多个价格，则取最低价格'}>
                        充电价格
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </div>
            }
            extra={
                <PageLink
                    to={`/assetCenter/stationManage/list/history-price?stationId=${stationId}&stationName=${stationBaseDetail?.stationName}`}
                    key="price"
                    target={'_blank'}
                >
                    <Button>历史价格查询</Button>
                </PageLink>
            }
        >
            <Tabs
                defaultActiveKey={stationPriceType}
                onChange={(chargingName) => {
                    updateStationPriceType(chargingName);
                }}
            >
                {priceTypeTabs}
            </Tabs>
            <TablePro
                name="price"
                loading={detailLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={stationPriceList}
                columns={StationPriceColumns(true)}
                pagination={false}
                noSort
                rowClassName={(record, index) => {
                    return (record.showFlag && commonStyles['bg_red']) || '';
                }}
                offsetHeader={46}
                bordered
            />
        </Card>
    );
};

const ActInfoView = (props) => {
    const {
        dispatch,
        stationManageModel: { stationActivityList, stationActivityTotal },
        detailLoading,
    } = props;

    const [pageInfo, changePageInfo, onTableChange] = usePageState(() => ({}));

    const { stationId } = useParams();
    const [tabTypeList, changeTabTypeList] = useState([]);
    const [curTabType, changeCurTabType] = useState();

    useEffect(() => {
        searchData();
    }, []);

    const searchData = () => {
        dispatch({
            type: 'stationManageModel/getStationActivity',
            options: { stationId },
        });
    };

    const tabTypeOptions = useMemo(() => {
        if (tabTypeList instanceof Array) {
            return tabTypeList.map((ele) => {
                return <TabPane tab="重复场站" key={TAB_TYPES.DUPLICATE} />;
            });
        }
        return [];
    }, [tabTypeList]);

    const columns = [
        {
            title: '活动类型',
            dataIndex: 'actTypeName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '活动时间',
            dataIndex: 'actTimePeriod',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '活动名称',
            dataIndex: 'actName',
            render(text, record) {
                let path = formatActivePage({
                    type: String(record.actType),
                    actId: record.actId,
                    belongType: record?.belongType,
                });
                if (path) {
                    return (
                        <PageLink to={path} target="_blank">
                            {text}
                        </PageLink>
                    );
                }
                return <span title={text}> {text || '-'}</span>;
            },
        },
    ];
    return (
        <Card
            title={
                <div className={commonStyles['form-title']} id="actInfo">
                    活动信息
                </div>
            }
            bordered={false}
        >
            <Tabs
                defaultActiveKey={curTabType}
                onChange={(key) => {
                    changeCurTabType(key);
                }}
            >
                {tabTypeOptions}
            </Tabs>
            <TablePro
                name="act"
                loading={detailLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.id}
                dataSource={stationActivityList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: stationActivityTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                noSort
            />
        </Card>
    );
};

const PackingView = (props) => {
    const {
        dispatch,
        history,
        stationManageModel: { stationParkingFeeInfo, stationBaseDetail },
        detailLoading,
    } = props;

    const { stationId } = useParams();

    useEffect(() => {
        searchData();
    }, []);

    const searchData = () => {
        dispatch({
            type: 'stationManageModel/getStationParkDelayFee',
            options: { stationId },
        });
    };
    return (
        <Card
            title={
                <div className={commonStyles['form-title']} id="parking">
                    停车及附加费
                </div>
            }
            bordered={false}
        >
            <Descriptions>
                <Descriptions.Item label="停车费说明*" span={3}>
                    {stationParkingFeeInfo?.parkPrice || ''}
                </Descriptions.Item>
                <Descriptions.Item label="停车费说明(外显)" span={3}>
                    {stationParkingFeeInfo?.parkPriceDesc || ''}
                </Descriptions.Item>
                <Descriptions.Item label="停车标签">
                    {stationBaseDetail?.parkPriceLabelDisplayName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="是否录入车牌">
                    {stationParkingFeeInfo?.forceCarNumName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="车位数量*">
                    {stationParkingFeeInfo?.parkNum || ''}
                </Descriptions.Item>
                <Descriptions.Item label="车位描述*">
                    {stationParkingFeeInfo?.parkDesc || ''}
                </Descriptions.Item>
                <Descriptions.Item label="占位开关" span={3}>
                    {stationParkingFeeInfo?.incrementSwitchName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="收取附加费*" span={3}>
                    {stationParkingFeeInfo?.delayFeeFlagName || ''}
                </Descriptions.Item>
                <Descriptions.Item label="附加费说明*" span={3}>
                    {stationParkingFeeInfo?.incrementExplain || ''}
                </Descriptions.Item>

                {/* <Descriptions.Item
                    label={
                        <Tooltip title="含短信、支付宝通知">
                            占位费通知
                            <InfoCircleOutlined style={{ marginLeft: '10px' }} />
                        </Tooltip>
                    }
                    span={3}
                >
                    {stationParkingFeeInfo?.delayFeeFlagName || ''}
                </Descriptions.Item> */}
            </Descriptions>
        </Card>
    );
};

const TagInfoView = (props) => {
    const {
        dispatch,
        history,
        global: { codeInfo = {} },
        stationManageModel: { stationLabelAndGuideInfo, stationBestName, stationBaseDetail },
        detailLoading,
    } = props;
    const [selectList, updateSelectList] = useState([]);
    useEffect(() => {
        initTreeData();
    }, []);

    const initTreeData = async () => {
        try {
            const {
                data: { list },
            } = await getStationTagTreeApi();

            updateSelectList(list);
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const { stationLabelPicture } = codeInfo;
    useEffect(() => {
        if (!stationLabelPicture) {
            dispatch({
                type: 'global/initCode',
                code: 'stationLabelPicture',
            });
        }
    }, []);
    const { stationId } = useParams();

    useEffect(() => {
        searchData();
    }, []);

    const searchData = () => {
        dispatch({
            type: 'stationManageModel/getStationLabelAndGuide',
            options: { stationId },
        });
    };

    return (
        <Card
            title={
                <div className={commonStyles['form-title']} id="tagInfo">
                    标签及路书
                </div>
            }
            bordered={false}
        >
            <Descriptions>
                <Descriptions.Item label="场站标签">
                    {stationLabelAndGuideInfo?.stationLabel || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="场站标签（互联互通）">
                    {stationBaseDetail?.labelHlhtList?.map((ele) => ele.labelName)?.join('、') ||
                        '-'}
                </Descriptions.Item>
                <Descriptions.Item label={`是否${stationBestName}`}>
                    {stationLabelAndGuideInfo?.goodFlagName || '-'}
                </Descriptions.Item>
            </Descriptions>

            <Tabs size="small">
                <TabPane tab="路书" key={'guide-01'}>
                    <Descriptions title="" column={3} key="label">
                        {stationBaseDetail?.stationLabelPictureList?.map((ele) => {
                            let labelFormat = '';
                            selectList?.forEach((item) => {
                                if (item.id === parseInt(ele.labelId)) {
                                    labelFormat = item.name;
                                }
                            });
                            return (
                                <Descriptions.Item label={labelFormat} key={ele.labelId}>
                                    <Upload
                                        listType="picture-card"
                                        fileList={[{ url: ele.url }]}
                                        disabled
                                    />
                                </Descriptions.Item>
                            );
                        })}
                    </Descriptions>
                    {(stationLabelAndGuideInfo?.guideList?.length &&
                        stationLabelAndGuideInfo?.guideList?.map((ele) => (
                            <Descriptions
                                title={`${ele.titleTypeName}${
                                    (ele.title?.length && `：${ele.title}`) || ''
                                }`}
                                column={2}
                                key="guide"
                            >
                                {(ele?.guidePictureList?.length && (
                                    <Descriptions.Item label="图片">
                                        <Upload
                                            listType="picture-card"
                                            fileList={ele?.guidePictureList?.map?.((ele) => ({
                                                url: ele,
                                            }))}
                                            disabled
                                        />
                                    </Descriptions.Item>
                                )) ||
                                    null}
                                <Descriptions.Item label="描述">
                                    {ele?.guideDesc || '-'}
                                </Descriptions.Item>
                            </Descriptions>
                        ))) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
                </TabPane>
                <TabPane tab="路书（互联互通）" key={'guide-02'}>
                    <AntDList
                        style={{ width: '100%' }}
                        dataSource={stationBaseDetail?.guideHlhtList}
                        renderItem={(item, i) => {
                            return (
                                <Descriptions column={2} key="guide">
                                    {(item?.guidePictureList?.length && (
                                        <Descriptions.Item label="图片">
                                            <Upload
                                                listType="picture-card"
                                                fileList={item?.guidePictureList?.map?.((ele) => ({
                                                    url: ele.url,
                                                }))}
                                                disabled
                                            />
                                        </Descriptions.Item>
                                    )) ||
                                        null}
                                    <Descriptions.Item label="描述">
                                        {item?.guideDesc || '-'}
                                    </Descriptions.Item>
                                </Descriptions>
                            );
                        }}
                    />
                </TabPane>
            </Tabs>
        </Card>
    );
};

const DuplicateModal = (props) => {
    const { initRef, stationId, onFinish, lineEvent } = props;

    const [visible, updateVisible] = useState(false);
    const [confirmStationId, updateStationId] = useState();
    const [form] = Form.useForm();
    const successRef = useRef();
    useImperativeHandle(initRef, () => ({
        show: (id) => {
            updateVisible(true);
            updateStationId(id);
            form?.resetFields();
            initEnum();
        },
    }));

    const onClose = () => {
        updateVisible(false);
        updateStationId(undefined);
    };

    const [stationRepeatReasonList, updateStationRepeatReasonList] = useState([]);
    const initEnum = async () => {
        if (!stationRepeatReasonList?.length) {
            try {
                const {
                    data: { stationRepeatReasonList: _stationRepeatReasonList },
                } = await getStationManageOptionsApi();
                updateStationRepeatReasonList(_stationRepeatReasonList);
            } catch (error) {}
        }
    };

    const [isLoading, updateLoading] = useState(false);
    const [inUseStations, updateInUseStations] = useState([]);
    return (
        <Fragment>
            <Modal
                title="设为重复站"
                visible={visible}
                okButtonProps={{ loading: isLoading }}
                onOk={() => {
                    form.validateFields().then(async (value) => {
                        /* 传参字段逻辑
                    confirmStation（确认站）：取非当前场站分组的其他组别的所有stationId
                    inUseFlag（是否在用）：勾选了哪个，就传勾选的stationId
                    */
                        updateLoading(true);
                        try {
                            const params = {
                                ...value,
                                mainStationId: stationId,
                                confirmStation: confirmStationId,
                            };
                            // console.log(params);
                            // return;
                            const {
                                data: {
                                    data: { scene, message: msg, stationId: stationIds },
                                },
                            } = await confirmDucplicateStationListApi(params);
                            if (stationIds?.length) {
                                updateInUseStations(stationIds.split(','));
                            } else {
                                updateInUseStations([]);
                            }
                            switch (scene) {
                                case '200':
                                    // 操作成功
                                    message.success(msg);
                                    onFinish?.();
                                    onClose();
                                    break;
                                case '202':
                                    // 在用站存在未通过校验，暂无法上线
                                    successRef.current.show(false);
                                    onFinish?.();
                                    onClose();
                                    break;
                                case '203':
                                    // 在用站未上线，是否立即上线
                                    successRef.current.show(true);
                                    onClose();
                                    break;
                                case '105':
                                    // 重复站逻辑处理异常
                                    message.error(msg);
                                    break;
                                default:
                                    message.error(msg);
                                    break;
                            }
                        } catch (error) {
                        } finally {
                            updateLoading(false);
                        }
                    });
                }}
                onCancel={onClose}
            >
                <Form form={form}>
                    <Form.Item label="是否在用" required name={'inUseFlag'} initialValue={'0'}>
                        <Radio.Group>
                            <Radio value={'0'}>否</Radio>
                            <Radio value={'1'}>是</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label="重复原因"
                        name="repeatReason"
                        rules={[{ required: true, message: '请选择重复原因' }]}
                        initialValue={'4'}
                    >
                        <Select placeholder="请选择">
                            {stationRepeatReasonList?.map((ele, index) => (
                                <Option value={ele.codeValue} key={index}>
                                    {ele.codeName}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                </Form>
            </Modal>

            <DuplicateSuccessModal
                initRef={successRef}
                lineEvent={() => {
                    lineEvent(inUseStations);
                }}
            />
        </Fragment>
    );
};

const OtherInfoView = (props) => {
    const {
        initRef,
        stationManageModel: { stationBaseDetail },
    } = props;

    const auditModal = useRef();
    useImperativeHandle(initRef, () => ({
        loadData,
    }));

    const { stationId } = useParams();

    const [curTab, changeTabEvent] = useState(TAB_TYPES.DUPLICATE);
    const duplicateRef = useRef();
    const [listLoading, updateListLoading] = useState(false);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props);
    useEffect(() => {
        loadData();
    }, [pageInfo, curTab]);

    const resetData = () => {
        changePageInfo({ pageIndex: 1 });
    };

    // 调用搜索接口
    const [stationManageList, updateStationManageList] = useState([]);
    const loadData = async () => {
        updateListLoading(true);
        try {
            let repeatFlag;
            switch (curTab) {
                case TAB_TYPES.DUPLICATE:
                    repeatFlag = '1';
                    break;
                case TAB_TYPES.NOT:
                    repeatFlag = '0';
                    break;

                default:
                    break;
            }
            const {
                data: { data },
            } = await getDucplicateStationComfirmListApi({
                stationId,
                repeatFlag,
            });
            // const list = DuplicateSeparteFunction(data);
            updateStationManageList(data);
        } catch (error) {
            updateStationManageList([]);
        } finally {
            updateListLoading(false);
        }
    };

    // 场站上线
    const [confirmUpVisible, toggleConfirmUpModal] = useState(false);
    const columns = useMemo(() => {
        return [
            ...(DuplicateTableColumns || []),
            {
                title: '操作',
                width: 120,
                fixed: 'right',
                render(text, record) {
                    if (record.currentStation) {
                        return null;
                    }
                    if (curTab == TAB_TYPES.DUPLICATE) {
                        return (
                            <a
                                onClick={() => {
                                    confirm({
                                        title: '移除场站',
                                        icon: <ExclamationCircleOutlined />,
                                        content:
                                            '执行后，该重复站将解除关联关系，并且不会再与主站检验重复',
                                        onOk: async () => {
                                            try {
                                                const {
                                                    data: {
                                                        data: { scene, message: msg },
                                                    },
                                                } = await removeDucplicateStationListApi({
                                                    mainStationId: stationId,
                                                    removeStation: record.stationId,
                                                });
                                                if (scene == '113') {
                                                    message.error(msg);
                                                } else {
                                                    message.success('操作成功');
                                                    resetData();
                                                }
                                            } catch (error) {}
                                        },
                                    });
                                }}
                            >
                                移除
                            </a>
                        );
                    }
                    if (curTab == TAB_TYPES.NOT) {
                        return (
                            <a
                                onClick={() => {
                                    duplicateRef.current.show(record.stationId);
                                }}
                            >
                                设为重复站
                            </a>
                        );
                    }
                },
            },
        ];
    }, [curTab]);

    const [inUseStations, updateInUseStations] = useState([]);

    return (
        <Card
            title={
                <div className={commonStyles['form-title']} id="otherInfo">
                    其他信息
                </div>
            }
            bordered={false}
        >
            <Descriptions>
                <Descriptions.Item label="三方渠道">
                    <Typography.Text
                        ellipsis={{ rows: 1, tooltip: true }}
                        style={{ width: 190, flex: 1 }}
                    >
                        {stationBaseDetail?.channelName || '-'}
                    </Typography.Text>
                </Descriptions.Item>
                <Descriptions.Item label="站点加权">
                    {stationBaseDetail?.stationWeight || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="支付方式*">
                    {stationBaseDetail?.payment || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="计价标准*" span={3}>
                    {stationBaseDetail?.pricingStandard || '-'}
                </Descriptions.Item>

                {stationBaseDetail?.noticeList?.map((ele, index) => {
                    return (
                        <>
                            <Descriptions.Item label={`公告信息${index + 1}`} span={2}>
                                {ele?.noticeInfo || '-'}
                            </Descriptions.Item>
                            <Descriptions.Item label="生效时间">
                                {(ele?.always == '0' &&
                                    `${ele?.effTime || '-'}至${ele?.expTime || '-'}`) ||
                                    '永久有效'}
                            </Descriptions.Item>
                        </>
                    );
                })}
                <Descriptions.Item label="贴码状态">
                    {stationBaseDetail?.codeStatusName || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="贴码情况说明" span={3}>
                    {stationBaseDetail?.codeDescription || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="备注*" span={3}>
                    {stationBaseDetail?.remark || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="备注（本地）" span={3}>
                    {stationBaseDetail?.localRemark || '-'}
                </Descriptions.Item>
            </Descriptions>

            <Tabs
                defaultActiveKey={TAB_TYPES.DUPLICATE}
                onChange={(key) => {
                    changeTabEvent(key);
                }}
            >
                <TabPane tab="重复场站" key={TAB_TYPES.DUPLICATE} />
                <TabPane tab="不重复场站" key={TAB_TYPES.NOT} />
            </Tabs>
            <TablePro
                name="confirm"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.actId}
                dataSource={stationManageList}
                columns={columns}
                onChange={onTableChange}
                offsetHeader={false}
            />

            {/* 场站上线确认弹窗 */}
            <ConfirmModal
                confirmVisible={confirmUpVisible}
                stationInfoList={
                    confirmUpVisible &&
                    inUseStations?.length &&
                    stationManageList?.length &&
                    JSON.stringify(
                        stationManageList.filter((ele) =>
                            inUseStations.some((useEleIds) => useEleIds == ele.stationId),
                        ),
                    )
                }
                onFinish={async (validProfitRuleStations) => {
                    toggleConfirmUpModal(false);
                    try {
                        const stationIds = validProfitRuleStations
                            ?.filter((v) => v?.hasRule)
                            ?.map((v) => v?.stationId);
                        if (isEmpty(stationIds)) {
                            message.error('没有可上线场站');
                            return;
                        }
                        const params = {
                            stationIds: stationIds?.join?.(',') || stationIds,
                        };
                        const { data } = await stationUpLine(params);
                        if (data) {
                            const handle = {
                                request: stationUpLine,
                                params: params,
                            };
                            auditModal.current.show({
                                ...data,
                                type: AUDIT_OPER_TYPE.ONLINE,
                                handle,
                            });
                        } else {
                            message.success('操作成功，请到审批记录或钉钉查看审批进度');
                        }
                        loadData();
                    } catch (error) {}
                }}
                onClose={() => {
                    loadData();
                    toggleConfirmUpModal(false);
                }}
            />

            <DuplicateModal
                initRef={duplicateRef}
                stationId={stationId}
                lineEvent={(ids) => {
                    toggleConfirmUpModal(true);
                    updateInUseStations(ids);
                }}
                onFinish={() => {
                    loadData();
                }}
            />

            <DingTalkAuditModal initRef={auditModal} />
        </Card>
    );
};

const StationManageDetailPage = (props) => {
    const {
        dispatch,
        history,
        route,
        stationManageModel: { stationBaseDetail, stationBestName },
        detailLoading,
        user,
        global,
    } = props;

    const { stationId } = useParams();
    const baseInfoRef = useRef();
    const otherRef = useRef();
    const duplicateRef = useRef();
    useEffect(() => {
        if (!stationBestName?.length) {
            dispatch({
                type: 'stationManageModel/getStationGoodFlagName',
                options: {},
            });
        }
    }, []);

    const goBack = () => {
        history.replace('/assetCenter/stationManage/list');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {(stationBaseDetail?.stationName && `${stationBaseDetail.stationName}详情`) ||
                        route.name}
                </div>
            }
            content={
                <Fragment>
                    <Alert
                        type="info"
                        message={
                            <div>
                                <BellOutlined style={{ marginRight: '6px' }} />
                                带* 为互联互通数据
                            </div>
                        }
                    ></Alert>
                    <br></br>
                    <Descriptions>
                        <Descriptions.Item label="运营状态*">
                            {stationBaseDetail?.busiStatusName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`是否上线`}>
                            {stationBaseDetail?.openFlagName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label={`是否隐藏`}>
                            {stationBaseDetail?.hiddenFlagName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="暂停开放">
                            {stationBaseDetail?.tempCloseName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={`价格基准`}>
                            {stationBaseDetail?.operDiscountName || '-'}
                        </Descriptions.Item>

                        <Descriptions.Item label="运营方式*">
                            {stationBaseDetail?.operatorWayName || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label="疑似重复">
                            {(stationBaseDetail?.validResult?.length && (
                                <a
                                    onClick={() => {
                                        duplicateRef.current.show(stationId);
                                    }}
                                >
                                    {stationBaseDetail.validResult}
                                </a>
                            )) ||
                                '无'}
                        </Descriptions.Item>
                        <Descriptions.Item label="数据校验">
                            {(stationBaseDetail?.problemTypeName?.length && (
                                <span>{stationBaseDetail?.problemTypeName}</span>
                            )) ||
                                '正常'}
                        </Descriptions.Item>
                    </Descriptions>
                </Fragment>
            }
            extra={
                stationBaseDetail && (
                    <WorkOrderHXTool
                        inParams={stationBaseDetail}
                        dispatch={dispatch}
                        user={user}
                        global={global}
                        type={WORK_TYPES.STATION}
                    />
                )
            }
        >
            <Row wrap={false}>
                <Col flex="1">
                    <BasicInfoView {...props} initRef={baseInfoRef}></BasicInfoView>
                    <br></br>
                    <PriceView {...props}></PriceView>

                    <br></br>
                    <ActInfoView {...props}></ActInfoView>

                    <br></br>

                    <PackingView {...props}></PackingView>

                    <br></br>

                    <TagInfoView {...props}> </TagInfoView>

                    <br></br>

                    <OtherInfoView {...props} initRef={otherRef}></OtherInfoView>
                </Col>
                <Col flex="0 0 auto" style={{ backgroundColor: '#fff' }}>
                    <Anchor
                        style={{
                            padding: '30px 20px',
                            backgroundColor: 'transparent',
                            fontSize: '16px',
                            fontWeight: 'bold',
                        }}
                        bounds={300}
                        offsetTop={60}
                    >
                        <Link href="#basicInfo" title={<span>基础信息</span>} />
                        <br></br>
                        <Link href="#price" title={<span>充电价格</span>} />
                        <br></br>

                        <Link href="#actInfo" title={<span>活动信息</span>} />
                        <br></br>

                        <Link href="#parking" title={<span>停车及附加费</span>} />
                        <br></br>

                        <Link href="#tagInfo" title={<span>标签及路书</span>} />
                        <br></br>

                        <Link href="#otherInfo" title={<span>其他信息</span>} />
                    </Anchor>
                </Col>
            </Row>

            <DuplicateStationModal
                initRef={duplicateRef}
                onFinish={() => {
                    baseInfoRef.current?.loadData();
                    otherRef.current?.loadData();
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ stationManageModel, global, loading, user }) => ({
    stationManageModel,
    global,
    detailLoading: loading.effects['stationManageModel/getPileManageDetail'],
    user,
}))(StationManageDetailPage);
