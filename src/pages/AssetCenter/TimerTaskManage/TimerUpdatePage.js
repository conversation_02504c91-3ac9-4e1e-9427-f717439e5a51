/**
 * 定时管理-新增、修改、查看 页
 */
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useCallback,
    useImperativeHandle,
} from 'react';
import { connect } from 'umi';
import moment from 'moment';
import commonStyles from '@/assets/styles/common.less';
import styles from './TimerUpdatePage.less';
import {
    timerTaskSavaApi,
    timerTaskUpdateApi,
    timerTaskValidTimeApi,
    timerTaskValidAreaInfoApi,
} from '@/services/AssetCenter/TimerTaskApi';
import {
    Button,
    Col,
    Card,
    Form,
    message,
    Modal,
    InputNumber,
    Row,
    Checkbox,
    Space,
    Radio,
    Tooltip,
    Input,
    DatePicker,
    TimePicker,
    Table,
    Select,
    List,
} from 'antd';

import { LeftOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import OperStationSearchList from '@/components/OperStationSearchList/SearchList';
import { MODELTYPS, ADJUST_TYPS } from './TimerTaskConfig';
import { SELECT_TYPES } from '@/config/declare';
import ConfirmModal from './ConfirmModal';
import ImplementationItem from './ImplementationItem';
import { isEmpty } from '@/utils/utils';

import { checkStationPriceEvent } from '../StationManage/checkStationFunctions';

const { Option } = Select;

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'left',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const weekOptions = [
    { label: '每周一', value: 1 },
    { label: '每周二', value: 2 },
    { label: '每周三', value: 3 },
    { label: '每周四', value: 4 },
    { label: '每周五', value: 5 },
    { label: '每周六', value: 6 },
    { label: '每周日', value: 7 },
];

// 定时管理子编辑页面
export const TimerUpdatePageRander = (props) => {
    const {
        dispatch,
        currentUser,
        detalisLoading,
        global,
        TimerTaskModel: { editActInfo, validProfitRule, validProfitRuleTotal },
        initRef,
        limitOperIds, // 活动范围限制运营商

        // 方法
        goBack,

        // 数据源
        taskId: taskIdSign,

        // 权限项控制
        isLock,
        openSomeThing,
    } = props;

    let onlineArray = []; // 定时内容中含场站上线的Array
    const [firstTimerContent, changeTimerContent] = useState('');
    const [stationInfoList, changeStationInfoList] = useState([]); // 校验分润规则req
    const [reqParams, changeReqParams] = useState({}); // 保存入参
    const [taskId, changeTaskId] = useState(taskIdSign);
    useImperativeHandle(initRef, () => ({
        goBack,
    }));

    const [submitLoading, changeSubmitLoading] = useState(false);

    // 上线确认弹窗状态
    const [confirmVisible, toggleConfirmModal] = useState(false);

    const [form] = Form.useForm();

    useEffect(() => {
        return () => {
            dispatch({
                type: 'TimerTaskModel/updateEditActInfo',
                info: {},
            });
        };
    }, []);

    useEffect(() => {
        form.resetFields();
        initEvent(taskId);
    }, [taskId]);

    const initEvent = (id) => {
        if (id) {
            dispatch({
                type: 'TimerTaskModel/initEditActInfo',
                taskId: id,
            });
        }
    };

    useEffect(() => {
        if (editActInfo) {
            initEditInfo(editActInfo);
        }
    }, [editActInfo]);

    useEffect(() => {
        form.setFieldsValue({ timerType: '0' });
    }, []);

    // 禁止选择小于当前日期
    const disabledDate = (current) => {
        return current && current < moment().startOf('day');
    };
    //
    const range = (start, end) => {
        const result = [];
        for (let i = start; i <= end; i++) {
            result.push(i);
        }
        return result;
    };
    const disabledDateTime = (selectDate) => {
        let date = new Date();
        if (selectDate > date) return;
        let currentDay = moment().date();
        let currentHours = moment().hours();
        let currentMinutes = moment().minutes();
        let settingHours = moment(date).hours();
        let settingDay = moment(date).date();
        if (date && settingDay === currentDay && settingHours === currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //设置为当天这一小时，禁用该小时，该分钟之前的时间
                // disabledMinutes: () => range(0, currentMinutes - 1),
            };
        } else if (date && settingDay === currentDay && settingHours > currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //设置为当天这小时之后，只禁用当天该小时之前的时间
            };
        } else if (date && settingDay === currentDay && settingHours < currentHours) {
            return {
                disabledHours: () => range(0, currentHours - 1), //若先设置了的小时小于当前的，再设置日期为当天，需要禁止选中
                // disabledMinutes: () => range(0, 59),
            };
        } else if (date && settingDay > currentDay) {
            return {
                disabledHours: () => [], //设置为当天之后的日期
                // disabledMinutes: () => [],
            };
        }
    };
    // 弹窗-显示错误内容
    const showErrorModal = (list) => {
        if (list?.length > 0) {
            Modal.error({
                title: `提示`,
                maskClosable: true,
                width: 800,
                zIndex: 100000,
                content: (
                    <List
                        style={{ maxHeight: '450px', overflowY: 'auto', marginTop: '10px' }}
                        size="small"
                        bordered
                        dataSource={list}
                        renderItem={(item) => <List.Item>{item}</List.Item>}
                    />
                ),
                footer: null,
            });
            return false;
        } else {
            return true;
        }
    };

    // 校验二：第一条分润购电配置 hasRule为true则校验通过,否则提示不通过；
    const handleValidProfitRule = () => {
        if (validProfitRule?.length > 0) {
            if (validProfitRule[0].hasRule) {
                return true;
            } else {
                message.error('无法提交, 存在未生效、未配置的场站');
            }
        }
        return false;
    };

    // 校验三：上线确认弹窗-点击确认
    const finishConfirmEvent = async () => {
        try {
            if (!handleValidProfitRule()) return;
            const {
                data: { list },
            } = await timerTaskValidAreaInfoApi({
                stationInfoList,
            }); // 校验站点省市区是否为空
            if (!showErrorModal(list)) return;

            submitEvent('send', reqParams);
            toggleConfirmModal(false);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    // 定时时间触发 定时内容的时间重复处理
    const onChangeTimerContent = (time) => {
        disabledDateTime(time);

        let timerContentNum = 0,
            timerContentIndex = '',
            timerContentExt = form.getFieldValue('timerContentExt');
        timerContentExt.forEach((item, index) => {
            if (
                item.timerContent.format('YYYY-MM-DD HH:mm:ss') ===
                time.format('YYYY-MM-DD HH:mm:ss')
            ) {
                timerContentNum++;
                timerContentIndex = index;
            }
        });
        if (timerContentNum > 1) {
            message.warning('定时内容的时间不能重复');
            timerContentExt[timerContentIndex].timerContent = '';
        }
    };

    // 初始化默认数据
    const initEditInfo = async (info) => {
        try {
            let infoList = { ...info },
                params = {};
            params.name = infoList.taskName;
            params.timerType = infoList.timerType?.toString();
            params.stationInfoList = infoList.stationInfoList;
            // 格式化选择周期
            if (infoList.timerType === 1) {
                if (infoList?.strategyVoList?.length > 0) {
                    let weeks = infoList.strategyVoList[0].weeks.split(',');
                    params.weeks = weeks.map((item) => parseInt(item));
                    params.dateTime = [
                        moment(infoList.strategyVoList[0].beginTime),
                        moment(infoList.strategyVoList[0].endTime),
                    ];
                }
            }
            // 格式化定时内容
            if (infoList?.strategyVoList?.length > 0) {
                params.timerContentExt = infoList.strategyVoList.map((item) => {
                    return {
                        timerContent:
                            infoList.timerType === 1
                                ? moment(item.timerContent, 'HH:mm:ss')
                                : moment(new Date(`${item.beginTime} ${item.timerContent}`)),
                        scene: item.scene.toString(),
                    };
                });
            }
            // delete params.stationInfoList;

            form.setFieldsValue(params);
        } catch (error) {
            console.log(33333, error);
        }
    };

    // 点击提交
    const saveEvent = async (type) => {
        if (submitLoading) {
            return;
        }
        try {
            await form.validateFields();
            const values = form.getFieldsValue();

            const params = {
                name: values.name,
                timerType: values.timerType,
                beginTime: (values.dateTime && values.dateTime[0].format('YYYY-MM-DD')) || '',
                endTime: (values.dateTime && values.dateTime[1].format('YYYY-MM-DD')) || '',
                weeks: values?.weeks?.join(',') ?? '',
                stationInfoList: JSON.stringify(values?.stationInfoList ?? ''),
                stationInfo: values.stationInfo || undefined,
                delScopeInfo: values.delScopeInfo || undefined,
            };
            // 定时类型：0按时间，1按周期
            let timerContentExt;
            if (values.timerType === '0') {
                timerContentExt = values?.timerContentExt.map((item) => {
                    return {
                        ...item,
                        timerContent: item?.timerContent?.format('YYYY-MM-DD HH:mm:ss'),
                    };
                });
            } else {
                timerContentExt = values?.timerContentExt.map((item) => {
                    return {
                        ...item,
                        timerContent: item?.timerContent?.format('HH:mm:ss'),
                    };
                });
            }
            params.timerContentExt = JSON.stringify(timerContentExt);
            params.id = taskId || '';
            changeReqParams(params);

            // 校验一：校验任务时间是否冲突
            const {
                data: { list },
            } = await timerTaskValidTimeApi(params);
            if (!showErrorModal(list)) return;

            onlineArray = timerContentExt.filter((item) => {
                return item.scene === '0';
            }); // 获取定时内容中场站上线部分
            // 存在上线，则校验分润规则
            if (onlineArray && onlineArray.length > 0) {
                // 校验站点价格是否配置正确能否上线
                if (values?.stationInfoList) {
                    await checkStationPriceEvent(
                        values?.stationInfoList.map((ele) => ele.stationId),
                    );
                }
                let time = JSON.parse(JSON.stringify(onlineArray?.[0].timerContent ?? ''));
                if (values.timerType === '0') {
                    time = moment(time).format('YYYY-MM-DD HH:mm');
                } else {
                    time = `${params.beginTime} ${time.slice(0, 5)}`;
                }
                changeTimerContent(time);
                changeStationInfoList(params.stationInfoList);
                toggleConfirmModal(true);
            } else {
                submitEvent(type, params);
            }
            return;
        } catch (error) {
            console.log(9999, error);
            changeSubmitLoading(false);
            return Promise.reject(error);
        } finally {
            changeSubmitLoading(false);
        }
    };

    /**
     * 保存定时
     * type  save/send
     */
    const submitEvent = async (type, params = {}) => {
        changeSubmitLoading(true);

        let options = {
            ...params,
            saveType: type,
        };

        try {
            if (taskId) {
                // 修改
                await timerTaskUpdateApi(options);
            } else {
                // 保存
                await timerTaskSavaApi(options);
            }

            if (type == 'save') {
                message.success('保存成功');
            } else if (type == 'send') {
                message.success('提交成功');
                goBack();
            }
            form.resetFields();
            changeSubmitLoading(false);
        } catch (error) {
            changeSubmitLoading(false);
        }
    };

    return (
        <>
            <Card loading={detalisLoading}>
                <Form form={form} scrollToFirstError name="basicForm">
                    <div className={commonStyles['form-title']}>定时信息</div>
                    {isLock ? (
                        <FormItem label="定时名称:" {...formItemFixedWidthLayout}>
                            <div>{editActInfo.taskName}</div>
                        </FormItem>
                    ) : (
                        <FormItem
                            label={<span>定时名称</span>}
                            name="name"
                            {...formItemFixedWidthLayout}
                            rules={[
                                { required: true, whitespace: true, message: '请填写定时名称' },
                            ]}
                        >
                            <Input
                                disabled={isLock}
                                maxLength={15}
                                placeholder="请填写"
                                autoComplete="off"
                            />
                        </FormItem>
                    )}

                    {isLock ? (
                        <FormItem label="定时类型:" {...formItemFixedWidthLayout}>
                            <div>{editActInfo.timerTypeName}</div>
                        </FormItem>
                    ) : (
                        <FormItem
                            name="timerType"
                            label="定时类型:"
                            {...formItemFixedWidthLayout}
                            rules={[{ required: true, message: '请选择' }]}
                            initialValue={'0'}
                        >
                            <Radio.Group disabled={isLock} defaultValue={'0'}>
                                <Radio value={'0'}>按时间</Radio>
                                <Radio value={'1'}>按周期</Radio>
                            </Radio.Group>
                        </FormItem>
                    )}

                    {isLock ? null : (
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.timerType !== curValues.timerType
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const dateTime = getFieldValue('dateTime');
                                const timerType = getFieldValue('timerType');
                                return [
                                    timerType === '1' ? (
                                        <Space
                                            direction="vertical"
                                            style={{
                                                width: '100%',
                                            }}
                                        >
                                            <FormItem
                                                name="dateTime"
                                                label="选择周期:"
                                                {...formItemFixedWidthLayout}
                                                rules={[
                                                    { required: true, message: '请选择周期' },
                                                    (_) => ({
                                                        validator(rule, value) {
                                                            if (!value) {
                                                                return Promise.reject('');
                                                            }
                                                            if (!value[0]) {
                                                                return Promise.reject(
                                                                    '请选择开始日期',
                                                                );
                                                            }
                                                            if (!value[1]) {
                                                                return Promise.reject(
                                                                    '请选择结束日期',
                                                                );
                                                            }
                                                            if (value[1]) {
                                                                const nowTime = +new Date();
                                                                const sendEndTime = +new Date(
                                                                    value[1],
                                                                );

                                                                if (sendEndTime < nowTime) {
                                                                    return Promise.reject(
                                                                        '结束日期不能早于当前时间',
                                                                    );
                                                                }
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                            >
                                                <RangePicker
                                                    ranges={
                                                        dateTime && dateTime[0] && dateTime[1]
                                                            ? {
                                                                  起始日期顺延7天: [
                                                                      moment(dateTime[0]).add(
                                                                          '7',
                                                                          'day',
                                                                      ),
                                                                      moment(dateTime[1]).add(
                                                                          '7',
                                                                          'day',
                                                                      ),
                                                                  ],
                                                              }
                                                            : {}
                                                    }
                                                    disabled={isLock}
                                                    disabledDate={disabledDate}
                                                    format="YYYY-MM-DD"
                                                />
                                            </FormItem>
                                            <FormItem
                                                name="weeks"
                                                label=""
                                                style={{
                                                    margin: '0 0 30px 110px',
                                                }}
                                                {...formItemLayout}
                                                rules={[
                                                    { required: true, message: '请选择星期' },
                                                    (_) => ({
                                                        validator(rule, value) {
                                                            if (!value) {
                                                                return Promise.reject('');
                                                            }
                                                            if (value?.length === 0) {
                                                                return Promise.reject(
                                                                    '至少勾选1项',
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    }),
                                                ]}
                                                initialValue={[1, 2, 3, 4, 5, 6, 7]}
                                            >
                                                <Checkbox.Group
                                                    disabled={isLock}
                                                    options={weekOptions}
                                                />
                                            </FormItem>
                                        </Space>
                                    ) : null,
                                ];
                            }}
                        </FormItem>
                    )}
                    {isLock ? (
                        <FormItem label="定时内容:" {...formItemFixedWidthLayout}>
                            <div className="text-wrap">{editActInfo.timerContent}</div>
                        </FormItem>
                    ) : (
                        <FormItem
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues.dateTime !== curValues.dateTime ||
                                prevValues.timerType !== curValues.timerType
                            }
                            noStyle
                        >
                            {({ getFieldValue }) => {
                                const timerType = getFieldValue('timerType');
                                return (
                                    <FormItem
                                        name="timerContentExt"
                                        label="定时内容:"
                                        {...formItemFixedWidthLayout}
                                        style={{
                                            marginBottom: 0,
                                        }}
                                        rules={[{ required: true, message: '请选择定时内容' }]}
                                    >
                                        <Form.List name="timerContentExt">
                                            {(fields, { add, remove }) => (
                                                <>
                                                    {fields.map(({ key, name, ...restField }) => (
                                                        <Space
                                                            key={key}
                                                            style={{
                                                                marginBottom: 8,
                                                            }}
                                                            align="baseline"
                                                        >
                                                            <FormItem
                                                                name={[name, 'timerContent']}
                                                                style={{
                                                                    display: 'inline-block',
                                                                }}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '请选择',
                                                                    },
                                                                ]}
                                                            >
                                                                {timerType === '1' ? (
                                                                    <TimePicker
                                                                        disabled={isLock}
                                                                        format="HH:mm"
                                                                        minuteStep={30}
                                                                        onChange={
                                                                            onChangeTimerContent
                                                                        }
                                                                    />
                                                                ) : (
                                                                    <DatePicker
                                                                        format="YYYY-MM-DD HH:mm"
                                                                        minuteStep={30}
                                                                        disabled={isLock}
                                                                        disabledDate={disabledDate}
                                                                        disabledTime={
                                                                            disabledDateTime
                                                                        }
                                                                        showTime={{
                                                                            defaultValue: moment(
                                                                                `${moment().hours()}:00`,
                                                                                'HH:mm',
                                                                            ),
                                                                        }}
                                                                        onChange={
                                                                            onChangeTimerContent
                                                                        }
                                                                    />
                                                                )}
                                                            </FormItem>
                                                            <FormItem
                                                                name={[name, 'scene']}
                                                                style={{
                                                                    display: 'inline-block',
                                                                }}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        whitespace: true,
                                                                        message: '请选择执行操作',
                                                                    },
                                                                ]}
                                                            >
                                                                <Select
                                                                    disabled={isLock}
                                                                    placeholder="请选择执行操作"
                                                                >
                                                                    <Option value={'0'}>
                                                                        场站上线
                                                                    </Option>
                                                                    <Option value={'1'}>
                                                                        场站下线
                                                                    </Option>
                                                                </Select>
                                                            </FormItem>
                                                            <Space>
                                                                {isLock ? null : (
                                                                    <Col
                                                                        style={{
                                                                            display: 'inline-block',
                                                                            width: 'calc(10%)',
                                                                            margin: '0 8px',
                                                                            position: 'relative',
                                                                            top: '8px',
                                                                        }}
                                                                    >
                                                                        <MinusCircleOutlined
                                                                            style={{
                                                                                fontSize: 24,
                                                                            }}
                                                                            onClick={() => {
                                                                                remove(name);
                                                                            }}
                                                                        />
                                                                    </Col>
                                                                )}
                                                            </Space>
                                                        </Space>
                                                    ))}
                                                    <Form.Item>
                                                        <Button
                                                            disabled={isLock}
                                                            type="dashed"
                                                            onClick={() => add()}
                                                            block
                                                            icon={<PlusOutlined />}
                                                        >
                                                            新增定时
                                                        </Button>
                                                    </Form.Item>
                                                </>
                                            )}
                                        </Form.List>
                                    </FormItem>
                                );
                            }}
                        </FormItem>
                    )}

                    <FormItem
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.belongType !== curValues.belongType
                        }
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            const belongType = getFieldValue('belongType');
                            return (
                                <OperStationSearchList
                                    title="定时范围"
                                    form={form}
                                    formItemLayout={formItemLayout}
                                    stationList={editActInfo?.stationInfoList || undefined}
                                    limitOperIds={limitOperIds}
                                    disabled={!openSomeThing}
                                    required
                                    purchase={
                                        belongType == 'oper'
                                            ? SELECT_TYPES.EXCEPTBUY
                                            : SELECT_TYPES.ALL
                                    }
                                    currentUser={currentUser}
                                    hasStastics
                                    // requestInfo={
                                    //     taskId && {
                                    //         recordParams: {
                                    //             relateId: taskId,
                                    //             scene: 'stc_timer',
                                    //         },
                                    //     }
                                    // }
                                />
                            );
                        }}
                    </FormItem>
                    {/* 场站上线确认弹窗 */}
                    <ConfirmModal
                        confirmVisible={confirmVisible}
                        firstTimerContent={firstTimerContent}
                        stationInfoList={stationInfoList}
                        onFinish={finishConfirmEvent}
                        onClose={() => toggleConfirmModal(false)}
                    ></ConfirmModal>
                    {isLock ? null : (
                        <div className={commonStyles['form-submit']}>
                            <Fragment>
                                <Button
                                    className={commonStyles['form-btn']}
                                    type="primary"
                                    loading={submitLoading}
                                    onClick={() => {
                                        saveEvent('send');
                                    }}
                                >
                                    提交
                                </Button>
                                <Button className={commonStyles['form-btn']} onClick={goBack}>
                                    取消
                                </Button>
                            </Fragment>
                        </div>
                    )}
                </Form>
            </Card>
            {isLock ? (
                <Card loading={detalisLoading} style={{ marginTop: '10px' }}>
                    <div className={commonStyles['form-title']}>执行情况</div>
                    <ImplementationItem taskId={taskId}></ImplementationItem>
                </Card>
            ) : null}
        </>
    );
};

const TimerUpdatePage = (props) => {
    const {
        match,
        history,
        route,
        TimerTaskModel: { editActInfo },
    } = props;

    const ref = useRef();

    const taskId = match.params.taskId || null;
    const isLock = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return true;
        }
        if (route.path.indexOf('/update') >= 0 && editActInfo?.actState >= 2) {
            return true;
        }
        return false;
    }, [editActInfo]); // 是否可编辑
    const openSomeThing = useMemo(() => {
        if (route.path.indexOf('/look') >= 0) {
            return false;
        }
        return !editActInfo || editActInfo?.actState == 2 || !isLock;
    }, [editActInfo, isLock]); // 是否可编辑

    const goBack = () => {
        // history.go(-1);
        history.replace('/assetCenter/TimerTaskManage/list');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={ref?.current?.goBack ?? goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <TimerUpdatePageRander
                {...props}
                taskId={taskId}
                initRef={ref}
                isLock={isLock}
                openSomeThing={openSomeThing}
                goBack={goBack}
            />
        </PageHeaderWrapper>
    );
};
export default connect(({ global, user, TimerTaskModel, loading }) => ({
    global,
    currentUser: user.currentUser,
    TimerTaskModel,
    detalisLoading: loading.effects['TimerTaskModel/initEditActInfo'],
}))(TimerUpdatePage);
