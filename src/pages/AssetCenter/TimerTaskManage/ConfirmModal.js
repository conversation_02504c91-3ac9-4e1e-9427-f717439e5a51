/**
 * 定时管理-新增/编辑-场站上线确认弹窗
 */
import { ExclamationCircleOutlined, SyncOutlined } from '@ant-design/icons';
import { Form, Modal, Button, message } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'umi';

import { TIMERTASK_STATUS } from '@/config/declare';
import styles from './TimerListPage.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { sendStationPriceMq, validStationPrice } from '@/services/MngAstApi';

const onCell = (record) => {
    return { rowSpan: record?.rowSpan };
};
const onelClRow1 = () => {
    return { rowSpan: 1 };
};
export const ConfirmModalLayout = (props) => {
    const {
        confirmVisible,
        onClose,
        onFinish, // 添加完毕的回调事件，如果是追加，返回空，如果是添加，返回已选中的cpn对象
        firstTimerContent,
        stationInfoList,
        partialStation = [], // 部分场站有误
        dispatch,
        TimerTaskModel: { validProfitRule, validProfitRuleTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },
    } = props;

    const [form] = Form.useForm();

    // 部分场站有误
    const [partialStationName, setPartialStationName] = useState('');

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: TIMERTASK_STATUS.ALL,
            pageSize: 10,
        },
        props,
    );

    useEffect(() => {
        if (partialStation?.length > 0) {
            const name = partialStation?.map((ele) => ele.stationName)?.join('；');
            setPartialStationName(name);
        } else {
            setPartialStationName('');
        }
    }, [partialStation]);

    useEffect(() => {
        if (stationInfoList?.length > 0) searchData();
    }, [stationInfoList]);

    const jumpEvent = (item) => {
        const params = {
            operName: item.buildName,
            operId: item.buildId,
        };
        let openUrl = '';
        // cooperationType: '01'分润，'02'购电
        if (item.cooperationType === '01') {
            dispatch({
                type: 'profitRuleModel/setSelectRuleInfo',
                info: params,
            });

            openUrl = `${window.location.origin}${PUBLIC_PATH}sellerCenter/operatormanage/profitRule/list/details?operName=${item.buildName}&operId=${item.buildId}&cooperationPlatform=${item.cooperationPlatform}`;
        } else {
            dispatch({
                type: 'purchaseModel/setSelectRuleInfo',
                info: params,
            });
            openUrl = `${window.location.origin}${PUBLIC_PATH}sellerCenter/operatormanage/purchase/list/details?operName=${item.buildName}&operId=${item.buildId}&cooperationPlatform=${item.cooperationPlatform}`;
        }
        window.open(openUrl, 'top'); // 打开分润/购电规则配置页
    };

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            stationInfoList,
        };

        dispatch({
            type: 'global/setPageInit',
            // pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });
        dispatch({
            type: 'TimerTaskModel/timerTaskValidProfitRule',
            options: {
                stationIds: JSON.parse(params?.stationInfoList)
                    ?.map((v) => v?.stationId)
                    .join(','),
            },
        }); // 定时内容中第一个场站上线的时间，并校验该上线时间的分润购电配置情况；
    };

    const columns = [
        {
            title: '场站',
            width: 200,
            dataIndex: 'stationName',
            onCell: onCell,
            render(text, record) {
                return record.hasRule ? (
                    <span title={text}>{text}</span>
                ) : (
                    <span style={{ color: 'red' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '城市',
            width: 200,
            dataIndex: 'cityName',
            onCell: onCell,
            render(text, record) {
                return record.hasRule ? (
                    <span title={text}>{text}</span>
                ) : (
                    <span style={{ color: 'red' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            onCell: onCell,
            render(text, record) {
                return record.hasRule ? (
                    <span title={text}>{text}</span>
                ) : (
                    <span style={{ color: 'red' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '类型',
            width: 140,
            dataIndex: 'cooperationTypeName',
            onCell: onCell,
            render(text, record) {
                return record.hasRule ? (
                    <span title={text}>{text}</span>
                ) : (
                    <span style={{ color: 'red' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '平台',
            width: 140,
            dataIndex: 'cooperationPlatformName',
            onCell: onelClRow1,
            render(text, record) {
                return record.platformHasRule ? (
                    <span title={text}>{text}</span>
                ) : (
                    <span style={{ color: 'red' }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '规则',
            width: 400,
            dataIndex: 'ruleName',
            sorter: true,
            filterDropdown: true,
            onCell: onelClRow1,
            filterIcon: () => {
                return (
                    <SyncOutlined
                        onClick={() => {
                            searchData();
                        }}
                    />
                );
            },
            render: (text, record) => {
                return record.platformHasRule ? (
                    <span title={text} style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                        {text}
                    </span>
                ) : (
                    <span
                        className={styles['table-btn']}
                        onClick={() => jumpEvent(record)}
                        title={text}
                    >
                        {text}
                    </span>
                );
            },
        },
    ];

    const finishEvent = () => {
        onFinish && onFinish(validProfitRule);
    };
    console.log(validProfitRule);
    return (
        <Modal
            title="场站上线确认"
            width={1000}
            visible={confirmVisible}
            onCancel={onClose}
            onOk={finishEvent}
            zIndex={1005}
            maskClosable={false}
        >
            {partialStationName && (
                <div style={{ backgroundColor: '#fde1e1', padding: '15px' }}>
                    <div style={{ marginBottom: '10px', color: 'red', fontSize: '14px' }}>
                        部分场站价格格式有误或为空，暂无法上线
                        <Button
                            style={{ marginLeft: '6px' }}
                            type="primary"
                            onClick={async () => {
                                try {
                                    const ids = partialStation?.map((ele) => ele.stationId);
                                    await sendStationPriceMq({ stationIds: ids.join(',') });
                                    message.success('发起成功，此过程预计1～2分钟');
                                    return;
                                } catch (error) {}
                            }}
                        >
                            重新获取价格
                        </Button>
                    </div>
                    <div className={''} style={{ color: '#000', fontSize: '14px' }}>
                        {partialStationName}
                    </div>
                </div>
            )}

            <div style={{ marginBottom: '15px', color: '#000', fontSize: '18px' }}>
                请确认{firstTimerContent}上线的场站分润/购电结算规则配置
            </div>
            <div className={''} style={{ marginBottom: '25px', color: '#666', fontSize: '14px' }}>
                <ExclamationCircleOutlined style={{ marginRight: '10px', color: '#666' }} />
                未配置分润/购电结算规则的场站将无法上线
            </div>
            <TablePro
                name="confirm"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                dataSource={validProfitRule}
                columns={columns}
                onChange={onTableChange}
                pagination={false}
                tabType={pageInfo.tabType}
                offsetHeader={false}
            />
        </Modal>
    );
};

const ConfirmModal = (props) => {
    return (
        <Fragment>
            <ConfirmModalLayout {...props} />
        </Fragment>
    );
};

export default connect(({ global, login, user, TimerTaskModel, loading }) => ({
    global,
    login,
    currentUser: user.currentUser,
    TimerTaskModel,
    listLoading: loading.effects['TimerTaskModel/timerTaskValidProfitRule'],
}))(ConfirmModal);
