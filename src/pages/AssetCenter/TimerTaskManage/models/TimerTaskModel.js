import {
    getBusinessActiveListApi,
    getBusinessActiveDetailApi,
} from '@/services/Marketing/MarketingBusinessActiveApi';
import { getCityAndStationByOperIdApi } from '@/services/CommonApi';
import { getWorkorderActDetailApi } from '@/services/Marketing/MarketingWorkorderApi';
import {
    timerTaskPageApi,
    timerTaskDetailApi,
    timerTaskRecordPageApi,
    astScopeTaskPageApi,
} from '@/services/AssetCenter/TimerTaskApi';
import { validProfitRule } from '@/services/MngAstApi';
const processTableData = (list) => {
    if (list) {
        const temp = [];
        let equipId = -1;
        list.forEach((item) => {
            item?.profitRuleDto?.forEach((gun, index) => {
                temp.push({
                    ...item,
                    gunName: gun?.gunName,
                    gunNo: gun?.gunNo,
                    gunEquipId: gun?.equipId,
                    xdtQrCode: gun?.xdtQrCode,
                    ruleName: gun?.ruleName,
                    platformHasRule: gun?.platformHasRule,
                    cooperationType: gun?.cooperationType,
                    cooperationTypeName: gun?.cooperationTypeName,
                    cooperationPlatform: gun?.cooperationPlatform,
                    cooperationPlatformName: gun?.cooperationPlatformName,
                    bindFlag: gun?.bindFlag,
                    rowSpan: equipId !== item?.stationId ? item?.profitRuleDto?.length : 0,
                    index,
                });
                equipId = item.stationId;
            });
        });
        return temp;
    } else {
        return [];
    }
};
const TimerTask = {
    namespace: 'TimerTaskModel',
    state: {
        businessActiveList: [], // 商家营销列表
        businessActiveListTotal: 0,
        timerTaskList: [], // 定时列表
        timerTaskListTotal: 0,
        validProfitRule: [], // 分润规则列表
        validProfitRuleTotal: 0,
        stationOptions: [], // 筛选站点列表
        editActInfo: {}, // 当前详情信息
        timerTaskRecordList: [], // 执行任务列表
        timerTaskRecordListTotal: 0,
    },
    effects: {
        /**
         * 定时任务分页列表
         */
        *timerTaskPage({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(timerTaskPageApi, options);

                yield put({
                    type: 'updateTimerTaskList',
                    timerTaskList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 校验分润规则
         */
        *timerTaskValidProfitRule({ options }, { call, put, select }) {
            try {
                const { data: list } = yield call(validProfitRule, options);
                const newList = processTableData(list);
                yield put({
                    type: 'updateValidProfitRule',
                    validProfitRule: newList,
                    total: newList?.length,
                });
            } catch (error) {}
        },
        /**
         * 商家营销列表
         */
        *getBusinessActiveList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBusinessActiveListApi, options);

                yield put({
                    type: 'updateBusinessActiveList',
                    businessActiveList: list,
                    total,
                });
            } catch (error) {}
        },
        /**
         *
         */
        *initStationOptions(_, { call, put, select }) {
            try {
                const { operId } = yield select((state) => state.user.currentUser);
                if (operId) {
                    const { data: cityAndStationList } = yield call(getCityAndStationByOperIdApi, {
                        operId,
                    });

                    yield put({
                        type: 'updateStationOptions',
                        list: cityAndStationList,
                    });
                }
            } catch (error) {
                console.log(7777, error);
            }
        },

        /**
         * 查询详情
         */
        *initEditActInfo({ taskId }, { call, put, select }) {
            try {
                const { data } = yield call(timerTaskDetailApi, taskId);
                yield put({
                    type: 'updateEditActInfo',
                    info: data.list,
                });
            } catch (error) {}
        },

        // 执行记录分页
        *timerTaskRecordPage({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(timerTaskRecordPageApi, options);

                yield put({
                    type: 'updateTimerTaskRecordList',
                    timerTaskRecordList: records,
                    total,
                });
            } catch (error) {}
        },
        /**
         * 任务管理分页列表
         */
        *astScopeTaskPage({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(astScopeTaskPageApi, options);

                yield put({
                    type: 'updateAstScopeTaskList',
                    astScopeTaskList: records,
                    total,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateTimerTaskList(state, { timerTaskList, total }) {
            return {
                ...state,
                timerTaskList,
                timerTaskListTotal: total,
            };
        },
        updateValidProfitRule(state, { validProfitRule, total }) {
            return {
                ...state,
                validProfitRule,
                validProfitRuleTotal: total,
            };
        },
        updateBusinessActiveList(state, { businessActiveList, total }) {
            return {
                ...state,
                businessActiveList,
                businessActiveListTotal: total,
            };
        },
        updateStationOptions(state, { list }) {
            return {
                ...state,
                stationOptions: list,
            };
        },

        updateEditActInfo(state, { info }) {
            return {
                ...state,
                editActInfo: info,
            };
        },

        updateTimerTaskRecordList(state, { timerTaskRecordList, total }) {
            return {
                ...state,
                timerTaskRecordList,
                timerTaskRecordListTotal: total,
            };
        },

        updateAstScopeTaskList(state, { astScopeTaskList, total }) {
            return {
                ...state,
                astScopeTaskList,
                astScopeTaskListTotal: total,
            };
        },
    },
};
export default TimerTask;
