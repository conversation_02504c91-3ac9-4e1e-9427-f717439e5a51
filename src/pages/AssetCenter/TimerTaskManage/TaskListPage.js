import { ExclamationCircleOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Modal, Select, Space, DatePicker, Tabs, Input } from 'antd';
import { useEffect, useRef } from 'react';
import { connect } from 'umi';

import styles from './TimerListPage.less';
import { astScopeTaskStopApi } from '@/services/AssetCenter/TimerTaskApi';
import { TIMERTASK_STATUS } from '@/config/declare';
import usePageState from '@/hooks/usePageState.js';
import { isEmpty } from '@/utils/utils';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = { labelCol: { flex: '0 0 80px' }, labelAlign: 'right' };

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        history,
        onSubmit,
        onReset,
        listLoading,
        TimerTaskModel: { stationOptions },
        global: { pageInit },
        currentUser,
    } = props;

    const {
        location: { pathname },
    } = history;

    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]) {
            if (pageInit[pathname].form.stationName) {
                stationRef.current.fetchStation(pageInit[pathname].form.stationName);
            }
        }
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const operId = Form.useWatch('operatorId', form);

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <OperSelectTypeItem
                        form={form}
                        name="operatorId"
                        label="运营商"
                        formItemLayout={formItemLayout}
                    />
                </Col>
                <Col span={8}>
                    <AllStationSelect
                        form={form}
                        operId={operId}
                        ref={stationRef}
                        label="场站名称"
                        name="stationId"
                        formItemLayout={formItemLayout}
                    />
                </Col>
                <Col span={8}>
                    <CitysSelect
                        label="城市"
                        name="city"
                        placeholder="请选择"
                        showArrow
                        provinceSelectable
                        required={false}
                        formItemLayout={formItemLayout}
                        rules={[]}
                    />
                </Col>
                <Col span={8}>
                    <Form.Item label="桩编号" name="pileNo">
                        <Input placeholder="请填写" maxLength={200} />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item label="枪编号" name="gunNo">
                        <Input placeholder="请填写" maxLength={200} />
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <FormItem label="任务模块:" name="scene">
                        <Select placeholder="请输入" allowClear>
                            <Option value={'10'}>场站上线</Option>
                            <Option value={'20'}>场站显示</Option>
                            <Option value={'40'}>枪恢复</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="dateTime" label="触发时间:" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="createTime" label="创建时间:" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export const TaskListLayout = (props) => {
    const {
        dispatch,
        history,
        TimerTaskModel: { astScopeTaskList, astScopeTaskListTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },

        workorderEvent,
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: TIMERTASK_STATUS.ALL,
        },
        props,
    );
    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            operatorId: data.operatorId,
            city: data.city?.join(','),
            stationId: data.stationId, // 场站ID
            pileNo: data.pileNo?.trim(),
            gunNo: data.gunNo?.trim(),
            scene: data.scene || '', // 任务模块
            triggerBeginTime: (data.dateTime && data.dateTime[0].format('YYYY-MM-DD')) || '', // 触发开始时间
            triggerEndTime: (data.dateTime && data.dateTime[1].format('YYYY-MM-DD')) || '', // 触发结束时间
            createBeginTime: (data.createTime && data.createTime[0].format('YYYY-MM-DD')) || '', // 创建开始时间
            createEndTime: (data.createTime && data.createTime[1].format('YYYY-MM-DD')) || '', // 创建结束时间
        };
        if (pageInfo.tabType !== TIMERTASK_STATUS.ALL) {
            params.status = pageInfo.tabType;
        }

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'TimerTaskModel/astScopeTaskPage',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };
    // 列表操作-停止
    const stopEvent = async (item) => {
        confirm({
            title: `确定停止${item.sceneName}任务?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    if (workorderEvent) {
                        workorderEvent('stop', item);
                    } else {
                        await astScopeTaskStopApi(item.id);
                    }
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    const columns = [
        {
            title: '运营商',
            dataIndex: 'operatorName',
            width: 160,
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '城市',
            dataIndex: 'cityName',
            width: 140,
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '场站',
            width: 200,
            dataIndex: 'scopeName',
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '桩编号',
            dataIndex: 'pileNo',
            width: 160,
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '枪编号',
            dataIndex: 'gunNo',
            width: 160,
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '任务模块',
            width: 200,
            dataIndex: 'sceneName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '触发条件',
            width: 140,
            dataIndex: 'triggerFlagName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '停止原因',
            dataIndex: 'stopReason',
            width: 140,
            render(value) {
                return value ?? '-';
            },
        },
        {
            title: '触发时间',
            width: 180,
            dataIndex: 'updateTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 200,
            dataIndex: 'createBy',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 180,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (text, record, index) => {
                let controlItems = [];
                if (
                    record.status !== TIMERTASK_STATUS.STOP &&
                    record.status !== TIMERTASK_STATUS.DONE &&
                    record.scene === '10'
                ) {
                    controlItems.push(
                        <span className={styles['table-btn']} onClick={() => stopEvent(record)}>
                            停止
                        </span>,
                    );
                }

                return <Space style={{ whiteSpace: 'nowrap' }}>{controlItems}</Space>;
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />
            <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={TIMERTASK_STATUS.ALL} />
                <TabPane tab="待执行" key={TIMERTASK_STATUS.WAIT} />
                <TabPane tab="执行中" key={TIMERTASK_STATUS.DOING} />
                <TabPane tab="已执行" key={TIMERTASK_STATUS.DONE} />
                <TabPane tab="已停止" key={TIMERTASK_STATUS.STOP} />
            </Tabs>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="id"
                dataSource={astScopeTaskList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: astScopeTaskListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                offsetHeader={workorderEvent ? 0 : undefined}
            />
        </Card>
    );
};

const TaskListPage = (props) => {
    return (
        <PageHeaderWrapper>
            <TaskListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, login, user, TimerTaskModel, loading }) => ({
    global,
    login,
    currentUser: user.currentUser,
    TimerTaskModel,
    listLoading: loading.effects['TimerTaskModel/astScopeTaskPage'],
}))(TaskListPage);
