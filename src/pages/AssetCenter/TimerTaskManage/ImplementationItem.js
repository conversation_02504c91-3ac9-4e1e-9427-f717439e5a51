/**
 * 定时管理-查看-执行情况
 */
import {
    Button,
    Card,
    Col,
    Form,
    Modal,
    Row,
    Select,
    Space,
    Input,
    DatePicker,
    TreeSelect,
    Tabs,
    Spin,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './TimerListPage.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

export const ImplementationItemLayout = (props) => {
    const {
        dispatch,
        TimerTaskModel: { timerTaskRecordList, timerTaskRecordListTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },

        taskId,
    } = props;

    const [pageInfo, changePageInfo, onTableChange] = usePageState(props);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用接口
    const searchData = () => {
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            taskId: taskId,
        };

        dispatch({
            type: 'global/setPageInit',
            // pathname,
            info: {
                form: '',
                state: pageInfo,
            },
        });

        dispatch({
            type: 'TimerTaskModel/timerTaskRecordPage',
            options: params,
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '场站',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '执行时间',
            width: 200,
            dataIndex: 'executeTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '执行操作',
            width: 140,
            dataIndex: 'sceneName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '执行情况',
            width: 140,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '失败原因',
            width: 300,
            dataIndex: 'reason',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    return (
        <TablePro
            name="timer"
            loading={listLoading}
            scroll={{ x: 'max-content' }}
            rowKey={(record) => record.taskId}
            dataSource={timerTaskRecordList}
            columns={columns}
            onChange={onTableChange}
            pagination={{
                current: pageInfo.pageIndex,
                total: timerTaskRecordListTotal,
                pageSize: pageInfo.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
            }}
            tabType={pageInfo.tabType}
            offsetHeader={false}
        />
    );
};

const ImplementationItem = (props) => {
    return (
        <Fragment>
            <ImplementationItemLayout {...props} />
        </Fragment>
    );
};

export default connect(({ global, login, user, TimerTaskModel, loading }) => ({
    global,
    login,
    currentUser: user.currentUser,
    TimerTaskModel,
    listLoading: loading.effects['TimerTaskModel/timerTaskRecordPage'],
}))(ImplementationItem);
