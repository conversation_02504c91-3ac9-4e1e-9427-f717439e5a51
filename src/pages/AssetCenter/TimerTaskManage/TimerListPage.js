/**
 * 定时管理
 */
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    Modal,
    Row,
    Select,
    Space,
    Input,
    DatePicker,
    TreeSelect,
    Tabs,
    Spin,
    message,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { timerTaskStopApi } from '@/services/AssetCenter/TimerTaskApi';
import { TIMERTASK_STATUS } from '@/config/declare';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './TimerListPage.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';

const { SHOW_CHILD } = TreeSelect;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        history,
        onSubmit,
        onReset,
        listLoading,
        global: { pageInit },
    } = props;

    const {
        location: { pathname },
    } = history;

    const stationRef = useRef();

    useEffect(() => {
        if (pageInit[pathname]) {
            if (pageInit[pathname].form.stationName) {
                stationRef.current.fetchStation(pageInit[pathname].form.stationName);
            }
        }
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                <Col span={8}>
                    <FormItem label="定时名称:" name="name">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <AllStationSelect
                        form={form}
                        ref={stationRef}
                        label="场站名称"
                        name="stationId"
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="定时内容:" name="scene">
                        <Select placeholder="请选择" allowClear>
                            <Option value={'0'}>场站上线</Option>
                            <Option value={'1'}>场站下线</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="定时类型:" name="timerType">
                        <Select placeholder="请选择" allowClear>
                            <Option value={'0'}>按时间</Option>
                            <Option value={'1'}>按周期</Option>
                        </Select>
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem name="dateTime" label="创建时间:" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

export const TimerListLayout = (props) => {
    const {
        dispatch,
        history,
        TimerTaskModel: { timerTaskList, timerTaskListTotal },
        listLoading,
        global: { pageInit },
        login: { dynamicMenu },
    } = props;

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: TIMERTASK_STATUS.ALL,
        },
        props,
    );
    useEffect(() => {
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            status: pageInfo.tabType,
            name: data.name,
            stationId: data.stationId,
            scene: data.scene,
            timerType: data.timerType,
            createBeginTime: data.dateTime?.[0]?.format('YYYY-MM-DD') ?? '',
            createEndTime: data.dateTime?.[1]?.format('YYYY-MM-DD') ?? '',
            dateTime: undefined,
        };

        dispatch({
            type: 'global/setPageInit',
            pathname,
            info: {
                form: data,
                state: pageInfo,
            },
        });

        dispatch({
            type: 'TimerTaskModel/timerTaskPage',
            options: params,
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const stopTimerEvent = async (item) => {
        confirm({
            title: `确定停止该定时?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '是',
            okType: 'danger',
            cancelText: '否',
            onOk: async () => {
                try {
                    await timerTaskStopApi(item.id);
                    message.success('操作成功');
                    searchData();
                } catch (error) {}
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };

    // 动态获取下级页面
    const getNextPagePath = (type = 'add') => {
        let path;
        if (dynamicMenu) {
            for (let index = 0; index < dynamicMenu?.length; index++) {
                const element = dynamicMenu[index];
                if (element?.path == '/assetCenter') {
                    for (let x = 0; x < element.routes?.length; x++) {
                        const ele = element.routes[x];
                        if (ele?.path == '/assetCenter/TimerTaskManage/list') {
                            path = `${ele.path}/${type}`;
                            break;
                        }
                    }
                }
                if (path?.length) {
                    break;
                }
            }
        }

        if (!path?.length) {
            path = `/assetCenter/TimerTaskManage/list/${type}`;
        }
        return path;
    };

    // 跳转新增页面
    const gotoAddEvent = () => {
        const path = getNextPagePath('add');
        history.push(path);
    };

    const editTimerEvent = (item) => {
        const path = getNextPagePath('update');
        history.push(`${path}/${item.id}`);
    };

    const lookTimerEvent = (item) => {
        const path = getNextPagePath('look');
        history.push(`${path}/${item.id}`);
    };

    const columns = [
        {
            title: '定时名称',
            width: 200,
            dataIndex: 'name',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '定时类型',
            width: 140,
            dataIndex: 'timerTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '定时内容',
            width: 400,
            dataIndex: 'cycleContent',
            render(text, record) {
                return (
                    <span className="text-wrap" title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '定时范围',
            width: 200,
            dataIndex: 'scope',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text || ''}
                    </div>
                );
            },
        },
        {
            title: '状态',
            width: 140,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '创建人',
            width: 200,
            dataIndex: 'createBy',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 180,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render: (text, record, index) => {
                let controlItems = [];
                controlItems.push(
                    <span className={styles['table-btn']} onClick={() => lookTimerEvent(record)}>
                        查看
                    </span>,
                );
                switch (record.status) {
                    case TIMERTASK_STATUS.WAIT:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => editTimerEvent(record)}
                            >
                                修改
                            </span>,
                        );
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => stopTimerEvent(record)}
                            >
                                停止
                            </span>,
                        );
                        break;
                    case TIMERTASK_STATUS.DOING:
                        controlItems.push(
                            <span
                                className={styles['table-btn']}
                                onClick={() => stopTimerEvent(record)}
                            >
                                停止
                            </span>,
                        );
                        break;
                    default:
                        break;
                }

                return <Space style={{ whiteSpace: 'nowrap' }}>{controlItems}</Space>;
            },
        },
    ];

    return (
        <Card>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />
            <div className={styles['btn-bar']}>
                <Button type="primary" onClick={gotoAddEvent}>
                    新增定时
                </Button>
            </div>
            <Tabs defaultActiveKey={pageInfo?.tabType || '00'} onChange={changeTabTypeEvent}>
                <TabPane tab="全部" key={TIMERTASK_STATUS.ALL} />
                <TabPane tab="待执行" key={TIMERTASK_STATUS.WAIT} />
                <TabPane tab="执行中" key={TIMERTASK_STATUS.DOING} />
                <TabPane tab="已完成" key={TIMERTASK_STATUS.DONE} />
                <TabPane tab="已停止" key={TIMERTASK_STATUS.STOP} />
            </Tabs>

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.id}
                dataSource={timerTaskList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: timerTaskListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                offsetHeader={undefined}
            />
        </Card>
    );
};

const TimerListPage = (props) => {
    return (
        <PageHeaderWrapper>
            <TimerListLayout {...props} />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, login, user, TimerTaskModel, loading }) => ({
    global,
    login,
    TimerTaskModel,
    listLoading: loading.effects['TimerTaskModel/timerTaskPage'],
}))(TimerListPage);
