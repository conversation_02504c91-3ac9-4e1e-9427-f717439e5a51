import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    message,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams, formatExportColumns } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import CitysSelect from '@/components/CitysSelect/index.js';
import { exportPileListApi } from '@/services/AssetCenter/PileApi';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global,
        informationModel,
    } = props;

    const stationRef = useRef();

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem
                            label="互通时间:"
                            name="dates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('请选择日期');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="变更字段:" name="modifyField">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'incrementExplain'}>附加费说明</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="变更值:" name="newValue">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            onChange={onSubmit}
                            {...formItemLayout}
                            form={form}
                        />
                    </Col>

                    <Col span={8}>
                        <AllStationSelect form={form} ref={stationRef} label="场站名称" />
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            // treeData={cityTreeData}
                            // loadData={onLoadDataEvent}
                            // onChange={changeTreeEvent}
                            provinceSelectable
                            placeholder="请选择"
                            rules={[]}
                        />
                    </Col>

                    <Col span={8}>
                        <FormItem label="电桩名称:" name="equipName" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="电桩编号:" name="equipNo" {...formItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const StationInformationListPage = (props) => {
    const {
        dispatch,
        history,
        informationModel: { pileList, pileListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        try {
            const data = await form.validateFields();
            const params = {
                buildId: data.operId,
                beginTime:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endTime: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',

                modifyField: data.modifyField,
                newValue: data.newValue,
                stationId: data.stationId,
                equipName: data.equipName,
                equipNo: data.equipNo,
            };
            if (data.city instanceof Array && data.city.length > 0) {
                params.city = JSON.stringify(data.city);
            }

            if (isDownload) {
                await exportPileListApi(params);
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;
                dispatch({
                    type: 'informationModel/getPileList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const goDetailEvent = (item) => {
        dispatch({
            type: 'informationModel/updatePileItemInfo',
            info: item,
        });
        history.push(`/assetCenter/informationManage/pile/list/detail/${item.equipId}`);
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '180px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '电桩名称',
            width: 140,
            dataIndex: 'equipName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '电桩编号',
            width: 140,
            dataIndex: 'equipNo',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '变更内容',
            children: [
                {
                    title: '互通时间',
                    width: 200,
                    dataIndex: 'syncTime',
                    render(text, record) {
                        return (
                            <div className="text-line" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '变更字段',
                    width: 140,
                    dataIndex: 'modifyFieldName',
                    render(text, record) {
                        return (
                            <div className="text-line" style={{ width: '140px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '原值',
                    width: 200,
                    dataIndex: 'originValue',
                    render(text, record) {
                        return (
                            <div className="text-third" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '变更值',
                    width: 200,
                    dataIndex: 'newValue',
                    render(text, record) {
                        return (
                            <div className="text-third" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
            ],
        },

        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (text, record) => {
                const detailBtn = (
                    <span className={styles['table-btn']} onClick={() => goDetailEvent(record)}>
                        详情
                    </span>
                );

                let btns = [detailBtn];

                return <Space>{btns}</Space>;
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />

                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={pileList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: pileListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ informationModel, global, loading }) => ({
    informationModel,
    global,
    listLoading: loading.effects['informationModel/getPileList'],
}))(StationInformationListPage);
