import { getStationListApi } from '@/services/AssetCenter/StationApi';
import { getPileListApi } from '@/services/AssetCenter/PileApi';
import { getSensitiveListApi } from '@/services/AssetCenter/SensitiveApi';
import { SessionStorage } from '@/utils/SessionStorage';

const InformationModel = {
    namespace: 'informationModel',
    state: {
        stationList: [], // 站点列表
        stationListTotal: 0, // 站点列表总条数

        stationItemInfo: SessionStorage.getItem('stationItemInfo') || null, //详情基础信息

        pileList: [], // 站点列表
        pileListTotal: 0, // 站点列表总条数

        pileItemInfo: SessionStorage.getItem('pileItemInfo') || null, //详情基础信息

        wordLost: [], //敏感词列表
        wordLostTotal: 0, // 敏感词列表总条数
    },
    effects: {
        *clearStationItemInfo({ _ }, { call, put, select }) {
            try {
                yield put({
                    type: 'clearStationItemInfo',
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 站点列表
         */
        *getStationList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getStationListApi, options);

                yield put({
                    type: 'updateStationList',
                    list,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 站点列表
         */
        *getPileList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getPileListApi, options);

                yield put({
                    type: 'updatePileList',
                    list,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },

        /**
         * 站点列表
         */
        *getWorkList({ options }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getSensitiveListApi, options);

                yield put({
                    type: 'updateWorkList',
                    list,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateStationOptions(state, { info }) {
            return {
                ...state,
                stationOptions: info,
            };
        },
        updateStationList(state, { list, total }) {
            return {
                ...state,
                stationList: list,
                stationListTotal: total,
            };
        },
        updatePileList(state, { list, total }) {
            return {
                ...state,
                pileList: list,
                pileListTotal: total,
            };
        },
        updateStationItemInfo(state, { info }) {
            SessionStorage.setItem('stationItemInfo', info);
            return {
                ...state,
                stationItemInfo: info,
            };
        },
        clearStationItemInfo(state) {
            SessionStorage.removeItem('stationItemInfo');

            return {
                ...state,
                stationItemInfo: null,
            };
        },
        updatePileItemInfo(state, { info }) {
            SessionStorage.setItem('pileItemInfo', info);
            return {
                ...state,
                pileItemInfo: info,
            };
        },
        clearPileItemInfo(state, { info }) {
            SessionStorage.removeItem('pileItemInfo');
            return {
                ...state,
                pileItemInfo: null,
            };
        },
        updateWorkList(state, { list, total }) {
            return {
                ...state,
                wordLost: list,
                wordLostTotal: total,
            };
        },
    },
};
export default InformationModel;
