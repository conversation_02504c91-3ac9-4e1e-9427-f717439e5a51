import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    Descriptions,
} from 'antd';
import { connect, useParams } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { getSyncFieldApi, getChangeFieldApi } from '@/services/AssetCenter/StationApi';
import { LeftOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const refundOptions = [
    { codeName: '订单坏账', codeValue: '0101' },
    { codeName: '坏账回收', codeValue: '0102' },
];

const timeTypes = [
    { codeName: '订单确认时间', codeValue: '01' },
    { codeName: '发生时间', codeValue: '02' },
];

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { codeInfo = {} },
    } = props;

    const { stationFieldManage, stationFieldManageStatus, stationServBusiStatus } = codeInfo;
    useEffect(() => {
        if (!stationFieldManage) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationFieldManage',
            });
        }
        if (!stationFieldManageStatus) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationFieldManageStatus',
            });
        }
        if (!stationServBusiStatus) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationServBusiStatus',
            });
        }
    }, []);

    const fieldTypeList = useMemo(() => {
        return (
            stationFieldManage?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || []
        );
    }, [stationFieldManage]);

    const dealStatusTypeList = useMemo(() => {
        return (
            dealStatusTypeList?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || []
        );
    }, [stationFieldManageStatus]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{}}
                scrollToFirstError
            >
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <FormItem label="变更字段:" name="modifyField" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                {fieldTypeList}
                            </Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const ChangeView = (props) => {
    const {
        serviceId,
        informationModel: { stationItemInfo },
    } = props;
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'changeStation');
    const [listLoading, updateListLoading] = useState(false);
    const [changeList, setChangeList] = useState([]);
    const [changeTotal, setChangeTotal] = useState(0);

    useEffect(() => {
        if (stationItemInfo) {
            if (stationItemInfo.modifyField) {
                form.setFieldsValue({
                    modifyField: stationItemInfo.modifyField,
                });
            }
        }
    }, [stationItemInfo]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                serviceId: serviceId,
                modifyField: data.modifyField || '',
            };
            updateListLoading(true);
            const {
                data: { list, total },
            } = await getChangeFieldApi(params);
            setChangeList(list);
            setChangeTotal(total);
            return data;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '互通时间',
            dataIndex: 'syncTime',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '变更字段',
            dataIndex: 'modifyFieldName',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '变更值',
            dataIndex: 'newValue',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '变更值匹配标签',
            dataIndex: 'newValueLabelName',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
    ];
    return (
        <Card title={<strong>变更记录</strong>} bordered={false}>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={changeList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: changeTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Card>
    );
};

const SyncView = (props) => {
    const { serviceId } = props;
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'syncStation');
    const [listLoading, updateListLoading] = useState(false);
    const [syncList, setSyncList] = useState([]);

    const [syncTotal, setSyncTotal] = useState(0);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                serviceId: serviceId,
            };

            updateListLoading(true);
            const {
                data: { list, total },
            } = await getSyncFieldApi(params);
            setSyncList(list);
            setSyncTotal(total);
            return data;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const asyncColumns = [
        {
            title: '处理时间',
            dataIndex: 'createTime',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '外显值',
            dataIndex: 'parkPriceDesc',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '外显标签',
            dataIndex: 'parkPriceLabelName',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '处理人',
            dataIndex: 'createBy',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
    ];

    return (
        <Card title={<strong>外显同步记录</strong>} bordered={false}>
            <TablePro
                name="outside"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={syncList}
                columns={asyncColumns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: syncTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Card>
    );
};

const StationInformationDetailPage = (props) => {
    const {
        dispatch,
        history,
        route,
        informationModel: { stationItemInfo },
        listLoading,
    } = props;

    const { serviceId } = useParams();

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
            content={
                <Descriptions>
                    <Descriptions.Item label="运营商">
                        {(stationItemInfo && stationItemInfo.buildName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="场站">
                        {(stationItemInfo && stationItemInfo.stationName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="场站编号">
                        {(stationItemInfo && stationItemInfo.stationNo) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="运营状态">
                        {(stationItemInfo && stationItemInfo.operatorStatusName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="是否开放">
                        {stationItemInfo && stationItemInfo.isOpen === '1' ? '是' : '否'}
                    </Descriptions.Item>

                    <Descriptions.Item label="是否隐藏">
                        {stationItemInfo && stationItemInfo.isHidden === '1' ? '是' : '否'}
                    </Descriptions.Item>
                    <Descriptions.Item label="最后处理时间">
                        {(stationItemInfo && stationItemInfo.dealTime) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="处理状态">
                        {(stationItemInfo && stationItemInfo.dealStatusName) || ''}
                    </Descriptions.Item>
                </Descriptions>
            }
        >
            <ChangeView {...props} serviceId={serviceId} />
            <br></br>
            <SyncView {...props} serviceId={serviceId} />
        </PageHeaderWrapper>
    );
};

export default connect(({ informationModel, global, loading }) => ({
    informationModel,
    global,
}))(StationInformationDetailPage);
