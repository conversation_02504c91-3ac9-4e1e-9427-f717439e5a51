import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    Popconfirm,
    message,
    Alert,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import {
    getSensitiveListApiPath,
    delSensitiveApi,
    delBatchSensitiveApi,
    addSensitiveApi,
    updateSensitiveApi,
    importSensitiveListApi,
    downloadSensitiveListFailExportApi,
    syncSensitive<PERSON>pi,
    getSyncSensitiveStatusApi,
    exportSensitiveListApi,
} from '@/services/AssetCenter/SensitiveApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import ImportModal from '@/components/ImportModal';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};
const editFormItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { dispatch, form, onSubmit, onReset, onExportForm, listLoading, global } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem label="敏感词:" name="sensitiveWord">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="替换词:" name="replaceWord">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const SensitiveManagePage = (props) => {
    const {
        dispatch,
        history,
        informationModel: { wordLost, wordLostTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();
    const [editForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState();
    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [editItem, changeEditItem] = useState(null);

    const [syncStatusInfo, changeSyncStatusInfo] = useState(null);

    const [showAddView, toggleAddView] = useState(false);

    const importModalRef = useRef();

    const alertMessage = useMemo(() => {
        let result = '新增、修改、删除敏感词字段，需要点击同步按钮才能生效';
        if (syncStatusInfo) {
            result = (
                <Space>
                    <span>新增、修改、删除敏感词字段，需要点击同步按钮才能生效</span>
                    <span className={styles['table-btn']}>
                        【最后一次同步时间：{syncStatusInfo.time} {syncStatusInfo.statusName}】
                    </span>
                </Space>
            );
        }
        return result;
    }, [syncStatusInfo]);

    useEffect(() => {
        initSyncStatus();
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        try {
            const data = await form.validateFields();
            const params = {
                sensitiveWord: data.sensitiveWord,
                replaceWord: data.replaceWord,
            };
            if (isDownload) {
                await exportSensitiveListApi(params);
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;
                dispatch({
                    type: 'informationModel/getWorkList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const initSyncStatus = async () => {
        try {
            const { data } = await getSyncSensitiveStatusApi();
            changeSyncStatusInfo(data);
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '敏感词',
            dataIndex: 'sensitiveWord',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '替换词',
            dataIndex: 'replaceWord',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            render: (text, record) => {
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );
                const delBtn = (
                    <Popconfirm
                        title={`确定删除？`}
                        okText="是"
                        cancelText="否"
                        onConfirm={() => deleteEvent(record)}
                    >
                        <span className={styles['table-btn']}>删除</span>
                    </Popconfirm>
                );

                let btns = [editBtn, delBtn];

                return <Space>{btns}</Space>;
            },
        },
    ];

    const editEvent = (item) => {
        changeEditItem(item);
        editForm.setFieldsValue({
            sensitiveWord: item.sensitiveWord,
            replaceWord: item.replaceWord,
        });
        toggleAddView(true);
    };

    const deleteEvent = async (item) => {
        try {
            await delSensitiveApi(item.id);
            await searchData();
            message.success('删除成功');

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const deleteBatchEvent = async () => {
        try {
            let params = {};
            const idList = selectItems.map((ele) => ele.id);
            params.idList = idList.filter((ele) => ele).join(',');
            await delBatchSensitiveApi(params);
            await searchData();
            message.success('删除成功');

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const openAddView = () => {
        editForm.resetFields();

        toggleAddView(true);
    };

    const closeAddView = () => {
        toggleAddView(false);
        editForm.resetFields();
        changeEditItem(null);
    };

    const onAddFinish = async (values) => {
        try {
            let params = {
                sensitiveWord: values.sensitiveWord,
                replaceWord: values.replaceWord || '',
            };
            if (editItem) {
                params.id = editItem.id;
                await updateSensitiveApi(params);
                message.success('编辑成功');
            } else {
                await addSensitiveApi(params);
                message.success('新增成功');
            }

            await searchData();
            closeAddView();

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.id),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            // disabled: record.invoiceAccessMode === '03', // Column configuration not to be checked
            name: record.id,
        }),
    };

    const batchDelSelectEvent = () => {
        if (selectItems?.length == 0) {
            message.error('请选择要删除的敏感词');
            return;
        }

        confirm({
            title: '确定删除选中敏感词?',
            icon: <ExclamationCircleOutlined />,
            content: '',
            onOk() {
                deleteBatchEvent();
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    const handlerSync = async () => {
        return new Promise((resolve, reject) => {
            confirm({
                title: '确定同步敏感词?',
                icon: <ExclamationCircleOutlined />,
                content: '',
                onOk: async () => {
                    resolve();
                },
                onCancel() {
                    console.log('Cancel');
                    reject();
                },
            });
        });
    };
    const syncSelectEvent = async () => {
        try {
            const syncInfo = await initSyncStatus();
            if (syncInfo && syncInfo?.statusName === '同步中') {
                message.error('当前正在同步敏感词，请勿重复操作');
                return;
            }

            await handlerSync();

            await syncSensitiveApi();
            await initSyncStatus();
            message.success('同步成功');
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />

                <Alert message={alertMessage} type="info" />

                <div className={styles['btn-bar']}>
                    <Space>
                        <Button type="primary" onClick={openAddView}>
                            新增
                        </Button>
                        <Button type="primary" onClick={() => importModalRef.current.show()}>
                            批量导入
                        </Button>
                        <Button onClick={batchDelSelectEvent}>批量删除</Button>
                        <Button onClick={syncSelectEvent}>同步敏感词</Button>
                    </Space>
                </div>

                <TablePro
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.id}
                    dataSource={wordLost}
                    columns={columns}
                    onChange={onTableChange}
                    bordered
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: wordLostTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
            <Modal
                title="敏感词"
                visible={showAddView}
                footer={false}
                onCancel={closeAddView}
                destroyOnClose
                maskClosable={false}
            >
                <Form
                    {...editFormItemLayout}
                    form={editForm}
                    onFinish={onAddFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <FormItem
                        label="敏感词"
                        name="sensitiveWord"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                    <FormItem
                        name="replaceWord"
                        label={
                            <Tooltip title={'放空表示去除敏感词'}>
                                替换词
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                    >
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                    <FormItem wrapperCol={{ offset: 8 }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeAddView}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
            <ImportModal
                title="批量导入"
                downLoadPath="/aliMini/xdt/static/excel/敏感词导入模版.xlsx"
                onUpload={async (formData, callback) => {
                    const {
                        ret,
                        data: { msg, serialNo },
                    } = await importSensitiveListApi(formData);

                    resetData();
                    callback &&
                        callback({
                            ret: serialNo ? 'false' : 'success',
                            importTime: serialNo,
                            msg: msg,
                        });
                }}
                onDoanLoadError={(serialNo) => {
                    downloadSensitiveListFailExportApi(serialNo);
                }}
                ref={importModalRef}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ informationModel, global, loading }) => ({
    informationModel,
    global,
    listLoading: loading.effects['informationModel/getWorkList'],
}))(SensitiveManagePage);
