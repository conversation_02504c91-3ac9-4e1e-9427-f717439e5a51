import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    message,
    Radio,
    Switch,
    Popconfirm,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { exportTableByParams, formatExportColumns, copyTextCommon } from '@/utils/utils';
import moment from 'moment';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import {
    syncStationFieldApi,
    switchIncrementApi,
    exportStationListApi,
} from '@/services/AssetCenter/StationApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import AllStationSelect from '@/components/AllStationSelect';
import CitysSelect from '@/components/CitysSelect/index.js';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';

import TextArea from 'antd/lib/input/TextArea';
import classnames from 'classnames';
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 80px',
    },
    labelAlign: 'left',
};

const modalFormItemLayout = {
    labelCol: {
        flex: '0 0 120px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const {
        dispatch,
        form,
        onSubmit,
        onReset,
        onExportForm,
        listLoading,
        global: { codeInfo = {} },
    } = props;

    const stationRef = useRef();

    const operGroupRef = useRef();
    const onSearchGroup = () => {
        operGroupRef.current.show();
    };

    const { stationFieldManage, stationFieldManageStatus, stationServBusiStatus } = codeInfo;
    useEffect(() => {
        if (!stationFieldManage) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationFieldManage',
            });
        }
        if (!stationFieldManageStatus) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationFieldManageStatus',
            });
        }
        if (!stationServBusiStatus) {
            dispatch({
                type: 'global/initMngCode',
                code: 'stationServBusiStatus',
            });
        }
    }, []);
    const fieldTypeList = useMemo(() => {
        return (
            stationFieldManage?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || []
        );
    }, [stationFieldManage]);

    const dealStatusTypeList = useMemo(() => {
        return (
            stationFieldManageStatus?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || []
        );
    }, [stationFieldManageStatus]);

    const stationServiceTypeList = useMemo(() => {
        return (
            stationServBusiStatus?.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )) || []
        );
    }, [stationServBusiStatus]);

    // const sensitiveTypeList = useMemo(() => {
    //     if (sensitiveTypeList) {
    //         return sensitiveTypeList?.map((ele) => (
    //             <Option key={ele.codeValue} value={ele.codeValue}>
    //                 {ele.codeName}
    //             </Option>
    //         ));
    //     }
    //     return [];
    // }, [sensitiveTypeList]);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const initOptionEvent = async () => {};

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    onSearchGroup={onSearchGroup}
                >
                    <Col span={8}>
                        <FormItem
                            label="互通时间:"
                            name="dates"
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value) {
                                            return Promise.reject('请选择日期');
                                        }
                                        if (!value[0]) {
                                            return Promise.reject('请选择开始日期');
                                        }
                                        if (!value[1]) {
                                            return Promise.reject('请选择结束日期');
                                        }
                                        if (value[0] && value[1]) {
                                            const startTime = +new Date(value[0]);
                                            const endTime = +new Date(value[1]);
                                            const dest = 60 * 1000 * 60 * 24 * 60;

                                            if (Math.abs(startTime - endTime) > dest) {
                                                return Promise.reject('选取范围最大不超过60天');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="变更字段:" name="modifyField">
                            <Select placeholder="请选择" allowClear>
                                {fieldTypeList}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="变更值:" name="newValue">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            onChange={onSubmit}
                            form={form}
                        />
                    </Col>

                    <Col span={8}>
                        <AllStationSelect form={form} ref={stationRef} label="场站名称" />
                    </Col>
                    <Col span={8}>
                        <FormItem label="场站编号:" name="stationNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <CitysSelect
                            label="城&nbsp;&nbsp;&nbsp;&nbsp;市:"
                            name="city"
                            // treeData={cityTreeData}
                            // loadData={onLoadDataEvent}
                            // onChange={changeTreeEvent}
                            provinceSelectable
                            placeholder="请选择"
                            rules={[]}
                        />
                    </Col>

                    <Col span={8}>
                        <FormItem label="含敏感词:" name="sensitiveType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>是</Option>
                                <Option value={'0'}>否</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="处理状态:" name="dealStatus">
                            <Select placeholder="请选择" allowClear>
                                {dealStatusTypeList}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="占位开关:" name="incrementSwitch">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>开启</Option>
                                <Option value={'0'}>关闭</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="运营状态:" name="operatorStatus">
                            <Select placeholder="请选择" allowClear>
                                {stationServiceTypeList}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="是否开放:" name="openType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>是</Option>
                                <Option value={'0'}>否</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="是否隐藏:" name="hiddenType">
                            <Select placeholder="请选择" allowClear>
                                <Option value={'1'}>是</Option>
                                <Option value={'0'}>否</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <FormItem noStyle name="stationIdList" />
                </SearchOptionsBar>
                <OperGroupImportModal
                    title="批量查询"
                    initRef={operGroupRef}
                    onConfirm={(addStationList) => {
                        const list =
                            (addStationList?.length &&
                                addStationList.map((item) => item.stationId)) ||
                            [];
                        form.setFieldsValue({ stationIdList: list });
                        const values = form.getFieldsValue();
                        onSubmit(values);
                    }}
                />
            </Form>
        </Fragment>
    );
};

const StationInformationListPage = (props) => {
    const {
        dispatch,
        history,
        informationModel: { stationList, stationListTotal },
        listLoading,
        global: { codeInfo = {} },
    } = props;
    const { parkPriceLabel: parkPriceLabelList } = codeInfo;
    const [form] = Form.useForm();
    const [asyncForm] = Form.useForm();

    const tableRef = useRef();

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [curSelectItems, changeCurSelectItems] = useState(null);

    const [showAsyncView, toggleAsyncView] = useState(false);
    useEffect(() => {
        if (!parkPriceLabelList) {
            dispatch({
                type: 'global/initCode',
                code: 'parkPriceLabel',
            });
        }
    }, []);
    const ModalView = useMemo(() => {
        if (curSelectItems) {
            return (
                <div>
                    <FormItem label="外显值" required>
                        <FormItem
                            noStyle
                            name="parkPriceDesc"
                            rules={[{ required: true, message: '请填写外显值' }]}
                        >
                            <TextArea placeholder="请填写" rows={6} maxLength={256}></TextArea>
                        </FormItem>
                        <div className="gray-color">注：外显值输入框默认自动填入变更值内容</div>
                    </FormItem>
                    <FormItem
                        label="外显标签"
                        name="parkPriceLabel"
                        extra="注：外显标签根据变更值内容由AI自动匹配"
                    >
                        <Select
                            placeholder="请选择"
                            allowClear
                            options={parkPriceLabelList}
                            fieldNames={{ label: 'codeName', value: 'codeValue' }}
                        />
                    </FormItem>

                    <FormItem label="原值">
                        {curSelectItems.originValue || '-'}
                        {curSelectItems.originValue && (
                            <span
                                className={classnames(styles['table-btn'], 'mg-l')}
                                onClick={() => {
                                    copyTextCommon(curSelectItems.originValue);
                                }}
                            >
                                复制
                            </span>
                        )}
                    </FormItem>
                    <FormItem label="变更值">
                        {curSelectItems.newValue || '-'}

                        {curSelectItems.newValue && (
                            <span
                                className={classnames(styles['table-btn'], 'mg-l')}
                                onClick={() => {
                                    copyTextCommon(curSelectItems.newValue);
                                }}
                            >
                                复制
                            </span>
                        )}
                    </FormItem>
                    <FormItem label="外显值（当前）">
                        <Space>
                            <span className="text-third">
                                {curSelectItems.parkPriceDesc || '-'}
                            </span>

                            {curSelectItems.parkPriceDesc && (
                                <span
                                    className={classnames(styles['table-btn'], 'mg-l')}
                                    onClick={() => {
                                        copyTextCommon(curSelectItems.parkPriceDesc);
                                    }}
                                >
                                    复制
                                </span>
                            )}
                        </Space>
                    </FormItem>
                    <FormItem label="外显标签（当前）">
                        <span className="text-third">
                            {curSelectItems.parkPriceLabelDisplayName || '-'}
                        </span>
                    </FormItem>
                </div>
            );
        } else {
            return (
                <div>
                    <FormItem label="同步外显" name="syncWay" initialValue={'0'}>
                        <Radio.Group>
                            <Radio value={'0'}>直接同步</Radio>
                            <Radio value={'1'}>修改同步</Radio>
                        </Radio.Group>
                    </FormItem>
                    <FormItem
                        noStyle
                        shouldUpdate={(pre, after) => {
                            return pre.syncWay != after.syncWay;
                        }}
                    >
                        {({ getFieldValue }) => {
                            const syncWay = getFieldValue('syncWay');
                            if (syncWay === '0') {
                                return (
                                    <FormItem
                                        name="openSensitive"
                                        label="替换敏感词"
                                        valuePropName="checked"
                                        initialValue
                                    >
                                        <Switch></Switch>
                                    </FormItem>
                                );
                            } else if (syncWay === '1') {
                                return (
                                    <>
                                        <FormItem
                                            name="parkPriceDesc"
                                            label="外显值"
                                            rules={[{ required: true, message: '请填写外显值' }]}
                                        >
                                            <TextArea
                                                placeholder="请填写"
                                                rows={6}
                                                maxLength={256}
                                            ></TextArea>
                                        </FormItem>
                                        <FormItem label="外显标签" name="parkPriceLabel">
                                            <Select
                                                placeholder="请选择"
                                                allowClear
                                                options={parkPriceLabelList}
                                                fieldNames={{
                                                    label: 'codeName',
                                                    value: 'codeValue',
                                                }}
                                            />
                                        </FormItem>
                                    </>
                                );
                            }
                        }}
                    </FormItem>
                </div>
            );
        }
    }, [selectItems, curSelectItems, parkPriceLabelList]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        try {
            const data = await form.validateFields();
            const params = {
                buildId: data.operId,
                beginTime:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endTime: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',

                modifyField: data.modifyField,
                newValue: data.newValue,
                stationId: data.stationId,
                stationNo: data.stationNo,
                sensitiveType: data.sensitiveType,
                dealStatus: data.dealStatus,
                operatorStatus: data.operatorStatus,
                openType: data.openType,
                hiddenType: data.hiddenType,
                incrementSwitch: data.incrementSwitch,
            };

            if (data.stationIdList instanceof Array && data.stationIdList.length > 0) {
                params.stationIdList = JSON.stringify(data.stationIdList);
            }

            if (data.city instanceof Array && data.city.length > 0) {
                params.city = JSON.stringify(data.city);
            }

            if (isDownload) {
                await exportStationListApi(params);
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;
                dispatch({
                    type: 'informationModel/getStationList',
                    options: params,
                });
            }

            return;
        } catch (error) {
            console.log(5555, error);
            return error;
        }
    };

    const resetData = () => {
        form.resetFields();
        changeSelectItems([]);
        changePageInfo({ pageIndex: 1 });
    };

    const toggleSeatEvent = async (state, item) => {
        try {
            await switchIncrementApi({
                stationServiceIds: item.serviceId,
                incrementSwitch: state,
            });
            message.success('操作成功');
            searchData();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '场站名称',
            width: 180,
            dataIndex: 'stationName',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '180px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '变更内容',
            children: [
                {
                    title: '互通时间',
                    width: 200,
                    dataIndex: 'syncTime',
                    render(text, record) {
                        return (
                            <div className="text-line" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '变更字段',
                    width: 140,
                    dataIndex: 'modifyFieldName',
                    render(text, record) {
                        return (
                            <div className="text-line" style={{ width: '140px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '原值',
                    width: 200,
                    dataIndex: 'originValue',
                    render(text, record) {
                        return (
                            <div className="text-third" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
                {
                    title: '变更值',
                    width: 200,
                    dataIndex: 'newValue',
                    render(text, record) {
                        return (
                            <div className="text-third" style={{ width: '200px' }} title={text}>
                                {text || '-'}
                            </div>
                        );
                    },
                },
            ],
        },
        {
            title: '变更值匹配标签',
            width: 200,
            dataIndex: 'newValueLabelName',
            render(text, record) {
                return (
                    <div className="text-third" style={{ width: '200px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: (
                <Tooltip title={`仅“停车费说明”涉及外显值`}>
                    外显值
                    <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                </Tooltip>
            ),
            width: 200,
            dataIndex: 'parkPriceDesc',
            render(text, record) {
                return (
                    <div className="text-third" style={{ width: '200px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '外显标签',
            width: 200,
            dataIndex: 'parkPriceLabelDisplayName',
            render(text, record) {
                return (
                    <div className="text-third" style={{ width: '200px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '最后处理时间',
            width: 200,
            dataIndex: 'dealTime',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '200px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '处理状态',
            width: 140,
            dataIndex: 'dealStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '收取占位费',
            width: 140,
            dataIndex: 'delayFeeFlagName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '占位开关',
            width: 140,
            dataIndex: 'incrementSwitchName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '场站编号',
            width: 140,
            dataIndex: 'stationNo',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '140px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '运营状态',
            width: 120,
            dataIndex: 'operatorStatusName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '是否开放',
            width: 80,
            dataIndex: 'isOpenName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '是否隐藏',
            width: 80,
            dataIndex: 'isHiddenName',
            render(text, record) {
                return <span title={text}> {text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 140,

            fixed: 'right',
            render: (text, record) => {
                let btns = [];

                const asyncBtn = (
                    <span
                        className={styles['table-btn']}
                        onClick={(event) => {
                            event.stopPropagation();
                            asyncStationItemEvent(record);
                        }}
                    >
                        外显同步
                    </span>
                );
                if (record.modifyField === 'parkPriceDesc') {
                    btns.push(asyncBtn);
                }
                const detailBtn = (
                    <span className={styles['table-btn']} onClick={() => goDetailEvent(record)}>
                        详情
                    </span>
                );

                const closeSeatBtn = (
                    <Popconfirm
                        title={`确定关闭？`}
                        okText="是"
                        cancelText="否"
                        onConfirm={() => toggleSeatEvent(0, record)}
                    >
                        <span className={styles['table-btn']}>关闭占位</span>
                    </Popconfirm>
                );

                const openSeatBtn = (
                    <Popconfirm
                        title={`确定开启？`}
                        okText="是"
                        cancelText="否"
                        onConfirm={() => toggleSeatEvent(1, record)}
                    >
                        <span className={styles['table-btn']}>开启占位</span>
                    </Popconfirm>
                );

                if (record.modifyField === 'incrementExplain') {
                    if (record.incrementSwitch == '1') {
                        btns.push(closeSeatBtn);
                    } else if (record.incrementSwitch == '0') {
                        btns.push(openSeatBtn);
                    }
                }

                btns.push(detailBtn);

                return <Space>{btns}</Space>;
            },
        },
    ];

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.id),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            // disabled: record.modifyField === 'incrementExplain', // Column configuration not to be checked
            name: record.id,
        }),
    };

    const asyncStationsEvent = () => {
        if (selectItems?.length == 0) {
            message.error('请选择要同步的站点');
            return;
        }
        openAsyncView();
    };

    const asyncStationsSeatEvent = (state) => {
        if (selectItems?.length == 0) {
            message.error('请选择要变更的站点');
            return;
        }
        let stateTypeText = '';
        if (state == '1') {
            stateTypeText = '开启';
        } else if (state == '0') {
            stateTypeText = '关闭';
        }
        // let hasStateItems = [];
        // for (const item of selectItems) {
        //     if (item.incrementSwitch === state) {
        //         hasStateItems.push(item.stationName);
        //     }
        // }
        let contentText = '';
        // if (hasStateItems?.length > 0) {
        //     contentText = `${hasStateItems.join('、')}已${stateTypeText}`;
        // }
        confirm({
            title: `确认${stateTypeText}选中站点占位开关`,
            content: contentText,
            okText: '确定',
            cancelText: '取消',
            onOk() {
                switchIncrementApi({
                    stationServiceIds: selectItems?.map((ele) => ele.serviceId).join(','),
                    incrementSwitch: state,
                }).then((data) => {
                    message.success('操作成功');
                    changeSelectItems([]);
                    searchData();
                });
            },
            onCancel() {},
        });
    };

    const asyncStationItemEvent = (item) => {
        changeCurSelectItems(item);
        asyncForm.setFieldsValue({
            parkPriceDesc: item.newValue,
            parkPriceLabel: item.newValueLabel,
        });
        openAsyncView();
    };

    const goDetailEvent = (item) => {
        dispatch({
            type: 'informationModel/updateStationItemInfo',
            info: item,
        });
        history.push(`/assetCenter/informationManage/station/list/detail/${item.serviceId}`);
    };

    const openAsyncView = () => {
        toggleAsyncView(true);
    };

    const closeAsyncView = () => {
        toggleAsyncView(false);
        asyncForm.resetFields();
        changeSelectItems([]);
        changeCurSelectItems(null);
    };

    const onAsyncFinish = async (values) => {
        try {
            let params = {
                parkPriceDesc: values.parkPriceDesc,
                parkPriceLabel: values.parkPriceLabel,
            };
            if (curSelectItems) {
                params.idList = curSelectItems.id;
                params.syncWay = '1';
            } else {
                const ids = selectItems.map((ele) => ele.id);
                params.idList = ids.join(',');
                params.openSensitive = values.openSensitive ? '1' : '0';
                params.syncWay = values.syncWay;
            }
            await syncStationFieldApi(params);
            message.success('同步成功');
            await searchData();
            closeAsyncView();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const canIAsyncSelect = useMemo(() => {
        if (selectItems instanceof Array) {
            const findSeatItem = selectItems.find((ele) => ele.modifyField === 'incrementExplain');
            if (findSeatItem) {
                return false;
            }
        }
        return true;
    }, [selectItems]);

    const canISeatSelect = useMemo(() => {
        if (selectItems instanceof Array) {
            const findSeatItem = selectItems.find((ele) => ele.modifyField === 'parkPriceDesc');
            if (findSeatItem) {
                return false;
            }
        }
        return true;
    }, [selectItems]);

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    onExportForm={() => searchData(true)}
                />

                <Space className={styles['btn-bar']}>
                    <Tooltip title={'只能选择停车费说明的数据同步'}>
                        <Button
                            type="primary"
                            onClick={asyncStationsEvent}
                            disabled={!canIAsyncSelect}
                        >
                            外显同步
                        </Button>
                    </Tooltip>
                    <Tooltip title={'只能选择附加费说明的数据同步'}>
                        <Button
                            type="primary"
                            onClick={() => {
                                asyncStationsSeatEvent('1');
                            }}
                            disabled={!canISeatSelect}
                        >
                            占位开启
                        </Button>
                    </Tooltip>
                    <Tooltip title={'只能选择附加费说明的数据同步'}>
                        <Button
                            onClick={() => {
                                asyncStationsSeatEvent('0');
                            }}
                            disabled={!canISeatSelect}
                        >
                            占位关闭
                        </Button>
                    </Tooltip>
                </Space>

                <TablePro
                    ref={tableRef}
                    rowSelection={
                        (stationList?.length && {
                            type: 'checkbox',
                            ...rowSelection,
                        }) ||
                        undefined
                    }
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => record.id}
                    dataSource={stationList}
                    columns={columns}
                    onChange={(page, filters, sorter) => {
                        changeSelectItems([]);
                        onTableChange(page, filters, sorter);
                    }}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: stationListTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
            <Modal
                title="停车费说明"
                visible={showAsyncView}
                footer={false}
                onCancel={closeAsyncView}
                destroyOnClose
                maskClosable={false}
                width={600}
            >
                <Form
                    {...modalFormItemLayout}
                    form={asyncForm}
                    onFinish={onAsyncFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    {ModalView}

                    <FormItem wrapperCol={{ offset: 8 }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                同步
                            </Button>
                            <Button onClick={closeAsyncView}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ informationModel, global, loading }) => ({
    informationModel,
    global,
    listLoading: loading.effects['informationModel/getStationList'],
}))(StationInformationListPage);
