import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    Descriptions,
} from 'antd';
import { connect, useParams } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { getPileChangeFieldApi } from '@/services/AssetCenter/PileApi';
import { LeftOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const refundOptions = [
    { codeName: '订单坏账', codeValue: '0101' },
    { codeName: '坏账回收', codeValue: '0102' },
];

const timeTypes = [
    { codeName: '订单确认时间', codeValue: '01' },
    { codeName: '发生时间', codeValue: '02' },
];

const SearchLayout = (props) => {
    const { dispatch, form, onSubmit, onReset, onExportForm, listLoading, global } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{}}
                scrollToFirstError
            >
                <SearchOptionsBar loading={listLoading} onReset={resetForm}>
                    <Col span={8}>
                        <FormItem label="变更字段:" name="modifyField" {...formItemLayout}>
                            <Select placeholder="请选择" allowClear>
                                <Option value={'incrementExplain'}>附加费说明</Option>
                            </Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const ChangeView = (props) => {
    const {
        equipId,
        informationModel: { pileItemInfo },
    } = props;
    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, 'changeStation');
    const [listLoading, updateListLoading] = useState(false);
    const [changeList, setChangeList] = useState([]);
    const [changeTotal, setChangeTotal] = useState(0);

    useEffect(() => {
        if (pileItemInfo) {
            if (pileItemInfo.modifyField) {
                form.setFieldsValue({
                    modifyField: pileItemInfo.modifyField,
                });
            }
        }
    }, [pileItemInfo]);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                equipId: equipId,
                modifyField: data.modifyField || '',
            };
            updateListLoading(true);
            const {
                data: { list, total },
            } = await getPileChangeFieldApi(params);
            setChangeList(list);
            setChangeTotal(total);
            return data;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '互通时间',
            dataIndex: 'syncTime',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '变更字段',
            dataIndex: 'modifyFieldName',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
        {
            title: '变更值',
            dataIndex: 'newValue',
            render(text, record) {
                return <span>{text}</span>;
            },
        },
    ];
    return (
        <Card title={<strong>变更记录</strong>} bordered={false}>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />

            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={changeList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: changeTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Card>
    );
};

const PileInformationDetailPage = (props) => {
    const {
        dispatch,
        history,
        route,
        informationModel: { pileItemInfo },
        listLoading,
    } = props;

    const { equipId } = useParams();
    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
            content={
                <Descriptions>
                    <Descriptions.Item label="运营商">
                        {(pileItemInfo && pileItemInfo.buildName) || ''}
                    </Descriptions.Item>
                    <Descriptions.Item label="场站">
                        {(pileItemInfo && pileItemInfo.stationName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="电桩名称">
                        {(pileItemInfo && pileItemInfo.equipName) || ''}
                    </Descriptions.Item>

                    <Descriptions.Item label="电桩编号">
                        {(pileItemInfo && pileItemInfo.equipNo) || ''}
                    </Descriptions.Item>
                </Descriptions>
            }
        >
            <ChangeView {...props} equipId={equipId} />
        </PageHeaderWrapper>
    );
};

export default connect(({ informationModel, global, loading }) => ({
    informationModel,
    global,
    listLoading: loading.effects['informationModel/getStationList'],
}))(PileInformationDetailPage);
