import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { connect } from 'umi';
import commonStyles from '@/assets/styles/common.less';
import { Card, Form, Checkbox, Button, message, Modal, Table } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import OperStationSearchList from '@/components/OperStationSearchList/SearchList';
import OperSearchList from './OperSearchList/SearchList';
import TablePro from '@/components/TablePro';
import {
    plugvinStationInfoApi,
    plugvinStationSaveApi,
} from '@/services/AssetCenter/PlugAndChargeApi';
import { useImperativeHandle } from 'react';
const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

const formItemFixedWidthLayout = {
    ...formItemLayout,
    wrapperCol: {
        span: 8,
    },
};

const ErrorStationModal = (props) => {
    const { initRef, onAdd, onDelete } = props;

    const [errorStationsList, updateErrorStationsList] = useState(false);
    const [errorStationVisible, updateErrorStationVisible] = useState(false);

    useImperativeHandle(initRef, () => ({
        show: (list) => {
            updateErrorStationsList(list);
            updateErrorStationVisible(true);
        },
    }));

    const onClose = () => {
        updateErrorStationVisible(false);
    };

    // 一键补齐
    const addEvent = (item) => {
        onAdd?.(item);

        // 处理完毕后，需要把场站从遗漏列表里面移除，并且把城市和运营商相匹配的场站都移除
        for (const ele of errorStationsList) {
            if (ele.city == item.city) {
                ele.cityFlag = undefined;
            }
            if (ele.buildId == item.buildId || ele.buildId == item.operId) {
                ele.operatorFlag = undefined;
            }
        }
        // 把城市和运营商都没问题的场站从列表里移除
        for (let index = errorStationsList.length - 1; index >= 0; index--) {
            const element = errorStationsList[index];
            if (!element.cityFlag && !element.operatorFlag) {
                errorStationsList.splice(index, 1);
            }
        }
        updateErrorStationsList([...errorStationsList]);
    };

    // 一键删除
    const deleteEvent = (item) => {
        onDelete?.(item);

        // 处理完毕后，需要把场站从遗漏列表里面移除
        errorStationsList.splice(errorStationsList.indexOf(item), 1);
        updateErrorStationsList([...errorStationsList]);
    };

    const columns = [
        {
            title: '场站 ',
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商 ',
            dataIndex: 'buildName',
            width: '120px',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.operatorFlag ? 'red' : 'black' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '城市 ',
            dataIndex: 'cityName',
            width: '120px',
            render(text, record) {
                return (
                    <span title={text} style={{ color: record.cityFlag ? 'red' : 'black' }}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '操作 ',
            fixed: 'right',
            width: '200px',
            render(text, record, index) {
                return [
                    <Button
                        type="link"
                        size="small"
                        key="add"
                        onClick={() => {
                            addEvent(record);
                        }}
                    >
                        一键补齐
                    </Button>,
                    <Button
                        type="link"
                        size="small"
                        key="delete"
                        onClick={() => {
                            deleteEvent(record);
                        }}
                    >
                        删除场站
                    </Button>,
                ];
            },
        },
    ];
    return (
        <Modal
            title="配置遗漏"
            visible={errorStationVisible}
            onCancel={onClose}
            width={680}
            footer={false}
        >
            <div
                style={{
                    maxHeight: `${document.body.clientHeight - 270}px`,
                    overflowY: 'auto',
                }}
            >
                {(errorStationsList?.length && (
                    <Fragment>
                        <p>标红内容在“运营商”的勾选中有遗漏，需处理完毕后再重新提交</p>
                        <TablePro
                            name="Table"
                            scroll={{ x: '100%' }}
                            rowKey={(record, index) => index}
                            dataSource={errorStationsList}
                            columns={columns}
                            pagination={false}
                            filterHeader={false}
                            offsetHeader={0}
                        />
                    </Fragment>
                )) || <span>已处理完成，请重新提交</span>}
            </div>
        </Modal>
    );
};

const ChannelConfigurePage = (props) => {
    const {
        dispatch,
        history,
        route,
        currentUser,
        global: { operatorList },
        listLoading,
    } = props;

    const channelInfo = props.location.state;
    const [form] = Form.useForm();
    const [submitLoading, changeSubmitLoading] = useState(true); // 详情全部加载完成后才允许提交
    const operatorSelectRef = useRef();
    const openRef = useRef();
    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                payload: { filterAbandon: false },
            });
        }
    }, []);

    useEffect(() => {
        loadData();
    }, []);

    const [info, updateInfo] = useState();
    const channelId = props.location.query.id;
    const loadData = async () => {
        try {
            changeSubmitLoading(true);
            const { data } = await plugvinStationInfoApi();
            updateInfo(data);
            const params = {
                ...data,
                channelId,
                operId: data.operator?.split?.(',') || data.operator || undefined,
                cityList: data.city?.split?.(',') || data.city || undefined,
                occupying: undefined,
                license: undefined,
                tickets: undefined,
            };
            const spical = [];
            if (data.occupying == '1') {
                // 开放占位费
                spical.push('2');
            }
            if (data.license == '1') {
                // 开放必填车牌号
                spical.push('1');
            }
            if (data.tickets == '1') {
                // 开放停车小票
                spical.push('3');
            }
            if (data.floorLock == '1') {
                // 开放地锁
                spical.push('4');
            }
            params.spical = spical;
            form.setFieldsValue(params);
        } catch (error) {
        } finally {
            changeSubmitLoading(false);
        }
    };

    const errorRef = useRef();
    const getErrorStations = async (values) => {
        const errorStations = [];
        values.openStation?.map?.((ele) => {
            if (values.cityList?.length > 0 && values.cityList.indexOf(ele.city) == -1) {
                ele.cityFlag = true;
            }
            if (!values.operId || values.operId.indexOf(ele.buildId) == -1) {
                ele.operatorFlag = true;
            }
            if (ele.cityFlag || ele.operatorFlag) {
                errorStations.push(ele);
            } else {
                ele.cityFlag = undefined;
                ele.operatorFlag = undefined;
            }
        });
        return errorStations;
    };
    const saveEvent = () => {
        form.validateFields().then(async (values) => {
            try {
                changeSubmitLoading(true);
                // 抽离出开放场站的城市和运营商，判断是否都已手动选中
                // 定义cityFlag为城市空缺，定义operatorFlag为运营商空缺
                const errorStations = await getErrorStations(values);
                if (errorStations.length > 0) {
                    errorRef.current.show(errorStations);
                    changeSubmitLoading(false);
                    return;
                }
                const params = {
                    channelId,
                    operator: values.operId?.join?.(','),
                    city: values.cityList?.join?.(','),
                    openStation: values.openStation?.map?.((ele) => ele.stationId)?.join?.(','),
                    noOpenStation: values.noOpenStation?.map?.((ele) => ele.stationId)?.join?.(','),
                    spical: undefined,
                    dockingMode: channelInfo?.dockingMode,
                };
                if (values.spical?.indexOf?.('1') >= 0) {
                    params.license = '1';
                }
                if (values.spical?.indexOf?.('2') >= 0) {
                    params.occupying = '1';
                }
                if (values.spical?.indexOf?.('3') >= 0) {
                    params.tickets = '1';
                }
                if (values.spical?.indexOf?.('4') >= 0) {
                    params.floorLock = '1';
                }
                await plugvinStationSaveApi(params);
                message.success('提交成功');
                // goBack();
            } catch (error) {
            } finally {
                changeSubmitLoading(false);
            }
        });
    };

    const goBack = () => {
        history.go(-1);
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card>
                <Form form={form} scrollToFirstError>
                    <FormItem noStyle name="channelId" />
                    <Fragment>
                        {/* <CitysSelect
                            label="开放城市"
                            name="cityList"
                            placeholder="请选择"
                            formItemLayout={formItemFixedWidthLayout}
                            showArrow
                            provinceSelectable
                            rules={null}
                        /> */}

                        <OperSearchList
                            label="开放运营商"
                            name="operId"
                            initRef={operatorSelectRef}
                            form={form}
                            formItemLayout={formItemLayout}
                            // 非图层则必填
                            rules={[
                                {
                                    message: '请选择运营商',
                                    required: true,
                                },
                            ]}
                            operList={info?.buildList}
                            required
                            callbackOrigin
                            cooperationPlatform={'xdt'}
                        />

                        {/* {channelInfo?.dockingMode != '0' && (
                            <FormItem label="特殊场站开放" name={'spical'} {...formItemLayout}>
                                <Checkbox.Group
                                    options={[
                                        {
                                            label: '开放必填车牌场站',
                                            value: '1',
                                        },
                                        {
                                            label: '开放占位费场站',
                                            value: '2',
                                        },
                                        {
                                            label: '开放停车小票场站',
                                            value: '3',
                                        },
                                        {
                                            label: '开放地锁场站',
                                            value: '4',
                                        },
                                    ]}
                                />
                            </FormItem>
                        )} */}

                        {(operatorList.length && (
                            <OperStationSearchList
                                title="支持场站"
                                form={form}
                                formItemLayout={formItemLayout}
                                initRef={openRef}
                                stationList={info?.openStation || []}
                                currentUser={currentUser}
                                keyName="openStation"
                                cooperationPlatform={'xdt'}
                            />
                        )) ||
                            null}
                    </Fragment>

                    {(operatorList.length && (
                        <OperStationSearchList
                            title="不支持场站"
                            form={form}
                            formItemLayout={formItemLayout}
                            stationList={info?.noOpenStation || []}
                            currentUser={currentUser}
                            keyName="noOpenStation"
                            cooperationPlatform={'xdt'}
                        />
                    )) ||
                        null}

                    <FormItem {...formItemFixedWidthLayout}>
                        <div className={commonStyles['form-submit']}>
                            <Button
                                className={commonStyles['form-btn']}
                                type="primary"
                                loading={submitLoading || listLoading}
                                onClick={() => {
                                    saveEvent();
                                }}
                            >
                                提交
                            </Button>
                            <Button className={commonStyles['form-btn']} onClick={goBack}>
                                返回
                            </Button>
                        </div>
                    </FormItem>
                </Form>
            </Card>

            <ErrorStationModal
                initRef={errorRef}
                onAdd={(station) => {
                    const cityList = form.getFieldValue('cityList') || [];
                    if (station.cityFlag) {
                        cityList.push(station.city);
                    }
                    if (station.operatorFlag) {
                        operatorSelectRef.current.addOperId(station.buildId);
                    }
                    form.setFieldsValue({ cityList });
                }}
                onDelete={(station) => {
                    openRef.current?.deleteAtIndex?.(station?.stationId);
                }}
            />
        </PageHeaderWrapper>
    );
};

export default connect(({ global, loading, user }) => ({
    global,
    currentUser: user.currentUser,

    listLoading: loading.effects['global/getOperatorList'],
}))(ChannelConfigurePage);
