import React, { useEffect, useRef, useState } from 'react';
import { Form, Modal } from 'antd';

import OperSelectItem from '@/components/OperSelectItem';

const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};

export const AddOperModal = (props) => {
    const { visible, onFinish, allOpers, operatorList } = props;
    const [operForm] = Form.useForm();
    const operatorSelectRef = useRef();

    useEffect(() => {
        if (visible) {
            operForm.setFieldsValue({ operId: undefined });
            // operatorSelectRef.current?.rest();
        }
    }, [visible]);

    const onOk = () => {
        operForm.validateFields().then(() => {
            const list = operatorSelectRef?.current?.operItems;
            const alreadyOpers = []; // 记录已存在的运营商信息
            const successOpers = []; // 可成功导入的运营商

            let allOpersInfo = {};

            allOpers?.forEach((operItem) => {
                allOpersInfo[operItem.operId] = operItem;
            });

            list?.forEach((operItem) => {
                // 查询是否已添加过
                const res = allOpersInfo[operItem.operId];
                if (res) {
                    alreadyOpers.push(res.operName);
                } else {
                    // 未匹配到
                    successOpers.push(operItem);
                }
            });

            if (alreadyOpers?.length) {
                confirm({
                    title: `以下场站已添加过，本次操作将添加${
                        successOpers.length || 0
                    }个，确定要继续吗？`,
                    content: (
                        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                            {alreadyOpers?.join('、')}
                        </div>
                    ),
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        onFinish && onFinish(successOpers);
                    },
                });
            } else {
                onFinish && onFinish(successOpers);
            }
        });
    };

    return (
        <Modal
            title="添加运营商"
            visible={visible}
            onOk={onOk}
            onCancel={() => onFinish()}
            // width={800}
            maskClosable={false}
        >
            <Form
                form={operForm}
                name="operForm"
                initialValues={{ rangeType: '1' }}
                {...formItemLayout}
            >
                <OperSelectItem
                    name="operId"
                    initRef={operatorSelectRef}
                    formItemLayout={formItemLayout}
                    rules={[{ message: '请选择运营商', required: true }]}
                    mode="multiple"
                    // maxTagCount="responsive"
                    showArrow
                    required
                    callbackOrigin
                    initialValues={[]}
                    operatorList={operatorList}
                />
            </Form>
        </Modal>
    );
};
