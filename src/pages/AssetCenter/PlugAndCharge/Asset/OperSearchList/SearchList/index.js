import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import {
    Button,
    Form,
    Modal,
    Space,
    Input,
    Popconfirm,
    Descriptions,
    message,
    Radio,
    Checkbox,
    Col,
    Row,
    Spin,
} from 'antd';

import TablePro from '@/components/TablePro';

import { PlusOutlined } from '@ant-design/icons';
import OperGroupImportModal from '../OperGroupImportModal';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import { AddOperModal } from './AddOperModal';

import { getOperListApi } from '@/services/CommonApi';

const { confirm } = Modal;
const FormItem = Form.Item;

const StationList = ({
    formItemLayout,
    allOpers,
    refreshAllOpers,
    form,
    disabled,
    keyName,
    deleteAllEnabled,
    initRef,
}) => {
    const [searchForm] = Form.useForm(); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
    const [searchParams, updateSearchParams] = useState();

    // 用于过滤出要显示的场站，兼容查询操作
    const showOpers = useMemo(() => {
        return (
            allOpers?.filter?.((item) => {
                // 如果没有筛选，直接返回true用于展示
                if (Object.values(searchParams || {})?.length == 0) {
                    return true;
                }
                let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。
                if (searchParams.operId?.length && passFilter) {
                    passFilter =
                        item.buildId === searchParams.operId || item.operId === searchParams.operId;
                }
                return passFilter;
            }) || []
        );
    }, [allOpers, searchParams]);

    const stationColumns = useMemo(() => {
        const columns = [
            {
                title: '序号 ',
                width: 60,
                render(text, record, index) {
                    return <span title={index}>{index + 1}</span>;
                },
            },
            {
                title: '运营商编号',
                dataIndex: 'operId',
                width: 140,
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '运营商简称',
                dataIndex: 'operNickname',
                width: 140,
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
            {
                title: '运营商全称',
                dataIndex: 'operName',
                width: 160,
                render(text, record) {
                    return <span title={text}>{text || '-'}</span>;
                },
            },
        ];
        if (!disabled) {
            columns.push({
                title: '操作 ',
                width: 60,
                render(text, record, index) {
                    return (
                        <Popconfirm
                            title={`确定要删除此运营商？`}
                            onConfirm={() => {
                                const allDatas = allOpers;
                                deleteSelectRows(false, allDatas.indexOf(record));
                            }}
                        >
                            <a>删除</a>
                        </Popconfirm>
                    );
                },
            });
        }
        return columns;
    }, [disabled, allOpers]);

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
        form.setFieldsValue({
            [keyName]: allOpers?.map((ele) => ele.operId) || allOpers,
        });
    };

    // 有选中的场站
    const hasSelectedOpers = useMemo(() => {
        return selectedRowKeys.length > 0;
    }, [selectedRowKeys]);

    const onSelectChange = (_selectedRowKeys) => {
        updateSelectedRowKeys(_selectedRowKeys);
    };

    const deleteSelectRows = async (isAll = false, indexes) => {
        try {
            const allDatas = allOpers;
            let total = 0;
            if (isAll) {
                total = allDatas.length || 0;
                refreshAllOpers && refreshAllOpers([]);
            } else {
                if (indexes instanceof Array) {
                    for (let i = indexes.length - 1; i >= 0; i--) {
                        const operId = indexes[i];
                        const stationObj = allDatas.find((ele) => ele.operId == operId);
                        const index = allDatas.indexOf(stationObj);
                        if (index >= 0) {
                            total += 1;
                            allDatas.splice(index, 1);
                            refreshAllOpers && refreshAllOpers([...allDatas]);
                        }
                    }
                    message.success(`成功删除${total}条数据`);
                } else {
                    if (indexes >= 0) {
                        allDatas.splice(indexes, 1);
                        refreshAllOpers && refreshAllOpers([...allDatas]);
                    }
                }
            }
            updateSelectedRowKeys([]);
        } catch (error) {}
    };

    // 从接口拉取数据的属性维护
    useImperativeHandle(initRef, () => ({
        resetData,
        deleteAtIndex: (operId) => {
            const allDatas = allOpers;
            const index = allDatas.findIndex((ele) => ele.operId == operId);
            if (index >= 0) {
                deleteSelectRows(false, index);
            } else {
                console.log('删除的场站不在列表中');
            }
        },
    }));

    const [isWaiting, updateWaiting] = useState(false);

    const resetData = () => {
        searchForm.resetFields();
    };

    return (
        <FormItem {...formItemLayout}>
            <Form form={searchForm} labelAlign="right">
                <Row gutter={24}>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            label=""
                            {...formItemLayout}
                            form={searchForm}
                        />
                    </Col>
                    <Col>
                        <Space>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    const params = await searchForm.getFieldsValue();
                                    updateSearchParams((params && { ...params }) || undefined);
                                    changePageNum(1);
                                    updateSelectedRowKeys([]);
                                }}
                            >
                                查询
                            </Button>

                            <Button
                                onClick={() => {
                                    resetData();
                                    updateSearchParams(undefined);
                                }}
                            >
                                重置
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Form>

            {(!disabled && false && (
                <p>
                    <Space>
                        <Button
                            type="primary"
                            disabled={!hasSelectedOpers}
                            onClick={() => deleteSelectRows(false, selectedRowKeys)}
                        >
                            删除选中项
                        </Button>

                        {(deleteAllEnabled && (
                            <Popconfirm
                                title="即将全部删除，请二次确认"
                                onConfirm={() => deleteSelectRows(true, selectedRowKeys)}
                            >
                                <Button danger disabled={!allOpers || allOpers.length == 0}>
                                    全部删除
                                </Button>
                            </Popconfirm>
                        )) ||
                            null}
                    </Space>
                </p>
            )) ||
                null}
            <FormItem noStyle name={keyName}></FormItem>

            <Spin spinning={isWaiting}>
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.operId}
                    dataSource={showOpers}
                    onChange={changePageInfo}
                    pagination={{
                        current: pageNum,
                        total: showOpers.length,
                        pageSize: pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    expandable={{ defaultExpandAllRows: true }}
                    columns={stationColumns}
                    sticky={{ offsetHeader: 64 }}
                    rowSelection={
                        !disabled &&
                        false && {
                            selectedRowKeys,
                            onChange: onSelectChange,
                            getCheckboxProps: (record) => ({
                                disabled: !record.operId,
                            }),
                        }
                    }
                />
            </Spin>
        </FormItem>
    );
};

// 商家营销子编辑页面
const OperSearchList = (props, ref) => {
    const {
        title = '运营商',
        formItemLayout,
        form,
        operList,
        disabled,
        keyName = 'operId',
        required,
        addEnabled = true, // 是否可新增
        deleteAllEnabled = true, // 是否可全部删除
        limitOperIds, // 限制哪几个运营商可选中，如果配置了，查询维度只支持运营商，无切换维度和添加按钮，且传参格式限制为[{operId, citys:[city1, city2]}]，其中citys为可选
        initRef,
        disabledOperIds, // 某些场站禁用，内部会自动拼已选场站不可再被勾选的逻辑
        hasReasonText,
        cooperationPlatform,
    } = props;

    const [initFlag, setInitFlag] = useState(true);
    const [allOpers, updateAllOpers] = useState([]);
    useEffect(() => {
        if (initFlag && operList?.length) {
            const list = typeof operList == 'string' ? operList.split(',') : operList;
            // 只有初始化的时候才和外部数据做关联，后续全部以自身维护的数据为参照
            refreshAllOpers((list?.length && [...list]) || []);
            setInitFlag(false);
        }
    }, [operList]);

    useEffect(() => {
        initOpers();
    }, []);
    const [operatorList, updateOperatorList] = useState([]);
    const initOpers = async () => {
        if (!operatorList?.length) {
            const {
                data: { operList: list },
            } = await getOperListApi({ filterAbandon: 1, cooperationPlatform });
            updateOperatorList(list);
        }
    };

    const disabledIds = useMemo(() => {
        const ids = [...(disabledOperIds || [])];
        allOpers?.map((ele) => {
            ids.push(ele.operId);
        });
        return ids;
    }, [disabledOperIds, allOpers]);

    const listRef = useRef();
    useImperativeHandle(initRef, () => ({
        reset: () => {
            // 清空数据
            refreshAllOpers([]);
        },
        hasOpers: () => {
            // 获取是否已配置场站
            return allOpers.length > 0;
        },
        reload: () => {
            // 如果是走接口刷新列表的，开放刷新事件
        },
        deleteAtIndex: (operId) => {
            listRef?.current.deleteAtIndex?.(operId);
        },
        addOperId: (operId) => {
            // 添加运营商对象，暂时不考虑重复等问题
            const operItem = operatorList?.filter?.((ele) => ele.operId == operId) || [];
            const opers = [...operItem, ...allOpers];
            refreshAllOpers(opers);
            form?.setFieldsValue({ [keyName]: opers?.map((ele) => ele.operId) || opers });
        },
        operatorList,
    }));

    const refreshAllOpers = (opers) => {
        // 同时要更新form，用于外部页面通过form的方法来获取数据
        updateAllOpers([...opers]);
        form?.setFieldsValue({ [keyName]: opers?.map((ele) => ele.operId) || opers });
    };

    // 添加运营商
    const operGroupRef = useRef();
    const [operVisible, setOperVisible] = useState(false);

    const showOperModal = () => {
        setOperVisible(true);
    };

    const hideOperModal = () => {
        setOperVisible(false);
    };

    return (
        <Fragment>
            {(disabled && title?.length == 0) || !addEnabled || !form ? null : (
                <FormItem
                    label={title}
                    {...formItemLayout}
                    name="empty"
                    rules={[
                        () => ({
                            validator(rule, value) {
                                if (allOpers?.length == 0 && required && !disabled) {
                                    return Promise.reject('请选择');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                    required={required}
                >
                    {(!disabled && (
                        <Space>
                            <Button type="primary" onClick={showOperModal}>
                                <PlusOutlined />
                                添加运营商
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => operGroupRef.current.show(allOpers)}
                            >
                                <PlusOutlined />
                                批量导入
                            </Button>
                        </Space>
                    )) ||
                        null}
                </FormItem>
            )}

            {form ? (
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        return (
                            (allOpers?.length && (
                                <StationList
                                    allOpers={allOpers}
                                    refreshAllOpers={refreshAllOpers}
                                    formItemLayout={
                                        formItemLayout || {
                                            ...{
                                                wrapperCol: {
                                                    offset: 6,
                                                    span: 18,
                                                },
                                            },
                                        }
                                    }
                                    operList={allOpers}
                                    form={form}
                                    disabled={disabled}
                                    keyName={keyName}
                                    deleteAllEnabled={deleteAllEnabled}
                                    initRef={listRef}
                                />
                            )) || <FormItem name={keyName} noStyle />
                        );
                    }}
                </FormItem>
            ) : (
                <StationList
                    allOpers={allOpers}
                    refreshAllOpers={refreshAllOpers}
                    operList={allOpers}
                    disabled={disabled}
                    keyName={keyName}
                    deleteAllEnabled={deleteAllEnabled}
                    initRef={listRef}
                />
            )}

            <AddOperModal
                visible={operVisible}
                allOpers={allOpers}
                onFinish={(_operList) => {
                    hideOperModal();
                    _operList?.length && refreshAllOpers([..._operList, ...allOpers]);
                }}
                disabled={disabled}
                openFlag={null}
                limitOperIds={limitOperIds}
                disabledOperIds={disabledIds}
                operatorList={operatorList}
                cooperationPlatform={cooperationPlatform}
            />
            <OperGroupImportModal
                initRef={operGroupRef}
                disabled={disabled}
                onConfirm={(_operList) => {
                    refreshAllOpers([..._operList, ...allOpers]);
                }}
                isLimit
                limitOperIds={limitOperIds}
                disabledOperIds={disabledIds}
                hasReasonText={hasReasonText || undefined}
                operatorList={operatorList}
                cooperationPlatform={cooperationPlatform}
            />
        </Fragment>
    );
};
export default forwardRef(OperSearchList);
