import React, { Fragment, useState, useMemo, useImperativeHandle, useEffect, useRef } from 'react';
import {
    Form,
    Modal,
    Space,
    Input,
    Button,
    Popconfirm,
    message,
    Typography,
    Popover,
    Row,
    Col,
} from 'antd';
import PropTypes from 'prop-types';
import { PlusOutlined } from '@ant-design/icons';
import ImportModal from '@/components/ImportModal';
import { operImportApi } from '@/services/CommonApi';
import TablePro from '@/components/TablePro';
import { SELECT_TYPES, MATCH_TYPES } from '@/config/declare';
import { connect } from 'dva';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

const FormItem = Form.Item;

const { TextArea } = Input;
const { Title, Text } = Typography;

const formItemLayout = {
    labelCol: {
        flex: '0 0 110px',
    },
    labelAlign: 'right',
};
const OperGroupImportModal = (props) => {
    const { initRef, onConfirm, title = '批量导入', operatorList, cooperationPlatform } = props;

    const [operForm] = Form.useForm();

    const [matchOpers, updateMatchOpers] = useState([]); // 存储所有已匹配到的运营商
    const [unmatchOpers, updateUnmatchOpers] = useState([]); // 存储所有未匹配到的运营商
    const [doubleMatchOpers, updateDoubleMatchOpers] = useState([]); // 存储所有重复匹配到的运营商

    const finalDoubleMatchedOpers = useMemo(() => {
        // 和场站重名不同，运营商支持提交重复匹配的数据
        return doubleMatchOpers;
    }, [doubleMatchOpers]);
    const allOpers = useMemo(() => {
        return [...unmatchOpers, ...finalDoubleMatchedOpers, ...matchOpers];
    }, [matchOpers, finalDoubleMatchedOpers, unmatchOpers]); // 存储所有已记录的运营商

    const [preSelectOpers, updatePreSelectOpers] = useState([]); // 已选中的运营商列表，用于添加时判断是否重复选择
    const [showModal, toggleShowModal] = useState(false); // 选择奖品弹窗
    const [searchKey, updateSearchKey] = useState(''); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
    const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑

    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const importModalRef = useRef();

    const [textForm] = Form.useForm(); // 文本输入
    const [showTextModal, toggleShowTextModal] = useState(false); // 选择奖品弹窗
    const [textUploading, updateTextUploading] = useState(false); // 文本是否正在上传

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
    };

    useImperativeHandle(initRef, () => ({
        show: (opers) => {
            toggleShowModal(true);
            updatePreSelectOpers(opers || []);
            changePageNum(1);
            updateSearchKey('');
            operForm.resetFields();
            updateMatchOpers([]);
            updateUnmatchOpers([]);
            updateDoubleMatchOpers([]);
            updateSelectedRowKeys([]);
        },

        close: () => {
            toggleShowModal(false);
        },
    }));

    // 用于过滤出要显示的运营商，兼容查询操作
    const showOpers = useMemo(() => {
        if (searchKey?.length) {
            const searchList = allOpers.filter((item) => searchKey.indexOf(item.operId) != -1);
            return searchList;
        }
        return allOpers || [];
    }, [allOpers, searchKey]);

    const importSuccess = (list) => {
        const matchArr = [];
        const unmatchArr = [];
        const doubleMatchArr = []; // 重复匹配
        list?.forEach((ele) => {
            const doubleArr = [
                ...matchArr,
                ...unmatchArr,
                ...doubleMatchArr,
                // ...matchOpers,
                // ...unmatchOpers,
                // ...doubleMatchOpers,
            ].filter((item) => {
                if (
                    item.operId &&
                    ((item.operName && item.matchFlag == MATCH_TYPES.MATCHED) ||
                        item.matchFlag == MATCH_TYPES.DOUBLEMACHED)
                ) {
                    return (
                        item.operId == ele.operId &&
                        item.operName == ele.operName &&
                        item.operNickname == ele.operNickname
                    );
                }
            }); // 记录重合的数组
            if (doubleArr?.length) {
                // 如果有重合数组，先移除，再添加
                doubleArr.forEach((item) => {
                    const arr = [
                        matchOpers,
                        matchArr,
                        unmatchOpers,
                        unmatchArr,
                        doubleMatchOpers,
                        doubleMatchArr,
                    ];
                    arr.forEach((subArr) => {
                        const index = subArr.indexOf(item);

                        if (index >= 0) {
                            subArr.splice(index, 1);
                        }
                    });
                });
            }

            if (ele?.operName?.length || true) {
                // 如果是批量查询，并且当前运营商是已废弃的，也可支持查询
                if (title === '批量查询' && ele.reason?.indexOf('已废弃') > 0) {
                    ele.matchFlag = MATCH_TYPES.MATCHED;
                }
                if (ele.matchFlag == MATCH_TYPES.MATCHED) {
                    matchArr.push(ele);
                } else if (ele.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    doubleMatchArr.push(ele);
                } else {
                    unmatchArr.push(ele);
                }
            }
        });
        // 每次导入后都是最新的列表，覆盖之前的数据
        updateMatchOpers([...matchArr]);
        updateDoubleMatchOpers([...doubleMatchArr]);
        updateUnmatchOpers([...unmatchArr]);
        changePageNum(1);
    };

    const operColumns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '运营商编号',
            dataIndex: 'operId',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '运营商简称',
            dataIndex: 'operNickname',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '运营商全称',
            dataIndex: 'operName',
            width: 160,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '状态 ',
            dataIndex: 'reason',
            width: 120,
            render(text, record) {
                let color = '#cccccc';
                if (record.matchFlag == MATCH_TYPES.UNMATCHED) {
                    // 红色
                    color = '#f50000';
                } else if (record.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    // 黄色
                    color = '#ff9901';
                } else if (record.matchFlag == MATCH_TYPES.MATCHED) {
                    // 绿色
                    color = '#87d068';
                }

                return (
                    <span style={{ color }} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '操作 ',
            fixed: 'right',
            width: 100,
            render(text, record, index) {
                return (
                    <Popconfirm
                        title={`确定要删除此运营商？`}
                        onConfirm={() => {
                            const realIndex = pageSize * (pageNum - 1) + index;
                            if (realIndex < unmatchOpers.length) {
                                unmatchOpers.splice(realIndex, 1);
                            } else if (realIndex < unmatchOpers.length + doubleMatchOpers.length) {
                                doubleMatchOpers.splice(realIndex - unmatchOpers?.length, 1);
                            } else {
                                matchOpers.splice(
                                    realIndex - (unmatchOpers.length + doubleMatchOpers.length),
                                    1,
                                );
                            }
                            updateMatchOpers([...matchOpers]);
                            updateDoubleMatchOpers([...doubleMatchOpers]);
                            updateUnmatchOpers([...unmatchOpers]);
                        }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                );
            },
        },
    ];

    // 有未匹配的运营商
    const hasUnmatchOpers = useMemo(() => {
        return unmatchOpers.length > 0;
    }, [unmatchOpers]);

    // 有选中的运营商
    const hasSelectedOpers = useMemo(() => {
        return selectedRowKeys.length > 0;
    }, [selectedRowKeys]);

    // 有之前已导入的运营商
    const hasAlreadyOpers = useMemo(() => {
        const allStationList = [...finalDoubleMatchedOpers, ...matchOpers];
        for (let index = 0; index < allStationList.length; index++) {
            const operItem = allStationList[index];

            // 查询是否已添加过
            const res = preSelectOpers?.filter((showItem) => operItem.operId == showItem.operId);
            if (res?.length > 0) {
                return true;
            }
        }
        return false;
    }, [finalDoubleMatchedOpers, matchOpers]);

    const onSelectChange = (_selectedRowKeys) => {
        updateSelectedRowKeys(_selectedRowKeys);
    };

    const deleteObjFromArray = (arr = [], obj) => {
        return new Promise((resolve, reject) => {
            let index = arr.indexOf(obj);
            if (index >= 0) {
                arr.splice(index, 1);
                resolve(arr);
            } else {
                reject();
            }
        });
    };

    // 批量删除已勾选运营商
    const deleteSelectOpers = async () => {
        let total = 0;
        for (let i = selectedRowKeys.length - 1; i >= 0; i--) {
            const operIndex = selectedRowKeys[i];
            const operObj = showOpers[operIndex];

            await deleteObjFromArray(unmatchOpers, operObj)
                .then((arr) => {
                    total += 1;
                    selectedRowKeys.splice(i, 1);
                    updateSelectedRowKeys([...selectedRowKeys]);
                    updateUnmatchOpers([...arr]);
                })
                .catch(() =>
                    deleteObjFromArray(doubleMatchOpers, operObj)
                        .then((arr) => {
                            total += 1;
                            selectedRowKeys.splice(i, 1);
                            updateSelectedRowKeys([...selectedRowKeys]);
                            updateDoubleMatchOpers([...arr]);
                        })
                        .catch(() =>
                            deleteObjFromArray(matchOpers, operObj)
                                .then((arr) => {
                                    total += 1;
                                    selectedRowKeys.splice(i, 1);
                                    updateSelectedRowKeys([...selectedRowKeys]);
                                    updateMatchOpers([...arr]);
                                })
                                .catch(),
                        ),
                );
        }
        message.success(`成功删除${total}条数据`);
        updateSelectedRowKeys([]);
    };

    // 批量删除已导入
    const deleteAlreadyOpers = () => {
        const alreadyOpers = []; // 记录已存在的运营商信息
        const allStationList = [...finalDoubleMatchedOpers, ...matchOpers];
        allStationList.forEach((operItem) => {
            // 查询是否已添加过
            const res = preSelectOpers?.filter((showItem) => operItem.operId == showItem.operId);
            if (res?.length > 0) {
                alreadyOpers.push(operItem.operId);
            }
        });

        let total = 0;
        // 如果存在已导入运营商，进入判断
        if (alreadyOpers.length) {
            for (let i = matchOpers.length - 1; i >= 0; i--) {
                let finded = alreadyOpers.filter((operId) => operId == matchOpers[i].operId);
                if (finded?.length) {
                    total += 1;
                    matchOpers.splice(i, 1);
                    updateMatchOpers([...matchOpers]);
                }
            }
            for (let i = doubleMatchOpers.length - 1; i >= 0; i--) {
                let finded = alreadyOpers.filter((operId) => operId == doubleMatchOpers[i].operId);
                if (finded?.length) {
                    total += 1;
                    doubleMatchOpers.splice(i, 1);
                    updateDoubleMatchOpers([...doubleMatchOpers]);
                }
            }
        }
        message.success(`成功删除${total}条数据`);
    };

    const [showConfirmModal, toggleConfirmModal] = useState(false); // 选择奖品弹窗
    const [addEnabledOpers, updateAddEnabledOpers] = useState([]); // 可支持导入的运营商列表
    const [addStopOpers, updateAddStopOpers] = useState([]); // 禁止导入的运营商列表
    const onOk = () => {
        const alreadyOpers = []; // 记录已存在的运营商信息
        const successOpers = []; // 可成功导入的运营商
        const allStationList = [...finalDoubleMatchedOpers, ...matchOpers];
        allStationList.forEach((operItem) => {
            // 查询是否已添加过
            const res = preSelectOpers?.filter((showItem) => operItem.operId == showItem.operId);
            if (res?.length > 0) {
                alreadyOpers.push(operItem.operName);
            } else {
                // 未匹配到
                successOpers.push(operItem);
            }
        });

        updateAddEnabledOpers(successOpers);
        updateAddStopOpers(alreadyOpers);
        if (alreadyOpers?.length || unmatchOpers?.length) {
            toggleConfirmModal(true);
        } else {
            onConfirm && onConfirm(successOpers);
            initRef?.current.close();
        }
    };

    return (
        <Fragment>
            <Modal
                title={title}
                visible={showModal}
                onOk={onOk}
                width={800}
                onCancel={() => initRef?.current.close()}
                maskClosable={false}
            >
                <Form form={operForm} name="operForm">
                    <FormItem
                        shouldUpdate={(prevValues, curValues) => prevValues.operList !== allOpers}
                        noStyle
                    >
                        {({ getFieldValue }) => {
                            return (
                                <Fragment>
                                    <Space>
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                importModalRef.current.show();
                                            }}
                                        >
                                            <PlusOutlined />
                                            表格导入
                                        </Button>
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                toggleShowTextModal(true);
                                                textForm.resetFields();
                                            }}
                                        >
                                            <PlusOutlined />
                                            文本导入
                                        </Button>
                                    </Space>
                                    {(allOpers?.length && (
                                        <Row gutter={24}>
                                            <Col span={10}>
                                                <OperSelectTypeItem
                                                    // rules={[{ message: '请选择运营商', required: true }]}
                                                    label=""
                                                    {...formItemLayout}
                                                    form={operForm}
                                                />
                                            </Col>
                                            <Col>
                                                <Button
                                                    type="primary"
                                                    onClick={() => {
                                                        const key =
                                                            operForm.getFieldValue('operId');
                                                        updateSearchKey(key);
                                                        changePageNum(1);
                                                        updateSelectedRowKeys([]);
                                                    }}
                                                >
                                                    搜索
                                                </Button>
                                            </Col>
                                        </Row>
                                    )) ||
                                        null}

                                    {(allOpers?.length && (
                                        <Fragment>
                                            <p>
                                                <Popover
                                                    content={
                                                        <div>
                                                            <Space>
                                                                <Button
                                                                    type="primary"
                                                                    onClick={deleteSelectOpers}
                                                                    disabled={!hasSelectedOpers}
                                                                >
                                                                    删除选中项
                                                                </Button>
                                                                <Button
                                                                    danger
                                                                    onClick={() => {
                                                                        message.success(
                                                                            `成功删除${
                                                                                unmatchOpers?.length ||
                                                                                0
                                                                            }条数据`,
                                                                        );
                                                                        updateUnmatchOpers([]);
                                                                    }}
                                                                    disabled={!hasUnmatchOpers}
                                                                >
                                                                    删除未匹配运营商
                                                                </Button>
                                                                <Button
                                                                    type="primary"
                                                                    onClick={deleteAlreadyOpers}
                                                                    disabled={!hasAlreadyOpers}
                                                                >
                                                                    删除已导入运营商
                                                                </Button>
                                                            </Space>
                                                        </div>
                                                    }
                                                    title="批量操作"
                                                    trigger="click"
                                                >
                                                    <Button>批量操作</Button>
                                                </Popover>
                                            </p>
                                            <TablePro
                                                scroll={{ x: 'max-content' }}
                                                rowKey={(record, index) => index}
                                                dataSource={showOpers}
                                                onChange={changePageInfo}
                                                pagination={{
                                                    current: pageNum,
                                                    total: showOpers?.length,
                                                    pageSize,
                                                    showSizeChanger: true,
                                                    showQuickJumper: true,
                                                    showTotal: (total) => `共 ${total} 条`,
                                                }}
                                                expandable={{ defaultExpandAllRows: true }}
                                                columns={operColumns}
                                                rowSelection={{
                                                    selectedRowKeys,
                                                    onChange: onSelectChange,
                                                }}
                                            />
                                        </Fragment>
                                    )) ||
                                        null}
                                </Fragment>
                            );
                        }}
                    </FormItem>
                </Form>

                <ImportModal
                    title="批量导入运营商"
                    downLoadPath="/aliMini/xdt/static/excel/运营商导入模板.xls"
                    onUpload={async (formData, callback) => {
                        formData.append('filterAbandon', '1');
                        formData.append('filterCloseStation', '0');
                        if (cooperationPlatform) {
                            formData.append('cooperationPlatform', cooperationPlatform);
                        }
                        const {
                            ret,
                            data: { validResult },
                        } = await operImportApi(formData);

                        callback &&
                            callback({
                                ret: ret == 200 ? 'suc' : 'false',
                                msg: ret == 200 ? '导入成功' : '导入失败',
                            });
                        importSuccess(validResult);
                    }}
                    ref={importModalRef}
                    surfix={['.xls']}
                />
            </Modal>

            <Modal
                title="文本导入"
                width={400}
                visible={showTextModal}
                onCancel={() => toggleShowTextModal(false)}
                onOk={() => {
                    updateTextUploading(true);
                    textForm
                        .validateFields()
                        .then(async (params) => {
                            const formData = new FormData();
                            for (const key in params) {
                                formData.append(key, params[key]);
                            }
                            if (cooperationPlatform) {
                                formData.append('cooperationPlatform', cooperationPlatform);
                            }

                            const {
                                data: { validResult },
                            } = await operImportApi(formData);
                            importSuccess(validResult);
                            updateTextUploading(false);
                            toggleShowTextModal(false);
                            message.success('导入成功');
                        })
                        .catch((e) => {
                            updateTextUploading(false);
                        });
                }}
                maskClosable={false}
            >
                <Form form={textForm} name="textForm">
                    <FormItem
                        label="输入文本"
                        name="operNickNames"
                        rules={[{ required: true, message: '请输入运营商名称' }]}
                    >
                        <TextArea placeholder="输入运营商名称，回车间隔" rows={4} />
                    </FormItem>
                    <FormItem name="filterCloseStation" noStyle initialValue={0} />
                    <FormItem name="filterAbandon" noStyle initialValue={1} />
                </Form>
            </Modal>

            <Modal
                title={`本次${title}将添加${addEnabledOpers.length || 0}个运营商`}
                width={650}
                visible={showConfirmModal}
                onCancel={() => toggleConfirmModal(false)}
                onOk={() => {
                    toggleConfirmModal(false);
                    onConfirm && onConfirm(addEnabledOpers);
                    initRef?.current.close();
                }}
                maskClosable={false}
            >
                {(finalDoubleMatchedOpers.some(
                    (item) => item.matchFlag == MATCH_TYPES.DOUBLEMACHED,
                ) && (
                    <>
                        <Title level={5}>
                            以下
                            <span style={{ color: '#ff9901' }}>重名运营商</span>
                            经确认后可继续添加：
                        </Title>
                        <Text style={{ whiteSpace: 'pre' }}>
                            {finalDoubleMatchedOpers
                                .filter((item) => item.matchFlag == MATCH_TYPES.DOUBLEMACHED)
                                .map((item) => `${item.operName}`)
                                .join('\n')}
                        </Text>
                    </>
                )) ||
                    null}

                {(addStopOpers?.length && (
                    <>
                        <Title level={5}>
                            以下运营商此前已添加过，将
                            <span style={{ color: '#ff9901' }}>不再添加</span>：
                        </Title>
                        <Text>{addStopOpers?.join('、')}</Text>
                    </>
                )) ||
                    null}

                {(unmatchOpers?.length && (
                    <Title level={5}>
                        {`另有${unmatchOpers.length}个未匹配运营商，将`}
                        <span style={{ color: '#ff9901' }}>自动忽略</span>。
                    </Title>
                )) ||
                    null}
            </Modal>
        </Fragment>
    );
};

OperGroupImportModal.propTypes = {
    dispatch: PropTypes.func.isRequired,
    // global: PropTypes.object.isRequired,
};

export default connect(({ global }) => ({
    global,
}))(OperGroupImportModal);
