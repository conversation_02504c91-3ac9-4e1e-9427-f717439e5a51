// 渠道下拉框
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { useEffect, useMemo } from 'react';
import { getIcnChannelListApi } from '@/services/SallerCenter/IcnApi.js';
import { useModel } from 'umi';
import { isEmpty } from '@/utils/utils';
import { RULE_TYPES } from '../declare';

const BusinessChannelSelect = (props: {
    value?: string;
    disabled?: boolean;
    ruleType?: string;
    onChange?: (v: string) => void;
    onChangeOption?: (v: any) => void;
}) => {
    const { value, disabled, ruleType = RULE_TYPES.COMPANIES, onChange, onChangeOption } = props;

    const { codeInfo, initCode } = useModel('codeState');

    useEffect(() => {
        if (isEmpty(codeInfo?.plugAndChargeRuleType)) {
            initCode('plugAndChargeRuleType');
        }
    }, []);
    const { run, data, loading } = useRequest(
        () => {
            return getIcnChannelListApi({ pageIndex: 1, pageSize: 10000 });
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        run();
    }, []);
    const disabledSelect = useMemo(() => {
        return disabled;
    }, [disabled]);

    const selectOptions = useMemo(() => {
        if (ruleType == RULE_TYPES.PLAT) {
            return codeInfo.plugAndChargeRuleType;
        } else if (ruleType == RULE_TYPES.COMPANIES) {
            return data?.data?.records?.map((ele) => {
                return {
                    codeName: ele.channelName,
                    codeValue: ele.channelId,
                };
            });
        }
    }, [data, codeInfo, ruleType]);

    return (
        <Select
            allowClear
            loading={loading}
            fieldNames={{
                label: 'codeName',
                value: 'codeValue',
            }}
            filterOption={(input, option) =>
                option?.codeName?.toLowerCase().indexOf(input?.toLowerCase()) >= 0 ||
                option?.codeValue?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
            }
            options={selectOptions}
            disabled={disabledSelect}
            placeholder="请选择鉴权方"
            showSearch
            onChange={(value, option) => {
                onChange?.(value);
                onChangeOption?.(option);
            }}
            value={value}
        ></Select>
    );
};

export default BusinessChannelSelect;
