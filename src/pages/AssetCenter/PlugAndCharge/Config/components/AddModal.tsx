import { useRequest } from 'ahooks';
import {
    Form,
    Input,
    Modal,
    message,
    Radio,
    Switch,
    Button,
    InputNumber,
    Tooltip,
    Card,
} from 'antd';
import { forwardRef, useImperativeHandle, useState, useMemo } from 'react';
import BusinessChannelSelect from './BusinessChannelSelect';
import { useWatch } from 'antd/lib/form/Form';
import { MinusCircleOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { plugChargeDetailApi, plugChargeSaveApi } from '@/services/AssetCenter/PlugAndChargeApi';
import { RULE_TYPES } from '../declare';
import { isEmpty } from '@/utils/utils';
import e from 'express';
const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};
const formItemLayoutWithOutLabel = {
    wrapperCol: {
        span: 16,
        offset: 6,
    },
};
const AddModal = (props: any, ref: any) => {
    const [visible, setVisible] = useState<boolean>(false);
    const [ruleId, setRuleId] = useState<string>('');
    const [form] = Form.useForm();
    const { callback } = props;

    const vinRulesFlag = Form.useWatch('vinRulesFlag', form);

    useImperativeHandle(ref, () => {
        return {
            show: (id?: string) => {
                setVisible(true);
                if (id) {
                    setRuleId(id);
                    getDetail({ ruleId: id });
                } else {
                    setRuleId('');
                }
            },
        };
    });

    const {
        run: getDetail,
        data: detailInfo,
        loading: detailLoading,
    } = useRequest(
        async (params: any) => {
            try {
                const { data } = await plugChargeDetailApi(params);
                const info = {
                    ...data,
                };
                if (isEmpty(info.vinRules)) {
                    info.vinRulesFlag = '0';
                } else {
                    info.vinRulesFlag = '1';
                }
                form.setFieldsValue(info);
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    const { run, loading } = useRequest(
        (params: any) => {
            return plugChargeSaveApi(params);
        },
        {
            manual: true,
            onSuccess: (res) => {
                if (res.ret === 200 && res?.data) {
                    message.success('操作成功');
                    callback && callback();
                    onClose();
                }
            },
        },
    );

    const onClose = () => {
        form.resetFields();
        setVisible(false);
    };

    const onSubmit = async () => {
        const formData = await form.validateFields();
        run({ ...formData });
    };

    const plugAndChargeFlag = Form.useWatch('plugAndChargeFlag', form);
    const ruleTypeName = useMemo(() => {
        if (detailInfo) {
            if (detailInfo.ruleType === RULE_TYPES.COMPANIES) {
                return '车企规则';
            } else if (detailInfo.ruleType === RULE_TYPES.PLAT) {
                return '平台规则';
            }
        }
        return '';
    }, [detailInfo]);
    return (
        <Modal
            visible={visible}
            title="即插即充配置"
            destroyOnClose
            width={800}
            onCancel={onClose}
            onOk={onSubmit}
            okButtonProps={{ loading: loading }}
        >
            <Form form={form} wrapperCol={{ span: 14 }} labelCol={{ span: 6 }}>
                <Card loading={detailLoading} bordered={false}>
                    <Form.Item name="ruleId" noStyle></Form.Item>
                    {(ruleId && (
                        <Form.Item
                            name="ruleType"
                            label="规则类型"
                            initialValue={RULE_TYPES.COMPANIES}
                            rules={[{ required: true, message: '请选择规则类型' }]}
                        >
                            {ruleTypeName}
                        </Form.Item>
                    )) || (
                        <Form.Item
                            name="ruleType"
                            label="规则类型"
                            initialValue={RULE_TYPES.COMPANIES}
                            rules={[{ required: true, message: '请选择规则类型' }]}
                        >
                            <Radio.Group>
                                <Radio value={RULE_TYPES.COMPANIES}>车企规则</Radio>
                            </Radio.Group>
                        </Form.Item>
                    )}

                    <Form.Item
                        name="ruleBelong"
                        label="鉴权方"
                        rules={[{ required: true, message: '请选择鉴权方' }]}
                    >
                        <BusinessChannelSelect
                            ruleType={detailInfo?.ruleType}
                            disabled={ruleId ? true : false}
                        />
                    </Form.Item>
                    <Form.Item
                        name="plugAndChargeFlag"
                        label="即插即充"
                        initialValue={1}
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Switch
                            // disabled={disabled}
                            checkedChildren="开"
                            unCheckedChildren="关"
                            checked={plugAndChargeFlag === 1}
                            onChange={(checked) => {
                                if (!checked) {
                                    form.setFieldValue('plugAndChargeFlag', 0);
                                } else {
                                    form.setFieldValue('plugAndChargeFlag', 1);
                                }
                            }}
                        ></Switch>
                    </Form.Item>
                    <Form.Item
                        name="priority"
                        label="鉴权优先级"
                        initialValue={'3'}
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <InputNumber min={1} />
                    </Form.Item>
                    <Form.Item
                        label={
                            <Tooltip title="若配署VIN码规则，则只在VIN码匹配VIN码规则时，向鉴权方发起鉴权若不配置VIN码规则，则直接向鉴权方发起鉴权">
                                VIN码规则
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                        required
                    >
                        <Form.Item name="vinRulesFlag" initialValue={'0'}>
                            <Radio.Group
                                onChange={(event) => {
                                    const {
                                        target: { value },
                                    } = event;
                                    form.setFieldValue('vinRules', []);
                                }}
                            >
                                <Radio value={'1'}>是</Radio>
                                <Radio value={'0'}>否</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {/* <div style={{ color: 'red', margin: '5px 0 10px 0' }}>
                            桩上报的VIN码可能存在倒序情况，建议同时配置正倒序规则
                        </div> */}
                        {vinRulesFlag == '1' && (
                            <Form.List
                                name="vinRules"
                                initialValue={['']}
                                rules={[
                                    {
                                        validator: async (_, vinRules) => {
                                            if (!vinRules || vinRules.length < 1) {
                                                return Promise.reject(
                                                    new Error('请输入至少一条VIN码规则'),
                                                );
                                            }
                                        },
                                    },
                                ]}
                            >
                                {(fields, { add, remove }, { errors }) => (
                                    <>
                                        {fields.map((field, index) => (
                                            <Form.Item
                                                wrapperCol={{ span: 24 }}
                                                label={''}
                                                required
                                                key={field.key}
                                            >
                                                <Form.Item
                                                    {...field}
                                                    validateTrigger={['onChange', 'onBlur']}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            whitespace: true,
                                                            message: '请输入VIN码规则',
                                                        },
                                                    ]}
                                                    noStyle
                                                >
                                                    <Input
                                                        placeholder="请输入正则表达式"
                                                        style={{
                                                            width: '80%',
                                                        }}
                                                    />
                                                </Form.Item>
                                                {fields.length > 1 ? (
                                                    <MinusCircleOutlined
                                                        className="dynamic-delete-button"
                                                        onClick={() => remove(field.name)}
                                                    />
                                                ) : null}
                                            </Form.Item>
                                        ))}
                                        <Form.Item>
                                            <Button
                                                type="dashed"
                                                style={{ width: '80%' }}
                                                onClick={() => add()}
                                                icon={<PlusOutlined />}
                                            >
                                                添加
                                            </Button>
                                            <Form.ErrorList errors={errors} />
                                        </Form.Item>
                                    </>
                                )}
                            </Form.List>
                        )}
                    </Form.Item>
                    <Form.Item
                        name="floorControlAmount"
                        label={
                            <Tooltip title="若鉴权方未返回预付金额/用户余额，则按兜底金额进行费控">
                                兜底费控金额
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                        rules={[{ required: true, message: '请选择' }]}
                        initialValue={300}
                    >
                        <InputNumber precision={2} step={0.01} min={0} addonAfter={'元'} />
                    </Form.Item>
                </Card>
            </Form>
        </Modal>
    );
};

export default forwardRef(AddModal);
