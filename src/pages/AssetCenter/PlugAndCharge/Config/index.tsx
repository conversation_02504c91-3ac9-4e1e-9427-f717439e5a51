import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import { <PERSON>ton, Card, Col, Form, Input, message, Popconfirm, Row, Space, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import XdtProTable from '@/components/XdtProTable/index';
import { plugChargeListApi } from '@/services/AssetCenter/PlugAndChargeApi';
import BusinessChannelSelect from './components/BusinessChannelSelect';
import AddModal from './components/AddModal';
import { RULE_TYPES } from './declare';

const ListPage = () => {
    const modalRef = useRef();
    const formRef = useRef();
    const actionRef = useRef();

    const columns: any = [
        {
            title: '鉴权优先级',
            dataIndex: 'priority',
            width: 120,
            hideInSearch: true,
            // render(text: string) {
            //     return <span title={text}>{text}</span>;
            // },
        },
        {
            title: '规则类型',
            dataIndex: 'ruleTypeName',
            width: 150,
            hideInSearch: true,
            // render(text: string) {
            //     return <span title={text}>{text}</span>;
            // },
        },
        {
            title: '鉴权方',
            dataIndex: 'ruleBelongName',
            width: 150,
            hideInSearch: true,
            // render(text: string) {
            //     return <span title={text}>{text}</span>;
            // },
        },
        {
            title: '场站范围',
            dataIndex: 'stationScope',
            width: 200,
            hideInSearch: true,
            // render(text: string) {
            //     return <span title={text}>{text}</span>;
            // },
        },
        {
            title: '鉴权方',
            width: 160,
            dataIndex: 'ruleBelong',
            fieldProps: { placeholder: '请填写' },

            renderFormItem(value: any, config: any, form: any) {
                return <BusinessChannelSelect />;
            },
            order: 10,
            search: {
                transform: (v: any) => {
                    return { ruleBelong: v };
                },
            },
            render: (value: any, record: any) => {
                return record?.ruleBelongName || '-';
            },
        },
        {
            title: '兜底费控金额',
            dataIndex: 'floorControlAmount',
            width: 140,
            hideInSearch: true,
            // render(text: string) {
            //     return <span title={text}>{text}</span>;
            // },
        },

        {
            title: '状态',
            dataIndex: 'plugAndChargeFlag',
            valueType: 'select',
            width: 120,
            valueEnum: {
                '0': '关闭',
                '1': '开启',
            },
            renderText(text: string, record: API.PlugChargeVo) {
                let statusType = 'danger';
                if (record?.plugAndChargeFlag === 1) {
                    statusType = 'success';
                }
                return (
                    <Typography.Text type={statusType}>
                        {record.plugAndChargeFlag === 1 ? '开启' : '关闭'}
                    </Typography.Text>
                );
            },
        },
        {
            title: '操作',
            hideInSearch: true,
            width: 160,
            render: (text: string, record: any) => {
                return (
                    <Space>
                        {(record?.ruleType === RULE_TYPES.COMPANIES ||
                            (record?.ruleType === RULE_TYPES.PLAT &&
                                record?.ruleBelong === 'alipay')) && (
                            <Typography.Link
                                onClick={() => {
                                    modalRef?.current?.show(record?.ruleId);
                                }}
                            >
                                编辑
                            </Typography.Link>
                        )}
                    </Space>
                );
            },
        },
    ];
    return (
        <PageHeaderWrapper>
            <Card>
                <XdtProTable
                    actionRef={actionRef}
                    formRef={formRef}
                    rowKey={(record: any) => record?.ruleId}
                    requestApi={plugChargeListApi}
                    columns={columns}
                    hasSort
                    toolButtons={[
                        <Button
                            key="add"
                            type="primary"
                            onClick={() => {
                                modalRef?.current?.show();
                            }}
                        >
                            新建
                        </Button>,
                    ]}
                ></XdtProTable>
            </Card>
            <AddModal ref={modalRef} callback={() => actionRef?.current?.reload()} />
        </PageHeaderWrapper>
    );
};

export default ListPage;
