import {
    getStationTagListApi,
    getFilterStationTagListApi,
} from '@/services/AssetCenter/TagManageApi';

const TagModel = {
    namespace: 'tagModel',
    state: {
        stationTagList: [], //场站标签列表
        stationTagListTotal: 0, // 场站标签列表总条数

        filterStationTagList: [], //场站标签筛选位置列表
        filterStationTagListTotal: 0, // 场站标签筛选位置列表总条数
    },
    effects: {
        /**
         * 站点列表
         */
        *getStationTagList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getStationTagListApi, options);

                yield put({
                    type: 'updateStationTagList',
                    list: records,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 站点列表
         */
        *getFilterStationTagList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getFilterStationTagListApi, options);

                yield put({
                    type: 'updateFilterStationTagList',
                    list: records,
                    total,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        updateStationTagList(state, { list, total }) {
            return {
                ...state,
                stationTagList: list,
                stationTagListTotal: total,
            };
        },
        updateFilterStationTagList(state, { list, total }) {
            return {
                ...state,
                filterStationTagList: list,
                filterStationTagListTotal: total,
            };
        },
    },
};
export default TagModel;
