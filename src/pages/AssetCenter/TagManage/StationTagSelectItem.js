import { Modal, Form, Button, Space, Col, Input } from 'antd';
import { Fragment, useState, useEffect } from 'react';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import moment from 'moment';
import usePageState from '@/hooks/usePageState';

const FormItem = Form.Item;
const editFormItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { dispatch, form, onSubmit, onReset, onExportForm, listLoading, global } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem label="标签名称:" name="sensitiveWord">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="标签属性:" name="replaceWord">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const StationTagSelectItem = (props) => {
    const {
        dispatch,
        visible,
        closeView,
        submitEvent,
        listLoading,
        tagModel: { stationTagList, stationTagListTotal },
    } = props;

    const [form] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                sensitiveWord: data.sensitiveWord,
                replaceWord: data.replaceWord,
            };

            dispatch({
                type: 'tagModel/getStationTagList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const onSubmitFinish = (values) => {
        submitEvent && submitEvent(values);
    };

    const columns = [
        {
            title: '筛选位置',
            dataIndex: 'sensitiveWord',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站标签',
            dataIndex: 'replaceWord',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    return (
        <Modal
            title="场站列表页"
            visible={visible}
            footer={false}
            onCancel={closeView}
            destroyOnClose
            maskClosable={false}
        >
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            ></SearchLayout>
            <TablePro
                name="tag"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.id}
                dataSource={stationTagList}
                columns={columns}
                onChange={onTableChange}
                bordered
                pagination={{
                    current: pageInfo.pageIndex,
                    total: stationTagListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
            <Space>
                <Button type="primary" onClick={onSubmitFinish}>
                    提交
                </Button>
                <Button onClick={closeView}>取消</Button>
            </Space>
        </Modal>
    );
};

export default StationTagSelectItem;
