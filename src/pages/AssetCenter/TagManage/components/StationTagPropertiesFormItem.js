import { Form, AutoComplete, Space } from 'antd';
import { Fragment, useState, useRef, useMemo, useEffect } from 'react';
import { BarsOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { getStationTagPropertiesListApi } from '@/services/AssetCenter/TagManageApi';
import { getStationNoticeTypeListApi } from '@/services/AssetCenter/StationManangeApi';

const FormItem = Form.Item;

export const PROPERTISE_TYPES = {
    STATION_TAG: '00', // 场站标签
    STATION_NOTICE: '01', // 场站公告
};

export const PropertiesItem = (props) => {
    const { value, onChange, type = PROPERTISE_TYPES.STATION_TAG } = props;

    const [serchStr, updateSerchStr] = useState('');
    const [options, updateOptions] = useState([]);

    useEffect(() => {
        initOptions();
    }, []);

    const filterOptions = useMemo(() => {
        if (serchStr === '') {
            return options;
        }
        return options.filter((ele) => ele.value.indexOf(serchStr) >= 0);
    }, [options, serchStr]);

    const initOptions = async () => {
        let api;
        switch (type) {
            case PROPERTISE_TYPES.STATION_TAG:
                api = getStationTagPropertiesListApi;
                break;
            case PROPERTISE_TYPES.STATION_NOTICE:
                api = getStationNoticeTypeListApi;
                break;

            default:
                break;
        }
        try {
            const {
                data: { list },
            } = await api?.();
            if (list instanceof Array) {
                updateOptions(
                    list.map((ele) => {
                        return {
                            label: ele?.noticeType || ele,
                            value: ele?.noticeType || ele,
                        };
                    }),
                );
            }
            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const onSearch = (searchText) => {
        updateSerchStr(searchText);
    };

    const onSelectItem = (data) => {
        onChange && onChange(data);
    };

    const onChangeItem = (data) => {
        onChange && onChange(data);
    };

    return (
        <AutoComplete
            value={value}
            options={filterOptions}
            onSelect={onSelectItem}
            onSearch={onSearch}
            onChange={onChangeItem}
            placeholder={type == PROPERTISE_TYPES.STATION_NOTICE ? '请填写，最多6个字' : '请填写'}
            {...props}
        ></AutoComplete>
    );
};
const StationTagPropertiesFormItem = (props) => {
    const { name = 'labelProperties', label = '标签属性' } = props;

    return (
        <FormItem label={label} name={name} {...props}>
            <PropertiesItem {...props}></PropertiesItem>
        </FormItem>
    );
};

export default StationTagPropertiesFormItem;
