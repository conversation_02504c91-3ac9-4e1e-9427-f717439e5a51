import { Form, But<PERSON>, Tree, Space, Popconfirm } from 'antd';
import { Fragment, useState, useRef, useMemo, useEffect } from 'react';
import { BarsOutlined, MinusCircleOutlined } from '@ant-design/icons';
const FormItem = Form.Item;

import StationTagSelectModal from './StationTagSelectModal';
import { copyObjectCommon } from '@/utils/utils';
const StationTagDragSelectItem = (props) => {
    const {
        name,
        form,
        positionName,
        value,
        onChange,
        defaultSelectList,
        unclassified = false, //是否分级展示
    } = props;

    const selectRef = useRef();

    const [selectTagList, updateSelectTagList] = useState(() => {
        let formatDefault = [];
        if (defaultSelectList instanceof Array) {
            //初始化是格式化数据  提交和返回格式不一样需要转化
            for (const item of defaultSelectList) {
                if (item.parentId !== 0) {
                    const parentItem = defaultSelectList.find((ele) => ele.id === item.parentId);
                    let newItem = {
                        id: item.id,
                        labelName: item.name,
                    };
                    if (parentItem) {
                        newItem.labelProperties = parentItem.name;
                        newItem.parentId = parentItem.id;
                    }
                    formatDefault.push(newItem);
                }
            }
        }
        return formatDefault;
    });
    const [visible, toggleVisible] = useState(false);

    const [selectTagTree, updateSelectTagTree] = useState([]);

    useEffect(() => {
        let list = [];

        if (selectTagList instanceof Array) {
            //提交和展示格式不一样需要转化
            const copyList = copyObjectCommon(selectTagList);
            for (const item of copyList) {
                if (item.parentId === 0 || !item.parentId) {
                    const children = copyList.filter((ele) => ele.parentId === item.id);
                    if (children && children.length > 0) {
                        item.children = children;
                    }
                    list.push(item);
                } else {
                    const parentItem = list.find((ele) => ele.id === item.parentId);
                    if (parentItem) {
                        parentItem.children.push(item);
                    } else {
                        if (!unclassified) {
                            //不分级
                            list.push(item);
                        } else {
                            let newItem = {
                                id: item.parentId,
                                parentId: 0,
                                labelName: item.labelProperties,
                                children: [item],
                            };
                            list.push(newItem);
                        }
                    }
                }
            }
        }

        //格式化树并更新form
        updateSelectTagTree(list);
        onChange && onChange(list);
    }, [selectTagList, unclassified]);

    const openModalEvent = () => {
        toggleVisible(true);
    };
    const closeModalEvent = () => {
        toggleVisible(false);
    };
    const changeActEvent = (list) => {
        updateSelectTagList(list);
        if (list?.length) {
            const result = list.map((ele) => ele.actId);
            form?.setFieldsValue({ [name]: result.join(',') });
        }
        closeModalEvent();
    };
    const clearActEvent = (list) => {
        updateSelectTagList([]);
        form?.setFieldsValue({ [name]: [] });
        selectRef.current && selectRef.current.clearSelect();
    };

    const loop = (data, id, callback) => {
        //查询指定元素在tree中的位置并做相关操作
        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) {
                return callback(data[i], i, data);
            }

            if (data[i].children) {
                loop(data[i].children, id, callback);
            }
        }
    };

    const delNodeEvent = (dragNode) => {
        const allTree = [...selectTagTree];

        loop(allTree, dragNode.id, (item, index, arr) => {
            arr.splice(index, 1);
        });

        loop(allTree, dragNode.parentId, (item, index, arr) => {
            if (item.children?.length == 0) {
                arr.splice(index, 1);
            }
        });

        updateSelectTagTree(allTree);
        onChange && onChange(allTree);
    };

    const onDropEvent = ({ event, node, dragNode, dragNodesKeys }) => {
        //放置方法
        const allTree = [...selectTagTree];

        let dragObj;

        if (dragNode.parentId === node.id) {
            //如果移到父级就放在第一个
            loop(allTree, dragNode.id, (item, index, arr) => {
                arr.splice(index, 1);
                dragObj = item;
            });
            node.children.unshift(dragObj);
        }
        if (dragNode.parentId === node.parentId) {
            //如果移到同级

            loop(allTree, dragNode.id, (item, index, arr) => {
                arr.splice(index, 1);
                dragObj = item;
            });
            let ar = [];
            let end; //要放置的位置下标

            loop(allTree, node.id, (_item, index, arr) => {
                ar = arr;
                end = index;
            });
            if (dragNode.parentId === 0) {
                //一级排序特殊处理
                const { target } = event;
                const { offsetTop } = target;
                if (offsetTop > 0) {
                    ar.splice(end + 1, 0, dragObj); //放到指定位置
                } else {
                    ar.unshift(dragObj);
                }
            } else {
                ar.splice(end + 1, 0, dragObj); //放到指定位置
            }
        }

        updateSelectTagTree(allTree);
        onChange && onChange(allTree);
    };
    return (
        <Fragment>
            <Button
                type="primary"
                onClick={() => {
                    openModalEvent();
                }}
            >
                + 选择标签
            </Button>

            <Tree
                className="draggable-tree"
                draggable={{
                    icon: false,
                    nodeDraggable: (treeNode) => {
                        // if (treeNode.parentId === 0) {
                        //     return false;
                        // }
                        return true;
                    },
                }}
                blockNode
                // onDragEnter={onDragEnter}
                onDrop={onDropEvent}
                treeData={selectTagTree}
                fieldNames={{
                    title: 'labelName',
                    key: 'id',
                    children: 'children',
                }}
                expandedKeys={selectTagTree.map((ele) => ele.id)}
                titleRender={(nodeData) => {
                    return (
                        <Space>
                            <BarsOutlined />
                            {nodeData.labelName}
                            {nodeData.parentId !== 0 || nodeData.children.length === 0 ? (
                                <Popconfirm
                                    placement="top"
                                    title={'确认删除标签？'}
                                    onConfirm={() => {
                                        delNodeEvent(nodeData);
                                    }}
                                    okText="是"
                                    cancelText="否"
                                >
                                    <MinusCircleOutlined />
                                </Popconfirm>
                            ) : (
                                ''
                            )}
                        </Space>
                    );
                }}
            />
            <StationTagSelectModal
                {...props}
                ref={selectRef}
                selectTagList={selectTagList}
                visible={visible}
                onClose={closeModalEvent}
                onFinish={changeActEvent}
            ></StationTagSelectModal>
        </Fragment>
    );
};
const StationTagDragSelectFormItem = (props) => {
    const { name = 'labelIds', label = '场站标签' } = props;

    return (
        <FormItem label={label} name={name} {...props}>
            <StationTagDragSelectItem {...props}></StationTagDragSelectItem>
        </FormItem>
    );
};

export default StationTagDragSelectFormItem;
