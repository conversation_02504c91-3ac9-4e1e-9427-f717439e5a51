import { Button, Col, Form, Modal, Space, Select, Input } from 'antd';
import {
    Fragment,
    useState,
    useRef,
    useMemo,
    useEffect,
    useImperativeHandle,
    forwardRef,
} from 'react';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import StationTagPropertiesFormItem from './StationTagPropertiesFormItem';
import { getStationTagListApi } from '@/services/AssetCenter/TagManageApi';

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { form, listLoading, onSubmit, onReset, global, dispatch } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form
            {...formItemLayout}
            name="stationTagSelect"
            form={form}
            onFinish={onFinish}
            initialValues={{}}
            scrollToFirstError
        >
            <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={40}>
                <Col span={8}>
                    <FormItem label="标签名称:" name="labelName">
                        <Input placeholder="请填写" autoComplete="off" />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <StationTagPropertiesFormItem
                        label="标签属性"
                        name="labelProperties"
                    ></StationTagPropertiesFormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const StationTagSelectModal = forwardRef((props, ref) => {
    const { visible, selectTagList, onClose, onFinish, positionName, global } = props;

    const [form] = Form.useForm();

    const [listLoading, updateListLoading] = useState(false);

    const [list, updateList] = useState([]);
    const [listTotal, updateListTotal] = useState(0);

    const [selectList, updateSelectList] = useState([]);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, undefined, 'actLinkModal');

    useImperativeHandle(ref, () => ({
        clearSelect: () => {
            updateSelectList([]);
        },
    }));
    useEffect(() => {
        searchData();
    }, [pageInfo]);
    useEffect(() => {
        if (selectTagList) {
            updateSelectList(selectTagList);
        }
    }, [selectTagList]);
    useEffect(() => {
        if (!visible) {
            updateList([]);
        }
    }, visible);
    const searchData = async () => {
        try {
            updateListLoading(true);
            const data = form.getFieldsValue();
            let params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                positionName: positionName,
                ...data,
            };
            const {
                data: { records, total },
            } = await getStationTagListApi(params);
            updateList(records);
            updateListTotal(total);
            return records;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };
    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };
    const changeTabType = (type) => {
        changePageInfo((state) => ({
            ...state,
            tabType: type,
            pageIndex: 1,
        }));
    };

    const columns = [
        {
            title: '标签名称',
            dataIndex: 'labelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '标签属性',
            dataIndex: 'labelProperties',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const rowSelection = {
        type: 'checkbox',
        selectedRowKeys: selectList.map((item) => item.id),
        onChange: (selectedRowKeys, selectedRows) => {
            // 筛选出非当前页的勾选项，不予处理
            let otherCpns = selectList.filter(
                (x) => list.filter((now) => now.id == x.id).length == 0,
            );
            updateSelectList([...otherCpns, ...selectedRows]);
        },
        getCheckboxProps: (record) => ({
            disabled: record.used === true, // Column configuration not to be checked
            name: record.id,
        }),
    };
    return (
        <Fragment>
            <Modal
                title={'选择标签'}
                destroyOnClose
                width={800}
                visible={visible}
                onCancel={() => onClose && onClose()}
                footer={null}
                maskClosable={false}
            >
                <SearchLayout
                    {...props}
                    form={form}
                    listLoading={listLoading}
                    onSubmit={searchData}
                    onReset={resetData}
                ></SearchLayout>
                <TablePro
                    name="label"
                    loading={listLoading}
                    scroll={{ x: 'max-content', y: 350 }}
                    rowKey={(record, index) => record.id}
                    dataSource={list}
                    columns={columns}
                    onChange={onTableChange}
                    rowSelection={{
                        type: 'checkbox',
                        ...rowSelection,
                    }}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: listTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
                <br></br>
                <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                        type="primary"
                        onClick={() => {
                            onFinish && onFinish(selectList);
                        }}
                    >
                        提交
                    </Button>
                    <Button onClick={onClose}>取消</Button>
                </Space>
            </Modal>
        </Fragment>
    );
});

export default StationTagSelectModal;
