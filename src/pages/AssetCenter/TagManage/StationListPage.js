import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    DatePicker,
    Tabs,
    Tooltip,
    Popconfirm,
    message,
    InputNumber,
    Radio,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import styles from '@/assets/styles/common.less';
import UpLoadImg from '@/components/UpLoadImg/index.js';
import StationTagDragSelectFormItem from './components/StationTagDragSelectFormItem';
import StationTagPropertiesFormItem from './components/StationTagPropertiesFormItem';

import {
    delStationTagApi,
    updateStationTagA<PERSON>,
    updateFilterStationTagApi,
    getPosStationTagApi,
} from '@/services/AssetCenter/TagManageApi';
import { addSensitiveApi } from '@/services/AssetCenter/SensitiveApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const TAB_TYPS = {
    STATION: '1',
    FILTER: '2',
};

const formItemLayout = {};
const editFormItemLayout = {
    labelCol: {
        flex: '0 0 100px',
    },
};

const SearchLayout = (props) => {
    const { dispatch, form, onSubmit, onReset, onExportForm, listLoading, global } = props;

    useEffect(() => {}, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    timeType: '02',
                    dates: [moment().subtract(7, 'days'), moment()],
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <FormItem label="标签名称:" name="labelName">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <StationTagPropertiesFormItem
                            label="标签属性"
                            name="labelProperties"
                        ></StationTagPropertiesFormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};
const StationTagListView = (props) => {
    const {
        dispatch,
        history,
        tagModel: { stationTagList, stationTagListTotal },
        listLoading,
    } = props;

    const [form] = Form.useForm();
    const [editForm] = Form.useForm();

    const {
        location: { pathname },
    } = history;

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {},
        props,
        `station_${pathname}`,
    );

    const [editItem, changeEditItem] = useState(null);

    const iconRef = useRef();

    const [showAddView, toggleAddView] = useState(false);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                labelName: data.labelName,
                labelProperties: data.labelProperties,
            };

            dispatch({
                type: 'tagModel/getStationTagList',
                options: params,
            });
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const columns = [
        {
            title: '标签名称',
            dataIndex: 'labelName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '标签属性',
            dataIndex: 'labelProperties',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '图标',
            dataIndex: 'iconUrl',
            render(text, record) {
                return <img style={{ width: '30px' }} src={record.iconUrl}></img>;
            },
        },
        {
            title: '优先级',
            dataIndex: 'sort',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            render: (text, record) => {
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );
                const delBtn = (
                    <Popconfirm
                        title={`确定删除？`}
                        okText="是"
                        cancelText="否"
                        onConfirm={() => deleteEvent(record)}
                    >
                        <span className={styles['table-btn']}>删除</span>
                    </Popconfirm>
                );

                let btns = [editBtn, delBtn];

                return <Space>{btns}</Space>;
            },
        },
    ];

    const editEvent = (item) => {
        changeEditItem(item);
        editForm.setFieldsValue({
            ...item,
        });

        toggleAddView(true);
        setTimeout(() => {
            iconRef.current?.init(item.iconUrl);
        }, 300);
    };

    const deleteEvent = async (item) => {
        try {
            await delStationTagApi(item.id);
            await searchData();
            message.success('删除成功');

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const openAddView = () => {
        editForm.resetFields();

        toggleAddView(true);
    };

    const closeAddView = () => {
        toggleAddView(false);
        editForm.resetFields();
        changeEditItem(null);
    };

    const onAddFinish = async (values) => {
        try {
            let params = {
                ...values,
            };
            if (editItem) {
                params.id = editItem.id;
            }
            await updateStationTagApi(params);

            message.success('提交成功');

            await searchData();
            closeAddView();

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Card bordered={false}>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
            />

            <div className={styles['btn-bar']}>
                <Space>
                    <Button type="primary" onClick={openAddView}>
                        新增
                    </Button>
                </Space>
            </div>

            <TablePro
                name="list"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.id}
                dataSource={stationTagList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: stationTagListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <Modal
                title={`${editItem ? '编辑' : '添加'}标签`}
                visible={showAddView}
                footer={false}
                onCancel={closeAddView}
                destroyOnClose
                maskClosable={false}
            >
                <Form
                    {...editFormItemLayout}
                    form={editForm}
                    onFinish={onAddFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <FormItem
                        label="标签名称"
                        name="labelName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="请填写,最多8个字"
                            autoComplete="off"
                            maxLength={8}
                            showCount
                        />
                    </FormItem>
                    <StationTagPropertiesFormItem
                        label="标签属性"
                        name="labelProperties"
                        maxLength={6}
                        placeholder="请填写,最多6个字"
                        rules={[{ required: true, message: '请填写' }]}
                    ></StationTagPropertiesFormItem>
                    <UpLoadImg
                        form={editForm}
                        ref={iconRef}
                        label={
                            <span>
                                图标
                                <Tooltip title="图标在站点详情页中显示">
                                    <InfoCircleOutlined
                                        style={{
                                            marginLeft: '6px',
                                        }}
                                    />
                                </Tooltip>
                            </span>
                        }
                        labelName="图标"
                        key="iconUrl"
                        name="iconUrl"
                        rules={[{ required: true, message: '请选择图片' }]}
                        uploadData={{
                            contentType: '02',
                            contRemrk: 'iconUrl',
                            relaTable: 'a_station_label',
                        }}
                        sizeInfo={{
                            size: 100,
                        }}
                        required
                    />
                    <FormItem
                        name="sort"
                        rules={[{ required: true, message: '请填写' }]}
                        label={
                            <Tooltip title={'影响标签在场站中的显示顺序,数值越小优先级越高'}>
                                优先级
                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                            </Tooltip>
                        }
                    >
                        <InputNumber placeholder="请填写" precision={1} step={1} min={0} />
                    </FormItem>
                    <FormItem wrapperCol={{ offset: 8 }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeAddView}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Card>
    );
};

const FilterPositionView = (props) => {
    const {
        dispatch,
        history,
        tagModel: { filterStationTagList, filterStationTagListTotal },
        filterListLoading,
    } = props;
    const {
        location: { pathname },
    } = history;

    const [editForm] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, `filter_${pathname}`);
    const [editItem, changeEditItem] = useState(null);

    const [defaultEditLabels, changeDefaultEditLabels] = useState([]);

    const [showAddView, toggleAddView] = useState(false);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };

            dispatch({
                type: 'tagModel/getFilterStationTagList',
                options: params,
            });
        } catch (error) {}
    };

    const columns = [
        {
            title: '筛选位置',
            dataIndex: 'positionName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站标签',
            dataIndex: 'labelNames',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '操作',
            render: (text, record) => {
                const editBtn = (
                    <span className={styles['table-btn']} onClick={() => editEvent(record)}>
                        编辑
                    </span>
                );

                let btns = [editBtn];

                return <Space>{btns}</Space>;
            },
        },
    ];

    const editEvent = async (item) => {
        try {
            changeEditItem(item);

            const {
                data: { list },
            } = await getPosStationTagApi({ positionName: item.positionName });
            if (list instanceof Array) {
                changeDefaultEditLabels(list);
            }

            toggleAddView(true);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const openAddView = () => {
        editForm.resetFields();

        toggleAddView(true);
    };

    const closeAddView = () => {
        toggleAddView(false);
        editForm.resetFields();
        changeEditItem(null);
    };

    const onAddFinish = async (values) => {
        try {
            let params = {};
            if (values.labelIds instanceof Array) {
                const findSecondItem = (list) => {
                    let findList = [];
                    for (const item of list) {
                        if (item.parentId !== 0) {
                            findList.push(item.id);
                        }
                        if (item.children instanceof Array) {
                            findList = [...findList, ...findSecondItem(item.children)];
                        }
                    }
                    return findList;
                };
                let labelIdList = findSecondItem(values.labelIds);
                params.labelIds = labelIdList.join(',');
            }
            if (editItem) {
                params.id = editItem.id;
                await updateFilterStationTagApi(params);
                message.success('编辑成功');
            } else {
                await addSensitiveApi(params);
                message.success('新增成功');
            }

            await searchData();
            closeAddView();

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Fragment>
            <TablePro
                name="filter"
                loading={filterListLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => record.id}
                dataSource={filterStationTagList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: filterStationTagListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />

            <Modal
                title="场站列表页"
                visible={showAddView}
                footer={false}
                onCancel={closeAddView}
                destroyOnClose
                maskClosable={false}
            >
                <Form
                    {...editFormItemLayout}
                    form={editForm}
                    onFinish={onAddFinish}
                    initialValues={{}}
                    scrollToFirstError
                >
                    <StationTagDragSelectFormItem
                        from={editForm}
                        name="labelIds"
                        defaultSelectList={defaultEditLabels}
                        positionName={editItem?.positionName || ''}
                        rules={[{ required: true, message: '请选择' }]}
                        unclassified={editItem?.positionName === '筛选集合页' ? true : false}
                    />

                    <FormItem wrapperCol={{ offset: 8 }}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                提交
                            </Button>
                            <Button onClick={closeAddView}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>
        </Fragment>
    );
};

const StationListPage = (props) => {
    const [tabType, changeTabType] = useState(TAB_TYPS.STATION);

    const pageView = useMemo(() => {
        if (tabType === TAB_TYPS.STATION) {
            return <StationTagListView {...props}></StationTagListView>;
        } else if (tabType === TAB_TYPS.FILTER) {
            return <FilterPositionView {...props}></FilterPositionView>;
        }
    });

    return (
        <PageHeaderWrapper>
            <Card>
                <div className="mg-b">
                    <Radio.Group
                        defaultValue={tabType}
                        onChange={(event) => {
                            const {
                                target: { value },
                            } = event;
                            changeTabType(value);
                        }}
                        buttonStyle="solid"
                    >
                        <Radio.Button value={TAB_TYPS.STATION}>场站标签</Radio.Button>
                        <Radio.Button value={TAB_TYPS.FILTER}>筛选位置</Radio.Button>
                    </Radio.Group>
                </div>
                {pageView}
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ tagModel, global, loading }) => ({
    tagModel,
    global,
    listLoading: loading.effects['tagModel/getStationTagList'],
    filterListLoading: loading.effects['tagModel/getFilterStationTagList'],
}))(StationListPage);
