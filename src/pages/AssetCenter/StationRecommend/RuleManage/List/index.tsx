import {
    <PERSON>,
    Ta<PERSON>,
    Popover,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>po<PERSON>,
    Button,
    Col,
    Form,
    Select,
    Modal,
    Input,
    Switch,
    Popconfirm,
    Row,
} from 'antd';
import { connect, useHistory } from 'umi';
import { Fragment, useState, useRef, useEffect } from 'react';
import SearchView from '@/components/SearchOptionsBar/SearchView';
import CityTransferModal from '@/components/CityTransferModal';

// import AllStationSelect from '@/components/AllStationSelect';

// import CitysSelect from '@/components/CitysSelect/index.js';
// import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import { isEmpty } from '@/utils/utils';
import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    getStationRecommendListApi,
    updateStationRecommendStatusApi,
    sortStationRecommendApi,
    saveStationRecommendScope<PERSON>pi,
    delStationRecommendByIdApi,
} from '@/services/AssetCenter/RuleManageApi';
import { useRequest } from 'ahooks';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const ManagePage = (props: any) => {
    const [searchForm] = Form.useForm();
    const history = useHistory();

    const cityModalRef = useRef();

    const [tabType, updateTabType] = useState('00');

    const [selectCityItem, updateSelectCityItem] = useState();

    const columns = [
        {
            title: '规则ID',
            width: 160,
            dataIndex: 'configId',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '规则名称',
            width: 160,
            dataIndex: 'configName',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '规则详情',
            width: 240,
            dataIndex: 'configDetailDesc',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '生效范围',
            width: 140,
            dataIndex: 'scopeDesc',
            render(text: string, record: any) {
                return (
                    <Typography.Link
                        onClick={() => {
                            cityModalRef?.current?.show({
                                defaultKeys: record.cityList,
                                disabled: true,
                            });
                        }}
                    >
                        {record.scopeDesc}
                    </Typography.Link>
                );
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTimeStr',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        // {
        //     title: '是否置顶',
        //     width: 120,
        //     dataIndex: 'top',
        //     render(text: string, record: any) {
        //         const result = record.top ? '是' : '否';
        //         return <span title={result}>{result}</span>;
        //     },
        // },

        {
            title: '状态',
            width: 140,
            dataIndex: 'configStatus',
            render(text: string, record: any) {
                return (
                    <Switch
                        loading={statusLoading}
                        checkedChildren="启用"
                        unCheckedChildren="关闭"
                        checked={record.configStatus == '01'}
                        onChange={(newVal) => {
                            console.log(newVal);
                            const params = {
                                configId: record.configId,
                            };
                            if (newVal) {
                                params.configStatus = '01';
                            } else {
                                params.configStatus = '09';
                            }
                            updateStatus(params);
                        }}
                    ></Switch>
                );
            },
        },
        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render(text: string, record: any) {
                const editBtn = (
                    <Typography.Link
                        onClick={() => {
                            history.push(
                                `/assetCenter/stationRecommend/manage/edit?configId=${record.configId}`,
                            );
                        }}
                    >
                        编辑
                    </Typography.Link>
                );
                const rangeBtn = (
                    <Typography.Link
                        onClick={() => {
                            updateSelectCityItem(record);
                            cityModalRef?.current?.show({
                                defaultKeys: record.cityList,
                            });
                        }}
                    >
                        配置生效范围
                    </Typography.Link>
                );

                const delBtn = (
                    <Popconfirm
                        title="确定删除规则"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => {
                            delEvent(record?.configId);
                        }}
                    >
                        <Typography.Text type="danger">删除</Typography.Text>
                    </Popconfirm>
                );

                const btnList = [editBtn, rangeBtn, delBtn];

                return <Space> {btnList}</Space>;
            },
        },
    ];
    const searchRef = useRef();

    const initTableEvent = async (params: API.QueryThresholdListRequest) => {
        try {
            const options = {
                ...params,
                configType: '01',
            };

            return getStationRecommendListApi(options);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const { run: updateStatus, loading: statusLoading } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await updateStationRecommendStatusApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
            },
        },
    );

    const { run: sortList } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await sortStationRecommendApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
            },
        },
    );

    const { run: scopedEvent } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await saveStationRecommendScopeApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
                updateSelectCityItem(undefined);
            },
        },
    );

    const { run: delEvent } = useRequest(
        async (configId: number) => {
            try {
                const options = {
                    configId,
                };
                await delStationRecommendByIdApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
                updateSelectCityItem(undefined);
            },
        },
    );

    useEffect(() => {
        if (tabType) {
            searchRef?.current?.search();
        }
    }, [tabType]);

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchView
                    rowKey="index"
                    ref={searchRef}
                    form={searchForm}
                    requestApi={initTableEvent}
                    requestParams={{ configStatus: tabType == '00' ? undefined : tabType }}
                    columns={columns}
                    toolsBar={
                        <Fragment>
                            <p>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        history.push('/assetCenter/stationRecommend/manage/edit');
                                    }}
                                >
                                    新增规则
                                </Button>
                            </p>
                            <Tabs activeKey={tabType} onChange={(v) => updateTabType(v)}>
                                <Tabs.TabPane tab="全部" key="00"></Tabs.TabPane>
                                <Tabs.TabPane tab="启动" key="01"></Tabs.TabPane>

                                <Tabs.TabPane tab="关闭" key="09"></Tabs.TabPane>
                            </Tabs>
                        </Fragment>
                    }
                    isDrag
                    setDataSource={(newitem, newIndex, oldIndex) => {
                        // console.log(
                        //     3213,
                        //     newitem,
                        //     newIndex,
                        //     oldIndex,
                        //     searchRef?.current?.getList(),
                        // );
                        const list = newitem?.map((item: any) => {
                            return {
                                configId: item.configId,
                                configSn: item.configSn,
                            };
                        });
                        const params = {
                            sortList: list,
                        };
                        sortList(params);
                    }}
                >
                    <Col span={8}>
                        <Form.Item name="configId" label="规则ID">
                            <Input autoComplete="off" placeholder="请填写"></Input>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="configName" label="规则名称">
                            <Input autoComplete="off" placeholder="请填写"></Input>
                        </Form.Item>
                    </Col>
                </SearchView>
            </Card>

            <CityTransferModal
                ref={cityModalRef}
                onFinish={(cityList: any) => {
                    scopedEvent({
                        configId: selectCityItem?.configId,
                        scopeType: '3',
                        scopeList: cityList?.map((ele) => {
                            return {
                                city: ele.areaCode,
                                cityName: ele.areaName,
                            };
                        }),
                    });
                }}
                zIndex={100}
                deleteEnabled={false}
            />
        </PageHeaderWrapper>
    );
};

export default ManagePage;
