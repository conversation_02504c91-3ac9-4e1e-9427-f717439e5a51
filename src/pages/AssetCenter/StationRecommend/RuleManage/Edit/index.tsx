import React, { useEffect, useRef } from 'react';
import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import { Card, Typography, Form, Space, Button, Input, Select, message } from 'antd';

import { useHistory, useLocation, connect } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import commonStyles from '@/assets/styles/common.less';
import RuleView, { RuleTypes } from './RuleView';
import {
    saveStationRecommendApi,
    getStationRecommendByIdApi,
} from '@/services/AssetCenter/RuleManageApi';

const { Option } = Select;

const DiagnosisPage: React.FC<any> = (props) => {
    const { route } = props;
    const [form] = Form.useForm();
    const history = useHistory();
    const location = useLocation();

    const { configId } = location?.query ?? {};

    const stationRef = useRef();

    useEffect(() => {
        if (configId) {
            console.log(3232, configId);
            run();
        }
    }, [configId]);

    const {
        run,
        data: configInfo,
        loading,
    } = useRequest(
        async () => {
            try {
                const { data } = await getStationRecommendByIdApi({ configId });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess(res) {
                setTimeout(() => {
                    stationRef?.current?.setAllStations(configInfo?.detailList);
                }, 500);
            },
        },
    );

    useEffect(() => {
        if (configInfo) {
            const params = {
                ...configInfo,
            };
            if (params.ruleType === RuleTypes.Station) {
                params.stationInfo = {
                    allStations: params.detailList,
                };

                delete params.detailList;
            }
            form.setFieldsValue(params);
        }
    }, [configInfo]);

    const {
        run: finishEvent,

        loading: submitLoading,
    } = useRequest(
        async (values = {}) => {
            try {
                const options = {
                    ...values,
                    configType: '01',
                };
                if (configId) {
                    options.configId = configId;
                }
                if (options.configRuleType == RuleTypes.Station) {
                    const { allStations } = options.stationInfo;

                    options.detailList = allStations?.map((ele) => {
                        return {
                            ruleValue: ele.stationId,
                        };
                    });
                }
                delete options.stationInfo;
                await saveStationRecommendApi(options);
                message.success('保存成功');
                goBack();
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    const goBack = () => {
        history.replace('/assetCenter/stationRecommend/manage');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={loading}>
                <Form
                    form={form}
                    onFinish={finishEvent}
                    // labelCol={{ flex: '0 0 140px' }}
                    wrapperCol={{ span: 8 }}
                >
                    <div className={commonStyles['form-title']}>基础信息</div>
                    <Form.Item
                        label="规则名称"
                        name="configName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="请填写"
                            autoComplete="off"
                            showCount
                            maxLength={20}
                        ></Input>
                    </Form.Item>

                    <RuleView stationRef={stationRef}></RuleView>

                    <Space
                        align="end"
                        className="mg-t-20"
                        style={{ display: 'flex', justifyContent: 'center' }}
                    >
                        <Button type="primary" htmlType="submit" loading={submitLoading}>
                            提交
                        </Button>
                        <Button onClick={goBack}>取消</Button>
                    </Space>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default DiagnosisPage;
