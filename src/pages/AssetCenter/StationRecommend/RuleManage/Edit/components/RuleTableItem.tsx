import { Table, Select, Form, Input, Space, Button, Typography, InputNumber } from 'antd';
import { useEffect, useState, useMemo } from 'react';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { getCodesApi } from '@/services/CommonApi';
import { getStationRuleDownApi } from '@/services/AssetCenter/RuleManageApi';
import { useRequest } from 'ahooks';

const { TextArea } = Input;
interface RuleTableItemVo {
    ruleValueType?: string;
    ruleCalculateType?: string;
    remark?: string;
    actType?: string;
    ruleName?: string;
    ruleCode?: string;
}
const { Option } = Select;
export const checkItemRule = (list: RuleTableItemVo[]) => {
    let errMsg = '';
    for (const item of list) {
        for (const key in item) {
            errMsg = checkItemByKey(item, key);
            if (errMsg) {
                break;
            }
        }
    }
    return errMsg;
};
const checkItemByKey = (item: RuleTableItemVo, key: string): string => {
    let errMsg = '';
    if (!item[key]) {
        switch (key) {
            case 'ruleValueType':
                errMsg = '请选择';
                break;
            case 'ruleCalculateType':
                errMsg = '请选择';
                break;
            case 'remark':
                errMsg = '请填写';
                break;
            case 'actType':
                errMsg = '请选择推荐活动类型';
                break;

            default:
                break;
        }
    }
    return errMsg;
};
const RuleTableItem = (props: {
    disabled?: boolean;
    value?: RuleTableItemVo[];
    onChange?: (params: RuleTableItemVo[]) => void;
}) => {
    const { disabled, value, onChange } = props;
    const [tableList, updateTableList] = useState<RuleTableItemVo[]>([{}]);

    useEffect(() => {
        if (value instanceof Array) {
            updateTableList(value);
        }
    }, [value]);

    // const { data: ruleCalculateTypeList } = useRequest(async () => {
    //     try {
    //         const {
    //             data: { list },
    //         } = await getCodesApi('ruleCalculateType');
    //         return list;
    //     } catch (error) {
    //         return Promise.reject(error);
    //     }
    // }, {});

    const { data: getDownList } = useRequest(async () => {
        try {
            const { data } = await getStationRuleDownApi({ ruleType: '01' });
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    }, {});

    const addEvent = () => {
        const newList = [...tableList];
        newList.unshift({});

        updateFormItem(newList);
    };
    const removeEvent = (index: number) => {
        const newList = [...tableList];
        newList.splice(index, 1);
        updateFormItem(newList);
    };

    const updateItem = (index: number, info: RuleTableItemVo) => {
        const item = tableList[index];
        const newItem = {
            ...item,
            ...info,
        };
        const newList = [...tableList];
        newList.splice(index, 1, newItem);
        updateFormItem(newList);
    };

    const updateFormItem = (newList: RuleTableItemVo[]) => {
        updateTableList(newList);
        onChange && onChange(newList);
    };

    const topEvent = (index: number) => {
        if (index == 0) {
            return;
        }
        const newList = [...tableList];
        const item = newList[index];
        newList.splice(index, 1);
        newList.unshift(item);
        updateFormItem(newList);
    };

    const ruleTypeOptions = useMemo(() => {
        return getDownList?.map((ele, index) => (
            <Option value={ele.ruleValueType} key={ele.ruleValueType}>
                {ele.ruleName}
            </Option>
        ));
    }, [getDownList]);

    const getCurDownInfo = (ruleValueType: string) => {
        if (getDownList instanceof Array) {
            const findDownInfo = getDownList.find((ele) => ele.ruleValueType == ruleValueType);
            return findDownInfo;
        }
        return null;
    };

    const columns = [
        {
            title: '指标类型',
            dataIndex: 'ruleValueType',
            render(text: string, record: RuleTableItemVo, index: number) {
                const errMsg = checkItemByKey(record, 'ruleValueType');
                return (
                    <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                        <Select
                            value={record.ruleValueType}
                            disabled={disabled}
                            placeholder="请选择"
                            onChange={(newVal) => {
                                const findItem = getCurDownInfo(newVal);
                                updateItem(index, {
                                    ruleValueType: newVal,
                                    ruleName: findItem?.ruleName || '',
                                    ruleCode: findItem?.ruleCode || '',
                                });
                            }}
                            allowClear
                        >
                            {ruleTypeOptions}
                        </Select>
                    </Form.Item>
                );
            },
        },
        {
            title: '指标计算',
            dataIndex: 'ruleCalculateType',
            render(text: string, record: RuleTableItemVo, index: number) {
                const errMsg = checkItemByKey(record, 'ruleCalculateType');
                let ruleCalculateTypeOptions = undefined;
                if (record.ruleValueType && getDownList instanceof Array) {
                    const findDownInfo = getDownList.find(
                        (ele) => ele.ruleValueType == record.ruleValueType,
                    );
                    if (findDownInfo) {
                        ruleCalculateTypeOptions =
                            findDownInfo?.ruleCalculateTypeList.map((ele: any, index: number) => (
                                <Option value={ele.value} key={ele.value}>
                                    {ele.name}
                                </Option>
                            )) || [];
                    }
                }
                return (
                    <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                        <Select
                            value={record.ruleCalculateType}
                            disabled={disabled}
                            placeholder="请选择"
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleCalculateType: newVal,
                                });
                            }}
                        >
                            {ruleCalculateTypeOptions}
                        </Select>
                    </Form.Item>
                );
            },
        },
        {
            title: '指标值',
            dataIndex: 'ruleValue',
            render(text: string, record: RuleTableItemVo, index: number) {
                const errMsg = checkItemByKey(record, 'ruleValue');
                return (
                    <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                        <InputNumber
                            style={{ width: '100%' }}
                            min={'0'}
                            step={0.01}
                            precision={2}
                            value={record.ruleValue}
                            disabled={disabled}
                            placeholder="请填写"
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValue: newVal,
                                });
                            }}
                        ></InputNumber>
                    </Form.Item>
                );
            },
        },

        {
            title: '操作 ',

            width: 120,
            render(text: string, record: RuleTableItemVo, index: number) {
                const minusBtn = (
                    <Typography.Link
                        onClick={() => {
                            removeEvent(index);
                        }}
                    >
                        删除
                    </Typography.Link>
                );

                const topBtn = (
                    <Typography.Link
                        onClick={() => {
                            topEvent(index);
                        }}
                    >
                        置顶
                    </Typography.Link>
                );

                const btnList = [];
                if (index > 0) {
                    btnList.push(topBtn);
                }

                return <Space style={{ transform: 'translateY(-10px)' }}>{btnList}</Space>;
            },
        },
    ];
    return (
        <div>
            {/* <p>
                <Button
                    type="primary"
                    onClick={() => {
                        addEvent();
                    }}
                >
                    新增指标
                </Button>
            </p> */}
            <Table dataSource={tableList} columns={columns} pagination={false}></Table>
        </div>
    );
};

export default RuleTableItem;
