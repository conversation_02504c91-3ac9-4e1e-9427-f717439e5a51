import {
    Card,
    Typography,
    Form,
    Space,
    Button,
    Input,
    Radio,
    Alert,
    Select,
    InputNumber,
} from 'antd';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import RuleTableItem from './components/RuleTableItem';
import { saveStationScopeInfoApi, getStationScopeListApi } from '@/services/CommonApi';
import { STATION_CONFIG_PAGE_TYPES } from '@/config/declare';

import { isEmpty } from '@/utils/utils';

import commonStyles from '@/assets/styles/common.less';
export enum RuleTypes {
    Rule = '01', //按规则获取站点
    Station = '02', //按固定站点获取
}
const RuleView = (props) => {
    const { stationRef } = props;
    return (
        <>
            <div className={commonStyles['form-title']}>规则配置</div>

            <Form.Item
                label="获取规则方式"
                name="configRuleType"
                initialValue={RuleTypes.Rule}
                wrapperCol={{ span: 24 }}
            >
                <Radio.Group>
                    <Radio value={RuleTypes.Rule}>按规则获取站点</Radio>
                    <Radio value={RuleTypes.Station}>按配置固定站点获取</Radio>
                </Radio.Group>
            </Form.Item>

            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.configRuleType !== curValues.configRuleType
                }
            >
                {({ getFieldValue, setFieldsValue }) => {
                    const configRuleType = getFieldValue('configRuleType');
                    let alertMsg = '';
                    if (configRuleType == RuleTypes.Rule) {
                        alertMsg =
                            '排序时，若多个站点符合【置顶规则】，则按评分取最高的站点置顶展示';
                    } else if (configRuleType == RuleTypes.Station) {
                        alertMsg = '排序时，若多个站点符合【置顶规则】，则取权重最高的站点置顶展示';
                    }
                    return (
                        <>
                            <Form.Item label="置顶规则" required></Form.Item>
                            <Form.Item wrapperCol={{ span: 24 }}>
                                <Alert message={alertMsg}></Alert>
                            </Form.Item>

                            {configRuleType == RuleTypes.Rule && (
                                <Form.Item
                                    label="站点指标"
                                    name={'detailList'}
                                    wrapperCol={{ span: 24 }}
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置站点指标');
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={[{}]}
                                >
                                    <RuleTableItem></RuleTableItem>
                                </Form.Item>
                            )}
                            {configRuleType == RuleTypes.Station && (
                                <Form.Item
                                    label="活动范围"
                                    name={'stationInfo'}
                                    wrapperCol={{ span: 24 }}
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                const { addStations, allStations } = value;
                                                if (isEmpty(allStations) && isEmpty(addStations)) {
                                                    return Promise.reject('请配置指定场站');
                                                }
                                                if (
                                                    addStations.length > 20000 ||
                                                    allStations.length > 20000
                                                ) {
                                                    return Promise.reject(
                                                        '一次性最多配置2万个站点',
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                >
                                    <SearchStationItem
                                        ref={stationRef}
                                        title="活动范围"
                                        // requestInfo={
                                        //     actId &&
                                        //     !isEmpty(editActInfo) && {
                                        //         listApi: getStationScopeListApi,
                                        //         params: {
                                        //             scopeRelateId: actId,
                                        //             scopeRelateType:
                                        //                 STATION_CONFIG_PAGE_TYPES.COUPON_1,
                                        //         },

                                        //         recordParams: {
                                        //             relateId: cpnId,
                                        //             scene: 'stc_coupon',
                                        //         },
                                        //     }
                                        // }
                                    ></SearchStationItem>
                                </Form.Item>
                            )}
                        </>
                    );
                }}
            </Form.Item>
        </>
    );
};

export default RuleView;
