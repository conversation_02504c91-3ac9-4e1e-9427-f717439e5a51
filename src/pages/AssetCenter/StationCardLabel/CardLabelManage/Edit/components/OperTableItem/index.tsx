import { Space, Typography, Tag, Divider } from 'antd';
import { useState } from 'react';
import OperEditDrawer from './OperEditDrawer';

const StationTableItem = (props) => {
    const { value = [], onChange } = props;
    const [visible, setVisible] = useState(false);
    return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ flex: 1, paddingRight: 20 }}>
                {value.map((item, i) => {
                    if (i < 3) {
                        return (
                            <Tag key={i} style={{ marginBottom: 5 }}>
                                {item.operId}
                                <Divider type="vertical" />
                                {item.buildName}
                            </Tag>
                        );
                    }
                    return;
                })}
                {value?.length > 3 && (
                    <Tag style={{ marginBottom: 5 }}>+ {value?.length - 3} ...</Tag>
                )}
            </div>
            <Typography.Link onClick={() => setVisible(true)}>批量编辑</Typography.Link>
            {visible && (
                <OperEditDrawer
                    value={value}
                    visible={visible}
                    onClose={() => setVisible(false)}
                    onChange={onChange}
                />
            )}
        </div>
    );
};

export default StationTableItem;
