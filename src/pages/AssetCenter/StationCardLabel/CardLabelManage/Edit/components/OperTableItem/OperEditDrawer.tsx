import ImportModal from '@/components/ImportModal';
import SelectOperModal from '@/components/OperSelectItem/SelectOperModal';
import XdtProTable from '@/components/XdtProTable';
import { importOperApi } from '@/services/Marketing/ReadjustApi';
import { Button, Drawer, Form, Space, Typography } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

const OperEditDrawer = (props) => {
    const { onClose, visible, onChange, value = [] } = props;
    const [tableData, setTableData] = useState([]);
    const [formFilterParams, updateFormFilterParams] = useState();
    const importRef = useRef();
    const selectOperRef = useRef();

    useEffect(() => {
        setTableData(value);
    }, [value]);
    const onDel = (operId) => {
        const newData = tableData.filter((item) => item.operId !== operId);
        setTableData(newData);
    };

    const operColumns = [
        {
            title: '运营商ID',
            dataIndex: 'operId',
        },
        {
            title: '运营商名称',
            dataIndex: 'buildName',
        },
        {
            title: '操作',
            dataIndex: 'operId',
            render: (_, record) => {
                return (
                    <Typography.Link type="danger" onClick={() => onDel(record?.operId)}>
                        删除
                    </Typography.Link>
                );
            },
        },
    ];
    const formatList = useMemo(() => {
        return tableData?.filter((ele) => {
            if (formFilterParams) {
                if (formFilterParams.operId) {
                    return ele.operId == formFilterParams.operId;
                }
                if (formFilterParams.buildName) {
                    return ele.buildName?.indexOf(formFilterParams.buildName) >= 0;
                }
            }
            return true;
        });
    }, [formFilterParams, tableData]);
    return (
        <Drawer
            title="批量编辑"
            placement="right"
            width={1300}
            onClose={onClose}
            visible={visible}
            destroyOnClose
            footer={[
                <Space key="1">
                    <Button onClick={() => onClose()}>取消</Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            onChange(tableData);
                            onClose();
                        }}
                    >
                        确定
                    </Button>
                </Space>,
            ]}
        >
            <ImportModal
                ref={importRef}
                title="批量导入运营商"
                downLoadPath="/aliMini/xdt/static/excel/导入运营商模版.xlsx"
                onUpload={async (formData: FormData, callback?: (params: any) => void) => {
                    const { ret, data } = await importOperApi(formData);

                    const successList = data?.filter((ele) => ele.matchFlag);
                    const failList = data?.filter((ele) => !ele.matchFlag);

                    const oldList = tableData?.filter(
                        (ele) => !successList?.find((item: any) => item.operId == ele.operId),
                    );
                    const newList = [...oldList, ...successList];
                    setTableData(newList);

                    callback &&
                        callback({
                            ret: ret == 200 ? 'suc' : 'false',
                            msg:
                                ret == 200
                                    ? `导入成功${successList?.length}条`
                                    : `导入失败${failList?.length}条`,
                        });
                }}
            ></ImportModal>
            <SelectOperModal
                ref={selectOperRef}
                onConfirm={(operList: any[]) => {
                    setTableData(operList);
                }}
            />
            <XdtProTable
                // formRef={formRef}
                dataSource={formatList}
                columns={operColumns}
                // rowSelection={rowSelection}
                toolBarRender={() => (
                    <Space key="1">
                        <Typography.Link onClick={() => selectOperRef?.current?.open(tableData)}>
                            添加
                        </Typography.Link>
                        <Typography.Link onClick={() => importRef?.current?.show()}>
                            导入
                        </Typography.Link>
                    </Space>
                )}
                rowKey="operId"
                request={async (params) => {
                    const options = {
                        ...params,
                    };
                    delete options.current;
                    delete options.pageSize;
                    updateFormFilterParams(options);
                    return;
                }}
            ></XdtProTable>
        </Drawer>
    );
};

export default OperEditDrawer;
