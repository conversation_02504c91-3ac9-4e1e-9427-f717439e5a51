import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import { Button, Drawer, Form, Space } from 'antd';
import { isEmpty } from 'lodash';
import { useEffect, useRef } from 'react';

const StationEditDrawer = (props) => {
    const { onClose, visible, onChange, value } = props;
    const stationRef = useRef();
    const [formDrawer] = Form.useForm();
    const onFinish = (values) => {
        const { stationInfo = {} } = values;
        const { allStations } = stationInfo;
        onChange(allStations);
        onClose();
    };
    useEffect(() => {
        if (visible) {
            setTimeout(() => {
                stationRef?.current?.setAllStations(value);
            }, 500);
        }
    }, [visible]);
    return (
        <Drawer
            title="批量编辑"
            placement="right"
            width={1300}
            onClose={onClose}
            visible={visible}
            destroyOnClose
            footer={[
                <Space key="1">
                    <Button onClick={() => onClose()}>取消</Button>
                    <Button type="primary" onClick={() => formDrawer.submit()}>
                        确定
                    </Button>
                </Space>,
            ]}
        >
            <Form form={formDrawer} onFinish={onFinish}>
                <Form.Item
                    label=""
                    name={'stationInfo'}
                    wrapperCol={{ span: 24 }}
                    required
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject('请配置指定场站');
                                }
                                const { addStations, allStations } = value;
                                if (isEmpty(allStations) && isEmpty(addStations)) {
                                    return Promise.reject('请配置指定场站');
                                }
                                if (addStations.length > 20000 || allStations.length > 20000) {
                                    return Promise.reject('一次性最多配置2万个站点');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <SearchStationItem ref={stationRef} title="活动范围" />
                </Form.Item>
            </Form>
        </Drawer>
    );
};

export default StationEditDrawer;
