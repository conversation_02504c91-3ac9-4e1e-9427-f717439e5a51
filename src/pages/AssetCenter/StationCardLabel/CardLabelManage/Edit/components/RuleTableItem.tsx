import { Table, Select, Form, Input, Space, Button, Typography, InputNumber } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useEffect, useState, useMemo } from 'react';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { getCodesApi } from '@/services/CommonApi';
import { getStationRuleDownApi } from '@/services/AssetCenter/RuleManageApi';
import { useRequest } from 'ahooks';
import StationTableItem from './StationTableItem';
import OperTableItem from './OperTableItem';
import { connect } from 'umi';

const { TextArea } = Input;
interface RuleTableItemVo {
    ruleValueType?: string;
    ruleCalculateType?: string;
    remark?: string;
    actType?: string;
    ruleName?: string;
    ruleCode?: string;
    ruleValues?: any;
    ruleValue?: any;
}
const { Option } = Select;
export const checkItemRule = (list: RuleTableItemVo[]) => {
    let errMsg = '';
    for (const item of list) {
        for (const key in item) {
            errMsg = checkItemByKey(item, key);
            if (errMsg) {
                break;
            }
        }
    }
    return errMsg;
};
const checkItemByKey = (item: RuleTableItemVo, key: string): string => {
    let errMsg = '';
    if (!item[key]) {
        switch (key) {
            case 'ruleCode':
                errMsg = '请选择';
                break;
            case 'ruleValueType':
                errMsg = '请选择';
                break;
            case 'ruleCalculateType':
                errMsg = '请选择';
                break;
            case 'remark':
                errMsg = '请填写';
                break;
            case 'actType':
                errMsg = '请选择推荐活动类型';
                break;
            case 'ruleValue':
                if (item?.ruleCode) {
                    errMsg = '请填写';
                }
                break;
            case 'ruleValues':
                if (item?.ruleCode) {
                    errMsg = '请填写';
                }
                break;
            default:
                break;
        }
    } else {
        // 校验站点，运营商，站点接入类型
        if (key === 'ruleValues') {
            // 计算条件为参数过滤
            if (item?.ruleValueType === '02') {
                // 运算符为等于不等于
                if (item?.ruleCalculateType === '05' || item?.ruleCalculateType === '06') {
                    const newDataArray = item?.ruleValues || [];
                    // 指标值不允许超过1个
                    if (newDataArray.length > 1) {
                        errMsg = '运算符选择等于或者不等于，指标值不能超过1个';
                    }
                }
            }
        }
    }
    return errMsg;
};
const RuleTableItem = (props: {
    disabled?: boolean;
    value?: RuleTableItemVo[];
    configSubType?: string;
    ConfigSubTypes?: any;
    onChange?: (params: RuleTableItemVo[]) => void;
    global: any;
    dispatch: any;
}) => {
    const {
        disabled,
        value,
        onChange,
        configSubType,
        ConfigSubTypes,
        global: { codeInfo = {} },
        dispatch,
    } = props;
    const { stationAccessType: stationAccessTypeList } = codeInfo;
    const [tableList, updateTableList] = useState<RuleTableItemVo[]>([{}]);

    useEffect(() => {
        if (value instanceof Array) {
            updateTableList(value);
        }
    }, [value]);

    // const { data: ruleCalculateTypeList } = useRequest(async () => {
    //     try {
    //         const {
    //             data: { list },
    //         } = await getCodesApi('ruleCalculateType');
    //         return list;
    //     } catch (error) {
    //         return Promise.reject(error);
    //     }
    // }, {});

    const { data: getDownList } = useRequest(async () => {
        try {
            const { data } = await getStationRuleDownApi({
                ruleType: '02',
                ruleSubType: configSubType,
            });
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    }, {});

    const addEvent = () => {
        const newList = [...tableList];
        newList.push({});

        updateFormItem(newList);
    };
    const removeEvent = (index: number) => {
        const newList = [...tableList];
        newList.splice(index, 1);
        updateFormItem(newList);
    };

    const updateItem = (index: number, info: RuleTableItemVo) => {
        const item = tableList[index];
        const newItem = {
            ...item,
            ...info,
        };
        const newList = [...tableList];
        newList.splice(index, 1, newItem);
        updateFormItem(newList);
    };

    const updateFormItem = (newList: RuleTableItemVo[]) => {
        updateTableList(newList);
        onChange && onChange(newList);
    };
    useEffect(() => {
        if (!stationAccessTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'stationAccessType',
            });
        }
    }, []);
    const topEvent = (index: number) => {
        if (index == 0) {
            return;
        }
        const newList = [...tableList];
        const item = newList[index];
        newList.splice(index, 1);
        newList.unshift(item);
        updateFormItem(newList);
    };

    const ruleTypeOptions = useMemo(() => {
        return getDownList?.map((ele, index) => (
            <Option value={ele.ruleCode} key={ele.ruleCode}>
                {ele.ruleName}
            </Option>
        ));
    }, [getDownList]);

    const getCurDownInfo = (ruleCode: string) => {
        if (getDownList instanceof Array) {
            const findDownInfo = getDownList.find((ele) => ele.ruleCode == ruleCode);
            return findDownInfo;
        }
        return null;
    };
    const ruleValueFun = (record, index) => {
        const { ruleCode } = record;
        if (ruleCode) {
            switch (ruleCode) {
                // 站点
                case 'stationId':
                    return (
                        <StationTableItem
                            value={record.ruleValues}
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValues: newVal,
                                });
                            }}
                        />
                    );
                    break;
                // 运营商
                case 'buildId':
                    return (
                        <OperTableItem
                            value={record.ruleValues}
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValues: newVal,
                                });
                            }}
                        />
                    );
                    break;
                // 站点接入类型
                case 'stationAccessType':
                    return (
                        <Select
                            mode="multiple"
                            allowClear
                            options={stationAccessTypeList}
                            fieldNames={{ label: 'codeName', value: 'codeValue' }}
                            placeholder="请选择"
                            value={record.ruleValues}
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValues: newVal,
                                });
                            }}
                        />
                    );
                    break;
                // 收入比例
                case 'incomeRatio':
                    return (
                        <InputNumber
                            style={{ width: '100%' }}
                            min={'0'}
                            step={0.01}
                            precision={2}
                            value={record.ruleValue}
                            disabled={disabled}
                            addonAfter={'%'}
                            placeholder="请填写"
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValue: newVal,
                                });
                            }}
                        />
                    );
                    break;
                default:
                    return (
                        <InputNumber
                            style={{ width: '100%' }}
                            min={'0'}
                            step={0.01}
                            precision={2}
                            value={record.ruleValue}
                            disabled={disabled}
                            placeholder="请填写"
                            onChange={(newVal) => {
                                updateItem(index, {
                                    ruleValue: newVal,
                                });
                            }}
                        />
                    );
                    break;
            }
        }
    };
    const columns = [
        {
            title: '站点指标条件',
            dataIndex: 'ruleCode',
            width: 300,
            formItemProps: () => {
                return {
                    rules: [{ required: true, message: '请选择' }],
                };
            },
            render(text: string, record: RuleTableItemVo, index: number) {
                const errMsg = checkItemByKey(record, 'ruleCode');
                return (
                    <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                        <Select
                            value={record.ruleCode}
                            disabled={disabled}
                            placeholder="请选择"
                            style={{ maxWidth: 300 }}
                            onChange={(newVal) => {
                                const findItem = getCurDownInfo(newVal);
                                updateItem(index, {
                                    ruleValueType: findItem?.ruleValueType || '',
                                    ruleName: findItem?.ruleName || '',
                                    ruleCode: findItem?.ruleCode || '',
                                    ruleValues: undefined,
                                    ruleValue: undefined,
                                });
                            }}
                            allowClear
                        >
                            {ruleTypeOptions}
                        </Select>
                    </Form.Item>
                );
            },
        },
        ...(configSubType !== ConfigSubTypes?.Virtually
            ? [
                  {
                      title: '计算条件',
                      width: 300,
                      dataIndex: 'ruleValueType',
                      render(text: string, record: RuleTableItemVo, index: number) {
                          const errMsg = checkItemByKey(record, 'ruleValueType');
                          let ruleValueTypeOptions = undefined;
                          if (record.ruleValueType && getDownList instanceof Array) {
                              const findDownInfo = getDownList.find(
                                  (ele) => ele.ruleValueType == record.ruleValueType,
                              );
                              if (findDownInfo) {
                                  ruleValueTypeOptions =
                                      findDownInfo?.ruleValueTypeList.map(
                                          (ele: any, index: number) => (
                                              <Option value={ele.value} key={ele.value}>
                                                  {ele.name}
                                              </Option>
                                          ),
                                      ) || [];
                              }
                          }
                          return (
                              <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                                  <Select
                                      value={record.ruleValueType}
                                      disabled={disabled}
                                      placeholder="请选择"
                                      onChange={(newVal) => {
                                          updateItem(index, {
                                              ruleValueType: newVal,
                                          });
                                      }}
                                  >
                                      {ruleValueTypeOptions}
                                  </Select>
                              </Form.Item>
                          );
                      },
                  },
                  {
                      title: '运算符',
                      width: 300,
                      dataIndex: 'ruleCalculateType',
                      render(text: string, record: RuleTableItemVo, index: number) {
                          const errMsg = checkItemByKey(record, 'ruleCalculateType');
                          let ruleCalculateTypeOptions = undefined;
                          if (record.ruleValueType && getDownList instanceof Array) {
                              const findDownInfo = getDownList.find(
                                  (ele) => ele.ruleValueType == record.ruleValueType,
                              );
                              if (findDownInfo) {
                                  ruleCalculateTypeOptions =
                                      findDownInfo?.ruleCalculateTypeList.map(
                                          (ele: any, index: number) => (
                                              <Option value={ele.value} key={ele.value}>
                                                  {ele.name}
                                              </Option>
                                          ),
                                      ) || [];
                              }
                          }
                          return (
                              <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                                  <Select
                                      value={record.ruleCalculateType}
                                      disabled={disabled}
                                      placeholder="请选择"
                                      onChange={(newVal) => {
                                          updateItem(index, {
                                              ruleCalculateType: newVal,
                                          });
                                      }}
                                  >
                                      {ruleCalculateTypeOptions}
                                  </Select>
                              </Form.Item>
                          );
                      },
                  },
                  {
                      title: '指标值',
                      width: 500,
                      dataIndex: 'ruleValues',
                      render(text: string, record: RuleTableItemVo, index: number) {
                          let errMsg: string = '';
                          //   校验站点，运营商，站点接入类型使用ruleValues
                          if (
                              record?.ruleCode === 'stationId' ||
                              record?.ruleCode === 'buildId' ||
                              record?.ruleCode === 'stationAccessType'
                          ) {
                              errMsg = checkItemByKey(record, 'ruleValues');
                          } else {
                              errMsg = checkItemByKey(record, 'ruleValue');
                          }
                          return (
                              <Form.Item help={errMsg} validateStatus={(errMsg && 'error') || ''}>
                                  {ruleValueFun(record, index)}
                              </Form.Item>
                          );
                      },
                  },
              ]
            : []),

        {
            title: '操作 ',

            width: 120,
            render(text: string, record: RuleTableItemVo, index: number) {
                const minusBtn = (
                    <Typography.Link
                        onClick={() => {
                            removeEvent(index);
                        }}
                        type="danger"
                    >
                        删除
                    </Typography.Link>
                );

                const topBtn = (
                    <Typography.Link
                        onClick={() => {
                            topEvent(index);
                        }}
                    >
                        置顶
                    </Typography.Link>
                );

                const btnList = [minusBtn];
                if (index > 0) {
                    btnList.push(topBtn);
                }

                return <Space style={{ transform: 'translateY(-10px)' }}>{btnList}</Space>;
            },
        },
    ];
    return (
        <div>
            <p>
                <Button
                    icon={<PlusOutlined />}
                    type="primary"
                    onClick={() => {
                        addEvent();
                    }}
                >
                    添加规则
                </Button>
            </p>
            {tableList?.length > 0 && (
                <Table dataSource={tableList} columns={columns} pagination={false}></Table>
            )}
        </div>
    );
};

export default connect(({ global }) => ({
    global,
}))(RuleTableItem);
