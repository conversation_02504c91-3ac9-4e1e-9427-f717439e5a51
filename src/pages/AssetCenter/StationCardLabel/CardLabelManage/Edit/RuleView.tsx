import {
    Card,
    Typography,
    Form,
    Space,
    Button,
    Input,
    Radio,
    Alert,
    Select,
    InputNumber,
} from 'antd';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import RuleTableItem from './components/RuleTableItem';
import { saveStationScopeInfoApi, getStationScopeListApi } from '@/services/CommonApi';
import { STATION_CONFIG_PAGE_TYPES } from '@/config/declare';
import { isEmpty, renderTableDataIndexText } from '@/utils/utils';

import commonStyles from '@/assets/styles/common.less';
export enum RuleTypes {
    Rule = '01', //按规则获取站点
    Station = '02', //按固定站点获取
}
const RuleView = (props: any) => {
    const { stationRef, ConfigSubTypes } = props;
    return (
        <>
            <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                    prevValues.configSubType !== curValues.configSubType
                }
            >
                {({ getFieldValue, setFieldsValue }) => {
                    const configSubType = getFieldValue('configSubType');

                    return (
                        <Form.Item
                            label="标签规则"
                            name="configRuleType"
                            // initialValue={RuleTypes.Rule}
                            wrapperCol={{ span: 24 }}
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <Radio.Group>
                                {configSubType !== ConfigSubTypes.Virtually && (
                                    <Radio value={RuleTypes.Station}>固定站点</Radio>
                                )}
                                <Radio value={RuleTypes.Rule}>按规则获取站点</Radio>
                            </Radio.Group>
                        </Form.Item>
                    );
                }}
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(prevValues, curValues) => true}>
                {({ getFieldValue, setFieldsValue }) => {
                    const configRuleType = getFieldValue('configRuleType');
                    const configSubType = getFieldValue('configSubType');
                    const stationInfo = getFieldValue('stationInfo');
                    const { allStations = [] } = stationInfo ?? {};
                    const disabledDelIds = allStations
                        .filter((ele) => ele.equityFlag)
                        ?.map((ele) => ele.stationId);

                    return (
                        <>
                            {configSubType === ConfigSubTypes.Offline &&
                                configRuleType == RuleTypes.Rule && (
                                    <Alert
                                        message="规则之间为【且】关系"
                                        type="info"
                                        showIcon
                                        style={{ marginBottom: 15 }}
                                    ></Alert>
                                )}

                            {configRuleType == RuleTypes.Rule && (
                                <Form.Item
                                    label=""
                                    name={'detailList'}
                                    wrapperCol={{ span: 24 }}
                                    required
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (isEmpty(value)) {
                                                    return Promise.reject('请配置站点指标');
                                                }

                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={[{}]}
                                >
                                    <RuleTableItem
                                        configSubType={configSubType}
                                        ConfigSubTypes={ConfigSubTypes}
                                    ></RuleTableItem>
                                </Form.Item>
                            )}
                            {configRuleType == RuleTypes.Station &&
                                configSubType === ConfigSubTypes.Offline && (
                                    <Form.Item
                                        label=""
                                        name={'stationInfo'}
                                        wrapperCol={{ span: 24 }}
                                        required
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (isEmpty(value)) {
                                                        return Promise.reject('请配置指定场站');
                                                    }
                                                    const { addStations, allStations } = value;
                                                    if (
                                                        isEmpty(allStations) &&
                                                        isEmpty(addStations)
                                                    ) {
                                                        return Promise.reject('请配置指定场站');
                                                    }
                                                    if (
                                                        addStations.length > 20000 ||
                                                        allStations.length > 20000
                                                    ) {
                                                        return Promise.reject(
                                                            '一次性最多配置2万个站点',
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <SearchStationItem
                                            ref={stationRef}
                                            title="活动范围"
                                            deleteAllEnabled={false}
                                            disabledStationIds={disabledDelIds}
                                            showFirstTime={false}
                                            extendColumns={[
                                                {
                                                    title: '生效时间',
                                                    dataIndex: 'effTime',
                                                    width: '200px',
                                                    render(text: string) {
                                                        return renderTableDataIndexText({ text });
                                                    },
                                                },
                                                {
                                                    title: '过期时间',
                                                    dataIndex: 'expTime',
                                                    width: '200px',
                                                    render(text: string) {
                                                        return renderTableDataIndexText({ text });
                                                    },
                                                },
                                            ]}
                                            // requestInfo={
                                            //     actId &&
                                            //     !isEmpty(editActInfo) && {
                                            //         listApi: getStationScopeListApi,
                                            //         params: {
                                            //             scopeRelateId: actId,
                                            //             scopeRelateType:
                                            //                 STATION_CONFIG_PAGE_TYPES.COUPON_1,
                                            //         },

                                            //         recordParams: {
                                            //             relateId: cpnId,
                                            //             scene: 'stc_coupon',
                                            //         },
                                            //     }
                                            // }
                                        ></SearchStationItem>
                                    </Form.Item>
                                )}
                        </>
                    );
                }}
            </Form.Item>
        </>
    );
};

export default RuleView;
