import React, { useEffect, useRef } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Typography, Form, Space, Button, Input, Select, message, Radio, Tag } from 'antd';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';

import { useHistory, useLocation, connect } from 'umi';
import { LeftOutlined, TagFilled } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import commonStyles from '@/assets/styles/common.less';
import RuleView, { RuleTypes } from './RuleView';
import {
    saveStationRecommendApi,
    getStationRecommendByIdApi,
} from '@/services/AssetCenter/RuleManageApi';

const { Option } = Select;

export enum ConfigSubTypes {
    Offline = '0201', // 离线标签
    Virtually = '0202', // 虚拟标签
}

export const LabelStyleOptions = [
    {
        label: '标签样式1',
        value: 'style1',
        background: 'linear-gradient(90deg, #EDBF85, #E29D45)',
    },
    {
        label: '标签样式2',
        value: 'style2',
        background: 'linear-gradient(90deg, #FF9703, #FF4409)',
    },
    {
        label: '标签样式3',
        value: 'style3',
        background: 'linear-gradient(90deg, #0A1641, #231F20)',
    },
    {
        label: '标签样式4',
        value: 'style4',
        background: 'linear-gradient(90deg, #C0F10F, #62D000)',
    },
    {
        label: '标签样式5',
        value: 'style5',
        background: 'linear-gradient(90deg, #FDAB52, #FF8400)',
    },
];
export const TagRender = (props: any) => {
    const { label, value, background } = props;
    return (
        <Radio style={{ marginBottom: '10px' }} value={value}>
            <div
                style={{
                    background: background,
                    color: '#fff',
                    padding: '2px 20px',
                    borderRadius: '10px 0 10px 0',
                }}
            >
                {label}
            </div>
        </Radio>
    );
};

const DiagnosisPage: React.FC<any> = (props) => {
    const { route } = props;
    const [form] = Form.useForm();
    const history = useHistory();
    const location = useLocation();

    const { configId } = location?.query ?? {};

    const stationRef = useRef();

    useEffect(() => {
        if (configId) {
            console.log(3232, configId);
            run();
        }
    }, [configId]);

    const {
        run,
        data: configInfo,
        loading,
    } = useRequest(
        async () => {
            try {
                const { data } = await getStationRecommendByIdApi({ configId });
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess(res) {
                setTimeout(() => {
                    stationRef?.current?.setAllStations(configInfo?.detailList);
                }, 500);
            },
        },
    );

    useEffect(() => {
        if (configInfo) {
            const params = {
                ...configInfo,
            };
            if (params.ruleType === RuleTypes.Station) {
                params.stationInfo = {
                    allStations: params.detailList,
                };

                delete params.detailList;
            } else {
                params.detailList = (params?.detailList || []).map((item) => {
                    // 站点
                    if (item.ruleCode === 'stationId') {
                        item.ruleValues = item.stationRuleScopeVos || [];
                    }
                    // 运营商
                    if (item.ruleCode === 'buildId') {
                        item.ruleValues = item.stationRuleScopeVos || [];
                    }

                    return item;
                });
            }
            form.setFieldsValue(params);
        }
    }, [configInfo]);

    const {
        run: finishEvent,

        loading: submitLoading,
    } = useRequest(
        async (values = {}) => {
            try {
                const options = JSON.parse(
                    JSON.stringify({
                        ...values,
                        configType: '02', // 规则类型 01推荐 02标签
                    }),
                );
                const { detailList = [] } = options;

                if (configId) {
                    options.configId = configId;
                }
                if (options.configRuleType == RuleTypes.Station) {
                    const { allStations } = options.stationInfo;

                    options.detailList = allStations
                        ?.filter((ele) => !ele.equityFlag)
                        ?.map((ele) => {
                            return {
                                ruleValue: ele.stationId,
                            };
                        });
                } else {
                    let errorMsg = false;
                    // 离线标签校验
                    if (options.configSubType === ConfigSubTypes.Offline) {
                        options.detailList = detailList.map((item) => {
                            // 站点
                            if (item.ruleCode === 'stationId') {
                                const newData = item.ruleValues || [];
                                item.ruleValues = newData.map((ele) => ele.stationId);
                            }
                            // 运营商
                            if (item.ruleCode === 'buildId') {
                                const newData = item.ruleValues || [];
                                item.ruleValues = newData.map((ele) => ele.operId);
                            }
                            // 列表规则校验
                            if (
                                item?.ruleCode &&
                                item?.ruleValueType &&
                                item?.ruleCalculateType &&
                                (item?.ruleValue || item?.ruleValues)
                            ) {
                                if (
                                    item?.ruleCode === 'stationId' ||
                                    item?.ruleCode === 'buildId' ||
                                    item?.ruleCode === 'stationAccessType'
                                ) {
                                    if (item?.ruleValueType === '02') {
                                        // 运算符为等于不等于
                                        if (
                                            item?.ruleCalculateType === '05' ||
                                            item?.ruleCalculateType === '06'
                                        ) {
                                            const newDataArray = item?.ruleValues || [];
                                            // 指标值不允许超过1个
                                            if (newDataArray.length > 1) {
                                                errorMsg = true;
                                            }
                                        }
                                    }
                                }
                            } else {
                                errorMsg = true;
                            }

                            return item;
                        });
                    }
                    // 虚拟标签校验
                    if (options.configSubType === ConfigSubTypes.Virtually) {
                        options.detailList = detailList.map((item) => {
                            // 列表规则校验
                            if (!item?.ruleCode) {
                                errorMsg = true;
                            }
                            return item;
                        });
                    }

                    if (errorMsg) {
                        message.error('提交失败,请先检查错误提示信息');
                        return;
                    }
                }
                delete options.stationInfo;
                console.log(options);
                await saveStationRecommendApi(options);
                message.success('保存成功');
                goBack();
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    const goBack = () => {
        history.replace('/assetCenter/stationCardLabel/manage');
    };

    return (
        <PageHeaderWrapper
            title={
                <div className="page-title" onClick={goBack}>
                    <LeftOutlined />
                    {route.name}
                </div>
            }
        >
            <Card loading={loading}>
                <Form
                    form={form}
                    onFinish={finishEvent}
                    // labelCol={{ flex: '0 0 140px' }}
                    wrapperCol={{ span: 8 }}
                >
                    {/* <div className={commonStyles['form-title']}>基础信息</div> */}
                    <Form.Item
                        label="标签名称"
                        name="configName"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="请填写"
                            autoComplete="off"
                            showCount
                            maxLength={20}
                        ></Input>
                    </Form.Item>
                    <Form.Item
                        label="标签展示文案"
                        name="configCopywriting"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Input
                            placeholder="请填写"
                            autoComplete="off"
                            showCount
                            maxLength={5}
                        ></Input>
                    </Form.Item>
                    <Form.Item
                        label="标签类型"
                        name="configSubType"
                        initialValue={ConfigSubTypes.Offline}
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Radio.Group
                            onChange={() => {
                                form.setFieldValue('detailList', []);
                            }}
                        >
                            <Radio value={ConfigSubTypes.Offline}>离线标签</Radio>
                            <Radio value={ConfigSubTypes.Virtually}>虚拟标签</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        label="标签样式"
                        name="configStyle"
                        rules={[{ required: true, message: '请填写' }]}
                        wrapperCol={{ span: 24 }}
                    >
                        <Radio.Group>
                            {LabelStyleOptions?.map((item, index) => (
                                <TagRender key={index} {...item} />
                            ))}
                        </Radio.Group>
                    </Form.Item>

                    <RuleView stationRef={stationRef} ConfigSubTypes={ConfigSubTypes}></RuleView>

                    <Space
                        align="end"
                        className="mg-t-20"
                        style={{ display: 'flex', justifyContent: 'center' }}
                    >
                        <Button type="primary" htmlType="submit" loading={submitLoading}>
                            提交
                        </Button>
                        <Button onClick={goBack}>取消</Button>
                    </Space>
                </Form>
            </Card>
        </PageHeaderWrapper>
    );
};

export default DiagnosisPage;
