// 站点卡片标签管理列表
import {
    Card,
    Tabs,
    Popover,
    Space,
    Tooltip,
    Typography,
    Button,
    Col,
    Form,
    Select,
    Modal,
    Input,
    Switch,
    Popconfirm,
    Row,
    Alert,
    Spin,
    Radio,
} from 'antd';
import { connect, useHistory } from 'umi';
import { Fragment, useState, useRef, useEffect } from 'react';
import SearchView from '@/components/SearchOptionsBar/SearchView';
import CityTransferModal from '@/components/CityTransferModal';
import { IMG_URL } from '@/config/global';

// import AllStationSelect from '@/components/AllStationSelect';

// import CitysSelect from '@/components/CitysSelect/index.js';
// import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';

import { isEmpty } from '@/utils/utils';
import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    getStationRecommendListApi,
    updateStationRecommendStatusApi,
    sortStationRecommendApi,
    saveStationRecommendScopeApi,
} from '@/services/AssetCenter/RuleManageApi';
import { useRequest } from 'ahooks';
import { LabelStyleOptions } from '../Edit/index';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const ManagePage = (props: any) => {
    const [searchForm] = Form.useForm();
    const history = useHistory();

    const cityModalRef = useRef();

    const [tabType, updateTabType] = useState('00');

    const [selectCityItem, updateSelectCityItem] = useState();
    const [showPreviewData, toggleShowPreviewData] = useState<boolean>(false);
    const [previewData, updatePreviewData] = useState<any>();
    const [topUPShowPreview, setTopUPShowPreview] = useState<any>(false);
    const [disabled, setDisabled] = useState<any>(false);

    const showPreview = (record: any) => {
        toggleShowPreviewData(true);
        updatePreviewData(record);
    };

    const columns = [
        {
            title: '标签名称',
            width: 160,
            dataIndex: 'configName',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '标签',
            width: 160,
            dataIndex: 'configCopywriting',
            render(text: string, record: any) {
                const tag: any = LabelStyleOptions?.filter(
                    (item) => item?.value === record?.configStyle,
                );
                return (
                    <div
                        style={{
                            background: tag?.[0]?.background,
                            color: '#fff',
                            borderRadius: '10px 0 10px 0',
                            width: '112px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: 12,
                            height: 22,
                        }}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '标签规则',
            width: 240,
            dataIndex: 'configDetailDesc',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '可见城市',
            width: 140,
            dataIndex: 'scopeDesc',
            render(text: string, record: any) {
                return (
                    <Typography.Link
                        onClick={() => {
                            updatePreviewData(record);
                            setDisabled(true);
                            cityModalRef?.current?.show({
                                defaultKeys: record.cityList,
                                disabled: true,
                                otherParams: {
                                    scopeType: record.scopeType,
                                    visibleLocation: record?.visibleLocation,
                                },
                            });
                        }}
                    >
                        {record.scopeDesc}
                    </Typography.Link>
                );
            },
        },
        {
            title: '可见位置',
            width: 200,
            dataIndex: 'visibleLocationName',
            render(text: string, record: any) {
                return (
                    <Typography.Link
                        onClick={() => {
                            updatePreviewData(record);
                            setDisabled(true);
                            cityModalRef?.current?.show({
                                defaultKeys: record.cityList,
                                disabled: true,
                                otherParams: {
                                    scopeType: record.scopeType,
                                    visibleLocation: record?.visibleLocation,
                                },
                            });
                        }}
                    >
                        {text}
                    </Typography.Link>
                );
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createdTimeStr',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            width: 120,
            dataIndex: 'createdBy',
            render(text: string, record: any) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '状态',
            width: 140,
            dataIndex: 'configStatus',
            render(text: string, record: any) {
                return (
                    <Switch
                        loading={statusLoading}
                        checkedChildren="启用"
                        unCheckedChildren="关闭"
                        checked={record.configStatus == '01'}
                        onChange={(newVal) => {
                            console.log(newVal);
                            const params = {
                                configId: record.configId,
                            };
                            if (newVal) {
                                params.configStatus = '01';
                            } else {
                                params.configStatus = '02';
                            }
                            updateStatus(params);
                        }}
                    ></Switch>
                );
            },
        },
        {
            title: '操作',
            width: 200,
            fixed: 'right',
            render(text: string, record: any) {
                const editBtn = (
                    <Typography.Link
                        onClick={() => {
                            history.push(
                                `/assetCenter/stationCardLabel/manage/edit?configId=${record.configId}`,
                            );
                        }}
                    >
                        编辑
                    </Typography.Link>
                );
                const rangeBtn = (
                    <Typography.Link
                        onClick={() => {
                            updateSelectCityItem(record);
                            updatePreviewData(record);
                            cityModalRef?.current?.show({
                                defaultKeys: record.cityList,
                                otherParams: {
                                    scopeType: record.scopeType,
                                    visibleLocation: record.visibleLocation,
                                },
                            });
                        }}
                    >
                        可见范围
                    </Typography.Link>
                );

                const delBtn = (
                    <Typography.Link
                        onClick={() => {
                            showPreview(record);
                        }}
                    >
                        预览
                    </Typography.Link>
                );

                const btnList = [editBtn, rangeBtn, delBtn];

                return <Space> {btnList}</Space>;
            },
        },
    ];
    const searchRef = useRef();

    const initTableEvent = async (params: API.QueryThresholdListRequest) => {
        try {
            const options = {
                ...params,
                configType: '02',
            };

            return getStationRecommendListApi(options);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const { run: updateStatus, loading: statusLoading } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await updateStationRecommendStatusApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
            },
        },
    );

    const { run: sortList } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await sortStationRecommendApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
            },
        },
    );

    const { run: scopedEvent } = useRequest(
        async (params = {}) => {
            try {
                const options = {
                    ...params,
                };
                await saveStationRecommendScopeApi(options);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            onSuccess() {
                searchRef?.current?.reload();
                updateSelectCityItem(undefined);
            },
        },
    );

    useEffect(() => {
        if (tabType) {
            searchRef?.current?.search();
        }
    }, [tabType]);

    return (
        <PageHeaderWrapper>
            <Card>
                <Alert
                    message="站点卡片标签将会在所有包含【站点卡片】的页面生效，规则变更时将在下一个整小时生效"
                    type="info"
                    showIcon
                    style={{ marginBottom: 15 }}
                />
                <SearchView
                    rowKey="index"
                    ref={searchRef}
                    form={searchForm}
                    requestApi={initTableEvent}
                    requestParams={{ configStatus: tabType == '00' ? undefined : tabType }}
                    columns={columns}
                    toolsBar={
                        <Fragment>
                            <p>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        history.push('/assetCenter/stationCardLabel/manage/add');
                                    }}
                                >
                                    新增站点卡片标签
                                </Button>
                            </p>
                            <Tabs activeKey={tabType} onChange={(v) => updateTabType(v)}>
                                <Tabs.TabPane tab="全部" key="00"></Tabs.TabPane>
                                <Tabs.TabPane tab="启动" key="01"></Tabs.TabPane>

                                <Tabs.TabPane tab="关闭" key="02"></Tabs.TabPane>
                            </Tabs>
                        </Fragment>
                    }
                    isDrag
                    hideSearch
                    setDataSource={(newitem, newIndex, oldIndex) => {
                        // console.log(
                        //     3213,
                        //     newitem,
                        //     newIndex,
                        //     oldIndex,
                        //     searchRef?.current?.getList(),
                        // );
                        const list = newitem?.map((item: any) => {
                            return {
                                configId: item.configId,
                                configSn: item.configSn,
                            };
                        });
                        const params = {
                            sortList: list,
                        };
                        sortList(params);
                    }}
                ></SearchView>
            </Card>

            <CityTransferModal
                title="可见范围"
                ref={cityModalRef}
                onFinish={(cityList: any, otherParams: any) => {
                    scopedEvent({
                        configId: selectCityItem?.configId,
                        scopeType: otherParams?.scopeType,
                        scopeList: cityList?.map((ele) => {
                            return {
                                city: ele.areaCode,
                                cityName: ele.areaName,
                            };
                        }),
                        visibleLocation: otherParams?.visibleLocation,
                    });
                }}
                extraFormItem={
                    <Form.Item
                        label="展示位置"
                        name="visibleLocation"
                        rules={[{ required: true, message: '请选择' }]}
                        wrapperCol={{ span: 24 }}
                        style={{ marginTop: 20 }}
                    >
                        <Radio.Group disabled={disabled}>
                            <Radio value={'0'}>所有【站点卡片】</Radio>
                            <Radio value={'1'} style={{ position: 'relative' }}>
                                <Space size={40}>
                                    <span>仅【综合排序】置顶的站点卡片</span>
                                    <Typography.Link
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toggleShowPreviewData(true);
                                            setTopUPShowPreview(true);
                                        }}
                                    >
                                        查看
                                    </Typography.Link>
                                </Space>
                                <div style={{ position: 'absolute', bottom: -22, left: 23 }}>
                                    <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                                        当【综合排序】无置顶时不展示
                                    </Typography.Text>
                                </div>
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                }
                zIndex={100}
                deleteEnabled={false}
                showAllRadio
            />
            <Modal
                width={425}
                title={topUPShowPreview ? '查看' : '预览'}
                visible={showPreviewData}
                onCancel={() => {
                    toggleShowPreviewData(false);
                    setTopUPShowPreview(false);
                }}
                destroyOnClose
                footer={false}
                bodyStyle={{ background: '#efefef' }}
            >
                <div style={{ position: 'relative' }}>
                    <div style={{ width: '100%', position: 'absolute', top: 0 }}>
                        <img
                            src={`${IMG_URL}/static/images/station/${previewData?.configStyle}.png`}
                            style={{
                                width: topUPShowPreview ? '90%' : '100%',
                                height: 22,
                                position: 'absolute',
                                top: topUPShowPreview ? 115 : 0,
                                left: topUPShowPreview ? 20 : 0,
                            }}
                        />
                        <div
                            style={{
                                width: '112px',
                                position: 'absolute',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: '#fff',
                                fontSize: 12,
                                top: topUPShowPreview ? 115 : 0,
                                height: 22,
                            }}
                        >
                            {previewData?.configCopywriting}
                        </div>
                    </div>
                    {topUPShowPreview ? (
                        <img
                            src={`${IMG_URL}/static/images/station/stationTopUpCard.jpg`}
                            style={{ width: '100%' }}
                        />
                    ) : (
                        <img
                            src={`${IMG_URL}/static/images/station/stationCard.png`}
                            style={{ width: '100%' }}
                        />
                    )}
                </div>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default ManagePage;
