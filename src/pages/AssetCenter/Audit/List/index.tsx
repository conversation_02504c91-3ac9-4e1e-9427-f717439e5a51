import { <PERSON><PERSON><PERSON>er<PERSON>rapper } from '@ant-design/pro-layout';
import { Card, Col, Form, Row, Tabs } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { usePagination } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';
import { connect, history, Link } from 'umi';

import TablePro from '@/components/TablePro';
import SearchLayout from './components/SearchLayout';
import { getApproveList } from '@/services/MngAstApi';
import { AUDIT_STATUS_TABS } from '@/constants/station';

const ListPage: React.FC<any> = (props) => {
    const { dispatch, global } = props;
    const { pageInit } = global;
    const [activeStatus, setActiveStatus] = useState<string>(AUDIT_STATUS_TABS[0].value);
    const {
        location: { pathname },
    } = history;
    const [form] = Form.useForm();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, formData?: any) => {
            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: [pagination, formData, activeStatus],
            });
            const params = processParams(formData);
            const response = await getApproveList({
                ...params,
                status: activeStatus !== '0' ? activeStatus : undefined,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            onError: () => {},
            cacheKey: '/assetCenter/audit/list',
            refreshDeps: [pageInit[pathname]?.form],
        },
    );

    const processParams = (formData: any): any => {
        const params = {
            ...formData,
            promoterStartDate:
                (formData?.promoterDates &&
                    formData?.promoterDates[0] &&
                    formData?.promoterDates[0]?.format('YYYY-MM-DD 00:00:00')) ||
                undefined,
            promoterEndDate:
                (formData?.promoterDates &&
                    formData?.promoterDates[1] &&
                    formData?.promoterDates[1]?.format('YYYY-MM-DD 23:59:59')) ||
                undefined,
            promoterDates: undefined,
            approvedByStartDate:
                (formData?.approvedDates &&
                    formData?.approvedDates[0] &&
                    formData?.approvedDates[0]?.format('YYYY-MM-DD 00:00:00')) ||
                undefined,
            approvedByEndDate:
                (formData?.approvedDates &&
                    formData?.approvedDates[1] &&
                    formData?.approvedDates[1]?.format('YYYY-MM-DD 23:59:59')) ||
                undefined,
            approvedDates: undefined,
        };
        return params;
    };

    const onFinish = (formData?: API.QrCodeTemplateRequest) => {
        searchList({ pageSize: pagination.pageSize, current: 1 }, formData);
    };
    const onReset = () => {
        form.resetFields();
        onFinish();
    };

    const stationRef = useRef();
    useEffect(() => {
        if (pageInit[pathname]) {
            const para = pageInit[pathname];
            searchList(para[0] as any, para[1]);
            const { stationId, stationName } = para[1] || {};
            if (stationId && stationName) {
                const ref: any = stationRef.current;
                ref?.init?.([
                    {
                        stationName: stationName,
                        stationId: stationId,
                    },
                ]);
            }
            form.setFieldsValue({ ...para[1] });
            setActiveStatus(para[2] || AUDIT_STATUS_TABS[0].value);
        } else {
            searchList({ pageSize: pagination.pageSize, current: 1 });
        }
    }, []);

    useEffect(() => {
        onFinish(form?.getFieldsValue(true));
    }, [activeStatus]);

    const columns: ColumnsType<any> = [
        {
            title: '运营商',
            width: 120,
            dataIndex: 'operatorName',
        },
        {
            title: '场站',
            width: 120,
            dataIndex: 'stationNum',
            render: (value: string) => {
                return value ? `${value}个场站` : '-';
            },
        },
        {
            title: '操作类型',
            width: 120,
            dataIndex: 'operateTypeName',
        },
        {
            title: '发起人',
            width: 120,
            dataIndex: 'promoterName',
        },
        {
            title: '发起时间',
            width: 120,
            dataIndex: 'promoterDate',
        },
        {
            title: '审批状态',
            width: 120,
            dataIndex: 'statusName',
        },
        {
            title: '审批时间',
            width: 120,
            dataIndex: 'approvedByTime',
        },
        {
            title: '操作',
            width: 140,
            dataIndex: 'id',
            fixed: 'right',
            render: (value: string, record: API.ApproveVo) => {
                const lookBtn = <Link to={`/assetCenter/audit/list/detail/${value}`}>查看</Link>;
                const jumpBtn = (
                    <a
                        onClick={() => {
                            window.location.href = `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(
                                record.url || '',
                            )}&pc_slide=true`;
                        }}
                    >
                        审批进度
                    </a>
                );
                const btns = [lookBtn];
                if (record.url?.length) {
                    btns.push(jumpBtn);
                }
                return (
                    <Row gutter={12}>
                        {btns.map((ele, index) => (
                            <Col key={index}>{ele}</Col>
                        ))}
                    </Row>
                );
            },
        },
    ];

    return (
        <PageHeaderWrapper>
            <Card>
                <Form form={form} onFinish={onFinish}>
                    <SearchLayout form={form} resetForm={onReset} stationRef={stationRef} />
                </Form>
                <Tabs
                    onChange={(v) => {
                        setActiveStatus(v);
                    }}
                    activeKey={activeStatus}
                >
                    {AUDIT_STATUS_TABS?.map((v) => (
                        <Tabs.TabPane key={v?.value} tabKey={v?.value} tab={v?.label} />
                    ))}
                </Tabs>
                <TablePro
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey="id"
                    dataSource={listData?.list}
                    columns={columns}
                    offsetHeader={0}
                    onChange={(pages: any) => {
                        pagination.onChange(pages?.current, pages?.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        total: pagination.total,
                        pageSize: pagination.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total: number) => `共 ${total || 0} 条`,
                    }}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ global }: any) => ({
    global,
}))(ListPage);
