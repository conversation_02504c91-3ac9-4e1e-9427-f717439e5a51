import React from 'react';
import { Col, DatePicker, Form, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';
import SystemAccountSelect from '@/components/SystemAccountSelect';
import { AuditOperOptions } from '@/constants/station';
import { connect } from 'umi';

const { RangePicker } = DatePicker;

const SearchLayout: React.FC<{
    form: FormInstance;
    resetForm?: () => void;
    listLoading?: boolean;
    currentUser?: any;
    stationRef: any;
}> = (props) => {
    const { resetForm, form, listLoading, currentUser, stationRef } = props;
    const operId = Form.useWatch('operatorId', form);
    return (
        <SearchOptionsBar loading={listLoading} onReset={resetForm} minSpan={24 * 2}>
            <Col span={8}>
                <OperSelectItem
                    form={form}
                    name="operatorId"
                    label="运营商"
                    formItemLayout={{ labelAlign: 'right' }}
                />
            </Col>
            <Col span={8}>
                <AllStationSelect
                    form={form}
                    label="场站名称"
                    name="stationId"
                    operId={operId}
                    ref={stationRef}
                />
            </Col>
            <Col span={8}>
                <Form.Item label="操作类型" name="operatorType">
                    <Select options={AuditOperOptions} allowClear />
                </Form.Item>
            </Col>
            {currentUser?.name == 'SYSADMIN' && (
                <Col span={8}>
                    <SystemAccountSelect label="发起人" fieldName="promoter" />
                </Col>
            )}
            <Col span={8}>
                <Form.Item label="发起时间" name="promoterDates">
                    <RangePicker format="YYYY-MM-DD" allowClear />
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item label="审批时间" name="approvedDates">
                    <RangePicker format="YYYY-MM-DD" allowClear />
                </Form.Item>
            </Col>
        </SearchOptionsBar>
    );
};

export default connect(({ global, user }: any) => ({
    global,
    user,
    currentUser: user.currentUser,
}))(SearchLayout);
