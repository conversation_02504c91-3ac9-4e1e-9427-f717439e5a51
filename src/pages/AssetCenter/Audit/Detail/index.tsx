import { LeftOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { useRequest } from 'ahooks';
import { Button, Card, Descriptions, Space, Typography } from 'antd';
import { useEffect, useMemo, useRef } from 'react';
import { history, useRouteMatch } from 'umi';

import { getApproveList } from '@/services/MngAstApi';
import { AUDIT_OPER_TYPE } from '@/constants/station';
import SearchStationItem from '@/components/OperStationSearchList/SearchList/SearchStationItem';
import commonStyles from '@/assets/styles/common.less';

const DetailPage = () => {
    const { params } = useRouteMatch();
    const { id } = params as any;
    const stationRef = useRef<any>();
    const {
        run: queryDetail,
        data: detailData,
        loading: detailLoading,
    } = useRequest(
        (id) => {
            return getApproveList({
                approveId: id,
                pageIndex: 1,
                pageSize: 1,
            });
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (id) {
            queryDetail(id);
        }
    }, [id]);

    const approveData: API.ApproveVo | undefined = useMemo(() => {
        return detailData?.data?.records?.[0];
    }, [detailData?.data]);

    useEffect(() => {
        if (approveData) {
            stationRef?.current?.setAllStations(
                approveData?.stationList?.map((v) => {
                    return {
                        buildId: v.operatorId,
                        buildName: approveData?.operatorName,
                        city: v.city,
                        cityName: v.cityName,
                        stationId: v.stationId,
                        stationName: v.stationName,
                    };
                }),
            );
        } else {
            stationRef?.current?.reset();
        }
    }, [approveData]);

    return (
        <PageHeaderWrapper
            title={
                <Space
                    size={2}
                    align="baseline"
                    onClick={() => {
                        history.goBack();
                    }}
                    style={{ cursor: 'pointer' }}
                >
                    <LeftOutlined title="返回" />
                    <Typography.Title level={4}>
                        {approveData
                            ? `${approveData?.operatorName}-${approveData?.stationNum}个场站-${approveData?.operateTypeName}`
                            : '审批详情'}
                    </Typography.Title>
                </Space>
            }
        >
            <Card loading={detailLoading}>
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginRight: '16px',
                    }}
                >
                    <div className={commonStyles['form-title']}>基础信息</div>
                    {(approveData?.url?.length && (
                        <Button
                            onClick={() => {
                                window.location.href = `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(
                                    approveData?.url || '',
                                )}&pc_slide=true`;
                            }}
                        >
                            审批进度
                        </Button>
                    )) ||
                        null}
                </div>
                <Descriptions>
                    <Descriptions.Item label="运营商">
                        {approveData?.operatorName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="操作类型">
                        {approveData?.operateTypeName || '-'}
                    </Descriptions.Item>
                    {approveData?.operateType === AUDIT_OPER_TYPE.ABANDON && (
                        <Descriptions.Item label="废弃原因">
                            {approveData?.operateDesc || '-'}
                        </Descriptions.Item>
                    )}
                    {approveData?.operateType === AUDIT_OPER_TYPE.OFFLINE && (
                        <Descriptions.Item label="下线原因">
                            {approveData?.operateDesc || '-'}
                        </Descriptions.Item>
                    )}
                    <Descriptions.Item label="发起人">
                        {(approveData?.promoterName &&
                            `${approveData?.promoterName}（${approveData?.promoter}）`) ||
                            approveData?.promoter ||
                            '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="发起时间">
                        {approveData?.promoterDate || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="审批状态">
                        {approveData?.statusName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="审批时间">
                        {approveData?.approvedByTime || '-'}
                    </Descriptions.Item>
                </Descriptions>
                <div className="mg-t-20"></div>
                <div className={commonStyles['form-title']}>涉及场站</div>
                <SearchStationItem
                    title="活动范围"
                    disabled
                    hasStastics
                    ref={stationRef}
                    limitOperIds={[approveData?.operatorId]}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default DetailPage;
