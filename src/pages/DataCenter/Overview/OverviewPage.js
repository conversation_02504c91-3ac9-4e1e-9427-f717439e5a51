import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Card,
    Col,
    // message,
    // Icon,
    Modal,
    Row,
    // Alert,
    // Divider,
    Space,
    Spin,
    Radio,
    DatePicker,
    Tabs,
} from 'antd';
import { connect } from 'umi';
import React, { Fragment, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import Trend from '@/components/Trend';

import styles from '@/assets/styles/common.less';
import Yuan from '@/utils/Yuan';
import classnames from 'classnames';
import pageStyles from './OverviewPage.less';
import ChartCard from '@/components/Charts/ChartCard';
import BarChart from '@/components/Charts/Bar/stack';
import PieChart from '@/components/Charts/Pie';
import OverviewLine from '@/components/Charts/Line/OverviewLine';

const { TabPane } = Tabs;
const { MonthPicker } = DatePicker;

const { confirm } = Modal;

const formItemLayout = {};

const DATATYPES = {
    ELE: '1',
    PRICE: '2',
    PEOPLE: '3',
    GUN: '4',
};

const topColResponsiveProps = {
    xs: 24,
    sm: 12,
    md: 12,
    lg: 12,
    xl: 6,
};

const formatDataName = (type) => {
    let title = '';
    switch (type) {
        case DATATYPES.ELE:
            title = '充电量';
            break;
        case DATATYPES.PRICE:
            title = '充电金额';
            break;
        case DATATYPES.PEOPLE:
            title = '充电人数';
            break;
        case DATATYPES.GUN:
            title = '平均单枪充电量';
            break;

        default:
            break;
    }
    return title;
};

const getDataUnit = (type) => {
    let unit = '';
    switch (type) {
        case DATATYPES.ELE:
            unit = '度';
            break;
        case DATATYPES.PRICE:
            unit = '元';
            break;
        case DATATYPES.PEOPLE:
            unit = '人';
            break;
        case DATATYPES.GUN:
            unit = '度';
            break;

        default:
            break;
    }
    return unit;
};

// 数据类型  1电量 2金额 3人数 4单枪电量
const IntroduceRow = ({ loading, info = {}, dataType = DATATYPES.ELE, changeDataType }) => {
    const changeDataTypeEvent = (type) => {
        changeDataType(type);
    };

    const formatRate = (rate) => {
        const rateStr = String(rate || 0);
        return rateStr.replace(/-/g, '');
    };
    return (
        <Card
            bordered={false}
            title={<span className={pageStyles.rankingTitle}>昨日运营状况</span>}
            bodyStyle={{ padding: 0 }}
            // style={{
            //     marginBottom: 24,
            // }}
        >
            <Row type="flex">
                <Col {...topColResponsiveProps}>
                    <ChartCard
                        bordered={false}
                        title={formatDataName(DATATYPES.ELE)}
                        // action={
                        //     <Tooltip
                        //         title={
                        //             <FormattedMessage
                        //                 id="datacenteranddashboardanalysis.analysis.introduce"
                        //                 defaultMessage="Introduce"
                        //             />
                        //         }
                        //     >
                        //         <InfoCircleOutlined />
                        //     </Tooltip>
                        // }
                        total={() => [
                            <Yuan key="yuan">{info.chargePq || 0}</Yuan>,
                            <span key="unit" className={pageStyles['chart-unit']}>
                                度
                            </span>,
                        ]}
                        footer={false}
                        contentHeight={46}
                        isTouch
                        isLight={dataType === DATATYPES.ELE}
                        onClick={() => {
                            changeDataTypeEvent(DATATYPES.ELE);
                        }}
                    >
                        <Trend
                            flag={info.pqDayRate > 0 ? 'up' : info.pqDayRate < 0 ? 'down' : ''}
                            style={{
                                marginRight: 16,
                            }}
                        >
                            较前日
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.pqDayRate > 0 ? 'up' : info.pqDayRate < 0 ? 'down' : '',
                                )}
                            >
                                {formatRate(info.pqDayRate)}%
                            </span>
                        </Trend>
                        <Trend
                            flag={info.pqWeekRate > 0 ? 'up' : info.pqWeekRate < 0 ? 'down' : ''}
                        >
                            较上周
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.pqWeekRate > 0 ? 'up' : info.pqWeekRate < 0 ? 'down' : '',
                                )}
                            >
                                {formatRate(info.pqWeekRate)}%
                            </span>
                        </Trend>
                    </ChartCard>
                </Col>
                <Col {...topColResponsiveProps}>
                    <ChartCard
                        bordered={false}
                        title={formatDataName(DATATYPES.PRICE)}
                        // action={
                        //     <Tooltip
                        //         title={
                        //             <FormattedMessage
                        //                 id="datacenteranddashboardanalysis.analysis.introduce"
                        //                 defaultMessage="Introduce"
                        //             />
                        //         }
                        //     >
                        //         <InfoCircleOutlined />
                        //     </Tooltip>
                        // }
                        total={() => (
                            <span>
                                <Yuan>{info.chargeAmt || 0}</Yuan>
                                <span className={pageStyles['chart-unit']}>元</span>
                            </span>
                        )}
                        footer={false}
                        contentHeight={46}
                        isTouch
                        isLight={dataType === DATATYPES.PRICE}
                        onClick={() => {
                            changeDataTypeEvent(DATATYPES.PRICE);
                        }}
                    >
                        <Trend
                            flag={info.amtDayRate > 0 ? 'up' : info.amtDayRate < 0 ? 'down' : ''}
                            style={{
                                marginRight: 16,
                            }}
                        >
                            较前日
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.amtDayRate > 0 ? 'up' : info.amtDayRate < 0 ? 'down' : '',
                                )}
                            >
                                {formatRate(info.amtDayRate)}%
                            </span>
                        </Trend>
                        <Trend
                            flag={info.amtWeekRate > 0 ? 'up' : info.amtWeekRate < 0 ? 'down' : ''}
                        >
                            较上周
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.amtWeekRate > 0
                                        ? 'up'
                                        : info.amtWeekRate < 0
                                        ? 'down'
                                        : '',
                                )}
                            >
                                {formatRate(info.amtWeekRate)}%
                            </span>
                        </Trend>
                    </ChartCard>
                </Col>
                <Col {...topColResponsiveProps}>
                    <ChartCard
                        bordered={false}
                        title={formatDataName(DATATYPES.PEOPLE)}
                        // action={
                        //     <Tooltip
                        //         title={
                        //             <FormattedMessage
                        //                 id="datacenteranddashboardanalysis.analysis.introduce"
                        //                 defaultMessage="Introduce"
                        //             />
                        //         }
                        //     >
                        //         <InfoCircleOutlined />
                        //     </Tooltip>
                        // }
                        total={() => [
                            <span key="amt">{info.chargeCustAmt || 0}</span>,
                            <span key="amt-unit" className={pageStyles['chart-unit']}>
                                人
                            </span>,
                        ]}
                        footer={false}
                        contentHeight={46}
                        isTouch
                        isLight={dataType === DATATYPES.PEOPLE}
                        onClick={() => {
                            changeDataTypeEvent(DATATYPES.PEOPLE);
                        }}
                    >
                        <Trend
                            flag={
                                info.custNumDayRate > 0
                                    ? 'up'
                                    : info.custNumDayRate < 0
                                    ? 'down'
                                    : ''
                            }
                            style={{
                                marginRight: 16,
                            }}
                        >
                            较前日
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.custNumDayRate > 0
                                        ? 'up'
                                        : info.custNumDayRate < 0
                                        ? 'down'
                                        : '',
                                )}
                            >
                                {formatRate(info.custNumDayRate)}%
                            </span>
                        </Trend>
                        <Trend
                            flag={
                                info.custNumWeekRate > 0
                                    ? 'up'
                                    : info.custNumWeekRate < 0
                                    ? 'down'
                                    : ''
                            }
                        >
                            较上周
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.custNumWeekRate > 0
                                        ? 'up'
                                        : info.custNumWeekRate < 0
                                        ? 'down'
                                        : '',
                                )}
                            >
                                {formatRate(info.custNumWeekRate)}%
                            </span>
                        </Trend>
                    </ChartCard>
                </Col>
                <Col {...topColResponsiveProps}>
                    <ChartCard
                        bordered={false}
                        title={formatDataName(DATATYPES.GUN)}
                        // action={
                        //     <Tooltip
                        //         title={
                        //             <FormattedMessage
                        //                 id="datacenteranddashboardanalysis.analysis.introduce"
                        //                 defaultMessage="Introduce"
                        //             />
                        //         }
                        //     >
                        //         <InfoCircleOutlined />
                        //     </Tooltip>
                        // }
                        total={() => [
                            <Yuan key="pq">{info.chargeGunPq || 0}</Yuan>,
                            <span key="pq-unit" className={pageStyles['chart-unit']}>
                                度
                            </span>,
                        ]}
                        footer={false}
                        contentHeight={46}
                        isTouch
                        isLight={dataType === DATATYPES.GUN}
                        onClick={() => {
                            changeDataTypeEvent(DATATYPES.GUN);
                        }}
                    >
                        <Trend
                            flag={
                                info.gunPqDayRate > 0 ? 'up' : info.gunPqDayRate < 0 ? 'down' : ''
                            }
                            style={{
                                marginRight: 16,
                            }}
                        >
                            较前日
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.gunPqDayRate > 0
                                        ? 'up'
                                        : info.gunPqDayRate < 0
                                        ? 'down'
                                        : '',
                                )}
                            >
                                {formatRate(info.gunPqDayRate)}%
                            </span>
                        </Trend>
                        <Trend
                            flag={
                                info.gunPqWeekRate > 0 ? 'up' : info.gunPqWeekRate < 0 ? 'down' : ''
                            }
                        >
                            较上周
                            <span
                                className={classnames(
                                    pageStyles.trendText,
                                    info.gunPqWeekRate > 0
                                        ? 'up'
                                        : info.gunPqWeekRate < 0
                                        ? 'down'
                                        : '',
                                )}
                            >
                                {formatRate(info.gunPqWeekRate)}%
                            </span>
                        </Trend>
                    </ChartCard>
                </Col>
            </Row>
        </Card>
    );
};

const oldColResponsiveProps = {
    md: 24,
    lg: 24,
    xl: 12,
    style: {
        marginBottom: 24,
    },
};

const PIE_TYPES = {
    timeSharePqList: '分时电量',
    dcAndAcPq: '交直流电量',

    amtList: '金额类型',
    applyModeAmtList: '下单渠道',
    actAmtList: '营销比例',

    custTypeNumList: '用户类型',
    custChannelList: '用户来源',

    stateTimesList: '状态时长',
    powerTypeList: '实际功率',
};

const OldRow = (props) => {
    const {
        overviewDetailList: { operHourData = {}, annularData },
        dataType,
    } = props;

    const [pieType, changePieType] = useState();

    const renderTitle = useMemo(() => (operHourData && operHourData.title) || null, [operHourData]);

    const buttonRender = useMemo(() => {
        if (dataType == DATATYPES.ELE) {
            changePieType('timeSharePqList');
            return [
                <Radio.Button value="timeSharePqList" key="timeSharePqList">
                    {PIE_TYPES.timeSharePqList}
                </Radio.Button>,
                <Radio.Button value="dcAndAcPq" key="dcAndAcPq">
                    {PIE_TYPES.dcAndAcPq}
                </Radio.Button>,
            ];
        }
        if (dataType == DATATYPES.PRICE) {
            changePieType('amtList');
            return [
                <Radio.Button value="amtList" key="amtList">
                    {PIE_TYPES.amtList}
                </Radio.Button>,
                <Radio.Button value="applyModeAmtList" key="applyModeAmtList">
                    {PIE_TYPES.applyModeAmtList}
                </Radio.Button>,
                <Radio.Button value="actAmtList" key="actAmtList">
                    {PIE_TYPES.actAmtList}
                </Radio.Button>,
            ];
        }
        if (dataType == DATATYPES.PEOPLE) {
            changePieType('custTypeNumList');
            return [
                <Radio.Button value="custTypeNumList" key="custTypeNumList">
                    {PIE_TYPES.custTypeNumList}
                </Radio.Button>,
                <Radio.Button value="custChannelList" key="custChannelList">
                    {PIE_TYPES.custChannelList}
                </Radio.Button>,
            ];
        }
        if (dataType == DATATYPES.GUN) {
            changePieType('stateTimesList');
            return [
                <Radio.Button value="stateTimesList" key="stateTimesList">
                    {PIE_TYPES.stateTimesList}
                </Radio.Button>,
                <Radio.Button value="powerTypeList" key="powerTypeList">
                    {PIE_TYPES.powerTypeList}
                </Radio.Button>,
            ];
        }
        return null;
    }, [dataType]);

    const pieData = useMemo(() => {
        // const salesTypeData = [
        //     {
        //         x: '家用电器',
        //         y: 4544,
        //     },
        //     {
        //         x: '食用酒水',
        //         y: 3321,
        //     },
        //     {
        //         x: '个护健康',
        //         y: 3113,
        //     },
        //     {
        //         x: '服饰箱包',
        //         y: 2341,
        //     },
        //     {
        //         x: '母婴产品',
        //         y: 1231,
        //     },
        //     {
        //         x: '其他',
        //         y: 1231,
        //     },
        // ];
        const salesTypeData = [];
        if (annularData && annularData[pieType]) {
            const { list = [] } = annularData[pieType];
            for (let index = 0; index < list.length; index++) {
                const element = list[index];
                salesTypeData.push({
                    x: element.name,
                    y: Number(element.value),
                    unit: element.unit,
                });
            }
        }
        return salesTypeData;
    }, [annularData, pieType]);

    const pieTotal = useMemo(() => {
        if (annularData && annularData[pieType]) {
            const { total, unit } = annularData[pieType];
            return `${total || ''}${unit || ''}`;
        }
        return '';
    }, [annularData, pieType]);
    const pieName = useMemo(() => {
        if (annularData && annularData[pieType]) {
            const { title } = annularData[pieType];
            return title || '';
        }
        return '';
    }, [annularData, pieType]);

    const lineData = useMemo(() => {
        const { beforeYesTodayList = [], lastWeekdayList = [], yesTodayList = [] } = operHourData;
        // const offlineChartData = [];
        // for (let i = 0; i < yesTodayList.length; i++) {
        //     offlineChartData.push({
        //         x: yesTodayList[i].name,
        //         y1: Number(yesTodayList[i].value),
        //         y2: Number(beforeYesTodayList[i].value),
        //         y3: Number(lastWeekdayList[i].value),
        //     });
        // }

        const legend = ['昨天', '前天', '7天前'];
        const xData = yesTodayList.map((ele) => ele.name);
        const yData = [
            yesTodayList.map((ele) => ele.value),
            beforeYesTodayList.map((ele) => ele.value),
            lastWeekdayList.map((ele) => ele.value),
        ];
        const offlineChartData = { legend, xData, yData };
        // for (let i = 0; i < yesTodayList.length; i++) {
        //     offlineChartData.push({
        //         x: yesTodayList[i].name,
        //         y1: Number(yesTodayList[i].value),
        //         y2: Number(beforeYesTodayList[i].value),
        //         y3: Number(lastWeekdayList[i].value),
        //     });
        // }
        return offlineChartData;
    }, [operHourData]);

    const changePieTypeEvent = (event) => {
        const {
            target: { value },
        } = event;
        changePieType(null);
        setTimeout(() => {
            changePieType(value);
        }, 0);
    };

    return (
        <Row className={pageStyles['old-row']}>
            <Col
                {...{
                    md: 24,
                    lg: 24,
                    xl: 10,
                    style: {
                        marginBottom: 24,
                    },
                }}
            >
                <Card title={false} bordered={false}>
                    <OverviewLine
                        title={renderTitle}
                        height={350}
                        data={lineData}
                        unit={operHourData.unit || ''}
                    />
                </Card>
            </Col>
            <Col
                {...{
                    md: 24,
                    lg: 24,
                    xl: 14,
                    style: {
                        marginBottom: 24,
                    },
                }}
            >
                <Card bordered={false} title={false}>
                    <div className={pageStyles['card-header']}>
                        <Radio.Group
                            defaultValue={pieType}
                            value={pieType}
                            buttonStyle="solid"
                            onChange={changePieTypeEvent}
                        >
                            {buttonRender}
                        </Radio.Group>
                    </div>
                    <PieChart
                        hasLegend
                        subTitle={pieName}
                        total={() => pieTotal}
                        data={pieData}
                        valueFormat={(value) => value}
                        height={300}
                        lineWidth={4}
                        unit={getDataUnit(dataType)}
                    />
                </Card>
            </Col>
        </Row>
    );
};

// const stationRankData = {
//     legend: [
//         {
//             name: '新用户',
//             value: 'new',
//         },
//         {
//             name: '老用户',
//             value: 'lod',
//         },
//     ],
//     x: ['8.11', '8.12', '8.13'],
//     y: [
//         {
//             new: 1,
//             old: 2,
//         },
//         {
//             new: 1,
//             old: 2,
//         },
//         {
//             new: 1,
//             old: 2,
//         },
//     ],
//     unit: '人',
//     title: '充电人数趋势',
// };

const ChargeRow = (props) => {
    const {
        overviewDetailList: { stationRankData, recentDayData = {} },
        dataType,
    } = props;
    const { recentSevenDataList = {}, recentThirtyDataList = {} } = recentDayData;
    const [timeType, changeTimeType] = useState('recentSevenDataList');

    const dataInfo = useMemo(() => {
        let info = {};
        if (timeType === 'recentSevenDataList') {
            info = recentSevenDataList;
        } else if (timeType === 'recentThirtyDataList') {
            info = recentThirtyDataList;
        }
        return info;
    }, [timeType, recentSevenDataList, recentThirtyDataList]);

    const changeTimeTypeEvent = (event) => {
        const {
            target: { value },
        } = event;
        changeTimeType(value);
    };

    const rankList = useMemo(
        () => (
            <div className={pageStyles.salesRank}>
                <h4 className={pageStyles.rankingTitle}>
                    {stationRankData && stationRankData.title}
                </h4>
                <ul className={pageStyles.rankingList}>
                    {stationRankData &&
                        stationRankData.stationRankList &&
                        stationRankData.stationRankList.map((item, i) => (
                            <li key={i}>
                                <span
                                    className={`${pageStyles.rankingItemNumber} ${
                                        i < 3 ? pageStyles.active : ''
                                    }`}
                                >
                                    {i + 1}
                                </span>
                                <span className={pageStyles.rankingItemTitle} title={item.name}>
                                    {item.name}
                                </span>
                                <span className={pageStyles.rankingItemValue}>
                                    {item.value}
                                    {(stationRankData && stationRankData.unit) || ''}
                                </span>
                            </li>
                        ))}
                </ul>
            </div>
        ),
        [stationRankData],
    );

    return (
        <Card title={false}>
            <div className={pageStyles['card-header']}>
                <Radio.Group
                    defaultValue={timeType}
                    value={timeType}
                    onChange={changeTimeTypeEvent}
                    buttonStyle="solid"
                >
                    <Radio.Button value="recentSevenDataList">近7日数据</Radio.Button>
                    <Radio.Button value="recentThirtyDataList">近30日数据</Radio.Button>
                </Radio.Group>
            </div>
            <Row gutter={50}>
                <Col xl={16} lg={16} md={16} sm={12} xs={24}>
                    <BarChart
                        height={320}
                        title={recentDayData && recentDayData.title}
                        data={dataInfo}
                        adjust={[
                            {
                                type: 'stack',
                            },
                        ]}
                    />
                </Col>
                <Col xl={8} lg={8} md={8} sm={12} xs={24}>
                    {rankList}
                </Col>
            </Row>
        </Card>
    );
};

const OverviewPage = (props) => {
    const {
        dispatch,
        history,
        global: { operatorList },
        totalLoading,
        overviewModel: { overviewTotalinfo, overviewDetailList },
        user: {
            currentUser: { operId },
        },
        pageLoading,
        detailLoading,
    } = props;

    const [dataType, changeDataType] = useState(DATATYPES.ELE);

    useEffect(() => {
        if (operId) {
            dispatch({
                type: 'overviewModel/initOverviewTotal',
                operId,
            });
        }
    }, []);

    useEffect(() => {
        if (operId) {
            dispatch({
                type: 'overviewModel/initOverviewDetails',
                options: {
                    operId,
                    dataType,
                },
            });
        }
    }, [dataType]);

    return (
        <PageHeaderWrapper title="运营概况">
            <IntroduceRow
                loading={totalLoading}
                info={overviewTotalinfo}
                dataType={dataType}
                changeDataType={changeDataType}
            />
            {operId ? (
                <Spin spinning={detailLoading}>
                    <OldRow overviewDetailList={overviewDetailList} dataType={dataType} />
                    <ChargeRow overviewDetailList={overviewDetailList} dataType={dataType} />
                </Spin>
            ) : null}
        </PageHeaderWrapper>
    );
};

export default connect(({ overviewModel, global, user, loading }) => ({
    overviewModel,
    global,
    user,
    totalLoading: loading.effects['overviewModel/initOverviewTotal'],
    detailLoading: loading.effects['overviewModel/initOverviewDetails'],
}))(OverviewPage);
