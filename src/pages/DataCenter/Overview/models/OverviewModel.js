import {
    getOperOverviewTotalApi,
    getOperOverviewDetailApi,
} from '@/services/DataCenter/OverviewApi';

const overviewModel = {
    namespace: 'overviewModel',
    state: {
        overviewTotalinfo: {}, // 统计数据
        overviewDetailList: [], // 图表数据
    },
    effects: {
        /**
         *统计
         */
        *initOverviewTotal({ operId }, { call, put, select }) {
            try {
                const {
                    data: { operOverviewTotal },
                } = yield call(getOperOverviewTotalApi, operId);

                yield put({
                    type: 'updateOverviewTotal',
                    info: operOverviewTotal,
                });
            } catch (error) {}
        },
        /**
         *图表数据
         */
        *initOverviewDetails({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getOperOverviewDetailApi, options);

                yield put({
                    type: 'updateOverviewDetails',
                    info: data,
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateOverviewTotal(state, { info }) {
            return {
                ...state,
                overviewTotalinfo: info,
            };
        },
        updateOverviewDetails(state, { info }) {
            return {
                ...state,
                overviewDetailList: info,
            };
        },
    },
};
export default overviewModel;
