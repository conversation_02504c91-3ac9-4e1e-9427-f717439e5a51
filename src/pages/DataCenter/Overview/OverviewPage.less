@import '~antd/es/style/themes/default.less';
.old-row {
    margin-bottom: 20px;
    background-color: white;
}
.card-header {
    margin-bottom: 20px;
}
.rankingTitle {
    font-weight: bold;
    font-size: 18px;
}
.chart-unit {
    color: @text-color;
    font-size: 16px;
}
.trendText {
    &:global(.up) {
        color: @red-6;
    }
    &:global(.down) {
        color: @green-6;
    }
}
.rankingList {
    margin: 25px 0 0;
    padding: 0;
    list-style: none;
    li {
        display: flex;
        align-items: center;
        margin-top: 16px;
        zoom: 1;
        &::before,
        &::after {
            display: table;
            content: ' ';
        }
        &::after {
            clear: both;
            height: 0;
            font-size: 0;
            visibility: hidden;
        }
        span {
            color: @text-color;
            font-size: 14px;
            line-height: 22px;
        }
        .rankingItemNumber {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-top: 1.5px;
            margin-right: 16px;
            font-weight: 600;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            background-color: @tag-default-bg;
            border-radius: 20px;
            &.active {
                color: #fff;
                background-color: #314659;
            }
        }
        .rankingItemTitle {
            flex: 1;
            margin-right: 8px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
