import {
    getSocConfigApi,
    getSocConfigHistoryApi,
    getSocEffectiveConfigListApi,
    getSocEffectiveDetailApi,
} from '@/services/DataCenter/SocApi.js';

const SocModel = {
    namespace: 'socModel',
    state: {
        socCommonConfigInfo: undefined, // 阈值-通用
        socCityConfigList: undefined, // 阈值-城市
        socOperConfigList: undefined, // 阈值-运营商
        socHumenConfigInfo: undefined, // 阈值-人责
        socGunConfigInfo: undefined, // 阈值-枪责

        socConfigHistoryList: [], //
        socConfigHistoryListTotal: 0, //

        socEffectiveConfigList: [], //
        socEffectiveConfigListTotal: 0, //

        socEffectiveConfigDetailList: [], //
        socEffectiveConfigDetailListTotal: 0, //
    },
    effects: {
        *getSocConfigInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getSocConfigApi, options);

                yield put({
                    type: 'updateSocProperty',
                    params: {
                        socCommonConfigInfo: data['01']?.[0],
                        socCityConfigList: data['02'],
                        socOperConfigList: data['03'],
                        socHumenConfigInfo: data['04']?.[0],
                        socGunConfigInfo: data['05']?.[0],
                    },
                });
            } catch (error) {}
        },

        *getSocConfigHistoryList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getSocConfigHistoryApi, options);

                yield put({
                    type: 'updateSocProperty',
                    params: { socConfigHistoryList: records, socConfigHistoryListTotal: total },
                });
            } catch (error) {}
        },

        *getSocEffectiveConfigList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getSocEffectiveConfigListApi, options);
                yield put({
                    type: 'updateSocProperty',
                    params: { socEffectiveConfigList: records, socEffectiveConfigListTotal: total },
                });
            } catch (error) {}
        },
        *getSocEffectiveDetailList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records, total },
                } = yield call(getSocEffectiveDetailApi, options);
                yield put({
                    type: 'updateSocProperty',
                    params: {
                        socEffectiveConfigDetailList: records,
                        socEffectiveConfigDetailListTotal: total,
                    },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateSocProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default SocModel;
