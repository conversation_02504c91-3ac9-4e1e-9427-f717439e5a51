import { <PERSON><PERSON>eaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON>ton,
    Card,
    Col,
    Form,
    Modal,
    Select,
    Space,
    Input,
    DatePicker,
    Tabs,
    message,
    Tooltip,
    Radio,
    Badge,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef, useImperativeHandle } from 'react';
import { exportTableByParams } from '@/utils/utils';
import { InfoCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';
import styles from '@/assets/styles/common.less';

import { saveSocConfigApi, exportSocEffectiveConfigListApi } from '@/services/DataCenter/SocApi';
import { getCityListApi } from '@/services/CommonApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { INVIOCE_STATUS } from '@/config/declare';

const { TextArea } = Input;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;

const PAGE_TYPES = {
    HUMEN: '01',
    GUN: '02',
};

const formItemLayout = {};

const inputOptions = {
    maxLength: 25,
    autoComplete: 'off',
};

const HumanErrorSearchLayout = (props) => {
    const {
        form,
        onSubmit,
        onReset,
        onExportForm,
        global: {
            codeInfo: { renewState = [] }, //
        },
        listLoading,
    } = props;

    const renewStateMode = useMemo(
        () =>
            renewState.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [renewState],
    );

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Fragment>
            <Form
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    dates: [moment().subtract(1, 'month'), moment()],
                    renewState: '01',
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    exportLoading={listLoading}
                    onReset={resetForm}
                    onExportForm={onExportForm}
                    open
                >
                    <Col span={8}>
                        <FormItem
                            label="手&nbsp;&nbsp;机&nbsp;&nbsp;号"
                            name="mobile"
                            {...formItemLayout}
                        >
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="最后异常" name="dates">
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="恢复状态" name="renewState" {...formItemLayout}>
                            <Select placeholder="请选择">{renewStateMode}</Select>
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const HumanErrorListPage = (props) => {
    const {
        dispatch,
        history,
        socModel: {
            socHumenConfigInfo, // 用于异常说明的！
            socGunConfigInfo,
            socEffectiveConfigList,
            socEffectiveConfigListTotal,
        },
        listLoading,
        global: { pageInit, invoiceStatusOptions },
        showHandleEvent,
        showRemarkEvent,
        showErrorListEvent,
        initRef,
    } = props;

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}human`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: INVIOCE_STATUS.ALL,
        },
        props,
        cacheName,
    );

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
        if (invoiceStatusOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceStatusList',
                options: {},
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                relateType: '01',
                expTimeBgn:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                expTimeEnd:
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                dates: undefined,
            };

            if (isDownload) {
                await exportSocEffectiveConfigListApi(params);
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;

                dispatch({
                    type: 'socModel/getSocEffectiveConfigList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    useImperativeHandle(initRef, () => ({
        resetData,
        searchData,
    }));

    const columns = [
        {
            title: '手机号码',
            width: 140,
            dataIndex: 'mobile',
            render(text, record) {
                return (
                    (record?.remarks?.length && (
                        <Tooltip title={record?.remarks}>
                            <Badge dot>
                                <span title={text}>{text || '-'}</span>
                            </Badge>
                        </Tooltip>
                    )) || <span title={text}>{text || '-'}</span>
                );
            },
        },
        {
            title: (
                <span>
                    异常说明
                    {(socHumenConfigInfo?.closeFlag == '0' && (
                        <Tooltip
                            title={`近${socHumenConfigInfo.effectiveDay}天SOC超100%的订单达${socHumenConfigInfo.effectiveNum}个以上，该用户阈值设置为${socHumenConfigInfo.effectiveSoc}%`}
                        >
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    )) ||
                        null}
                </span>
            ),
            width: 240,
            dataIndex: 'exceptionMarks',
            render(text, record) {
                return <a onClick={() => showErrorListEvent(record)}>{text}</a>;
            },
        },
        {
            title: '最后异常时间',
            width: 200,
            dataIndex: 'lastExceptionTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '异常影响',
            width: 200,
            dataIndex: 'exceptionEffect',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: (
                <span>
                    恢复状态
                    <Tooltip title="恢复后，用户的异常阈值将失效">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            dataIndex: 'renewStateName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '恢复时间',
            width: 200,
            dataIndex: 'renewTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                const editItem = [];
                if (record.renewState == '01') {
                    editItem.push(
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                showHandleEvent(record);
                            }}
                        >
                            处理
                        </span>,
                    );
                } else {
                    editItem.push(
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                showRemarkEvent(record);
                            }}
                        >
                            备注
                        </span>,
                    );
                }

                return <Space>{editItem}</Space>;
            },
        },
    ];

    return (
        <Fragment>
            <HumanErrorSearchLayout
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />

            <TablePro
                name="human"
                rowKey={(record) => record.invoiceAppNo}
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                dataSource={socEffectiveConfigList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: socEffectiveConfigListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

const GunErrorSearchLayout = (props) => {
    const {
        form,
        onSubmit,
        onReset,
        global: {
            codeInfo: { renewState = [] }, //
        },
        listLoading,
        onExportForm,
    } = props;

    useEffect(() => {
        initCities();
    }, []);

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const stationRef = useRef();

    // 初始化状态
    const renewStateMode = useMemo(
        () =>
            renewState.map((ele) => (
                <Option key={ele.codeValue} value={ele.codeValue}>
                    {ele.codeName}
                </Option>
            )),
        [renewState],
    );

    // 初始化城市选择数据
    const [cityLoading, updateCityLoading] = useState(false);
    const [cityList, updateCityList] = useState([]);
    const initCities = async () => {
        try {
            // form.setFieldsValue({ [name]: list });
            updateCityLoading(true);
            const { data: areaList } = await getCityListApi();

            updateCityList(areaList);
        } catch (error) {
            updateCityList([]);
            throw new Error(error);
        } finally {
            updateCityLoading(false);
        }
    };

    const cityData = useMemo(() => {
        const list = [];
        for (const item of cityList) {
            for (const city of item.cityList) {
                list.push({
                    title: city.areaName,
                    value: city.areaCode,
                });
            }
        }
        return list;
    }, [cityList]);

    return (
        <Fragment>
            <Form
                // labelCol={{ span: 4, offset: 4 }}
                // wrapperCol={{ span: 16 }}
                {...formItemLayout}
                form={form}
                onFinish={onFinish}
                initialValues={{
                    dates: [moment().subtract(1, 'month'), moment()],
                    renewState: '01',
                }}
                scrollToFirstError
            >
                <SearchOptionsBar
                    loading={listLoading}
                    exportLoading={listLoading}
                    onReset={resetForm}
                    open
                    onExportForm={onExportForm}
                >
                    <Col span={8}>
                        <OperSelectTypeItem
                            formItemLayout={{ wrapperCol: null, labelCol: null }}
                            name="buildId"
                            form={form}
                        />
                    </Col>
                    <Col span={8}>
                        <AllStationSelect form={form} ref={stationRef} label="站点" />
                    </Col>
                    <Col span={8}>
                        <FormItem label="城市" name="city">
                            <Select
                                showSearch
                                filterOption={(input, option) =>
                                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                }
                                loading={cityLoading}
                                allowClear
                                showArrow
                                placeholder="请选择"
                            >
                                {cityData.map((ele) => {
                                    return (
                                        <Option key={ele.value} value={ele.value}>
                                            {ele.title}
                                        </Option>
                                    );
                                })}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="最后异常" name="dates">
                            <RangePicker format="YYYY-MM-DD" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="恢复状态" name="renewState" {...formItemLayout}>
                            <Select placeholder="请选择">{renewStateMode}</Select>
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <FormItem label="电桩名称" name="pileName" {...formItemLayout}>
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="电桩编号" name="pileNo" {...formItemLayout}>
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪名称" name="gunName" {...formItemLayout}>
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="枪编号" name="gunNo" {...formItemLayout}>
                            <Input placeholder="请填写" {...inputOptions} />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
        </Fragment>
    );
};

const GunErrorPage = (props) => {
    const {
        dispatch,
        history,
        socModel: { socGunConfigInfo, socEffectiveConfigList, socEffectiveConfigListTotal },
        global: { pageInit, operatorList },
        showHandleEvent,
        showRemarkEvent,
        showErrorListEvent,
        listLoading,
        initRef,
    } = props;

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}gun`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cacheName);

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async (isDownload) => {
        try {
            const data = await form.validateFields();
            const params = {
                ...data,
                relateType: '02',
                expTimeBgn:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                expTimeEnd:
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
                dates: undefined,
            };

            if (isDownload) {
                await exportSocEffectiveConfigListApi(params);
            } else {
                params.pageIndex = pageInfo.pageIndex;
                params.pageSize = pageInfo.pageSize;
                dispatch({
                    type: 'global/setPageInit',
                    pathname: cacheName,
                    info: {
                        form: data,
                        state: pageInfo,
                    },
                });

                dispatch({
                    type: 'socModel/getSocEffectiveConfigList',
                    options: params,
                });
            }
        } catch (error) {}
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    useImperativeHandle(initRef, () => ({
        resetData,
        searchData,
    }));

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return (
                    <div>
                        <div className="text-line" style={{ width: '140px' }} title={text}>
                            {text}
                        </div>
                        <div
                            className="text-line"
                            style={{ width: '140px' }}
                            title={record.projectNo}
                        >
                            {record.projectNo}
                        </div>
                    </div>
                );
            },
        },
        {
            title: '站点',
            width: 160,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩名称',
            width: 140,
            dataIndex: 'pileName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '电桩编号',
            width: 140,
            dataIndex: 'pileNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '枪名称',
            width: 140,
            dataIndex: 'gunName',
            render(text, record) {
                return (
                    (record?.remarks?.length && (
                        <Tooltip title={record?.remarks}>
                            <Badge dot>
                                <span title={text}>{text || '-'}</span>
                            </Badge>
                        </Tooltip>
                    )) || <span title={text}>{text || '-'}</span>
                );
            },
        },
        {
            title: '枪编号',
            width: 140,
            dataIndex: 'gunNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '联系人',
            width: 140,
            dataIndex: 'contactName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '联系电话',
            width: 140,
            dataIndex: 'contactTel',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: (
                <span>
                    异常说明
                    {(socGunConfigInfo?.closeFlag == '0' && (
                        <Tooltip
                            title={`近${socGunConfigInfo.effectiveDay}天有${socGunConfigInfo.effectiveNum}个以上不同用户，订单SOC超100%，该充电枪阈值设置为${socGunConfigInfo.effectiveSoc}%`}
                        >
                            <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                        </Tooltip>
                    )) ||
                        null}
                </span>
            ),
            width: 320,
            dataIndex: 'exceptionMarks',
            render(text, record) {
                return <a onClick={() => showErrorListEvent(record)}>{text}</a>;
            },
        },
        {
            title: '最后异常时间',
            width: 200,
            dataIndex: 'lastExceptionTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '异常影响',
            width: 200,
            dataIndex: 'exceptionEffect',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: (
                <span>
                    恢复状态
                    <Tooltip title="恢复后，用户的异常阈值将失效">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 140,
            dataIndex: 'renewStateName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '恢复时间',
            width: 200,
            dataIndex: 'renewTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                const editItem = [];
                if (record.renewState == '01') {
                    editItem.push(
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                showHandleEvent(record);
                            }}
                        >
                            处理
                        </span>,
                    );
                } else {
                    editItem.push(
                        <span
                            className={styles['table-btn']}
                            onClick={() => {
                                showRemarkEvent(record);
                            }}
                        >
                            备注
                        </span>,
                    );
                }

                return <Space>{editItem}</Space>;
            },
        },
    ];

    return (
        <Fragment>
            <GunErrorSearchLayout
                cacheName={cacheName}
                form={form}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                onExportForm={() => searchData(true)}
            />

            <TablePro
                name="gun"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record, index) => index}
                dataSource={socEffectiveConfigList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: socEffectiveConfigListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Fragment>
    );
};

const SocListPage = (props) => {
    const {
        dispatch,
        global: {
            codeInfo: { renewState = [] }, //
        },
        socModel: {
            socEffectiveConfigDetailList, //
            socEffectiveConfigDetailListTotal, //
            socHumenConfigInfo, // 用于异常说明的！
            socGunConfigInfo,
        },
        detailListLoading,
    } = props;

    const [pageType, changePageType] = useState(PAGE_TYPES.HUMEN);
    const listRef = useRef();

    let tabOptions = [
        { tab: '人责', key: PAGE_TYPES.HUMEN },
        { tab: '枪责', key: PAGE_TYPES.GUN },
    ];

    useEffect(() => {
        if (renewState.length == 0) {
            dispatch({
                type: 'global/initCode',
                code: 'renewState',
            });
        }
        if (!socHumenConfigInfo || !socGunConfigInfo) {
            dispatch({
                type: 'socModel/getSocConfigInfo',
            });
        }
    }, []);

    const onPageChange = (e) => {
        changePageType(e);
    };

    const [submitLoading, changeSubmitloading] = useState(false); // 上传加载
    const [modalForm] = Form.useForm();

    // 处理
    const [showHandleVisible, updateShowHandleVisible] = useState(false);
    const showHandleEvent = (item, configType) => {
        modalForm.setFieldsValue({
            ...item,
            configType,
            operatorType: '04',
        });
        updateShowHandleVisible(true);
    };

    const closeHandleEvent = (item) => {
        updateShowHandleVisible(false);
        modalForm.resetFields();
    };

    // 备注
    const [showRemarkVisible, updateShowRemarkVisible] = useState(false);
    const showRemarkEvent = (item) => {
        modalForm.setFieldsValue({
            ...item,
            operatorType: '05',
        });
        updateShowRemarkVisible(true);
    };

    const closeRemarkEvent = (item) => {
        updateShowRemarkVisible(false);
        modalForm.resetFields();
    };

    const onFinish = async (values) => {
        try {
            changeSubmitloading(true);
            const params = {
                ...values,
            };
            const { msg } = await saveSocConfigApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            closeHandleEvent();
            closeRemarkEvent();
            listRef.current.searchData();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    // 异常说明
    const [errorListVisible, updateErrorListVisible] = useState(false);
    const [editItem, updateEditItem] = useState();
    const showErrorListEvent = (item) => {
        updateEditItem({ ...item });
        updateErrorListVisible(true);
    };

    const closeErrorListEvent = (item) => {
        updateEditItem(undefined);
        updateErrorListVisible(false);
        changeErrorPageInfo({ pageIndex: 1 });
    };

    const [errorPageInfo, changeErrorPageInfo, onErrorTableChange] = usePageState({});
    useEffect(() => {
        if (errorListVisible && editItem) {
            searchDetailList(editItem);
        }
    }, [errorPageInfo, pageType, errorListVisible, editItem]);

    // 调用搜索接口
    const searchDetailList = async (item) => {
        try {
            const params = {
                pageIndex: errorPageInfo.pageIndex,
                pageSize: errorPageInfo.pageSize,
                relateId: item?.relateId,
                relateType: item?.relateType,
                lastExceptionTime: item?.lastExceptionTime,
                effectiveDay: item?.effectiveDay,
            };

            dispatch({
                type: 'socModel/getSocEffectiveDetailList',
                options: params,
            });
        } catch (error) {}
    };

    const errorColumns = [
        {
            title: (
                <span>
                    异常时间
                    <Tooltip title="订单第一次上报异常的时间，异常场景包括SOC超100%">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </span>
            ),
            width: 200,
            dataIndex: 'exceptionTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '异常订单',
            width: 140,
            dataIndex: 'orderNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '第三方订单号',
            width: 140,
            dataIndex: 'partOrderNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '订单时间',
            width: 200,
            dataIndex: 'bgnTime',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '运营商',
            width: 140,
            dataIndex: 'operName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '站点',
            width: 160,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '充电枪',
            width: 140,
            dataIndex: 'equipName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '枪编号',
            width: 180,
            dataIndex: 'equipNo',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '开始SOC',
            width: 120,
            dataIndex: 'startSoc',
            render(text, record) {
                return <span title={text}>{text || '-'}%</span>;
            },
        },
        {
            title: '结束SOC',
            width: 120,
            dataIndex: 'endSoc',
            render(text, record) {
                return <span title={text}>{text || '-'}%</span>;
            },
        },
        {
            title: '用户',
            width: 120,
            dataIndex: 'mobile',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
    ];

    const pageShow = () => {
        if (pageType == PAGE_TYPES.HUMEN) {
            return (
                <HumanErrorListPage
                    {...props}
                    showHandleEvent={(item) => showHandleEvent(item, '04')}
                    showRemarkEvent={showRemarkEvent}
                    showErrorListEvent={showErrorListEvent}
                    initRef={listRef}
                />
            );
        }
        if (pageType == PAGE_TYPES.GUN) {
            return (
                <GunErrorPage
                    {...props}
                    showHandleEvent={(item) => showHandleEvent(item, '05')}
                    showRemarkEvent={showRemarkEvent}
                    showErrorListEvent={showErrorListEvent}
                    initRef={listRef}
                />
            );
        }
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <Tabs onChange={onPageChange}>
                    {tabOptions.map((ele) => (
                        <TabPane tab={ele.tab} key={ele.key} />
                    ))}
                </Tabs>
                <div style={{ marginTop: '20px' }}>{pageShow()}</div>
            </Card>

            <Modal
                title="处理"
                visible={showHandleVisible}
                footer={false}
                onCancel={closeHandleEvent}
                maskClosable={false}
            >
                <Form
                    {...formItemLayout}
                    form={modalForm}
                    onFinish={onFinish}
                    labelCol={{ span: 4 }}
                >
                    <FormItem name="recordId" noStyle />
                    <FormItem name="relateId" noStyle />
                    <FormItem name="relateType" noStyle />
                    <FormItem name="operatorType" noStyle />
                    <FormItem name="configType" noStyle />
                    <FormItem name="lastExceptionTime" noStyle />
                    <FormItem name="effectiveDay" noStyle />

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.renewState !== curValues.renewState
                        }
                    >
                        {({ getFieldValue }) => {
                            const renewState = getFieldValue('renewState');
                            return (
                                <FormItem
                                    label="恢复状态"
                                    name="renewState"
                                    initialValue={'01'}
                                    extra={
                                        renewState == '03' &&
                                        '恢复后，该用户/充电枪的异常阈值将失效'
                                    }
                                >
                                    <Radio.Group>
                                        <Radio value="01">未恢复</Radio>
                                        <Radio value="03">手动恢复</Radio>
                                    </Radio.Group>
                                </FormItem>
                            );
                        }}
                    </FormItem>

                    <FormItem label="备注" name="remarks">
                        <TextArea placeholder="请填写" />
                    </FormItem>

                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                提交
                            </Button>
                            <Button onClick={closeHandleEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>

            <Modal
                title="备注"
                visible={showRemarkVisible}
                footer={false}
                onCancel={closeRemarkEvent}
                maskClosable={false}
            >
                <Form
                    {...formItemLayout}
                    form={modalForm}
                    onFinish={onFinish}
                    labelCol={{ span: 4 }}
                >
                    <FormItem name="recordId" noStyle />
                    <FormItem name="relateId" noStyle />
                    <FormItem name="relateType" noStyle />
                    <FormItem name="operatorType" noStyle />
                    <FormItem name="configType" noStyle />
                    <FormItem name="lastExceptionTime" noStyle />

                    <FormItem label="备注" name="remarks">
                        <TextArea placeholder="请填写" />
                    </FormItem>

                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                提交
                            </Button>
                            <Button onClick={closeRemarkEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>

            <Modal
                title="异常详情"
                visible={errorListVisible}
                footer={false}
                onCancel={closeErrorListEvent}
                maskClosable
                width={820}
            >
                <TablePro
                    name="list"
                    loading={detailListLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={socEffectiveConfigDetailList}
                    columns={errorColumns}
                    onChange={onErrorTableChange}
                    pagination={{
                        current: errorPageInfo.pageIndex,
                        total: socEffectiveConfigDetailListTotal,
                        pageSize: errorPageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ user, financeModel, socModel, global, loading }) => ({
    financeModel,
    socModel,
    global,
    user,
    listLoading: loading.effects['socModel/getSocEffectiveConfigList'],
    detailListLoading: loading.effects['socModel/getSocEffectiveDetailList'],
}))(SocListPage);
