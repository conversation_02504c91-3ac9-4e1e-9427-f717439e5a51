import { <PERSON>HeaderWrapper } from '@ant-design/pro-layout';
import {
    <PERSON><PERSON>,
    Card,
    Col,
    Form,
    // message,
    // Icon,
    Modal,
    Row,
    Select,
    // Alert,
    // Divider,
    Space,
    Input,
    InputNumber,
    DatePicker,
    Tabs,
    message,
    Tooltip,
    Upload,
    Radio,
    Spin,
    Result,
    Popconfirm,
    Alert,
    Switch,
} from 'antd';
import { connect, Link } from 'umi';
import React, { Fragment, useEffect, useState, useMemo, useRef } from 'react';
import { exportTableByParams } from '@/utils/utils';
import {
    InfoCircleOutlined,
    FormOutlined,
    EyeOutlined,
    LoadingOutlined,
    PlusOutlined,
    DeleteOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import AllStationSelect from '@/components/AllStationSelect';
import OperSelectItem from '@/components/OperSelectItem';
import styles from './style.less';
import commonStyles from '@/assets/styles/common.less';

import {
    invoiceApplyListApiPath,
    invoiceStatisticsListApiPath,
    invalidInvoiceApi,
    retryInvoiceApi,
    retryInvoiceAgainApi,
    changeInvoiceApi,
    invoiceApplyExportListApi,
    invoiceStatisticsExportListApi,
    importInvoiceResultApi,
    downloadInvoiceImportFailExportApi,
    importInvoiceApi,
    invoiceNoticeKingDeeApi,
} from '@/services/FinanceManage/InvoiceManageApi';
import { saveSocConfigApi } from '@/services/DataCenter/SocApi';
import { getCityListApi } from '@/services/CommonApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { INVIOCE_STATUS } from '@/config/declare';
import { values } from 'lodash';
// import InvoiceModal from './InvoiceModal';

const { TextArea } = Input;

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const PAGE_TYPES = {
    CITY: '02',
    OPER: '03',
};

const formItemLayout = {};

const CityListPage = (props) => {
    const {
        dispatch,
        history,
        socModel: { invoiceApplyList, invoiceApplyListTotal },
        socModel: {
            socCommonConfigInfo, // 阈值-通用
            socCityConfigList, // 阈值-城市
            socOperConfigList, // 阈值-运营商
            socHumenConfigInfo, // 阈值-人责
            socGunConfigInfo, // 阈值-枪责
        },
        configLoading,
        global: { pageInit, invoiceStatusOptions },
        errorEditEvent,
        deleteEvent,
    } = props;

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}city`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: INVIOCE_STATUS.ALL,
        },
        props,
        cacheName,
    );

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
        if (invoiceStatusOptions.length == 0) {
            dispatch({
                type: 'global/getInvoiceStatusList',
                options: {},
            });
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                beginDate:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD')) || '',
                endDate: (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD')) || '',
            };

            dispatch({
                type: 'global/setPageInit',
                pathname: cacheName,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'socModel/getInvoiceApplyList',
                options: params,
            });
        } catch (error) {}
    };

    const columns = [
        {
            title: '城市',
            width: 140,
            dataIndex: 'cityName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: 'SOC阈值',
            width: 140,
            dataIndex: 'effectiveSoc',
            render(text = '-', record) {
                return <span title={text}>{text}%</span>;
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                const editItem = [
                    <a
                        key="edit"
                        onClick={() => {
                            errorEditEvent(record);
                        }}
                    >
                        编辑
                    </a>,
                    <Popconfirm
                        key="delete"
                        title="是否确认删除？"
                        okText="是"
                        cancelText="否"
                        onConfirm={() => deleteEvent(record)}
                    >
                        <a>删除</a>
                    </Popconfirm>,
                ];

                return <Space>{editItem}</Space>;
            },
        },
    ];

    return (
        <Space size="middle" direction="vertical">
            <Button onClick={() => errorEditEvent()} type="primary">
                新增
            </Button>
            <TablePro
                name="city"
                rowKey={(record) => record.configId}
                loading={configLoading}
                scroll={{ x: 'max-content' }}
                dataSource={socCityConfigList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: socCityConfigList?.length,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Space>
    );
};

const OperListPage = (props) => {
    const {
        dispatch,
        history,
        socModel: { invoiceStatisticsList, invoiceStatisticsListTotal },
        socModel: {
            socCommonConfigInfo, // 阈值-通用
            socCityConfigList, // 阈值-城市
            socOperConfigList, // 阈值-运营商
            socHumenConfigInfo, // 阈值-人责
            socGunConfigInfo, // 阈值-枪责
        },
        configLoading,
        global: { pageInit, operatorList },
        errorEditEvent,
        deleteEvent,
    } = props;

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);

    const {
        location: { pathname },
    } = history;

    const cacheName = `${pathname}oper`;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, props, cacheName);

    useEffect(() => {
        if (pageInit[cacheName]) {
            form.setFieldsValue(pageInit[cacheName].form);
        }
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = await form.validateFields();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operId: data.operId,
                invoiceAccessMode: data.invoiceAccessMode,
                projectNo: data.projectNo,
                beginDate: data.beginDate.format('YYYY-MM'),
            };

            dispatch({
                type: 'global/setPageInit',
                pathname: cacheName,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            dispatch({
                type: 'socModel/getInvoiceStatisticsList',
                options: params,
            });
        } catch (error) {}
    };

    const columns = [
        {
            title: '运营商',
            width: 140,
            dataIndex: 'buildName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: 'SOC阈值',
            width: 140,
            dataIndex: 'effectiveSoc',
            render(text = '-', record) {
                return <span title={text}>{text}%</span>;
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                const editItem = [
                    <a
                        key="edit"
                        onClick={() => {
                            errorEditEvent(record);
                        }}
                    >
                        编辑
                    </a>,
                    <Popconfirm
                        key="delete"
                        title="是否确认删除"
                        okText="是"
                        cancelText="否"
                        onConfirm={() => deleteEvent(record)}
                    >
                        <a>删除</a>
                    </Popconfirm>,
                ];

                return <Space>{editItem}</Space>;
            },
        },
    ];

    return (
        <Space size="middle" direction="vertical">
            <Button onClick={() => errorEditEvent()} type="primary">
                新增
            </Button>
            <TablePro
                name="oper"
                loading={configLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.configId}
                dataSource={socOperConfigList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: socOperConfigList?.length,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
        </Space>
    );
};

const SupportPage = (props) => {
    const {
        dispatch,
        global: {
            invoiceAccessModeOptions, // 开票方式
            invoiceTypeCodeOptions, // 发票类型
            operatorList,
        },
        socModel: {
            socCommonConfigInfo, // 阈值-通用
            socCityConfigList, // 阈值-城市
            socOperConfigList, // 阈值-运营商
            socHumenConfigInfo, // 阈值-人责
            socGunConfigInfo, // 阈值-枪责

            socConfigHistoryList,
            socConfigHistoryListTotal,
        },
        historyListLoading,
        configLoading,
    } = props;

    const [pageType, changePageType] = useState(PAGE_TYPES.CITY);

    let tabOptions = [
        { tab: '城市', key: PAGE_TYPES.CITY },
        { tab: '运营商', key: PAGE_TYPES.OPER },
    ];

    useEffect(() => {
        initData();
    }, []);

    const initData = () => {
        dispatch({
            type: 'socModel/getSocConfigInfo',
        });
    };

    const onPageChange = (e) => {
        changePageType(e);
    };

    const [submitLoading, changeSubmitloading] = useState(false); // 上传加载
    const [modalForm] = Form.useForm();
    const [editItem, updateEditItem] = useState();

    // 修改通用阈值
    const [commonForm] = Form.useForm();
    const [commonVisible, updateCommonVisible] = useState(false);
    const showCommonEvent = (item) => {
        commonForm.setFieldsValue({ ...item, operatorType: '02', configType: '01' });
        updateCommonVisible(true);
    };

    const closeCommonEvent = (item) => {
        updateCommonVisible(false);
        commonForm.resetFields();
    };

    const onCommonFinish = () => {
        commonForm.validateFields().then(async (values) => {
            try {
                changeSubmitloading(true);
                const { msg } = await saveSocConfigApi(values);
                if (msg?.length) {
                    message.success(msg);
                }
                closeCommonEvent();
                initData();
                resetHistoryData();
            } catch (error) {
            } finally {
                changeSubmitloading(false);
            }
        });
    };

    // 城市/运营商
    const [addVisible, updateHandleVisible] = useState(false);
    const showAddEvent = (item, configType = PAGE_TYPES.CITY) => {
        updateEditItem((item && { ...item }) || undefined);
        modalForm.setFieldsValue({ ...item, configType, operatorType: item ? '02' : '01' });
        updateHandleVisible(true);

        if (!cityList?.length) {
            initCities();
        }

        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    };

    const closeAddEvent = (item) => {
        updateEditItem(undefined);
        updateHandleVisible(false);
        modalForm.resetFields();
    };

    const onAddFinish = async (values) => {
        try {
            changeSubmitloading(true);
            const params = { ...values };
            const { msg } = await saveSocConfigApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            changePageType(values.configType);
            closeAddEvent();
            initData();
            resetHistoryData();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    const deleteEvent = async (item) => {
        try {
            changeSubmitloading(true);
            const params = {
                operatorType: '03',
                configId: item.configId,
                configType: item.configType,
            };
            const { msg } = await saveSocConfigApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            initData();
            resetHistoryData();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    // 初始化城市选择数据
    const [cityLoading, updateCityLoading] = useState(false);
    const [cityList, updateCityList] = useState([]);
    const initCities = async () => {
        try {
            // form.setFieldsValue({ [name]: list });
            updateCityLoading(true);
            const { data: areaList } = await getCityListApi();

            updateCityList(areaList);
        } catch (error) {
            updateCityList([]);
            throw new Error(error);
        } finally {
            updateCityLoading(false);
        }
    };

    const cityData = useMemo(() => {
        const list = [];
        for (const item of cityList) {
            for (const city of item.cityList) {
                list.push({
                    title: city.areaName,
                    value: city.areaCode,
                });
            }
        }
        return list;
    }, [cityList]);

    // 运营商数据
    const operatorSelectRef = useRef();

    // 异常阈值
    const [editForm] = Form.useForm();
    const errorInfo = useMemo(() => {
        return [
            {
                title: '人责',
                ...socHumenConfigInfo,
            },
            {
                title: '枪责',
                ...socGunConfigInfo,
            },
        ];
    }, [socHumenConfigInfo, socGunConfigInfo]);

    const [errorEditVisible, updateErrorEditVisible] = useState(false);
    const editErrorEvent = (item) => {
        updateEditItem({ ...item });
        updateErrorEditVisible(true);
        editForm.setFieldsValue({ ...item });
    };

    const closeErrorEvent = (item) => {
        updateEditItem(undefined);
        updateErrorEditVisible(false);
        editForm.resetFields();
    };

    const editFinishEvent = () => {
        editForm.validateFields().then(async (values) => {
            try {
                changeSubmitloading(true);
                const params = {
                    ...values,
                    operatorType: '02',
                    configId: editItem.configId,
                    configType: editItem.configType,
                };
                const { msg } = await saveSocConfigApi(params);
                if (msg?.length) {
                    message.success(msg);
                }
                closeErrorEvent();
                initData();
                resetHistoryData();
            } catch (error) {
            } finally {
                changeSubmitloading(false);
            }
        });
    };

    const switchErrorEvent = async (item) => {
        // 切换开关,关闭类型 0正常 1关闭
        try {
            changeSubmitloading(true);
            const params = {
                operatorType: '02',
                configId: item.configId,
                configType: item.configType,
                closeFlag: item.closeFlag == '1' ? '0' : '1',
            };
            const { msg } = await saveSocConfigApi(params);
            if (msg?.length) {
                message.success(msg);
            }
            initData();
            resetHistoryData();
        } catch (error) {
        } finally {
            changeSubmitloading(false);
        }
    };

    // 变动历史
    const [historyPageInfo, changeErrorPageInfo, onHistoryTableChange] = usePageState({});
    useEffect(() => {
        searchHistoryData();
    }, [historyPageInfo]);

    // 调用搜索接口
    const searchHistoryData = async () => {
        try {
            const params = {
                pageIndex: historyPageInfo.pageIndex,
                pageSize: historyPageInfo.pageSize,
            };

            dispatch({
                type: 'socModel/getSocConfigHistoryList',
                options: params,
            });
        } catch (error) {}
    };

    const resetHistoryData = () => {
        if (historyPageInfo.pageIndex != 1) {
            changeErrorPageInfo({ pageIndex: 1 });
        } else {
            searchHistoryData();
        }
    };

    const historyColumns = [
        {
            title: '操作时间',
            width: 200,
            dataIndex: 'dataOperTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作类型',
            width: 140,
            dataIndex: 'operType',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作内容',
            width: 240,
            dataIndex: 'operInfo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作人',
            width: 100,
            dataIndex: 'operator',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const pageShow = () => {
        if (pageType == PAGE_TYPES.CITY) {
            return (
                <CityListPage
                    {...props}
                    errorEditEvent={(item) => showAddEvent(item, PAGE_TYPES.CITY)}
                    deleteEvent={(item) => deleteEvent(item)}
                />
            );
        }
        if (pageType == PAGE_TYPES.OPER) {
            return (
                <OperListPage
                    {...props}
                    errorEditEvent={(item) => showAddEvent(item, PAGE_TYPES.OPER)}
                    deleteEvent={(item) => deleteEvent(item)}
                />
            );
        }
    };

    return (
        <PageHeaderWrapper>
            <Card loading={configLoading}>
                <p className={styles['formTitle']}>
                    SOC阈值
                    <Tooltip title="当某个充电枪/用户没有异常阈值时，阈值生效顺序：运营商>城市>通用">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </p>
                <Alert
                    message={
                        <Space size="middle">
                            <div style={{ fontWeight: 'bold' }}>
                                通用：{`${socCommonConfigInfo?.effectiveSoc || '-'}`}%
                            </div>
                            <a onClick={() => showCommonEvent(socCommonConfigInfo)}>
                                <FormOutlined />
                            </a>
                        </Space>
                    }
                    type="info"
                />
                <br />
                <Tabs onChange={onPageChange} activeKey={pageType}>
                    {tabOptions.map((ele) => (
                        <TabPane tab={ele.tab} key={ele.key} />
                    ))}
                </Tabs>
                <div>{pageShow()}</div>
            </Card>
            <br />
            <Card>
                <p className={styles['formTitle']}>
                    异常阈值
                    <Tooltip title="当某个充电枪/用户达到异常条件，生效枪责/人责阈值；若充电枪和用户同时异常，取最小阈值">
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                </p>
                <Space direction="vertical" style={{ marginLeft: '20px' }}>
                    {errorInfo?.map((ele) => {
                        return (
                            <Space key={ele.configType} direction="vertical">
                                <div style={{ display: 'flex' }}>
                                    <h4>{ele.title}</h4>
                                    <Switch
                                        checked={ele.closeFlag == '0'}
                                        style={{ marginLeft: '12px' }}
                                        onChange={() => {
                                            switchErrorEvent(
                                                ele.configType == '04'
                                                    ? socHumenConfigInfo
                                                    : socGunConfigInfo,
                                            );
                                        }}
                                        loading={submitLoading}
                                    />
                                </div>
                                {ele.closeFlag == '0'
                                    ? (ele.configType == '04' && (
                                          <p>
                                              近
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveDay === undefined
                                                      ? '-'
                                                      : ele.effectiveDay}
                                                  天
                                              </span>
                                              SOC超100%的订单达
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveNum === undefined
                                                      ? '-'
                                                      : ele.effectiveNum}
                                                  个
                                              </span>
                                              以上，该用户阈值设置为
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveSoc === undefined
                                                      ? '-'
                                                      : ele.effectiveSoc}
                                                  %
                                              </span>
                                              <a onClick={() => editErrorEvent(ele)}>
                                                  <FormOutlined />
                                              </a>
                                          </p>
                                      )) ||
                                      (ele.configType == '05' && (
                                          <p>
                                              近
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveDay === undefined
                                                      ? '-'
                                                      : ele.effectiveDay}
                                                  天
                                              </span>
                                              有
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveNum === undefined
                                                      ? '-'
                                                      : ele.effectiveNum}
                                                  个
                                              </span>
                                              以上不同用户，订单SOC超100%，该充电枪阈值设置为
                                              <span className={styles['primary-text']}>
                                                  {ele.effectiveSoc === undefined
                                                      ? '-'
                                                      : ele.effectiveSoc}
                                                  %
                                              </span>
                                              <a onClick={() => editErrorEvent(ele)}>
                                                  <FormOutlined />
                                              </a>
                                          </p>
                                      ))
                                    : null}
                            </Space>
                        );
                    })}
                </Space>
            </Card>
            <br />
            <Card>
                <p className={styles['formTitle']}>变动历史</p>
                <TablePro
                    name="history"
                    loading={historyListLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={socConfigHistoryList}
                    columns={historyColumns}
                    onChange={onHistoryTableChange}
                    pagination={{
                        current: historyPageInfo.pageIndex,
                        total: socConfigHistoryListTotal,
                        pageSize: historyPageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
            <Modal
                title="通用阈值"
                visible={commonVisible}
                onCancel={closeCommonEvent}
                okText="保存"
                onOk={onCommonFinish}
                maskClosable={false}
            >
                <Form form={commonForm}>
                    <FormItem name="operatorType" noStyle />
                    <FormItem name="configType" noStyle />
                    <FormItem name="configId" noStyle />

                    <FormItem label="阈值" required>
                        <Space>
                            <FormItem
                                name="effectiveSoc"
                                noStyle
                                rules={[{ required: true, message: '请填写' }]}
                            >
                                <InputNumber
                                    placeholder="请输入阈值，可填写范围为90~100"
                                    style={{ width: '280px' }}
                                    max={100}
                                    min={90}
                                    step={0.01}
                                    precision={2}
                                />
                            </FormItem>
                            <span>%</span>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>

            <Modal
                title={editItem ? '编辑阈值' : '新增阈值'}
                visible={addVisible}
                footer={false}
                onCancel={closeAddEvent}
                maskClosable={false}
            >
                <Form
                    {...formItemLayout}
                    form={modalForm}
                    onFinish={onAddFinish}
                    labelCol={{ span: 4 }}
                >
                    <FormItem name="operatorType" noStyle />
                    <FormItem name="configId" noStyle />

                    <FormItem label="类型" name="configType">
                        <Radio.Group disabled={editItem !== undefined}>
                            <Radio value={PAGE_TYPES.CITY}>城市</Radio>
                            <Radio value={PAGE_TYPES.OPER}>运营商</Radio>
                        </Radio.Group>
                    </FormItem>

                    <FormItem
                        noStyle
                        shouldUpdate={(prevValues, curValues) =>
                            prevValues.configType !== curValues.configType
                        }
                    >
                        {({ getFieldValue }) => {
                            const configType = getFieldValue('configType');
                            return (
                                (configType == PAGE_TYPES.OPER && (
                                    <OperSelectItem
                                        name="buildId"
                                        initRef={operatorSelectRef}
                                        operatorList={operatorList}
                                        rules={[{ required: true, message: '请选择' }]}
                                        required
                                        disabled={editItem !== undefined}
                                    />
                                )) || (
                                    <FormItem
                                        label="城市"
                                        name="city"
                                        {...formItemLayout}
                                        rules={[{ required: true, message: '请选择' }]}
                                    >
                                        <Select
                                            showSearch
                                            filterOption={(input, option) =>
                                                option.children
                                                    .toLowerCase()
                                                    .indexOf(input.toLowerCase()) >= 0
                                            }
                                            loading={cityLoading}
                                            allowClear
                                            showArrow
                                            placeholder={'请选择'}
                                            disabled={editItem !== undefined}
                                        >
                                            {cityData.map((ele) => {
                                                return (
                                                    <Option key={ele.value} value={ele.value}>
                                                        {ele.title}
                                                    </Option>
                                                );
                                            })}
                                        </Select>
                                    </FormItem>
                                )
                            );
                        }}
                    </FormItem>

                    <FormItem label="阈值" {...formItemLayout} required>
                        <Space>
                            <FormItem
                                name="effectiveSoc"
                                noStyle
                                rules={[{ required: true, message: '请填写' }]}
                            >
                                <InputNumber
                                    placeholder="请输入阈值，可填写范围为90~100"
                                    style={{ width: '370px' }}
                                    max={100}
                                    min={90}
                                    step={0.01}
                                    precision={2}
                                />
                            </FormItem>
                            <span>%</span>
                        </Space>
                    </FormItem>

                    <FormItem style={{ textAlign: 'center' }}>
                        <Space>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                提交
                            </Button>
                            <Button onClick={closeAddEvent}>取消</Button>
                        </Space>
                    </FormItem>
                </Form>
            </Modal>

            <Modal
                title={editItem?.title}
                visible={errorEditVisible}
                // footer={false}
                onCancel={closeErrorEvent}
                onOk={editFinishEvent}
                maskClosable
                width={720}
            >
                <Form form={editForm}>
                    <FormItem>
                        {(editItem?.configType == '04' && (
                            <>
                                近
                                <Form.Item
                                    noStyle
                                    name="effectiveDay"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value) {
                                                    return Promise.reject('请填写天数');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="天数"
                                        style={{ width: '60px', margin: '0 6px' }}
                                        max={30}
                                        min={1}
                                        precision={0}
                                    />
                                </Form.Item>
                                天SOC超100%的订单达
                                <Form.Item
                                    noStyle
                                    name="effectiveNum"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value) {
                                                    return Promise.reject('请填写个数');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="个数"
                                        style={{ width: '60px', margin: '0 6px' }}
                                        min={1}
                                        precision={0}
                                    />
                                </Form.Item>
                                个以上，该用户阈值设置为
                                <Form.Item
                                    noStyle
                                    name="effectiveSoc"
                                    rules={[
                                        ({ getFieldValue }) => ({
                                            validator(rule, value) {
                                                if (!value) {
                                                    return Promise.reject('请填写阈值');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    required
                                >
                                    <InputNumber
                                        placeholder="阈值"
                                        style={{ width: '80px', margin: '0 6px' }}
                                        min={90}
                                        max={100}
                                        step={0.01}
                                        precision={2}
                                    />
                                </Form.Item>
                                %
                            </>
                        )) ||
                            (editItem?.configType == '05' && (
                                <>
                                    近
                                    <Form.Item
                                        noStyle
                                        name="effectiveDay"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请填写天数');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        required
                                    >
                                        <InputNumber
                                            placeholder="天数"
                                            style={{ width: '60px', margin: '0 6px' }}
                                            max={30}
                                            min={1}
                                            precision={0}
                                        />
                                    </Form.Item>
                                    天有
                                    <Form.Item
                                        noStyle
                                        name="effectiveNum"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请填写用户数');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        required
                                    >
                                        <InputNumber
                                            placeholder="用户数"
                                            style={{ width: '80px', margin: '0 6px' }}
                                            min={1}
                                            precision={0}
                                        />
                                    </Form.Item>
                                    个以上不同用户，订单SOC超100%，该充电枪阈值设置为
                                    <Form.Item
                                        noStyle
                                        name="effectiveSoc"
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!value) {
                                                        return Promise.reject('请填写阈值');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                        required
                                    >
                                        <InputNumber
                                            placeholder="阈值"
                                            style={{ width: '80px', margin: '0 6px' }}
                                            min={90}
                                            max={100}
                                            step={0.01}
                                            precision={2}
                                        />
                                    </Form.Item>
                                    %
                                </>
                            ))}
                        <Form.Item>
                            <div style={{ color: 'gray', marginTop: '22px' }}>
                                注：1、天数不大于30；2、阈值范围可填写90~100；
                            </div>
                        </Form.Item>
                    </FormItem>
                </Form>
            </Modal>
        </PageHeaderWrapper>
    );
};

export default connect(({ user, financeModel, socModel, global, loading }) => ({
    financeModel,
    socModel,
    global,
    user,
    configLoading: loading.effects['socModel/getSocConfigInfo'],
    historyListLoading: loading.effects['socModel/getSocConfigHistoryList'],
}))(SupportPage);
