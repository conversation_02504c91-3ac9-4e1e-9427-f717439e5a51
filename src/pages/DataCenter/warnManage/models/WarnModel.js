import { equipAbnormalListApi, equipAbnormalBaseApi } from '@/services/DataCenter/WarnManageApi';

const WarnModel = {
    namespace: 'WarnModel',
    state: {
        equipAbnormalList: [], // 列表
        equipAbnormalTotal: 0, // 列表数量

        userEnums: undefined, // 枚举 状态、告警维度、告警类型、接单人
    },
    effects: {
        *equipAbnormal({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(equipAbnormalListApi, options);
                yield put({
                    type: 'updateUserProperty',
                    params: { equipAbnormalList: records, equipAbnormalTotal: total },
                });
            } catch (error) {}
        },
        *equipAbnormalInitEnum({ options }, { call, put, select }) {
            try {
                const { data } = yield call(equipAbnormalBaseApi, options);
                yield put({
                    type: 'updateUserProperty',
                    params: { userEnums: data },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateUserProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default WarnModel;
