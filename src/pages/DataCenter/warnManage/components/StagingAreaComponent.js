import { Card, Space, Dropdown, Alert, message, Badge, Spin } from 'antd';
import { DownOutlined, InfoCircleOutlined, SyncOutlined } from '@ant-design/icons';
import TablePro from '@/components/TablePro';
import styles from '@/assets/styles/common.less';
import { getFileCacheListApi, downloadFileCacheApi } from '@/services/CommonApi';
import React, { useEffect, useState, useImperativeHandle } from 'react';
import { useRef } from 'react';
import { throttle } from 'lodash';

const StagingAreaTable = (props) => {
    const { scene, initRef } = props;

    useImperativeHandle(initRef, () => ({
        destory,
    }));

    const [pageIndex, updatePageIndex] = useState(1);
    const [pageSize, updatePageSize] = useState(5);

    useEffect(() => {
        loadData();
    }, [pageIndex, pageSize]);

    const downLoadEvent = throttle(async (item) => {
        try {
            await downloadFileCacheApi({ id: item.attachFileId });
        } catch (error) {}
    }, 500);

    const [dataList, updateDataList] = useState([]);
    const [dataTotal, updateDataTotal] = useState(0);
    const [isWaiting, updateWaitingStatus] = useState(false);
    const [timer, setTimer] = useState();
    const loadData = async () => {
        try {
            updateWaitingStatus(true);
            updateDataList([]);
            updateDataTotal(0);

            const {
                data: { total, list },
            } = await getFileCacheListApi({ scene, pageIndex, pageSize });
            updateDataList([...list]);
            updateDataTotal(total);
        } catch (error) {
        } finally {
            updateWaitingStatus(false);
        }
    };

    const refresh = () => {
        if (pageIndex == 1) {
            loadData();
        } else {
            updatePageIndex(1);
        }
    };

    // useEffect(() => {
    //     let pending = false;
    //     // 当前列表是否有上传中文件，如果有，进入循环
    //     dataList.forEach((element) => {
    //         //  0=导出中 1=已完成 2=导出失败
    //         if (element.status == '0') {
    //             pending = true;
    //         }
    //     });
    //     if (pending) {
    //         // 如果状态为待生成，开启自动刷新功能，隔2秒刷新一次
    //         if (!timer) {
    //             const _timer = setInterval(() => {
    //                 loadData();
    //             }, 5000);
    //             setTimer(_timer);
    //         } else {
    //             // 如果已经在轮训的判断
    //         }
    //     } else {
    //         destory();
    //     }
    // }, [dataList]);

    const destory = () => {
        if (timer) {
            // 状态变更，需要结束轮训
            clearInterval(timer);
            setTimer(undefined);
        }
    };

    const onTableChange = (page, filters, sorter) => {
        updatePageIndex(page.current);
        updatePageSize(page.pageSize);
    };
    const columns = [
        {
            title: '导出人',
            width: 100,
            dataIndex: 'exportBy',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '导出日期',
            width: 200,
            dataIndex: 'exportTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 100,
            dataIndex: 'statusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 60,
            fixed: 'right',
            render(text, record) {
                let btnList = [];
                const downBtn = (
                    <span className={styles['table-btn']} onClick={() => downLoadEvent(record)}>
                        下载
                    </span>
                );
                if (record.status == 1) {
                    // 已完成才显示下载按钮
                    btnList.push(downBtn);
                }
                return btnList;
            },
        },
    ];
    return (
        <div style={{ width: '560px' }}>
            <Card>
                <Alert
                    message={
                        <Space>
                            文件保存7天后自动删除
                            <SyncOutlined
                                className={styles['table-btn']}
                                onClick={() => {
                                    refresh();
                                }}
                            />
                        </Space>
                    }
                    type="info"
                    showIcon
                    style={{ marginBottom: '12px' }}
                />

                <Spin spinning={isWaiting}>
                    <TablePro
                        name="area"
                        size="small"
                        scroll={{ x: 'max-content' }}
                        rowKey={(record, index) => record}
                        dataSource={dataList}
                        columns={columns}
                        onChange={onTableChange}
                        pagination={{
                            current: pageIndex,
                            total: dataTotal,
                            pageSize: pageSize,
                            showTotal: (total) => `共 ${total} 条`,
                        }}
                        noSort
                    />
                </Spin>
            </Card>
        </div>
    );
};

const AreaComponent = (props, ref) => {
    const [enableDownloadCount, updateEnableDownloadCount] = useState(0);
    const count = () => {
        updateEnableDownloadCount(enableDownloadCount + 1);
    };
    const clearCount = () => {
        updateEnableDownloadCount(0);
    };
    useImperativeHandle(ref, () => ({
        count, // 小标自动+1，一般不由外部主动调用
        clearCount, // 小标清空，一般不由外部主动调用
        apply: () => {
            // 外部处理完成导出行为后，调用此方法，进行页面更新
            message.success('提交成功，请到文件暂存区查看');
            count();
        },
    }));

    const areaRef = useRef();
    return (
        <Badge count={enableDownloadCount} offset={[-18, 0]} size="small">
            <Dropdown
                arrow
                trigger="click"
                overlay={<StagingAreaTable {...props} initRef={areaRef} />}
                onVisibleChange={(open) => {
                    if (open) {
                        clearCount();
                    } else {
                        areaRef?.current?.destory();
                    }
                }}
                destroyPopupOnHide
            >
                <a onClick={(e) => e.preventDefault()}>
                    <Space>
                        文件暂存区
                        <DownOutlined />
                    </Space>
                </a>
            </Dropdown>
        </Badge>
    );
};
export default React.forwardRef(AreaComponent);
