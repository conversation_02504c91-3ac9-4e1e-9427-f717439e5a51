// 数据中心-告警管理=设备异常
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    message,
    // Icon,
    Modal,
    InputNumber,
    Row,
    Select,
    // Alert,
    // Divider,
    Input,
    DatePicker,
    Tabs,
    Space,
    Tooltip,
} from 'antd';
import { connect, Link } from 'umi';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import React, { Fragment, useEffect, useState, useRef } from 'react';
import { exportTableByParams, copyTextCommon } from '@/utils/utils';
import { EQUIPAB_STATUS } from '@/config/declare';
import moment from 'moment';
import usePageState from '@/hooks/usePageState.js';
import styles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import AllStationSelect from '@/components/AllStationSelect';
import CitysSelect from '@/components/CitysSelect/index.js';
import CacheAreaView from '@/components/CacheAreaView';

import { equipAbnormalUpdateApi } from '@/services/DataCenter/WarnManageApi';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {
    labelCol: {
        flex: '0 0 95px',
    },
    labelAlign: 'left',
};

const SearchLayout = (props) => {
    const {
        location,
        listLoading,
        form,
        onSubmit,
        onReset,
        onExportForm,
        tabType,
        WarnModel: { equipAbnormalList, equipAbnormalTotal, userEnums },
    } = props;

    const {
        query: {},
    } = location;

    const stationRef = useRef();

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    const disabledDate = (current) => {
        // 告警时间、接单时间、解除时间三个必填一个，每个时间跨度不能超过60天
        return current && (current > moment() || current <= moment().subtract(60, 'days'));
    };

    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                eliminateFlag: '0',
                warnDates: [moment().subtract(30, 'days'), moment()],
            }}
            scrollToFirstError
        >
            <SearchOptionsBar
                loading={listLoading}
                onExportForm={onExportForm}
                onReset={resetForm}
                minSpan={24 * 3}
                exportName="导出至暂存区"
            >
                <Col span={8}>
                    <OperSelectTypeItem name="operatorId" form={form}></OperSelectTypeItem>
                </Col>
                <Col span={8}>
                    <AllStationSelect
                        form={form}
                        ref={stationRef}
                        label="场站名称"
                        name="stationId"
                    />
                </Col>
                <Col span={8}>
                    <CitysSelect
                        label="城市"
                        name="city"
                        placeholder="请选择"
                        // formItemLayout={{ labelAlign: 'right' }}
                        showArrow
                        provinceSelectable
                        rules={null}
                    />
                </Col>
                <Col span={8}>
                    <FormItem label="桩编号:" name="pileNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" allowClear />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="枪编号:" name="gunNo" {...formItemLayout}>
                        <Input placeholder="请填写" autoComplete="off" allowClear />
                    </FormItem>
                </Col>

                <Col span={8}>
                    <FormItem label="状态" name="eliminateFlag" {...formItemLayout}>
                        <Select placeholder="请选择">
                            {userEnums?.eliminateFlagList?.map((ele) => {
                                return (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="告警维度" name="equipWarnDimension" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {userEnums?.equipWarnDimensionList?.map((ele) => {
                                return (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="告警类型" name="equipWarnType" {...formItemLayout}>
                        <Select placeholder="请选择" allowClear>
                            {userEnums?.equipWarnTypeList?.map((ele) => {
                                return (
                                    <Option key={ele.codeValue} value={ele.codeValue}>
                                        {ele.codeName}
                                    </Option>
                                );
                            })}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem label="告警时间" name="warnDates" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" disabledDate={disabledDate} allowClear />
                    </FormItem>
                </Col>

                {tabType !== '0' && tabType !== '1' ? (
                    <Col span={8}>
                        <FormItem label="接单人" name="receivingPeople" {...formItemLayout}>
                            <Select
                                showSearch
                                placeholder="支持姓名、账号模糊搜索"
                                optionFilterProp="children"
                                fieldNames={{ label: 'codeName', value: 'codeValue' }}
                                filterOption={(input, option) => {
                                    let status =
                                        (option?.codeName ?? '').indexOf(input.toString()) > -1 ||
                                        (option?.codeValue ?? '').indexOf(input.toString()) > -1;
                                    return status;
                                }}
                                options={userEnums?.accountList}
                                allowClear
                            />
                        </FormItem>
                    </Col>
                ) : (
                    ''
                )}
                {tabType !== '1' ? (
                    <Col span={8}>
                        <FormItem label="接单时间" name="receiveDates" {...formItemLayout}>
                            <RangePicker
                                format="YYYY-MM-DD"
                                disabledDate={disabledDate}
                                allowClear
                            />
                        </FormItem>
                    </Col>
                ) : (
                    ''
                )}

                <Col span={8}>
                    <FormItem label="解除时间" name="relieveDates" {...formItemLayout}>
                        <RangePicker format="YYYY-MM-DD" disabledDate={disabledDate} allowClear />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};

const EquipABListPage = (props) => {
    const {
        dispatch,
        history,
        WarnModel: { equipAbnormalList, equipAbnormalTotal, userEnums },
        global: { pageInit },
        listLoading,
    } = props;

    const [selectItems, changeSelectItems] = useState([]); // 选中项
    const [copyStatus, setCopyStatus] = useState(false);

    const {
        location: { pathname },
    } = history;

    const [form] = Form.useForm();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: EQUIPAB_STATUS.ALL,
        },
        props,
    );

    useEffect(() => {
        if (!userEnums) {
            dispatch({
                type: 'WarnModel/equipAbnormalInitEnum',
            });
        }
        if (pageInit[pathname]) {
            form.setFieldsValue(pageInit[pathname].form);
        }
        return () => {
            dispatch({
                type: 'WarnModel/updateUserProperty',
                params: { equipAbnormalList: [], equipAbnormalTotal: 0 },
            });
        };
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const cacheRef = useRef();
    const searchData = async (isDownLoad) => {
        const data = form.getFieldsValue();
        if (!(data.warnDates || data.receiveDates || data.relieveDates)) {
            message.warning(`告警时间、接单时间、解除时间三个必填一个`);
            return;
        }

        const params = {
            ...data,
            city: data.city?.join?.(',') || data.city,
            warnStartTime: data.warnDates && `${data.warnDates[0].format('YYYY-MM-DD')} 00:00:00`,
            warnEndTime: data.warnDates && `${data.warnDates[1].format('YYYY-MM-DD')} 23:59:59`,
            receivingStartTime:
                data.receiveDates && `${data.receiveDates[0].format('YYYY-MM-DD')} 00:00:00`,
            receivingEndTime:
                data.receiveDates && `${data.receiveDates[1].format('YYYY-MM-DD')} 23:59:59`,
            relieveStartTime:
                data.relieveDates && `${data.relieveDates[0].format('YYYY-MM-DD')} 00:00:00`,
            relieveEndTime:
                data.relieveDates && `${data.relieveDates[1].format('YYYY-MM-DD')} 23:59:59`,
            warnDates: undefined,
            receiveDates: undefined,
            relieveDates: undefined,
            stationName: undefined,
        };

        if (pageInfo.tableMark !== EQUIPAB_STATUS.ALL) {
            params.tableMark = pageInfo.tabType;
        }

        if (isDownLoad) {
            // 下载
            cacheRef?.current?.apply(params).then(() => {
                cacheRef?.current?.count();
            });
        } else {
            (params.pageIndex = pageInfo.pageIndex || undefined),
                (params.pageSize = pageInfo.pageSize);

            dispatch({
                type: 'global/setPageInit',
                pathname,
                info: {
                    form: data,
                    state: pageInfo,
                },
            });

            // 查询
            dispatch({
                type: 'WarnModel/equipAbnormal',
                options: params,
            });
        }
        return;
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const rowSelection = {
        selectedRowKeys: selectItems.map((ele) => ele.warnInfoId),
        onChange: (selectedRowKeys, selectedRows) => {
            changeSelectItems(selectedRows);
        },
        getCheckboxProps: (record) => ({
            disabled: !(record.eliminateFlag === '0' && !record.receivingPeopleName),
            name: record.warnInfoId,
        }),
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = [
        {
            title: '运营商简称',
            dataIndex: 'operatorShortName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商编号',
            dataIndex: 'operatorId',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '场站',
            dataIndex: 'stationName',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            dataIndex: 'cityName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '告警条件',
            dataIndex: 'warnConditionDetail',
            width: 240,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '告警维度',
            dataIndex: 'dimensionName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '告警类型',
            dataIndex: 'warnTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '桩编号',
            dataIndex: 'pileNo',
            width: 160,
            render(text, record) {
                return (
                    <span className="text-line" title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '枪编号',
            dataIndex: 'gunNo',
            width: 160,
            render(text, record) {
                return (
                    <span className="text-line" title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '异常值',
            dataIndex: 'abnormal',
            width: 160,
            render(text, record) {
                return (
                    <div className="text-wrap" title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '关键参数',
            dataIndex: 'param',
            width: 400,
            render(text, record) {
                return (
                    <div>
                        <Tooltip
                            title={copyStatus ? '复制完成' : '点击复制'}
                            placement="right"
                            onVisibleChange={(visible) => {
                                if (visible) {
                                    // 还原状态
                                    setCopyStatus(false);
                                }
                            }}
                        >
                            <div
                                title={text}
                                onClick={() => {
                                    copyTextCommon(text);
                                    setCopyStatus(true);
                                }}
                                className="text-wrap"
                                style={{
                                    height: '50px',
                                    display: 'inline-block',
                                    overflowY: 'auto',
                                }}
                            >
                                {text}
                            </div>
                        </Tooltip>
                    </div>
                );
            },
        },
        {
            title: '解决方案',
            dataIndex: 'solution',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '告警次数',
            dataIndex: 'noticeNum',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '首次告警时间',
            dataIndex: 'dataTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '最后告警时间',
            dataIndex: 'lastNoticeTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            dataIndex: 'eliminateFlagName',
            width: 140,
            render(text, record) {
                return (
                    <span style={record.eliminateFlag === '0' ? { color: 'red' } : {}} title={text}>
                        {text}
                    </span>
                );
            },
        },
        {
            title: '解除时间',
            dataIndex: 'relieveTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '解除人',
            dataIndex: 'relievePeopleName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '接单时间',
            dataIndex: 'receivingTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '接单人',
            dataIndex: 'receivingPeopleName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 100,
            fixed: 'right',
            render: (text, record) => {
                let controlItems = [];
                if (record.eliminateFlag === '0') {
                    controlItems.push(
                        <span
                            className={styles['table-btn']}
                            onClick={() =>
                                receiveHandle(
                                    record,
                                    record.receivingPeopleName ? '0' : '1',
                                    record.receivingPeopleName ? '手动解除' : '接单',
                                )
                            }
                        >
                            {record.receivingPeopleName ? '手动解除' : '接单'}
                        </span>,
                    );
                }

                return <Space style={{ whiteSpace: 'nowrap' }}>{controlItems}</Space>;
            },
        },
    ];

    // 批量处理
    const batchHandle = () => {
        if (selectItems && selectItems.length > 0) {
            receiveHandle(selectItems, '2', '批量接单');
        } else {
            message.warning('请选择批量操作的数据');
            return;
        }
    };

    // 接单、解除 弹窗
    const receiveHandle = async (item, type, title) => {
        confirm({
            title: `确定${title}?`,
            icon: <ExclamationCircleOutlined />,
            content: '',
            okText: '确认',
            okType: 'primary',
            cancelText: '取消',
            onOk: async () => {
                try {
                    let params = {
                        warnInfoId: '',
                        type: type,
                    };
                    if (type === '2') {
                        params.warnInfoId = item.map((ele) => ele.warnInfoId)?.join?.(',');
                    } else if (selectItems) {
                        params.warnInfoId = item.warnInfoId;
                    }
                    await equipAbnormalUpdateApi(params);
                    searchData();
                    changeSelectItems([]);
                    message.success(`${title}成功`);
                    return;
                } catch (error) {
                    return Promise.reject(error);
                }
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    };
    return (
        <PageHeaderWrapper
            extra={<CacheAreaView bizType={'equipWarnListExport'} initRef={cacheRef} />}
        >
            <Card>
                <SearchLayout
                    form={form}
                    {...props}
                    tabType={pageInfo.tabType}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onExportForm={() => searchData(true)}
                    // onExportForm={exportFormEvent}
                    onReset={resetData}
                />
                {pageInfo?.tabType === '1' || pageInfo?.tabType === '3' ? (
                    <Row gutter={{ md: 8, lg: 24, xl: 48 }} style={{ marginBottom: '20px' }}>
                        <Col>
                            <Button type="primary" onClick={() => batchHandle()}>
                                批量接单
                            </Button>
                        </Col>
                    </Row>
                ) : null}
                <Tabs defaultActiveKey={pageInfo?.tabType || '3'} onChange={changeTabTypeEvent}>
                    <TabPane tab="全部" key={EQUIPAB_STATUS.ALL} />
                    <TabPane tab="待接单" key={EQUIPAB_STATUS.WAIT} />
                    <TabPane tab="已接单" key={EQUIPAB_STATUS.DONE} />
                    <TabPane tab="我负责的" key={EQUIPAB_STATUS.MY} />
                </Tabs>
                <TablePro
                    rowSelection={
                        equipAbnormalList?.length &&
                        (pageInfo?.tabType === '1' || pageInfo?.tabType === '3')
                            ? {
                                  type: 'checkbox',
                                  ...rowSelection,
                                  fixed: true,
                              }
                            : null
                    }
                    loading={listLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.warnInfoId}
                    dataSource={equipAbnormalList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: equipAbnormalTotal,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    tabType={pageInfo.tabType}
                />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ WarnModel, loading, global }) => ({
    WarnModel,
    listLoading: loading.effects['WarnModel/equipAbnormal'],
    global,
}))(EquipABListPage);
