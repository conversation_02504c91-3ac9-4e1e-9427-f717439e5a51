import { useState, useEffect, Fragment } from 'react';
import { Modal, Spin, Form, Row, Col, Checkbox, Input, Button, message, TimePicker } from 'antd';
import moment from 'moment';

const { RangePicker } = TimePicker;

const formItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 14,
    },
};

const DetailModal = (props) => {
    const {
        dispatch,
        modalVisible,
        detailInfo,
        interflow: { serviceInfosList },
        detailEditable,
        onOk,
    } = props;
    const [spinning, changeSpinning] = useState(false);
    const [checkboxGroupOptions, changeCheckboxGroupOptions] = useState([]);

    const [form] = Form.useForm();

    useEffect(() => {
        // 多选框数据初始值
        const serviceInfos = detailInfo.serviceInfos ? detailInfo.serviceInfos.split(',') : [];
        form.setFieldsValue({
            serviceInfos,
            siteGuide: detailInfo.siteGuide,
            parkPrice: detailInfo.parkfee,
            busiTime: detailInfo.busineHours,
        });
    }, [detailInfo]);

    useEffect(() => {
        // 多选框数据初始化

        changeCheckboxGroupOptions(
            serviceInfosList.map((item, index) => ({
                label: item.serviceInfosName,
                value: item.serviceInfos,
            })),
        );
    }, [serviceInfosList]);

    const closeModal = () => {
        onOk();
        changeSpinning(false);
    };

    const handleSubmit = (values) => {
        changeSpinning(true);
        dispatch({
            type: 'interflow/syncExamineInfo',
            payload: {
                examineStationDataId: detailInfo.examineStationDataId,
                serviceInfos: values.serviceInfos ? values.serviceInfos.toString() : '',
                siteGuide: values.siteGuide || '',
                busiTime: values.busiTime,
                parkPrice: values.parkPrice,
            },
            callback: () => {
                onOk();
                changeSpinning(false);
                message.success('发放成功！');
            },
        });
    };

    return (
        <Modal
            title="新充电站"
            width="700px"
            style={{ top: 120 }}
            footer={null}
            visible={modalVisible}
            onCancel={closeModal}
            destroyOnClose
            maskClosable={false}
        >
            <Spin spinning={spinning}>
                <Form form={form} onFinish={handleSubmit} initialValues={{}}>
                    <Form.Item label="运营商：" {...formItemLayout}>
                        <span>{detailInfo.operatorName}</span>
                    </Form.Item>
                    <Form.Item label="城市区域：" {...formItemLayout}>
                        <span>{detailInfo.areaName}</span>
                    </Form.Item>
                    <Form.Item label="站点名称：" {...formItemLayout}>
                        <span>{detailInfo.stationName}</span>
                    </Form.Item>
                    <Form.Item name="serviceInfos" label="服务内容：" {...formItemLayout}>
                        <Checkbox.Group
                            options={checkboxGroupOptions}
                            disabled={detailInfo.serviceInfos != '' || !detailEditable}
                        />
                    </Form.Item>
                    <Form.Item
                        name="siteGuide"
                        rules={[
                            {
                                required: true,
                                message: '请输入位置指引',
                            },
                        ]}
                        label="位置指引："
                        {...formItemLayout}
                    >
                        <Input
                            maxLength={50}
                            disabled={detailInfo.siteGuide != '' || !detailEditable}
                        />
                    </Form.Item>
                    <Form.Item
                        name="busiTime"
                        label="营业时间："
                        {...formItemLayout}
                        rules={[
                            {
                                required: true,
                                message: '请输入营业时间',
                            },
                        ]}
                    >
                        <Input disabled={detailInfo.busineHours != '' || !detailEditable} />
                    </Form.Item>
                    <Form.Item
                        name="parkPrice"
                        label="停车费描述："
                        {...formItemLayout}
                        rules={[
                            {
                                required: true,
                                message: '请输入停车费描述',
                            },
                        ]}
                    >
                        <Input
                            maxLength={50}
                            disabled={detailInfo.parkfee != '' || !detailEditable}
                        />
                    </Form.Item>
                    {detailEditable && (
                        <Row>
                            <Col span={6} offset={18}>
                                <Button type="primary" htmlType="submit">
                                    提交
                                </Button>
                                <Button onClick={onOk} style={{ marginLeft: '20px' }}>
                                    取消
                                </Button>
                            </Col>
                        </Row>
                    )}
                </Form>
            </Spin>
        </Modal>
    );
};

export default DetailModal;
