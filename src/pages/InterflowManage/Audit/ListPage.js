import { useState, useEffect, useRef, Fragment } from 'react';
import { connect } from 'umi';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
    Button,
    Card,
    Col,
    Form,
    Input,
    Row,
    Select,
    Alert,
    Modal,
    Checkbox,
    Calendar,
} from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import usePageState from '@/hooks/usePageState.js';
import moment from 'moment';
import DetailModal from './DetailModal';
import styles from '../style.less';
import TablePro from '@/components/TablePro';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = ({
    form,
    syncTime,
    operatorList,
    areaCodeList,
    inFlagList,
    onSubmit,
    onReset,
    openCalendar,
}) => {
    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();

        // 列表数据查询
        onReset();
    };

    const checkboxGroupOptions = inFlagList.map((item, index) => ({
        label: item.inFlagName,
        value: item.inFlag,
    }));
    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onFinish}
            initialValues={{
                inFlag: ['0'],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <FormItem label="管理单元:" name="operatorId" {...formItemLayout}>
                        <Select placeholder="请选择">
                            {operatorList.map((item, index) => (
                                <Option value={item.operatorId} key={item.operatorId}>
                                    {item.operatorName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
                <Col span={8}>
                    <OperSelectTypeItem name="operatorId" {...formItemLayout} form={form} />
                </Col>
                <Col>
                    <Button type="primary" htmlType="submit">
                        查询
                    </Button>
                    <Button className={styles.btnMargin} onClick={resetForm}>
                        重置
                    </Button>
                    <Button className={styles.btnMargin}>导出</Button>
                </Col>
            </Row>
        </Form>
    );
};
const ListPage = (props) => {
    const {
        dispatch,
        tableLoading,
        interflow: {
            auditList,
            auditListNum,
            operatorList,
            areaCodeList,
            inFlagList,
            calendarData,
        },
    } = props;
    const [form] = Form.useForm();

    const [calendarVisible, changeCalendarVisible] = useState(false);
    const [syncTime, changeSyncTime] = useState(moment().format('YYYY-MM-DD'));
    const [modalVisible, changeModalVisible] = useState(false);
    const [detailInfo, setDetailInfo] = useState({});
    const [isEditStatus, setEditStatus] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        dispatch({
            type: 'interflow/getProblemParam',
            payload: {},
        });
    }, []);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        const data = form.getFieldsValue();
        dispatch({
            type: 'interflow/queryExamine',
            payload: {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operatorId: data.operatorId || '',
                areaCode: data.areaCode || '',
                inFlag: (data.inFlag && data.inFlag.toString()) || '',
                syncTime: syncTime || '',
            },
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 200,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            width: 200,
            dataIndex: 'areaName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '站点名称',
            width: 200,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '获取方式',
            width: 120,
            dataIndex: 'accessMethodName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '设备数（个）',
            width: 140,
            dataIndex: 'equipmentNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '接口数（个）',
            width: 120,
            dataIndex: 'connectorNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '时间',
            width: 120,
            dataIndex: 'syncTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 120,
            dataIndex: 'oper',
            render: (text, record) => (
                <Fragment>
                    {record.inFlag == '0' ? (
                        <a onClick={() => showDetail(record, true)}>处理</a>
                    ) : null}
                    {record.inFlag == '1' ? (
                        <a onClick={() => showDetail(record, false)}>查看</a>
                    ) : null}
                </Fragment>
            ),
        },
    ];

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const openCalendar = (e) => {
        changeCalendarVisible(true);
    };
    // 日历弹出框相关 ----点击事件
    const onSelect = (dateValue) => {
        changeSyncTime(dateValue.format('YYYY-MM-DD'));
    };

    // 日期日期变化
    const onPanelChange = (dateValue) => {
        const date = dateValue.format('YYYY-MM-DD');
        // changeSyncTime(date)
        dispatch({
            type: 'interflow/queryExamineDayNum',
            payload: { syncTime: date },
        });
    };

    // 日历弹出框相关 ----日历面板自定义渲染
    const dateCellRender = (val) => {
        for (const item of calendarData) {
            if (val.format('YYYY-MM-DD') == item.syncTime) {
                return <div style={{ paddingLeft: '20px', paddingTop: '20px' }}>{item.num}条</div>;
            }
            return <div style={{ paddingLeft: '20px', paddingTop: '20px' }} />;
        }
    };

    // 显示当前详情
    const showDetail = (record, detailEditable) => {
        changeModalVisible(true);
        setDetailInfo(record);
        setEditStatus(detailEditable);
    };

    const handleOk = () => {
        changeModalVisible(false);
        changePageInfo({ pageIndex: 1 });
    };

    return (
        <PageHeaderWrapper>
            <Card>
                <SearchLayout
                    form={form}
                    syncTime={syncTime}
                    inFlagList={inFlagList}
                    areaCodeList={areaCodeList}
                    operatorList={operatorList}
                    onSubmit={() => {
                        changePageInfo((state) => ({
                            ...state,
                            pageIndex: 1,
                        }));
                    }}
                    onReset={resetData}
                    openCalendar={openCalendar}
                />
                <Alert
                    className={styles['total-bar']}
                    message={
                        <Fragment>
                            <span className={styles.btnMargin}>{syncTime}</span>
                            <span className={styles.btnMargin}>共{auditListNum}个站</span>
                        </Fragment>
                    }
                    type="info"
                    showIcon
                />
                <TablePro
                    loading={tableLoading}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.examineStationDataId}
                    dataSource={auditList}
                    columns={columns}
                    onChange={onTableChange}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: auditListNum,
                        pageSize: pageInfo.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Card>
            <DetailModal
                {...props}
                onOk={() => handleOk()}
                modalVisible={modalVisible}
                detailInfo={detailInfo}
                detailEditable={isEditStatus}
            />
            <Modal
                visible={calendarVisible}
                width="800px"
                footer={null}
                onCancel={() => changeCalendarVisible(false)}
                closable={false}
                maskClosable={false}
            >
                <Calendar
                    value={moment(syncTime)}
                    onSelect={onSelect}
                    onPanelChange={onPanelChange}
                    dateCellRender={dateCellRender}
                />
            </Modal>
        </PageHeaderWrapper>
    );
};
export default connect(({ interflow, loading }) => ({
    interflow,
    tableLoading: loading.effects['interflow/queryExamine'],
}))(ListPage);
