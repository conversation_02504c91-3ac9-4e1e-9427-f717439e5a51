import { Fragment, useState, useEffect, useRef } from 'react';
import {
    Button,
    Col,
    Form,
    Row,
    Select,
    DatePicker,
    message,
    Modal,
    Checkbox,
    Badge,
    Alert,
} from 'antd';
import moment from 'moment';
import styles from '../style.less';
import commonStyles from '@/assets/styles/common.less';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import { exportTableByParams } from '@/utils/utils';
import { queryProblemApiPath } from '@/services/InterflowMngApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const FormItem = Form.Item;
const { Option } = Select;
const { confirm } = Modal;

const formItemLayout = {};

const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm } = props;

    const resetEvent = () => {
        onReset();
    };
    return (
        <Form
            {...formItemLayout}
            form={form}
            onFinish={onSubmit}
            initialValues={{
                syncTime: moment(),
                accessMethod: ['1'],
            }}
            scrollToFirstError
        >
            <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
                <Col span={8}>
                    <OperSelectTypeItem name="operatorId" form={form} {...formItemLayout} />
                </Col>
                <Col span={8}>
                    <FormItem name="syncTime" label="选择日期:" {...formItemLayout} required>
                        <DatePicker />
                    </FormItem>
                </Col>
                <Col span={8}>
                    <FormItem name="accessMethod" label="获取方式:" {...formItemLayout}>
                        <Checkbox.Group
                            options={[
                                { label: '主动获取', value: '1' },
                                { label: '推送', value: '2' },
                            ]}
                        />
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col>
                    <Button type="primary" htmlType="submit">
                        查询
                    </Button>
                    <Button className={styles.btnMargin} onClick={resetEvent}>
                        重置
                    </Button>
                    <Button className={styles.btnMargin} onClick={onExportForm}>
                        导出
                    </Button>
                </Col>
            </Row>
        </Form>
    );
};

const modalColumns = [
    {
        title: '名称',
        width: 200,
        dataIndex: 'filedText',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '字段',
        width: 160,
        dataIndex: 'filedName',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '数值',
        width: 200,
        dataIndex: 'numValue',
        render(text, record) {
            return <span title={text}>{text}</span>;
        },
    },
    {
        title: '问题类别',
        width: 160,
        dataIndex: 'problemTypeName',
        render(text, record) {
            if (record.problemType == '00') {
                return (
                    <span title={text}>
                        <Badge status="success" />
                        {text}
                    </span>
                );
            }
            return (
                <span title={text}>
                    <Badge status="error" />
                    {text}
                </span>
            );
        },
    },
];

const TableSite = (props) => {
    const {
        dispatch,
        tableLoading,
        interflow: { problemDataList, problemDataListNum, problemDetailData },
    } = props;
    const [form] = Form.useForm();
    const [visible, changeVisible] = useState(false); // 控制Modal弹窗

    const [pageInfo, changePageInfo, onTableChange] = usePageState();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const searchData = () => {
        const data = form.getFieldsValue();
        if (!data.syncTime) {
            message.warn('请选择日期！');
            return;
        }
        dispatch({
            type: 'interflow/queryProblem',
            payload: {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                operatorId: data.operatorId || '',
                classType: 'station',
                syncTime: data.syncTime.format('YYYY-MM-DD') || '',
                accessMethod: data.accessMethod.toString() || '',
            },
        });
    };

    const resetData = () => {
        form.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const handleSearch = () => {
        changePageInfo({ pageIndex: 1 });
    };

    // 导出
    const exportFormEvent = () => {
        const data = form.getFieldsValue();
        const params = {
            operatorId: data.operatorId || '',
            classType: 'station',
            syncTime: data.syncTime.format('YYYY-MM-DD') || '',
            accessMethod: data.accessMethod.toString() || '',
        };
        const columnsStrs = [];
        for (const item of columns) {
            if (item.dataIndex) {
                columnsStrs.push({
                    key: item.dataIndex,
                    value: item.title,
                });
            }
        }
        exportTableByParams({
            methodUrl: queryProblemApiPath,
            options: params,
            columnsStr: columnsStrs,
        });
    };

    // 查看详情
    const showDetail = (id) => {
        changeVisible(true);
        dispatch({
            type: 'interflow/queryProblemDetail',
            payload: {
                classType: 'station',
                problemSummaryId: id,
            },
        });
    };

    const columns = [
        {
            title: '运营商',
            width: 180,
            dataIndex: 'operatorName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '站点名称',
            width: 220,
            dataIndex: 'stationName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '问题类型',
            width: 200,
            dataIndex: 'problemTypeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '获取方式',
            width: 120,
            dataIndex: 'accessMethodName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '时间',
            width: 140,
            dataIndex: 'syncTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 80,
            fixed: 'right',
            dataIndex: 'oper',
            render: (text, record) => (
                <Fragment>
                    <a onClick={() => showDetail(record.problemSummaryId)}>查看</a>
                </Fragment>
            ),
        },
    ];

    const searchTime = form.getFieldValue('syncTime');

    return (
        <Fragment>
            <SearchLayout
                form={form}
                {...props}
                onSubmit={handleSearch}
                onReset={resetData}
                onExportForm={exportFormEvent}
            />

            <Alert
                className={styles['total-bar']}
                message={
                    <Fragment>
                        <span className={styles.btnMargin}>
                            {searchTime && searchTime.format('YYYY-MM-DD')}
                        </span>
                        <span className={styles.btnMargin}>共{problemDataListNum}个站</span>
                    </Fragment>
                }
                type="info"
                showIcon
            />
            <TablePro
                name="site"
                loading={tableLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => record.problemSummaryId}
                dataSource={problemDataList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: problemDataListNum,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
            />
            <Modal
                title="查看站点"
                visible={visible}
                onCancel={() => {
                    changeVisible(false);
                }}
                footer={null}
                maskClosable={false}
                style={{ height: '400px' }}
                width={800}
            >
                <div className={commonStyles['btn-bar']}>
                    <Button className={commonStyles['btn-item']}>导出</Button>
                </div>
                <TablePro
                    name="station"
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.no}
                    dataSource={problemDetailData}
                    columns={modalColumns}
                    pagination={false}
                />
            </Modal>
        </Fragment>
    );
};

export default TableSite;
