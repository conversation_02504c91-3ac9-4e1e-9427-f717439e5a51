import { useState, useEffect } from 'react';
import { connect } from 'umi';
import { Card, Tabs } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import TableSite from './TableSite';
import TablePile from './TablePile';
import TableGun from './TableGun';

const { TabPane } = Tabs;

const ShowTableView = (props) => {
    const { tab } = props;

    switch (tab) {
        case '1':
            return <TableSite {...props} />;
            break;
        case '2':
            return <TablePile {...props} />;
            break;
        case '3':
            return <TableGun {...props} />;
            break;
        default:
            return null;
    }
};

const AuditList = (props) => {
    const [tab, changeTab] = useState('1');
    const { dispatch } = props;

    const callChange = (key) => {
        changeTab(key);
    };

    useEffect(() => {
        dispatch({
            type: 'interflow/getProblemParam',
            payload: {},
        });
    }, []);

    return (
        <PageHeaderWrapper>
            <Card>
                <Tabs defaultActiveKey="1" onChange={callChange}>
                    <TabPane tab="充电站" key="1" />
                    <TabPane tab="充电桩" key="2" />
                    <TabPane tab="充电枪" key="3" />
                </Tabs>
                <ShowTableView tab={tab} {...props} />
            </Card>
        </PageHeaderWrapper>
    );
};

export default connect(({ interflow, loading }) => ({
    interflow,
    tableLoading: loading.effects['interflow/queryProblem'],
}))(AuditList);
