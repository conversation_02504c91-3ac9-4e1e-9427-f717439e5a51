// 互联互通模块
import { message } from 'antd';
import {
    queryExamineApi,
    getProblemParamApi,
    queryExamineDayNumApi,
    queryProblemApi,
    queryProblemDetailApi,
    syncExamineInfoApi,
} from '@/services/InterflowMngApi.js';

export default {
    namespace: 'interflow',
    state: {
        // 新站审核---
        auditList: [], // 新站审核列表
        auditListNum: 0, // 新站审核列表记录数
        accessMethodList: [], // 获取方式下拉框
        operatorList: [], // 运营商下拉框
        areaCodeList: [], // 城市下拉框
        inFlagList: [], // 是否入档多选框
        calendarData: [], // 日历面板数据
        serviceInfosList: [], // 服务内容多选框
        // 异常数据---
        problemDataList: [], // 异常数据列表
        problemDataListNum: 0, // 异常数据列表记录数
        problemDetailData: [], // 异常数据详情明细
    },

    effects: {
        *queryExamine({ payload }, { call, put }) {
            try {
                const response = yield call(queryExamineApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        auditList: response.data.list || [],
                        auditListNum: response.data.total || 0,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *getProblemParam({ payload }, { call, put }) {
            try {
                const response = yield call(getProblemParamApi, payload);

                yield put({
                    type: 'setGeneralData',
                    payload: {
                        accessMethodList: response.data.accessMethodList || [],
                        operatorList: response.data.operatorList || [],
                        areaCodeList: response.data.AreaCodeList || [],
                        inFlagList: response.data.inFlagList || [],
                        serviceInfosList: response.data.serviceInfosList || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *queryExamineDayNum({ payload }, { call, put }) {
            try {
                const response = yield call(queryExamineDayNumApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        calendarData: response.data.list || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *queryProblem({ payload }, { call, put }) {
            try {
                const response = yield call(queryProblemApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        problemDataList: response.data.list || [],
                        problemDataListNum: response.data.total || 0,
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *queryProblemDetail({ payload }, { call, put }) {
            try {
                const response = yield call(queryProblemDetailApi, payload);
                yield put({
                    type: 'setGeneralData',
                    payload: {
                        problemDetailData: response.data.list || [],
                    },
                });
            } catch (error) {
                console.log(error);
            }
        },
        *syncExamineInfo({ payload, callback }, { call, put }) {
            try {
                const response = yield call(syncExamineInfoApi, payload);
                callback();
            } catch (error) {
                console.log(error);
            }
        },
        *changeCommonData({ payload }, { call, put }) {
            try {
                yield put({
                    type: 'setGeneralData',
                    payload: { ...payload },
                });
            } catch (error) {
                console.log(error);
            }
        },
    },

    reducers: {
        setGeneralData(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
    },
};
