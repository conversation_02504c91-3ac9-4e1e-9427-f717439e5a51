import { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { connect, useIntl, getLocale } from 'umi';
import CountUp from 'react-countup';

import styles from './Screen.less';
import CardWapper from './components/CardWapper';
import ShowItem from './components/ShowItem';
import RightShowItem from './components/RightShowItem';

import Echarts from 'echarts';

import power_show_icon from './images/power_show_icon.png';
import pile_show_icon from './images/pile_show_icon.png';
import dcstation_show_icon from './images/dcstation_show_icon.png';

import right_charge_icon from './images/right_charge_icon.png';
import right_car_icon from './images/right_car_icon.png';
import right_drives_icon from './images/right_drives_icon.png';

import {
    queryScreenOperEcologyTotalApi,
    queryScreenOperEcologyEnergyRecordApi,
} from '@/services/LarageScreen/storeApi';
import { isEmpty } from '@/utils/utils';

import { formatScreenLocalNumberInfo, formatScreenChargeNumberInfo } from './storeFormat';

const prefixInteger = (num, length) => {
    return (Array(length).join('0') + num).slice(-length);
};

let upHandle = null;

const RightWapper = (props) => {
    const intl = useIntl();

    const [totalInfo, updateTotalInfo] = useState();

    const [barData, updateBarData] = useState([]);

    const barChart = useRef();

    const totalNumRef = useRef();

    useEffect(() => {
        initTotalInfo();
        // initBarInfo();

        upHandle = setInterval(() => {
            // if (totalNumRef.current != totalInfo?.energyPq) {
            //     updateTotalInfo({
            //         ...totalInfo,
            //         energyPq: totalNumRef.current + Math.round(Math.random() * 5),
            //     });
            // }
            initTotalInfo();
        }, 1000 * 60 * 60);

        return () => {
            if (upHandle) {
                clearInterval(upHandle);
                upHandle = null;
            }
        };
    }, []);

    useEffect(() => {
        if (totalInfo && totalInfo.energyPq > 0) {
            totalNumRef.current = totalInfo.energyPq;
        }
    }, [totalInfo]);

    // useEffect(() => {
    //     if (barData instanceof Array) {
    //         setBarChartOption(barData);
    //     }
    // }, [barData]);

    const initBarInfo = async () => {
        try {
            const {
                data: { energyRecordList },
            } = await queryScreenOperEcologyEnergyRecordApi();
            updateBarData(energyRecordList);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const setBarChartOption = (info) => {
        if (!barChart.current) {
            initBarEchart();
        }
        // 绘制图表

        if (!info) {
            return;
        }

        const dataTime = [];
        const dataList = [];

        const curDate = new Date();
        const curMonth = curDate.getMonth() + 1;
        curDate.setMonth(curMonth, 0);
        const days = curDate.getDate();

        for (let index = 1; index <= days; index++) {
            dataTime.push(`${index}`);
        }

        dataTime.forEach((ele, index) => {
            let count = 0;
            for (const item of info) {
                if (item.dataDay == index) {
                    count += item.energyPq;
                }
            }
            dataList.push(count);
        });

        const option = {
            color: ['#2adecf'],
            textStyle: {
                color: 'rgb(222,222,222)',
            },

            grid: {
                left: '3%',
                right: '3%',
                bottom: '6%',
                top: '10%',
                containLabel: true,
            },
            animationDuration(idx) {
                // 越往后的数据延迟越大
                return idx * 200;
            },
            animationEasing: 'quarticOut',
            xAxis: [
                {
                    data: dataTime,
                    // axisLine: {
                    //     show: true
                    // },
                    axisLine: {
                        show: true,
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 0,
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                },
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '',
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                },
            ],
            series: [
                {
                    name: '',
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        // opacity: 0.5,
                        color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                            {
                                offset: 0,
                                color: 'rgba(0,125,221,1)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(0,166,255,1)',
                            },
                        ]),
                    },
                    itemStyle: {
                        normal: {
                            //   barBorderRadius: [3, 3, 0, 0],
                            color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                {
                                    offset: 0,
                                    color: 'rgba(0,125,221,1)',
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(0,166,255,1)',
                                },
                            ]),
                        },
                    },
                    data: dataList,
                },
            ],
        };
        barChart?.current?.setOption(option, true);
    };

    const initTotalInfo = async () => {
        try {
            const { data } = await queryScreenOperEcologyTotalApi();
            updateTotalInfo(data);

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const initBarEchart = () => {
        // 基于准备好的dom，初始化echarts实例
        barChart.current = new Echarts.init(document.getElementById('chart-bar'));
    };

    const numScrollList = useMemo(() => {
        if (!isEmpty(totalInfo?.agentPq)) {
            const prefixNum = prefixInteger(totalInfo?.agentPq, 8);

            return prefixNum?.split('')?.map((ele) => Number(ele));
        }
        return [0, 0, 0, 0, 0, 0, 0, 0];
    }, [totalInfo]);

    const renderNumScrollList = useMemo(() => {
        return numScrollList.map((ele, index) => {
            const listLen = numScrollList.length - index;
            return (
                <div key={index} className={styles['number-scroll-item']}>
                    <div className={styles['item-main']}>
                        <CountUp
                            className={styles['item-num']}
                            decimals={0}
                            start={0}
                            end={ele}
                            duration={4}
                        />
                        {/* {listLen % 3 == 1 && listLen > 3 && (
                            <span className={styles['item-tips']}>,</span>
                        )} */}
                    </div>
                </div>
            );
        });
    });

    return (
        <Fragment>
            <div className={styles['rightWapper']}>
                <CardWapper
                    title={intl.formatMessage({
                        id: 'screen.store.right.business',
                    })}
                >
                    <div className={styles['number-scroll-bar-title']}>
                        {intl.formatMessage({
                            id: 'screen.store.right.businessCharge',
                        })}
                        (
                        {intl.formatMessage({
                            id: 'screen.store.unit.kWh',
                        })}
                        )
                    </div>
                    <div className={styles['number-scroll-bar']}>{renderNumScrollList}</div>
                    <div className={styles['left-show-list']}>
                        <ShowItem
                            logo={dcstation_show_icon}
                            count={totalInfo?.agentOperStationNum || 0}
                            color={'blue'}
                            unit={intl.formatMessage({
                                id: 'screen.store.unit.one',
                            })}
                            name={intl.formatMessage({
                                id: 'screen.store.right.stationNum',
                            })}
                        ></ShowItem>

                        <ShowItem
                            logo={pile_show_icon}
                            count={totalInfo?.agentOperGunNum || 0}
                            color={'blue'}
                            unit={intl.formatMessage({
                                id: 'screen.store.unit.one',
                            })}
                            name={intl.formatMessage({
                                id: 'screen.store.right.pileNum',
                            })}
                        ></ShowItem>
                        <ShowItem
                            logo={power_show_icon}
                            count={totalInfo?.agentOperPower || 0}
                            color={'blue'}
                            unit={`${intl.formatMessage({
                                id: 'screen.store.unit.KW',
                            })}`}
                            name={intl.formatMessage({
                                id: 'screen.store.right.power',
                            })}
                        ></ShowItem>
                    </div>
                </CardWapper>

                <CardWapper
                    title={intl.formatMessage({
                        id: 'screen.store.right.fleet',
                    })}
                    // headerAfter={
                    //     <span className={styles['bar-tips']}>
                    //         {`${intl.formatMessage({
                    //             id: 'screen.store.middle.discharge',
                    //         })}
                    //     （${intl.formatMessage({
                    //         id: 'screen.store.unit.kWh',
                    //     })}
                    //     ）`}
                    //     </span>
                    // }
                >
                    <div className={styles['right-show-list']}>
                        <RightShowItem
                            logo={right_charge_icon}
                            count={formatScreenLocalNumberInfo(totalInfo?.companyPq || 0).count}
                            unit={formatScreenLocalNumberInfo(totalInfo?.companyPq || 0).unit}
                            name={`${intl.formatMessage({
                                id: 'screen.store.right.fleetCharge',
                            })} (${intl.formatMessage({
                                id: 'screen.store.unit.kWh',
                            })})`}
                        ></RightShowItem>
                        <RightShowItem
                            logo={right_car_icon}
                            count={totalInfo?.companyTeamNum || 0}
                            name={`${intl.formatMessage({
                                id: 'screen.store.right.companyTeamNum',
                            })} (${intl.formatMessage({
                                id: 'screen.store.unit.one',
                            })})`}
                        ></RightShowItem>
                        <RightShowItem
                            logo={right_drives_icon}
                            count={totalInfo?.companyDriverNum || 0}
                            name={`${intl.formatMessage({
                                id: 'screen.store.right.companyDriverNum',
                            })} (${intl.formatMessage({
                                id: 'screen.store.unit.one',
                            })})`}
                        ></RightShowItem>
                    </div>
                </CardWapper>
            </div>
        </Fragment>
    );
};
export default RightWapper;
