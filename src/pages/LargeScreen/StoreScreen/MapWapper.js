import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { connect, useIntl, getLocale } from 'umi';
import AMapLoader from '@amap/amap-jsapi-loader';

import CardWapper from './components/CardWapper';

import business_station_bar_img from './images/business_station_bar.png';
import store_station_bar_img from './images/store_station_bar.png';

import styles from './Screen.less';
import Echarts from 'echarts';

import { queryScreenOperEcologyMapApi } from '@/services/LarageScreen/storeApi';

const MAP_OPTION_TYPES = {
    STATION: '01', //储能站点
    FLEET: '02', //车队
    BUSINESS: '03', //运营业务
};

const MapWapper = (props) => {
    const intl = useIntl();

    const [option, changeOption] = useState({});

    const [lightType, changeLightType] = useState();

    const AMapJs = useRef();

    const chinaGeoRef = useRef(); //全国经纬度
    const subGeoRef = useRef(); //重点位置

    const mapWapperRef = useRef();

    const [stationMapList, updateStationMapList] = useState([]);

    const initStationInfo = async () => {
        try {
            const {
                data: { ScreenOperEcologyEnergyStationList },
            } = await queryScreenOperEcologyMapApi();
            updateStationMapList(ScreenOperEcologyEnergyStationList);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        initMapEchart();
        loadMap().then(async (data) => {
            const fulldata = await initGeoJson(440300);
            // const fulldata = await initGeoJson(100000);
            // const gdSubData = await initGeoJson(440000);
            // const gzSubData = await initGeoJson(440100);
            // const szSubData = await initGeoJson(440300);
            // const dgSubData = await initGeoJson(441900);
            // const hzSubData = await initGeoJson(441300);

            chinaGeoRef.current = fulldata;

            // subGeoRef.current = {
            //     subFeatures: [
            //         ...gzSubData.subFeatures,
            //         ...szSubData.subFeatures,
            //         ...dgSubData.subFeatures,
            //         ...hzSubData.subFeatures,
            //     ],
            //     parentFeatures: [
            //         ...gdSubData.subFeatures,
            //         ...gzSubData.parentFeatures,
            //         ...szSubData.parentFeatures,
            //         ...dgSubData.parentFeatures,
            //         ...hzSubData.parentFeatures,
            //     ],
            // };
            setMapChartOption();

            initStationInfo();

            // let lightIndex = 0;
            // setInterval(() => {
            //     lightIndex++;
            //     if (lightIndex > 3) {
            //         lightIndex = 0;
            //     }
            //     for (let index = 0; index < 4; index++) {
            //         mapWapperRef.current?.dispatchAction({
            //             type: 'downplay',
            //             seriesIndex: 0,
            //             dataIndex: index,
            //         });
            //     }

            //     mapWapperRef.current?.dispatchAction({
            //         type: 'highlight',
            //         seriesIndex: 0,
            //         dataIndex: lightIndex,
            //     });
            // }, 5000);
        });
    }, []);

    const loadMap = async (code) => {
        window._AMapSecurityConfig = {
            securityJsCode: 'f1d96ff30ff169db2e5bf7c5f683113f',
        };
        return new Promise((resolve, reject) => {
            AMapLoader.load({
                key: 'f0ad238a3a4b229bcdd93c77278f6148', //需要设置您申请的key
                version: '2.0',
                plugins: ['AMap.DistrictSearch'],
                AMapUI: {
                    version: '1.1',
                    plugins: [],
                },
                Loca: {
                    version: '2.0.0',
                },
            })
                .then((AMap) => {
                    AMapJs.current = AMap;
                    resolve(AMap);
                })
                .catch((e) => {
                    reject(e);
                });
        });
    };
    // 初始化地图数据
    const initGeoJson = async (adcode) => {
        try {
            const data = await getGeoJson(adcode);
            let subFeatures = data.subFeatures;
            let parentFeatures = data.parentFeatures;
            return {
                subFeatures,
                parentFeatures,
            };
        } catch (error) {
            return {
                subFeatures: [],
                parentFeatures: [],
            };
        }
    };

    // 获取地图数据
    const getGeoJson = (adcode, childAdcode = '') => {
        return new Promise((resolve, reject) => {
            function insideFun(adcode, childAdcode) {
                AMapUI?.loadUI(['geo/DistrictExplorer'], (DistrictExplorer) => {
                    let districtExplorer = new DistrictExplorer();
                    districtExplorer.loadAreaNode(adcode, async (error, areaNode) => {
                        if (error) {
                            console.error(error);
                            reject(error);
                            return;
                        }
                        let Json = areaNode.getSubFeatures(); // 返回该区域中全部的子级区划Feature数组
                        let parentJson = areaNode.getParentFeature(); // 返回父级区划对应Feature
                        if (Json.length === 0) {
                            let parent = areaNode._data.geoData.parent.properties.acroutes;
                            const { subFeatures = [] } = await getGeoJson(
                                parent[parent.length - 1],
                                adcode,
                            );
                            Json = subFeatures;
                        }

                        if (childAdcode) {
                            Json = Json.filter((item) => {
                                return item.properties.adcode == childAdcode;
                            });
                        }
                        let mapJson = {
                            subFeatures: Json,
                            parentFeatures: [parentJson],
                        };
                        resolve(mapJson);
                    });
                });
            }
            insideFun(adcode, childAdcode);
        });
    };

    useEffect(() => {
        if (chinaGeoRef?.current) {
            setMapChartOption();
        }
    }, [stationMapList, chinaGeoRef, subGeoRef, lightType]);

    const setMapChartOption = () => {
        if (!mapWapperRef.current) {
            initMapEchart();
        }

        let allFeatures = [];
        if (chinaGeoRef?.current?.parentFeatures instanceof Array) {
            allFeatures = allFeatures.concat(chinaGeoRef?.current?.parentFeatures);
        }
        if (chinaGeoRef?.current?.subFeatures instanceof Array) {
            allFeatures = allFeatures.concat(chinaGeoRef?.current?.subFeatures);
        }
        // if (subGeoRef?.current?.parentFeatures instanceof Array) {
        //     allFeatures = allFeatures.concat(subGeoRef?.current?.parentFeatures);
        // }
        // if (subGeoRef?.current?.subFeatures instanceof Array) {
        //     allFeatures = allFeatures.concat(subGeoRef?.current?.subFeatures);
        // }

        //全国的时候才显示南海诸岛  只有当注册的名字为china的时候才会显示南海诸岛
        new Echarts.registerMap('map', { features: allFeatures });

        const zoom = 1.1; //1.1;
        const center = [114.15, 22.65]; //[114.42, 23.11]; //[113.280714, 23.125624]; //[114.06, 22.54];

        // const stationDatas = stationMapList?.map((ele) => {
        //     return {
        //         value: [Number(ele.lon), Number(ele.lat), 1],
        //         ...ele,
        //     };
        // });

        const storeStationDatas = stationMapList
            ?.filter((ele) => ele.type == 1)
            ?.map((ele) => {
                return {
                    value: [Number(ele.lon), Number(ele.lat), 1],
                    ...ele,
                };
            });
        const businessStationDatas = stationMapList
            ?.filter((ele) => ele.type == 2)
            ?.map((ele) => {
                return {
                    value: [Number(ele.lon), Number(ele.lat), 1],
                    ...ele,
                };
            });

        // const cityLightDatas = [
        //     {
        //         value: [113.26, 23.13, 1],
        //         name: '广州',
        //     },
        //     {
        //         value: [113.75, 23.02, 1],
        //         name: '东莞',
        //     },
        //     {
        //         value: [114.06, 22.54, 1],
        //         name: '深圳',
        //     },
        //     {
        //         value: [114.42, 23.11, 1],
        //         name: '惠州',
        //     },
        // ];
        // const cityDatas = [
        //     {
        //         value: [115.38, 22.79, 1],
        //         name: '汕尾',
        //     },
        //     {
        //         value: [116.37, 23.55, 1],
        //         name: '揭阳',
        //     },
        //     {
        //         value: [116.68, 23.35, 1],
        //         name: '汕头',
        //     },
        //     {
        //         value: [116.62, 23.66, 1],
        //         name: '潮州',
        //     },
        //     {
        //         value: [116.12, 24.29, 1],
        //         name: '梅州',
        //     },
        //     {
        //         value: [114.7, 23.74, 1],
        //         name: '河源',
        //     },
        //     {
        //         value: [113.6, 24.81, 1],
        //         name: '韶关',
        //     },

        //     {
        //         value: [113.06, 23.68, 1],
        //         name: '清远',
        //     },

        //     {
        //         value: [113.12, 23.02, 1],
        //         name: '佛山',
        //     },
        //     {
        //         value: [116.39, 39.91, 1],
        //         name: '中山',
        //     },
        //     {
        //         value: [112.47, 23.05, 1],
        //         name: '肇庆',
        //     },
        //     {
        //         value: [113.58, 22.27, 1],
        //         name: '珠海',
        //     },
        //     {
        //         value: [113.08, 22.58, 1],
        //         name: '江门',
        //     },
        //     {
        //         value: [112.04, 22.92, 1],
        //         name: '云浮',
        //     },
        //     {
        //         value: [111.98, 21.86, 1],
        //         name: '阳江',
        //     },

        //     {
        //         value: [110.93, 21.66, 1],
        //         name: '茂名',
        //     },
        //     {
        //         value: [110.36, 21.27, 1],
        //         name: '湛江',
        //     },
        // ];

        let mapOption = {
            backgroundColor: 'transparent',

            title: [],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            grid: {
                left: '5%',
                right: '5%',
                bottom: '0%',
                top: '5%',
                containLabel: true,
            },
            geo: {
                map: 'map',
                zoom,
                center,
                roam: false,
                // left: '10%',
                top: '10%',
                select: {
                    disabled: true,
                },
                emphasis: {
                    disabled: true,
                },

                label: {
                    normal: {
                        show: false,
                        color: 'rgb(249, 249, 249)', //省份标签字体颜色
                        formatter: (p) => {
                            switch (p.name) {
                                case '内蒙古自治区':
                                    p.name = '内蒙古';
                                    break;
                                case '西藏自治区':
                                    p.name = '西藏';
                                    break;
                                case '新疆维吾尔自治区':
                                    p.name = '新疆';
                                    break;
                                case '宁夏回族自治区':
                                    p.name = '宁夏';
                                    break;
                                case '广西壮族自治区':
                                    p.name = '广西';
                                    break;
                                case '香港特别行政区':
                                    p.name = '香港';
                                    break;
                                case '澳门特别行政区':
                                    p.name = '澳门';
                                    break;
                            }
                            return p.name;
                        },
                    },
                    emphasis: {
                        show: false,
                        // color: '#fff',
                    },
                },
                itemStyle: {
                    normal: {
                        areaColor: 'rgba(0,7,12)',
                        borderColor: '#98CEDB',
                        borderWidth: 2,
                        // shadowBlur: 15,
                        // shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        //     {
                        //         offset: 0,
                        //         color: 'rgba(0,226,255,0)',
                        //     },
                        //     {
                        //         offset: 1,
                        //         color: 'rgba(0,226,255,0.3)',
                        //     },
                        // ]),
                        // shadowOffsetX: 5,
                        // shadowOffsetY: 5,
                    },
                    emphasis: {
                        areaColor: 'rgba(0,7,12)',
                        borderColor: '#98CEDB',
                        borderWidth: 2,
                    },
                },
                regions: [
                    {
                        name: '深圳市',
                        // itemStyle: {
                        //     areaColor: 'rgba(0,7,12,0)',
                        //     borderColor: 'rgba(190,19,95,0.5)',
                        //     borderWidth: 5,
                        // },
                        itemStyle: {
                            normal: {
                                areaColor: 'rgba(0,7,12,0)',
                                borderColor: '#CAEAEF',
                                borderWidth: 20,
                                shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: 'rgba(0,226,255,0)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(0,226,255,0.3)',
                                    },
                                ]),
                                shadowOffsetX: 10,
                                shadowOffsetY: 50,
                                shadowBlur: 20,
                            },
                            emphasis: {
                                disabled: true,
                                areaColor: 'rgba(0,7,12,0)',
                                borderColor: '#CAEAEF',
                                borderWidth: 10,
                                focus: 'self',
                            },
                        },
                    },
                    // {
                    //     name: '广东省',
                    //     itemStyle: {
                    //         areaColor: 'rgba(5,25,53,0.8)',
                    //         borderColor: '#8BB3CB',
                    //         borderWidth: 5,
                    //         shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    //             {
                    //                 offset: 0,
                    //                 color: 'rgba(0,226,255,0)',
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,226,255,0.3)',
                    //             },
                    //         ]),
                    //         shadowOffsetX: 30,
                    //         shadowOffsetY: 30,
                    //         shadowBlur: 40,
                    //     },
                    // },
                    // {
                    //     name: '深圳市',
                    //     itemStyle: {
                    //         areaColor: '#ad7700',
                    //         borderColor: '#91B7CE',
                    //         borderWidth: 5,
                    //         shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    //             {
                    //                 offset: 0,
                    //                 color: 'rgba(0,226,255,0)',
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,226,255,0.3)',
                    //             },
                    //         ]),
                    //         shadowOffsetX: 5,
                    //         shadowOffsetY: 5,
                    //         shadowBlur: 10,
                    //     },
                    // },
                    // {
                    //     name: '东莞市',
                    //     itemStyle: {
                    //         areaColor: '#ad7700',
                    //         borderColor: '#91B7CE',
                    //         borderWidth: 5,
                    //         shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    //             {
                    //                 offset: 0,
                    //                 color: 'rgba(0,226,255,0)',
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,226,255,0.3)',
                    //             },
                    //         ]),
                    //         shadowOffsetX: 5,
                    //         shadowOffsetY: 5,
                    //         shadowBlur: 10,
                    //     },
                    // },
                    // {
                    //     name: '惠州市',
                    //     itemStyle: {
                    //         areaColor: '#ad7700',
                    //         borderColor: '#91B7CE',
                    //         borderWidth: 5,
                    //         shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    //             {
                    //                 offset: 0,
                    //                 color: 'rgba(0,226,255,0)',
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,226,255,0.3)',
                    //             },
                    //         ]),
                    //         shadowOffsetX: 5,
                    //         shadowOffsetY: 5,
                    //         shadowBlur: 10,
                    //     },
                    // },
                    // {
                    //     name: '广州市',
                    //     itemStyle: {
                    //         areaColor: '#ad7700',
                    //         borderColor: '#91B7CE',
                    //         borderWidth: 5,
                    //         shadowColor: new Echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    //             {
                    //                 offset: 0,
                    //                 color: 'rgba(0,226,255,0)',
                    //             },
                    //             {
                    //                 offset: 1,
                    //                 color: 'rgba(0,226,255,0.3)',
                    //             },
                    //         ]),
                    //         shadowOffsetX: 5,
                    //         shadowOffsetY: 5,
                    //         shadowBlur: 10,
                    //     },
                    // },
                ],
            },
            series: [
                // {
                //     name: '高亮城市',
                //     type: 'scatter',
                //     coordinateSystem: 'geo',
                //     data: cityLightDatas,
                //     zlevel: 2,
                //     symbol: 'image://data:image/png;base64,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',
                //     symbolSize: [185, 213],
                //     symbolOffset: [0, -80],
                //     label: {
                //         show: true,
                //         formatter: '{b}',
                //         position: 'top',
                //         fontSize: 44,
                //         color: '#fff',
                //         offset: [0, 70],
                //         fontWeight: 'bold',
                //         textShadowColor: '#845100',
                //         textShadowBlur: 10,
                //         textShadowOffsetX: 5,
                //         textShadowOffsetY: 5,
                //     },
                //     // showEffectOn: 'render',
                //     // rippleEffect: {
                //     //     brushType: 'stroke',
                //     //     number: 1,
                //     //     period: 5,
                //     //     scale: 1.3,
                //     // },
                // },
                // {
                //     name: '普通城市',
                //     type: 'scatter',
                //     coordinateSystem: 'geo',
                //     data: cityDatas,
                //     zlevel: 1,
                //     symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALgAAACqCAYAAAAeLYVXAAAACXBIWXMAAAsTAAALEwEAmpwYAAA88ElEQVR4nO29ebQkR3Xn/4nMqrf2ol6kbgntbolFEpIskIAWaAZhgwBjA2YTjGc8nDO2f3jMIsNhGQwez4AxCM8BbMx6zNjYYLCwUKMeQCAWIZCwhCTQipbuVqvVe6u731ZL5v39kZlVkVE3IrOeWuj1a91zqioy4saNGxHfe+NmZFam4epNY8D7EAo6gKELgAAGAToIU4Bgci5BMBxEcl5yXqGDYTrnKPJBOAgkFh9AB5jJW7J5p6Ekl/x4xtKr/BsiH49PRr+P5bRbB6XMzquSH5KrtaH1Q6vn9sHlqzNeWl2tTKvr9jV07Kvv66tLFXo0EEYxvBnDn+ewW4oQY3IwZ3kjGCaQ3rHkwpdgiEt50ATGesZRlBmWIEQWuFOgAYxZqkqeP9lT0ZDm+THCWI9PSPL0NJmhZLySGyS5kWUGWvDOkBlK2pNjcsPJ2gEhyXWfyeullm5JzluMQ5rLn+u1ISXeuVymWDJaue7FGJDztnucptfHDpDkE2uPW3tgiiXvl7FaK5wTA9xdCmelgbjgc42tyjD68vVj4/xqbWvGUPz6dNX0zMsbeUYb+JDXiwh6J12LqtOZkFUK4Q66+RlNALGT18DkRlbuxzgQOatQYThlLbO8qAefDCxRycjolY324GJ6cg0wXjLyrHyZ1ZINxUbet5S+U2jkmqe53MIgGpQNWjBEPc37jgIgBtK8bsFb9CjtyckMKQGSngzJ821Dz/ROcsdgj3Cat5P08vr69ev2HUlq9asY334fpaRrf7z7DlIGjLmMF8GQD2pB7rJaUJ1lRltO3bS21MGgLJdXW677NDOQp+nneidXh6owRtNTM3BfqKCR1ifNi7qyNKP36aY5Le0YTG7srpyox20BByFWWjY9rjLYivp9cBYyi+NyvvSkiVPbltwPmNNSj6z+NQYGS/v1eWHfAFfx14kHXV5tAn0rS0i2D7AaiFweTcfQMq/1w5XtGoDPCYT6qI2nLUPTcXC+BOgG++OTpf26fXJ11uq7pGHPJc1JWHlRr0hTqirPDUO0co3HBzzt15YT8oYubx3DqVvPnbjQBGp1fWWuTkVaq+MC1s4LGaMLZt/qXAUg16GEnKImuwrcIaxpztPuj29VEnpxmxmYQE0peyA0wIqSruv1NUtWFPYakCtTCxWqjMwu8/UP9MG029Tad/Vz23Tb19I+Gcb5oPziKQ/paufV6bNWL+RhtTF2+UPjp7XtjEURg4vaGBC9/JJJYCQ/TNIrNh7wDojrDTSQ1fEUoc64INes30d1vKg28T5+bYnVnINmuFWeOtQeTjq0vPvGy21f0zs0tr651fRy+x/Syc7zyQ2RM84NL3D69GngdcVB9IpLbDHF7z4G/fUM0HL4WvT3vW3eRxSZB+nvhRd5s2Rbb3Y3ujmv28UD9Lf4Cpqhv8VW5Bd78W6/Digyp8HaJcioTbEd2Kck53VpOr1iYzrgFUPLtTaxoTxfOBUCjJtXtWqEDLOKz+Wv45x8IPeNmSV3cBdFYfKoZtNRSpmWZw+DuwCJku/ya3W1fI1nGL66ZUV+sSWXVsmJXnGJmzdH3xEU9Ttkxuy2c1DRZxYGtuza+aeQR84zax0X9Wccvcn1cWUmDDoswTBjzaJd33UEXYrrFX1KFZkw6MQAOukVG8sX/1wO5bjhBbXUyPVTyGZd23WBHjq1sCesqj1tofXV98kIlbs+MFXy7LZ9hjlKFgK6ZUc5dQRYo8hxjarI09J1DNenq54nqg6aYwnJcX/VOnn0YPN26RuInV8YyBcbNaHrgs1VQOPx1XMXwirS2zb0d1V1vvLeaL9tDeQhAwgtkPbHt4C6YdJgX/wR5rBG5+b52g2VuWMwnzZcmdp8+85MtDJNjpBdxJpQ9Cku3MXRgBjX5/WFhzqg8dQxCh+5Xn4wX4IyfGUaEN0ym6oixKpT40Jm1amrO7E+/X37BlW6aXsTPj188vo6GhWcdjo0HppD9JWFDDR09tg77m8TusV++NRRwlXEVaJq0rWyuqtGljbe9jRdQuQDXGiPwuWp8tRhPfolVV7N5hYn7XMaVTQIKFHzfTraee4c+er4ZBe8tftQ3JfRb8Av2i2pUqIqz2fZbnv2seuV/J5CD1/cOj5d3fbrGIHG5+bX85Buu4MaamPhyrGPXQP3kWYwodVQK9e8u67vIOqqZPvkex1eFGTtl2kArAozfHxVgPINbkiOBlytfigW1Aa5bhgUCi3c2FKr7+ZlZxhhww8t9xq/fVwV89ddQTXSzbFOfVMygjpUGVJGXtsuQ0abmGE9YR0A2ny+5V7Tydd+aBXQZGpgGsZ7+7y+C6py3wZbsMOA0KrgG9uQt64yXB9/KMQMz324tX4fyg7VJ1tzwV7nFOVJbYirFAuFFJqHCHkHd7CqJqDOBIXi3ZAX1+Ll0AT6jMRnvIOy/L0JrTh1dJtPuS/E0BxQSK5N7ti6ZcVv3RXE5XEdR68sGhClL6ia5/T5fbshTaFQJ20tinxffFUFKo1cbzRsv4aRbeeHjE2TY5yPrUfYWPoyqvJCoY+vD75jH9ljoslw0yGZvvFweUpOIVKbsVnLwkPLlettqgBZZQBah1zyB1X+NjQP5fOUmqG57buA9JFxeEMrlVSUQ3mc6nhTn27auLvtVIUjPqDZZaEQs0646eqoefyBPkT5oZSqafY2CNC6gHXLhx2sUFmdONAmbRJ93sgFmcajGUxV225dTaav3Od1qwBWJ+zzGbNvPnzOzDV6rQ1fns+JhOoF+xZ5h2iwuSoPNQyFJsRuvWpVqKK6k+XqULevVV7UNgBtRagbtoTa18IzNyzSlvaQ8dT1qDZ/ldf1zWfIiWjhorZqayg20D/JHIyW/Iq6wu0OhJajquXW5w1Dnj8ESMPgAITI1l8Lxez2XN3ctE++plvdOlU0rPOpCstsvjqet9pFDtarGgdfZBCKJkp55W3CUJQ02KAWf2pgd6VpLfiW0VCY4+riynb11n7t+trAa3GkKzfkiX1xoq+O26+6oPW1X55RM9CGrz+ujNCx1qZv7HwrtZb2UZV+pbziUr2uUl9UKA7VyAWmBuqqjtX1CHXKfDKrDKKu7KoVypVRJz6t2x+frMFVSHrfdUO1gt8NhUL13XbdEMVXp2jbp0vIUXhXnahXxQ0SylXc+FFTxqdwyMtqsjQDqFtWd8n3hRZVulWFaRqF6pd1M6XyUF/1+vXzXcC6VCckqeLXynxz5OtraDULhTe9OYkGsvx2Gmrcp7CW5y5ZtqzQklk1UFXgCA2Ebtb1g7cq4woFgn2ewQtuIZ2Kcl9+CACavLog1Y6rxlnj8zkLn4yQg3RR63hw7U/H5WkIDaSmrF2uGUaVV7HJ1ia03NUFh69e1aBqq1Eo7ZPhcwzi8IR0cnWparcO+fh9BukL46ocX52VJgR+8BvcwPhG3tCkrKYG6rpLqGsuvmVdA7J2bFNVvp32hRxujOnqFA4rfCPm56szbnVDrYLXN+FVc1MX/DZvyEjrUtWKVrVChI3Eqh1Z2X2WQVsOTZAvrHCl1hmQuvHgfOrViZV95T5Dc71KtR660whNaBWFvGvd8MQ+HtTFlHir+ljH2bjlVf2vAnUZbxZn/8E/7sJX7qrdQNXg+WK/Ot4kFDfWzXPbcydG836hMEUbETdUCk1iOezQ/4nkrqM+fbU6IXJXGJtco0U5Dmvgb8cdY18dX7m2ylat3moIGeGjQZvweSvfhFWFBjZp4Ylbt66BufJsXg1EWroOaYbhM06XR6NQKOaTpfH6yrS0D1xVIVBIn1C+Lxh2qU6ez4mW8otL9abU3KC6bqfdeDrs+cyAgiGvqsmoynPJtXpf2h1wHyB8nrquQdTlqwvCKudS1KtjvJpxhtr0HdvyfN68Tv2QbtqcaPw9Kk4ypVel+K0DI79XLVupqI3XDQ18S5Tdrpu2dbDLNWCEZFcZsuaFfDFqnQkthzNhGsaz1pvNvg7auNWV5QO1tjLYdcA/jpqDcWWr7evbhK74QSHasQsg3xKkdcIXxtSV49Z1l0GXXG8+X0/jW9V8OlXJs3UK8YZnqp+vASo0nr4x1YzOFxO79cKxgS7Lt7LWcTClvMGny+q+Suu8r8O+ZdEFgM8r+OIql4zz8U2oRnXiuSoZdeoX41oVioXa9IUhVQ5gGCOpo0PIcH3uscpxVI2H5ojcfPe4ZBRRqdiu4lfKbdxVyAWtBmKfsYSWNi3kKH5dSw/pW2dpr6OXr52qEqO0HvKUdckHSt9KW2dl0pyLu1L75kxL+xyLtvr5+G29fEDvUVTyfaFFpKyQT4k6S32dJdhur/j1DbavLV9vfCuLr/0qgwiXDZ6+hx5ZpOkcOh4mth7UzZ/vglLTwbcC2/XsX5+80FyEjKuK8hAltLgMNuZar62E22HfslLXul1t6sSBvvquHnVAMow3tXnLdWSg/4M89Q1uWN6CJzS2BVWFOu6x2x9Xr9AK7wt5QjpoY6aNbYk3UhjKqgwq4KZdEPmsuy44NcV94A4BpS6PBsxQXOmjkKHU4zUDY6rpZaerln9fmGLL8jksH2kG43MaWshax9g00kJeTaYTg4cWTdcXlQXUVawKYK6yoZBA66RWbrelGZw4NTQwh4K3ELiGMQpXW19brry6RlMF7OqgVK8/n9BIW/F9PNqx5ogq+cu3y/qq1V8etHp14jMt9HHLQ0uipqfrDfWYMRwP+45dfUPhzLAOoQ75xqugKhCFYmdfe1q6ir/O6qkZjM9RFWWao1NDpEgt1lTxN67VqPI6ofjM15YGXpcvNDA+yaHVYj6rikbzAXbIu1atID4HMF99tHENGZnrWHxyXEOsE84Kg+0XPAOAj7xDYbxHoRDDp7CPfF65jleczyRKIOVbpUJ96Ovo53JXkrpes8pg3FU05NGr5Pja9BmLO99a/6ocYZXDc8NEl9cNtbQ2LA+u2WiZ5ht3+ayyjmVXgatq6R8GTFVxrzbQ/UnQz2OqPJZPlyrjtWWFxsrnJIzz8ZHPKIdxLPZa6eIh5F4fjSPo5RUnmVXLeGjAQ/FzlZJ1lqK6dav4fIMWCtDqhEyh5XU+hueLS+16ZeMajkLe3jfmQS/p0c3O02rUBbEv7KzV//672DWV+lVd71ZXubpeaCila5IbP9eNGW1y9fK14/bDlaHJDVHImWh6hfh9+mhl2rxq8+2GXa5ct72qFcsnwxca2boHV/JInbYwhKviNjtPA0dVbObzuL740K3vO7bztZUiNNm+AXQNp46h2PJcquvR6oYnVe24ukO9cfTF0b4VWNNTC/1CfCHdvAZUXMkMLSUuDRMbaR6mKlatO9Ca4fgMr45BuoarpbUJCIUx9nGdkM2mwQXdX14VQmrkurFQPBwaP22cQ+PjG8thjV7TaWCs9Qs92nTrgn1KaB7NB7iSQoqiw8SxdcIDXx3N41WtAnWW3lBMrcm16/o0qdMvd2WqYxCajtqKp42TK78qpHMBWoWJkK7aygq4j0/WVA0L1hpwBzW0fPrA7wtTqtqt4vENZNWEV4G5qt2Qflp7IV6f86jymlUxtMbntmkf2+GBGzb55lOT50u7GBomdOqR/z+ZdhPVguuEIcNMeB0PbvO5aR9P1XHIGKq8e93QS+O1Aecaldc7KTq68l2ZIf18uml8IcejOaiqMMZtR/IjG9yhefPG7sU+uBlQuzx0VcuFbcV1vIdvmdMUrxu3DrNs1wFAXcMqjuusNL7J1TxiKLSxwV93hatDdeJdN68qVKzSQTNSk9esK0dbhfMYvCj2x913Ko3YSlV5bhdE2pLjlteND30UsnobNHXApJEvttX4inZtvXw61/XOdfQdxmvbsuuEY1pcbst3jdVXz63r5lWtoKEVMvfgha14Ftz0io3/C7gQuJlBYMDgRNexWJ/SGvnk1o3TtTrzKZ9PDBjyvr426gBsPiC084vfYT281n6dON7nzApZVcboC3GqwrfeSeZgA5bo9IqN1wPPBP4I2O00pHkp2ytqA1ClWN3Y1ZZX5ht83bQtKzRIId4Qn8bj63sdkIfkFG3VCaN8IA+FFtrKp41lqA82j4aNKgfhS9dxZj3dipPMCMN4yF7Sr21M0ys2fgp4CvBJoKso7Vv6fUpoHahSXEu7Mop32ftiXR94tVAipH8dILrthEIPjd/VIeSVbZ4qD11nBbJlhUIEn4NyeTSg+lbi0BxUHffyIgwzGK5GuAXh4l6TnigrvWLjvvSKjX8MnA983+Gua+XDhBRVQNPK6oYivvCqjocvHrZR17u4fPM1fGFwhqriU5/8OuRbBV3glseu34o45ZpcH88wIWyRLs1ZhNABXgu8Ffg08PcYjhmo6nQr/drG24AXAP8J2Ko0WAXMurG6DcLQEuYLh1wdXA+jteXK1UmCHtkmt706VBU6uTxicYRWz0HD6HPV0dMXc5dl2toQTGvGrunhc5Sa1+/l2U+XvRo4C8N2hJ8B/wX3Er4ztOkVGyX92sYvAWcBHwDajuIhBess1e7xsN7HlhGaPH06wnppsudLGmh9+lYv2/0tA/fXJXFKQ2FNXdD7DMAuc/tbNxSp0mlAD8M3NmnRztOBTyLMAf8fcPeAaCXKil5+ya8BHwZeojXm+XXTIZ7UU6bVnW8dX13fp+AL9SMrz8a5jsyQbrZeIVm+clvf0HjX7dt85tCgz4v2W/DbsvWwaLCtTw4+Hzw7vg3huRi+iuG7wP8AxoCgX0mv2Hhf+rWNrwBeBtxncWoeSlPQd1KoybHJWB+3vpvval51QqYNortK+WJqe0IKCb5zFd9SrelVFRa5S7ULgFBIpjmmELmuTjuvsUHpyq06b9FONqvmygpRBqOnIp0ifBLhGcAZGG7EcFFJLVfNIj5fv+GbwLnAe4CpgELaUhVapqo65tZxBz+kR5VxVMWzdcBQx5BD7fl4h2nPBr/PaficgK8N7TzJNwdavq8NLdzV9PWOWfk/mbpqD2N4HcK7gL9D+AywSlXRkpWu39BO12+4nCw+/zL9JUmjkAfT0narvmNffpXXCpWFlsS6cbgmwy0L9btOOyGdi3XEp3OVIftp8KF0vrHWwOxbOUJhSaVO+s1W2rQavkF2sWcPhhsRLkUwAyB3KF2/YXu6fsN/Idtxuc0q0ry3Lw7T6oQ8j7sMh7yVRqGJsXnsT9UqUgeoVaEHDBqBz0DdcKHcnigpv05VemXyy/8L84V0PnCHVlFXH7uNUP+d56L4Isp+egp4J8IrgD/AsAFhndq8Q+n6DT8GngO8BdjD4AD4J6OfH6KQh68KJQbrGoPzMc6xOGnj5Nu/4vBnn0EdtL77JrBq5agT52pAc2W6eXUAj8WjGaSGMN2tVrfhMwwBG+DaoqAtGpmoWzE8H7gK+CbwTmCkSp10/YY0Xb/hM8A5wGfoXw2tE6Nr+cMOSLmdMkDFAZ8WLyryDGAia6zdVdHvufuGY5y0PeE+d6NNrNZf36qn9c3nBFzAa/V9IYZbrnl3rT03PyRT09Xy4BVhhicCShD+DsNzyfbPr0N4TqVdA+n6DXvT9RveClwE/Ki6RknLuuXlAep7WaN4UFNOGsBI/jFW3oBbd9oxRWPWpziOLEPwuvGcBGOiEuCNyjvf+F/pt9dwqgCpnTcU+SE5mh6hfoTKvOccgzG4JiY8fNswvB54P4ZPYfgYl8UrgjVyStdvuBW4BHgjsE1pzWf11RNbDhtcGc5A94A8CAAxIMbkH5yP8aTDfE4TjgGJA3zbSG1PbwvwjoLfhrzhiS6n+vxocOz6tzJosl05IQNwyefZB4ytDHDNVu2qrj2Wu301wrMRpoAfcVn8qjqapus3SLp+w1eA84CPkl0N9VH1ABQA0D2Krb1V0gNwNJDO2MUBYtGQViZOeQFa6TUbMoD+KtDrjBfDfs9enrnyYmF7y2EApc2+j0yvll8vLa9IB08ca+T19B28kmlXq2Nbtkp9vnOAjwD7gHdwefKAp/YART966anAB4HfZNDK7Y73r4QZ45aHjlMkMk5Z/jF6nUFP5OqhtRfiH+QV0fU2osgRf9si5bxsbLR2tPY0/bR++cZW89a+ur423HJfmS9d8Brgb8v/6MFh99mo69ndpi9PbsHwIuAaYAOXxW/hsrjpkVaidP2G+9P1G14DvBq439OZXI9S+OHylPklEiQiBzeKNzblLIzjp0SpZ3try0uXvG9k/WpdzlszlD6Deg9UAWWlsj26MYPjoZ9Eu+Qfx35eFY/G52tjPuTTqxRb6PvgbvjhE+0Ojz1kH0m6XJ58iswT/zqGb3FZfH5d7dP1G66R0bXrMeYvgOkBDaqX5uywBA5nyXejDo1MJP3zw8gGYX7yFxtMTP4bZb8DW4r0d2kit6Bo3DGvgt/udWSs/jgGZ+tstNi7HArosbkvLNDW9yI/hIJQ/bB+fpnuLwwaW+/YcPUmnU3ziXo4Mlj+kYQBuix+CfB+4DvAB7k82T/INCAJgOjHr1iLdP4MkdMwZgkQW62Pghmjv7RGwGSvXHqxb+7B8jCl3LsiRHFCk9i++lov3BgMCYpy5wapVCCy+BI7BHHb698cVb5TMMWkzhoqUL6Zyj0u84dDljp5tnxlTCtDkKqwxBeG4PBpPJ/MAK4B1gdwUdIuXa4AHOCyeCnwDrKdk7/g8uRKhav65MWfp1k+/Pydy4nG+mXdA5NGkhhAoqYxaWsU6Y5i4pwhjUmSJSVJkiy1jyCdoHQdQUYgHbF4IkTG+/yAdJdgTYghHQPpNYpIExgFSfoykvF+fUlBJnoSs/uFxnKXXshtYtIm5YnXAO6C3ObDy+sHXpUzqAKuL89N2+VamZ3+2+wk06UQxDQxbl3Ng9t0WXwOhr9E2AG8l8uTLTVa1sGrH2vp+ZbXOQ6Rb2K0Y5+H8uVVp7f8U2R2fT8ztO7BzLDS9gSls4tkFOnY4WoDsI3VAJmh9R3bOGUaIVtZC4oB+7zLkN2RassYpexKRyiHzYUM262OOn0dtfhtGQb4274HL6r7gFuU26J9UVgVwAEuixsY/ivCH2L4FNvl8/xj2vVw1wVcXRCH+KtkVOnnG8FhgD3fsqp2QvpVlfnpT+N+bW2V16KDYVpyZUIYs9ZslE8yNbC6pwyaYtoCU0WXJ10+knwaeBnCBRxj/o23xecqnFWeugqsdT6R81uVdj+uLK3c5XXr+fTwfbR+uumq8Ts0pIHNTvvAaWsU0tIXuPiQYPH098FdQa53dtOuMral+mJwPxneFl+C4d3ANbT4az6RHCDc7WE89KP9DaWrqCqcmM/vsB4+lA7pW48ui/URCXjWSqy5ZRqFvHcur/ym44LBt7RofK7S8/UPH002MsdLABjlSt4Wv8ijui9d5zfkLUO/IY8c+rie202HfkNe2+fFQ+NEjfxHT+5q7uLDd7roc54+b+2SjVurbv8ks8o7h45dRevE4CF13xqfg+HPgRawH5hBaAPF0066CNO5Hmn+O0VKSrGNltAGWj1dUxJEZgGhSzHQs3Tzs39B6NIloQMICQYhZVZme31OgFladEnyOoYuCfvz2wuE7B+XW2QO/1T5XIbmoeum6/y6ae24Kl+nwoNXnb/5YvRh0raGPixa/P19cM2KtCXF57Ht9KMFOMBvmgZPi55Bds49jtCwShukjBPlQMyg2N/Gy/6mOmrVETLPOE4CGb8IJt8vL4AZ0cxl27fuj+ZwKrbBmpC3meUbDA3o1RIMI/Qv76ektIjokCIYEiBBaOWaJrmsuTxd/EG5DXSJSUlIMaREzJEiRKR53TaQEpus7ayNLlHep4gEoVP631ZEGyHF9Hqa5vpIr09NOvQfnSQISa++ycfC9BxLwZPmM1mMnWA/fikUZsAgvqowWBUm5/UaaBSKhXze/NAueoZvScK3khscyXXDEDtd5zhUX2vHTWvUH6nVpsl47xEdKSPELKfZMxqDsKS3pZYSAyPENPL7wg1CA0OT2AKv0MznL5Y0N6iIZr6+RSYlwRDRRICYwghGKWZSEOJeP/srmSHKdStAHVGsT7bpG/t+oF7trA99bKS5zMJI7ItWSc8p2B/xpPtG02+vGFN33PPvRgmwLnirlh3bYuYXfw9TY77g9uUNC3ZX3zq6Z6O3WxLKf+5oA7PoYYeVp91opfJTkbZ/Q+lQnp98J5kFmfxbeil7PSjz1fHSmYblUinl9h683Cg14wJWaxRP2aGjYUBUB9xVAK/r6YfVDXQg+UBqnONhRtYXLLqLtiazCkrz08KW4oYsWhTg/voiBR9pUYdAY9MZw/WhDp186EVWedL5eOlhAe9r3z12p0LzpAGvXYu09dLnkjT4HFLX9Fhg6FBR+BUmjy/5PKQGPPtY+9TZ0qv7iUvpCUb5nfhPOcHcTIPXkt3oFR/iNocx2qrxO6JooQFcm5Sq8ODReuz6YLbLIhq8JH47a+PruI1XsTTqcF78Vk40P8iBPuHUqwv6Kh01MGv99Y3ffMOtw5L0XZRfDc1nUH2hiY8vBOo6eYNyVptRfi3ayFbGuDHbXQeykZwCxqOEc3kzibyDX6Q30+Yy+k/38p0Y2rsAkSdfGwObtDhayz+i6PEEeIhCMW6V56rjsX2g9l9FPN0sY3m0kYcRtiB0SEt3oKfADNleyRwwaVqcE59BW67ljvSHtHk/cJDBe8hTyqC2gR2hG0DoKWEF+bYG6tIhjdMfL1pIAA+FJ1p6Pp+qm6rKx4aYF0Rnsdt8jD1ETOegTugHDC4VV0kTMqCPmRbnxOcTy//jLnmYR+Q9CPdanDAIbhfYLqjreHEN2AVo3d9FSwsJ4HWoKqyZL7jLeQ0a/EZ0HtvNX3EfMV2guDgbk0Hf13oBm5TsRoMu2SsGRkzKaWYNI/JZ7pKD7JF3INwJ+a0BOrhtYIdWMddb23mudj5alGA/HAA+TEhSVe4CvgxsQ8RvmKezO/o49xJhX56JLT0K2Nm/GhXXKduQyxLGDJxmlvI0+SS3ywH2ylstjw6D4HZ/3TGpe4ntsb16sUBpIQLcOL9u2lcnBHLfHYL9vFdEb+Ee81o2k5DQh1KjFnjqUYfsgnULYdTAOrOMpXyOzWnMJvkwHb5BFsn7wF01NsMA94gA+kLbJgzRo/Xcejjyivh/cG58A3eZV9Eh6cEoxhCT3YXRJAtNhvn46kB2Ta+FYZrsMaRHRwnPj9/G6eaHNHkD/S3GqvMFPGnf+UzVec6io8fLg2thRx0+H88wII842Uzy5OhqdjHOPQgpXaRXqrepu4KQflLpPop7B9M8Tl8btTmdN5HKn/CD9BameQ/CLrI43fbirkd3L89rui16b63R4eDBQ+Cv8mKDXu8CcyJnRF/nIGMYpHQZJvPafW8b4Xpg43z8HrxRk9c2qqIHq03Cb0dnMcHHgRUDfai3ks13TBcVLcQYHOqdWLq8oZClD5AbZCsXyrsZNR9lhEa+01HclV1Pn/lFr66H7Z8JjADjGJYiTAKTAl+SNjO8E3iEaiAXGtmaDbY3vPaHfZy+0AA+jAdxJ9jO84cnEHFd+jNWR//MCeZiTuQ49mHo0N/HmJ8+w+gteZyfPfRgguwOlkkMTUn4puzkEfkkwkNKv+zwxAa2OxZ1QpXDHsBVtNAAblMIXCEPbqf9n72ygf1cyB6zmYs4nodp0kKs2/Lnu8zbNAiebC0x+ZNHhAlgKTAuEV2m2cMuHpAZhOusGjbZ4HZBPt+LOIsW6As9BveFKm75cOCGiFR2IfJBHpaIr8hm9rONE4iYAJoYmpD/gY1eutkrM07e4Keh8I6QPfpmCVlkvQrDamBCYC/b2Mk2NqZTCO9GB3Kd+Ns3LlVjuyhpIXvwgrS4Gx4NuIt0Ig/SMJtIOJZbZJp9ZgsXcBy7iEnJQpbUaW9YX1fUMkADYZTsmVDjQCSQ0uYRdtFijm0itLiJ7GGjxRXNYUDu8+Cu1ovWY7u0EADuA26dOvMHN2R/Ve6kH6AZfQ6YZbO0eIjNnGeWciJHs5eqk896VOylj2KYzHduGpLyCHuY4SBCyl6JuEXuAr5IeTtQA7k2HnVAXtARAW5Y+CGKRu4yXKR94Uw12EX+ple/i3CDHOR6tnIcKaNkbiAOSLKP7Hz3JDKLtQ1Im51s5yAHSRC6GO5iFrhyQLfwxZ3QyuY7dmlRhyoLCeDDem6tjjbp1UDvys/J/gTcr/eQtLmCB1lGl+X04+j+3x9MfveKu3ee7XuPYHrgXgIsA5aKYZoZHmYHc8z1Li49JCPsl5uArTX0rfoMM54aLSrALySA2+Rbhn28VRNeHcp00vdiL+ujZiVwClfT4OcIp1KcJJr8Ik7m2Qvv3sD0TkjHyGLsJRiWYnoA328EMROMcTLZ81aE3RJzpzwAfA39go5P/zrj53MERwwtVIC7pHmmuh4/DOxSoCE/IjLjLDen02AFSb5t+CBZ8HBM7slH6APbfkjwCFlI0iTz3BNkwJ7AsJXsNqoOMGlSjjbHM86pPADAt2roOEx/fGNRNVaLjhYywOsAucqra2nfr6EtV5HKJrrM9bxx4akPYPgqcDdwLP1tv6J8hD6wl9MPS3YBm4Eu5bvI2wJ3yiy75dr8vnBfv7VxGCace7Q8hzUtZIDD8JMUikPrgBwS+QIzsoNpduZ/Le5LFeBh4OvAcWShSP5OBhpkD4IrvHaDzPMXj/ex7w2cEsMmHmSz3An8gEFjPFQgP+JpoQO8LlXFpfXADYZU9pHKvyIiPMJ9NClOJvu3u84BXwI2AcdTDkkmgZ1k4C4u/RfgTsTwiMwyxf38Ih2lzd+TmYCtQ12dtTqhvtflX1S0WACu0fxPxLpyAyL3EMkIu+UBYLb0Mg7IdqrvAq4CVpGdfI6S7YPY74QrwN0SOMgeOmznXlnCDN8AdlO+1XU+J4fDnJAfcbTQAf5YXJCoB/wOX2c0D0z2y3am2M0IUf5s2f5nH/AvwA4MOyn/lbgY3f0i7GUTsxxgiogdshO44THsg49C47koL/4sZIC7jzz71VIq25iVDSxjJWCYkyl2ci+Q9O41aZCN4BxlYBexdkcMe+QgB3kAAZpE3C0raPFPZM9KeaxIPOkQ36KkhQzwx4qqJrVf3pUfMsPDxHn0bSRmtzxIm5meBy9AXtz0UPjWKYF97GKGvWRPwoLNMsF++RbZvkqdZ5vMtw+PhhYV6BcSwENepz4oH12dwbKWfJ7VFC+ajRnlaKYlZT8zvfg621Ls/9mtLSltDtJknAZLSUhpE7FN9gPXDqFLHbDVBeSiAm5dWggAH3bgXUNwH54TqhMyBM2ohFS2s4MfcTwnMcpyiqdci6Ts4WDvHpUm2cuL56TNAaYLLiYYYRVHc5+sZY5/Jnu6Vag/VXlPhB9D0EIAeF2aj6eqC3jXSMoyOukVdNjOSD5exZZhQwz7ZIqULgjMMcc0bdwHA90vEXtkA3Cv00ZdIFf189HQojaEhQjwQ7Hk1gGOD9y4x/EI0erZ9MusI24k0o3GZCwakbF4RMbjERmPWhKTIFFbItOgSZxfHmrRZR972Sw7Ea7B/0rtYXTU+q4ZTIj/iKGFcD/4MOTe6+yWucd1PPUAyNaskvEzTkiOO3aFHD85ztpGJMunO2b6+/c1Nz14kjm9sZWZzhgT0n83jDCKYZo4iiSKwURtzMQIM4/cz6+dfUL371vtaPyuh6OptB0E5zDgd/sYyjti6XADuEs2CAyDoAA/qEtlJx6bTpy/LjltzQo5PW6wYudBs3fLI2b35k3RzQ/uMwelbbrAtznBvCsRIvaznyUchQFSEzMizVTMLG2iZIpZImbn2rJ8/EB60zGnMLdmafLci87qjhyYNvf/cpu5/8ZfNh7K31NWx4tXrTa+sbF/3fwjgg4HgLveOuTF3XqVIHnumd0156xLz50Y5aTNj5gd122O7r51c7ydTv5qPsH03hMG8KD8X043f8Tt7OMguxhlCeNM0gRmSTjAHjrZhXkekAOzHfPFb97S2A+YlSvSJU8/Pj3+zHXpc85/SpuHdkc//86t8Z0Hpswcgwao6e8Lo3whSmg86tBhbwyPF8CrwOny1QVzCAQlvovP7a45//TkOWlkjr7tIXPPd+9q/Ftrhg6psV9ul/1dLXuybLYp2JE76XInK8yp7GOGVv7/yf1MMMseUiKaJEzJk5jmC8TsoJNpv3dPNPe9fdGe78Xy83VPklUXnJQ8+Q8u6ZyzdZe5acNPG784OG1aPn0DffKNBQEet+ywB7KPFpoHt4Hs89x2WhQ+m38ALCcdm45e+tzOfxgdY93NW6M7vv7zxnXM0SUx2QPcMkAXm4FR7zdrITspv0P+jueYy7mVJnOkdGgj+YteR4CWrOAe+R6G6+iS5HUzc4kxdA33bjFz9z5kHj7pWFlx8Wnds/74pZ0zb70/+u7VP208gA7sKq9eF8whWnRAXygAH9aja3Uql/jfe37n5LNOTV/8yz3m4S9e37hi+qCZo2PS3ruHIeqBO/vPTpYyQNx7okn2p7W75HMca97IA8yUjKEL3C9tGnwbyJ6vIvkTCPuvGs/eV5wYNm81c5/f0dz1jHXJky4+Pbn4jBPTzZ//bvO7e/aZ2UCfhvHehwL4hy0tFIC7FAK8D9he/rExMe98Zft5ExOc9a+/iL//k3vih2mZLp38HTsZqLN/sBvi/MK6FBfoyf5laYh6l3UiZuReLjaGLfRfyB0jGFnFLD8kZh+S32or+SexANq1XpedmNa/3x3fd9vW+KHfe3bngrf8Vvu137w5vvK6XzR2oRhq4GOPCRwBAK6ihbgPrpFvJ6DqhEtWr5D4f7+h9RIZ4ZSP/iD+t5/cFW9i1szQpkVClyR/X3v2umyIEWKEBhExMQ2i/JMBP5YGsTSIJOIbyXu4lJOIiVlGhJGV3CR3MMq/EWNoEOf1MqAX/9s0uRdPSejQJaFDx7TaU+bgZ7/fvPb6LfEdL31W8upXXth9EoMADnlyX/xdN5RZdLRQPbhNdrwN4Ti8NJErlkv0/te2XvHwjGl++NrmVe39Zpa2SXpvbkjz4MPkt1P1o23Th6XEeWlxS1Xmy4vg5Tb5EKebN5NguEW6jMnX8gs9CcUb5I2Jc62yN8abvP0450ryC0Apgpho4y3x7VNdpl/21OS3J5py5T9c27Tjcm18fB7cTdctXzS0EAEeirN95W6YIksmxXzg9a2Xbp8z0QevbX4zPWjmaJluHm+bHNwREZLDVfJHQWR/VDMS9dMZwM87xRz3zBPNU49ZzmkToxw31mB5AmMfW8bSLddJ43lPMdMXPNVcsmUP915zp9y++4BMZ5oJOXgzs0iJaOQxeYzkz0ZJ8pClCyb54e3xfV2h9aozk5fNdrpf+up1jW0MemLNu7tjdUTTQgR4QVVxOJS3EO3JNh94fetZ08LRH/pe88p0yswym4O7AxRPN7Ffr1o856QhEVHu1SMa69aYla97pnnhiSvNb2BYes9+SW/aJ2PbdwpTbTjYhqiR0rzNMLdejtvSjX77aScx96IzouaeGbn39oe5+gs3pDckLbKbsYSUxGRBUfEA/ALkkgdMXRKM6f747njz6iVy/Quf3n35L7fzuVvvbUxTDe668XeVMSwK43g8Aa6dLFbtdeOpUwL521/ZPn5ikmd97EeNK+YOmGlmTccBd5o/oKfvuWMiYsliZkN0zEqz9C3/0bzh5JXmRddtE/nHW5LRBw46c27f0b0Mbtgh3LAjMcB4FMP5R5unvOyk6KRPXxr//o0PyOc+9YP0uuxFhJI/ms1EQEKSe/IuQtp730SCMclVP2vcffIqWf3GF3Rf/JYtja/kl/qr4vKq/COGDoeTTC2+9O4YHLs6jc4+NX3pNZuiH977YLyHFm0SEjq9V0tl8W4RczcwxJJ57iYxMdFrLoie9n9eGX3mEeFFb/pRd+Rv71TADZRWgLVSOk4FfrJTePdPk/H/c0ey4txT+O8fvzR63wnHmCU0ibO2pHgqVmackXXy2aVLmw4t5v7musb1HVj53le2nowfxPZYuGl3PI8YWqgArzMJ6vL83le1z93dYu5LNzfvoU2Ljunmr2/t38mX7Wjnry8Rk9//FxMTvfUF0fN+91zzwctvT5b/1a3JyL5WLrnqf/sB+vke4Y+uS0Zv2c+ZH3h59IlfP9Ucne+yRDQlysOjbPeG3glnipDQMu10xsxuuKdx3Ulr5fkrj5LigZyhEKVueLLowb5QAV5QaBvMzZNVK8SsOkrWf+u++Ce0adGmS5eErnOban9Po3gMm6GJ+aOLol9/9q+Zt192Y3f0ph1WM+6TSuqA3ZQ/AnzurqT51fvTVW+7OPqrk9aYpVlYhKEpmR7Fbnyx0mRvfevSoXXtPfGDu+Y48Ke/1Xa9eMiD1w1PFi3QFxrA606CujX2vle11u3rMHf1nfE2OnRom24ObhvgWbpB9oeFBhFNorNOMCtf9DTzvvf9LBnbMkX/GSg2aY/l8X1ANYQrN6XR9Ttk5bteZC4rPXMlEvJNS7tvKV1SOqZLh86PHoxvO+5ouUDpvy8dGsMQLRrAP94APxQD3pvs41bJmb/cE99DYtr5qZv7B4M+uGMxvT8MR/Cm55nf/952Gb1zrxWq2++2dCkE7pCHj+Hv7klGVk3yzN9/dnQGTeyn0xY69o0yzfuR0P76HY3NRBz1uxd2lrp9RzF4z7gdMeEJPP4AD1HdZbdXHjVYd+2D0X0k+TXKjuq5pXfVMn/U8ZqVZvTkleaF/3B/0iwD0wG6D+xVVKqfkqYp/3y/jDz3NF7X4ym8eD9E6evcza94dmhvmzZbn39mcorS/2GBfkTQQgT4fJZXefPL2ku7gtz8ULQ/A4Ppv07KBXkR6+aX5f/zM8zZ22dIds/k+34DHrhwqNQDu/Z2Y1sG8K2tqTl2mTmXGLHeilzspAzqnZ90bp8x2yfG5Vj8gPaO0ZD5i4IWIsBtCk1K6fP0E9OV0x0eISW1QKJ90t7tTxnYOWUlJ951QIpbqwIxtB094H8J7ACvtWGey5zqpqRC9KxTzQrIgZ3t7tjnDFm6S0pqUqC77aDZO9rkmED/qsKW0LhWlR12tBCuZArVexIFTzH4A/yTYzK5LzGzFN4uLV2GSekbs2AkJeoZQjraoHlwioZ6g4CrXW/6h3huj9HTnZTkmKVmlFgKfcvxd8koxSCke+eYjhosoQzWqh2TI9J7w8IAuEZ1Qd+jkZg47eYXvyN1krOrh8a6rG+yK4jTHfYcO0kHQ6MS3PPZC9fqRHDUmBm5dUeyJw+ZMqPTTopBSE2KETnYMd2G3Qc/zcd7Lzpa6CEKDE6MehKVCLPjI1I8uSQcokT5BXGT3Vd4/YPc8ow1JoNhKESpu1MS4snznroiZqojuzfvYYriZeJSutpaBntTwCBHj0tDsqchDhuW1KFFB/6FAnBtYKsGuzSRrS7TIzHjvZtaGyWe8ifp/cMmQUj++ZZks6TsvHCtdeZY9+JOFeg9++OvOjVq756WqyC/l5B874cAyCM4fpksjbIHdw4DbM17Lzowa7RQAF5Fmhe3y+SGe+JdyxqsGJtIG72rghoAOiSkJrtptvDiCZ07dshfv/3seC5yd0eGubgTAruV9+QVEf9xbdT5+PXyTwgdTL4NaHrGV15xRpD8DW/mScvTNQjblb6VxkMZJ208q/IOe1pIAK8a9JAXko9vGJmJI/b/1rp0DaNiGBfDSI+nvyMBxR172V8NEjqI6Vy2MfmOgZ/+r/OaHa+GQ4DYx7NkLOIj5zdav9iZ/uVND6V7ETp06SJ0ya67Fh68D94G2UtkR8U8aVKObrV4kEFwV8XcixLAVbSQAA7DeZaBiRsR7nj22vR0YmLGAl68uJEpyYGVxcCdt25MLjvvaPPAX17QbJthTr9r3oi1cjziCxc15qbb/Ot/vyr5Ol3T7hsZCV1TPGW8bJCxQBOzbm06uWacNd++rXEffnDPJzRZtOBfaACvIp9XEkB2HeC2U5fJuvHJdIRxaTAp9m5D/9MmoWP6Hjy7MbVz3y45+AdXJZeeucLc/H+fNzK3dmn0qO4itOmi42O+cnGjNTvHP7z2S90PIb1HTbTz9SS7MNWhuECVfUaACSJGJX79Gd2nGrj3899uzrh994zLEU8LEeDDhiq930svH9862WDP25+ZnMkoDSaJWOoBeZc0v/yd0KVDalqktDftkqlXfyX5A5PyN/9ycXPmrWc3O8vHI39Y4qOc72mrYz51UXPuz86Jd/x4i7zpDf/S/RhCi67p0KVFi3ZmYr3begvvnYF7XGCCeGxpOnrmKjn34BQ/ph64j3jvDYf3PrjN1/tttfjOOavTVx2zKr1jZyfq0DYpbYSWM5FtUhoGWtIlygMVDDQlnZmR9A1f7X72xU+OrvrDZ0Rvec0pzUtu3J2mV2+R8Rt2peybTYNbhCcvi7hwbSSvPjmaO2rMtDbtlc/9zpeSLx6ckRbQpWOyVWMuj7mT/Nawdu/qUaZrE8MyYsal8Y7zk6dPRux58eXj91If1Ec0uAHMpk2bDrnQk/9rcijE+Dbl3PRA3nc+MPt7dz5i5v742yPfYZpZ9pouUyZlTqk3QUxTDKM0GM0vtjelQbP3GOT4/BPNsjc9I37h6kkuWTVmzm4L0bYp6exsiZluYxoRZrJJsnbCcPykGZvp8MiBlvzknj3yjfd+L/3x7Iy0Kf4/3zFduvnDItp0aZHSNgnlv6JlrwGflJgVMnLeqcnR7zu/e+mu3eazb/zE2MO5/sOC+jED+KbPz/cutMeeFqoHB92L23mu9+7lbdpmrlx3rLz5v53Xve/TP23cxxJSOiJghDlnUrukGGNAOqSkjJHmD95MGJEmhujGzdK98cHul4EvA/Erz4iPecZxnLB6wqxeOsJ4IiRzbWZ/tld2fvDe9IGfPpQeJH8IRB52ZH+ZE9PNw6I0A7fJ7jNpW8CG7FHMY2JYJo2J5enYn5zTvaQh/GQhgnuh00L24AVpl06045Inv+b9s0+dNfzuR3/e+NK198Y7eMS0eYQuc4Yc5GXPP0r254cxyR7yM5I/9AdiRqT456TJ/4tfPN6trIvkoJEc2uS37IpJ8y3AlE5+SjlXAndWC7K3Ji+VmKU0WS6jH3tB+4VPWyZL3v/l5uevu73RLbWm/4bSobx500L24AvxJLOK6ly8kBe8f/zOCcP33nxW95UXnJys4igZYQVNlohhosffB0crv2uvZRLadJihzRzt7A9jpkXbzJEwR5cWCS1SWiTMkeTHWV47/52jbeZomRYd06KTn0rO0mHatJnK/2nUdva7l4rhKIlYJU2Wy+iHn9++8KlHybE//WX0RQvcVbsmR4RnrkuHA8Dr7qoMpC/+s/EfLm3wk3ed233tb56WrOUoGWG1NFguMUcpuystUqZImDEJLZPBeZYWrewPcLRNh45p5YB3P3PWp00n+8szLdrM0mHKdGiZpAdsG9yjwHIxLCNihYwyyejlz29fdM4qWXfPg+az7/qHUft5KChp31gdsaFJQQs5Bq8iLR4v0uTHcvF7xq+95n/Ott9ydvc1p61IN/7NTc37iaXFtOkQiTBnYMaZ9Fax62KyK4gjkuTPKszaGCUixfSOC8pulyqeVCUkJtsV6fR2ccrtjAGjYhgjYoKISRk5ZlU6+aH13RefuETGbn/AfOZPPjO2X+l3VVo79uUtajocYvCCqnZVtLxe+jt/Pruu2+TSu/aZX7z7hsb101PRDDN0mTJdZkgVoPtj/1E17u+TBuaMsryxvN4SMSwhZkKajNN8/Znd419+SvLCFSM8uOGG+Iq/vnKkPVB3MF3nuCr/UdFCjsEPJ4DD/EDeO/7Ht80uP+EY+Z3dc+bob22Nr/3UbY1NdGgzS8KcSThAwpzJQDBTqx1fvg6kCWBEDONEjBIxKTEjNI9fkU6+/bzus5+yQp7a6LLx4j8b//eArAUFbngC4IeShgGZmvfkE1Pz6T9snd2FF2+dYdeGzfH1X7m7sT37Sy9d2iTMGMm9egH20IWnMMAnMIzkzz1ZgmEsfzzcCM0VS9LRt5+XnHPOqvSZEzF33veQ+fYbPzG2n3rhxTAhyGMamjwB8ENLoSucdYHOe1/TGn3+2emzU3ju7ha7r98Z3fyJ25qbk3b+uKA03ydpmZQOWRzdcYAyZ4keETuEya5CNvKtxNF86zF/ZNuznpQsf9265OzTlstZoxFbpqa45rc/ML5VUXMYwIZA/ATADyU9xgCH4UHuzf+fr281/8MZ6dO7hvWtlOWbprjvF3vje75wd7R1aibKboKS3r/ai6fASv7nuAw4kSU7yvfKi6dmRdnzqy48Pln2whPSdacvS9cdNcbqMcNtB6a4/rf+9/gOR6X5APVxAzc8AfDHiuYD8mD5/3vf7DETY5zVhad1hNWPtNi+Y44dD01HO+7YF+367jZzcHomSrFfAGvLzC78mKesTEcvPDZdddIyWbN2Il2zZoy1kw1GjZF7o9TctuHf43s+8rUR+77z+QK0Cry/kl2TJwD+2FDde/rmxfOPb5udPPloTooiOW42MScAa4DJttDuJswm0E2EJAUTgTQMo1FEYyRiPH9w7Z6GYWsED03N8tBb/n50291bIt/uikuHAri/si3BhQzwxbIPHuIpyMfrAsEAvOGj49PAHfkHgF8/rWve+TudJasmWdKFkdh6Cko7odXu0rnuvmj/X3x5tK3Iravnr5Jv0dPhDHCoB3Kbt6BQHQ0cBuDmXzbk1R9uHAQO1myzrvzHk39R0+EOcChfuRy2Tt16PtDUXRWGocer7qKkxQDwgobx5m49l+azKsyXFoqMRUmLCeAwP28ekuPSfOU+VgB8AtgVtNgAXtCwIch85D6etFD0WPC0WAFuk7pLcpjRE4CeJx0JAHfp0cTcvwp6AsyHkI5EgGtUBarFGOYcEfT/AxLC5JU23yDlAAAAAElFTkSuQmCC',
                //     symbolSize: [184, 170],
                //     symbolOffset: [0, -50],
                //     label: {
                //         show: true,
                //         formatter: '{b}',
                //         position: 'top',
                //         fontSize: 44,
                //         color: '#fff',
                //         offset: [0, 70],
                //         fontWeight: 'bold',
                //         textShadowColor: '#845100',
                //         textShadowBlur: 10,
                //         textShadowOffsetX: 5,
                //         textShadowOffsetY: 5,
                //     },
                // },
                {
                    name: '储能场站',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    data: storeStationDatas,
                    showEffectOn: 'render',
                    symbolSize: [26, 20],
                    symbolOffset: [0, -10],
                    itemStyle: {
                        color: '#f8b500',
                    },
                    rippleEffect: {
                        color: '#f8b500',
                        brushType: 'stroke',
                        number: 1,
                        period: 5,
                        scale: 3,
                    },
                },
                {
                    name: '储能场站',

                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: storeStationDatas,
                    showEffectOn: 'emphasis',
                    symbol: 'image://data:image/png;base64,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',
                    symbolSize: [114, 114],
                    symbolOffset: [0, -50],
                    itemStyle: {
                        opacity: 1,
                    },
                    // rippleEffect: {
                    //     color: 'yellow',
                    //     brushType: 'stroke',
                    //     number: 1,
                    //     period: 5,
                    //     scale: 1.7,
                    // },
                },
                {
                    name: '托管运营站',
                    type: 'effectScatter',
                    coordinateSystem: 'geo',
                    data: businessStationDatas,
                    showEffectOn: 'render',
                    symbolSize: [24, 18],
                    symbolOffset: [0, -12],
                    itemStyle: {
                        color: '#00b9f8',
                    },
                    rippleEffect: {
                        color: '#00b9f8',
                        brushType: 'stroke',
                        number: 1,
                        period: 5,
                        scale: 3,
                    },
                },
                {
                    name: '托管运营站',
                    type: 'scatter',
                    coordinateSystem: 'geo',
                    data: businessStationDatas,
                    showEffectOn: 'emphasis',

                    symbol: 'image://data:image/png;base64,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',
                    symbolSize: [114, 114],
                    symbolOffset: [0, -50],
                    itemStyle: {
                        opacity: 1,
                    },
                    // rippleEffect: {
                    //     color: 'red',
                    //     brushType: 'stroke',
                    //     number: 1,
                    //     period: 5,
                    //     scale: 1.7,
                    // },
                },

                // {
                //     name: '车队业务',
                //     type: 'effectScatter',
                //     coordinateSystem: 'geo',
                //     zlevel: 2,
                //     data: [
                //         { value: [114.168772, 22.600497, 3000] },
                //         { value: [113.23803, 22.60268, 3000] },
                //         { value: [114.115494, 22.34357, 3000] },
                //         { value: [114.061847, 22.574352, 3000] },
                //     ],
                //     symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJgAAACpCAMAAADgOgQbAAADAFBMVEXZjxCwcCb/cwL/cwDcjxWubiWsbCNMaXHZjxDZjxCxbyi1ciqzcCm0cyinaSC5dCzAei+paiK9dy63cyu+eS6jZxy4dCuhZRm9eC27dS27dyymaR/FgjugZRaiZRuTVQekZBfokEOsbiP9smCVWgmQUgacYQyfYwuZWAn4eBGrdSvjyKLBejG0gj78dAa4hkHYuZLw277PpWjSmUrKjjvxuGvJn2XiuYTkp1H3v3HxuVy/iD/arGXIkEjy0Jj//fr7rV//48b3n1D//Pj//fn//frBejDCfDD///7/1Ij/+vH/tWb/9uj/z3v/3aL//fn/2Zf/4a3/8+D/47b/6sb/573/79brmVH/0pL/7M7/xmPbjUT/y2/8tGTGfzXQhTz/xob/1KL/2q/KgDj/5MP/v3j/uWzLj1EAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYnc/FAAAARnRSTlMB1jNcCNLOAAIE2uPg3MLs+8n15/iy6avy8e68/qG3ss0ZxjKkwpV+zUyl6Hq3JKbe98TX49/X9Ovw9s7XvO7Wf0e+lFi2wqeDDwAAAAlwSFlzAAALEwAACxMBAJqcGAAAFblJREFUeJzNnVuT3MZ1gL9u9AyABSfLpUiaV8m6UbJCOXJSIqsSO5U4VXnJq/9nUuXyQ6ryEtuVhLQjuSzaEimJoihzeVtydzmLxWUAdB66cQdmZ3fJJAe7Swwa6P7mnNOnL0CDwuX4kqx+6srFieOAHQKoKwcWe1SwYzDVsqzso4CNQnnj18SjKSMAhwYboloC1JVBwCGGw4ENUB0CqpI+XZ/iMGA9rKNAWemxdTkOAdbhGqQSI9fqgWNdtjbJymBtrB7VGFFLenhtthbKErDk72V28qWEhWFxd1TxX1HrSGN/HOwnC/+VMVUSTX7d1FoDxlHDV+R/pyavFMnIRF7WcVaXWtOMcF14fQ24P/3oF68O6p9+l74OvM7DWmlJpbNBUybqb07C/WvfHJj5zfqrFfZfaf7J4FqrRgxVzDdvvg479xtky8H42zV4tH4wlWoQTUVZflryZQez7Z6D/V/RJxsE+0lwMJehKhhsULQgMXAZXF+GJnbOQfjrmqzMbcjHksWCh2cOxipsNvsBYSs1EFq4aFJQcKOhNtFF0+v3z7MQ2ivJSi8b0thf+3B5mX/dRJVU+0FodnKn+dWAQAutSUF2LNpBe/M7iP6zDrYWaEhjEh5mA8d7WPvgZm5ODpCbnD2Tq0OWQICr00LKm017ijbZN0/PlxXGfCl3FCxzmR6AZanIcyAGAdoYyuLl4EIYCOFqUilvNNA69pxCBnjt9mkALJkVfDRiyRZWnkOMsO2kqP4YVjxwM5JAMO2hNck++o4ZPbKmEg1WQgEjcbXk2t93XZUTx4kQAEVbQAgh4jjO89zNQqScTgvFjToj0XC6X9QxsCFdjSVDsBWXxcIlJ7a5l5lGvtnsIUcLiMHDDQlkMU0VI0ory7MqM07WAVvWmbiJonCNEYkRNVRk/5oNwCc3bDrGU05IwJRMZTdGzNmXFtjSPk6trjyvsSJAddWeEeFDDg7oGM8lDGSh0gPIml7WzHMlLpec2GJF1fXtOKUgW2TgmwQdG3sWU2AFnSVuG+xALqMuYiEslmoiBWEQEoT2iEIZvTkCMPYMKFSmGo7WI2uobKTbs5SrxjJUgSUjgABCS6cgi8B3BOjYy92QoFAtR6vI+sqra+BSv1cULgI3jxNBURBFSiEEBEFguTbsRoA5JARKQbSXI6SI49wFiaIZN8aHCpXGGly9sG/15arcqKvSlmUiB7bsBpzeDtjYDhFoRUZ0IneEJvZUlhid1VkLbcqzKqttWWqs5vJ6I6CSqzRjRKktgDzP8+bZpzm9lW9ssBEExqKKvQikICZ3Q4lC3egW0dbdYDDtDxdv0uAqCiKlKiwLNWM2m2G2hC3Y2oKNjUsBwjjbHgg5SNYxZlW87ChsiEs1uCBS1FiAZYKSbDYDg4ZFU4q9HJaTdQg7GhsaXqti2ucqsWYGA2j0xwyqRbtkamhEi4wBaw6ALamRN1UxFfv0uCwWgOM4jgOOk6ap6fAb9ZmagCXbq8iQKNUnEy3VtDU2YEgFYh/XcEWRQhAEph7OZuDgpDkRkRNFEVPSaYqlm1ml9chCJMXSAJp0wCyXqhs/4/i4eVxVR2NGqy0HB9Kq8U4hJZ2mNuDMZpRkokHmhjBtuJnqNbalxpIxfVUOFiMaXHkOM8BxiKKod01KmqbGpCXZJRo6M8bsu1nL/Uf7XvXpdde05MJyUVFJkFJK2ciuRUZtTYj7xlzUu6XZekca4OKGKlxCNzcORsVlsGzfS8L5CwN90JZcAeDx2qeOLECFAdNMNXoaprxGi6kYr5NCV4Y0DtbmqrA4f+4gLivfUw7IIg4cZKGWjMSapuwpTHCz2rXtI10uY7sLq2EB2ds5SEJCJM0wu+ie2asPogyVAq0KpDEklYOVEtVf6+whJqwW0QmA3AVKlTn90xK30phXcVU7N8E1rt53sIhSXYcUtWdiRoak6KpM1CRjeQvQqkCELrGga8g0qi6dTk22q21w0VTNnHCgf9UQ2fb9Zii5CdNSYRiFbVQVspRpCiwWkwUr/ValxrgdL+uDUeuvKTdaCgOCsvGr7Tid2m+9WP1X2akOwqUd+7YpO10Pq+oCUILKkFWsn6aNs1aXyKjMFF6prHayATB7ysKqXOyXVRI6hpTAtGaSK2+A9TIXTPivymvKqDK1Kqb1bIkSwBa4AdCYa8znlvIQXCrDRNkwYDoaZC2YVyHA4iSY/o4t3NpuY4vT6xc7189uPx3LekSEhr0TAC6yQN04cXIJWC2LOloKquBq7flnXS7mF1hCtvZlOgEWP5y3yjQqy1FQDPiSFwOq21JOjL1vUFnS2QO4xBYffm+g8Heejrj/N+KxApjCbxdnw/e66V1bLloNSFNj7TopIXQxgQzY3uD0D57DN60xs3iTbMaAqDsP1mzmKUwn23xy9u1SBypr2HJERp1fFQKXGJwcBWyA/xzUvdO2MABOVEWVGypTGbefrK8DzIHCJ0FMJ9ufelcyGp2b3IHRPrY53Aivdc3VgrzqJgZsvf994N7pEmqymJTXK1CZwv5VuLeTddOiTE+eDAO+wNcJbh7/89W6IFnEXjJeLxu8bUsWU+OWZZ3k9A9fwB/SFE0kYUZRTsnLEs5m92WSIsH3hXgXgPNf6tld9ifO29tnwyCEsl72pVJnR5G1A5YuVlpy+8IL+LNQayIpYZoASes+Xm5/vtxyJeBntbu/C3uTB4vnp3iyy6U/advhcZxxJ+uATRbANtiGu56u2n73beDzSWIUaOqhH5lmwDTqjvn5YssHlPignfP7LNY+e47DRn3M3rTY7qtl3PmV/SbC9sQ2fhjC52FK1GjF+gMkCBIfhCff76W8v1h80cTqK6vBlSwdd5azOFrg78DXMdqwTIFZBpGpK82m7pMMgffxzkDzx+vTzdbn2EuC5lR6U2PtwNvIzRhLlHA/AG7lZSBKgXkU0b85/fmeEHgf7wxgAecGRgeN6NyMsLXGRCcF3Hreax148iMwd1dqKeuW0NUGyJ3mgcbGef0QELoMsS1ZDIOZtHq3oUsBAobao0a6QCC+zAH/HaoDnY0rD+HSn0ZzGiy+knUTtcOBpINEA967zSOzx60ma+8CdLlseW1RsPxRGNu/XzqirRKfxkBWn7t+65s1vtXn/vxFdejK5moaG6+VKw6uW/ICmNRx9ekv19ZZIB4/PnepPLa7sd24YPxxlBXn+VfUmAam1adnm+vABBY8qkfranuDFeQoY9ZxiWmOBrchSZJpMo81D6sa/sF6w5LjU5mqmbTojfV1o2XPneGfRhrAa+XhzUjin/iLnfz5l4krnp/JHcgddlb7ji1TtrmCZsjKze/gT7W3A3Ayt58XEtxLz+DUG99G/uap8tSmjPtYy5RDzQgMP2Ry8GULygGVRzlWXl1aYB1LHvMRrZPNjFrtvSazbUariHaf322kWR/TAGl7hLG0krQS18q+UgjR7X+IMV2VD5stSdMEqRo0yKjGJBgbHOIpslMA1fMHE2D9Vw+eP3z6Jxr9Y/tghtkNWgRNn2g5f6NWZgpwcgrpmP2VxIvRVe7np4/Bf2I+zC6VivwaTFeg6E3Z9RQTDyRAc/4Qz0tGNg88z/MSz/O8GRB75lzPe1uRMAG9D9+jPEcfWJmApZG/NUjeZX13eAN2gfVdWM+1IPv6NOb47rX/3jFzG2dPnGTXnGOn1KtCOoWWzO7yOydlqgZ2RzaevYaF2z25H4NnzgUeX/K8O3B2fWbTqTr4J9oF9GU8JZ2C48VwYm91J5tF8PDMyerzaf6yM2v+iM2yt+k5LqQj9b0+rKHpZZlNqXStzLhRgdp58dVXt7fMJ0XhlHuKDQVS1Z8VKmt+YNsrzVUAIciKuyo7boO15TpQNLxfKVT559HWkzTNnzyxha0/UXXBp4CHO4bIHqt2UKhH8SZUXfJgvNOyvHeR4NlcnjWOPjWzBOaeH9vMHtVpG4rSvyv7Nxxh04PKkkublpFR0k/t5Y5RecZm3dY9m6R6P47tzMqjR3Cv0RCegiQamzLbCuOGJR26Y5tGIGm79YTJAiKYcv2GKqfR8SO87TtvApyHItWRhNjzgM2tCaw/le2n0r5+pwwvVZRhfRduXWQTqtYkKCC7/ofIlt2SBpgWnR5ZOg1C14sRDg8uemf2gPx9Y9VCwiRehz+YGYzHiypjCax//gPTs1hnd93usM5XF+NdNBkOGs9JFKmEn6YMiDFl9X0n/TQAsgdxDODchbccoIC5C2STBBKY1FdGJGzdhRKNajr5UXvoFSxz8V5KVcD1DIogwdOaE/CAOI5jUuCcuVty6S24a9rmJDGOLElkhA+TbwfK+sKzCjuB1h4JBWTlLcsJWBczWloaOtMpODmAH6kH5uZbIXkLcS+9kr8DPJokJjnxI5/IT/wIIhJXffE+hWxufLUXG4UZD3Oy8ejaAes42fUbCoJQoRGQKS3CAO5cge/zBlDA3VymUyAyz9tVD9wBT9VbLa7i3g5slh6mPZKgQFYK64iL7HS7Jy03SwtwPOr+yW68d6dOvvuQ1jx5LfMJD+8VNLfPsIa0CqOeyaVXJysfq2t7s7OWSQgS0BqfDE0I7P3LPZN6784zgPKBgaak0zn8pnXozsW48nytsQprX9ZwMRx7M8yYVICD85FSOgQuPZCZFIuJkyGkkxVSME2UWt/7pbu7/fQ/ZJISA9M8d9Jmly918pxMzeLX6mN3X5AZQ/omVuRTnYriOnBiotTv6+szg7O0SbIV0wGN44MmZDfGeWf32bPdc4AusE+BkJZbWplo926V070d4l2rChPD2h7Wv3l9wEhcpgAeWuf4GRCyG8fm66WJjqreR2oeUaHG2odn98p8fke8G1oP07qhmqa0OrYlWNxPolaZB9KBTAPsPo8fLx4niQYomn3QtJoeLIoCnVLObP7b62C5HF0qjMEqWXr7QXMXRQYBjqfRWGPi7PKcnZ08NxWsaKE1YDWTnwNw5xRx5WDgOUkA2ZLHLlyQ7WF6T2WQFihA4+BnaMLQ1PUZ5Pi+b/RjYcrYAPiQcO7n97h3Z6/BZWokRdpeGtEru9JYPHzW9YxUkzgeWjuQocGQzedmJsL3wYeixDNUvk+e54859/STpx0uz4EiHTbkAFgXu9opZKIDQ2aimQajs/ncPKPo+5Ffj4t9o0bzmODCcXC6XElQDEzy67Z+em1lNfFUz0BJjen/yMKPMgUhAQ45zGeQO7nj0yKzczpzeH4KypbIp+ZqrHEZGWRKSifrLSw0V1wnI9WA42kt8ckyY00cB+bzOTl5Z3IpB2NqnN3d3d1NdJb5PqZCEkDDkLrzLxao37uoNWX2rutPZDoNQhcv1rLwiTKFCAkwXY85zLrTXrYJdYCwLNPRhktRpKONd60d2T9Ys5s9kSHTIkgcPJDYZtNUARNq53Pm8zl2m8/nczAatVwZPpoGV6eIAYMOxbFJ1R211swgLYIkczytpcT3DZm1p2M1VJIZKIMVojVZ5vvIsjddpGRtB6vLa7iThCqSDSz8NVXweiZJi8BEDST4k0xrCE1Mc5z2tE35MQxBk2X4SMsVUKQUfcdvKcztaWxoSXJJJtNiLbBkEsgyrW3hBs5x7F/DFYYhWmsyjLq0Z7nkANdA+bJmHBGtgesZMkUHieN5GqT0fbJMW7WV/axKb+aQxlpRovFwkkAXqTwwUAwtmIoHb95oBNdvKJlMIUF5MQJZ+BAps0yKELMCIgjra8AszqDEytZ0gqwCRR+rZTALVs7EDpOhEdf1TZVO19jPXI8YISmo2AAREpouqvmc4U8wFrHqokiRWbkM7qDJu26tHFuarzXXMpkmrJE4judp62r+JDPdBF0JkE0y3wekRGvteU6WrOkmlx7gskV3FuVVk9cjOgMgUySsIUIXqzWjNoCFeQ7DDLMm+PY7WysadZFRrQ4d4yqlH/ljj5G1HNe4iSoSV6/t4+LFGoEs+7Dl8Mr8tYYosbSmqa52tu2yynpYrXpWVauSOedFsj9ExsUHhSOyXE0mcT6ZOioDgRBCaKmlKU9qqaUwxXtKOYs8mECSS2G5etryp+J3TzqGbGqsvhMRDPTHrVzjppIkuGvsgxvkxIBoPE2HRKLNdL5DkrGmSZAYrCEbZhD0fbvh/CWsJ2D93AgZ17IMKZOEtTWSJHMCz/NcrV2tNdpUAVd7nofjZIkyZ0mZZdcEesjlObfesGYdUFtLZa3O1Mcfstu8D9sRu4DXBfbLzJrdC6fMas0uGci4Nr4Eb2Odz35TWqimaTm/tabjwroaf5DuGjdRkgTcNdhP6iwTN8H6wRrlMp4Mro2HrTMBuH2u7uJiU4h/9UcQLn/4sFz3bDNrVpa1KitLtUTOBPDprajH1V/1nADe+XMfwu7O8uhsVtUX3RxJ3GrF00FY4uQ6fPaovC+2FMygvX32Q4hS79GBbJTr2GsxFSrjIKpz8dSHz558PcA1uE48Ad4+dRawd/pejTwH4MnzQa6RdxEkoH58+fmrgyrl1He/HnB8WPIijgT1Y3nxlbKdelBUWId62UsCb77c5zJaUjTXovcwDnrZy0o37A/7mpyX9d6elZ8mWAVw1VcdHf29PctkkHD09VCDCEd4adVLfmPOCMAx3j/2MgDHSz/Wi9GAY/AtL3nlu93Dkl8+4ITvBrucK2jjmGDvvn7ACa2bXYexzvHAEkZ7uqUc1VWOB+aODg6OLcdrchKyjCVblr35fwPmwjKu42R9zHBxcK0cWKO4khw/jr0ieYXdmuPJ/1uwYwZY86zDEgk3l6ePynHB4hd/tSz54cOjZnxs53/vyT+OJ35946iV8vg+dvvJZMLI9sfNI3ONvhVwZUm8+WWHwe1fp/eOnu9xuXCLz50RN9s87NLLphxbY6AeDvcxbr4YOrqqvAQw1HN/VvTk06+XL3r6XwBD31enu8fu/3bo9Rqry8uJ/E5vKuH+b48Zh16KxmD+4lLr8xePj+VgvDQwXsRnGv71zdZRW6JKXlojfvdxY/Hn02NzvRyNJROZ6fuvnSiggILf39X6uPkeB0xml2YXHhXvbmwXOXD/jH3S+OknQF4U72xsPPnAne0ML3E4QI7WiCfq8oxbvMed1uGP3wB4+u/tk69w++r80H3sI4Dll2e3gPfu9JN+xjTtcgFcuQ1cnT86xLK1w4KJ87NbZmcw+Wd8+5vBhCu3Aa7+cWW9HbJWXki/NVy9BfxGHjPMZW1+q3iv++jImBxOYxfqqYjxt9MNS1UB5GpKO5zGGlMkVw514ZW6Yn6w5LSGHC5cNJz3Gbz3bPzMplx5batxpj/y6GxHDmfKvFut3oOBytmE4nb7wNUvV6uah6yV8t1bQ4frqnCnNvHtoTPf+GbFAg8dx+S52SDbKnJ1virW0SK/PDfj0HBX599lhynryP85hbrMinhXOXyDdOwBr1xw2bwxuMN4FWDOdxypBQf+B1egv7UoES3dAAAAAElFTkSuQmCC',
                //     symbolSize: [150, 170],
                //     symbolOffset: [0, -80],
                //     showEffectOn: lightType === MAP_OPTION_TYPES.FLEET ? 'render' : 'emphasis', //加载完毕显示特效。//emphasis
                //     rippleEffect: {
                //         brushType: 'stroke',
                //         number: 1,
                //         period: 5,
                //         scale: 1.7,
                //     },
                // },
                // {
                //     name: '运营业务',
                //     type: 'effectScatter',
                //     coordinateSystem: 'geo',
                //     zlevel: 2,
                //     data: [
                //         { value: [114.067772, 22.800597, 3000] },
                //         { value: [113.92703, 22.30368, 3000] },
                //         { value: [114.115494, 22.52457, 3000] },
                //         { value: [114.011747, 22.575352, 3000] },
                //     ],
                //     symbol: 'image://data:image/png;base64,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',
                //     symbolSize: [150, 170],
                //     symbolOffset: [0, -80],
                //     showEffectOn: lightType === MAP_OPTION_TYPES.BUSINESS ? 'render' : 'emphasis', //加载完毕显示特效。//emphasis
                //     rippleEffect: {
                //         brushType: 'stroke',
                //         number: 1,
                //         period: 5,
                //         scale: 1.7,
                //     },
                // },
            ],
        };

        changeOption(mapOption);

        mapWapperRef?.current?.setOption(mapOption, true);

        //点击前解绑，防止点击事件触发多次
        mapWapperRef?.current?.off('click');
        //  mapWapperRef.current.on('click', echartsMapClick);
    };

    // 地图
    const initMapEchart = () => {
        // 基于准备好的dom，初始化echarts实例
        let chart = new Echarts.init(document.getElementById('map-wapper'));
        mapWapperRef.current = chart;
        return chart;
    };

    const changeMapLightOptionByType = (type) => {
        if (lightType != type) {
            changeLightType(type);
        } else {
            changeLightType('');
        }
    };
    return (
        <div className={styles.middleWapper}>
            <div id="map-wapper" ref={mapWapperRef} className={styles.mapWapper}></div>
            <div className={styles['tips-space']}>
                <img className={styles['tips-img']} src={store_station_bar_img} alt="" />
                <img className={styles['tips-img']} src={business_station_bar_img} alt="" />
            </div>
        </div>
    );
};
export default MapWapper;
