@import '~antd/lib/style/themes/default.less';
@import '~@/assets/styles/common.less';
html {
    width: 100%;
    height: 100%;
}
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
:global {
    .test{
        width:100%;
    }

}
.storeMain{
    position: relative;
    display: flex;
    flex-direction: column;
    width: 5760px;
    height: 2160px;
    padding: 0 0 20px 0;
    background: #eee;
    background-image: url('./images/new_background.jpg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform-origin: 0% 0%;
    line-height: 1;

}
.mainHeader{
    position: relative;
    flex-shrink: 0;
    width: 100%;
    height:250px;
    z-index: 1;
    .header-main{
        position: relative;
    width: 100%;
    height:100%;
        display: flex;
        justify-content:flex-end;
        align-items: center;
        // background-image: url('./images/screen_header.png');

        .header-bg{
            position: absolute;
            width: 100%;
            // height: 425px;
            top:0;
            left:0;
        }

.header-title{
    position: relative;
z-index: 1;
    margin: 0 auto;
    transform: translateX(-60px);
    font-size: 100px;
    font-family: 'DINProBold';
    font-weight: bold;
    text-align: left;
    color: transparent;
    background: linear-gradient(0deg,#fff 0%, #fff 0%, #93b1ea 0%, #fff 100%);
    background-clip: text;
-webkit-background-clip: text;
font-style: italic;
}
.header-data{
    padding-right:160px;
    font-size: 78px;
    font-family: 'DINProBold';
    font-weight: bold;
    text-align: left;
    color: transparent;
    background: linear-gradient(0deg,#fff 0%, #fff 0%, #93b1ea 0%, #fff 100%);
    background-clip: text;
-webkit-background-clip: text;
}
    }
}
.mainWapper{
    display: flex;
    width: 100%;
    height: 100%;
    padding:0 110px;
    .middleWapper{
        position: relative;
        flex:1;
        height: 100%;
        .mapWapper{
            position: relative;
            width: 100%;
            height: 100%;

        }

        .tips-space{
            position: absolute;
            width: 374px;
            top:260px;
            right:60px;
            z-index: 1;
            .tips-img{
                margin: 30px 0;
                display: block;
                width:100%;
            }
        }
    }

    .leftWapper,.rightWapper{
        flex-shrink: 0;
        padding: 50px 70px;
        .number-scroll-bar-title{
            margin:70px 0;
            font-size: 51px;
            color:#fff;
            font-weight: bold;
            text-align: center;

        }
        .number-scroll-bar{
            width: 100%;
            display: flex;
            align-items: center;
            color:#fff;
            font-size: 120px;
            font-family: 'DINProBold';
            font-weight: bold;
            .number-scroll-item{
                flex:1;
                .item-main{
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width:136px;
                height:192px;
                background-image: url("./images/number_item_bg.png");
                .item-num{
                    margin-top:-20px;
                }
                .item-tips{
                    position: absolute;
                    bottom:20px;
                    right:0;
                    transform: translateX(100%);
                }
                }
            }
        }
        .left-show-list{
            margin:70px 0;
            width: 100%;
            padding: 32px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .under-view{
            display: flex;
            align-items: center;
            padding:100px 0;
            width:100%;
            z-index: 1;
            .chart-bar-img{
                width: 100%;
            }
            .chart-bar-view{
                margin-top:13px;
                width: 100%;
            height:450px;
            background-color: rgba(0,6,13,0.5);
            }
            .bar-tips{
                position: relative;
                &::before{
                    position: absolute;
                    display: block;
                    width: 44px;
                    height: 13px;
                    background-color: #2773D0;
                    top:50%;
                    left:0;
                    content:'';
                    transform: translate(-120%,-50%);
                }
            }

            .under-round{
                flex:1;
                display: flex;
                justify-content: center;
                align-items: center;
                .under-round-main{
                    position: relative;
                    width:527px;
                    height:568px;
                    background: url('./images/left_round_bg.jpg') no-repeat 100% 100%;

                    .under-round-act{
                        position: absolute;
                        width:372px;
                        height:363px;
                    background: url('./images/left_round_act.png') no-repeat 100% 100%;
                    top:65px;
                    left:77px;
                    animation: loopRotate 3s linear infinite;

                    }


                    .round-info{
                        position: absolute;
                        top:45%;
                        left:50%;
                        transform: translate(-50%,-50%);
                        text-align: center;

                    }

                    .under-round-num{
                        color:#15E1FD;
                        font-weight: bold;
                        font-family: 'DINProBold';
                        .num-price{
                            font-size: 114px;
                        }
                        .num-unit{
                            font-size: 80px;
                        }
                    }
                    .under-round-name{
                        margin-top:25px;
                        color:#fff;
                        font-size: 40px;
                    }
                }
            }
            .under-info{
                flex:1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .under-line{
                    margin:50px 0;
                }
            }
        }
    }
}
.card-wapper{
    .card-header{
        display: flex;
        justify-content: space-between;
        padding:0 40px 0 220px;
        height: 98px;
        background-image: url('./images/card_header.png');
        background-size: auto 100%;
        background-repeat: no-repeat;
        font-size: 42px;
        font-weight: bold;
        color:#fff;
        line-height: 100px;
        text-shadow: 4px 8px 0 0 rgba(22,22,19,0.27);

    }
    .card-main{
        width: 100%;
        min-height: 400px;
    }
}
.show-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .show-logo{
        flex-shrink: 0;
        width:126px;
        height:125px;
    }
    .show-item-info{
        padding-left:36px;
        flex:1;
    }
    .show-number{
        margin-bottom: 30px;
        font-size: 76px;
        font-weight: bold;
    color: transparent;
    font-family: 'DINProBold';
    text-shadow: 0 6px 0 0 rgba(43,159,214,0.20);



        &.green{
            background: linear-gradient(0deg, #01D99F 9%, #fff 100%);
            // color:#01D99F
            background-clip: text;
        -webkit-background-clip: text;
        }
        &.yellow{
            background: linear-gradient(0deg, #FFC561 9%, #fff 100%);
            background-clip: text;
        -webkit-background-clip: text;

            // color:#FFC561
        }
        &.blue{
            background: linear-gradient(0deg, #49C5FF 9%, #fff 100%);
            background-clip: text;
        -webkit-background-clip: text;

            // color:#49C5FF
        }


    }
    .show-info{
        display: flex;
        align-items: center;
        justify-content: center;
        color:#fff;
        font-size: 40px;

    }
    .info-name{
        color:#fff;

    }
    .info-unit{

        //
    }

    &.big{
        .show-logo{
            flex-shrink: 0;
            width:154px;
            height:153px;
        }
        .show-number{
            font-size: 93px;
        }
        .show-info{
            font-size: 50px;
        }
    }
}
.right-show-list{
    margin-top:80px;
    display: flex;
    justify-content: space-between;

}


.right-show-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    .right-show-logo{
        flex-shrink: 0;
        width:270px;
        height:291px;
    }
    .right-show-item-info{
        margin-top:36px;
    }
    .right-show-number{
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
        font-size: 110px;
        font-weight: bold;
    color: transparent;
    font-family: 'DINProBold';
    text-shadow: 0 6px 0 0 rgba(43,159,214,0.20);
    background: linear-gradient(0deg, #49C5FF 9%, #fff 100%);
            background-clip: text;
        -webkit-background-clip: text;

    .right-show-unit{
        margin-top:25px;
        font-size: 54px;
    }





    }
    .right-show-remark{
        display: flex;
        align-items: center;
        justify-content: center;
        color:#fff;
        font-size: 44px;

    }

}

@keyframes loopRotate {
    from {
        transform: rotate(0deg);

    }
    to {
        transform: rotate(359deg);

    }
}
