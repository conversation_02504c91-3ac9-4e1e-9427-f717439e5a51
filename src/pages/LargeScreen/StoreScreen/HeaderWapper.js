import { Fragment, useEffect, useRef, useMemo, useState } from 'react';
import { useIntl, getLocale } from 'umi';
import styles from './Screen.less';

import screen_header_img from './images/screen_header.png';

import moment from 'moment';

const HeaderWapper = (props) => {
    const intl = useIntl();

    const dateRef = useRef();

    const [dataTime, changeDataTime] = useState(moment().format('YYYY-MM-DD hh:mm'));

    useEffect(() => {
        const intervalHandle = setInterval(() => {
            changeDataTime(moment().format('YYYY-MM-DD hh:mm'));
        }, 5000);
        return () => {
            clearInterval(intervalHandle);
        };
    }, []);

    return (
        <Fragment>
            <div className={styles['header-main']}>
                <img className={styles['header-bg']} src={screen_header_img}></img>
                {/* <div className={styles['header-title']}>
                    {intl.formatMessage({
                        id: 'screen.store.title',
                    })}
                </div> */}

                {/* <div className={styles['header-data']}>{dataTime}</div> */}
            </div>
        </Fragment>
    );
};
export default HeaderWapper;
