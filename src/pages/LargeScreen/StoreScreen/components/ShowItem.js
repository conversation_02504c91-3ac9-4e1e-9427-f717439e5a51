import { Fragment } from 'react';
import styles from '../Screen.less';
import classnames from 'classnames';

const ShowItem = (props) => {
    const { title, count, name, color, unit, logo, big = false } = props;
    return (
        <Fragment>
            <div className={classnames(styles['show-item'], big ? styles['big'] : '')}>
                <img src={logo} className={styles['show-logo']}></img>
                <div className={styles['show-item-info']}>
                    <div className={classnames(styles['show-number'], styles[color])}>{count}</div>
                    <div className={styles['show-info']}>
                        <div className={styles['info-name']}>{name}</div>
                        <div className={styles['info-unit']}>（{unit}）</div>
                    </div>
                </div>
            </div>
        </Fragment>
    );
};

export default ShowItem;
