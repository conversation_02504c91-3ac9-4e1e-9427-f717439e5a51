import { Fragment } from 'react';
import styles from '../Screen.less';
import classnames from 'classnames';

const CardWapper = (props) => {
    const { title, children, long = false, headerAfter } = props;
    return (
        <Fragment>
            <div className={classnames(styles['card-wapper'])}>
                <div className={classnames(styles['card-header'], (long && styles['long']) || '')}>
                    <span>{title}</span>
                    {headerAfter && <span>{headerAfter}</span>}
                </div>
                <div className={styles['card-main']}>{children}</div>
            </div>
        </Fragment>
    );
};

export default CardWapper;
