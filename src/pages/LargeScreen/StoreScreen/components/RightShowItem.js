import { Fragment } from 'react';
import styles from '../Screen.less';
import classnames from 'classnames';

const RightShowItem = (props) => {
    const { count, name, color, unit, logo } = props;
    return (
        <Fragment>
            <div className={classnames(styles['right-show-item'])}>
                <img src={logo} className={styles['right-show-logo']}></img>
                <div className={styles['right-show-item-info']}>
                    <div className={classnames(styles['right-show-number'], styles[color])}>
                        {count}

                        {unit && <div className={styles['right-show-unit']}>{unit}</div>}
                    </div>
                    <div className={styles['right-show-remark']}>{name}</div>
                </div>
            </div>
        </Fragment>
    );
};

export default RightShowItem;
