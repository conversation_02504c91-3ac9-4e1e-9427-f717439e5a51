import { getLocale } from 'umi';

export const formatScreenLocalNumberInfo = (count = 0) => {
    let result = 0;
    const localeType = getLocale();
    const lowerType = localeType.toLowerCase();
    let unit = '';
    switch (lowerType) {
        case 'en-us':
            let baseNum = 1;

            if (count >= 10000) {
                baseNum = 1000;
                unit = 'K';
            }
            if (count >= 10000000) {
                baseNum = 1000000;
                unit = 'M';
            }
            const resultNum = Math.round((Number(count) / baseNum) * 100) / 100;
            result = `${resultNum}`;
            break;
        case 'zh-cn':
            let zhbaseNum = 1;

            if (count >= 100000) {
                zhbaseNum = 10000;
                unit = '万';
            }
            if (count >= 10000000) {
                zhbaseNum = 1000000;
                unit = '百万';
            }
            result = Math.round((Number(count) / zhbaseNum) * 100) / 100;

            break;

        default:
            break;
    }
    return { count: result, unit };
};
export const formatScreenChargeNumberInfo = (count) => {
    let result = 0;
    let unit = 'K';
    let baseNum = 1;

    if (count >= 10000) {
        baseNum = 1000;
        unit = 'M';
    }
    const resultNum = Math.round((Number(count) / baseNum) * 100) / 100;
    result = `${resultNum}`;
    return { count: result, unit };
};
