import { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { connect, useIntl, getLocale } from 'umi';
import CountUp from 'react-countup';

import styles from './Screen.less';
import CardWapper from './components/CardWapper';
import ShowItem from './components/ShowItem';

import Echarts from 'echarts';

import show_logo_charge from './images/show_logo_charge.png';
import show_logo_store from './images/show_logo_store.png';
import show_logo_battery from './images/show_logo_battery.png';

import show_logo_station from './images/show_logo_station.png';
import show_logo_pile from './images/show_logo_pile.png';
import show_logo_power from './images/show_logo_power.png';

import show_logo_chargeNum from './images/show_logo_chargeNum.png';
import show_logo_fleet from './images/show_logo_fleet.png';
import show_logo_driver from './images/show_logo_driver.png';

import income_show_icon from './images/income_show_icon.png';
import charge_show_icon from './images/charge_show_icon.png';
import station_show_icon from './images/station_show_icon.png';

import big_station_icon from './images/big_station_icon.png';
import big_device_icon from './images/big_device_icon.png';

import line_chart_img from './images/line_chart_img.png';

import {
    queryScreenOperEcologyTotalApi,
    queryScreenOperEcologyEnergyRecordApi,
} from '@/services/LarageScreen/storeApi';
import { isEmpty } from '@/utils/utils';

import { formatScreenLocalNumberInfo, formatScreenChargeNumberInfo } from './storeFormat';

const prefixInteger = (num, length) => {
    return (Array(length).join('0') + num).slice(-length);
};

let upHandle = null;

const LeftWapper = (props) => {
    const intl = useIntl();

    const [totalInfo, updateTotalInfo] = useState();

    const [barData, updateBarData] = useState([]);

    const barChart = useRef();

    const totalNumRef = useRef();

    useEffect(() => {
        initTotalInfo();
        // initBarInfo();

        upHandle = setInterval(() => {
            // if (totalNumRef.current != totalInfo?.energyPq) {
            //     updateTotalInfo({
            //         ...totalInfo,
            //         energyPq: totalNumRef.current + Math.round(Math.random() * 5),
            //     });
            // }
            initTotalInfo();
        }, 1000 * 60 * 60);

        return () => {
            if (upHandle) {
                clearInterval(upHandle);
                upHandle = null;
            }
        };
    }, []);

    useEffect(() => {
        if (totalInfo && totalInfo.energyPq > 0) {
            totalNumRef.current = totalInfo.energyPq;
        }
    }, [totalInfo]);

    // useEffect(() => {
    //     if (barData instanceof Array) {
    //         setBarChartOption(barData);
    //     }
    // }, [barData]);

    const initBarInfo = async () => {
        try {
            const {
                data: { energyRecordList },
            } = await queryScreenOperEcologyEnergyRecordApi();
            updateBarData(energyRecordList);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const setBarChartOption = (info) => {
        if (!barChart.current) {
            initBarEchart();
        }
        // 绘制图表

        if (!info) {
            return;
        }

        const dataTime = [];
        const dataList = [];

        const curDate = new Date();
        const curMonth = curDate.getMonth() + 1;
        curDate.setMonth(curMonth, 0);
        const days = curDate.getDate();

        for (let index = 1; index <= days; index++) {
            dataTime.push(`${index}`);
        }

        dataTime.forEach((ele, index) => {
            let count = 0;
            for (const item of info) {
                if (item.dataDay == index) {
                    count += item.energyPq;
                }
            }
            dataList.push(count);
        });

        const option = {
            color: ['#2adecf'],
            textStyle: {
                color: 'rgb(222,222,222)',
            },

            grid: {
                left: '3%',
                right: '3%',
                bottom: '6%',
                top: '10%',
                containLabel: true,
            },
            animationDuration(idx) {
                // 越往后的数据延迟越大
                return idx * 200;
            },
            animationEasing: 'quarticOut',
            xAxis: [
                {
                    data: dataTime,
                    // axisLine: {
                    //     show: true
                    // },
                    axisLine: {
                        show: true,
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 0,
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                },
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '',
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        textStyle: {
                            fontSize: 37,
                        },
                    },
                },
            ],
            series: [
                {
                    name: '',
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        // opacity: 0.5,
                        color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                            {
                                offset: 0,
                                color: 'rgba(0,125,221,1)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(0,166,255,1)',
                            },
                        ]),
                    },
                    itemStyle: {
                        normal: {
                            //   barBorderRadius: [3, 3, 0, 0],
                            color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                {
                                    offset: 0,
                                    color: 'rgba(0,125,221,1)',
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(0,166,255,1)',
                                },
                            ]),
                        },
                    },
                    data: dataList,
                },
            ],
        };
        barChart?.current?.setOption(option, true);
    };

    const initTotalInfo = async () => {
        try {
            const { data } = await queryScreenOperEcologyTotalApi();
            updateTotalInfo(data);

            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const initBarEchart = () => {
        // 基于准备好的dom，初始化echarts实例
        barChart.current = new Echarts.init(document.getElementById('chart-bar'));
    };

    const numScrollList = useMemo(() => {
        if (!isEmpty(totalInfo?.energyPq)) {
            const prefixNum = prefixInteger(totalInfo?.energyPq, 8);

            return prefixNum?.split('')?.map((ele) => Number(ele));
        }
        return [0, 0, 0, 0, 0, 0, 0, 0];
    }, [totalInfo]);

    const renderNumScrollList = useMemo(() => {
        return numScrollList.map((ele, index) => {
            const listLen = numScrollList.length - index;
            return (
                <div key={index} className={styles['number-scroll-item']}>
                    <div className={styles['item-main']}>
                        <CountUp
                            className={styles['item-num']}
                            decimals={0}
                            start={0}
                            end={ele}
                            duration={4}
                        />
                        {/* {listLen % 3 == 1 && listLen > 3 && (
                            <span className={styles['item-tips']}>,</span>
                        )} */}
                    </div>
                </div>
            );
        });
    });

    return (
        <Fragment>
            <div className={styles['leftWapper']}>
                <CardWapper
                    title={intl.formatMessage({
                        id: 'screen.store.left.overview',
                    })}
                >
                    <div className={styles['number-scroll-bar-title']}>
                        {intl.formatMessage({
                            id: 'screen.store.left.charge',
                        })}
                        (
                        {intl.formatMessage({
                            id: 'screen.store.unit.kWh',
                        })}
                        )
                    </div>
                    <div className={styles['number-scroll-bar']}>{renderNumScrollList}</div>
                    <div className={styles['left-show-list']}>
                        <ShowItem
                            logo={station_show_icon}
                            count={totalInfo?.energyStationNum || 0}
                            color={'blue'}
                            unit={intl.formatMessage({
                                id: 'screen.store.unit.one',
                            })}
                            name={intl.formatMessage({
                                id: 'screen.store.left.stationNum',
                            })}
                        ></ShowItem>

                        <ShowItem
                            logo={charge_show_icon}
                            count={
                                formatScreenChargeNumberInfo(totalInfo?.energyCapacity)?.count || 0
                            }
                            color={'blue'}
                            unit={`${
                                formatScreenChargeNumberInfo(totalInfo?.energyCapacity)?.unit ===
                                'K'
                                    ? 'k'
                                    : formatScreenChargeNumberInfo(totalInfo?.energyCapacity)?.unit
                            }${intl.formatMessage({
                                id: 'screen.store.unit.Wh',
                            })}`}
                            name={intl.formatMessage({
                                id: 'screen.store.left.capacity',
                            })}
                        ></ShowItem>
                        <ShowItem
                            logo={income_show_icon}
                            count={formatScreenLocalNumberInfo(totalInfo?.energyIncome).count || 0}
                            color={'blue'}
                            unit={`${
                                formatScreenLocalNumberInfo(totalInfo?.energyIncome).unit
                            }${intl.formatMessage({
                                id: 'screen.store.unit.yuan',
                            })}`}
                            name={intl.formatMessage({
                                id: 'screen.store.left.income',
                            })}
                        ></ShowItem>
                    </div>
                </CardWapper>

                <CardWapper
                    title={intl.formatMessage({
                        id: 'screen.store.left.line',
                    })}
                    // headerAfter={
                    //     <span className={styles['bar-tips']}>
                    //         {`${intl.formatMessage({
                    //             id: 'screen.store.middle.discharge',
                    //         })}
                    //     （${intl.formatMessage({
                    //         id: 'screen.store.unit.kWh',
                    //     })}
                    //     ）`}
                    //     </span>
                    // }
                >
                    <div className={styles['under-view']}>
                        <div className={styles['under-round']}>
                            <div className={styles['under-round-main']}>
                                <div className={styles['under-round-act']}></div>
                                <div className={styles['round-info']}>
                                    <div className={styles['under-round-num']}>
                                        <span className={styles['num-price']}>
                                            {formatScreenChargeNumberInfo(
                                                totalInfo?.polymerizeCapacity,
                                            )?.count || 0}
                                        </span>
                                        <span className={styles['num-unit']}>
                                            {`${
                                                formatScreenChargeNumberInfo(
                                                    totalInfo?.polymerizeCapacity,
                                                )?.unit
                                            }${intl.formatMessage({
                                                id: 'screen.store.unit.W',
                                            })}`}
                                        </span>
                                    </div>
                                    <div className={styles['under-round-name']}>
                                        {intl.formatMessage({
                                            id: 'screen.store.left.roundName',
                                        })}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className={styles['under-info']}>
                            <ShowItem
                                logo={big_station_icon}
                                count={totalInfo?.polymerizeStationNum || 0}
                                color={'blue'}
                                unit={`${intl.formatMessage({
                                    id: 'screen.store.unit.one',
                                })}`}
                                name={intl.formatMessage({
                                    id: 'screen.store.left.station',
                                })}
                                big
                            ></ShowItem>
                            <div className={styles['under-line']}></div>
                            <ShowItem
                                logo={big_device_icon}
                                count={totalInfo?.polymerizeEquipNum || 0}
                                color={'blue'}
                                unit={`${intl.formatMessage({
                                    id: 'screen.store.unit.one',
                                })}`}
                                name={intl.formatMessage({
                                    id: 'screen.store.left.device',
                                })}
                                big
                            ></ShowItem>
                        </div>
                    </div>
                </CardWapper>
            </div>
        </Fragment>
    );
};
export default LeftWapper;
