import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { connect, useIntl, setLocale } from 'umi';
import qs from 'qs';
import HeaderWapper from './HeaderWapper';
import MapWapper from './MapWapper';
import LeftWapper from './LeftWapper';
import RightWapper from './RightWapper';

import styles from './Screen.less';

const Screen = (props) => {
    const { history, dispatch } = props;

    const intl = useIntl();
    const [winSize, changeWinSize] = useState({
        width: 5760,
        height: 2160,
    });

    const {
        location: { query },
    } = history;
    useEffect(() => {
        const { clientWidth, clientHeight } = document.body;
        changeWinSize(clientWidth, clientHeight);

        const { search } = window.location;
        const { locale } = qs.parse(search, { ignoreQueryPrefix: true });
        if (!locale) {
            const historyLocal = window.localStorage.getItem('local');
            if (historyLocal) {
                setLocale(historyLocal);
            }
        }
    }, []);

    const scale = useMemo(
        () => `scale(${winSize.width / 5760}, ${winSize.height / 2160})`,
        [winSize.width, winSize.height],
    );
    return (
        <Fragment>
            <div className={styles.storeMain} style={{ transform: scale }}>
                <div className={styles.mainHeader}>
                    <HeaderWapper {...props}></HeaderWapper>
                </div>
                <div className={styles.mainWapper}>
                    <LeftWapper {...props}></LeftWapper>
                    <MapWapper {...props}></MapWapper>
                    <RightWapper {...props}></RightWapper>
                </div>
            </div>
        </Fragment>
    );
};
export default Screen;
