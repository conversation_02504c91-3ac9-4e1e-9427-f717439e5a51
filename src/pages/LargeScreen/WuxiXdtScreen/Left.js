import React, { Fragment, useState, useEffect, useRef, useMemo } from 'react';
import { connect, useIntl, setLocale, getLocale } from 'umi';
import { Progress } from 'antd';

import { formatScreenLocalNumber, formatNumberRatio } from '@/utils/utils';

import Echarts from 'echarts';
import classNames from 'classnames';
import styles from './Screen.less';

import img1 from './images/img1.png';
import img2 from './images/img2.png';
import img3 from './images/img3.png';
import img4 from './images/img4.png';
import img25 from './images/img25.png';
import us_img25 from './images/us_img25.png';
import company from './images/company.png';

const LeftPage = (props) => {
    const {
        cityInfo,
        wuxiXdtDPmessage: { JoinGeneralView },
    } = props;

    const intl = useIntl();

    const [cdz, changeCdz] = useState(
        intl.formatMessage({
            id: 'page.screen.left.pileNumRank.tipRank',
            defaultMessage: '充电桩运营商排名',
        }),
    );
    const cityInfoRef = useRef({});

    const isChinaLanguage = useMemo(() => {
        if (getLocale() === 'zh-CN') {
            return true;
        }
        return false;
    });

    useEffect(() => {
        cityInfoRef.current = cityInfo;
    }, [cityInfo]);

    const getTitleImg = () => {
        const localeType = getLocale();
        const lowerType = localeType.toLowerCase();
        let titleImg = null;
        switch (lowerType) {
            case 'en-us':
                titleImg = us_img25;
                break;
            case 'zh-cn':
                titleImg = img25;

                break;

            default:
                break;
        }
        return titleImg;
    };

    return (
        <div className={styles.xdt_leftPage}>
            <div className={styles.left_header_title}>
                <img className={styles.img} src={getTitleImg()} />
            </div>
            {/* 充电桩接入概览 */}
            <div className={styles.left_overview}>
                <div className={styles.left_title}>
                    {intl.formatMessage({
                        id: 'page.screen.left.overview.title',
                        defaultMessage: '充电桩接入概览',
                    })}
                </div>
                <div className={styles.left_content_box}>
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img1} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {JoinGeneralView.cityNum > 0 ? JoinGeneralView.cityNum : '--'}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.cover',
                                    defaultMessage: '覆盖',
                                })}
                                &nbsp;
                                {cityInfoRef?.current?.dataType === '3'
                                    ? intl.formatMessage({
                                          id: 'page.screen.left.overview.county',
                                          defaultMessage: '区县',
                                      })
                                    : intl.formatMessage({
                                          id: 'page.screen.left.overview.cities',
                                          defaultMessage: '城市',
                                      })}
                                {isChinaLanguage &&
                                    `（
                                ${intl.formatMessage({
                                    id: 'page.screen.unit.one',
                                    defaultMessage: '个',
                                })}
                                ）`}
                            </div>
                        </div>
                    </div>
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img2} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {JoinGeneralView.stationNum >= 10000
                                    ? formatScreenLocalNumber(JoinGeneralView.stationNum)
                                    : JoinGeneralView.stationNum}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.chargeStation',
                                    defaultMessage: '充电站',
                                })}
                                {isChinaLanguage &&
                                    `（
                                ${
                                    JoinGeneralView.stationNum >= 10000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandSeat',
                                              defaultMessage: '万座',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.oneSeat',
                                              defaultMessage: '座',
                                          })
                                }
                                ）`}
                            </div>
                        </div>
                    </div>
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img3} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>{JoinGeneralView.operNum}</div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.oper',
                                    defaultMessage: '运营商（个）',
                                })}
                            </div>
                        </div>
                    </div>
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img4} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {JoinGeneralView.pileNum >= 10000
                                    ? formatScreenLocalNumber(JoinGeneralView.pileNum)
                                    : JoinGeneralView.pileNum}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.facilities',
                                    defaultMessage: '充电设施',
                                })}
                                {isChinaLanguage &&
                                    `（
                                    ${
                                        JoinGeneralView.pileNum >= 10000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandInd',
                                                  defaultMessage: '万个',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.one',
                                                  defaultMessage: '个',
                                              })
                                    }
                                    ）`}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.left_numers}>
                <div className={styles.left_title}>{cdz}</div>
                <div className={styles.left_content_box}>
                    {/* 图表 */}
                    <div className={styles.cdz_rank}>
                        {JoinGeneralView?.operPileNumList?.slice(0, 5)?.map((item, index) => {
                            let denominator = JoinGeneralView?.operPileNumList?.[0]?.pileNum || 0;
                            let percent = (item.pileNum / denominator) * 100;
                            return (
                                <div key={index} className={styles.cdz_rank_item}>
                                    <div className={styles.cdz_item_index}>{index + 1}</div>
                                    <div className={styles.cdz_item_logo}>
                                        <img
                                            className={styles.cdz_logo_img}
                                            src={item.buildIconUrl ? item.buildIconUrl : company}
                                        />
                                    </div>
                                    <div className={styles.cdz_item_content}>
                                        <div className={styles.rank_content_top}>
                                            <div className={styles.rank_content_title}>
                                                {item.operName}
                                            </div>
                                            <div className={styles.rank_content_value}>
                                                {/* {item.pileNum} */}
                                                {item.pileNum >= 100000 ? (
                                                    <span className={styles.values}>
                                                        {isChinaLanguage
                                                            ? formatScreenLocalNumber(item.pileNum)
                                                            : formatNumberRatio(item.pileNum)}
                                                        {!isChinaLanguage &&
                                                            (item.pileNum >= 1000000 ? 'M' : 'K')}
                                                    </span>
                                                ) : (
                                                    <span className={styles.values}>
                                                        {item.pileNum || 0}
                                                    </span>
                                                )}
                                                {item.pileNum >= 100000
                                                    ? intl.formatMessage({
                                                          id: 'page.screen.unit.disEnCount',
                                                          defaultMessage: ' ',
                                                      })
                                                    : intl.formatMessage({
                                                          id: 'page.screen.unit.disEnOne',
                                                          defaultMessage: ' ',
                                                      })}
                                            </div>
                                        </div>
                                        <div className={styles.rank_content_bottom}>
                                            <Progress
                                                percent={percent}
                                                showInfo={false}
                                                strokeWidt={15}
                                                strokeColor={{ '0%': '#1FFCF9', '100%': '#1FFCF9' }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default connect(({ wuxiXdtDPmessage, loading }) => ({
    loading,
    wuxiXdtDPmessage,
}))(LeftPage);
