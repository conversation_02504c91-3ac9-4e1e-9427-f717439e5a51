import React, { Fragment, useState, useEffect, useRef, useMemo } from 'react';
import { connect, useIntl, getLocale } from 'umi';
import AMapLoader from '@amap/amap-jsapi-loader';
import CountUp from 'react-countup';
import Echarts from 'echarts';
import moment from 'moment';
import scriptjs from 'scriptjs';
import { match } from 'minimatch';
import classnames from 'classnames';
import { getStationByCityCode } from '@/services/newScreenXdt.js';
import jt_bottom from './images/jt_bottom.png';
import jt_top from './images/jt_top.png';
import styles from './Screen.less';
import {
    formatWan,
    formatBillion,
    formatScreenLocalNumber,
    formatNumberRatio,
} from '@/utils/utils';
import img13 from './images/img13.png';
import img14 from './images/img14.png';
import img15 from './images/img15.png';
import img17 from './images/img17.png';
// import img20 from './images/img20.png';
// import img21 from './images/img21.png';

// import us_img20 from './images/us_img20.png';
// import us_img21 from './images/us_img21.png';

const resolvePath = (dir) => {
    return require(`./images/${dir}`);
};
const DATE_TYPE = {
    DAY: '1',
    MONTH: '2',
    YEAR: '3',
};

let map = null;
let cityMarkers = null;
let stationMarkers = null;
let mapAreaStationList = []; // 区县 地图站点坐标
let cityPointData = []; // 充电热点城市

const MiddlePage = (props) => {
    const { dispatch, cityInfo, getvalue, wuxiXdtDPmessage = {} } = props;

    const intl = useIntl();

    const isChinaLanguage = useMemo(() => {
        if (getLocale() === 'zh-CN') {
            return true;
        }
        return false;
    });

    const {
        mapStationList,
        messageList,
        ChargePqRecordTotal,
        ChargePqRecordYearOnYear,
        mapCityList,
        chargePqDataList,
        userDataList,
    } = wuxiXdtDPmessage;
    const initParentInfo = [
        {
            cityName: '全国',
            cityCode: 100000,
        },
    ];
    const messagelist = messageList;
    const [totalPq, changeTotalPq] = useState(0);
    const [chargeType, changeChargeType] = useState(DATE_TYPE.YEAR);
    const [totalOrderNum, changeTotalOrderNum] = useState(0);
    const [totalUserNum, changeTotalUserNum] = useState(0);
    const [parentInfo, changeParentInfo] = useState(initParentInfo);
    const [pointData, changePointData] = useState([]); // 已覆盖城市-全国
    // const [cityPointData, changeCityPointData] = useState([]); // 充电热点城市
    const [mapChart, changeMapChart] = useState({});
    const [option, changeOption] = useState({});

    const loopInterval = useRef();
    const chargeTypeRef = useRef(DATE_TYPE.YEAR);
    const AMapJs = useRef();
    const cityInfoRef = useRef({});
    const geoJsonRef = useRef({});
    const geoParentJsonRef = useRef({});

    let barChart = null;
    let userBarChart = null;
    let zhixiaArr = ['北京市', '天津市', '上海市', '重庆市'];

    useEffect(() => {
        cityMarkers = [];
        stationMarkers = [];

        dispatch({
            // 充电量情况 同比
            type: 'wuxiXdtDPmessage/xdtqryChargePqRecordYearOnYear',
            payload: {
                dataType: cityInfoRef?.current.dataType, // 数据类型 1 全国 2省 3城市 4区
                cityCode: cityInfoRef?.current.cityCode, // 城市编码
                parentCode: cityInfoRef?.current.parentCode, // 父级编码
                dateType: chargeTypeRef.current,
            },
        });
        dispatch({
            // 年度 -充电量&市占  和  用户增长趋势
            type: 'wuxiXdtDPmessage/qryChargePqAndTimesAndUserRate',
            payload: {},
        });
        if (!cityInfo.query) {
            dispatch({
                type: 'wuxiXdtDPmessage/xdtqryStation',
                payload: {},
            });
        }

        loopInterval.current = setInterval(() => {
            // if (chargeTypeRef.current == DATE_TYPE.DAY) {
            //     changeDateTypeEvent(DATE_TYPE.MONTH);
            // } else {
            //     changeDateTypeEvent(DATE_TYPE.DAY);
            // }
        }, 31000);

        initBarEchart();
        initMapEchart();

        return () => {
            clearInterval(loopInterval.current);
        };
    }, []);

    useEffect(() => {
        changeParentInfo(parentInfo);
    }, [parentInfo]);

    useEffect(() => {
        changePointData(pointData);
    }, [pointData]);

    // useEffect(() => {
    //     changeCityPointData(cityPointData);
    // }, [cityPointData]);

    useEffect(() => {
        changeOption(option);
    }, [option]);

    useEffect(async () => {
        cityInfoRef.current = cityInfo;
        // 选择活跃城市
        if (cityInfo && cityInfo.cityCode) {
            dispatch({
                // 充电量情况 同比
                type: 'wuxiXdtDPmessage/xdtqryChargePqRecordYearOnYear',
                payload: {
                    dataType: cityInfoRef?.current.dataType, // 数据类型 1 全国 2省 3城市 4区
                    cityCode: cityInfoRef?.current.cityCode, // 城市编码
                    parentCode: cityInfoRef?.current.parentCode, // 父级编码
                    dateType: chargeTypeRef.current,
                },
            });

            let obj = JSON.stringify(parentInfo);
            // query链接入参,则先初始化
            if (cityInfo.query && parentInfo.length === 1) {
                parentInfo.push(cityInfo);
            }
            if (obj.indexOf(cityInfo.cityCode.toString()) === -1) {
                if (cityInfo.dataType === '4' && parentInfo.length > 2) {
                    parentInfo.pop(); // 如果选中活跃城市为县区且parentInfo.length > 2，则清除县区数据
                }
                if (!cityInfo.query) {
                    parentInfo.push(cityInfo);
                }

                if (cityInfo.dataType === '4' && parentInfo.length > 2) {
                    getMapData(cityInfo.dataType);
                } else {
                    // 直辖市取parentCode刷新地图
                    let code = cityInfo.cityCode;
                    zhixiaArr.forEach((item) => {
                        if (cityInfo.cityName === item) {
                            code = cityInfo.parentCode;
                        }
                    });
                    if (cityInfo.dataType === '3') {
                        const res = await getStationByCityCode({
                            cityCode: cityInfo.cityCode,
                        }); // 获取区县的站点
                        mapAreaStationList = res.data?.stationVoList;
                        // query链接入参,则先初始化
                        if (cityInfo.query) {
                            if (res.data?.stationVoList && res.data?.stationVoList.length > 0) {
                                addMapStation(res.data?.stationVoList);
                            }
                        } else {
                            initGeoJson(code);
                        }
                    }
                }
            }
        }
    }, [cityInfo]);

    useEffect(() => {
        if (chargeTypeRef.current === DATE_TYPE.DAY || chargeTypeRef.current === DATE_TYPE.MONTH) {
            setBarChartOption(ChargePqRecordYearOnYear);
        }
    }, [ChargePqRecordYearOnYear]);
    useEffect(() => {
        if (chargeTypeRef.current === DATE_TYPE.YEAR) {
            setBarChartOption(chargePqDataList);
        }
    }, [chargePqDataList]);

    useEffect(() => {
        setUserBarChartOption(userDataList);
    }, [userDataList]);

    useEffect(() => {
        // query链接入参,则先初始化
        if (cityInfo.query) return;
        if (mapStationList?.sreenMapStationList && mapStationList?.sreenMapStationList.length > 0) {
            addMapStation(mapStationList.sreenMapStationList);
        }
    }, [mapStationList]);

    useEffect(() => {
        updateMapCity(mapCityList);
    }, [mapCityList]);

    useEffect(() => {
        changeTotalPq(
            ChargePqRecordTotal.totalPq >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.totalPq)
                    : formatNumberRatio(ChargePqRecordTotal.totalPq)
                : ChargePqRecordTotal.totalPq || 0,
        );
        changeTotalOrderNum(
            ChargePqRecordTotal.totalOrderNum >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.totalOrderNum)
                    : formatNumberRatio(ChargePqRecordTotal.totalOrderNum)
                : ChargePqRecordTotal.totalOrderNum || 0,
        );
        changeTotalUserNum(
            ChargePqRecordTotal.totalUserNum >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.totalUserNum)
                    : formatNumberRatio(ChargePqRecordTotal.totalUserNum)
                : ChargePqRecordTotal.totalUserNum || 0,
        );
    }, [ChargePqRecordTotal]);

    const run = () => {
        // var data = [
        //     "08-16 12:00 厦门 用户通过新电途启动充电",
        //     "08-16 12:00 上海 用户通过新电途启动充电",
        //     "08-16 12:00 深圳 用户通过新电途启动充电",
        //     "08-16 12:00 北京 用户通过新电途启动充电"
        // ];

        const container = document.querySelector('.container');
        const scroll = document.querySelector('.scroll');

        // function fillData (node, data) {
        //     [...node.children].forEach((item, i) => (item.innerHTML = data[i]));
        // }

        // fillData(scroll, data);

        function move() {
            const top = scroll.style.top || '0px';
            let topNum = +top.replace(/px/g, '');
            if (topNum > -scroll.clientHeight) {
                topNum -= 1;
            } else {
                topNum = scroll.parentNode.clientHeight;
                // scroll.appendChild(scroll.children[0])
            }
            if (scroll.clientHeight == 0) {
                scroll.style.top = `${scroll.parentNode.clientHeight}px`;
            } else {
                scroll.style.top = `${topNum}px`;
            }

            setTimeout(move, 35);
        }

        move();
    };
    const initBarEchart = () => {
        // 基于准备好的dom，初始化echarts实例
        barChart = new Echarts.init(document.getElementById('middle-barchart'));
        userBarChart = new Echarts.init(document.getElementById('middle-user-barchart'));
    };
    // 充电量情况
    const setBarChartOption = (info) => {
        if (!barChart) {
            initBarEchart();
        }

        // 绘制图表

        let thisName = '',
            lastName = '',
            company = '',
            dataTime = [],
            thisData = [],
            lastData = [],
            rate = [];
        if (chargeTypeRef.current === DATE_TYPE.DAY || chargeTypeRef.current === DATE_TYPE.MONTH) {
            // 日表 月表
            thisData = info.thisData;
            lastData = info.lastData;
            rate = info.rate;
            if (!thisData || !lastData || !rate) {
                return;
            }

            if (chargeTypeRef.current == DATE_TYPE.DAY) {
                thisName = intl.formatMessage({
                    id: 'page.screen.unit.thisYear',
                    defaultMessage: '本年',
                });
                lastName = intl.formatMessage({
                    id: 'page.screen.unit.lasterYear',
                    defaultMessage: '去年',
                });
                company = '';
            } else {
                thisName = intl.formatMessage({
                    id: 'page.screen.unit.thisYear',
                    defaultMessage: '本年',
                });
                lastName = intl.formatMessage({
                    id: 'page.screen.unit.lasterYear',
                    defaultMessage: '去年',
                });
                company = intl.formatMessage({
                    id: 'page.screen.unit.month',
                    defaultMessage: '月',
                });
            }

            const curDate = new Date();
            const curMonth = curDate.getMonth() + 1;
            curDate.setMonth(curMonth, 0);
            const days = curDate.getDate();

            if (chargeTypeRef.current == DATE_TYPE.DAY) {
                for (let index = 1; index <= days; index++) {
                    const element = thisData[index];
                    dataTime.push(`${index}`);
                }
            } else {
                for (let index = 1; index <= 12; index++) {
                    dataTime.push(`${index}月`);
                }
            }

            dataTime.forEach((ele, index) => {
                if (!thisData[index]) {
                    thisData[index] = {
                        pq: null,
                    };
                }
                if (!lastData[index]) {
                    lastData[index] = {
                        pq: null,
                    };
                }

                if (!rate[index]) {
                    rate[index] = {
                        rate: null,
                    };
                }
            });
        } else {
            // 年表
            if (!info || info.length === 0) return;
            thisName = intl.formatMessage({
                id: 'page.screen.middle.totalChargeNum',
                defaultMessage: '累计充电量',
            });
            dataTime = info.map((ele) => ele.dataYear);
            thisData = info;
            rate = info;
        }

        let infoIndex = thisData?.length - 1;
        let lastValue;

        if (chargeTypeRef.current === DATE_TYPE.YEAR) {
            lastValue = thisData?.[infoIndex].chargePq;
        } else {
            lastValue = thisData?.[infoIndex].pq;
        }

        const option = {
            color: ['#2adecf'],
            textStyle: {
                color: 'rgb(222,222,222)',
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
                formatter: (params, ticket) => {
                    const data = new Date();
                    const endyear = data.getFullYear();
                    const startyear = Number(endyear) - 1;
                    const yue = data.getMonth() + 1;

                    let htmlStr = `<div  style="padding:10px;border:2px solid #29EDFF;color:#29EDFF;font-size: 24px;">`;
                    for (let i = 0; i < params.length; i++) {
                        const param = params[i];
                        let title = '';
                        if (i == 0) {
                            if (chargeTypeRef.current == DATE_TYPE.DAY) {
                                title = `${endyear}-${yue}-${params[0].name}`;
                            } else if (chargeTypeRef.current == DATE_TYPE.MONTH) {
                                title = `${endyear}-${params[0].name.replace(
                                    intl.formatMessage({
                                        id: 'page.screen.unit.month',
                                        defaultMessage: '月',
                                    }),
                                    '',
                                )}`;
                            } else {
                                title = `${params[0].name}`;
                            }
                        } else if (i == 1) {
                            if (chargeTypeRef.current == DATE_TYPE.DAY) {
                                title = `${startyear}-${yue}-${params[0].name}`;
                            } else if (chargeTypeRef.current == DATE_TYPE.MONTH) {
                                title = `${startyear}-${params[0].name.replace(
                                    intl.formatMessage({
                                        id: 'page.screen.unit.month',
                                        defaultMessage: '月',
                                    }),
                                    '',
                                )}`;
                            } else {
                                title = intl.formatMessage({
                                    id: 'page.screen.unit.yoy',
                                    defaultMessage: '同比',
                                });
                            }
                        } else {
                            title = param.seriesName;
                        }
                        let value = '';
                        let unit = intl.formatMessage({
                            id: 'page.screen.unit.degrees',
                            defaultMessage: '度',
                        });
                        if (
                            param.seriesName !=
                                intl.formatMessage({
                                    id: 'page.screen.unit.yoy',
                                    defaultMessage: '同比',
                                }) &&
                            param.value >= 100000
                        ) {
                            value = formatScreenLocalNumber(param.value);
                            unit = intl.formatMessage({
                                id: 'page.screen.unit.tenThousandDegrees',
                                defaultMessage: '万度',
                            });
                        } else {
                            if (isNaN(param.value)) {
                                value = '-';
                            } else {
                                value = Number(param.value);
                            }
                        }

                        htmlStr += `<p>${title}  ${
                            param.seriesName ==
                            intl.formatMessage({
                                id: 'page.screen.unit.yoy',
                                defaultMessage: '同比',
                            })
                                ? value == '-'
                                    ? ''
                                    : param.value >= 0
                                    ? `<image style="width:12px" src="${jt_top}">`
                                    : `<image style="width:12px" src="${jt_bottom}">`
                                : ''
                        } <span style="color:#fff">${value}</span><span style="color:#fff"> ${
                            param.seriesName ==
                            intl.formatMessage({
                                id: 'page.screen.unit.yoy',
                                defaultMessage: '同比',
                            })
                                ? '%'
                                : unit
                        }</span></p> `;
                    }
                    htmlStr += '</div>';
                    return htmlStr;

                    // `params.name + ':' + params.value + '%'
                },
            },
            legend: {
                data: [
                    thisName,
                    lastName,
                    intl.formatMessage({
                        id: 'page.screen.unit.yoy',
                        defaultMessage: '同比',
                    }),
                ],
                itemWidth: 9,
                itemHeight: 9,
                itemGap: 22,
                right: '2%',
                top: '2%',
                textStyle: {
                    color: '#FFFEFE',
                    fontSize: '26',
                },
            },
            grid: {
                left: '3%',
                right: '3%',
                bottom: '6%',
                top: '30%',
                containLabel: true,
            },
            animationDuration(idx) {
                // 越往后的数据延迟越大
                return idx * 800;
            },
            animationEasing: 'quarticOut',
            xAxis: [
                {
                    data: dataTime,
                    // axisLine: {
                    //     show: true
                    // },
                    axisLine: {
                        show: true,
                        textStyle: {
                            fontSize: 24,
                        },
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 0,
                        color: '#d8e3e5',
                        fontSize: 24,
                    },
                },
            ],
            yAxis: [
                {
                    type: 'value',
                    name: 'kWh',
                    nameTextStyle: {
                        color: '#fff',
                        fontSize: 28,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        show: true,
                        formatter: function (value) {
                            if (chargeTypeRef.current == DATE_TYPE.YEAR) {
                                return formatBillion(value);
                            } else {
                                return formatWan(value);
                            }
                        },
                        color: '#d8e3e5',
                        fontSize: 28,
                    },
                },
                {
                    type: 'value',
                    name: '%',
                    nameTextStyle: {
                        color: '#fff',
                        fontSize: 28,
                        padding: [0, 0, 0, 30],
                    },
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        color: '#d8e3e5',
                        fontSize: 28,
                    },
                },
            ],
            series: [
                {
                    name: thisName,
                    type: 'bar',
                    barWidth: '20%',
                    itemStyle: {
                        normal: {
                            //   barBorderRadius: [3, 3, 0, 0],
                            // color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                            //     {
                            //         offset: 0,
                            //         color: 'rgba(41,237,255,0)',
                            //     },
                            //     {
                            //         offset: 1,
                            //         color: 'rgba(41,237,255,0.8)',
                            //     },
                            // ]),
                            color: '#18FDFE',
                        },
                    },

                    markPoint: {
                        symbol: 'roundRect',
                        symbolSize: [60, 38],
                        symbolOffset: [0, 54],
                        data: [{ name: 'Max', value: '预测', xAxis: infoIndex, yAxis: lastValue }],
                        itemStyle: {
                            color: '#fff',
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            color: '#9B570A',
                            fontSize: 28,
                            fontWeight: 'bold',
                        },
                    },

                    data: thisData.map((ele, index) => {
                        let itemValue;
                        if (chargeTypeRef.current === DATE_TYPE.YEAR) {
                            itemValue = ele.chargePq;
                        } else {
                            itemValue = ele.pq;
                        }
                        if (infoIndex === index) {
                            return {
                                value: itemValue,
                                itemStyle: {
                                    color: '#FEAD51',
                                },
                            };
                        } else {
                            return itemValue;
                        }
                    }),
                },
                // {
                //     name: lastName,
                //     type: 'bar',
                //     barWidth: '20%',
                //     itemStyle: {
                //         normal: {
                //             //   barBorderRadius: [3, 3, 0, 0],
                //             color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [
                //                 {
                //                     offset: 0,
                //                     color: 'rgba(220,72,78,0)',
                //                 },
                //                 {
                //                     offset: 1,
                //                     color: 'rgba(218,32,50,0.8)',
                //                 },
                //             ]),
                //         },
                //     },
                //     data: lastData.map((ele) => {
                //         if (chargeTypeRef.current === DATE_TYPE.YEAR) {
                //             return ele.chargePq;
                //         } else {
                //             return ele.pq;
                //         }
                //     }),
                // },
                {
                    name: intl.formatMessage({
                        id: 'page.screen.unit.yoy',
                        defaultMessage: '同比',
                    }),
                    type: 'line',
                    smooth: true,
                    symbolSize: 20,
                    yAxisIndex: 1,
                    label: {
                        show: true,
                        position: 'top',
                        textStyle: {
                            color: '#fff',
                            fontSize: 28,
                        },
                        formatter: (params) => {
                            return `${formatBillion(params.value)}%`;
                        },
                    },
                    itemStyle: {
                        normal: {
                            color: '#FEAD51', //'rgba(73,230,175,1)',
                            barBorderRadius: [30, 30, 30, 30],
                            lineStyle: {
                                color: '#FEAD51', //'rgba(73,230,175,0.8)',
                                width: 6,
                            },
                        },
                    },
                    data: rate.map((ele) => {
                        if (chargeTypeRef.current === DATE_TYPE.YEAR) {
                            return ele.chargeTimesRate;
                        } else {
                            return ele.rate;
                        }
                    }),

                    markLine: {
                        symbol: 'none',
                        symbolSize: 20,
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    type: 'dashed',
                                    color: '#ccff66',
                                },
                                label: {
                                    show: true,
                                    position: 'left',
                                },
                            },
                        },
                        large: true,
                        effect: {
                            show: false,
                            loop: true,
                            period: 0,
                            scaleSize: 12,
                            color: '#fff',
                            shadowColor: null,
                            shadowBlur: null,
                        },
                        data: [
                            [
                                {
                                    name: '',
                                    xAxis: '',
                                    yAxis: '',
                                },
                                {
                                    name: '',
                                    xAxis: '',
                                    yAxis: '',
                                },
                            ],
                        ],
                    },
                },
            ],
        };
        barChart.setOption(option, true);
    };
    // 用户情况
    const setUserBarChartOption = (info) => {
        if (!userBarChart) {
            initBarEchart();
        }
        // 绘制图表
        let infoIndex = info?.length - 1;
        let lastValue = info?.[infoIndex]?.registerNum;
        let dataTime = info.map((ele) => ele.dataYear);
        let echartData = info.map((ele, index) => {
            if (infoIndex === index) {
                return {
                    value: ele.registerNum,
                    itemStyle: {
                        color: '#FEAD51',
                    },
                };
            } else {
                return ele.registerNum;
            }
        });
        const option = {
            color: ['#2adecf'],
            textStyle: {
                color: 'rgb(222,222,222)',
            },
            legend: {
                data: [
                    intl.formatMessage({
                        id: 'page.screen.middle.totalRegisterNum',
                        defaultMessage: '累计注册用户数',
                    }),
                    intl.formatMessage({
                        id: 'page.screen.middle.newUserYOY',
                        defaultMessage: '新用户YOY',
                    }),
                ],
                itemWidth: 9,
                itemHeight: 9,
                itemGap: 22,
                right: '2%',
                top: '6%',
                textStyle: {
                    color: '#FFFEFE',
                    fontSize: '26',
                },
            },
            grid: {
                left: '3%',
                right: '3%',
                bottom: '6%',
                top: '30%',
                containLabel: true,
            },
            animationDuration(idx) {
                // 越往后的数据延迟越大
                return idx * 800;
            },
            animationEasing: 'quarticOut',
            xAxis: [
                {
                    data: dataTime,
                    // axisLine: {
                    //     show: true
                    // },
                    axisLine: {
                        show: true,
                        textStyle: {
                            fontSize: 24,
                        },
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 0,
                        color: '#d8e3e5',
                        fontSize: 24,
                    },
                },
            ],
            yAxis: [
                {
                    type: 'value',
                    name: intl.formatMessage({
                        id: 'page.screen.unit.one',
                        defaultMessage: '个',
                    }),
                    nameTextStyle: {
                        color: '#fff',
                        fontSize: 28,
                    },
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        show: true,
                        formatter: function (value) {
                            return formatWan(value);
                        },
                        color: '#d8e3e5',
                        fontSize: 28,
                    },
                },
                // {
                //     type: 'value',
                //     name: '%',
                //     nameTextStyle: {
                //         color: '#fff',
                //         fontSize: 24,
                //         padding: [0, 0, 0, 30],
                //     },
                //     axisLine: {
                //         show: false,
                //     },
                //     axisTick: {
                //         show: false,
                //     },
                //     splitLine: {
                //         show: false,
                //     },
                //     axisLabel: {
                //         color: '#d8e3e5',
                //         fontSize: 24,
                //     },
                // },
            ],
            series: [
                {
                    name: intl.formatMessage({
                        id: 'page.screen.middle.totalRegisterNum',
                        defaultMessage: '累计注册用户数',
                    }),
                    type: 'bar',
                    barWidth: '20%',
                    label: {
                        show: true,
                        position: 'top',
                        textStyle: {
                            color: '#fff',
                            fontSize: 28,
                        },
                        formatter: (params) => {
                            return formatWan(params.value);
                        },
                    },
                    itemStyle: {
                        normal: {
                            //   barBorderRadius: [3, 3, 0, 0],
                            color: '#18FDFE',
                        },
                    },
                    markPoint: {
                        symbol: 'roundRect',
                        symbolSize: [60, 38],
                        symbolOffset: [0, 38],
                        data: [{ name: 'Max', value: '预测', xAxis: infoIndex, yAxis: lastValue }],
                        itemStyle: {
                            color: '#fff',
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            color: '#9B570A',
                            fontSize: 28,
                            fontWeight: 'bold',
                        },
                    },
                    data: echartData,
                },
                // {
                //     name: intl.formatMessage({
                //         id: 'page.screen.middle.newUserYOY',
                //         defaultMessage: '新用户YOY',
                //     }),
                //     type: 'line',
                //     label: {
                //         show: true,
                //         position: 'top',
                //         textStyle: {
                //             color: '#fff',
                //             fontSize: 26,
                //         },
                //         formatter: (params) => {
                //             return `${params.value}%`;
                //         },
                //     },
                //     smooth: true,
                //     yAxisIndex: 1,
                //     itemStyle: {
                //         normal: {
                //             color: '#ad8008',
                //             barBorderRadius: [30, 30, 30, 30],
                //             lineStyle: {
                //                 color: '#ad8008',
                //             },
                //         },
                //     },
                //     data: info.map((ele) => ele.userUpRate),
                //     markLine: {
                //         symbol: 'none',
                //         symbolSize: 20,
                //         itemStyle: {
                //             normal: {
                //                 lineStyle: {
                //                     type: 'dashed',
                //                     color: '#ccff66',
                //                 },
                //                 label: {
                //                     show: true,
                //                     position: 'left',
                //                 },
                //             },
                //         },
                //         large: true,
                //         effect: {
                //             show: false,
                //             loop: true,
                //             period: 0,
                //             scaleSize: 12,
                //             color: null,
                //             shadowColor: null,
                //             shadowBlur: null,
                //         },
                //         data: [
                //             [
                //                 {
                //                     name: '',
                //                     xAxis: '',
                //                     yAxis: '',
                //                 },
                //                 {
                //                     name: '',
                //                     xAxis: '',
                //                     yAxis: '',
                //                 },
                //             ],
                //         ],
                //     },
                // },
            ],
        };
        userBarChart.setOption(option, true);
    };
    // 地图

    const initMapEchart = () => {
        // 基于准备好的dom，初始化echarts实例
        let chart = new Echarts.init(document.getElementById('map-view'));
        changeMapChart(chart);
    };
    // 加载高德地图
    const loadMap = async (code) => {
        window._AMapSecurityConfig = {
            securityJsCode: 'f1d96ff30ff169db2e5bf7c5f683113f',
        };
        return new Promise((resolve, reject) => {
            AMapLoader.load({
                key: 'f0ad238a3a4b229bcdd93c77278f6148', //需要设置您申请的key
                version: '2.0',
                plugins: ['AMap.DistrictSearch'],
                AMapUI: {
                    version: '1.1',
                    plugins: [],
                },
                Loca: {
                    version: '2.0.0',
                },
            })
                .then((AMap) => {
                    AMapJs.current = AMap;
                    initGeoJson(code || 100000);
                    resolve(AMap);
                })
                .catch((e) => {
                    reject(e);
                });
        });
    };
    // 初始化地图数据
    const initGeoJson = (adcode) => {
        getGeoJson(adcode).then((data) => {
            // geoJson = data;
            let subFeatures = {
                features: data.subFeatures,
            };
            let parentFeatures = {
                features: data.parentFeatures,
            };
            geoJsonRef.current = subFeatures;
            geoParentJsonRef.current = parentFeatures;
            getMapData('');
        });
    };

    // 获取地图数据
    const getGeoJson = (adcode, childAdcode = '') => {
        return new Promise((resolve, reject) => {
            function insideFun(adcode, childAdcode) {
                AMapUI?.loadUI(['geo/DistrictExplorer'], (DistrictExplorer) => {
                    let districtExplorer = new DistrictExplorer();
                    districtExplorer.loadAreaNode(adcode, async (error, areaNode) => {
                        if (error) {
                            console.error(error);
                            reject(error);
                            return;
                        }
                        let Json = areaNode.getSubFeatures(); // 返回该区域中全部的子级区划Feature数组
                        let parentJson = areaNode.getParentFeature(); // 返回父级区划对应Feature
                        if (Json.length === 0) {
                            let parent = areaNode._data.geoData.parent.properties.acroutes;
                            const { subFeatures = [] } = await getGeoJson(
                                parent[parent.length - 1],
                                adcode,
                            );
                            Json = subFeatures;
                        }

                        if (childAdcode) {
                            Json = Json.filter((item) => {
                                return item.properties.adcode == childAdcode;
                            });
                        }
                        let mapJson = {
                            subFeatures: Json,
                            parentFeatures: [parentJson],
                        };
                        resolve(mapJson);
                    });
                });
            }
            insideFun(adcode, childAdcode);
        });
    };

    //获取点位数据 type: back返回上一级;
    const getMapData = (dataType) => {
        let mapData = [];

        if (dataType === '4') {
            let mapCity = [];
            mapCityList.forEach((item) => {
                if (item.cityCode == cityInfoRef?.current.parentCode) {
                    mapCity.push({
                        value: [item.lon, item.lat, 3000],
                    });
                }
            });

            option.baseOption.geo.regions = [
                {
                    name: cityInfoRef?.current.cityName, //与china.js对应的省份名称
                    itemStyle: {
                        areaColor: '#c9b410', //省份背景色
                    },
                },
            ]; // 高亮县区
            option.baseOption.series[3].data = mapCity; // 充电热点城市

            mapChart.setOption(option, true);
        } else {
            geoJsonRef?.current?.features.forEach((item) => {
                let value = Math.random() * 3000;
                mapData.push({
                    name: item.properties.name,
                    value: value,
                    cityCode: item.properties.adcode,
                });
            });

            let arr = [];
            let cityArr = [];
            changePointData([]);
            // changeCityPointData([]);
            cityPointData = [];
            if (
                cityInfoRef?.current.query ||
                cityInfoRef?.current.dataType === '3' ||
                cityInfoRef?.current.dataType === '4'
            ) {
                mapAreaStationList.forEach((item) => {
                    arr.push({
                        value: [item.lon, item.lat, 3000],
                    });
                });
                mapStationList?.xdtScreenCityMapList?.forEach((item) => {
                    if (item.cityCode == cityInfoRef?.current.cityCode) {
                        cityArr.push({
                            value: [item.lon, item.lat, 3000],
                        });
                    }
                });
            } else {
                mapStationList?.sreenMapStationList?.forEach((item) => {
                    arr.push({
                        value: [item.lon, item.lat, 3000],
                    });
                });
                mapStationList?.xdtScreenCityMapList?.forEach((item) => {
                    cityArr.push({
                        value: [item.lon, item.lat, 3000],
                    });
                });
            }
            pointData.push(...arr);
            cityPointData.push(...cityArr);

            mapData = mapData.sort((a, b) => {
                return b.value - a.value;
            });
            setMapChartOption(mapData);
        }
    };
    const setMapChartOption = (mapData) => {
        if (!mapChart) {
            initMapEchart();
        }
        //全国的时候才显示南海诸岛  只有当注册的名字为china的时候才会显示南海诸岛
        new Echarts.registerMap(parentInfo.length === 1 ? 'china' : 'map', geoJsonRef?.current);
        new Echarts.registerMap('mapOutline', geoParentJsonRef.current);
        let mapOption = {
            baseOption: {
                title: [],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '0%',
                    top: '5%',
                    containLabel: true,
                },
                geo: {
                    map: parentInfo.length === 1 ? 'china' : 'map',
                    zoom: parentInfo.length === 1 ? 1.6 : 1.2, // 1.7
                    // left: '10%',
                    top: parentInfo.length === 1 ? '30%' : '10%',
                    label: {
                        normal: {
                            show: false,
                            color: 'rgb(249, 249, 249)', //省份标签字体颜色
                            formatter: (p) => {
                                switch (p.name) {
                                    case '内蒙古自治区':
                                        p.name = '内蒙古';
                                        break;
                                    case '西藏自治区':
                                        p.name = '西藏';
                                        break;
                                    case '新疆维吾尔自治区':
                                        p.name = '新疆';
                                        break;
                                    case '宁夏回族自治区':
                                        p.name = '宁夏';
                                        break;
                                    case '广西壮族自治区':
                                        p.name = '广西';
                                        break;
                                    case '香港特别行政区':
                                        p.name = '香港';
                                        break;
                                    case '澳门特别行政区':
                                        p.name = '澳门';
                                        break;
                                }
                                return p.name;
                            },
                        },
                        emphasis: {
                            show: true,
                            color: '#fff',
                        },
                    },
                    itemStyle: {
                        normal: {
                            areaColor: 'rgba(0,7,12,0.75)',
                            borderColor: '#98CEDB',
                            borderWidth: 1.3,
                            // shadowBlur: 15,
                            // shadowColor: '#0092A6',
                            // shadowOffsetX: 7,
                            // shadowOffsetY: 6,
                        },
                        // emphasis: {
                        //     areaColor: '#8dd7fc',
                        //     borderWidth: 1.6,
                        //     shadowBlur: 25,
                        // },
                    },
                },
                series: [
                    {
                        map: 'mapOutline',
                        silent: true,
                        type: 'map',
                        zoom: parentInfo.length === 1 ? 1.6 : 1.2,
                        label: {
                            normal: {
                                show: false,
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                            emphasis: {
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                        },
                        top: parentInfo.length === 1 ? '30%' : '10%',
                        itemStyle: {
                            normal: {
                                areaColor: 'rgba(0,255,255,.02)',
                                borderColor: '#c4ebf3',
                                borderWidth: 6,
                                shadowColor: '#00e2ff',
                                shadowOffsetX: 5,
                                shadowOffsetY: 4,
                                shadowBlur: 15,
                            },
                            emphasis: {
                                areaColor: 'transparent', //悬浮背景
                                textStyle: {
                                    color: '#fff',
                                },
                            },
                        },
                    },
                    {
                        name: intl.formatMessage({
                            id: 'page.screen.middle.chargeMap',
                            defaultMessage: '充电地图',
                        }),
                        type: 'map',
                        geoIndex: 0,
                        map: parentInfo.length === 1 ? 'china' : 'map',
                        silent: true,
                        zoom: parentInfo.length === 1 ? 1.6 : 1.2,
                        itemStyle: {
                            color: '#CDEEF5',
                            shadowBlur: 10,
                            shadowColor: '#00e2ff',
                        },
                        label: {
                            normal: {
                                show: false,
                            },
                            emphasis: {
                                show: false,
                            },
                        },
                        data: mapData,
                    },
                    {
                        name: intl.formatMessage({
                            id: 'page.screen.middle.coverCity',
                            defaultMessage: '已覆盖城市',
                        }),
                        type: 'effectScatter',
                        coordinateSystem: 'geo',
                        // rippleEffect: {
                        //     brushType: 'fill',
                        // },
                        // itemStyle: {
                        //     normal: {
                        //         color: '#F4E925',
                        //         shadowBlur: 10,
                        //         shadowColor: '#333',
                        //     },
                        // },

                        data: pointData,
                        symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAMCAYAAABfnvydAAAACXBIWXMAAAsTAAALEwEAmpwYAAAApklEQVQYlX3QoUrDYRjF4ed7F4UJBgWLbbcg4hXZ7MKSzTps3oMDL0KzTVDEuPCCweLUd+UbDPlvB045v3PKaZGl6wjnGOEJHyCyWmRdR9Z3ZFX3T2TNImvUIusCt4Y1bZH1iNMthddAbYHwF7jfUZi3yDrAG/b/wSUmgcTNwPoO763/sIcXHHf4iQkW0YMvXG6sr7CAtvEkPOAQZ/gdKpxgjOd1sAI0kDi4dqcGyAAAAABJRU5ErkJggg==',
                        symbolSize: [16, 24],
                        showEffectOn: 'emphasis', //加载完毕显示特效
                    },
                    {
                        name: '充电热点城市',
                        type: 'effectScatter',
                        coordinateSystem: 'geo',
                        showEffectOn: 'render',
                        zlevel: 1,
                        rippleEffect: {
                            period: 3,
                            scale: 4,
                            brushType: 'fill',
                        },
                        hoverAnimation: true,
                        itemStyle: {
                            normal: {
                                color: '#f68717',
                                shadowBlur: 10,
                                shadowColor: '#333',
                            },
                        },
                        symbolSize: 24,
                        data: cityPointData,
                    },
                    // {
                    //     name: '充电热点城市',
                    //     type: 'effectScatter',
                    //     coordinateSystem: 'geo',
                    //     rippleEffect: {
                    //         brushType: 'fill',
                    //         number: 1,
                    //         period: 4,
                    //         scale: 1.9,
                    //     },
                    //     // itemStyle: {
                    //     //     shadowBlur: 1,
                    //     //     normal: {
                    //     //         color: '#F4E925',
                    //     //         shadowBlur: 3,
                    //     //         shadowColor: '#333',
                    //     //     },
                    //     // },

                    //     data: cityPointData,
                    //     symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAaCAYAAABCfffNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFAElEQVRIiZ2V229c1RWHv7X2uczFMx5fYo/t2A4JIpBAXKl9on2oWvFQ/oS+VPAGUpFAkas8VEIQgQSi5SYlQtCXtohKbd9aWpBokegL4oFbxEWASnDskZ0EZzL2jGfO3ouHOTYT2wHE1tnaW+es/fvOb+21zxF7hW9u9bg/NnpV4FfAT4E/AC9Tj8O3rIZGD/lWCNwM3IPwa4qaEUlgKzi2LAIeAF6gHje/D0SAO4F7cPILCtq1hl1i2VqWSVHK1mVWh6ViNdohwngWOMNU/MEepZW9kDJwN/A0qXgT3bRlu8gabVunSw9vKlUxrlI0JzVSphmScRmTTkjJ7F/AGeAfNhMbgFz4GnIYuA/hPsqaha5csQbrdlnaYZNMMoLl9sxRFU/TABEgxknFEhmXok7YuHgr0QkOeBg4DXTFXuEwGe8xpKUwHmXhQrjoV+QiLelawAQwATEDAXNSlYwmAmD9SwAn6mpW0oNWl6pUdC1TMjOEQgQ0UBKWwrpmvZrUdJJURnyTS9aStdDFY/QJAAoSwAwQRdSEghVcxSZ12MYUQ1Yzeg3W4wn+vu0E4AH7kns5T4spVOrcZkMOr4Lf0DW/oauhK1t9GFWEpqg5KdpQVLYJl4aqdgLSDrQ+44ukSS06RkETbgfe2oYIEOwD3s1WeFNinB7iqM5yu5XUQlEly9yVrC2reIklDupKdiCWUNSmp3fRmhtLstZpsB5FFEs32Fhpjj/5m9KT7uOta6rrl9bmSTvHlazFa5bfdDPMu2lulRGZCcPOQizmNoNK07O5LGsbS7LWvUob6+/R+ChHktusIo55fzS94D7aIhoo3xelyM/DBD+wLYZCoCUGvRU+763wuRu2kfjG7MeuwHRzSZavLrnV0DPf3ydBzCgl1LIpstRx2h9NL+wcul3n5Efmed3e51K7xcs7iczrN5nnmKT8ZOlD9555uiJ5AeTlPDEabigdtxGEBMi2RXXX+XxLHM+Eg6y7hDnvIDjwUX+0PDqoOO8EL+Cd4FWoFmySgyjC/YMAgMgtLlxD8Y+980g0yklWKXQ2OA9fOzHpT72KBDMQ7T8UwVUtKdXsHPDUoJ5bXNjjBLe40ERYZI64GPHD4CBEEDQ/dAheUa+KV/Aq1At+Lp2lTP+TsqftgeSg30VlPqWGBofz2ynTviOvor4/YorLRskKZfsn8OfvDMnbmWiG+WH4mXfg3baTfroyB5kKc4k/VJmx0eu5+EaIW1z4m4v5S3eaywrVTCHkexIU50WI1ZL2pHSSxM4C/7meVrTPPdmedL2cLY7YXdEqBxpO/h3UhABeES/CfNybL01aqevlbPHUiZ11/rF3bFBQd4nL4Lx46sT7GfL77qy0ihmHArkTQSsaKu1p6YjaE8VTJz4eXO8WF8QtLsggZI943hVwj742eTotcXMUM+v7fxC8iE6lfuLAmB//76dDjwNul9YO7HpOdBsAuIdenbT1tvsN01JJOxwCmMAP+RlCO9MH73juyOYARNnnxXUfgBvoERBPPXT8+Z7puV5BggHlgqVjw370+ONHnwXigdh9HSl7HWi+IMoFEiA51yj8UQoy3la1dNrc+S+Tk+fXk3ggZhByDWjQyWCaBgEpULjjuSNv/O//5XsL5SCfXE5/e/jRW/6aP0vz2G1Hu93gpF53u1IV55BkABQDyUtvjywfq3fevfP5w6/mMfsVjuV9p30FUYMKtgBzr4EAAAAASUVORK5CYII=',
                    //     symbolSize: [25, 26],
                    //     symbolOffset: [0, -12],
                    //     showEffectOn: 'emphasis', //加载完毕显示特效
                    // },
                ],
            },
        };

        changeOption(mapOption);

        mapChart.setOption(mapOption, true);

        //点击前解绑，防止点击事件触发多次
        mapChart.off('click');
        // mapChart.on('click', echartsMapClick);
    };
    // 地图-返回上一级
    const goBack = () => {
        if (parentInfo.length === 1) {
            return;
        }
        parentInfo.pop();
        if (parentInfo.length === 1) {
            parentInfo[0].dataType = '1';
        }
        getvalue(parentInfo[parentInfo.length - 1]); // 传参

        let code = parentInfo[parentInfo.length - 1].cityCode;
        // 直辖市取parentCode刷新地图
        if (parentInfo[parentInfo.length - 1].dataType === '3') {
            zhixiaArr.forEach((item) => {
                if (cityInfoRef?.current.cityName === item) {
                    code = parentInfo[parentInfo.length - 1].parentCode;
                }
            });
        }

        initGeoJson(code);
    };

    //echarts点击事件
    // const echartsMapClick = (params) => {
    //     if (!params.data) {
    //         return;
    //     } else {
    //         //如果当前是最后一级，直接return
    //         if (parentInfo[parentInfo.length - 1].cityCode == params.data.cityCode) {
    //             return;
    //         }
    //         let data = params.data;
    //         parentInfo.push({
    //             cityName: data.name,
    //             cityCode: data.cityCode,
    //         });
    //         initGeoJson(data.cityCode);
    //     }
    // };

    // 已覆盖城市
    const addMapStation = (stationList) => {
        if (option && option.baseOption) {
            changePointData([]);
            stationList.forEach((item) => {
                pointData.push({
                    value: [item.lon, item.lat, 3000],
                });
            });
            option.baseOption.series[2].data = pointData;
            mapChart.setOption(option, true);
        } else {
            // 初始化地图
            // query链接入参,则先初始化
            if (cityInfo.query) {
                initGeoJson(cityInfo.cityCode || 100000);
            } else {
                loadMap();
            }
        }
    };
    // 更新充电热点城市
    const updateMapCity = () => {
        if (mapCityList && mapCityList.length > 0) {
            if (option && option.baseOption) {
                let mapCity = [];
                if (
                    cityInfoRef?.current.dataType === '3' ||
                    cityInfoRef?.current.dataType === '4'
                ) {
                    mapCityList.forEach((item) => {
                        if (
                            item.cityCode == cityInfoRef?.current.cityCode ||
                            item.cityCode == cityInfoRef?.current.parentCode
                        ) {
                            mapCity.push({
                                value: [item.lon, item.lat, 3000],
                            });
                        }
                    });
                } else {
                    mapCityList.forEach((item) => {
                        mapCity.push({
                            value: [item.lon, item.lat, 3000],
                        });
                    });
                }
                option.baseOption.series[3].data = mapCity;
                mapChart.setOption(option, true);
            }
        }
    };

    const changeChargeTypeEvent = (type) => {
        changeChargeType(type);
        chargeTypeRef.current = type;
        if (type === DATE_TYPE.YEAR) {
            setBarChartOption(chargePqDataList);
        } else {
            dispatch({
                // 充电量情况 同比
                type: 'wuxiXdtDPmessage/xdtqryChargePqRecordYearOnYear',
                payload: {
                    dataType: cityInfoRef?.current.dataType, // 数据类型 1 全国 2省 3城市 4区
                    cityCode: cityInfoRef?.current.cityCode, // 城市编码
                    parentCode: cityInfoRef?.current.parentCode, // 父级编码
                    dateType: type,
                },
            });
        }
    };

    const renderScroll = () =>
        messageList.map((ele, index) => (
            <div
                key={index}
                className={`${styles.cityitem} text-line`}
                style={{
                    transform: `translateY(${-62 + index * 81 * -1}px)`,
                }}
            >
                {moment(ele.chargeTime).format('MM-DD HH:mm')}
                <text className={`${styles.cityitem_text}`}>
                    {ele.cityName
                        .replace(
                            intl.formatMessage({
                                id: 'page.screen.unit.city',
                                defaultMessage: '市',
                            }),
                            '',
                        )
                        .slice(0, 6)}
                </text>
                {intl.formatMessage({
                    id: 'page.screen.middle.useXdt',
                    defaultMessage: '用户通过新电途',
                })}
                {ele.chargeType === '1'
                    ? intl.formatMessage({
                          id: 'page.screen.middle.startCharge',
                          defaultMessage: '启动充电',
                      })
                    : intl.formatMessage({
                          id: 'page.screen.middle.endCharge',
                          defaultMessage: '结束充电',
                      })}
            </div>
        ));

    const getImgByLocal = (fileName) => {
        const localeType = getLocale();
        const lowerType = localeType.toLowerCase();
        let titleImg = null;
        switch (lowerType) {
            case 'en-us':
                titleImg = resolvePath(`us_${fileName}`);
                break;
            case 'zh-cn':
                titleImg = resolvePath(`${fileName}`);

                break;

            default:
                break;
        }
        return titleImg;
    };

    return (
        <div className={styles.xdt_middlepage}>
            {/* 总计 */}
            <div className={styles.xdt_pagecup_box}>
                <div className={styles.xdt_map_back}>
                    {parentInfo.length === 1 ? (
                        <div onClick={goBack} className={styles.xdt_back_null}></div>
                    ) : (
                        <div onClick={goBack} className={styles.xdt_back_btn}>
                            {intl.formatMessage({
                                id: 'page.screen.middle.back',
                                defaultMessage: '返回',
                            })}
                        </div>
                    )}
                    <div className={styles.xdt_city_info}>
                        <img className={styles.icon} src={img17} />
                        <div className={styles.xdt_city_name}>
                            {intl.formatMessage({
                                id: 'page.screen.middle.currentCity',
                                defaultMessage: '当前城市',
                            })}
                            ：{parentInfo[0].cityName}
                            {parentInfo.length > 1 ? ` > ${parentInfo[1].cityName}` : ''}
                            {parentInfo.length > 2 ? ` > ${parentInfo[2].cityName}` : ''}
                        </div>
                    </div>
                </div>
                <div className={styles.xdt_pagecup_box}>
                    <div className={styles.xdt_pagecup}>
                        <div className={styles.page_item}>
                            <img className={styles.icon} src={img13} />
                            <div className={styles.info}>
                                {ChargePqRecordTotal.totalPq >= 100000 ? (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={
                                                isChinaLanguage &&
                                                ChargePqRecordTotal.totalPq >= 100000000
                                                    ? 0
                                                    : 2
                                            }
                                            start={totalPq}
                                            end={
                                                isChinaLanguage
                                                    ? formatScreenLocalNumber(
                                                          ChargePqRecordTotal.totalPq,
                                                      )
                                                    : formatNumberRatio(ChargePqRecordTotal.totalPq)
                                            }
                                            duration={4}
                                        />
                                        {!isChinaLanguage &&
                                            (ChargePqRecordTotal.totalPq >= 1000000 ? 'M' : 'K')}
                                    </div>
                                ) : (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={0}
                                            start={totalPq}
                                            end={ChargePqRecordTotal.totalPq || 0}
                                            duration={4}
                                        />
                                    </div>
                                )}

                                <div className={styles.page_name}>
                                    <span className={styles.page_span}>
                                        {intl.formatMessage({
                                            id: 'page.screen.middle.totalChargeNum',
                                            defaultMessage: '累计充电量',
                                        })}
                                        （
                                        {ChargePqRecordTotal.totalPq >= 100000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandDegrees',
                                                  defaultMessage: '万度',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.degrees',
                                                  defaultMessage: '度',
                                              })}
                                        ）
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className={`${styles.page_item} ${styles.page_item_center}`}>
                            <img className={styles.icon} src={img14} />
                            <div className={styles.info}>
                                {ChargePqRecordTotal.totalOrderNum >= 100000 ? (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={
                                                isChinaLanguage &&
                                                ChargePqRecordTotal.totalOrderNum >= 100000000
                                                    ? 0
                                                    : 2
                                            }
                                            start={totalOrderNum}
                                            end={
                                                isChinaLanguage
                                                    ? formatScreenLocalNumber(
                                                          ChargePqRecordTotal.totalOrderNum,
                                                      )
                                                    : formatNumberRatio(
                                                          ChargePqRecordTotal.totalOrderNum,
                                                      )
                                            }
                                            duration={4}
                                        />
                                        {!isChinaLanguage &&
                                            (ChargePqRecordTotal.totalOrderNum >= 1000000
                                                ? 'M'
                                                : 'K')}
                                    </div>
                                ) : (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={0}
                                            start={totalOrderNum}
                                            end={ChargePqRecordTotal.totalOrderNum || 0}
                                            duration={4}
                                        />
                                    </div>
                                )}

                                <div className={styles.page_name}>
                                    <span className={styles.page_span}>
                                        {intl.formatMessage({
                                            id: 'page.screen.middle.totalOrderNum',
                                            defaultMessage: '累计订单数量',
                                        })}
                                        （
                                        {ChargePqRecordTotal.totalOrderNum >= 100000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandOrder',
                                                  defaultMessage: '万笔',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.order',
                                                  defaultMessage: '笔',
                                              })}
                                        ）
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className={styles.page_item}>
                            <img className={styles.icon} src={img15} />
                            <div className={styles.info}>
                                {ChargePqRecordTotal.totalUserNum >= 100000 ? (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={
                                                isChinaLanguage &&
                                                ChargePqRecordTotal.totalUserNum >= 100000000
                                                    ? 0
                                                    : 2
                                            }
                                            start={totalUserNum}
                                            end={
                                                isChinaLanguage
                                                    ? formatScreenLocalNumber(
                                                          ChargePqRecordTotal.totalUserNum,
                                                      )
                                                    : formatNumberRatio(
                                                          ChargePqRecordTotal.totalUserNum,
                                                      )
                                            }
                                            duration={4}
                                        />
                                        {!isChinaLanguage &&
                                            (ChargePqRecordTotal.totalUserNum >= 1000000
                                                ? 'M'
                                                : 'K')}
                                    </div>
                                ) : (
                                    <div className={styles.item_value}>
                                        <CountUp
                                            decimals={0}
                                            start={totalUserNum}
                                            end={ChargePqRecordTotal.totalUserNum || 0}
                                            duration={4}
                                        />
                                    </div>
                                )}

                                <div className={styles.page_name}>
                                    <span className={styles.page_span}>
                                        {intl.formatMessage({
                                            id: 'page.screen.middle.registerNum',
                                            defaultMessage: '注册用户数',
                                        })}
                                        （
                                        {ChargePqRecordTotal.totalUserNum >= 100000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandInd',
                                                  defaultMessage: '万个',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.one',
                                                  defaultMessage: '个',
                                              })}
                                        ）
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* 地图 */}
            <div className={styles.xdt_map_box}>
                <div id="map-view" className={styles.xdt_map} />
            </div>
            {/* 地图点位提示 */}
            <div className={styles.xdt_map_tooltips}>
                <img className={styles.icon} src={getImgByLocal('img20.jpg')} />
                <img className={styles.icon} src={getImgByLocal('img21.jpg')} />
            </div>
            {/* 充电滚动 */}
            <div id="father" className={classnames('main', styles.xdt_run)}>
                <div className={`${styles.container} container`}>
                    <div
                        className={`${styles.scroll} scroll`}
                        // style={{
                        //     top: `${
                        //         messagelist && messagelist.length >= 3
                        //             ? (messagelist.length - 3) * 39 * -1
                        //             : 42
                        //     }px`,
                        // }}
                    >
                        {renderScroll()}
                    </div>
                    {messagelist && messagelist.length > 2 ? (
                        <div className={styles.mark}></div>
                    ) : null}
                </div>
            </div>

            <div className={styles.Middle_bottom}>
                {/* 用户情况 */}
                <div className={styles.xdt_echart}>
                    <div className={styles.left_title}>
                        {intl.formatMessage({
                            id: 'page.screen.middle.userview',
                            defaultMessage: '用户情况',
                        })}
                    </div>
                    <div className={styles.left_content_box}>
                        <div className={styles.left_top_box}>
                            <div className={styles.left_unit}>
                                <div>
                                    {intl.formatMessage({
                                        id: 'page.screen.middle.userGrowthTrend',
                                        defaultMessage: '用户增长趋势',
                                    })}
                                </div>
                            </div>
                        </div>
                        {/* 图表 */}
                        <div className={styles.left_echa_box}>
                            <div id="middle-user-barchart" className={styles.left_echa} />
                        </div>
                    </div>
                </div>
                {/* 充电量情况 */}
                <div className={styles.xdt_echart}>
                    <div className={styles.left_title}>
                        {intl.formatMessage({
                            id: 'page.screen.middle.chargeview',
                            defaultMessage: '充电量情况',
                        })}
                    </div>
                    <div className={styles.left_content_box}>
                        {/* tab */}
                        <div className={styles.left_top_box}>
                            <div className={styles.left_tab}>
                                <div
                                    onClick={() => {
                                        changeChargeTypeEvent(DATE_TYPE.DAY);
                                    }}
                                    className={
                                        chargeType === DATE_TYPE.DAY
                                            ? styles.left_tab_active
                                            : styles.left_tab_item
                                    }
                                >
                                    {intl.formatMessage({
                                        id: 'page.screen.middle.forDay',
                                        defaultMessage: '按日',
                                    })}
                                </div>
                                <div
                                    onClick={() => {
                                        changeChargeTypeEvent(DATE_TYPE.MONTH);
                                    }}
                                    className={
                                        chargeType === DATE_TYPE.MONTH
                                            ? styles.left_tab_active
                                            : styles.left_tab_item
                                    }
                                >
                                    {intl.formatMessage({
                                        id: 'page.screen.middle.forMonth',
                                        defaultMessage: '按月',
                                    })}
                                </div>
                                <div
                                    onClick={() => {
                                        changeChargeTypeEvent(DATE_TYPE.YEAR);
                                    }}
                                    className={
                                        chargeType === DATE_TYPE.YEAR
                                            ? styles.left_tab_active
                                            : styles.left_tab_item
                                    }
                                >
                                    {intl.formatMessage({
                                        id: 'page.screen.middle.forYear',
                                        defaultMessage: '按年',
                                    })}
                                </div>
                            </div>
                        </div>
                        {/* 图表 */}
                        <div className={styles.left_echa_box}>
                            <div id="middle-barchart" className={styles.left_echa} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default connect(({ wuxiXdtDPmessage, loading }) => ({
    loading,
    wuxiXdtDPmessage,
}))(MiddlePage);
