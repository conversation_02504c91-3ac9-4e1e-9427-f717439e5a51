import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react';
import { connect, useIntl, setLocale } from 'umi';
import { Button } from 'antd';
import mqtt from 'mqtt';
import qs from 'qs';

import styles from './Screen.less';

import Left from './Left';
import Middle from './Middle';
import Right from './Right';

// import scriptjs from "scriptjs"

let client = null;

const Screen = (props) => {
    const {
        history,
        dispatch,
        newXdtDPmessage: { winSize, mapCityList, messageList },
    } = props;

    const {
        location: { query },
    } = history;

    const intl = useIntl();

    const messageInfo = useRef();
    const mapCityInfo = useRef();
    const cityInfo = useRef({});
    const nextMqUpdateTime = useRef({
        user: +new Date(),
        city: +new Date(),
        order: +new Date(),
    });
    useEffect(() => {
        messageInfo.current = messageList;
        mapCityInfo.current = mapCityList;
    });

    useEffect(() => {
        const { search } = window.location;
        const { locale } = qs.parse(search, { ignoreQueryPrefix: true });
        if (!locale) {
            const historyLocal = window.localStorage.getItem('local');
            if (historyLocal) {
                setLocale(historyLocal);
            }
        }

        if (query && query.cityCode) {
            let params = {
                dataType: query.dataType || '3', // 数据类型 1 全国 2省 3城市 4区 (显示前30城市 选1，显示区 选3+cityCode)
                cityCode: query.cityCode, // 城市编码
                cityName: query.cityName, // 城市名称
                parentCode: query.parentCode, // 父级编码
                dateType: '', // 1 按日 2按月
                query: 'query', // 判读信息来源于url
            };
            getCityInfo(params);
        } else {
            dispatch({
                // 大屏资产信息
                type: 'newXdtDPmessage/xdtqryJoinGeneralView',
                payload: {
                    dataType: '1',
                },
            });

            dispatch({
                // 充电量总计
                type: 'newXdtDPmessage/xdtqryChargePqRecordTotal',
                payload: {
                    dataType: '1',
                },
            });

            dispatch({
                // 城市
                type: 'newXdtDPmessage/cityEquipNum',
                payload: {
                    dataType: '1', // 数据类型 1 全国 2省 3城市 4区 (显示前30城市 选1，显示区 选3+cityCode)
                    cityCode: '', // 城市编码
                    parentCode: '', // 父级编码
                    dateType: '', // 1 按日 2按月
                },
            });
        }

        initMqtt();

        document.title = '新电途大屏';

        return () => {
            detoryMqtt();
        };
    }, []);

    const initMqtt = () => {
        if (!client) {
            let url = `wss://test.evshine.net/websocket/`;
            if (process.env.NODE_ENV === 'production') {
                url = `wss://${window.location.hostname}/websocket/`;
            }

            client = mqtt.connect(url, {
                username: 'admin',
                password: 'password',
            });

            client.on('connect', (info) => {
                client.subscribe('xdtOrderMsg');
                client.subscribe('xdtCityMsg');
                client.subscribe('xdtUserMsg');
                client.subscribe('xdtUserMsgNew');
                client.subscribe('xdtScreenOrderMsgNew');
                client.subscribe('xdtScreenOrderMsgCity');
            });

            client.on('message', onMessageMqttArrived);
        }

        // })
    };
    const onMessageMqttArrived = (topic, message) => {
        const s = '{onMessageArrived()}';
        // console.log(44444, s, topic, message.toString(), messageList);
        const mesStr = message.toString();
        let info = '';
        const noTime = +new Date();
        try {
            info = JSON.parse(mesStr);
        } catch (error) {
            info = mesStr;
        }

        // 判断市、区县是否显示
        if (cityInfo?.current?.dataType === '3') {
            if (cityInfo?.current?.cityCode === info.cityCode) {
            } else {
                return;
            }
        } else if (cityInfo?.current?.dataType === '4') {
            if (cityInfo?.current?.cityCode === (info.countyCode || info.cityCode)) {
            } else {
                return;
            }
        } else {
        }

        switch (topic) {
            case 'xdtUserMsgNew':
                if (nextMqUpdateTime.current.user > noTime) {
                    return;
                }
                let list = [];
                list = [info, ...messageInfo.current];
                if (list.length > 5) {
                    list.splice(5, 1);
                }
                dispatch({
                    // 用户
                    type: 'newXdtDPmessage/setMessageList',
                    payload: {
                        messageList: list,
                    },
                });
                nextMqUpdateTime.current.user = noTime + 5000;
                break;
            case 'xdtCityMsg':
                if (nextMqUpdateTime.current.city > noTime) {
                    return;
                }
                const citydata = info;
                let has = false;
                const citylist = mapCityInfo.current;

                for (let index = 0; index < citylist.length; index++) {
                    const item = citylist[index];
                    if (item.cityName == citydata.cityName) {
                        has = true;
                        citylist[index] = citydata;
                        break;
                    }
                }
                if (!has) {
                    if (citylist.length > 30) {
                        citylist.shift();
                    }
                    citylist.push(citydata);
                }

                dispatch({
                    type: 'newXdtDPmessage/updateMapCityList',
                    payload: citylist,
                });
                nextMqUpdateTime.current.city = noTime + 5000;
                break;
            case 'xdtScreenOrderMsgNew':
                if (nextMqUpdateTime.current.order > noTime) {
                    return;
                }

                const data = info;
                dispatch({
                    // 用户
                    type: 'newXdtDPmessage/setqryChargePqRecordTotal',
                    payload: {
                        ChargePqRecordTotal: data,
                    },
                });
                nextMqUpdateTime.current.order = noTime + 1000;

                break;
            case 'xdtScreenOrderMsgCity':
                if (cityInfo?.current?.dataType === '3' || cityInfo?.current?.dataType === '4') {
                    if (nextMqUpdateTime.current.order > noTime) {
                        return;
                    }

                    const dataCity = info;
                    dispatch({
                        // 用户
                        type: 'newXdtDPmessage/setqryChargePqRecordTotal',
                        payload: {
                            ChargePqRecordTotal: dataCity,
                        },
                    });
                    nextMqUpdateTime.current.order = noTime + 1000;
                }

                break;
            default:
                break;
        }
        // console.log(`收到消息:${topic}/${info}`, info);
    };
    const onMessageArrived = (message) => {
        const s = '{onMessageArrived()}';
        // console.log(s, message);
        switch (message.destinationName) {
            case 'xdtUserMsg':
                dispatch({
                    // 用户
                    type: 'newXdtDPmessage/setMessageList',
                    payload: {
                        messageList: JSON.parse(message.payloadString),
                    },
                });
                break;
            case 'xdtCityMsg':
                const citydata = JSON.parse(message.payloadString);
                let has = false;
                for (let index = 0; index < mapCityList.length; index++) {
                    const item = mapCityList[index];
                    if (item.cityName == citydata.cityName) {
                        has = true;
                        mapCityList[index] = citydata;
                        break;
                    }
                }
                if (!has) {
                    if (mapCityList.length > 30) {
                        mapCityList.shift();
                    }
                    mapCityList.push(citydata);
                }

                dispatch({
                    // 城市词云
                    type: 'newXdtDPmessage/updateMapCityList',
                    payload: mapCityList,
                });
                break;
            case 'xdtOrderMsg':
                const data = JSON.parse(message.payloadString);
                dispatch({
                    // 用户
                    type: 'newXdtDPmessage/setqryChargePqRecordTotal',
                    payload: {
                        ChargePqRecordTotal: data,
                    },
                });
                break;
            default:
                break;
        }
        // console.log(`收到消息:${message.payloadString}`);
    };
    const onConnectionLost = (responseObject) => {
        // console.log(responseObject);
        const s = '{ onConnectionLost()}';
        // console.log(s);
        if (responseObject.errorCode !== 0) {
            // console.log(`onConnectionLost:${responseObject.errorMessage}`);
            // console.log('连接已断开');
        }
    };
    const detoryMqtt = () => {
        if (client) {
            client.end();
            // client.disconnect();
            client = null;
        }
    };

    const scale = useMemo(
        () => `scale(${winSize.width / 6330}, ${winSize.height / 1550})`,
        [winSize.width, winSize.height],
    );
    // 获取活跃城市信息
    const getCityInfo = (val) => {
        cityInfo.current = val;
        dispatch({
            // 用户
            type: 'newXdtDPmessage/setMessageList',
            payload: {
                messageList: [],
            },
        });
        dispatch({
            // 大屏资产信息
            type: 'newXdtDPmessage/xdtqryJoinGeneralView',
            payload: { ...val },
        });
        dispatch({
            // 充电量总计
            type: 'newXdtDPmessage/xdtqryChargePqRecordTotal',
            payload: { ...val },
        });
        // dispatch({
        //     // 城市
        //     type: 'newXdtDPmessage/xdtqryCity',
        //     payload: { ...val },
        // });
    };

    return (
        <Fragment>
            <div className={styles.Near} style={{ transform: scale }}>
                <Left cityInfo={cityInfo.current} />
                <Middle cityInfo={cityInfo.current} getvalue={getCityInfo} />
                <Right cityInfo={cityInfo.current} getvalue={getCityInfo} />
            </div>
        </Fragment>
    );
};

export default connect(({ newXdtDPmessage, loading }) => ({
    loading,
    newXdtDPmessage,
}))(Screen);
