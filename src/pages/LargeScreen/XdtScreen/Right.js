import React, { Fragment, useState, useEffect, useRef, useMemo } from 'react';
import { connect, useIntl, getLocale } from 'umi';
import { Space } from 'antd';
import CountUp from 'react-countup';

import Echarts from 'echarts';
import moment from 'moment';
import styles from './Screen.less';

import {
    formatWan,
    toggleLocalEvent,
    getOtherLocalName,
    formatScreenLocalNumber,
    formatNumberRatio,
} from '@/utils/utils';

const LanguageToggleBtn = () => {
    return (
        <div className={styles.language_btn} onClick={toggleLocalEvent}>
            {getOtherLocalName()}
        </div>
    );
};

const DATE_TYPE = {
    DAY: '1',
    MONTH: '2',
};

const RightPage = (props) => {
    const {
        dispatch,
        cityInfo,
        getvalue,
        xdtDPmessage: { ChargeCity, ChargePqRecordTotal, cityEquipNumList },
    } = props;
    const [dateType, changeDateType] = useState(DATE_TYPE.DAY);
    const [changingEquipNum, changeChangingEquipNum] = useState(0);
    const [thisMonthPq, changeThisMonthPq] = useState(0);
    const [thisYearPq, changeThisYearPq] = useState(0);
    const [todayPq, changeTodayPq] = useState(0);

    const loopInterval = useRef();
    const dateTypeRef = useRef();
    const cityInfoRef = useRef({});

    const intl = useIntl();

    const isChinaLanguage = useMemo(() => {
        if (getLocale() === 'zh-CN') {
            return true;
        }
        return false;
    });

    const changeInfo = (city) => {
        if (!(city && city.cityCode)) return;
        if (
            cityInfoRef?.current?.dataType !== '4' ||
            (cityInfoRef?.current?.query && cityInfoRef?.current?.dataType === '3')
        ) {
            dispatch({
                // 城市
                type: 'xdtDPmessage/cityEquipNum',
                payload: {
                    dataType: city.dataType || '1', // 数据类型 1 全国 2省 3城市 4区
                    cityCode: city.cityCode, // 城市编码
                    parentCode: city.parentCode, // 父级编码
                    dateType: '', // 1 按日 2按月
                },
            });
        }
    };
    useEffect(() => {
        loopInterval.current = setInterval(() => {
            if (
                !(
                    cityInfoRef?.current?.dataType === '4' ||
                    (cityInfoRef?.current?.query && cityInfoRef?.current?.dataType === '3')
                )
            ) {
                dispatch({
                    // 城市
                    type: 'xdtDPmessage/cityEquipNum',
                    payload: {
                        dataType: cityInfoRef?.current?.dataType ?? '1', // 数据类型 1 全国 2省 3城市 4区
                        cityCode: cityInfoRef?.current.cityCode, // 城市编码
                        parentCode: cityInfoRef?.current.parentCode, // 父级编码
                        dateType: '', // 1 按日 2按月
                    },
                });
            }
        }, 30000);

        initChart();

        return () => {
            clearInterval(loopInterval.current);
        };
    }, []);

    useEffect(() => {
        cityInfoRef.current = cityInfo;
        changeInfo(cityInfo); // 地图-返回上一级
    }, [cityInfo]);

    useEffect(() => {
        dateTypeRef.current = dateType;
    }, [dateType]);

    useEffect(() => {
        changeChangingEquipNum(ChargePqRecordTotal.changingEquipNum || 0);
        changeThisMonthPq(
            ChargePqRecordTotal.thisMonthPq >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.thisMonthPq)
                    : formatNumberRatio(ChargePqRecordTotal.thisMonthPq)
                : ChargePqRecordTotal.thisMonthPq || 0,
        );
        changeThisYearPq(
            ChargePqRecordTotal.thisYearPq >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.thisYearPq)
                    : formatNumberRatio(ChargePqRecordTotal.thisYearPq)
                : ChargePqRecordTotal.thisYearPq || 0,
        );
        changeTodayPq(
            ChargePqRecordTotal.todayPq >= 100000
                ? isChinaLanguage
                    ? formatScreenLocalNumber(ChargePqRecordTotal.todayPq)
                    : formatNumberRatio(ChargePqRecordTotal.todayPq)
                : ChargePqRecordTotal.todayPq || 0,
        );
    }, [ChargePqRecordTotal]);

    useEffect(() => {
        if (ChargeCity && ChargeCity.length > 0) {
            // drawLine(ChargeCity);
        }
    }, [ChargeCity]);

    const initChart = () => {
        // 基于准备好的dom，初始化echarts实例
    };

    //echarts点击事件
    const echartsOpennsClick = (params) => {
        if (!params) {
            return;
        } else {
            let city = {
                cityName: params.cityName,
                cityCode: params.cityCode,
                dataType: params.dataType,
                parentCode: params.parentCode,
                dateType: dateTypeRef.current,
            };
            getvalue(city); // 传参
            cityInfoRef.current = city;
            if (params.dataType === '3') {
                changeInfo(city); // 下钻活跃城市
            }
        }
    };

    return (
        <div className={styles.xdt_rightPage}>
            <div className={styles.left_header_title}>
                <div className={styles.text}>
                    <Space>
                        {moment().format('YYYY/MM/DD HH:mm')}
                        <LanguageToggleBtn></LanguageToggleBtn>
                    </Space>
                </div>
            </div>
            {/* 运营情况 */}
            <div className={styles.left_overview}>
                <div className={styles.left_title}>
                    {intl.formatMessage({
                        id: 'page.screen.right.operateview',
                        defaultMessage: '运营情况',
                    })}
                </div>
                <div className={styles.left_content_box}>
                    <div className={`${styles.left_content} ${styles.left_content_color1}`}>
                        <div className={styles.left_text}>
                            {ChargePqRecordTotal.todayPq >= 100000 ? (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={
                                            isChinaLanguage &&
                                            ChargePqRecordTotal.todayPq >= 100000000
                                                ? 0
                                                : 2
                                        }
                                        start={todayPq}
                                        end={
                                            isChinaLanguage
                                                ? formatScreenLocalNumber(
                                                      ChargePqRecordTotal.todayPq,
                                                  )
                                                : formatNumberRatio(ChargePqRecordTotal.todayPq)
                                        }
                                        duration={4}
                                    />
                                    {!isChinaLanguage &&
                                        (ChargePqRecordTotal.todayPq >= 1000000 ? 'M' : 'K')}
                                </div>
                            ) : (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={0}
                                        start={todayPq}
                                        end={ChargePqRecordTotal.todayPq || 0}
                                        duration={4}
                                    />
                                </div>
                            )}
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.right.dayEle',
                                    defaultMessage: '今日电量',
                                })}

                                <text>
                                    {!isChinaLanguage && <br></br>}（
                                    {ChargePqRecordTotal.todayPq >= 100000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandDegrees',
                                              defaultMessage: '万度',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.degrees',
                                              defaultMessage: '度',
                                          })}
                                    ）
                                </text>
                            </div>
                        </div>
                    </div>
                    <div className={`${styles.left_content} ${styles.left_content_color1}`}>
                        <div className={styles.left_text}>
                            {ChargePqRecordTotal.thisMonthPq >= 100000 ? (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={
                                            isChinaLanguage &&
                                            ChargePqRecordTotal.thisMonthPq >= 100000000
                                                ? 0
                                                : 2
                                        }
                                        start={thisMonthPq}
                                        end={
                                            isChinaLanguage
                                                ? formatScreenLocalNumber(
                                                      ChargePqRecordTotal.thisMonthPq,
                                                  )
                                                : formatNumberRatio(ChargePqRecordTotal.thisMonthPq)
                                        }
                                        duration={4}
                                    />
                                    {!isChinaLanguage &&
                                        (ChargePqRecordTotal.thisMonthPq >= 1000000 ? 'M' : 'K')}
                                </div>
                            ) : (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={0}
                                        start={thisMonthPq}
                                        end={ChargePqRecordTotal.thisMonthPq || 0}
                                        duration={4}
                                    />
                                </div>
                            )}
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.right.monthEle',
                                    defaultMessage: '本月电量',
                                })}
                                <text>
                                    {!isChinaLanguage && <br></br>}（
                                    {ChargePqRecordTotal.thisMonthPq >= 100000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandDegrees',
                                              defaultMessage: '万度',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.degrees',
                                              defaultMessage: '度',
                                          })}
                                    ）
                                </text>
                            </div>
                        </div>
                    </div>
                    <div className={`${styles.left_content} ${styles.left_content_color1}`}>
                        <div className={styles.left_text}>
                            {ChargePqRecordTotal.thisYearPq >= 100000 ? (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={
                                            isChinaLanguage &&
                                            ChargePqRecordTotal.thisYearPq >= 100000000
                                                ? 0
                                                : 2
                                        }
                                        start={thisYearPq}
                                        end={
                                            isChinaLanguage
                                                ? formatScreenLocalNumber(
                                                      ChargePqRecordTotal.thisYearPq,
                                                  )
                                                : formatNumberRatio(ChargePqRecordTotal.thisYearPq)
                                        }
                                        duration={4}
                                    />
                                    {!isChinaLanguage &&
                                        (ChargePqRecordTotal.thisYearPq >= 1000000 ? 'M' : 'K')}
                                </div>
                            ) : (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={0}
                                        start={thisYearPq}
                                        end={ChargePqRecordTotal.thisYearPq || 0}
                                        duration={4}
                                    />
                                </div>
                            )}
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.right.yearEle',
                                    defaultMessage: '本年电量',
                                })}
                                <text>
                                    {!isChinaLanguage && <br></br>}（
                                    {ChargePqRecordTotal.thisYearPq >= 100000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandDegrees',
                                              defaultMessage: '万度',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.degrees',
                                              defaultMessage: '度',
                                          })}
                                    ）
                                </text>
                            </div>
                        </div>
                    </div>
                    <div className={`${styles.left_content} ${styles.left_content_color1}`}>
                        <div className={styles.left_text}>
                            {ChargePqRecordTotal.changingEquipNum >= 100000 ? (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={
                                            isChinaLanguage &&
                                            ChargePqRecordTotal.changingEquipNum >= 100000000
                                                ? 0
                                                : 2
                                        }
                                        start={formatScreenLocalNumber(changingEquipNum)}
                                        end={formatScreenLocalNumber(
                                            ChargePqRecordTotal.changingEquipNum,
                                        )}
                                        duration={3}
                                    />
                                    {!isChinaLanguage &&
                                        (ChargePqRecordTotal.changingEquipNum >= 1000000
                                            ? 'M'
                                            : 'K')}
                                </div>
                            ) : (
                                <div className={styles.values}>
                                    <CountUp
                                        decimals={0}
                                        start={changingEquipNum}
                                        end={ChargePqRecordTotal.changingEquipNum || 0}
                                        duration={3}
                                    />
                                </div>
                            )}
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.right.charging',
                                    defaultMessage: '充电中',
                                })}
                                {
                                    <text>
                                        {!isChinaLanguage && <br></br>} （
                                        {ChargePqRecordTotal.changingEquipNum >= 100000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandInd',
                                                  defaultMessage: '万个',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.one',
                                                  defaultMessage: '个',
                                              })}
                                        ）
                                    </text>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* 充电桩城市排名 */}
            <div className={styles.xdt_city_echart}>
                <div className={styles.left_title}>
                    {intl.formatMessage({
                        id: 'page.screen.right.pileNumRank.title',
                        defaultMessage: '充电桩城市排名',
                    })}
                </div>
                <div className={styles.left_content_box}>
                    {/* 图表 */}
                    <div className={styles.city_rank}>
                        {cityEquipNumList?.map((item, index) => {
                            return (
                                <div
                                    onClick={() => {
                                        echartsOpennsClick(item);
                                    }}
                                    key={index}
                                    className={`${styles.city_rank_item} ${
                                        cityEquipNumList?.length <= 10 ? styles.city_rank_item2 : ''
                                    }`}
                                >
                                    <div className={styles.city_item_logo}>
                                        <div
                                            className={`${styles.city_logo_img} ${
                                                styles[`city_logo_img${index + 1}`]
                                            }`}
                                        >
                                            {index + 1}
                                        </div>
                                    </div>
                                    <div className={styles.city_item_content}>
                                        <div className={styles.rank_content_top}>
                                            <div className={styles.rank_content_title}>
                                                {item.cityName}
                                            </div>
                                            <div className={styles.rank_content_value}>
                                                {item.pileNum >= 100000 ? (
                                                    <span className={styles.values}>
                                                        {isChinaLanguage
                                                            ? formatScreenLocalNumber(item.pileNum)
                                                            : formatNumberRatio(item.pileNum)}
                                                        {!isChinaLanguage &&
                                                            (item.pileNum >= 1000000 ? 'M' : 'K')}
                                                    </span>
                                                ) : (
                                                    <span className={styles.values}>
                                                        {item.pileNum || 0}
                                                    </span>
                                                )}
                                                {item.pileNum >= 100000
                                                    ? intl.formatMessage({
                                                          id: 'page.screen.unit.tenThousandInd',
                                                          defaultMessage: '万个',
                                                      })
                                                    : intl.formatMessage({
                                                          id: 'page.screen.unit.one',
                                                          defaultMessage: '个',
                                                      })}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default connect(({ xdtDPmessage, loading }) => ({
    loading,
    xdtDPmessage,
}))(RightPage);
