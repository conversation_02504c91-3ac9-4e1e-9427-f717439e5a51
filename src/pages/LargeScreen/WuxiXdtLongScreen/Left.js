import React, { Fragment, useState, useEffect, useRef, useMemo } from 'react';
import { connect, useIntl, setLocale, getLocale } from 'umi';
import { Progress } from 'antd';

import {
    formatScreenLocalNumber,
    formatNumberRatio,
    formatScreenLocalNumberUnit,
} from '@/utils/utils';

import Echarts from 'echarts';
import classNames from 'classnames';
import styles from './Screen.less';

import img1 from './images/img1.png';
import img2 from './images/img2.png';
import img3 from './images/img3.png';
import img4 from './images/img4.png';
import img25 from './images/img25.png';
import us_img25 from './images/us_img25.png';
import company from './images/company.png';
import operRankImg from './images/oper_rank_img.png';

import operGunImg from './images/oper_rank_gun.png';

const LeftPage = (props) => {
    const {
        history,
        cityInfo,
        wuxiXdtLongDPmessage: { JoinGeneralView, ChargePqRecordTotal },
    } = props;

    const {
        location: { pathname },
    } = history;

    const isLevelTwo = useMemo(() => {
        if (pathname.indexOf('/largescreen/wuxixdtlongtwo') >= 0) {
            return true;
        }
        return false;
    }, [pathname]);

    const intl = useIntl();

    const [cdz, changeCdz] = useState(
        intl.formatMessage({
            id: 'page.screen.left.pileNumRank.tipRank',
            defaultMessage: '运营商排名',
        }),
    );
    const cityInfoRef = useRef({});

    const formatCityNum = useMemo(() => {
        if (isLevelTwo && cityInfoRef?.current?.dataType == '1') {
            return 436;
        } else {
            return JoinGeneralView.cityNum;
        }
    }, [JoinGeneralView]);

    const formatOperNum = useMemo(() => {
        if (isLevelTwo && cityInfoRef?.current?.dataType == '1') {
            return 1653;
        } else {
            return JoinGeneralView.operNum;
        }
    }, [JoinGeneralView]);

    const isChinaLanguage = useMemo(() => {
        if (getLocale() === 'zh-CN') {
            return true;
        }
        return false;
    });

    useEffect(() => {
        cityInfoRef.current = cityInfo;
    }, [cityInfo]);

    const getTitleImg = () => {
        const localeType = getLocale();
        const lowerType = localeType.toLowerCase();
        let titleImg = null;
        switch (lowerType) {
            case 'en-us':
                titleImg = us_img25;
                break;
            case 'zh-cn':
                titleImg = img25;

                break;

            default:
                break;
        }
        return titleImg;
    };

    return (
        <div className={styles.xdt_leftPage}>
            <div className={styles.left_header_title}>
                <img className={styles.img} src={getTitleImg()} />
            </div>
            {/* 充电桩接入概览 */}
            <div className={styles.left_overview}>
                <div className={styles.left_title}>
                    {intl.formatMessage({
                        id: 'page.screen.left.overview.title',
                        defaultMessage: '充电桩接入概览',
                    })}
                </div>
                <div className={styles.left_content_box}>
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img1} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {formatCityNum > 0 ? formatCityNum : '--'}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.cover',
                                    defaultMessage: '覆盖',
                                })}
                                &nbsp;
                                {cityInfoRef?.current?.dataType === '3'
                                    ? intl.formatMessage({
                                          id: 'page.screen.left.overview.county',
                                          defaultMessage: '区县',
                                      })
                                    : intl.formatMessage({
                                          id: 'page.screen.left.overview.cities',
                                          defaultMessage: '城市',
                                      })}
                                {isChinaLanguage &&
                                    `（
                                ${intl.formatMessage({
                                    id: 'page.screen.unit.one',
                                    defaultMessage: '个',
                                })}
                                ）`}
                            </div>
                        </div>
                    </div>
                    {/* <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img2} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {ChargePqRecordTotal.totalOrderNum >= 100000 ? (
                                    <div className={styles.item_value}>
                                        {isChinaLanguage
                                            ? formatScreenLocalNumber(
                                                  ChargePqRecordTotal.totalOrderNum,
                                                  0,
                                              )
                                            : formatNumberRatio(
                                                  ChargePqRecordTotal.totalOrderNum,
                                                  0,
                                              )}
                                        {!isChinaLanguage &&
                                            (ChargePqRecordTotal.totalOrderNum >= 1000000
                                                ? 'M'
                                                : 'K')}
                                    </div>
                                ) : (
                                    <div className={styles.item_value}>
                                        {ChargePqRecordTotal.totalOrderNum || 0}
                                    </div>
                                )}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.totalOrderNum',
                                    defaultMessage: '充电订单',
                                })}
                                {isChinaLanguage &&
                                    `（
                                ${
                                    ChargePqRecordTotal.totalOrderNum >= 100000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandOrder',
                                              defaultMessage: '万笔',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.order',
                                              defaultMessage: '笔',
                                          })
                                }
                                ）`}
                            </div>
                        </div>
                    </div> */}
                    {/* <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img2} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {JoinGeneralView.stationNum >= 10000
                                    ? formatScreenLocalNumber(JoinGeneralView.stationNum)
                                    : JoinGeneralView.stationNum}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.chargeStation',
                                    defaultMessage: '充电站',
                                })}
                                {isChinaLanguage &&
                                    `（
                                ${
                                    JoinGeneralView.stationNum >= 10000
                                        ? intl.formatMessage({
                                              id: 'page.screen.unit.tenThousandSeat',
                                              defaultMessage: '万座',
                                          })
                                        : intl.formatMessage({
                                              id: 'page.screen.unit.oneSeat',
                                              defaultMessage: '座',
                                          })
                                }
                                ）`}
                            </div>
                        </div>
                    </div> */}
                    <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img3} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {formatOperNum > 0 ? formatOperNum : '--'}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.oper',
                                    defaultMessage: '运营商（个）',
                                })}
                            </div>
                        </div>
                    </div>
                    {/* <div className={styles.left_content}>
                        <img className={styles.left_icon} src={img4} />
                        <div className={styles.left_text}>
                            <div className={styles.values}>
                                {JoinGeneralView.pileNum >= 10000
                                    ? formatScreenLocalNumber(JoinGeneralView.pileNum)
                                    : JoinGeneralView.pileNum}
                            </div>
                            <div className={styles.type}>
                                {intl.formatMessage({
                                    id: 'page.screen.left.overview.facilities',
                                    defaultMessage: '充电设施',
                                })}
                                {isChinaLanguage &&
                                    `（
                                    ${
                                        JoinGeneralView.pileNum >= 10000
                                            ? intl.formatMessage({
                                                  id: 'page.screen.unit.tenThousandInd',
                                                  defaultMessage: '万个',
                                              })
                                            : intl.formatMessage({
                                                  id: 'page.screen.unit.one',
                                                  defaultMessage: '个',
                                              })
                                    }
                                    ）`}
                            </div>
                        </div>
                    </div> */}
                </div>
            </div>

            <div className={styles.left_numers}>
                <div className={styles.left_title}>{cdz}</div>
                <div className={styles.left_content_box}>
                    {/* 图表 */}
                    <div className={styles.cdz_rank}>
                        {JoinGeneralView?.operPileNumList?.slice(0, 5)?.map((item, index) => {
                            let denominator = JoinGeneralView?.operPileNumList?.[0]?.pileNum || 0;
                            let percent = (item.pileNum / denominator) * 100;

                            const { num: stationNum, unit: stationUnit } =
                                formatScreenLocalNumberUnit(item.stationNum);

                            const { num: gunNum, unit: gunUnit } = formatScreenLocalNumberUnit(
                                item.gunNum,
                            );

                            return (
                                <div key={index} className={styles.cdz_rank_item}>
                                    <div className={styles.rank_title_view}>
                                        <div className={styles.rank_title_info}>
                                            <div className={styles.cdz_item_index}>{index + 1}</div>
                                            <div className={styles.cdz_item_logo}>
                                                <img
                                                    className={styles.cdz_logo_img}
                                                    src={
                                                        item.buildIconUrl
                                                            ? item.buildIconUrl
                                                            : company
                                                    }
                                                />
                                            </div>
                                            <div className={styles.rank_oper_name}>
                                                {item.operName}
                                            </div>
                                        </div>
                                        <div className={styles.rank_title_progress}>
                                            <Progress
                                                percent={percent}
                                                showInfo={false}
                                                strokeWidt={25}
                                                trailColor={'#000'}
                                                strokeColor={{ '0%': '#1FFCF9', '100%': '#1FFCF9' }}
                                            />
                                        </div>
                                    </div>
                                    <div className={styles.rank_info_view}>
                                        <div className={styles.rank_info_cell}>
                                            <div className={styles.logo}>
                                                <img src={operRankImg} alt="" />
                                            </div>
                                            <div className={styles.rank_info_content}>
                                                <div className={styles.rank_info_content_value}>
                                                    {stationNum}

                                                    {stationUnit}
                                                </div>
                                                <div className={styles.rank_info_content_label}>
                                                    {intl.formatMessage({
                                                        id: 'page.screen.left.station',
                                                        defaultMessage: '充电站',
                                                    })}
                                                    {isChinaLanguage &&
                                                        `(
                                                    ${
                                                        item.stationNum >= 10000
                                                            ? intl.formatMessage({
                                                                  id: 'page.screen.unit.tenThousandSeat',
                                                                  defaultMessage: '万座',
                                                              })
                                                            : intl.formatMessage({
                                                                  id: 'page.screen.unit.oneSeat',
                                                                  defaultMessage: '座',
                                                              })
                                                    }
                                                    )`}
                                                </div>
                                            </div>
                                        </div>
                                        <div className={styles.rank_info_cell}>
                                            <div className={styles.logo}>
                                                <img src={operGunImg} alt="" />
                                            </div>
                                            <div className={styles.rank_info_content}>
                                                <div className={styles.rank_info_content_value}>
                                                    {gunNum}
                                                    {gunUnit}
                                                </div>
                                                <div className={styles.rank_info_content_label}>
                                                    {intl.formatMessage({
                                                        id: 'page.screen.left.overview.facilities',
                                                        defaultMessage: '充电设施',
                                                    })}
                                                    {isChinaLanguage &&
                                                        `(
                                                    ${
                                                        item.gunNum >= 10000
                                                            ? intl.formatMessage({
                                                                  id: 'page.screen.unit.tenThousandInd',
                                                                  defaultMessage: '万个',
                                                              })
                                                            : intl.formatMessage({
                                                                  id: 'page.screen.unit.one',
                                                                  defaultMessage: '个',
                                                              })
                                                    }
                                                    )`}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/* <div className={styles.cdz_item_content}>
                                        <div className={styles.rank_content_top}>
                                            <div className={styles.rank_content_title}>
                                                {item.operName}
                                            </div>
                                            <div className={styles.rank_content_value}>
                                                {item.pileNum >= 100000 ? (
                                                    <span className={styles.values}>
                                                        {isChinaLanguage
                                                            ? formatScreenLocalNumber(item.pileNum)
                                                            : formatNumberRatio(item.pileNum)}
                                                        {!isChinaLanguage &&
                                                            (item.pileNum >= 1000000 ? 'M' : 'K')}
                                                    </span>
                                                ) : (
                                                    <span className={styles.values}>
                                                        {item.pileNum || 0}
                                                    </span>
                                                )}
                                                {item.pileNum >= 100000
                                                    ? intl.formatMessage({
                                                          id: 'page.screen.unit.disEnCount',
                                                          defaultMessage: ' ',
                                                      })
                                                    : intl.formatMessage({
                                                          id: 'page.screen.unit.disEnOne',
                                                          defaultMessage: ' ',
                                                      })}
                                            </div>
                                        </div>
                                        <div className={styles.rank_content_bottom}>
                                            <Progress
                                                percent={percent}
                                                showInfo={false}
                                                strokeWidt={15}
                                                strokeColor={{ '0%': '#1FFCF9', '100%': '#1FFCF9' }}
                                            />
                                        </div>
                                    </div> */}
                                </div>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default connect(({ wuxiXdtLongDPmessage, loading }) => ({
    loading,
    wuxiXdtLongDPmessage,
}))(LeftPage);
