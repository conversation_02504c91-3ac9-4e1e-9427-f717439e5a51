@import '~antd/lib/style/themes/default.less';
@import '~@/assets/styles/common.less';
html {
    width: 100%;
    height: 100%;
}
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
:global {
    .dp-station {
        position: relative;
    }
    .dp-station .count {
        position: absolute;
        left: 50%;
        color: #27e0fd;
        font-size: 10px;
        transform: translateX(-50%);
    }
    .cityoption {
        position: relative;
        width: 10px;
        height: 10px;
        background-color: red;
        border-radius: 50%;
        animation: cityshow 4s linear infinite;
    }
    .cityoption::before {
        position: absolute;
        top: -7.5px;
        left: -7.5px;
        width: 25px;
        height: 25px;
        border: 1px solid red;
        border-radius: 50%;
        animation: scalemiddle 4s linear infinite;
        content: '';
    }
    .cityoption::after {
        position: absolute;
        top: -15px;
        left: -15px;
        width: 40px;
        height: 40px;
        border: 1px solid red;
        border-radius: 50%;
        animation: scalemax 4s linear infinite;
        content: '';
    }
}

    .language_btn {
        color: #fff;
        width: 180px;
        height: 56px;
        line-height: 56px;
        background-image: url('./images/img7.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-left: 7px;
        text-align: center;
        font-size: 40px;
    }

.Near {
    position: relative;
    width: 6330px;
    height: 1550px;
    padding: 0 0 20px 0;
    background: #eee;
    background-image: url('./images/background.jpg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform-origin: 0% 0%;
    line-height: 1;
    display: flex;
    justify-content: space-evenly;
    overflow: hidden;
}

@media screen and (max-width: @screen-lg) {
    .salesExtra {
        display: none;
    }

    .rankingList {
        li {
            span:first-child {
                margin-right: 8px;
            }
        }
    }

    .topCard {
        > li {
            width: 25%;
        }
    }
}

@media screen and (max-width: @screen-md) {
    .rankingTitle {
        margin-top: 16px;
    }

    .salesCard .salesBar {
        padding: 16px;
    }

    .topCard {
        > li {
            width: 50%;
        }
    }
}

@media screen and (max-width: @screen-sm) {
    .salesExtraWrap {
        display: none;
    }

    .salesCard {
        :global {
            .ant-tabs-content {
                padding-top: 30px;
            }
        }
    }

    .topCard {
        > li {
            width: 100%;
        }
    }
}
.fle {
    display: flex;
    align-items: center;
    .citys {
        color: #fff;
        font-size: 12px;
        .por {
            color: #ffa200;
        }
    }
}
.count {
    display: inline-block;
    color: rgba(31, 168, 252, 1);
    font-weight: 400;
    font-size: 12px;
}

//左边
.xdt_leftPage {
    position: relative;
    width: 14.8%;
    .left_header_title {
        margin-top:40px;
        height: 150px;
        line-height: 150px;
        img {
            width: 100%;
        }
    }

    .left_overview {
        width: 95%;
        position: relative;
        height: 285px;
        margin-bottom: 29px;

        .left_title {
            width: 100%;
            height: 53px;
            line-height: 53px;
            padding-left: 32px;
            color: #FFF;
            font-weight: 400;
            font-size: 26px;
            background-image: url('./images/img26.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin-bottom: 3px;
        }
        .left_content_box {
            display: flex;
            flex-wrap: wrap;
            height: 225px;
            background-color: #031619;
            .left_content {
                width: 50%;
                display: flex;
                align-items: center;
                margin-bottom: 4px;
                .left_icon {
                    margin-left: 15px;
                    width: 140px;
                    height: 150px;
                }
                .left_text {
                    margin-left: 19px;
                    .values {
                        font-size: 54px;
                        font-weight: bold;
                        color: #fff;
                        margin: 15px 0 10px 0;
                        font-family: 'DINProBold';
                    }
                    .type {
                        white-space: nowrap;
                        font-size: 22px;
                        color: #d6d9db;
                    }
                }
            }
        }
    }
    .left_numers {
        width: 95%;
        position: relative;
        height: 1036px;
        margin-bottom: 23px;
        .left_title {
            width: 100%;
            height: 53px;
            line-height: 53px;
            padding-left: 32px;
            color: #FFF;
            font-weight: 400;
            font-size: 26px;
            background-image: url('./images/img26.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin-bottom: 3px;
        }

        .left_content_box {
            width: 100%;
            height: 976px;
            padding: 28px 31px;
            background-color: #031619;
            .cdz_rank {
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                height: 976px;
                .cdz_rank_item {
                    width: 100%;
                    height: 170px;
                    font-size: 26px;
                    padding: 18px 20px;
                    margin-bottom: 23px;
                    // background-image: url('./images/img30.png');
                    // background-repeat: no-repeat;
                    // background-size: 100% 100%;
                    background-color: #072E35;
                    .rank_title_view{
                        width:100%;
                        display: flex;
                    align-items: center;
                    justify-content: space-between;
                    }
                    .cdz_item_index {
                        color: #fff;
                        width: 32px;
                    }
                    .cdz_item_logo {
                        margin: 0 16px 0 10px;
                        .cdz_logo_img {
                            width: 42px;
                            height: 42px;
                        }
                    }
                    .rank_oper_name{
                        flex-shrink: 0;
                        font-size:26px ;
                        color:#fff;
                    }
                    .rank_title_info{
                        display: flex;
                        align-items: center;
                    }
                    .rank_title_progress{
                        width:40%;
                    }
                    .cdz_item_content {
                        flex-grow: 1;
                        .rank_content_top {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 4px;
                            .rank_content_title {
                                color: #fff;
                                max-width: 146px;
                                overflow: hidden;
                                white-space: nowrap;
                                word-break: keep-all;
                                text-overflow: ellipsis;
                            }
                            .rank_content_value {
                                color: #1FFCF9;
                                font-size: 24px;
                            }
                        }
                    }

                    .rank_info_view{
                        margin-top: 15px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        gap: 30px;

                        .rank_info_cell{
                            display: flex;
                            align-items: center;
                            padding:0 50rpx;
                            flex:1;
height: 83px;
background: #07272d;
border-radius: 6px;
.logo{
    margin:0 30px;
    width:84px;
    img{
        width:100%;
    }
}
.rank_info_content{
    flex:1;


    .rank_info_content_value{
        font-size: 36px;
    color:#fff;
    font-weight: bold;
    }
    .rank_info_content_label{
        margin-top:10px;
        font-size: 16px;
        color:#fff;
    }
}
                        }
                    }
                }
            }
        }
    }
}
@-webkit-keyframes rotate {
    from {
        -webkit-transform: rotate(360deg);
    }
    to {
        -webkit-transform: rotate(0deg);
    }
}
@-moz-keyframes rotate {
    from {
        -moz-transform: rotate(359deg);
    }
    to {
        -moz-transform: rotate(0deg);
    }
}
@-o-keyframes rotate {
    from {
        -o-transform: rotate(359deg);
    }
    to {
        -o-transform: rotate(0deg);
    }
}
@keyframes rotate {
    from {
        transform: rotate(359deg);
    }
    to {
        transform: rotate(0deg);
    }
}

@-webkit-keyframes rotates {
    from {
        -webkit-transform: rotate(360deg);
    }
    to {
        -webkit-transform: rotate(0deg);
    }
}
@-moz-keyframes rotates {
    from {
        -moz-transform: rotate(359deg);
    }
    to {
        -moz-transform: rotate(0deg);
    }
}
@-o-keyframes rotates {
    from {
        -o-transform: rotate(359deg);
    }
    to {
        -o-transform: rotate(0deg);
    }
}
@keyframes rotates {
    from {
        transform: rotate(359deg);
    }
    to {
        transform: rotate(0deg);
    }
}

@keyframes rotatePint {
    0% {
        transform: rotateY(0deg);
    }
    88% {
        transform: rotateY(0deg);
    }
    100% {
        transform: rotateY(90deg);
    }
}

//中间
.xdt_middlepage {
    position: relative;
    width: 65.56%;
    color: #fff;

    .xdt_pagecup_box {
        .xdt_map_back {
            display: flex;
            position: absolute;
            font-size: 28px;
            padding-top: 35px;
            z-index: 100;
            .xdt_back_btn {
                width: 235px;
                height: 66px;
                line-height: 66px;
                text-align: center;
                background-image: url('./images/img16.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                margin-right: 20px;
                font-size: 28px;
                cursor: pointer;
            }
            .xdt_back_null {
                width: 235px;
                height: 66px;
                margin-right: 20px;
                line-height: 66px;
            }
            .xdt_city_info {
                display: flex;
                align-items: center;
                .icon {
                    width: 33px;
                    height: 35px;
                    margin-right: 10px;
                }
                .xdt_city_name {
                    font-size: 36px;
                }
            }
        }
        .xdt_pagecup {
            position: absolute;
            top: 110px;
            right: 168px;
            margin: 24px 52px 0 52px;
            .page_item {
                display: flex;
                flex-grow: 1;
                justify-content: left;
                margin-bottom: 90px;
                .icon {
                    width: 157px;
                    height: 144px;
                    margin-right: 18px;
                    opacity: 0.8;
                }
                .info {
                    .item_value {
                        font-size: 76px;
                        font-weight: bold;
                        margin: 5px 0 8px 0;
                        font-family: 'DINProBold';
                        color: #ffa100;
                    }
                    .page_name {
                        width: 100%;
                        text-align: left;
                        .page_span {
                            display: inline-block;
                            color: #FFF;
                            font-size: 30px;
                        }
                    }

                }
            }
            .page_margin {
                margin: 0 94px;
            }
        }
    }

    .xdt_map_box {
        width: 100%;
        height: 100%;
        .xdt_map_name {
            width: 100%;
            position: absolute;
            top: 505px;
            left: 177px;
            z-index: 100;
            .xdt_map_nowName {
                width: 561px;
                height: 86px;
                font-size: 38px;
                line-height: 86px;
                text-align: center;
                list-style-type: none;
                background-image: url('./images/img32.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
        }
        .xdt_map {
            width: 100%;
            height: 100%;
        }
    }
    .xdt_map_type_tools{
        width: 200px;
        position: absolute;
        top: 320px;
        left: 268px;
        .xdt_map_tools_btn{
            display: flex;
            align-items: center;
            justify-content: center;
            color:#fff;
            margin:10px 0;
            width:173px;
            height: 50px;
            font-size: 20px;
            background: url('./images/tool_btn.png') no-repeat 100% 100%;
            &.active{
            background: url('./images/tool_btn_active.png') no-repeat 100% 100%;

            }
        }
    }

    .xdt_map_tooltips {
        width: 271px;
        position: absolute;
        bottom: 520px;
        right: 920px;
        .icon {
            width:271px;
            height: 60px;
            margin-bottom: 11px;
        }
    }

    .xdt_run {
        position: absolute;
        bottom: 500px;
        left: 168px;
        width: 563px;
        height: 227px;
        .xdt_ul {
            width: 100%;
            height: 100%;
            .xdt_li {
                width: 100%;
                height: 63px;
                margin-bottom: 18px;
                font-size: 26px;
                line-height: 63px;
                text-align: center;
                list-style-type: none;
                background-image: url('./images/k.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                span {
                    margin: 0 4px;
                    color: #2aeeff;
                }
            }
        }
        .container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            .mark{
                position: absolute;
                top: 0;
                width: 100%;
                height: 227px;
                background: linear-gradient(180deg,  rgba(3, 52, 71,0.9), rgba(3, 52, 71,0));;
            }
            .scroll {
                position: absolute;
                bottom: 0;
                width: 100%;
                .cityitem {
                    position: absolute;
                    width: 100%;
                    height: 63px;
                    margin-bottom: 10px;
                    color: red;
                    color: #fff;
                    font-size: 26px;
                    line-height: 63px;
                    text-align: center;
                    background-image: url('./images/k.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    transition: all 0.5s;
                    transform: translateY(0);

                    span {
                        margin: 0 4px;
                        color: #2aeeff;
                    }

                    .cityitem_text {
                        color: #02E5F6;
                        margin-left: 5px;
                    }
                }
            }
        }
    }


        .xdt_echart {
            position: absolute;
            bottom:0;
            width: 30.3%;
            height: 434px;
            &.left{
                left:0;
            }
            &.right{
                right:0
            }
            .left_title {
                width: 100%;
                height: 66px;
                line-height: 66px;
                padding-left: 24px;
                color: #FFF;
                font-weight: 400;
                font-size: 26px;
                background-image: url('./images/img27.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                margin-bottom: 3px;
            }

            .left_content_box {
                width: 100%;
                height: 366px;
                opacity: 0.8;
                background: #071a1d;
                .left_top_box {
                    display: flex;
                    align-items: center;
                    padding: 13px 11px 2px 18px;
                    position: absolute;
                    z-index: 10;
                    .left_unit {
                        flex-grow: 1;
                        font-size: 26px;
                        color: #fff;
                    }
                    .left_title {
                        font-size: 26px;
                        color: #fff;
                    }
                    .left_tab {
                        font-size: 26px;
                        display: flex;
                        justify-content: center;
                        .left_tab_item {
                            color: #B5C1D1;
                            opacity: 0.8;
                            width: 112px;
                            height: 36px;
                            line-height: 36px;
                            background-image: url('./images/img6.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            margin-left: 7px;
                            text-align: center;
                        }
                        .left_tab_active {
                            color: #fff;
                            width: 112px;
                            height: 36px;
                            line-height: 36px;
                            background-image: url('./images/img7.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            margin-left: 7px;
                            text-align: center;
                        }
                    }
                }
                .left_echa_box {
                    .left_echa {
                        width: 100%;
                        height: 366px;
                    }
                }

            }
        }

}
@keyframes bloom2 {
    0% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}

@keyframes bloom3 {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.6;
    }
}

@keyframes bloom4 {
    0% {
        opacity: 0.1;
    }
    100% {
        opacity: 0.3;
    }
}

//右边
.xdt_rightPage {
    position: relative;
    width: 13.31%;
    .left_header_title {
        height: 145px;
        line-height: 145px;
        text-align: right;
        padding-right: 5px;
        .text {
            background: linear-gradient(0deg,#496a91 9%, #fff 100%);
            font-size: 52px;
            font-weight: bold;
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
    }
    .city_view {
        position: relative;
        width: 100%;
        height: 260px;
        padding: 20px;
        overflow: hidden;
        .city_item {
            position: absolute;
            color: #2aeeff;
            line-height: 1;
            white-space: nowrap;
        }
    }
    .left_overview {
        position: relative;
        width: 100%;
        height: 480px;
        margin-bottom: 23px;

        .left_title {
            width: 100%;
            height: 53px;
            line-height: 53px;
            padding-left: 24px;
            color: #FFF;
            font-weight: 400;
            font-size: 26px;
            background-image: url('./images/img26.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin-bottom: 3px;
        }
        .left_content_box {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            height: 421px;
            background-color: #031619;
            padding: 9px 5px 11px 5px;
            .left_content {
                width: 380px;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                background-image: url('./images/img22.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                margin: 0 6px 11px 6px;
                .left_text {
                    .values {
                        font-size: 60px;
                        font-weight: bold;
                        color: #fff;
                        margin: 8px 0 10px 0;
                        font-family: 'DINProBold';
                    }
                    .type {
                        white-space: nowrap;
                        margin-left: 10px;
                        font-size: 26px;
                        color: #d6d9db;
                        text {
                            color: #d6d9db;
                        }
                    }
                }
            }
            .left_content_color1 {
                background-image: url('./images/img22.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                .left_text {
                    .values {
                        // background: linear-gradient(0deg,#48a1d2 0%, #fff 100%);
                        // background-clip: text;
                        // -webkit-background-clip: text;
                        // color: transparent;
                        color:#fff;
                        font-size: 70px;

                    }
                }
            }
        }
    }
    .xdt_city_echart {
        position: relative;
        width: 100%;
        height: 876px;
        margin-bottom: 23px;
        .left_title {
            width: 100%;
            height: 53px;
            line-height: 53px;
            padding-left: 24px;
            color: #FFF;
            font-weight: 400;
            font-size: 26px;
            background-image: url('./images/img26.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin-bottom: 3px;
        }

        .left_content_box {
            width: 100%;
            height: 819px;
            background-color: #031619;
            overflow: hidden;
            .city_rank {
                height: 819px;
                padding: 16px 93px 16px 38px;
                overflow-y: auto;
                margin-right: -60px;
                .city_rank_item {
                    width: 100%;
                    height: 92px;
                    font-size: 26px;
                    padding: 0 18px;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    background-image: url('./images/img28.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    cursor: pointer;

                    .city_item_logo {
                        margin: 0 16px 0 20px;
                        .city_logo_img {
                            background-image: url('./images/rank4.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            width: 33px;
                            height: 38px;
                            color: #fff;
                            font-size: 21px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                        .city_logo_img1 {
                            background-image: url('./images/rank1.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                        .city_logo_img2 {
                            background-image: url('./images/rank2.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                        .city_logo_img3 {
                            background-image: url('./images/rank3.png');
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                    .city_item_content {
                        flex-grow: 1;


                        .rank_content_top {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            .rank_content_title {
                                display: flex;
                                align-items: center;
                                color: #fff;
                                font-weight: bold;
                                max-width: 320px;
                                overflow: hidden;
                                word-break: keep-all;
                                text-overflow: ellipsis;
                            }
                            .rank_content_flag{
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                margin-left:  20px;
                                padding:0 15px;
                                height: 29px;
                                background: #334d52;
                                border: 2px solid #adadad;
                                border-radius: 6px;
                                font-size: 18px;
                                font-weight: 500;
                                text-align: left;
                                color: #fff;
                                white-space: no-warp;
                            }
                            .rank_content_value {
                                color: #1FFCF9;
                                font-size: 36px;
                                font-weight: bold;
                            }
                        }
                    }
                }
                .city_rank_item2 {
                    width: 762px;
                    height: 58px;
                    font-size: 26px;
                    padding: 0 18px;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    background-image: url('./images/img29.png');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }
            .left_echa_box {
                .left_echa {
                    width: 100%;
                    height: 240px;
                }
            }

        }
    }
}
@-webkit-keyframes rotate {
    from {
        -webkit-transform: rotate(360deg);
    }
    to {
        -webkit-transform: rotate(0deg);
    }
}
@-moz-keyframes rotate {
    from {
        -moz-transform: rotate(359deg);
    }
    to {
        -moz-transform: rotate(0deg);
    }
}
@-o-keyframes rotate {
    from {
        -o-transform: rotate(359deg);
    }
    to {
        -o-transform: rotate(0deg);
    }
}
@keyframes rotate {
    from {
        transform: rotate(359deg);
    }
    to {
        transform: rotate(0deg);
    }
}
