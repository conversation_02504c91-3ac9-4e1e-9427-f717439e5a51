import { message } from 'antd';
import {
    qryJoinGeneralView,
    qryChargePqRecordTotals,
    qryChargePqRecordYearOnYear,
    qryChargePqRecordChainRatio,
    qryChargePqRecordChainRatioCity,
    querySreenMapStation,
    querySreenMapInfoApi,
} from '@/services/newScreenXdt.js';
import {
    qryChargePqAndTimesAndUserRateApi,
    areaEquipNumApi,
} from '@/services/LarageScreen/newXdtScreenApi.js';

import { MapToolTypes } from '../declare';

export default {
    namespace: 'wuxiXdtLongDPmessage',

    state: {
        winSize: {
            width: 6330,
            height: 1550,
        },
        JoinGeneralView: {
            pileNumList: [],
            stationTypeList: [],
            pileTypeList: [],
            pqList: [],
        }, // 大屏资产信息
        ChargePqRecordTotal: {
            changingEquipNum: 0,
            thisMonthPq: '',
            thisMonthPqRate: 0,
            thisYearPq: '',
            thisYearPqRate: 0,
            todayPq: 0,
            todayPqRate: 0,
            totalOrderNum: 0,
            totalPq: 0,
            totalUserNum: 0,
        }, // 充电量总计
        ChargePqRecordYearOnYear: {
            thisData: [],
            lastData: [],
            rate: [],
        }, // 新电途大屏充电量总计
        ChargePqRecordChainRatio: {
            thisData: [],
            lastData: [],
        }, // 充电量情况 环比
        ChargeCity: [],
        messageList: [], // 消息推送
        mapCityList: [], // 地图城市点坐标
        mapStationList: [], // 地图站点坐标
        mapAreaStationList: [], // 区县 地图站点坐标
        chargePqDataList: [], // 年度 -充电量&市占
        userDataList: [], // 用户增长趋势
        cityEquipNumList: [], // 新充电桩城市排名

        mapToolType: MapToolTypes.Station, //接入场站
    },

    effects: {
        *xdtqryJoinGeneralView({ payload }, { call, put }) {
            try {
                const response = yield call(qryJoinGeneralView, payload);
                yield put({
                    type: 'setqryJoinGeneralView',
                    payload: {
                        JoinGeneralView: response.data,
                    },
                });
            } catch (error) {}
        },
        *xdtqryChargePqRecordTotal({ payload }, { call, put }) {
            try {
                const response = yield call(qryChargePqRecordTotals, payload);
                const {
                    changingEquipNum,
                    thisMonthPq,
                    thisMonthPqRate,
                    thisYearPq,
                    thisYearPqRate,
                    todayPq,
                    todayPqRate,
                    totalOrderNum,
                    totalPq,
                    totalUserNum,
                } = response.data;
                yield put({
                    type: 'setqryChargePqRecordTotal',
                    payload: {
                        ChargePqRecordTotal: {
                            changingEquipNum: Number(changingEquipNum),
                            thisMonthPq: Number(thisMonthPq),
                            thisMonthPqRate: Number(thisMonthPqRate),
                            thisYearPq: Number(thisYearPq),
                            thisYearPqRate: Number(thisYearPqRate),
                            todayPq: Number(todayPq),
                            todayPqRate: Number(todayPqRate),
                            totalOrderNum: Number(totalOrderNum),
                            totalPq: Number(totalPq),
                            totalUserNum: Number(totalUserNum),
                        },
                    },
                });
            } catch (error) {}
        },

        *xdtqryChargePqRecordYearOnYear({ payload }, { call, put }) {
            try {
                const response = yield call(qryChargePqRecordYearOnYear, payload);
                yield put({
                    type: 'setqryChargePqRecordYearOnYear',
                    payload: {
                        ChargePqRecordYearOnYear: response.data,
                    },
                });
            } catch (error) {}
        },

        *xdtqryChargePqRecordChainRatio({ payload }, { call, put }) {
            try {
                const response = yield call(qryChargePqRecordChainRatio, payload);
                yield put({
                    type: 'setqryChargePqRecordChainRatio',
                    payload: {
                        ChargePqRecordChainRatio: response.data,
                    },
                });
            } catch (error) {}
        },

        *xdtqryCity({ payload }, { call, put }) {
            try {
                const response = yield call(qryChargePqRecordChainRatioCity, payload);
                yield put({
                    type: 'setqryCitys',
                    payload: {
                        ChargeCity: response.data?.cityList?.slice(0, 20),
                    },
                });
            } catch (error) {}
        },
        *xdtqryStation({ payload }, { call, put }) {
            try {
                // const response = yield call(querySreenMapStation, payload);
                const mapInfo = yield call(querySreenMapInfoApi, payload);

                yield put({
                    type: 'updateMapStationList',
                    // payload: response.data.sreenMapStationList,
                    payload: {
                        // ...response.data,
                        ...mapInfo.data,
                    },
                });
                // if (
                //     response.data.xdtScreenCityMapList &&
                //     response.data.xdtScreenCityMapList.length &&
                //     response.data.xdtScreenCityMapList.length > 0
                // ) {
                //     yield put({
                //         type: 'updateMapCityList',
                //         payload: response.data.xdtScreenCityMapList,
                //     });
                // }
            } catch (error) {}
        },
        // 市级 已覆盖城市
        *xdtqryAreaStation({ payload }, { call, put }) {
            try {
                const response = yield call(querySreenMapInfoApi, payload);
                yield put({
                    type: 'updateMapAreaStationList',
                    payload: response.data.stationVoList,
                });
            } catch (error) {}
        },
        // 年度 -充电量&市占  和  用户增长趋势
        *qryChargePqAndTimesAndUserRate({ payload }, { call, put }) {
            try {
                const response = yield call(qryChargePqAndTimesAndUserRateApi, payload);
                yield put({
                    type: 'updateChargePqAndTimesAndUserRateList',
                    payload: {
                        chargePqDataList: response.data.chargePqData,
                        userDataList: response.data.userData,
                    },
                });
            } catch (error) {}
        },
        // 新充电桩城市排名
        *cityEquipNum({ payload }, { call, put }) {
            try {
                let response = null;
                response = yield call(areaEquipNumApi, payload);
                yield put({
                    type: 'updateCityEquipNumList',
                    payload: response?.data?.pileNumList || [],
                });
            } catch (error) {}
        },
    },

    reducers: {
        updataScreenDp(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        setqryJoinGeneralView(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        setqryChargePqRecordTotal(state, { payload }) {
            return {
                ...state,
                ChargePqRecordTotal: {
                    ...state.ChargePqRecordTotal,
                    ...payload.ChargePqRecordTotal,
                },
            };
        },
        setqryChargePqRecordYearOnYear(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        setqryChargePqRecordChainRatio(state, { payload }) {
            // console.log(3333);
            return {
                ...state,
                ...payload,
            };
        },
        setqryCitys(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        setMessageList(state, { payload }) {
            // console.log(4444, payload, state.messageList);
            return {
                ...state,
                messageList: payload.messageList,
            };
        },
        updateMapCityList(state, { payload }) {
            return {
                ...state,
                mapCityList: [...payload],
            };
        },
        updateMapStationList(state, { payload }) {
            return {
                ...state,
                mapStationList: payload,
            };
        },
        updateMapAreaStationList(state, { payload }) {
            return {
                ...state,
                mapAreaStationList: payload,
            };
        },
        updateChargePqAndTimesAndUserRateList(state, { payload }) {
            return {
                ...state,
                chargePqDataList: payload.chargePqDataList,
                userDataList: payload.userDataList,
            };
        },
        updateCityEquipNumList(state, { payload }) {
            return {
                ...state,
                cityEquipNumList: payload,
            };
        },

        updateWindowSize(state, { size }) {
            return {
                ...state,
                winSize: size,
            };
        },
    },
    subscriptions: {
        setup({ history, dispatch }) {
            (function () {
                const { clientWidth } = document.documentElement;
                const { clientHeight } = document.documentElement;
                if (!clientWidth) return;
                dispatch({
                    type: 'updateWindowSize',
                    size: { width: clientWidth, height: clientHeight },
                });
            })();
        },
        newKeyEvent({ dispatch }) {
            // 监听窗口变化 去改变状态, 监听 表单输入等等。
            window.addEventListener('resize', () => {
                const { clientWidth } = document.documentElement;
                const { clientHeight } = document.documentElement;
                if (!clientWidth) return;
                dispatch({
                    type: 'updateWindowSize',
                    size: { width: clientWidth, height: clientHeight },
                });
            });
        },
    },
};
