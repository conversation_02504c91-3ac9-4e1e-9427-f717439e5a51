import type { ActionType, ProColumnType, ProFormInstance } from '@ant-design/pro-components';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { DownOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Dropdown, Menu, message, Modal, Popconfirm, Space, Typography } from 'antd';
import moment from 'moment';
import React, { useRef, useState } from 'react';

import XdtProTable from '@/components/XdtProTable';
import MngCacheAreaView from '@/components/CacheAreaView/MngCacheAreaView';
import MainOperatorSelect from '@/components/FormItem/MainOperatorSelect';
import CreatePhysicalCardModal from './components/CreatePhysicalCardModal';
import ImportModal from '@/components/ImportModal';
import ImportResultModal from './components/ImportResultModal';
import AllocateMerchantByCardNoModal from './components/AllocateMerchantByCardNoModal';
import AllocateMerchantModal from './components/AllocateMerchantModal';
import EditPhysicalCardModal from './components/EditPhysicalCardModal';

import { PhysicalCardAllocationStatus } from '@/constants/Fleet';
import {
    deleteCardInventoryApi,
    getCardInventoryPageListApi,
    importCardInventoryApi,
    importCardInventoryByKeyApi,
    recoverCardInventoryApi,
} from '@/services/Fleet/PhysicalCardApi';
import { DATE_FORMAT_STRING } from '@/constants';
import { uploadFileMngCacheApi } from '@/services/CommonApi';

export default () => {
    const cacheRef = useRef<ActionRef.CacheArea>();
    const actionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();
    const importRef = useRef<ActionRef.OpenModal>();
    const resultRef = useRef<ActionRef.OpenModal>();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<Fleet.CardInventoryQueryVo[]>([]);
    const allocateMerchantModalRef = useRef<ActionRef.OpenModal>(null);
    const allocateMerchantByCardNoModalRef = useRef<ActionRef.OpenModal>(null);
    const [importKey, setImportKey] = useState<string>('');

    const { run: deleteApi } = useRequest(deleteCardInventoryApi, {
        manual: true,
        onSuccess: () => {
            message.success('删除成功');
            refresh();
        },
    });
    const { run: recoverApi } = useRequest(recoverCardInventoryApi, {
        manual: true,
        onSuccess: () => {
            message.success('收回成功');
            refresh();
        },
    });
    const handleAllocate = (ids: (string | number)[]) => {
        allocateMerchantModalRef.current?.open(ids);
    };

    const columns: ProColumnType<Fleet.CardInventoryQueryVo>[] = [
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 180,
            hideInSearch: true,
        },
        {
            title: '物理卡号',
            dataIndex: 'physicsCardNo',
            key: 'physicsCardNo',
            width: 180,
            fieldProps: {
                maxLength: 100,
            },
        },
        {
            title: '资产卡号',
            dataIndex: 'cardNo',
            key: 'cardNo',
            width: 180,
            fieldProps: { maxLength: 100 },
        },
        {
            title: '充电商家',
            dataIndex: 'allocationOperId',
            key: 'allocationOperId',
            hideInTable: true,
            renderFormItem: () => <MainOperatorSelect />,
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime_search',
            valueType: 'dateRange',
            search: {
                transform: (value) => {
                    const [startTime, endTime] = value;
                    return {
                        startTime: startTime
                            ? moment(startTime).startOf('day').format(DATE_FORMAT_STRING.FULL)
                            : undefined,
                        endTime: endTime
                            ? moment(endTime).endOf('day').format(DATE_FORMAT_STRING.FULL)
                            : undefined,
                        createTime: undefined,
                    };
                },
            },
            hideInTable: true,
        },
        {
            title: '分配状态',
            dataIndex: 'allocationStatusName',
            key: 'allocationStatusName',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '分配商家',
            dataIndex: 'allocationOperName',
            key: 'allocationOperName',
            width: 240,
            hideInSearch: true,
        },
        {
            title: '操作',
            key: 'action',
            width: 160,
            render: (_, record) => (
                <Space>
                    {record?.allocationStatus === PhysicalCardAllocationStatus.UNALLOCATED && (
                        <Typography.Link
                            onClick={() => handleAllocate([record.inventoryId as string])}
                        >
                            分配
                        </Typography.Link>
                    )}
                    {/* 暂时取消查看功能，没有页面可以跳转 */}
                    {/* {record?.allocationStatus === PhysicalCardAllocationStatus.ALLOCATED && (
                        <Typography.Link onClick={() => {}}>查看</Typography.Link>
                    )} */}
                    {record?.allocationStatus === PhysicalCardAllocationStatus.UNALLOCATED && (
                        <Typography.Link
                            onClick={() =>
                                editPhysicalCardModalRef.current.open(record?.inventoryId)
                            }
                        >
                            编辑
                        </Typography.Link>
                    )}
                    {record?.allocationStatus === PhysicalCardAllocationStatus.ALLOCATED && (
                        <Popconfirm
                            title="确定要收回这张电卡吗？"
                            onConfirm={async () => {
                                await recoverApi({ inventoryIds: [record.inventoryId as string] });
                                return true;
                            }}
                        >
                            <Typography.Link>收回</Typography.Link>
                        </Popconfirm>
                    )}
                    <Popconfirm
                        title="确定要删除这张电卡吗？"
                        onConfirm={async () => {
                            await deleteApi({ inventoryIds: [record.inventoryId as string] });
                            return true;
                        }}
                    >
                        <Typography.Link>删除</Typography.Link>
                    </Popconfirm>
                </Space>
            ),
            hideInSearch: true,
        },
    ];

    const refresh = () => {
        setSelectedRowKeys([]);
        actionRef.current?.reload();
    };

    const rowSelection = {
        width: 48,
        selectedRowKeys,
        onChange: (keys: React.Key[], rows: any) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
        },
    };

    const createPhysicalCardModalRef = useRef<ActionRef.OpenModal>(null);
    const editPhysicalCardModalRef = useRef<any>(null);

    const toolButtons = [
        // 新建按钮
        <Button key="new" type="primary" onClick={() => createPhysicalCardModalRef.current?.open()}>
            新建
        </Button>,
        // 批量导入按钮
        <Button key="batchImport" type="primary" onClick={() => importRef.current?.show?.()}>
            批量导入
        </Button>,
        // 批量分配按钮
        <Dropdown.Button
            icon={<DownOutlined />}
            key="batchAllocation"
            onClick={() => {
                console.debug('批量分配');
                if (selectedRowKeys.length === 0) {
                    message.error('请选择要批量分配的电卡');
                    return;
                } else {
                    const hasAllocatedCard = selectedRows.some(
                        (card) => card.allocationStatus === PhysicalCardAllocationStatus.ALLOCATED,
                    );
                    if (hasAllocatedCard) {
                        message.error('存在已分配的卡，请重新选择');
                        return;
                    }
                    handleAllocate(selectedRowKeys);
                }
            }}
            type="primary"
            overlay={
                <Menu
                    items={[
                        {
                            label: '按卡号批量分配',
                            key: 'batchAllocationByCardNo',
                            onClick: () => {
                                allocateMerchantByCardNoModalRef.current?.open();
                            },
                        },
                    ]}
                />
            }
        >
            批量分配
        </Dropdown.Button>,
        // 批量收回按钮
        <Button
            key="batchRecover"
            type="primary"
            onClick={() => {
                if (selectedRowKeys.length === 0) {
                    message.error('请选择要批量收回的电卡');
                    return;
                } else {
                    const hasUnallocatedCard = selectedRows.some(
                        (card) =>
                            card.allocationStatus === PhysicalCardAllocationStatus.UNALLOCATED,
                    );
                    if (hasUnallocatedCard) {
                        message.error('存在未分配的卡，请重新选择');
                        return;
                    }
                    Modal.confirm({
                        title: '确定要收回这些电卡吗？',
                        okText: '确定',
                        cancelText: '取消',
                        onOk: async () => {
                            await recoverApi({
                                inventoryIds: selectedRowKeys as string[],
                            });
                            return true;
                        },
                    });
                }
            }}
        >
            批量收回
        </Button>,
        // 批量删除按钮
        <Button
            key="batchDelete"
            danger
            onClick={() => {
                if (selectedRowKeys.length === 0) {
                    message.error('请选择要批量删除的电卡');
                    return;
                } else {
                    Modal.confirm({
                        title: '确定要删除这些电卡吗？',
                        okText: '确定',
                        cancelText: '取消',
                        onOk: async () => {
                            await deleteApi({
                                inventoryIds: selectedRowKeys as string[],
                            });
                            return true;
                        },
                    });
                }
            }}
        >
            批量删除
        </Button>,
    ];

    return (
        <PageHeaderWrapper
            extra={
                <MngCacheAreaView
                    listParams={{ bizType: 'cardInventoryExport' }}
                    uploadApi={uploadFileMngCacheApi}
                    upLoadParams={{
                        bizType: 'cardInventoryExport',
                    }}
                    ref={cacheRef}
                />
            }
        >
            <XdtProTable
                actionRef={actionRef}
                formRef={formRef}
                rowKey="inventoryId"
                prefixKey="cardInventorylist"
                exportButtonTitle="导出至暂存区"
                exportEvent={(params) => {
                    cacheRef?.current?.apply(params).then(() => {
                        cacheRef?.current?.count();
                    });
                }}
                toolButtons={toolButtons}
                tabsConfig={{
                    dataIndex: 'allocationStatus',
                    options: [
                        { label: '全部', key: 'ALL' },
                        { label: '未分配', key: '0' },
                        { label: '已分配', key: '1' },
                    ],
                }}
                columns={columns}
                requestApi={getCardInventoryPageListApi}
                rowSelection={rowSelection}
            />
            <CreatePhysicalCardModal ref={createPhysicalCardModalRef} callback={refresh} />
            <EditPhysicalCardModal ref={editPhysicalCardModalRef} callback={refresh} />
            <AllocateMerchantModal ref={allocateMerchantModalRef} callback={refresh} />
            <AllocateMerchantByCardNoModal
                ref={allocateMerchantByCardNoModalRef}
                callback={refresh}
            />
            <ImportModal
                ref={importRef}
                title="批量导入电卡库存"
                downLoadPath="/aliMini/xdt/static/excel/fleetManage/电卡库存导入.xlsx"
                onUpload={async (formData: any, callback?: (params: any) => void) => {
                    const options = new FormData();
                    for (const key in formData) {
                        options.append(key, formData[key]);
                    }
                    const { ret, data } = await importCardInventoryApi(formData);
                    if (ret === 200) {
                        const successList = data?.successList;
                        const failList = data?.errorList;
                        setImportKey(data?.importKey as string);
                        resultRef?.current?.open({
                            successList: successList,
                            failList: failList,
                            importSuccNum: successList?.length,
                            importFailNum: failList?.length,
                        });
                    }
                }}
                zIndex={101}
            ></ImportModal>
            <ImportResultModal
                ref={resultRef}
                onConfirm={() => {
                    importCardInventoryByKeyApi(importKey).then((res) => {
                        if (res.ret === 200) {
                            message.success('导入成功');
                            importRef?.current?.close?.();
                            resultRef?.current?.close?.();
                            refresh();
                        }
                    });
                }}
                zIndex={102}
                downloadColumns={[
                    {
                        title: '物理卡号',
                        dataIndex: 'physicsCardNo',
                        width: 140,
                    },
                    {
                        title: '资产卡号',
                        dataIndex: 'cardNo',
                        width: 140,
                    },
                    {
                        title: '分配商家',
                        dataIndex: 'allocationOperName',
                        width: 160,
                    },
                ]}
                downloadFileName="电卡库存"
            ></ImportResultModal>
        </PageHeaderWrapper>
    );
};
