import { useRequest } from 'ahooks';
import { Modal, Form, Input, Button, Row, Space, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import MainOperatorSelect from '@/components/FormItem/MainOperatorSelect';
import { allocationOperCardNoApi } from '@/services/Fleet/PhysicalCardApi';

const AllocateMerchantByCardNoModal: React.ForwardRefRenderFunction<
    ActionRef.OpenModal,
    { callback?: (values?: any) => void }
> = ({ callback }, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
        open() {
            form.resetFields();
            setVisible(true);
        },
    }));

    const { run, loading } = useRequest(allocationOperCardNoApi, {
        manual: true,
        onSuccess: () => {
            message.success('操作成功');
            callback?.();
            handleCancel();
        },
    });

    const handleOk = () => {
        form.validateFields().then((values) => {
            run(values);
        });
    };

    const handleCancel = () => {
        form.resetFields();
        setVisible(false);
    };

    return (
        <Modal
            title="分配商家"
            visible={visible}
            footer={null}
            onCancel={handleCancel}
            destroyOnClose
            width={600}
        >
            <Form form={form} layout="inline">
                <Form.Item
                    name="startCardNo"
                    label="资产卡号"
                    required
                    rules={[
                        { required: true, message: '请输入起始资产卡号' },
                        {
                            validator: (_, value) => {
                                //校验正整数
                                if (value && !/^\d*$/.test(value)) {
                                    return Promise.reject(new Error('请输入正整数'));
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    <Input placeholder="起始卡号" maxLength={100} />
                </Form.Item>
                <span style={{ marginLeft: '8px', marginRight: '8px' }}>-</span>
                <Form.Item
                    name="endCardNo"
                    required
                    rules={[
                        { required: true, message: '请输入结束资产卡号' },
                        {
                            validator: (_, value) => {
                                //校验正整数
                                if (value && !/^\d*$/.test(value)) {
                                    return Promise.reject(new Error('请输入正整数'));
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    <Input placeholder="结束卡号" maxLength={100} />
                </Form.Item>
                <br />
                <Form.Item
                    name="allocationOperId"
                    label="充电商家"
                    required
                    rules={[{ required: true, message: '请选择充电商家' }]}
                    style={{ marginTop: '16px', width: '100%' }}
                >
                    <MainOperatorSelect style={{ width: '180px' }} />
                </Form.Item>
                <br />
                <Form.Item style={{ marginTop: '16px', width: '100%' }}>
                    <Row justify="center">
                        <Space size="large">
                            <Button type="primary" onClick={handleOk} loading={loading}>
                                提交
                            </Button>
                            <Button onClick={handleCancel}>取消</Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(AllocateMerchantByCardNoModal);
