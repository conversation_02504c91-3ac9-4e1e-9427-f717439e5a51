import { useRequest } from 'ahooks';
import type { PaginationProps } from 'antd';
import { Modal, Space, Typography, Spin, Tabs, Table } from 'antd';
import { useImperativeHandle, forwardRef, useState } from 'react';

import { localDownloadFileToExcel } from '@/utils/utils';
import { exportFailFileApi } from '@/services/Marketing/ReadjustApi';

type IMPORT_RESULT_VO = {
    importFailNum?: number;
    importSuccNum?: number;
    successList?: any[];
    failList?: any[];
    batchId?: string;
};

const ImportResultModal = (props: any, ref: any) => {
    const { onConfirm, zIndex, downloadColumns = [], downloadFileName = '' } = props;
    const [visible, toggleVisible] = useState(false);
    const [resultInfo, updateResultInfo] = useState<IMPORT_RESULT_VO>();
    const [activeTab, setActiveTab] = useState('success');

    const [pagination, setPagination] = useState<PaginationProps>({
        current: 1,
        pageSize: 5,
        total: 0,
    });

    const handleTableChange = (newPagination: PaginationProps) => {
        setPagination(newPagination);
    };

    useImperativeHandle(ref, () => {
        return {
            open: (result: IMPORT_RESULT_VO) => {
                updateResultInfo(result);
                openModalEvent();
            },
            close: () => {
                closeModalEvent();
            },
        };
    });

    const openModalEvent = () => {
        toggleVisible(true);
    };

    const closeModalEvent = () => {
        toggleVisible(false);
        updateResultInfo(undefined);
    };

    const handleTabChange = (key: string) => {
        setActiveTab(key);
    };

    const { run: exportFailEvent, loading: exportLoading } = useRequest(
        async () => {
            try {
                if (resultInfo?.batchId) {
                    const params = {
                        batchId: resultInfo?.batchId,
                    };
                    await exportFailFileApi(params);
                } else {
                    const { failList } = resultInfo ?? {};
                    const tableData: any[] = [];
                    failList?.forEach((item) => {
                        const col: Record<string, any> = {};
                        [
                            ...downloadColumns,
                            {
                                title: '失败原因',
                                dataIndex: 'msg',
                                key: 'failReason',
                                width: 150,
                            },
                        ].forEach((colItem: any) => {
                            const name = colItem.title;
                            const key = colItem.dataIndex;
                            if (key) {
                                const value = item[key];
                                col[name] = value;
                            }
                        });
                        tableData.push(col);
                    });
                    localDownloadFileToExcel({ data: tableData, fileName: downloadFileName });
                }

                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            throttleWait: 2000,
        },
    );

    return (
        <Modal
            title="批量导入电卡库存"
            visible={visible}
            width={1000}
            onCancel={closeModalEvent}
            destroyOnClose
            onOk={onConfirm}
            okText="确定"
            okButtonProps={{
                disabled: !resultInfo?.successList?.length,
            }}
            cancelText="取消"
            zIndex={zIndex}
        >
            <div>
                <Space>
                    已成功导入{resultInfo?.importSuccNum || resultInfo?.successList?.length || 0}
                    条数据，
                    {resultInfo?.importFailNum || 0}
                    条导入失败
                    {resultInfo?.failList && (
                        <Spin spinning={exportLoading}>
                            <Typography.Link
                                onClick={() => {
                                    exportFailEvent();
                                }}
                            >
                                下载失败文件
                            </Typography.Link>
                        </Spin>
                    )}
                </Space>
            </div>
            <Tabs activeKey={activeTab} onChange={handleTabChange}>
                <Tabs.TabPane tab="导入成功" key="success">
                    <Table
                        columns={downloadColumns}
                        dataSource={resultInfo?.successList || []}
                        pagination={{
                            ...pagination,
                            total: resultInfo?.successList?.length || 0,
                            onChange: (page, pageSize) =>
                                handleTableChange({ current: page, pageSize }),
                        }}
                    />
                </Tabs.TabPane>
                <Tabs.TabPane tab="导入失败" key="fail">
                    <Table
                        columns={[
                            ...downloadColumns,
                            {
                                title: '失败原因',
                                dataIndex: 'msg',
                                key: 'failReason',
                                width: 150,
                            },
                        ]}
                        dataSource={resultInfo?.failList || []}
                        pagination={{
                            ...pagination,
                            total: resultInfo?.failList?.length || 0,
                            onChange: (page, pageSize) =>
                                handleTableChange({ current: page, pageSize }),
                        }}
                    />
                </Tabs.TabPane>
            </Tabs>
        </Modal>
    );
};

export default forwardRef(ImportResultModal);
