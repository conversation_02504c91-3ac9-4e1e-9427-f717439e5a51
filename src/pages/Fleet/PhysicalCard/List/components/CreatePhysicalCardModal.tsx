import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Modal, Form, Button, Row, Space, Switch, Input, message } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import MainOperatorSelect from '@/components/FormItem/MainOperatorSelect';
import { saveCardInventoryApi } from '@/services/Fleet/PhysicalCardApi';
import { isNumber, isInteger } from '@/utils/verify';

const CreatePhysicalCardModal: React.ForwardRefRenderFunction<
    ActionRef.OpenModal,
    { callback?: (values?: any) => void }
> = ({ callback }, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState<boolean>(false);
    const [autoIncrement, setAutoIncrement] = useState<boolean>(true);
    const [sameMerchant, setSameMerchant] = useState<boolean>(true);
    const [selectedMerchant, setSelectedMerchant] = useState<string | undefined>();

    useImperativeHandle(ref, () => ({
        open() {
            setVisible(true);
        },
    }));

    const { run, loading } = useRequest(saveCardInventoryApi, {
        manual: true,
        onSuccess: () => {
            callback?.();
            handleCancel();
        },
    });

    const handleOk = () => {
        form.validateFields()
            .then((values) => {
                const cards = values.cards;
                const physicsCardNoSet = new Set<string>();
                const cardNoSet = new Set<string>();

                for (const card of cards) {
                    if (physicsCardNoSet.has(card.physicsCardNo)) {
                        message.error(`物理卡号 ${card.physicsCardNo} 重复`);
                        return;
                    }
                    if (cardNoSet.has(card.cardNo)) {
                        message.error(`资产卡号 ${card.cardNo} 重复`);
                        return;
                    }
                    physicsCardNoSet.add(card.physicsCardNo);
                    cardNoSet.add(card.cardNo);
                }
                run({ items: values.cards });
            })
            .catch((errorInfo) => {
                console.log('Failed:', errorInfo);
            });
    };

    const handleCancel = () => {
        form.resetFields();
        setSelectedMerchant(undefined);
        setAutoIncrement(true);
        setSameMerchant(true);
        setVisible(false);
    };

    return (
        <Modal
            title="新建电卡"
            visible={visible}
            width={648}
            footer={null}
            onCancel={handleCancel}
            destroyOnClose
        >
            <Space direction="horizontal" size="large" style={{ marginBottom: 16 }}>
                <div>
                    资产卡号自动递增：
                    <Switch
                        checked={autoIncrement}
                        onChange={(checked) => {
                            setAutoIncrement(checked);
                            if (checked) {
                                const cards = form.getFieldValue('cards');
                                if (cards && cards.length > 0 && isNumber(cards[0].cardNo)) {
                                    form.setFieldsValue({
                                        cards: cards.map((card: any, index: number) => ({
                                            ...card,
                                            cardNo: `${parseInt(cards[0].cardNo || '0') + index}`,
                                        })),
                                    });
                                }
                            }
                        }}
                    />
                </div>
                <div>
                    分配给同个商家：
                    <Switch
                        checked={sameMerchant}
                        onChange={(checked) => setSameMerchant(checked)}
                    />
                    {sameMerchant && (
                        <MainOperatorSelect
                            value={selectedMerchant}
                            onChange={(value) => {
                                setSelectedMerchant(value);
                                const cards = form.getFieldValue('cards');
                                if (cards && cards.length > 0) {
                                    form.setFieldsValue({
                                        cards: cards.map((card: any) => ({
                                            ...card,
                                            allocationOperId: value,
                                        })),
                                    });
                                }
                            }}
                            style={{ width: '180px', marginLeft: '16px' }}
                        />
                    )}
                </div>
            </Space>

            <Form form={form}>
                <Form.List
                    name="cards"
                    initialValue={[{}]}
                    rules={[
                        {
                            validator: (_, cards) => {
                                if (cards.length === 0) {
                                    return Promise.reject(new Error('请至少填写一行'));
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    {(fields, { add, remove }) => (
                        <>
                            <Row style={{ marginBottom: 8, fontWeight: 'bold' }}>
                                <Space style={{ width: '31%' }}>
                                    <span style={{ color: 'red' }}>*</span>物理卡号
                                </Space>
                                <Space style={{ width: '31%' }}>
                                    <span style={{ color: 'red' }}>*</span>资产卡号
                                </Space>
                                <Space style={{ width: '30%' }}>分配商家</Space>
                                <Space style={{ width: '8%' }}></Space>
                            </Row>
                            {fields.map(({ key, name, ...restField }, fieldIndex) => (
                                <Space
                                    key={key}
                                    style={{ display: 'flex', marginBottom: 8 }}
                                    align="baseline"
                                >
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'physicsCardNo']}
                                        rules={[{ required: true, message: '请输入物理卡号' }]}
                                    >
                                        <Input placeholder="请填写" />
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'cardNo']}
                                        rules={[{ required: true, message: '请输入资产卡号' }]}
                                    >
                                        <Input
                                            placeholder="请填写"
                                            onChange={(e) => {
                                                try {
                                                    const value = e.target.value;
                                                    const cards = form.getFieldValue('cards');
                                                    if (
                                                        cards &&
                                                        cards.length > 0 &&
                                                        value &&
                                                        isInteger(value) &&
                                                        autoIncrement
                                                    ) {
                                                        form.setFieldsValue({
                                                            cards: cards.map(
                                                                (card: any, index: number) => {
                                                                    if (index <= fieldIndex) {
                                                                        return card;
                                                                    } else {
                                                                        return {
                                                                            ...card,
                                                                            cardNo: `${
                                                                                parseInt(value) +
                                                                                (index - fieldIndex)
                                                                            }`,
                                                                        };
                                                                    }
                                                                },
                                                            ),
                                                        });
                                                    }
                                                } catch (error) {}
                                            }}
                                        />
                                    </Form.Item>
                                    <Form.Item {...restField} name={[name, 'allocationOperId']}>
                                        <MainOperatorSelect style={{ width: '180px' }} />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                        onClick={() => remove(name)}
                                        style={{ color: '#ff7875' }}
                                    />
                                </Space>
                            ))}
                            <Form.Item>
                                <Button
                                    onClick={() => {
                                        try {
                                            console.debug('fields==>', fields);
                                            const cards = form.getFieldValue('cards');
                                            console.debug('cards==>', cards);
                                            const lastNo = cards?.[cards.length - 1]?.cardNo;
                                            add({
                                                allocationOperId: sameMerchant
                                                    ? selectedMerchant
                                                    : undefined,
                                                cardNo:
                                                    autoIncrement && isNumber(lastNo)
                                                        ? `${parseInt(lastNo || '0') + 1}`
                                                        : undefined,
                                            });
                                        } catch (error) {
                                            add({});
                                        }
                                    }}
                                    block
                                    icon={<PlusOutlined />}
                                >
                                    新增一行
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
                <Form.Item>
                    <Row justify="center">
                        <Space size="large">
                            <Button type="primary" onClick={handleOk} loading={loading}>
                                提交
                            </Button>
                            <Button onClick={handleCancel}>取消</Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(CreatePhysicalCardModal);
