import { useRequest } from 'ahooks';
import { Modal, Form, Button, Row, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import MainOperatorSelect from '@/components/FormItem/MainOperatorSelect';
import { allocationOperApi } from '@/services/Fleet/PhysicalCardApi';

const AllocateMerchantModal: React.ForwardRefRenderFunction<
    ActionRef.OpenModal,
    { callback?: (values?: any) => void }
> = ({ callback }, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState<boolean>(false);
    const [inventoryIds, setInventoryIds] = useState<(number | string)[]>([]);

    useImperativeHandle(ref, () => ({
        open(ids: (number | string)[]) {
            setInventoryIds(ids);
            setVisible(true);
        },
    }));

    const { run, loading } = useRequest(allocationOperApi, {
        manual: true,
        onSuccess: () => {
            callback?.();
            handleCancel();
        },
    });

    const handleOk = () => {
        form.validateFields().then((values) => {
            run({
                ...values,
                inventoryIds,
            });
        });
    };

    const handleCancel = () => {
        form.resetFields();
        setVisible(false);
    };

    return (
        <Modal
            title="分配商家"
            visible={visible}
            footer={null}
            onCancel={handleCancel}
            destroyOnClose
        >
            <Form form={form}>
                <Form.Item
                    name="allocationOperId"
                    label="充电商家："
                    rules={[{ required: true, message: '请选择充电商家' }]}
                >
                    <MainOperatorSelect />
                </Form.Item>
                <Form.Item>
                    <Row justify="center">
                        <Space size="large">
                            <Button type="primary" onClick={handleOk} loading={loading}>
                                提交
                            </Button>
                            <Button onClick={handleCancel}>取消</Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(AllocateMerchantModal);
