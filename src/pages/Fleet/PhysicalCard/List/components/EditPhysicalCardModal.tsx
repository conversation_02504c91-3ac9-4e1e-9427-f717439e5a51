import { useRequest } from 'ahooks';
import { Modal, Form, Input, Button, message, Row, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

import {
    updateCardInventoryApi,
    getCardInventoryDetailApi,
} from '@/services/Fleet/PhysicalCardApi';

const EditPhysicalCardModal: React.ForwardRefRenderFunction<
    ActionRef.OpenModal,
    { callback?: (values?: any) => void }
> = ({ callback }, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
        open(inventoryId: string) {
            form.resetFields();
            setVisible(true);
            // 调用查询接口获取电卡详情
            getCardInventoryDetailApi(inventoryId).then((response) => {
                if (response.ret === 200) {
                    form.setFieldsValue(response.data);
                } else {
                    message.error('获取电卡详情失败');
                    handleCancel();
                }
            });
        },
    }));

    const { run, loading } = useRequest(updateCardInventoryApi, {
        manual: true,
        onSuccess: () => {
            message.success('更新成功');
            callback?.();
            handleCancel();
        },
    });

    const handleOk = () => {
        form.validateFields().then((values) => {
            run({ ...values });
        });
    };

    const handleCancel = () => {
        form.resetFields();
        setVisible(false);
    };

    return (
        <Modal
            title="编辑电卡"
            visible={visible}
            footer={null}
            onCancel={handleCancel}
            destroyOnClose
        >
            <Form form={form} layout="horizontal">
                <Form.Item noStyle name="inventoryId">
                    <Input type="hidden" />
                </Form.Item>
                <Form.Item
                    name="physicsCardNo"
                    label="物理卡号"
                    rules={[{ required: true, message: '请输入物理卡号' }]}
                >
                    <Input placeholder="物理卡号" maxLength={100} />
                </Form.Item>
                <Form.Item
                    name="cardNo"
                    label="资产卡号"
                    rules={[{ required: true, message: '请输入资产卡号' }]}
                >
                    <Input placeholder="资产卡号" maxLength={100} />
                </Form.Item>
                <Form.Item>
                    <Row justify="center">
                        <Space size="large">
                            <Button type="primary" onClick={handleOk} loading={loading}>
                                提交
                            </Button>
                            <Button onClick={handleCancel} style={{ marginLeft: '8px' }}>
                                取消
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default forwardRef(EditPhysicalCardModal);
