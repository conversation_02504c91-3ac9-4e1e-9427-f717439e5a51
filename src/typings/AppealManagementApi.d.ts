declare namespace API {
    /**
     * 申诉列表项
     */
    interface AppealListItem {
        id: string;
        appealNo: string;
        licensePlateNumber: '粤A88888',
        userPhone: string;
        merchantId: string;
        appealType: string;
        appealTypeName: string;
        appealStatus: string;
        appealStatusName: string;
        priority: string;
        priorityName: string;
        appealTitle: string;
        appealContent: string;
        appealAmount?: number;
        orderNo?: string;
        stationId?: string;
        stationName?: string;
        operatorName?: string;
        cityName?: string;
        appealTime: string;
        handleTime?: string;
        handlePerson?: string;
        handleResult?: string;
        handleRemark?: string;
        deadlineTime?: string;
        attachments?: AppealAttachment[];
        createTime: string;
        updateTime: string;
    }

    /**
     * 申诉详情
     */
    interface AppealDetail extends AppealListItem {
        userInfo?: {
            userId: string;
            userName: string;
            userPhone: string;
            userEmail?: string;
        };
        orderInfo?: {
            orderNo: string;
            orderAmount: number;
            orderTime: string;
            orderStatus: string;
        };
        stationInfo?: {
            stationId: string;
            stationName: string;
            stationAddress: string;
            operatorName: string;
        };
        handleHistory?: AppealHandleHistory[];
        replyHistory?: AppealReply[];
    }

    /**
     * 申诉附件
     */
    interface AppealAttachment {
        id: string;
        fileName: string;
        fileUrl: string;
        fileType: string;
        fileSize: number;
        uploadTime: string;
    }

    /**
     * 申诉处理历史
     */
    interface AppealHandleHistory {
        id: string;
        appealNo: string;
        operationType: string;
        licensePlateNumber: string;
        operationTypeName: string;
        operationContent: string;
        operatorId: string;
        operatorName: string;
        operationTime: string;
        remark?: string;
    }

    /**
     * 申诉回复
     */
    interface AppealReply {
        id: string;
        appealNo: string;
        replyType: string; // 01-用户回复 02-客服回复
        replyContent: string;
        replyPerson: string;
        replyTime: string;
        attachments?: AppealAttachment[];
    }

    /**
     * 申诉统计
     */
    interface AppealStatistics {
        totalCount: number;
        pendingCount: number;
        processingCount: number;
        processedCount: number;
        rejectedCount: number;
        closedCount: number;
        todayNewCount: number;
        todayProcessedCount: number;
        avgProcessTime: number; // 平均处理时长（小时）
        timeoutCount: number; // 超时数量
    }
}
