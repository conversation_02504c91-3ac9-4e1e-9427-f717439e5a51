/*global PaginationRequest PaginationResponse CommonRequest CommonResponse Moment */
declare namespace API {
    interface GetApproveListRequest extends PaginationRequest {
        //审批ID
        approveId?: string;
        //审批结束时间
        approvedByEndDate?: string;
        //审批开始时间
        approvedByStartDate?: string;
        //运营商
        operatorId?: string;
        //操作类型:1=上线,2=下线,3=废弃,4=恢复,5=显示,6=隐藏
        operatorType?: string;
        //发起人
        promoter?: string;
        //发起结束时间
        promoterEndDate?: string;
        //发起开始时间
        promoterStartDate?: string;
        //站点ID
        stationId?: string;
        //状态:1=审批中,2=已通过,3=未通过
        status?: string;
        /********前端表单使用数据*********/
        promoterDates?: [Moment, Moment];
        approvedDates?: [Moment, Moment];
    }

    interface ApproveVo {
        //审批时间
        approvedByTime?: string;
        id?: number;
        //操作明细
        operateDesc?: string;
        //操作编号
        operateReason?: string;
        //操作类型:1=上线,2=下线,3=废弃,4=恢复,5=显示,6=隐藏
        operateType?: string;
        //操作类型描述
        operateTypeName?: string;
        //运营商编号
        operatorId?: string;
        //运营商名称
        operatorName?: string;
        //发起人工号
        promoter?: string;
        //发起人姓名
        promoterName?: string;
        //站点明细
        stationList?: {
            //审批单个信息明细参数
            //城市
            city?: string;
            //城市名称
            cityName?: string;
            //自增数据
            id?: number;
            //运营商编号?:
            operatorId?: string;
            //站点ID
            stationId?: number;
            //站点名称
            stationName?: string;
        }[];
        //发起时间
        promoterDate?: string;
        //站点个数
        stationNum?: string;
        //状态:1=审批中,2=已通过,3=未通过
        status?: string;
        //状态描述
        statusName?: string;
        // 审批进度
        url?: string;
    }

    type GetApproveListResponse = PaginationResponse<ApproveVo>;

    interface StationManageParams {
        //操作原因说明
        operateDesc?: string;
        //操作原因
        operateReason?: string;
        //站点ID:多个都好隔开
        stationIds?: string;
    }

    interface OperatorsProfitruleDto {
        //商户标识
        buildId?: string;
        //商户名称
        buildName?: string;
        //城市名称
        cityName?: string;
        //合作类型 01-抽成模式 02-购电模式 03-混合模式
        cooperationType?: string;
        //合作类型
        cooperationTypeName?: string;
        //是否有配置
        hasRule?: boolean;
        //主键列
        ruleId?: number;
        //分润规则备注
        ruleName?: string;
        //站点标识
        stationId?: number;
        //站点名称
        stationName?: string;
        //分润模板id
        templateId?: number;
    }

    type ValidProfitRuleResponse = CommonResponse<OperatorsProfitruleDto[]>;

    type ValidStationPriceResponse = CommonResponse<
        {
            //站点id
            stationId?: number;
            //站点名称
            stationName?: string;
        }[]
    >;

    interface SyncStationParams {
        //城市编码
        city: string;
        //运营商id
        operatorId: string;
        synOperator?: string;
        synOperatorName?: string;
    }

    interface SyncRecordParams extends PaginationRequest {
        //审批ID
        keyword?: string;
    }

    interface ClueStaticVo {
        accessCompletedNum?: number; //	接入完成数	integer
        assertBelong?: string; //资产归属运营商	string
        businessExpansionNum?: number; //商务拓展数	integer
        clueNum?: number; //线索总数	integer
        contractSignNum?: number; //合同签订数	integer
        expansionHinderedNum?: number; //拓展受阻数	integer
        followedUpNum?: number; //可跟进数	integer
        newTodayNum?: number; //今日新增	integer
        notFollowedUpNum?: number; //不可跟进数	integer
        notYetContactedNum?: number; //尚未联系数	integer
        technicalDockNum?: number; //技术对接数	integer
        unDealNum?: number; //未处理数	integer
    }

    interface ClueManageVo {
        assertBelong?: string; //资产归属运营商	string
        billdate?: string; //统计日期	string
        bindSite?: number; //绑定场站	integer
        bindSiteName?: string; //绑定场站名称	string
        chargegunAll?: number; //总枪数	integer
        chargegunOth?: number; //其他枪数	integer
        chargegunQui?: number; //快充枪数	integer
        chargegunSlo?: number; //慢充枪数	integer
        chargegunSup?: number; //超充枪数	integer
        chargegunSupqui?: number; //超快充枪数	integer
        city?: string; //城市	string
        clueSource?: string; //线索来源	string
        clueState?: string; //接入状态	string
        clueType?: string; //线索类型	string
        cpoName?: string; //CPO/子公司	string
        createTime?: string; //创建时间	string
        dealtime?: string; //最后处理时间	string
        distributeState?: string; //分发状态	string
        followDepart?: string; //跟进部门	string
        followDepartId?: string; //跟进部门ID	string
        followPerson?: string; //跟进人 多个、隔开	string
        id?: number; //ID，自增加	integer
        lastDealer?: string; //最后处理人	string
        lat?: number; //纬度	integer
        lon?: number; //经度	integer
        newFlag?: string; //标识新	string
        province?: string; //省份	string
        situationDescp?: string; //情况描述	string
        stationAcdc?: string; //站快慢类型	string
        stationAddress?: string; //站点地址	string
        stationName?: string; //站点名	string
        stationUid?: string; //场站uid	string
        updateTime?: string; //更新时间	string
    }

    interface DealClueParams {
        bindSite?: number; //	绑定场站

        bindSiteName?: string; //绑定场站名称

        clueState?: string; //接入状态

        clueType?: string; //线索类型

        idList?: string[]; //ID，自增加

        situationDescp?: string; //情况描述
    }

    interface DistributeClueParams {
        followDepart?: string;
        followDepartId?: string;
        followPerson?: string;
        idList?: string[]; //ID，自增加
    }

    interface OperGuideInfoVo {
        channel?: string; //	渠道
        channelList?: string[]; //	渠道	array	string
        channelNameList?: string[]; //	渠道	array	string
        cityList?: string[]; //	城市	array	string
        cityNameList?: string[]; //	运营商信息	array	string
        configId?: string; //		integer(int64)
        configName?: string; //	配置名称	string
        configStatus?: number | boolean; //	状态	integer(int32)
        configStatusName?: string; //	状态	string
        createTime?: string; //	创建时间	string
        createdBy?: string; //	创建人	string
        guideDesc?: string; //	引导文案	string
        guideImgOssUrl?: string; //	引导图片	string
        guideImgUrl?: string; //	引导图片	string
        operIdList?: string[]; //	运营商	array	string
        operatorVoList?: { batchId?: string; buildName?: string; operId?: string }[]; //	运营商信息	array	OperatorVo

        updateBy?: string; //	更新人	string
        updateTime?: string; //	更新时间	string
    }

    interface OperWhitelistParams extends PaginationRequest {
        operIds?: string;
        operWhitelistId?: string;
        stationIds?: string;
    }

    interface OperWhitelistStationInfoVo {
        city?: string; // 操作白名单ID
        stationIdList?: string[]; // 站点ID
    }
    interface OperWhitelistVo {
        createTime?: string; // 创建时间
        creator?: string; // 创建者
        operId?: string; // 操作ID
        operName?: string; // 操作名称
        operWhitelistId?: number; // 操作白名单ID
        scopeList?: OperWhitelistStationInfoVo[]; // 站点信息列表
        updateTime?: string; // 更新时间
    }

    type StopChargeWhitelistResponse = PaginationResponse<OperWhitelistVo>;
    type StopChargeWhitelistDetailResponse = CommonResponse<OperWhitelistVo>;

    /**
     * 站点管理-停充白名单
     */
    interface PlugChargeListParams extends PaginationRequest {
        ruleBelong?: string;
        plugAndChargeFlag?: string;
    }

    interface PlugChargeVo {
        // 创建者
        createdBy?: string;
        // 插电即充标志
        plugAndChargeFlag?: number;
        // 优先级
        priority?: number;
        // 规则归属
        ruleBelong?: string;
        // 规则归属名称
        ruleBelongName?: string;
        // 规则ID
        ruleId?: number;
        // 规则类型
        ruleType?: string;
        // 规则类型名称
        ruleTypeName?: string;
        // 站点范围
        stationScope?: string;
        // VIN规则列表
        vinRules?: any[];
    }

    type PlugChargeListResponse = PaginationResponse<PlugChargeVo>;
    type plugChargeDetailResponse = CommonResponse<PlugChargeVo>;
}
