import { Table, Button, Typography, Popconfirm, Input, Form, Space } from 'antd';
import { renderTableDataIndexText, isEmpty, copyObjectCommon } from '@/utils/utils';
import type { ReactNode } from 'react';
import { FormOutlined, EditOutlined } from '@ant-design/icons';

import React, { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react';

const EditCellItem = (
    props: API.CommonFormItem<any> & { children: any; render?: (record: any) => ReactNode },
    ref: any,
) => {
    const { value, onChange, disabled, children, render } = props;

    const [isEdit, toggleIsEdit] = useState(false);

    useImperativeHandle(ref, () => {
        return {};
    });
    const childProps = {
        ...props,
        style: {
            width: '100%',
        },
    };
    delete childProps.children;
    return (
        <>
            {isEdit ? (
                React.cloneElement(children, childProps)
            ) : (
                <Space>
                    {(render && render(value)) || value}
                    <Typography.Link>
                        <EditOutlined
                            className="table-btn"
                            onClick={() => {
                                toggleIsEdit(!isEdit);
                            }}
                        />
                    </Typography.Link>
                </Space>
            )}
        </>
    );
};

export default forwardRef(EditCellItem);
