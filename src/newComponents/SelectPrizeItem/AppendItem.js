import { Fragment, useState } from 'react';
import { Modal, Button, Space, InputNumber, Form, message } from 'antd';
import commonStyles from '@/assets/styles/common.less';
import { isEmpty } from '@/utils/utils';
import { appendGiftApi } from '@/services/Marketing/MarketingNewCouponApi';
import { COUPON_CUSTOM_PAGE_TYPE } from './SelectCouponItem';
const FormItem = Form.Item;

const AppendItem = (props) => {
    const {
        value,
        onChange,
        reload,
        disabled,
        canAppend = false,
        maxCount,
        actId,
        actSubId,
        appendExtParams,
        pageType,
        editOnBlur,
    } = props;

    const [showAppendModal, toggleAppendModal] = useState(false);
    const [appendLoading, updateAppendLoading] = useState(false);

    const [appendForm] = Form.useForm();

    const openAppendModal = () => {
        toggleAppendModal(true);
    };
    const closeAppendModal = () => {
        toggleAppendModal(false);
    };
    const onFinish = async (values) => {
        try {
            if (appendLoading) {
                return;
            }
            let params = {
                actId,
                ...(appendExtParams || {}),
                stockParamList: [
                    {
                        actSubId,
                        ...(appendExtParams?.stockParamList || {}),
                        ...values,
                    },
                ],
            };
            updateAppendLoading(true);
            await appendGiftApi(params);
            message.success('追加成功');
            reload && reload();
            closeAppendModal();
            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateAppendLoading(false);
        }
    };

    return (
        <Fragment>
            <Space>
                <InputNumber
                    value={value}
                    addonAfter={'份'}
                    precision={0}
                    step={1}
                    min={pageType == COUPON_CUSTOM_PAGE_TYPE.SECKILL ? -1 : 0}
                    placeholder="请填写"
                    onChange={onChange}
                    disabled={disabled}
                    onBlur={editOnBlur}
                ></InputNumber>
                {(canAppend && (
                    <span className={commonStyles['table-btn']} onClick={openAppendModal}>
                        追加库存
                    </span>
                )) ||
                    (pageType == COUPON_CUSTOM_PAGE_TYPE.SECKILL && <span>（-1代表无限制）</span>)}
            </Space>
            <Modal
                title="追加库存"
                width={500}
                visible={showAppendModal}
                onCancel={closeAppendModal}
                footer={null}
                maskClosable={false}
                destroyOnClose
            >
                <Form name="append-modal" form={appendForm} onFinish={onFinish} scrollToFirstError>
                    <FormItem label="可追加量">{!isEmpty(maxCount) ? maxCount : '无限制'}</FormItem>
                    <FormItem
                        label="追加数量"
                        name="appendStockNum"
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject(`请填写`);
                                    }
                                    if (!(Number(value) > 0)) {
                                        return Promise.reject(`值必须为大于0正整数`);
                                    }
                                    if (!isEmpty(maxCount) && Number(value) > maxCount) {
                                        return Promise.reject(`值必须不能大于库存数${maxCount}`);
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            addonAfter={'份'}
                            precision={0}
                            step={1}
                            min={0}
                            placeholder="请填写"
                        ></InputNumber>
                    </FormItem>
                    <div className={commonStyles['btn-bar']} style={{ textAlign: 'center' }}>
                        <Button
                            className={commonStyles['btn-item']}
                            type="primary"
                            htmlType="submit"
                            loading={appendLoading}
                        >
                            提交
                        </Button>

                        <Button className={commonStyles['btn-item']} onClick={closeAppendModal}>
                            取消
                        </Button>
                    </div>
                </Form>
            </Modal>
        </Fragment>
    );
};

export default AppendItem;
