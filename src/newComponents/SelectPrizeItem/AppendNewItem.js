import React, { useState, useImperativeHandle } from 'react';
import { Form, message, InputNumber, Modal } from 'antd';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { appendGiftApi } from '@/services/Marketing/MarketingNewCouponApi';

const FormItem = Form.Item;

// 2.0优惠券追加
const AppendNewItem = (props) => {
    const { initRef, onFinish } = props;

    const [showAppendModal, toggleAppendModal] = useState(false);
    const [appendLoading, updateAppendLoading] = useState(false);

    const [originItem, updateOriginItem] = useState(undefined); // 原始库存数，用于限制最小可输入库存数
    const [appendForm] = Form.useForm();

    const show = (currentItem) => {
        toggleAppendModal(true);
        updateOriginItem(currentItem);
        appendForm.setFieldsValue({ appendStockNum: currentItem.stockNum });
    };
    const close = () => {
        toggleAppendModal(false);
    };

    useImperativeHandle(initRef, () => ({
        show,
    }));

    return (
        <Modal
            title="追加库存"
            width={500}
            visible={showAppendModal}
            onCancel={close}
            maskClosable={false}
            destroyOnClose
            onOk={() => {
                appendForm.validateFields().then(async (values) => {
                    try {
                        let params = {
                            actId: originItem?.actId,
                            actType: originItem?.actType,
                            stockParamList: [
                                {
                                    actSubId: originItem?.actSubId,
                                    giftBagId: originItem?.giftbagId,
                                    ...values,
                                },
                            ],
                        };
                        updateAppendLoading(true);
                        await appendGiftApi(params);
                        message.success('追加成功');
                        onFinish?.();
                        close();
                    } catch (error) {
                    } finally {
                        updateAppendLoading(false);
                    }
                });
            }}
            okButtonProps={{ loading: appendLoading }}
        >
            <Form form={appendForm} scrollToFirstError>
                <FormItem
                    label="追加数量"
                    name="appendStockNum"
                    required
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请填写`);
                                }
                                if (!(Number(value) > 0)) {
                                    return Promise.reject(`值必须为大于0正整数`);
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <InputNumber
                        addonAfter={'份'}
                        precision={0}
                        step={1}
                        min={originItem?.stockNum || 0}
                        placeholder="请填写"
                    ></InputNumber>
                </FormItem>
            </Form>
        </Modal>
    );
};

export default AppendNewItem;
