import { Fragment, useMemo, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { Form, Select, Radio, InputNumber, Space, Modal, Button } from 'antd';
import CouponTable, { REPLACE_TYPE } from '@/components/CouponComponents/CouponTable';
import SelectCouponItem, { CouponComponents } from './SelectCouponItem';
import { PRIZE_TYPES, CHARGE_COUPON_TYPES } from '@/config/declare';
import { isEmpty } from '@/utils/utils';
import AppendItem from './AppendItem';
import SelectGiftItem from './SelectGiftItem';
import GiftPrizeItem from './GiftPrizeItem';

const FormItem = Form.Item;
const { Option } = Select;
const SelectPrizeItem = (props, ref) => {
    const {
        parentName = [],
        fullParentName = [],
        listName,
        disabled,
        canAppend, //是否可追加
        canRecommend, // 是否可推荐
        appendExtParams, // 追加操作附加的额外字段，可嵌套为{xx:xx, stockParamList: {xx: xx}}
        otherPrizeStatistic = [], //统计其他list里配的礼包，计算出当前券里可配置库存
        initStockNum = 0, // 如果库存可编辑，需要传初始化的库存值，用于提交时，计算差值进行最多可配库存数的校验

        /*******老优惠券需要入参*******/
        actId, // 进行中的礼包列表需要传活动id，用于接口判断是否是当前活动用掉的库存，修正可用库存数值
        actSubId,
        actState,
        onRefresh,
        onChangeType, //用户类型切换时父级手动还原数据
        disabledIds,
        putCpnList = [],
        title,
        extInParams = {},
        dispatch,
        couponModel,
        /****************************/

        newCouponModel,
        global,
        form,
        formItemLayout = { wrapperCol: { span: 24 } },
        pageType = undefined, //区分各个活动配置页面用于定制化，目前优惠券2.0只需要做新人礼包的个性化
        isCopy,
        ...otherProps
    } = props;

    useImperativeHandle(ref, () => {
        return {};
    });

    const giftBagType = Form.useWatch([...fullParentName, 'giftBagType'], form);
    const detPrizeList = Form.useWatch([...fullParentName, 'detPrizeList'], form);
    const giftBagPutNum = Form.useWatch([...fullParentName, 'giftBagPutNum'], form);
    const stockNum = Form.useWatch([...fullParentName, 'stockNum'], form);
    const giftBagId = Form.useWatch([...fullParentName, 'giftBagId'], form);

    const couponRef = useRef();

    const giftRef = useRef();
    const giftPrizeRef = useRef();

    const endNum = useMemo(() => {
        let count = undefined;
        if (!isCopy && !isEmpty(stockNum) && !isEmpty(giftBagPutNum)) {
            count = Math.floor(Number(stockNum) - Number(giftBagPutNum));
        }

        return count;
    }, [giftBagPutNum, stockNum, isCopy]);

    const prizeRender = useMemo(() => {
        let maxCount;
        if (detPrizeList instanceof Array) {
            //计算库存最多追加上限
            for (const item of detPrizeList) {
                const {
                    stockUpLimit,
                    stockLimitNum,
                    totalUseStockNum = 0,
                    totalNoSelfUseStockNum = 0,
                    totalSelfUseStockNum = 0,
                    singlePutNum,
                    prizeId,
                } = item;
                if (stockUpLimit && stockUpLimit !== 'false') {
                    let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    if (!isCopy && !disabled) {
                        diff += Number(totalSelfUseStockNum);

                        //追加计算不同任务配置相同礼品时的数量

                        let otherPrizeSinglePutNum = 0;
                        otherPrizeStatistic.forEach((element) => {
                            if (element.prizeId === prizeId) {
                                otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                            }
                        });

                        if (otherPrizeSinglePutNum > 0) {
                            diff = diff - otherPrizeSinglePutNum;
                        }

                        // const otherPrizeItem = otherPrizeStatistic.find(
                        //     (ele) => ele.prizeId === prizeId,
                        // );
                        // if (otherPrizeItem) {
                        //     diff = diff - Number(otherPrizeItem.singlePutNum);
                        // }
                    }

                    const itemMax = Math.floor((diff > 0 ? diff : 0) / Number(singlePutNum));
                    //取数组每个优惠券可用数量除去单人发券数的最小值

                    if (isEmpty(maxCount) || itemMax < maxCount) {
                        maxCount = itemMax;
                    }
                }
            }
        }

        if (giftBagType === PRIZE_TYPES.COUPON) {
            return (
                <CouponTable
                    actId={!isCopy && actId}
                    dispatch={dispatch}
                    global={global}
                    couponModel={couponModel}
                    form={form}
                    needCheck
                    actState={!isCopy && actState}
                    editabled={isCopy || !disabled}
                    putCpnList={putCpnList}
                    title={title}
                    name={listName}
                    filePath={fullParentName}
                    onRefresh={onRefresh}
                    extInParams={extInParams}
                    disabledIds={disabledIds}
                    {...otherProps}
                />
            );
        } else if (giftBagType === PRIZE_TYPES.NEW_COUPON) {
            return (
                <Fragment>
                    <FormItem
                        label="选择优惠券"
                        name={[...parentName, listName]}
                        rules={[
                            // { required: true, message: '请选择优惠券' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (isEmpty(value)) {
                                        return Promise.reject(`请选择优惠券`);
                                    }
                                    let errList = [];
                                    for (const item of value) {
                                        if (isEmpty(item.singlePutNum)) {
                                            errList.push(item.prizeName);
                                        }
                                    }
                                    if (errList.length > 0) {
                                        return Promise.reject(
                                            `请配置${errList.join('、')}的单人发放数量`,
                                        );
                                    }

                                    if (canRecommend) {
                                        const chargeType = value.find(
                                            (ele) => ele.prizeClass == '1',
                                        );
                                        if (!chargeType) {
                                            return Promise.reject('请至少包含一张充电券');
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required
                        {...formItemLayout}
                    >
                        <SelectCouponItem
                            ref={couponRef}
                            global={global}
                            dispatch={dispatch}
                            extInParams={extInParams}
                            disabled={!isCopy && disabled}
                            otherPrizeStatistic={otherPrizeStatistic}
                            pageType={pageType}
                            form={form}
                            fullParentName={fullParentName}
                            isCopy={isCopy}
                            canRecommend={canRecommend}
                        ></SelectCouponItem>
                    </FormItem>
                    <FormItem label="活动库存" required {...formItemLayout}>
                        <Space direction="vertical">
                            <FormItem
                                noStyle
                                name={[...parentName, 'stockNum']}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (isEmpty(value)) {
                                                return Promise.reject(`请填写`);
                                            }
                                            if (!(Number(value) > 0)) {
                                                return Promise.reject(`值必须为大于0正整数`);
                                            }
                                            if (
                                                (isCopy || !canAppend) &&
                                                !isEmpty(maxCount) &&
                                                Number(value) - initStockNum > Number(maxCount)
                                            ) {
                                                return Promise.reject(
                                                    `最多可配库存数${
                                                        Number(maxCount) + initStockNum
                                                    }`,
                                                );
                                            }

                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <AppendItem
                                    disabled={!isCopy && disabled}
                                    canAppend={!isCopy && canAppend}
                                    actId={!isCopy && actId}
                                    actSubId={actSubId}
                                    reload={onRefresh}
                                    maxCount={maxCount}
                                    appendExtParams={appendExtParams}
                                ></AppendItem>
                            </FormItem>
                            <FormItem noStyle name={[...parentName, 'giftBagPutNum']}>
                                {!isEmpty(endNum) ? `剩余库存:${endNum}份` : ''}
                            </FormItem>
                        </Space>
                    </FormItem>
                </Fragment>
            );
        } else if (giftBagType === PRIZE_TYPES.GIFT_BAG) {
            const giftMax = giftPrizeRef.current?.getMaxCount();

            return (
                <Fragment>
                    <FormItem label="选择礼包" required {...formItemLayout}>
                        {/* 礼包选择和奖品展示分开 并联动id变化联动 */}
                        <FormItem
                            noStyle
                            name={[...parentName, 'giftBagId']}
                            rules={[{ required: true, message: '请选择礼包' }]}
                        >
                            <SelectGiftItem
                                ref={giftRef}
                                disabled={!isCopy && disabled}
                                otherPrizeStatistic={otherPrizeStatistic}
                                onChange={(newGagId) => {
                                    if (isEmpty(newGagId)) {
                                        giftPrizeRef.current?.clear();
                                    } else {
                                        const giftBagId = form.getFieldValue([
                                            ...parentName,
                                            'giftBagId',
                                        ]);
                                        if (giftBagId != newGagId) {
                                            form.setFieldsValue({
                                                recommendCycle: undefined,
                                                recommendPrizeId: undefined,
                                            });
                                        }
                                    }
                                }}
                            ></SelectGiftItem>
                        </FormItem>

                        <FormItem
                            name={[...parentName, listName]}
                            noStyle
                            rules={[
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (canRecommend) {
                                            const chargeType = value.find(
                                                (ele) => ele.prizeClass == '1',
                                            );
                                            if (!chargeType) {
                                                return Promise.reject('请至少包含一张充电券');
                                            }
                                        }
                                        return Promise.resolve();
                                    },
                                }),
                            ]}
                        >
                            <GiftPrizeItem
                                actId={!disabled && actId}
                                ref={giftPrizeRef}
                                giftbagId={giftBagId}
                                otherPrizeStatistic={otherPrizeStatistic}
                                disabled={!isCopy && disabled}
                                form={form}
                                fullParentName={fullParentName}
                                pageType={pageType}
                                isCopy={isCopy}
                                canRecommend={canRecommend}
                            ></GiftPrizeItem>
                        </FormItem>
                    </FormItem>

                    <FormItem label="活动库存" required {...formItemLayout}>
                        <Space direction="vertical">
                            <FormItem
                                name={[...parentName, 'stockNum']}
                                rules={[
                                    ({ getFieldValue }) => ({
                                        validator(rule, value) {
                                            if (isEmpty(value)) {
                                                return Promise.reject(`请填写`);
                                            }
                                            if (!(Number(value) > 0)) {
                                                return Promise.reject(`值必须为大于0正整数`);
                                            }
                                            if (
                                                (isCopy || !canAppend) &&
                                                !isEmpty(giftMax) &&
                                                Number(value) - initStockNum > Number(giftMax)
                                            ) {
                                                return Promise.reject(
                                                    `最多可配库存数${
                                                        Number(giftMax) + initStockNum
                                                    }`,
                                                );
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                                noStyle
                            >
                                <AppendItem
                                    actId={!isCopy && actId}
                                    actSubId={actSubId}
                                    disabled={!isCopy && disabled}
                                    canAppend={!isCopy && canAppend}
                                    reload={onRefresh}
                                    maxCount={giftMax}
                                    appendExtParams={appendExtParams}
                                ></AppendItem>
                            </FormItem>
                            <FormItem noStyle name={[...parentName, 'giftBagPutNum']}>
                                {!isEmpty(endNum) ? `剩余库存:${endNum}份` : ''}
                            </FormItem>
                        </Space>
                    </FormItem>
                </Fragment>
            );
        }
    });

    const itemName = useMemo(() => {
        let levelIndex = parentName instanceof Array ? parentName.length : 0;
        return fullParentName.slice(0, fullParentName.length - levelIndex);
    }, [fullParentName, parentName]);

    return (
        <Fragment>
            <FormItem noStyle shouldUpdate={(pre, after) => true}>
                {({ getFieldValue }) => {
                    const couponType = getFieldValue('couponType');

                    let defaultValue = PRIZE_TYPES.NEW_COUPON;

                    let radioList = [];

                    if (CHARGE_COUPON_TYPES.OLD == couponType) {
                        radioList.push(<Radio value={PRIZE_TYPES.COUPON}>优惠券</Radio>);
                        defaultValue = PRIZE_TYPES.COUPON;
                    } else {
                        radioList.push(<Radio value={PRIZE_TYPES.NEW_COUPON}>优惠券</Radio>);
                        radioList.push(<Radio value={PRIZE_TYPES.GIFT_BAG}>礼包</Radio>);
                    }

                    return (
                        <FormItem
                            label="礼品类型"
                            name={[...parentName, 'giftBagType']}
                            rules={[{ required: true, message: '请选择奖励类型' }]}
                            initialValue={defaultValue}
                            {...formItemLayout}
                        >
                            <Radio.Group
                                placeholder="请选择"
                                disabled={!isCopy && disabled}
                                onChange={(event) => {
                                    const {
                                        target: { value: newValue },
                                    } = event;
                                    onChangeType && onChangeType(newValue);

                                    // const itemInfo = form.getFieldValue(itemName);

                                    // // setFormValues(itemInfo);
                                    // debugger;

                                    //切换礼包和优惠券时互相清除原来配置的数据
                                    if (newValue === PRIZE_TYPES.GIFT_BAG) {
                                        couponRef.current?.clear();
                                    } else if (newValue === PRIZE_TYPES.NEW_COUPON) {
                                        giftRef.current?.clear();
                                    }
                                    form.setFieldsValue({
                                        recommendCycle: undefined,
                                        recommendPrizeId: undefined,
                                    });
                                }}
                            >
                                {radioList}
                            </Radio.Group>
                        </FormItem>
                    );
                }}
            </FormItem>
            {prizeRender}
            {(canRecommend && <FormItem name="recommendCycle" noStyle />) || null}
            {(canRecommend && <FormItem name="recommendPrizeId" noStyle />) || null}
        </Fragment>
    );
};
export default forwardRef(SelectPrizeItem);
