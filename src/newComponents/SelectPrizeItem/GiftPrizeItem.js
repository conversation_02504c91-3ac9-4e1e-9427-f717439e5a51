import React, {
    useState,
    useEffect,
    useMemo,
    useRef,
    useContext,
    useImperativeHandle,
    forwardRef,
    Fragment,
} from 'react';
import { Form, Space, Select, Popconfirm } from 'antd';
import TablePro from '@/components/TablePro';
import commonStyles from '@/assets/styles/common.less';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { getGiftListApi, getGiftInfoApi } from '@/services/Marketing/MarketingNewCouponApi';
import RecommandModal, { openChannelOptionItems } from './RecommandModal';
import { Link } from 'umi';
import { GIFT_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const { Option } = Select;

const GiftPrizeItem = (props, ref) => {
    const {
        giftbagId,
        value,
        onChange,
        disabled,
        putColDataIndex = 'singlePutNum',
        otherPrizeStatistic = [],
        form,
        pageType = undefined,
        fullParentName,
        isCopy,
        actId,
        canRecommend,
        ...otherProps
    } = props;
    const [resultList, updateResultList] = useState([]);

    // 仅新人礼包使用的属性，上一次选中的推荐周期+推荐奖品id *业务页面赋值
    const recommendCycle = Form.useWatch('recommendCycle', form);
    const recommendPrizeId = Form.useWatch('recommendPrizeId', form);
    const recommendRef = useRef();

    useImperativeHandle(ref, () => {
        return {
            clear: () => {
                onClearEvent();
            },

            getMaxCount: () => {
                let maxCount;
                if (resultList instanceof Array) {
                    //计算库存最多追加上限
                    for (const item of resultList) {
                        const {
                            stockUpLimit,
                            stockLimitNum,
                            totalUseStockNum = 0,
                            totalNoSelfUseStockNum = 0,
                            totalSelfUseStockNum = 0,
                            singlePutNum,
                            prizeId,
                        } = item;
                        if (stockUpLimit && stockUpLimit !== 'false') {
                            let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                            if (!isCopy && !disabled) {
                                diff += Number(totalSelfUseStockNum);

                                let otherPrizeSinglePutNum = 0;
                                otherPrizeStatistic.forEach((element) => {
                                    if (element.prizeId === prizeId) {
                                        otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                                    }
                                });

                                if (otherPrizeSinglePutNum > 0) {
                                    diff = diff - otherPrizeSinglePutNum;
                                }

                                // const otherPrizeItem = otherPrizeStatistic.find(
                                //     (ele) => ele.prizeId === prizeId,
                                // );
                                // if (otherPrizeItem) {
                                //     diff = diff - Number(otherPrizeItem.singlePutNum);
                                // }
                            }

                            const itemMax = Math.floor(
                                (diff > 0 ? diff : 0) / Number(singlePutNum),
                            );
                            //取数组每个优惠券可用数量除去单人发券数的最小值

                            if (isEmpty(maxCount) || itemMax < maxCount) {
                                maxCount = itemMax;
                            }
                        }
                    }
                }
                return maxCount;
            },
            getPrizeList: () => {
                return resultList;
            },
        };
    });

    useEffect(() => {
        if (giftbagId) {
            changeSelectEvent(giftbagId);
        }
    }, [giftbagId]);

    const changeSelectEvent = async (newValues) => {
        try {
            if (isEmpty(newValues)) {
                return;
            }
            updateResultList([]);
            const {
                data: { prizeGiftQueryVoList },
            } = await getGiftInfoApi(newValues, actId);
            updateResultList(prizeGiftQueryVoList);
            updateFormInfo(prizeGiftQueryVoList);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const updateFormInfo = (newValues) => {
        onChange && onChange(newValues);
    };

    const newUserColumns = [
        {
            title: '序号',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '礼品名称',
            dataIndex: 'prizeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '礼品类型',
            dataIndex: 'giftTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '单人发放量',
            width: 120,
            dataIndex: 'singlePutNum',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠额度',
            dataIndex: 'dctValueName',
            width: 160,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '抵扣类型',
            dataIndex: 'deductionTypeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用库存',
            dataIndex: 'stockUpLimit',
            width: 120,
            render(text, record) {
                let count = 0;
                const {
                    stockUpLimit,
                    stockLimitNum,
                    totalUseStockNum = 0,
                    totalNoSelfUseStockNum = 0,
                    totalSelfUseStockNum = 0,
                    prizeId,
                } = record;
                if (stockUpLimit && stockUpLimit !== 'false') {
                    let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    if (!isCopy && !disabled) {
                        diff += Number(totalSelfUseStockNum);

                        let otherPrizeSinglePutNum = 0;
                        otherPrizeStatistic.forEach((element) => {
                            if (element.prizeId === prizeId) {
                                otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                            }
                        });

                        if (otherPrizeSinglePutNum > 0) {
                            diff = diff - otherPrizeSinglePutNum;
                        }

                        // const otherPrizeItem = otherPrizeStatistic.find(
                        //     (ele) => ele.prizeId === prizeId,
                        // );
                        // if (otherPrizeItem) {
                        //     diff = diff - Number(otherPrizeItem.singlePutNum);
                        // }
                    }

                    count = diff > 0 ? diff : 0;
                    return <span title={count}>{count}</span>;
                } else {
                    return <span title={text}>{'-'}</span>;
                }
            },
        },
        {
            title: '用户属性',
            dataIndex: 'userTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用范围',
            dataIndex: 'scopeTypeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '有效期',
            dataIndex: 'expirationDesc',
            width: 200,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },

        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];

                let lookPath = '';
                switch (record.giftType) {
                    case GIFT_TYPES.COUPON:
                        lookPath = `/marketing/couponCenter/cpnManage/list/look/${record.prizeId}`;
                        break;
                    case GIFT_TYPES.RIGHT:
                        lookPath = `/marketing/weekcard/manage/list/plan/look/${record.prizeId}`;
                        break;
                    case GIFT_TYPES.EXP_MEMBER:
                        lookPath = `/userCenter/membership/plan/list/detail-vip/${record.prizeId}`;
                        break;

                    default:
                        break;
                }
                const lookBtn = (
                    <Link to={lookPath} target="_blank">
                        查看
                    </Link>
                );
                const recommend = (
                    <span
                        className={commonStyles['table-btn']}
                        onClick={() => {
                            recommendRef.current.show({ item: record, recommendCycle });
                        }}
                    >
                        推荐
                    </span>
                );
                const disRecommend = (
                    <Popconfirm
                        title={`是否确认取消会员${
                            openChannelOptionItems?.find((ele) => ele.value == recommendCycle)
                                ?.label || ''
                        }推荐？`}
                        okText="确定"
                        cancelText="取消"
                        placement="left"
                        onConfirm={() => {
                            // delItemEvent(record);
                            form.setFieldsValue({
                                recommendCycle: undefined,
                                recommendPrizeId: undefined,
                            });
                        }}
                    >
                        <span className={commonStyles['table-btn']}>取消推荐</span>
                    </Popconfirm>
                );
                btnList.push(lookBtn);

                if (canRecommend && record.deductionType?.indexOf('02') >= 0) {
                    // 抵扣类型包含会员卡
                    if (record.prizeId == recommendPrizeId) {
                        // 推荐奖品和当前卡id一致，当前显示取消推荐按钮
                        btnList.push(disRecommend);
                    } else {
                        btnList.push(recommend);
                    }
                }
                return (
                    <div
                        style={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            whiteSpace: 'nowrap',
                        }}
                    >
                        {btnList}
                    </div>
                );
            },
        },
    ];

    const columns = useMemo(() => {
        return newUserColumns;
    }, [recommendPrizeId, resultList]);

    const countCpnNumber = useMemo(() => {
        let count = 0;
        resultList?.forEach?.((element) => {
            count += (element[putColDataIndex] && Number(element[putColDataIndex])) || 0;
        });
        return Math.round(count * 100) / 100;
    }, [resultList]);

    const onClearEvent = () => {
        updateResultList([]);
        updateFormInfo([]);
    };

    return (
        <Fragment>
            {resultList?.length > 0 && (
                <div className="mg-t-20" style={{ width: '100%' }}>
                    <TablePro
                        style={{ width: '100%' }}
                        scroll={{ x: 'max-content' }}
                        rowKey={(record) => record.giftbagId}
                        dataSource={resultList}
                        columns={columns}
                        noSort
                        filterHeader={false}
                        pagination={false}
                    />
                    <Space size="large">
                        <span style={{ color: 'blue' }}>单人发券总数： {countCpnNumber}</span>
                    </Space>
                </div>
            )}

            <RecommandModal
                initRef={recommendRef}
                onFinish={(values) => {
                    form.setFieldsValue(values);
                }}
            />
        </Fragment>
    );
};

export default forwardRef(GiftPrizeItem);
