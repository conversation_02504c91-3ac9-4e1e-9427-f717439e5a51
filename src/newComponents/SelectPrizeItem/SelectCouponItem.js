import React, {
    useState,
    useEffect,
    useMemo,
    useRef,
    useContext,
    useImperativeHandle,
    forwardRef,
    Fragment,
} from 'react';
import {
    Button,
    Modal,
    Form,
    Row,
    Col,
    Input,
    Space,
    message,
    InputNumber,
    Select,
    Popconfirm,
} from 'antd';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import commonStyles from '@/assets/styles/common.less';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { getCouponPopListApi } from '@/services/Marketing/MarketingNewCouponApi';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import { NEW_COUPON_TYPES } from '@/config/declare';
import RecommandModal, { openChannelOptionItems } from './RecommandModal';
import useWindowState from '@/hooks/useWindowState';

import { Link } from 'umi';
import AppendItem from './AppendItem';

const FormItem = Form.Item;

const { Option } = Select;

// 优惠券定制化的页面，定义类型
export const COUPON_CUSTOM_PAGE_TYPE = {
    SEND_GIFT: 'sendGift', // 直接发券
    CPN_ACT_2_0: 'cpnAct2', // 优惠券营销2.0
    SECKILL: 'seckillPage', // 秒杀
    SIGN_IN_TOTAL: 'signinLeiji', // 累计签到
    TURN_OVER_FLIP: 'turnOver-flip', // 翻牌
    TURN_OVER: 'turnPage', // 转盘
};

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    otherPrizeStatistic,
    ...restProps
}) => {
    const inputRef = useRef(null);
    const form = useContext(EditableContext);

    useEffect(() => {
        if (editable && (!record || !record[dataIndex])) {
            form.setFieldsValue({
                [dataIndex]: 1,
            });
            save();
        }
    }, []);

    useEffect(() => {
        if (record) {
            if (record[dataIndex]) {
                form.setFieldsValue({
                    [dataIndex]: record[dataIndex],
                });
            }
        }
    }, [record]);

    const editMax = useMemo(() => {
        if (record) {
            const { stockUpLimit, stockLimitNum, totalUseStockNum = 0, prizeId } = record;
            //最终可配置和追加库存量计算
            if (stockUpLimit) {
                let count = 0;

                let diff = Number(stockLimitNum) - Number(totalUseStockNum);

                let otherPrizeSinglePutNum = 0;
                otherPrizeStatistic.forEach((element) => {
                    if (element.prizeId == prizeId) {
                        otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                    }
                });

                if (otherPrizeSinglePutNum > 0) {
                    diff = diff - otherPrizeSinglePutNum;
                }

                // const otherPrizeItem = otherPrizeStatistic.find((ele) => ele.prizeId == prizeId);
                // if (otherPrizeItem) {
                //     diff = diff - Number(otherPrizeItem.singlePutNum);
                // }
                count = diff > 0 ? diff : 0;
                return count;
            }
        }
        return;
    }, [record, otherPrizeStatistic]);

    const save = async () => {
        try {
            const values = form.getFieldsValue();
            handleSave &&
                handleSave({
                    ...record,
                    ...values,
                });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };
    let childNode = children;
    if (editable) {
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                required
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请填写${title}`);
                            }
                            if (!(Number(value) > 0)) {
                                return Promise.reject(`${title}值必须为大于0正整数`);
                            }
                            if (editMax > 0 && Number(value) > editMax) {
                                return Promise.reject(`${title}值不能大于可用库存数${editMax}`);
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <InputNumber
                    ref={inputRef}
                    onPressEnter={save}
                    onBlur={save}
                    precision={0}
                    step={1}
                    min={0}
                />
            </Form.Item>
        );
    }
    return <td {...restProps}>{childNode}</td>;
};

const ModalView = (props) => {
    const {
        dispatch,
        global,
        defaultValues = [],
        disabled,
        disabledIds = [],
        onConfirm,
        onCancel,
        extInParams = {},
        listParams = {},
        pageType,
        disabledPrizeType = NEW_COUPON_TYPES.OTHER, // 禁用的券类型
        prizeClass = '1', // 如果要查全部，传null，否则按约定传固定参 奖品类别  1：充电券 2：非充电券
        singleMode,
    } = props;

    const { codeInfo } = global;

    const { chargingVoucher: chargingVoucherList, prizeType: prizeTypeList } = codeInfo;

    const [searchForm] = Form.useForm();

    const [prizeList, updatePrizeList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [listLoading, updateListLoading] = useState(false);

    const [list, updateList] = useState([]);
    const [listTotal, updateListTotal] = useState(0);

    const { modalHeight } = useWindowState();

    useEffect(() => {
        if (isEmpty(chargingVoucherList)) {
            dispatch({
                type: 'global/initCode',
                code: 'chargingVoucher',
            });
        }
        if (isEmpty(prizeTypeList)) {
            dispatch({
                type: 'global/initCode',
                code: 'prizeType',
            });
        }
    }, [pageType]);

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            updatePrizeList(defaultValues);
        }
    }, [defaultValues]);

    const disabledType = useMemo(() => {
        if (disabledPrizeType == null) {
            return NEW_COUPON_TYPES.OTHER;
        }
        return null;
    }, [disabledPrizeType]);

    const prizeTypeOptions = useMemo(() => {
        let arr = prizeTypeList;

        if (arr instanceof Array) {
            return arr.map((ele) => {
                return (
                    <Option
                        disabled={disabledPrizeType == ele.codeValue}
                        key={ele.codeValue}
                        value={ele.codeValue}
                    >
                        {ele.codeName}
                    </Option>
                );
            });
        }
        return [];
    }, [chargingVoucherList, prizeTypeList, disabledType, disabledPrizeType]);

    useEffect(() => {
        searchData();
    }, [pageInfo, pageType]);

    const onFinish = (values) => {
        // onSubmit(values);
        searchData();
    };

    // 调用搜索接口
    const searchData = async () => {
        try {
            const formData = searchForm.getFieldsValue();
            const params = {
                ...formData,
                ...listParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                prizeState: 1,
                prizeClass: typeof prizeClass == 'string' ? prizeClass : undefined,
            };
            updateListLoading(true);
            const {
                data: { records = [], total = 0 },
            } = await getCouponPopListApi(params);
            updateList(records);
            updateListTotal(total);

            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        updatePrizeList([]);
        changePageInfo({ pageIndex: 1 });
    };

    const rowSelection = {
        type: singleMode ? 'radio' : 'checkbox',
        selectedRowKeys: prizeList.map((item) => `${item.prizeId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            if (singleMode) {
                let nowPageCpns = list.filter(
                    (x) => selectedRows.filter((now) => now.prizeId == x.prizeId).length > 0,
                );
                updatePrizeList(nowPageCpns);
            } else {
                // 筛选出非当前页的勾选项，不予处理
                let otherCpns = prizeList.filter(
                    (x) => list.filter((now) => now.prizeId == x.prizeId).length == 0,
                );
                let nowPageCpns = list.filter(
                    (x) => selectedRows.filter((now) => now.prizeId == x.prizeId).length > 0,
                );
                const newList = [...otherCpns, ...nowPageCpns];

                updatePrizeList(newList);
            }
        },
        getCheckboxProps: (record) => ({
            disabled:
                disabled ||
                defaultValues.findIndex((ele) => ele.prizeId == record.prizeId) >= 0 ||
                disabledIds.findIndex((ele) => ele == record.prizeId) >= 0,
            name: record.prizeId,
        }),
    };

    const onOk = () => {
        if (prizeList.length == 0) {
            message.error('请选择优惠券');
            return;
        }
        onConfirm(prizeList);
    };
    const onCancelEvent = () => {
        onCancel();
    };

    const newUserColumns = [
        {
            title: '券名称',
            dataIndex: 'prizeName',
            fixed: 'left',
            width: 160,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠额度',
            dataIndex: 'dctValueName',
            fixed: 'left',
            width: 200,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '券类型',
            dataIndex: 'prizeTypeName',
            fixed: 'left',
            width: 200,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠类型',
            dataIndex: 'dctTypeName',
            width: 120,
            render(text) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '抵扣类型',
            dataIndex: 'deductionTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '券组编号',
            dataIndex: 'prizeNo',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '用户属性',
            dataIndex: 'userTypeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用范围',
            dataIndex: 'scopeTypeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用库存',
            dataIndex: 'stockUpLimit',
            width: 120,
            render(text, record) {
                let count = 0;
                const { stockUpLimit, stockLimitNum, totalUseStockNum = 0 } = record;
                if (stockUpLimit) {
                    const diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    count = diff > 0 ? diff : 0;
                    return <span title={count}>{count}</span>;
                } else {
                    return <span title={text}>{'-'}</span>;
                }
            },
        },
        {
            title: '券备注',
            dataIndex: 'remark',
            width: 160,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '有效期',
            dataIndex: 'expirationDesc',
            width: 200,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
    ];

    const columns = useMemo(() => {
        return newUserColumns;
    }, []);

    const disableTypeSelect = useMemo(() => {
        if (extInParams) {
            const { dctType } = extInParams;
            if (dctType) {
                return true;
            }
        }
        return false;
    }, [extInParams]);

    return (
        <Fragment>
            <Form
                name="prize-search-select"
                form={searchForm}
                onFinish={onFinish}
                scrollToFirstError
                initialValues={{ ...extInParams }}
                labelCol={{ flex: '0 0 80px' }}
            >
                <SearchOptionsBar loading={listLoading} onReset={resetData} minSpan={40}>
                    <FormItem noStyle name="contributParty"></FormItem>
                    <Col span={8}>
                        <FormItem label="券名称" name="prizeName">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="券编号" name="prizeNo">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <FormItem label="券类型" name="prizeType">
                            <Select
                                placeholder="请选择"
                                allowClear
                                disabled={extInParams?.prizeType}
                            >
                                {prizeTypeOptions}
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="优惠类型" name="dctType">
                            <Select placeholder="请选择" allowClear disabled={disableTypeSelect}>
                                <Select.Option value={'01'}>满减券</Select.Option>
                                <Select.Option value={'02'}>折扣券</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <FormItem label="券备注:" name="remark">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                rowSelection={list?.length > 0 && rowSelection}
                loading={listLoading}
                scroll={{ x: 'max-content', y: modalHeight }}
                rowKey={(record) => record.prizeId}
                dataSource={list}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: listTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                noSort
            />
            <div className={commonStyles['btn-bar']} style={{ textAlign: 'center' }}>
                {!disabled ? (
                    <Button className={commonStyles['btn-item']} type="primary" onClick={onOk}>
                        提交
                    </Button>
                ) : null}

                <Button className={commonStyles['btn-item']} onClick={onCancelEvent}>
                    取消
                </Button>
            </div>
        </Fragment>
    );
};
const SelectCouponItem = (props) => {
    const {
        value,
        onChange,
        disabled,
        global,
        dispatch,
        putColDataIndex = 'singlePutNum',
        otherPrizeStatistic = [],
        pageType = undefined,
        canRecommend,
        form, // 目前仅新人礼包需要取表单里面的推荐属性
        parentName = [],
        fullParentName = [],
        isCopy,
        ref,
        singleMode, // 是否开启单个优惠券模式，限制添加只能单选，如果选了一个，再点添加，会提示错误
        columns: customColumns,
        appendBtn, // 追加按钮入口，配合customColumns使用，达到在操作列显示追加按钮的目的，（回传item, index）
        ...otherProps
    } = props;

    const [showSelectModal, toggleSelectModal] = useState(false);
    const [resultList, setResultList] = useState([]);
    const [countCpnNumber, updateCountCpnNumber] = useState(0);

    // 仅新人礼包使用的属性，上一次选中的推荐周期+推荐奖品id *业务页面赋值
    const recommendCycle = Form.useWatch([...fullParentName, 'recommendCycle'], form);
    const recommendPrizeId = Form.useWatch([...fullParentName, 'recommendPrizeId'], form);
    const recommendRef = useRef();

    const updateResultList = (list) => {
        list.forEach((element, index) => {
            element.index = index;
        });

        let count = 0;
        list?.forEach?.((element) => {
            count += (element[putColDataIndex] && Number(element[putColDataIndex])) || 0;
        });
        updateCountCpnNumber(Math.round(count * 100) / 100);
        setResultList(list);
    };
    useImperativeHandle(ref, () => {
        return {
            clear: () => {
                changeResultEvent([]);
            },
        };
    });
    useEffect(() => {
        updateResultList(value || []);
    }, [value]);
    const openSelectModal = () => {
        if (singleMode && resultList?.length >= 1) {
            message.error('只能添加一张优惠券');
            return;
        }
        toggleSelectModal(true);
    };
    const closeSelectModal = () => {
        toggleSelectModal(false);
    };
    const changeResultEvent = (values) => {
        updateResultList(values);
        updateFormItem(values);
    };
    const updateFormItem = (values) => {
        onChange && onChange(values);
    };
    const delItemEvent = (item) => {
        const itemIndex = resultList.findIndex((ele) => {
            return ele.prizeId == item.prizeId;
        });
        if (item.prizeId == recommendPrizeId) {
            // 如果删除的是已推荐的券，把推荐标识清空
            setValues({
                recommendCycle: undefined,
                recommendPrizeId: undefined,
            });
        }
        const newList = copyObjectCommon(resultList);
        newList.splice(itemIndex, 1);
        changeResultEvent(newList);
    };

    const handleSave = (row) => {
        const newData = copyObjectCommon(resultList);
        const index = newData.findIndex((item) => row.prizeId == item.prizeId);
        const item = newData[index];
        newData.splice(index, 1, {
            ...item,
            ...row,
        });
        changeResultEvent(newData);

        updateFormItem(newData);

        // const newSelectData = [...resultList];
        // const selectIndex = newSelectData.findIndex((item) => row.prizeId == item.prizeId);
        // if (selectIndex >= 0) {
        //     const item = newSelectData[selectIndex];
        //     newSelectData.splice(index, 1, {
        //         ...row,
        //         ...item,
        //     });
        //     updateFormItem(newSelectData);
        // }
    };

    const editCellInfo = !disabled
        ? {
              onCell: (record, index) => ({
                  record,
                  index: index,
                  editable: true,
                  dataIndex: 'singlePutNum',
                  title: '单人发放量',
                  handleSave,
                  otherPrizeStatistic,
              }),
          }
        : {
              render(text, record) {
                  return <span title={text}>{text}</span>;
              },
          };

    const setValues = (values) => {
        if (!values || typeof values != 'object') {
            return;
        }
        const allValues = form.getFieldsValue();
        let item;
        if (fullParentName.length) {
            for (const key of fullParentName) {
                if (!item) {
                    item = allValues[key];
                } else {
                    item = item[key];
                }
            }
            if (isEmpty(item)) {
                item = {};
            }
        } else {
            item = allValues;
        }

        const keys = Object.keys(values);
        keys.forEach((key) => {
            item[key] = values[key];
        });
        form.setFieldsValue(allValues);
    };

    const columnOper = {
        title: '操作',
        width: 140,
        fixed: 'right',
        render: (text, record, index) => {
            let btnList = [];
            const delBtn = (
                <Popconfirm
                    title="要从礼包列表中删除此优惠券？"
                    okText="确定"
                    cancelText="取消"
                    placement="left"
                    onConfirm={() => {
                        delItemEvent(record);
                    }}
                >
                    <span className={commonStyles['table-btn']} style={{ color: 'red' }}>
                        删除
                    </span>
                </Popconfirm>
            );
            const lookPath = `/marketing/couponCenter/cpnManage/list/look/${record.prizeId}`;
            const lookBtn = (
                <Link to={lookPath} target="_blank">
                    查看
                </Link>
            );
            const recommend = (
                <span
                    className={commonStyles['table-btn']}
                    onClick={() => {
                        recommendRef.current.show({ item: record, recommendCycle });
                    }}
                >
                    推荐
                </span>
            );
            const disRecommend = (
                <Popconfirm
                    title={`是否确认取消会员${
                        openChannelOptionItems?.find((ele) => ele.value == recommendCycle)?.label ||
                        ''
                    }推荐？`}
                    okText="确定"
                    cancelText="取消"
                    placement="left"
                    onConfirm={() => {
                        setValues({
                            recommendCycle: undefined,
                            recommendPrizeId: undefined,
                        });
                    }}
                >
                    <span className={commonStyles['table-btn']}>取消推荐</span>
                </Popconfirm>
            );
            btnList.push(lookBtn);
            if (!disabled) {
                btnList.push(delBtn);
            }

            if (appendBtn) {
                btnList.push(appendBtn(record, index));
            }

            if (
                canRecommend &&
                record.deductionType?.indexOf('02') >= 0 &&
                record?.prizeDeductionProjects?.length
            ) {
                // 抵扣类型包含会员卡
                if (record.prizeId == recommendPrizeId) {
                    // 推荐奖品和当前卡id一致，当前显示取消推荐按钮
                    btnList.push(disRecommend);
                } else {
                    btnList.push(recommend);
                }
            }
            return (
                <Row gutter={8}>
                    {btnList.map((ele, index) => (
                        <Col key={index}>{ele}</Col>
                    ))}
                </Row>
            );
        },
    };
    const newUserColumns = [
        ...((pageType == COUPON_CUSTOM_PAGE_TYPE.SEND_GIFT && []) || [
            {
                title: '序号 ',
                width: 60,
                render(text, record, index) {
                    return <span title={record.index}>{record.index + 1}</span>;
                },
            },
        ]),
        {
            title: '券组编号',
            dataIndex: 'prizeNo',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '券名称',
            dataIndex: 'prizeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠额度',
            dataIndex: 'dctValueName',
            width: 160,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '券类型',
            dataIndex: 'prizeTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '优惠类型',
            dataIndex: 'dctTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '抵扣类型',
            dataIndex: 'deductionTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用库存',
            dataIndex: 'stockUpLimit',
            width: 120,
            render(text, record) {
                let count = 0;
                const {
                    stockUpLimit,
                    stockLimitNum,
                    totalUseStockNum = 0,
                    totalNoSelfUseStockNum = 0,
                    totalSelfUseStockNum = 0,
                    stockNum,
                    prizeId,
                } = record;
                if (stockUpLimit && stockUpLimit !== 'false') {
                    let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    if (!disabled) {
                        if (!isCopy) {
                            diff += Number(totalSelfUseStockNum);
                        }

                        let otherPrizeSinglePutNum = 0;
                        otherPrizeStatistic.forEach((element) => {
                            if (element.prizeId == prizeId) {
                                otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                            }
                        });

                        if (otherPrizeSinglePutNum > 0) {
                            diff = diff - otherPrizeSinglePutNum;
                        }

                        // const otherPrizeItem = otherPrizeStatistic.find(
                        //     (ele) => ele.prizeId == prizeId,
                        // );
                        // if (otherPrizeItem) {
                        //     diff = diff - Number(otherPrizeItem.singlePutNum);
                        // }
                    }
                    count = diff > 0 ? diff : 0;
                    return <span title={count}>{count}</span>;
                } else {
                    return <span title={text}>{'-'}</span>;
                }
            },
        },
        ...(pageType == COUPON_CUSTOM_PAGE_TYPE.CPN_ACT_2_0 ||
        pageType == COUPON_CUSTOM_PAGE_TYPE.SECKILL ||
        pageType == COUPON_CUSTOM_PAGE_TYPE.SIGN_IN_TOTAL
            ? []
            : [
                  {
                      title: '已发数量',
                      dataIndex: 'prizePutNum',
                      width: 120,
                      render(text, record) {
                          return (
                              <span title={text}>{text == undefined || isCopy ? '-' : text}</span>
                          );
                      },
                  },
              ]),

        {
            title: '单人发放量',
            width: 120,
            dataIndex: 'singlePutNum',
            ...editCellInfo,
        },
        {
            title: '用户属性',
            dataIndex: 'userTypeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '可用范围',
            dataIndex: 'scopeTypeName',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '有效期',
            dataIndex: 'expirationDesc',
            width: 200,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        ...((pageType == COUPON_CUSTOM_PAGE_TYPE.SEND_GIFT && []) || [columnOper]),
    ];

    const columns = useMemo(() => {
        const c = customColumns instanceof Array ? [...customColumns, columnOper] : newUserColumns;
        return c || [];
    }, [newUserColumns, customColumns, columnOper]);

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    return (
        <Fragment>
            {!disabled && (
                <Button type="primary" disabled={disabled} onClick={openSelectModal}>
                    + 添加优惠券
                </Button>
            )}

            {/* {resultList?.length > 0 && ( */}
            <div
                className={pageType != COUPON_CUSTOM_PAGE_TYPE.SEND_GIFT && 'mg-t-20'}
                style={{ width: '100%' }}
            >
                <TablePro
                    components={
                        pageType == COUPON_CUSTOM_PAGE_TYPE.TURN_OVER_FLIP ? undefined : components
                    }
                    style={{ width: '100%' }}
                    offsetHeader={0}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.prizeId}
                    dataSource={resultList}
                    columns={columns}
                    pagination={false}
                />
                {pageType != COUPON_CUSTOM_PAGE_TYPE.TURN_OVER_FLIP &&
                    pageType != COUPON_CUSTOM_PAGE_TYPE.SEND_GIFT && (
                        <Space size="large">
                            <span style={{ color: 'blue' }}>单人发券总数： {countCpnNumber}</span>
                        </Space>
                    )}
            </div>
            {/* )} */}

            <Modal
                title="添加优惠券"
                width={1200}
                visible={showSelectModal}
                onCancel={() => {
                    closeSelectModal();
                }}
                footer={null}
                maskClosable={false}
                destroyOnClose
            >
                <ModalView
                    {...otherProps}
                    singleMode={singleMode}
                    pageType={pageType}
                    global={global}
                    dispatch={dispatch}
                    disabled={disabled}
                    defaultValues={value}
                    onCancel={() => {
                        closeSelectModal();
                    }}
                    onConfirm={(confirmValues) => {
                        //过滤已配置数据
                        const newSelectList = confirmValues.filter((ele) => {
                            return !resultList.find((item) => {
                                return item.prizeId == ele.prizeId;
                            });
                        });
                        //合并成新数据
                        const newList = [
                            // 签到管理需求，后加的券放在前面
                            ...newSelectList.map((ele) => {
                                return {
                                    ...ele,
                                    [putColDataIndex]: 1,
                                };
                            }),
                            ...resultList,
                        ];

                        changeResultEvent(newList);
                        closeSelectModal();
                    }}
                ></ModalView>
            </Modal>

            <RecommandModal
                initRef={recommendRef}
                onFinish={(values) => {
                    setValues(values);
                }}
            />
        </Fragment>
    );
};

export const CouponComponents = (props) => {
    const {
        parentName = [],
        fullParentName = [],
        listName = '',
        disabled,
        title = '选择优惠券',

        canAppend, //是否可追加
        canRecommend,
        appendExtParams, // 追加操作附加的额外字段，可嵌套为{xx:xx, stockParamList: {xx: xx}}
        otherPrizeStatistic = [], //统计其他list里配的礼包，计算出当前券里可配置库存
        editOnBlur, // 库存编辑后的事件回调

        /*******老优惠券需要入参*******/
        actId,
        actSubId,
        onRefresh,
        extInParams = {},
        dispatch,

        global,
        form,
        formItemLayout = {},
        pageType = undefined, // 区分各个活动配置页面用于定制化，转盘活动turnPage，优惠券营销2.0 cpnAct2，限时秒杀，seckillPage，累积签到，signinLeiji
        isCopy,

        couponRef,
    } = props;

    const giftBagPutNum = Form.useWatch([...fullParentName, 'giftBagPutNum'], form);
    const stockNum = Form.useWatch([...fullParentName, 'stockNum'], form);

    const pageInfo = useMemo(() => {
        const info = { stockKey: 'stockNum', stockName: '活动库存' };
        if (pageType == COUPON_CUSTOM_PAGE_TYPE.TURN_OVER) {
            info.stockKey = 'prizeNum';
            info.stockName = '奖励份数';
        } else if (pageType == COUPON_CUSTOM_PAGE_TYPE.SECKILL) {
            info.stockName = '活动总库存数量';
        }
        return info;
    }, [pageType]);

    const endNum = useMemo(() => {
        let count = undefined;
        if (!isCopy && !isEmpty(stockNum) && !isEmpty(giftBagPutNum)) {
            count = Math.floor(Number(stockNum) - Number(giftBagPutNum));
        }

        return count;
    }, [giftBagPutNum, stockNum, isCopy]);

    const detPrizeList = Form.useWatch([...fullParentName, listName], form);
    const maxCount = useMemo(() => {
        let _maxCount;
        if (detPrizeList instanceof Array && detPrizeList?.length) {
            //计算库存最多追加上限
            for (const item of detPrizeList) {
                const {
                    stockUpLimit,
                    stockLimitNum,
                    totalUseStockNum = 0,
                    totalNoSelfUseStockNum = 0,
                    totalSelfUseStockNum = 0,
                    singlePutNum,
                    prizeId,
                } = item;
                if (stockUpLimit && stockUpLimit !== 'false') {
                    let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    if (!disabled) {
                        if (!isCopy) {
                            diff += Number(totalSelfUseStockNum);
                        }

                        //追加计算不同任务配置相同礼品时的数量

                        let otherPrizeSinglePutNum = 0;
                        otherPrizeStatistic.forEach((element) => {
                            if (element.prizeId == prizeId) {
                                otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                            }
                        });

                        if (otherPrizeSinglePutNum > 0) {
                            diff = diff - otherPrizeSinglePutNum;
                        }

                        // const otherPrizeItem = otherPrizeStatistic.find(
                        //     (ele) => ele.prizeId == prizeId,
                        // );
                        // if (otherPrizeItem) {
                        //     diff = diff - Number(otherPrizeItem.singlePutNum);
                        // }
                    }

                    const itemMax = Math.floor((diff > 0 ? diff : 0) / Number(singlePutNum));
                    //取数组每个优惠券可用数量除去单人发券数的最小值

                    if (isEmpty(_maxCount) || itemMax < _maxCount) {
                        _maxCount = itemMax;
                    }
                }
            }
        }
        return _maxCount;
    });

    useEffect(() => {
        // 记录当前可用库存
        const allValues = form.getFieldsValue();
        let item;
        for (const key of fullParentName) {
            if (!item) {
                item = allValues[key];
            } else {
                item = item[key];
            }
        }
        if (isEmpty(item)) {
            item = {};
        }
        if (maxCount !== undefined && item.maxCount != maxCount) {
            item.maxCount = maxCount;
            form.setFieldsValue(allValues);
        } else if (maxCount == undefined && item.maxCount !== undefined) {
            item.maxCount = undefined;
            form.setFieldsValue(allValues);
        }
    }, [maxCount, parentName]);

    const stockItem = (
        <FormItem label={pageInfo?.stockName} required {...formItemLayout}>
            <Space direction="vertical">
                <FormItem
                    noStyle
                    name={[...parentName, pageInfo?.stockKey]}
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请填写`);
                                }
                                if (Number(value) != -1 && Number(value) <= 0) {
                                    return Promise.reject(`值必须为大于0正整数`);
                                }
                                if (
                                    (isCopy || !canAppend) &&
                                    !isEmpty(maxCount) &&
                                    Number(value) > Number(maxCount)
                                ) {
                                    return Promise.reject(`最多可配库存数${maxCount}`);
                                }

                                if (pageType == COUPON_CUSTOM_PAGE_TYPE.CPN_ACT_2_0) {
                                    const cpnList =
                                        form.getFieldValue([...fullParentName, listName]) || [];
                                    let maxPutNum = 0;
                                    for (const item of cpnList) {
                                        maxPutNum =
                                            maxPutNum < Number(item.singlePutNum)
                                                ? Number(item.singlePutNum)
                                                : maxPutNum;
                                    }
                                    if (maxPutNum > Number(value)) {
                                        return Promise.reject(
                                            `库存数至少支持最大单人发放量${maxPutNum}`,
                                        );
                                    }
                                }

                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <AppendItem
                        disabled={!isCopy && disabled}
                        canAppend={!isCopy && canAppend}
                        actId={!isCopy && actId}
                        actSubId={actSubId}
                        reload={onRefresh}
                        maxCount={maxCount}
                        appendExtParams={appendExtParams}
                        pageType={pageType}
                        editOnBlur={editOnBlur}
                    ></AppendItem>
                </FormItem>
                <FormItem noStyle name={[...parentName, 'giftBagPutNum']}>
                    {!isEmpty(endNum) ? `剩余库存:${endNum}份` : ''}
                </FormItem>
            </Space>
        </FormItem>
    );

    return (
        <Fragment>
            {(pageType == COUPON_CUSTOM_PAGE_TYPE.TURN_OVER && stockItem) || null}
            <FormItem name={[...parentName, 'maxCount']} noStyle />
            <FormItem
                label={title}
                name={[...parentName, listName]}
                rules={[
                    // { required: true, message: '请选择优惠券' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请选择优惠券`);
                            }
                            let errList = [];
                            for (const item of value) {
                                if (isEmpty(item.singlePutNum)) {
                                    errList.push(item.prizeName);
                                }
                            }
                            if (errList.length > 0) {
                                return Promise.reject(`请配置${errList.join('、')}的单人发放数量`);
                            }

                            if (canRecommend) {
                                const chargeType = value.find((ele) => ele.prizeClass == '1');
                                if (!chargeType) {
                                    return Promise.reject('请至少包含一张充电券');
                                }
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
                required
                {...formItemLayout}
            >
                <SelectCouponItem
                    {...props}
                    ref={couponRef}
                    global={global}
                    dispatch={dispatch}
                    extInParams={extInParams}
                    disabled={!isCopy && disabled}
                    otherPrizeStatistic={otherPrizeStatistic}
                    pageType={pageType}
                    form={form}
                    fullParentName={fullParentName}
                    isCopy={isCopy}
                    canRecommend={canRecommend}
                ></SelectCouponItem>
            </FormItem>
            {(pageType != COUPON_CUSTOM_PAGE_TYPE.TURN_OVER &&
                pageType != COUPON_CUSTOM_PAGE_TYPE.SIGN_IN_TOTAL &&
                stockItem) ||
                null}
        </Fragment>
    );
};

export default SelectCouponItem;
