import React, {
    useState,
    useEffect,
    useMemo,
    useRef,
    useContext,
    useImperativeHandle,
    forwardRef,
    Fragment,
} from 'react';
import { Form, Space, Select, Spin } from 'antd';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { getGiftListApi, getGiftInfoApi } from '@/services/Marketing/MarketingNewCouponApi';
import debounce from 'lodash/debounce';

const FormItem = Form.Item;

const { Option } = Select;

const SelectGiftItem = (props, ref) => {
    const {
        value,
        onChange,
        disabled,
        putColDataIndex = 'singlePutNum',
        otherPrizeStatistic = [],
        ...otherProps
    } = props;
    const [fetchList, changeFetchList] = useState([]);

    const [fetching, changeFetching] = useState(false);

    useImperativeHandle(ref, () => {
        return {
            clear: () => {
                updateFormInfo('');
            },
        };
    });

    useEffect(() => {
        fetchEvent();
    }, []);

    useEffect(() => {
        if (value) {
            changeSelectEvent(value);
        }
    }, [value]);

    const changeSelectEvent = async (newValues) => {
        try {
            if (isEmpty(newValues)) {
                return;
            }

            updateFormInfo(newValues);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const updateFormInfo = (newValues) => {
        onChange && onChange(newValues);
    };

    const onClearEvent = () => {
        updateFormInfo('');
    };

    const fetchEvent = debounce(async (searchText) => {
        try {
            const params = {
                giftbagName: searchText || '',
                pageIndex: 1,
                pageSize: 99,
            };
            changeFetching(true);
            const { data } = await getGiftListApi(params);
            changeFetchList(data);
            return;
        } catch (error) {
        } finally {
            changeFetching(false);
        }
    }, 800);

    return (
        <Fragment>
            <FormItem wrapperCol={{ span: 8 }} style={{ marginBottom: '0' }}>
                <Select
                    value={value}
                    placeholder="请选择"
                    disabled={disabled}
                    loading={fetching}
                    notFoundContent={fetching ? <Spin size="small" /> : null}
                    onFocus={() => {
                        fetchEvent('');
                    }}
                    showSearch
                    onSearch={fetchEvent}
                    filterOption={false}
                    onClear={onClearEvent}
                    onChange={changeSelectEvent}
                    allowClear
                >
                    {fetchList.map((d) => (
                        <Option key={d.giftbagId}>{d.giftbagName}</Option>
                    ))}
                </Select>
            </FormItem>
        </Fragment>
    );
};

export default forwardRef(SelectGiftItem);
