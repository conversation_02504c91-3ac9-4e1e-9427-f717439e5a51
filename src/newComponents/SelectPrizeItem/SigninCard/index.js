import React, {
    useState,
    useEffect,
    useMemo,
    useRef,
    useContext,
    useImperativeHandle,
    Fragment,
} from 'react';
import {
    Button,
    Modal,
    Form,
    Row,
    Col,
    Input,
    Space,
    message,
    InputNumber,
    Popconfirm,
} from 'antd';
import TablePro from '@/components/TablePro';
import usePageState from '@/hooks/usePageState.js';
import commonStyles from '@/assets/styles/common.less';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import SearchOptionsBar from '@/components/SearchOptionsBar';
import useWindowState from '@/hooks/useWindowState';

import { Link } from 'umi';
import { getSupplementListApi } from '@/services/Marketing/MarketingSigninApi';
import { COUPON_CUSTOM_PAGE_TYPE } from '../SelectCouponItem';
// import AppendItem from './AppendItem';

const FormItem = Form.Item;

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    otherPrizeStatistic,
    ...restProps
}) => {
    const inputRef = useRef(null);
    const form = useContext(EditableContext);

    useEffect(() => {
        if (editable && (!record || !record[dataIndex])) {
            form.setFieldsValue({
                [dataIndex]: 1,
            });
            save();
        }
    }, []);

    useEffect(() => {
        if (record) {
            if (record[dataIndex]) {
                form.setFieldsValue({
                    [dataIndex]: record[dataIndex],
                });
            }
        }
    }, [record]);

    const editMax = useMemo(() => {
        if (record) {
            const { stockUpLimit, stockLimitNum, totalUseStockNum = 0, patchId } = record;
            //最终可配置和追加库存量计算
            if (stockUpLimit) {
                let count = 0;

                let diff = Number(stockLimitNum) - Number(totalUseStockNum);

                let otherPrizeSinglePutNum = 0;
                otherPrizeStatistic.forEach((element) => {
                    if (element.patchId === patchId) {
                        otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                    }
                });

                if (otherPrizeSinglePutNum > 0) {
                    diff = diff - otherPrizeSinglePutNum;
                }

                // const otherPrizeItem = otherPrizeStatistic.find((ele) => ele.patchId === patchId);
                // if (otherPrizeItem) {
                //     diff = diff - Number(otherPrizeItem.singlePutNum);
                // }
                count = diff > 0 ? diff : 0;
                return count;
            }
        }
        return;
    }, [record, otherPrizeStatistic]);

    const save = async () => {
        try {
            const values = form.getFieldsValue();
            handleSave &&
                handleSave({
                    ...record,
                    ...values,
                });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };
    let childNode = children;
    if (editable) {
        childNode = (
            <Form.Item
                style={{
                    margin: 0,
                }}
                name={dataIndex}
                required
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请填写${title}`);
                            }
                            if (!(Number(value) > 0)) {
                                return Promise.reject(`${title}值必须为大于0正整数`);
                            }
                            if (editMax > 0 && Number(value) > editMax) {
                                return Promise.reject(`${title}值不能大于可用库存数${editMax}`);
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <InputNumber
                    ref={inputRef}
                    onPressEnter={save}
                    onBlur={save}
                    precision={0}
                    step={1}
                    min={0}
                />
            </Form.Item>
        );
    }
    return <td {...restProps}>{childNode}</td>;
};

const ModalView = (props) => {
    const {
        defaultValues = [],
        disabled,
        onConfirm,
        onCancel,
        extInParams = {},
        listParams = {},
        pageType,
        singleMode,
    } = props;

    const [searchForm] = Form.useForm();

    const [prizeList, updatePrizeList] = useState([]); // 已选中的补签卡列表，用于添加时判断是否重复选择

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [listLoading, updateListLoading] = useState(false);

    const [list, updateList] = useState([]);
    const [listTotal, updateListTotal] = useState(0);

    const { modalHeight } = useWindowState();

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            updatePrizeList(defaultValues);
        }
    }, [defaultValues]);

    useEffect(() => {
        searchData();
    }, [pageInfo, pageType]);

    const onFinish = (values) => {
        // onSubmit(values);
        searchData();
    };

    // 调用搜索接口
    const searchData = async () => {
        try {
            const formData = searchForm.getFieldsValue();
            const params = {
                ...formData,
                ...listParams,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                status: 2,
            };
            updateListLoading(true);
            const {
                data: { records = [], total = 0 },
            } = await getSupplementListApi(params);
            updateList(records);
            updateListTotal(total);

            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        updatePrizeList([]);
        changePageInfo({ pageIndex: 1 });
    };

    const rowSelection = {
        type: singleMode ? 'radio' : 'checkbox',
        selectedRowKeys: prizeList.map((item) => `${item.patchId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            if (singleMode) {
                let nowPageCpns = list.filter(
                    (x) => selectedRows.filter((now) => now.patchId == x.patchId).length > 0,
                );
                updatePrizeList(nowPageCpns);
            } else {
                // 筛选出非当前页的勾选项，不予处理
                let otherCpns = prizeList.filter(
                    (x) => list.filter((now) => now.patchId == x.patchId).length == 0,
                );
                let nowPageCpns = list.filter(
                    (x) => selectedRows.filter((now) => now.patchId == x.patchId).length > 0,
                );
                const newList = [...otherCpns, ...nowPageCpns];

                updatePrizeList(newList);
            }
        },
        getCheckboxProps: (record) => ({
            disabled:
                disabled || defaultValues.findIndex((ele) => ele.patchId === record.patchId) >= 0,
            name: record.patchId,
        }),
    };

    const onOk = () => {
        if (prizeList.length == 0) {
            message.error('请选择补签卡');
            return;
        }
        onConfirm(prizeList);
    };
    const onCancelEvent = () => {
        onCancel();
    };

    const newUserColumns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index + 1}>{index + 1}</span>;
            },
        },
        {
            title: '补签卡ID',
            width: 120,
            dataIndex: 'patchId',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '补签卡名称',
            width: 140,
            dataIndex: 'name',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '补签卡类型',
            width: 140,
            dataIndex: 'typeName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '补签天数',
            width: 120,
            dataIndex: 'patchDayNum',
            render(text = '', record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建时间',
            width: 200,
            dataIndex: 'createTime',
            render(text = '', record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '有效期',
            width: 200,
            dataIndex: 'temp_',
            render(text = '', record) {
                if (record.validityType == '1') {
                    text = `发放后${record.validityDayNum}天内有效`;
                } else if (record.validityType == '2') {
                    text = `${record.effTime} ~ ${record.expTime}`;
                }
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const columns = useMemo(() => {
        return newUserColumns;
    }, []);

    return (
        <Fragment>
            <Form
                name="prize-search-select"
                form={searchForm}
                onFinish={onFinish}
                scrollToFirstError
                initialValues={{ ...extInParams }}
            >
                <SearchOptionsBar loading={listLoading} onReset={resetData} minSpan={40}>
                    <Col span={10}>
                        <FormItem label="补签卡名称" name="name">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={10}>
                        <FormItem label="补签卡ID" name="patchId">
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                rowSelection={(list?.length > 0 && rowSelection) || undefined}
                loading={listLoading}
                scroll={{ x: 'max-content', y: modalHeight }}
                rowKey={(record) => record.patchId}
                dataSource={list}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: listTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                noSort
            />
            <div className={commonStyles['btn-bar']} style={{ textAlign: 'center' }}>
                {!disabled ? (
                    <Button className={commonStyles['btn-item']} type="primary" onClick={onOk}>
                        提交
                    </Button>
                ) : null}

                <Button className={commonStyles['btn-item']} onClick={onCancelEvent}>
                    取消
                </Button>
            </div>
        </Fragment>
    );
};
const SelectCouponItem = (props) => {
    const {
        value,
        onChange,
        disabled,
        global,
        dispatch,
        putColDataIndex = 'singlePutNum',
        otherPrizeStatistic = [],
        pageType = undefined,
        form, // 目前仅新人礼包需要取表单里面的推荐属性
        parentName,
        fullParentName,
        isCopy,
        ref,
        singleMode, // 是否开启单个补签卡模式，限制添加只能单选，如果选了一个，再点添加，会提示错误
        ...otherProps
    } = props;

    const [showSelectModal, toggleSelectModal] = useState(false);
    const [resultList, setResultList] = useState([]);
    const [countCpnNumber, updateCountCpnNumber] = useState(0);

    const updateResultList = (list) => {
        list.forEach((element, index) => {
            element.index = index;
        });

        let count = 0;
        list?.forEach?.((element) => {
            count += (element[putColDataIndex] && Number(element[putColDataIndex])) || 0;
        });
        updateCountCpnNumber(Math.round(count * 100) / 100);
        setResultList(list);
    };
    useImperativeHandle(ref, () => {
        return {
            clear: () => {
                changeResultEvent([]);
            },
        };
    });
    useEffect(() => {
        updateResultList(value || []);
    }, [value]);
    const openSelectModal = () => {
        if (singleMode && resultList?.length >= 1) {
            message.error('只能添加一张补签卡');
            return;
        }
        toggleSelectModal(true);
    };
    const closeSelectModal = () => {
        toggleSelectModal(false);
    };
    const changeResultEvent = (values) => {
        updateResultList(values);
        updateFormItem(values);
    };
    const updateFormItem = (values) => {
        onChange && onChange(values);
    };
    const delItemEvent = (item) => {
        const itemIndex = resultList.findIndex((ele) => {
            return ele.patchId === item.patchId;
        });
        const newList = copyObjectCommon(resultList);
        newList.splice(itemIndex, 1);
        changeResultEvent(newList);
    };

    const handleSave = (row) => {
        const newData = copyObjectCommon(resultList);
        const index = newData.findIndex((item) => row.patchId === item.patchId);
        const item = newData[index];
        newData.splice(index, 1, {
            ...item,
            ...row,
        });
        changeResultEvent(newData);

        updateFormItem(newData);

        // const newSelectData = [...resultList];
        // const selectIndex = newSelectData.findIndex((item) => row.patchId === item.patchId);
        // if (selectIndex >= 0) {
        //     const item = newSelectData[selectIndex];
        //     newSelectData.splice(index, 1, {
        //         ...row,
        //         ...item,
        //     });
        //     updateFormItem(newSelectData);
        // }
    };

    const editCellInfo = !disabled
        ? {
              onCell: (record, index) => ({
                  record,
                  index: index,
                  editable: true,
                  dataIndex: 'singlePutNum',
                  title: '单次发放量',
                  handleSave,
                  otherPrizeStatistic,
              }),
          }
        : {
              render(text, record) {
                  return <span title={text}>{text}</span>;
              },
          };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={record.index}>{record.index + 1}</span>;
            },
        },
        {
            title: '补签卡ID',
            dataIndex: 'patchId',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '补签卡名称',
            dataIndex: 'name',
            width: 140,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '补签卡类型',
            dataIndex: 'typeName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '补签天数',
            dataIndex: 'patchDayNum',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '单次发放量',
            width: 120,
            dataIndex: 'singlePutNum',
            ...editCellInfo,
        },
        {
            title: '已发数量',
            dataIndex: 'putNum',
            width: 120,
            render(text, record) {
                return <span title={text}>{text === undefined || isCopy ? '-' : text}</span>;
            },
        },
        {
            title: '操作',
            width: 140,
            fixed: 'right',
            render: (text, record) => {
                let btnList = [];
                const delBtn = (
                    <Popconfirm
                        title="要从礼包列表中删除此补签卡？"
                        okText="确定"
                        cancelText="取消"
                        placement="left"
                        onConfirm={() => {
                            delItemEvent(record);
                        }}
                    >
                        <span className={commonStyles['table-btn']} style={{ color: 'red' }}>
                            删除
                        </span>
                    </Popconfirm>
                );
                const lookPath = `/marketing/signin/activity/supplement/look/${record.patchId}`;
                const lookBtn = (
                    <Link to={lookPath} target="_blank">
                        查看
                    </Link>
                );

                btnList.push(lookBtn);
                if (!disabled) {
                    btnList.push(delBtn);
                }

                return (
                    <Row gutter={8}>
                        {btnList.map((ele, index) => (
                            <Col key={index}>{ele}</Col>
                        ))}
                    </Row>
                );
            },
        },
    ];

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    return (
        <Fragment>
            <Button type="primary" disabled={disabled} onClick={openSelectModal}>
                + 添加补签卡
            </Button>
            {/* {resultList?.length > 0 && ( */}
            <div className="mg-t-20" style={{ width: '100%' }}>
                <TablePro
                    components={components}
                    style={{ width: '100%' }}
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.patchId}
                    dataSource={resultList}
                    columns={columns}
                    noSort
                    pagination={false}
                />
                <Space size="large">
                    <span style={{ color: 'blue' }}>总发放数量： {countCpnNumber}</span>
                </Space>
            </div>
            {/* )} */}

            <Modal
                title="添加补签卡"
                width={800}
                visible={showSelectModal}
                onCancel={() => {
                    closeSelectModal();
                }}
                footer={null}
                maskClosable={false}
                destroyOnClose
            >
                <ModalView
                    {...otherProps}
                    singleMode={singleMode}
                    pageType={pageType}
                    global={global}
                    dispatch={dispatch}
                    disabled={disabled}
                    defaultValues={value}
                    onCancel={() => {
                        closeSelectModal();
                    }}
                    onConfirm={(confirmValues) => {
                        //过滤已配置数据
                        const newSelectList = confirmValues.filter((ele) => {
                            return !resultList.find((item) => {
                                return item.patchId === ele.patchId;
                            });
                        });
                        //合并成新数据
                        const newList = [
                            ...resultList,
                            ...newSelectList.map((ele) => {
                                return {
                                    ...ele,
                                    [putColDataIndex]: 1,
                                };
                            }),
                        ];

                        changeResultEvent(newList);
                        closeSelectModal();
                    }}
                ></ModalView>
            </Modal>
        </Fragment>
    );
};

export const SigninCardComponents = (props) => {
    const {
        parentName = [],
        fullParentName = [],
        listName = '',
        disabled,
        title = '选择补签卡',

        otherPrizeStatistic = [], //统计其他list里配的礼包，计算出当前券里可配置库存
        editOnBlur, // 库存编辑后的事件回调

        extInParams = {},
        dispatch,

        global,
        form,
        formItemLayout = {},
        pageType = undefined, // 区分各个活动配置页面用于定制化，转盘活动turnPage，补签卡营销2.0 cpnAct2，限时秒杀，seckillPage
        isCopy,

        couponRef,
    } = props;

    const giftBagPutNum = Form.useWatch([...fullParentName, 'giftBagPutNum'], form);
    const stockNum = Form.useWatch([...fullParentName, 'stockNum'], form);

    const pageInfo = useMemo(() => {
        const info = { stockKey: 'stockNum', stockName: '活动库存' };
        if (pageType == COUPON_CUSTOM_PAGE_TYPE.TURN_OVER) {
            info.stockKey = 'prizeNum';
            info.stockName = '奖励份数';
        } else if (pageType == COUPON_CUSTOM_PAGE_TYPE.SECKILL) {
            info.stockName = '活动总库存数量';
        }
        return info;
    }, [pageType]);

    const endNum = useMemo(() => {
        let count = undefined;
        if (!isCopy && !isEmpty(stockNum) && !isEmpty(giftBagPutNum)) {
            count = Math.floor(Number(stockNum) - Number(giftBagPutNum));
        }

        return count;
    }, [giftBagPutNum, stockNum, isCopy]);

    const detPrizeList = Form.useWatch([...fullParentName, listName], form);
    const maxCount = useMemo(() => {
        let _maxCount;
        if (detPrizeList instanceof Array && detPrizeList?.length) {
            //计算库存最多追加上限
            for (const item of detPrizeList) {
                const {
                    stockUpLimit,
                    stockLimitNum,
                    totalUseStockNum = 0,
                    totalNoSelfUseStockNum = 0,
                    totalSelfUseStockNum = 0,
                    singlePutNum,
                    patchId,
                } = item;
                if (stockUpLimit && stockUpLimit !== 'false') {
                    let diff = Number(stockLimitNum) - Number(totalUseStockNum);
                    if (!disabled) {
                        if (!isCopy) {
                            diff += Number(totalSelfUseStockNum);
                        }

                        //追加计算不同任务配置相同礼品时的数量

                        let otherPrizeSinglePutNum = 0;
                        otherPrizeStatistic.forEach((element) => {
                            if (element.patchId === patchId) {
                                otherPrizeSinglePutNum += Number(element.singlePutNum || 0);
                            }
                        });

                        if (otherPrizeSinglePutNum > 0) {
                            diff = diff - otherPrizeSinglePutNum;
                        }

                        // const otherPrizeItem = otherPrizeStatistic.find(
                        //     (ele) => ele.patchId === patchId,
                        // );
                        // if (otherPrizeItem) {
                        //     diff = diff - Number(otherPrizeItem.singlePutNum);
                        // }
                    }

                    const itemMax = Math.floor((diff > 0 ? diff : 0) / Number(singlePutNum));
                    //取数组每个补签卡可用数量除去单人发券数的最小值

                    if (isEmpty(_maxCount) || itemMax < _maxCount) {
                        _maxCount = itemMax;
                    }
                }
            }
        }
        return _maxCount;
    });

    useEffect(() => {
        // 记录当前可用库存
        const allValues = form.getFieldsValue();
        let item;
        for (const key of fullParentName) {
            if (!item) {
                item = allValues[key];
            } else {
                item = item[key];
            }
        }
        if (isEmpty(item)) {
            item = {};
        }
        if (maxCount !== undefined && item.maxCount != maxCount) {
            item.maxCount = maxCount;
            form.setFieldsValue(allValues);
        } else if (maxCount === undefined && item.maxCount !== undefined) {
            item.maxCount = undefined;
            form.setFieldsValue(allValues);
        }
    }, [maxCount, parentName]);

    const stockItem = (
        <FormItem label={pageInfo?.stockName} required {...formItemLayout}>
            <Space direction="vertical">
                <FormItem
                    noStyle
                    name={[...parentName, pageInfo?.stockKey]}
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (isEmpty(value)) {
                                    return Promise.reject(`请填写`);
                                }
                                if (Number(value) != -1 && Number(value) <= 0) {
                                    return Promise.reject(`值必须为大于0正整数`);
                                }
                                if (
                                    isCopy &&
                                    !isEmpty(maxCount) &&
                                    Number(value) > Number(maxCount)
                                ) {
                                    return Promise.reject(`最多可配库存数${maxCount}`);
                                }

                                if (pageType == COUPON_CUSTOM_PAGE_TYPE.CPN_ACT_2_0) {
                                    const cpnList =
                                        form.getFieldValue([...fullParentName, listName]) || [];
                                    let maxPutNum = 0;
                                    for (const item of cpnList) {
                                        maxPutNum =
                                            maxPutNum < Number(item.singlePutNum)
                                                ? Number(item.singlePutNum)
                                                : maxPutNum;
                                    }
                                    if (maxPutNum > Number(value)) {
                                        return Promise.reject(
                                            `库存数至少支持最大单人发放量${maxPutNum}`,
                                        );
                                    }
                                }

                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <InputNumber
                        addonAfter={'份'}
                        precision={0}
                        step={1}
                        min={0}
                        placeholder="请填写"
                        disabled={disabled}
                        onBlur={editOnBlur}
                    ></InputNumber>
                </FormItem>
                <FormItem noStyle name={[...parentName, 'giftBagPutNum']}>
                    {!isEmpty(endNum) ? `剩余库存:${endNum}份` : ''}
                </FormItem>
            </Space>
        </FormItem>
    );

    return (
        <Fragment>
            {stockItem}
            <FormItem name={[...parentName, 'maxCount']} noStyle />
            <FormItem
                label={title}
                name={[...parentName, listName]}
                rules={[
                    // { required: true, message: '请选择补签卡' },
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (isEmpty(value)) {
                                return Promise.reject(`请选择补签卡`);
                            }
                            let errList = [];
                            for (const item of value) {
                                if (isEmpty(item.singlePutNum)) {
                                    errList.push(item.prizeName);
                                }
                            }
                            if (errList.length > 0) {
                                return Promise.reject(`请配置${errList.join('、')}的单次发放数量`);
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
                required
                {...formItemLayout}
            >
                <SelectCouponItem
                    {...props}
                    ref={couponRef}
                    global={global}
                    dispatch={dispatch}
                    extInParams={extInParams}
                    disabled={!isCopy && disabled}
                    otherPrizeStatistic={otherPrizeStatistic}
                    pageType={pageType}
                    form={form}
                    fullParentName={fullParentName}
                    isCopy={isCopy}
                ></SelectCouponItem>
            </FormItem>
        </Fragment>
    );
};

export default SelectCouponItem;
