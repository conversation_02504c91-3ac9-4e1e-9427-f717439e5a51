import { Modal, Form, Radio } from 'antd';
import { useImperativeHandle, useState } from 'react';

export const openChannelOptionItems = [
    { label: '月卡', value: '01' },
    { label: '季卡', value: '02' },
    { label: '年卡', value: '03' },
    { label: '连续包月', value: '04' },
    { label: '连续包季', value: '05' },
    { label: '连续包年', value: '06' },
    { label: '体验版', value: '07' },
];

const formItemLayout = {
    labelCol: {
        flex: '0 0 140px',
    },
    labelAlign: 'right',
    labelWrap: true,
};

const RecommandModal = (props) => {
    const { initRef, onFinish } = props;

    const [form] = Form.useForm();
    const [visible, updateVisible] = useState(false);
    const [recommendItem, updateRecommendItem] = useState();
    useImperativeHandle(initRef, () => ({
        show: ({
            item,
            recommendCycle, // 上一次推荐的周期值，用于回填
        }) => {
            updateVisible(true);
            updateRecommendItem(item);
            // 默认选中可推荐周期的第一个值
            const sort = item?.prizeDeductionProjects?.split?.(',')?.sort();

            form.setFieldsValue({
                ...item,
                currentRecommendCycle:
                    // recommendCycle ||
                    sort?.[0] || '01',
            });
        },
        onClose,
    }));
    const onClose = () => {
        updateVisible(false);
    };

    return (
        <Modal
            title="推荐使用"
            visible={visible}
            onOk={() => {
                form.validateFields().then((values) => {
                    onFinish?.({
                        recommendCycle: values.currentRecommendCycle,
                        recommendPrizeId: recommendItem?.prizeId,
                    });
                    onClose();
                });
            }}
            okText="提交"
            onCancel={onClose}
        >
            <Form form={form} {...formItemLayout}>
                <Form.Item label="券名称" name="prizeName">
                    {recommendItem?.prizeName}
                </Form.Item>
                <Form.Item
                    label={'可推荐周期'}
                    rules={[
                        {
                            required: true,
                            message: '请选择',
                        },
                    ]}
                    name={'currentRecommendCycle'}
                >
                    <Radio.Group>
                        {openChannelOptionItems.map((ele) => {
                            if (recommendItem?.prizeDeductionProjects?.indexOf?.(ele.value) >= 0) {
                                return <Radio value={ele.value}>{ele.label}</Radio>;
                            }
                            return null;
                        })}
                    </Radio.Group>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default RecommandModal;
