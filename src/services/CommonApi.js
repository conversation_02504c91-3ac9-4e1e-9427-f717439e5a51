import { Ajax } from '@/utils/request';
import { request } from 'umi';
import {
    MNG_BIL_URL,
    MNG_SSO_URL,
    MNG_CODE,
    MNG_SSO_OPEN,
    MNG_ALY,
    MNG_AST_URL,
    MNG_PUB_URL,
    MNG_DCR_URL,
} from '@/config/global';

export const checkVersion = async () => {
    const checkUrl = `version.json`;

    return request(checkUrl, { prefix: PUBLIC_PATH });
};

/** 上传文件
 * contentType	T文本	是	01 文档  02 图片
contRemrk	T文本	是	btnFile  当有多个文档时，用来附件时，用来区分文件的名字，英文字母
relaId	T文本	是	888888 业务关联id
relaTable	T文本	是	e_mkt_act  业务类型
file	文件	是	附件
*/
export const uploadFile = async (json) =>
    Ajax({
        prefix: MNG_PUB_URL,
        url: `/pub/v0.1/upload`,
        method: 'post',
        data: json,
    });

export const uploadMngFile = async (json) =>
    <PERSON>({
        prefix: MNG_PUB_URL,
        url: `/pub/v0.1/upload`,
        method: 'post',
        data: json,
    });

export const uploadCertImageFile = async (json) =>
    Ajax({
        url: `/oper/uploadCertImage`,
        method: 'post',
        data: json,
    });

export const exportTableApi = async (json) =>
    Ajax({
        url: `/pub/exportXml`,
        method: 'get',
        data: json,
        responseType: 'blob',
    });

/**
 * 获取标准代码
 */
export const getCodesApi = async (codeType) =>
    Ajax({
        url: `/pub/v0.1/codes/${codeType}`,
        method: 'get',
        data: {},
    });

/**
 * 获取标准代码
 */
export const getMngCodesApi = async (codeType) =>
    Ajax({
        prefix: '/mng-code/code',
        url: `/allInfo/${codeType}`,
        method: 'get',
        data: {},
    });
/**
 * 获取会员策略标准代码
 */
export const getPolicyLabelCodesApi = async (codeType) =>
    Ajax({
        url: `/pub/v0.1/policyLabel/${codeType}`,
        method: 'get',
        data: {},
    });

/**
 * 获取子集标准代码
 */
export const getSecondTypeCodesApi = async (firstType, secondType) =>
    Ajax({
        url: `/pub/v0.1/codes/${firstType}/${secondType}`,
        method: 'get',
        data: {},
    });

/**
 * 获取所有子集标准代码
 */
export const getAllSecondTypeCodesApi = async (firstType) =>
    Ajax({
        prefix: MNG_CODE,
        url: `/code/info/${firstType}`,
        method: 'get',
        data: {},
    });

/**
 * 获取树结构标准代码
 */
export const getTreeCodesApi = async (codeType) =>
    Ajax({
        url: `/pub/v0.1/all-codes/${codeType}`,
        method: 'get',
        data: {},
    });

/**
 * 根据运营商查询城市和站点
 */
export const getCityAndStationByOperIdApi = async (JSON) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/getCityAndStationByOperId`,
        method: 'get',
        data: JSON,
        // timeout: 30000,
    });

/**
 * 运营商站点城市列表（分润规则用）
 */
export const getStationCityListApi = async ({ operId }) =>
    Ajax({
        url: `/ast/station/stationCityList/${operId}`,
        method: 'get',
    });

// 根据城市查场站
export const getOperAndStationByCityApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/getOperAndStationByCity`,
        method: 'get',
        data: json,
    });

// 根据省份查场站
export const getOperAndStationByProvinceApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/getOperAndStationByProvince`,
        method: 'get',
        data: json,
    });

/**
 * 省份和城市查询
 */
export const getProvinceAndCityApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/province-city`,
        method: 'get',
        data: json,
    });

// 查询区
export const getCountryListApi = async (json) =>
    Ajax({
        url: `/pub/v0.1/country-list`,
        method: 'get',
        data: json,
    });

/**
 * 省市区联动查询
 */
export const getAllProvinceAndCityApi = async (json) =>
    Ajax({
        url: `/oper/qryDownAreaInfos`,
        method: 'get',
        data: json,
    });
// 数据权限切割，获取账号下所有省市的接口，区的数据要另外调
export const getAllProvinceCityApi = async (json) =>
    Ajax({
        prefix: MNG_SSO_URL,
        url: `/mng/dataPermission/getCitys`,
        method: 'get',
        data: json,
    });

/**
 * 获取所有城市数据
 */
export const getCityListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/province-city-list`,
        method: 'get',
        data: json,
    });

// 获取场站配置记录
export const getStationRecordListApi = async (json) =>
    Ajax({
        url: `/pub/component/station-config-record/page`,
        method: 'get',
        data: json,
    });

/**
 * 获取站点下拉框
 * operId
 * stationName
 */
export const getStationListApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/v0.1/station-list`,
        method: 'get',
        data: json,
    });

/**
 * 获取可以关联的优惠券
 */
export const getPutCpnListApi = async (json) =>
    Ajax({
        url: `/bil/act/put-cpn-list`,
        method: 'get',
        data: json || {},
    });

/**
 * 活动站点信息导入
 */
export const stationImportApi = async (info) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/stationImport`,
        method: 'post',
        data: info,
    });

// 城市批量导入
export const cityImportApi = async (info) =>
    Ajax({
        url: `/bil/act/common/cityImport`,
        method: 'post',
        data: info,
    });

// 运营商导入
export const operImportApi = async (info) =>
    Ajax({
        url: `/pub/component/channel-config-operator/build-import`,
        method: 'post',
        data: info,
    });

// 运营商导入模块使用的列表接口
export const getOperListApi = async (info) =>
    Ajax({
        url: `/pub/component/channel-config-operator/operators`,
        method: 'get',
        data: info,
    });

/**
 * 导出下载
 */
export const exportXmlApi = async (json) =>
    Ajax({
        url: `/pub/exportXml`,
        method: 'get',
        data: json,
        responseType: 'blob',
    });

// 文件暂存区分页接口
export const getFileCacheListApi = async (json) =>
    Ajax({
        url: `/file/manage/page`,
        method: 'get',
        data: json,
    });

// 文件暂存区分页接口
export const downloadFileCacheApi = async (json) =>
    Ajax({
        url: `/file/manage/download`,
        method: 'get',
        data: json,
    });

// 导出文件到暂存区
export const uploadFileCacheApi = async (json) =>
    Ajax({
        url: `/file/upload/uploadFile`,
        method: 'POST',
        data: json,
    });
export const uploadFileMngCacheApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/export/excel`,
        method: 'POST',
        data: json,
    });
export const uploadFileDcrCacheApi = async (json) =>
    Ajax({
        prefix: MNG_DCR_URL,
        url: `/export/excel`,
        method: 'POST',
        data: json,
    });
// 获取文件暂存区内容
export const getUploadFileCacheListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/file/page`,
        method: 'get',
        data: json,
    });
// 获取系统参数
export const getConfigParamsApi = async (json) =>
    Ajax({
        prefix: MNG_PUB_URL,
        url: `/config/v0.1/param`,
        method: 'get',
        data: json,
    });

// 下载文件暂存区内容
export const downloadUploadFileCacheListApi = async (json) =>
    Ajax({
        url: `/file/upload/download`,
        method: 'get',
        data: json,
    });

// 系统账号
export const getSystemAccountListApi = async (json) =>
    Ajax({
        url: `/pub/0.1/get-system-account`,
        method: 'get',
        data: json,
    });

// 查询相关配置页面站点的配置列表信息
export const getStationScopeListApi = async (json) =>
    Ajax({
        prefix: MNG_BIL_URL,
        url: `/scope/station-scope-page`,
        method: 'post',
        data: json,
    });

// 保存相关配置页面的站点信息
export const saveStationScopeInfoApi = async (json) =>
    Ajax({
        prefix: MNG_BIL_URL,
        url: `/scope/station-scope-save`,
        method: 'post',
        data: json,
        requestType: 'json',
    });

/**
 * 保存推广码
 */
export const addPromotionCodeApi = async (options) =>
    Ajax({
        prefix: MNG_BIL_URL,
        url: `/promotionCode/add-promotion-code`,
        method: 'post',
        data: options,
        requestType: 'json',
    });
/**
 * 查询推广码
 */
export const getPromotionCodeListApi = async (json) =>
    Ajax({
        prefix: MNG_BIL_URL,
        url: `/promotionCode/promotion-code-list`,
        method: 'get',
        data: json,
    });
/**
 * 保存推广码
 */
export const delPromotionCodeApi = async (id) =>
    Ajax({
        prefix: MNG_BIL_URL,
        url: `/promotionCode/delete/${id}`,
        method: 'post',
        data: {},
        requestType: 'json',
    });

export const getFilingTitleApi = async (json = {}) =>
    Ajax({
        prefix: MNG_SSO_OPEN,
        url: `/filing/title`,
        method: 'get',
        data: json,
    });

// 获取第三方渠道所有站点信息
export const getThirdChannelAllStationApi = async (json) =>
    Ajax({
        url: `/pub/v0.1/getThirdChannelAllStation`,
        method: 'get',
        data: json,
    });

export const getBiPageUrlByCodeApi = async (pageCode, params = {}) =>
    Ajax({
        prefix: MNG_PUB_URL,
        url: `/bi-page/url/${pageCode}`,
        method: 'post',
        data: params,
    });
