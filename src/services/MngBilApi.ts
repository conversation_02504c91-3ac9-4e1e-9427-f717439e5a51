import { MNG_BIL_URL } from '@/config/global';
import { Ajax } from '@/utils/request';

/**
 * 代充人员分页列表  平台实体人群分页查询
 * @param params
 * @returns
 */
export async function getCrowdDefineQueryPage(
    params: API.GetCrowdDefineQueryPageRequest,
): Promise<API.GetCrowdDefineQueryPageResponse> {
    return Ajax({
        prefix: MNG_BIL_URL,
        url: '/crowd/strategy/cdp-crowd-page',
        method: 'get',
        data: params,
    });
}

export async function getImageManageListPage(
    params: API.PaginationRequest,
): Promise<API.PaginationResponse<any>> {
    return Ajax({
        prefix: MNG_BIL_URL,
        url: '/market/image/v0.1/list',
        method: 'get',
        data: params,
    });
}
export async function saveImageManagePage(params: {
    imgName: string;
    imgUrl: string;
    id?: number;
}): Promise<API.PaginationResponse<any>> {
    return Ajax({
        prefix: MNG_BIL_URL,
        url: '/market/image/v0.1/save',
        method: 'post',
        data: params,
    });
}
export async function deleteImageManagePage(params: {
    id: number;
}): Promise<API.PaginationResponse<any>> {
    return Ajax({
        prefix: MNG_BIL_URL,
        url: '/market/image/v0.1/delete',
        method: 'post',
        data: params,
    });
}
