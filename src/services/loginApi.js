import { Ajax } from '@/utils/request';
const mngPrefix = '/mng-ssoapi';

export const getLoginInfoApi = async (json) =>
    Ajax({
        url: `/open/getLoginInfo`,
        method: 'get',
        data: json,
    });

export const getDynamicmenuApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/oauth/getUserMenuTree`,
        method: 'post',
        data: json,
        requestType: 'json',
    });

export const getHomePathApi = async (json) =>
    Ajax({
        url: `/login-info`,
        method: 'get',
        data: json,
    });

export const checkAuthorizeApi = async (code) =>
    Ajax({
        prefix: mngPrefix,
        url: `/oauth/code?code=${code}`,
        method: 'post',
        data: { code },
        requestType: 'json',
    });

export const getUserInfoApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/oauth/getUserInfo`,
        method: 'post',
        data: json,
        requestType: 'json',
    });

/**
 * 登出接口
 */
export const sendLogoutApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/oauth/logout`,
        method: 'get',
        data: json,
    });

/**
 * 登出地址接口
 */
export const getLogouUrlApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/openapi/mng/getLogoutUrl`,
        method: 'get',
        data: json,
    });

/**
 * B端用户信息根据账户名模糊查询
 */
export const getUserListByName = async (name) =>
    Ajax({
        prefix: mngPrefix,
        url: '/webapi/mng/user/getUserListByName',
        method: 'get',
        data: { accountNameLike: name },
    });
/**
 * 大屏登录校验
 */
export const psdLoginApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/webapi/mng/psdLogin`,
        method: 'post',
        data: json,
        requestType: 'json',
    });

/**
 * B端用户根据账户查询组织结构
 */
export const getOrgsByAccount = async (accountName) =>
    Ajax({
        prefix: mngPrefix,
        url: '/webapi/mng/user/getOrgs',
        method: 'get',
        data: { accountName },
    });

/**
 * 查询用户后端权限
 */
export const queryAllPemissionsApi = async () =>
    Ajax({
        prefix: mngPrefix,
        url: '/webapi/mng/user/queryAllPemissions',
        method: 'get',
        data: {},
    });
