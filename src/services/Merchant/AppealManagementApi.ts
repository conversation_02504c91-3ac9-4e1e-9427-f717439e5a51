import { MNG_PUB_URL } from '@/config/global';
import { Ajax } from '@/utils/request';

/**
 * 申诉列表查询
 * @param params
 * @returns
 */
export async function queryAppealListApi(
    params: API.PaginationRequest & {
        appealNo?: string;
        userPhone?: string;
        appealStatus?: string;
        appealType?: string;
        priority?: string;
        merchantId?: string;
        startTime?: string;
        endTime?: string;
    },
): Promise<API.PaginationResponse<API.AppealListItem>> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/list',
        method: 'post',
        data: params,
    });
}

/**
 * 申诉详情查询
 * @param appealNo 申诉单号
 * @returns
 */
export async function getAppealDetailApi(
    appealNo: string,
): Promise<API.CommonResponse<API.AppealDetail>> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: `/appeal/detail/${appealNo}`,
        method: 'get',
    });
}

/**
 * 更新申诉状态
 * @param params
 * @returns
 */
export async function updateAppealStatusApi(params: {
    appealNo: string;
    status: string;
    remark?: string;
}): Promise<API.CommonBooleanResponse> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/status',
        method: 'post',
        data: params,
    });
}

/**
 * 申诉处理
 * @param params
 * @returns
 */
export async function handleAppealApi(params: {
    appealNo: string;
    handleResult: string;
    handleRemark: string;
    refundAmount?: number;
}): Promise<API.CommonBooleanResponse> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/handle',
        method: 'post',
        data: params,
    });
}

/**
 * 申诉统计
 * @param params
 * @returns
 */
export async function getAppealStatisticsApi(params?: {
    startTime?: string;
    endTime?: string;
}): Promise<API.CommonResponse<API.AppealStatistics>> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/statistics',
        method: 'get',
        data: params,
    });
}

/**
 * 导出申诉列表
 * @param params
 * @returns
 */
export async function exportAppealListApi(params: {
    appealNo?: string;
    userPhone?: string;
    appealStatus?: string;
    appealType?: string;
    priority?: string;
    merchantId?: string;
    startTime?: string;
    endTime?: string;
}): Promise<any> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/export',
        method: 'get',
        data: params,
    });
}

/**
 * 申诉回复
 * @param params
 * @returns
 */
export async function replyAppealApi(params: {
    appealNo: string;
    replyContent: string;
    attachments?: string[];
}): Promise<API.CommonBooleanResponse> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: '/appeal/reply',
        method: 'post',
        data: params,
    });
}

/**
 * 获取申诉回复历史
 * @param appealNo
 * @returns
 */
export async function getAppealReplyHistoryApi(
    appealNo: string,
): Promise<API.CommonResponse<API.AppealReply[]>> {
    return Ajax({
        prefix: MNG_PUB_URL,
        url: `/appeal/reply/history/${appealNo}`,
        method: 'get',
    });
}
