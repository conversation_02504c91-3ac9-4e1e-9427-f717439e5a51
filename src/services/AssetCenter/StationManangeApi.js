import { MNG_AST_URL, MNG_DCR_URL, BASE_URL, MNG_ALY } from '@/config/global';
import { Ajax } from '@/utils/request';

const mngPrefix = MNG_AST_URL;
export const getStationPriceApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/station-price/list`,
        method: 'get',
        data: json,
    });
/**
 * 站点分页接口 （20250208疑似废弃）

 */
export const getStationListApiPath = `/ast/station/manage/page`;
export const getStationListApi = async (json) =>
    Ajax({
        url: getStationListApiPath,
        method: 'post',
        data: json,
    });
// 新场站管理列表接口
export const getStationPageApi = async (json) =>
    Ajax({
        prefix: mngPrefix,
        url: `/station/manage/page`,
        method: 'post',
        data: json,
    });
// 场站渠道列表
export const getChannelSelectListsApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/select-list`,
        method: 'post',
        data: json,
    });

// 站点查询参数枚举
export const getStationManageOptionsApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/init`,
        method: 'get',
        data: json,
    });

// 【场站管理】站点导出至暂存区
export const exportStationListApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/page-export`,
        method: 'post',
        data: json,
    });

// 【场站管理】精品站名称接口
export const getStationGoodFlagNameApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/stationGoodFlagName`,
        method: 'get',
        data: json,
    });

// 【场站管理】枪价格获取历史记录分页列表
export const getStationHistoryListApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station-price/history-list`,
        method: 'get',
        data: json,
    });

// 【场站管理】场站监控指标
export const getStationMonitorStatApi = async (json) =>
    Ajax({
        prefix: MNG_DCR_URL,
        url: `/station-monitor/stat`,
        method: 'get',
        data: json,
    });

/**
 * 站点批量操作
 */
// 【场站管理】场站下线
export const stationOffLineApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/stationOffLine`,
        method: 'post',
        data: json,
    });

// 【场站管理】场站商家优惠
export const stationManageDiscountOpenOrCloseApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/stationManageDiscountOpenOrClose`,
        method: 'post',
        data: json,
    });

// 【场站管理】标签批量设置
export const stationLabelRelSaveApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/stationLabelRelSave`,
        method: 'post',
        data: json,
    });

// 【场站管理】批量设置、取消精品站
export const updateStationGoodFlagApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/updateStationGoodFlag`,
        method: 'post',
        data: json,
    });

// 【场站管理】标签模板下载
export const stationDownloadTemplateApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/labelTemplate/down`,
        method: 'get',
        data: json,
        fileName: '场站标签批量导入模板.xlsx',
    });

// 【场站管理】标签批量导入失败详情下载
export const stationDownloadFailRecordApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/load/failRecord/${json.serialNo}`,
        method: 'get',
        data: json,
        fileName: '场站标签批量导入失败数据.xlsx',
    });

// 【站点详情】基础信息
export const getStationBaseDetailApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/detail`,
        method: 'get',
        data: json,
    });

// 【站点详情】充电价格
export const getStationPriceFeeApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/priceFee`,
        method: 'get',
        data: json,
    });

// 【站点详情】站点活动
export const getStationActivityApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/activity`,
        method: 'get',
        data: json,
    });

// 【站点详情】停车以及附加费
export const getStationParkDelayFeeApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/parkDelayFee`,
        method: 'get',
        data: json,
    });

// 【站点详情】标签与路书
export const getStationLabelAndGuideApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/labelAndGuide`,
        method: 'get',
        data: json,
    });

// 【站点编辑】编辑信息展示
export const getStationInfoEditApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/edit`,
        method: 'get',
        data: json,
    });

// 【站点编辑】站点信息保存
export const saveStationInfoApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/ast/station/manage/base/save`,
        method: 'post',
        data: json,
    });

// 【站点编辑】批量设置公告
export const batchSetStationNoticeApi = async (json) =>
    Ajax({
        url: `/ast/station/manage/batchSetStationNotice`,
        method: 'post',
        data: json,
    });

// 站点废弃检测
export const checkStationManageAbandonApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/stationManageCheckAbandon`,
        method: 'post',
        data: json,
    });

// 【公告模板】公告类型列表
export const getStationNoticeTypeListApi = async (json) =>
    Ajax({
        url: `/ast/station/notice/template/typeList`,
        method: 'get',
        data: json,
    });

// 【公告模板】公告模板分页
export const getStationNoticeListApi = async (json) =>
    Ajax({
        url: `/ast/station/notice/template/page`,
        method: 'post',
        data: json,
    });

// 【公告模板】新增或修改公告模板
export const saveStationNoticeTemplateApi = async (json) =>
    Ajax({
        url: `/ast/station/notice/template/saveOrUpdate`,
        method: 'post',
        data: json,
    });

// 【公告模板】公告模板删除
export const deleteStationNoticeTemplateApi = async (json) =>
    Ajax({
        url: `/ast/station/notice/template/delete/${json.id}`,
        method: 'get',
    });

// 【电桩管理】列表查询
export const getPileManageApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/pile/manage/page`,
        method: 'get',
        data: json,
    });
// 更新贴码状态
export const updateCodeStatusApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/updateCodeStatus`,
        method: 'post',
        data: json,
    });
// 【场站管理】贴码状态模板下载
export const stationDownloadCodeStatusTemplateApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/codeStatusTemplate/down`,
        method: 'get',
        data: json,
        fileName: '贴码状态批量导入模板.xlsx',
    });
// 【场站管理】贴码状态批量导入失败详情下载
export const stationDownloadCodeStatusFailRecordApi = async (json) =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/station/manage/codeStatus/failRecord/${json.serialNo}`,
        method: 'get',
        data: json,
        fileName: '场站贴码状态批量导入失败数据.xlsx',
    });
