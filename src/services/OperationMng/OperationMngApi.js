import { Ajax } from '@/utils/request';
import { MNG_ALY, MNG_DEFRAY_URL, BASE_URL, MNG_AST_URL } from '@/config/global';

// 运营商管理选项列表
export const getOperatorListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.1/operators`,
        method: 'get',
        data: json,
    });

// 运营商管理选项列表2.0，简化1.0的字段，仅供账单管理页面运营商选择控件使用
export const getOperatorList2Api = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/station/config/v0.2/operators`,
        method: 'get',
        data: json,
    });
// 运营商管理所有列表
export const getOperatorAllListApi = async () =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/build/v0.1/getOperatorInfo`,
        method: 'get',
    });
// 运营商管理选项列表
export const getOperatorListByCityApi = async (city) =>
    Ajax({
        url: `/pub/v0.1/operatorList`,
        method: 'get',
        data: { city },
    });

// 运营商管理界面列表数据查询
export const qryXdtOperatorInfosPath = '/oper/qryXdtOperatorInfos';
export const qryXdtOperatorInfosApi = async (json) =>
    Ajax({
        url: qryXdtOperatorInfosPath,
        method: 'get',
        data: json,
    });

// 运营商管理界面初始化
export const xdtOperatorInitApi = async (json) =>
    Ajax({
        url: `/oper/xdtOperatorInit`,
        method: 'get',
        data: json,
    });
// 运营商停运/恢复
export const operStopAndStartApi = async (json) =>
    Ajax({
        url: `/oper/operStopAndStart`,
        method: 'post',
        data: json,
    });
// 进件管理界面下拉框初始化
export const saveInitApi = async (json) =>
    Ajax({
        url: `/oper/saveInit`,
        method: 'get',
        data: json,
    });

// 查询进件审核列表
export const queryInputInfosApi = async (json) =>
    Ajax({
        url: `/indirect/queryInputInfos`,
        method: 'get',
        data: json,
    });

// 查询进件审核详细信息
export const qryInputDetailApi = async (json) =>
    Ajax({
        url: `/indirect/qryInputDetail`,
        method: 'get',
        data: json,
    });

// 进件管理界面下拉框初始化
export const indirectInitApi = async (json) =>
    Ajax({
        url: `/indirect/indirectInit`,
        method: 'get',
        data: json,
    });

// 运营商保存
export const saveOperatorsApi = async (json) =>
    Ajax({
        url: `/oper/saveOperators`,
        method: 'post',
        data: json,
    });
// 身份证护照上传
export const uploadCertImageApi = async (json) =>
    Ajax({
        url: `/oper/uploadCertImage`,
        method: 'post',
        data: json,
    });

// 查询运营商详情
export const qryXdtOperatorInfoDetailApi = async (json) =>
    Ajax({
        url: `/oper/qryXdtOperatorInfoDetail`,
        method: 'get',
        data: json,
    });

// 根据上级区域编码查询下级区域信息
export const qryDownAreaInfosApi = async (json) =>
    Ajax({
        url: `/oper/qryDownAreaInfos`,
        method: 'get',
        data: json,
    });

// mock调试
export const mockApi = async (json) =>
    Ajax({
        url: '/api/test',
        method: 'get',
        data: json,
    });

// 删除运营商项目
export const delProjectApi = async (projectId) =>
    Ajax({
        url: '/oper/oper-elecpurchase-project-del',
        method: 'post',
        data: { projectId },
    });

// 查询父级运营商下的运营商信息
export const qryBuildParentInfoApi = async (json) =>
    Ajax({
        url: `/oper/qryBuildParentInfo`,
        method: 'get',
        data: json,
    });

// 查询默认子运营商编号
export const qryOperSubBuildidApi = async (json) =>
    Ajax({
        url: `/oper/oper-sub-buildid`,
        method: 'get',
        data: json,
    });

// 废弃运营商

export const abandonOrRecoverBuildApi = async (json) =>
    Ajax({
        url: `/oper/abandonOrRecoverBuild`,
        method: 'post',
        data: json,
    });

// 查询运营商开票默认信息
export const getOperInvoiceConfigApi = async (json) =>
    Ajax({
        prefix: MNG_DEFRAY_URL,
        url: `/invoice/get-config`,
        method: 'get',
        data: json,
    });
// 查询运营商开票默认信息
export const getOperOnlineGaodesaasVerifyApi = async (json) =>
    Ajax({
        prefix: BASE_URL,
        url: `/oper/oper-online-gaodesaas-verify`,
        method: 'post',
        data: json,
    });

export const queryBuildServiceConfigApi = async () =>
    Ajax({
        prefix: MNG_AST_URL,
        url: `/oper/queryBuildServiceConfig`,

        // url: `/oper/queryBuildServiceConfig`,

        method: 'get',
    });
