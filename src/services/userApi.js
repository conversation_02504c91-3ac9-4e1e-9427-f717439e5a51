import { <PERSON> } from '@/utils/request';
import { MNG_CUST } from '@/config/global';

export const queryCurrent = async (json) =>
    Ajax({
        url: `/login-user`,
        method: 'get',
        data: json,
    });
export const queryCurrentEnvironment = async (json) =>
    Ajax({
        url: `/open/current-environment`,
        method: 'get',
        data: json,
    });

// 用户导入
export const importUserListApi = async (json) =>
    Ajax({
        prefix: MNG_CUST,
        url: `/cust/import/mobile`,
        method: 'post',
        data: json,
    });

// 用户注册
export const registUserListApi = async (json) =>
    Ajax({
        url: `/bil/act/common/groupCradSendCustRegist`,
        method: 'post',
        data: json,
    });
