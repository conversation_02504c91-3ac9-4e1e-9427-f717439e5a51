import { Ajax } from '@/utils/request';
import { MNG_ALY } from '@/config/global';

// 获取标准代码
export const getIcnBaseCodeApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/base`,
        method: 'get',
        data: json,
    });

// 渠道管理-列表
export const getIcnChannelListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/list`,
        method: 'get',
        data: json,
    });

// 渠道管理-列表
export const saveIcnChannelInfoApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/save`,
        method: 'POST',
        data: json,
    });

// 【渠道信息】配置列表
export const getIcnChannelConfigInfoApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/config-info`,
        method: 'get',
        data: json,
    });

// 【渠道信息】 配置保存
export const saveIcnChannelConfigInfoApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/config-save`,
        method: 'POST',
        data: json,
    });
// 【渠道信息】 配置保存校验高德渠道
export const saveIcnChannelverifyConfigInfoApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/open-staiton-mutex-verify`,
        method: 'POST',
        data: json,
    });
// 【充值记录】渠道充值
export const saveIcnChannelChargeApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/deposit`,
        method: 'get',
        data: json,
    });

// 【充值记录】资产信息
export const getIcnChannelAssetInfoApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/assetInfo`,
        method: 'get',
        data: json,
    });

// 【充值记录】分页
export const getIcnChannelChargeAmtListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/page`,
        method: 'get',
        data: json,
    });

// 【充电记录】分页
export const getIcnChannelChargeEleListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/charge-record-page`,
        method: 'get',
        data: json,
    });

// 【账单管理】分页
export const getIcnChannelBillListApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/bill-page`,
        method: 'get',
        data: json,
    });

// 【账单管理】下载
export const downloadIcnChannelBillApi = async (json) =>
    Ajax({
        url: `/file/upload/downloadByBusiness`,
        method: 'get',
        data: json,
    });

// 【充值记录】渠道初始化接口
export const initIcnChannelApi = async (json) =>
    Ajax({
        prefix: MNG_ALY,
        url: `/manage/channel/v0.1/init`,
        method: 'get',
        data: json,
    });
