.textOverflow() {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
}


.textOverflowMulti(@line: 3, @bg: #fff) {
    position: relative;
    max-height: @line * 1.5em;
    margin-right: -1em;
    padding-right: 1em;
    overflow: hidden;
    line-height: 1.5em;
    text-align: justify;
    &::before {
        position: absolute;
        right: 14px;
        bottom: 0;
        padding: 0 1px;
        background: @bg;
        content: '...';
    }
    &::after {
        position: absolute;
        right: 14px;
        width: 1em;
        height: 1em;
        margin-top: 0.2em;
        background: white;
        content: '';
    }
}

// mixins for clearfix
// ------------------------
.clearfix {
    zoom: 1;
    &::before,
    &::after {
        display: table;
        content: ' ';
    }
    &::after {
        clear: both;
        height: 0;
        font-size: 0;
        visibility: hidden;
    }
}

.text-wrap{
    white-space: pre-wrap;
    word-break: break-all;
    word-wrap: break-word;
}

.table-btn {
    color: @primary-color;
    cursor: pointer;
}

.btn-margin {
    margin-left: 10px;
}

.btn-bar {
    margin: 20px 0;
    .btn-item {
        margin-right: 10px;
    }
}
.text-clamp(@line) when (isnumber(@line)){
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @line;
    word-break: break-all;
    white-space:pre-wrap;
}

.text-clamp-2{
    .text-clamp(@line:2)
}


.text-clamp-6{
    .text-clamp(@line:6)
}




.form-title {
    position: relative;
    margin-bottom: 20px;
    padding-left:12px;
    color: #333;
    font-weight: bold;
    font-size: 20px;
    &::before{
        position: absolute;
        top:50%;
        left:0;
        width:6px;
        height:85%;
        background-color: @primary-color;
        border-radius: 2px;
        transform: translateY(-50%);
        content:'';
    }
}

.card-title {
    position: relative;
    margin-bottom: 20px;
    padding-left:12px;
    color: @primary-color;
    font-size: 14px;
    &::before{
        position: absolute;
        top:50%;
        left:0;
        width:3px;
        height:85%;
        background-color: @primary-color;
        border-radius: 2px;
        transform: translateY(-50%);
        content:'';
    }
}

.form-submit {
    margin: 20px;
    text-align: center;
    .form-btn {
        margin: 0 10px;
    }
    .form-btn-left {
        margin: 0 60px 0 10px;
    }
    .form-btn-right {
        margin: 0 10px 0 60px;
    }
}

.statistics-num{
    // margin-right: 10px;
    font-weight: 400;
    font-size: 28px;
}

.bg_red {
    background-color: #fff1f0;
}

.upload-smaller {
    :global {
        .ant-upload.ant-upload-select-picture-card {
            width: 60px;
            height: 60px;
            margin-right: 0;
            margin-bottom: 0;
        }
        .ant-upload-list-picture-card-container {
            width: 60px;
            height: 60px;
        }
        .ant-space-align-center{
            span {
                svg {
                    width: 18px;
                    height: 22px;
                }
            }
        }
    }
}

.child-table {
    :global {
        .ant-table-thead > tr >th {
            // color: rgb(24, 144, 255) !important;
            background: rgb(208, 232, 255) !important;
        }
        .ant-table-tbody > tr >td {
            // color: rgb(24, 144, 255) !important;
            background: rgb(247, 251, 255) !important;
        }
    }
}

.table-expanded-row-unfixed {
    :global{
        .ant-table-expanded-row-fixed {
            width: auto !important;
            // width: 1408px !important;
            // position: relative !important;
        }
    }
}

.dynamic-add-button {
    position: relative;
    top: 8px;
    color: @primary-color;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s;
  }

.dynamic-delete-button {
    position: relative;
    top: 8px;
    color: #f50;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s;
  }

:global {
    .amap-logo {
        display: none;
    }
    .amap-logo img {
        display: none;
    }
    .ant-pro-basicLayout {
        transform: none;
    }

    .ant-table thead > tr > th {
        font-weight: bold;
    }
    .ant-tabs-nav .ant-tabs-tab {
        padding: 12px 16px;
    }
    .ant-tooltip {
        max-width: none;
    }

    .ant-menu-item .ant-menu-item-only-child {
        color: #fff;
    }

    .ant-layout.ant-layout-has-sider > .ant-layout,
    .ant-layout.ant-layout-has-sider > .ant-layout-content {
        overflow-x: inherit;
    }
    .ant-picker-range{
        width: 100%;
    }
    .ant-descriptions-row > th {
        padding-bottom: 10px;
        font-size: 20px;

    }
    // .ant-descriptions-row > td {
    //     padding-bottom: 36px;
    // }
}
