.btnSpace {
    margin-right: 8px;
}

.cardSpace {
    margin-bottom: 15px;
    :global {
        .ant-card-head-title {
            font-weight: 600;
        }
    }
}

.card {
    :global {
        .ant-card-head-title {
            font-weight: 600;
        }
        .ant-card-head {
            padding: 0;
        }
    }
}

.formItemDetail {
    :global {
        .ant-form-item {
            margin-bottom: 6px;
        }
    }
}

.cardExtra {
    :global {
        .ant-card-extra {
            padding: 0;
        }
    }
}

.table {
    :global {
        .ant-table-body {
            overflow: auto;
        }
        .ant-table-row > td {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
            white-space: nowrap;
        }
        .ant-divider-vertical {
            margin: 0 0;
        }

        .ant-form-item {
            margin-bottom: 0;
        }
    }
}

.scrollTable {
    :global {
        .ant-table-row > td {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
            white-space: nowrap;
        }
        .ant-divider-vertical {
            margin: 0 0;
        }

        .ant-form-item {
            margin-bottom: 0;
        }

        .ant-table-body {
            &::-webkit-scrollbar {
                height: 5px;
            }
            &::-webkit-scrollbar-thumb {
                background: #737372;
                border-radius: 5px;
                -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            }
            &::-webkit-scrollbar-track {
                background: #191f28;
                border-radius: 0;
                -webkit-box-shadow: 0;
            }
        }
    }
}

.leftForm {
    :global {
        .ant-form-item {
            display: flex;
            margin-right: 0;
            margin-bottom: 24px;
            > .ant-form-item-label {
                width: auto;
                min-width: 120px;
                padding-right: 8px;
                line-height: 32px;
                text-align: left;
            }
            .ant-form-item-control {
                line-height: 32px;
            }
        }
        .ant-form-item-control-wrapper {
            flex: 1;
        }
    }
    .submitButtons {
        display: block;
        margin-bottom: 24px;
        white-space: nowrap;
    }
}
.searchForm {
    :global {
        .ant-form-item {
            display: flex;
            margin-right: 0;
            margin-bottom: 24px;
            > .ant-form-item-label {
                width: auto;
                padding-right: 8px;
                line-height: 32px;
                text-align: left;
            }
            .ant-form-item-control {
                line-height: 32px;
            }
        }
        .ant-form-item-control-wrapper {
            flex: 1;
        }
    }
    .submitButtons {
        display: block;
        margin-bottom: 24px;
        white-space: nowrap;
    }
}

.antFormItemInfo {
    :global {
        .ant-form-item {
            display: flex;
            margin-right: 0;
            margin-bottom: 24px;
            > .ant-form-item-label {
                padding-right: 8px;
                line-height: 32px;
            }
            .ant-form-item-control {
                line-height: 32px;
            }
        }
        .ant-form-item-control-wrapper {
            flex: 1;
        }
    }
    .submitButtons {
        display: block;
        margin-bottom: 24px;
        white-space: nowrap;
    }
}

.antFormItemMb {

    :global {
        .ant-form-item {
            margin-bottom: 0;
        }
    }
}

.loading {
    padding-top: 100px;
    text-align: center;
}

.title {
    display: inline-block;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 16px;
    line-height: 30px;
}

.hiddenTop {
    position: absolute;
    top: -3000px;
}
.colorBlue {
    color: blue;
}
.colorRed {
    color: red;
}
.colorGreen {
    color: green;
}
.colorYellow {
    color: #fb9a00;
}
.colorBlack {
    color: #333;
}
.colorDeepBlue {
    color: #24569f;
}
.colorLightBlue {
    color: #0ea5e7;
}

.textRight {
    text-align: right;
}
.utilsFr {
    float: right;
}
.utilsMl10 {
    margin-left: 10px;
}
.utilsPl10 {
    padding-left: 10px;
}
.utilsMt10 {
    margin-top: 10px;
}
.textCenter {
    text-align: center;
}

.titleWeight {
    margin-top: 24px;
    margin-bottom: 16px;
    padding-top: 24px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 16px;
    border-top: 1px solid #e8e8e8;
}
.titleDefault {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 16px;
}
.headerWrapperTitle {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 20px;
}
.bottomBottom {
    border-bottom: 1px solid #e8e8e8;
}

.tabsCard {
    :global {
        .ant-card-head {
            padding: 0 16px;
        }
        .ant-card-body {
            padding: 10px 30px;
        }
    }
}

.text-three{
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    word-break: break-all;
    white-space: wrap;
}

.text-two{
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
}

:global {
    // .ant-drawer-body {
    //     padding: 0;
    // }
    .text-line {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: keep-all;
    }
}
