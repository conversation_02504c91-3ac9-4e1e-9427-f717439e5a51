import { taskForecastDetailApi } from '@/services/Marketing/MessageCenterApi';
import { useRequest } from 'ahooks';
import { useState } from 'react';

let timer: any = null;
const messageCenter = () => {
    const [forecastSuccess, setForecastSuccess] = useState<boolean>(false);
    const [forceTimeout, setForceTimeout] = useState<boolean>(false);
    const [formMode, setFormMode] = useState<'ADD' | 'GOON'>('ADD');
    const [taskForecastKey, setTaskForecastKey] = useState<string>('');
    const [taskId, setTaskId] = useState<string>('');
    const [sendNumber, setSendNumber] = useState<number>(); // 人数预估
    const [refluxFlag, setRefluxFlag] = useState<string>(); // 回流状态
    const [refrshRefluxFlag, setRefrshRefluxFlag] = useState<boolean>(false); // 刷新回流状态

    const forceCountdown = () => {
        if (!timer) {
            timer = setTimeout(() => {
                setForceTimeout(true);
                clearTimeout(timer as any);
                timer = null;
            }, 10000);
        }
    };

    const {
        run: getForecastDetail,
        data: forecastData,
        cancel: cancelForceRequest,
    } = useRequest(
        (params: any) => {
            return taskForecastDetailApi(params);
        },
        {
            manual: true,
            // pollingInterval: 2000,
            onSuccess(res) {
                if (res.ret === 200 && res.data instanceof Array) {
                    const finishFlag = !res.data.some((ele) => !ele.finishFlag);
                    if (finishFlag) {
                        const sendNumber = res.data.reduce((a, b) => a + b.sendNumber, 0);
                        const hasRefluxFlag = !res.data.some((ele) => ele.refluxFlag == '0');
                        if (timer) {
                            clearTimeout(timer);
                            timer = null;
                        }
                        setSendNumber(sendNumber);
                        setRefluxFlag(hasRefluxFlag ? '1' : '0');
                        setForecastSuccess(true);
                        setForceTimeout(false);
                        cancelForceRequest();
                    } else {
                        setForecastSuccess(false);
                    }
                } else {
                    setForecastSuccess(false);
                }
            },
        },
    );

    return {
        forecastSuccess,
        setForecastSuccess,
        forceTimeout,
        setForceTimeout,
        forceCountdown,
        getForecastDetail,
        forecastData,
        cancelForceRequest,
        formMode,
        setFormMode,
        taskForecastKey,
        setTaskForecastKey,
        taskId,
        setTaskId,
        sendNumber,
        setSendNumber,
        refluxFlag,
        setRefluxFlag,
        refrshRefluxFlag,
        setRefrshRefluxFlag,
    };
};

export default messageCenter;
