import { getTaskPkgListApi } from '@/services/Marketing/TaskPackageCenterApi';
import { usePagination } from 'ahooks';
import { useRef } from 'react';

const useTaskPackageList = () => {
    const cacheRef = useRef<{ count: () => void }>();
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: API.EventTaskListRequest) => {
            const response = await getTaskPkgListApi({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    return {
        searchList,
        listData,
        listLoading,
        pagination,
        cacheRef,
    };
};

export default useTaskPackageList;
