import { initMainOperatorsApi } from '@/services/MngAstApi';
import { useState } from 'react';

export default () => {
    const [operatorList, changeOperatorList] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const initOperatorList = async () => {
        if (!loading) {
            try {
                setLoading(true);
                const { data } = await initMainOperatorsApi({ filerXingXingOperator: '1' });
                changeOperatorList(data);
                setLoading(false);
            } catch (error) {
                setLoading(false);
            }
        }
    };

    return {
        operatorList,
        initOperatorList,
    };
};
