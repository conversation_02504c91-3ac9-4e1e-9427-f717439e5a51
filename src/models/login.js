import { routerRedux } from 'dva';
import { stringify } from 'qs';
import { getLoginInfoApi, getDynamicmenuApi, sendLogoutApi } from '@/services/loginApi';
import { getPageQuery } from '@/utils/utils';
import { Switch, Modal } from 'antd';
import { history } from 'umi';
import { SessionStorage } from '@/utils/SessionStorage/index';
import '@/utils/security';

const RSAUtilsItem = window.RSAUtils;

const { confirm } = Modal;
const loginEvent = () =>
    new Promise((resolve, reject) => {
        confirm({
            title: `确定要退出吗？`,
            content: '',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                resolve();
            },
            onCancel() {
                reject();
            },
        });
    });
export default {
    namespace: 'login',

    state: {
        status: null,
        encryptionExponent: '',
        modulus: '',
        loginSuccessUrl: '',
        eunomiaUrl: '',
        isCheckVerifyCode: '0',
        str: '',
        dynamicMenu: JSON.parse(SessionStorage.getItem('dynamicMenu')) || null,
    },

    effects: {
        *getLoginInfo({ payload }, { call, put }) {
            try {
                const rsaKeyResponse = yield call(getLoginInfoApi);
                yield put({
                    type: 'setLoginInfo',
                    payload: rsaKeyResponse,
                });
            } catch (error) {}
        },
        *getDynamicMenu(_, { call, put }) {
            try {
                const {
                    data: { menuList },
                } = yield call(getDynamicmenuApi);
                if (menuList) {
                    yield put({
                        type: 'setDynamicMenu',
                        menu: menuList,
                    });
                }
            } catch (error) {
                console.log(88888, error);
            }
        },

        *login({ payload }, { call, put, select }) {
            const { encryptionExponent, modulus, loginSuccessUrl, isCheckVerifyCode, eunomiaUrl } =
                yield select((_) => _.login);
            if (process.env.NODE_ENV !== 'production') {
                yield put(routerRedux.replace('/'));
                return;
            }
            const raskey = RSAUtilsItem.getKeyPair(encryptionExponent, '', modulus);
            // const raskey = RSAUtilsItem.getKeyPair("010001","","00CAFEE6E5B2D17F5CE43D73242A676CC0C9CD8BADA370F4A1BB35B4F802C3E5249ACFD47AAD5DDA91EAE97C935CEF16FF92511656EFEF0A8BAABE687B0628340F532792620FE62752421E6E94C0CA8A2988703EB880566526D397A91D92F2CBA5240AFAF98577E085A79D0B8862D402DE93819FB8CE2E7A4E82F79E280365798F")

            const loginform = document.createElement('form');
            document.body.appendChild(loginform);

            const j_username = document.createElement('input');
            j_username.type = 'text';
            j_username.name = 'j_username';
            j_username.value = payload.j_username;

            const j_password = document.createElement('input');
            j_password.type = 'password';
            j_password.name = 'j_password';
            j_password.style.display = 'none';
            j_password.value = RSAUtilsItem.encryptedString(raskey, payload.j_password);

            const ori_password = document.createElement('input');
            ori_password.type = 'password';
            ori_password.id = 'i_password';
            ori_password.value = payload.j_password;

            const clientUrl = document.createElement('input');
            clientUrl.type = 'text';
            clientUrl.name = 'clientUrl';
            clientUrl.value = loginSuccessUrl;
            clientUrl.style.display = 'none';

            const urlParams = new URL(window.location.href);
            const params = getPageQuery();
            const { redirect } = params;
            if (redirect) {
                clientUrl.value = redirect;
            }

            loginform.appendChild(j_username);
            loginform.appendChild(j_password);
            loginform.appendChild(ori_password);
            loginform.appendChild(clientUrl);
            if (isCheckVerifyCode) {
                const j_verifycode = document.createElement('input');
                j_verifycode.type = 'text';
                j_verifycode.name = 'j_verifycode';
                j_verifycode.value = payload.j_verifycode;
                j_verifycode.style.display = 'none';
                loginform.appendChild(j_verifycode);
            }

            loginform.method = 'post';
            loginform.action = eunomiaUrl;
            loginform.submit();

            // const rsaKeyResponse = yield call(getLoginInfo);
            // debugger
            // const raskey = RSAUtilsItem.getKeyPair(rsaKeyResponse.encryptionExponent,"",rsaKeyResponse.modulus)
            // // const raskey = RSAUtilsItem.getKeyPair("010001","","00CAFEE6E5B2D17F5CE43D73242A676CC0C9CD8BADA370F4A1BB35B4F802C3E5249ACFD47AAD5DDA91EAE97C935CEF16FF92511656EFEF0A8BAABE687B0628340F532792620FE62752421E6E94C0CA8A2988703EB880566526D397A91D92F2CBA5240AFAF98577E085A79D0B8862D402DE93819FB8CE2E7A4E82F79E280365798F")
            // payload.j_password = RSAUtilsItem.encryptedString(raskey,payload.j_password);
            // const response = yield call(fakeAccountLogin, payload);
            // console.log("开始跳转")
            // // yield put(routerRedux.replace('/'));
            // console.log("跳转完成")

            // yield put({
            //     type: 'changeLoginStatus',
            //     payload: response,
            // });
            //
            // // Login successfully
            // if (response.status === 'ok') {
            //
            //     reloadAuthorized();
            //     const urlParams = new URL(window.location.href);
            //     const params = getPageQuery();
            //     let { redirect } = params;
            //     if (redirect) {
            //         const redirectUrlParams = new URL(redirect);
            //         if (redirectUrlParams.origin === urlParams.origin) {
            //             redirect = redirect.substr(urlParams.origin.length);
            //             if (redirect.startsWith('/#')) {
            //                 redirect = redirect.substr(2);
            //             }
            //         } else {
            //             window.location.href = redirect;
            //             return;
            //         }
            //     }
            //     yield put(routerRedux.replace(redirect || '/'));
            // }
        },

        *logout(_, { call, put }) {
            try {
                yield call(loginEvent);
                yield call(sendLogoutApi, {
                    // redirect_uri: window.location.href
                });
                yield put({
                    type: 'setDynamicMenu',
                    menu: null,
                });
                // history.replace('/user/login');

                return;
            } catch (error) {
                return Promise.reject(error);
            }

            // reloadAuthorized();
            // yield put(
            //     routerRedux.push({
            //         pathname: '/user/login',
            //         search: stringify({
            //             redirect: window.location.href,
            //         }),
            //     }),
            // );
        },
        *goOut(_, { call, put }) {
            try {
                yield put({
                    type: 'setDynamicMenu',
                    menu: null,
                });
                yield call(sendLogoutApi, {
                    // redirect_uri: window.location.href
                });
                // history.replace('/user/login');
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },

    reducers: {
        changeLoginStatus(state, { payload }) {
            return {
                ...state,
                status: payload.status,
                type: payload.type,
            };
        },
        setLoginInfo(state, { payload }) {
            return {
                ...state,
                ...payload,
            };
        },
        setDynamicMenu(state, { menu }) {
            if (menu) {
                SessionStorage.setItem('dynamicMenu', JSON.stringify(menu));
            } else {
                SessionStorage.removeItem('dynamicMenu');
            }
            return {
                ...state,
                dynamicMenu: menu,
            };
        },
    },
};
