import { getBillListApi, detailInfoApi } from '@/services/FinanceManage/BillManageApi';

const cache = {
    namespace: 'cacheModel',
    state: {
        cacheList: [], // 订单文件暂存区列表
        cacheListTotal: 0, // 订单文件暂存区数量

        cacheDetailInfo: undefined, // 暂存详情

        cacheAreaList: [], //通用文件暂存区
        cacheAreaListTotal: 0, // 订单文件暂存区数量
    },
    effects: {
        *getCacheList({ options = '01' }, { call, put, select }) {
            try {
                const {
                    data: { list, total },
                } = yield call(getBillListApi, options);

                yield put({
                    type: 'updateCacheProperty',
                    params: { cacheList: list, cacheListTotal: total },
                });
            } catch (error) {}
        },

        *getCacheDetailInfo({ options }, { call, put, select }) {
            try {
                const { data } = yield call(detailInfoApi, options);

                yield put({
                    type: 'updateCacheProperty',
                    params: { cacheDetailInfo: data },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateCacheProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default cache;
