import { getCouponListApi } from '@/services/Marketing/MarketingNewCouponApi';

const newCouponModel = {
    namespace: 'newCouponModel',
    state: {
        couponMangeList: [], // 券列表
        couponMangeListTotal: 0, // 券列表数量
    },
    effects: {
        *getCouponMangeList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCouponListApi, options);
                yield put({
                    type: 'updateCouponMangeProperty',
                    params: {
                        couponMangeList: records,
                        couponMangeListTotal: total,
                    },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateCouponMangeProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default newCouponModel;
