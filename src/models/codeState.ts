import { useState, useRef } from 'react';
import { getCodes<PERSON>pi, getSecondTypeCodesApi } from '@/services/CommonApi';
import { isEmpty } from '@/utils/utils';
export default () => {
    const [codeInfo, updateCodeInfo] = useState<Record<string, any>>({});
    const codeInfoRef = useRef({});

    const initCode = async (key: string, secondType?: string) => {
        try {
            let requestPro: any = '';
            if (secondType) {
                requestPro = getSecondTypeCodesApi;
            } else {
                requestPro = getCodesApi;
            }
            const {
                data: { list },
            } = await requestPro(key, secondType);

            if (!isEmpty(list)) {
                updateCodeInfoEvent({
                    [key]: list,
                });
            }

            return list;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const updateCodeInfoEvent = (info: Record<string, any[]>) => {
        codeInfoRef.current = {
            ...codeInfoRef.current,
            ...info,
        };
        updateCodeInfo({
            ...codeInfoRef.current,
        });
    };
    return {
        initCode,
        codeInfo,
    };
};
