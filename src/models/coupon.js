import { getCouponListApi } from '@/services/Marketing/MarketingCouponApi';
import {
    getCpnBagListApi,
    getCpnBagDetailApi,
    getCpnBagRecordListApi,
} from '@/services/Marketing/MarketingCouponBagApi';

const couponModel = {
    namespace: 'couponModel',
    state: {
        couponMangeList: [], // 券列表
        couponMangeListTotal: 0, // 券列表数量
        almNum: 0, // 预警数量
        cpnAlmValue: 0, // 告警值

        couponBagList: [], // 券包
        couponBagCount: 0, // 券包数量
        couponBagInfo: undefined, // 券包详情

        // 券包发放记录
        couponBagSendList: [], // 券包发放
        couponBagSendCount: 0, // 券包发放数量
    },
    effects: {
        *getCouponMangeList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0, almNum = 0, cpnAlmValue = 0 },
                } = yield call(getCouponListApi, options);
                yield put({
                    type: 'updateCouponMangeProperty',
                    params: {
                        couponMangeList: records,
                        couponMangeListTotal: total,
                        almNum,
                        cpnAlmValue,
                    },
                });
            } catch (error) {}
        },
        *getCpnBagList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCpnBagListApi, options);
                yield put({
                    type: 'updateCouponMangeProperty',
                    params: { couponBagList: records, couponBagCount: total },
                });
            } catch (error) {}
        },
        *getCpnBagDetail({ options }, { call, put, select }) {
            try {
                const { data } = yield call(getCpnBagDetailApi, options);
                yield put({
                    type: 'updateCouponMangeProperty',
                    params: { couponBagInfo: data },
                });
            } catch (error) {}
        },
        *getCpnBagRecordList({ options }, { call, put, select }) {
            try {
                const {
                    data: { records = [], total = 0 },
                } = yield call(getCpnBagRecordListApi, options);
                yield put({
                    type: 'updateCouponMangeProperty',
                    params: { couponBagSendList: records, couponBagSendCount: total },
                });
            } catch (error) {}
        },
    },
    reducers: {
        updateCouponMangeProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
    },
};
export default couponModel;
