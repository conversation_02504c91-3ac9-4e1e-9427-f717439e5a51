import { useState } from 'react';
export default () => {
    const [pageOption, updatePageOption] = useState<any>({});
    const [formOption, updateFormOption] = useState<any>({});

    const initOptions = {
        current: 1,
        pageSize: 10,
    };

    const changePageOption = (params: any, reset?: boolean) => {
        if (typeof params === 'function') {
            const newParams = params && params();
            if (reset) {
                updatePageOption({
                    ...initOptions,
                    ...newParams,
                });
                return;
            }

            updatePageOption((state: any) => ({
                ...state,
                ...newParams,
            }));

            return;
        }
        if (reset) {
            updatePageOption({
                ...initOptions,
                ...params,
            });
        } else {
            updatePageOption((state: any) => ({
                ...state,
                ...params,
            }));
        }
    };
    const changeFormOption = (params: any, reset?: boolean) => {
        if (typeof params === 'function') {
            const newParams = params && params();
            if (reset) {
                updateFormOption({
                    ...newParams,
                });
                return;
            }

            updateFormOption((state: any) => ({
                ...state,
                ...newParams,
            }));

            return;
        }
        if (reset) {
            updateFormOption({
                ...params,
            });
        } else {
            updateFormOption((state: any) => ({
                ...state,
                ...params,
            }));
        }
    };

    window.tablePersist = {
        pageOption,
        updatePageOption,
        formOption,
        updateFormOption,
    };

    return { pageOption, changePageOption, formOption, changeFormOption };
};
