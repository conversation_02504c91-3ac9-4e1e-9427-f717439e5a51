import { message } from 'antd';
import {
    getPutCpnListApi,
    getCodesApi,
    getSecondTypeCodesApi,
    getAllSecondTypeCodesApi,
    getCityListApi,
    getAllProvinceCityApi,
    exportXmlApi,
    exportTableApi,
    getTreeCodesApi,
    getSystemAccountListApi,
    getMngCodesApi,
    getConfigParamsApi,
} from '@/services/CommonApi';
import { LOGIN_ERROR_CODE } from '@/config/declare';

import { BASE_URL } from '@/config/global';
import { getOperatorListApi, getOperatorList2Api } from '../services/OperationMng/OperationMngApi';
import {
    queryEnterpriseListApi,
    queryEnterpriseChildListApi,
} from '@/services/Marketing/MarketingWeekCardApi';
import { resolve } from 'eslint-import-resolver-webpack';

let cityHandler = null;
const getCityList = async () => {
    if (!cityHandler) {
        cityHandler = new Promise((resolve, reject) => {
            // getAllProvinceCityApi()
            getCityListApi()
                .then((data) => {
                    resolve(data);
                    cityHandler = null;
                })
                .catch((err) => {
                    reject(err);
                    cityHandler = null;
                });
        });
    }
    return cityHandler;
};

const GlobalModel = {
    namespace: 'global',
    state: {
        pageInit: {}, // 页面的缓存筛选分页数据  form和state

        codeInfo: {}, // 标准代码
        configParams: {}, // 系统参数

        collapsed: false,
        notices: [],
        operatorList: [], // 运营商列表
        operatorList2: [], // 运营商列表
        stationList: [], // 站点下拉列表
        cpnList: [], // 奖品优惠券列表
        billTransChannelList: [], // 账单交易渠道
        billTransTypeList: [], // 账单交易类型
        cpnOwnerOptions: [], // 优惠券所属方
        verifyChannelOptions: [], //核销渠道
        channelOptions: [], // 投放渠道
        actStateOptions: [], // 活动状态下拉项
        platFormActStateOptions: [], // 平台活动状态下拉项
        renewState: [], // soc: 01未恢复 02主动恢复 03手动恢复
        payType: [], // 支付渠道

        bearOptions: [], // 承担方下拉项

        cityOptions: [], // 城市树列表数据

        invoiceAccessModeOptions: [], // 开票方式
        invoiceTypeCodeOptions: [], // 发票类型
        invoiceStatusOptions: [], // 开票状态

        cityList: [], // 城市列表的原始数据，避免重复请求

        custLabelTypeList: [], //用户人群树标准代码

        enterpriseList: [], //大客户列表
        enterpriseChildList: [],

        systemAccountList: [], // 系统账号列表
    },
    effects: {
        /**
         * 运营商管理选项列表2.0，简化1.0的字段，仅供账单管理页面运营商选择控件使用
         */
        *getEnterpriseList({ payload }, { call, put, select }) {
            try {
                const { data } = yield call(queryEnterpriseListApi, payload);
                yield put({
                    type: 'updateEnterpriseList',
                    list: data,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *queryEnterpriseChildList({ options }, { call, put, select }) {
            try {
                const { data } = yield call(queryEnterpriseChildListApi, options);

                yield put({
                    type: 'updateGlobalProperty',
                    params: { enterpriseChildList: data },
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 通用导出
         */
        *exportXml(
            {
                origin = window.location.origin,
                methodUrl = ``,
                options = {},
                columnsStr = [],
                fileName = 'tabel',
                fileType = 'xls',
            },
            { call, put, select },
        ) {
            try {
                const params = {};
                const metodsOptions = [];
                for (const key in options) {
                    if (options.hasOwnProperty(key)) {
                        const element = options[key];
                        if (element) {
                            metodsOptions.push({
                                key,
                                value: element,
                            });
                        }
                    }
                }
                const formatOptions = (optionList) =>
                    optionList.map((ele) => `${ele.key}=${ele.value}`);
                const dataUrl = encodeURIComponent(
                    `${origin}${BASE_URL}${methodUrl}?${formatOptions(metodsOptions).join('&')}`,
                );

                params.dataUrl = dataUrl;

                const columnsStrList = columnsStr.map((ele) => {
                    let columnsValue = '';
                    if (typeof ele?.value === 'string') {
                        columnsValue = ele.value;
                    } else if (
                        typeof ele.value === 'object' &&
                        ele?.value?.props &&
                        ele?.value?.props?.children
                    ) {
                        const { children } = ele.value.props;
                        for (const item of children) {
                            if (typeof item === 'string') {
                                columnsValue = item;
                                break;
                            }
                        }
                    }
                    return `${ele.key};${columnsValue}`;
                });

                params.columnsStr = columnsStrList.join(',');

                params.fileName = fileName;

                params.fileType = fileType;

                yield call(exportXmlApi, params);
            } catch (error) {
                console.log(33333333333, error);
            }
        },
        /**
         * 设置分页信息
         */
        *setPageInit({ pathname, info }, { call, put, select }) {
            try {
                yield put({
                    type: 'updatePageInit',
                    pathname,
                    info,
                });
            } catch (error) {}
        },
        *initCode({ code }, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, code);
                yield put({
                    type: 'updateCode',
                    code,
                    list,
                });
            } catch (error) {}
        },
        *initMngCode({ code }, { call, put, select }) {
            try {
                const { data: list } = yield call(getMngCodesApi, code);
                yield put({
                    type: 'updateCode',
                    code,
                    list,
                });
            } catch (error) {}
        },
        *initConfigParams({}, { call, put, select }) {
            try {
                const { data } = yield call(getConfigParamsApi);
                yield put({
                    type: 'updateGlobalProperty',
                    params: { configParams: data },
                });
            } catch (error) {}
        },
        *initSecondTypeCode({ firstType, secondType }, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getSecondTypeCodesApi, firstType, secondType);
                yield put({
                    type: 'updateCode',
                    code: `${firstType}/${secondType}`,
                    list,
                });
            } catch (error) {
                console.log(error);
            }
        },
        *initAllSecondTypeCode({ firstType }, { call, put, select }) {
            try {
                const { data } = yield call(getAllSecondTypeCodesApi, firstType);
                yield put({
                    type: 'updateCode',
                    code: `${firstType}/ALL_CHILDREN`,
                    list: data,
                });
            } catch (error) {
                console.log(error);
            }
        },
        *initCustLabelTypeList({ payload }, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getTreeCodesApi, 'custLabelType');
                yield put({
                    type: 'updateCustLabelTypeList',
                    list,
                });
            } catch (error) {
                return Promise.reject(error);
            }
        },
        /**
         * 获取运营商列表
         */
        *getOperatorList({ options }, { call, put, select }) {
            try {
                const { data: list } = yield call(getOperatorListApi, options);
                yield put({
                    type: 'updateOperatorList',
                    list,
                    tatal: list?.length,
                });
            } catch (error) {}
        },
        /**
         * 运营商管理选项列表2.0，简化1.0的字段，仅供账单管理页面运营商选择控件使用
         */
        *getOperatorList2({ options }, { call, put, select }) {
            try {
                const { data: list = [] } = yield call(getOperatorList2Api, options);
                yield put({
                    type: 'updateOperatorList2',
                    list,
                    tatal: list?.length,
                });
            } catch (error) {}
        },

        /**
         * 获取站点下拉列表
         */
        *getStationList({ payload }, { call, put, select }) {
            try {
                const { data: stationList } = yield call(getOperatorListApi, payload);
                yield put({
                    type: 'updateStationList',
                    list: stationList,
                });
            } catch (error) {}
        },

        /**
         * 获取优惠券列表
         */
        *getCpnList({ options }, { call, put, select }) {
            try {
                const {
                    data: { cpnList },
                } = yield call(getPutCpnListApi, options);
                yield put({
                    type: 'updateCpnList',
                    cpnList,
                });
            } catch (error) {}
        },

        /**
         * 账单交易渠道
         */
        *getBillTransChannelList(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'billTransChannel');

                yield put({
                    type: 'updateBillTransChannelList',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 账单交易类型
         */
        *getBillTransTypeList(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'billTransType');

                yield put({
                    type: 'updateBillTransTypeList',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 优惠券所属方
         */
        *initCpnOwnerOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'cpnOwner');

                yield put({
                    type: 'updateCpnOwnerOptions',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 核销渠道
         */
        *initVerifyChannelOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getTreeCodesApi, 'prizeChannel');

                yield put({
                    type: 'updateVerifyChannelOptions',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 广告投放渠道
         */
        *initChannelOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'actChannel');

                yield put({
                    type: 'updateChannelOptions',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 分销明细-渠道 distributionChannel
         */
        *initDistributionChannelOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'distributionChannel');

                yield put({
                    type: 'updateDistributionChannelOptions',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 活动状态下拉项
         */
        *initActStateOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'actState');

                yield put({
                    type: 'updateActStateOptions',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 活动状态下拉项
         */
        *initPlatFormActStateOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'platFormActState');

                yield put({
                    type: 'updatePlatFormActStateOptions',
                    list: list || [],
                });
            } catch (error) {}
        },
        /**
         * 承担方下拉项
         */
        *initBearOptions(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'bearObject');

                yield put({
                    type: 'updateBearOptions',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 城市列表数据源
         */
        *initCityList({ callback }, { call, put, select }) {
            try {
                const { data: areaList } = yield call(getCityList);

                yield put({
                    type: 'updateGlobalProperty',
                    params: { cityList: areaList },
                });
                callback && callback(areaList);
            } catch (error) {}
        },

        /**
         * 城市树列表数据
         */
        *initCityOptions(_, { call, put, select }) {
            try {
                const { data: areaList } = yield call(getCityListApi);
                yield put({
                    type: 'updateGlobalProperty',
                    params: { cityList: areaList },
                });

                const list = [];
                const formatCityItem = (data) => {
                    const temps = [];
                    for (const item of data) {
                        temps.push({
                            key: item.areaCode,
                            title: item.areaName,
                            value: item.areaCode,
                        });
                    }
                    return temps;
                };
                for (const item of areaList) {
                    list.push({
                        key: item.areaCode,
                        title: item.areaName,
                        value: item.areaCode,
                        children: item.cityList ? formatCityItem(item.cityList) : [],
                    });
                }
                yield put({
                    type: 'updateCityOptions',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 开票方式
         */
        *getInvoiceAccessModeList(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'invoiceAccessMode');

                yield put({
                    type: 'updateInvoiceAccessModeList',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 发票类型
         */
        *getInvoiceTypeCodeList(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'invoiceTypeCode');

                yield put({
                    type: 'updateInvoiceTypeCodeList',
                    list: list || [],
                });
            } catch (error) {}
        },

        /**
         * 开票状态
         */
        *getInvoiceStatusList(_, { call, put, select }) {
            try {
                const {
                    data: { list },
                } = yield call(getCodesApi, 'invoiceStatus');

                yield put({
                    type: 'updateInvoiceStatusList',
                    list: list || [],
                });
            } catch (error) {}
        },
        // 系统账号
        *getSystemAccountList(_, { call, put, select }) {
            try {
                const {
                    data: { accountList },
                } = yield call(getSystemAccountListApi, {});
                yield put({
                    type: 'updateGlobalProperty',
                    params: {
                        systemAccountList: accountList,
                    },
                });
            } catch (error) {}
        },

        *clearNotices({ payload }, { put, select }) {
            //   yield put({
            //     type: 'saveClearedNotices',
            //     payload,
            //   });
            //   const count = yield select(state => state.global.notices.length);
            //   const unreadCount = yield select(
            //     state => state.global.notices.filter(item => !item.read).length,
            //   );
            //   yield put({
            //     type: 'user/changeNotifyCount',
            //     payload: {
            //       totalCount: count,
            //       unreadCount,
            //     },
            //   });
        },

        *changeNoticeReadState({ payload }, { put, select }) {
            //   const notices = yield select(state =>
            //     state.global.notices.map(item => {
            //       const notice = { ...item };
            //       if (notice.id === payload) {
            //         notice.read = true;
            //       }
            //       return notice;
            //     }),
            //   );
            //   yield put({
            //     type: 'saveNotices',
            //     payload: notices,
            //   });
            //   yield put({
            //     type: 'user/changeNotifyCount',
            //     payload: {
            //       totalCount: notices.length,
            //       unreadCount: notices.filter(item => !item.read).length,
            //     },
            //   });
        },
    },
    reducers: {
        updateGlobalProperty(state, { params }) {
            return {
                ...state,
                ...params,
            };
        },
        updatePageInit(state, { pathname, info }) {
            return {
                ...state,
                pageInit: {
                    ...state.pageInit,
                    [pathname]: info,
                },
            };
        },
        updateCode(state, { code, list }) {
            return {
                ...state,
                codeInfo: {
                    ...state.codeInfo,
                    [code]: list,
                },
            };
        },
        updateCustLabelTypeList(state, { list }) {
            return {
                ...state,
                custLabelTypeList: list,
            };
        },
        updateOperatorList(state, { list }) {
            return {
                ...state,
                operatorList: list,
            };
        },
        updateOperatorList2(state, { list }) {
            return {
                ...state,
                operatorList2: list,
            };
        },
        updateEnterpriseList(state, { list }) {
            return {
                ...state,
                enterpriseList: list,
            };
        },
        updateStationList(state, { list }) {
            return {
                ...state,
                stationList: list,
            };
        },
        updateCpnList(state, { cpnList }) {
            return {
                ...state,
                cpnList,
            };
        },
        updateBillTransChannelList(state, { list }) {
            return {
                ...state,
                billTransChannelList: list,
            };
        },
        updateBillTransTypeList(state, { list }) {
            return {
                ...state,
                billTransTypeList: list,
            };
        },
        updateCpnOwnerOptions(state, { list }) {
            return {
                ...state,
                cpnOwnerOptions: list,
            };
        },
        updateVerifyChannelOptions(state, { list }) {
            return {
                ...state,
                verifyChannelOptions: list,
            };
        },
        updateChannelOptions(state, { list }) {
            return {
                ...state,
                channelOptions: list,
            };
        },
        updateDistributionChannelOptions(state, { list }) {
            return {
                ...state,
                channelOptions: list,
            };
        },
        updateActStateOptions(state, { list }) {
            return {
                ...state,
                actStateOptions: list,
            };
        },

        updatePlatFormActStateOptions(state, { list }) {
            return {
                ...state,
                platFormActStateOptions: list,
            };
        },
        updateBearOptions(state, { list }) {
            return {
                ...state,
                bearOptions: list,
            };
        },
        updateCityOptions(state, { list }) {
            return {
                ...state,
                cityOptions: list,
            };
        },
        updateInvoiceAccessModeList(state, { list }) {
            return {
                ...state,
                invoiceAccessModeOptions: list,
            };
        },
        updateInvoiceTypeCodeList(state, { list }) {
            return {
                ...state,
                invoiceTypeCodeOptions: list,
            };
        },
        updateInvoiceStatusList(state, { list }) {
            return {
                ...state,
                invoiceStatusOptions: list,
            };
        },

        changeLayoutCollapsed(
            state = {
                notices: [],
                collapsed: true,
            },
            { payload },
        ) {
            return { ...state, collapsed: payload };
        },

        saveNotices(state, { payload }) {
            return {
                collapsed: false,
                ...state,
                notices: payload,
            };
        },

        saveClearedNotices(
            state = {
                notices: [],
                collapsed: true,
            },
            { payload },
        ) {
            return {
                collapsed: false,
                ...state,
                notices: state.notices.filter((item) => item.type !== payload),
            };
        },
    },
    subscriptions: {
        setup(props) {
            const { dispatch, history } = props;
            // Subscribe history(url) change, trigger `load` action if pathname is `/`

            history.listen((listenInfo) => {
                const { pathname, search, query } = listenInfo;
                // console.log(66666666666, listenInfo);
                // if (typeof window.ga !== 'undefined') {
                //     window.ga('send', 'pageview', pathname + search);
                // }

                const { error } = query;
                if (
                    pathname == '/user/login' &&
                    error &&
                    LOGIN_ERROR_CODE[`ENM_security_${error}`]
                ) {
                    message.error(LOGIN_ERROR_CODE[`ENM_security_${error}`]);
                }

                window.scrollTo(0, 0);
            });
        },
    },
};
export default GlobalModel;
