import { query as queryUsers, queryCurrent, queryCurrentEnvironment } from '@/services/userApi';
import { SessionStorage } from '@/utils/SessionStorage';
import { history } from 'umi';

export default {
    namespace: 'user',

    state: {
        list: [],
        currentUser:
            SessionStorage.getItem('currentUser') ||
            {
                // operId: '325616137'
            },

        currentEnvironment: '',
        currentPlatName: '',
    },

    effects: {
        *fetchCurrent(_, { call, put }) {
            const response = yield call(queryCurrent);
            // if(response == null) {
            //   history.push('/user/login');
            //   return;
            // }
            window.localStorage.setItem('userName', JSON.stringify(response));
            yield put({
                type: 'saveCurrentUser',
                payload: { ...response },
            });
        },
        *qryCurrent({ callback }, { call, put }) {
            console.log('local');
            const response = yield call(queryCurrent);
            callback(response);
        },

        *qryCurrentEnvironment(_, { call, put }) {
            const response = yield call(queryCurrentEnvironment);
            yield put({
                type: 'saveCurrentEnvironment',
                payload: response,
            });
        },
    },

    reducers: {
        saveCurrentUser(state, action) {
            SessionStorage.setItem('currentUser', action.payload || {});
            return {
                ...state,
                currentUser: action.payload || {},
            };
        },

        saveCurrentEnvironment(state, { payload }) {
            state.currentEnvironment = payload.currentEnvironment;
            state.currentPlatName = payload.currentPlatName;
            return {
                ...state,
            };
        },
    },
};
