import { getEquipList } from '@/services/AssetCenter/QrcodeManageApi';
import { usePagination } from 'ahooks';
import { useState } from 'react';

const useBindOperation = () => {
    const [showPastModal, setShowPastModal] = useState<boolean>(false);
    const [pastQrcode, setPastQrcode] = useState<API.QrCodeQueryVo | undefined>(undefined);
    const {
        run: searchList,
        mutate,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getEquipList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 5,
        },
    );

    return {
        searchList,
        mutate,
        listData,
        listLoading,
        pagination,
        showPastModal,
        setShowPastModal,
        pastQrcode,
        setPastQrcode,
    };
};

export default useBindOperation;
