import { getQrcodeList } from '@/services/AssetCenter/QrcodeManageApi';
import { usePagination } from 'ahooks';
import { useRef, useState } from 'react';

const useQrcodeList = () => {
    const [activeStatus, setActiveStatus] = useState<string>('binded');
    const cacheRef = useRef<{ count: () => void }>();

    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getQrcodeList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    return {
        searchList,
        listData,
        listLoading,
        pagination,
        activeStatus,
        setActiveStatus,
        cacheRef,
    };
};

export default useQrcodeList;
