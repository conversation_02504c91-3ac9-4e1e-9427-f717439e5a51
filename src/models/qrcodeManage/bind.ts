import { getEquipList } from '@/services/AssetCenter/QrcodeManageApi';
import { usePagination } from 'ahooks';
import { useState } from 'react';

const useBindOperation = () => {
    const [showModal, setShowModal] = useState<boolean>(false);
    const [currentQrcode, setCurrentQrcode] = useState<API.QrCodeQueryVo | undefined>(undefined);

    const processTableData = (list: any) => {
        if (list) {
            const temp: any[] = [];
            let equipId = -1;
            list.forEach((item: API.QrEquipVo) => {
                item?.gunEquipVoList?.forEach((gun: API.QrGunEquipVo, index: number) => {
                    temp.push({
                        ...item,
                        gunName: gun?.gunName,
                        gunNo: gun?.gunNo,
                        gunEquipId: gun?.equipId,
                        xdtQrCode: gun?.xdtQrCode,
                        bindFlag: gun?.bindFlag,
                        rowSpan: equipId !== item?.equipId ? item?.gunEquipVoList?.length : 0,
                        index,
                    });
                    equipId = item.equipId as number;
                });
            });
            return temp;
        } else {
            return [];
        }
    };

    const {
        run: searchList,
        mutate,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            // 注释：202401XX-B端-【贴码管理】生成固定码，选择设备弹窗页码优化
            // if (pageSize !== 5) {
            //     return;
            // }
            const response = await getEquipList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                const listData = processTableData(response?.data?.records);
                return {
                    list: listData,
                    current: response?.data?.pageIndex,
                    pageSize: listData.length || pageSize,
                    total: response?.data?.total
                        ? Math.ceil(response?.data?.total / pageSize) * listData.length
                        : response?.data?.total,
                    dataTotal: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                    dataTotal: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 5,
        },
    );

    return {
        searchList,
        mutate,
        listData,
        listLoading,
        pagination,
        showModal,
        setShowModal,
        currentQrcode,
        setCurrentQrcode,
    };
};

export default useBindOperation;
