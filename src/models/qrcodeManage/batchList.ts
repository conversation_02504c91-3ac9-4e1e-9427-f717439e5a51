import { getQrcodeRecordList } from '@/services/AssetCenter/QrcodeManageApi';
import { usePagination } from 'ahooks';

const useBatchList = () => {
    const {
        run: searchList,
        loading: listLoading,
        data: listData,
        refresh,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response = await getQrcodeRecordList({
                ...params,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records,
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    return {
        searchList,
        listData,
        refresh,
        listLoading,
        pagination,
    };
};

export default useBatchList;
