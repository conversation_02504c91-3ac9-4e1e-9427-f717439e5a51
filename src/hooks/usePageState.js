import React, { useEffect, useState, useRef } from 'react';
import { useLocation } from 'umi';

const usePageState = (options, props = { global: {} }, cacheName) => {
    const { global = {} } = props;

    const { pageInit = {} } = global;

    const location = useLocation();

    const { pathname } = location || {};

    const cacheKey = cacheName || pathname || 'ls-table';
    /**
     * 初始化
     */

    const initOptions = {
        pageIndex: 1,
        pageSize: 10,
    };
    const initOptionsEvent = () => {
        let cache = {};
        if (pageInit && pageInit && pageInit[cacheKey]) {
            cache = pageInit[cacheKey].state;
        }
        if (options && typeof options === 'object') {
            return {
                ...initOptions,
                ...options,
                ...cache,
            };
        }
        if (options && typeof options === 'function') {
            const newParams = options();
            return {
                ...initOptions,
                ...newParams,
                ...cache,
            };
        }
        return initOptions;
    };

    const [pageInfo, changePageInfo] = useState(initOptionsEvent);
    const changePageInfoEvent = (params, reset) => {
        if (typeof params === 'function') {
            const newParams = params && params();
            if (reset) {
                changePageInfo({
                    ...initOptionsEvent(),
                    ...newParams,
                });
                return;
            }

            changePageInfo((state) => ({
                ...state,
                ...newParams,
            }));

            return;
        }
        if (reset) {
            changePageInfo({
                ...initOptionsEvent(),
                ...params,
            });
        } else {
            changePageInfo((state) => ({
                ...state,
                ...params,
            }));
        }
    };

    // 搜索列表
    const onTableChange = (page, filters, sorter) => {
        const option = {
            pageIndex: page.current,
            pageSize: page.pageSize,
        };
        changePageInfoEvent(option);
    };
    return [pageInfo, changePageInfoEvent, onTableChange];
};
export default usePageState;
