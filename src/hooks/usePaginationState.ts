import { usePagination } from 'ahooks';
import { useLocation } from 'umi';
import { throttle } from 'lodash';
import { useState, useEffect } from 'react';

const usePaginationState = (
    apiEvent: { (params?: any): Promise<API.PaginationResponse<any>> },

    otherParams?: Record<string, any>,
) => {
    const { options, props, cacheName = '' } = otherParams || {};
    const { global, dispatch } = props;

    const location = useLocation();
    const { pageInit } = global;
    const { pathname } = location || {};

    const cacheKey = cacheName || pathname || 'ls-table';

    const initOptions = {
        pageIndex: 1,
        pageSize: 10,
    };

    const initOptionsEvent = () => {
        let cache = {};
        let page = {};
        if (pageInit && pageInit && pageInit[cacheKey]) {
            cache = pageInit[cacheKey].state;
            page = pageInit[cacheKey].page;
        }

        if (options && typeof options === 'object') {
            return {
                ...initOptions,
                ...page,
                ...options,
                ...cache,
            };
        }
        if (options && typeof options === 'function') {
            const newParams = options();
            return {
                ...initOptions,
                ...page,
                ...newParams,
                ...cache,
            };
        }
        return {
            ...initOptions,
            ...page,
            ...cache,
        };
    };

    const listInfo = usePagination(
        async ({ current, pageSize }, params?: any) => {
            const response =
                apiEvent &&
                (await apiEvent({
                    ...initOptionsEvent(),
                    ...params,
                    pageSize,
                    pageIndex: current,
                }));
            if (response.ret === 200) {
                const tableList = response?.data?.records || response?.data?.list;
                return {
                    list: tableList?.map((ele, index) => {
                        return {
                            ...ele,
                            index: (current - 1) * pageSize + index,
                        };
                    }),
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                } as any;
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,

            defaultPageSize: 10,
            cacheKey: cacheName,
        },
    );

    const { run, data, pagination } = listInfo;

    const searchList = throttle((formData?: Record<string, any>) => {
        const params = { ...(formData || {}) };

        dispatch({
            type: 'global/setPageInit',
            pathname: cacheKey,
            info: {
                form: formData,
                page: {
                    current: 1,
                    pageSize: pagination.pageSize,
                },
            },
        });
        run({ current: 1, pageSize: pagination.pageSize }, params);
    }, 500);
    const initList = (formData?: Record<string, any>) => {
        const params = { ...(formData || {}) };
        const pageInfo = {
            current: initOptionsEvent()?.current || pagination.current,
            pageSize: initOptionsEvent()?.pageSize || pagination.pageSize,
        };
        dispatch({
            type: 'global/setPageInit',
            pathname: cacheKey,
            info: {
                form: formData,
                page: pageInfo,
            },
        });
        run(pageInfo, params);
    };

    const onTableChange = (pages: any) => {
        dispatch({
            type: 'global/setPageInit',
            pathname: cacheKey,
            info: {
                form: pageInit[cacheKey]?.form || {},
                page: { current: pages?.current, pageSize: pages?.pageSize },
            },
        });
        pagination.onChange(pages?.current, pages?.pageSize);
    };

    return {
        ...listInfo,

        searchList,
        initList,
        onTableChange,
    };
};
export default usePaginationState;
