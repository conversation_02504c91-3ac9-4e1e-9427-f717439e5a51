import { useEffect, useMemo } from 'react';
import { useLocation } from 'umi';
const useCooperationPlatform = () => {
    const location = useLocation();
    const { state, query } = location;
    const { cooperationPlatform: newCooperationPlatform } = state ?? {};
    const { cooperationPlatform: urlCooperationPlatform } = query ?? {};

    useEffect(() => {
        const oldCooperationPlatform = sessionStorage.getItem('cooperationPlatform');
        if (newCooperationPlatform && newCooperationPlatform != oldCooperationPlatform) {
            sessionStorage.setItem('cooperationPlatform', newCooperationPlatform);
        }
    }, [newCooperationPlatform]);
    const cooperationPlatform = useMemo(() => {
        const oldCooperationPlatform = sessionStorage.getItem('cooperationPlatform');
        return urlCooperationPlatform || newCooperationPlatform || oldCooperationPlatform;
    }, [urlCooperationPlatform, newCooperationPlatform]);
    return {
        cooperationPlatform,
    };
};
export default useCooperationPlatform;
