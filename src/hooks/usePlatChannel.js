import { useContext, createContext, useState } from 'react';
import { Radio } from 'antd';

const PLAT_CHANNEL_TYPS = {
    XDT: '01',
    XDT_X: '02',
};
export const PlatChannelContext = createContext({
    platChannel: '',
    updatePlatChannel: () => {},
});
export const PlatChanelLayout = (props) => {
    const { children } = props;
    const [platChannel, updatePlatChannel] = useState(PLAT_CHANNEL_TYPS.XDT);
    return (
        <>
            <PlatChannelContext.Provider value={{ platChannel, updatePlatChannel }}>
                <Radio.Group
                    value={platChannel}
                    onChange={(newVal) => {
                        updatePlatChannel(newVal);
                    }}
                    optionType="button"
                >
                    <Radio value={PLAT_CHANNEL_TYPS.XDT}>新电途</Radio>
                    <Radio value={PLAT_CHANNEL_TYPS.XDT_X}>新电途X</Radio>
                </Radio.Group>
                {children}
            </PlatChannelContext.Provider>
        </>
    );
};
export const usePlatChannelContext = () => {
    return useContext(PlatChannelContext);
};
