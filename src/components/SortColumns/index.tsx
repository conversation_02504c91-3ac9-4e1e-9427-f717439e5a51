import {
    ColumnHeightOutlined,
    SortAscendingOutlined,
    SortDescendingOutlined,
} from '@ant-design/icons';
import { Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

interface SORT_ITEM {
    //排序字段名
    name: string;
    //排序标题
    label: string;
    //升序返回值
    ascValue?: string;
    //降序返回值
    descValue?: string;
    //当前值
    current?: string;
}

interface Props {
    //选项值
    items: SORT_ITEM[];
    //选项互斥
    mutualExclusion?: boolean;
    //变更事件
    onChange?: (value: Record<string, any>) => void;
}

const SortColumns = (props: Props) => {
    const { items, onChange, mutualExclusion = false } = props;
    const [sortOrder, setSortOrder] = useState<SORT_ITEM[]>();
    useEffect(() => {
        setSortOrder(
            items.map((v) => {
                const { name, label, ascValue = 'asc', descValue = 'desc', current = '' } = v;
                return { name, label, ascValue, descValue, current };
            }),
        );
    }, [items]);

    const itemClick = (item: SORT_ITEM, index: number) => {
        let current = item.current;
        if (current !== item.ascValue && current !== item.descValue) {
            current = item.ascValue;
        } else if (current === item.ascValue) {
            current = item.descValue;
        } else if (current === item.descValue) {
            current = '';
        }
        item.current = current;
        const newSortArr: SORT_ITEM[] =
            sortOrder?.map((v: SORT_ITEM) => {
                return { ...v, current: mutualExclusion ? '' : v.current };
            }) || [];
        newSortArr?.splice(index, 1, item);
        const params: any = {};
        newSortArr?.forEach((v) => {
            if (v.current) {
                params[v.name] = v.current;
            }
        });
        onChange?.(params);
        setSortOrder(newSortArr);
    };

    return (
        <Space size="large">
            {sortOrder?.map((v, index: number) => {
                return (
                    <Space
                        key={index}
                        size={4}
                        onClick={() => {
                            itemClick({ ...v }, index);
                        }}
                        align="start"
                        style={{ cursor: 'pointer' }}
                    >
                        {v.current === v.ascValue && (
                            <SortAscendingOutlined size={16} style={{ marginTop: '5px' }} />
                        )}
                        {v.current === v.descValue && (
                            <SortDescendingOutlined size={16} style={{ marginTop: '5px' }} />
                        )}
                        {v.current !== v.ascValue && v.current !== v.descValue && (
                            <ColumnHeightOutlined size={16} style={{ marginTop: '5px' }} />
                        )}
                        <Typography.Title level={5}>{v?.label}</Typography.Title>
                    </Space>
                );
            })}
        </Space>
    );
};

export default React.memo(SortColumns);
