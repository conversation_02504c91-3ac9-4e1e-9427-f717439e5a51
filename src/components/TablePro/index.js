import { Table, Space, Dropdown, Form, Tooltip, Tree, Button } from 'antd';
import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import {
    copyObjectCommon,
    formatTableColumns,
    formatExportColumns,
    exportTableByParams,
} from '@/utils/utils';
import { SettingOutlined, MenuOutlined } from '@ant-design/icons';
import { LocalStorage } from '@/utils/LocalStorage/index';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';

const ALL_KEYS = '999';

const filterDataHeaderListEvent = (columns, ignoreDismiss = true) => {
    let list = [];
    if (columns instanceof Array) {
        for (const item of columns) {
            if (item.children) {
                list = [...list, ...filterDataHeaderListEvent(item.children, ignoreDismiss)];
            } else {
                if (item.dataIndex && (ignoreDismiss || !item.defaultDismiss)) {
                    list.push(item);
                }
            }
        }
    }

    return list;
};
// 拖拽相关组件
const DragHandle = SortableHandle(() => (
    <MenuOutlined
        style={{
            cursor: 'grab',
            color: '#999',
        }}
    />
));
const SortableItem = SortableElement((props) => <tr {...props} />);
const SortableBody = SortableContainer((props) => <tbody {...props} />);

const FormItem = Form.Item;

/**
 * 封装表单  保留antd的所有api
 */
const TablePro = (props, ref) => {
    let {
        loading,
        name = 'table',
        onChange,
        pagination,
        columns = [],
        noSort,
        tabType,
        offsetHeader,
        filterHeader = true, // 配合columns的defaultDismiss，实现表头过滤默认不显示的需求
        onSort, //排序事件
        showToggleExpanded = false, //展示全部展开收缩
        defaultToggle = false, // 用expandable属性配置展开样式的前提下，可配置默认全部收起还是展开，默认收起
        dataSource = [],
        rowKey, //通过rowKey处理展开交互
        tabsRender, //用于展示tabbar占位
        isDrag, //是否开启拖拽
        setDataSource, //更改完的data返回方法
    } = props;
    const { expandable = {} } = props;

    useImperativeHandle(ref, () => {
        return {
            getExportColumns: getExportColumns,
            exportTable(options) {
                exportTableByParams({
                    ...options,
                    columnsStr: getExportColumns(),
                });
            },
        };
    });

    const [tableState, changeTableState] = useState({ filters: null, sorter: null });

    const [expandedRowKeys, updateExpandedRowKeys] = useState([]);

    let newExpandable = {
        ...expandable,
    };
    if (showToggleExpanded && expandable) {
        const expandedConfig = {
            expandedRowKeys,

            onExpandedRowsChange: (expandedRows) => {
                updateExpandedRowKeys([...expandedRows]);
            },
        };
        newExpandable = {
            ...expandable,
            ...expandedConfig,
        };
    }
    // 拖拽方法

    const onSortEnd = ({ oldIndex, newIndex }) => {
        if (oldIndex !== newIndex) {
            const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(
                (el) => !!el,
            );
            setDataSource(newData, newIndex, oldIndex);
        }
    };
    const DraggableContainer = (props) => (
        <SortableBody
            useDragHandle
            disableAutoscroll
            helperClass="row-dragging"
            onSortEnd={onSortEnd}
            {...props}
        />
    );
    const DraggableBodyRow = ({ className, style, ...restProps }) => {
        // function findIndex base on Table rowKey props and should always be a right array index
        const index = dataSource.findIndex((x) => x.index === restProps['data-row-key']);
        return <SortableItem index={index} {...restProps} />;
    };

    const formatColumns = useMemo(() => {
        if (isDrag) {
            columns = columns.filter((item) => item.dataIndex !== 'drag');
            columns.unshift({
                title: '排序',
                dataIndex: 'drag',
                width: 80,
                className: 'drag-visible',
                render: () => <DragHandle />,
            });
        }
        if (noSort) {
            return columns;
        }
        return formatTableColumns(columns, tableState.sorter);
    }, [columns, noSort, tableState]);

    /* 自定义表头展示列，前端存储用户不勾选的列名，便于后期扩展
        数据存储结构：
        {'页面名称': {'dismissDefaultList(默认不展示列名，作为版本记录)': [], 'dismissUserList(用户操作的不展示列名)': []}}
        实现逻辑
        1、只要列表有显示，就去读取对应的本地数据
            1.1 如果有用户操作列，抽离当前列表配置的所有不展示列名，和dismissDefaultList进行比较，
            如果有新增，表示是从 版本更新进来/更改字段名 的默认不展示的新列名，加到dismissDefaultList和dismissUserList进行保存
            1.2 如果没有取到存储的信息，直接把抽离出来的dismissDefaultList内容交给dismissUserList，同时本地保存两个字段
        2、展示列表以dismissUserList字段来渲染，包含的字段不显示，便于后期业务扩展
        3、用户的所有勾选操作，都取反并同步到dismissUserList，不要动dismissDefaultList
    */
    const localKey = `dismiss_${window.location.href}${name}`;
    const [dismissUserList, updateDismissUserList] = useState([]);

    useEffect(() => {
        if (loading) {
            updateExpandedRowKeys([]);
        }
    }, [loading]);

    // 获取不显示的表头值
    useEffect(() => {
        if (!dismissUserList?.length) {
            const defaultList = formatColumns.filter((ele) => ele.defaultDismiss) || [];
            let _dismissDefaultList;
            let _dismissUserList;
            let isNew = false;
            const dismissPageColumnInfo = LocalStorage.getItem(localKey);
            if (dismissPageColumnInfo) {
                // 自定义表头 1.1
                _dismissDefaultList = dismissPageColumnInfo.dismissDefaultList;
                _dismissUserList = dismissPageColumnInfo.dismissUserList;
                const newColumns = defaultList.filter(
                    (ele) =>
                        _dismissDefaultList.some(
                            (userDataIndex) => userDataIndex == ele.dataIndex,
                        ) == false,
                );
                if (newColumns?.length) {
                    _dismissDefaultList.push(...newColumns.map((ele) => ele.dataIndex));
                    _dismissUserList.push(...newColumns.map((ele) => ele.dataIndex));
                    isNew = true;
                }
            } else {
                // 自定义表头 1.2
                _dismissDefaultList = _dismissUserList = defaultList.map((ele) => ele.dataIndex);
                isNew = true;
            }

            if (isNew) {
                LocalStorage.setItem(localKey, {
                    dismissDefaultList: _dismissDefaultList,
                    dismissUserList: _dismissUserList,
                });
            }

            updateDismissUserList([..._dismissUserList]);
        }
    }, [formatColumns]);

    //已选表头初始化
    const [selectHeaderKeys, updateSelectHeaderKeys] = useState([]);
    useEffect(() => {
        let list = [];
        if (!formatColumns) {
        } else {
            if (filterHeader) {
                const tempList = filterDataHeaderListEvent(formatColumns);
                const filter = tempList.filter(
                    (ele) =>
                        dismissUserList.some((userDataIndex) => userDataIndex == ele.dataIndex) ==
                        false,
                );
                list = filter.map((ele) => ele.dataIndex);
            } else {
                list = formatColumns.map((ele) => ele.dataIndex);
            }
        }
        updateSelectHeaderKeys([...list]);
    }, [formatColumns, dismissUserList]);

    //修改选中表头
    const onCheckFilterTree = (checkedKeys, info) => {
        const key = info?.node?.key;
        // 判断全选/非全选
        if (key == '999') {
            // 全选
            if (info.checked) {
                dismissUserList.splice(0, dismissUserList.length);
            } else {
                const list = filterDataHeaderListEvent(formatColumns);
                dismissUserList.push(...list?.map((ele) => ele.dataIndex));
            }
        } else {
            // 单选
            if (info.checked) {
                // 未选 -> 勾选
                const index = dismissUserList.findIndex((ele) => ele == key);
                if (index >= 0) {
                    dismissUserList.splice(index, 1);
                }
            } else {
                // 勾选 -> 未选
                dismissUserList.push(key);
            }
        }

        //更改时写入本地缓存
        const dismissPageColumnInfo = LocalStorage.getItem(localKey);
        dismissPageColumnInfo.dismissUserList = dismissUserList;
        LocalStorage.setItem(localKey, dismissPageColumnInfo);
        updateDismissUserList([...dismissUserList]);
    };

    const filterColumnsEvent = (columns) => {
        let list = [];
        if (columns instanceof Array) {
            for (const item of columns) {
                if (item.children) {
                    const children = filterColumnsEvent(item.children);
                    if (children?.length > 1) {
                        list.push({ ...item, children });
                    } else if (children?.length == 1) {
                        list = [...list, ...children];
                    }
                } else {
                    if (item.dataIndex) {
                        if (!filterHeader || !dismissUserList.includes(item.dataIndex)) {
                            list.push(item);
                        }
                    } else {
                        // 操作列要暴露出来
                        list.push(item);
                    }
                }
            }
        }

        return list;
    };

    const filterColumns = useMemo(() => {
        const list = filterColumnsEvent(formatColumns);
        if (filterHeader) {
            // 给最后一列的表头增加过滤按钮
            const tooltip = (
                <Tooltip
                    key={'filter'}
                    trigger="click"
                    placement="left"
                    color="#ffffff"
                    title={
                        <Fragment>
                            <Tree
                                checkable
                                defaultExpandAll
                                selectedKeys={selectHeaderKeys}
                                checkedKeys={selectHeaderKeys}
                                onCheck={onCheckFilterTree}
                                treeData={[
                                    {
                                        title: '全部',
                                        key: ALL_KEYS,
                                        children: filterDataHeaderListEvent(formatColumns)?.map(
                                            (ele, index) => {
                                                return {
                                                    title: ele.name || ele.title,
                                                    key: ele.dataIndex,
                                                };
                                            },
                                        ),
                                    },
                                ]}
                            />
                        </Fragment>
                    }
                >
                    <SettingOutlined />
                </Tooltip>
            );
            if (list.length) {
                const column = list[list.length - 1];
                list.splice(list.length - 1, 1);
                list.push({
                    ...column,
                    title: (
                        <Space>
                            {column.title || null}
                            {tooltip}
                        </Space>
                    ),
                });
            } else {
                list.push({ title: tooltip });
            }
        }
        return list;
    }, [formatColumns, selectHeaderKeys, filterHeader]);

    useEffect(() => {
        changeTableState({ filters: null, sorter: null });
    }, [tabType]);

    const tableChangeEvent = (pages, filters, sorter) => {
        if (pagination) {
            const { current, pageSize } = pagination;
            if (pages?.current != current || pages?.pageSize != pageSize) {
                changeTableState({ filters: null, sorter: null });
                onChange && onChange(pages, filters, sorter);
            } else {
                changeTableState({ filters, sorter });
            }
        }
        if (sorter) {
            onSort && onSort(sorter);
        }
    };

    const getExportColumns = () => {
        const columnsStrs = formatExportColumns(filterColumns);
        return columnsStrs;
    };

    useEffect(() => {
        if (defaultToggle && dataSource?.length) {
            openAllEvent();
        }
    }, [defaultToggle, dataSource]);

    const openAllEvent = () => {
        const keys =
            dataSource?.map((ele) => {
                if (typeof rowKey === 'string') {
                    return ele[rowKey];
                } else if (typeof rowKey === 'function') {
                    return rowKey(ele);
                } else {
                    return ele.id;
                }
            }) || [];
        updateExpandedRowKeys(keys);

        for (const item of dataSource) {
            expandable?.onExpand && expandable?.onExpand(true, item);
        }
    };
    return (
        <Fragment>
            {showToggleExpanded && (
                <Space style={{ margin: '0 0 10px' }}>
                    <Button
                        onClick={() => {
                            updateExpandedRowKeys([]);
                        }}
                    >
                        全部收起
                    </Button>
                    <Button onClick={openAllEvent}>全部展开</Button>
                </Space>
            )}

            {tabsRender}

            <Table
                sticky={{ offsetHeader: offsetHeader >= 0 ? offsetHeader : 60 }}
                {...props}
                onChange={tableChangeEvent}
                columns={filterColumns}
                expandable={newExpandable}
                components={
                    // 开启拖拽，暂时无法在修改自定义components
                    isDrag
                        ? {
                              body: {
                                  wrapper: DraggableContainer,
                                  row: DraggableBodyRow,
                              },
                          }
                        : {
                              ...props.components,
                          }
                }
            />
        </Fragment>
    );
};
export default forwardRef(TablePro);
