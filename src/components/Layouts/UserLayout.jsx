import { DefaultFooter, getMenuData, getPageTitle } from '@ant-design/pro-layout';
import { Helmet } from 'react-helmet';
import { Link, formatMessage, connect } from 'umi';
import React from 'react';

import SelectLang from '@/components/SelectLang';
import logo from '../../public/logo.png';
import styles from './UserLayout.less';

const UserLayout = (props) => {
    const {
        route = {
            routes: [],
        },
    } = props;
    const { routes = [] } = route;
    const {
        children,
        location = {
            pathname: '',
        },
    } = props;
    const { breadcrumb } = getMenuData(routes);
    const title = getPageTitle({
        pathname: location.pathname,
        formatMessage,
        breadcrumb,
        ...props,
    });
    return (
        <>
            <Helmet>
                <title>{title}</title>
                <meta name="description" content={title} />
            </Helmet>

            <div className={styles.container}>
                <div className={styles.lang}>
                    <SelectLang />
                </div>
                <div className={styles.content}>
                    <div className={styles.top}>
                        <div className={styles.header}>
                            <Link to="/">
                                <img alt="logo" className={styles.logo} src={logo} />
                                <span className={styles.title}>Ant Design</span>
                            </Link>
                        </div>
                        <div className={styles.desc}>
                            Ant Design 是西湖区最具影响力的 Web 设计规范
                        </div>
                    </div>
                    {children}
                </div>
                <DefaultFooter />
            </div>
        </>
    );
};

export default connect(({ settings }) => ({ ...settings }))(UserLayout);
