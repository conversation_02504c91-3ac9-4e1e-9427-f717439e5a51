import { Fragment, useEffect } from 'react';
import { Icon, Spin } from 'antd';
import DocumentTitle from 'react-document-title';
import styles from './LoginLayout.less';
import baseStyles from '@/assets/styles/base.less';
import { connect } from 'dva';
import { tranCurrentEnvironment } from '@/utils/commonUtils';
import Login from '@/pages/Login/LoginPage';

const LoginLayout = (props) => {
    const {
        dispatch,
        children,
        user: { currentEnvironment, currentPlatName },
        loading,
    } = props;

    useEffect(() => {
        dispatch({
            type: 'user/qryCurrentEnvironment',
        });
    }, []);

    const renderLoginPage = () => {
        const environmentCode = tranCurrentEnvironment(currentEnvironment);
        return <Login {...props} />;
    };

    if (loading) {
        return (
            <div className={baseStyles.loading}>
                <Spin size="large" />
            </div>
        );
    }
    // let legengStyle = '';
    // let bodyStyle = '';
    // let environmentCode = tranCurrentEnvironment(currentEnvironment);
    // legengStyle = styles[environmentCode+'_legeng'] || styles.default_legeng;
    // bodyStyle = styles[environmentCode+'_body'] || styles.default_body;
    return (
        <DocumentTitle title={currentPlatName || '加载中'}>
            <div className={styles.loginLayout}>{renderLoginPage()}</div>
        </DocumentTitle>
    );
};

export default connect(({ user, loading }) => ({
    user,
    loading: loading.effects['user/qryCurrentEnvironment'],
}))(LoginLayout);
