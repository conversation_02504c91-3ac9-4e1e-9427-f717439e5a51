import { Form, Checkbox, InputNumber, Button, Space, message } from 'antd';
import { Fragment, useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import commonStyles from '@/assets/styles/common.less';
import TablePro from '@/components/TablePro/index';
import SelectBudgetModal from './SelectBudgetModal';
import { getActBudgetRelInfoApi } from '@/services/FinanceManage/BudgetApi';
import { BEARING_TYPES } from './budgetConfig';

const FormItem = Form.Item;
const BudgetFormItem = (props, ref) => {
    const {
        disabled,
        filePath = [],
        budgetList,
        form,
        effTime,
        expTime,
        currentUser,
        belongType,
        formItemLayout,
        rules,
        hideTitle,
    } = props;

    const preName = filePath ? [...filePath] : [];
    const columns = [
        {
            title: '',
            render(text, record) {
                return <span title={record.monthName}>{record.monthName}</span>;
            },
        },
        {
            title: '计划配置金额',
            dataIndex: 'configurePrice',
            render(text, record, index) {
                return (
                    <span title={text}>
                        {text}
                        {index <= 1 && record.remainAmount >= 0 ? (
                            <strong>
                                <span>(可配:{record.remainAmount}元)</span>
                            </strong>
                        ) : null}
                    </span>
                );
            },
        },

        {
            title: '预计核销金额',
            dataIndex: 'useAmount',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    useImperativeHandle(ref, () => ({
        update: (options) => {
            getMonthInfo(options);
        },
    }));

    const [tableList, updateTableList] = useState([]);

    const [showBudgetModal, toggleBudgetModal] = useState(false);

    // useEffect(() => {
    //     if (budgetList instanceof Array) {
    //         updateTableList(budgetList);
    //     }
    // }, [budgetList]);

    useEffect(() => {
        if ((currentUser && currentUser.operId) || belongType === 'oper') {
            setFormValues({
                expenseBearing: [BEARING_TYPES.BUSINESS],
            });
        }
    }, []);

    const selectBudgetEvent = () => {
        toggleBudgetModal(true);
    };
    const closeBudgetEvent = () => {
        toggleBudgetModal(false);
    };
    const finishBudgetEvent = async (item) => {
        try {
            await getMonthInfo({ budgetId: item.budgetId, effTime, expTime });
            closeBudgetEvent();
            return item;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const getMonthInfo = async ({ budgetId, effTime, expTime, ...otherOptions }) => {
        try {
            let params = {
                budgetId,
                effTime: effTime && effTime.format('YYYY-MM-DD HH:mm:ss'),
                expTime: expTime && expTime.format('YYYY-MM-DD HH:mm:ss'),
                ...otherOptions,
            };
            if (!effTime || !expTime) {
                message.error('请先选择活动开始和失效时间');
                return Promise.reject('请先选择活动开始和失效时间');
            }
            const { data } = await getActBudgetRelInfoApi(params);
            if (!(data?.monthMap instanceof Array)) {
                message.error('这条预算没有数据');
                return Promise.reject('这条预算不能选');
            }

            // updateTableList([...data.monthMap]);
            setFormValues({
                budgetId: budgetId,
                budgetName: data.budgetName,
                monthMap: [...data.monthMap],
            });
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const setFormValues = (values) => {
        if (filePath?.length) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }

            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });

            form.setFieldsValue({ [superName]: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    if ((currentUser && currentUser.operId) || belongType === 'oper') {
        return (
            <FormItem
                name={[...preName, 'expenseBearing']}
                noStyle
                initialValue={[BEARING_TYPES.BUSINESS]}
            ></FormItem>
        );
    }

    return (
        <Fragment>
            {hideTitle ? null : <div className={commonStyles['form-title']}>活动预算</div>}

            <FormItem
                name={[...preName, 'expenseBearing']}
                label="费用承担"
                rules={
                    (rules && rules.expenseBearing) || [{ required: true, message: '请选择承担方' }]
                }
                {...formItemLayout}
                initialValue={
                    (currentUser && currentUser.operId) || belongType === 'oper'
                        ? [BEARING_TYPES.BUSINESS]
                        : []
                }
            >
                <Checkbox.Group
                    disabled={
                        disabled || (currentUser && currentUser.operId) || belongType === 'oper'
                    }
                    placeholder="请选择"
                >
                    <Checkbox value={BEARING_TYPES.BUSINESS}>商家承担</Checkbox>
                    {currentUser && !currentUser.operId && belongType !== 'oper' ? (
                        <Checkbox value={BEARING_TYPES.PLATFORM}>平台承担</Checkbox>
                    ) : null}
                </Checkbox.Group>
            </FormItem>
            <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                {({ getFieldValue }) => {
                    const expenseBearing = getFieldValue([...preName, 'expenseBearing']);
                    const budgetName = getFieldValue([...preName, 'budgetName']);
                    const monthMap = getFieldValue([...preName, 'monthMap']);

                    return expenseBearing?.includes(BEARING_TYPES.PLATFORM) ? (
                        <Fragment>
                            <FormItem
                                name={[...preName, 'budgetId']}
                                label="预算设置"
                                rules={
                                    (rules && rules.budgetId) || [
                                        { required: true, message: '请选择预算' },
                                    ]
                                }
                                {...formItemLayout}
                            >
                                <Space>
                                    {budgetName ? budgetName : ''}
                                    <Button
                                        disabled={disabled}
                                        type="primary"
                                        onClick={selectBudgetEvent}
                                    >
                                        {' '}
                                        选择预算
                                    </Button>
                                </Space>

                                {monthMap && monthMap?.length > 0 ? (
                                    <TablePro
                                        scroll={{ x: 'max-content' }}
                                        rowKey={(record, index) => index}
                                        dataSource={monthMap}
                                        columns={columns}
                                        pagination={false}
                                        noSort
                                    />
                                ) : null}
                            </FormItem>
                            <FormItem
                                name={[...preName, 'monthMap']}
                                initialValue={[]}
                                noStyle
                            ></FormItem>

                            <FormItem
                                name={[...preName, 'budgetAmount']}
                                label="活动计划配置预算"
                                rules={
                                    (rules && rules.budgetAmount) || [
                                        { required: true, message: '请配置计划预算' },
                                    ]
                                }
                                {...formItemLayout}
                            >
                                <InputNumber
                                    style={{ width: '100%' }}
                                    precision={2}
                                    step={0.01}
                                    min={0}
                                    placeholder="请填写"
                                ></InputNumber>
                            </FormItem>
                        </Fragment>
                    ) : null;
                }}
            </FormItem>
            <SelectBudgetModal
                visible={showBudgetModal}
                onFinish={finishBudgetEvent}
                onClose={closeBudgetEvent}
                {...props}
            ></SelectBudgetModal>
        </Fragment>
    );
};
// export default forwardRef(BudgetFormItem);

const BudgetForm = forwardRef(BudgetFormItem);
export default BudgetForm;
