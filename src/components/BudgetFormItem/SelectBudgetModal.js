import React, {
    Fragment,
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useMemo,
} from 'react';
import { connect } from 'umi';
import { Button, InputNumber, Select, Form, Modal, message, Space, Input, Row, Col } from 'antd';
import { getBudgetYearListApi } from '@/services/FinanceManage/BudgetApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import styles from '@/assets/styles/common.less';
import { getActBudgetRelInfoApi } from '@/services/FinanceManage/BudgetApi';

const { Option } = Select;

const FormItem = Form.Item;

const searchformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};

const SelectBudgetModal = (props, ref) => {
    const {
        visible,
        onClose,
        onFinish, // 添加完毕的回调事件，如果是追加，返回空，如果是添加，返回已选中的cpn对象
        extInParams, // 额外入参
        disabledIds, // 限制不允许勾选的id列表
    } = props;

    const [budgetList, updateBudgetList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [listLoading, updateListLoading] = useState(false);
    const [tableList, updateTableList] = useState([]);
    const [tableListTotal, updateTableListTotal] = useState(0);

    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, { global: {} });

    useEffect(() => {}, []);

    useImperativeHandle(ref, () => ({}));

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [form] = Form.useForm();

    const searchData = async () => {
        try {
            const data = form.getFieldsValue(true);
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                budgetName: data.budgetName,
                budgetId: data.budgetId,
            };

            updateListLoading(true);
            const {
                data: { list, total },
            } = await getBudgetYearListApi(params);
            updateTableList(list || []);
            updateTableListTotal(total);
            return list;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        form.setFieldsValue({ ...extInParams });
        updateBudgetList([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const onFinishEvent = (item) => {
        onFinish && onFinish(item);
    };

    const columns = [
        {
            title: '预算名称',
            width: 200,
            dataIndex: 'budgetName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '预算编号',
            width: 140,
            dataIndex: 'budgetId',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '部门',
            width: 200,
            dataIndex: 'departmentName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '项目',
            width: 200,
            dataIndex: 'projectName',
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
        {
            title: '年度计划配置金额',
            width: 160,
            dataIndex: 'configurePrice',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '年度预计核销金额',
            width: 160,
            dataIndex: 'predictUsePrice',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '160px' }} title={text}>
                        {text}
                    </div>
                );
            },
        },
        {
            title: '预算年',
            width: 100,
            dataIndex: 'budgetYear',
            render(text, record) {
                return (
                    <div className="text-line" style={{ width: '100px' }} title={text}>
                        {text || '-'}
                    </div>
                );
            },
        },
        {
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (text, record) => {
                return (
                    <Space>
                        <span className={styles['table-btn']} onClick={() => onFinishEvent(record)}>
                            选择
                        </span>
                    </Space>
                );
            },
        },
    ];

    // const rowSelection = {
    //     selectedRowKeys: budgetList.map((item) => `${item.budgetId}`),
    //     onChange: (selectedRowKeys, selectedRows) => {
    //         // 筛选出非当前页的勾选项，不予处理
    //         let otherCpns = budgetList.filter(
    //             (x) => tableList.filter((now) => now.budgetId == x.budgetId).length == 0,
    //         );
    //         updateBudgetList([...otherCpns, ...selectedRows]);
    //     },
    //     getCheckboxProps: (record) => ({
    //         disabled: record.pushPrizeId || disabledIds?.indexOf(record.budgetId) >= 0,
    //         name: record.budgetId,
    //     }),
    // };

    return (
        <Modal
            title={'选择预算'}
            destroyOnClose
            width={1200}
            visible={visible}
            onCancel={() => onClose()}
            footer={null}
            maskClosable={false}
        >
            <Form form={form} initialValues={{ ...extInParams }} scrollToFirstError>
                <Row>
                    <Col span={6}>
                        <FormItem label="预算名称" name="budgetName" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <FormItem label="预算编号" name="budgetId" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <Space>
                            <Button type="primary" onClick={searchData}>
                                查询
                            </Button>
                            <Button onClick={resetData}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${record.budgetId}`}
                dataSource={tableList}
                columns={columns}
                onChange={onTableChange}
                // rowSelection={{
                //     type: 'checkbox',
                //     ...rowSelection,
                // }}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: tableListTotal,
                    pageSize: pageInfo.pageSize,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                sticky={{ offsetHeader: 0 }}
            />
        </Modal>
    );
};

export default forwardRef(SelectBudgetModal);
