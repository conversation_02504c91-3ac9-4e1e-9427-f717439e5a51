import React, { useEffect, useState } from 'react';
import { Form, Input, Popover } from 'antd';
import ColorItem from './Item';

const FormItem = Form.Item;

const SelectColor = (props) => {
    const {
        form,
        required,
        name,
        label,
        rules,
        placeholder,
        disabled,
        onChange,
        initialValue = '',
        ...extendProps
    } = props;
    const [color, changeColor] = useState(initialValue);

    const nameValue = Form.useWatch(name, form);

    const changeColorEvent = (value) => {
        changeColor(value);
        onChange && onChange(value);
        form.setFieldsValue({ [name]: value });
    };
    useEffect(() => {
        if (nameValue && nameValue != color) {
            changeColor(nameValue);
            onChange && onChange(nameValue);
        }
    }, [nameValue]);
    return (
        <FormItem label={label} required={required} {...extendProps}>
            <FormItem name={name} rules={rules} noStyle initialValue={color || ''}>
                <ColorItem
                    value={color}
                    onChange={changeColorEvent}
                    placeholder={placeholder}
                    disabled={disabled}
                    {...extendProps}
                ></ColorItem>
            </FormItem>
        </FormItem>
    );
};
export default SelectColor;
