import { SketchPicker } from 'react-color';
import { Form, Input, Popover } from 'antd';
import { BgColorsOutlined } from '@ant-design/icons';
import { useState } from 'react';
const ColorItem = (props) => {
    const { value, onChange, placeholder, disabled, small = false, ...extendProps } = props;
    const closeAfter = <BgColorsOutlined />;
    const handleChangeComplete = (colorValue) => {
        const value = colorValue.hex || colorValue.rgb;
        onChange && onChange(value);

        hide();
    };
    const changeColorEvent = (value) => {
        onChange && onChange(value);
    };

    const [hovered, setHovered] = useState(false);

    const hide = () => {
        setHovered(false);
    };

    const handleHoverChange = (open) => {
        setHovered(open);
    };

    return (
        <Popover
            overlayClassName="selectColorPopover"
            placement="bottomLeft"
            content={<SketchPicker color={value} onChangeComplete={handleChangeComplete} />}
            trigger="hover"
            open={hovered}
            onOpenChange={handleHoverChange}
        >
            {!small ? (
                <Input
                    value={value}
                    placeholder={placeholder}
                    autoComplete="off"
                    onChange={(e) => changeColorEvent(e.target.value)}
                    addonAfter={closeAfter}
                    // onFocus={() => {
                    //     togglePicker(true);
                    // }}
                    disabled={disabled}
                    {...extendProps}
                />
            ) : (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '30px',
                        height: '30px',
                        borderRadius: '10px',
                        backgroundColor: value,
                    }}
                >
                    {closeAfter}
                </div>
            )}
        </Popover>
    );
};
export default ColorItem;
