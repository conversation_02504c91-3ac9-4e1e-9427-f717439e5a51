import usePageState from '@/hooks/usePageState';
import { PlusOutlined } from '@ant-design/icons';
import { Alert, Button, Form, Modal, Radio, Select, Tag } from 'antd';
import { Fragment, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import TablePro from '../TablePro';
import {
    getCrowdCalculateListApi,
    getCrowdRealListApi,
    refreshCrowdRefluxApi,
} from '@/services/UserManage/UserApi';
import { getCDPListApi } from '@/services/Marketing/MarketingMembershipApi';
import { useWatch } from 'antd/lib/form/Form';
import moment from 'moment';
import { getCrowdDefineQueryPage } from '@/services/MngBilApi';

export const CROWD_TYPES = {
    CDP_USER: '01',
    CDP_REAL: '02',
    XDT_CALCULATE: '04', // 人群计算
};

const TargetUserSelectModal = (props) => {
    const { initRef, applyType, callback } = props;

    const [form] = Form.useForm();
    const [step, updateStep] = useState(0);
    const [visible, updateVisible] = useState(false);
    const temp_type = useWatch('temp_type', form);

    const [selectedRows, updateSelectRows] = useState([]);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({});
    const [listLoading, updateListLoading] = useState(false);
    const [crowdCalculateList, updateCrowdCalculateList] = useState([]);
    const [crowdCalculateTotal, updateCrowdCalculateTotal] = useState(0);

    useImperativeHandle(initRef, () => ({
        show: (type, selectedItems = []) => {
            updateVisible(true);
            updateStep(0);
            form.resetFields();
            form.setFieldsValue({
                temp_type: type || CROWD_TYPES.CDP_USER,
                temp_cdp_list: selectedItems,
            });
            updateSelectRows(selectedItems);
        },
    }));

    const onClose = () => {
        updateVisible(false);
    };

    const searchData = async () => {
        try {
            updateListLoading(true);
            const params = { pageIndex: pageInfo.pageIndex, pageSize: pageInfo.pageSize };
            params.applyType = applyType;
            if (temp_type == CROWD_TYPES.CDP_USER) {
                const {
                    data: { records = [], total },
                } = await getCrowdDefineQueryPage(params);
                updateCrowdCalculateList(records);
                updateCrowdCalculateTotal(total);
            } else if (temp_type == CROWD_TYPES.CDP_REAL) {
                const {
                    data: { records, total },
                } = await getCrowdRealListApi(params);
                updateCrowdCalculateList(records);
                updateCrowdCalculateTotal(total);
            } else if (temp_type == CROWD_TYPES.XDT_CALCULATE) {
                const params = {
                    pageIndex: pageInfo.pageIndex,
                    pageSize: pageInfo.pageSize,
                    applyType,
                };
                const {
                    data: { records, total },
                } = await getCrowdCalculateListApi(params);
                updateCrowdCalculateList(records);
                updateCrowdCalculateTotal(total);
            }
        } catch (error) {
        } finally {
            updateListLoading(false);
        }
    };
    useEffect(() => {
        if (visible && temp_type?.length) {
            searchData();
        }
    }, [pageInfo, temp_type, visible]);

    const onConfirm = async () => {
        if (step == 0 && temp_type == CROWD_TYPES.CDP_REAL) {
            updateStep(1);
            return;
        }
        const params = form.getFieldsValue();
        if (selectedRows?.length > 0 && params.linkTypeValues?.length) {
            selectedRows[0].linkTypeValue = params.linkTypeValues.join(',');
            delete params.linkTypeValues;
        }
        try {
            updateListLoading(true);
            const refluxParams = {};
            const item = selectedRows?.[0];
            refluxParams.crowdType = item.crowdType || item.crowdType;
            refluxParams.crowdId = item.crowdId || item.cdpCrowdId;
            refluxParams.crowdName = item.crowdName || item.cdpCrowdName;
            if (temp_type == CROWD_TYPES.CDP_REAL) {
                refluxParams.linkTypeValue = item.linkTypeValue;
                refluxParams.linkType = 'station_id';
            }
            const { data } = await refreshCrowdRefluxApi(refluxParams);
            if (temp_type == CROWD_TYPES.CDP_REAL && data?.crowdRuleId?.length) {
                selectedRows.map((ele) => (ele.ruleId = data?.crowdRuleId));
            }

            params.temp_cdp_list = selectedRows;
            if (data?.linkTypeValue && data?.linkTypeValue.length > 0) {
                data.linkTypeValue = data?.linkTypeValue?.split(',') || [];
            } else {
                data.linkTypeValue = [];
            }
            params.crowdRefluxData = data;
            callback?.(params);
            onClose();
        } catch (error) {
        } finally {
            updateListLoading(false);
        }
    };

    const columns = useMemo(() => {
        const list = [];
        if (temp_type == CROWD_TYPES.CDP_USER) {
            list.push(
                ...[
                    {
                        title: '人群画像ID',
                        dataIndex: 'crowdDefineId',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '人群画像名称',
                        dataIndex: 'cdpCrowdName',
                        width: 140,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '说明',
                        dataIndex: 'labelRemark',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '创建时间',
                        dataIndex: 'createTime',
                        width: 200,
                        render(text, record) {
                            return (
                                <span>
                                    {moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}
                                </span>
                            );
                        },
                    },
                    {
                        title: '版本更新时间',
                        dataIndex: 'updateTime',
                        width: 200,
                        render(text, record) {
                            return (
                                <span>
                                    {moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}
                                </span>
                            );
                        },
                    },
                ],
            );
        } else if (temp_type == CROWD_TYPES.CDP_REAL) {
            list.push(
                ...[
                    {
                        title: '人群画像ID',
                        dataIndex: 'crowdId',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '人群画像名称',
                        dataIndex: 'crowdName',
                        width: 140,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '说明',
                        dataIndex: 'crowdTypeName',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '创建时间',
                        dataIndex: 'createTime',
                        width: 200,
                        render(text, record) {
                            return (
                                <span>
                                    {moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}
                                </span>
                            );
                        },
                    },
                    {
                        title: '版本更新时间',
                        dataIndex: 'updateTime',
                        width: 200,
                        render(text, record) {
                            return (
                                <span>
                                    {moment(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}
                                </span>
                            );
                        },
                    },
                ],
            );
        } else if (temp_type == CROWD_TYPES.XDT_CALCULATE) {
            list.push(
                ...[
                    {
                        title: '人群画像ID',
                        dataIndex: 'crowdId',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '人群画像名称',
                        dataIndex: 'crowdName',
                        width: 140,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                    {
                        title: '说明',
                        dataIndex: 'labelRemark',
                        width: 120,
                        render(text, record) {
                            return <span title={text}>{text}</span>;
                        },
                    },
                ],
            );
        }
        return list;
    }, [temp_type]);

    const rowKey = useMemo(() => {
        if (temp_type == CROWD_TYPES.CDP_USER) {
            return 'cdpCrowdId';
        }
        if (temp_type == CROWD_TYPES.CDP_REAL || temp_type == CROWD_TYPES.XDT_CALCULATE) {
            return 'crowdId';
        }
        return undefined;
    }, [temp_type]);

    const rowSelection = useMemo(() => {
        if (!crowdCalculateList?.length) {
            return undefined;
        }
        if (temp_type == CROWD_TYPES.CDP_USER) {
            return {
                type: 'radio',
                checkStrictly: false,
                selectedRowKeys: selectedRows.map((item) => item[rowKey]),
                onChange: (selectedRowKeys, selectedRows) => {
                    for (let index = selectedRows.length - 1; index >= 0; index--) {
                        const element = selectedRows[index];
                        if (element.children) {
                            selectedRows.splice(index, 1);
                        }
                    }
                    updateSelectRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                    name: record[rowKey],
                }),
            };
        }
        if (temp_type == CROWD_TYPES.CDP_REAL || temp_type == CROWD_TYPES.XDT_CALCULATE) {
            return {
                type: 'radio',
                onChange: (selectedRowKeys, selectedRows) => {
                    updateSelectRows(selectedRows);
                },
                selectedRowKeys: selectedRows?.map((ele) => ele[rowKey]) || [],
            };
        }
        return undefined;
    }, [selectedRows, temp_type, crowdCalculateList]);

    return (
        <Modal
            title="添加目标用户"
            visible={visible}
            onCancel={onClose}
            okText={(step == 0 && temp_type == CROWD_TYPES.CDP_REAL && '下一步') || '添加'}
            cancelText={step == 1 ? '上一步' : '取消'}
            cancelButtonProps={{
                onClick: () => {
                    if (step == 1) {
                        updateStep(0);
                    } else {
                        onClose();
                    }
                },
            }}
            okButtonProps={{
                disabled: !selectedRows?.length,
                loading: listLoading,
            }}
            width={880}
            onOk={onConfirm}
        >
            <Form form={form}>
                {(step == 0 && (
                    <Fragment>
                        <Form.Item
                            name="temp_type"
                            label="人群画像类型"
                            required
                            initialValue={CROWD_TYPES.CDP_USER}
                        >
                            <Radio.Group
                                onChange={(e) => {
                                    updateCrowdCalculateList([]);
                                    updateCrowdCalculateTotal(0);
                                    updateSelectRows([]);
                                    onTableChange({ current: 1, pageSize: 10 });
                                }}
                            >
                                <Radio value={CROWD_TYPES.CDP_USER}>CDP用户分群</Radio>
                                <Radio value={CROWD_TYPES.CDP_REAL}>CDP实体标签</Radio>
                                <Radio value={CROWD_TYPES.XDT_CALCULATE}>人群计算</Radio>
                            </Radio.Group>
                        </Form.Item>

                        <TablePro
                            name={`${temp_type}_list`}
                            rowSelection={rowSelection}
                            loading={listLoading}
                            scroll={{ x: 'max-content' }}
                            rowKey={rowKey}
                            dataSource={crowdCalculateList}
                            columns={columns}
                            onChange={onTableChange}
                            pagination={{
                                current: pageInfo.pageIndex,
                                total: crowdCalculateTotal,
                                pageSize: pageInfo.pageSize,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 条`,
                            }}
                        />
                    </Fragment>
                )) || (
                    <Fragment>
                        <Form.Item name="temp_type" noStyle />
                        <Alert
                            message="「实体标签」需要过滤关联标识，例如：过滤标识为station_id，输入过滤值为111、222，则只会获取station_id为111、22的数，不填写代表不过滤"
                            type="info"
                            showIcon
                            style={{ marginBottom: '12px' }}
                        />
                        <Form.Item label="已选择画像" labelCol={{ flex: '0 0 160px' }}>
                            {selectedRows?.map(
                                ({ crowdId, crowdName }) => `${crowdId} | ${crowdName}`,
                            )}
                        </Form.Item>
                        <Form.Item
                            label="过滤关联标识参数值"
                            name={'linkTypeValues'}
                            labelCol={{ flex: '0 0 160px' }}
                        >
                            <Select
                                mode="tags"
                                style={{ width: '100%' }}
                                placeholder="请输入"
                                open={false}
                                allowClear
                                autoFocus
                                tokenSeparators={[',', '\n', '\t', ' ', '\r']}
                            />
                        </Form.Item>
                    </Fragment>
                )}
            </Form>
        </Modal>
    );
};

const TargetUserSelect = (props) => {
    const { applyType } = props;
    const addRef = useRef();
    return (
        <Fragment>
            <Form.Item noStyle name="temp_cdp_list" />
            <Form.Item noStyle name="temp_type" />
            <Form.Item noStyle name="crowdRefluxData" />

            <Form.Item
                noStyle
                shouldUpdate={(pre, after) =>
                    pre.temp_cdp_list !== after.temp_cdp_list || pre.temp_type !== after.temp_type
                }
            >
                {({ getFieldValue, setFieldsValue }) => {
                    const temp_cdp_list = getFieldValue('temp_cdp_list') || [];
                    const temp_type = getFieldValue('temp_type');
                    const crowdRefluxData = getFieldValue('crowdRefluxData') || {};
                    return (
                        <Fragment>
                            {temp_type != CROWD_TYPES.CDP_REAL &&
                                temp_type != CROWD_TYPES.XDT_CALCULATE && (
                                    <Form.Item
                                        label=" "
                                        name={'temp_btn'}
                                        colon={false}
                                        rules={[
                                            ({ getFieldValue }) => ({
                                                validator(rule, value) {
                                                    if (!temp_cdp_list?.length) {
                                                        return Promise.reject('请选择目标用户');
                                                    }
                                                    return Promise.resolve();
                                                },
                                            }),
                                        ]}
                                    >
                                        <Button
                                            type="primary"
                                            onClick={() => {
                                                addRef.current.show(temp_type, temp_cdp_list);
                                            }}
                                        >
                                            <PlusOutlined />
                                            添加目标用户
                                        </Button>
                                    </Form.Item>
                                )}

                            {(temp_cdp_list?.length && (
                                <Form.Item label=" " colon={false} wrapperCol={{ span: 24 }}>
                                    {temp_cdp_list?.map(
                                        ({
                                            cdpCrowdId,
                                            cdpCrowdName,
                                            crowdPortraitId,
                                            crowdPortraitName,
                                            crowdId,
                                            crowdName,
                                            index,
                                        }) => {
                                            let key = 'crowdPortraitId';
                                            let target = crowdPortraitId;
                                            let name = crowdPortraitName;
                                            if (temp_type == CROWD_TYPES.CDP_USER) {
                                                key = 'cdpCrowdId';
                                                target = cdpCrowdId;
                                                name = cdpCrowdName;
                                            } else if (
                                                temp_type == CROWD_TYPES.XDT_CALCULATE ||
                                                temp_type == CROWD_TYPES.CDP_REAL
                                            ) {
                                                key = 'crowdId';
                                                target = crowdId;
                                                name = crowdName;
                                            }
                                            return (
                                                <Tag
                                                    onClose={(e) => {
                                                        e.preventDefault();
                                                        const index = temp_cdp_list.findIndex(
                                                            (ele) => ele[key] == target,
                                                        );
                                                        temp_cdp_list.splice(index, 1);
                                                        setFieldsValue({
                                                            temp_cdp_list,
                                                            temp_type: !temp_cdp_list?.length
                                                                ? undefined
                                                                : temp_type,
                                                        });
                                                    }}
                                                    closable
                                                    key={index}
                                                    style={{ padding: '5px 7px' }}
                                                >
                                                    <Tag
                                                        color={
                                                            crowdRefluxData?.refluxStatusName ===
                                                            '已回流'
                                                                ? '#2db7f5'
                                                                : '#f50'
                                                        }
                                                    >
                                                        {crowdRefluxData?.refluxStatusName}
                                                    </Tag>
                                                    <span style={{ marginRight: '10px' }}>
                                                        {crowdRefluxData?.crowdRefluxRet}
                                                    </span>
                                                    {crowdRefluxData?.linkTypeValue?.map(
                                                        (item, index) => (
                                                            <Tag key={index} color="#87d068">
                                                                {item}
                                                            </Tag>
                                                        ),
                                                    )}
                                                </Tag>
                                            );
                                        },
                                    )}
                                </Form.Item>
                            )) ||
                                undefined}

                            <TargetUserSelectModal
                                initRef={addRef}
                                applyType={applyType}
                                callback={(items) => {
                                    setFieldsValue(items);
                                }}
                            />
                        </Fragment>
                    );
                }}
            </Form.Item>
        </Fragment>
    );
};

export default TargetUserSelect;
