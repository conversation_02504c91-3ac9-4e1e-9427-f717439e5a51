import { useMemo } from 'react';
import { Tooltip, Input } from 'antd';
import { useEffect } from 'react';
import lodash from 'lodash';

const formatNumber = (value) => {
    value += '';
    const list = value.split('.');
    const prefix = list[0].charAt(0) === '-' ? '-' : '';
    let num = prefix ? list[0].slice(1) : list[0];
    let result = '';
    while (num.length > 3) {
        result = `,${num.slice(-3)}${result}`;
        num = num.slice(0, num.length - 3);
    }
    if (num) {
        result = num + result;
    }
    return `${prefix}${result}${list[1] ? `.${list[1]}` : ''}`;
};

const NumericInput = (props) => {
    const { value = '', isHide, onChange, onBlur } = props;

    useEffect(() => {}, []);

    const onChangeEvent = (e) => {
        const { value: newValue } = e.target;
        const reg = /^-?\d*(\.\d*)?$/;
        if (
            (!lodash.isNaN(newValue) && reg.test(newValue)) ||
            newValue === '' ||
            newValue === '-'
        ) {
            onChange && onChange(newValue);
        }
    };

    // '.' at the end or only '-' in the input box.
    const onBlurEvent = (e) => {
        let valueTemp = value;
        if (value.charAt(value.length - 1) === '.' || value === '-') {
            valueTemp = value.slice(0, -1);
        }
        onChange && onChange(valueTemp.replace(/0*(\d+)/, '$1'));
        onBlur && onBlur();
    };

    const title = useMemo(
        () =>
            value ? (
                <span className="numeric-input-title">
                    {value !== '-' ? formatNumber(value) : '-'}
                </span>
            ) : (
                '请输入数字'
            ),
        [value],
    );
    return isHide ? (
        <Input
            placeholder="请输入数字"
            maxLength={25}
            {...props}
            onFocus={onChangeEvent}
            onChange={onChangeEvent}
            onBlur={onBlurEvent}
        />
    ) : (
        <Tooltip
            trigger={['focus']}
            title={title}
            placement="topLeft"
            overlayClassName="numeric-input"
        >
            <Input
                placeholder="请输入数字"
                maxLength={25}
                {...props}
                onFocus={onChangeEvent}
                onChange={onChangeEvent}
                onBlur={onBlurEvent}
            />
        </Tooltip>
    );
};

export default NumericInput;
