import React, { useEffect, useMemo, useState } from 'react';
import { Button, Collapse, Popconfirm, Space } from 'antd';
import { DeleteOutlined, DownCircleOutlined, UpCircleOutlined } from '@ant-design/icons';
import { randomStringCommon } from '@/utils/utils';
const { Panel } = Collapse;

const CollapseFormList: React.FC<
    API.CommonFormItem<any[]> & {
        title?: string;
        maxCount?: number;
        children: React.ReactElement;
        hideAdd?: boolean;
        hideDel?: boolean;
        hideSort?: boolean;
        hideLayout?: boolean;
        extra?: (index: number, record: any) => React.ReactNode;
    }
> = (props) => {
    const {
        value,
        onChange,
        title,
        disabled = false,
        maxCount = 10,
        children,
        hideAdd = false,
        hideDel = false,
        hideSort = false,
        hideLayout = false,
        extra,
    } = props;

    const [tableList, updateTableList] = useState<any[]>([]);

    useEffect(() => {
        if (value instanceof Array) {
            updateTableList(value);
        }
    }, [value]);

    const defaultActiveKey = useMemo(() => {
        const list = [];
        for (let index = 0; index < maxCount; index++) {
            list.push(index);
        }
        return list;
    }, [maxCount]);

    const updateFormItem = (newTable: any[]) => {
        updateTableList(newTable);
        onChange && onChange(newTable);
    };

    const addTableItem = () => {
        const newTable = [...tableList];
        newTable.push({});
        updateFormItem(newTable);
    };
    const updateTableItemByIndex = (index: number, info: any) => {
        const newTable = [...tableList];
        newTable.splice(index, 1, info);
        updateFormItem(newTable);
    };
    const delTableItemByIndex = (index: number) => {
        const newTable = [...tableList];
        newTable.splice(index, 1);
        updateFormItem(newTable);
    };

    const upMoveItem = (index: number) => {
        const newTable = [...tableList];
        if (newTable[index - 1]) {
            const temp = newTable[index - 1];
            newTable[index - 1] = newTable[index];
            newTable[index] = temp;
            updateFormItem(newTable);
        }
    };
    const downMoveItem = (index: number) => {
        const newTable = [...tableList];
        if (newTable[index + 1]) {
            const temp = newTable[index + 1];
            newTable[index + 1] = newTable[index];
            newTable[index] = temp;
            updateFormItem(newTable);
        }
    };

    return (
        <>
            <p>
                {!hideAdd && !disabled && tableList?.length < maxCount && (
                    <Button
                        type="primary"
                        onClick={() => {
                            addTableItem();
                        }}
                    >{`新增${title}`}</Button>
                )}
            </p>

            {tableList?.length > 0 &&
                (!hideLayout ? (
                    <Collapse defaultActiveKey={defaultActiveKey} expandIconPosition={'end'}>
                        {tableList.map((item, index) => {
                            return (
                                <Panel
                                    header={
                                        title ? (
                                            `${title}${index + 1}`
                                        ) : (
                                            <div
                                                style={{
                                                    maxWidth: '95%',
                                                    whiteSpace: 'pre-wrap',
                                                    wordBreak: 'break-all',
                                                    wordWrap: 'break-word',
                                                }}
                                            >
                                                {item.title}
                                            </div>
                                        )
                                    }
                                    key={index}
                                    extra={
                                        <Space
                                            onClick={(event) => {
                                                event.stopPropagation();
                                            }}
                                        >
                                            {extra && extra(index, item)}

                                            {!disabled && (
                                                <Space>
                                                    {!hideSort && index > 0 && (
                                                        <UpCircleOutlined
                                                            style={{
                                                                fontSize: '20px',
                                                            }}
                                                            onClick={() => {
                                                                upMoveItem(index);
                                                            }}
                                                        />
                                                    )}
                                                    {!hideSort && index < tableList?.length - 1 && (
                                                        <DownCircleOutlined
                                                            style={{
                                                                fontSize: '20px',
                                                            }}
                                                            onClick={() => {
                                                                downMoveItem(index);
                                                            }}
                                                        />
                                                    )}

                                                    {!hideDel && (
                                                        <Popconfirm
                                                            title="是否删除"
                                                            okText="确定"
                                                            cancelText="取消"
                                                            onConfirm={async () => {
                                                                try {
                                                                    delTableItemByIndex(index);
                                                                    return;
                                                                } catch (error) {
                                                                    return Promise.reject(error);
                                                                }
                                                            }}
                                                        >
                                                            <DeleteOutlined
                                                                style={{
                                                                    fontSize: '20px',
                                                                }}
                                                            />
                                                        </Popconfirm>
                                                    )}
                                                </Space>
                                            )}
                                        </Space>
                                    }
                                >
                                    {React.cloneElement(children, {
                                        ...children.props,
                                        index,
                                        value: item,
                                        onChange: (info: any) => {
                                            updateTableItemByIndex(index, info);
                                        },
                                    })}
                                </Panel>
                            );
                        })}
                    </Collapse>
                ) : (
                    tableList.map((item, index) => {
                        return (
                            <div key={index}>
                                {React.cloneElement(children, {
                                    ...children.props,

                                    index,
                                    value: item,
                                    onChange: (info: any) => {
                                        updateTableItemByIndex(index, info);
                                    },
                                })}
                            </div>
                        );
                    })
                ))}
        </>
    );
};
export default CollapseFormList;
