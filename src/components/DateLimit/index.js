import { Fragment, useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Form, Button, Row, Col, Checkbox, Radio, Card, TimePicker } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import { isEmpty } from '@/utils/utils';

const FormItem = Form.Item;

const { RangePicker: TimeRangePicker } = TimePicker;

// 日期多选项
const dataOptions = [
    {
        label: '周一',
        value: '1',
    },
    {
        label: '周二',
        value: '2',
    },
    {
        label: '周三',
        value: '3',
    },
    {
        label: '周四',
        value: '4',
    },
    {
        label: '周五',
        value: '5',
    },
    {
        label: '周六',
        value: '6',
    },
    {
        label: '周日',
        value: '7',
    },
];

const DateLimitItem = (props, ref) => {
    const {
        form,
        formItemLayout,
        label = '可使用日期',
        flagName = 'dateFlag',
        dateName = 'dateLimit',
        disabled,
        maxLimit = 5,
        rules,
    } = props;

    useEffect(() => {}, []);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        init: (list) => {},
    }));

    const canIadd = () => {
        let hasAdd = true;
        const formItemData = form.getFieldValue(dateName);
        if (!isEmpty(formItemData)) {
            if (formItemData.length >= maxLimit) {
                hasAdd = false;
            }
        }
        return !disabled && hasAdd;
    };

    return (
        <Fragment>
            <Form.Item label={label} {...formItemLayout} required>
                <Form.Item noStyle name={flagName} initialValue={'01'}>
                    <Radio.Group disabled={disabled}>
                        <Radio value="01">不限制</Radio>
                        <Radio value="02">限制</Radio>
                    </Radio.Group>
                </Form.Item>

                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues[flagName] !== curValues[flagName]
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const flagNameValue = getFieldValue(flagName);
                        if (flagNameValue == '02') {
                            // 限制
                            return (
                                <Form.Item name={dateName} noStyle initialValue={[{}]}>
                                    <Form.List name={dateName}>
                                        {(fields, { add, remove }) => (
                                            <Card
                                                bordered={false}
                                                bodyStyle={{
                                                    padding: '12px 0',
                                                }}
                                            >
                                                {fields.map((field, index) => (
                                                    <Card
                                                        bordered={false}
                                                        key={index}
                                                        bodyStyle={{
                                                            padding: '12px 0',
                                                        }}
                                                    >
                                                        <Row>
                                                            <Col flex="1">
                                                                <Form.Item
                                                                    {...field}
                                                                    name={[
                                                                        field.name,
                                                                        'availableDay',
                                                                    ]}
                                                                    validateTrigger={[
                                                                        'onChange',
                                                                        'onBlur',
                                                                    ]}
                                                                    key={`availableDay_${field.key}`}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请选择可用日期',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <Checkbox.Group
                                                                        disabled={disabled}
                                                                        options={dataOptions}
                                                                    />
                                                                </Form.Item>

                                                                <Form.Item
                                                                    {...field}
                                                                    validateTrigger={[
                                                                        'onChange',
                                                                        'onBlur',
                                                                    ]}
                                                                    key={`period_${field.key}`}
                                                                    name={[field.name, 'period']}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message: '请选择时段',
                                                                        },
                                                                        () => ({
                                                                            validator(rule, value) {
                                                                                const dateValue =
                                                                                    getFieldValue(
                                                                                        dateName,
                                                                                    );
                                                                                let hasRepeat = false;
                                                                                if (
                                                                                    !isEmpty(
                                                                                        dateValue,
                                                                                    )
                                                                                ) {
                                                                                    dateValue.forEach(
                                                                                        (
                                                                                            element,
                                                                                            eleIndex,
                                                                                        ) => {
                                                                                            if (
                                                                                                eleIndex !=
                                                                                                index
                                                                                            ) {
                                                                                                const curItem =
                                                                                                    dateValue[
                                                                                                        index
                                                                                                    ];
                                                                                                if (
                                                                                                    !isEmpty(
                                                                                                        curItem.availableDay,
                                                                                                    ) &&
                                                                                                    !isEmpty(
                                                                                                        element.availableDay,
                                                                                                    )
                                                                                                ) {
                                                                                                    const formatDay =
                                                                                                        [
                                                                                                            ...new Set(
                                                                                                                [
                                                                                                                    ...curItem.availableDay,
                                                                                                                    ...element.availableDay,
                                                                                                                ],
                                                                                                            ),
                                                                                                        ];
                                                                                                    if (
                                                                                                        formatDay.length <
                                                                                                        curItem
                                                                                                            .availableDay
                                                                                                            .length +
                                                                                                            element
                                                                                                                .availableDay
                                                                                                                .length
                                                                                                    ) {
                                                                                                        // 当前日期和已配置项有重叠
                                                                                                        if (
                                                                                                            curItem.period
                                                                                                        ) {
                                                                                                            const [
                                                                                                                curStart,
                                                                                                                curEnd,
                                                                                                            ] =
                                                                                                                curItem.period;
                                                                                                            const [
                                                                                                                eleStart,
                                                                                                                eleEnd,
                                                                                                            ] =
                                                                                                                element.period;
                                                                                                            if (
                                                                                                                curStart &&
                                                                                                                curEnd &&
                                                                                                                eleStart &&
                                                                                                                eleEnd
                                                                                                            ) {
                                                                                                                if (
                                                                                                                    curStart.isBetween(
                                                                                                                        eleStart,
                                                                                                                        eleEnd,
                                                                                                                        'second',
                                                                                                                    ) ||
                                                                                                                    curEnd.isBetween(
                                                                                                                        eleStart,
                                                                                                                        eleEnd,
                                                                                                                        'second',
                                                                                                                    ) ||
                                                                                                                    curStart.isSame(
                                                                                                                        eleStart,
                                                                                                                        'second',
                                                                                                                    )
                                                                                                                ) {
                                                                                                                    hasRepeat = true;
                                                                                                                }
                                                                                                            }
                                                                                                        }
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                        },
                                                                                    );
                                                                                }

                                                                                if (hasRepeat) {
                                                                                    return Promise.reject(
                                                                                        '已设置重复时间段',
                                                                                    );
                                                                                }

                                                                                return Promise.resolve();
                                                                            },
                                                                        }),
                                                                    ]}
                                                                >
                                                                    <TimeRangePicker
                                                                        style={{ width: '100%' }}
                                                                        disabled={disabled}
                                                                        showTime={{
                                                                            format: 'HH:mm',
                                                                            // defaultValue: [
                                                                            //     moment('00:00', 'HH:mm'),
                                                                            //     moment('11:59', 'HH:mm'),
                                                                            // ],
                                                                        }}
                                                                        format="HH:mm"
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            {disabled ? null : (
                                                                <Col>
                                                                    <MinusCircleOutlined
                                                                        className={
                                                                            styles[
                                                                                'dynamic-delete-button'
                                                                            ]
                                                                        }
                                                                        style={{
                                                                            margin: '0 8px',
                                                                        }}
                                                                        onClick={() => {
                                                                            remove(field.name);
                                                                        }}
                                                                    />
                                                                </Col>
                                                            )}
                                                        </Row>
                                                    </Card>
                                                ))}
                                                <FormItem label="">
                                                    <Button
                                                        disabled={!canIadd()}
                                                        type="dashed"
                                                        onClick={() => {
                                                            add();
                                                        }}
                                                        style={{ width: '200px' }}
                                                    >
                                                        <PlusOutlined />
                                                        添加
                                                    </Button>
                                                </FormItem>
                                            </Card>
                                        )}
                                    </Form.List>
                                </Form.Item>
                            );
                        }
                        return null;
                    }}
                </Form.Item>
            </Form.Item>
        </Fragment>
    );
};
export default forwardRef(DateLimitItem);
