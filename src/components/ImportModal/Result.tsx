import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message, Space, Typography, Form, Radio, Input, Spin } from 'antd';

import { exportFailFileApi } from '@/services/Marketing/ReadjustApi';
import { useRequest } from 'ahooks';
import { localDownloadFileToExcel } from '@/utils/utils';
const IMPORT_TYPES = {
    NAMES: '01',
    IDS: '02',
};
const { TextArea } = Input;
type IMPORT_RESULT_VO = {
    importFailNum?: number;
    importSuccNum?: number;
    successList?: any[];
    failList?: any[];
    batchId?: string;
};
const ImportResultModal = (props: any, ref: any) => {
    const { onConfirm, zIndex, downloadColumns = [], downloadFileName = '' } = props;
    const [visible, toggleVisible] = useState(false);

    const [resultInfo, updateResultInfo] = useState<IMPORT_RESULT_VO>();

    useImperativeHandle(ref, () => {
        return {
            open: (params?: IMPORT_RESULT_VO) => {
                updateResultInfo(params);
                openModalEvent();
            },
        };
    });
    const openModalEvent = () => {
        toggleVisible(true);
    };
    const closeModalEvent = () => {
        toggleVisible(false);
        updateResultInfo(undefined);
    };

    const submitEvent = async () => {
        onConfirm && onConfirm(resultInfo?.successList);
        closeModalEvent();
    };

    const { run: exportFailEvent, loading: exportLoading } = useRequest(
        async () => {
            try {
                if (resultInfo?.batchId) {
                    const params = {
                        batchId: resultInfo?.batchId,
                    };
                    await exportFailFileApi(params);
                } else {
                    const { failList } = resultInfo ?? {};
                    const tableData: any[] = [];
                    failList?.forEach((item, index) => {
                        const col: Record<string, any> = {};
                        downloadColumns.forEach((colItem: any) => {
                            const name = colItem.title;
                            const key = colItem.dataIndex;
                            if (key) {
                                const value = item[key];
                                col[name] = value;
                            }
                        });
                        tableData.push(col);
                    });
                    localDownloadFileToExcel({ data: tableData, fileName: downloadFileName });
                }

                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
            throttleWait: 2000,
        },
    );
    return (
        <Modal
            title="导入结果"
            visible={visible}
            width={550}
            onCancel={closeModalEvent}
            destroyOnClose
            onOk={submitEvent}
            okText="确定"
            cancelText="取消"
            zIndex={zIndex}
        >
            <Space>
                已成功导入{resultInfo?.importSuccNum || resultInfo?.successList?.length || 0}
                条数据，其中
                {resultInfo?.importFailNum || 0}
                条失败
                {resultInfo?.failList && (
                    <Spin spinning={exportLoading}>
                        <Typography.Link
                            onClick={() => {
                                exportFailEvent();
                            }}
                        >
                            下载失败文件
                        </Typography.Link>
                    </Spin>
                )}
            </Space>
        </Modal>
    );
};
export default forwardRef(ImportResultModal);
