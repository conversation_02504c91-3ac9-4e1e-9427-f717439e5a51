import { Button, Form, Modal, Result, Upload, message, Space } from 'antd';
import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';

import { downloadProfitRuleApi } from '@/services/FinanceManage/ProfitRuleApi';

const FormItem = Form.Item;

import { CDN_STATIC_URL } from '@/config/global';

/**
 * 导入模板弹窗
 */
const ImportModal = (props, ref) => {
    const {
        title = '批量导入',
        name,
        downLoadPath,
        onUpload,
        onDoanLoadError,
        surfix = ['.xls', '.xlsx'],
        modelExtra,
        extra,
        zIndex,
    } = props;

    const [uploadLoading, changeUploadLoading] = useState(false);
    const [showResult, changeShowResult] = useState(false); // 显示上传结果
    const [uploadStatus, changeUploadStatus] = useState(false); // 上传状态
    const [resultMsg, changeResultMsg] = useState(''); // 上传结果
    const [errorImportTime, setErrorImportTime] = useState(null); // 导入失败文件标识

    const [showImportView, toggleImportView] = useState(false); // 导入弹窗状态
    useImperativeHandle(ref, () => ({
        show: () => {
            toggleImportView(true);
        },
        close: () => {
            toggleImportView(false);
            changeShowResult(false);
        },
    }));

    useEffect(
        () => () => {
            changeShowResult(false);
            changeResultMsg('');
        },
        [],
    );

    const downLoadFile = () => {
        const a = document.createElement('a');
        a.setAttribute('href', `${CDN_STATIC_URL}${downLoadPath}`);
        a.setAttribute('id', 'putImport');
        // 防止反复添加
        if (document.getElementById('putImport')) {
            document.body.removeChild(document.getElementById('putImport'));
        }
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a); // 下载完成移除元素
    };

    const downErrorFileEvent = () => {
        if (onDoanLoadError) {
            onDoanLoadError(errorImportTime);
        } else {
            downloadProfitRuleApi(errorImportTime);
        }
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            const isXls =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
            if (!isXls) {
                message.error(`只支持${(surfix?.join && surfix.join('、')) || ''}文件!`);
                rej();
                return;
            }

            res();
        });
    const uploadOptions = {
        name,
        showUploadList: false,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const formData = new FormData();

                formData.append(filename, file);
                changeUploadLoading(true);
                if (onUpload) {
                    onUpload(formData, ({ ret = 999, importTime, msg }) => {
                        changeUploadLoading(false);
                        changeUploadStatus(ret !== 'false');
                        setErrorImportTime(importTime);

                        onSuccess();
                        changeShowResult(true);
                        changeResultMsg(msg);
                    });
                }
            } catch (error) {
                console.log(error);
                if (error && error.msg) {
                    message.error(error.msg);
                }
            } finally {
                changeUploadLoading(false);
            }

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        beforeUpload,
    };
    return (
        <Modal
            title={title}
            width={400}
            visible={showImportView}
            onCancel={() => ref?.current?.close()}
            footer={null}
            maskClosable={false}
            zIndex={zIndex}
        >
            {!showResult ? (
                <Form>
                    <FormItem label="下载模板">
                        <Space>
                            <Button type="primary" onClick={downLoadFile}>
                                下载导入模板
                            </Button>
                            {modelExtra}
                        </Space>
                    </FormItem>
                    <FormItem label="上传文件">
                        <Upload {...uploadOptions}>
                            <Button loading={uploadLoading} type="primary">
                                上传文件
                            </Button>
                        </Upload>
                        <div>支持扩展名：{(surfix?.join && surfix?.join(' ')) || ''}</div>
                        {extra}
                    </FormItem>
                </Form>
            ) : uploadStatus ? (
                <Result status="success" title={resultMsg} />
            ) : (
                <Result
                    status="error"
                    title={
                        // <div style={{ textAlign: 'center' }}>
                        //     <div>{resultMsg}</div>
                        //     <div style={{ fontSize: '16px' }}>
                        //         <a onClick={downErrorFileEvent}>导出失败数据</a>
                        //     </div>
                        // </div>
                        resultMsg
                    }
                    subTitle={
                        errorImportTime && (
                            <a style={{ fontSize: '16px' }} onClick={downErrorFileEvent}>
                                导出失败数据
                            </a>
                        )
                    }
                />
            )}
        </Modal>
    );
};

export default forwardRef(ImportModal);
