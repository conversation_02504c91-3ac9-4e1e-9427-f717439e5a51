import { Form, DatePicker } from 'antd';
import moment from 'moment';
const { RangePicker } = DatePicker;

const DateSearchFormItem = (props) => {
    const { dateProps, name = 'dates', ...formProps } = props;
    return (
        <Form.Item {...formProps} name={name}>
            <RangePicker
                format="YYYY-MM-DD"
                ranges={{
                    今天: [moment(), moment()],
                    昨天: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    最近一周: [moment().subtract(1, 'weeks'), moment()],
                    最近一个月: [moment().subtract(1, 'month'), moment()],
                    最近三个月: [moment().subtract(3, 'month'), moment()],
                    最近半年: [moment().subtract(0.5, 'years'), moment()],
                }}
                allowClear
                {...dateProps}
            />
        </Form.Item>
    );
};

export default DateSearchFormItem;
