import React, { useEffect, useState, useRef, useMemo } from 'react';
import Debounce from 'lodash.debounce';
import autoHeight from '../autoHeight';
import ReactEcharts from 'echarts-for-react';
import 'echarts/lib/chart/bar';
import 'echarts/lib/chart/line';
import 'echarts/lib/chart/pie';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

import initEcharts from '@/utils/Charts';

const Bar = (props) => {
    const { height = 1, title, data } = props;
    const getOption = useMemo(() => {
        if (data) {
            const { x, y, legend } = data;

            if (x && y && legend) {
                // legName = [],
                // xData = [],
                // serDatas = [],
                const legName = legend.map((ele) => ele.name);
                const xData = x;
                const series = [];
                for (let index = 0; index < legend.length; index++) {
                    const legendItem = legend[index];
                    if (legendItem.value.indexOf('last') < 0) {
                        const serData = {
                            type: 'bar',
                            name: legendItem.name,
                            stack: 'sum',
                        };
                        const list = y.map((ele) => ele[legendItem.value]);
                        serData.data = list;

                        series.push(serData);
                    } else {
                        const serData = {
                            type: 'line',
                            name: legendItem.name,
                        };
                        const list = y.map((ele) => ele[legendItem.value]);
                        serData.data = list;

                        series.push(serData);
                    }
                }
                const options = {
                    legName,
                    xData,
                    series,
                };
                return initEcharts(options);
            }
        }
        return {};
    }, [data]);
    const computedHeight = height - 41;
    return (
        <div
            style={{
                height,
            }}
        >
            <div>
                {title && (
                    <h1
                        style={{
                            marginBottom: 20,
                            fontWeight: 'bold',
                            fontSize: '18px',
                        }}
                    >
                        {title}
                    </h1>
                )}

                <ReactEcharts
                    option={getOption}
                    style={{ height: title ? `${computedHeight}px` : `${height}px` }}
                />
            </div>
        </div>
    );
};
export default autoHeight()(Bar);
