import React, { useMemo } from 'react';
import Debounce from 'lodash.debounce';
import autoHeight from '../autoHeight';
import ReactEcharts from 'echarts-for-react';
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

import initEcharts from '@/utils/Charts';

const Bar = (props) => {
    const { height = 1, title, data } = props;
    const getOption = useMemo(() => {
        if (data) {
            const { x, y, legend } = data;
            if (x && y && legend) {
                // legName = [],
                // xData = [],
                // serDatas = [],
                const legName = legend.map((ele) => ele.name);
                const xData = x;
                const series = [];
                for (let index = 0; index < legend.length; index++) {
                    const legendItem = legend[index];
                    if (legendItem.value.indexOf('last') < 0) {
                        const serData = {
                            type: 'bar',
                            name: legendItem.name,
                            stack: 'sum',
                            barMaxWidth: 30,
                        };
                        const formatData = y.map((ele) => ele[legendItem.value]);
                        serData.data = formatData;

                        series.push(serData);
                    } else {
                        const serData = {
                            type: 'line',
                            name: legendItem.name,
                            smooth: true,
                        };
                        const formatData = y.map((ele) => ele[legendItem.value]);
                        serData.data = formatData;

                        series.push(serData);
                    }
                }
                const options = {
                    legName,
                    xData,
                    series,
                };
                return initEcharts(options);
            }
        }
        return {};
    }, [data]);
    const computedHeight = height - 41;

    return (
        <div
            style={{
                height,
            }}
        >
            <div>
                {title && (
                    <h1
                        style={{
                            marginBottom: 20,
                            fontWeight: 'bold',
                            fontSize: '18px',
                        }}
                    >
                        {title}
                    </h1>
                )}

                <ReactEcharts
                    notMerge
                    option={getOption}
                    style={{ height: title ? `${computedHeight}px` : `${height}px` }}
                />
            </div>
        </div>
    );
};
export default autoHeight()(Bar);
