import React, { useMemo, useState } from 'react';
import classNames from 'classnames';
import autoHeight from '../autoHeight';
import styles from './index.less';
import ReactFitText from 'react-fittext';

import ReactEcharts from 'echarts-for-react';
import 'echarts/lib/chart/pie';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

import initEchartsPie from '@/utils/Charts/pie';

const OverviewPie = (props) => {
    const { subTitle, total, style, height = 0, data: sourceData } = props;

    const getOption = useMemo(() => {
        if (sourceData) {
            const { legend, data = [] } = sourceData;

            const legName = legend;
            const series = [];
            for (let index = 0; index < data.length; index++) {
                const datalist = data[index];

                series.push(datalist);
            }
            const options = {
                legName,
                serData: series,
            };
            return initEchartsPie(options);
        }
        return {};
    }, [sourceData]);

    return (
        <div style={style}>
            <ReactFitText maxFontSize={25}>
                <div className={styles.chart}>
                    <ReactEcharts option={getOption} style={{ height: `${height}px` }} />

                    {(subTitle || total) && (
                        <div className={styles.total}>
                            {subTitle && <h4 className="pie-sub-title">{subTitle}</h4>}
                            {/* eslint-disable-next-line */}
                            {total && (
                                <div className="pie-stat">
                                    {typeof total === 'function' ? total() : total}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </ReactFitText>
        </div>
    );
};

export default autoHeight()(OverviewPie);
