import React, { useMemo } from 'react';
import autoHeight from '../autoHeight';
import styles from './index.less';

import ReactEcharts from 'echarts-for-react';
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

import initEcharts from '@/utils/Charts';

const OverviewLine = (props) => {
    const {
        title,
        height = 400,
        data: sourceData,
        unit,
        areaStyle = {
            opacity: 0.2,
        },
        tooltip = {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
    } = props;

    const getOption = useMemo(() => {
        if (sourceData) {
            const { legend, xData, yData } = sourceData;

            const legName = legend;
            const series = [];
            for (let index = 0; index < yData.length; index++) {
                const datalist = yData[index];
                const serData = {
                    type: 'line',
                    name: legend[index],
                    areaStyle: areaStyle,
                    smooth: true,
                };
                const data = datalist;
                serData.data = data;

                series.push(serData);
            }
            const options = {
                colorList: ['#3BA1FF', '#FBD438', '#F04864', '#F04864', '#9860E5', '#37CBCB'],
                legName,
                xData,
                series,
                yList: [unit],
                tooltip: tooltip,
            };
            return initEcharts(options);
        }
        return {};
    }, [sourceData]);

    return (
        <div
            className={styles.timelineChart}
            style={{
                height: height + 30,
            }}
        >
            <div>
                {title && <h1 className={styles['charts-title']}>{title}</h1>}
                <ReactEcharts notMerge option={getOption} style={{ height: `${height}px` }} />
            </div>
        </div>
    );
};

export default autoHeight()(OverviewLine);
