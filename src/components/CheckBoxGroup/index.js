import { copyObjectCommon } from '@/utils/utils';
import { Checkbox, Row, Col, Form, TreeSelect } from 'antd';
import { Fragment, useEffect, useMemo, useState } from 'react';

const { SHOW_CHILD, SHOW_PARENT, SHOW_ALL } = TreeSelect;
const FormItem = Form.Item;

const initTreeList = (treeList, disableIds, filterIds) => {
    //先过滤再禁用
    return (
        treeList
            ?.filter((ele) => !filterIds.includes(ele.codeValue))
            .map((ele) => {
                let children = [];
                if (ele.children instanceof Array && ele.children?.length > 0) {
                    children = initTreeList(ele.children, disableIds, filterIds);
                }
                let treeItem = {
                    title: ele.codeName,
                    value: ele.codeValue,
                    key: ele.codeValue,
                    disabled: disableIds.includes(ele.codeValue),
                };
                if (children?.length > 0) {
                    treeItem.children = children;
                    let hasChildCanSelect = false;
                    children.forEach((child) => {
                        if (!child.disabled) {
                            hasChildCanSelect = true;
                        }
                    });
                    if (!hasChildCanSelect) {
                        treeItem.disabled = true;
                    }
                }
                return treeItem;
            }) || []
    );
};
const filterTreeList = (treeList, disableIds) => {
    return treeList?.filter((ele) => {
        if (ele?.children instanceof Array && ele?.children?.length > 0) {
            ele.children = filterTreeList(ele.children, disableIds);
        }
        return !disableIds.includes(ele.codeValue);
    });
};

/**
 * 提取tree内所有的子集合成成一个一围数组
 */
const getChildList = (parentList) => {
    let list = [];
    if (parentList instanceof Array) {
        parentList.forEach((element) => {
            if (element?.children?.length > 0) {
                list = [...list, ...getChildList(element.children)];
            } else {
                list = [...list, element];
            }
        });
    } else {
        if (parentList?.children?.length > 0) {
            list = [...list, ...getChildList(parentList.children)];
        }
    }

    return list;
};

const CheckBoxGroup = (props) => {
    const {
        form,
        name = 'sourceChannel', //name
        parentName, //父级name
        disabled,
        selectList,
        initialValue,
        valueType = 'select', // 值的展示效果，默认复选框
        maxTagCount,
        allowClear = true,
        allKey = '999',
        rules,
        disableIds = [], //禁用id  会在组件里置灰
        filterIds = [], //过滤id  不会显示在组件里
        showCheckedStrategy = SHOW_CHILD,
        treeCheckable = true,
        hideAll = false, //隐藏全部选项
        onChange,
        onChangeCheckbox,
        ...allOptions
    } = props;
    const [checkAll, setCheckAll] = useState(false);

    const [indeterminate, setIndeterminate] = useState(false);

    //将name组装成数组
    let formItemName = [];
    if (typeof name === 'string') {
        formItemName = [name];
    } else if (name instanceof Array) {
        formItemName = name;
    }

    //拼出完整的表达层级name用于值的查询和修改
    let allName = [];
    if (typeof parentName === 'string') {
        allName = [parentName, ...formItemName];
    } else if (parentName instanceof Array) {
        allName = [...parentName, ...formItemName];
    } else {
        allName = formItemName;
    }

    //拷贝当前树结构用于过滤
    const copySelectList = useMemo(() => {
        return copyObjectCommon(selectList);
    }, [selectList]);

    const selectListOption = useMemo(() => {
        return selectList?.map((ele) => (
            <Checkbox
                value={ele.codeValue}
                disabled={disableIds.includes(ele.codeValue)}
                key={ele.codeValue}
                onChange={(e) => onChangeCheckbox && onChangeCheckbox(e)}
            >
                {ele.codeName}
            </Checkbox>
        ));
    }, [selectList, disableIds]);
    const selectListFilterDisableTree = useMemo(() => {
        return filterTreeList(copySelectList, [...filterIds, ...disableIds]);
    }, [selectList, copySelectList, disableIds, filterIds]);
    // const selectListFilterTree = useMemo(() => {
    //     return filterTreeList(copySelectList, [...filterIds]);
    // }, [selectList, copySelectList, filterIds]);
    // const selectListDisableTree = useMemo(() => {
    //     return filterTreeList(copySelectList, [...disableIds]);
    // }, [selectList, copySelectList, disableIds]);

    const formatSelectListFilterDisableList = useMemo(() => {
        let list = [];

        if (selectListFilterDisableTree) {
            return [...getChildList(selectListFilterDisableTree)];
        }
        return list;
    }, [selectListFilterDisableTree, selectList, copySelectList, disableIds, filterIds]);

    useEffect(() => {
        const itemlist = form.getFieldValue(allName);

        //计算除禁用和过滤的外已经被选到的 数据要保留 计算全选
        const extraList =
            itemlist?.filter?.(
                (ele) => !formatSelectListFilterDisableList.some((child) => child.codeValue == ele),
            ) || [];

        setCheckAll(
            itemlist?.length === formatSelectListFilterDisableList?.length + extraList?.length,
        );
        // console.log(222222, itemlist?.length, selectListFilterDisableTree?.length);
        setIndeterminate(
            !!itemlist?.length &&
                itemlist?.length < formatSelectListFilterDisableList?.length + extraList?.length,
        );
    }, [selectList, selectListFilterDisableTree, formatSelectListFilterDisableList]);

    const onGroupChange = (list) => {
        try {
            let valueList = typeof list === 'string' ? [list] : list;
            const newList = valueList?.filter((ele) => ele);

            if (newList && newList?.length >= 0) {
                //计算除禁用和过滤的外已经被选到的 数据要保留
                const extraList =
                    newList?.filter?.(
                        (ele) =>
                            !formatSelectListFilterDisableList.some(
                                (child) => child.codeValue == ele,
                            ),
                    ) || [];
                const resultList = newList;
                setFormValues(resultList);
                setCheckAll(
                    newList.length === formatSelectListFilterDisableList.length + extraList.length,
                );
                setIndeterminate(
                    !!newList.length &&
                        newList.length <
                            formatSelectListFilterDisableList.length + extraList.length,
                );
                onChange?.(resultList);
            } else {
                setFormValues([]);
                onChange?.([]);
            }
            return;
        } catch (error) {
            throw new Error(error);
        }
    };
    const onCheckAllChange = (e) => {
        const checked = e.target.checked;
        setIndeterminate(false);

        setCheckAll(checked);

        const itemlist = form.getFieldValue(allName);

        //计算除禁用和过滤的外已经被选到的 数据要保留

        let extraList = [];
        if (itemlist instanceof Array) {
            extraList = itemlist?.filter(
                (ele) => !formatSelectListFilterDisableList.some((child) => child.codeValue == ele),
            );
        }

        if (checked) {
            setFormValues([
                ...formatSelectListFilterDisableList.map((ele) => ele.codeValue),
                ...extraList,
            ]);
        } else {
            setFormValues([...extraList]);
        }
    };

    const setFormValues = (values) => {
        if (allName?.length > 1) {
            let index = 0;
            const superName = allName[index];
            let superObj = form.getFieldValue(superName);

            let obj;
            while (++index < allName.length) {
                if (!obj) {
                    obj = superObj[allName[index]];
                } else {
                    obj = obj[allName[index]];
                }
            }

            if (!obj) {
                return;
            }

            if (values.length === 0) {
                obj.splice(0, obj.length);
            } else {
                Object.keys(values)?.forEach((key) => {
                    obj[key] = values[key];
                });
            }

            form.setFieldsValue({ [superName]: superObj });
        } else {
            form.setFieldsValue({ [allName]: values });
        }
    };

    const treeOptions = useMemo(() => {
        if (!hideAll) {
            return [
                {
                    title: '全部',
                    value: allKey,
                    key: allKey,
                    children: initTreeList(selectList, disableIds, filterIds),
                },
            ];
        } else {
            return [...initTreeList(selectList, disableIds, filterIds)];
        }
    }, [selectList, disableIds, filterIds]);

    const renderItem = () => {
        if (valueType === 'checkbox') {
            return (
                <Row wrap={false} align={'top'}>
                    <Col flex="0 0 auto">
                        <Checkbox
                            onChange={onCheckAllChange}
                            indeterminate={indeterminate}
                            disabled={disabled}
                            checked={checkAll}
                        >
                            全选
                        </Checkbox>
                    </Col>
                    <Col flex={1}>
                        <FormItem
                            name={formItemName}
                            noStyle
                            initialValue={initialValue}
                            rules={rules}
                        >
                            <Checkbox.Group onChange={onGroupChange} disabled={disabled}>
                                {selectListOption}
                            </Checkbox.Group>
                        </FormItem>
                    </Col>
                </Row>
            );
        } else if (valueType === 'select') {
            return (
                <FormItem name={formItemName} noStyle initialValue={initialValue} rules={rules}>
                    <TreeSelect
                        treeData={treeOptions}
                        showCheckedStrategy={showCheckedStrategy}
                        treeCheckable={treeCheckable}
                        showArrow
                        treeDefaultExpandAll
                        placeholder="请选择"
                        onChange={onGroupChange}
                        disabled={disabled}
                        maxTagCount={maxTagCount}
                        allowClear={allowClear}
                        filterTreeNode={(inputValue, treeNode) => {
                            return treeNode?.title?.indexOf(inputValue) >= 0;
                        }}
                    />
                </FormItem>
            );
        }
    };

    return (
        <Fragment>
            <FormItem {...allOptions}>{renderItem()}</FormItem>
        </Fragment>
    );
};

export default CheckBoxGroup;
