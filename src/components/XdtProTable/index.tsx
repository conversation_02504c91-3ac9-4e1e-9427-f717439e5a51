import { ProTable, ProCard } from '@ant-design/pro-components';
import type { ProFormInstance, ActionType, ProTableProps } from '@ant-design/pro-components';
import type { TablePaginationConfig } from 'antd';
import { Button, Row, Space, notification } from 'antd';
import type { SearchProps } from 'antd/lib/input/Search';
import type { MutableRefObject, ReactNode } from 'react';
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useModel, useLocation } from 'umi';

import styles from './index.less';
import { isEmpty, formatTableColumns } from '@/utils/utils';

type Props = Partial<
    ProTableProps<any, any, any> & {
        //表格操作实例Ref
        bordered?: boolean;
        columns?: any[];

        hasSort?: boolean;
        dateFormatter?: string;
        defaultPageSize?: number;
        //请求方法
        request?: (p: any) => any;
        requestApi?: (params: any) => any;
        //格式化参数方法
        formatParams?: (data: any) => any;
        //缓存前置key
        prefixKey: string;
        //请求数据接口额外附带参数
        otherParams?: Record<string, any>;
        //对 返回数据多包一层的处理
        parentRecord?: string;
        //导出接口
        exportApi?: (params?: any) => any;
        // 导出事件，如果有值则在操作按钮中间插入导出按钮
        exportEvent?: false | ((params?: any) => Promise<any>) | ((params?: any) => void);
        //是否记录操作状态
        remeberState?: boolean;
        // 其余的操作按钮，会插入到导出和重置中间，数组格式
        restDoms?: ReactNode[];
        // 导出按钮loading状态
        exportLoading?: boolean;
        // 导出按钮名字
        exportButtonTitle?: string;
        // 如果需要持有数据源，实现此回调，arge1：查询结果数据，arge2：查询参数
        dataSourceLoadCallback?: (dataSource: any[], params: any) => void;
        // 查询中状态，用于请求受控模式
        quaryLoading?: boolean;
        //分页数量选项
        pageSizeOptions?: number[];
        pagination?: TablePaginationConfig;
        //表格搜索条件改变事件，点击搜索或者充值触发
        onSearchChange?: (params?: any) => void;
        //轻量化筛选器
        filter?: React.ReactNode;
        //表格增加tabs来切换数据
        tabsConfig?: {
            dataIndex: string; //字段名,
            options: any[]; //选项, 如果有全部选项，value请填写ALL，会自动过滤参数。如果有冲突，请全局修改😭
            onChange?: (value: string | undefined) => void; //当有onchange的时候，不会自动刷新表格
        };
        toolButtons?: ReactNode[];
        toolbarSearch?:
            | (SearchProps & {
                  onSearch: (searchValue: string) => Promise<false | void> | false | void;
              })
            | React.ReactNode
            | boolean;
        tabValue?: string;
        offsetHeader?: string;
        noRecords?: boolean; //后端接口返回字段是否没有records字段。直接使用data返回数组，不分页
    }
>;

export default (props: Props) => {
    const {
        actionRef = useRef<ActionType>(),
        bordered,
        columns,
        hasSort = true,
        dataSourceLoadCallback,
        defaultPageSize = 10,
        exportApi,
        exportButtonTitle = '导出',
        exportEvent,
        exportLoading,
        filter,
        formatParams,
        formRef = useRef<ProFormInstance>(),
        onReset,
        onSearchChange = () => {},
        options = { fullScreen: true, reload: true, setting: true, density: false },
        otherParams,
        pagination,
        pageSizeOptions = [10, 20, 50, 100],
        prefixKey = '',
        quaryLoading,
        remeberState = true,
        request,
        requestApi,
        restDoms,
        search = { labelWidth: 'auto', span: 8 },
        tabsConfig,
        toolButtons,
        toolbarSearch,
        tabValue,
        offsetHeader = '46',
        columnsState = {},
        noRecords = false,
    } = props;

    useEffect(() => {
        if (otherParams) {
            actionRef?.current?.reload();
        }
    }, [otherParams]);

    const [currentTab, setCurrentTab] = useState<string | undefined>(
        tabValue?.length ? tabValue : tabsConfig?.options?.[0]?.key,
    );
    const [innerExportLoading, updateInnerExportLoading] = useState<boolean>(false);
    const { pageOption, changePageOption, formOption, changeFormOption } = useModel('tablePersist');
    const { pathname } = useLocation();
    const tableKey = prefixKey + pathname;

    useEffect(() => {
        if (tabsConfig?.onChange) {
            tabsConfig.onChange(currentTab);
        } else {
            if (search === false) {
                actionRef?.current?.reload(true);
            } else {
                formRef?.current?.submit();
            }
        }
    }, [currentTab, actionRef, formRef]);

    useEffect(() => {
        if (formOption[tableKey] && remeberState) {
            if (formOption[tableKey]) {
                for (const key in columns) {
                    if (Object.prototype.hasOwnProperty.call(columns, key)) {
                        const element = columns[key as any];
                        if (
                            Object.prototype.hasOwnProperty.call(
                                formOption[tableKey],
                                element.dataIndex,
                            )
                        ) {
                            formRef?.current?.setFieldsValue({
                                [element.dataIndex]: formOption[tableKey][element.dataIndex],
                            });
                        }
                    }
                }
            }
            formRef?.current?.submit();
        }
        if (pageOption[tableKey] && remeberState) {
            // 因为submit会自动把page的current改为0，因此如果有page信息的话，需要异步再设置回来。
            setTimeout(() => {
                actionRef?.current?.setPageInfo?.({ ...pageOption?.[tableKey] });
                actionRef?.current?.reload();
            }, 0);
        }
    }, []);
    const [submitLoading, updateSubmitLoading] = useState(false);

    const formatColumns = useMemo(() => {
        if (!hasSort) {
            return columns;
        }
        return formatTableColumns(columns);
    }, [columns, hasSort]);

    return (
        <ProTable
            revalidateOnFocus={false}
            {...props}
            bordered={bordered}
            className={styles['xdt-pro-search']}
            columns={formatColumns}
            form={{
                labelAlign: 'left',
                ignoreRules: false,
            }}
            key={prefixKey}
            options={options}
            pagination={
                pagination === undefined
                    ? {
                          current: pageOption?.[tableKey]?.current || 1,
                          pageSize: pageOption?.[tableKey]?.pageSize || defaultPageSize || 10,
                          showQuickJumper: true,
                          showSizeChanger: true,
                          pageSizeOptions,
                      }
                    : pagination
            }
            request={
                request ||
                (requestApi &&
                    (async (values, sorter, filter) => {
                        console.debug('requestApi', values, sorter, filter);
                        try {
                            updateSubmitLoading(true);
                            // 表单搜索项会从 params 传入，传递给后端接口。
                            const params: Record<string, any> = { ...(values || {}) };
                            for (const key in params) {
                                if (Array.isArray(params[key]) && isEmpty(params[key])) {
                                    params[key] = undefined;
                                }
                            }
                            if (
                                tabsConfig &&
                                currentTab &&
                                !tabsConfig.onChange &&
                                currentTab !== 'ALL'
                            ) {
                                params[tabsConfig?.dataIndex] = currentTab;
                            }
                            const sorterKeys = Object.keys(sorter ?? {});
                            if (sorterKeys?.length) {
                                sorterKeys.map((ele) => {
                                    params.orderByColumn = ele;
                                    params.asc = sorter[ele] == 'ascend';
                                });
                            }
                            console.debug('sorterKeys', sorterKeys);
                            const formatEndParams = (formatParams &&
                                formatParams({ ...otherParams, ...params })) || {
                                ...(otherParams || {}),
                                ...params,
                            };
                            const options = {
                                ...formatEndParams,
                                pageIndex: params.current,
                                pageSize: params.pageSize,
                                current: undefined,
                            };
                            console.debug('options', options);
                            const { data } = await requestApi(options);
                            const { records = [], total = 0 } =
                                (noRecords ? { records: data, total: data?.length } : data) ?? {};

                            //缓存列表
                            changePageOption({
                                [tableKey]: {
                                    current: params.current,
                                    pageSize: params.pageSize,
                                    sorter,
                                    filter,
                                },
                            });
                            const formData = values;
                            delete formData.current;
                            delete formData.pageSize;
                            if (remeberState) {
                                changeFormOption({
                                    [tableKey]: formData,
                                });
                            }

                            dataSourceLoadCallback?.(records ?? [], { ...options, total });
                            return {
                                data: records,
                                success: true,
                                total: total,
                            };
                        } catch (error) {
                            console.error('xdtprotable request error:', error);
                            return {
                                data: undefined,
                                success: false,
                                total: 0,
                            };
                        } finally {
                            updateSubmitLoading(false);
                        }
                    }))
            }
            search={
                search && {
                    optionRender: (searchConfig = {}, props = {}, dom = []) => {
                        const doms: ReactNode[] = [
                            <Button
                                key="check"
                                onClick={() => {
                                    if (!request && !requestApi) {
                                        notification.error({
                                            message: '无访问权限',
                                        });
                                        return;
                                    }
                                    onSearchChange &&
                                        onSearchChange(
                                            formRef?.current?.getFieldsFormatValue?.(true) ||
                                                props?.form?.getFieldsValue(true),
                                        );
                                    props?.form?.submit();
                                }}
                                type="primary"
                                loading={submitLoading || quaryLoading}
                            >
                                查询
                            </Button>,
                            <Button
                                key="reset"
                                onClick={() => {
                                    onReset?.();
                                    props?.form?.resetFields();
                                    onSearchChange && onSearchChange();
                                    props?.form?.submit();
                                }}
                            >
                                重置
                            </Button>,
                        ];
                        if (exportEvent || exportApi) {
                            doms.splice(
                                1,
                                0,
                                <Button
                                    key="out"
                                    onClick={async () => {
                                        let exportResult = undefined;
                                        if (exportApi) {
                                            try {
                                                updateInnerExportLoading(true);
                                                const params =
                                                    formRef?.current?.getFieldsFormatValue?.(
                                                        true,
                                                    ) || props?.form?.getFieldsValue(true);
                                                if (tabsConfig) {
                                                    params[tabsConfig?.dataIndex] =
                                                        currentTab !== 'ALL'
                                                            ? currentTab
                                                            : undefined;
                                                }
                                                const formatEndParams = (formatParams &&
                                                    formatParams({
                                                        ...otherParams,
                                                        ...params,
                                                    })) || {
                                                    ...otherParams,
                                                    ...params,
                                                };
                                                exportResult = await exportApi({
                                                    ...formatEndParams,
                                                });
                                                updateInnerExportLoading(false);
                                            } catch (error) {
                                                updateInnerExportLoading(false);
                                            }
                                        }
                                        exportEvent &&
                                            exportEvent?.(
                                                exportApi
                                                    ? exportResult
                                                    : formRef?.current?.getFieldsFormatValue?.(
                                                          true,
                                                      ) || props?.form?.getFieldsValue(true),
                                            );
                                    }}
                                    loading={exportLoading || innerExportLoading}
                                    type="primary"
                                >
                                    {exportButtonTitle}
                                </Button>,
                            );
                        }
                        if (restDoms) {
                            doms.splice(doms.length - 1, 0, ...restDoms);
                        }
                        return doms;
                    },
                    ...search,
                }
            }
            scroll={props?.scroll || { x: 'max-content' }}
            sticky={{ offsetHeader: offsetHeader ? Number(offsetHeader) : undefined }}
            tableRender={(props, dom) => {
                if (toolButtons) {
                    return (
                        <ProCard ghost style={{ background: '#fff' }}>
                            <Space direction="vertical" size={0} style={{ width: '100%' }}>
                                <Row
                                    justify="start"
                                    key="toolbar-btns"
                                    style={{ padding: '24px 24px 0px 24px' }}
                                >
                                    <Space>{toolButtons}</Space>
                                </Row>
                                {dom}
                            </Space>
                        </ProCard>
                    );
                } else {
                    return dom;
                }
            }}
            toolbar={{
                filter: filter,
                search: toolbarSearch,
                menu: tabsConfig
                    ? {
                          type: 'tab',
                          activeKey: currentTab,
                          items: tabsConfig.options,
                          onChange: (key: string) => {
                              setCurrentTab(key);
                          },
                      }
                    : (undefined as any),
                ...props?.toolbar,
            }}
            columnsState={{
                persistenceKey: tableKey, // 持久化键名
                persistenceType: 'localStorage', // 持久化类型：localStorage 或 sessionStorage
                ...columnsState,
            }}
        />
    );
};
