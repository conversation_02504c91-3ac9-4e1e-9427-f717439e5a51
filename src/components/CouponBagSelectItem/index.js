import React, {
    Fragment,
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useMemo,
    useRef,
} from 'react';
import { Button, InputNumber, Select, Form, Modal, message, Space, Input, Row, Col } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { Option } = Select;

const FormItem = Form.Item;

const searchformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};

export const CouponBagSelectLayout = forwardRef((props, ref) => {
    const {
        dispatch,
        defaultIds, //默认选中的
        disabledIds, // 限制不允许勾选的id列表
        extInParams = { couponBagStatus: 1, popFlag: 1 }, //额外参数，默认只查可发放的券
        isMultiple,
        couponModel: { couponBagList, couponBagCount },
    } = props;

    const [selectList, updateSelectList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [listLoading, updateListLoading] = useState(false);

    const [form] = Form.useForm();
    const [cpnBagList, updateCpnBagList] = useState([]); //查询的列表
    const [cpnBagListTotal, updateCpnBagListTotal] = useState([]); //查询的列表

    // 替换场景用到的相关属性

    const [selectedRows, updateSelectedRows] = useState();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        () => ({
            tabType: '03',
        }),
        props,
        'couponBag',
    );

    useEffect(() => {
        if (defaultIds?.length) {
            defaultIds.forEach((id) => {
                selectList.push({ couponBagId: id });
            });
            updateSelectList([...selectList]);
        }
    }, [defaultIds]);

    useImperativeHandle(ref, () => ({
        selectList,
        resetData,
    }));

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const searchData = async () => {
        try {
            updateListLoading(true);
            const data = form.getFieldsValue();
            const params = {
                ...data,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
            };

            dispatch({
                type: 'couponModel/getCpnBagList',
                options: params,
            });
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        form.resetFields();
        form.setFieldsValue({ ...extInParams });
        updateSelectList([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 160,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '券包名',
            width: 200,
            dataIndex: 'couponBagName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包模板ID',
            width: 120,
            dataIndex: 'couponBagNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包价值',
            width: 100,
            dataIndex: 'singleAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券包数量',
            width: 100,
            dataIndex: 'couponBagNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const rowSelection = {
        type: !isMultiple ? 'radio' : 'checkbox',
        selectedRowKeys: selectList.map((item) => `${item.couponBagId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            if (!isMultiple) {
                // 替换只能单选，所以不用考虑之前是否已经选过其他优惠券的逻辑
                updateSelectList([...selectedRows]);
            } else {
                // 筛选出非当前页的勾选项，不予处理
                let otherCpns = selectList.filter(
                    (x) =>
                        couponBagList.filter((now) => now.couponBagId == x.couponBagId).length == 0,
                );
                updateSelectList([...otherCpns, ...selectedRows]);
            }
        },
        getCheckboxProps: (record) => ({
            disabled: record.pushPrizeId || disabledIds?.indexOf(record.couponBagId) >= 0,
            name: record.couponBagId,
        }),
    };

    return (
        <Fragment>
            <Form form={form} initialValues={{ ...extInParams }} scrollToFirstError>
                <FormItem name="couponBagStatus" noStyle />
                <FormItem name="popFlag" noStyle />
                <Row>
                    <Col span={10}>
                        <FormItem label="券包名" name="couponBagName" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={10}>
                        <FormItem label="券包模板ID" name="couponBagNo" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>

                    <Col span={4}>
                        <Space>
                            <Button type="primary" onClick={searchData}>
                                查询
                            </Button>
                            <Button onClick={resetData}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${record.couponBagId}`}
                dataSource={couponBagList}
                columns={columns}
                onChange={onTableChange}
                rowSelection={{
                    type: 'checkbox',
                    ...rowSelection,
                }}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponBagCount,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                sticky={{ offsetHeader: 0 }}
                size="small"
            />
        </Fragment>
    );
});

const CouponBagSelectModal = (props, ref) => {
    const { onFinish } = props;

    const [visible, updateVisible] = useState(false);
    const onClose = () => {
        updateVisible(false);
    };
    const [disabledBagIds, updateDisabledBagIds] = useState([]);
    const [defaultBagIds, updateDefaultBagIds] = useState([]);
    useImperativeHandle(ref, () => ({
        show: ({ disabledIds = [], defaultIds = [] }) => {
            updateVisible(true);
            updateDisabledBagIds([...disabledIds]);
            updateDefaultBagIds([...defaultIds]);
        },
        onClose,
    }));

    const cpnBagRef = useRef();
    useEffect(() => {
        if (visible) {
            cpnBagRef.current?.resetData();
        }
    }, [visible]);
    return (
        <Modal
            title={'添加券包'}
            destroyOnClose
            width={1200}
            visible={visible}
            onCancel={onClose}
            footer={null}
            maskClosable={false}
        >
            <CouponBagSelectLayout
                {...props}
                ref={cpnBagRef}
                disabledIds={disabledBagIds}
                defaultIds={defaultBagIds}
            />
            <br></br>
            <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                    type="primary"
                    onClick={() => {
                        onFinish?.(cpnBagRef.current.selectList);
                    }}
                >
                    提交
                </Button>
                <Button onClick={onClose}>取消</Button>
            </Space>
        </Modal>
    );
};

export default forwardRef(CouponBagSelectModal);
