import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { connect } from 'umi';
import { Form, Checkbox, Space, TreeSelect } from 'antd';

const FormItem = Form.Item;

const ActGroupCheckbox = (props) => {
    const {
        dispatch,
        form,
        global: { codeInfo = {} },
        disabled,
        disableIds, // 不可选择的人群id
        enableIds, // 可选择的人群id，如果配置了，其他不可选择的直接不显示了
        excludeIds, //设置隐藏人群id, 如果配置了就不会显示
        defaultCheckedAll,
        customerOnly, // 是否只选择自定义
        mode = 'multiple', // 是否支持多选，单选的情况下，没有全选按钮，没有复选框
        filePath,
        valueType = 'checkbox', // 值的展示效果，默认复选框
        hideAll = false, // 隐藏全选按钮
        ...extendProps
    } = props;

    const { custLoyalType: custLoyalTypeList } = codeInfo;
    // 记录全部可选类型的下标
    const [allTypeValues, updateAllTypeValues] = useState([]);
    useEffect(() => {
        if (!custLoyalTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'custLoyalType',
            });
        }
    }, []);

    // useEffect(() => {
    //     if (form.getFieldValue('activeCrowdFlag') == 1) {

    //     }
    //     console.log(form);
    // }, [form]);

    const setFormValues = (values) => {
        if (filePath?.length) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }

            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });

            form.setFieldsValue({ [superName]: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    // select属性
    const { maxTagCount, allowClear } = props;

    // 如果切换customerOnly类型，把淘汰掉的选中项缓存下来，切回来的时候可自动恢复选中效果
    const [tempUnselectItems, updateTempUnselectItems] = useState([]);

    useEffect(() => {
        if (valueType == 'select') {
            if (customerOnly) {
                const unSelectItems = [];
                const selectItems = [];
                const activeCrowd = form?.getFieldValue('activeCrowd');
                activeCrowd?.forEach((ele) => {
                    if (Number(ele) >= 1 && Number(ele) <= 7 && activeCrowd.indexOf(ele)) {
                        unSelectItems.push(ele);
                    } else {
                        selectItems.push(ele);
                    }
                });

                updateTempUnselectItems(unSelectItems);
                form?.setFieldsValue({ activeCrowd: selectItems });
            } else if (tempUnselectItems?.length) {
                const activeCrowd = form?.getFieldValue('activeCrowd');
                form?.setFieldsValue({ activeCrowd: [...activeCrowd, ...tempUnselectItems] });
                updateTempUnselectItems([]);
            }
        }
    }, [customerOnly]);

    useEffect(() => {
        if (defaultCheckedAll && valueType == 'select') {
            form.setFieldsValue({ activeCrowdFlag: '1' });
            changeAllState && changeAllState();
        }
    }, [defaultCheckedAll]);

    const changeAllState = () => {
        if (valueType == 'select') {
            if (custLoyalTypeOptions?.length && form?.getFieldValue('activeCrowdFlag') == '1') {
                const { children } = custLoyalTypeOptions[0];
                const indexes = [];
                children.forEach((ele) => {
                    indexes.push(ele.value);
                });
                form.setFieldsValue({ activeCrowd: indexes });
            }
        }
    };

    // checkbox属性
    const [indeterminate, setIndeterminate] = useState(false);
    const [checkAll, setCheckAll] = useState(false);

    const onChange = (list) => {
        // 判断是否全选
        if (list.length === allTypeValues.length) {
            setCheckAll(list.length === allTypeValues.length);
            setFormValues({ activeCrowd: list, activeCrowdFlag: '1' });
        } else {
            setCheckAll(false);
            setFormValues({ activeCrowd: list, activeCrowdFlag: '0' });
        }
        setIndeterminate(!!list.length && list.length < allTypeValues.length);
    };

    const onCheckAllChange = (e) => {
        if (e.target.checked) {
            // 全选
            setFormValues({ activeCrowd: allTypeValues, activeCrowdFlag: '1' });
        } else {
            // 取消全选
            setFormValues({ activeCrowd: [], activeCrowdFlag: '0' });
        }
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };

    useEffect(() => {
        if (valueType == 'checkbox') {
            let obj;
            if (filePath?.length) {
                let index = 0;
                const superName = filePath[index];
                let superObj = form.getFieldValue(superName);
                while (++index < filePath.length) {
                    if (!obj) {
                        obj = superObj[filePath[index]];
                    } else {
                        obj = obj[filePath[index]];
                    }
                }
            } else {
                obj = form.getFieldsValue();
            }

            // 处理默认全选逻辑
            if (defaultCheckedAll && obj?.activeCrowdFlag != 1) {
                setFormValues({ activeCrowdFlag: '1' });
            }

            if (
                defaultCheckedAll &&
                allTypeValues?.length &&
                obj?.activeCrowd?.length != allTypeValues?.length
            ) {
                const indexes = [];
                allTypeValues.forEach((ele) => {
                    indexes.push(ele);
                });
                setFormValues({ activeCrowd: indexes });
                setIndeterminate(false);
                setCheckAll(true);
            }
            const activeCrowd = form?.getFieldValue(
                (filePath && [...filePath, 'activeCrowd']) || 'activeCrowd',
            );
            if (activeCrowd?.length == allTypeValues.length && activeCrowd.length != 0) {
                setIndeterminate(false);
                setCheckAll(true);
            } else if (activeCrowd?.length) {
                setIndeterminate(true);
                setCheckAll(false);
            } else {
                setIndeterminate(false);
                setCheckAll(false);
            }
        }
    }, [defaultCheckedAll, allTypeValues, filePath, form]);

    // 公共属性Z
    const custLoyalTypeOptions = useMemo(() => {
        let list = [];
        if (valueType == 'select') {
            const children = [];
            if (mode == 'multiple') {
                list.push({ title: '全部', value: '999', key: '999', children });
            } else {
                list = children;
            }
            if (custLoyalTypeList) {
                custLoyalTypeList.forEach((ele) => {
                    if (customerOnly && Number(ele.codeValue) >= 1 && Number(ele.codeValue) <= 7) {
                        return;
                    }
                    children.push({
                        title: ele.codeName,
                        value: ele.codeValue,
                        key: ele.codeValue,
                    });
                });

                changeAllState && changeAllState();
            }
            return list;
        }
        const values = [];
        if (custLoyalTypeList) {
            custLoyalTypeList.forEach((ele) => {
                if (customerOnly && Number(ele.codeValue) >= 1 && Number(ele.codeValue) <= 7) {
                    return;
                }
                const item = {
                    label: ele.codeName,
                    value: ele.codeValue,
                    key: ele.codeValue,
                };
                if (disableIds) {
                    if (disableIds.includes(ele.codeValue)) {
                        item.disabled = true;
                    }
                }

                if (enableIds) {
                    if (enableIds.includes(ele.codeValue)) {
                        list.push(item);
                    } else {
                        item.disabled = true;
                    }
                } else {
                    if (!excludeIds || !excludeIds.includes(ele.codeValue)) {
                        list.push(item);
                    }
                }

                if (!item.disabled) {
                    values.push(ele.codeValue);
                }
            });
            updateAllTypeValues(values);
        }
        return list;
    }, [custLoyalTypeList, customerOnly, disableIds, enableIds, mode]);

    return (
        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
            {({ getFieldValue }) => {
                const activeCrowd = filePath
                    ? getFieldValue([...filePath])?.activeCrowd
                    : getFieldValue('activeCrowd');
                if (valueType == 'select') {
                    const activeCrowd = getFieldValue('activeCrowd');
                    if (typeof activeCrowd == 'string' && activeCrowd.indexOf(',') > -1) {
                        // 内容格式转换
                        form.setFieldsValue({ activeCrowd: activeCrowd?.split(',') });
                        return null;
                    }
                    // 考虑接口只给flag为1的情景，而没有传其他key的情况下，要默认全选
                    if (
                        custLoyalTypeOptions?.length &&
                        activeCrowd?.length != custLoyalTypeOptions[0].children?.length
                    ) {
                        changeAllState && changeAllState();
                    }
                }
                return (
                    <Fragment>
                        {(valueType == 'checkbox' && (
                            <FormItem {...extendProps}>
                                {!hideAll ? (
                                    <FormItem
                                        name={
                                            filePath
                                                ? [filePath[filePath.length - 1], 'activeCrowdFlag']
                                                : 'activeCrowdFlag'
                                        }
                                        noStyle
                                    >
                                        <Checkbox
                                            disabled={disabled || !allTypeValues.length}
                                            indeterminate={indeterminate}
                                            onChange={onCheckAllChange}
                                            checked={checkAll}
                                            style={{ marginTop: '4px' }}
                                        >
                                            全部
                                        </Checkbox>
                                    </FormItem>
                                ) : null}

                                <FormItem
                                    name={
                                        filePath
                                            ? [filePath[filePath.length - 1], 'activeCrowd']
                                            : 'activeCrowd'
                                    }
                                    {...extendProps}
                                    noStyle
                                >
                                    <Checkbox.Group
                                        disabled={disabled}
                                        options={custLoyalTypeOptions}
                                        value={activeCrowd}
                                        onChange={onChange}
                                    />
                                </FormItem>
                            </FormItem>
                        )) || (
                            <Fragment>
                                <FormItem {...extendProps} name="activeCrowd">
                                    <TreeSelect
                                        treeData={custLoyalTypeOptions}
                                        treeCheckable={mode == 'multiple' ? true : undefined}
                                        showArrow
                                        treeDefaultExpandAll
                                        placeholder="请选择"
                                        onChange={(value, f) => {
                                            if (custLoyalTypeOptions?.length) {
                                                if (
                                                    value?.length ==
                                                    custLoyalTypeOptions[0].children?.length
                                                ) {
                                                    form.setFieldsValue({ activeCrowdFlag: '1' });
                                                } else {
                                                    form.setFieldsValue({ activeCrowdFlag: '0' });
                                                }
                                            }
                                        }}
                                        disabled={disabled}
                                        maxTagCount={maxTagCount}
                                        allowClear={allowClear}
                                    />
                                </FormItem>
                                <FormItem name="activeCrowdFlag" noStyle />
                            </Fragment>
                        )}
                    </Fragment>
                );
            }}
        </FormItem>
    );
};

export default connect(({ global }) => ({
    global,
}))(ActGroupCheckbox);
