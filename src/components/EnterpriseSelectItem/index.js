import { useEffect, useMemo, useState } from 'react';
import { Form, Select, Popover } from 'antd';
import { connect } from 'dva';

const FormItem = Form.Item;
const { Option } = Select;

const OperSelectTypeItem = (props) => {
    const {
        // 私有属性
        dispatch,
        global: { enterpriseList, enterpriseChildList },
        currentUser,
        // 外部配置属性
        // 必传属性
        formItemLayout,
        form,
        // 可选属性
        extraOpers = [], // 额外的可选运营商，如果配置了，固定放在最前面
        disabledOperIds, // 部分运营商禁止选择，根据id判断
        disabled, // 整个控件禁用
        disabledFilter, // 类型筛选禁用，配置后查询全称类型
        ...rest
    } = props;

    // FormItem属性
    const {
        name = 'enterpriseId',
        rules,
        label = '客户',
        validateStatus,
        fieldKey,
        validateTrigger,
        help,
    } = rest || {};

    const {
        wrapperCol = { flex: 'auto' },
        labelCol = { flex: '0 0 auto' },
        labelAlign = 'left',
    } = formItemLayout || {};

    // Select属性
    const {
        placeholder = '支持全称、简称、合同号模糊搜索',
        onChange,
        onClear,
        mode,
        maxTagCount,
        showArrow,
        labelInValue = false,
    } = rest || {};

    const [nowOperators, changeNowOperators] = useState([]);
    const defaultValue = Object.keys(rest).includes('initialValue')
        ? rest.initialValue
        : currentUser.enterpriseId || undefined;

    const formatOperator = useMemo(() => {
        let opers = [];

        opers = enterpriseList;
        const resultArr = [...extraOpers, ...opers];
        return resultArr;
    }, [enterpriseList, disabledOperIds]);

    useEffect(() => {
        changeNowOperators([...formatOperator]);
    }, [formatOperator]);

    useEffect(() => {
        if (currentUser) {
            initOper();
        }
    }, [currentUser]);

    useEffect(() => {
        if (nowOperators?.length && currentUser.enterpriseId) {
            const foundItem = nowOperators?.find(
                (item) => item?.enterpriseId === currentUser.enterpriseId,
            );
            onChange && onChange(currentUser.enterpriseId, foundItem);
        }
    }, [nowOperators]);

    const initOper = async () => {
        if (currentUser.enterpriseId) {
            // 运营商登录
            form?.setFieldsValue({ [name]: currentUser.enterpriseId });
        }
        // 平台账号登录
        dispatch({
            type: 'global/getEnterpriseList',
            options: {},
        });
        if (!enterpriseChildList?.length && currentUser?.enterpriseId) {
            dispatch({
                type: 'global/queryEnterpriseChildList',
                options: { parentEnterpriseId: currentUser?.enterpriseId },
            });
        }
    };

    const filterKeys = ['enterpriseFullName', 'enterpriseName', 'contractNo'];

    const handleSearch = (txt) => {
        if (txt?.length) {
            const list = [];
            for (const item of formatOperator) {
                let matched = false;
                for (const key of filterKeys) {
                    if (item[key] && item[key].toLowerCase().indexOf(txt?.toLowerCase()) >= 0) {
                        matched = true;
                        break;
                    }
                }
                if (matched) {
                    list.push(item);
                }
            }
            changeNowOperators(list);
        } else {
            changeNowOperators(formatOperator);
        }
    };

    const handleFilter = (input, option) => true;
    return (
        <FormItem
            label={label}
            {...formItemLayout}
            // wrapperCol={wrapperCol}
            // labelCol={labelCol}
            labelAlign={labelAlign}
            name={name}
            rules={rules}
            help={help}
            validateStatus={validateStatus}
            initialValue={defaultValue}
            fieldKey={fieldKey}
            validateTrigger={validateTrigger}
        >
            <Select
                labelInValue={labelInValue}
                showSearch
                placeholder={placeholder}
                filterOption={handleFilter}
                onSearch={handleSearch}
                disabled={disabled || currentUser.enterpriseId}
                onChange={(value) => {
                    handleSearch(''); // 已选中的情况下，清空筛选条件
                    const foundItem = nowOperators?.find((item) => item?.enterpriseId === value);
                    onChange && onChange(value, foundItem);

                    if (value?.length) {
                        dispatch({
                            type: 'global/queryEnterpriseChildList',
                            options: { parentEnterpriseId: value },
                        });
                    } else {
                        dispatch({
                            type: 'global/updateGlobalProperty',
                            params: { enterpriseChildList: [] },
                        });
                    }
                }}
                mode={mode}
                onClear={onClear}
                allowClear
                maxTagCount={maxTagCount}
                showArrow={showArrow}
            >
                {nowOperators?.map((item, index) => {
                    if (disabledOperIds?.indexOf[item.enterpriseId] >= 0) {
                        return undefined;
                    }
                    return (
                        <Option value={item.enterpriseId} key={item.enterpriseId}>
                            {(item['enterpriseFullName'] ? 1 : 0) +
                                (item['enterpriseName'] ? 1 : 0) +
                                (item['contractNo'] ? 1 : 0) >
                            1 ? (
                                <Popover
                                    content={
                                        <div>
                                            {(item['enterpriseFullName'] && (
                                                <p>全称：{`${item['enterpriseFullName']}`}</p>
                                            )) ||
                                                null}
                                            {(item['enterpriseName'] && (
                                                <p>简称：{`${item['enterpriseName']}`}</p>
                                            )) ||
                                                null}
                                            {(item['contractNo'] && (
                                                <p>
                                                    合同号：
                                                    {`${item['contractNo']}`}
                                                </p>
                                            )) ||
                                                null}
                                        </div>
                                    }
                                    zIndex={1051}
                                    mouseEnterDelay={0.6}
                                >
                                    {item['enterpriseName']}
                                </Popover>
                            ) : (
                                item['enterpriseName']
                            )}
                        </Option>
                    );
                })}
            </Select>
        </FormItem>
    );
};

export default connect(({ user, global }) => ({
    global,
    currentUser: user.currentUser,
}))(OperSelectTypeItem);
