import { Fragment, useEffect, useState, useImperativeHandle, useMemo } from 'react';
import { Form, Select, TreeSelect, Button, Row, Col, Spin } from 'antd';
import { connect } from 'dva';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import OperSelectItem from '@/components/OperSelectItem';
import styles from '@/assets/styles/common.less';
import { getCityAndStationByOperIdApi } from '@/services/CommonApi';
import { SELECT_TYPES } from '@/config/declare';

const FormItem = Form.Item;
const { Option } = Select;

const { SHOW_CHILD } = TreeSelect;

const OperTreeSelectList = (props) => {
    const {
        form,
        dispatch,
        formItemLayout,
        initRef,
        label = '参与运营商',
        name = 'opertorList',
        disabled,
        // stationsListInfo,
        rules,
        currentUser,
        purchase,
        global: { operatorList = [] },
        openFlag,
        limitOperIds, // 限制哪几个运营商可选中，如果配置了，查询维度只支持运营商，无切换维度/添加/删除按钮，且传参格式限制为[{operId, citys:[city1, city2]}]，其中citys为可选
        limitCitys,
        exceptOperIds, // 限制哪几个运营商不可选中，打折立减的临时解决方案，只需传[{operId}]
        operChannelCodes, // 运营方式的筛选项，如果有值，会动态的变更已选中的场站
        disabledStationIds, // 某些场站禁用
        limitCityIds, // 限制只有哪些场站可被选择
        formTypeWhenLimitOperIds,
        premiumFlag,
        isChannelRule,
        channelId,
        cooperationPlatform,
        mode = '',
        disabledOperSelect, // 编辑状态下，支持场站维度的增减，运营商不可编辑
        operSelectParams, // 运营商接口入参
        onChangeOption, // 运营商item返回
    } = props;

    const [stationList, changeStationList] = useState([]);
    const [stationLoading, toggleStationLoading] = useState(false);

    useEffect(() => {
        // if (operatorList.length == 0) {
        dispatch({
            type: 'global/getOperatorList',
            options: {
                channelId,
                cooperationPlatform,
                cityStr: limitCitys || undefined,
                ...operSelectParams,
            },
        });
        // }
    }, [channelId, cooperationPlatform]);

    useEffect(() => {
        if (limitOperIds?.length && formTypeWhenLimitOperIds !== 'SELECT') {
            Promise.all(
                limitOperIds.map((ele, index) => {
                    if (ele.operId) {
                        return initCityAndStationEvent({
                            operId: ele.operId,
                            city: ele.citys,
                            index,
                        });
                    }
                    return Promise.resolve([]);
                }),
            ).then((data) => {
                changeStationList(data);
            });
            // let formatOperIds = [];
            // if (purchase == SELECT_TYPES.EXCEPTBUY) {
            //     let findOpers = operatorList.filter((ele) => ele.cooperationType === '02');
            //     formatOperIds = limitOperIds.filter((ele) =>
            //         findOpers.find((child) => child.operId === ele.operId),
            //     );
            // } else if (purchase == SELECT_TYPES.ONLYBUY) {
            //     let findOpers = operatorList.filter((ele) => ele.cooperationType !== '02');
            //     formatOperIds = limitOperIds.filter((ele) =>
            //         findOpers.find((child) => child.operId === ele.operId),
            //     );
            // }
            form.setFieldsValue({ [name]: limitOperIds });
        }
    }, [limitOperIds, purchase, operatorList, channelId]);

    const limitOperatorList = useMemo(() => {
        let list = operatorList;
        if (operatorList) {
            if (exceptOperIds) {
                const exceptOperIdStrings = exceptOperIds?.map((v) => v.operId);
                list = operatorList.filter((v) => exceptOperIdStrings.includes(v?.operId) == false);
            } else if (formTypeWhenLimitOperIds === 'SELECT' && limitOperIds) {
                const limitOperIdStrings = limitOperIds?.map((v) => v.operId);
                list = operatorList.filter((v) => limitOperIdStrings.includes(v?.operId));
            }
        }
        return list;
    }, [limitOperIds, exceptOperIds, operatorList, formTypeWhenLimitOperIds]);

    // 遍历出所有站点，给场站加禁用标识
    useEffect(() => {
        if (operChannelCodes || disabledStationIds || limitCityIds) {
            const formList = form.getFieldValue(name);
            const selectLoop = (list) => {
                for (const ele of list) {
                    if (!ele) {
                        continue;
                    }
                    if (Array.isArray(ele)) {
                        selectLoop(ele);
                    } else if (ele.children?.length) {
                        selectLoop(ele.children);
                        // 只要子项有一个是可用的，那父项就可用
                        const enabled = ele.children.some((subEle) => subEle.disabled != true);
                        ele.disabled = enabled == false;
                        if (ele.disabled == false && limitCityIds && ele.value != 'all') {
                            // 限制了城市，那不在可选列表的城市，是不支持可选的
                            ele.disabled =
                                limitCityIds.some((cityId) => ele.value == cityId) == false;
                            if (ele.disabled == true && enabled) {
                                // 父项禁用，那么所有子项都不可选
                                ele.children.forEach((subEle) => (subEle.disabled = true));
                            }
                        }
                    } else {
                        // 找到具体站点是否具备可选条件
                        let enabled = operChannelCodes
                            ? operChannelCodes.some((code) => ele.operationWay == code)
                            : true;
                        if (enabled && disabledStationIds) {
                            // 不被禁用字段包含，才可用
                            enabled = disabledStationIds.some((id) => ele.value == id) == false;
                        }
                        // 更新选择器
                        ele.disabled = enabled == false;
                        const stationIds = formList?.[0]?.stationId;
                        if (!enabled && stationIds?.some((stationId) => stationId == ele.value)) {
                            // 更新已选项
                            stationIds.splice(stationIds.indexOf(ele.value), 1);
                        }
                    }
                }
            };
            selectLoop(stationList);
            form.setFieldsValue({ [name]: formList });
        }
    }, [operChannelCodes, stationList, disabledStationIds, limitCityIds]);

    const [allStations, updateAllStations] = useState([]); // 同上，收集场站信息
    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        init: (list) => {
            if (!limitOperIds?.length) {
                // 有限制条件的走useeffect
                Promise.all(
                    list.map((ele, index) => {
                        if (ele.operId) {
                            return initCityAndStationEvent({
                                operId: ele.operId,
                                city: ele.citys,
                                index,
                            });
                        }
                        return Promise.resolve([]);
                    }),
                ).then((data) => {
                    changeStationList(data);
                });
            }
        },
        reset: () => {
            // 页面重置，用于modal弹出后所有内容清空
            form.setFieldsValue({ [name]: undefined });
        },
        formatter: () => {
            const promise = new Promise((resolve, reject) => {
                const formatterList = [];
                const formItemData = form.getFieldValue(name);
                formItemData instanceof Array &&
                    formItemData.forEach((ele, index) => {
                        let OperSelectItemitem = { ...ele };
                        // 因为场站是必填，所以不用判断所有场站的类型
                        if (OperSelectItemitem.stationId?.length) {
                            const operItem = limitOperatorList.find(
                                (operSubItem) =>
                                    (operSubItem.operId ||
                                        operSubItem.buildId ||
                                        operSubItem.operatorId) == OperSelectItemitem.operId ||
                                    (operSubItem.operId ||
                                        operSubItem.buildId ||
                                        operSubItem.operatorId) == OperSelectItemitem.buildId ||
                                    (operSubItem.operId ||
                                        operSubItem.buildId ||
                                        operSubItem.operatorId) == OperSelectItemitem.operatorId,
                            );
                            OperSelectItemitem.stationId.forEach((stationId) => {
                                const stationItem = allStations.find(
                                    (stationSubItem) => stationSubItem.stationId == stationId,
                                );
                                OperSelectItemitem = {
                                    ...OperSelectItemitem,
                                    ...stationItem,
                                    buildId: operItem?.operId,
                                    buildName: operItem?.operNickname || operItem?.operName,
                                    cooperationType: operItem?.cooperationType,
                                    operId: undefined,
                                };
                                formatterList.push(OperSelectItemitem);
                            });
                        }
                    });
                return resolve(formatterList);
            });
            return promise;
        },
    }));

    const changeOperEvent = async (value, index) => {
        if (!value?.length) {
            return;
        }
        try {
            toggleStationLoading(true);
            // 清空选中站点
            if (currentUser.operId != value) {
                const formItemData = form.getFieldValue(name);
                formItemData[index].stationId = [];
                form.setFieldsValue({
                    [name]: formItemData,
                });
            }
            const formItemData = form.getFieldValue(name);
            let selectStationList = [];
            for (const item of formItemData) {
                if (item.operId == value && item.stationId) {
                    selectStationList = [...new Set([...selectStationList, ...item.stationId])];
                }
            }

            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                operId: value,
                openFlag: (openFlag && 1) || undefined,
                premiumFlag: (premiumFlag && 1) || undefined,
                cooperationPlatform,
                cityStr: limitCitys || undefined,
            });
            changeStationList((oldList) => {
                oldList[index] = cityAndStationList;

                const list = [];
                cityAndStationList.forEach((ele, eleIndex) => {
                    if (ele.children) {
                        const childList = [];
                        ele.children.forEach((child, childIndex) => {
                            if (!selectStationList.includes(child.value)) {
                                childList.push({ ...child, key: String(child.value) });
                            }
                        });
                        if (childList.length > 0) {
                            list.push({
                                title: ele.title,
                                value: ele.value,
                                key: String(ele.value),
                                children: childList,
                            });
                        }
                    }
                });
                oldList[index] = list?.length && [
                    {
                        title: '全部',
                        key: 'all',
                        value: 'all',
                        children: list,
                    },
                ];
                return oldList;
            });
        } catch (error) {
            console.log(4444, error);
        } finally {
            toggleStationLoading(false);
        }
    };

    const initCityAndStationEvent = async ({ operId, city, index }) => {
        if (!operId?.length) {
            return undefined;
        }
        try {
            toggleStationLoading(true);
            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({
                operId,
                city: (city?.join && city.join(',')) || city,
                openFlag: (openFlag && 1) || undefined,
                channelId,
                cooperationPlatform,
                cityStr: limitCitys || undefined,
            });

            const formItemData = form.getFieldValue(name);
            const stationId =
                (formItemData && formItemData[index] && formItemData[index].stationId) || [];

            const list = [];

            let disableInfo = disabled
                ? {
                      disableCheckbox: true,
                  }
                : {};
            cityAndStationList.forEach((ele, eleIndex) => {
                if (ele.children) {
                    const childList = [];
                    ele.children.forEach((child, childIndex) => {
                        if (purchase == SELECT_TYPES.ONLYBUY && child.cooperationType !== '02') {
                            // 仅支持选择购电模式
                            return;
                        }
                        if (purchase == SELECT_TYPES.EXCEPTBUY && child.cooperationType === '02') {
                            // 不允许选择购电模式
                            return;
                        }
                        if (disabled && stationId.includes(child.value)) {
                            //禁用只显示已选中的
                            childList.push({ ...child, ...disableInfo });
                        } else {
                            //非禁用显示全部站点
                            childList.push({ ...child });
                        }
                    });

                    if (childList.length > 0) {
                        list.push({
                            title: ele.title,
                            value: ele.value,
                            children: childList,
                            ...disableInfo,
                        });
                    }
                }
            });

            return (
                (list?.length && [
                    {
                        title: '全部',
                        key: 'all',
                        value: 'all',
                        children: list,
                    },
                ]) ||
                []
            );
            // return cityAndStationList;
        } catch (error) {
            console.log(4444, error);
        } finally {
            toggleStationLoading(false);
        }
    };

    const treeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        placeholder: '请选择',
        maxTagCount: 3,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 3}个</span>,
        // disabled,
        style: {
            width: '100%',
        },
    };

    const canIadd = () => {
        let hasAdd = true;
        const formItemData = form.getFieldValue(name);
        if (formItemData) {
            for (const item of formItemData) {
                if (item && currentUser.operId && item.operId == currentUser.operId) {
                    hasAdd = false;
                    break;
                }
            }
        }
        return !disabled && hasAdd;
    };

    return (
        <Form.Item
            name={name}
            {...formItemLayout}
            label={label}
            rules={rules}
            initialValue={mode === 'select' ? [{}] : []}
        >
            <Form.List name={name}>
                {(fields, { add, remove }) => (
                    <Fragment>
                        {fields.map((field, index) => (
                            <Fragment key={index}>
                                <Row>
                                    <Col flex="200px">
                                        <OperSelectItem
                                            label=""
                                            fieldKey={field.fieldKey}
                                            name={[field.name, 'operId']}
                                            validateTrigger={['onChange', 'onBlur']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择运营商',
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(rule, value) {
                                                        return Promise.resolve();
                                                    },
                                                }),
                                            ]}
                                            onChange={(value, obj) => {
                                                changeOperEvent(value, index);
                                            }}
                                            onChangeOption={(option) => {
                                                onChangeOption?.(option);
                                            }}
                                            operatorList={limitOperatorList}
                                            disabled={
                                                disabled ||
                                                (limitOperIds?.length > 0 &&
                                                    formTypeWhenLimitOperIds !== 'SELECT') ||
                                                disabledOperSelect
                                            }
                                            purchase={purchase}
                                            cooperationPlatform={cooperationPlatform}
                                        />
                                    </Col>
                                    <Col flex="1">
                                        <Spin spinning={stationLoading || false}>
                                            <FormItem
                                                style={{ margin: '0 8px' }}
                                                fieldKey={field.fieldKey}
                                                name={[field.name, 'stationId']}
                                                validateTrigger={['onChange', 'onBlur']}
                                                rules={
                                                    !limitOperIds && [
                                                        {
                                                            required: true,
                                                            message: '请选择场站',
                                                        },
                                                        ({ getFieldValue }) => ({
                                                            validator(rule, value) {
                                                                return Promise.resolve();
                                                            },
                                                        }),
                                                    ]
                                                }
                                            >
                                                <TreeSelect
                                                    treeData={stationList[index] || []}
                                                    {...treeProps}
                                                    onChange={(value, labelitem, ext) => {
                                                        const cityList = stationList[index];

                                                        if (
                                                            ext.triggerValue === 'all' &&
                                                            ext.checked
                                                        ) {
                                                            // 全选
                                                            const list = [];
                                                            cityList?.[0]?.children.forEach(
                                                                (item) => {
                                                                    item.children.forEach(
                                                                        (subItem) => {
                                                                            list.push({
                                                                                stationName:
                                                                                    subItem.title,
                                                                                stationId:
                                                                                    subItem.value,
                                                                                cityName:
                                                                                    item.title,
                                                                                city: item.value,
                                                                                firstOpenTime:
                                                                                    item.firstOpenTime ||
                                                                                    subItem.firstOpenTime,
                                                                            });
                                                                        },
                                                                    );
                                                                },
                                                            );
                                                            updateAllStations([
                                                                ...allStations,
                                                                ...list,
                                                            ]);
                                                        } else if (ext.triggerValue) {
                                                            let cityObj;
                                                            let superObj;
                                                            const roop = (tempList) => {
                                                                for (const item of tempList || []) {
                                                                    if (cityObj) {
                                                                        break;
                                                                    }
                                                                    if (
                                                                        item?.value ==
                                                                        ext.triggerValue
                                                                    ) {
                                                                        cityObj = item;
                                                                    } else if (
                                                                        item.children?.length
                                                                    ) {
                                                                        superObj = item;
                                                                        roop(item.children);
                                                                    }
                                                                }
                                                            };
                                                            roop(cityList);

                                                            // 单选，判断是单选城市还是单选站点
                                                            if (!cityObj?.children?.length) {
                                                                // 单选城市
                                                                cityObj = superObj;
                                                            } else {
                                                                // 单选站点
                                                            }

                                                            const findChildrenByValue = (
                                                                tree,
                                                                targetValue,
                                                            ) => {
                                                                for (const node of tree) {
                                                                    if (
                                                                        node.value === targetValue
                                                                    ) {
                                                                        return node;
                                                                    }
                                                                    if (
                                                                        node.children &&
                                                                        node.children.length > 0
                                                                    ) {
                                                                        const result =
                                                                            findChildrenByValue(
                                                                                node.children,
                                                                                targetValue,
                                                                            );
                                                                        if (result) {
                                                                            return result;
                                                                        }
                                                                    }
                                                                }
                                                                return undefined;
                                                            };

                                                            const list = value?.map(
                                                                (id, itemIndex) => {
                                                                    const findStationItem =
                                                                        findChildrenByValue(
                                                                            cityList,
                                                                            id,
                                                                        );
                                                                    return {
                                                                        stationName:
                                                                            labelitem[itemIndex],
                                                                        stationId: id,
                                                                        cityName: cityObj?.title,
                                                                        city: cityObj?.value,
                                                                        firstOpenTime:
                                                                            findStationItem?.firstOpenTime,
                                                                    };
                                                                },
                                                            );

                                                            updateAllStations([
                                                                ...allStations,
                                                                ...list,
                                                            ]);
                                                        }
                                                    }}
                                                />
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    {disabled ||
                                    (limitOperIds?.length > 0 &&
                                        formTypeWhenLimitOperIds !== 'SELECT') ||
                                    mode === 'select' ? null : (
                                        <Col>
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{ margin: '0 8px' }}
                                                onClick={() => {
                                                    stationList.splice(index, 1);
                                                    changeStationList(stationList);
                                                    remove(field.name);
                                                }}
                                            />
                                        </Col>
                                    )}
                                </Row>
                            </Fragment>
                        ))}
                        {(limitOperIds?.length > 0 && formTypeWhenLimitOperIds !== 'SELECT') ||
                        mode === 'select' ? null : (
                            <FormItem label="">
                                <Button
                                    disabled={!canIadd()}
                                    type="dashed"
                                    onClick={() => {
                                        add();
                                    }}
                                    style={{ width: '200px' }}
                                >
                                    <PlusOutlined />
                                    添加运营商
                                </Button>
                            </FormItem>
                        )}
                    </Fragment>
                )}
            </Form.List>
        </Form.Item>
    );
};
export default connect(({ user, global, loading }) => ({
    global,
    currentUser: user.currentUser,
    operLoading: loading.effects['global/getOperatorList'],
}))(OperTreeSelectList);
