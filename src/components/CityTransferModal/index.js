import { Modal, Transfer, Tree, Spin, Input, Checkbox, Radio, Form } from 'antd';
import React, { useState, useImperativeHandle, useMemo, useEffect, forwardRef } from 'react';
import { getCityListApi } from '@/services/CommonApi';
import { useRef } from 'react';
const { Search } = Input;

const isChecked = (selectedKeys, eventKey) => selectedKeys?.includes(eventKey);

const generateTree = ({ treeNodes = [], targetKeys = [], direction, disabledCityKeys, key }) => {
    const loop = (list = []) => {
        const res = [];
        for (const item of list) {
            if (direction == 'right') {
                // 处理右侧数据，前提是checkedKeys只返回城市一级
                if (item?.children?.length) {
                    const tempRes = loop(item?.children);
                    if (tempRes.length) {
                        res.push(...tempRes);
                    }
                } else if (targetKeys.includes(item.key)) {
                    if (key?.length) {
                        // 搜索模式
                        if (item.title?.indexOf(key) == -1) {
                            continue;
                        }
                    }
                    res.push({
                        ...item,
                        disabled: disabledCityKeys.includes(item.key) || item.disabled,
                    });
                }
            } else {
                // 处理左侧数据
                if (item?.children?.length) {
                    const tempRes = loop(item?.children);
                    if (tempRes.length) {
                        const tempItem = {
                            ...item,
                            children: tempRes,
                            disabled:
                                tempRes.filter((ele) => ele.disabled == true)?.length ==
                                tempRes.length,
                        };
                        res.push(tempItem);
                    } else if (key?.length && item.title?.indexOf(key) >= 0) {
                        if (
                            item.children &&
                            item.children.find((ele) => targetKeys?.indexOf(ele.key) == -1)
                        ) {
                            // 如果是省，判断子集至少要有一个未归类到右侧，才显示
                            res.push({
                                ...item,
                                children: undefined,
                            });
                        }
                    }
                } else if (!targetKeys.includes(item.key)) {
                    if (key?.length) {
                        // 搜索模式
                        if (item.title?.indexOf(key) == -1) {
                            continue;
                        }
                    }
                    res.push(item);
                    if (item.pid == 0 && !item.children?.length) {
                        item.disabled = true;
                    }
                }
            }
        }
        return res;
    };

    const list = loop(treeNodes);
    return list;
};

// 仅穿梭框，左侧做成树形，右侧做成一级列表
export const CityTransfer = forwardRef((props, ref) => {
    const { defaultCityKeys, disabledCityKeys, disabled = false, request, multiple = true } = props;

    const [expandedKeys, updateExpandedKeys] = useState([]); // 受控展开项
    const [targetKeys, setTargetKeys] = useState([]); // 显示在右侧框数据的 key 集合
    useImperativeHandle(ref, () => ({
        initCities,
        destoryCities,
        targetKeys,
        cityTreeData,
    }));

    useEffect(() => {
        let defaultCityIdArray = defaultCityKeys;
        if (typeof defaultCityKeys == 'string') {
            defaultCityIdArray = defaultCityKeys.split(',');
        }
        setTargetKeys([...defaultCityIdArray]);
    }, [defaultCityKeys]);

    // 初始化城市选择数据
    const [cityLoading, updateCityLoading] = useState(false);
    const [cityList, updateCityList] = useState([]);
    const initCities = async () => {
        try {
            updateCityLoading(true);
            if (request) {
                const areaList = await request({ cityType: disabled ? '2' : '1' });
                updateCityList(areaList);
            } else {
                const { data: areaList } = await getCityListApi();

                updateCityList(areaList);
            }
        } catch (error) {
            updateCityList([]);
            throw new Error(error);
        } finally {
            updateCityLoading(false);
        }
    };

    const cityTreeData = useMemo(() => {
        const loop = (list = [], pid) => {
            const res = [];
            for (const item of list) {
                const temp = {
                    key: item.areaCode,
                    title: item.areaName,
                    upAreaCode: item.upAreaCode,
                    upAreaName: item.upAreaName,
                    pid,
                    disabled: disabledCityKeys?.includes(item.areaCode) || disabled,
                };
                if (item?.cityList?.length) {
                    temp.children = loop(item?.cityList, pid + 1);
                    res.push(temp);
                } else if (pid != 0) {
                    res.push(temp); // 绕过无城市选项的省份
                }
            }
            return res;
        };

        const list = loop(cityList, 0);
        return list;
    }, [cityList, disabledCityKeys, disabled]);

    const [sourceSelectedKeys, setSourceSelectedKeys] = useState([]); // 左侧数据的勾选 集合
    const [sourceSearchKey, setSourceSearchKey] = useState('');
    // 左侧数据源
    const sourceTreeData = useMemo(() => {
        return generateTree({
            treeNodes: cityTreeData,
            targetKeys,
            direction: 'left',
            key: sourceSearchKey,
            disabledCityKeys,
        });
    }, [cityTreeData, disabledCityKeys, targetKeys, sourceSearchKey]);
    // 左侧待选总数
    const sourceTotalNum = useMemo(() => {
        let count = 0;
        for (const item of cityTreeData || []) {
            for (const cityItem of item.children || []) {
                if (!isChecked(targetKeys, cityItem.key) && !cityItem.disabled) {
                    count += 1;
                }
            }
        }
        return count;
    }, [cityTreeData, targetKeys]);
    // 左侧已选总数
    const sourceSelectNum = useMemo(() => {
        let count = 0;
        for (const item of cityTreeData || []) {
            if (item?.children?.length) {
                for (const subItem of item?.children) {
                    if (sourceSelectedKeys.includes(subItem.key)) {
                        // 如果选择的是全省，必定选了其中的地级市
                        count += 1;
                    }
                }
            }
        }
        return count;
    }, [sourceTreeData, sourceSelectedKeys, cityTreeData]);

    useEffect(() => {
        // 左侧默认展开项
        if (sourceSearchKey?.length) {
            const expandedKeys = [];
            // 有搜索条件的前提下，默认展开所有搜索结果
            sourceTreeData?.map((ele) => {
                expandedKeys.push(ele.key);
            });
            updateExpandedKeys(expandedKeys);
        }
    }, [sourceTreeData, sourceSearchKey]);

    const [targetSelectedKeys, setTargetSelectedKeys] = useState([]); // 右侧数据的勾选 集合
    const [targetSearchKey, setTargetSearchKey] = useState('');
    // 右侧数据源
    const targetTreeData = useMemo(() => {
        return generateTree({
            treeNodes: cityTreeData,
            targetKeys,
            direction: 'right',
            disabledCityKeys,
            key: targetSearchKey,
        });
    }, [cityTreeData, targetKeys, disabledCityKeys, targetSearchKey]);
    // 右侧待选总数
    const targetTotalNum = useMemo(() => {
        return disabled ? 0 : targetKeys.length;
    }, [targetKeys]);
    // 右侧已选总数
    const targetSelectNum = useMemo(() => {
        return targetSelectedKeys.length;
    }, [targetSelectedKeys]);

    const onCheckSourceAll = (e) => {
        if (sourceSelectNum == sourceTotalNum) {
            // 全选->不选
            setSourceSelectedKeys([]);
        } else {
            const keys = [];
            for (const item of cityTreeData || []) {
                for (const cityItem of item.children || []) {
                    if (!isChecked(targetKeys, cityItem.key) && !cityItem?.disabled) {
                        keys.push(item.key);
                        keys.push(cityItem.key);
                    }
                }
            }
            const newKeys = Array.from(new Set(keys));
            setSourceSelectedKeys([...newKeys]);
        }
    };
    const onCheckTargetAll = (e) => {
        if (targetSelectNum == targetTotalNum) {
            // 全选->不选
            setTargetSelectedKeys([]);
        } else {
            // setTargetSelectedKeys([...targetKeys]);
            // 因为要绕过禁用项，不能直接从targetKeys取值
            const keys = [];
            for (const cityItem of targetTreeData || []) {
                if (isChecked(targetKeys, cityItem.key) && !cityItem?.disabled) {
                    keys.push(cityItem.key);
                }
            }
            const newKeys = Array.from(new Set(keys));
            setTargetSelectedKeys([...newKeys]);
        }
    };

    const onChange = (_, direction) => {
        if (direction == 'left') {
            if (!multiple) {
                setTargetKeys(targetSelectedKeys);
            } else {
                // 从右到左
                for (const key of targetSelectedKeys) {
                    while (targetKeys.includes(key)) {
                        targetKeys.splice(targetKeys?.indexOf(key), 1);
                    }
                }
                setTargetKeys([...targetKeys]);
            }
        } else if (direction == 'right') {
            if (!multiple) {
                setTargetKeys(sourceSelectedKeys);
            } else {
                // 从左到右
                for (const item of cityTreeData) {
                    // 省的key移除
                    while (sourceSelectedKeys.includes(item.key)) {
                        sourceSelectedKeys.splice(sourceSelectedKeys?.indexOf(item.key), 1);
                    }
                }
                const newKeys = Array.from(new Set([...targetKeys, ...sourceSelectedKeys]));
                setTargetKeys([...newKeys]);
            }
        }
        setTargetSelectedKeys([]);
        setSourceSelectedKeys([]);
    };

    const onSearchSelectChange = (direction, key) => {
        // 单独维护处理左侧有关键字时的选中事件，用于维护省份的勾选框
        if (direction == 'left') {
            const provinceItem = sourceTreeData.find((ele) => ele.key == key);
            // 用于退出搜索时判断要不要扩大勾选范围，如果是省的勾选，要勾上全省，如果只是勾选到市，省份的勾勾隐藏。右侧因为是一级列表，不用管
            if (provinceItem) {
                if (!multiple) {
                    return;
                }
                // 操作项是省份
                // 产品的意思是，选中筛选后的省份，意味着退出搜索后，省份下的其他城市也会被勾上，所以要去cityDataTree的数据里面查询
                const provinceData =
                    cityTreeData.find((cityEle) => cityEle.key == provinceItem.key) || {};
                if (isChecked(sourceSelectedKeys, key)) {
                    // 取消选中
                    for (const item of provinceData.children || []) {
                        const index = sourceSelectedKeys?.indexOf(item.key);
                        if (index >= 0) {
                            sourceSelectedKeys.splice(index, 1);
                        }
                    }
                    sourceSelectedKeys.splice(sourceSelectedKeys?.indexOf(key), 1);
                } else {
                    // 选中全省
                    sourceSelectedKeys.push(key);
                    for (const item of provinceData.children || []) {
                        if (
                            !isChecked(targetKeys, item.key) &&
                            !isChecked(disabledCityKeys, item.key)
                        ) {
                            // 源列表，不应包含目的列表的城市
                            sourceSelectedKeys.push(item.key);
                        }
                    }
                }
            } else {
                if (!multiple) {
                    setSourceSelectedKeys([key]);
                    return;
                }
                // 操作项是市名
                const provinceItem = cityTreeData.find((ele) => {
                    const res = ele.children?.find((cityEle) => cityEle.key == key);
                    if (res) {
                        return true;
                    }
                    return false;
                });
                if (isChecked(sourceSelectedKeys, key)) {
                    // 取消选中
                    if (sourceSelectedKeys?.indexOf(provinceItem.key) >= 0) {
                        sourceSelectedKeys.splice(sourceSelectedKeys?.indexOf(provinceItem.key), 1);
                    }
                    sourceSelectedKeys.splice(sourceSelectedKeys?.indexOf(key), 1);
                } else {
                    sourceSelectedKeys.push(key);
                    // 逆向判断省是否要全选
                    let isAll = true;
                    for (const cityItem of provinceItem.children || []) {
                        if (
                            !isChecked(targetKeys, cityItem.key) &&
                            !isChecked(sourceSelectedKeys, cityItem.key) &&
                            !isChecked(disabledCityKeys, cityItem.key)
                        ) {
                            isAll = false;
                            break;
                        }
                    }
                    if (isAll) {
                        sourceSelectedKeys.push(provinceItem.key);
                    }
                }
            }
            const array = Array.from(new Set(sourceSelectedKeys)); // 去重
            setSourceSelectedKeys([...array]);
        } else {
            if (!multiple) {
                setTargetSelectedKeys([key]);
                return;
            }
            if (isChecked(targetSelectedKeys, key)) {
                // 取消选中
                targetSelectedKeys.splice(targetSelectedKeys?.indexOf(key), 1);
            } else {
                targetSelectedKeys.push(key);
            }
            const array = Array.from(new Set(targetSelectedKeys)); // 去重
            setTargetSelectedKeys([...array]);
        }
    };

    const onSearch = (direction, value) => {
        if (direction == 'left') {
            setSourceSearchKey(value);
        } else {
            setTargetSearchKey(value);
        }
    };

    const destoryCities = () => {
        setSourceSearchKey('');
        setTargetSearchKey('');
        setSourceSelectedKeys([]);
        setTargetSelectedKeys([]);
        updateExpandedKeys([]);
    };

    return (
        <Spin spinning={cityLoading}>
            <Transfer
                dataSource={cityTreeData}
                titles={['源列表', '目的列表']}
                selectAllLabels={[
                    multiple && sourceTotalNum > 0 ? (
                        <Checkbox
                            key="source"
                            checked={sourceTotalNum > 0 && sourceSelectNum == sourceTotalNum}
                            onChange={onCheckSourceAll}
                            disabled={disabled}
                        >
                            {`${sourceSelectNum}/${sourceTotalNum}项`}
                        </Checkbox>
                    ) : (
                        <div></div>
                    ),
                    multiple && targetTotalNum > 0 ? (
                        <Checkbox
                            key="target"
                            checked={targetTotalNum > 0 && targetSelectNum == targetTotalNum}
                            onChange={onCheckTargetAll}
                            disabled={disabled}
                        >
                            {`${targetSelectNum}/${targetTotalNum}项`}
                        </Checkbox>
                    ) : (
                        <div></div>
                    ),
                ]}
                showSelectAll={false} // 未开放全选事件的api，所以单独定制，原生的隐藏
                targetKeys={targetKeys}
                selectedKeys={[...sourceSelectedKeys, ...targetSelectedKeys]}
                onChange={onChange}
            >
                {({ direction, onItemSelect, _selectedKeys }) => {
                    return (
                        <div>
                            <Search
                                style={{
                                    marginBottom: 8,
                                }}
                                placeholder="请输入搜索的内容"
                                onChange={(e) => {
                                    onSearch(direction, e.target.value);
                                }}
                                value={direction == 'left' ? sourceSearchKey : targetSearchKey}
                            />
                            <div
                                style={{
                                    maxHeight: `${document.body.clientHeight - 400}px`,
                                    overflowY: 'auto',
                                }}
                            >
                                <Tree
                                    blockNode
                                    checkable={multiple}
                                    multiple={multiple}
                                    checkStrictly
                                    defaultExpandAll
                                    checkedKeys={
                                        (direction == 'left' && sourceSelectedKeys) ||
                                        targetSelectedKeys
                                    }
                                    selectedKeys={
                                        (direction == 'left' && sourceSelectedKeys) ||
                                        targetSelectedKeys
                                    }
                                    treeData={
                                        (direction == 'left' && sourceTreeData) || targetTreeData
                                    }
                                    onCheck={(_, { node: { key } }) => {
                                        onSearchSelectChange(direction, key);
                                    }}
                                    onSelect={(_, { node: { key } }) => {
                                        onSearchSelectChange(direction, key);
                                    }}
                                    expandedKeys={expandedKeys}
                                    onExpand={(_expandedKeys, { expanded, node }) => {
                                        updateExpandedKeys([..._expandedKeys]);
                                    }}
                                />
                            </div>
                        </div>
                    );
                }}
            </Transfer>
        </Spin>
    );
});

// 带弹窗的穿梭框
const CityTransferModal = (props, ref) => {
    const {
        title = '选择区域',
        onFinish, // 点击确定的回调，带上目标列表的城市key集合
        request,
        showAllRadio = false, // 是否展示全部选项
        multiple,
        extraFormItem,
    } = props;

    const [modalVisible, toggleModalVisible] = useState(false); // 导入弹窗状态
    const cityRef = useRef();
    const [defaultCityKeys, updateDefaultCityKeys] = useState([]);
    const [disabledCityKeys, updateDisabledCityKeys] = useState([]);
    const [disabled, updateDisabled] = useState(false);
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
        /** show方法参数说明
         * @param {defaultKeys} ：当前在目的列表的城市key集合
         * @param {disabledKeys} ：不可被操作的城市key集合，查看详情，或者只增不减，都利用此字段
         * @param {otherParams} ：其他参数
         */
        show: ({
            defaultKeys = [],
            disabledKeys = [],
            disabled: _disabled = false,
            otherParams = {},
        } = {}) => {
            updateDefaultCityKeys([...defaultKeys]);
            updateDisabledCityKeys([...disabledKeys]);
            updateDisabled(_disabled);
            toggleModalVisible(true);
            if (showAllRadio) {
                form.setFieldsValue({
                    ...otherParams,
                });
            }

            setTimeout(() => {
                cityRef?.current?.initCities();
            }, 200);
        },

        onClose,
        getSelectInfo: getSelectInfo,
    }));

    const onClose = () => {
        updateDefaultCityKeys([]);
        updateDisabledCityKeys([]);
        toggleModalVisible(false);
        updateDisabled(false);
        cityRef?.current?.destoryCities();
    };

    const getSelectInfo = () => {
        const res = [];
        for (const key of cityRef?.current?.targetKeys || []) {
            let has = false; // 判断当前key是否被消费掉
            for (const item of cityRef?.current?.cityTreeData || []) {
                for (const cityItem of item?.children || []) {
                    if (cityItem.key == key) {
                        res.push({
                            areaCode: cityItem.key,
                            areaName: cityItem.title,
                            upAreaCode: cityItem.upAreaCode,
                            upAreaName: cityItem.upAreaName,
                        });
                        has = true;
                        break;
                    }
                }
                if (has) {
                    break;
                }
            }
        }
        return res;
    };

    const handleConfirm = (values) => {
        const res = getSelectInfo();
        onFinish?.(res, values);
        onClose();
    };

    return (
        <Modal
            title={title}
            width={600}
            visible={modalVisible}
            onCancel={onClose}
            maskClosable={false}
            onOk={() => {
                if (showAllRadio) {
                    form.validateFields()
                        .then(async (values) => {
                            handleConfirm(values);
                        })
                        .catch((e) => {
                            return;
                        });
                } else {
                    handleConfirm();
                }
            }}
            footer={disabled ? null : undefined}
        >
            <Form form={form} wrapperCol={{ span: 8 }}>
                {showAllRadio && (
                    <Form.Item
                        label="生效范围"
                        name="scopeType"
                        rules={[{ required: true, message: '请选择' }]}
                    >
                        <Radio.Group
                            disabled={disabled}
                            onChange={(e) => {
                                if (e.target.value === '3') {
                                    setTimeout(() => {
                                        cityRef?.current?.initCities();
                                    }, 200);
                                }
                            }}
                        >
                            <Radio value={'1'}>全部</Radio>
                            <Radio value={'3'}>部分城市</Radio>
                        </Radio.Group>
                    </Form.Item>
                )}
                <Form.Item
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.scopeType !== curValues.scopeType
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const scopeType = getFieldValue('scopeType');
                        if (showAllRadio && scopeType === '1') {
                            return null;
                        } else {
                            // setTimeout(() => {
                            //     cityRef?.current?.initCities();
                            // }, 200);
                            return (
                                <CityTransfer
                                    multiple={multiple}
                                    ref={cityRef}
                                    disabled={disabled}
                                    defaultCityKeys={defaultCityKeys}
                                    disabledCityKeys={disabledCityKeys}
                                    request={request}
                                />
                            );
                        }
                    }}
                </Form.Item>
                {extraFormItem}
            </Form>
            {/* <CityTransfer
                    ref={cityRef}
                    disabled={disabled}
                    defaultCityKeys={defaultCityKeys}
                    disabledCityKeys={disabledCityKeys}
                    request={request}
                /> */}
        </Modal>
    );
};

export default forwardRef(CityTransferModal);
