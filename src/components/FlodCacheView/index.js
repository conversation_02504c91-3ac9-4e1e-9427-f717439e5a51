import React, {
    Fragment,
    useEffect,
    useState,
    useImperativeHandle,
    useRef,
    useCallback,
} from 'react';
import { Button, Popover, Badge, Space, message, Descriptions, Spin, Modal } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import { FolderOpenOutlined, DownOutlined, ExclamationCircleTwoTone } from '@ant-design/icons';
import { connect } from 'umi';
import { useMemo } from 'react';

import { applyBillApi } from '@/services/FinanceManage/BillManageApi';

const FlodCacheView = (props) => {
    const {
        dispatch,
        billType,
        cacheModel: { cacheList, cacheListTotal, cacheDetailInfo },
        listLoading,
        initRef,
        detailLoading,
        api,
    } = props;

    // 处理文件暂存区
    const [enableDownloadCount, updateEnableDownloadCount] = useState(0);
    const [showAreaView, updateShowAreaView] = useState(false);

    useImperativeHandle(initRef, () => ({
        count: () => {
            updateEnableDownloadCount(enableDownloadCount + 1);
        },
        clearCount: () => {
            updateEnableDownloadCount(0);
        },
        apply: (params) => {
            // 文件下载至暂存区，走临时账单申请的接口
            params.billType = billType;
            return new Promise(async (resolve, reject) => {
                try {
                    await (api ? api(params) : applyBillApi(params));
                    message.success('提交成功，请到文件暂存区查看');
                    resolve();
                } catch {
                    reject();
                }
            });
        },
        reload: () => {
            if (pageInfo.pageIndex == 1) {
                searchData();
            } else {
                changePageInfo((state) => ({
                    ...state,
                    pageIndex: 1,
                }));
            }
        },
    }));

    const [pageInfo, changePageInfo, onTableChange] = usePageState({});

    useEffect(() => {
        showAreaView && searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = () => {
        try {
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                billType,
            };
            dispatch({
                type: 'cacheModel/getCacheList',
                options: params,
            });
        } catch (error) {}
    };

    const downloadCacheColumns = useMemo(() => {
        const extraCol = [];
        if (billType == '02') {
            extraCol.push({
                title: '结清日期',
                width: 240,
                dataIndex: 'realCreateTime',
                render(text, record) {
                    text = '';
                    if (record?.startDate && record?.endDate) {
                        text = `${record?.startDate} 至 ${record?.endDate}`;
                    }
                    return <span title={text}>{text}</span>;
                },
            });
        }
        if (billType == '03') {
            extraCol.push({
                title: '运营商全称',
                width: 140,
                dataIndex: 'operName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            });
        }
        const columns = [
            {
                title: '申请日期',
                width: 200,
                dataIndex: 'createTime',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            ...extraCol,
            {
                title: '文件名',
                width: 120,
                dataIndex: 'fileName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '状态',
                width: 120,
                dataIndex: 'billStatusName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '操作',
                width: 140,
                fixed: 'right',
                align: 'center',
                render(text, record) {
                    return (
                        <Space>
                            <a
                                onClick={() => {
                                    cacheDetailShow(record.id);
                                }}
                            >
                                详情
                            </a>

                            {(record.billStatus == 3 && record.billDownLoadUrl?.length && (
                                <a
                                    href={record.billDownLoadUrl}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    下载账单
                                </a>
                            )) ||
                                null}
                        </Space>
                    );
                },
            },
        ];
        return columns;
    }, [billType]);

    const downloadCache = (
        <TablePro
            style={{ width: '800px' }}
            loading={listLoading}
            scroll={{ x: 'max-content' }}
            dataSource={cacheList}
            columns={downloadCacheColumns}
            onChange={onTableChange}
            size="small"
            pagination={{
                current: pageInfo.pageIndex,
                total: cacheListTotal,
                pageSize: pageInfo.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
                pageSizeOptions: [10],
            }}
        />
    );

    // 下载详情
    const [cacheDetailVisiable, updateCacheDetailVisiable] = useState(false);
    const cacheDetailShow = (id) => {
        updateCacheDetailVisiable(true);
        dispatch({
            type: 'cacheModel/getCacheDetailInfo',
            options: { id },
        });
    };

    const cacheDetailDismiss = () => {
        updateCacheDetailVisiable(false);
        dispatch({
            type: 'cacheModel/updateCacheProperty',
            params: { cacheDetailInfo: undefined },
        });
    };

    const descItems = useMemo(() => {
        return [
            { title: '运营商', value: cacheDetailInfo?.operName },
            { title: '订单号', value: cacheDetailInfo?.orderNo },
            {
                title: '订单日期',
                value:
                    (cacheDetailInfo?.startDate &&
                        cacheDetailInfo?.endDate &&
                        `${cacheDetailInfo?.startDate} 至 ${cacheDetailInfo?.endDate}`) ||
                    undefined,
            },
            { title: '城市', value: cacheDetailInfo?.city },
            {
                title: '结算日期',
                value:
                    (cacheDetailInfo?.settleStartDate &&
                        cacheDetailInfo?.settleEndDate &&
                        `${cacheDetailInfo?.settleStartDate} 至 ${cacheDetailInfo?.settleEndDate}`) ||
                    undefined,
            },
            { title: '充电站', value: cacheDetailInfo?.stationName },
            { title: '订单渠道', value: cacheDetailInfo?.orderChannelName },
            { title: '支付渠道', value: cacheDetailInfo?.payChannelName },
        ];
    }, [cacheDetailInfo]);

    return (
        <Popover
            placement="bottomLeft"
            content={downloadCache}
            title={
                <div style={{ margin: '10px' }}>
                    <ExclamationCircleTwoTone style={{ marginRight: '8px' }} />
                    文件保存7天后自动删除
                </div>
            }
            trigger="click"
            onClick={() => {
                if (!showAreaView) {
                    initRef?.current?.reload();
                    updateEnableDownloadCount(0);
                }
            }}
            zIndex={10}
            visible={showAreaView}
            onVisibleChange={(visible) => {
                if (cacheDetailVisiable) {
                    visible = true;
                }
                updateShowAreaView(visible);
            }}
        >
            <Badge count={enableDownloadCount} offset={[-30, 6]}>
                <Button icon={<FolderOpenOutlined />} type="link">
                    文件暂存区
                    <DownOutlined />
                </Button>
            </Badge>

            <Modal
                title="下载详情"
                visible={cacheDetailVisiable}
                onCancel={cacheDetailDismiss}
                footer={null}
                maskClosable={false}
                zIndex={999}
                width={800}
            >
                {(detailLoading && (
                    <div style={{ textAlign: 'center' }}>
                        <Spin />
                    </div>
                )) || (
                    <>
                        <Descriptions column={2}>
                            {descItems?.map((ele) => {
                                return (
                                    (ele && ele.value && (
                                        <Descriptions.Item label={ele.title}>
                                            {ele.value}
                                        </Descriptions.Item>
                                    )) ||
                                    undefined
                                );
                            })}
                        </Descriptions>
                        {(cacheDetailInfo?.failReason?.length && (
                            <Descriptions>
                                <Descriptions.Item label="失败原因">
                                    {cacheDetailInfo?.failReason}
                                </Descriptions.Item>
                            </Descriptions>
                        )) ||
                            null}
                    </>
                )}
            </Modal>
        </Popover>
    );
};

export default connect(({ cacheModel, loading }) => ({
    cacheModel,
    listLoading: loading.effects['cacheModel/getCacheList'],
    detailLoading: loading.effects['cacheModel/getCacheDetailInfo'],
}))(FlodCacheView);
