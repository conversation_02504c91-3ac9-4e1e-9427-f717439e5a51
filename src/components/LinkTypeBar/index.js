import { Fragment, useMemo, useEffect } from 'react';
import { Form, Input, Row, Col, Button, Radio } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { connect } from 'umi';
import styles from '@/assets/styles/common.less';
const FormItem = Form.Item;
const LINK_TYPES = {
    NONE: '00',
    ALIPAY: '01',
    WEB: '02',
    APPLET: '03',
};

const LinkTypeBar = (props) => {
    const {
        dispatch,
        disabled,
        disabledIds = [],
        global: { codeInfo },
        label,
        formItemLayout,
        formItemFixedWidthLayout,
        required,
    } = props;
    const { linkType: linkTypeList } = codeInfo;

    useEffect(() => {
        if (!linkTypeList) {
            dispatch({
                type: 'global/initCode',
                code: 'linkType',
            });
        }
    }, []);
    const linkTypeOptions = useMemo(() => {
        if (linkTypeList) {
            return linkTypeList.map((ele) => {
                return (
                    <Radio
                        key={ele.codeValue}
                        value={ele.codeValue}
                        disabled={disabledIds.includes(ele.codeValue)}
                    >
                        {ele.codeName}
                    </Radio>
                );
            });
        }
        return [];
    }, [linkTypeList]);
    return (
        <Fragment>
            <FormItem
                name="linkType"
                label={label ? label : '跳转类型'}
                rules={[{ required: required, message: '请选择' }]}
                initialValue={'00'}
                {...formItemLayout}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={'00'}>无链接</Radio>

                    {linkTypeOptions}
                </Radio.Group>
            </FormItem>
            <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) => prevValues.linkType !== curValues.linkType}
            >
                {({ getFieldValue }) => {
                    const linkType = getFieldValue('linkType');

                    if (linkType == LINK_TYPES.APPLET) {
                        return (
                            <Fragment>
                                <FormItem
                                    name="appId"
                                    label="小程序appId"
                                    rules={[{ required: true, message: '请填写' }]}
                                    {...formItemFixedWidthLayout}
                                >
                                    <Input
                                        disabled={disabled}
                                        placeholder="请输入"
                                        autoComplete="off"
                                    />
                                </FormItem>
                                <FormItem
                                    name="linkUrl"
                                    label="小程序页面地址"
                                    {...formItemFixedWidthLayout}
                                >
                                    <Input
                                        disabled={disabled}
                                        placeholder="请输入"
                                        autoComplete="off"
                                    />
                                </FormItem>
                                <FormItem label="例" {...formItemLayout}>
                                    <div className="text-line" style={{ whiteSpace: 'pre' }}>
                                        /pages/index/index (不填默认首页)
                                    </div>
                                </FormItem>

                                <FormItem
                                    name="extend"
                                    label="额外参数"
                                    {...formItemLayout}
                                    // rules={[
                                    //     ({ getFieldValue }) => ({
                                    //         validator(rule, value) {
                                    //             const list = getFieldValue('useMarks');
                                    //             if (!list || list.length == 0) {
                                    //                 return Promise.reject('请输入规则!');
                                    //             }
                                    //             return Promise.resolve();
                                    //         },
                                    //     }),
                                    // ]}
                                    // required
                                >
                                    <Form.List name="extend" disabled={disabled}>
                                        {(fields, { add, remove }) => (
                                            <Fragment>
                                                {fields.map((field, index) => {
                                                    return (
                                                        <Row key={field.key}>
                                                            <Col flex="1">
                                                                <FormItem
                                                                    validateTrigger={['onChange']}
                                                                    label="字段名"
                                                                    name={[index, 'name']}
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            whitespace: true,
                                                                            message: '请输入字段名',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <Input
                                                                        placeholder="请输入字段名"
                                                                        autoComplete="off"
                                                                    />
                                                                </FormItem>
                                                            </Col>
                                                            <Col flex="1">
                                                                <FormItem
                                                                    validateTrigger={['onChange']}
                                                                    name={[index, 'value']}
                                                                    label="字段值"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            whitespace: true,
                                                                            message: '请输入字段值',
                                                                        },
                                                                    ]}
                                                                >
                                                                    <Input
                                                                        placeholder="请输入字段值"
                                                                        autoComplete="off"
                                                                    />
                                                                </FormItem>
                                                            </Col>
                                                            <Col>
                                                                <MinusCircleOutlined
                                                                    className={
                                                                        styles[
                                                                            'dynamic-delete-button'
                                                                        ]
                                                                    }
                                                                    style={{
                                                                        margin: '0 8px',
                                                                    }}
                                                                    onClick={() => {
                                                                        remove(field.name);
                                                                    }}
                                                                />
                                                            </Col>
                                                        </Row>
                                                    );
                                                })}
                                                {!disabled ? (
                                                    <FormItem label="">
                                                        <Button
                                                            type="dashed"
                                                            onClick={() => {
                                                                add();
                                                            }}
                                                            style={{ width: '60%' }}
                                                        >
                                                            <PlusOutlined />
                                                            添加参数
                                                        </Button>
                                                    </FormItem>
                                                ) : null}
                                            </Fragment>
                                        )}
                                    </Form.List>
                                </FormItem>
                            </Fragment>
                        );
                    }
                    if (linkType == LINK_TYPES.ALIPAY || linkType == LINK_TYPES.WEB) {
                        let linkMode = '';
                        if (linkType == LINK_TYPES.WEB) {
                            linkMode = '/pages/home/<USER>//www.evshine.cn';
                        } else {
                            linkMode =
                                '生活号文章地址https://render.alipay.com/p/s/i/?scheme=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FappId%3D2018121712345678%26page%3Dpages%2Findex%2Findex%26query%3Dchannel%253d1';
                        }
                        return (
                            <Fragment>
                                {linkType == LINK_TYPES.WEB ? (
                                    <FormItem
                                        name="loginFlag"
                                        label="是否登陆"
                                        initialValue="0"
                                        {...formItemLayout}
                                    >
                                        <Radio.Group>
                                            <Radio value={'0'}>否</Radio>
                                            <Radio value={'1'}>是</Radio>
                                        </Radio.Group>
                                    </FormItem>
                                ) : null}
                                <FormItem label="链接地址" required {...formItemFixedWidthLayout}>
                                    <FormItem
                                        name="linkUrl"
                                        rules={[{ required: true, message: '请填写' }]}
                                        noStyle
                                    >
                                        <Input
                                            disabled={disabled}
                                            placeholder="请输入"
                                            autoComplete="off"
                                        />
                                    </FormItem>
                                </FormItem>
                                <FormItem label="例" {...formItemLayout}>
                                    <div
                                        className="text-line"
                                        style={{ whiteSpace: 'pre' }}
                                        title={linkMode}
                                    >
                                        {linkMode}
                                    </div>
                                </FormItem>
                            </Fragment>
                        );
                    }
                }}
            </FormItem>
        </Fragment>
    );
};
export default connect(({ global }) => ({
    global,
}))(LinkTypeBar);
