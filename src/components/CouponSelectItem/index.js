import { Fragment, useEffect, useState, useImperativeHandle } from 'react';
import { Form, Select } from 'antd';
import { connect } from 'umi';

const FormItem = Form.Item;
const { Option } = Select;

const CouponSelectItem = (props) => {
    const {
        dispatch,
        formItemLayout = {},
        initRef,
        label = '选择优惠券:',
        name = 'cpnId',
        disabled,
        rules,
        onChange,
        global: { cpnList = [] },
    } = props;

    const [nowCoupons, changeNowCoupons] = useState([]);

    useEffect(() => {
        if (!cpnList?.length) {
            dispatch({
                type: 'global/getCpnList',
            });
        }
    }, []);

    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            changeNowCoupons(cpnList);
        },
    }));

    useEffect(() => {
        changeNowCoupons(cpnList);
    }, [cpnList]);

    const handleSearch = (value) => {
        if (value) {
            const list = [];
            for (const item of cpnList) {
                const { cpnName } = item;
                if (cpnName.indexOf(value) >= 0) {
                    list.push(item);
                }
            }
            changeNowCoupons(list);
        } else {
            changeNowCoupons(cpnList);
        }
    };

    const handleFilter = (value, option) => true;
    return (
        <FormItem label={label} name={name} {...formItemLayout} rules={rules}>
            <Select
                showSearch
                placeholder="请选择"
                onSearch={handleSearch}
                filterOption={handleFilter}
                disabled={disabled}
                onChange={onChange}
            >
                {nowCoupons?.map((item, index) => (
                    <Option key={item.cpnId} value={item.cpnId}>
                        {item.cpnName}
                    </Option>
                ))}
            </Select>
        </FormItem>
    );
};
export default connect(({ global }) => ({
    global,
}))(CouponSelectItem);
