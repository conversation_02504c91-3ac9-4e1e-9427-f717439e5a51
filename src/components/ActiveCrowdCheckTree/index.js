import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { connect } from 'umi';
import { Form, Checkbox, Space, TreeSelect, Radio } from 'antd';
import CheckBoxGroup from '@/components/CheckBoxGroup/index';
import { copyObjectCommon, filterTreeList, isEmpty } from '@/utils/utils';

const FormItem = Form.Item;

const ActiveCrowdCheckTree = (props) => {
    const {
        parentName = [],
        name = 'activeCrowd',
        dispatch,
        form,
        global,
        disabled,
        disableIds, // 不可选择的人群id
        excludeIds, //设置隐藏人群id
        onlyIds, //只有这几个id可以选  过滤掉其他的
        requiredIds, // 设置必须有的人群，默认就选中，且不可被取消勾选
        mode = 'multiple', // 是否支持多选，单选的情况下，没有全选按钮，没有复选框
        hideAll = false, // 隐藏全选按钮
        label = '活动人群',
        showTableSearch = false,
        initialValue = [],
        mustRequired = undefined, //是否校验必填
        ...extendProps
    } = props;

    const { custLabelTypeList } = global;

    useEffect(() => {
        if (!(custLabelTypeList instanceof Array) || custLabelTypeList?.length === 0) {
            dispatch({
                type: 'global/initCustLabelTypeList',
            });
        }
    }, []);

    const filterIds = useMemo(() => {
        if (onlyIds?.length > 0) {
            let treeList = copyObjectCommon(custLabelTypeList);
            const formatFilterItem = filterTreeList(treeList, onlyIds);

            return formatFilterItem;
        } else {
            return excludeIds || [];
        }
    }, [custLabelTypeList, onlyIds, excludeIds]);

    const setFormValues = (values) => {
        if (parentName?.length > 0) {
            let index = 0;
            const superName = parentName[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < parentName.length) {
                if (!obj) {
                    obj = superObj[parentName[index]];
                } else {
                    obj = obj[parentName[index]];
                }
            }

            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });

            form.setFieldsValue({ [superName]: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    // select属性
    const { maxTagCount = 3, allowClear } = props;
    return (
        <FormItem noStyle shouldUpdate={(prevValues, curValues) => true}>
            {({ getFieldValue }) => {
                const activeCrowd = getFieldValue([...parentName, name]);
                const activeCrowdFlag = getFieldValue([...parentName, 'activeCrowdFlag']);

                // console.log(66666, parentName, activeCrowdFlag, activeCrowd);
                if (typeof activeCrowd == 'string' && activeCrowd.indexOf(',') > -1) {
                    // 内容格式转换
                    setFormValues({ activeCrowd: activeCrowd?.split(',') });
                    return null;
                }
                return (
                    <Fragment>
                        {
                            <Fragment>
                                {!showTableSearch ? (
                                    <FormItem
                                        label={label}
                                        rules={[
                                            {
                                                required: isEmpty(mustRequired)
                                                    ? true
                                                    : mustRequired,
                                                message: '请选择',
                                            },
                                        ]}
                                        {...extendProps}
                                        name={
                                            parentName?.length > 0
                                                ? [
                                                      parentName[parentName.length - 1],
                                                      'activeCrowdFlag',
                                                  ]
                                                : 'activeCrowdFlag'
                                        }
                                        noStyle={hideAll}
                                        initialValue={'0'}
                                    >
                                        {!hideAll && (
                                            <Radio.Group disabled={disabled}>
                                                <Radio value={'1'}>全部</Radio>
                                                <Radio value={'0'}>部分</Radio>
                                            </Radio.Group>
                                        )}
                                    </FormItem>
                                ) : null}

                                {!activeCrowdFlag || activeCrowdFlag === '0' || showTableSearch ? (
                                    <CheckBoxGroup
                                        label={label}
                                        rules={[
                                            {
                                                required: isEmpty(mustRequired)
                                                    ? true
                                                    : mustRequired,
                                                message: '请选择',
                                            },
                                        ]}
                                        {...extendProps}
                                        name={
                                            parentName?.length > 0
                                                ? [parentName[parentName.length - 1], name]
                                                : name
                                        }
                                        showArrow
                                        parentName={
                                            parentName?.length > 0
                                                ? parentName.slice(0, parentName.length - 1)
                                                : parentName
                                        }
                                        treeCheckable={mode == 'multiple' ? true : false}
                                        form={form}
                                        selectList={custLabelTypeList}
                                        disabled={disabled}
                                        disableIds={[...(disableIds || []), ...(requiredIds || [])]}
                                        filterIds={filterIds}
                                        maxTagCount={maxTagCount}
                                        initialValue={requiredIds || []}
                                        hideAll
                                        allowClear={
                                            !requiredIds?.length || extendProps?.allowClear == true
                                        }
                                    />
                                ) : null}
                            </Fragment>
                        }
                    </Fragment>
                );
            }}
        </FormItem>
    );
};

export default connect(({ global }) => ({
    global,
}))(ActiveCrowdCheckTree);
