import { useState, useMemo, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { Select } from 'antd';
import { getStationCityListApi } from '@/services/CommonApi';

const Option = Select;

/**
 * 过滤运营商下的城市选择组件
 */
const OperCitySelectItem = (props, ref) => {
    const { operId, value, onChange, otherProps } = props;

    const orignOperId = useRef();

    useImperativeHandle(ref, () => {
        return {};
    });

    const [cityLoading, updateCityLoading] = useState(false);
    const [cityList, updateCityList] = useState([]);

    useEffect(() => {
        if (operId) {
            //重制运营商城市选择选项
            initCities();
            if (orignOperId.current && operId != orignOperId.current) {
                onChange && onChange([]);
            }
            orignOperId.current = operId;
        }
    }, [operId]);

    const cityData = useMemo(() => {
        const list = [];
        for (const item of cityList) {
            list.push({
                title: item.cityName,
                value: item.city,
            });
        }
        return list;
    }, [cityList]);

    const initCities = async () => {
        try {
            updateCityLoading(true);
            const {
                data: { cityList },
            } = await getStationCityListApi({ operId });

            updateCityList(cityList);
            return;
        } catch (error) {
            updateCityList([]);
            throw new Error(error);
        } finally {
            updateCityLoading(false);
        }
    };
    const selectCityEvent = (citys) => {
        onChange && onChange(citys);
    };
    return (
        <Select
            showSearch
            value={value}
            filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            loading={cityLoading}
            mode="multiple"
            allowClear
            showArrow
            {...otherProps}
            onChange={selectCityEvent}
        >
            {cityData.map((ele) => {
                return (
                    <Option key={ele.value} value={ele.value}>
                        {ele.title}
                    </Option>
                );
            })}
        </Select>
    );
};

export default forwardRef(OperCitySelectItem);
