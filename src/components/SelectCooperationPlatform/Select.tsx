import { useImperativeHandle, forwardRef, Fragment, useEffect, useMemo, useState } from 'react';
import { Checkbox, Row, Col, Form, Select } from 'antd';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';

const { Option } = Select;
const SelectChannelItem = (
    props: API.CommonFormItem<string | string[]> & {
        multiple?: boolean;
        allowClear?: boolean;
        maxTagCount?: number;
        fieldProps?: Record<string, any>;
        disabledIds?: string[];
    },
    ref: any,
) => {
    const {
        value,
        onChange,
        disabled,
        multiple = false,
        allowClear = true,
        maxTagCount = 5,
        fieldProps = {},
        disabledIds = [],
    } = props;
    useImperativeHandle(ref, () => {});

    const changeSelectEvent = (newVal: string | string[]) => {
        const result = newVal;
        onChange && onChange(result);
    };
    return (
        <Select
            value={value}
            showArrow
            placeholder="请选择"
            onChange={changeSelectEvent}
            disabled={disabled}
            allowClear={allowClear}
            maxTagCount={maxTagCount}
            mode={multiple ? 'multiple' : undefined}
            style={{ width: '100%' }}
            {...fieldProps}
        >
            <Option
                value={COOPERATION_PLATFORM_TYPES.XDT}
                disabled={disabledIds.includes(COOPERATION_PLATFORM_TYPES.XDT)}
            >
                新电途
            </Option>
            <Option
                value={COOPERATION_PLATFORM_TYPES.XDT_X}
                disabled={disabledIds.includes(COOPERATION_PLATFORM_TYPES.XDT_X)}
            >
                新电途X
            </Option>
        </Select>
    );
};

export default forwardRef(SelectChannelItem);
