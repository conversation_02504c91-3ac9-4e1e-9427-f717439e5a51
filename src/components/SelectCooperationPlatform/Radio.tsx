import { useImperativeHandle, forwardRef, Fragment, useEffect, useMemo, useState } from 'react';
import { Radio } from 'antd';
import { COOPERATION_PLATFORM_TYPES } from '@/config/declare';

const SelectChannelItem = (
    props: API.CommonFormItem<string> & {
        fieldProps?: Record<string, any>;
        disabledIds?: string[];
    },
    ref: any,
) => {
    const { value, onChange, disabled, fieldProps = {}, disabledIds = [] } = props;
    useImperativeHandle(ref, () => {});

    const changeSelectEvent = (event: any) => {
        const {
            target: { value: newVal },
        } = event;
        const result = newVal;
        onChange && onChange(result);
    };
    return (
        <Radio.Group {...fieldProps} value={value} onChange={changeSelectEvent} disabled={disabled}>
            <Radio
                value={COOPERATION_PLATFORM_TYPES.XDT}
                disabled={disabledIds.includes(COOPERATION_PLATFORM_TYPES.XDT)}
            >
                新电途
            </Radio>
            <Radio
                value={COOPERATION_PLATFORM_TYPES.XDT_X}
                disabled={disabledIds.includes(COOPERATION_PLATFORM_TYPES.XDT_X)}
            >
                新电途X
            </Radio>
        </Radio.Group>
    );
};

export default forwardRef(SelectChannelItem);
