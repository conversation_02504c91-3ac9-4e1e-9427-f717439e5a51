import { Tooltip } from 'antd';
import { copyTextCommon } from '@/utils/utils';
import React, { useState } from 'react';

const CopyComponent = (props) => {
    const { text } = props;

    const [copyStatus, setCopyStatus] = useState(false);
    return (
        <Tooltip
            title={copyStatus ? '复制完成' : '点击复制'}
            placement="right"
            onVisibleChange={(visible) => {
                if (visible) {
                    // 还原状态
                    setCopyStatus(false);
                }
            }}
        >
            <span
                title={text}
                onClick={() => {
                    copyTextCommon(text);
                    setCopyStatus(true);
                }}
            >
                {text}
            </span>
        </Tooltip>
    );
};

export default CopyComponent;
