import { ButtonProps } from 'antd';
import { PopoverProps } from 'antd';

export interface ButtonConfig {
  props?: ButtonProps;
  style?: React.CSSProperties;
  children?: ButtonConfig[];
  event?: (row: any, e?: React.MouseEvent) => Promise<void> | void;
  show?: boolean | ((row: any) => boolean);
  label?: string;
}

export interface BuseButtonsProps {
  btns: ButtonConfig[] | ((row: any) => ButtonConfig[]);
  row?: any;
  layout?: {
    type?: 'flex';
    justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between';
    align?: 'top' | 'middle' | 'bottom';
    gutter?: number | [number, number];
  };
  buttonSpan?: number;
  maxCount?: number;
  loading?: boolean;
  popoverProps?: PopoverProps;
}

export interface PopoverButtonProps {
  btn: ButtonConfig;
  row?: any;
  loading?: boolean;
  popoverProps?: PopoverProps;
  className?: string;
}

export interface CustomButtonProps {
  btn: ButtonConfig;
  row?: any;
  pLoading?: boolean;
  className?: string;
}
