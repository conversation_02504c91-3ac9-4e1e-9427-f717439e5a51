import React, { useState } from 'react';
import { Button } from './deps';
import { noBorderButtonProp } from './deps';
import { CustomButtonProps } from './types';
import styles from './BuseButtons.less';

const CustomButton: React.FC<CustomButtonProps> = ({
  btn,
  row = {},
  pLoading = false,
  className = '',
  ...restProps
}) => {
  const [loading, setLoading] = useState(false);

  const handleButtonClick = async (e: React.MouseEvent) => {
    if (!btn.event) return;

    try {
      setLoading(true);
      await btn.event(row, e);
    } catch (error) {
      console.error('Error executing button event:', error);
    } finally {
      setLoading(false);
    }
  };

  const getClassName = () => {
    const cls = ['custom-button'];

    // 检查是否为文本按钮
    const isTextButton =
      btn?.props?.[Object.keys(noBorderButtonProp)[0] as keyof typeof btn.props] ===
      Object.values(noBorderButtonProp)[0];

    if (isTextButton) {
      cls.push('custom-button-text');
    }

    if (className) {
      cls.push(className);
    }

    return cls.join(' ');
  };

  return (
    <Button
      {...restProps}
      {...(btn?.props || {})}
      className={getClassName()}
      onClick={handleButtonClick}
      loading={loading || pLoading}
    >
      {btn?.label}
    </Button>
  );
};

export default CustomButton;
