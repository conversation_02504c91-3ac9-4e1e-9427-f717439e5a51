# BuseButtons - Umi3.5 + Antd 版本

基于 umi3.5 和 antd 的按钮组组件，从原 Vue 版本迁移而来。

## 功能特性

1. **配置化渲染** - 通过配置项进行按钮渲染
2. **布局支持** - 支持 antd Row 组件的所有布局配置
3. **最大显示限制** - 支持最大显示按钮数限制，超过则通过气泡按钮展示
4. **Loading 状态** - 支持按钮组整体和单个按钮的 loading 状态
5. **条件显示** - 支持按钮的条件显示逻辑

## Props

### BuseButtonsProps

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| btns | `ButtonConfig[]` \| `(row: any) => ButtonConfig[]` | - | 按钮组数据，用以渲染按钮 |
| row | `any` | `{}` | 按钮所在模块的数据，如行数据 |
| layout | `object` | `{ type: 'flex', justify: 'start', align: 'bottom' }` | 布局配置，参考 antd Row 组件 |
| buttonSpan | `number` | - | 一行中按钮所占用的网格数 |
| maxCount | `number` | `5` | 最多显示几个按钮，超出的将会被隐藏 |
| loading | `boolean` | `false` | 按钮组整体loading状态 |
| popoverProps | `PopoverProps` | `{}` | 气泡框配置 |

### ButtonConfig

| 参数 | 类型 | 说明 |
|------|------|------|
| props | `ButtonProps` | 按钮属性，参考 antd Button 组件文档 |
| style | `React.CSSProperties` | 按钮样式 |
| children | `ButtonConfig[]` | 若设置了此属性，button group 将被渲染为浮动气泡按钮组 |
| event | `(row: any, e?: React.MouseEvent) => Promise<void> \| void` | 按钮点击事件 |
| show | `boolean \| (row: any) => boolean` | 按钮是否显示 |
| label | `string` | 按钮文本 |

## 使用示例

```tsx
import React from 'react';
import BuseButtons from '@/components/BuseButtons';

const Example = () => {
  const handleEdit = async (row: any) => {
    console.log('编辑', row);
  };

  const handleDelete = async (row: any) => {
    console.log('删除', row);
  };

  const buttons = [
    {
      label: '编辑',
      props: { type: 'primary' },
      event: handleEdit,
    },
    {
      label: '删除',
      props: { danger: true },
      event: handleDelete,
      show: (row: any) => row.canDelete,
    },
    {
      label: '更多',
      children: [
        {
          label: '查看详情',
          event: (row: any) => console.log('查看详情', row),
        },
        {
          label: '导出',
          event: (row: any) => console.log('导出', row),
        },
      ],
    },
  ];

  const rowData = { id: 1, name: '测试', canDelete: true };

  return (
    <BuseButtons
      btns={buttons}
      row={rowData}
      maxCount={3}
    />
  );
};

export default Example;
```

## 迁移说明

从 Vue 版本迁移到 React 版本的主要变化：

1. **框架变更**: Vue → React + TypeScript
2. **UI 库变更**: Element Plus → Antd
3. **状态管理**: Vue 响应式 → React Hooks
4. **样式**: Vue scoped CSS → CSS Modules
5. **插槽**: Vue slots → React children props

## 注意事项

1. 确保项目已安装 `antd` 依赖
2. 确保项目支持 CSS Modules (umi3.5 默认支持)
3. 按钮事件函数建议使用异步函数，以便控制 loading 状态
