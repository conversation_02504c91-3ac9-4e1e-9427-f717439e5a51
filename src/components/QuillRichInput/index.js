import { useRef, useImperativeHandle, forwardRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import quillStyles from './quill.less';
import { Form } from 'antd';
const FormItem = Form.Item;

const QuillRichInput = (props, ref) => {
    const { placeholder, filePath, disabled, modules, height } = props;
    const editRef = useRef();
    useImperativeHandle(ref, () => {
        return {
            getRichTextLength() {
                const editDom = editRef?.current?.getEditor();
                return editDom?.getLength();
            },
        };
    });

    return (
        <FormItem {...props}>
            <ReactQuill
                ref={editRef}
                // className={quillStyles['quill-rich-input']}
                readOnly={disabled}
                style={{ height: height || '250px', marginBottom: '44px' }}
                placeholder={placeholder}
                theme="snow"
                modules={modules}
            />
        </FormItem>
    );
};

export default forwardRef(QuillRichInput);
