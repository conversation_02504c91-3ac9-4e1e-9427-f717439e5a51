import { Button, Divider, Form, Space } from 'antd';
import CityTransferModal from '../CityTransferModal';
import CityImportModal from '../CitysSelect/CityImportModal';
import { Fragment, useRef } from 'react';

export const CityForm = (props) => {
    const {
        name = 'cityCodes',
        label = '活动城市',
        formItemLayout = {},
        disabled,
        disabledKeys,
    } = props;

    const cityModalRef = useRef();
    const cityImportModalRef = useRef();

    return (
        <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues[name] !== curValues[name]}
        >
            {({ getFieldValue }) => {
                const cityCodes = getFieldValue(name);
                return (
                    <Form.Item
                        label={label}
                        name={name}
                        required
                        rules={[
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!cityCodes?.length) {
                                        return Promise.reject(`请选择${label}`);
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        {...formItemLayout}
                    >
                        {(cityCodes?.length && (
                            <Space size="small">
                                {!disabled && (
                                    <Fragment>
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                cityModalRef?.current?.show({
                                                    defaultKeys: cityCodes,
                                                    disabledKeys,
                                                });
                                            }}
                                        >
                                            编辑
                                        </Button>
                                        <Divider
                                            type="vertical"
                                            style={{
                                                margin: 0,
                                            }}
                                        />
                                    </Fragment>
                                )}

                                <Button
                                    type="link"
                                    onClick={() =>
                                        cityModalRef?.current?.show({
                                            defaultKeys: cityCodes,
                                            disabled: true,
                                        })
                                    }
                                >
                                    查看
                                </Button>

                                {!disabled && (
                                    <Fragment>
                                        <Divider
                                            type="vertical"
                                            style={{
                                                margin: 0,
                                            }}
                                        />
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                cityImportModalRef?.current?.show({
                                                    ids: cityCodes,
                                                });
                                            }}
                                        >
                                            导入
                                        </Button>
                                    </Fragment>
                                )}
                            </Space>
                        )) || (
                            <Space size="small">
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        cityModalRef?.current?.show();
                                    }}
                                    disabled={disabled}
                                >
                                    选择区域
                                </Button>

                                <Button
                                    type="primary"
                                    onClick={() => {
                                        cityImportModalRef?.current?.show({
                                            ids: cityCodes,
                                        });
                                    }}
                                    disabled={disabled}
                                >
                                    文本导入
                                </Button>
                            </Space>
                        )}

                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                prevValues[name] !== curValues[name]
                            }
                        >
                            {({ setFieldsValue }) => {
                                return (
                                    <Fragment>
                                        <CityTransferModal
                                            ref={cityModalRef}
                                            onFinish={(citys) => {
                                                setFieldsValue({
                                                    [name]: citys.map((ele) => ele.areaCode || ele),
                                                });
                                            }}
                                            deleteEnabled={false}
                                        />

                                        <CityImportModal
                                            initRef={cityImportModalRef}
                                            onFinish={(citys) => {
                                                const _citys = [
                                                    ...(cityCodes || []),
                                                    ...(citys.map((ele) => ele.areaCode || ele) ||
                                                        []),
                                                ];
                                                setFieldsValue({
                                                    [name]: _citys,
                                                });
                                            }}
                                        />
                                    </Fragment>
                                );
                            }}
                        </Form.Item>
                    </Form.Item>
                );
            }}
        </Form.Item>
    );
};
