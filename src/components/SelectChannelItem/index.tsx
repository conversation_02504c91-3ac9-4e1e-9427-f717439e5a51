import { useImperativeHandle, forwardRef, Fragment, useEffect, useMemo, useState } from 'react';
import { Checkbox, Row, Col, Form, TreeSelect } from 'antd';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import { useRequest } from 'ahooks';
import { getCodesApi } from '@/services/CommonApi';

const { SHOW_CHILD, SHOW_PARENT, SHOW_ALL } = TreeSelect;
type SelectVo = { codeName: string; codeValue: string; children?: SelectVo[] };
type ItemPropVo = {
    showCheckedStrategy?: any;
    hideAll?: boolean; //是否隐藏全部选项
    allKey?: string; //自定义全部节点的标识 正常用不到
    maxTagCount?: number; //最多显示多少个tag
    optionList: SelectVo[];
    disableIds?: string[]; //禁用id  会在组件里置灰
    filterKeys?: string[]; //只有这种类别下的会显示
    filterIds?: string[]; //过滤id  不会显示在组件里
    onlyIds?: string[]; //只有这个数组里的会显示在组件里
    multiple?: boolean; //是否多选
    transform?: (selects: string[]) => any; //格式化数据
};

const initTreeList = (treeList: SelectVo[], disableIds: string[], filterIds: string[]) => {
    //先过滤再禁用
    return (
        treeList
            ?.filter((ele) => !filterIds.includes(ele.codeValue))
            .map((ele) => {
                let children: any[] = [];
                if (ele.children instanceof Array && ele.children?.length > 0) {
                    children = initTreeList(ele.children, disableIds, filterIds);
                }
                const treeItem = {
                    title: ele.codeName,
                    value: ele.codeValue,
                    key: ele.codeValue,
                    disabled: disableIds.includes(ele.codeValue),
                };
                if (children?.length > 0) {
                    treeItem.children = children;
                    let hasChildCanSelect = false;
                    children.forEach((child) => {
                        if (!child.disabled) {
                            hasChildCanSelect = true;
                        }
                    });
                    if (!hasChildCanSelect) {
                        treeItem.disabled = true;
                    }
                }
                return treeItem;
            }) || []
    );
};
const filterTreeList = (treeList: SelectVo[], disableIds: string[]) => {
    return treeList?.filter((ele) => {
        if (ele?.children instanceof Array && ele?.children?.length > 0) {
            ele.children = filterTreeList(ele.children, disableIds);
        }
        return !disableIds.includes(ele.codeValue);
    });
};
export const filterKeyList = (treeList: SelectVo[], typeKey?: string) => {
    const reg = new RegExp(`^${typeKey}-\.*`);

    return treeList?.filter((ele) => {
        if (ele?.children instanceof Array && ele?.children?.length > 0) {
            ele.children = filterKeyList(ele.children, typeKey);
        }
        return reg.test(ele?.codeName);
    });
};
const unfilterKeyList = (treeList: SelectVo[], filterKeys: string[]) => {
    return treeList?.filter((ele) => {
        if (ele?.children instanceof Array && ele?.children?.length > 0) {
            ele.children = unfilterKeyList(ele.children, filterKeys);
        }
        return !filterKeys.find((key) => {
            const reg = new RegExp(`^${key}-\.*`);
            return reg.test(ele?.codeName);
        });
    });
};
const onlyTreeList = (treeList: SelectVo[], onlyIds: string[]) => {
    return treeList?.filter((ele) => {
        if (ele?.children instanceof Array && ele?.children?.length > 0) {
            ele.children = onlyTreeList(ele.children, onlyIds);
        }
        return onlyIds.includes(ele.codeValue);
    });
};
const SelectChannelItem = (props: API.CommonFormItem<string[]> & ItemPropVo, ref: any) => {
    const {
        value,
        onChange,
        multiple = true,
        disabled,
        showCheckedStrategy = SHOW_CHILD,
        hideAll = true,
        allKey = '999',
        maxTagCount = 5,
        disableIds = [],
        filterKeys = [],
        filterIds = [],
        onlyIds = [],
        optionList = [],
        transform,
    } = props;
    useImperativeHandle(ref, () => {});

    const AllKeys = ['新电途', '新电途X', '线下渠道'];

    const formatValue = useMemo(() => {
        if (value instanceof Array) {
            return value;
        } else if (typeof value === 'string') {
            return value?.split(',')?.filter((ele: string) => ele);
        }
        return [];
    }, [value]);

    // const { data: optionList, loading } = useRequest(async () => {
    //     try {
    //         const { data } = await getCodesApi('actChannel');
    //         return data;
    //     } catch (error) {
    //         return Promise.reject(error);
    //     }
    // });

    //拷贝当前树结构用于过滤
    const filterSelectList = useMemo(() => {
        let list = [];

        let filterOriginList = optionList;
        if (onlyIds instanceof Array && onlyIds.length > 0) {
            filterOriginList = onlyTreeList(optionList, onlyIds);
        }

        for (const item of AllKeys) {
            if (filterKeys.length > 0 && !filterKeys.includes(item)) {
                continue;
            }

            const childList = filterKeyList(filterOriginList, item);
            if (childList.length > 0) {
                list.push({
                    codeName: item,
                    codeValue: item,
                    children: childList,
                });
            }
        }
        const otherList = unfilterKeyList(filterOriginList, AllKeys);
        if (isEmpty(filterKeys)) {
            if (otherList.length > 0) {
                // list.push({
                //     codeName: '其他-',
                //     codeValue: '其他-',
                //     children: otherList,
                // });
                list = list.concat(otherList);
            }
        }

        return list;
    }, [optionList]);

    const treeOptions = useMemo(() => {
        if (!hideAll) {
            return [
                {
                    title: '全部',
                    value: allKey,
                    key: allKey,
                    children: initTreeList(filterSelectList, disableIds, filterIds),
                },
            ];
        } else {
            return [...initTreeList(filterSelectList, disableIds, filterIds)];
        }
    }, [optionList, filterSelectList, disableIds, filterIds]);

    const changeSelectEvent = (selects: string[]) => {
        const result = transform ? transform(selects) : selects;
        onChange && onChange(result);
    };
    return (
        <>
            <TreeSelect
                value={formatValue}
                treeData={treeOptions}
                showCheckedStrategy={showCheckedStrategy}
                multiple={multiple}
                treeCheckable={multiple ? true : false}
                showArrow
                treeDefaultExpandAll
                placeholder="请选择"
                onChange={changeSelectEvent}
                disabled={disabled}
                maxTagCount={maxTagCount}
                allowClear
                filterTreeNode={(inputValue, treeNode) => {
                    return treeNode?.title?.indexOf(inputValue) >= 0;
                }}
            />
        </>
    );
};

export default forwardRef(SelectChannelItem);
