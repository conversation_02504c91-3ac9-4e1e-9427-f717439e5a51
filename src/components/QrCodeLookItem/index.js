import { <PERSON><PERSON><PERSON>, Card, Button, Space } from 'antd';
import { createQrcodeCommon, downloadImageByUrl } from '@/utils/utils';
import { useEffect, useMemo } from 'react';
import { useState } from 'react';
import { isFullUrl, isBase64Url } from '@/utils/verify';
const QrCodeLookComponent = (props) => {
    const { url, qrCode: showUrl, isBase64, bigType = false, withLogo = true } = props;

    const [qrcode, updateQrcode] = useState();
    useEffect(() => {
        createQRCode();
    }, []);

    const createQRCode = async () => {
        if (isBase64) {
            updateQrcode(`data:image/png;base64,${url}`);
        } else {
            const _qrcode = await createQrcodeCommon(url, withLogo);
            updateQrcode(_qrcode);
        }
    };

    return !bigType ? (
        <Tooltip
            title={
                <Card bordered={false} style={{ textAlign: 'center' }}>
                    <img src={qrcode} style={{ width: '150px', height: '150px' }} />
                    <p style={{ width: '150px' }}>{showUrl}</p>
                    <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                downloadImageByUrl(qrcode);
                            }}
                        >
                            下载
                        </Button>
                    </Space>
                </Card>
            }
            color="#ffffff"
        >
            <img src={qrcode} style={{ width: '30px', height: '30px' }} />
        </Tooltip>
    ) : (
        <img
            src={qrcode}
            style={{ width: '240px', height: '240px', margin: 'auto', display: 'block' }}
        />
    );
};

export default QrCodeLookComponent;
