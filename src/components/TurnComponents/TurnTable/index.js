import React, { Fragment, useEffect, useState, useRef, useMemo, forwardRef } from 'react';
import commonStyles from '@/assets/styles/common.less';
import { Button, Form, Modal, Space, Popconfirm, InputNumber } from 'antd';
import TablePro from '@/components/TablePro';
import PropTypes from 'prop-types';
import AddPrizeViewModal from '@/components/TurnComponents/GroupPageModal';
import GroupPageModal from '@/components/TurnComponents/GroupPageModal';
import { PlusOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';

const FormItem = Form.Item;

const TurnTable = (props, ref) => {
    const {
        form,
        actId,
        needCheck, // 是否需要校验必填
        editabled, // 是否可编辑
        addToEnabled, // 是否可追加
        deleteEnabled, // 是否可删除
        putCpnList, // 已发放优惠券列表
        onRefresh, // 外部各自实现刷新方法
        formItemLayout,
        extInParams, // 额外入参
        title = '配置礼包',
        name = 'turnList',
        filePath, // 父级键，仅嵌套时传参
        putNumCol, // 默认使用券数量编辑框
        putColTitle = '券数量', // 券数量编辑框标题
        putColDataIndex = 'putNum', // 券数量编辑框字段
        totalItem = undefined, // 默认使用礼包总数统计项
        disabledIds, // 限制不允许勾选的id列表，传参给GroupPageModal组件用
        changeCouponCallback, // 如果实现了变更优惠券的回调，需要在回调内返回处理后的数据集，添加和删除都会调用
    } = props;

    const addNewPrizeRef = useRef();
    const [cpnList, changeCpnList] = useState([]);

    useEffect(() => {
        if (putCpnList?.length) {
            changeCpnList(putCpnList);
        }
    }, [putCpnList]);

    const setFormValues = (values) => {
        if (filePath?.length) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }
            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue({ superName: superObj });
        } else {
            form.setFieldsValue(values);
        }
    };

    useEffect(() => {
        const list = cpnList?.map((ele) => ({
            ...ele,
        }));
        const preList = form.getFieldValue(filePath ? [...filePath, name] : name);
        if (!preList?.length || JSON.stringify(preList.sort()) != JSON.stringify(list.sort())) {
            setFormValues({ [name]: list });
        }
    }, [cpnList]);

    const formmatList = useMemo(() => {
        // 格式化追加券
        const list = [];
        cpnList?.forEach((cpn, index) => {
            const item = { ...cpn };

            let hasIndex = -1;
            for (let i = 0; i < list.length; i++) {
                const element = list[i];
                if (
                    element.pushPrizeId &&
                    item.pushPrizeId &&
                    element.pushPrizeId == item.pushPrizeId
                ) {
                    hasIndex = i;
                    break;
                }
            }
            item.key = index;
            if (hasIndex >= 0) {
                item.isChildren = true;
                if (!list[hasIndex].children) {
                    list[hasIndex].children = [item];
                } else {
                    list[hasIndex].children.push(item);
                }
            } else {
                list.push(item);
            }
        });
        return list;
    }, [cpnList]);

    // 新增
    const addCpnEvent = () => {
        addNewPrizeRef.current.add(cpnList);
    };

    const prizeColumns = [
        {
            title: '活动编号 ',
            width: 80,
            dataIndex: 'actNo',
            render(text, record, index) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称 ',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动有效期 ',
            width: 200,
            dataIndex: 'actTime',
            render(text, record) {
                return <span title={text}>{text || record.unGetNum}</span>;
            },
        },
        {
            title: '活动状态 ',
            width: 100,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    if (editabled) {
        prizeColumns.push({
            title: '操作',
            fixed: 'right',
            width: 140,
            render(text, record) {
                const editList = [
                    <Link
                        key={'03'}
                        to={`/marketing/platformactive/turnOver/list/look/${record.actId}`}
                        target="_blank"
                    >
                        查看
                    </Link>,
                ];
                deleteEnabled &&
                    editList.push(
                        <Popconfirm
                            title="要从列表中删除此转盘？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => {
                                const newList = [...cpnList];
                                for (let index = cpnList.length - 1; index >= 0; index--) {
                                    const element = cpnList[index];
                                    if (element.actId == record.actId) {
                                        newList.splice(index, 1);
                                        if (record.isChildren) {
                                            // 因为根节点的actId和子节点的一致，所以要判断如果点的是根节点的删除按钮，则所有优惠券全部删掉，否则只删掉当前子节点
                                            break;
                                        }
                                    }
                                }
                                changeCpnList([...newList]);
                                changeCouponCallback && changeCouponCallback([...newList]);
                            }}
                        >
                            <span className={commonStyles['table-btn']}>删除</span>
                        </Popconfirm>,
                    );

                return <Space>{editList}</Space>;
            },
        });
    }

    return (
        <Fragment>
            <FormItem
                name={filePath ? [filePath[filePath.length - 1], '_turnList'] : '_turnList'}
                label={title}
                required
                {...formItemLayout}
            >
                <Button disabled={!editabled} type="primary" onClick={addCpnEvent}>
                    <PlusOutlined />
                    添加转盘
                </Button>
            </FormItem>

            <FormItem
                name={filePath ? [filePath[filePath.length - 1], name] : name}
                rules={[
                    ({ getFieldValue }) => ({
                        validator(rule, value) {
                            if (needCheck && !formmatList?.length) {
                                return Promise.reject(`请选择`);
                            }
                            return Promise.resolve();
                        },
                    }),
                ]}
                {...formItemLayout}
            >
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.actId}
                    dataSource={formmatList}
                    pagination={false}
                    expandable={{ defaultExpandAllRows: true }}
                    columns={prizeColumns}
                />
            </FormItem>

            <GroupPageModal
                {...props}
                initRef={addNewPrizeRef}
                onAddFinish={(_turnList) => {
                    let newList = [];
                    if (changeCouponCallback) {
                        newList = changeCouponCallback(_turnList);
                    } else {
                        // 如果原先有填数量，以原先的数量为准
                        newList = _turnList.map((cpn) => {
                            let oldCpns = cpnList.filter((x) => x.actId == cpn.actId);
                            let oldCpn = oldCpns?.length && oldCpns[0];
                            if (oldCpn && oldCpn[putColDataIndex]) {
                                cpn[putColDataIndex] = oldCpn[putColDataIndex];
                            }
                            if (parseInt(cpn[putColDataIndex]) < 1 || !oldCpn) {
                                cpn[putColDataIndex] = 1;
                            }
                            if (oldCpn?.pushPrizeId) {
                                cpn.pushPrizeId = oldCpn.pushPrizeId;
                            }
                            return cpn;
                        });
                    }
                    changeCpnList(newList);
                }}
                extInParams={extInParams}
            />
        </Fragment>
    );
};

// TurnTable.propTypes = {
//     dispatch: PropTypes.func.isRequired,
//     global: PropTypes.object.isRequired,
//     couponModel: PropTypes.object.isRequired,
// };

export default forwardRef(TurnTable);
