import React, {
    Fragment,
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useMemo,
} from 'react';
import { connect } from 'umi';
import { Button, InputNumber, Select, Form, Modal, message, Space, Input, Row, Col } from 'antd';
import { addCpnApi } from '@/services/Marketing/MarketingTurnoverApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { Option } = Select;

const FormItem = Form.Item;

const searchformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};

const GroupPageModal = (props, ref) => {
    const {
        dispatch,
        global,
        initRef,
        onAddFinish, // 添加完毕的回调事件，如果是追加，返回空，如果是添加，返回已选中的cpn对象
        onEditFinish, // 编辑完毕回调，如果是追加，返回空，如果是编辑，返回编辑后的cpn对象
        extInParams, // 额外入参
        disabledIds, // 限制不允许勾选的id列表
        turnoverModel: { turnoverList = [], turnoverListTotal },
    } = props;

    const [showPrizeView, togglePrizeView] = useState(false); // 选择奖品弹窗
    const [cpnList, updateCpnList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [listLoading, updateListLoading] = useState(false);

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        () => ({
            tabType: '03',
        }),
        props,
    );

    // useEffect(() => {
    //     if (!cpnOwnerOptions || cpnOwnerOptions.length == 0) {
    //         dispatch({
    //             type: 'global/initCpnOwnerOptions',
    //         });
    //     }
    // }, []);

    useImperativeHandle(initRef, () => ({
        // 追加和添加都调add方法，传入已选中的id列表，活动id
        add: (_turnList) => {
            resetData();
            togglePrizeView(true);
            updateCpnList([..._turnList]);
        },
        close: () => {
            togglePrizeView(false);
        },
    }));

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [form] = Form.useForm();

    const searchData = () => {
        const data = form.getFieldsValue(true);
        const params = {
            ...data,
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: '',
            endDate: '',
            // actState: '2',
            actStates: '1,2',
            actSubType: '1502',
        };

        updateListLoading(true);
        dispatch({
            type: 'turnoverModel/getTurnoverList',
            options: params,
        }).then(() => updateListLoading(false));
    };

    const resetData = () => {
        form.resetFields();
        form.setFieldsValue({ ...extInParams });
        updateCpnList([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const onFinish = () => {
        if (!cpnList?.length) {
            message.error('请选择转盘');
            return;
        }

        onAddFinish && onAddFinish(cpnList.sort((a, b) => (a.creTime < b.creTime ? 1 : -1)));
        initRef?.current?.close();
    };

    // const cpnOwnerOptionsView = useMemo(
    //     () =>
    //         cpnOwnerOptions.map((ele) => {
    //             return (
    //                 <Option key={ele.codeValue} value={ele.codeValue}>
    //                     {ele.codeName}
    //                 </Option>
    //             );
    //         }),
    //     [cpnOwnerOptions],
    // );

    const columns = [
        {
            title: '活动编码 ',
            width: 80,
            dataIndex: 'actNo',
            render(text, record, index) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动名称 ',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动状态 ',
            width: 160,
            dataIndex: 'actStateName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '奖品数量 ',
            width: 160,
            dataIndex: 'prizeNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];

    const rowSelection = {
        selectedRowKeys: cpnList.map((item) => `${item.actId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            // 筛选出非当前页的勾选项，不予处理
            let otherCpns = cpnList.filter(
                (x) => turnoverList.filter((now) => now.actId == x.actId).length == 0,
            );
            // if (cpnList.length === 0) {
            updateCpnList([...selectedRows]);
            // }
        },
        getCheckboxProps: (record) => ({
            disabled: record.pushPrizeId || disabledIds?.indexOf(record.actId) >= 0,
            name: record.actId,
        }),
        type: 'radio',
    };

    return (
        <Modal
            title={'添加转盘'}
            destroyOnClose
            width={1200}
            visible={showPrizeView}
            onCancel={() => initRef?.current?.close()}
            footer={null}
            maskClosable={false}
        >
            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{ ...extInParams }}
                scrollToFirstError
            >
                <Row>
                    <Col span={6}>
                        <FormItem label="活动编码" name="actNo" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <FormItem label="活动名称" name="actName" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <Space>
                            <Button type="primary" onClick={searchData}>
                                查询
                            </Button>
                            <Button onClick={resetData}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${record.actId}`}
                dataSource={turnoverList}
                columns={columns}
                onChange={onTableChange}
                rowSelection={{
                    type: 'checkbox',
                    ...rowSelection,
                }}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: turnoverListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                sticky={{ offsetHeader: 0 }}
            />
            <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                <Button type="primary" onClick={onFinish}>
                    提交
                </Button>
                <Button onClick={() => initRef?.current?.close()}>取消</Button>
            </Space>
        </Modal>
    );
};

export default forwardRef(GroupPageModal);
