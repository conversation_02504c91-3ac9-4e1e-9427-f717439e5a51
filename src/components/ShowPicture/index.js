import { Fragment, useState, useMemo } from 'react';
import { Image } from 'antd';

import styles from '@/assets/styles/common.less';

const ShowPicture = (props) => {
    const { url } = props;
    const [bShow, toggleShowEvent] = useState(false);
    const imgRender = useMemo(() => {
        let urlDom = null;
        if (url instanceof Array) {
            urlDom = url.map((ele, index) => {
                return <Image key={index} src={ele}></Image>;
            });
        } else {
            urlDom = <Image src={url}></Image>;
        }
        return (
            <div
                style={{
                    display: 'none',
                }}
            >
                <Image.PreviewGroup
                    preview={{
                        visible: bShow,
                        onVisibleChange: (vis) => toggleShowEvent(vis),
                    }}
                >
                    {urlDom}
                </Image.PreviewGroup>
            </div>
        );
    });
    return (
        <Fragment>
            <span
                className={styles['table-btn']}
                key="look"
                onClick={() => {
                    toggleShowEvent(true);
                }}
            >
                查看
            </span>

            {imgRender}
        </Fragment>
    );
};
export default ShowPicture;
