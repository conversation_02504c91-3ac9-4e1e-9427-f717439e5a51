.thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 2px;
    cursor: pointer;
    &:hover {
        .thumbnail-utils {
            opacity: 1;
        }
    }
    .thumbnail-utils {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        font-size: 28px;
        background-color: rgba(0, 0, 0, 0.3);
        opacity: 0;
        transition: all 0.5;
        z-index: 10;
        .utils-bar {
            position: absolute;
            top: 50%;
            left: 50%;
            color: white;
            transform: translate(-50%, -50%);
            .eye {
                cursor: pointer;
            }
        }
    }
}

.thumbnail_bar {
    position: relative;
    width: 160px;
    height: 26px;
    overflow: hidden;
    border-radius: 2px;
    cursor: pointer;
    &:hover {
        .thumbnail-utils {
            opacity: 1;
        }
    }
    .thumbnail-utils {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        font-size: 28px;
        background-color: rgba(0, 0, 0, 0.3);
        opacity: 0;
        transition: all 0.5;
        z-index: 10;
        .utils-bar {
            position: absolute;
            top: 50%;
            left: 50%;
            color: white;
            transform: translate(-50%, -50%);
            .eye {
                cursor: pointer;
            }
        }
    }
}
