import { Fragment, useState } from 'react';
import { Button, Modal, Space } from 'antd';
import styles from './Thumbnail.less';
import {
    EyeOutlined,
    EyeInvisibleOutlined,
    DownloadOutlined,
    DeleteOutlined,
} from '@ant-design/icons';
import classnames from 'classnames';
import { downloadImageByUrl } from '@/utils/utils';
import { useMemo } from 'react';

const ThumbnailView = (props) => {
    const { url, width, delEvent, disabled, isBar } = props;
    const [bShow, toggleShowEvent] = useState(false);
    const downloadPicture = () => {
        downloadImageByUrl(url);
    };

    const previewUrl = useMemo(() => {
        if (url?.endsWith?.('.gif') && bShow) {
            return `${url}${Math.random()}`;
        }
        return url;
    }, [url, bShow]);

    return (
        <>
            <div className={isBar ? styles.thumbnail_bar : styles.thumbnail} key="info">
                <div className={styles['thumbnail-utils']}>
                    <div className={styles['utils-bar']}>
                        <Space>
                            <EyeOutlined
                                className={classnames(styles.eye)}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleShowEvent(true);
                                }}
                            />
                            {!disabled ? (
                                <DeleteOutlined
                                    className={classnames(styles.eye)}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        delEvent();
                                    }}
                                />
                            ) : null}
                            {/* <DownloadOutlined
                            onClick={() => {
                                downloadPicture();
                            }}
                        /> */}
                        </Space>
                    </div>
                </div>

                <img style={{ maxWidth: '100%', maxHeight: '100%' }} src={url} alt="thumbnail" />
            </div>
            <Modal
                visible={bShow}
                key="modal"
                onCancel={(e) => {
                    e.stopPropagation();
                    toggleShowEvent(false);
                }}
                onOk={(e) => {
                    e.stopPropagation();
                    toggleShowEvent(false);
                }}
                width="70%"
                footer={false}
                maskClosable={false}
            >
                <div
                    style={{ textAlign: 'center' }}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                >
                    <img style={{ maxWidth: '100%', backgroundColor: 'black' }} src={previewUrl} />
                </div>
            </Modal>
        </>
    );
};
export default ThumbnailView;
