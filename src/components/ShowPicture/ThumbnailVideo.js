import { Fragment, useState } from 'react';
import { Button, Modal, Space } from 'antd';
import styles from './Thumbnail.less';
import {
    PlayCircleOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    DownloadOutlined,
    DeleteOutlined,
} from '@ant-design/icons';
import classnames from 'classnames';
import { downloadImageByUrl } from '@/utils/utils';
import { useMemo } from 'react';

const ThumbnailView = (props) => {
    const { url, width, delEvent, disabled, isBar } = props;

    const downloadPicture = () => {
        downloadImageByUrl(url);
    };

    return (
        <>
            <div className={isBar ? styles.thumbnail_bar : styles.thumbnail} key="info">
                <div className={styles['thumbnail-utils']}>
                    <div className={styles['utils-bar']}>
                        <Space>
                            <PlayCircleOutlined
                                className={classnames(styles.eye)}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    window.open(url);
                                }}
                            />
                            {!disabled ? (
                                <DeleteOutlined
                                    className={classnames(styles.eye)}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        delEvent();
                                    }}
                                />
                            ) : null}
                            {/* <DownloadOutlined
                            onClick={() => {
                                downloadPicture();
                            }}
                        /> */}
                        </Space>
                    </div>
                </div>

                <video width="100" height="100" alt="thumbnail">
                    <source src={url} type="video/mp4" />
                </video>
            </div>
        </>
    );
};
export default ThumbnailView;
