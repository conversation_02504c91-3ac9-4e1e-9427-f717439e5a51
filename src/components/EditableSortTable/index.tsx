import { EditableProTable } from '@ant-design/pro-components';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { MenuOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';
import { copyObjectCommon } from '@/utils/utils';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
    'data-row-key': string;
}

const TableRow = ({ children, ...props }: RowProps) => {
    const id = props['data-row-key'];
    const {
        isDragging,
        listeners,
        setNodeRef,
        setActivatorNodeRef,
        transform,
        transition,
        attributes,
    } = useSortable({ id });
    const rowStyle = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };
    return (
        <tr {...props} ref={setNodeRef} style={rowStyle} {...attributes}>
            {React.Children.map(children, (child) => {
                if ((child as React.ReactElement).key === 'sort') {
                    return React.cloneElement(child as React.ReactElement, {
                        children: (
                            <MenuOutlined
                                ref={setActivatorNodeRef}
                                style={{ touchAction: 'none', cursor: 'move' }}
                                {...listeners}
                            />
                        ),
                    });
                }
                return child;
            })}
        </tr>
    );
};

const EditableSortTable = (props: any) => {
    const { columns, value, onChange } = props;

    const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

    useEffect(() => {
        if (value?.length > 0) {
            console.log(321321, value);
            setEditableRowKeys(value.map((item: any) => item.id));
        }
    }, [value]);
    const handleDragEnd = (event: any) => {
        const { active, over } = event;
        if (over && active.id !== over.id) {
            const newTable: any[] = copyObjectCommon(value || []);
            const oldIndex = newTable.findIndex((item: any) => item.id === active.id);
            const newIndex = newTable.findIndex((item: any) => item.id === over.id);
            const newData = arrayMove(newTable, oldIndex, newIndex);
            updateFormItem(newData);
        }
    };
    const updateFormItem = (newTabel: any) => {
        onChange && onChange(newTabel);
    };
    return (
        <>
            <DndContext
                sensors={useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor))}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
            >
                <SortableContext
                    items={value?.map((item: any) => item.id) || []}
                    strategy={verticalListSortingStrategy}
                >
                    <EditableProTable
                        {...props}
                        columns={columns}
                        rowKey="id"
                        scroll={{
                            x: 960,
                        }}
                        value={value}
                        onChange={updateFormItem}
                        toolBarRender={null}
                        editable={{
                            type: 'multiple',
                            editableKeys,
                            actionRender: (row, config, defaultDoms) => {
                                return [defaultDoms.delete];
                            },
                            onValuesChange: (record, recordList) => {
                                updateFormItem(recordList);
                            },
                            onChange: setEditableRowKeys,
                        }}
                        components={{
                            body: {
                                row: TableRow,
                            },
                        }}
                    />
                </SortableContext>
            </DndContext>
        </>
    );
};
export default EditableSortTable;
