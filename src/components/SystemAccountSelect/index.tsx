import React, { useState } from 'react';
import { Form, Select, Spin } from 'antd';
import { debounce } from 'lodash';
import { getUserListByName } from '@/services/loginApi';

const SystemAccountSelect: React.FC<{
    label?: string;
    fieldName?: string;
    required?: boolean;
    rules?: any;
    formItemLayout?: any;
    style?: React.CSSProperties;
    keyName?: string;
}> = ({
    label = '创建人',
    fieldName = 'accountNo',
    required = false,
    rules,
    formItemLayout,
    style,
    keyName = 'accountName',
}) => {
    const [fetching, changeFetching] = useState<boolean>(false);
    const [accountList, changeAccountList] = useState<SSOAPI.SystemAccount[]>([]);

    const serchAccount = async (name: string) => {
        try {
            if (!name) {
                return;
            }
            changeAccountList([]);
            changeFetching(true);
            const response = (await getUserListByName(name)) as SSOAPI.GetUserListByNameResponse;
            const list = response?.data || [];
            changeAccountList(list);
        } catch (error) {
            changeFetching(false);
        } finally {
            changeFetching(false);
        }
    };
    const fetchStation = debounce(serchAccount, 800);

    return (
        <Form.Item
            label={label}
            name={fieldName}
            required={required}
            rules={rules}
            style={style}
            {...formItemLayout}
        >
            <Select
                showSearch
                placeholder="请选择"
                loading={fetching}
                notFoundContent={fetching ? <Spin size="small" /> : null}
                filterOption={false}
                onSearch={fetchStation}
                onFocus={() => {
                    fetchStation('');
                }}
                allowClear
            >
                {accountList.map((d: SSOAPI.SystemAccount) => (
                    <Select.Option key={d?.[keyName]} value={d?.[keyName]}>
                        {d?.displayName ? `${d.displayName}（${d?.accountName}）` : d?.accountName}
                    </Select.Option>
                ))}
            </Select>
        </Form.Item>
    );
};

export default React.memo(SystemAccountSelect);
