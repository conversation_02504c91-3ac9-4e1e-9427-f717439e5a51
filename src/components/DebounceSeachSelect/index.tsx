import React, { useState } from 'react';
import { Form, Select, Spin } from 'antd';
import { debounce } from 'lodash';

const DebounceSeachSelect: React.FC<{
    value?: string;
    onChange?: (newVlaue: string) => void;
    labelName?: string;
    labelKey?: string;
    valueKey?: string;
    fetchApi: (options: any) => any;
}> = ({
    value = '',
    onChange,
    labelName = '申请部门',
    labelKey = 'codeName',
    valueKey = 'codeValue',
    fetchApi,
}) => {
    const [fetching, changeFetching] = useState<boolean>(false);
    const [selectList, changeSelectList] = useState<any[]>([]);

    const serchAccount = async (name: string) => {
        try {
            if (!name) {
                return;
            }
            changeSelectList([]);
            changeFetching(true);
            const response = await fetchApi(name);
            const list = response?.data || [];
            changeSelectList(list);
        } catch (error) {
            changeFetching(false);
        } finally {
            changeFetching(false);
        }
    };
    const fetchStation = debounce(serchAccount, 800);

    return (
        <Select
            showSearch
            placeholder="请选择"
            loading={fetching}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            filterOption={false}
            onSearch={fetchStation}
            onFocus={() => {
                fetchStation('');
            }}
            allowClear
            onChange={onChange}
        >
            {selectList.map((d: any) => (
                <Select.Option key={d?.[labelKey]} value={d?.[valueKey]}>
                    {d?.[labelKey]}
                </Select.Option>
            ))}
        </Select>
    );
};

export default React.memo(DebounceSeachSelect);
