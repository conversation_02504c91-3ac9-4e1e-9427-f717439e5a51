import { Button, Space, Table } from 'antd';
import React, { useEffect, useState } from 'react';

import CrowdSelectModal from './CDPCrowdSelectModal';

const CDPCrowdSelect: React.FC<{
    applyType?: string;
    value?: API.CdpCrowdDefineQueryVo[];
    onChange?: (value: API.CdpCrowdDefineQueryVo[]) => void;
    disabled?: boolean;
}> = (props) => {
    const { value, onChange, disabled, applyType = '' } = props;

    const [showSelectModal, updateShowSelectModal] = useState<boolean>(false);
    const [crowdList, setCrowdList] = useState<API.CdpCrowdDefineQueryVo[]>([]);

    useEffect(() => {
        setCrowdList(value ?? []);
    }, [value]);
    const closeSelectModal = () => {
        updateShowSelectModal(false);
    };

    const finishSelectCrowdEvent = (selectList: API.CdpCrowdDefineQueryVo[]) => {
        onChange && onChange(selectList);
        closeSelectModal();
    };
    const columns = [
        {
            title: '人群ID',
            dataIndex: 'crowdDefineId',
        },
        {
            title: '人群名称',
            dataIndex: 'cdpCrowdName',
        },
        {
            title: '标签值',
            dataIndex: 'cdpCrowdId',
        },
    ];

    return (
        <Space direction="vertical">
            <Button
                type="primary"
                onClick={() => {
                    updateShowSelectModal(true);
                }}
            >
                编辑
            </Button>
            <Table
                scroll={{ x: 'max-content', y: 300 }}
                columns={columns}
                rowKey="crowdDefineId"
                dataSource={crowdList}
                pagination={false}
            ></Table>
            <CrowdSelectModal
                applyType={applyType}
                defaultSelects={crowdList}
                visible={showSelectModal}
                onClose={closeSelectModal}
                onFinish={finishSelectCrowdEvent}
            ></CrowdSelectModal>
        </Space>
    );
};

export default CDPCrowdSelect;
