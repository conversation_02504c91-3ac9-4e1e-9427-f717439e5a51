import { Button, Col, Form, Input, Space } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';

import usePageState from '@/hooks/usePageState';
import SearchOptionsBar from '../SearchOptionsBar';
import TablePro from '../TablePro';
import { getCrowdDefineQueryPage } from '@/services/MngBilApi';

const CdpCrowdSelectView: React.FC<{
    applyType?: string;
    onFinish: (list: API.CdpCrowdDefineQueryVo[]) => void;
    onClose: () => void;
    disabledIds?: string[];
    isMultiple?: boolean;
    defaultSelects?: API.CdpCrowdDefineQueryVo[];
}> = (props: any) => {
    const {
        applyType = '',
        onFinish,
        onClose,
        disabledIds = [],
        isMultiple = true,
        defaultSelects = [],
    } = props;
    const [listLoading, updateListLoading] = useState(false);
    const [tableList, updateTableList] = useState<API.CdpCrowdDefineQueryVo[]>([]);
    const [tableListTotal, updateTableListTotal] = useState<number>(0);
    const [selectList, updateSelectList] = useState<API.CdpCrowdDefineQueryVo[]>([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, { global: {} });
    const [serchForm] = Form.useForm();

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (defaultSelects instanceof Array) {
            updateSelectList(defaultSelects as any);
        }
    }, [defaultSelects]);

    const searchData = async () => {
        try {
            const data = serchForm.getFieldsValue(true);
            const params: API.GetCrowdDefineQueryPageRequest = {
                applyType,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                crowdNameOrId: data.searchName,
            };

            updateListLoading(true);
            const response = await getCrowdDefineQueryPage(params);
            const records = response?.data?.records ?? [];
            const total = response?.data?.total ?? 0;
            updateTableList(records);
            updateTableListTotal(total);
            return records;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateListLoading(false);
        }
    };

    const resetData = () => {
        serchForm.resetFields();
        changePageInfo((state: any) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const searchEvent = () => {
        searchData();
    };

    const columns = [
        {
            title: '人群ID',
            dataIndex: 'crowdDefineId',
        },
        {
            title: '人群名称',
            dataIndex: 'cdpCrowdName',
        },
        {
            title: '标签值',
            dataIndex: 'cdpCrowdId',
        },
    ];

    const rowSelection = {
        type: 'checkbox',
        checkStrictly: false,
        selectedRowKeys: selectList.map((item: API.CdpCrowdDefineQueryVo) => item.cdpCrowdId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            if (!isMultiple) {
                // 替换只能单选，所以不用考虑之前是否已经选过其他优惠券的逻辑
                updateSelectList([...selectedRows] as any);
            } else {
                const tableRoundList: any = [];
                for (const item of tableList) {
                    tableRoundList.push(item);
                }
                // 筛选出非当前页的勾选项，不予处理
                const otherCpns = selectList.filter(
                    (x: any) =>
                        tableRoundList.filter((now: any) => now.cdpCrowdId == x.cdpCrowdId)
                            .length == 0,
                );
                updateSelectList([...otherCpns, ...selectedRows] as any);
            }
        },
        getCheckboxProps: (record: API.CdpCrowdDefineQueryVo) => ({
            disabled: disabledIds?.indexOf(record.cdpCrowdId) >= 0,
            name: record.cdpCrowdId,
        }),
    };
    return (
        <Fragment>
            <Form
                name="prize-search-select"
                form={serchForm}
                onFinish={searchEvent}
                scrollToFirstError
            >
                <SearchOptionsBar loading={listLoading} onReset={resetData} minSpan={40}>
                    <Col span={6}>
                        <Form.Item label="人群名称" name="searchName">
                            <Input
                                placeholder="请填写"
                                autoComplete="off"
                                allowClear
                                maxLength={100}
                            />
                        </Form.Item>
                    </Col>
                </SearchOptionsBar>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content', y: 450 }}
                rowKey="cdpCrowdId"
                dataSource={tableList}
                columns={columns}
                onChange={onTableChange}
                rowSelection={rowSelection}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: tableListTotal,
                    pageSize: pageInfo.pageSize,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
                sticky={{ offsetHeader: 0 }}
                filterHeader={false}
                expandable={{
                    defaultExpandAllRows: true,
                }}
            />
            <Space
                align="end"
                className="mg-t-20"
                style={{ display: 'flex', justifyContent: 'center' }}
            >
                <Button
                    type="primary"
                    onClick={() => {
                        onFinish?.(selectList);
                    }}
                >
                    提交
                </Button>
                <Button onClick={onClose}>取消</Button>
            </Space>
        </Fragment>
    );
};

export default CdpCrowdSelectView;
