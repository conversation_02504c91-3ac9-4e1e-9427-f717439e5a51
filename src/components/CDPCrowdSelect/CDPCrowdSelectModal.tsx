import { Modal } from 'antd';
import React from 'react';

import CdpCrowdSelectView from './CDPCrowdSelectView';

const CDPCrowdSelectModal: React.FC<{
    applyType?: string;
    visible: boolean;
    onClose: () => void;
    onFinish: (list: API.CdpCrowdDefineQueryVo[]) => void;
    defaultSelects?: API.CdpCrowdDefineQueryVo[];
}> = (props) => {
    const { applyType = '', visible, onClose, onFinish, defaultSelects } = props;

    const closeSelectModal = () => {
        onClose && onClose();
    };
    return (
        <Modal
            title="编辑人群"
            width={1000}
            visible={visible}
            onCancel={closeSelectModal}
            footer={null}
            maskClosable={false}
            destroyOnClose
        >
            <CdpCrowdSelectView
                applyType={applyType}
                defaultSelects={defaultSelects}
                onFinish={onFinish}
                onClose={closeSelectModal}
            />
        </Modal>
    );
};

export default CDPCrowdSelectModal;
