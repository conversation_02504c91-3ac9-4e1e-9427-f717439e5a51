/**
 * 根据运营商查询对应城市下的站点
 */

import { Fragment, useEffect, useState, useMemo, useImperativeHandle } from 'react';
import { Form, Select, Button, Row, Col, Spin } from 'antd';
import { connect } from 'dva';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import { getCityAndStationByOperIdApi } from '@/services/CommonApi';

const FormItem = Form.Item;
const { Option } = Select;

const StationListByCityForOper = (props) => {
    const {
        operId,
        form,
        dispatch,
        formItemLayout,
        initRef,
        label = '活动区域',
        name = 'areaList',
        disabled,
        // stationsListInfo,
        rules,
        currentUser,
    } = props;

    const [allList, updateAllList] = useState([]);
    const [loading, toggleLoading] = useState(false);

    const formData = form.getFieldValue(name);

    useEffect(() => {
        if (operId) {
            initEvent(operId);
        }
    }, [operId]);

    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        init: async (id) => {
            try {
                await initEvent(id);
            } catch (error) {}
        },
    }));

    const initEvent = async (operId) => {
        try {
            form.setFieldsValue({ [name]: [] });
            toggleLoading(true);
            const { data: cityAndStationList } = await getCityAndStationByOperIdApi({ operId });

            updateAllList(cityAndStationList);
        } catch (error) {
            console.log(555, error);
        } finally {
            toggleLoading(false);
        }
    };

    const cityOptions = useMemo(() => {
        const cityCodes = (formData && formData.map((ele) => ele && ele.city)) || [];
        const cityList = [];
        for (const ele of allList) {
            cityList.push({
                title: ele.title,
                value: ele.value,
            });
        }
        return cityList.map((ele) => {
            return (
                <Option disabled={cityCodes.includes(ele.value)} key={ele.value} value={ele.value}>
                    {ele.title}
                </Option>
            );
        });
    }, [allList, formData]);

    const changeCityEvent = async (value, index) => {
        // try {
        //     const list = await initCityAndStationEvent(value);
        //     changeStationList(oldList => {
        //         oldList[index] = list;
        //         return oldList;
        //     });
        //     return list;
        // } catch (error) {
        //     console.log(4444, error);
        // } finally {
        // }

        try {
            // 清空选中站点
            if (currentUser.operId != value) {
                const formItemData = form.getFieldValue(name);
                formItemData[index].stationId = [];
                form.setFieldsValue({
                    [name]: formItemData,
                });
            }
        } catch (error) {
            console.log(4444, error);
        } finally {
        }
    };

    const canIadd = () => {
        let hasAdd = true;
        const id = form.getFieldValue('operId');
        if (!id) {
            hasAdd = false;
        }
        if (!allList || allList.length == 0) {
            hasAdd = false;
        }
        return !disabled && hasAdd;
    };

    return (
        <Form.Item name={name} {...formItemLayout} label={label} rules={rules}>
            <Form.List name={name}>
                {(fields, { add, remove }) => (
                    <Fragment>
                        {fields.map((field, index) => (
                            <Fragment key={index}>
                                <Row>
                                    <Col flex="200px">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                label=""
                                                fieldKey={field.fieldKey}
                                                name={[field.name, 'city']}
                                                validateTrigger={['onChange', 'onBlur']}
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: '请选择城市',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    onChange={(value) => {
                                                        changeCityEvent(value, index);
                                                    }}
                                                    disabled={disabled}
                                                >
                                                    {cityOptions}
                                                </Select>
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    <Col flex="1">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) => true}
                                            >
                                                {({ getFieldValue }) => {
                                                    const formItemData = getFieldValue(name);
                                                    const cityCode =
                                                        formItemData &&
                                                        formItemData[index] &&
                                                        formItemData[index].city;
                                                    let stationList = [];
                                                    if (cityCode) {
                                                        const item = allList.find((ele) => {
                                                            return ele.value == cityCode;
                                                        });
                                                        if (
                                                            item &&
                                                            item.children &&
                                                            item.children instanceof Array
                                                        ) {
                                                            stationList = item.children;
                                                        }
                                                    }

                                                    const stationOptions = stationList.map(
                                                        (ele) => {
                                                            return (
                                                                <Option
                                                                    value={ele.value}
                                                                    key={ele.value}
                                                                >
                                                                    {ele.title}
                                                                </Option>
                                                            );
                                                        },
                                                    );
                                                    return (
                                                        <FormItem
                                                            style={{ margin: '0 8px' }}
                                                            fieldKey={field.fieldKey}
                                                            name={[field.name, 'stationId']}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            // rules={[
                                                            //     {
                                                            //         required: true,
                                                            //         message: '请选择场站',
                                                            //     },
                                                            //     ({ getFieldValue }) => ({
                                                            //         validator(rule, value) {
                                                            //             return Promise.resolve();
                                                            //         },
                                                            //     }),
                                                            // ]}
                                                        >
                                                            <Select
                                                                disabled={disabled}
                                                                mode="multiple"
                                                                placeholder={
                                                                    !cityCode
                                                                        ? '请先选择城市'
                                                                        : '全部站点'
                                                                }
                                                                allowClear
                                                                initialValue={[]}
                                                            >
                                                                {stationOptions}
                                                            </Select>
                                                        </FormItem>
                                                    );
                                                }}
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    {disabled ? null : (
                                        <Col>
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{ margin: '0 8px' }}
                                                onClick={() => {
                                                    remove(field.name);
                                                }}
                                            />
                                        </Col>
                                    )}
                                </Row>
                            </Fragment>
                        ))}
                        <FormItem label="">
                            <Button
                                loading={loading}
                                disabled={!canIadd()}
                                type="dashed"
                                onClick={() => {
                                    add();
                                }}
                                style={{ width: '200px' }}
                            >
                                <PlusOutlined />
                                添加城市
                            </Button>
                        </FormItem>
                    </Fragment>
                )}
            </Form.List>
        </Form.Item>
    );
};
export default connect(({ user, loading }) => ({
    currentUser: user.currentUser,
}))(StationListByCityForOper);
