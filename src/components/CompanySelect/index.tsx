import { Select } from 'antd';
import { useEffect, useState } from 'react';
import { connect } from 'umi';

import { getCompanyDropdownlistApi } from '@/services/Enterprise/StaffApi';

const CompanySelect = (props: any) => {
    const {
        accountType,
        dispatch,
        staffModel: { enterpriseDropDownList },
        listLoading,
        disabled,
        value,
        onChange,
        onChangeOption,
    } = props;

    const [companyList, updateCompanyList] = useState([]);

    const initCompany = async (accountType: string | number) => {
        try {
            const {
                data: { companyList },
            } = await getCompanyDropdownlistApi({ accountType });
            if (companyList && companyList?.length === 1) {
                onChange && onChange(companyList[0]?.companyId);
                onChangeOption && onChangeOption(companyList[0]);
            }
            updateCompanyList(companyList);
        } catch (error) {}
    };

    useEffect(() => {
        if (accountType) {
            initCompany(accountType);
        } else {
            dispatch({
                type: 'staffModel/getEnterpriseDropDownList',
                companyNickname: '',
            });
        }
    }, [accountType]);

    return (
        <Select
            allowClear
            fieldNames={{
                label: 'companyNickname',
                value: 'companyId',
            }}
            loading={listLoading}
            onChange={(value: string, option) => {
                onChange && onChange(value);
                onChangeOption && onChangeOption(option);
            }}
            options={accountType ? companyList : enterpriseDropDownList}
            optionFilterProp="companyNickname"
            placeholder="请选择"
            disabled={disabled}
            showArrow
            showSearch
            value={value}
        />
    );
};

export default connect(({ staffModel, loading }: any) => ({
    staffModel,
    listLoading: loading.effects['staffModel/getEnterpriseDropDownList'],
}))(CompanySelect);
