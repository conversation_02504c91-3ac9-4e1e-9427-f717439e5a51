import { Link } from 'umi';
import { Fragment, useMemo, useState, useEffect } from 'react';
import { Form, Col, Input, DatePicker, Tabs } from 'antd';
import usePageState from '@/hooks/usePageState.js';

import SearchOptionsBar from '@/components/SearchOptionsBar';
import TablePro from '@/components/TablePro';
import { STATUS_TYPES } from '@/config/declare';
import { formatActivePage } from '@/utils/utils';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const SearchLayout = (props) => {
    const { form, onSubmit, onReset, onExportForm, tableLoading } = props;

    const onFinish = (values) => {
        onSubmit(values);
    };

    const resetForm = () => {
        form.resetFields();
        // 列表数据查询
        onReset();
    };

    return (
        <Form form={form} onFinish={onFinish} scrollToFirstError>
            <SearchOptionsBar
                loading={tableLoading}
                onReset={resetForm}
                onExportForm={onExportForm}
            >
                <Col flex={'0 1 360px'}>
                    <FormItem label="活动名称" name="actName">
                        <Input autoComplete="off" />
                    </FormItem>
                </Col>
                <Col flex={'0 1 360px'}>
                    <FormItem label="活动时间:" name="dates">
                        <RangePicker format="YYYY-MM-DD" />
                    </FormItem>
                </Col>
            </SearchOptionsBar>
        </Form>
    );
};
const DetailLinkActPage = (props) => {
    const {
        tabList = [], //tab配置列表
        otherParams = {}, //列表接口额外参数
        columnsExpand = [],
        listLoading,
        listApi, //列表查询接口
        exportApi, //列表导出接口
    } = props;
    const [searchForm] = Form.useForm();
    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        {
            tabType: tabList?.[0]?.value,
        },
        props,
    );
    const [linkActList, updateLinkActList] = useState([]);
    const [linkActListTotal, updateLinkActListTotal] = useState(0);

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    // 调用搜索接口
    const searchData = async () => {
        try {
            const data = searchForm.getFieldsValue();
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                actName: data.actName?.trim(),
                effTime:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD 00:00:00')) ||
                    undefined,
                expTime:
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD 23:59:59')) ||
                    undefined,
                dates: undefined,
                ...otherParams,
            };

            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.actState = pageInfo.tabType;
            }

            if (listApi) {
                const {
                    data: { records, total },
                } = await listApi(params);
                updateLinkActList(records);
                updateLinkActListTotal(total);
            }
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    //导出
    const exportFormEvent = () => {
        try {
            const data = searchForm.getFieldsValue();
            const params = {
                actName: data.actName?.trim(),
                effTime:
                    (data.dates && data.dates[0] && data.dates[0].format('YYYY-MM-DD 00:00:00')) ||
                    undefined,
                expTime:
                    (data.dates && data.dates[1] && data.dates[1].format('YYYY-MM-DD 23:59:59')) ||
                    undefined,
                dates: undefined,
                ...otherParams,
            };

            if (pageInfo.tabType !== STATUS_TYPES.ALL) {
                params.actState = pageInfo.tabType;
            }
            exportApi && exportApi(params);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        changePageInfo({ pageIndex: 1 });
    };

    const changeTabTypeEvent = (type) => {
        changePageInfo({ tabType: type, pageIndex: 1 });
    };

    const columns = [
        {
            title: '活动名称',
            width: 140,
            dataIndex: 'actName',
            render(text, record) {
                let path = formatActivePage({ type: String(record.actType), actId: record.actId });
                if (path) {
                    return (
                        <Link to={path} target="_blank">
                            {text}
                        </Link>
                    );
                }
                return <span title={text}> {text || '-'}</span>;
            },
        },
        ...columnsExpand,
        {
            title: '活动状态',
            width: 200,
            dataIndex: 'actStateName',
            render(text) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '活动时间',
            width: 200,
            dataIndex: 'effTime',
            render(text, record) {
                const timeResult = `${record.effTime || ''}-${record.expTime || ''}`;
                return <span title={timeResult}>{timeResult}</span>;
            },
        },
    ];

    const tabOptions = useMemo(() => {
        if (tabList instanceof Array) {
            return tabList.map((ele) => <TabPane tab={ele.label} key={ele.value}></TabPane>);
        }
        return [];
    }, [tabList]);
    return (
        <Fragment>
            <SearchLayout
                form={searchForm}
                {...props}
                onSubmit={() => {
                    changePageInfo((state) => ({
                        ...state,
                        pageIndex: 1,
                    }));
                }}
                onReset={resetData}
                tableLoading={listLoading}
                onExportForm={exportFormEvent}
            />
            <Tabs defaultActiveKey={pageInfo.tabType} onChange={changeTabTypeEvent}>
                {tabOptions}
            </Tabs>

            <TablePro
                name="linklist"
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey="actId"
                offsetHeader={0}
                dataSource={linkActList}
                columns={columns}
                onChange={onTableChange}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: linkActListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
            />
        </Fragment>
    );
};
export default DetailLinkActPage;
