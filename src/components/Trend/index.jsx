import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import React from 'react';
import classNames from 'classnames';
import styles from './index.less';

const Trend = ({ colorful = true, reverseColor = false, flag, children, className, ...rest }) => {
    const classString = classNames(
        styles.trendItem,
        {
            [styles.trendItemGrey]: !colorful,
            [styles.reverseColor]: reverseColor && colorful,
        },
        className,
    );
    return (
        <div {...rest} className={classString} title={typeof children === 'string' ? children : ''}>
            {flag && (
                <span className={styles[flag]}>
                    {flag === 'up' ? <CaretUpOutlined /> : <CaretDownOutlined />}
                </span>
            )}
            <span>{children}</span>
        </div>
    );
};

export default Trend;
