import React, { useState, useImperativeHandle, useEffect } from 'react';
import { Space, Button, Card } from 'antd';

import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import commonStyles from '@/assets/styles/common.less';
import selfStyles from './index.less';

// 样式预览
const LayoutPreview = (props) => {
    const {
        title = '样式预览',
        layouts, // 预览样式数组，接收可被展示的doms
        initRef,
        style = {}, // 如果外部传参了，以外部布局为准
    } = props;
    const [step, updateStep] = useState(1);
    const [showFlag, updateShowFlag] = useState(true);
    const togglePage = (page) => {
        if (step != page) {
            updateStep(page);
        }
    };
    useImperativeHandle(initRef, () => ({
        togglePage, // 外部主动切换到指定页
    }));
    const changeShowFlag = () => {
        updateShowFlag(!showFlag);
    };
    useEffect(() => {
        // 监听doms如果超过step边界的话，要及时纠正
        if (layouts?.length && step > layouts.length) {
            updateStep(layouts.length);
        }
    }, [layouts]);
    return (
        <div
            style={{
                zoom: '0.8',
                position: 'fixed',
                top: '90px',
                right: `70px`,
                zIndex: '999',
                ...style,
            }}
        >
            {(layouts?.length && (
                <div className={!showFlag && selfStyles.cardBody}>
                    <Card
                        title={
                            <div
                                className={commonStyles['form-title']}
                                style={{ marginBottom: '0', cursor: 'pointer' }}
                                onClick={changeShowFlag}
                            >
                                <Space>
                                    <span>{title}</span>
                                    {showFlag ? <CaretUpOutlined /> : <CaretDownOutlined />}
                                </Space>
                            </div>
                        }
                        extra={
                            showFlag &&
                            layouts?.length >= 2 && [
                                step != layouts.length && (
                                    <Button
                                        onClick={() => {
                                            updateStep(step + 1);
                                        }}
                                        type="text"
                                        key="next"
                                        size="small"
                                    >
                                        下一页
                                    </Button>
                                ),
                                step != 1 && (
                                    <Button
                                        onClick={() => {
                                            updateStep(step - 1);
                                        }}
                                        type="text"
                                        key="pre"
                                        size="small"
                                    >
                                        上一页
                                    </Button>
                                ),
                            ]
                        }
                    >
                        <div
                            style={{
                                maxHeight: `${document.body.clientHeight - 270}px`,
                                overflowY: 'auto',
                                display: showFlag ? 'block' : 'none',
                            }}
                        >
                            {layouts?.[step - 1] || null}
                        </div>
                    </Card>
                </div>
            )) ||
                null}
        </div>
    );
};

export default LayoutPreview;
