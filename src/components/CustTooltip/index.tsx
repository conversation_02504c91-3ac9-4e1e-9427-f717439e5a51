import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
const CustTooltip = (
    props: {
        title?: React.ReactNode;
        hidden?: boolean;
    },
    ref: any,
) => {
    const { title, hidden = false } = props;
    const [showTips, toggleShowTips] = useState(false);
    useImperativeHandle(ref, () => {
        return {
            open() {
                openTipsEvent();
            },
            close() {
                closeTipsEvent();
            },
        };
    });
    const openTipsEvent = () => {
        toggleShowTips(true);
    };
    const closeTipsEvent = () => {
        toggleShowTips(false);
    };
    return (
        !hidden && (
            <Tooltip
                visible={showTips}
                title={
                    <span
                        onClick={() => {
                            closeTipsEvent();
                        }}
                    >
                        {title}
                    </span>
                }
            >
                <InfoCircleOutlined
                    onMouseEnter={() => {
                        openTipsEvent();
                    }}
                    onMouseLeave={() => {
                        closeTipsEvent();
                    }}
                    onFocus={() => {
                        openTipsEvent();
                    }}
                />
            </Tooltip>
        )
    );
};

export default forwardRef(CustTooltip);
