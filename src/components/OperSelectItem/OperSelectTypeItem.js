import { useEffect, useMemo, useState } from 'react';
import { Form, Select, Row, Col, Popover } from 'antd';
import { connect } from 'dva';
import { SELECT_TYPES } from '@/config/declare';

import { getCompanyOperListApi } from '@/services/Enterprise/EnterpriseManageApi';
import { isEmpty } from '@/utils/utils';

const FormItem = Form.Item;
const { Option } = Select;

const FILTER_TYPES = {
    FULL_NAME: '01', // 按全称查询
    SHOTR_NAME: '02', // 按简称查询
    CONTRACT_NO: '03', // 按合同号查询
};

const OperSelectTypeItem = (props) => {
    const {
        // 私有属性
        dispatch,
        global: { operatorList2 },
        currentUser,
        // 外部配置属性
        // 必传属性
        formItemLayout,
        form,
        // 可选属性
        extraOpers = [], // 额外的可选运营商，如果配置了，固定放在最前面
        disabledOperIds, // 部分运营商禁止选择，根据id判断
        disabled, // 整个控件禁用
        disabledFilter, // 类型筛选禁用，配置后查询全称类型
        purchase = SELECT_TYPES.ALL,
        isCompany, // 是否获取企业列表，会包含平台数据源
        formParams = {}, //查询接口额外入参
        ...rest
    } = props;

    // FormItem属性
    const {
        name = 'operId',
        rules,
        label = '运营商',
        validateStatus,
        fieldKey,
        validateTrigger,
        help,
    } = rest || {};

    const { wrapperCol, labelCol, labelAlign = 'left' } = formItemLayout || {};

    const [companyList, updateCompanyList] = useState([]);
    // Select属性
    const {
        placeholder = '支持全称、简称、合同号模糊搜索',
        onChange,
        onChangeOption,
        onClear,
        mode,
        maxTagCount,
        showArrow,
    } = rest || {};

    const [nowOperators, changeNowOperators] = useState([]);
    const defaultValue = Object.keys(rest).includes('initialValue')
        ? rest.initialValue
        : currentUser.operId || undefined;

    useEffect(() => {
        return () => {
            // if (!isEmpty(formParams)) {
            //     dispatch({
            //         type: 'global/getOperatorList2',
            //         options: {},
            //     });
            // }
        };
    }, []);

    useEffect(() => {
        updateCompanyList([...operatorList2]);
    }, [operatorList2]);

    const formatOperator = useMemo(() => {
        let opers = [];
        if (purchase == SELECT_TYPES.ONLYBUY) {
            // 仅支持选择购电模式
            opers = companyList.filter((ele) => {
                return ele.cooperationType === '02';
            });
        } else if (purchase == SELECT_TYPES.EXCEPTBUY) {
            // 不允许选择购电模式
            opers = companyList.filter((ele) => {
                return ele.cooperationType !== '02';
            });
        } else {
            opers = companyList;
        }
        const resultArr = [...extraOpers, ...opers];
        return resultArr;
    }, [companyList, purchase, disabledOperIds]);

    useEffect(() => {
        changeNowOperators([...formatOperator]);
    }, [formatOperator]);

    useEffect(() => {
        if (currentUser) {
            initOper();
        }
    }, [currentUser, isCompany]);

    const initOper = async () => {
        if (currentUser.operId) {
            // 运营商登录
            form?.setFieldsValue({ [name]: currentUser.operId });
        }
        const params = {
            filterAbandon: false, //不过滤废弃站点
            ...formParams,
        };
        if (isCompany) {
            params.operId = currentUser.operId;
            const {
                data: { companyOperList = [] },
            } = await getCompanyOperListApi();
            updateCompanyList([...companyOperList]);
        } else if (operatorList2.length == 0) {
            // 平台账号登录
            dispatch({
                type: 'global/getOperatorList2',
                options: params,
            });
        }
    };

    const filterKeys = ['operName', 'operNickname', 'operProjectCode', 'operId'];

    const handleSearch = (txt) => {
        txt = txt?.replace?.(/(^\s+)|(\s+$)/g, '');
        if (txt?.length) {
            const list = [];
            for (const item of formatOperator) {
                let matched = false;
                for (const key of filterKeys) {
                    if (item[key] && item[key].toLowerCase().indexOf(txt?.toLowerCase()) >= 0) {
                        matched = true;
                        break;
                    }
                }
                const keys = Object.keys(item);
                if (
                    matched ||
                    (isCompany &&
                        keys.find(
                            (ele) =>
                                ele !== 'companyId' &&
                                item?.[ele]?.toLowerCase().indexOf(txt?.toLowerCase()) >= 0,
                        ))
                ) {
                    list.push(item);
                }
            }
            changeNowOperators(list);
        } else {
            changeNowOperators(formatOperator);
        }
    };

    const handleFilter = (input, option) => true;

    return (
        <FormItem
            label={label}
            {...formItemLayout}
            wrapperCol={wrapperCol}
            labelCol={labelCol}
            labelAlign={labelAlign}
            name={name}
            rules={rules}
            help={help}
            validateStatus={validateStatus}
            initialValue={defaultValue}
            fieldKey={fieldKey}
            validateTrigger={validateTrigger}
        >
            <Select
                showSearch
                placeholder={placeholder}
                filterOption={handleFilter}
                onSearch={handleSearch}
                disabled={disabled || currentUser.operId}
                onChange={(value, option) => {
                    const operItem = nowOperators?.find((item) => item.operId == value);
                    handleSearch(''); // 已选中的情况下，清空筛选条件
                    onChange?.(value);
                    onChangeOption?.(operItem);
                }}
                mode={mode}
                onClear={onClear}
                allowClear
                maxTagCount={maxTagCount}
                showArrow={showArrow}
            >
                {nowOperators?.map((item, index) => {
                    if (disabledOperIds?.indexOf[item.operId] >= 0) {
                        return undefined;
                    }

                    return (
                        <Option value={item.operId} key={item.operId}>
                            {(item['operName'] ? 1 : 0) +
                                (item['operNickname'] ? 1 : 0) +
                                (item['operProjectCode'] ? 1 : 0) >
                            1 ? (
                                <Popover
                                    content={
                                        <div>
                                            {(item['operName'] && (
                                                <p>全称：{`${item['operName']}`}</p>
                                            )) ||
                                                null}
                                            {(item['operNickname'] && (
                                                <p>简称：{`${item['operNickname']}`}</p>
                                            )) ||
                                                null}
                                            {(item['operProjectCode'] && (
                                                <p>
                                                    合同号：
                                                    {`${item['operProjectCode']}`}
                                                </p>
                                            )) ||
                                                null}
                                        </div>
                                    }
                                    zIndex={1051}
                                    mouseEnterDelay={0.6}
                                >
                                    {item['operNickname']}
                                </Popover>
                            ) : (
                                item['operNickname']
                            )}
                        </Option>
                    );
                })}
            </Select>
        </FormItem>
    );
};

export default connect(({ user, global }) => ({
    global,
    currentUser: user.currentUser,
}))(OperSelectTypeItem);
