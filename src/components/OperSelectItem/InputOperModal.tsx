import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message, Space, Typography, Form, Radio, Input } from 'antd';
import { useRequest } from 'ahooks';

import { importOperInputAndFileApi } from '@/services/Marketing/ReadjustApi';

const IMPORT_TYPES = {
    NAMES: '01',
    IDS: '02',
};
const { TextArea } = Input;
const InputOperModal = (props: any, ref: any) => {
    const { onConfirm, zIndex } = props;
    const [visible, toggleVisible] = useState(false);

    const [form] = Form.useForm();

    const importType = Form.useWatch('importType', form);

    useImperativeHandle(ref, () => {
        return {
            open: (defaultList?: any[]) => {
                openModalEvent();
            },
            close: (defaultList?: any[]) => {
                closeModalEvent();
            },
        };
    });
    const openModalEvent = () => {
        toggleVisible(true);
    };
    const closeModalEvent = () => {
        toggleVisible(false);

        form?.resetFields();
    };
    const confirmEvent = (params: any) => {
        onConfirm && onConfirm(params);
        closeModalEvent();
    };

    const { run: submitEvent, loading } = useRequest(
        async () => {
            try {
                const values = await form.validateFields();
                const { importContent, importType } = values;

                const resultList = importContent.split(/[\r\n\s]+/).filter((ele: string) => ele);

                // const params = new FormData();
                // if (importType == IMPORT_TYPES.NAMES) {
                //     params.append('operNames', resultList.join(','));
                // } else if (importType == IMPORT_TYPES.IDS) {
                //     params.append('operIds', resultList.join(','));
                // }
                const params = {};
                if (importType == IMPORT_TYPES.NAMES) {
                    params.operNames = resultList.join(',');
                } else if (importType == IMPORT_TYPES.IDS) {
                    params.operIds = resultList.join(',');
                }

                // const { data } = await importOperInputAndFileApi(params);

                confirmEvent && (await confirmEvent(params));

                return;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true, throttleWait: 2000 },
    );

    return (
        <Modal
            title="文本导入"
            visible={visible}
            width={550}
            onCancel={closeModalEvent}
            destroyOnClose
            onOk={submitEvent}
            zIndex={zIndex}
            okButtonProps={{
                disabled: loading,
            }}
        >
            <Form form={form} name="InputOperModal">
                <Form.Item
                    label="导入方式"
                    name="importType"
                    initialValue={IMPORT_TYPES.NAMES}
                    rules={[
                        {
                            required: true,
                            message: '请选择导入方式',
                        },
                    ]}
                >
                    <Radio.Group>
                        <Radio value={IMPORT_TYPES.NAMES}>运营商名称</Radio>
                        <Radio value={IMPORT_TYPES.IDS}>运营商ID</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    label="导入文本"
                    name="importContent"
                    rules={[
                        {
                            required: true,
                            message: '请填写导入内容',
                        },
                    ]}
                >
                    <TextArea
                        placeholder={`请填写运营商${
                            importType == IMPORT_TYPES.IDS ? 'ID' : '名称'
                        },用换行隔开`}
                        autoComplete="off"
                        rows={6}
                    ></TextArea>
                </Form.Item>
            </Form>
        </Modal>
    );
};
export default forwardRef(InputOperModal);
