import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message } from 'antd';
import XdtProTable from '@/components/XdtProTable/index';
import { useRequest } from 'ahooks';
import { getOperByCityApi } from '@/services/MngAstApi';
import { isEmpty } from '@/utils/utils';
import { getOperatorAllListApi } from '@/services/OperationMng/OperationMngApi';

export const operColumns = [
    {
        title: '运营商ID',
        dataIndex: 'operId',
    },
    {
        title: '运营商名称',
        dataIndex: 'buildName',
    },
];

const SelectOperModal = (props: any, ref: any) => {
    const {
        formParams = {},
        multiple = true,
        disabledIds = [],
        limitCitys = [], //过滤城市
        onConfirm,
    } = props;
    const [visible, toggleVisible] = useState(false);

    const [selectList, updateSelectList] = useState<[]>([]); // 已选中的站点列表，用于添加时判断是否重复选择

    const [formFilterParams, updateFormFilterParams] = useState();

    const formRef = useRef();

    useImperativeHandle(ref, () => {
        return {
            open: (defaultList?: any[]) => {
                if (!isEmpty(defaultList)) {
                    updateSelectList(defaultList);
                }
                toggleVisible(true);
                run();
            },
        };
    });
    const closeModalEvent = () => {
        toggleVisible(false);
        updateFormFilterParams(undefined);

        formRef?.current?.resetFields();
        updateSelectList([]);
    };

    const {
        data: list,
        run,
        loading,
    } = useRequest(
        async () => {
            try {
                const options = {
                    ...formParams,
                    city: limitCitys?.join(','),
                };
                const { data = [] } = !isEmpty(limitCitys)
                    ? await getOperByCityApi(options)
                    : await getOperatorAllListApi();
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    const formatList = useMemo(() => {
        return list?.filter((ele) => {
            if (formFilterParams) {
                if (formFilterParams.operId) {
                    return ele.operId == formFilterParams.operId;
                }
                if (formFilterParams.buildName) {
                    return ele.buildName?.indexOf(formFilterParams.buildName) >= 0;
                }
            }
            return true;
        });
    }, [formFilterParams, list]);

    const rowSelection = {
        type: multiple ? 'checkbox' : 'radio',
        checkStrictly: false,
        selectedRowKeys: selectList.map((item: any) => item.operId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            if (!multiple) {
                // 替换只能单选，所以不用考虑之前是否已经选过其他优惠券的逻辑
                updateSelectList([...selectedRows] as any);
            } else {
                const tableRoundList: any = [];
                for (const item of formatList) {
                    tableRoundList.push(item);
                }
                // 筛选出非当前页的勾选项，不予处理
                const otherCpns = selectList.filter(
                    (x: any) =>
                        tableRoundList.filter((now: any) => now.operId == x.operId).length == 0,
                );
                updateSelectList([...otherCpns, ...selectedRows] as any);
            }
        },
        getCheckboxProps: (record: any) => ({
            disabled: disabledIds?.indexOf(record.operId) >= 0,
            name: record.operId,
        }),
    };
    const submitSelect = () => {
        if (isEmpty(selectList)) {
            message.error('请选择运营商');
            return;
        }
        onConfirm && onConfirm(selectList);
        closeModalEvent();
    };

    return (
        <Modal
            title="选择运营商"
            visible={visible}
            width={800}
            onCancel={closeModalEvent}
            destroyOnClose
            onOk={() => {
                submitSelect();
            }}
        >
            <XdtProTable
                formRef={formRef}
                loading={loading}
                dataSource={formatList}
                columns={operColumns}
                scroll={{ x: '100%', y: 400 }}
                rowSelection={rowSelection}
                rowKey="operId"
                request={async (params) => {
                    const options = {
                        ...params,
                    };
                    delete options.current;
                    delete options.pageSize;
                    updateFormFilterParams(options);
                    return;
                }}
            ></XdtProTable>
        </Modal>
    );
};
export default forwardRef(SelectOperModal);
