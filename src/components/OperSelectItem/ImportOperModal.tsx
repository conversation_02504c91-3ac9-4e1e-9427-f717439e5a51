import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message, Space, Typography } from 'antd';
import { useRequest } from 'ahooks';
import { getOperByCityApi } from '@/services/MngAstApi';
import { isEmpty } from '@/utils/utils';
import { getOperatorAllListApi } from '@/services/OperationMng/OperationMngApi';
import ImportModal from '@/components/ImportModal';
import ResultModal from '@/components/ImportModal/Result';
import InputOperModal from './InputOperModal';
import { importOperInputAndFileApi, importOperApi } from '@/services/Marketing/ReadjustApi';
const ImportOperModal = (props: any, ref: any) => {
    const { onConfirm, importApi = importOperInputAndFileApi, importParams = {} } = props;
    const [visible, toggleVisible] = useState(false);
    const importOperRef = useRef();
    const inputOperRef = useRef();
    const resultRef = useRef();
    const [defaultList, updateDefaultList] = useState<any[]>([]);
    useImperativeHandle(ref, () => {
        return {
            open: (defList?: any[]) => {
                if (defList instanceof Array) {
                    updateDefaultList(defList);
                }
                openModalEvent();
            },
        };
    });
    const openModalEvent = () => {
        toggleVisible(true);
    };
    const closeModalEvent = () => {
        toggleVisible(false);
        updateDefaultList([]);
    };

    const submitSelect = (newList: any[]) => {
        onConfirm && onConfirm(newList);
        closeModalEvent();
    };

    return (
        <>
            <Modal
                title="导入运营商"
                visible={visible}
                width={400}
                onCancel={closeModalEvent}
                destroyOnClose
                footer={false}
                zIndex={100}
            >
                <Space>
                    <Typography.Link
                        onClick={() => {
                            inputOperRef?.current?.open();
                        }}
                    >
                        +文本导入
                    </Typography.Link>
                    <Typography.Link
                        onClick={() => {
                            importOperRef?.current?.show();
                        }}
                    >
                        +文件导入
                    </Typography.Link>
                </Space>
            </Modal>
            <InputOperModal
                ref={inputOperRef}
                onConfirm={async (params: any) => {
                    const options = new FormData();
                    const allParams = {
                        ...params,
                        ...importParams,
                    };
                    for (const key in allParams) {
                        options.append(key, allParams[key]);
                    }
                    const { data } = await importApi(options);

                    if (data instanceof Array) {
                        const successList = data?.filter((ele) => ele.matchFlag);
                        const failList = data?.filter((ele) => !ele.matchFlag);
                        resultRef?.current?.open({
                            successList: successList,
                            failList: failList,
                            importSuccNum: successList?.length,
                            importFailNum: failList?.length,
                        });
                    } else {
                        const { importFailNum, importSuccNum, buildDtos = [], batchId } = data;
                        resultRef?.current?.open({
                            successList: buildDtos,
                            importSuccNum,
                            importFailNum,
                            batchId,
                        });
                    }

                    return;
                }}
                zIndex={101}
            ></InputOperModal>
            <ImportModal
                ref={importOperRef}
                title="批量导入运营商"
                downLoadPath="/aliMini/xdt/static/excel/导入运营商模版.xlsx"
                onUpload={async (formData: FormData, callback?: (params: any) => void) => {
                    if (importParams) {
                        for (const key in importParams) {
                            if (Object.prototype.hasOwnProperty.call(importParams, key)) {
                                formData.append(key, importParams[key]);
                            }
                        }
                    }
                    const { data } = await importApi(formData);

                    if (data instanceof Array) {
                        const successList = data?.filter((ele) => ele.matchFlag);
                        const failList = data?.filter((ele) => !ele.matchFlag);
                        resultRef?.current?.open({
                            successList: successList,
                            failList: failList,
                            importSuccNum: successList?.length,
                            importFailNum: failList?.length,
                        });
                    } else {
                        const { importFailNum, importSuccNum, buildDtos = [], batchId } = data;
                        resultRef?.current?.open({
                            successList: buildDtos,
                            importSuccNum,
                            importFailNum,
                            batchId,
                        });
                    }

                    return;
                }}
                zIndex={101}
            ></ImportModal>
            <ResultModal
                ref={resultRef}
                onConfirm={(newList) => {
                    submitSelect(newList);

                    inputOperRef?.current?.close();
                    importOperRef?.current?.close();
                }}
                zIndex={102}
                downloadColumns={[
                    {
                        title: '运营商ID',
                        dataIndex: 'operId',
                        width: 140,
                    },
                    {
                        title: '运营商名称',
                        dataIndex: 'buildName',
                        width: 160,
                    },
                    {
                        title: '失败原因',
                        dataIndex: 'reason',
                        width: 160,
                    },
                ]}
                downloadFileName="运营商"
            ></ResultModal>
        </>
    );
};
export default forwardRef(ImportOperModal);
