import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message, Form, Space, Button } from 'antd';
import { useRequest } from 'ahooks';
import { getOperByCityApi } from '@/services/MngAstApi';
import { isEmpty } from '@/utils/utils';
import { getOperatorAllListApi } from '@/services/OperationMng/OperationMngApi';
import OperSelectItem from '@/components/OperSelectItem';
import { connect } from 'umi';

const SelectOperFormModal = (props: any) => {
    const {
        dispatch,
        global: { operatorList },

        onConfirm,
        refInstance,
    } = props;

    const [visible, toggleVisible] = useState(false);

    const [form] = Form.useForm();

    useImperativeHandle(refInstance, () => {
        return {
            open: (defaultList?: string[]) => {
                if (operatorList.length == 0) {
                    dispatch({
                        type: 'global/getOperatorList',
                        options: {},
                    });
                }
                if (!isEmpty(defaultList)) {
                    form.setFieldsValue({
                        workOrderOper: defaultList?.map((ele) => {
                            if (typeof ele === 'string') {
                                return ele;
                            }
                            return ele.operId;
                        }),
                    });
                }
                toggleVisible(true);
            },
        };
    });
    const closeModalEvent = () => {
        toggleVisible(false);

        form?.resetFields();
    };

    const submitSelect = (values = {}) => {
        if (isEmpty(values.workOrderOper)) {
            message.error('请选择运营商');
            return;
        }
        const newList = operatorList?.filter((ele) => values.workOrderOper.includes(ele.operId));
        onConfirm && onConfirm(newList);
        closeModalEvent();
    };

    return (
        <Modal
            title="选择运营商"
            visible={visible}
            width={500}
            onCancel={closeModalEvent}
            destroyOnClose
            footer={false}
        >
            <Form
                form={form}
                name="SelectOperFormModal"
                onFinish={submitSelect}
                initialValues={{
                    workOrderOper: [],
                }}
                scrollToFirstError
            >
                <OperSelectItem
                    label="活动商户"
                    name="workOrderOper"
                    operatorList={operatorList}
                    rules={[{ required: true, message: '请选择' }]}
                    placeholder="请选择商户"
                    mode="multiple"
                    required
                />
                <Space
                    align="end"
                    className="mg-t-20"
                    style={{ display: 'flex', justifyContent: 'center' }}
                >
                    <Button onClick={closeModalEvent}>取消</Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form?.submit();
                        }}
                    >
                        确认
                    </Button>
                </Space>
            </Form>
        </Modal>
    );
};

const ModalView = connect(({ global, user }: any) => ({
    global,
    user,
}))(SelectOperFormModal);

export default forwardRef((props: any, ref) => <ModalView {...props} refInstance={ref} />);
