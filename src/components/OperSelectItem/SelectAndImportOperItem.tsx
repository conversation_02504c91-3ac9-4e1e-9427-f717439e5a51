import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message, Table, Popconfirm, Typography, Space, Button } from 'antd';
import { useRequest } from 'ahooks';
import { getOperByCityApi } from '@/services/MngAstApi';
import { isEmpty } from '@/utils/utils';
import SelectOperFormModal from './SelectOperFormModal';
import ImportOperModal from './ImportOperModal';

export const operColumns = [
    {
        title: '运营商名称',
        dataIndex: 'buildName',
        render(text, record) {
            return (
                <span>
                    {record?.buildName ||
                        record?.operNickname ||
                        record?.opername ||
                        record?.operName ||
                        ''}
                </span>
            );
        },
    },
    {
        title: '运营商ID',
        dataIndex: 'operId',
    },
];

const SelectAndImportOperItem = (props: API.CommonFormItem<Record<string, any>[]>, ref: any) => {
    const { value, onChange, disabled } = props;

    const [tableList, updateTableList] = useState<any[]>([]);

    const addRef = useRef();
    const importRef = useRef();

    useImperativeHandle(ref, () => {
        return {};
    });

    useEffect(() => {
        if (value instanceof Array) {
            updateTableList(value);
        }
    }, [value]);

    const removeItemByIndex = (index: number) => {
        const newTable = [...tableList];
        newTable.splice(index, 1);
        updateFormItem(newTable);
    };

    const updateFormItem = (newList: any[]) => {
        updateTableList(newList);
        onChange && onChange(newList);
    };

    const columns = [
        {
            title: '序号',
            width: 80,
            render(text: string, record: Record<string, any>, index: number) {
                return <span>{index + 1}</span>;
            },
        },
        ...operColumns,
        ...(disabled
            ? []
            : [
                  {
                      title: '操作',
                      width: 120,
                      render(text: string, record: Record<string, any>, index: number) {
                          return (
                              <Popconfirm
                                  title={`是否删除`}
                                  okText="确定"
                                  cancelText="取消"
                                  onConfirm={async () => {
                                      removeItemByIndex(index);
                                  }}
                              >
                                  <Typography.Link>删除</Typography.Link>
                              </Popconfirm>
                          );
                      },
                  },
              ]),
    ];

    return (
        <>
            <p>
                <Space>
                    {!disabled && (
                        <>
                            <Button
                                type="primary"
                                onClick={() => {
                                    addRef?.current?.open(tableList);
                                }}
                            >
                                添加
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    importRef?.current?.open(tableList);
                                }}
                            >
                                批量添加
                            </Button>
                        </>
                    )}

                    {!isEmpty(tableList) && (
                        <Typography.Text type="secondary">
                            已添加个{tableList?.length}运营商
                        </Typography.Text>
                    )}
                </Space>
            </p>
            <Table columns={columns} dataSource={tableList}></Table>
            <SelectOperFormModal
                ref={addRef}
                onConfirm={(newList: any[]) => {
                    const otherList = tableList.filter(
                        (ele) => !newList.some((child) => ele.operId == child.operId),
                    );

                    updateFormItem([...otherList, ...newList]);
                }}
            ></SelectOperFormModal>
            <ImportOperModal
                ref={importRef}
                onConfirm={(newList: any[]) => {
                    const otherList = tableList.filter(
                        (ele) => !newList.some((child) => ele.operId == child.operId),
                    );

                    updateFormItem([...otherList, ...newList]);
                }}
            ></ImportOperModal>
        </>
    );
};
export default forwardRef(SelectAndImportOperItem);
