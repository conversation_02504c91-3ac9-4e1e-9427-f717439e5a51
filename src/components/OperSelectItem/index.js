import {
    Fragment,
    useEffect,
    useLayoutEffect,
    useMemo,
    useState,
    useImperativeHandle,
} from 'react';
import { Form, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { SELECT_TYPES } from '@/config/declare';

const FormItem = Form.Item;
const { Option } = Select;

const FILTER_TYPES = {
    FULL_NAME: '01',
    SHOTR_NAME: '02',
    CONTRACT_NO: '03',
};

const formatFilterKey = (type) => {
    let keyValue = '';
    switch (type) {
        case FILTER_TYPES.FULL_NAME:
            keyValue = 'opername';
            break;
        case FILTER_TYPES.SHOTR_NAME:
            keyValue = 'operNickname';
            break;
        case FILTER_TYPES.CONTRACT_NO:
            keyValue = 'operProjectCode';
            break;

        default:
            break;
    }
    return keyValue;
};

const OperSelectItem = (props) => {
    const {
        formItemLayout,
        operatorList,
        initRef,
        label = '运营商',
        name = 'operId',
        disabled,
        rules,
        currentUser,
        help,
        validateStatus,
        purchase = SELECT_TYPES.ALL,
        value,
        limitOperIds,
        required,
        onChangeOption,
        ...rest
    } = props;

    // Select属性
    const {
        placeholder = '请选择',
        onChange,
        callbackOrigin, // 如果需要返回原始的选中对象数组，配true，并且监听onChange
        onClear,
        mode,
        maxTagCount,
        maxTagPlaceholder,
        cooperationPlatform,
    } = rest || {};
    const [nowOperators, changeNowOperators] = useState([]);

    const formatOperator = useMemo(() => {
        let list = operatorList;
        if (cooperationPlatform) {
            list = list?.filter((ele) => {
                let cooperationPlatformList =
                    ele.cooperationPlatform?.split(',').filter((item) => item) || [];

                return cooperationPlatformList.includes(cooperationPlatform);
            });
        }
        if (purchase == SELECT_TYPES.ONLYBUY) {
            // 仅支持选择购电模式
            return list?.filter((ele) => {
                return ele.cooperationType === '02';
            });
        }
        if (purchase == SELECT_TYPES.EXCEPTBUY) {
            // 不允许选择购电模式
            return list?.filter((ele) => {
                return ele.cooperationType !== '02';
            });
        }

        return list;
    }, [operatorList]);

    const [operItems, updateOperItems] = useState([]);
    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            changeNowOperators(formatOperator);
        },
        operItems,
    }));

    useEffect(() => {
        changeNowOperators(formatOperator);
    }, [formatOperator]);

    useEffect(() => {
        if (currentUser.operId) {
            callbackEvent(currentUser.operId);
        }
    }, [currentUser.operId]);

    const onClearEvent = () => {
        changeNowOperators(formatOperator);
        onClear && onClear();
    };

    const handleSearch = (txt) => {
        if (txt) {
            const list = [];

            for (const item of formatOperator) {
                const searchName = [
                    item.operNickname,
                    item.operName,
                    item.operatorName,
                    item.buildName,
                    item.operId,
                    item.operProjectCode,
                    // item[filterKey],
                ];
                // 只要满足其一，就可被查询到
                for (const ele of searchName) {
                    if (ele?.indexOf(txt) >= 0) {
                        list.push(item);
                        break;
                    }
                }
            }
            changeNowOperators(list);
        } else {
            changeNowOperators(formatOperator);
        }
    };

    const handleFilter = (filterTxt, option) => true;
    const initialValue = useMemo(() => {
        if (Object.keys(rest).includes('initialValue')) {
            return rest.initialValue;
        }
        return currentUser.operId || '';
    }, [rest]);

    const callbackEvent = (id) => {
        if (callbackOrigin) {
            const operIds = id instanceof Array ? id : (typeof id == 'string' && [id]) || undefined;
            const res = [];
            operIds?.map((operId) => {
                const matchOper = formatOperator.filter((ele) => ele[name] == operId);
                const matched = [...((matchOper?.length && matchOper) || [{ [name]: operId }])];
                res.push(...matched);
            });
            onChange?.(res);
            updateOperItems(res);
        } else {
            onChange?.(id);
            updateOperItems(id);
        }
    };

    return (
        <FormItem label={label} {...formItemLayout} required={required}>
            <Row>
                {/* <Col flex="70px">
                    <Select value={filterType} onChange={changeFilterEvent}>
                        <Option value={FILTER_TYPES.FULL_NAME}>全称</Option>
                        <Option value={FILTER_TYPES.SHOTR_NAME}>简称</Option>
                        <Option value={FILTER_TYPES.CONTRACT_NO}>合同号</Option>
                    </Select>
                </Col> */}
                <Col flex="auto">
                    <FormItem
                        noStyle
                        name={name}
                        rules={rules}
                        help={help}
                        validateStatus={validateStatus}
                        initialValue={initialValue}
                    >
                        <Select
                            value={value}
                            showSearch
                            placeholder={placeholder}
                            onSearch={handleSearch}
                            filterOption={handleFilter}
                            disabled={disabled || currentUser.operId || limitOperIds?.length > 0}
                            onChange={(value) => {
                                const operItem = nowOperators?.find((item) => item.operId == value);
                                handleSearch(''); // 已选中的情况下，清空筛选条件
                                callbackEvent(value);
                                onChangeOption?.(operItem);
                            }}
                            onClear={onClearEvent}
                            allowClear
                            mode={mode}
                            maxTagCount={maxTagCount}
                            maxTagPlaceholder={maxTagPlaceholder}
                            showArrow
                        >
                            {nowOperators?.map((item, index) => (
                                <Option
                                    value={
                                        item[name] || item.operId || item.buildId || item.operatorId
                                    }
                                    key={
                                        item[name] || item.operId || item.buildId || item.operatorId
                                    }
                                >
                                    {item.operNickname || item.operatorName || item.buildName}
                                </Option>
                            ))}
                        </Select>
                    </FormItem>
                </Col>
            </Row>
        </FormItem>
    );
};

export default connect(({ user }) => ({
    currentUser: user.currentUser,
}))(OperSelectItem);
