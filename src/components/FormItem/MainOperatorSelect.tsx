import { Select } from 'antd';
import type { FormItemProps, SelectProps } from 'antd';
import React, { useEffect } from 'react';
import { useModel } from 'umi';

const MainOperatorSelect: React.FC<FormItemProps & SelectProps> = (props) => {
    const { value, onChange, placeholder, ...other } = props;
    const { operatorList, initOperatorList } = useModel('GlobalDict');

    useEffect(() => {
        if (operatorList.length === 0) {
            initOperatorList();
        }
    }, [operatorList]);

    return (
        <Select
            {...other}
            value={value}
            showSearch
            placeholder={placeholder}
            fieldNames={{
                label: 'buildName',
                value: 'operId',
            }}
            filterOption={(input, option) => {
                return (
                    option?.buildName?.toLowerCase().indexOf(input?.toLowerCase()) >= 0 ||
                    option?.operId?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
                );
            }}
            options={operatorList}
            onChange={(value, option) => {
                onChange?.(value, option);
            }}
            allowClear
            showArrow
        />
    );
};

export default MainOperatorSelect;
