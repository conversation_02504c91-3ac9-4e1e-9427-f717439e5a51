// 场景类型下拉框
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { useEffect } from 'react';

import { querySceneTypeDownApi } from '@/services/Marketing/MessageCenterApi';

const MessageSceneTypeSelect = (props: {
    value?: string;
    onChange?: (v: string) => void;
    onChangeOption?: (v: any) => void;
}) => {
    const { value, onChange, onChangeOption } = props;
    const { run, data, loading } = useRequest(
        () => {
            return querySceneTypeDownApi();
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        run();
    }, []);

    return (
        <Select
            allowClear
            loading={loading}
            options={data?.data?.map((v) => ({ label: v.sceneTypeName, value: v.sceneType })) || []}
            placeholder="请选择场景类型"
            showSearch
            onChange={(value, option) => {
                onChange?.(value);
                onChangeOption?.(option);
            }}
            value={value}
        ></Select>
    );
};

export default MessageSceneTypeSelect;
