import { Cascader } from 'antd';
import type { CascaderProps } from 'antd';
import React, { useEffect, useState } from 'react';

import { getAllProvinceCityApi } from '@/services/CommonApi';
import { isEmpty } from '@/utils/utils';

const CityCascader: React.FC<Omit<CascaderProps<any>, 'options'>> = (props) => {
    const { placeholder = '请选择', ...restProps } = props;
    const [cityTreeData, changeCityTreeData] = useState<any[]>([]);
    const initCityTree = async () => {
        try {
            const { data: areaList } = await getAllProvinceCityApi();
            const list = areaList.map((ele: any) => ({
                value: ele.areaCode,
                label: ele.areaName,
                children: ele.childs?.map((cityEle: any) => ({
                    value: cityEle.areaCode,
                    label: cityEle.areaName,
                })),
            }));
            changeCityTreeData(list);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (isEmpty(cityTreeData)) {
            initCityTree();
        }
    }, [cityTreeData]);
    console.debug('cityTreeData', cityTreeData);
    return <Cascader {...(restProps as any)} placeholder={placeholder} options={cityTreeData} />;
};

export default CityCascader;
