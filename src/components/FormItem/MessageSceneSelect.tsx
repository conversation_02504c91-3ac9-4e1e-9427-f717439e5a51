// 场景名称下拉框
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { useEffect } from 'react';

import { querySceneDownApi, getMessageSceneListApi } from '@/services/Marketing/MessageCenterApi';

const MessageSceneSelect = (props: {
    value?: string;
    onChange?: (v: string | undefined) => void;
    onChangeOption?: (v: any) => void;
    sceneType?: string;
}) => {
    const { value, onChange, onChangeOption, sceneType } = props;
    const { run, data, loading } = useRequest(
        async (sceneType) => {
            const params = {
                sceneType,
                pageIndex: 1,
                pageSize: 999,
            };
            const {
                data: { records = [] },
            } = await getMessageSceneListApi(params);
            return records;
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        run(sceneType);
    }, [sceneType]);
    const handleFilter = (input, option) => {
        return option?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
    };
    return (
        <Select
            allowClear
            loading={loading}
            options={
                data?.map((v) => ({ label: v?.sceneName, value: v?.messageCenterSceneId })) || []
            }
            placeholder="请选择场景名称"
            showSearch
            filterOption={handleFilter}
            onChange={(value, option) => {
                onChange?.(value);
                onChangeOption?.(option);
            }}
            value={value}
        ></Select>
    );
};

export default MessageSceneSelect;
