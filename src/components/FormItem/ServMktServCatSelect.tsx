// 场景类型下拉框
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { useEffect } from 'react';
import { useModel } from 'umi';
import { isEmpty } from '@/utils/utils';

const ServMktServCatSelect = (props: {
    value?: string;
    onChange?: (v: string) => void;
    onChangeOption?: (v: any) => void;
}) => {
    const { value, onChange, onChangeOption } = props;
    const { codeInfo, initCode } = useModel('codeState');

    useEffect(() => {
        if (isEmpty(codeInfo?.serviceModule)) {
            initCode('serviceModule');
        }
    }, []);

    return (
        <Select
            allowClear
            options={
                codeInfo?.serviceModule?.map((v) => ({
                    label: v?.codeName,
                    value: v?.codeValue,
                })) || []
            }
            placeholder="全部"
            showSearch
            onChange={(value, option) => {
                onChange?.(value);
                onChangeOption?.(option);
            }}
            value={value}
        ></Select>
    );
};

export default ServMktServCatSelect;
