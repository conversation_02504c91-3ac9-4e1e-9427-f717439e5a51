import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { useEffect } from 'react';

import { getChannelInfoPageListApi } from '@/services/MngAlyApi';

const ChannelSelect = (props: {
    value?: string;
    onChange?: (v: string) => void;
    onChangeOption?: (v: any) => void;
}) => {
    const { value, onChange, onChangeOption } = props;
    const { run, data, loading } = useRequest(
        () => {
            return getChannelInfoPageListApi();
        },
        {
            manual: true,
        },
    );

    useEffect(() => {
        run();
    }, []);

    return (
        <Select
            allowClear
            fieldNames={{
                label: 'channelName',
                value: 'applyMode',
            }}
            filterOption={(input, option) =>
                option?.channelName?.toLowerCase().indexOf(input?.toLowerCase()) >= 0 ||
                option?.applyMode?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
            }
            loading={loading}
            options={data?.data || []}
            placeholder="请选择渠道"
            showSearch
            onChange={(value, option) => {
                onChange?.(value);
                onChangeOption?.(option);
            }}
            value={value}
        ></Select>
    );
};

export default ChannelSelect;
