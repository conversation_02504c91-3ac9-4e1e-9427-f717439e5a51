import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Input, Modal, Space, Table, Tag } from 'antd';
import type { ColumnType } from 'antd/lib/table';
import { useEffect, useState } from 'react';

import usePageState from '@/hooks/usePageState';
import { getCrowdDefineQueryPage } from '@/services/MngBilApi';

/**
 * 用户画像选择器，带展示和选择弹窗， 按照formItem写的。暂时不支持在Form.Item外面使用
 * 目标用户仅允许添加配置在人群画像管理下，「批量推送场景」上配置应用功能为「触达任务」的人群（那个地方也需要把名称改了），且应用数据来源为「回流数据」，
 * 当数据未回流成功时，使用上一人群版本的逻辑（人群回流的默认逻辑），如果没有上一版本的记录则该人群不会再选取
 */
interface Props {
    value?: API.CdpCrowdDefineQueryVo[]; //选中的人群数据，多个以","隔开
    onChange?: (values: any[]) => void; //选中后回调事件
    disabled?: boolean;
    applyType?: string; //过滤人群类型
}
const MessageUserProfile = (props: Props) => {
    const { value, onChange, disabled, applyType } = props;
    const [selectUserProfiles, setSelectUserProfiles] = useState<API.CdpCrowdDefineQueryVo[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [searchColumn, setSearchColumn] = useState<string>();
    const [selectRowKeys, setSelectRowKeys] = useState<string[]>([]);
    const [pageInfo, changePageInfo, onTableChange] = usePageState({}, { global: {} });
    const { run, data, loading } = useRequest(
        (params) => {
            return getCrowdDefineQueryPage(params);
        },
        {
            manual: true,
        },
    );

    const searchData = () => {
        run({
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            crowdName: searchColumn?.trim(),
            applyType,
        });
    };
    useEffect(() => {
        searchData();
    }, [pageInfo]);

    useEffect(() => {
        if (value) {
            const values = value?.map((v) => v?.cdpCrowdId);
            setSelectUserProfiles(
                data?.data?.records?.filter((v: any) => values.includes(v?.cdpCrowdId)) as any[],
            );
        } else {
            setSelectUserProfiles([]);
        }
    }, [value, data?.data]);

    const submitSelect = () => {
        if (selectRowKeys) {
            onChange?.([
                ...(data?.data?.records?.filter((v) =>
                    selectRowKeys.includes(v?.cdpCrowdId as string),
                ) || []),
            ]);
            setSelectRowKeys([]);
        }
        setSearchColumn('');
        setVisible(false);
    };

    const removeSelect = (cdpCrowdId: string) => {
        onChange?.(selectUserProfiles?.filter((v: any) => v?.cdpCrowdId !== cdpCrowdId));
    };

    const showSelectModal = () => {
        setSearchColumn('');
        setVisible(true);
    };

    const columns: ColumnType<any>[] = [
        {
            title: '人群ID',
            dataIndex: 'crowdDefineId',
            width: 140,
        },
        {
            title: '人群名称',
            dataIndex: 'cdpCrowdName',
            width: 240,
            filterDropdown: () => (
                <div style={{ padding: 8 }}>
                    <Input
                        style={{ width: 188, marginBlockEnd: 8, display: 'block' }}
                        maxLength={20}
                        onChange={(e) => {
                            setSearchColumn(e.target.value?.trim());
                            changePageInfo({ ...pageInfo, pageIndex: 1 });
                        }}
                    />
                </div>
            ),
            filterIcon: () => (
                <SearchOutlined style={{ color: searchColumn ? '#1890ff' : undefined }} />
            ),
        },
        {
            title: '标签值',
            dataIndex: 'cdpCrowdId',
            width: 140,
        },
    ];

    return (
        <>
            <Space wrap>
                {selectUserProfiles?.map((v: any) => {
                    return (
                        <Space direction="vertical" size={4} key={v?.crowdId}>
                            <Tag
                                closable={!disabled}
                                onClose={() => {
                                    removeSelect(v?.cdpCrowdId);
                                }}
                                key={v?.cdpCrowdId}
                            >
                                {v?.cdpCrowdName || v?.cdpCrowdId}
                            </Tag>
                        </Space>
                    );
                })}
                {!disabled && (!selectUserProfiles || selectUserProfiles.length === 0) && (
                    <Button onClick={showSelectModal} type="primary" icon={<PlusOutlined />}>
                        添加目标用户
                    </Button>
                )}
            </Space>
            <Modal
                visible={visible}
                title="添加目标用户"
                onCancel={() => {
                    setSelectRowKeys([]);
                    setVisible(false);
                }}
                destroyOnClose
                onOk={submitSelect}
                width={648}
            >
                <Table
                    columns={columns}
                    dataSource={data?.data?.records}
                    loading={loading}
                    onChange={onTableChange}
                    rowSelection={{
                        selectedRowKeys: selectRowKeys,
                        type: 'radio',
                        onChange(select) {
                            setSelectRowKeys(select as string[]);
                        },
                        getCheckboxProps(record) {
                            if (value?.includes(record?.codeValue)) {
                                return {
                                    disabled: true,
                                };
                            } else {
                                return {};
                            }
                        },
                    }}
                    pagination={{
                        current: pageInfo.pageIndex,
                        total: data?.data?.total,
                        pageSize: pageInfo.pageSize,
                        showTotal: (total: number) => `共 ${total} 条`,
                    }}
                    expandable={{
                        defaultExpandAllRows: true,
                    }}
                    rowKey="cdpCrowdId"
                    scroll={{
                        x: 'max-content',
                        y: 400,
                    }}
                />
            </Modal>
        </>
    );
};

export default MessageUserProfile;
