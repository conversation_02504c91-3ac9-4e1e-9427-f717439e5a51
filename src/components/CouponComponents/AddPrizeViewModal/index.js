import React, { Fragment, useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { connect } from 'umi';
import { Button, InputNumber, Select, Form, Modal, message, Space } from 'antd';
import { addCpnApi } from '@/services/Marketing/MarketingTurnoverApi';
import { appendCpnApi } from '@/services/Marketing/MarketingSigninApi';

const { Option } = Select;

const FormItem = Form.Item;

const prizeformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 8,
    },
};

const EDIT_STATUS = {
    ADD: '1',
    EDIT: '2',
};

const AddPrizeViewModal = (props, ref) => {
    const {
        dispatch,
        global,
        initRef,
        onAddFinish, // 添加完毕的回调事件，如果是追加，返回空，如果是添加，返回已选中的cpn对象
        onEditFinish, // 编辑完毕回调，如果是追加，返回空，如果是编辑，返回编辑后的cpn对象
        extInParams, // 额外入参
        pageType,
    } = props;

    const { cpnList = [] } = global;
    const [showPrizeView, togglePrizeView] = useState(false); // 选择奖品弹窗
    const [cpnInfo, changeCpnInfo] = useState(undefined); // 已选中的优惠券信息
    const [editStatus, updateEidtStatus] = useState(EDIT_STATUS.ADD); // 当前编辑状态
    const [selectIds, updateSelectIds] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [actId, updateActId] = useState(undefined); // 编辑的活动id，用于追加时传参

    useImperativeHandle(initRef, () => ({
        // 追加和添加都调add方法，传入已选中的id列表，活动id、如果是追加，还要传cpnInfo
        add: (_selectIds, _actId, _cpnInfo) => {
            updateSelectIds([..._selectIds]);
            updateActId(_actId);
            updateEidtStatus(EDIT_STATUS.ADD);
            changeCpnInfo({ ..._cpnInfo });
            togglePrizeView(true);
            show();
        },
        // 编辑调用，传入待编辑对象
        edit: (_cpnInfo) => {
            updateSelectIds([]);
            updateActId(undefined);
            updateEidtStatus(EDIT_STATUS.EDIT);
            changeCpnInfo({ ..._cpnInfo });
            togglePrizeView(true);
            show();
        },
        close: () => {
            togglePrizeView(false);
        },
    }));

    useEffect(() => {
        form.resetFields();
        form.setFieldsValue({
            ...cpnInfo,
            cpnId: cpnInfo?.cpnOwner != '03' ? undefined : cpnInfo?.cpnId, // 如果非平台券，需要选择其他新增的优惠券后才可提交，且平台券不可被选中
            putNum: cpnInfo?.putNum || 1,
        });
    }, [cpnInfo]);

    const [form] = Form.useForm();

    const show = () => {
        dispatch({
            type: 'global/getCpnList',
            options: extInParams,
        });
    };

    const onFinish = () => {
        if (!cpnInfo) {
            message.error('请选择奖品');
            return;
        }
        form.validateFields().then(async (values) => {
            const params = {
                ...cpnInfo,
                ...values,
                pushPrizeId: cpnInfo?.pushPrizeId,
            };
            if (editStatus == EDIT_STATUS.EDIT) {
                // 编辑
                onEditFinish && onEditFinish(params);
                initRef?.current?.close();
            } else if (actId) {
                // 追加，明明说后端不接收actId字段，所以仅仅作为前端追加的逻辑判断使用
                try {
                    params.actId = actId;
                    if (pageType === 'signin') {
                        params.categoryType = 32;
                        await appendCpnApi(params);
                    } else {
                        await addCpnApi(params);
                    }
                    onAddFinish && onAddFinish();
                    initRef?.current?.close();
                } catch (error) {
                    console.log(error);
                }
            } else {
                // 添加
                const item = selectIds.find((ele) => ele == params.cpnId);
                if (item) {
                    message.error('已添加此优惠券');
                    return;
                }
                onAddFinish && onAddFinish(params);
                initRef?.current?.close();
            }
        });
    };

    return (
        <Modal
            title={(cpnInfo?.pushPrizeId && '追加奖品') || (cpnInfo && '编辑奖品') || '添加奖品'}
            destroyOnClose
            width={800}
            visible={showPrizeView}
            onCancel={() => initRef?.current?.close()}
            footer={null}
            maskClosable={false}
        >
            <Form form={form} onFinish={onFinish} initialValues={{}} scrollToFirstError>
                <FormItem name="cpnOwner" noStyle />
                <FormItem name="cpnName" noStyle />
                <FormItem name="pushPrizeId" noStyle />
                <FormItem name="unGetNum" noStyle />

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.cpnOwner !== curValues.cpnOwner ||
                        prevValues.cpnName !== curValues.cpnName
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const cpnOwner = getFieldValue('cpnOwner');

                        // 支付宝券、第三方券追加，需先增加券，追加时进行选择追加
                        return (
                            <FormItem
                                label="选择奖品:"
                                name="cpnId"
                                {...prizeformItemLayout}
                                wrapperCol={{ span: 16 }}
                                rules={[{ required: true, message: '请选择奖品' }]}
                            >
                                {(cpnOwner == '03' && getFieldValue('cpnName')) ||
                                    (cpnList instanceof Array ? (
                                        <Select
                                            placeholder="请选择"
                                            // style={{ width: '260px' }}
                                            showSearch
                                            onChange={(id) => {
                                                const item =
                                                    cpnList &&
                                                    cpnList.find((ele) => ele.cpnId == id);
                                                form.resetFields();
                                                form.setFieldsValue({
                                                    ...item,
                                                    putNum: item?.putNum || 1,
                                                });
                                            }}
                                        >
                                            {cpnList &&
                                                cpnList?.map((ele) => (
                                                    // 支付宝和第三方券追加的时候，不可以选中平台券
                                                    <Option
                                                        key={ele.cpnId}
                                                        value={ele.cpnId}
                                                        disabled={ele.cpnOwner == '03'}
                                                    >
                                                        {ele.cpnName}
                                                    </Option>
                                                ))}
                                        </Select>
                                    ) : null)}
                            </FormItem>
                        );
                    }}
                </FormItem>

                <FormItem
                    shouldUpdate={(prevValues, curValues) => prevValues.cpnNo !== curValues.cpnNo}
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const cpnNo = getFieldValue('cpnNo');
                        return (
                            <FormItem label="券组编号:" name="cpnNo" {...prizeformItemLayout}>
                                <span>{cpnNo || ''}</span>
                            </FormItem>
                        );
                    }}
                </FormItem>
                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.cpnOwner !== curValues.cpnOwner ||
                        prevValues.unGetNum !== curValues.unGetNum
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const cpnOwner = getFieldValue('cpnOwner');
                        const unGetNum = getFieldValue('unGetNum');

                        // 平台券追加，追加券数量
                        return (
                            <Fragment>
                                <FormItem
                                    label="奖品数量:"
                                    name="unGetNum"
                                    {...prizeformItemLayout}
                                    rules={[{ required: true, message: '请填写奖品数量' }]}
                                >
                                    <InputNumber
                                        disabled={cpnOwner != '03'}
                                        min={1}
                                        // 因平台券情况下，未发放数量会随着输入值变动，所以需要锚定初始值，其他不可编辑的情况可用优惠券本身的值
                                        max={cpnOwner == '03' ? cpnInfo?.unGetNum : unGetNum}
                                        placeholder="请填写"
                                    />
                                </FormItem>

                                <FormItem label="每人领取数量:" {...prizeformItemLayout} required>
                                    <FormItem
                                        name="putNum"
                                        noStyle
                                        // rules={[
                                        //     { required: true, message: '请填写领取数量' },
                                        //     ({ getFieldValue }) => ({
                                        //         validator(rule, value) {
                                        //             if (!value) {
                                        //                 return Promise.reject('');
                                        //             }
                                        //             if (value > unGetNum) {
                                        //                 return Promise.reject(
                                        //                     '每人领取数量不能超过奖品数量',
                                        //                 );
                                        //             }
                                        //             return Promise.resolve();
                                        //         },
                                        //     }),
                                        // ]}
                                    >
                                        <InputNumber
                                            disabled
                                            // min={1}
                                            // max={unGetNum}
                                            // placeholder="请填写"
                                        />
                                    </FormItem>
                                    <span style={{ marginLeft: '10px' }}>张</span>
                                </FormItem>
                            </Fragment>
                        );
                    }}
                </FormItem>

                <FormItem
                    {...{
                        wrapperCol: {
                            span: 8,
                            offset: 6,
                        },
                    }}
                >
                    <Space>
                        <Button type="primary" htmlType="submit">
                            提交
                        </Button>
                        <Button onClick={() => initRef?.current?.close()}>取消</Button>
                    </Space>
                </FormItem>
            </Form>
        </Modal>
    );
};

export default forwardRef(AddPrizeViewModal);
