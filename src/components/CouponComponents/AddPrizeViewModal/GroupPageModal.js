import React, {
    Fragment,
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useMemo,
} from 'react';
import { connect } from 'umi';
import { Button, InputNumber, Select, Form, Modal, message, Space, Input, Row, Col } from 'antd';
import { addCpnApi } from '@/services/Marketing/MarketingTurnoverApi';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';

const { Option } = Select;

const FormItem = Form.Item;

const searchformItemLayout = {
    labelCol: {
        span: 6,
    },
    wrapperCol: {
        span: 16,
    },
};

const GroupPageModal = (props, ref) => {
    const {
        dispatch,
        global,
        initRef,
        onAddFinish, // 添加完毕的回调事件，如果是追加，返回空，如果是添加，返回已选中的cpn对象
        onEditFinish, // 编辑完毕回调，如果是追加，返回空，如果是编辑，返回编辑后的cpn对象
        onReplaceFinish, // 替换完成
        extInParams, // 额外入参
        disabledIds, // 限制不允许勾选的id列表
        couponModel: { couponMangeList, couponMangeListTotal, almNum, cpnAlmValue },
        pageType,
        singleMode,
    } = props;
    const { cpnOwnerOptions } = global;

    const [showPrizeView, togglePrizeView] = useState(false); // 选择奖品弹窗
    const [cpnList, updateCpnList] = useState([]); // 已选中的优惠券列表，用于添加时判断是否重复选择
    const [listLoading, updateListLoading] = useState(false);

    // 替换场景用到的相关属性
    const [replaceFlag, updateReplaceFlag] = useState(false);
    const [editCpnInfo, updateEditCpnInfo] = useState();
    const [selectedRows, updateSelectedRows] = useState();

    const [pageInfo, changePageInfo, onTableChange] = usePageState(
        () => ({
            tabType: '03',
        }),
        props,
    );

    useEffect(() => {
        if (!cpnOwnerOptions || cpnOwnerOptions.length == 0) {
            dispatch({
                type: 'global/initCpnOwnerOptions',
            });
        }
    }, []);

    useImperativeHandle(initRef, () => ({
        // 追加和添加都调add方法，传入已选中的id列表，活动id
        add: (_cpnList) => {
            updateReplaceFlag(false);
            resetData();
            togglePrizeView(true);
            if (_cpnList) {
                updateCpnList([..._cpnList]);
            }
        },
        // 替换的交互，传替换前的优惠券对象
        replace: (_cpnList, editCpnItem) => {
            updateReplaceFlag(true);
            resetData();
            togglePrizeView(true);
            updateCpnList([..._cpnList]);
            updateEditCpnInfo(editCpnItem);
        },
        close: () => {
            togglePrizeView(false);
            updateEditCpnInfo(undefined);
        },
    }));

    useEffect(() => {
        searchData();
    }, [pageInfo]);

    const [form] = Form.useForm();

    const searchData = () => {
        const data = form.getFieldsValue(true);
        const params = {
            pageIndex: pageInfo.pageIndex,
            pageSize: pageInfo.pageSize,
            beginDate: '',
            endDate: '',
            cpnName: data.cpnName || '',
            cpnOwner: data.cpnOwner || '',
            cpnNo: data.cpnNo || '',
            actName: data.actName || '',
            capitalType: data.capitalType || '',
            contributParty: data.contributParty || '',
            vipCouponFlag: data.vipCouponFlag,
            almFlag: pageInfo.almFlag ? '1' : '0',
            cpnStatus: '03',
            isActCoupon: '0',
        };

        updateListLoading(true);
        dispatch({
            type: 'couponModel/getCouponMangeList',
            options: params,
        }).then(() => updateListLoading(false));
    };

    const resetData = () => {
        form.resetFields();
        form.setFieldsValue({ ...extInParams });
        updateCpnList([]);
        changePageInfo((state) => ({
            ...state,
            pageIndex: 1,
        }));
        searchData();
    };

    const onFinish = () => {
        if (replaceFlag) {
            if (!selectedRows?.length) {
                message.error('请选择替换奖品');
                return;
            }
            onReplaceFinish && onReplaceFinish([editCpnInfo, ...selectedRows]);
        } else if (singleMode) {
            if (!selectedRows?.length) {
                message.error('请选择奖品');
                return;
            }
            onAddFinish && onAddFinish([...selectedRows]);
        } else {
            if (!cpnList?.length) {
                message.error('请选择奖品');
                return;
            }
            onAddFinish && onAddFinish(cpnList.sort((a, b) => (a.creTime < b.creTime ? 1 : -1)));
        }
        initRef?.current?.close();
    };

    const cpnOwnerOptionsView = useMemo(() => {
        return (
            cpnOwnerOptions?.map((ele) => {
                return (
                    <Option key={ele.codeValue} value={ele.codeValue}>
                        {ele.codeName}
                    </Option>
                );
            }) || []
        );
    }, [cpnOwnerOptions]);

    const columns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '券创建时间',
            dataIndex: 'creTime',
            width: 160,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },

        {
            title: '优惠券名称',
            width: 200,
            dataIndex: 'cpnName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '会员专享',
            dataIndex: 'vipCouponFlag',
            width: 140,
            render(text = '-', record) {
                if (record.vipCouponFlag == '1') {
                    text = '是';
                }
                if (record.vipCouponFlag == '0') {
                    text = '否';
                }
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '券组编号',
            width: 120,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...((pageType == 'couponBagPage' && [
            {
                title: '出资方',
                width: 100,
                dataIndex: 'contributPartyName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]) || [
            {
                title: '券类型',
                width: 100,
                dataIndex: 'cpnOwnerName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
        ]),
        {
            title: '券面额',
            width: 100,
            dataIndex: 'cpnAmt',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '库存数量 ',
            width: 140,
            dataIndex: 'cpnNum',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '创建人',
            dataIndex: 'createdBy',
            width: 120,
            render(text, record) {
                return <span title={text}>{text || '-'}</span>;
            },
        },
    ];

    const rowSelection = {
        type: replaceFlag || singleMode ? 'radio' : 'checkbox',
        selectedRowKeys:
            replaceFlag || singleMode ? undefined : cpnList.map((item) => `${item.cpnId}`),
        onChange: (selectedRowKeys, selectedRows) => {
            if (replaceFlag || singleMode) {
                // 替换只能单选，所以不用考虑之前是否已经选过其他优惠券的逻辑
                updateSelectedRows([...selectedRows]);
            } else {
                // 筛选出非当前页的勾选项，不予处理
                let otherCpns = cpnList.filter(
                    (x) => couponMangeList.filter((now) => now.cpnId == x.cpnId).length == 0,
                );
                updateCpnList([...otherCpns, ...selectedRows]);
            }
        },
        getCheckboxProps: (record) => ({
            disabled: record.pushPrizeId || disabledIds?.indexOf(record.cpnId) >= 0,
            name: record.cpnId,
        }),
    };

    const modalTitle = useMemo(() => {
        if (pageType === 'couponBagPage') {
            return '添加优惠券';
        }
        return '添加奖品';
    }, [pageType]);

    return (
        <Modal
            title={modalTitle}
            destroyOnClose
            width={1200}
            visible={showPrizeView}
            onCancel={() => initRef?.current?.close()}
            footer={null}
            maskClosable={false}
        >
            <Form
                form={form}
                onFinish={onFinish}
                initialValues={{ ...extInParams }}
                scrollToFirstError
            >
                <Row>
                    <FormItem name="vipCouponFlag" noStyle />
                    <Col span={6}>
                        <FormItem label="券名称" name="cpnName" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <FormItem label="券编号" name="cpnNo" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        {(pageType == 'couponBagPage' && (
                            <FormItem
                                label="出资方"
                                name="contributParty"
                                {...searchformItemLayout}
                            >
                                <Select
                                    placeholder="请选择"
                                    disabled={extInParams?.contributParty}
                                    allowClear
                                >
                                    <Option value="01">运营商</Option>
                                    <Option value="02">平台</Option>
                                    <Option value="04">混合出资</Option>
                                    <Option value="05">大客户</Option>
                                </Select>
                            </FormItem>
                        )) || (
                            <FormItem label="券类型" name="cpnOwner" {...searchformItemLayout}>
                                <Select
                                    placeholder="请选择"
                                    disabled={extInParams?.cpnOwner}
                                    allowClear
                                >
                                    {cpnOwnerOptionsView}
                                </Select>
                            </FormItem>
                        )}
                    </Col>
                    <Col span={8}>
                        <FormItem label="券备注:" name="remark" {...searchformItemLayout}>
                            <Input placeholder="请填写" autoComplete="off" />
                        </FormItem>
                    </Col>
                    <Col span={6}>
                        <Space>
                            <Button type="primary" onClick={searchData}>
                                查询
                            </Button>
                            <Button onClick={resetData}>重置</Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <TablePro
                loading={listLoading}
                scroll={{ x: 'max-content' }}
                rowKey={(record) => `${record.cpnId}`}
                dataSource={couponMangeList}
                columns={columns}
                onChange={onTableChange}
                rowSelection={{
                    type: 'checkbox',
                    ...rowSelection,
                }}
                pagination={{
                    current: pageInfo.pageIndex,
                    total: couponMangeListTotal,
                    pageSize: pageInfo.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条`,
                }}
                tabType={pageInfo.tabType}
                sticky={{ offsetHeader: 0 }}
            />
            <Space align="end" style={{ display: 'flex', justifyContent: 'center' }}>
                <Button type="primary" onClick={onFinish}>
                    提交
                </Button>
                <Button onClick={() => initRef?.current?.close()}>取消</Button>
            </Space>
        </Modal>
    );
};

export default forwardRef(GroupPageModal);
