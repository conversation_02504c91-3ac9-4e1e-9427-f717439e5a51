import React, { Fragment, useEffect, useState, useRef, useMemo, forwardRef } from 'react';
import commonStyles from '@/assets/styles/common.less';
import { Button, Form, Modal, Space, Popconfirm, InputNumber, message } from 'antd';
import TablePro from '@/components/TablePro';
import PropTypes from 'prop-types';
import AddPrizeViewModal from '@/components/CouponComponents/AddPrizeViewModal';
import GroupPageModal from '@/components/CouponComponents/AddPrizeViewModal/GroupPageModal';
import { PlusOutlined } from '@ant-design/icons';
import { Link } from 'umi';
import { STATUS_TYPES } from '@/config/declare';
import { copyObjectCommon, isEmpty } from '@/utils/utils';

const FormItem = Form.Item;

// 替换类型
export const REPLACE_TYPE = {
    NEW: 'new', // 替换后的券
    OLD: 'old', //  被替换的券
};

const CouponTable = (props, ref) => {
    const {
        form,
        actId,
        /** 新增/复制(活动类型=空) --> 有添加/删除，没有追加；
            编辑
            -- 草稿(活动类型=0) --> 有添加/删除，没有追加；
            -- 待开始(活动类型=1) --> 有追加，没有添加/删除；
            -- 进行中(活动类型=2) --> 有追加，没有添加/删除；
            -- 已结束(活动类型=3) --> 不能编辑；
            详情
            -- 全部状态不可编辑  */
        actState,
        isCopy, // 复制情况单独处理
        editabled, // 是否可编辑，false则不开放添加/删除/追加按钮，判断优先级最高
        needCheck, // 是否需要校验必填
        replaceEnabled, // 是否支持替换
        putCpnList, // 已发放优惠券列表
        onRefresh, // 外部各自实现刷新方法
        formItemLayout,
        extInParams, // 额外入参
        title = '配置礼包',
        name = 'cpnList',
        filePath, // 父级键，仅嵌套时传参
        putNumCol = undefined, // 默认使用券数量编辑框
        otherCol = undefined, // 显示在操作列前的col
        putColTitle = '单人发券数', // 券数量编辑框标题
        putColDataIndex = 'putNum', // 券数量编辑框字段
        totalItem = undefined, // 默认使用礼包总数统计项
        disabledIds, // 限制不允许勾选的id列表，传参给GroupPageModal组件用
        changeCouponCallback, // 如果实现了变更优惠券的回调，需要在回调内返回处理后的数据集，添加和删除都会调用
        rules = undefined,
        pageType = undefined, //区分各个活动配置页面用于定制化
        singleMode, // 是否开启单个优惠券模式，限制添加只能单选，如果选了一个，再点添加，会提示错误
    } = props;

    const addToPrizeRef = useRef();
    const addNewPrizeRef = useRef();
    const [cpnList, changeCpnList] = useState([]);

    const changeCpnListAndFormValue = (newList) => {
        changeCpnList(newList);
        setFormValues({ [name]: newList });
    };

    // 可添加=可删除
    const addNewEnabled = useMemo(() => {
        if (!editabled) {
            return false;
        }
        if (isCopy) {
            return true;
        }

        return !actState || actState == STATUS_TYPES.DRAFT;
    }, [actState, editabled, isCopy, pageType]);

    // 是否可追加
    const addToEnabled = useMemo(() => {
        if (!editabled) {
            return false;
        }
        if (isCopy) {
            return false;
        }
        return actState == STATUS_TYPES.NOSTART || actState == STATUS_TYPES.DOING;
    }, [actState]);

    useEffect(() => {
        if (JSON.stringify(putCpnList) != JSON.stringify(cpnList)) {
            changeCpnList(copyObjectCommon(putCpnList));
        }
    }, [putCpnList]);

    const setFormValues = (values) => {
        if (filePath?.length) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }

            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue({ superName: superObj, [name]: undefined });
        } else {
            form.setFieldsValue(values);
        }

        form.validateFields([filePath ? [...filePath, name] : name]);
    };

    // useEffect(() => {
    //     if (cpnList && cpnList instanceof Array) {
    //         const list =
    //             (cpnList &&
    //                 cpnList?.map((ele) => ({
    //                     ...ele,
    //                 }))) ||
    //             [];
    //         const preList = form.getFieldValue(filePath ? [...filePath, name] : name);
    //         console.log(
    //             7777,
    //             preList,
    //             list,
    //             !preList?.length || JSON.stringify(preList.sort()) != JSON.stringify(list.sort()),
    //         );
    //         if (!preList?.length || JSON.stringify(preList.sort()) != JSON.stringify(list.sort())) {
    //             // setFormValues({ [name]: list });
    //         }
    //     }
    // }, [cpnList]);

    const formmatList = useMemo(() => {
        // 格式化追加券
        const list = [];
        cpnList?.forEach &&
            cpnList?.forEach((cpn, index) => {
                const item = { ...cpn };

                if (item.replaceFlag == REPLACE_TYPE.OLD) {
                    // 替换逻辑，旧数据不进行展示
                    return;
                }

                let hasIndex = -1;
                for (let i = 0; i < list.length; i++) {
                    const element = list[i];
                    if (
                        element.pushPrizeId &&
                        item.pushPrizeId &&
                        element.pushPrizeId == item.pushPrizeId
                    ) {
                        hasIndex = i;
                        break;
                    }
                }
                item.key = index;
                if (hasIndex >= 0) {
                    item.isChildren = true;
                    if (!list[hasIndex].children) {
                        list[hasIndex].children = [item];
                    } else {
                        list[hasIndex].children.push(item);
                    }
                } else {
                    list.push(item);
                }
            });
        return list;
    }, [cpnList]);

    const countCpnNumber = useMemo(() => {
        let count = 0;
        cpnList?.forEach?.((element) => {
            if (element?.replaceFlag == REPLACE_TYPE.OLD) {
                return;
            }
            count += (element[putColDataIndex] && Number(element[putColDataIndex])) || 0;
        });
        return Math.round(count * 100) / 100;
    }, [cpnList]);

    // 券包数量
    const countCpnBagNumber = useMemo(() => {
        let count = undefined;
        cpnList?.forEach?.((element) => {
            if (element?.replaceFlag == REPLACE_TYPE.OLD) {
                return;
            }
            // 库存/发放=可发放天数
            const curCount =
                parseInt(Math.max(element['cpnNum'] && Number(element['cpnNum']), 1)) /
                parseInt(Math.max(element[putColDataIndex] && Number(element[putColDataIndex]), 1));
            if (count === undefined) {
                count = curCount;
            } else {
                // 取所有券包最小数量
                count = Math.min(count, curCount);
            }
        });
        return parseInt(count || 0);
    }, [cpnList]);

    const cpnTotal = useMemo(() => {
        let num = 0;
        cpnList?.forEach?.((item) =>
            !item.replaceFlag || item.replaceFlag == REPLACE_TYPE.NEW
                ? (num += parseInt(Math.max(item[putColDataIndex], 1)))
                : null,
        );
        return num || '-';
    }, [cpnList]);

    // 新增
    const addCpnEvent = () => {
        if (singleMode && cpnList.length >= 1) {
            message.error('只能添加一张优惠券');
            return;
        }
        addNewPrizeRef.current.add(cpnList);
    };

    //查看
    const lookCpnEvent = (item) => {
        history.push(`/marketing/couponCenter/coupon/list/look/${item.cpnId}`);
    };

    // 追加
    const pushCpnEvent = (item) => {
        addToPrizeRef.current.add(
            cpnList.map((ele) => ele.cpnId),
            actId,
            item,
        );
    };

    // 替换
    const replaceCpnEvent = (item) => {
        addNewPrizeRef.current.replace(cpnList, item);
    };

    // 还原
    const recoverCpnEvent = (item) => {
        // 删掉新的数据
        const newList = [...cpnList];
        newList.splice(
            newList.findIndex((ele) => ele.cpnId == item.cpnId),
            1,
        );
        // 旧的数据标识还原
        const oldCpn = newList.find((ele) => ele.cpnId == item.relatedId);
        oldCpn.replaceFlag = undefined;
        newList.splice(
            newList.findIndex((ele) => ele.cpnId == oldCpn.cpnId),
            1,
            oldCpn,
        );
        changeCpnListAndFormValue([...newList]);
    };

    const prizeColumns = [
        {
            title: '序号 ',
            width: 60,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '券名称 ',
            width: 180,
            dataIndex: 'cpnName',
            render(text, record) {
                return (
                    <span title={text}>{text?.length > 8 ? text.substr(0, 8) + '...' : text}</span>
                );
            },
        },
        {
            title: '券组编号 ',
            width: 140,
            dataIndex: 'cpnNo',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    if (pageType === 'signin') {
        prizeColumns.push({
            title: '剩余库存总数 ',
            width: 120,
            dataIndex: 'prizeNowNum',
            render(text, record) {
                let resultNum = record.prizeNowNum;
                if (isEmpty(resultNum)) {
                    resultNum = record.cpnNum;
                }
                return <span title={resultNum}>{resultNum}</span>;
            },
        });
    } else {
        prizeColumns.push({
            title: '库存总数 ',
            width: 120,
            dataIndex: 'cpnNum',
            render(text, record) {
                return <span title={text}>{text || record.unGetNum}</span>;
            },
        });
    }

    if (putNumCol !== undefined) {
        if (putNumCol) {
            prizeColumns.push(putNumCol);
        }
    } else {
        prizeColumns.push({
            title: putColTitle,
            width: 140,
            dataIndex: putColDataIndex,
            render(text, record, index) {
                return (
                    ((addNewEnabled || pageType == 'couponBagPage') &&
                        editabled &&
                        !singleMode && (
                            <InputNumber
                                min={1}
                                max={record.cpnNum || record.unGetNum}
                                step={1}
                                precision={0}
                                value={text}
                                onChange={(value) => {
                                    let cpn = { ...record };
                                    cpn[putColDataIndex] = value;
                                    const realIndex = cpnList.findIndex(
                                        (ele) => ele.cpnId == cpn.cpnId,
                                    );
                                    const newList = [...cpnList];
                                    newList.splice(realIndex, 1, cpn);
                                    changeCpnListAndFormValue([...newList]);
                                }}
                            />
                        )) || <span title={text}>{text}</span>
                );
            },
        });
    }

    if (pageType) {
        if (pageType == 'couponBagPage') {
            prizeColumns.push({
                title: '出资方',
                width: 120,
                dataIndex: 'contributPartyName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            });
        } else {
            prizeColumns.push({
                title: '券类型',
                width: 120,
                dataIndex: 'cpnOwnerName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            });
        }

        prizeColumns.push({
            title: '会员专属',
            width: 120,
            dataIndex: 'cpnOwnerName',
            render(text, record) {
                let result = record.vipCouponFlag === '1' ? '是' : '否';
                return <span title={result}>{result}</span>;
            },
        });
        prizeColumns.push({
            title: '可用范围',
            width: 160,
            dataIndex: 'scopeTypeName',
            render(text, record) {
                if (!text) {
                    text =
                        record.scopType ||
                        record.scopeName ||
                        record.scopeDesc ||
                        record.scopTypeName ||
                        record.scopeDesc ||
                        '-';
                    console.log('跟后端讲可用区域字段配不上！！！！！！！先借用其他字段');
                }
                return <span title={text}>{text}</span>;
            },
        });
    }

    prizeColumns.push({
        title: '发放有效期 ',
        width: 220,
        dataIndex: 'getDate',
        render(text, record) {
            if (!text?.length) {
                text = record.putTime || record.getEndDate || record.getTime;
            }
            return <span title={text}>{text}</span>;
        },
    });

    if (otherCol !== undefined && otherCol) {
        prizeColumns.push(otherCol);
    }

    prizeColumns.push({
        title: '操作',
        fixed: 'right',
        width: 140,
        render(text, record) {
            const editList = [];
            if (record.cpnId) {
                editList.push(
                    <Link
                        key={'01'}
                        to={`/marketing/couponCenter/coupon/list/look/${record.cpnId}`}
                        target="_blank"
                    >
                        查看
                    </Link>,
                );
            }
            //添加的还未提交的券也可以删除
            addNewEnabled &&
                editList.push(
                    <Popconfirm
                        title="要从礼包列表中删除此优惠券？"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => {
                            const newList = [...cpnList];
                            for (let index = cpnList.length - 1; index >= 0; index--) {
                                const element = cpnList[index];
                                if (element.cpnId == record.cpnId) {
                                    newList.splice(index, 1);
                                    if (record.isChildren) {
                                        // 因为根节点的cpnId和子节点的一致，所以要判断如果点的是根节点的删除按钮，则所有优惠券全部删掉，否则只删掉当前子节点
                                        break;
                                    }
                                }
                            }
                            changeCpnListAndFormValue([...newList]);
                            changeCouponCallback && changeCouponCallback([...newList]);
                        }}
                    >
                        <span className={commonStyles['table-btn']}>删除</span>
                    </Popconfirm>,
                );

            if (
                addToEnabled &&
                record.cpnOwner !== '04' &&
                pageType != 'aliCpn' &&
                (name != 'invitedCpnPrize' || record.cpnOwner == '03')
            ) {
                if (pageType == 'turnOver' && record.cpnOwner == '01') {
                    // 翻牌活动支付宝券不支持追加
                } else {
                    // 邀请有礼只支持平台券的追加
                    editList.push(
                        <span
                            key={'03'}
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                pushCpnEvent(record);
                            }}
                        >
                            追加
                        </span>,
                    );
                }
            }
            if (replaceEnabled) {
                if (!record.replaceFlag || record.replaceFlag == REPLACE_TYPE.OLD) {
                    editList.push(
                        <span
                            key={'04'}
                            className={commonStyles['table-btn']}
                            onClick={() => {
                                replaceCpnEvent(record);
                            }}
                        >
                            替换
                        </span>,
                    );
                } else if (record.replaceFlag == REPLACE_TYPE.NEW) {
                    if (record.relatedId) {
                        <Popconfirm
                            title="要还原成旧的优惠券？"
                            okText="确定"
                            cancelText="取消"
                            onConfirm={() => {
                                recoverCpnEvent(record);
                            }}
                        >
                            <span key={'04'} className={commonStyles['table-btn']}>
                                还原
                            </span>
                        </Popconfirm>;
                    } else {
                        editList.push(
                            <span
                                key={'04'}
                                className={commonStyles['table-btn']}
                                onClick={() => {
                                    recoverCpnEvent(record);
                                }}
                            >
                                复原
                            </span>,
                        );
                    }
                }
            }

            return <Space>{editList}</Space>;
        },
    });

    return (
        <Fragment>
            {(addNewEnabled && (
                <FormItem
                    name={filePath ? [filePath[filePath.length - 1], '_cpnList'] : '_cpnList'}
                    label={title}
                    required
                    {...formItemLayout}
                >
                    <Button type="primary" onClick={addCpnEvent}>
                        <PlusOutlined />
                        添加优惠券
                    </Button>
                </FormItem>
            )) ||
                null}
            {totalItem !== undefined ? totalItem : null}
            <FormItem
                name={filePath ? [filePath[filePath.length - 1], name] : name}
                rules={
                    rules === undefined
                        ? [
                              ({ getFieldValue }) => ({
                                  validator(rule, value) {
                                      if (needCheck && !formmatList?.length) {
                                          return Promise.reject(`请选择${title || ''}`);
                                      }
                                      const values = getFieldValue(
                                          filePath ? [...filePath, name] : name,
                                      );
                                      if (
                                          !values ||
                                          values.findIndex((ele) => ele.putNum == null) > -1
                                      ) {
                                          return Promise.reject(`${putColTitle}不能为空`);
                                      }
                                      return Promise.resolve();
                                  },
                              }),
                          ]
                        : rules
                }
                {...formItemLayout}
            >
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.cpnId}
                    dataSource={formmatList}
                    pagination={false}
                    expandable={{ defaultExpandAllRows: true }}
                    columns={prizeColumns}
                    noSort
                />
                <Space size="large">
                    <span style={{ color: 'blue' }}>单人发券总数： {countCpnNumber}</span>
                    {pageType == 'couponBagPage' && (
                        <span style={{ color: 'blue' }}>券包数量： {countCpnBagNumber}</span>
                    )}
                </Space>
            </FormItem>

            <GroupPageModal
                {...props}
                initRef={addNewPrizeRef}
                onAddFinish={(_cpnList) => {
                    let newList = [];
                    if (changeCouponCallback) {
                        newList = changeCouponCallback(_cpnList);
                    } else {
                        // 如果原先有填数量，以原先的数量为准
                        newList = _cpnList.map((cpn) => {
                            let cpnItem = { ...cpn };
                            let oldCpns = cpnList?.filter?.((x) => x.cpnId == cpnItem.cpnId);
                            let oldCpn = oldCpns?.length && oldCpns?.[0];
                            if (oldCpn && oldCpn?.[putColDataIndex]) {
                                cpnItem[putColDataIndex] = oldCpn[putColDataIndex];
                            }
                            if (parseInt(cpnItem?.[putColDataIndex]) < 1 || !oldCpn) {
                                cpnItem[putColDataIndex] = 1;
                            }
                            if (oldCpn?.pushPrizeId) {
                                cpnItem.pushPrizeId = oldCpn.pushPrizeId;
                            }
                            // if (!cpn?.pushPrizeId && !oldCpn?.invitePrizeRate) {
                            //     cpn.invitePrizeRate = 100;
                            // }
                            // console.log(555555555, cpnItem, oldCpn, oldCpn?.invitePrizeRate);
                            if (oldCpn?.invitePrizeRate) {
                                cpnItem.invitePrizeRate = oldCpn.invitePrizeRate;
                            } else {
                                cpnItem.invitePrizeRate = 100;
                            }

                            return cpnItem;
                        });
                    }
                    // console.log(444444, newList);
                    changeCpnListAndFormValue(newList);

                    if (filePath instanceof Array && filePath[0]) {
                        form.validateFields([filePath[0]]);
                    }
                }}
                onReplaceFinish={(_cpnList) => {
                    // 定义第0个为旧券，第1个为新券
                    // 如果原先有填数量，以原先的数量为准
                    let newList = _cpnList.map((cpn, index) => {
                        let cpnItem = { ...cpn };
                        let oldCpns = cpnList.filter((x) => x.cpnId == cpnItem.cpnId);
                        let oldCpn = oldCpns?.length && oldCpns[0];
                        if (oldCpn && oldCpn[putColDataIndex]) {
                            cpnItem[putColDataIndex] = oldCpn[putColDataIndex];
                        }
                        if (parseInt(cpnItem[putColDataIndex]) < 1 || !oldCpn) {
                            cpnItem[putColDataIndex] = 1;
                        }
                        if (oldCpn?.pushPrizeId) {
                            cpnItem.pushPrizeId = oldCpn.pushPrizeId;
                        }
                        // if (!cpn?.pushPrizeId && !oldCpn?.invitePrizeRate) {
                        //     cpn.invitePrizeRate = 100;
                        // }
                        // console.log(555555555, cpnItem, oldCpn, oldCpn?.invitePrizeRate);
                        if (oldCpn?.invitePrizeRate) {
                            cpnItem.invitePrizeRate = oldCpn.invitePrizeRate;
                        } else {
                            cpnItem.invitePrizeRate = 100;
                        }
                        if (index == 0) {
                            cpnItem.replaceFlag = REPLACE_TYPE.OLD;
                        } else if (index == 1) {
                            cpnItem.replaceFlag = REPLACE_TYPE.NEW;
                            cpnItem.editabled = true;
                            cpnItem.relatedId = _cpnList[0].cpnId;
                        }
                        return cpnItem;
                    });

                    // 替换第0个所在旧列表的位置
                    const oldCpn = newList[0];
                    const oldCpnIndex = cpnList.findIndex((ele) => ele.cpnId == oldCpn.cpnId);
                    const list = [...cpnList];
                    list.splice(oldCpnIndex, 1, ...newList);
                    changeCpnListAndFormValue([...list]);
                }}
                extInParams={extInParams}
                pageType={pageType}
            />

            <AddPrizeViewModal
                {...props}
                initRef={addToPrizeRef}
                onAddFinish={(cpn) => {
                    if (cpn) {
                        changeCpnListAndFormValue([...cpnList, cpn]);
                    } else {
                        // 追加
                        onRefresh && onRefresh();
                    }
                }}
                onEditFinish={(cpn) => {
                    if (cpn) {
                        // 编辑
                        for (let index = 0; index < cpnList.length; index++) {
                            const element = cpnList[index];
                            if (element.cpnId == cpn.cpnId) {
                                cpnList[index] = cpn;
                                break;
                            }
                        }
                        changeCpnListAndFormValue([...cpnList]);
                    } else {
                    }
                }}
                extInParams={extInParams}
            />
        </Fragment>
    );
};

// CouponTable.propTypes = {
//     dispatch: PropTypes.func.isRequired,
//     global: PropTypes.object.isRequired,
//     couponModel: PropTypes.object.isRequired,
// };

export default forwardRef(CouponTable);
