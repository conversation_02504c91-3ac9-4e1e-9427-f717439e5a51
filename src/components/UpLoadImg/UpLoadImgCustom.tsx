import React, { useState, useImperativeHandle, useMemo, forwardRef } from 'react';
import type { Ref, ReactNode } from 'react';
import { Upload, message, Button, Space } from 'antd';
import { PlusOutlined, LoadingOutlined, UploadOutlined } from '@ant-design/icons';

import { uploadFile } from '@/services/CommonApi';
import Thumbnail from '@/components/ShowPicture/Thumbnail';
import styles from './index.less';

interface FileInfo {
    uid: string;
    url: string;
    status?: any;
}
interface UploadProps {
    initialValue?: FileInfo[];
    onChange?: (values: any) => void;
    filePath?: string; // 如果是嵌套from，由此路径赋值
    disabled?: boolean;
    uploadData: Record<string, any>;
    relaId?: string;
    returnId?: boolean;
    returnRelativePath?: boolean;
    returnOriginData?: boolean;
    sizeInfo: {
        size: number;
        width?: number;
        height?: number;
        suggest?: boolean;
    };
    showPlaceholder?: boolean; // 控制底部提示语显隐
    showUploderText?: boolean; // 控制添加按钮上传文字显隐
    placeholder?: string;
    multiple?: boolean;
    maxCount?: number;
    listType?: string;
    //自定义上传按钮，只有listType！=picture-card的时候生效
    uploadButton?: ReactNode;
    otherParams?: Record<string, any>;
}

// 上传图片按钮
const UploadButton = ({
    uploading,
    showUploderText,
}: {
    uploading: boolean;
    showUploderText: boolean;
}) => (
    <div>
        {uploading ? <LoadingOutlined /> : <PlusOutlined />}
        {(showUploderText && <div className="ant-upload-text">上传</div>) || null}
    </div>
);

const UpLoadImgItem = (props: UploadProps, ref: Ref<any>) => {
    const {
        initialValue,
        onChange,
        filePath, // 如果是嵌套from，由此路径赋值
        disabled,
        uploadData,
        relaId,
        returnId = false,
        returnOriginData = false, //返回接口原始对象，仅multiple=true支持
        returnRelativePath = false, // 返回相对路径，仅multiple=true支持
        sizeInfo = {
            size: 1024,
        },
        showPlaceholder = true, // 控制底部提示语显隐
        showUploderText = true, // 控制添加按钮上传文字显隐
        placeholder,
        multiple = false,
        maxCount = 5,
        listType = 'picture-card',
        uploadButton,
        ...otherParams
    } = props;

    const getOnChangeData = (imageData: any) => {
        if (Array.isArray(imageData)) {
            return imageData.map((ele: any) => {
                if (returnOriginData) {
                    return { ...ele };
                }
                if (returnRelativePath) {
                    return ele.relativePath;
                }
                if (returnId) {
                    return ele?.uid;
                }
                return ele.url;
            });
        } else {
            return returnOriginData
                ? imageData
                : returnId
                ? imageData?.uid
                : returnRelativePath
                ? imageData?.relativePath
                : imageData?.url;
        }
    };

    const [imageUrl, changeImageUrl] = useState<string | undefined>(
        multiple ? undefined : initialValue?.[0]?.url,
    ); // 上传文件
    const [imageList, updateImageList] = useState<FileInfo[]>(initialValue || []);
    const [uploading, changeUploading] = useState<boolean>(false); // 上传加载

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {},
        init: (file: FileInfo | FileInfo[]) => {
            if (file instanceof Array) {
                for (const item of file as FileInfo[]) {
                    handleUploadChange(item);
                    updateImageList([...imageList, { ...item, status: 'done' }]);
                }
            } else {
                changeImageUrl(file?.url);
                updateImageList([...imageList, { ...file, status: 'done' }]);
            }
        },
    }));

    const tipName = '图片';

    const handleUploadChange = (file: FileInfo) => {
        changeImageUrl(file?.url);
        if (multiple) {
            const newImages = [...imageList, { ...file, status: 'done' }];
            if (maxCount) {
                newImages.slice(-maxCount);
            }
            onChange && onChange(getOnChangeData(newImages));
            updateImageList([...newImages]);
        } else {
            onChange && onChange(getOnChangeData(file));
        }
    };
    const handleRemoveEvent = (file: FileInfo) => {
        const list = imageList.filter((ele) => ele.uid !== file.uid);
        updateImageList(list);
        onChange && onChange(getOnChangeData(list));
    };

    const beforeUpload = (file: any) =>
        new Promise((res, rej) => {
            const isJpgOrPng =
                file.type === 'image/jpg' ||
                file.type === 'image/png' ||
                file.type === 'image/jpeg' ||
                file.type === 'image/gif';
            if (!isJpgOrPng) {
                message.error(`${tipName}格式错误!`);
                rej(false);
            }
            const isLessThen = file.size / 1024 < sizeInfo.size;
            if (!isLessThen) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}MB`;
                } else {
                    sizeNum = `${sizeInfo.size}KB`;
                }
                message.error(`${tipName}不大于 ${sizeNum}!`);
                rej(false);
            }

            if (sizeInfo.width || sizeInfo.height) {
                const reader = new FileReader();
                reader.onload = function (e: any) {
                    const data = e.target.result;
                    const image = new Image();
                    image.onload = function () {
                        const { width } = image;
                        const { height } = image;
                        let isAllow = true;
                        let errorMessage = '';

                        // 独立校验宽度
                        if (sizeInfo.width && width !== sizeInfo.width) {
                            isAllow = false;
                            errorMessage += `宽度必须为${sizeInfo.width},`;
                        }

                        // 独立校验高度
                        if (sizeInfo.height && height !== sizeInfo.height) {
                            isAllow = false;
                            errorMessage += `高度必须为${sizeInfo.height},`;
                        }

                        // 如果配置了suggest，则允许不匹配
                        if (sizeInfo.suggest) {
                            isAllow = true;
                        }

                        if (!isAllow && !sizeInfo.suggest) {
                            message.error(errorMessage.slice(0, -1));
                        }

                        if (isJpgOrPng && isAllow) {
                            res(true);
                        } else {
                            rej(false);
                        }
                    };
                    image.src = data;
                };
                reader.readAsDataURL(file);
            } else {
                res(true);
            }
        });

    const uploadProps: any = {
        name: 'avatar',
        className: styles['avatar-uploader'],
        showUploadList: multiple ? true : false,
        data: uploadData,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            file,
            onSuccess,
        }: any) => {
            try {
                const params: any = {
                    ...uploadData,
                };

                if (relaId) {
                    params.relaId = relaId;
                }
                const formData = new FormData();
                if (params) {
                    Object.keys(params).forEach((key) => {
                        formData.append(key, params[key]);
                    });
                }
                formData.append(uploadData?.contRemrk, file);

                changeUploading(true);
                const { data } = await uploadFile(formData);
                onSuccess({
                    url: data?.filePath,
                    uid: data?.fileId,
                    relativePath: data?.relativePath,
                });
            } catch (error) {
            } finally {
                changeUploading(false);
            }

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType,
        onSuccess: handleUploadChange,
        beforeUpload,
        maxCount,
        multiple,
    };

    const placeholderMessage = useMemo(() => {
        if (placeholder) {
            return placeholder;
        }
        let str = '';

        if (sizeInfo.width && sizeInfo.height) {
            if (sizeInfo.suggest) {
                str += `建议尺寸${sizeInfo.width}*${sizeInfo.height},不限制尺寸上传,`;
            } else {
                str += `尺寸为${sizeInfo.width}*${sizeInfo.height},`;
            }
        }
        let sizeNum = '';
        const sizeRadio = Math.floor(sizeInfo.size / 1024);
        if (sizeRadio >= 1) {
            sizeNum = `${sizeRadio}MB`;
        } else {
            sizeNum = `${sizeInfo.size}kb`;
        }
        str += `格式支持png、jpg、jpeg,大小不得超过${sizeNum}`;
        return str;
    }, [placeholder, sizeInfo]);

    const uploadRender = useMemo(() => {
        if (multiple) {
            if (imageList?.length >= maxCount || disabled) {
                return null;
            }
            return <UploadButton uploading={uploading} showUploderText={showUploderText} />;
        } else {
            if (imageUrl) {
                return (
                    <Thumbnail
                        url={imageUrl}
                        disabled={disabled}
                        delEvent={() => {
                            onChange && onChange(null);
                            changeImageUrl(undefined);
                        }}
                    />
                );
            } else if (listType !== 'picture-card' && !disabled) {
                return uploadButton || <Button icon={<UploadOutlined />}>上传</Button>;
            } else if (!disabled) {
                return <UploadButton uploading={uploading} showUploderText={showUploderText} />;
            }
        }
        return null;
    }, [multiple, imageUrl, imageList, disabled, uploading, uploadButton]);

    return (
        <Space direction="vertical">
            <Upload
                {...uploadProps}
                disabled={disabled}
                fileList={(multiple && imageList) || null}
                onRemove={handleRemoveEvent}
                {...otherParams}
            >
                {uploadRender}
            </Upload>
            {(showPlaceholder && (
                <span className={styles['mark-text']}>{placeholderMessage}</span>
            )) ||
                null}
        </Space>
    );
};

export default forwardRef(UpLoadImgItem);
