import { Fragment, useEffect, useState, useImperativeHandle, useMemo, forwardRef } from 'react';
import { Form, Upload, message, Button, Space } from 'antd';

import { PlusOutlined, LoadingOutlined, UploadOutlined } from '@ant-design/icons';

import { uploadFile } from '@/services/CommonApi';
import Thumbnail from '@/components/ShowPicture/Thumbnail';
import styles from './index.less';

const FormItem = Form.Item;

// 上传图片按钮
const UploadButton = ({ uploading }) => (
    <div>
        {uploading ? <LoadingOutlined /> : <PlusOutlined />}
        <div className="ant-upload-text">上传</div>
    </div>
);

const UpLoadImg = (props, ref) => {
    const {
        formItemLayout,
        form,
        label = '背景图片',
        labelName, // 提示错误的描述文字
        name = 'iconUrl',
        filePath, // 如果是嵌套from，由此路径赋值
        disabled,
        rules,
        uploadData,
        relaId,
        sizeInfo = {
            size: 1024,
        },
        placeholder,
        required,
        multiple = false,
        maxCount = 5,
        isBar,
        acceptType = ['png', 'jpg', 'jpeg'], // 代码只支持png jpg jpeg gif格式，如果有扩展需求，开发自己改判断
        ...otherParams
    } = props;

    const [imageUrl, changeImageUrl] = useState(); // 上传文件
    const [imageList, updateImageList] = useState([]);
    const [uploading, changeUploading] = useState(false); // 上传加载

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        imageList,
        rest: () => {
            changeImageUrl();
            updateImageList([]);
        },
        init: (url) => {
            if (multiple) {
                for (const item of url) {
                    handleUploadChange(item);
                    updateImageList([...imageList, { url: item, uid: item, status: 'done' }]);
                }
            } else {
                changeImageUrl(url);
                updateImageList([...imageList, { url: url, uid: url, status: 'done' }]);
            }
        },
    }));

    const tipName = useMemo(() => {
        if (typeof label == 'string') {
            return label;
        }
        if (labelName) {
            return labelName;
        }
        return '图片';
    }, [label, labelName]);

    const setFormValues = (values) => {
        if (filePath?.length) {
            let index = 0;
            const superName = filePath[index];
            let superObj = form.getFieldValue(superName);
            let obj;
            while (++index < filePath.length) {
                if (!obj) {
                    obj = superObj[filePath[index]];
                } else {
                    obj = obj[filePath[index]];
                }
            }
            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue({ [superName]: superObj });
        } else {
            const obj = form.getFieldsValue();
            Object.keys(values)?.forEach((key) => {
                obj[key] = values[key];
            });
            form.setFieldsValue(values);
        }
    };

    const handleUploadChange = (imgUrl) => {
        updateImageList([...imageList, { url: imgUrl, uid: imgUrl, status: 'done' }]);
        if (multiple) {
            setFormValues({
                [name]: imageList.map((ele) => ele.url),
            });
        } else {
            changeImageUrl(imgUrl);
            setFormValues({ [name]: imgUrl });
        }
    };

    const handleRemoveEvent = (file) => {
        let list = imageList.filter((ele) => ele.uid !== file.uid);
        updateImageList(list);
        setFormValues({ [name]: list.map((ele) => ele.url) });
    };

    const beforeUpload = (file) =>
        new Promise((res, rej) => {
            let isTypeAllow = false;
            acceptType.map((ele) => {
                if (file.type === `image/${ele}`) {
                    isTypeAllow = true;
                }
            });
            if (!isTypeAllow) {
                message.error(`${tipName}格式错误!`);
                rej();
                return;
            }
            const isLt2M = file.size / 1024 < sizeInfo.size;
            if (!isLt2M) {
                let sizeNum = '';
                const sizeRadio = Math.floor(sizeInfo.size / 1024);
                if (sizeRadio >= 1) {
                    sizeNum = `${sizeRadio}mb`;
                } else {
                    sizeNum = `${sizeInfo.size}kb`;
                }
                message.error(`${tipName}不大于 ${sizeNum}!`);
                rej();
                return;
            }

            if (sizeInfo.width && sizeInfo.height) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const data = e.target.result;
                    // 加载图片获取图片真实宽度和高度
                    const image = new Image();
                    image.onload = function () {
                        const { width } = image;
                        const { height } = image;
                        const isAllow =
                            (width == sizeInfo.width && height == sizeInfo.height) ||
                            sizeInfo.suggest;
                        if (!isAllow) {
                            message.error(
                                `${tipName}尺寸必须为${sizeInfo.width}*${sizeInfo.height}`,
                            );
                        }
                        if (isTypeAllow && isLt2M && isAllow) {
                            res();
                        } else {
                            rej();
                        }
                    };
                    image.src = data;
                };
                reader.readAsDataURL(file);
            } else {
                res();
            }
        });

    const uploadProps = {
        name: 'avatar',
        className: styles['avatar-uploader'],
        showUploadList: multiple ? true : false,
        // action: '/oper/uploadCertImage',
        data: uploadData,
        customRequest: async ({
            // 自定义上传实现 https://github.com/react-component/upload/blob/master/examples/customRequest.js
            action,
            data,
            file,
            filename,
            headers,
            onError,
            onProgress,
            onSuccess,
            withCredentials,
        }) => {
            try {
                const params = {
                    ...data,
                };

                if (relaId) {
                    params.relaId = relaId;
                }
                const formData = new FormData();
                if (params) {
                    Object.keys(params).forEach((key) => {
                        formData.append(key, params[key]);
                    });
                }
                formData.append(data.contRemrk, file);

                changeUploading(true);
                const {
                    data: { filePath, fileId },
                } = await uploadFile(formData);
                onSuccess(filePath);
            } catch (error) {
            } finally {
                changeUploading(false);
            }

            return {
                abort() {
                    console.log('upload progress is aborted.');
                },
            };
        },
        listType: isBar ? undefined : 'picture-card',
        onSuccess: handleUploadChange,
        // onChange: handleUploadChange,
        beforeUpload,
    };

    const placeholderMessage = useMemo(() => {
        if (placeholder) {
            return placeholder;
        }
        let str = '';
        if (sizeInfo.width && sizeInfo.height && sizeInfo.suggest) {
            str += `建议尺寸${sizeInfo.width}*${sizeInfo.height}，不限制尺寸上传，`;
        } else if (sizeInfo.width && sizeInfo.height) {
            str += `尺寸为${sizeInfo.width}*${sizeInfo.height}，`;
        }
        let sizeNum = '';
        const sizeRadio = Math.floor(sizeInfo.size / 1024);
        if (sizeRadio >= 1) {
            sizeNum = `${sizeRadio}mb`;
        } else {
            sizeNum = `${sizeInfo.size}kb`;
        }
        str += `格式支持${acceptType?.join?.('、') || 'png、jpg、jpeg'}，大小不得超过${sizeNum}`;
        return str;
    }, [placeholder, sizeInfo, acceptType]);

    const uploadRender = useMemo(() => {
        if (multiple) {
            return (
                <Fragment>
                    {/* {imageList?.length &&
                        imageList.map((ele, index) => {
                            return (
                                <Thumbnail
                                    key={index}
                                    url={ele}
                                    disabled={disabled}
                                    delEvent={() => {}}
                                />
                            );
                        })} */}
                    {imageList?.length < maxCount && <UploadButton uploading={uploading} />}
                </Fragment>
            );
        } else {
            if (imageUrl) {
                return (
                    <Thumbnail
                        url={imageUrl}
                        disabled={disabled}
                        delEvent={() => {
                            setFormValues({ [name]: null });
                            changeImageUrl(undefined);
                        }}
                    />
                );
            } else {
                return <UploadButton uploading={uploading} />;
            }
        }
    }, [multiple, imageUrl, imageList, disabled, uploading]);

    return (
        <FormItem
            label={label}
            name={filePath ? undefined : name}
            {...formItemLayout}
            rules={rules}
            required={required}
            {...otherParams}
        >
            <Space direction="vertical">
                <FormItem noStyle>
                    <Upload
                        {...uploadProps}
                        disabled={disabled}
                        fileList={(multiple && imageList) || null}
                        onRemove={handleRemoveEvent}
                    >
                        {imageUrl ? (
                            <Thumbnail
                                url={imageUrl}
                                disabled={disabled}
                                delEvent={() => {
                                    form.setFieldsValue({ [name]: null });
                                    changeImageUrl(null);
                                }}
                                isBar={isBar}
                            />
                        ) : isBar ? (
                            <Button icon={<UploadOutlined />}>上传</Button>
                        ) : (
                            !disabled && <UploadButton uploading={uploading} />
                        )}
                    </Upload>
                </FormItem>
                {disabled ? undefined : (
                    <span className={styles['mark-text']}>{placeholderMessage}</span>
                )}
            </Space>
        </FormItem>
    );
};

export default forwardRef(UpLoadImg);
