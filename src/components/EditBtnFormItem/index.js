import React, { Fragment, useEffect, useState } from 'react';
import { Form, Space } from 'antd';
import { EditOutlined } from '@ant-design/icons';
const FormItem = Form.Item;
const EditBtnFormItem = (props) => {
    const {
        label,
        defaultValue,
        children,
        childProps = {},
        clickEvent,
        disabled,
        ...otherParams
    } = props;
    const [onlyRead, changeOnlyRead] = useState(true);
    const touchIconEvent = () => {
        if (clickEvent) {
            clickEvent();
        } else {
            changeOnlyRead(!onlyRead);
        }
    };

    return (
        <FormItem label={label} {...otherParams}>
            <Space>
                {/* {onlyRead ? defaultValue : React.cloneElement(children, childProps)} */}
                {onlyRead && (defaultValue || <span style={{ color: 'lightgray' }}>未设置</span>)}
                <div style={{ display: onlyRead ? 'none' : 'inline-block' }}>
                    {children && React.cloneElement(children, childProps)}
                </div>
                {(!disabled && <EditOutlined onClick={touchIconEvent} />) || null}
            </Space>
        </FormItem>
    );
};

export default EditBtnFormItem;
