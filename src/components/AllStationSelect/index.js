import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Form, Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { getStationListApi } from '@/services/CommonApi';
import { Fragment } from 'react';
import { isEmpty } from 'lodash';

const FormItem = Form.Item;
const { Option } = Select;

const AllStationSelect = (props, ref) => {
    const {
        name = 'stationId',
        form,
        operId,
        label = '充电站:',
        mode = undefined,
        disabled = false,
        onChange,
        cityList = [],
    } = props;
    const [fetching, changeFetching] = useState(false);
    const [stationList, changeStationList] = useState([]);

    const serchStation = async (stationName = '') => {
        try {
            const params = {
                stationName,
            };
            if (!isEmpty(cityList)) {
                params.cityList = cityList.join(',');
            }
            if (operId) {
                if (operId instanceof Array) {
                    params.operIds = operId.join(',');
                } else {
                    params.operId = operId;
                }
            }
            if (!stationName && !params.operId) {
                return;
            }
            changeFetching(true);
            const {
                data: { stationList: list },
            } = await getStationListApi(params);
            changeStationList(list);
        } catch (error) {
        } finally {
            changeFetching(false);
        }
    };
    const fetchStation = debounce(serchStation, 800);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            form?.setFieldValue([name], undefined);
            changeStationList([]);
        },
        init: (list = []) => {
            // 带着默认值的初始化，把场站条件也带进来
            changeStationList([...list]);
        },
        fetchStation,
    }));

    useEffect(() => {
        const nameValue = form?.getFieldValue('stationName');
        if (nameValue) {
            serchStation(nameValue);
        }
    }, []);

    return (
        <Fragment>
            <FormItem label={label} name={name} {...props}>
                <Select
                    showSearch
                    placeholder="请选择"
                    loading={fetching}
                    notFoundContent={fetching ? <Spin size="small" /> : null}
                    filterOption={false}
                    onSearch={fetchStation}
                    onFocus={() => {
                        fetchStation('');
                    }}
                    mode={mode}
                    allowClear
                    onSelect={(e, option) => {
                        form &&
                            typeof option?.children == 'string' &&
                            form?.setFieldsValue({ stationName: option?.children });
                        onChange?.(option);
                    }}
                    onChange={(e) => {
                        if (!e) {
                            form?.setFieldsValue({ stationName: undefined });
                        }
                    }}
                    disabled={disabled}
                >
                    {stationList.map((d) => (
                        <Option key={d.stationId} value={d.stationId}>
                            {d.stationName}
                        </Option>
                    ))}
                </Select>
            </FormItem>
            <FormItem noStyle name="stationName"></FormItem>
        </Fragment>
    );
};
export default forwardRef(AllStationSelect);
