import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { getStationListApi } from '@/services/CommonApi';
import { Fragment } from 'react';
import { isEmpty } from 'lodash';

import { useRequest } from 'ahooks';

const { Option } = Select;
type ValueTypes = {
    stationId: string;
    stationName: string;
};
const AllStationSelectItem = (
    props: API.CommonFormItem<ValueTypes | undefined> & {
        operId?: string | string[];
        cityList?: string[];
        defStationName?: string;
        mode?: 'multiple' | 'tags';
        showOperName?: boolean;
        showDiscardFlag?: boolean;
    },
    ref: any,
) => {
    const {
        operId,
        value,
        mode = undefined,
        disabled = false,
        onChange,
        cityList = [],
        defStationName = '',
        showOperName = false,
        showDiscardFlag = false, // 是否显示废弃场站
    } = props;

    useEffect(() => {
        if (defStationName) {
            serchStation(defStationName);
        }
    }, []);

    const {
        run: serchStation,
        data: stationList = [],
        loading: fetching,
        mutate,
    } = useRequest(
        async (stationName = '') => {
            try {
                const params = {
                    searchKey: stationName,
                };
                if (!isEmpty(cityList)) {
                    params.cityList = cityList.join(',');
                }
                if (operId) {
                    if (operId instanceof Array) {
                        params.operIds = operId.join(',');
                    } else {
                        params.operId = operId;
                    }
                }
                if (showDiscardFlag) {
                    params.discardFlag = '1';
                }
                if (!stationName && !params.operId) {
                    return;
                }

                const {
                    data: { stationList: list },
                } = await getStationListApi(params);
                return list;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        { manual: true },
    );

    const fetchStation = debounce(serchStation, 800);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        rest: () => {
            mutate([]);
        },
        init: (list = []) => {
            // 带着默认值的初始化，把场站条件也带进来
            mutate([...list]);
        },
        fetchStation,
    }));

    const updateFormItem = (newVal: ValueTypes) => {
        onChange && onChange(newVal);
    };

    return (
        <Fragment>
            <Select
                value={value?.stationId}
                showSearch
                placeholder="请选择"
                loading={fetching}
                notFoundContent={fetching ? <Spin size="small" /> : null}
                filterOption={false}
                onSearch={fetchStation}
                onFocus={() => {
                    fetchStation('');
                }}
                mode={mode}
                allowClear
                onSelect={(e, option) => {
                    const { value: newVal, children: newName } = option;
                    updateFormItem?.({
                        stationId: newVal,
                        stationName: newName,
                    });
                }}
                // onChange={(e) => {
                //     updateFormItem?.(e);
                // }}
                disabled={disabled}
            >
                {stationList?.map((d) => (
                    <Option key={d.stationId} value={d.stationId}>
                        {showOperName ? `${d.buildShortName}-` : ''}
                        {d.stationName}
                    </Option>
                ))}
            </Select>
        </Fragment>
    );
};
export default forwardRef(AllStationSelectItem);
