/**
 * 根据运营商查询对应城市下的站点
 */

import {
    Fragment,
    useEffect,
    useRef,
    useState,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { Form, Select, TreeSelect, Button, Row, Col, Spin } from 'antd';
import { connect } from 'dva';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import { getCityListApi, getOperAndStationByCityApi } from '@/services/CommonApi';
import { SELECT_TYPES } from '@/config/declare';
import { findValueInTreeCommon } from '@/utils/function';

const FormItem = Form.Item;
const { Option } = Select;

const { SHOW_CHILD } = TreeSelect;

const CityTreeSelectList = (props) => {
    const {
        form,
        dispatch,
        formItemLayout,
        initRef,
        label = '活动区域',
        name = 'opertorList',
        disabled,
        // stationsListInfo,
        rules,
        currentUser,
        purchase,
        global: { operatorList = [] },
        limitOperIds,
        limitCitys,
        exceptOperIds, // 限制哪几个运营商不可选中，打折立减的临时解决方案，只需传operId数组
        openFlag,
        premiumFlag,
        channelId,
        cooperationPlatform,
    } = props;

    const [cityList, updateCityList] = useState([]);

    const [stationList, changeStationList] = useState([]);
    const [allStations, updateAllStations] = useState([]); // 同上，收集场站信息
    const [stationLoading, toggleStationLoading] = useState(false);

    useEffect(() => {
        if (operatorList.length == 0) {
            dispatch({
                type: 'global/getOperatorList',
                options: {},
            });
        }
    }, []);

    const [loading, toggleLoading] = useState(false);

    useEffect(() => {
        initEvent();
    }, [channelId, cooperationPlatform]);

    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        init: (list) => {
            try {
                for (let index = 0; index < list.length; index++) {
                    const element = list[index];
                    changeCityEvent(element.city, index);
                }
            } catch (error) {
                console.log(88888888888, error);
            }
        },
        reset: () => {
            // 页面重置，用于modal弹出后所有内容清空
            form.setFieldsValue({ [name]: undefined });
        },
        formatter: () => {
            const promise = new Promise((resolve, reject) => {
                const formatterList = [];
                const formItemData = form.getFieldValue(name);
                formItemData instanceof Array &&
                    formItemData.forEach((ele, index) => {
                        ele?.stationId?.forEach((stationId) => {
                            const operItem = stationFormatStore?.find(
                                (operSubItem) => operSubItem.stationId == stationId,
                            );
                            formatterList.push(operItem);
                        });
                    });
                return resolve(formatterList);
            });
            return promise;
        },
    }));

    const initEvent = async () => {
        toggleLoading(true);
        try {
            // form.setFieldsValue({ [name]: list });
            const { data: areaList } = await getCityListApi({
                channelId,
                cooperationPlatform,
                cityStr: limitCitys || undefined,
            });

            const list = [];
            for (const item of areaList) {
                for (const city of item.cityList || []) {
                    list.push({
                        title: city.areaName,
                        value: city.areaCode,
                        key: city.areaCode,
                    });
                }
            }
            updateCityList(list);
            toggleLoading(false);
            return list;
        } catch (error) {
            toggleLoading(false);
            throw new Error(error);
        } finally {
            toggleLoading(false);
        }
    };

    // 存储格式化后的数据，最后确定的时候直接从这个对象里面查选中的站点信息
    const [stationFormatStore, updateStationFormatStore] = useState([]);

    const changeCityEvent = async (value, index) => {
        if (!value) {
            return;
        }
        try {
            toggleStationLoading(true);
            // 清空选中城市
            if (currentUser.operId != value) {
                const formItemData = form.getFieldValue(name);
                formItemData[index].stationId = [];
                form.setFieldsValue({
                    [name]: formItemData,
                });
            }

            const formItemData = form.getFieldValue(name);
            let selectStationList = [];
            for (const item of formItemData) {
                if (item?.operId == value && item?.stationId) {
                    selectStationList = [...new Set([...selectStationList, ...item.stationId])];
                }
            }

            const { data: cityAndStationList } = await getOperAndStationByCityApi({
                city: value,
                openFlag: (openFlag && 1) || undefined,
                premiumFlag: (premiumFlag && 1) || undefined,
                channelId,
                cooperationPlatform,
                cityStr: limitCitys || undefined,
            });

            const list = [];
            const formatData = [];

            const cityItem = cityList.find((operSubItem) => operSubItem.value == value);
            cityAndStationList.forEach((ele, eleIndex) => {
                if (ele.children) {
                    const childList = [];
                    ele.children.forEach((child, childIndex) => {
                        if (purchase == SELECT_TYPES.ONLYBUY && child.cooperationType !== '02') {
                            // 仅支持选择购电模式
                            return;
                        }
                        if (purchase == SELECT_TYPES.EXCEPTBUY && child.cooperationType === '02') {
                            // 不允许选择购电模式
                            return;
                        }
                        if (exceptOperIds?.includes(ele.value)) {
                            // 不允许选择的运营商
                            return;
                        }
                        if (!selectStationList.includes(child.value)) {
                            childList.push({ ...child });
                        }
                    });

                    if (childList.length > 0) {
                        if (
                            (limitOperIds instanceof Array && limitOperIds.includes(ele.value)) ||
                            !limitOperIds
                        ) {
                            list.push({
                                title: ele.title,
                                value: ele.value,
                                key: ele.value,

                                children: childList,
                            });
                        }
                    }

                    childList?.forEach((child) => {
                        if (child) {
                            formatData.push({
                                cityName: cityItem.title,
                                city: cityItem.value,
                                buildName: ele.title,
                                buildId: ele.value,
                                cooperationType:
                                    child.cooperationType ||
                                    operatorList.find(
                                        (operSubItem) =>
                                            (operSubItem.operId ||
                                                operSubItem.buildId ||
                                                operSubItem.operatorId) == ele.value,
                                    )?.cooperationType,
                                stationName: child.title,
                                stationId: child.value,
                                firstOpenTime: child.firstOpenTime,
                            });
                        }
                    });
                }
            });
            updateStationFormatStore([...stationFormatStore, ...formatData]);
            changeStationList((oldList) => {
                oldList[index] = list?.length && [
                    {
                        title: '全部',
                        key: 'all',
                        value: 'all',
                        children: list,
                    },
                ];
                return oldList;
            });
        } catch (error) {
            console.log(error);
        } finally {
            toggleStationLoading(false);
        }

        // try {
        //     // 清空选中站点
        //     if (currentUser?.operId != value) {
        //         const formItemData = form.getFieldValue(name);
        //         formItemData[index].stationId = [];
        //         form.setFieldsValue({
        //             [name]: formItemData,
        //         });
        //     }
        // } catch (error) {
        //     console.log(4444, error);
        // } finally {
        // }
    };

    const treeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 3,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 3}个</span>,
        // disabled,
        style: {
            width: '100%',
        },
    };

    const canIadd = () => {
        const hasAdd = true;

        return !disabled && hasAdd;
    };

    return (
        <Form.Item name={name} {...formItemLayout} label={label} rules={rules}>
            <Form.List name={name}>
                {(fields, { add, remove }) => (
                    <Fragment>
                        {fields.map((field, index) => (
                            <Fragment key={index}>
                                <Row>
                                    <Col flex="200px">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) => true}
                                            >
                                                {({ getFieldValue }) => {
                                                    const formItemData = getFieldValue(name);

                                                    const cityCodes = formItemData?.map(
                                                        (ele, i) => index != i && ele && ele.city,
                                                    );

                                                    const listOptions = cityList?.map((ele) => {
                                                        return (
                                                            <Option
                                                                key={ele.value}
                                                                disabled={cityCodes?.includes(
                                                                    ele.value,
                                                                )}
                                                                value={ele.value}
                                                            >
                                                                {ele.title}
                                                            </Option>
                                                        );
                                                    });

                                                    return (
                                                        <FormItem
                                                            label=""
                                                            fieldKey={field.fieldKey}
                                                            name={[field.name, 'city']}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择城市',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                placeholder="请选择"
                                                                showSearch
                                                                filterOption={(input, option) =>
                                                                    option.children
                                                                        .toLowerCase()
                                                                        .indexOf(
                                                                            input.toLowerCase(),
                                                                        ) >= 0
                                                                }
                                                                allowClear
                                                                onChange={(value) => {
                                                                    changeCityEvent(value, index);
                                                                }}
                                                                disabled={disabled}
                                                            >
                                                                {listOptions}
                                                            </Select>
                                                        </FormItem>
                                                    );
                                                }}
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    <Col flex="1">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) => true}
                                            >
                                                {({ getFieldValue }) => {
                                                    const formItemData = getFieldValue(name);
                                                    const cityCode =
                                                        formItemData &&
                                                        formItemData[index] &&
                                                        formItemData[index].city;

                                                    // let stationList = [];
                                                    // if (cityCode) {
                                                    //     let item = cityRef.current.find(ele => {
                                                    //         return ele.value == cityCode;
                                                    //     });
                                                    //     if (
                                                    //         item &&
                                                    //         item.children &&
                                                    //         item.children instanceof Array
                                                    //     ) {
                                                    //         stationList = item.children;
                                                    //     }
                                                    // }

                                                    // let stationOptions = stationList.map(ele => {
                                                    //     return (
                                                    //         <Option value={ele.value}>
                                                    //             {ele.title}
                                                    //         </Option>
                                                    //     );
                                                    // });
                                                    return (
                                                        <Spin spinning={stationLoading || false}>
                                                            <FormItem
                                                                style={{ margin: '0 8px' }}
                                                                fieldKey={field.fieldKey}
                                                                name={[field.name, 'stationId']}
                                                                validateTrigger={[
                                                                    'onChange',
                                                                    'onBlur',
                                                                ]}
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '请选择场站',
                                                                    },
                                                                    ({ getFieldValue }) => ({
                                                                        validator(rule, value) {
                                                                            return Promise.resolve();
                                                                        },
                                                                    }),
                                                                ]}
                                                            >
                                                                {/* <Select
                                                                disabled={disabled}
                                                                mode="multiple"
                                                                placeholder={
                                                                    !cityCode
                                                                        ? '请先选择城市'
                                                                        : '全部站点'
                                                                }
                                                                allowClear
                                                                initialValue={[]}
                                                            >
                                                                {stationOptions}
                                                            </Select> */}
                                                                <TreeSelect
                                                                    treeData={
                                                                        stationList[index] || []
                                                                    }
                                                                    placeholder="请选择"
                                                                    {...treeProps}
                                                                    onChange={(
                                                                        value,
                                                                        labelitem,
                                                                        ext,
                                                                    ) => {
                                                                        const cityList =
                                                                            stationList[index];

                                                                        let cityObj =
                                                                            cityList?.find(
                                                                                (item) =>
                                                                                    item.value ==
                                                                                    ext.triggerValue,
                                                                            );
                                                                        for (const ele of cityList) {
                                                                            if (
                                                                                ele.children?.find(
                                                                                    (item) =>
                                                                                        item.value ==
                                                                                        ext.triggerValue,
                                                                                )
                                                                            ) {
                                                                                cityObj = ele;
                                                                            }
                                                                            if (cityObj) break;
                                                                        }
                                                                        const list = value?.map(
                                                                            (id, itemIndex) => {
                                                                                return {
                                                                                    stationName:
                                                                                        labelitem[
                                                                                            itemIndex
                                                                                        ],
                                                                                    stationId: id,
                                                                                    cityName:
                                                                                        cityObj?.title,
                                                                                    city: cityObj?.value,
                                                                                };
                                                                            },
                                                                        );
                                                                        updateAllStations([
                                                                            ...allStations,
                                                                            ...list,
                                                                        ]);
                                                                    }}
                                                                />
                                                            </FormItem>
                                                        </Spin>
                                                    );
                                                }}
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    {disabled ? null : (
                                        <Col>
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{ margin: '0 8px' }}
                                                onClick={() => {
                                                    remove(field.name);
                                                    changeStationList((oldList) => {
                                                        oldList.splice(field.name, 1);
                                                        return oldList;
                                                    });
                                                }}
                                            />
                                        </Col>
                                    )}
                                </Row>
                            </Fragment>
                        ))}
                        <FormItem label="">
                            <Button
                                loading={loading}
                                disabled={!canIadd()}
                                type="dashed"
                                onClick={() => {
                                    add();
                                }}
                                style={{ width: '200px' }}
                            >
                                <PlusOutlined />
                                添加城市
                            </Button>
                        </FormItem>
                    </Fragment>
                )}
            </Form.List>
        </Form.Item>
    );
};
export default connect(({ user, global, loading }) => ({
    global,
    currentUser: user.currentUser,
    operLoading: loading.effects['global/getOperatorList'],
}))(CityTreeSelectList);
