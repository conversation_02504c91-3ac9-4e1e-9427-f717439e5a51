import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { Modal } from 'antd';

import SearchStationItem from './SearchStationItem';

const ShowStationModal = (props, ref) => {
    const [visible, toggleVisible] = useState(false);
    const seachRef = useRef();
    useImperativeHandle(ref, () => {
        return {
            open: (stationList) => {
                toggleVisible(true);
                setTimeout(() => {
                    seachRef?.current?.setAllStations(stationList);
                }, 200);
            },
        };
    });
    const closeModalEvent = () => {
        toggleVisible(false);
    };
    return (
        <Modal
            title="活动范围"
            visible={visible}
            width={1000}
            onCancel={closeModalEvent}
            footer={false}
        >
            <SearchStationItem {...props} ref={seachRef} disabled />
        </Modal>
    );
};

export default forwardRef(ShowStationModal);
