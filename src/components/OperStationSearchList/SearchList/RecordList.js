import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { Button, Form, Modal, Space, Col, Row, Spin } from 'antd';

import TablePro from '@/components/TablePro';

import { getStationRecordListApi } from '@/services/CommonApi';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import AllStationSelect from '@/components/AllStationSelect';

import { debounce } from 'lodash';

const formItemLayout = {};

const RecordList = ({ recordParams = {}, initRef }) => {
    const [searchForm] = Form.useForm(); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
    const [visible, updateVisible] = useState(false);

    // 用于过滤出要显示的场站，兼容查询操作
    const [showStations, updateShowStations] = useState([]);
    const [showStationsTotal, updateShowStationsTotal] = useState(0);

    const stationColumns = [
        {
            title: '场站',
            dataIndex: 'stationName',
            width: 160,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '运营商',
            dataIndex: 'buildName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '城市',
            dataIndex: 'cityName',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            dataIndex: 'actionName',
            width: 80,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作人',
            dataIndex: 'operator',
            width: 120,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作时间',
            dataIndex: 'createTime',
            width: 200,
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
    ];
    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);

    useEffect(() => {
        if (visible) {
            searchData();
        }
    }, [pageNum, visible]);

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
    };

    // 从接口拉取数据的属性维护
    useImperativeHandle(initRef, () => ({
        show: () => {
            updateVisible(true);
            resetData();
        },
    }));

    const [isWaiting, updateWaiting] = useState(false);
    const searchData = debounce(async () => {
        const data = await searchForm.getFieldsValue();
        const params = {
            ...recordParams,
            city: data.cityList?.join?.(',') || data.cityList,
            buildId: data.operId || recordParams.operId,
            stationId: data.stationId,
            pageIndex: pageNum,
            pageSize,
            operId: undefined,
        };
        updateWaiting(true);
        try {
            updateShowStations([]);
            const {
                data: { list, total },
            } = await getStationRecordListApi(params);
            updateShowStations(list);
            updateShowStationsTotal(total);
        } catch (error) {
        } finally {
            updateWaiting(false);
        }
    }, 200);

    const queryData = () => {
        if (pageNum == 1) {
            searchData();
        } else {
            changePageNum(1);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        queryData();
    };

    const onClose = () => {
        updateVisible(false);
    };

    return (
        <Modal visible={visible} title="配置记录" width={880} onCancel={onClose} footer={null}>
            <Form form={searchForm} labelAlign="right">
                <Row gutter={24}>
                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            {...formItemLayout}
                            form={searchForm}
                            initialValue={recordParams?.operId || undefined}
                            disabled={recordParams?.operId?.length > 0}
                            disabledFilter={recordParams?.operId?.length > 0}
                        />
                    </Col>

                    <Col span={8}>
                        <AllStationSelect form={searchForm} label="场站名称" name="stationId" />
                    </Col>

                    <Col span={8}>
                        <CitysSelect
                            label="城市"
                            name="cityList"
                            placeholder="请选择"
                            formItemLayout={{ labelAlign: 'right' }}
                            showArrow
                            provinceSelectable
                            rules={null}
                        />
                    </Col>
                </Row>

                <div style={{ display: 'flex', margin: '16px 0' }}>
                    <Space>
                        <Button
                            type="primary"
                            onClick={async () => {
                                queryData();
                            }}
                        >
                            查询
                        </Button>

                        {/* <Button type="primary" onClick={exportFormEvent}>
                            全部导出
                        </Button> */}

                        <Button onClick={resetData}>重置</Button>
                    </Space>
                </div>
            </Form>

            <Spin spinning={isWaiting}>
                <TablePro
                    scroll={{ x: 'max-content', y: 350 }}
                    rowKey={(record) => record.stationId}
                    dataSource={showStations}
                    onChange={changePageInfo}
                    pagination={{
                        current: pageNum,
                        total: showStationsTotal,
                        pageSize: pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    columns={stationColumns}
                />
            </Spin>
        </Modal>
    );
};

export default RecordList;
