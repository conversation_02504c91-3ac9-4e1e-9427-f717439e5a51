import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import {
    Button,
    Form,
    Modal,
    Space,
    Input,
    Popconfirm,
    Descriptions,
    message,
    Radio,
    Checkbox,
    Col,
    Row,
    Spin,
} from 'antd';

import TablePro from '@/components/TablePro';

import { PlusOutlined } from '@ant-design/icons';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import { localDownloadFileToExcel, renderTableDataIndexText } from '@/utils/utils';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import RecordList from './RecordList';

import { AddOperModal, StasticsModal } from './SearchStationItem';

const { confirm } = Modal;
const FormItem = Form.Item;

const { TextArea } = Input;
const formItemLayout = {};

const StationList = ({
    formItemLayout,
    allStations,
    refreshAllStations,
    form,
    disabled,
    keyName,
    showStasticsEvent, // 统计按钮的点击事件，如实现了，自动显示统计按钮
    operationRecord, // 操作记录的标识，如果配置true，记录新增和删除的场站顺序，分别放到form的字段stationInfo和delScopeInfo中，可以直接取
    requestInfo,
    deleteAllEnabled,
    initRef,
    adjustStationEvent,
    isCopy,
}) => {
    const [searchForm] = Form.useForm(); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
    const [searchParams, updateSearchParams] = useState();

    // 用于过滤出要显示的场站，兼容查询操作
    const showStations = useMemo(() => {
        return (
            allStations?.filter?.((item) => {
                // 如果没有筛选，直接返回true用于展示
                if (Object.values(searchParams || {})?.length == 0) {
                    return true;
                }
                let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。
                if (searchParams.searchKey) {
                    const searchKeyList = searchParams.searchKey.split('\n');
                    const filterList = searchKeyList.filter((ele) => ele);
                    const find = filterList.find(
                        (ele) => item.stationName && item.stationName.indexOf(ele) != -1,
                    );
                    if (!find) {
                        passFilter = false;
                    }
                }
                if (searchParams.operId?.length && passFilter) {
                    passFilter = item.buildId === searchParams.operId;
                }
                if (searchParams.cityList?.length && passFilter) {
                    //过滤区域
                    const find = searchParams.cityList.find((ele) => item.city == ele);
                    if (!find) {
                        passFilter = false;
                    }
                }
                return passFilter;
            }) || []
        );
    }, [allStations, searchParams]);

    const stationColumns = useMemo(() => {
        const columns = [
            {
                title: '序号 ',
                width: 60,
                render(text, record, index) {
                    return <span title={index}>{index + 1}</span>;
                },
            },
            {
                title: '运营商 ',
                dataIndex: 'buildName',
                width: '120px',
                render(text, record) {
                    return renderTableDataIndexText({ text });
                },
            },
            {
                title: '城市 ',
                dataIndex: 'cityName',
                width: '120px',
                render(text, record) {
                    return renderTableDataIndexText({ text });
                },
            },
            {
                title: '场站 ',
                dataIndex: 'stationName',
                render(text, record) {
                    return renderTableDataIndexText({ text });
                },
            },
            {
                title: '首次上线时间',
                dataIndex: 'firstOpenTime',
                width: '200px',
                render(text, record) {
                    return renderTableDataIndexText({ text });
                },
            },
        ];
        if (disabled == false) {
            columns.push({
                title: '操作 ',
                render(text, record, index) {
                    return (
                        <Popconfirm
                            title={`确定要删除此场站？`}
                            onConfirm={() => {
                                const allDatas = requestInfo?.listApi ? dataList : allStations;
                                deleteSelectRows(false, allDatas.indexOf(record));
                            }}
                        >
                            <a>删除</a>
                        </Popconfirm>
                    );
                },
            });
        }
        return columns;
    }, [disabled, allStations]);
    const [pageNum, changePageNum] = useState(1);
    const [pageSize, changePageSize] = useState(10);
    const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑

    // 搜索列表
    const changePageInfo = (page) => {
        if (pageSize != page.pageSize) {
            changePageSize(page.pageSize);
        }

        changePageNum(page.current);
        form.setFieldsValue({ [keyName]: allStations });
    };

    // 有选中的场站
    const hasSelectedStations = useMemo(() => {
        return selectedRowKeys.length > 0;
    }, [selectedRowKeys]);

    const onSelectChange = (_selectedRowKeys) => {
        updateSelectedRowKeys(_selectedRowKeys);
    };

    const deleteSelectRows = async (isAll = false, indexes) => {
        try {
            const allDatas = requestInfo?.listApi ? dataList : allStations;
            if (operationRecord || requestInfo?.deleteEvent) {
                let curDeleteStations = [];
                if (isAll) {
                    curDeleteStations = allDatas;
                } else {
                    if (indexes instanceof Array) {
                        allDatas.map((ele) =>
                            selectedRowKeys.indexOf(ele.stationId) >= 0
                                ? curDeleteStations.push(ele)
                                : undefined,
                        );
                    } else {
                        curDeleteStations.push(allDatas[indexes]);
                    }
                }
                const stationInfo = JSON.parse(form.getFieldValue('stationInfo') || '[]');
                const deleteInfo = JSON.parse(form.getFieldValue('delScopeInfo') || '[]');
                const deleteStations = [];
                // 对象去重
                [...curDeleteStations, ...deleteInfo].map((stationObj) => {
                    const index = deleteStations.findIndex(
                        (ele) => `${ele.stationId}` == `${stationObj.stationId}`,
                    );
                    if (index == -1) {
                        deleteStations.push(stationObj);
                    }
                });
                if (stationInfo?.length) {
                    // 判断删除的是否是新增的，要把新增的移除
                    deleteStations.map((station) => {
                        const index = stationInfo.findIndex(
                            (ele) => `${ele.stationId}` == `${station.stationId}`,
                        );
                        if (index >= 0) {
                            stationInfo.splice(index, 1);
                        }
                    });
                }

                if (requestInfo?.deleteEvent) {
                    requestInfo.deleteEvent({ delScopeInfo: JSON.stringify(deleteStations) });
                    return;
                }

                form.setFieldsValue({
                    delScopeInfo: JSON.stringify(deleteInfo),
                    stationInfo: JSON.stringify(stationInfo),
                });
                adjustStationEvent();
            }
            let total = 0;
            if (isAll) {
                total = allDatas.length || 0;
                refreshAllStations && refreshAllStations([]);
            } else {
                if (indexes instanceof Array) {
                    for (let i = indexes.length - 1; i >= 0; i--) {
                        const stationId = indexes[i];
                        const stationObj = allDatas.find((ele) => ele.stationId == stationId);
                        const index = allDatas.indexOf(stationObj);
                        if (index >= 0) {
                            total += 1;
                            allDatas.splice(index, 1);
                            refreshAllStations && refreshAllStations([...allDatas]);
                        }
                    }
                    message.success(`成功删除${total}条数据`);
                } else {
                    if (indexes >= 0) {
                        allDatas.splice(indexes, 1);
                        refreshAllStations && refreshAllStations([...allDatas]);
                    }
                }
            }
            updateSelectedRowKeys([]);
        } catch (error) {}
    };

    const exportFormEvent = async () => {
        if (requestInfo?.exportEvent) {
            const data = await searchForm.getFieldsValue();
            const params = {
                ...(requestInfo.params || {}),
                city: data.cityList?.join?.(','),
                operId: data.operId,
                stationName: data.searchKey,
            };
            requestInfo?.exportEvent(params, stationColumns);
            return;
        }
        const tableData = [];
        allStations.forEach((item, index) => {
            const col = {};
            stationColumns.forEach((colItem) => {
                const name = colItem.title;
                const key = colItem.dataIndex;
                if (key) {
                    const value = item[key];
                    col[name] = value;
                }
            });
            tableData.push(col);
        });
        localDownloadFileToExcel({ data: tableData, fileName: '场站列表' });
    };

    const exportStyle = useMemo(() => {
        return { marginRight: 0, marginLeft: showStasticsEvent ? undefined : 'auto' };
    }, [showStasticsEvent]);

    // 从接口拉取数据的属性维护
    useImperativeHandle(initRef, () => ({
        resetData,
        deleteAtIndex: (stationId) => {
            const allDatas = requestInfo?.listApi ? dataList : allStations;
            const index = allDatas.findIndex((ele) => ele.stationId == stationId);
            if (index >= 0) {
                deleteSelectRows(false, index);
            } else {
                console.log('删除的场站不在列表中');
            }
        },
    }));

    useEffect(() => {
        searchData();
    }, [pageNum]);

    const [dataList, updateDataList] = useState([]);
    const [dataListTotal, updateDataListTotal] = useState(0);
    const [isWaiting, updateWaiting] = useState(false);
    const searchData = async () => {
        if (!requestInfo?.listApi) {
            return;
        }
        const data = await searchForm.getFieldsValue();
        const params = {
            ...(requestInfo.params || {}),
            city: data.cityList?.join?.(','),
            operId: data.operId,
            stationName: data.searchKey,
            pageIndex: pageNum,
            pageSize,
        };

        updateWaiting(true);
        try {
            updateDataList([]);
            const {
                data: { list, records, total },
            } = await requestInfo.listApi(params);
            updateDataList(list || records);
            updateDataListTotal(total);
        } catch (error) {
        } finally {
            updateWaiting(false);
        }
    };

    const queryData = () => {
        if (pageNum == 1) {
            searchData();
        } else {
            changePageNum(1);
        }
    };

    const resetData = () => {
        searchForm.resetFields();
        if (requestInfo?.listApi) {
            queryData();
        }
    };

    // 配置记录
    const recordRef = useRef();
    const showRecordEvent = () => {
        recordRef.current?.show();
    };

    return (
        <FormItem {...formItemLayout}>
            <Form form={searchForm} labelAlign="right">
                <Row gutter={24}>
                    <Col span={8}>
                        <FormItem noStyle name="searchKey">
                            <TextArea
                                placeholder="请输入场站进行查询,多个场站使用回车间隔"
                                allowClear
                            />
                        </FormItem>
                    </Col>

                    <Col span={8}>
                        <OperSelectTypeItem
                            // rules={[{ message: '请选择运营商', required: true }]}
                            {...formItemLayout}
                            form={searchForm}
                            initialValue={requestInfo?.recordParams?.operId || undefined}
                            disabled={requestInfo?.recordParams?.operId?.length > 0}
                            disabledFilter={requestInfo?.recordParams?.operId?.length > 0}
                        />
                    </Col>
                    <Col span={8}>
                        <CitysSelect
                            label="城市"
                            name="cityList"
                            placeholder="请选择"
                            formItemLayout={{ labelAlign: 'right' }}
                            showArrow
                            provinceSelectable
                            rules={null}
                            // multiple={false}
                        />
                    </Col>
                </Row>
                <br />
                <Row gutter={[12, 12]}>
                    <Col>
                        <Space>
                            <Button
                                type="primary"
                                onClick={async () => {
                                    if (requestInfo?.listApi) {
                                        queryData();
                                        return;
                                    }
                                    const params = await searchForm.getFieldsValue();
                                    updateSearchParams((params && { ...params }) || undefined);
                                    changePageNum(1);
                                    updateSelectedRowKeys([]);
                                }}
                            >
                                查询
                            </Button>

                            <Button type="primary" onClick={exportFormEvent}>
                                全部导出
                            </Button>

                            <Button
                                onClick={() => {
                                    resetData();
                                    updateSearchParams(undefined);
                                }}
                            >
                                重置
                            </Button>
                        </Space>
                    </Col>
                    <Col flex={'auto'} />
                    <Col>
                        <div style={{ display: 'flex' }}>
                            {showStasticsEvent && (
                                <Button
                                    style={{ marginRight: '16px', marginLeft: 'auto' }}
                                    onClick={showStasticsEvent}
                                >
                                    场站明细
                                </Button>
                            )}
                            {requestInfo?.recordParams && !isCopy && (
                                <Button style={exportStyle} onClick={showRecordEvent}>
                                    配置记录
                                </Button>
                            )}
                        </div>
                    </Col>
                </Row>
            </Form>

            <br />
            {(!disabled && (
                <p>
                    <Space>
                        {(requestInfo?.deleteEvent && (
                            <Popconfirm
                                title="即将删除已勾选场站"
                                onConfirm={() => deleteSelectRows(false, selectedRowKeys)}
                                disabled={!hasSelectedStations}
                            >
                                <Button type="primary" disabled={!hasSelectedStations}>
                                    删除选中项
                                </Button>
                            </Popconfirm>
                        )) || (
                            <Button
                                type="primary"
                                disabled={!hasSelectedStations}
                                onClick={() => deleteSelectRows(false, selectedRowKeys)}
                            >
                                删除选中项
                            </Button>
                        )}

                        {(deleteAllEnabled && (
                            <Popconfirm
                                title="即将全部删除，请二次确认"
                                onConfirm={() => deleteSelectRows(true, selectedRowKeys)}
                            >
                                <Button danger disabled={!allStations || allStations.length == 0}>
                                    全部删除
                                </Button>
                            </Popconfirm>
                        )) ||
                            null}
                    </Space>
                </p>
            )) ||
                null}
            <FormItem noStyle name={keyName}></FormItem>

            <Spin spinning={isWaiting}>
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={requestInfo?.listApi ? dataList : showStations}
                    onChange={changePageInfo}
                    pagination={{
                        current: pageNum,
                        total: requestInfo?.listApi ? dataListTotal : showStations.length,
                        pageSize: pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    expandable={{ defaultExpandAllRows: true }}
                    columns={stationColumns}
                    sticky={{ offsetHeader: 64 }}
                    rowSelection={
                        !disabled && {
                            selectedRowKeys,
                            onChange: onSelectChange,
                            getCheckboxProps: (record) => ({
                                disabled: !record.stationId,
                            }),
                        }
                    }
                />
            </Spin>

            <RecordList initRef={recordRef} recordParams={requestInfo?.recordParams} />
        </FormItem>
    );
};

// 商家营销子编辑页面
const OperStationSearchList = (props, ref) => {
    const {
        title = '活动范围',
        formItemLayout,
        form,
        stationList,
        disabled,
        keyName = 'stationInfoList',
        required,
        purchase,
        currentUser,
        hasStastics, // 是否包含统计按钮
        addEnabled = true, // 是否可新增
        deleteAllEnabled = true, // 是否可全部删除
        limitOperIds, // 限制哪几个运营商可选中，如果配置了，查询维度只支持运营商，无切换维度和添加按钮，且传参格式限制为[{operId, citys:[city1, city2]}]，其中citys为可选
        initRef,
        operChannels, // 运营方式，如果配了，自动过滤所选场站，例[{code: '01', name:'直营'}]
        disabledStationIds, // 某些场站禁用，内部会自动拼已选场站不可再被勾选的逻辑
        limitCityIds, // 限制只有哪些场站可被选择
        hideRange,
        hasReasonText,
        operationRecord = true, // 操作记录的标识，如果配置true，记录新增和删除的场站顺序，分别放到form的字段stationInfo和delScopeInfo中，可以直接取
        /** 请求信息配置，传参结构：
        {
            listApi: '...',     // 如果配了接口，分页会调接口，提交需要取delScopeInfo、stationInfo分别对应删除、新增的汇总
            deleteEvent: (values) => void,
            exportEvent: (params, columns) => void,
            recordParams: {relateId: 配置记录相关活动id，有值的话显示配置记录按钮},
            params: {其他参数}
        }*/
        requestInfo,
        isCopy, // 如果需要记录操作标识，复制场景需要单独处理，删除就直接抹掉，剩余的默认场站全部都是新增
        cooperationPlatform,
    } = props;

    const [initFlag, setInitFlag] = useState(true);
    const [allStations, updateAllStations] = useState([]);
    useEffect(() => {
        if (initFlag && stationList?.length) {
            // 只有初始化的时候才和外部数据做关联，后续全部以自身维护的数据为参照
            refreshAllStations((stationList && [...stationList]) || []);
            setInitFlag(false);
        }
    }, [stationList]);

    useEffect(() => {
        if (isCopy) {
            form.setFieldsValue({
                delScopeInfo: '',
                stationInfo: JSON.stringify(allStations),
            });
        }
    }, [isCopy, allStations, form]);

    const disabledIds = useMemo(() => {
        const ids = [...(disabledStationIds || [])];
        allStations?.map((ele) => {
            ids.push(ele.stationId);
        });
        return ids;
    }, [disabledStationIds, allStations]);

    // 维护更多的统计语句，如果有传值，会在统计弹窗里显示在最前面
    const [moreStastics, updateMoreStastics] = useState(undefined);
    const [stasticsVisiable, updateStasticsVisiable] = useState(false);
    const listRef = useRef();
    useImperativeHandle(initRef, () => ({
        // changeVal 就是暴露给父组件的方法
        showStastics: (_moreStastics) => {
            updateMoreStastics(_moreStastics);
            if (_moreStastics?.length || allStations?.length) {
                showStasticsEvent();
            } else {
                message.error('无场站明细可展示');
            }
        },
        reset: () => {
            // 清空数据
            refreshAllStations([]);
        },
        hasStations: () => {
            // 获取是否已配置场站
            return allStations.length > 0;
        },
        reload: () => {
            // 如果是走接口刷新列表的，开放刷新事件
            if (requestInfo?.listApi) {
                listRef.current?.resetData();
            }
        },
        deleteAtIndex: (stationId) => {
            listRef?.current.deleteAtIndex?.(stationId);
        },
    }));

    const refreshAllStations = (stations) => {
        // 同时要更新form，用于外部页面通过form的方法来获取数据
        updateAllStations([...stations]);
        form?.setFieldsValue({ [keyName]: stations });
    };

    // 添加运营商
    const operGroupRef = useRef();
    const [operVisible, setOperVisible] = useState(false);

    const showOperModal = () => {
        setOperVisible(true);
    };

    const hideOperModal = () => {
        setOperVisible(false);
    };

    const showStasticsEvent = () => {
        updateStasticsVisiable(true);
    };

    const addEvent = (list) => {
        // 新增的场站
        const stationInfo = JSON.parse(form.getFieldValue('stationInfo') || '[]');
        const deleteInfo = JSON.parse(form.getFieldValue('delScopeInfo') || '[]');

        const stationInfoList = [];
        // 对象去重
        [...stationInfo, ...list].map((stationObj) => {
            const index = stationInfoList.findIndex(
                (ele) => `${ele.stationId}` == `${stationObj.stationId}`,
            );
            if (index == -1) {
                stationInfoList.push(stationObj);
            }
        });
        if (deleteInfo?.length) {
            // 判断新增的是否是删除的，要把删除的移除
            stationInfoList.map((info) => {
                const idIndex = deleteInfo.findIndex((ele) => ele.stationId == `${info.stationId}`);
                if (idIndex >= 0) {
                    deleteInfo.splice(idIndex, 1);
                }
            });
        }

        form.setFieldsValue({
            delScopeInfo: JSON.stringify(deleteInfo),
            stationInfo: JSON.stringify(stationInfo),
        });

        adjustStationEvent();
    };

    // 各自判断业务上是不是保存过
    const adjustStationEvent = () => {
        if (operationRecord) {
            const stationInfo = JSON.parse(form.getFieldValue('stationInfo') || '[]');
            const deleteInfo = JSON.parse(form.getFieldValue('delScopeInfo') || '[]');

            if (!isCopy) {
                // 如果有记录，和原始记录比较是否是真实新增/删除
                stationList?.map((info) => {
                    const addIndex = stationInfo.findIndex(
                        (ele) => ele.stationId == `${info.stationId}`,
                    );

                    // 新增如果保存过，不当做新增处理
                    if (addIndex >= 0 && stationInfo?.length) {
                        stationInfo.splice(addIndex, 1);
                    }
                });

                // 删除如果不在原本列表，不当做删除
                for (let delIndex = deleteInfo.length - 1; delIndex >= 0; delIndex--) {
                    const element = deleteInfo[delIndex];
                    const delTempIndex = stationList?.findIndex?.(
                        (ele) => ele.stationId == `${element.stationId}`,
                    );
                    if (delTempIndex === undefined || delTempIndex == -1) {
                        deleteInfo.splice(delIndex, 1);
                    }
                }
            } else {
                // 复制的情况，不需要记录删除
                for (let delIndex = deleteInfo.length - 1; delIndex >= 0; delIndex--) {
                    deleteInfo.splice(delIndex, 1);
                }
            }

            form.setFieldsValue({
                delScopeInfo: JSON.stringify(deleteInfo),
                stationInfo: JSON.stringify(stationInfo),
            });
        }
    };

    return (
        <Fragment>
            {(disabled && title?.length == 0) || !addEnabled || !form ? null : (
                <FormItem
                    label={title}
                    {...formItemLayout}
                    name="empty"
                    rules={[
                        () => ({
                            validator(rule, value) {
                                if (allStations?.length == 0 && required && !disabled) {
                                    return Promise.reject('请选择活动场站');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                    required={required}
                >
                    {(!disabled && (
                        <Space>
                            <Button type="primary" onClick={showOperModal}>
                                <PlusOutlined />
                                添加场站
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => operGroupRef.current.show(allStations)}
                            >
                                <PlusOutlined />
                                批量导入
                            </Button>
                        </Space>
                    )) ||
                        null}
                </FormItem>
            )}

            {form ? (
                <FormItem shouldUpdate={(prevValues, curValues) => true} noStyle>
                    {({ getFieldValue }) => {
                        return (
                            ((allStations?.length || requestInfo?.listApi) && (
                                <StationList
                                    allStations={allStations}
                                    refreshAllStations={refreshAllStations}
                                    formItemLayout={
                                        formItemLayout || {
                                            ...{
                                                wrapperCol: {
                                                    offset: 6,
                                                    span: 18,
                                                },
                                            },
                                        }
                                    }
                                    stationList={allStations}
                                    form={form}
                                    disabled={disabled}
                                    keyName={keyName}
                                    showStasticsEvent={
                                        hasStastics && !requestInfo?.listApi && showStasticsEvent
                                    }
                                    operationRecord={operationRecord}
                                    requestInfo={requestInfo}
                                    deleteAllEnabled={deleteAllEnabled}
                                    initRef={listRef}
                                    adjustStationEvent={adjustStationEvent}
                                    isCopy={isCopy}
                                />
                            )) || <FormItem name={keyName} noStyle />
                        );
                    }}
                </FormItem>
            ) : (
                <StationList
                    allStations={allStations}
                    refreshAllStations={refreshAllStations}
                    stationList={allStations}
                    disabled={disabled}
                    keyName={keyName}
                    showStasticsEvent={hasStastics && !requestInfo?.listApi && showStasticsEvent}
                    operationRecord={operationRecord}
                    requestInfo={requestInfo}
                    deleteAllEnabled={deleteAllEnabled}
                    initRef={listRef}
                    adjustStationEvent={adjustStationEvent}
                    isCopy={isCopy}
                />
            )}

            {/* 传参用 */}
            {(operationRecord && <FormItem name="stationInfo" noStyle />) || null}
            {(operationRecord && <FormItem name="delScopeInfo" noStyle />) || null}
            <AddOperModal
                visible={operVisible}
                allStations={allStations}
                onFinish={(_stationList) => {
                    hideOperModal();
                    _stationList?.length && refreshAllStations([..._stationList, ...allStations]);
                    if (operationRecord && _stationList?.length) {
                        addEvent(_stationList);
                    }
                }}
                disabled={disabled}
                purchase={purchase}
                openFlag={null}
                limitOperIds={limitOperIds}
                operChannels={operChannels}
                disabledStationIds={disabledIds}
                limitCityIds={limitCityIds}
                hideRange={hideRange}
                cooperationPlatform={cooperationPlatform}
            />
            <OperGroupImportModal
                initRef={operGroupRef}
                disabled={disabled}
                onConfirm={(_stationList) => {
                    refreshAllStations([..._stationList, ...allStations]);
                    if (operationRecord && _stationList?.length) {
                        addEvent(_stationList);
                    }
                }}
                purchase={purchase}
                currentUser={currentUser}
                isLimit
                limitOperIds={limitOperIds}
                disabledStationIds={disabledIds}
                limitCityIds={limitCityIds}
                hasReasonText={hasReasonText || undefined}
                cooperationPlatform={cooperationPlatform}
            />

            <StasticsModal
                allStations={allStations}
                visible={stasticsVisiable}
                onCancel={() => updateStasticsVisiable(false)}
            >
                {moreStastics}
            </StasticsModal>
        </Fragment>
    );
};
export default forwardRef(OperStationSearchList);
