import React, {
    Fragment,
    useEffect,
    useState,
    useRef,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import {
    Button,
    Form,
    Modal,
    Space,
    Input,
    Popconfirm,
    Descriptions,
    message,
    Radio,
    Checkbox,
    Col,
    Row,
    Spin,
    Empty,
    Typography,
    Select,
} from 'antd';

import TablePro from '@/components/TablePro';

import { PlusOutlined } from '@ant-design/icons';
import OperTreeSelectList from '@/components/OperTreeSelectList';
import CityTreeSelectList from '@/components/OperStationSearchList/CityTreeSelectList';
import ProvinceTreeSelectList from '@/components/OperStationSearchList/ProvinceTreeSelectList';
import OperGroupImportModal from '@/components/OperStationSearchList/OperGroupImportModal';
import {
    isEmpty,
    localDownloadFileToExcel,
    isObjectEqual,
    renderTableDataIndexText,
} from '@/utils/utils';
import OperSelectTypeItem from '@/components/OperSelectItem/OperSelectTypeItem';
import CitysSelect from '@/components/CitysSelect/index.js';
import RecordList from './RecordList';
import { Link } from 'umi';
import PriceEstimationDrawer from './components/priceEstimationDrawer';
import { initIcnChannelApi } from '@/services/SallerCenter/IcnApi.js';
import { getThirdChannelAllStationApi } from '@/services/CommonApi';

import { isNull } from 'lodash';
import { useRequest } from 'ahooks';

const { confirm } = Modal;
const FormItem = Form.Item;

const { TextArea } = Input;
const { Option } = Select;
const formItemLayout = {};

export const AddOperModal = ({
    visible,
    onFinish,
    disabled,
    allStations,
    purchase,
    openFlag = 1,
    limitOperIds,
    limitCitys,
    operChannels,
    disabledStationIds,
    limitCityIds, // 限制只有哪些场站可被选择
    exceptOperIds,
    hideRange,
    formTypeWhenLimitOperIds,
    premiumFlag,
    isChannelRule,
    cooperationPlatform, //过滤新电途还是xdtx渠道  xdt xdt_x
}) => {
    const [operForm] = Form.useForm();
    const operSelectList = useRef();

    useEffect(() => {
        if (visible) {
            if (isChannelRule) {
                loadChannelList();
            }
            if (limitOperIds?.length) {
                operSelectList.current?.init(limitOperIds);
            } else {
                operSelectList.current?.reset();
            }

            if (operChannels?.length) {
                operForm.setFieldsValue({ operChannels: operChannels.map((ele) => ele.codeValue) });
            } else {
                operForm.setFieldsValue({ operChannels: undefined });
            }
        }
    }, [visible, limitOperIds, cooperationPlatform]);

    const onOk = async () => {
        try {
            const values = await operForm.validateFields();
            let list = [];
            if (operSelectList.current && typeof operSelectList.current.formatter === 'function') {
                list = await operSelectList.current.formatter();
            }

            const alreadyStations = []; // 记录已存在的场站信息
            let successStations = []; // 可成功导入的场站
            if (isChannelRule && values.rangeType === 'all' && values.channelId) {
                const {
                    data: { stationList },
                } = await getThirdChannelAllStationApi({
                    channelId,
                });
                successStations = successStations.concat(stationList);
            }

            let allStationsInfo = {};

            allStations?.forEach((stationItem) => {
                allStationsInfo[
                    `${stationItem.stationId}${
                        stationItem.channelId ? `_${stationItem.channelId}` : ''
                    }`
                ] = stationItem;
            });

            list?.forEach((stationItem) => {
                // 查询是否已添加过
                const res =
                    allStationsInfo[
                        `${stationItem.stationId}${values.channelId ? `_${values.channelId}` : ''}`
                    ];
                if (res) {
                    alreadyStations.push(
                        `${values.channelId ? `【${values.channelId}】` : ''}${
                            stationItem.stationName
                        }`,
                    );
                } else {
                    // 未匹配到
                    successStations.push(stationItem);
                }
            });

            if (isChannelRule) {
                const channelItem = commonChannelList.find(
                    (ele) => ele.channelId === values.channelId,
                );
                successStations = successStations.map((ele) => {
                    return {
                        ...ele,
                        channelId: values.channelId,
                        channelName: channelItem.channelName,
                    };
                });
            }

            if (alreadyStations?.length) {
                confirm({
                    title: `以下场站已添加过，本次操作将添加${
                        successStations.length || 0
                    }个，确定要继续吗？`,
                    content: (
                        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                            {alreadyStations?.join('、')}
                        </div>
                    ),
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        onFinish && onFinish(successStations);
                    },
                });
            } else {
                onFinish && onFinish(successStations);
            }
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const { run: loadChannelList, data: commonChannelList } = useRequest(
        async () => {
            try {
                let params = {};
                if (isChannelRule) {
                    params.dockingMode = 2;
                }
                const {
                    data: { channelList = [] },
                } = await initIcnChannelApi(params);
                return channelList;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        {
            manual: true,
        },
    );

    const channelId = Form.useWatch('channelId', operForm);

    return (
        <Modal
            title="添加范围"
            visible={visible}
            onOk={onOk}
            onCancel={() => onFinish()}
            width={800}
            maskClosable={false}
        >
            <Form
                form={operForm}
                name="operForm"
                initialValues={{ rangeType: isChannelRule ? 'all' : '1' }}
            >
                {hideRange ? null : (
                    <Fragment>
                        {isChannelRule && (
                            <Form.Item
                                label="渠道简称"
                                name="channelId"
                                wrapperCol={{ span: 8 }}
                                rules={[
                                    {
                                        required: true,
                                        message: '请先选择企业',
                                    },
                                ]}
                            >
                                <Select
                                    placeholder="请选择"
                                    allowClear
                                    showSearch
                                    filterOption={(input, option) =>
                                        (option?.children ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    onChange={(value) => {
                                        let params = {
                                            stationList: [],
                                        };
                                        if (!value) {
                                            params.rangeType = 'all';
                                        }
                                        operForm.setFieldsValue(params);
                                    }}
                                >
                                    {commonChannelList?.map((item) => {
                                        return (
                                            <Select.Option
                                                value={item.channelId}
                                                key={item.channelId}
                                            >
                                                {item.channelName}
                                            </Select.Option>
                                        );
                                    })}
                                </Select>
                            </Form.Item>
                        )}

                        <FormItem label="查询维度" name="rangeType">
                            <Radio.Group
                                onChange={() => {
                                    operForm.setFieldsValue({ stationList: [] });
                                }}
                            >
                                {isChannelRule && <Radio value="all">全部场站</Radio>}

                                <Radio value="1" disabled={isChannelRule && !channelId}>
                                    运营商
                                </Radio>

                                <Radio value="2" disabled={isChannelRule && !channelId}>
                                    城市
                                </Radio>
                                <Radio value="3" disabled={isChannelRule && !channelId}>
                                    省份
                                </Radio>
                            </Radio.Group>
                        </FormItem>
                    </Fragment>
                )}

                {(operChannels?.length && (
                    <FormItem label="运营方式" name="operChannels">
                        <Checkbox.Group>
                            {operChannels.map((ele) => (
                                <Checkbox value={ele.codeValue} key={ele.codeValue}>
                                    {ele.codeName}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </FormItem>
                )) ||
                    null}

                <FormItem
                    shouldUpdate={(prevValues, curValues) =>
                        prevValues.rangeType !== curValues.rangeType ||
                        prevValues.operChannels !== curValues.operChannels
                    }
                    noStyle
                >
                    {({ getFieldValue }) => {
                        const rangeType = getFieldValue('rangeType');
                        const operChannels = getFieldValue('operChannels');

                        return rangeType == '1' ? (
                            <OperTreeSelectList
                                form={operForm}
                                initRef={operSelectList}
                                label="运营商"
                                name="stationList"
                                disabled={disabled}
                                rules={[{ required: true, message: '请选择活动场站' }]}
                                purchase={purchase}
                                openFlag={openFlag}
                                limitOperIds={limitOperIds}
                                limitCitys={limitCitys}
                                operChannelCodes={operChannels}
                                disabledStationIds={disabledStationIds}
                                limitCityIds={limitCityIds}
                                formTypeWhenLimitOperIds={formTypeWhenLimitOperIds}
                                premiumFlag={premiumFlag}
                                isChannelRule={isChannelRule}
                                channelId={channelId}
                                cooperationPlatform={cooperationPlatform}
                                exceptOperIds={exceptOperIds}
                            />
                        ) : rangeType == '2' ? (
                            <CityTreeSelectList
                                form={operForm}
                                initRef={operSelectList}
                                label="城市"
                                name="stationList"
                                disabled={disabled}
                                limitOperIds={
                                    (limitOperIds &&
                                        limitOperIds.map((ele) => ele.operId || ele)) ||
                                    undefined
                                }
                                limitCitys={limitCitys}
                                exceptOperIds={
                                    exceptOperIds?.map((ele) => ele.operId || ele) || undefined
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择活动场站',
                                    },
                                ]}
                                purchase={purchase}
                                openFlag={openFlag}
                                premiumFlag={premiumFlag}
                                isChannelRule={isChannelRule}
                                channelId={channelId}
                                cooperationPlatform={cooperationPlatform}
                            />
                        ) : rangeType == '3' ? (
                            <ProvinceTreeSelectList
                                form={operForm}
                                initRef={operSelectList}
                                label="省份"
                                name="stationList"
                                disabled={disabled}
                                limitOperIds={
                                    (limitOperIds &&
                                        limitOperIds.map((ele) => ele.operId || ele)) ||
                                    undefined
                                }
                                limitCitys={limitCitys}
                                exceptOperIds={
                                    exceptOperIds?.map((ele) => ele.operId || ele) || undefined
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择活动场站',
                                    },
                                ]}
                                purchase={purchase}
                                openFlag={openFlag}
                                premiumFlag={premiumFlag}
                                isChannelRule={isChannelRule}
                                channelId={channelId}
                                cooperationPlatform={cooperationPlatform}
                            />
                        ) : null;
                    }}
                </FormItem>
            </Form>
        </Modal>
    );
};

export const StasticsModal = (props) => {
    const { visible, allStations = [], onCancel, children } = props;
    const [stationsStastics, updateStationsStastics] = useState(undefined);

    useEffect(() => {
        if (visible) {
            /* 构造一个统计对象，格式为
                {'1': {
                    "name": '运营商名称',
                    'city': {'01': {
                        'name': '城市名称',
                        'stations': [//场站列表，不考虑重复情况]
                    }}
                }}
            */
            let res = {};
            for (const ele of allStations || []) {
                let operObj = res[ele.buildId];
                if (!operObj) {
                    operObj = { name: ele.buildName, city: {} };
                    res[ele.buildId] = operObj;
                }
                let cityObj = operObj.city[ele.city];
                if (!cityObj) {
                    cityObj = { name: ele.cityName, stations: [] };
                    operObj.city[ele.city] = cityObj;
                }
                cityObj.stations.push(ele);
            }

            let divs = [];
            if (children) {
                let cityStations = children.split(',');
                cityStations.forEach((ele) => {
                    divs.push(<Descriptions title={ele} />);
                });
            }
            let cities;
            for (const key in res) {
                const ele = res[key];
                cities = [];
                for (const cityKey in ele.city) {
                    const city = ele.city[cityKey];
                    cities.push(
                        <Descriptions.Item
                            label={city.name}
                        >{`${city.stations.length}个`}</Descriptions.Item>,
                    );
                }
                divs.push(<Descriptions title={ele.name}>{cities}</Descriptions>);
            }
            updateStationsStastics(divs);
        } else {
            updateStationsStastics(undefined);
        }
    }, [visible, allStations]);

    return (
        <Modal
            title="场站明细"
            visible={visible}
            width={500}
            onCancel={onCancel}
            footer={null}
            maskClosable={false}
        >
            <div style={{ maxHeight: '430px', overflowY: 'auto' }}>{stationsStastics}</div>
        </Modal>
    );
};

const StationList = forwardRef(
    (
        {
            formItemLayout,
            defaultStations = [],
            disabledStationIds = [], //禁止操作的站点
            delStations = [],
            allStations = [],
            refreshAllStations,
            disabled,
            showStasticsEvent, // 统计按钮的点击事件，如实现了，自动显示统计按钮
            requestInfo,
            deleteAllEnabled,
            isCopy,
            emptyText,
            canAppend,
            isPrizeOptimize,
            isPriceEstimation,
            isWorkorderOrigin,
            menuType,
            submitEvent,
            actInfo,
            isChannelRule,
            isEnrollDetail,
            isEnrollExamine,
            showFirstTime = true,
            extendColumns = [],
        },
        ref,
    ) => {
        const [searchForm] = Form.useForm(); // 点击查询后赋值，用于记录下当前关键字，导入后可直接显示过滤后的列表
        const [searchParams, updateSearchParams] = useState();
        const priceRef = useRef();
        // 用于过滤出要显示的场站，兼容查询操作
        const showStations = useMemo(() => {
            const newList =
                allStations?.filter?.((item) => {
                    // 如果没有筛选，直接返回true用于展示
                    if (Object.values(searchParams || {})?.length == 0) {
                        return true;
                    }
                    let passFilter = true; // 标记当前项是否已符合要求，符合filterOptions所有条件才行。
                    if (searchParams.searchKey) {
                        const searchKeyList = searchParams.searchKey.split('\n');
                        const filterList = searchKeyList.filter((ele) => ele);
                        const find = filterList.find(
                            (ele) => item.stationName && item.stationName.indexOf(ele) != -1,
                        );
                        if (!find) {
                            passFilter = false;
                        }
                    }
                    if (searchParams.operId?.length && passFilter) {
                        passFilter = item.buildId === searchParams.operId;
                    }
                    if (searchParams.cityList?.length && passFilter) {
                        //过滤区域
                        const find = searchParams.cityList.find((ele) => item.city == ele);
                        if (!find) {
                            passFilter = false;
                        }
                    }
                    return passFilter;
                }) || [];

            return newList.map((ele, index) => {
                return {
                    ...ele,
                    index: index + 1,
                };
            });
        }, [allStations, searchParams]);

        const stationColumns = useMemo(() => {
            const columns = [
                {
                    title: '序号 ',
                    width: 60,
                    render(text, record, index) {
                        let result = !isEmpty(record.index) ? record.index : index + 1;
                        return <span title={result}>{result}</span>;
                    },
                },
                ...(isChannelRule
                    ? [
                          {
                              title: '企业简称',
                              dataIndex: 'channelName',
                              width: '120px',
                              render(text, record) {
                                  return renderTableDataIndexText({ text });
                              },
                          },
                      ]
                    : []),
                {
                    title: '运营商 ',
                    dataIndex: 'buildName',
                    width: '120px',
                    render(text, record) {
                        return renderTableDataIndexText({ text });
                    },
                },
                {
                    title: '城市 ',
                    dataIndex: 'cityName',
                    width: '120px',
                    render(text, record) {
                        return renderTableDataIndexText({ text });
                    },
                },
                {
                    title: '场站 ',
                    width: '200px',
                    dataIndex: 'stationName',
                    render(text, record) {
                        return renderTableDataIndexText({ text });
                    },
                },
                ...(showFirstTime
                    ? [
                          {
                              title: '首次上线时间',
                              dataIndex: 'firstOpenTime',
                              width: '200px',
                              render(text, record) {
                                  return renderTableDataIndexText({ text });
                              },
                          },
                      ]
                    : []),

                ...(isEnrollExamine || isEnrollDetail
                    ? [
                          {
                              title: '长协',
                              dataIndex: 'longRuleName',
                              width: '200px',
                              render(text, record) {
                                  return <span title={text}>{text}</span>;
                              },
                          },
                      ]
                    : []),
                ...(isEnrollDetail
                    ? [
                          {
                              title: '结算',
                              dataIndex: 'settleRuleName',
                              width: '200px',
                              render(text, record) {
                                  return <span title={text}>{text}</span>;
                              },
                          },
                      ]
                    : []),
                ...extendColumns,
            ];
            if (
                disabled == false ||
                canAppend ||
                isPrizeOptimize ||
                isPriceEstimation ||
                isWorkorderOrigin
            ) {
                columns.push({
                    title: '操作 ',
                    render(text, record, index) {
                        const btns = [];
                        if (isPrizeOptimize) {
                            btns.push(
                                <Link
                                    to={`/marketing/businessActive/module-basic-price/prize-optimize/summary?stationId=${record.stationId}&city=${record.city}&operId=${record.buildId}`}
                                    target="_blank"
                                >
                                    定价辅助
                                </Link>,
                            );
                        }
                        if (
                            isPriceEstimation
                            //  && requestInfo
                        ) {
                            const params = {
                                stationId: record?.stationId,
                            };

                            btns.push(
                                <Typography.Link
                                    onClick={async () => {
                                        if (submitEvent) {
                                            try {
                                                const data = await submitEvent();
                                                const newData =
                                                    JSON.parse(JSON.stringify(data)) || {};
                                                // 未选择平台渠道
                                                if (!newData?.temp_?.includes('01')) {
                                                    delete newData.gridList;
                                                }
                                                // 选择图商渠道
                                                if (newData?.temp_?.includes('02')) {
                                                    // 选择平台同步
                                                    if (newData.synPlatChannel === '1') {
                                                        delete newData.mapGridList;
                                                    }
                                                } else {
                                                    delete newData.mapGridList;
                                                }

                                                priceRef?.current?.show({ ...params, ...newData });
                                            } catch (error) {}
                                        } else {
                                            if (actInfo) {
                                                const newObj =
                                                    JSON.parse(JSON.stringify(actInfo)) || {};

                                                if (!isWorkorderOrigin) {
                                                    newObj.gridList = JSON.stringify(
                                                        actInfo.gridList,
                                                    );
                                                    newObj.mapGridList = !isNull(
                                                        actInfo?.mapGridList,
                                                    )
                                                        ? JSON.stringify(actInfo.mapGridList)
                                                        : actInfo?.mapGridList;
                                                }
                                                priceRef?.current?.show({ ...params, ...newObj });
                                            }
                                        }
                                    }}
                                >
                                    价格估算
                                </Typography.Link>,
                            );
                        }
                        if (
                            (!disabled || (canAppend && record.isAdd)) &&
                            !disabledStationIds?.includes(record.stationId)
                        ) {
                            //如果限制了不可操作，不显示删除按钮
                            btns.push(
                                <Popconfirm
                                    title={`确定要删除此场站？`}
                                    onConfirm={() => {
                                        const allDatas = allStations;
                                        deleteSelectRows(false, [record.stationId]);
                                    }}
                                >
                                    <a>删除</a>
                                </Popconfirm>,
                            );
                        }
                        return <Space>{btns}</Space>;
                    },
                });
            }
            return columns;
        }, [disabled, allStations, requestInfo]);
        const [pageNum, changePageNum] = useState(1);
        const [pageSize, changePageSize] = useState(10);
        const [selectedRowKeys, updateSelectedRowKeys] = useState([]); // 处理多选逻辑

        // 搜索列表
        const changePageInfo = (page) => {
            if (pageSize != page.pageSize) {
                changePageSize(page.pageSize);
            }

            changePageNum(page.current);
        };

        // 有选中的场站
        const hasSelectedStations = useMemo(() => {
            return selectedRowKeys.length > 0;
        }, [selectedRowKeys]);

        const onSelectChange = (_selectedRowKeys) => {
            updateSelectedRowKeys(_selectedRowKeys);
        };

        const deleteSelectRows = async (isAll = false, stationIdList) => {
            try {
                const { deleteApi, deleteParams = {}, deleteEvent } = requestInfo || {};
                if (deleteApi || deleteEvent) {
                    if (isAll) {
                        //删除全部
                        if (deleteApi) {
                            await deleteApi({
                                ...deleteParams,
                                delAll: isAll,
                            });
                        } else if (deleteEvent) {
                            deleteEvent(undefined, isAll);
                        }
                        refreshAllStations([]);
                        updateSelectedRowKeys([]);
                        return;
                    }
                    const allDatas = allStations;
                    let asyncDelList = [];
                    let locatDelList = [];
                    for (const item of stationIdList) {
                        const findItem = defaultStations.find((ele) => ele.stationId === item);
                        if (findItem) {
                            asyncDelList.push(item);
                        } else {
                            locatDelList.push(item);
                        }
                    }
                    if (locatDelList.length > 0) {
                        //本地新添加的数据直接删除
                        const filterList = allDatas.filter(
                            (ele) => !locatDelList.includes(ele.stationId),
                        );
                        refreshAllStations(filterList);

                        stationIdList.map((delId) => {
                            const delIndex = selectedRowKeys.findIndex((ele) => ele == delId);
                            if (delIndex >= 0) {
                                selectedRowKeys.splice(delIndex, 1);
                            }
                        });
                        updateSelectedRowKeys([...selectedRowKeys]);
                    }

                    if (asyncDelList.length > 0) {
                        //远端旧配置的数据通过接口删除
                        if (deleteApi) {
                            await deleteApi({
                                ...deleteParams,
                                delAll: isAll,
                                delStationIds: asyncDelList,
                            });
                            //过滤删除的项
                            const filterList = allDatas.filter(
                                (ele) => !asyncDelList.includes(ele.stationId),
                            );
                            refreshAllStations(filterList);

                            stationIdList.map((delId) => {
                                const delIndex = selectedRowKeys.findIndex((ele) => ele == delId);
                                if (delIndex >= 0) {
                                    selectedRowKeys.splice(delIndex, 1);
                                }
                            });
                            updateSelectedRowKeys([...selectedRowKeys]);
                        } else if (deleteEvent) {
                            deleteEvent(asyncDelList, isAll);
                        }
                    }
                } else {
                    if (isAll) {
                        refreshAllStations([]);
                        updateSelectedRowKeys([]);
                        return;
                    }
                    const filterList = allStations.filter(
                        (ele) => !stationIdList.includes(ele.stationId),
                    );
                    refreshAllStations(filterList);

                    stationIdList.map((delId) => {
                        const delIndex = selectedRowKeys.findIndex((ele) => ele == delId);
                        if (delIndex >= 0) {
                            selectedRowKeys.splice(delIndex, 1);
                        }
                    });
                    updateSelectedRowKeys([...selectedRowKeys]);
                }

                return;
            } catch (error) {
                console.log(6565, error);
            }
        };

        const exportFormEvent = async () => {
            if (requestInfo?.exportEvent) {
                const data = await searchForm.getFieldsValue();
                const params = {
                    ...(requestInfo.params || {}),
                    city: data.cityList?.join?.(','),
                    operId: data.operId,
                    stationName: data.searchKey,
                };
                requestInfo?.exportEvent(params, stationColumns);
                return;
            }
            const tableData = [];
            allStations.forEach((item, index) => {
                const col = {};
                stationColumns.forEach((colItem) => {
                    const name = colItem.title;
                    const key = colItem.dataIndex;
                    if (key) {
                        const value = item[key];
                        col[name] = value;
                    }
                });
                tableData.push(col);
            });
            localDownloadFileToExcel({ data: tableData, fileName: '场站列表' });
        };

        const exportStyle = useMemo(() => {
            return { marginRight: 0, marginLeft: showStasticsEvent ? undefined : 'auto' };
        }, [showStasticsEvent]);

        // 从接口拉取数据的属性维护
        useImperativeHandle(ref, () => ({
            resetData,
            deleteAtIndex: (stationId) => {
                deleteSelectRows(false, [stationId]);
            },
        }));

        const queryData = () => {
            changePageNum(1);
        };

        const resetData = () => {
            searchForm.resetFields();
            if (requestInfo?.listApi) {
                queryData();
            }
        };

        // 配置记录
        const recordRef = useRef();
        const showRecordEvent = () => {
            recordRef.current?.show();
        };
        return (
            <FormItem {...formItemLayout}>
                <PriceEstimationDrawer ref={priceRef} menuType={menuType} />
                <Form form={searchForm} labelAlign="right">
                    <Row gutter={24}>
                        <Col span={8}>
                            <FormItem noStyle name="searchKey">
                                <TextArea
                                    placeholder="请输入场站进行查询,多个场站使用回车间隔"
                                    allowClear
                                />
                            </FormItem>
                        </Col>

                        <Col span={8}>
                            <OperSelectTypeItem
                                // rules={[{ message: '请选择运营商', required: true }]}
                                {...formItemLayout}
                                form={searchForm}
                                initialValue={requestInfo?.recordParams?.operId || undefined}
                                disabled={requestInfo?.recordParams?.operId?.length > 0}
                                disabledFilter={requestInfo?.recordParams?.operId?.length > 0}
                            />
                        </Col>
                        <Col span={8}>
                            <CitysSelect
                                label="城市"
                                name="cityList"
                                placeholder="请选择"
                                formItemLayout={{ labelAlign: 'right' }}
                                showArrow
                                provinceSelectable
                                rules={null}
                                // multiple={false}
                            />
                        </Col>
                    </Row>
                    <br />
                    <Row gutter={[12, 12]}>
                        <Col>
                            <Space>
                                <Button
                                    type="primary"
                                    onClick={async () => {
                                        const params = await searchForm.getFieldsValue();
                                        updateSearchParams((params && { ...params }) || undefined);
                                        changePageNum(1);
                                        updateSelectedRowKeys([]);
                                    }}
                                >
                                    查询
                                </Button>

                                <Button type="primary" onClick={exportFormEvent}>
                                    全部导出
                                </Button>

                                <Button
                                    onClick={() => {
                                        resetData();
                                        updateSearchParams(undefined);
                                    }}
                                >
                                    重置
                                </Button>
                            </Space>
                        </Col>
                        <Col flex={'auto'} />
                        <Col>
                            <div style={{ display: 'flex' }}>
                                {showStasticsEvent && (
                                    <Button
                                        style={{ marginRight: '16px', marginLeft: 'auto' }}
                                        onClick={showStasticsEvent}
                                    >
                                        场站明细
                                    </Button>
                                )}
                                {requestInfo?.recordParams && !isCopy && (
                                    <Button style={exportStyle} onClick={showRecordEvent}>
                                        配置记录
                                    </Button>
                                )}
                            </div>
                        </Col>
                    </Row>
                </Form>

                <br />
                {(!disabled && (
                    <p>
                        <Space>
                            <Popconfirm
                                title="即将删除已勾选场站"
                                onConfirm={() => deleteSelectRows(false, selectedRowKeys)}
                                disabled={!hasSelectedStations}
                            >
                                <Button type="primary" disabled={!hasSelectedStations}>
                                    删除选中项
                                </Button>
                            </Popconfirm>

                            {(deleteAllEnabled && (
                                <Popconfirm
                                    title="即将全部删除，请确认"
                                    onConfirm={() => deleteSelectRows(true, selectedRowKeys)}
                                >
                                    <Button
                                        danger
                                        disabled={!allStations || allStations.length == 0}
                                    >
                                        全部删除
                                    </Button>
                                </Popconfirm>
                            )) ||
                                null}
                        </Space>
                    </p>
                )) ||
                    (canAppend && allStations?.filter((ele) => ele.isAdd) && (
                        <Popconfirm
                            title="即将删除已勾选场站"
                            onConfirm={() => deleteSelectRows(false, selectedRowKeys)}
                            disabled={!hasSelectedStations}
                        >
                            <Button type="primary" disabled={!hasSelectedStations}>
                                删除选中项
                            </Button>
                        </Popconfirm>
                    )) ||
                    null}

                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record) => record.stationId}
                    dataSource={showStations}
                    onChange={changePageInfo}
                    pagination={{
                        current: pageNum,
                        total: showStations.length,
                        pageSize: pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                    expandable={{ defaultExpandAllRows: true }}
                    columns={stationColumns}
                    sticky={{ offsetHeader: 64 }}
                    rowSelection={
                        (!disabled || (canAppend && allStations?.filter((ele) => ele.isAdd))) && {
                            selectedRowKeys,
                            onChange: onSelectChange,
                            getCheckboxProps: (record) => ({
                                disabled:
                                    !record.stationId ||
                                    disabledStationIds?.includes(record.stationId),
                            }),
                        }
                    }
                    locale={{
                        emptyText: (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={emptyText || '暂无数据'}
                            />
                        ),
                    }}
                />

                <RecordList initRef={recordRef} recordParams={requestInfo?.recordParams} />
            </FormItem>
        );
    },
);

// 商家营销子编辑页面
const SearchStationItem = (props, ref) => {
    const {
        value,
        onChange,
        onLoadingToggle,
        disabled,
        purchase,
        currentUser,
        hasStastics, // 是否包含统计按钮
        addEnabled = true, // 是否可新增
        deleteAllEnabled = true, // 是否可全部删除
        limitOperIds, // 限制哪几个运营商可选中，如果配置了，查询维度只支持运营商，无切换维度和添加按钮，且传参格式限制为[{operId, citys:[city1, city2]}]，其中citys为可选
        limitCitys, // 限制只有哪些城市可被选择
        exceptOperIds,
        operChannels, // 运营方式，如果配了，自动过滤所选场站，例[{code: '01', name:'直营'}]
        disabledStationIds, // 某些场站禁用，内部会自动拼已选场站不可再被勾选的逻辑
        limitCityIds, // 限制只有哪些场站可被选择
        isContainAbandon, // 是否包含废弃
        hideRange,
        hasReasonText,
        /** 请求信息配置，传参结构：
        {
            listApi: '...',     // 如果配了接口，分页会调接口，提交需要取delScopeInfo、stationInfo分别对应删除、新增的汇总
            deleteApi: (values) => void,  //和deleteEvent二选一  都配优先使用deleteApi  删除完自动更新展示 默认删除接口传delStations和delAll字段
            deleteEvent: (values) => void,  //可自定义删除事件，需要手动动过ref更新表格展示
            exportEvent: (params, columns) => void,
            recordParams: {relateId: 配置记录相关活动id，有值的话显示配置记录按钮},
            params: {其他参数}
        }*/
        requestInfo,
        pageSize = 1000,
        isCopy, // 如果需要记录操作标识，复制场景需要单独处理，删除就直接抹掉，剩余的默认场站全部都是新增
        emptyText = '暂无数据',
        canAppend, // 是否可新增，仅disabled=true的时候，开放新增入口，新增的场站可被删除
        formTypeWhenLimitOperIds, //当使用运营商白名单限制哪几个运营商可选中时，使用下拉选择“SELECT”，不传或者其他默认展开所有运营商
        maxTagCount, //批量导入服务商选择框最多显示数量
        maxTagPlaceholder, //超过显示文案
        isPrizeOptimize, // 带定价辅助按钮
        premiumFlag = undefined, // 是否过滤出溢价分成生效中的场站
        isChannelRule, //是否企业渠道售价标识 是的话就没有运营商过滤只能选企业
        isEnrollDetail, //是否招商报名详情里的场站列表
        isEnrollExamine, //是否招商报名审核里的场站列表
        isPriceEstimation,
        isWorkorderOrigin,
        menuType,
        submitEvent,
        actInfo,
        cooperationPlatform,
        showFirstTime = true, //是否展示首次上线时间
        extendColumns = [], //列表扩展字段
    } = props;

    const [defaultStations, updateDefaultStations] = useState([]);

    const [allStations, updateAllStations] = useState([]);

    const [isWaiting, updateWaiting] = useState(false);

    const addStations = useMemo(() => {
        let deffList = [];
        allStations?.forEach((element) => {
            let hasDef = false;
            for (const defItem of defaultStations) {
                if (defItem.stationId === element.stationId) {
                    hasDef = true;
                    break;
                }
            }
            if (!hasDef) {
                deffList.push(element);
            }
        });
        return deffList;
    }, [defaultStations, allStations]);

    //本地删除数据   用不上预留
    const delStations = useMemo(() => {
        let deffList = [];
        defaultStations?.forEach((element) => {
            let hasDef = false;
            for (const defItem of allStations) {
                if (defItem.stationId === element.stationId) {
                    hasDef = true;
                    break;
                }
            }
            if (!hasDef) {
                deffList.push(element);
            }
        });
        return deffList;
    }, [defaultStations, allStations]);

    useEffect(() => {
        updateFormInfo({
            addStations: [],
            allStations: [],
            defaultStations: [],
        });
    }, []);

    useEffect(() => {
        updateFormInfo({
            addStations: addStations,
            allStations: allStations,
            defaultStations: defaultStations,
            delStations: delStations,
        });
    }, [addStations, defaultStations, allStations, delStations]);

    const updateFormInfo = (info) => {
        onChange && onChange(info);
    };

    const requestInfoRef = useRef();

    useEffect(() => {
        if (requestInfo) {
            if (!requestInfoRef.current || !isObjectEqual(requestInfoRef.current, requestInfo)) {
                requestInfoRef.current = requestInfo;

                const { listApi } = requestInfo;
                if (listApi) {
                    //详情时通过接口拉取之前配置的所有站点信息，再使用前端做过滤和添加去重
                    initDefaultStationEvent({ ...(requestInfo?.params || {}) }).then(
                        (allDefaults) => {
                            //初始化场站
                            updateDefaultStations(allDefaults);
                            updateAllStations(allDefaults);
                        },
                    );
                }
            }
        }
    }, [requestInfo]);

    const initDefaultStationEvent = async (options) => {
        try {
            updateWaiting(true);
            onLoadingToggle && onLoadingToggle(true);
            const allDefaults = await pullStationEvent(1, options); //查出所有数据
            // allDefaults.forEach((element, index) => {
            //     element.index = index + 1;
            // });

            return allDefaults;
        } catch (error) {
            confirm({
                title: `查询场站失败`,
                content: '请刷新页面重新拉取已配置场站',
                okText: '确定刷新',
                cancelText: '关闭',
                onOk() {
                    window.location.reload();
                },
            });
            return Promise.reject(error);
        } finally {
            updateWaiting(false);
            onLoadingToggle && onLoadingToggle(false);
        }
    };

    const pullStationEvent = async (pageIndex, options = {}) => {
        if (!requestInfo?.listApi) {
            return await Promise.reject('没有配置接口');
        }
        const params = {
            ...options,
            pageIndex: pageIndex,
            pageSize: pageSize,
        };

        try {
            const {
                data: { list, records, total },
            } = await requestInfo.listApi(params);

            const nowPageList = records || list;

            if (Math.ceil(total / pageSize) > pageIndex) {
                const nextPageList = await pullStationEvent(pageIndex + 1, options);
                return nowPageList.concat(nextPageList);
            }
            return nowPageList;
        } catch (error) {
            return Promise.reject(error);
        } finally {
        }
    };

    useEffect(() => {
        if (!isEmpty(value)) {
        }
    }, [value]);

    const disabledIds = useMemo(() => {
        const ids = [...(disabledStationIds || [])];
        allStations?.map((ele) => {
            ids.push(ele.stationId);
        });
        return ids;
    }, [disabledStationIds, allStations]);

    // 维护更多的统计语句，如果有传值，会在统计弹窗里显示在最前面
    const [moreStastics, updateMoreStastics] = useState(undefined);
    const [stasticsVisiable, updateStasticsVisiable] = useState(false);
    const listRef = useRef();
    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        showStastics: (_moreStastics) => {
            updateMoreStastics(_moreStastics);
            if (_moreStastics?.length || allStations?.length) {
                showStasticsEvent();
            } else {
                message.error('无场站明细可展示');
            }
        },
        reset: () => {
            // 清空数据
            refreshAllStations([]);
        },
        hasStations: () => {
            // 获取是否已配置场站
            return allStations.length > 0;
        },
        reload: () => {
            // 如果是走接口刷新列表的，开放刷新事件
            if (requestInfo?.listApi) {
                initDefaultStationEvent({ ...(requestInfo?.params || {}) }).then((allDefaults) => {
                    //初始化场站
                    updateDefaultStations(allDefaults);
                    updateAllStations(allDefaults);
                });
            }
        },
        deleteStations: (stationIds = []) => {
            const filterList = allStations.filter((ele) => !stationIds.includes(ele.stationId));
            refreshAllStations(filterList);
        },
        deleteAtIndex: (stationId) => {
            const filterList = allStations.filter((ele) => ele.stationId !== stationId);
            refreshAllStations(filterList);
        },
        //直接外部设置所有的场站信息，目前仅平台定价页面用，只读，不可修改，不确定其他功能是否可用
        setAllStations: (stations) => {
            updateAllStations(stations);
            updateDefaultStations(stations);
        },
    }));

    const refreshAllStations = (stations) => {
        // 同时要更新form，用于外部页面通过form的方法来获取数据
        updateAllStations([...stations]);
    };

    // 添加运营商
    const operGroupRef = useRef();
    const [operVisible, setOperVisible] = useState(false);

    const showOperModal = () => {
        setOperVisible(true);
    };

    const hideOperModal = () => {
        setOperVisible(false);
    };

    const showStasticsEvent = () => {
        updateStasticsVisiable(true);
    };
    return (
        <Fragment>
            {(disabled && !canAppend) || !addEnabled
                ? null
                : (!disabled || canAppend) && (
                      <div style={{ marginBottom: '20px' }}>
                          <Space>
                              <Button disabled={isWaiting} type="primary" onClick={showOperModal}>
                                  <PlusOutlined />
                                  添加场站
                              </Button>

                              <Button
                                  disabled={isWaiting}
                                  type="primary"
                                  onClick={() => operGroupRef.current.show(allStations)}
                              >
                                  <PlusOutlined />
                                  批量导入
                              </Button>
                          </Space>
                      </div>
                  )}
            <Spin spinning={isWaiting}>
                <StationList
                    defaultStations={defaultStations}
                    delStations={delStations}
                    allStations={allStations}
                    refreshAllStations={refreshAllStations}
                    stationList={allStations}
                    disabled={disabled}
                    showStasticsEvent={hasStastics && showStasticsEvent}
                    requestInfo={requestInfo}
                    deleteAllEnabled={deleteAllEnabled}
                    disabledStationIds={disabledStationIds}
                    ref={listRef}
                    isCopy={isCopy}
                    emptyText={emptyText}
                    canAppend={canAppend}
                    isPrizeOptimize={isPrizeOptimize}
                    isPriceEstimation={isPriceEstimation}
                    isWorkorderOrigin={isWorkorderOrigin}
                    menuType={menuType}
                    submitEvent={submitEvent}
                    actInfo={actInfo}
                    isChannelRule={isChannelRule}
                    isEnrollDetail={isEnrollDetail}
                    isEnrollExamine={isEnrollExamine}
                    showFirstTime={showFirstTime}
                    extendColumns={extendColumns}
                />
            </Spin>

            <AddOperModal
                visible={operVisible}
                allStations={allStations}
                onFinish={(_stationList) => {
                    hideOperModal();
                    _stationList?.length &&
                        refreshAllStations([
                            ...(_stationList?.map((ele) => ({ ...ele, isAdd: true })) || []),
                            ...allStations,
                        ]);
                }}
                purchase={purchase}
                openFlag={null}
                limitOperIds={limitOperIds}
                limitCitys={limitCitys}
                exceptOperIds={exceptOperIds}
                operChannels={operChannels}
                disabledStationIds={disabledIds}
                limitCityIds={limitCityIds}
                hideRange={hideRange}
                formTypeWhenLimitOperIds={formTypeWhenLimitOperIds}
                premiumFlag={premiumFlag}
                isChannelRule={isChannelRule}
                cooperationPlatform={cooperationPlatform}
            />
            <OperGroupImportModal
                isContainAbandon={isContainAbandon}
                initRef={operGroupRef}
                onConfirm={(_stationList) => {
                    _stationList?.length &&
                        refreshAllStations([
                            ...(_stationList?.map((ele) => ({ ...ele, isAdd: true })) || []),
                            ...allStations,
                        ]);
                }}
                purchase={purchase}
                currentUser={currentUser}
                isLimit
                limitOperIds={limitOperIds}
                limitCitys={limitCitys}
                exceptOperIds={exceptOperIds}
                disabledStationIds={disabledIds}
                limitCityIds={limitCityIds}
                hasReasonText={hasReasonText || undefined}
                maxTagCount={maxTagCount}
                maxTagPlaceholder={maxTagPlaceholder}
                premiumFlag={premiumFlag}
                isChannelRule={isChannelRule}
                cooperationPlatform={cooperationPlatform}
            />

            <StasticsModal
                allStations={allStations}
                visible={stasticsVisiable}
                onCancel={() => updateStasticsVisiable(false)}
            >
                {moreStastics}
            </StasticsModal>
        </Fragment>
    );
};
export default forwardRef(SearchStationItem);
