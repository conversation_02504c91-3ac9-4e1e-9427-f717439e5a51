import { ACT_MENU_TYPE } from '@/pages/MarketingManage/BusinessActive/ModuleActive/components/ActModuleConfig';
import { getPriceDiscountList } from '@/services/Marketing/MarketingBusinessActiveApi';
import { useRequest } from 'ahooks';
import { But<PERSON>, Col, Drawer, Row, Table, Typography } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useImperativeHandle, useState } from 'react';
const PriceEstimationDrawer: React.ForwardRefRenderFunction<ActionRef.OpenModal, any> = (
    props,
    ref,
) => {
    const { menuType } = props;
    const [visible, setVisible] = useState(false);
    useImperativeHandle(ref, () => ({
        show: (params) => {
            setVisible(true);
            run(params);
        },
    }));
    const {
        run,
        loading,
        data = {},
    } = useRequest(
        (params) => {
            return getPriceDiscountList(params).then((res) => res?.data);
        },
        {
            manual: true,
        },
    );
    const onClose = () => {
        setVisible(false);
    };
    const columns = [
        {
            title: '投放渠道',
            dataIndex: 'channelTypeName',
        },
        {
            title: '活动周期',
            dataIndex: 'cycleName',
        },
        {
            title: '活动时段',
            dataIndex: 'periodStr',
        },
        {
            title: '营销方式',
            dataIndex: 'actTypeName',
        },
        {
            title: '活动优惠',
            dataIndex: 'discountDesc',
        },
        {
            title: '优惠金额',
            dataIndex: 'discountAmt',
        },
        {
            title: '优惠后服务费金额',
            dataIndex: 'afterServiceAmt',
        },
        {
            title: '优惠后度电金额',
            dataIndex: 'afterChargeAmt',
        },
    ];
    const { defaultDisList = [], vipDisList = [], proVipDisList = [], vThreeDisList = [] } = data;
    return (
        <Drawer
            title="价格估算"
            placement="right"
            width={1300}
            onClose={onClose}
            visible={visible}
            footer={[
                <Button key="1" onClick={() => onClose()}>
                    关闭
                </Button>,
            ]}
        >
            <div style={{ padding: 20 }}>
                {menuType === ACT_MENU_TYPE.COMPANY_ACT && (
                    <Table columns={columns} dataSource={defaultDisList} pagination={false} />
                )}
                {menuType === ACT_MENU_TYPE.BASIC_PRICE && (
                    <Row gutter={[20, 20]}>
                        {!isEmpty(defaultDisList) && (
                            <Col span={24}>
                                <Typography.Paragraph>普通用户</Typography.Paragraph>
                                <Table
                                    columns={columns}
                                    dataSource={defaultDisList}
                                    pagination={false}
                                />
                            </Col>
                        )}
                        {!isEmpty(vThreeDisList) && (
                            <Col span={24}>
                                <Typography.Paragraph>V3用户</Typography.Paragraph>
                                <Table
                                    columns={columns}
                                    dataSource={vThreeDisList}
                                    pagination={false}
                                />
                            </Col>
                        )}
                        {!isEmpty(vipDisList) && (
                            <Col span={24}>
                                <Typography.Paragraph>会员用户</Typography.Paragraph>
                                <Table
                                    columns={columns}
                                    dataSource={vipDisList}
                                    pagination={false}
                                />
                            </Col>
                        )}
                        {!isEmpty(proVipDisList) && (
                            <Col span={24}>
                                <Typography.Paragraph>联户会员Pro用户</Typography.Paragraph>
                                <Table
                                    columns={columns}
                                    dataSource={proVipDisList}
                                    pagination={false}
                                />
                            </Col>
                        )}
                    </Row>
                )}
            </div>
        </Drawer>
    );
};

export default React.forwardRef(PriceEstimationDrawer);
