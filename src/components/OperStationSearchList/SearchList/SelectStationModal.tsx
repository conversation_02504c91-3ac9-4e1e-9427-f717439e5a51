import { useImperativeHandle, forwardRef, useState, useMemo, useRef, useEffect } from 'react';
import { Modal, message } from 'antd';
import XdtProTable from '@/components/XdtProTable/index';
import { useRequest } from 'ahooks';
import { getOperatorList2Api } from '@/services/OperationMng/OperationMngApi';
import { isEmpty, renderTableDataIndexText } from '@/utils/utils';
import { getStationAndChargeDataByCityApi } from '@/services/Marketing/ReadjustApi';

export const stationColumns = [
    {
        title: '省份',
        dataIndex: 'provinceName',
        width: 140,
        renderText(text: string, record: any) {
            return renderTableDataIndexText({ text: record?.provinceName });
        },
        hideInSearch: true,
    },
    {
        title: '城市',
        dataIndex: 'cityName',
        width: 140,

        renderText(text: string, record: any) {
            return renderTableDataIndexText({ text: record?.cityName });
        },
        hideInSearch: true,
    },
    {
        title: '运营商ID',
        dataIndex: 'operId',
        width: 140,
    },
    {
        title: '运营商名称',
        dataIndex: 'operName',
        width: 160,
    },
    {
        title: '站点ID',
        dataIndex: 'stationId',
        width: 140,
    },
    {
        title: '站点名称',
        dataIndex: 'stationName',
        width: 140,
    },
    {
        title: '状态',
        dataIndex: 'stationStatus',
        width: 140,

        hideInSearch: true,
    },
    {
        title: '近期数据状态',
        dataIndex: 'chargeDay',
        width: 200,

        hideInSearch: true,
    },
];

const SelectStationModal = (props: any, ref: any) => {
    const {
        formParams = {},
        multiple = true,
        disabledIds = [],
        limitCitys = [], //过滤城市
        limitStationIds = [], //过滤站点
        onConfirm,
        noSelect = false, //不展示选择组件
    } = props;
    const [visible, toggleVisible] = useState(false);
    const [tableList, updateTableList] = useState([]);
    const [selectList, updateSelectList] = useState<[]>([]); // 已选中的站点列表，用于添加时判断是否重复选择

    const formRef = useRef();

    // useEffect(() => {
    //     run();
    // }, [limitCitys]);

    useImperativeHandle(ref, () => {
        return {
            open: (defaultList?: any[]) => {
                if (!isEmpty(defaultList)) {
                    updateSelectList(defaultList);
                }
                toggleVisible(true);
            },
        };
    });
    const closeModalEvent = () => {
        formRef?.current?.resetFields();

        toggleVisible(false);

        updateSelectList([]);
    };

    const rowSelection = {
        type: multiple ? 'checkbox' : 'radio',
        checkStrictly: false,
        selectedRowKeys: selectList.map((item: any) => item.stationId),
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            if (!multiple) {
                // 替换只能单选，所以不用考虑之前是否已经选过其他优惠券的逻辑
                updateSelectList([...selectedRows] as any);
            } else {
                const tableRoundList: any = [];
                for (const item of tableList) {
                    tableRoundList.push(item);
                }
                // 筛选出非当前页的勾选项，不予处理
                const otherCpns = selectList.filter(
                    (x: any) =>
                        tableRoundList.filter((now: any) => now.stationId == x.stationId).length ==
                        0,
                );
                updateSelectList([...otherCpns, ...selectedRows] as any);
            }
        },
        getCheckboxProps: (record: any) => ({
            disabled: disabledIds?.indexOf(record.stationId) >= 0,
            name: record.stationId,
        }),
    };
    const submitSelect = () => {
        if (!noSelect && isEmpty(selectList)) {
            message.error('请选择站点');
            return;
        }
        onConfirm && onConfirm(selectList);
        closeModalEvent();
    };

    return (
        <Modal
            title="选择站点"
            visible={visible}
            width={1000}
            onCancel={closeModalEvent}
            destroyOnClose
            onOk={() => {
                submitSelect();
            }}
        >
            <XdtProTable
                remeberState={false}
                prefixKey="select-adjust-station"
                formRef={formRef}
                columns={stationColumns}
                scroll={{ x: '100%', y: 400 }}
                rowSelection={!noSelect ? rowSelection : undefined}
                rowKey="stationId"
                requestApi={async (params) => {
                    try {
                        const options = {
                            city: limitCitys?.join(',') || undefined,
                            stationIds: isEmpty(limitStationIds) ? undefined : limitStationIds,
                            ...params,
                        };
                        const {
                            data: { records, total },
                        } = await getStationAndChargeDataByCityApi(options);
                        return {
                            data: {
                                records: records,
                                total: total,
                            },
                        };
                    } catch (error) {
                        return Promise.reject(error);
                    }
                }}
                dataSourceLoadCallback={(list: any[]) => {
                    updateTableList(list);
                }}
            ></XdtProTable>
        </Modal>
    );
};
export default forwardRef(SelectStationModal);
