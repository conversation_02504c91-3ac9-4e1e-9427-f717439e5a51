import React, { Fragment, useContext, useEffect, useMemo, useState } from 'react';
import {
    Form,
    Select,
    Space,
    Input,
    Row,
    Col,
    Button,
    Tooltip,
    Spin,
    Modal,
    Table,
    Alert,
    message,
} from 'antd';
import { CopyOutlined, InfoCircleOutlined, ScanOutlined, DeleteOutlined } from '@ant-design/icons';
import {
    copyTextCommon,
    copyObjectCommon,
    isEmpty,
    getUrlQueryCommon,
    showScanEventCommon,
} from '@/utils/utils';
import { getWXUrlApi } from '@/services/Marketing/MarketingGiftApi';
import {
    addPromotionCodeApi,
    getPromotionCodeListApi,
    delPromotionCodeApi,
} from '@/services/CommonApi';
import { ACT_LINK_TYPES } from '@/config/declare';

import qs from 'qs';

const WX_CODE_TYPES = {
    SCHEME: 'urlscheme',
    SORTLINK: 'urlLink',
    QRCODE: 'qrCode',
};

const CHANNELS = [
    'INSIDE',
    'ALI_OUTSIDE',
    'WECHAT_SCHEME', //微信scheme
    'WECHAT_LINK', //微信LinkUrl
    'WECHAT_QRCODE',
    'BAIDU', //百度app
    'BAIDU_MAP', //百度地图
    'AMAP', //高德地图];
    'POLYMERIZATION', //聚合码
];

const FormItem = Form.Item;
const Option = Select;

const EditableContext = React.createContext(null);
const EditableRow = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};
const EditableCell = ({
    title,
    editable,
    children,
    dataIndex,
    disabled,
    record,
    handleSave,
    channelOptions = [],
    index,
    ...restProps
}) => {
    const form = useContext(EditableContext);

    useEffect(() => {
        if (record) {
            if (record[dataIndex]) {
                form.setFieldsValue({
                    [dataIndex]: record[dataIndex],
                });
            } else {
                form.setFieldsValue({
                    [dataIndex]: null,
                });
            }
        }
    }, [record]);

    const save = async () => {
        try {
            const values = form.getFieldsValue();
            handleSave &&
                handleSave(index, {
                    ...record,
                    ...values,
                });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };
    let childNode = children;
    switch (dataIndex) {
        case 'promotionCode':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    name={dataIndex}
                    required
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(rule, value) {
                                if (!value) {
                                    return Promise.reject('请填写');
                                }
                                const codeReg = new RegExp(/^[0-9a-zA-z]*$/);
                                if (!codeReg.test(value)) {
                                    return Promise.reject('请输入数字、字母格式追踪码');
                                }
                                return Promise.resolve();
                            },
                        }),
                    ]}
                >
                    <Input
                        placeholder="请输入数字、字母格式追踪码"
                        maxLength={15}
                        showCount
                        autoComplete="off"
                        onPressEnter={save}
                        onBlur={save}
                        disabled={disabled}
                    />
                </Form.Item>
            );
            break;
        case 'promotionName':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    name={dataIndex}
                    rules={[{ required: true, message: '请输入中文名称' }]}
                >
                    <Input
                        placeholder="请输入中文名称"
                        autoComplete="off"
                        onPressEnter={save}
                        onBlur={save}
                        disabled={disabled}
                    />
                </Form.Item>
            );
            break;
        case 'promotionChannel':
            childNode = (
                <Form.Item
                    style={{
                        margin: 0,
                    }}
                    name={dataIndex}
                    required
                >
                    <Select placeholder="请选择" onChange={save} onBlur={save}>
                        {channelOptions}
                    </Select>
                </Form.Item>
            );
            break;

        default:
            break;
    }
    return <td {...restProps}>{childNode}</td>;
};

const PopCodeModal = (props) => {
    const {
        visible,
        path,
        mergePath,
        disableds = [],
        readOnly,
        actId,
        actType,
        onClose,
        ...otherProps
    } = props;

    const { mergeLabel } = otherProps;

    const [resultList, setResultList] = useState(() => {
        return readOnly ? [] : [{}];
    });

    useEffect(() => {
        if (visible && actId && actType) {
            initResultEvent();
        }
    }, [visible, actId, actType]);

    const initResultEvent = async () => {
        try {
            const { data } = await getPromotionCodeListApi({
                actId,
                actType,
            });
            if (data instanceof Array && data.length > 0) {
                setResultList(data);
            }
            return data;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const changeOptions = useMemo(() => {
        let keys = Object.keys(ACT_LINK_TYPES);

        return keys
            .filter((ele) => {
                let disFlag = false;
                if (disableds.length > 0) {
                    disFlag = disableds.includes(ACT_LINK_TYPES[ele]);
                }
                if (ele === 'POLYMERIZATION' && !mergePath) {
                    return false;
                }
                return CHANNELS.includes(ele) && !disFlag;
            })
            .map((ele) => {
                let optionLabel = '';
                switch (ele) {
                    case 'INSIDE':
                        optionLabel = '内部运营位投放';
                        break;
                    case 'ALI_OUTSIDE':
                        optionLabel = '支付宝外部投放';

                        break;
                    case 'AMAP':
                        optionLabel = '高德投放';

                        break;
                    case 'BAIDU':
                        optionLabel = '百度APP投放';

                        break;
                    case 'BAIDU_MAP':
                        optionLabel = '百度地图投放';

                        break;
                    case 'WECHAT_SCHEME':
                        optionLabel = '微信投放';
                        break;
                    case 'WECHAT_LINK':
                        optionLabel = '微信短信投放';
                        break;
                    case 'WECHAT_QRCODE':
                        optionLabel = '微信小程序码';

                        break;
                    case 'POLYMERIZATION':
                        optionLabel = mergeLabel || '聚合码';
                        break;

                    default:
                        break;
                }
                const optionsValue = ACT_LINK_TYPES[ele];
                return (
                    <Option
                        key={optionsValue}
                        disabled={disableds.includes(optionsValue)}
                        value={optionsValue}
                    >
                        {optionLabel}
                    </Option>
                );
            });
    });

    const delResultItem = async (index) => {
        try {
            // await delPromotionCodeApi(resultList[index].id);
            const newData = copyObjectCommon(resultList);
            newData[index] = null;
            newData.splice(index, 1);
            setResultList(newData);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const handleSave = async (index, row) => {
        try {
            const newData = copyObjectCommon(resultList);

            const item = newData[index];

            const newItem = {
                ...item,
                ...row,
            };
            if (newItem.promotionCode) {
                newItem.resultUrl = await getFormatLink({
                    path,
                    marketChannel: newItem.promotionCode,
                    linkType: newItem.promotionChannel,
                    mergePath,
                });
            } else {
                newItem.resultUrl = undefined;
            }
            newData.splice(index, 1, newItem);

            setResultList(newData);
        } catch (error) {}
    };

    const columns = [
        {
            title: '序号',
            width: 80,
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '推广码 ',
            dataIndex: 'promotionCode',
            width: 200,
            onCell: (record, index) => ({
                record,
                index: index,
                dataIndex: 'promotionCode',
                title: '推广码',
                handleSave,
                disabled: readOnly,
            }),
        },
        {
            title: '中文名称 ',
            dataIndex: 'promotionName',
            width: 200,
            onCell: (record, index) => ({
                record,
                index: index,
                dataIndex: 'promotionName',
                title: '中文名称',
                handleSave,
                disabled: readOnly,
            }),
        },
        {
            title: '渠道选择 ',
            dataIndex: 'promotionChannel',
            width: 200,
            onCell: (record, index) => ({
                record,
                index: index,
                dataIndex: 'promotionChannel',
                title: '渠道选择',
                handleSave,
                channelOptions: changeOptions,
            }),
        },
        {
            title: '操作 ',
            width: 120,
            render(text, record, index) {
                const delItem = (
                    <DeleteOutlined
                        onClick={() => {
                            delResultItem(index);
                        }}
                    />
                );
                const copyItem = (
                    <CopyOutlined
                        onClick={() => {
                            copyTextCommon(record.resultUrl);
                        }}
                    />
                );
                const scanItem = initScanBtn({
                    linkType: record.promotionChannel,
                    formatLink: record.resultUrl,
                });

                const btnList = [];
                if (record.resultUrl) {
                    if (record.promotionChannel !== ACT_LINK_TYPES.WECHAT_QRCODE) {
                        btnList.push(copyItem);
                    }

                    btnList.push(scanItem);
                }
                if (!readOnly) {
                    btnList.push(delItem);
                }

                return <Space>{btnList}</Space>;
            },
        },
    ];

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const addResultItem = () => {
        if (resultList?.length >= 50) {
            message.error('推广码最多支持50个');
            return;
        }
        const newData = copyObjectCommon(resultList);
        newData.unshift({ promotionCode: '', promotionName: '', promotionChannel: '' });
        setResultList(newData);
    };

    const submitEvent = async () => {
        try {
            const codeReg = new RegExp(/^[0-9a-zA-z]*$/);
            for (let index = 0; index < resultList.length; index++) {
                const element = resultList[index];
                if (!element.promotionCode) {
                    message.error(`请填写第${index + 1}条 推广码`);
                    return;
                }

                if (!codeReg.test(element.promotionCode)) {
                    message.error(`第${index + 1}条 推广码格式错误，请填写数字、字母格式追踪码`);
                    return;
                }

                if (!element.promotionName) {
                    message.error(`请填写第${index + 1}条 推广码的中文名称`);
                    return;
                }
            }
            await addPromotionCodeApi({
                actId,
                actType,
                codeBos: resultList.map((ele) => {
                    return { promotionCode: ele.promotionCode, promotionName: ele.promotionName };
                }),
            });
            message.success('保存成功');
            onClose && onClose();
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    return (
        <Modal
            width={850}
            visible={visible}
            {...otherProps}
            footer={false}
            onCancel={onClose}
            destroyOnClose
        >
            <Alert
                message="* 推广码为分渠道推广需要使用，可用于数据统计与追踪。可通过切换渠道，复制不同渠道下带推广码的链接或二维码。"
                type="error"
            />
            <div className="mg-t-10"></div>
            {!readOnly && (
                <Fragment>
                    <Button
                        type="primary"
                        ghost
                        onClick={() => {
                            addResultItem();
                        }}
                    >
                        新增
                    </Button>
                    <div className="mg-b-10"></div>
                </Fragment>
            )}

            <Table
                dataSource={resultList}
                rowKey={(record, index) => record}
                columns={columns}
                pagination={false}
                components={components}
                style={{ width: '100%' }}
                scroll={{ x: 'max-content', y: 450 }}
            ></Table>
            <Space
                align="end"
                className="mg-t-20"
                style={{ display: 'flex', justifyContent: 'center' }}
            >
                {!readOnly && (
                    <Button
                        type="primary"
                        onClick={() => {
                            submitEvent();
                        }}
                    >
                        提交
                    </Button>
                )}
                <Button onClick={onClose}>取消</Button>
            </Space>
        </Modal>
    );
};

const initWechatPath = async (type, path) => {
    try {
        const jumpHost = path.split('?')[0];
        const urlParams = getUrlQueryCommon(path);

        const query = qs.stringify(urlParams);

        let params = {
            path: jumpHost,
            generateType: type,
        };
        if (query) {
            params.query = query;
        }

        const { data } = await getWXUrlApi(params);
        return data[type];
    } catch (error) {
        return Promise.reject(error);
    }
};

const getFormatLink = async ({ path, marketChannel, linkType, mergePath }) => {
    try {
        let urlPath = '';
        let query = '';
        if (marketChannel) {
            query = `&query=promotionChannel=${marketChannel}`;
        }
        const urlParams = getUrlQueryCommon(path);
        switch (linkType) {
            case ACT_LINK_TYPES.INSIDE:
                const jumpHost = path.split('?')[0];
                urlParams.promotionChannel = marketChannel;
                const pathQuery = qs.stringify(urlParams);

                if (pathQuery) {
                    urlPath = `${jumpHost}?${pathQuery}`;
                } else {
                    urlPath = path;
                }

                break;
            case ACT_LINK_TYPES.ALI_OUTSIDE:
                urlPath = `alipays://platformapi/startapp?appId=2019092067648076&page=${path}${query}`;

                break;
            case ACT_LINK_TYPES.AMAP:
                urlPath = `amapuri://applets/platformapi/startapp?appId=2019092067648076&page=${path}${query}`;

                break;
            case ACT_LINK_TYPES.BAIDU:
                urlPath = `baiduboxapp://swan/********************************${path}&_baiduboxapp={"form":"10810008","ext":{}}`;

                break;
            case ACT_LINK_TYPES.BAIDU_MAP:
                urlParams._baiduboxapp =
                    '%7B%22from%22%3A%221291009800000000%22%2C%22ext%22%3A%7B%7D%7D';
                const mapPath = `mapmnp://swan/********************************/?${qs.stringify(
                    urlParams,
                )}`;
                urlPath = `baidumap://map/mnp?src=near_by&url=${encodeURIComponent(mapPath)}`;
                break;
            case ACT_LINK_TYPES.WECHAT_SCHEME:
                urlPath = await initWechatPath(WX_CODE_TYPES.SCHEME, path);
                break;
            case ACT_LINK_TYPES.WECHAT_LINK:
                urlPath = await initWechatPath(WX_CODE_TYPES.SORTLINK, path);

                break;
            case ACT_LINK_TYPES.WECHAT_QRCODE:
                let formatPath = path[0] === '/' ? path.substring(1, path.length) : path;
                if (formatPath.indexOf('?') < 0) {
                    formatPath = formatPath + '?xdtImg=1';
                }
                urlPath = await initWechatPath(WX_CODE_TYPES.QRCODE, formatPath);

                break;
            case ACT_LINK_TYPES.POLYMERIZATION:
                let mergeHost = mergePath.split('?')[0];
                const mergeParams = getUrlQueryCommon(mergePath);
                mergeParams.promotionChannel = marketChannel;
                const mergePathQuery = qs.stringify(mergeParams);

                if (mergePathQuery) {
                    urlPath = `${mergeHost}?${mergePathQuery}`;
                } else {
                    urlPath = mergePath;
                }
                break;

            default:
                break;
        }

        return urlPath;
    } catch (error) {
        return Promise.reject(error);
    }
};

const initScanBtn = ({ linkType, formatLink }) => {
    let title = '';
    switch (linkType) {
        case ACT_LINK_TYPES.ALI_OUTSIDE:
            title = '支付宝';

            break;
        case ACT_LINK_TYPES.AMAP:
            title = '高德';
            break;
        case ACT_LINK_TYPES.BAIDU:
            title = '百度App';

            break;
        case ACT_LINK_TYPES.BAIDU_MAP:
            title = '百度地图';

            break;
        case ACT_LINK_TYPES.WECHAT_SCHEME:
        case ACT_LINK_TYPES.WECHAT_LINK:
        case ACT_LINK_TYPES.WECHAT_QRCODE:
            title = '微信';
            break;

        case ACT_LINK_TYPES.POLYMERIZATION:
            title = '聚合码';
            break;

        default:
            break;
    }
    let resultLink = undefined;
    if (linkType === ACT_LINK_TYPES.WECHAT_QRCODE) {
        resultLink = `data:image/png;base64,${formatLink}`;
    }
    let btn = (
        <ScanOutlined
            onClick={() => {
                showScanEventCommon(formatLink, title, resultLink);
            }}
        />
    );
    const excludeList = [
        ACT_LINK_TYPES.WECHAT_SCHEME,
        ACT_LINK_TYPES.WECHAT_LINK,
        ACT_LINK_TYPES.INSIDE,
    ];
    if (excludeList.includes(linkType)) {
        return undefined;
    } else {
        return btn;
    }
};

const useActChannelLink = (props) => {
    const { path, mergePath, mergeTips = '', disableds = [], ...otherProps } = props;
    const [linkType, changeLinkType] = useState(ACT_LINK_TYPES.INSIDE);

    const [marketChannel, changeMarketChannel] = useState();

    const [formatLink, updateFormatLink] = useState('');
    const [creatLinkLoading, updateCreatLinkLoading] = useState(false);

    useEffect(() => {
        if (path) {
            initFormatLink();
        }
    }, [path, linkType, marketChannel, mergePath]);

    //异步生成链接
    const initFormatLink = async () => {
        try {
            updateCreatLinkLoading(true);
            const resultUrl = await getFormatLink({ path, marketChannel, linkType, mergePath });
            updateFormatLink(resultUrl);
            return;
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateCreatLinkLoading(false);
        }
    };

    const tooltipTitle = useMemo(() => {
        let title = '';
        switch (linkType) {
            case ACT_LINK_TYPES.INSIDE:
                title = '投放在微信、支付宝、高德小程序内部运营位、H5内部运营位';
                break;
            case ACT_LINK_TYPES.ALI_OUTSIDE:
                title = '投放在支付宝环境内，用于访问唤起新电途支付宝小程序对应页面';

                break;
            case ACT_LINK_TYPES.AMAP:
                title = '投放在高德环境内，用于访问唤起新电途高德小程序对应页面';
                break;
            case ACT_LINK_TYPES.BAIDU:
                title = '投放在百度app环境内，用于访问唤起新电途高德小程序对应页面';

                break;
            case ACT_LINK_TYPES.BAIDU_MAP:
                title = '投放在百度地图app环境内，用于访问唤起新电途高德小程序对应页面';

                break;
            case ACT_LINK_TYPES.WECHAT_SCHEME:
                title =
                    '仅链接（短期有效），投放在微信环境内，用于访问唤起新电途微信小程序对应页面';
                break;
            case ACT_LINK_TYPES.WECHAT_LINK:
                title = '仅链接（短期有效），投放在短信内，用于访问唤起新电途微信小程序对应页面';
                break;
            case ACT_LINK_TYPES.WECHAT_QRCODE:
                title = '长期有效，投放于线下/微信对话框，扫码唤起新电途微信小程序对应页面';
                break;
            case ACT_LINK_TYPES.POLYMERIZATION:
                title =
                    mergeTips || '长期有效，投放于线下，微信或支付宝扫码唤起新电途小程序对应页面';
                break;

            default:
                break;
        }
        return title;
    }, [linkType, mergeTips]);
    const scanCodeBtn = useMemo(() => {
        return initScanBtn({ linkType, formatLink });
    }, [linkType, formatLink]);
    const changeLinkTypeEvent = async (type) => {
        changeLinkType(type);
    };

    const marketChannelItem = useMemo(() => {
        let item = (
            <Input
                bordered={false}
                prefix={
                    <Tooltip title="用于小磨盘埋点统计支付宝端推广渠道数据">
                        推广渠道
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                }
                style={{ width: '280px' }}
                value={marketChannel}
                placeholder="填写的自定义渠道编号用于小磨盘埋点统计支付宝端数据"
                onChange={(event) => {
                    const {
                        target: { value },
                    } = event;
                    changeMarketChannel(value);
                }}
            ></Input>
        );
        const alicludeList = [ACT_LINK_TYPES.ALI_OUTSIDE, ACT_LINK_TYPES.AMAP];
        if (alicludeList.includes(linkType)) {
            return item;
        } else {
            return undefined;
        }
    });

    return {
        linkType,
        formatLink,
        creatLinkLoading,
        tooltipTitle,
        scanCodeBtn,
        changeLinkTypeEvent,
    };
};

const ActLinkItem = (props) => {
    const {
        label = '页面链接',
        path, //必传
        actId, //如需配推广码需要传入actId和actType
        actType,
        disableds = [],
        readOnly = false, //只读限制
        ...otherProps
    } = props;

    const { mergePath, mergeLabel } = otherProps;

    const {
        linkType,
        creatLinkLoading,
        formatLink,
        tooltipTitle,
        scanCodeBtn,
        changeLinkTypeEvent,
    } = useActChannelLink(props);

    const [visibleCodeModal, changeVisibleCodeModal] = useState(false);

    const closeCodeModal = () => {
        changeVisibleCodeModal(false);
    };

    const showCopyBtn = useMemo(() => {
        if (linkType === ACT_LINK_TYPES.WECHAT_QRCODE) {
            return false;
        }
        return true;
    }, [linkType]);

    const changeOptions = useMemo(() => {
        let keys = Object.keys(ACT_LINK_TYPES);

        return keys
            .filter((ele) => {
                let disFlag = false;
                if (disableds.length > 0) {
                    disFlag = disableds.includes(ACT_LINK_TYPES[ele]);
                }
                if (ele === 'POLYMERIZATION' && !mergePath) {
                    return false;
                }
                return CHANNELS.includes(ele) && !disFlag;
            })
            .map((ele) => {
                let optionLabel = '';
                switch (ele) {
                    case 'INSIDE':
                        optionLabel = '内部运营位投放';
                        break;
                    case 'ALI_OUTSIDE':
                        optionLabel = '支付宝外部投放';

                        break;
                    case 'AMAP':
                        optionLabel = '高德投放';

                        break;
                    case 'BAIDU':
                        optionLabel = '百度APP投放';

                        break;
                    case 'BAIDU_MAP':
                        optionLabel = '百度地图投放';

                        break;
                    case 'WECHAT_SCHEME':
                        optionLabel = '微信投放';
                        break;
                    case 'WECHAT_LINK':
                        optionLabel = '微信短信投放';
                        break;
                    case 'WECHAT_QRCODE':
                        optionLabel = '微信小程序码';

                        break;
                    case 'POLYMERIZATION':
                        optionLabel = mergeLabel || '聚合码';

                        break;
                    default:
                        break;
                }
                const optionsValue = ACT_LINK_TYPES[ele];
                return (
                    <Option
                        key={optionsValue}
                        disabled={disableds.includes(optionsValue)}
                        value={optionsValue}
                    >
                        {optionLabel}
                    </Option>
                );
            });
    });

    return (
        <Fragment>
            <FormItem label={label} {...otherProps}>
                <Tooltip title={formatLink} style={{ width: '100%' }}>
                    <Row gutter={20}>
                        <Col flex={1}>
                            <Input
                                value={formatLink}
                                readOnly
                                alt={formatLink}
                                addonBefore={
                                    <Select value={linkType} onChange={changeLinkTypeEvent}>
                                        {changeOptions}
                                    </Select>
                                }
                                suffix={
                                    <Spin spinning={creatLinkLoading}>
                                        <Space>
                                            <Tooltip title={tooltipTitle}>
                                                <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                            </Tooltip>
                                            {showCopyBtn && (
                                                <CopyOutlined
                                                    onClick={() => {
                                                        copyTextCommon(formatLink);
                                                    }}
                                                />
                                            )}

                                            {scanCodeBtn}
                                        </Space>
                                    </Spin>
                                }
                            ></Input>
                        </Col>
                        <Col flex={'0 0 auto'}>
                            {actId && (
                                <Button
                                    type="primary"
                                    ghost
                                    onClick={() => {
                                        changeVisibleCodeModal(true);
                                    }}
                                >
                                    推广码
                                </Button>
                            )}
                        </Col>
                    </Row>
                </Tooltip>
            </FormItem>
            <PopCodeModal
                actId={actId}
                actType={actType}
                path={path}
                disableds={disableds}
                title={'推广码'}
                visible={visibleCodeModal}
                onClose={closeCodeModal}
                readOnly={readOnly}
                mergePath={mergePath}
                mergeLabel={mergeLabel}
            ></PopCodeModal>
        </Fragment>
    );
};
export default ActLinkItem;
