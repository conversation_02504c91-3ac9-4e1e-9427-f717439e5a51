import React, { Fragment, useEffect, useMemo, useState } from 'react';
import { Form, Select, Space, Input, Row, Col, Button, Tooltip, Spin } from 'antd';
import { CopyOutlined, InfoCircleOutlined, ScanOutlined } from '@ant-design/icons';
import { copyTextCommon, isEmpty, getUrlQueryCommon, showScanEventCommon } from '@/utils/utils';
import { getWXUrlApi } from '@/services/Marketing/MarketingGiftApi';
import { ACT_LINK_TYPES } from '@/config/declare';

import qs from 'qs';

const CHANNELS = [
    'INSIDE',
    'ALI_OUTSIDE',
    'WECHAT_SCHEME', //微信scheme
    'WECHAT_LINK', //微信LinkUrl
    'BAIDU', //百度app
    'BAIDU_MAP', //百度地图
    'AMAP', //高德地图];
];

const FormItem = Form.Item;
const Option = Select;

const ActLinkItem = (props) => {
    const {
        path,
        label = '页面链接',
        marketChannel: marketChangeResult,
        disableds = [],
        ...otherProps
    } = props;
    const [linkType, changeLinkType] = useState(ACT_LINK_TYPES.INSIDE);
    const [wxPathInfo, changeWxPathInfo] = useState({
        urlscheme: '',
        urlLink: '',
    });
    const [wxLoading, changeWxLoading] = useState(false);
    const [marketChannel, changeMarketChannel] = useState();
    useEffect(() => {
        if (marketChangeResult) {
            changeMarketChannel(marketChangeResult);
        }
    }, [marketChangeResult]);
    const formatLink = useMemo(() => {
        let urlPath = '';
        let query = '';
        if (marketChannel) {
            query = `&query=promotionChannel=${marketChannel}`;
        }
        switch (linkType) {
            case ACT_LINK_TYPES.INSIDE:
                urlPath = path;
                break;
            case ACT_LINK_TYPES.ALI_OUTSIDE:
                urlPath = `alipays://platformapi/startapp?appId=2019092067648076&page=${path}${query}`;

                break;
            case ACT_LINK_TYPES.AMAP:
                urlPath = `amapuri://applets/platformapi/startapp?appId=2019092067648076&page=${path}${query}`;

                break;
            case ACT_LINK_TYPES.BAIDU:
                urlPath = `baiduboxapp://swan/********************************${path}&_baiduboxapp={"form":"10810008","ext":{}}`;

                break;
            case ACT_LINK_TYPES.BAIDU_MAP:
                const urlParams = getUrlQueryCommon(path);
                urlParams._baiduboxapp =
                    '%7B%22from%22%3A%221291009800000000%22%2C%22ext%22%3A%7B%7D%7D';
                const mapPath = `mapmnp://swan/********************************/?${qs.stringify(
                    urlParams,
                )}`;
                urlPath = `baidumap://map/mnp?src=near_by&url=${encodeURIComponent(mapPath)}`;
                break;
            case ACT_LINK_TYPES.WECHAT_SCHEME:
                urlPath = wxPathInfo.urlscheme;
                break;
            case ACT_LINK_TYPES.WECHAT_LINK:
                urlPath = wxPathInfo.urlLink;

                break;

            default:
                break;
        }
        return urlPath;
    }, [path, linkType, wxPathInfo, marketChannel]);

    const tooltipTitle = useMemo(() => {
        let title = '';
        switch (linkType) {
            case ACT_LINK_TYPES.INSIDE:
                title = '用于投放在小程序内部banner、运营位、弹窗';
                break;
            case ACT_LINK_TYPES.ALI_OUTSIDE:
                title = '投放在小程序外部，用于访问唤起支付宝小程序';

                break;
            case ACT_LINK_TYPES.AMAP:
                title = '投放在小程序外部，用于访问唤起高德小程序';
                break;
            case ACT_LINK_TYPES.BAIDU:
                title = '投放在小程序外部，用于访问唤起百度App小程序';

                break;
            case ACT_LINK_TYPES.BAIDU_MAP:
                title = '投放在小程序外部，用于访问唤起百度地图小程序';

                break;
            case ACT_LINK_TYPES.WECHAT_SCHEME:
                title = '投放在微信聊天群内，用于访问唤起微信小程序';
                break;
            case ACT_LINK_TYPES.WECHAT_LINK:
                title = '用于投放短信，访问唤起微信小程序';
                break;

            default:
                break;
        }
        return title;
    }, [linkType]);
    const scanCodeBtn = useMemo(() => {
        let title = '';
        switch (linkType) {
            case ACT_LINK_TYPES.ALI_OUTSIDE:
                title = '支付宝';

                break;
            case ACT_LINK_TYPES.AMAP:
                title = '高德';
                break;
            case ACT_LINK_TYPES.BAIDU:
                title = '百度App';

                break;
            case ACT_LINK_TYPES.BAIDU_MAP:
                title = '百度地图';

                break;
            case ACT_LINK_TYPES.WECHAT_SCHEME:
            case ACT_LINK_TYPES.WECHAT_LINK:
                title = '微信';
                break;

            default:
                break;
        }
        let btn = (
            <ScanOutlined
                onClick={() => {
                    showScanEventCommon(formatLink, title);
                }}
            />
        );
        const excludeList = [
            ACT_LINK_TYPES.WECHAT_SCHEME,
            ACT_LINK_TYPES.WECHAT_LINK,
            ACT_LINK_TYPES.INSIDE,
        ];
        if (excludeList.includes(linkType)) {
            return undefined;
        } else {
            return btn;
        }
    }, [linkType, formatLink]);
    const changeLinkTypeEvent = async (type) => {
        try {
            const wxList = [ACT_LINK_TYPES.WECHAT_SCHEME, ACT_LINK_TYPES.WECHAT_LINK];
            if (wxList.includes(type)) {
                const wxpath = await initWechatPath();
                if (!isEmpty(wxpath)) {
                    changeWxPathInfo(wxpath);
                }
            }
            changeLinkType(type);
        } catch (error) {}
    };
    const initWechatPath = async () => {
        try {
            const jumpHost = path.split('?')[0];
            const urlParams = getUrlQueryCommon(path);

            const query = qs.stringify(urlParams);

            let params = {
                path: jumpHost,
            };
            if (query) {
                params.query = query;
            }
            changeWxLoading(true);
            const {
                data: { urlscheme, urlLink },
            } = await getWXUrlApi(params);
            return { urlscheme, urlLink };
        } catch (error) {
            return Promise.reject(error);
        } finally {
            changeWxLoading(false);
        }
    };

    const options = useMemo(() => {
        let keys = Object.keys(ACT_LINK_TYPES);
        return keys
            .filter((ele) => {
                let disFlag = false;
                if (disableds.length > 0) {
                    disFlag = disableds.includes(ACT_LINK_TYPES[ele]);
                }
                return CHANNELS.includes(ele) && !disFlag;
            })
            .map((ele) => {
                let optionLabel = '';
                switch (ele) {
                    case 'INSIDE':
                        optionLabel = '内部运营位投放';
                        break;
                    case 'ALI_OUTSIDE':
                        optionLabel = '支付宝外部投放';

                        break;
                    case 'AMAP':
                        optionLabel = '高德投放';

                        break;
                    case 'BAIDU':
                        optionLabel = '百度APP投放';

                        break;
                    case 'BAIDU_MAP':
                        optionLabel = '百度地图投放';

                        break;
                    case 'WECHAT_SCHEME':
                        optionLabel = '微信投放';
                        break;
                    case 'WECHAT_LINK':
                        optionLabel = '微信短信投放';

                        break;

                    default:
                        break;
                }
                const optionsValue = ACT_LINK_TYPES[ele];
                return (
                    <Option
                        key={optionsValue}
                        disabled={disableds.includes(optionsValue)}
                        value={optionsValue}
                    >
                        {optionLabel}
                    </Option>
                );
            });
    });

    const marketChannelItem = useMemo(() => {
        let item = (
            <Input
                bordered={false}
                prefix={
                    <Tooltip title="用于小磨盘埋点统计支付宝端推广渠道数据">
                        推广渠道
                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                    </Tooltip>
                }
                style={{ width: '280px' }}
                value={marketChannel}
                placeholder="填写的自定义渠道编号用于小磨盘埋点统计支付宝端数据"
                onChange={(event) => {
                    const {
                        target: { value },
                    } = event;
                    changeMarketChannel(value);
                }}
            ></Input>
        );
        const alicludeList = [ACT_LINK_TYPES.ALI_OUTSIDE, ACT_LINK_TYPES.AMAP];
        if (alicludeList.includes(linkType)) {
            return item;
        } else {
            return undefined;
        }
    });

    return (
        <Fragment>
            <FormItem label={label} {...otherProps}>
                <Tooltip title={formatLink} style={{ width: '100%' }}>
                    <Input
                        style={{ width: '100%' }}
                        value={formatLink}
                        readOnly
                        alt={formatLink}
                        addonBefore={
                            <Select value={linkType} onChange={changeLinkTypeEvent}>
                                {options}
                            </Select>
                        }
                        addonAfter={marketChannelItem}
                        suffix={
                            <Spin spinning={wxLoading}>
                                <Space>
                                    <Tooltip title={tooltipTitle}>
                                        <InfoCircleOutlined style={{ marginLeft: '6px' }} />
                                    </Tooltip>
                                    <CopyOutlined
                                        onClick={() => {
                                            copyTextCommon(formatLink);
                                        }}
                                    />
                                    {scanCodeBtn}
                                </Space>
                            </Spin>
                        }
                    ></Input>
                </Tooltip>
            </FormItem>
        </Fragment>
    );
};
export default ActLinkItem;
