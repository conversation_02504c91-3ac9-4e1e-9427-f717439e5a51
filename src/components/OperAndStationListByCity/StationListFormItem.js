/**
 * 根据运营商查询对应城市下的站点
 */

import {
    Fragment,
    useEffect,
    useRef,
    useState,
    useMemo,
    useImperativeHandle,
    forwardRef,
} from 'react';
import { Form, Select, TreeSelect, Button, Row, Col, Spin } from 'antd';
import { connect } from 'dva';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import styles from '@/assets/styles/common.less';
import { getCityListApi, getOperAndStationByCityApi } from '@/services/CommonApi';

const FormItem = Form.Item;
const { Option } = Select;

const { SHOW_CHILD } = TreeSelect;

const StationListFormItem = (props, ref) => {
    const {
        form,
        formItemLayout,
        label = '活动区域',
        name = 'areaList',
        disabled,
        // stationsListInfo,
        rules,
        currentUser,
    } = props;

    const cityRef = useRef();

    const [cityList, updateCityList] = useState([]);
    const [formatCityInfo, updateFormatCityInfo] = useState({});

    const [stationList, changeStationList] = useState([]);
    const [stationLoading, toggleStationLoading] = useState(false);

    const [loading, toggleLoading] = useState(false);

    useEffect(() => {
        initEvent();
        initCityStation();
    }, []);

    useEffect(() => {
        cityRef.current = cityList;
    }, [cityList]);

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        init: (list) => {
            try {
                for (let index = 0; index < list.length; index++) {
                    const element = list[index];
                    changeCityEvent(element.city, index);
                }
            } catch (error) {
                console.log(88888888888, error);
            }
        },
    }));

    const initEvent = async () => {
        try {
            // form.setFieldsValue({ [name]: list });
            toggleLoading(true);
            const { data: areaList } = await getCityListApi();

            const list = [];
            for (const item of areaList) {
                for (const city of item.cityList) {
                    list.push({
                        title: city.areaName,
                        value: city.areaCode,
                    });
                }
            }
            updateCityList(list);
            return list;
        } catch (error) {
            throw new Error(error);
        } finally {
            toggleLoading(false);
        }
    };

    const initCityStation = () => {
        const list = form.getFieldValue(name);
        try {
            for (let index = 0; index < list.length; index++) {
                const element = list[index];
                changeCityEvent(element.city, index);
            }
        } catch (error) {}
    };

    const handleSearch = (txt, index) => {
        if (txt) {
            const list = [];
            for (const item of cityRef.current) {
                const searchName = item.title;
                if (searchName.indexOf(txt) >= 0) {
                    list.push(item);
                }
            }
            updateFormatCityInfo({
                ...formatCityInfo,
                [index]: list,
            });
        } else {
            updateFormatCityInfo({
                ...formatCityInfo,
                [index]: cityRef.current,
            });
        }
    };
    const onClearEvent = (index) => {
        updateFormatCityInfo({
            ...formatCityInfo,
            [index]: cityRef.current,
        });
    };

    const handleFilter = (filterTxt, option) => true;
    const cleartCityStation = (value, index) => {
        // 清空选中站点
        if (currentUser.operId != value) {
            const formItemData = form.getFieldValue(name);
            formItemData[index].stationId = [];
            form.setFieldsValue({
                [name]: formItemData,
            });
        }
    };
    const changeCityEvent = async (value, index) => {
        try {
            toggleStationLoading(true);
            const { data: cityAndStationList } = await getOperAndStationByCityApi({
                city: value,
            });
            const formItemData = form.getFieldValue(name);
            const stationId =
                (formItemData && formItemData[index] && formItemData[index].stationId) || [];
            changeStationList((oldList) => {
                if (disabled) {
                    const list = [];
                    cityAndStationList.forEach((ele, eleIndex) => {
                        if (ele.children) {
                            const childList = [];
                            ele.children.forEach((child, childIndex) => {
                                if (stationId.includes(child.value)) {
                                    childList.push({ ...child, disableCheckbox: true });
                                }
                            });
                            if (childList.length > 0) {
                                list.push({
                                    title: ele.title,
                                    value: ele.title,
                                    children: childList,
                                    disableCheckbox: true,
                                });
                            }
                        }
                    });
                    oldList[index] = list;
                } else {
                    oldList[index] = cityAndStationList.map((ele) => {
                        if (disabled) {
                            return {
                                ...ele,
                                disableCheckbox: true,
                                children: ele.children.map((item) => {
                                    return {
                                        ...item,
                                        disableCheckbox: true,
                                    };
                                }),
                            };
                        }
                        return ele;
                    });
                }

                return [...oldList];
            });

            return cityAndStationList;
        } catch (error) {
            console.log(error);
        } finally {
            toggleStationLoading(false);
        }

        // try {
        //     // 清空选中站点
        //     if (currentUser.operId != value) {
        //         const formItemData = form.getFieldValue(name);
        //         formItemData[index].stationId = [];
        //         form.setFieldsValue({
        //             [name]: formItemData,
        //         });
        //     }
        // } catch (error) {
        //     console.log(4444, error);
        // } finally {
        // }
    };

    const treeProps = {
        treeCheckable: true,
        showCheckedStrategy: SHOW_CHILD,
        maxTagCount: 3,
        treeNodeFilterProp: 'title',
        maxTagPlaceholder: (omittedValues) => <span>共选了{omittedValues.length + 3}个</span>,
        // disabled,
        style: {
            width: '100%',
        },
    };

    const canIadd = () => {
        const hasAdd = true;

        return !disabled && hasAdd;
    };

    return (
        <Form.Item name={name} {...formItemLayout} label={label} rules={rules}>
            <Form.List name={name}>
                {(fields, { add, remove }) => (
                    <Fragment>
                        {fields.map((field, index) => (
                            <Fragment key={index}>
                                <Row>
                                    <Col flex="200px">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) => true}
                                            >
                                                {({ getFieldValue }) => {
                                                    const formItemData = getFieldValue(name);
                                                    const cityCodes =
                                                        (formItemData &&
                                                            formItemData.map(
                                                                (ele) => ele && ele.city,
                                                            )) ||
                                                        [];

                                                    const list =
                                                        (formatCityInfo && formatCityInfo[index]) ||
                                                        cityRef.current ||
                                                        [];

                                                    const listOptions = list.map((ele) => {
                                                        return (
                                                            <Option
                                                                key={ele.value}
                                                                disabled={cityCodes.includes(
                                                                    ele.value,
                                                                )}
                                                                value={ele.value}
                                                            >
                                                                {ele.title}
                                                            </Option>
                                                        );
                                                    });

                                                    return (
                                                        <FormItem
                                                            label=""
                                                            fieldKey={field.fieldKey}
                                                            name={[field.name, 'city']}
                                                            validateTrigger={['onChange', 'onBlur']}
                                                            rules={[
                                                                {
                                                                    required: true,
                                                                    message: '请选择城市',
                                                                },
                                                            ]}
                                                        >
                                                            <Select
                                                                showSearch
                                                                onSearch={(txt) => {
                                                                    handleSearch(txt, index);
                                                                }}
                                                                filterOption={handleFilter}
                                                                onChange={(value) => {
                                                                    cleartCityStation(value, index);
                                                                    changeCityEvent(value, index);
                                                                }}
                                                                disabled={disabled}
                                                                onClear={() => {
                                                                    onClearEvent(index);
                                                                }}
                                                            >
                                                                {listOptions}
                                                            </Select>
                                                        </FormItem>
                                                    );
                                                }}
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    <Col flex="1">
                                        <Spin spinning={loading || false}>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(prevValues, curValues) => true}
                                            >
                                                {({ getFieldValue }) => {
                                                    const formItemData = getFieldValue(name);
                                                    const cityCode =
                                                        formItemData &&
                                                        formItemData[index] &&
                                                        formItemData[index].city;

                                                    // let stationList = [];
                                                    // if (cityCode) {
                                                    //     let item = cityRef.current.find(ele => {
                                                    //         return ele.value == cityCode;
                                                    //     });
                                                    //     if (
                                                    //         item &&
                                                    //         item.children &&
                                                    //         item.children instanceof Array
                                                    //     ) {
                                                    //         stationList = item.children;
                                                    //     }
                                                    // }

                                                    // let stationOptions = stationList.map(ele => {
                                                    //     return (
                                                    //         <Option value={ele.value}>
                                                    //             {ele.title}
                                                    //         </Option>
                                                    //     );
                                                    // });
                                                    return (
                                                        <Spin spinning={stationLoading || false}>
                                                            <FormItem
                                                                style={{ margin: '0 8px' }}
                                                                fieldKey={field.fieldKey}
                                                                name={[field.name, 'stationId']}
                                                                validateTrigger={[
                                                                    'onChange',
                                                                    'onBlur',
                                                                ]}
                                                                // rules={[
                                                                //     {
                                                                //         required: true,
                                                                //         message: '请选择场站',
                                                                //     },
                                                                //     ({ getFieldValue }) => ({
                                                                //         validator(rule, value) {
                                                                //             return Promise.resolve();
                                                                //         },
                                                                //     }),
                                                                // ]}
                                                            >
                                                                {/* <Select
                                                                disabled={disabled}
                                                                mode="multiple"
                                                                placeholder={
                                                                    !cityCode
                                                                        ? '请先选择城市'
                                                                        : '全部站点'
                                                                }
                                                                allowClear
                                                                initialValue={[]}
                                                            >
                                                                {stationOptions}
                                                            </Select> */}
                                                                <TreeSelect
                                                                    placeholder={
                                                                        !cityCode
                                                                            ? '请先选择城市'
                                                                            : '全部站点'
                                                                    }
                                                                    treeData={
                                                                        stationList[index] || []
                                                                    }
                                                                    {...treeProps}
                                                                />
                                                            </FormItem>
                                                        </Spin>
                                                    );
                                                }}
                                            </FormItem>
                                        </Spin>
                                    </Col>
                                    {disabled ? null : (
                                        <Col>
                                            <MinusCircleOutlined
                                                className={styles['dynamic-delete-button']}
                                                style={{ margin: '0 8px' }}
                                                onClick={() => {
                                                    remove(field.name);
                                                    updateFormatCityInfo({});
                                                    changeStationList((oldList) => {
                                                        oldList.splice(field.name, 1);
                                                        return oldList;
                                                    });
                                                }}
                                            />
                                        </Col>
                                    )}
                                </Row>
                            </Fragment>
                        ))}
                        <FormItem label="">
                            <Button
                                loading={loading}
                                disabled={!canIadd()}
                                type="dashed"
                                onClick={() => {
                                    add();
                                }}
                                style={{ width: '200px' }}
                            >
                                <PlusOutlined />
                                添加城市
                            </Button>
                        </FormItem>
                    </Fragment>
                )}
            </Form.List>
        </Form.Item>
    );
};
export default forwardRef(StationListFormItem);
