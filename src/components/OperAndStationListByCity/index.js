import { Fragment, useRef, useImperativeHandle, forwardRef } from 'react';
import { Form, Radio } from 'antd';
import StationListFormItem from './StationListFormItem';
import { RANGE_TYPES } from '@/config/declare';

const FormItem = Form.Item;

const OperAndStationListByCity = (props, ref) => {
    const {
        form,
        formItemLayout,
        stationFormItemLayout,
        rangeLabel = '活动范围',
        rangeName = 'areaRangeType',
        stationLabel = '活动区域',
        stationName = 'stationList',
        disabled,
        // stationsListInfo,
        currentUser,
    } = props;

    const operSelectList = useRef();

    useImperativeHandle(ref, () => ({
        // changeVal 就是暴露给父组件的方法
        init: (list) => {
            operSelectList.current && operSelectList.current.init(list);
        },
    }));

    return (
        <Fragment>
            <FormItem
                label={rangeLabel}
                name={rangeName}
                {...formItemLayout}
                initialValue={RANGE_TYPES.ALL}
                rules={[{ required: true, message: '请选择' }]}
            >
                <Radio.Group disabled={disabled}>
                    <Radio value={RANGE_TYPES.ALL}>全部</Radio>
                    <Radio value={RANGE_TYPES.OTHER}>选择区域</Radio>
                </Radio.Group>
            </FormItem>
            <FormItem
                shouldUpdate={(prevValues, curValues) => {
                    return prevValues[rangeName] !== curValues[rangeName];
                }}
                noStyle
            >
                {({ getFieldValue }) => {
                    const areaRangeType = getFieldValue(rangeName);
                    return areaRangeType == RANGE_TYPES.OTHER ? (
                        <StationListFormItem
                            form={form}
                            initRef={operSelectList}
                            label={stationLabel}
                            name={stationName}
                            disabled={disabled}
                            initialValue={[]}
                            currentUser={currentUser}
                            rules={[
                                {
                                    required: true,
                                    message: '请选择活动场站',
                                },
                            ]}
                            formItemLayout={stationFormItemLayout}
                        />
                    ) : null;
                }}
            </FormItem>
        </Fragment>
    );
};
export default forwardRef(OperAndStationListByCity);
