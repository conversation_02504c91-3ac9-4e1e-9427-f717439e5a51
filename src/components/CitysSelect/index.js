import { Button, Form, Row, Col, TreeSelect, message, Modal, Input, Space } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import React, { Fragment, useEffect, useState, useRef, useMemo } from 'react';
import { MATCH_TYPES } from '@/config/declare';
import { getProvinceAndCityApi, getCityListApi, getAllProvinceCityApi } from '@/services/CommonApi';
import { connect } from 'umi';
import { cityImportApi } from '@/services/CommonApi';
import { copyObjectCommon } from '@/utils/utils';

const { TextArea } = Input;
const { SHOW_CHILD } = TreeSelect;

const CitysSelect = (props) => {
    // form item 属性
    const {
        form,
        dispatch,
        global: { cityList },
        name = 'citys',
        label = '区域',
        rules = [{ required: true, message: '请选择' }],
        formItemLayout,
        isNewDataPermission,
        provinceFilterEmpty, // 是否要过滤掉无城市的省
        required = false,
        onChangeOption,
        ...treeProps
    } = props;

    // tree 属性
    const {
        showCheckedStrategy = SHOW_CHILD,
        treeNodeFilterProp = 'title',
        placeholder = '请选择',
        loadData = undefined,
        multiple = true,
        provinceSelectable = false,
        disabledIds,
        defaultCityIds, // 默认已选的城市id，如果未出现在initTree的数据源里，要截取前3位，去拉取对应的省下的城市列表
        canSelectCityIds, //限制可选城市
        ...restTreeProps
    } = treeProps;

    const [cityTreeData, changeCityTreeData] = useState([]);
    const [pendingList, updatePendingList] = useState([]); // 缓存待加载列表，等init方法执行后拼接

    const initCityTree = async () => {
        try {
            const allList = [];

            if (provinceSelectable) {
                let areaList = [...cityList];
                if (!areaList?.length) {
                    if (isNewDataPermission) {
                        const { data } = await getAllProvinceCityApi();
                        data.map((ele) => {
                            ele.cityList = ele.childs;
                            ele.childs = undefined;
                        });
                        areaList = data;
                    } else {
                        await dispatch({
                            type: 'global/initCityList',
                            callback: (list) => {
                                areaList = [...list];
                            },
                        });
                    }
                }

                for (const item of areaList) {
                    const children =
                        item.cityList?.map((ele) => ({
                            id: Number(ele.areaCode),
                            pId: Number(item.areaCode),
                            title: ele.areaName,
                            value: ele.areaCode,
                            key: ele.areaCode,
                            isLeaf: true,
                        })) || undefined;
                    if ((provinceFilterEmpty && children?.length) || !provinceFilterEmpty) {
                        allList.push({
                            id: Number(item.areaCode),
                            pId: 0,
                            title: item.areaName,
                            value: item.areaCode,
                            key: item.areaCode,
                            checkable: provinceSelectable,
                            selectable: provinceSelectable && multiple,
                            disableCheckbox: !item.cityList || item.cityList?.length == 0,
                            children,
                        });
                    }
                }
            } else {
                const { data: areaList } = await getProvinceAndCityApi();

                for (const item of areaList) {
                    allList.push({
                        id: Number(item.areaCode),
                        pId: 0,
                        title: item.areaName,
                        value: item.areaCode,
                        key: item.areaCode,
                        checkable: provinceSelectable,
                        selectable: provinceSelectable,

                        isLeaf: false,
                    });
                }
            }
            changeCityTreeData([...allList]);
            return Promise.resolve(allList);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        if (!cityTreeData?.length) {
            if (defaultCityIds?.length) {
                // 判断是否已经加载了数据源，防止重复拉取
                initCityTree().then(async (list) => {
                    const initList = copyObjectCommon(list);
                    let pendingList = [];
                    let defaultCityIdArray = defaultCityIds;
                    if (typeof defaultCityIds == 'string') {
                        defaultCityIdArray = defaultCityIds.split(',');
                    }

                    // 加载完基本的省列表后，开始判断城市id是否在列表中
                    for (let index = 0; index < defaultCityIdArray.length; index++) {
                        const defaultId = defaultCityIdArray[index];
                        // 判断当前的id是否存在于数据列表里
                        const has = initList.filter((ele) => defaultId == ele.value);
                        // 不在列表中，说明要找到上级省的列表
                        if (!has || has.length == 0) {
                            // 找到归属的省value
                            const provinces = initList.filter((provinceEle) => {
                                if (defaultId == '110000') {
                                    // 北京直接返回
                                    return '100000';
                                }

                                return (
                                    (provinceEle.pId == 0 &&
                                        `${defaultId}`.length >= 4 &&
                                        `${provinceEle.value}`.startsWith(
                                            `${defaultId}`.slice(0, 3),
                                        ) &&
                                        provinceEle.value) ||
                                    undefined
                                );
                            });
                            // 判断是否已经在pengding list内，判重
                            if (provinces?.length) {
                                const provinceObj = provinces[0];
                                const hasPending = pendingList.filter(
                                    (ele) => provinceObj.value == ele.value,
                                );
                                if (!hasPending || hasPending.length == 0) {
                                    pendingList = [...pendingList, provinceObj];
                                }
                            }
                        }
                    }

                    updatePendingList(pendingList);
                    // const getAllCitys = async (_pendingList) =>
                    //     Promise.all(_pendingList.map((ele) => getCitysByProvince(ele.value)));
                    // const cityList = await getAllCitys(pendingList);
                    // let trees = copyObjectCommon(initList);

                    // cityList?.forEach((ele) => {
                    //     trees = [...trees, ...(ele || [])];
                    // });

                    changeCityTreeData(copyObjectCommon(initList));
                });
            } else {
                initCityTree();
            }
        }
    }, [defaultCityIds]);

    const canSelectCityTreeData = useMemo(() => {
        if (canSelectCityIds?.length) {
            if (provinceSelectable) {
                const list = [];
                for (const item of cityTreeData) {
                    if (canSelectCityIds.includes(item.key)) {
                        list.push(item);
                    } else if (item.children?.length) {
                        const cityList = [];
                        for (const cityItem of item.children) {
                            if (canSelectCityIds.includes(cityItem.key)) {
                                cityList.push(cityItem);
                            }
                        }
                        if (cityList?.length) {
                            list.push({ ...item, children: cityList });
                        }
                    }
                }
                return list;
            } else {
                let cityList = cityTreeData?.filter((ele) => {
                    if (canSelectCityIds.includes(ele.value)) {
                        return true;
                    }

                    return false;
                });

                return cityList;
            }
        }
        return cityTreeData;
    }, [canSelectCityIds, cityTreeData]);

    const getCitysByProvince = async (areaCode) => {
        try {
            const { data: areaList } = await getProvinceAndCityApi({
                province: areaCode,
            });
            const list = [];
            for (const item of areaList) {
                list.push({
                    id: Number(item.areaCode),
                    pId: Number(areaCode),
                    title: item.areaName,
                    value: item.areaCode,
                    key: item.areaCode,
                    isLeaf: true,
                    disabled: disabledIds?.indexOf(`${item.areaCode}`) > -1,
                });
            }
            return Promise.resolve(list);
        } catch (error) {
            return Promise.reject();
        }
    };

    const onLoadDataEvent = async (treeNode) => {
        try {
            const { id } = treeNode.props;
            if (
                (provinceSelectable && cityTreeData?.filter((ele) => ele.value == id).length) ||
                pendingList?.filter((ele) => ele.value == id).length
            ) {
                // 已经加载过，就不要重复加载了
                return Promise.resolve(cityTreeData);
            }
            const { data: areaList } = await getProvinceAndCityApi({
                province: id,
            });
            const list = [];
            for (const item of areaList) {
                list.push({
                    id: Number(item.areaCode),
                    pId: id,
                    title: item.areaName,
                    value: item.areaCode,
                    key: item.areaCode,
                    isLeaf: true,
                    disabled: disabledIds?.indexOf(`${item.areaCode}`) > -1,
                });
            }

            const newTree = [...(cityTreeData || []), ...(list || [])];
            changeCityTreeData([...newTree]);
            return Promise.resolve(newTree);
        } catch (error) {
            return Promise.reject(error);
        }
    };

    useEffect(() => {
        // 动态变更交互事件
        if (cityTreeData?.length) {
            const copyCityTreeData = copyObjectCommon(cityTreeData);
            const newTree = copyCityTreeData?.map((ele) => {
                ele.disabled = ele && disabledIds?.indexOf(`${ele.value}`) > -1;
                if (ele.children instanceof Array) {
                    ele.children.forEach((element) => {
                        element.disabled = element && disabledIds?.indexOf(`${element.value}`) > -1;
                    });
                }
                return ele;
            });

            changeCityTreeData(newTree);
        }
    }, [disabledIds]);

    const useProps = useMemo(() => {
        return {
            treeDataSimpleMode: true,
            treeCheckable: multiple,
            showCheckedStrategy,
            placeholder,
            maxTagCount: 'responsive',
            treeNodeFilterProp,
            style: {
                width: '100%',
            },
            loadData: loadData === undefined ? onLoadDataEvent : loadData,
            treeData: (canSelectCityIds && canSelectCityTreeData) || cityTreeData,
            onDropdownVisibleChange: (open) => {
                if (open && !cityTreeData?.length) {
                    try {
                        initCityTree();
                    } catch (error) {}
                }
            },
            onChange: (value, label, extra) => {
                onChangeOption?.(value, label);
            },
            multiple,
            allowClear: true,
            ...restTreeProps,
        };
    }, [cityTreeData, canSelectCityTreeData, restTreeProps]);

    // 文本导入
    const [textForm] = Form.useForm(); // 文本输入
    const [showTextModal, toggleShowTextModal] = useState(false);
    const [textUploading, updateTextUploading] = useState(false); // 文本是否正在上传

    const [matchStations, updateMatchStations] = useState([]); // 存储所有已匹配到的场站
    const [unmatchStations, updateUnmatchStations] = useState([]); // 存储所有未匹配到的场站
    const [doubleMatchStations, updateDoubleMatchStations] = useState([]); // 存储所有重复匹配到的场站

    const [showImportResultModal, updateShowImportResultModal] = useState(false);
    const [importResultPageInfo, changeImportResultPageInfo, onImportResulTableChange] =
        usePageState({}, props);

    const showModalEvent = () => {
        updateMatchStations([]);
        updateUnmatchStations([]);
        updateDoubleMatchStations([]);
        toggleShowTextModal(true);
        textForm.resetFields();
        changeImportResultPageInfo({ pageIndex: 1 });
    };

    const allStations = useMemo(() => {
        return [...unmatchStations, ...doubleMatchStations, ...matchStations];
    }, [matchStations, doubleMatchStations, unmatchStations]); // 存储所有已记录的场站

    const importSuccess = (list) => {
        const matchArr = [];
        const unmatchArr = [];
        const doubleMatchArr = []; // 重复匹配
        const defaultValue = form.getFieldValue(name);
        list?.forEach((ele) => {
            const doubleArr = [
                ...matchArr,
                ...unmatchArr,
                ...doubleMatchArr,
                // ...matchStations,
                // ...unmatchStations,
                // ...doubleMatchStations,
            ].filter((item) => {
                // 如果是未匹配，不返回id，所以要用名称来匹配
                return item.areaName == ele.areaName;
            }); // 记录重合的数组
            if (doubleArr?.length) {
                // 如果有重合数组，先移除，再添加
                doubleArr.forEach((item) => {
                    const arr = [
                        matchStations,
                        matchArr,
                        unmatchStations,
                        unmatchArr,
                        doubleMatchStations,
                        doubleMatchArr,
                    ];
                    arr.forEach((subArr) => {
                        const index = subArr.indexOf(item);

                        if (index >= 0) {
                            subArr.splice(index, 1);
                        }
                    });
                });
            }

            if (ele?.areaName?.length) {
                if (!ele.matchFlag) {
                    ele.matchFlag = MATCH_TYPES.MATCHED;
                    ele.reason = '导入成功';
                }
                if (
                    ele.matchFlag != MATCH_TYPES.UNMATCHED &&
                    canSelectCityIds &&
                    canSelectCityIds.some((cityId) => cityId == ele.areaCode) == false
                ) {
                    // 限制可选
                    ele.matchFlag = MATCH_TYPES.UNMATCHED;
                    ele.reason = '城市不支持选中';
                } else if (
                    ele.matchFlag != MATCH_TYPES.UNMATCHED &&
                    disabledIds?.some((cityId) => cityId == ele.areaCode)
                ) {
                    ele.matchFlag = MATCH_TYPES.UNMATCHED;
                    ele.reason = '城市不支持选中';
                } else if (
                    ele.matchFlag == MATCH_TYPES.MATCHED &&
                    defaultValue?.length &&
                    defaultValue?.some((cityId) => cityId == ele.areaCode)
                ) {
                    // 重复选中
                    ele.matchFlag = MATCH_TYPES.DOUBLEMACHED;
                    ele.reason = '已被添加';
                }
                if (ele.matchFlag == MATCH_TYPES.MATCHED) {
                    matchArr.push(ele);
                } else if (ele.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    doubleMatchArr.push(ele);
                } else {
                    unmatchArr.push(ele);
                }
            }
        });
        // 每次导入后都是最新的列表，覆盖之前的数据
        updateMatchStations([...matchArr]);
        updateDoubleMatchStations([...doubleMatchArr]);
        updateUnmatchStations([...unmatchArr]);
    };

    useEffect(() => {
        if (matchStations?.length) {
            const defaultValue = form.getFieldValue(name) || [];
            form.setFieldsValue({
                [name]: [...defaultValue, ...matchStations.map((ele) => ele.areaCode)],
            });
        }
    }, [matchStations]);

    const importResultColumns = [
        {
            title: '序号 ',
            width: '60px',
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '城市 ',
            dataIndex: 'areaName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态 ',
            dataIndex: 'reason',
            render(text, record) {
                let color = '#cccccc';
                if (
                    record.matchFlag == MATCH_TYPES.UNMATCHED ||
                    record.matchFlag == MATCH_TYPES.DOUBLEMACHED
                ) {
                    // 红色
                    color = '#f50000';
                } else if (record.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    // 黄色
                    color = '#ff9901';
                } else if (record.matchFlag == MATCH_TYPES.MATCHED) {
                    // 绿色
                    color = '#87d068';
                }

                return (
                    <span style={{ color }} title={text}>
                        {text}
                    </span>
                );
            },
        },
    ];
    return (
        <>
            {(form && (
                <Form.Item label={label} rules={rules} {...formItemLayout} required={required}>
                    <Row gutter={24}>
                        <Col span={20}>
                            <Form.Item name={name} noStyle>
                                <TreeSelect {...useProps} />
                            </Form.Item>
                        </Col>
                        <Col span={4}>
                            <Button
                                type="primary"
                                onClick={() => {
                                    showModalEvent();
                                }}
                            >
                                导入
                            </Button>
                        </Col>
                    </Row>
                </Form.Item>
            )) || (
                <Form.Item
                    name={name}
                    label={label}
                    rules={rules}
                    {...formItemLayout}
                    required={required}
                >
                    <TreeSelect {...useProps} />
                </Form.Item>
            )}

            <Modal
                title="文本导入"
                width={400}
                visible={showTextModal}
                onCancel={() => toggleShowTextModal(false)}
                onOk={() => {
                    updateTextUploading(true);
                    textForm
                        .validateFields()
                        .then(async (params) => {
                            const formData = new FormData();
                            for (const key in params) {
                                formData.append(key, params[key]);
                            }

                            const { data } = await cityImportApi(formData);
                            importSuccess(data?.matchList);
                            updateShowImportResultModal(true);
                            updateTextUploading(false);
                            toggleShowTextModal(false);
                            message.success('导入成功');
                        })
                        .catch((e) => {
                            updateTextUploading(false);
                        });
                }}
                okButtonProps={{ disabled: textUploading }}
                maskClosable={false}
            >
                <Form form={textForm} name="textForm">
                    <Form.Item
                        label="输入文本"
                        name="cityNames"
                        rules={[{ required: true, message: '请输入城市' }]}
                    >
                        <TextArea placeholder="输入城市，空格或回车间隔" rows={4} />
                    </Form.Item>
                </Form>
            </Modal>

            <Modal
                title="导入结果"
                width={800}
                visible={showImportResultModal}
                onCancel={() => updateShowImportResultModal(false)}
                footer={null}
                maskClosable={false}
            >
                <Space size="large">
                    <div>
                        成功：<span style={{ color: '#87d068' }}>{matchStations?.length || 0}</span>
                    </div>
                    <div>
                        失败：
                        <span style={{ color: '#f50000' }}>
                            {unmatchStations?.length + doubleMatchStations?.length}
                        </span>
                    </div>
                </Space>
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={allStations}
                    columns={importResultColumns}
                    onChange={onImportResulTableChange}
                    pagination={{
                        current: importResultPageInfo?.pageIndex,
                        total: allStations?.length,
                        pageSize: importResultPageInfo?.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Modal>
        </>
    );
};

export default connect(({ global }) => ({
    global,
}))(CitysSelect);
