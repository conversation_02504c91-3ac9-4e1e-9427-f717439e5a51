import { Button, Form, Row, Col, TreeSelect, message, Modal, Input, Space } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import React, { Fragment, useEffect, useState, useRef, useMemo, useImperativeHandle } from 'react';
import { MATCH_TYPES } from '@/config/declare';
import { connect } from 'umi';
import { cityImportApi } from '@/services/CommonApi';

const { TextArea } = Input;

const CityImportModal = (props) => {
    const { initRef, onFinish } = props;

    // 文本导入
    const [textForm] = Form.useForm(); // 文本输入
    const [showTextModal, toggleShowTextModal] = useState(false);
    const [textUploading, updateTextUploading] = useState(false); // 文本是否正在上传

    const [disabledIds, updateDisabledIds] = useState(undefined); // 不可选的场站id列表
    const [enabledIds, updateEnabledIds] = useState(undefined); // 可选的场站id列表
    const [ids, updateIds] = useState(undefined); // 已选的场站id列表

    const [matchStations, updateMatchStations] = useState([]); // 存储所有已匹配到的场站
    const [unmatchStations, updateUnmatchStations] = useState([]); // 存储所有未匹配到的场站
    const [doubleMatchStations, updateDoubleMatchStations] = useState([]); // 存储所有重复匹配到的场站

    const [showImportResultModal, updateShowImportResultModal] = useState(false);
    const [importResultPageInfo, changeImportResultPageInfo, onImportResulTableChange] =
        usePageState({}, props);

    useImperativeHandle(initRef, () => ({
        show: ({ ids: _ids = [], disabledIds: _disabledIds = [], enabledIds: _enabledIds }) => {
            updateDisabledIds([..._disabledIds]);
            if (_enabledIds?.length) {
                updateEnabledIds([..._enabledIds]);
            } else {
                updateEnabledIds(undefined);
            }
            updateIds([..._ids]);

            updateMatchStations([]);
            updateUnmatchStations([]);
            updateDoubleMatchStations([]);
            toggleShowTextModal(true);
            textForm.resetFields();
            changeImportResultPageInfo({ pageIndex: 1 });
        },
    }));

    const allStations = useMemo(() => {
        return [...unmatchStations, ...doubleMatchStations, ...matchStations];
    }, [matchStations, doubleMatchStations, unmatchStations]); // 存储所有已记录的场站

    const importSuccess = (list) => {
        const matchArr = [];
        const unmatchArr = [];
        const doubleMatchArr = []; // 重复匹配
        list?.forEach((ele) => {
            const doubleArr = [
                ...matchArr,
                ...unmatchArr,
                ...doubleMatchArr,
                // ...matchStations,
                // ...unmatchStations,
                // ...doubleMatchStations,
            ].filter((item) => {
                // 如果是未匹配，不返回id，所以要用名称来匹配
                return item.areaName == ele.areaName;
            }); // 记录重合的数组
            if (doubleArr?.length) {
                // 如果有重合数组，先移除，再添加
                doubleArr.forEach((item) => {
                    const arr = [
                        matchStations,
                        matchArr,
                        unmatchStations,
                        unmatchArr,
                        doubleMatchStations,
                        doubleMatchArr,
                    ];
                    arr.forEach((subArr) => {
                        const index = subArr.indexOf(item);

                        if (index >= 0) {
                            subArr.splice(index, 1);
                        }
                    });
                });
            }

            if (ele?.areaName?.length) {
                if (!ele.matchFlag) {
                    ele.matchFlag = MATCH_TYPES.MATCHED;
                    ele.reason = '导入成功';
                }
                if (
                    ele.matchFlag != MATCH_TYPES.UNMATCHED &&
                    enabledIds?.some((cityId) => cityId == ele.areaCode) == false
                ) {
                    // 限制可选
                    ele.matchFlag = MATCH_TYPES.UNMATCHED;
                    ele.reason = '城市不支持选中';
                } else if (
                    ele.matchFlag != MATCH_TYPES.UNMATCHED &&
                    disabledIds?.some((cityId) => cityId == ele.areaCode)
                ) {
                    ele.matchFlag = MATCH_TYPES.UNMATCHED;
                    ele.reason = '城市不支持选中';
                } else if (
                    ele.matchFlag == MATCH_TYPES.MATCHED &&
                    ids?.some((cityId) => cityId == ele.areaCode)
                ) {
                    // 重复选中
                    ele.matchFlag = MATCH_TYPES.DOUBLEMACHED;
                    ele.reason = '已被添加';
                }
                if (ele.matchFlag == MATCH_TYPES.MATCHED) {
                    matchArr.push(ele);
                } else if (ele.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    doubleMatchArr.push(ele);
                } else {
                    unmatchArr.push(ele);
                }
            }
        });
        // 每次导入后都是最新的列表，覆盖之前的数据
        updateMatchStations([...matchArr]);
        updateDoubleMatchStations([...doubleMatchArr]);
        updateUnmatchStations([...unmatchArr]);
    };

    useEffect(() => {
        if (matchStations?.length && onFinish) {
            message.success('导入成功');
            onFinish(matchStations.map((ele) => ele.areaCode));
        }
    }, [matchStations]);

    const importResultColumns = [
        {
            title: '序号 ',
            width: '60px',
            render(text, record, index) {
                return <span title={index}>{index + 1}</span>;
            },
        },
        {
            title: '城市 ',
            dataIndex: 'areaName',
            width: '120px',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态 ',
            dataIndex: 'reason',
            width: 160,
            render(text, record) {
                let color = '#cccccc';
                if (
                    record.matchFlag == MATCH_TYPES.UNMATCHED ||
                    record.matchFlag == MATCH_TYPES.DOUBLEMACHED
                ) {
                    // 红色
                    color = '#f50000';
                } else if (record.matchFlag == MATCH_TYPES.DOUBLEMACHED) {
                    // 黄色
                    color = '#ff9901';
                } else if (record.matchFlag == MATCH_TYPES.MATCHED) {
                    // 绿色
                    color = '#87d068';
                }

                return (
                    <span style={{ color }} title={text}>
                        {text}
                    </span>
                );
            },
        },
    ];
    return (
        <>
            <Modal
                title="文本导入"
                width={400}
                visible={showTextModal}
                onCancel={() => toggleShowTextModal(false)}
                onOk={() => {
                    updateTextUploading(true);
                    textForm
                        .validateFields()
                        .then(async (params) => {
                            const formData = new FormData();
                            for (const key in params) {
                                formData.append(key, params[key]);
                            }

                            const { data } = await cityImportApi(formData);
                            importSuccess(data?.matchList);
                            updateShowImportResultModal(true);
                            updateTextUploading(false);
                            toggleShowTextModal(false);
                        })
                        .catch((e) => {
                            updateTextUploading(false);
                        });
                }}
                okButtonProps={{ disabled: textUploading }}
                maskClosable={false}
            >
                <Form form={textForm} name="textForm">
                    <Form.Item
                        label="输入文本"
                        name="cityNames"
                        rules={[
                            { required: true, message: '请输入城市' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    const res = value?.replaceAll(' ', '');
                                    if (value?.length && !res?.length) {
                                        return Promise.reject('请输入城市');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <TextArea placeholder="输入城市，空格或回车间隔" rows={4} />
                    </Form.Item>
                </Form>
            </Modal>

            <Modal
                title="导入结果"
                width={800}
                visible={showImportResultModal}
                onCancel={() => updateShowImportResultModal(false)}
                footer={null}
                maskClosable={false}
            >
                <Space size="large">
                    <div>
                        成功：<span style={{ color: '#87d068' }}>{matchStations?.length || 0}</span>
                    </div>
                    <div>
                        失败：
                        <span style={{ color: '#f50000' }}>
                            {unmatchStations?.length + doubleMatchStations?.length}
                        </span>
                    </div>
                </Space>
                <TablePro
                    scroll={{ x: 'max-content' }}
                    rowKey={(record, index) => index}
                    dataSource={allStations}
                    columns={importResultColumns}
                    onChange={onImportResulTableChange}
                    pagination={{
                        current: importResultPageInfo?.pageIndex,
                        total: allStations?.length,
                        pageSize: importResultPageInfo?.pageSize,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                    }}
                />
            </Modal>
        </>
    );
};

export default CityImportModal;
