import React, {
    Fragment,
    useEffect,
    useState,
    useImperativeHandle,
    useRef,
    useCallback,
} from 'react';
import { Button, Popover, Badge, Space, message, Descriptions, Spin, Modal } from 'antd';
import usePageState from '@/hooks/usePageState.js';
import TablePro from '@/components/TablePro';
import {
    FolderOpenOutlined,
    DownOutlined,
    ExclamationCircleTwoTone,
    SyncOutlined,
} from '@ant-design/icons';
import { connect } from 'umi';
import { useMemo } from 'react';
import commonStyles from '@/assets/styles/common.less';

import {
    uploadFileCacheApi,
    uploadFileMngCacheApi,
    getUploadFileCacheListApi,
    downloadUploadFileCacheListApi,
} from '@/services/CommonApi';

const CacheAreaView = (props) => {
    const {
        dispatch,
        bizType,
        bizTypeAll, // 用于mng缓存列表查询，多个业务类型逗号隔开，取该值
        requestParams = {},
        cacheModel: { cacheAreaList, cacheAreaListTotal },
        listLoading,
        initRef,
        isMng = true,
    } = props;

    // 处理文件暂存区
    const [enableDownloadCount, updateEnableDownloadCount] = useState(0);
    const [showAreaView, updateShowAreaView] = useState(false);

    useImperativeHandle(initRef, () => ({
        count: () => {
            updateEnableDownloadCount(enableDownloadCount + 1);
        },
        clearCount: () => {
            updateEnableDownloadCount(0);
        },
        apply: (params) => {
            return new Promise(async (resolve, reject) => {
                try {
                    if (params) {
                        let uploadApi = uploadFileCacheApi;
                        if (isMng) {
                            uploadApi = uploadFileMngCacheApi;
                        }
                        await uploadApi({ uploadParam: JSON.stringify(params), bizType });
                    }

                    message.success('提交成功，请到文件暂存区查看');
                    resolve();
                } catch {
                    reject();
                }
            });
        },
        reload: () => {
            if (pageInfo.pageIndex == 1) {
                searchData();
            } else {
                changePageInfo((state) => ({
                    ...state,
                    pageIndex: 1,
                }));
            }
        },
    }));

    const [pageInfo, changePageInfo, onTableChange] = usePageState({ pageSize: 5 });

    useEffect(() => {
        showAreaView && searchData();
    }, [pageInfo]);

    const [isWaiting, updateWaiting] = useState(false);
    // 调用搜索接口
    const searchData = async () => {
        try {
            updateWaiting(true);
            const params = {
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                bizType: bizTypeAll || bizType,
                ...requestParams,
            };
            const {
                data: { records, total },
            } = await getUploadFileCacheListApi(params);
            dispatch({
                type: 'cacheModel/updateCacheProperty',
                params: {
                    cacheAreaList: records,
                    cacheAreaListTotal: total,
                },
            });
        } catch (error) {
            return Promise.reject(error);
        } finally {
            updateWaiting(false);
        }
    };

    const downLoadFile = async (id, ossFilePath) => {
        if (ossFilePath) {
            // 如果返回oss地址，直接下载
            window.open(ossFilePath);
        } else {
            try {
                let params = {
                    id,
                };
                await downloadUploadFileCacheListApi(params);
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        }
    };

    const downloadCacheColumns = useMemo(() => {
        const extraCol = [];

        const columns = [
            {
                title: '导出人',
                width: 100,
                dataIndex: 'accountName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '导出日期',
                width: 180,
                dataIndex: 'createTime',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            ...extraCol,
            {
                title: '文件名',
                width: 200,
                dataIndex: 'fileName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '状态',
                width: 80,
                dataIndex: 'uploadStatusName',
                render(text, record) {
                    return <span title={text}>{text}</span>;
                },
            },
            {
                title: '操作',
                width: 80,
                fixed: 'right',
                align: 'left',
                render(text, record) {
                    return (
                        <Space>
                            {(record.uploadStatus === '02' && (
                                <span
                                    className={commonStyles['table-btn']}
                                    onClick={() => {
                                        downLoadFile(record.id, record.ossFilePath);
                                    }}
                                >
                                    下载
                                </span>
                            )) ||
                                null}
                        </Space>
                    );
                },
            },
        ];
        return columns;
    }, [bizType]);

    const downloadCache = (
        <TablePro
            style={{ width: '800px' }}
            loading={listLoading}
            scroll={{ x: 'max-content' }}
            dataSource={cacheAreaList}
            columns={downloadCacheColumns}
            onChange={onTableChange}
            size="small"
            pagination={{
                current: pageInfo.pageIndex,
                total: cacheAreaListTotal,
                pageSize: pageInfo.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
            }}
        />
    );

    return (
        <Popover
            placement="bottomLeft"
            content={downloadCache}
            title={
                <div style={{ margin: '10px' }}>
                    <ExclamationCircleTwoTone style={{ marginRight: '8px' }} />
                    文件保存7天后自动删除
                    <a
                        onClick={() => {
                            searchData();
                        }}
                        style={{ marginLeft: '8px' }}
                    >
                        <SyncOutlined spin={isWaiting} />
                    </a>
                </div>
            }
            trigger="click"
            onClick={() => {
                if (!showAreaView) {
                    initRef?.current?.reload();
                    updateEnableDownloadCount(0);
                }
            }}
            zIndex={600}
            visible={showAreaView}
            onVisibleChange={(visible) => {
                updateShowAreaView(visible);
            }}
        >
            <Badge count={enableDownloadCount} offset={[-30, 6]}>
                <Button icon={<FolderOpenOutlined />} type="link">
                    文件暂存区
                    <DownOutlined />
                </Button>
            </Badge>
        </Popover>
    );
};

export default connect(({ cacheModel, loading }) => ({
    cacheModel,
    listLoading: loading.effects['cacheModel/getCacheAreaList'],
}))(CacheAreaView);
