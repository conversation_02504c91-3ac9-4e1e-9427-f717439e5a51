import React, { Fragment, useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Button, Popover, Badge, Space, message, Descriptions, Spin, Modal } from 'antd';
import { usePagination } from 'ahooks';

import TablePro from '@/components/TablePro';
import {
    FolderOpenOutlined,
    DownOutlined,
    ExclamationCircleTwoTone,
    SyncOutlined,
} from '@ant-design/icons';

import commonStyles from '@/assets/styles/common.less';

import {
    uploadFileCacheApi,
    uploadFileMngCacheApi,
    getUploadFileCacheListApi,
    downloadUploadFileCacheListApi,
} from '@/services/CommonApi';

const MngCacheAreaView = (props, ref) => {
    const {
        listApi = getUploadFileCacheListApi,
        listParams = {},
        uploadApi = uploadFileCacheApi,
        upLoadParams = {},
        downLoadApi = downloadUploadFileCacheListApi,
    } = props;

    // 处理文件暂存区
    const [enableDownloadCount, updateEnableDownloadCount] = useState(0);
    const [showAreaView, updateShowAreaView] = useState(false);

    useImperativeHandle(ref, () => ({
        count: () => {
            updateEnableDownloadCount(enableDownloadCount + 1);
        },
        clearCount: () => {
            updateEnableDownloadCount(0);
        },
        apply: (params) => {
            return new Promise(async (resolve, reject) => {
                try {
                    if (params) {
                        uploadApi &&
                            (await uploadApi({
                                uploadParam: JSON.stringify(params),
                                ...upLoadParams,
                            }));
                    }

                    message.success('提交成功，请到文件暂存区查看');
                    resolve();
                } catch {
                    reject();
                }
            });
        },
        reload: () => {
            if (pagination.pageIndex == 1) {
                searchData({ current: 1, pageSize: pagination?.pageSize });
            } else {
                searchData({ current: 1, pageSize: pagination?.pageSize });
            }
        },
    }));

    const {
        run: searchData,
        loading: listLoading,
        data: listData,
        pagination,
    } = usePagination(
        async ({ current, pageSize }, params = {}) => {
            const response = await listApi({
                ...params,
                ...listParams,
                pageSize,
                pageIndex: current,
            });
            if (response.ret === 200) {
                return {
                    list: response?.data?.records || response?.data?.list || [],
                    current: response?.data?.pageIndex,
                    pageSize: response?.data?.pageSize,
                    total: response?.data?.total,
                };
            } else {
                return {
                    list: [],
                    current: current,
                    pageSize: pageSize,
                    total: 0,
                };
            }
        },
        {
            manual: true,
            defaultPageSize: 10,
        },
    );

    const downLoadFile = async (id, ossFilePath) => {
        if (ossFilePath) {
            // 如果返回oss地址，直接下载
            window.open(ossFilePath);
        } else {
            try {
                let params = {
                    id,
                };
                downLoadApi && (await downLoadApi(params));
                return;
            } catch (error) {
                return Promise.reject(error);
            }
        }
    };

    const extraCol = [];

    const downloadCacheColumns = [
        {
            title: '导出人',
            width: 100,
            dataIndex: 'accountName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '导出日期',
            width: 180,
            dataIndex: 'createTime',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        ...extraCol,
        {
            title: '文件名',
            width: 200,
            dataIndex: 'fileName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '状态',
            width: 80,
            dataIndex: 'uploadStatusName',
            render(text, record) {
                return <span title={text}>{text}</span>;
            },
        },
        {
            title: '操作',
            width: 80,
            fixed: 'right',
            align: 'left',
            render(text, record) {
                return (
                    <Space>
                        {(record.uploadStatus === '02' && (
                            <span
                                className={commonStyles['table-btn']}
                                onClick={() => {
                                    downLoadFile(record.id, record.ossFilePath);
                                }}
                            >
                                下载
                            </span>
                        )) ||
                            null}
                    </Space>
                );
            },
        },
    ];

    const downloadCache = (
        <TablePro
            style={{ width: '800px' }}
            loading={listLoading}
            scroll={{ x: 'max-content', y: 450 }}
            dataSource={listData?.list}
            columns={downloadCacheColumns}
            size="small"
            onChange={(pages) => {
                pagination.onChange(pages?.current, pages?.pageSize);
            }}
            pagination={{
                current: pagination.pageIndex,
                total: pagination.total,
                pageSize: pagination.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`,
            }}
        />
    );

    return (
        <Popover
            placement="bottomLeft"
            content={downloadCache}
            title={
                <div style={{ margin: '10px' }}>
                    <ExclamationCircleTwoTone style={{ marginRight: '8px' }} />
                    文件保存7天后自动删除
                    <a
                        onClick={() => {
                            searchData({ current: 1, pageSize: pagination?.pageSize });
                        }}
                        style={{ marginLeft: '8px' }}
                    >
                        <SyncOutlined spin={listLoading} />
                    </a>
                </div>
            }
            trigger="click"
            onClick={() => {
                if (!showAreaView) {
                    ref?.current?.reload();
                    updateEnableDownloadCount(0);
                }
            }}
            zIndex={600}
            visible={showAreaView}
            onVisibleChange={(visible) => {
                updateShowAreaView(visible);
            }}
        >
            <Badge count={enableDownloadCount} offset={[-30, 6]}>
                <Button icon={<FolderOpenOutlined />} type="link">
                    文件暂存区
                    <DownOutlined />
                </Button>
            </Badge>
        </Popover>
    );
};

export default forwardRef(MngCacheAreaView);
