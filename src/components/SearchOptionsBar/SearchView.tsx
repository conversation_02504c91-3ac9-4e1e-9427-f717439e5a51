import { Form } from 'antd';
import SearchOptionsBar from '@/components/SearchOptionsBar/index';
import TablePro from '@/components/TablePro/index';

import usePaginationState from '@/hooks/usePaginationState';

import React, {
    useState,
    useRef,
    forwardRef,
    useImperativeHandle,
    useEffect,
    Fragment,
    useMemo,
} from 'react';

import { Col } from 'antd';

import { connect, useHistory } from 'umi';
import { copyObjectCommon, isEmpty } from '@/utils/utils';
import type { ComComponent } from '@/typings/component';

const SearchViewContent: React.FC<ComComponent.SearchViewProps & { global?: any }> = (props) => {
    const {
        global,

        form,
        formRef,
        refInstance,
        hideSearch,
        toolsBar,
        children,
        loading,
        exportLoading,
        exportPrivateLoading,
        onSearchGroup,
        onExportForm,
        exportName = '导出',
        hideOper,
        minSpan = 16,
        open,

        columns = [],
        manual = false,
        requestApi,
        requestParams = {},
        cacheName,
    } = props;

    const { pageInit } = global;
    const [searchForm] = Form.useForm();
    const searchFormRef = useRef(form || searchForm);

    const history = useHistory();

    const {
        location: { pathname },
    } = history;

    useImperativeHandle(refInstance, () => {
        return {
            search: searchData,
            reload: refresh,
            getList: () => {
                return data?.list;
            },
        };
    });
    useImperativeHandle(formRef, () => {
        return {
            validateFields: validateFields,
            getFieldsValue: getFieldsValue,
            setFieldsValue: setFieldsValue,
            resetFields: resetFields,
        };
    });
    const validateFields = async () => {
        return searchFormRef.current?.validateFields();
    };
    const getFieldsValue = async () => {
        return searchFormRef.current?.getFieldsValue();
    };
    const setFieldsValue = async (formData: Record<string, any>) => {
        return searchFormRef.current?.setFieldsValue(formData);
    };
    const resetFields = () => {
        searchFormRef.current?.resetFields();
        searchData({});
    };

    const cacheKey = cacheName || pathname;

    useEffect(() => {
        let initParams = {};
        if (pageInit[cacheKey]) {
            initParams = pageInit[cacheKey].form;

            setFieldsValue(initParams);
        }

        if (!manual) {
            initData(initParams);
        }
    }, []);

    const transformFormData = (formData: Record<string, any>) => {
        let newData = copyObjectCommon(formData);
        for (const key in newData) {
            if (Object.prototype.hasOwnProperty.call(newData, key)) {
                const element = newData[key];
                //找到dataIndex 或者 searchIndex值和表单一致的列 看是否有格式化方法
                const columnConfigItem = searchColumns.find(
                    (ele) => ele.dataIndex === key || ele.searchIndex === key,
                );
                if (
                    columnConfigItem &&
                    columnConfigItem.transform &&
                    typeof columnConfigItem.transform === 'function'
                ) {
                    newData = { ...newData, ...columnConfigItem.transform(element) };
                }
            }
        }
        return newData;
    };
    const getRequestParams = async () => {
        try {
            const data = await validateFields();
            const formData = transformFormData(data);

            const options: Record<string, any> = {
                ...formData,
                ...requestParams,
            };

            for (const key in options) {
                if (Object.prototype.hasOwnProperty.call(options, key)) {
                    const element = options[key];
                    if (!element) {
                        delete options[key];
                    }
                }
            }

            return options;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const initData = async (defOptions: Record<string, any>) => {
        try {
            const data = await getRequestParams();
            const options = {
                ...defOptions,
                ...data,
                ...requestParams,
                pageIndex: pagination.current,
                pageSize: pagination.pageSize,
            };
            initList(options);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };
    const searchData = async (defOptions: Record<string, any>) => {
        try {
            const data = await getRequestParams();
            const options = {
                ...defOptions,
                ...data,
                ...requestParams,
                pageIndex: pagination.current,
                pageSize: pagination.pageSize,
            };
            searchList(options);
            return;
        } catch (error) {
            console.log(4343, error);
            return Promise.reject(error);
        }
    };

    const exportData = async () => {
        try {
            const data = await getRequestParams();
            const options = {
                ...data,
                ...requestParams,
                pageIndex: pagination.current,
                pageSize: pagination.pageSize,
            };
            onExportForm && onExportForm(options);
            return;
        } catch (error) {
            return Promise.reject(error);
        }
    };

    const {
        initList,
        searchList,

        data,
        pagination,
        onTableChange,
        loading: listLoading,
        refresh,
    } = usePaginationState(requestApi, {
        props,
        cacheName: props?.cacheName,
    });

    const tableColumns = useMemo(() => {
        return columns.filter((ele) => !ele.hideInTable);
    }, [columns]);

    const searchColumns = useMemo(() => {
        return columns.filter((ele) => ele.searchRender);
    }, [columns]);
    return (
        <Fragment>
            {!hideSearch && (
                <Form
                    form={searchFormRef.current}
                    onFinish={(values) => {
                        searchData({});
                    }}
                    scrollToFirstError
                >
                    <SearchOptionsBar
                        loading={loading}
                        exportLoading={exportLoading}
                        exportPrivateLoading={exportPrivateLoading}
                        onReset={resetFields}
                        onSearchGroup={onSearchGroup}
                        onExportForm={onExportForm && exportData}
                        exportName={exportName}
                        hideOper={hideOper}
                        minSpan={minSpan}
                        form={searchFormRef.current}
                        open={open}
                    >
                        {children
                            ? children
                            : searchColumns?.map((ele, index) => (
                                  <Col span={8} key={index}>
                                      {ele?.searchRender?.()}
                                  </Col>
                              ))}
                    </SearchOptionsBar>
                </Form>
            )}

            {toolsBar && <Fragment>{toolsBar}</Fragment>}
            <TablePro
                {...props}
                loading={listLoading}
                scroll={{ x: 'max-content', y: 450 }}
                dataSource={data?.list}
                columns={tableColumns}
                onChange={onTableChange}
                pagination={{
                    current: pagination.current,
                    total: pagination.total,
                    pageSize: pagination.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
            ></TablePro>
        </Fragment>
    );
};

const SearchView = connect(({ global, user }: any) => ({
    global,
    user,
}))(SearchViewContent);

export default forwardRef((props: ComComponent.SearchViewProps, ref) => (
    <SearchView {...props} refInstance={ref} />
));
