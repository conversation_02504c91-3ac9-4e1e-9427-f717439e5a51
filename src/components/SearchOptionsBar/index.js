import React, { Fragment, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { connect } from 'umi';
import { Button, Col, Modal, Row, Space, Form, Popconfirm } from 'antd';
import styles from './index.less';
/**
 * 强制要求组件内容必须为COl栅格单元 否则不展示
 */
const SearchOptionsBar = (props) => {
    const {
        loading,
        exportLoading,
        exportPrivateLoading,
        onReset,
        onSearchGroup,
        onExportForm,
        exportName = '导出',
        children,
        hideOper,
        minSpan = 16,
        form,
        open,
    } = props;
    const [isToggle, changeToggle] = useState(open);
    const [showToggle, changeShowToggle] = useState(false);
    useEffect(() => {
        if (children) {
            let colSpanCount = 0;
            if (children instanceof Array) {
                children.forEach((ele) => {
                    if (ele) {
                        if (ele?.props?.span) {
                            colSpanCount += ele.props.span;
                        }
                    }
                });
            } else if (children) {
                if (children?.props?.span) {
                    colSpanCount += children.props.span;
                }
            }
            if (!hideOper && colSpanCount > minSpan) {
                changeShowToggle(true);
            }
        }
    }, []);
    const searchChild = useMemo(() => {
        if (children) {
            if (isToggle) {
                const list = [];
                if (children instanceof Array) {
                    children.forEach((ele, index) => {
                        // ele.key = index;
                        list.push(ele);
                    });
                } else {
                    list.push(children);
                }

                return list;
            }
            let colSpanCount = 0;
            const list = [];
            if (children instanceof Array) {
                children.forEach((ele) => {
                    if (ele) {
                        if (ele?.props?.span) {
                            colSpanCount += ele.props.span;
                        }

                        if (hideOper || colSpanCount <= minSpan) {
                            list.push(
                                React.cloneElement(ele, {
                                    style: { display: 'block' },
                                    key: list.length,
                                }),
                            );
                        } else {
                            list.push(
                                React.cloneElement(ele, {
                                    style: { display: 'none' },
                                    key: list.length,
                                }),
                            );
                        }
                    }
                });
            } else {
                if (children?.props?.span) {
                    colSpanCount += children.props.span;
                }

                if (hideOper || colSpanCount <= minSpan) {
                    list.push(
                        React.cloneElement(children, {
                            style: { display: 'block' },
                            key: list.length,
                        }),
                    );
                } else {
                    list.push(
                        React.cloneElement(children, {
                            style: { display: 'none' },
                            key: list.length,
                        }),
                    );
                }
            }

            return list;
        }
        return null;
    });
    const OperItem = () =>
        hideOper ? null : (
            <Col style={{ cssFloat: 'right' }}>
                <Form.Item>
                    <Space>
                        {form ? (
                            <Button
                                loading={loading}
                                type="primary"
                                onClick={() => {
                                    form.submit();
                                }}
                            >
                                查询
                            </Button>
                        ) : (
                            <Button loading={loading} type="primary" htmlType="submit">
                                查询
                            </Button>
                        )}

                        {onSearchGroup && (
                            <Button type="primary" onClick={onSearchGroup}>
                                批量查询
                            </Button>
                        )}
                        {onExportForm ? (
                            <Popconfirm
                                title="确定执行此操作？"
                                onConfirm={onExportForm}
                                loading={exportLoading}
                            >
                                <Button
                                    loading={exportLoading || exportPrivateLoading}
                                    type="primary"
                                >
                                    {exportName}
                                </Button>
                            </Popconfirm>
                        ) : null}

                        {(onReset && <Button onClick={onReset}>重置</Button>) || null}
                        {showToggle ? (
                            !isToggle ? (
                                <span
                                    className={styles['toggle-btn']}
                                    onClick={() => {
                                        changeToggle(true);
                                    }}
                                >
                                    展开
                                </span>
                            ) : (
                                <span
                                    className={styles['toggle-btn']}
                                    onClick={() => {
                                        changeToggle(false);
                                    }}
                                >
                                    收起
                                </span>
                            )
                        ) : null}
                    </Space>
                </Form.Item>
            </Col>
        );
    return (
        <Row gutter={{ md: 8, lg: 24, xl: 48 }}>
            {searchChild}
            {OperItem()}
        </Row>
    );
};

export default connect(({ loading }) => ({
    exportLoading: loading.effects['global/exportXml'],
}))(SearchOptionsBar);
