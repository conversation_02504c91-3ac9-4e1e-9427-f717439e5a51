import { useMemo, useState } from 'react';
import { Tooltip, Input } from 'antd';
import { useEffect } from 'react';

const InputItem = (props) => {
    const { maxLength, onChange } = props;

    const [textLength, changeTextLength] = useState(0);

    useEffect(() => {}, []);

    const onChangeEvent = (e) => {
        const { value: newValue } = e.target;
        changeTextLength(newValue?.length || 0);
        onChange && onChange(newValue);
    };

    const title = useMemo(
        () =>
            maxLength && textLength === maxLength ? (
                <span className="numeric-input-title">{`最多输入${maxLength}个字`}</span>
            ) : null,
        [textLength, maxLength],
    );
    return (
        <Tooltip
            visible={title ? true : false}
            trigger={['focus']}
            title={title}
            placement="topLeft"
            overlayClassName="numeric-input"
        >
            <Input {...props} onChange={onChangeEvent} />
        </Tooltip>
    );
};

export default InputItem;
