import { PageLoading } from '@ant-design/pro-layout';
import { history, Link, getDvaApp, setCreateHistoryOptions, addLocale, setLocale } from 'umi';
import RightContent from '@/components/RightContent';
import Footer from '@/components/Footer';
import { queryCurrent } from '@/services/userApi';
import React, { createRef } from 'react';
import { zhLanguage, enLanguage } from '@/config/local';
import { SCAN_LOGIN_PAGES } from '@/config/global';
import qs, { stringify } from 'qs';
import {
    getHomePathApi,
    getDynamicmenuApi,
    getUserInfoApi,
    checkAuthorizeApi,
    psdLoginApi,
    queryAllPemissionsApi,
} from '@/services/loginApi';
import { checkVersion } from '@/services/CommonApi';
const isDev = process.env.NODE_ENV === 'development';
import CustomRequest from '@/utils/request';
import { getPageQuery } from '@/utils/utils';
const loginPath = '/user/login';

import * as AndIcons from '@ant-design/icons';
import { sendLogoutApi, getLogouUrlApi } from '@/services/loginApi';
import { getFilingTitleApi } from '@/services/CommonApi';

import moment from 'moment';
import 'moment/locale/zh-cn';

import { getAuthSourceConfigListApi, socialAuthorizeApi } from '@/services/idassApi';
const initFilingTitle = async () => {
    //获取备案号
    try {
        const { data } = await getFilingTitleApi();
        return data;
    } catch (error) {
        return '';
    }
};
const checkScreenScanLogin = async () => {
    try {
        const pageUrl = window.location.href;
        const isCheckPage = SCAN_LOGIN_PAGES.some((ele) => pageUrl.indexOf(ele) >= 0);
        if (isCheckPage) {
            const { scanLogin } = getPageQuery();
            if (scanLogin) {
                await sendLogoutApi({});
                await socialAuthorizeApi({
                    optType: 'login',
                    authSourceCode: scanLogin,
                });
                // const { data } = await getAuthSourceConfigListApi();
                // if (data instanceof Array) {
                //     const findDingScanItem = data.find(
                //         (ele) => ele.authTemplateCode === 'authSourceDing',
                //     );
                //     if (findDingScanItem) {
                //         await socialAuthorizeApi({
                //             optType: 'login',
                //             authSourceCode: findDingScanItem.authSourceCode,
                //         });
                //     }
                // }
            }
        }
        return;
    } catch (error) {
        console.log(321321, error);
        return Promise.reject(error);
    }
};

moment.locale('zh-cn');

const { HomeOutlined, createFromIconfontCN } = AndIcons;

addLocale('zh-CN', zhLanguage);

addLocale('en-US', enLanguage);

/** 获取用户信息比较慢的时候会展示一个 loading */

const MyIcon = createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/font_1271235_d7rbbizir.js', // 在 iconfont.cn 上生成
});

const createIcon = (icon) => React.createElement(icon);
const getIcon = (icon) => {
    if (typeof icon === 'string' && icon !== '') {
        if (icon.startsWith('icon') || icon.startsWith('icon-')) {
            return <MyIcon type={icon} />;
        }

        if (icon) {
            if (AndIcons[icon]) {
                return createIcon(AndIcons[icon]);
            }
        }
    }

    return icon;
};

const formatPermissionList = (list) => {
    let newList = [];
    for (const item of list) {
        if (item?.button instanceof Array) {
            const childrenList = formatPermissionList(item.button);
            newList = newList.concat(childrenList);
        }
        if (item?.children instanceof Array) {
            const childrenList = formatPermissionList(item.children);
            newList = newList.concat(childrenList);
        }

        if (item.href) {
            newList.push({ code: item.href });
        }
        if (item.uri) {
            newList.push({ code: item.uri });
        }
        if (item.code) {
            newList.push({ code: item.code });
        }
    }
    return newList;
};

export const initialStateConfig = {
    loading: <PageLoading />,
};
/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */

export async function getInitialState() {
    const formatPermissionButtonList = (list) => {
        const newData = JSON.parse(JSON.stringify(list));
        let newList = [];
        for (const item of newData) {
            if (item?.button instanceof Array) {
                newList = newList.concat(item?.button);
            }
            if (item?.children instanceof Array) {
                const childrenList = formatPermissionButtonList(item.children);
                newList = newList.concat(childrenList);
            }
        }
        return newList;
    };
    const fetchUserInfo = async () => {
        try {
            const userInfo = await queryCurrent();

            const { data } = await getUserInfoApi();

            const { data: permissionList } = await queryAllPemissionsApi();

            // await checkUserLoginEvent(code);

            // setTimeout(() => {
            //     const dvaApp = getDvaApp();
            //     const { _store } = dvaApp;

            //     _store.dispatch({
            //         type: 'user/saveCurrentUser',
            //         payload: userInfo,
            //     });
            // }, 500);
            return { ...(data || {}), ...userInfo, permissionList };
        } catch (error) {
            return;
        }
    }; // 如果是登录页面，不执行

    // 通过code获取用户信息
    const checkUserLoginEvent = async (code) => {
        try {
            await checkAuthorizeApi(code);
            return;
        } catch (error) {
            return;
        }
    };

    const getInitHomePath = async () => {
        try {
            const {
                data: { mainUrl },
            } = await getHomePathApi();
            return mainUrl;
        } catch (error) {
            return '/';
        }
    };

    const getDynamicmenu = async () => {
        try {
            const { data } = await getDynamicmenuApi();
            return data;
        } catch (error) {
            return false;
        }
    };

    let dynamicMenuList = [];

    let homePath = '/';

    if (history.location.pathname === '/') {
        const { code, errorCode } = getPageQuery();
        if (errorCode) {
            const { data } = await getLogouUrlApi({});
            window.location.href = data;
        }
        if (code) {
            await checkUserLoginEvent(code);
        }
    }

    await checkScreenScanLogin();

    let hasScanScreen = SCAN_LOGIN_PAGES.some((ele) => {
        return history.location.pathname.indexOf(ele) >= 0;
    });
    if (!hasScanScreen) {
        let currentUser = null;

        const scanLogin = window.sessionStorage.getItem('scanLogin');

        if (scanLogin) {
            //大屏鉴权特殊处理
            await psdLoginApi();
            window.sessionStorage.removeItem('scanLogin');
        }

        console.log('启动项目11111111111111111111111111');
        dynamicMenuList = await getDynamicmenu();

        if (dynamicMenuList instanceof Array) {
            currentUser = await fetchUserInfo();
            homePath = await getInitHomePath();

            if (history.location.pathname == '/') {
                // 获取首页地址然后重定向
                history.replace(homePath);
            }
        }

        const filingTitle = await initFilingTitle();
        return {
            fetchUserInfo,
            currentUser,
            settings: {},
            menuList: dynamicMenuList,
            permissionButtonList: formatPermissionButtonList(dynamicMenuList),
            homePath,
            filingTitle,
        };
    }
    return {
        fetchUserInfo,
        currentUser: null,
        settings: {},
        menuList: dynamicMenuList,
        permissionButtonList: formatPermissionButtonList(dynamicMenuList),
        homePath,
    };
} // ProLayout 支持的api https://procomponents.ant.design/components/layout

export const request = CustomRequest;

const gotoPageEvent = async (path) => {
    try {
        const dvaApp = getDvaApp();
        const { _store } = dvaApp;

        const storeState = _store.getState();
        const {
            global: { pageInit },
        } = storeState;
        const keys = Object.keys(pageInit);
        for (let index = 0; index < keys.length; index++) {
            const key = keys[index];
            if (key.indexOf(path) >= 0) {
                _store.dispatch({
                    type: 'global/updatePageInit',
                    pathname: key,
                    info: null,
                });
            }
        }

        history.push(path);
    } catch (error) {}
};

const checkMenu = (item, list) => {
    let has = false;
    if (Array.isArray(list)) {
        if (/\:.*$/g.test(item.path)) {
            has = true;
        } else {
            for (const father of list) {
                if (
                    item.path == father.href ||
                    item.redirect == father.href ||
                    father.href.indexOf(item.path) >= 0
                ) {
                    has = true;
                    break;
                }
                if (father.children) {
                    has = checkMenu(item, father.children);
                }
                if (has) {
                    break;
                }
            }
        }
    }
    if (has) {
        // console.log(777777, item);
        return true;
    }
    return false;
};

const loopMenuItem = (menus = []) => {
    let list = loopFilterMenuItem(menus);
    return list.map(({ icon, children, ...item }) => {
        return {
            ...item,
            path: item.href,
            name: item.title,
            icon: getIcon(icon),
            children: children && loopMenuItem(children),
        };
    });
};

const loopFilterMenuItem = (menus = []) =>
    menus.filter(({ isHidden }) => {
        return !isHidden;
    }) || [];

const initCurBreadcrumb = (path, routes) => {
    let list = [];
    for (let index = routes.length - 1; index >= 0; index--) {
        const item = routes[index];
        let formatPath = path.replace(PUBLIC_PATH, '/');
        if (
            item.path !== '/' &&
            (path.indexOf(`${item.path}?`) >= 0 || path.indexOf(`${item.path}/`) >= 0) &&
            formatPath !== item.path &&
            !item.redirect
        ) {
            if (item.routes instanceof Array) {
                list = list.concat(initCurBreadcrumb(path, item.routes));
            }
            const hasItem = list.find((ele) => ele.breadcrumbName === item.name);

            if (!hasItem) {
                list.unshift({
                    path: item.path,
                    breadcrumbName: item.name,
                });
            }
        }
    }
    return list;
};

const menuRef = createRef();
export const layout = (props) => {
    const { initialState } = props;

    const { currentUser, menuList: dynamicMenu = [], homePath, filingTitle } = initialState;

    //刷新用户信息

    const dvaApp = getDvaApp();
    const { _store, _router } = dvaApp;

    let info = currentUser || {};

    _store.dispatch({
        type: 'user/saveCurrentUser',
        payload: { ...info },
    });
    _store.dispatch({
        type: 'login/setDynamicMenu',
        menu: dynamicMenu,
    });

    const menuDataRender = (menuList) => {
        const list = menuList.map((item) => {
            item.icon = getIcon(item.icon);
            const localItem = {
                ...item,
                children: item.children ? menuDataRender(item.children) : [],
                routes: item.routes ? menuDataRender(item.routes) : [],
            };
            if (process.env.NODE_ENV === 'production') {
                const has = checkMenu(item, dynamicMenu);

                if (has) {
                    return localItem;
                } else {
                    return null;
                }
            }
            return localItem;
        });

        return list;
    };
    let menuConifg = { locale: false };

    // const filterMenuList = loopMenuItem(dynamicMenu);

    const {
        props: { routes },
    } = _router();
    const routeConfig = routes.reduce((a, b) => {
        if (b.routes instanceof Array) {
            return a.concat(b.routes);
        }
        return a;
    }, []);

    // console.log(555555555555, dynamicMenu, routes, dvaApp, _store.getState());

    // if (process.env.NODE_ENV === 'production') {
    menuConifg = {
        request: async () => {
            const list = loopMenuItem(dynamicMenu);
            return list;
        },
        locale: false,
    };
    // }

    return {
        actionRef: menuRef,
        menu: menuConifg,
        rightContentRender: () => <RightContent />,
        disableContentMargin: false,

        // waterMarkProps: {
        //     content: currentUser?.name,
        // },
        footerRender: () => <Footer filingTitle={filingTitle} />,
        onPageChange: async () => {
            const { location } = history; // 如果没有登录，重定向到 login

            // if (location.pathname == '/') {
            // 获取首页地址然后重定向
            // history.replace(homePath);
            // }

            // if (
            //     !initialState?.currentUser &&
            //     // location.pathname !== loginPath
            // ) {
            //      没用户信息重定向到登录页
            //     console.log('重定向登录11111111111111111111111111');
            //     history.push(loginPath);
            // }
        },
        links: isDev ? [] : [],
        menuHeaderRender: null,
        // menuDataRender: menuDataRender,

        menuItemRender: (menuItemProps, defaultDom) => {
            if (menuItemProps.hideChildrenInMenu) {
                return (
                    <div
                        to={menuItemProps.path}
                        onClick={() => {
                            gotoPageEvent(menuItemProps.path);
                        }}
                    >
                        {defaultDom}
                    </div>
                );
            }
            if (menuItemProps.children) {
                let hasChildCount = 0;
                menuItemProps.children.forEach((ele) => {
                    if (ele.hideInMenu) {
                        hasChildCount++;
                    }
                });
                if (hasChildCount == menuItemProps.children.length) {
                    return (
                        <div
                            to={menuItemProps.path}
                            onClick={() => {
                                gotoPageEvent(menuItemProps.path);
                            }}
                        >
                            {defaultDom}
                        </div>
                    );
                }
                return defaultDom;
            }
            if (menuItemProps.isUrl || !menuItemProps.path) {
                return defaultDom;
            }
            return (
                <div
                    to={menuItemProps.path}
                    onClick={() => {
                        gotoPageEvent(menuItemProps.path);
                    }}
                >
                    {defaultDom}
                </div>
            );
        },
        breadcrumbRender: (routers = []) => {
            if (!(routers instanceof Array)) {
                return undefined;
            }
            const breadcrumbList = initCurBreadcrumb(location.pathname, routeConfig);
            // breadcrumbList.pop();

            // console.log(6666666, breadcrumbList);

            // for (let index = routers.length - 1; index >= 0; index--) {
            //     const element = routers[index];
            //     element.path = element.path.replace(/\/\//g, '/');
            //     element.path = element.path.replace(/\/front\//g, '/');
            //     let hasRoute = false;
            //     for (const item of breadcrumbList) {
            //         // console.log(5555555, location.pathname, item.path);
            //         if (
            //             location.pathname != item.path &&
            //             location.pathname.indexOf(item.path) >= 0
            //         ) {
            //             hasRoute = true;
            //             break;
            //         }
            //     }
            //     if (!hasRoute) {
            //         breadcrumbList.unshift(element);
            //     }
            // }

            breadcrumbList.unshift({
                path: homePath,
                breadcrumbName: '首页',
            });
            return breadcrumbList;
            // return [
            //     {
            //         path: homePath,
            //         breadcrumbName: '首页',
            //     },
            //     ...routers,
            // ];
        },
        itemRender: (route, params, routes, paths) => {
            route.path = route.path.replace(/\/\//g, '/');
            route.path = route.path.replace(/\/front\//g, '/');
            const first = routes.indexOf(route) === 0;
            const last = routes.indexOf(route) === routes.length - 1;

            return first && route.path ? (
                <Link to={route.path}>{route.breadcrumbName}</Link>
            ) : (
                <span>{route.breadcrumbName}</span>
            );
        },

        // 自定义 403 页面
        // unAccessible: <div>unAccessible</div>,
        ...initialState?.settings,
    };
};

export const onRouteChange = async ({ location, routes, action }) => {
    try {
        if (process.env.NODE_ENV === 'production') {
            const versionInfo = await checkVersion();
            if (APP_VERSION !== versionInfo.data.version) {
                window.location.reload(true);
            }
        }
        return;
    } catch (error) {
        return;
    }
};

export const locale = {
    getLocale() {
        const { search } = window.location;
        const { locale = 'zh-CN' } = qs.parse(search, { ignoreQueryPrefix: true });
        return locale;
    },
    setLocale({ lang, realReload, updater }) {
        const pageParams = getPageQuery();
        pageParams.locale = lang;
        const replacePath = window.location.pathname;
        history.replace(`${replacePath.replace(PUBLIC_PATH, '/')}?${stringify(pageParams)}`);
        window.localStorage.setItem('local', lang);
        updater();
        if (realReload) {
            window.location.reload();
        }
    },
};

// export const qiankun = {
//     async bootstrap(props) {
//         console.log(5555555555545454544, props);
//         const basename = props?.basename;
//         if (basename) setCreateHistoryOptions({ basename });
//     },
// };
