import type { CheckboxOptionType } from 'antd';

export const CooperationTypeTitle = {
    '01': '分润',
    '02': '购电',
};

/** 对外售价类型 */
export enum PRICE_TYPE {
    SERVICE = '01',
    PLATFORM = '02',
}

export const PriceTypeTitle = {
    [PRICE_TYPE.SERVICE]: '商家定价',
    [PRICE_TYPE.PLATFORM]: '平台定价',
};

export const PriceTypeOptions: CheckboxOptionType[] = [
    {
        label: PriceTypeTitle[PRICE_TYPE.SERVICE],
        value: PRICE_TYPE.SERVICE,
    },
    {
        label: PriceTypeTitle[PRICE_TYPE.PLATFORM],
        value: PRICE_TYPE.PLATFORM,
    },
];

/** 对外售价模板 */
export enum TEMPLATE_TYPE {
    'PRICE' = '01',
    'CYCLE' = '02',
}

export const TemplateTypeTitle = {
    [TEMPLATE_TYPE.CYCLE]: '周期模板',
    [TEMPLATE_TYPE.PRICE]: '收费模板',
};

export const TemplateTypeOptions: CheckboxOptionType[] = [
    {
        label: TemplateTypeTitle[TEMPLATE_TYPE.CYCLE],
        value: TEMPLATE_TYPE.CYCLE,
    },
    {
        label: TemplateTypeTitle[TEMPLATE_TYPE.PRICE],
        value: TEMPLATE_TYPE.PRICE,
    },
];

/** 周期模板类型 */
export enum CYCLE_TYPE {
    WEEKLY = '01',
    DAILY = '02',
}

export const CycleTypeTitle = {
    [CYCLE_TYPE.WEEKLY]: '按周',
    [CYCLE_TYPE.DAILY]: '按日',
};

export const CycleTypeOptions: CheckboxOptionType[] = [
    {
        label: CycleTypeTitle[CYCLE_TYPE.WEEKLY],
        value: CYCLE_TYPE.WEEKLY,
    },
    {
        label: CycleTypeTitle[CYCLE_TYPE.DAILY],
        value: CYCLE_TYPE.DAILY,
    },
];

// 周类型
export const CYCLE_WEEK_MAP = {
    '1': '周一',
    '2': '周二',
    '3': '周三',
    '4': '周四',
    '5': '周五',
    '6': '周六',
    '7': '周日',
};

// 周类型
export const CYCLE_WEEK_OPTIONS: CheckboxOptionType[] = [
    { label: '每周一', value: '1' },
    { label: '每周二', value: '2' },
    { label: '每周三', value: '3' },
    { label: '每周四', value: '4' },
    { label: '每周五', value: '5' },
    { label: '每周六', value: '6' },
    { label: '每周日', value: '7' },
];

/** 收费模板 */
export enum PREMIUM_TYPE {
    ELECTRIC = '01',
    SERVICE = '02',
}

export const PremiumTypeTitle = {
    [PREMIUM_TYPE.ELECTRIC]: '电费',
    [PREMIUM_TYPE.SERVICE]: '服务费',
};

export const PremiumTypeOptions: CheckboxOptionType[] = [
    {
        label: PremiumTypeTitle[PREMIUM_TYPE.SERVICE],
        value: PREMIUM_TYPE.SERVICE,
    },
    {
        label: PremiumTypeTitle[PREMIUM_TYPE.ELECTRIC],
        value: PREMIUM_TYPE.ELECTRIC,
        disabled: true,
    },
];

/** 溢价方式 */
export enum PREMIUM_WAY_TYPE {
    FIXED = '01',
    PERCENT = '02',
}

export const PremiumWayTitle = {
    [PREMIUM_WAY_TYPE.FIXED]: '固定金额',
    [PREMIUM_WAY_TYPE.PERCENT]: '比例',
};

export const PremiumWayDetail = {
    [PREMIUM_WAY_TYPE.FIXED]: '元',
    [PREMIUM_WAY_TYPE.PERCENT]: '%',
};

export const PremiumWayOptions: CheckboxOptionType[] = [
    {
        label: PremiumWayTitle[PREMIUM_WAY_TYPE.FIXED],
        value: PREMIUM_WAY_TYPE.FIXED,
    },
    {
        label: PremiumWayTitle[PREMIUM_WAY_TYPE.PERCENT],
        value: PREMIUM_WAY_TYPE.PERCENT,
    },
];

/** 冲突场站处理方式 */
export enum CONFLICT_WAY {
    OLD = '1',
    NEW = '2',
}

/** 规则状态字典 */
export enum CONFIG_STATUS {
    DRAFT = '0',
    UN_START = '1',
    PROCESSING = '2',
    INVALIDITY = '3',
}

/** 规则状态字典 */
export const ConfigStatusTitle = {
    [CONFIG_STATUS.DRAFT]: '草稿',
    [CONFIG_STATUS.UN_START]: '未生效',
    [CONFIG_STATUS.PROCESSING]: '生效中',
    [CONFIG_STATUS.INVALIDITY]: '已失效',
};

export const ConfigStatusOptions = [
    {
        label: ConfigStatusTitle[CONFIG_STATUS.UN_START],
        value: CONFIG_STATUS.UN_START,
    },
    {
        label: ConfigStatusTitle[CONFIG_STATUS.PROCESSING],
        value: CONFIG_STATUS.PROCESSING,
    },
    {
        label: ConfigStatusTitle[CONFIG_STATUS.INVALIDITY],
        value: CONFIG_STATUS.INVALIDITY,
    },
];

/** 平台售价规则切换处理方式 */
export enum RULE_INVALIDITY_FLAG {
    CURRENT = '0',
    ALL = '1',
}
