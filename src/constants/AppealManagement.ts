/**
 * 申诉管理相关常量
 */

/** 申诉状态枚举 */
export enum AppealStatusEnum {
    /** 二次申诉 */
    SECOND_APPEAL = '01',
    /** 待平台审核 */
    PENDING = '02',
    /** 待商家处理 */
    PROCESSING = '03',
    /** 超时未处理 */
    TIMEOUT = '04',
    /** 商家通过 */
    PROCESSED = '05',
    /** 商家拒绝 */
    REJECTED = '06',
    /** 系统处理 */
    SYSTEM_PROCESSED = '07',
    /** 已结束 */
    CLOSED = '08',
    /** 平台拒绝 */
    SYSTEM_REJECTED = '09',
}

/** 申诉状态映射 */
export const AppealStatus = {
    [AppealStatusEnum.SECOND_APPEAL]: '二次申诉',
    [AppealStatusEnum.PENDING]: '待平台审核',
    [AppealStatusEnum.PROCESSING]: '待商家处理',
    [AppealStatusEnum.TIMEOUT]: '超时未处理',
    [AppealStatusEnum.PROCESSED]: '商家通过',
    [AppealStatusEnum.REJECTED]: '商家拒绝',
    [AppealStatusEnum.SYSTEM_PROCESSED]: '系统处理',
    [AppealStatusEnum.CLOSED]: '已结束',
    [AppealStatusEnum.SYSTEM_REJECTED]: '平台拒绝',
};

/** 申诉类型枚举 */
export enum AppealTypeEnum {
    /** 停车费 */
    PARKING_FEE = '01',
    /** 占位费 */
    SPACE_OCCUPANCY_FEE = '02',
}

/** 申诉类型映射 */
export const AppealType = {
    [AppealTypeEnum.PARKING_FEE]: '停车费',
    [AppealTypeEnum.SPACE_OCCUPANCY_FEE]: '占位费',
};

/** 平台端待处理TAB */
export const PLATFORM_TODO_TABS = [
    {
        key: AppealStatusEnum.PENDING,
        label: '待平台审核',
    },
    {
        key: AppealStatusEnum.SECOND_APPEAL,
        label: '二次申诉',
    },
    {
        key: AppealStatusEnum.PROCESSING,
        label: '待商家处理',
    },
    {
        key: AppealStatusEnum.TIMEOUT,
        label: '超时未处理',
    },
];

/** 商家端待处理TAB */
export const MERCHANT_TODO_TABS = [
    {
        key: AppealStatusEnum.PROCESSING,
        label: '待商家处理',
    },
    {
        key: AppealStatusEnum.TIMEOUT,
        label: '超时未处理',
    },
];

/** 赔付状态枚举 */
export enum PayStatusEnum {
    /** 未赔付 */
    UNPAID = '01',
    /** 已赔付 */
    PAID = '02',
}

/** 赔付状态映射 */
export const PayStatus = {
    [PayStatusEnum.UNPAID]: '未赔付',
    [PayStatusEnum.PAID]: '已赔付',
};

/** 赔付同申诉枚举 */
export enum PayAppealEnum {
    /** 是 */
    YES = '01',
    /** 否 */
    NO = '02',
}

/** 赔付同申诉映射 */
export const PayAppeal = {
    [PayAppealEnum.YES]: '是',
    [PayAppealEnum.NO]: '否',
};

/** 赔付形式 枚举 */
export enum PayTypeEnum {
    /** 退款 */
    REFUND = '01',
    /** 优惠券 */
    PROMOTION = '02',
    /** 线下赔付 */
    OFFLINE = '03',
}

/** 赔付形式 枚举映射 */
export const PayType = {
    [PayTypeEnum.REFUND]: '退款',
    [PayTypeEnum.PROMOTION]: '优惠券',
    [PayTypeEnum.OFFLINE]: '线下赔付',
};
