/**
 * 服务类目枚举：01 渠道推广，02 运营赋能，03 设备维保，04 场站配套
 */
export enum ServiceCategoryEnum {
    CHANNEL_PROMOTION = '01',
    OPERATION_ENGAGEMENT = '02',
    DEVICE_MAINTENANCE = '03',
    STATION_SUPPLEMENT = '04',
}

/**
 * 服务场景枚举
 * 01高德上图 02电子发票 03红外抄表 04AI建站选址 05金融服务 06设备运维 07运营保险 08设备延保 09洗车 10餐饮 11售货
 */
export enum ServiceSceneEnum {
    GAODE_MAP = '01',
    E_INVOICE = '02',
    INFRARED_READING = '03',
    AI_BUILD_SITE = '04',
    FINANCIAL_SERVICE = '05',
    DEVICE_MAINTENANCE = '06',
    OPERATION_INSURANCE = '07',
    DEVICE_MAINTENANCE_EXTEND = '08',
    CAR_WASH = '09',
    FOOD_SERVICE = '10',
    SHOPPING_SERVICE = '11',
}

/**
 * 配置类型枚举： 01 自主开通、02留资回访
 */
export enum ConfigTypeEnum {
    SELF_OPEN = '01',
    LEAD_RETURN = '02',
}

/**
 * 服务状态：01未上架 02已上架
 */
export enum ServiceStatusEnum {
    NOT_ON_SHELVES = '01',
    ON_SHELVES = '02',
}

//商品状态：01未上架 02已上架 03已下架
export enum PileMarketProductStatusEnum {
    NOT_ON_SHELVES = '01',
    ON_SHELVES = '02',
    OFF_SHELVES = '03',
}

//	电桩属性：01交流 02直流
export enum PileMarketAttributeEnum {
    AC = '01',
    DC = '02',
}

//	电桩留资来源 01商家后台 02新电途小程序 03商家助手小程序
export enum PileMarketSourceEnum {
    MERCHANT_BACKEND = '01',
    XDT_MINI_APP = '02',
    MERCHANT_MINI_APP = '03',
}

//状态：01未处理 02已处理 03暂缓 04处理中
export enum PileMarketApplyStatusEnum {
    UNPROCESSED = '01',
    PROCESSED = '02',
    HOLD = '03',
    PROCESSING = '04',
}
