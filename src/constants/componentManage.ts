export const ComponentModuleStatusEnum = {
    //启动
    ENABLED: 1,
    //关闭
    DISABLED: 0,
};

export const ComponentModuleStatusTabOptions = [
    {
        key: '',
        label: '全部',
    },
    {
        key: ComponentModuleStatusEnum.ENABLED,
        label: '启动',
    },
    {
        key: ComponentModuleStatusEnum.DISABLED,
        label: '关闭',
    },
];

export const ComponentModuleTypeEnum = {
    //任务包
    TASK: '60',
    //充电有奖--待修改
    RECHARGE: '6002',
    //组团活动--待修改
    GROUP: '3',
    //组队活动--待修改
    TEAM: '4',
    //签到活动
    SIGN: '27',
    //发奖活动
    REWARD: '14',
    //发放权益
    EQUITY: '61',

    //站点推荐
    STATION_RECOMMEND: '66',
};

/**
 * 组件模块子类型枚举，无子类型则位null
 */
export const RelaActSubTypeEnum = {
    //任务包
    TASK: null,
    //充电有奖--待修改
    RECHARGE: '6002',
    //组团活动--待修改
    GROUP: null,
    //组队活动--待修改
    TEAM: null,
    //签到活动
    SIGN: null,
    //发奖活动
    REWARD: '1410',
    //发放权益
    EQUITY: null,

    //站点推荐
    STATION_RECOMMEND: null,
};

/**
 * 组件模块类型
 */
export const ComponentModuleTypeOptions = [
    {
        label: '任务包',
        value: ComponentModuleTypeEnum.TASK,
        subType: RelaActSubTypeEnum.TASK,
    },
    // {
    //     label: '充电有奖',
    //     value: ComponentModuleTypeEnum.RECHARGE,
    // },
    // {
    //     label: '组团活动',
    //     value: ComponentModuleTypeEnum.GROUP,
    // },
    // {
    //     label: '组队活动',
    //     value: ComponentModuleTypeEnum.TEAM,
    // },
    {
        label: '会员签到活动',
        value: ComponentModuleTypeEnum.SIGN,
        subType: RelaActSubTypeEnum.SIGN,
    },
    {
        label: '发奖活动',
        value: ComponentModuleTypeEnum.REWARD,
        subType: RelaActSubTypeEnum.REWARD,
    },
    {
        label: '发放权益',
        value: ComponentModuleTypeEnum.EQUITY,
        subType: RelaActSubTypeEnum.EQUITY,
    },
    {
        label: '充电有奖',
        value: ComponentModuleTypeEnum.RECHARGE,
        subType: RelaActSubTypeEnum.RECHARGE,
    },
    {
        label: '站点推荐',
        value: ComponentModuleTypeEnum.STATION_RECOMMEND,
        subType: RelaActSubTypeEnum.STATION_RECOMMEND,
    },
];

//任务领奖触发类型 01手动 02自动
export const TASK_OBTAIN_TYPE_ENUM = {
    HAND: '01',
    AUTO: '02',
};

//任务开启触发类型 01手动触发, 02 自动触发
export const TASK_OPEN_TYPE_ENUM = {
    HAND: '01',
    AUTO: '02',
};

//奖励规则 01单独领奖 02合并领奖
export const TASK_REWARD_TYPE_ENUM = {
    ONE_SELF: '01',
    MERGE: '02',
};

//未接受任务:unstart 已接受，进行中:receive 已完成:complete 已领奖claim 任务包完成:complete
export const MODULE_BTN_STATUS_ENUM = {
    UNSTART: 'unstart',
    UNRECEIVE: 'unreceive',
    RECIVE: 'receive',
    COMPLETE: 'complete',
    CLAIM: 'claim',
};
//单独发奖时按钮文案
export const moduleBtnTitleEnumWhenOneself = {
    [MODULE_BTN_STATUS_ENUM.UNSTART]: '未开始时',
    [MODULE_BTN_STATUS_ENUM.UNRECEIVE]: '未接受任务时',
    [MODULE_BTN_STATUS_ENUM.RECIVE]: '未完成任务时（所有任务）',
    [MODULE_BTN_STATUS_ENUM.COMPLETE]: '完成任务时（所有任务）',
};
//合并发奖时按钮文案
export const moduleBtnTitleEnumWhenMerge = {
    [MODULE_BTN_STATUS_ENUM.UNSTART]: '未开始时',
    [MODULE_BTN_STATUS_ENUM.UNRECEIVE]: '未接受任务时',
    [MODULE_BTN_STATUS_ENUM.RECIVE]: '已接受任务，未完成任务时',
    [MODULE_BTN_STATUS_ENUM.COMPLETE]: '已完成任务，未领奖时',
    [MODULE_BTN_STATUS_ENUM.CLAIM]: '已领奖时',
};

//单独发奖时任务列表文案
export const moduleTaskTitleEnumWhenOneself = {
    [MODULE_BTN_STATUS_ENUM.UNSTART]: '未开始时',
    [MODULE_BTN_STATUS_ENUM.UNRECEIVE]: '未接受任务时',
    [MODULE_BTN_STATUS_ENUM.RECIVE]: '已接受任务，未完成任务条件时',
    [MODULE_BTN_STATUS_ENUM.COMPLETE]: '已接受任务，已完成任务条件，未领取奖励时',
    [MODULE_BTN_STATUS_ENUM.CLAIM]: '已接受任务，已完成任务条件，已领取奖励时',
};
//合并发奖时任务列表文案
export const moduleTaskTitleEnumWhenMerge = {
    [MODULE_BTN_STATUS_ENUM.UNSTART]: '未开始时',
    [MODULE_BTN_STATUS_ENUM.UNRECEIVE]: '未接受任务时',
    [MODULE_BTN_STATUS_ENUM.RECIVE]: '已接受任务，未完成任务条件时',
    [MODULE_BTN_STATUS_ENUM.COMPLETE]: '已接受任务，已完成任务条件时',
    [MODULE_BTN_STATUS_ENUM.CLAIM]: '已完成任务时',
};

//按钮是否点击 1可点击 0不可点击
export const MODULE_BTN_CLICK_ENUM = {
    ENABLE: 1,
    DISABLE: 0,
};

//按钮是否显示 1显示 0隐藏
export const MODULE_BTN_SHOW_ENUM = {
    SHOW: 1,
    HIDE: 0,
};

export const ComponentAssemblyStatusEnum = {
    //启动
    ENABLED: 1,
    //关闭
    DISABLED: 0,
};

export const ComponentAssemblyStatusTabOptions = [
    {
        key: '',
        label: '全部',
    },
    {
        key: ComponentModuleStatusEnum.ENABLED,
        label: '启动',
    },
    {
        key: ComponentModuleStatusEnum.DISABLED,
        label: '关闭',
    },
];

export const ComponentAssemblyTypeEnum = {
    //单活动组件
    SINGLE: '01',
    //多活动组件
    MULTIPLE: '02',
};

/**
 * 组件模版类型
 */
export const ComponentAssemblyTypeOptions = [
    {
        label: '单活动组件',
        value: ComponentAssemblyTypeEnum.SINGLE,
    },
    {
        label: '多活动组件',
        value: ComponentAssemblyTypeEnum.MULTIPLE,
    },
];

export const AssemblyTypeEnum = {
    //卡片
    CARD: '001',
    //会员导购
    VIP_GUIDE: '002',
};

export const AssemblyTypeOptions = [
    {
        label: '卡片',
        value: AssemblyTypeEnum.CARD,
    },
    {
        label: '会员导购',
        value: AssemblyTypeEnum.VIP_GUIDE,
    },
];

export const MATERIAL_BIZ_TYPES = {
    OPEN: '02',
    TRIAL: '01',
    RENEW: '04',
    UPGRADE: '03',
    RECOMMEND: '05',
};

export const MATERIAL_BIZ_TYPE_NAMES = {
    [MATERIAL_BIZ_TYPES.OPEN]: '推荐开通会员',
    [MATERIAL_BIZ_TYPES.TRIAL]: '推荐体验会员',
    [MATERIAL_BIZ_TYPES.RENEW]: '推荐续费会员',
    [MATERIAL_BIZ_TYPES.UPGRADE]: '推荐升级会员',
    [MATERIAL_BIZ_TYPES.RECOMMEND]: '推荐续费会员',
};

export const MATERIAL_BIZ_OPTIONS = [
    {
        codeLabel: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.OPEN],
        codeValue: MATERIAL_BIZ_TYPES.OPEN,
    },
    {
        codeLabel: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.TRIAL],
        codeValue: MATERIAL_BIZ_TYPES.TRIAL,
    },
    {
        codeLabel: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.RENEW],
        codeValue: MATERIAL_BIZ_TYPES.RENEW,
    },
    {
        codeLabel: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.UPGRADE],
        codeValue: MATERIAL_BIZ_TYPES.UPGRADE,
    },
    {
        codeLabel: MATERIAL_BIZ_TYPE_NAMES[MATERIAL_BIZ_TYPES.RECOMMEND],
        codeValue: MATERIAL_BIZ_TYPES.RECOMMEND,
    },
];
