/** 人群相关常量 */

export enum CROWD_TYPE {
    CDP = '01',
    XDT = '02',
}

export const CrowdTypeTitle = {
    [CROWD_TYPE.CDP]: 'CDP人群',
    [CROWD_TYPE.XDT]: '新电途人群',
};

export const CrowdTypeOptions = [
    {
        label: CrowdTypeTitle[CROWD_TYPE.CDP],
        value: CROWD_TYPE.CDP,
    },
    {
        label: CrowdTypeTitle[CROWD_TYPE.XDT],
        value: CROWD_TYPE.XDT,
    },
];

export enum CROWD_STATUS {
    ENABLE = '1',
    DISABLE = '0',
}

export const CrowdStatusTitle = {
    [CROWD_STATUS.ENABLE]: '启用',
    [CROWD_STATUS.DISABLE]: '禁用',
};

export const CrowdStatusOptions = [
    {
        label: CrowdStatusTitle[CROWD_STATUS.ENABLE],
        value: CROWD_STATUS.ENABLE,
    },
    {
        label: CrowdStatusTitle[CROWD_STATUS.DISABLE],
        value: CROWD_STATUS.DISABLE,
    },
];
