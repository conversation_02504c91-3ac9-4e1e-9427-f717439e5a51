/**
 * 任务类型 ONCE("01", "单次执行"),CYCL<PERSON>("02", "周期执行")
 */
export const TASK_CYCLE_TYPE_ENUM = {
    ONCE: '01',
    CYCLE: '02',
};

/**
 * 执行周期 DAY("01", "每日"),WEEK("02", "每周"),MONTH("03", "每月"),
 */
export const EXEC_CYCLE_TYPE_ENUM = {
    DAILY: '01',
    WEEKLY: '02',
    MONTHLY: '03',
};

// 周类型
export const WEEKLY_OPTIONS = [
    { label: '一', value: '1' },
    { label: '二', value: '2' },
    { label: '三', value: '3' },
    { label: '四', value: '4' },
    { label: '五', value: '5' },
    { label: '六', value: '6' },
    { label: '日', value: '7' },
];

/**
 * 模板类型
 *  MSG("01", "短信"),
 *  APP("02", "APP"),
 *  E_MAIL("03", "邮件"),
 *  WX("04", "微信"),
 *  ALIPAY_MINI("05", "支付宝小程序"),
 *  UNION_PAY_YSF("06", "银联"),
 *  AILPAYV2("07", "支付宝小程序V2"),
 *  DING_TALK("08", "钉钉"),
 *  WECHAT_ONCE("09", "微信一次"),
 *  WECHAT_PUBLIC("10", "微信公共");
 */
export const MESSAGE_TEMPLATE_TYPE_ENUM = {
    MSG: '01', // 短信
    APP: '02', // APP
    EMAIl: '03', // 邮件
    WX: '04', // 微信
    ALIPAY_MINI: '05', // 支付宝小程序
    UNION_PAY_YSF: '06', // 云闪付
    AILPAYV2: '07', // 支付宝小程序v2
    DING_TALK: '08', // 钉钉
    WECHAT_ONCE: '09', // 微信一次
    WECHAT_PUBLIC: '10', // 微信公共
};

export const MESSAGE_TEMPLATE_TYPE_VALUE_ENUM = {
    [MESSAGE_TEMPLATE_TYPE_ENUM.MSG]: '短信',
    [MESSAGE_TEMPLATE_TYPE_ENUM.APP]: 'APP',
    [MESSAGE_TEMPLATE_TYPE_ENUM.EMAIl]: '邮件',
    [MESSAGE_TEMPLATE_TYPE_ENUM.WX]: '微信',
    [MESSAGE_TEMPLATE_TYPE_ENUM.ALIPAY_MINI]: '支付宝小程序',
    [MESSAGE_TEMPLATE_TYPE_ENUM.UNION_PAY_YSF]: '云闪付',
    [MESSAGE_TEMPLATE_TYPE_ENUM.AILPAYV2]: '支付宝小程序v2',
    [MESSAGE_TEMPLATE_TYPE_ENUM.DING_TALK]: '钉钉',
    [MESSAGE_TEMPLATE_TYPE_ENUM.WECHAT_ONCE]: '微信一次',
    [MESSAGE_TEMPLATE_TYPE_ENUM.WECHAT_PUBLIC]: '短信',
    [MESSAGE_TEMPLATE_TYPE_ENUM.MSG]: '微信公共',
};
/**
 * 消息类型
 * ALIPAY_ONCE_SUB("0701", "支付宝一次性订阅消息"),
 * ALIPAY_FOREVER_SUB("0702", "支付宝长期性订阅消息"),
 * WC_ONCE_SUB("0901", "微信一次性订阅消息"),
 * WC_FOREVER_SUB("0902", "微信订阅消息");
 * ORDER_MESSAGE("0001", "订单消息"),
 */
export const MESSAGE_TYPE_ENUM = {
    ALIPAY_ONCE_SUB: '0701', // 支付宝一次性订阅消息
    ALIPAY_FOREVER_SUB: '0702', // 支付宝长期性订阅消息
    WC_ONCE_SUB: '0901', // 微信一次性订阅消息
    WC_FOREVER_SUB: '0902', // 微信订阅消息
    ORDER_MESSAGE: '0001', //订单消息
    WC_SERVUCE_TMP: '0002', //微信服务号模板消息
};

export const MESSAGE_TYPE_VALUE_ENUM = {
    [MESSAGE_TYPE_ENUM.ALIPAY_ONCE_SUB]: '支付宝一次性订阅消息',
    [MESSAGE_TYPE_ENUM.ALIPAY_FOREVER_SUB]: '支付宝长期性订阅消息',
    [MESSAGE_TYPE_ENUM.WC_ONCE_SUB]: '微信一次性订阅消息',
    [MESSAGE_TYPE_ENUM.WC_FOREVER_SUB]: '微信长期性订阅消息',
    [MESSAGE_TYPE_ENUM.ORDER_MESSAGE]: '订单消息',
    [MESSAGE_TYPE_ENUM.WC_SERVUCE_TMP]: '微信服务号模板消息',
};
