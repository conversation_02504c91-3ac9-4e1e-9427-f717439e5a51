export const CHANNEL_TYPES = {
    APP: '01', //app
    ALI: '10', // 支付宝
    WX: '22', // 微信
    AMAP: '11', // 高德
    BAIDU: '15', //百度
    YSF: '13', // 云闪付
    LINGXI: '0602', // 灵犀
    JIUDAO: '0603', // 就到出行
    XIANG: '0604', // 响车车
    EFZHOU: '0605', // e福州
};

//活动渠道链接类型
export const ACT_LINK_TYPES = {
    INSIDE: '01', //内部,
    ALI_OUTSIDE: '02', //支付宝外部
    WECHAT_SCHEME: '03', //微信scheme
    WECHAT_LINK: '08', //微信LinkUrl
    WECHAT_QRCODE: '09', //微信小程序码
    BAIDU: '04', //百度app
    BAIDU_MAP: '05', //百度地图
    AMAP: '06', //高德地图
    YSF: '07', //云闪付
    POLYMERIZATION: '99', //聚合码
};

//小程序活动页面类型
export const APPLET_ACT_TYPES = {
    GIFT: '01', //页面领取
    INVITE: '02', //邀请有礼
    INVITE_SOURCE: '03', //邀请
    MEMBER_PLAN: '04', //会员方案
    MEMBER_RIGHT: '05', //会员权益
    CARD: '06', //权益卡方案
    CARD_GROUP: '07', //权益卡团购
    LARGE_ACT: '08', //大促
    VIP: '09', //会员页
    TEMPLATE: '10', //页面模板
};

//活动类型
export const ACT_TYPES = {
    COUPON_SPREAD: '01', // '优惠券推广'
    DISCOUNT: '02', //'限时打折'
    FULL: '03', //'满就送'
    FIRST: '04', //首单免
    REGISTER: '05', //注册送
    NEW: '06', //推荐新人送
    PRESTRORE: '07', //预存赠送
    REDUCE: '10', //限时立减
    TEAM: '11', //组队优惠
    GROUP: '12', //组团优惠
    COUPON_EXCHANGE: '13', //优惠券兑换
    APPLE: '14', //小程序有礼
    TURN: '15', //翻牌
    OPERATOR_DIS: '16', //运营商优惠
    AD: '17', //平台广告投放
    MEMBER_CARD: '18', //充享惠会员卡
    COMPANY: '19', //企业优惠
    OPER_LIMITE_REDUCE: '20', //运营商限时立减
    OPER_LIMITE_DIS: '21', //运营商限时打折
    WEEK_CARD: '22', //折扣卡
    COUPON: '25', //优惠券
    CHARGE_CARD: '26', //充电卡
    MEMBERSHIP: '27', // 会员
    INVOITE: '28', // 邀请有礼
    TURN_PLATE: '29', // 转盘
    DYNAMIC_PRICE: '30', //动态调价
    FILL_TASK: '31', //充单任务
    PRIZE_TASK: '34', // 充电有奖
    NEW_USER: '33', // 新人礼包
    TASK_ACT: '35', //任务活动
    TURNTABLE_ACT: '36', //转盘活动
    LARGE_ACTIVE: '38', //大促活动
    CROWDPLLICY_DISCOUNT: '46', // 策略打折
    CROWDPLLICY_REDUCTION: '47', // 策略立减
    SECKILL: '49', // 限时秒杀
    PRICE: '50', // 一口价
    CROWDPLLICY_PRICE: '51', // 策略一口价
    EQUITY_ACT: '61', // 发放权益
    EQUITY: '62', // 权益（订单优惠）
};
export const OPER_COOPERATION = {
    TAKE: '01', // 抽成
    BUY: '02', // 购电
    MIX: '03', // 混合
};
// 小程序有礼类型
export const ACTSUBTYPES = {
    DIRECT: '1401', //新页面领取
    RECEIVE: '1402', // 页面领取
    SEND: '1403', // 直接发放
    COUPON: '1404', // 兑换码兑换
    POPUP: '1405', // 弹窗领取
    ALI: '1406', // 支付宝商家券
    THIRD: '1407', // 第三方发放新电途券
    PRIZE_SEND: '1410', // 直接发奖活动
    WEIXIN: '1411', // 微信券活动
};
// 优惠券营销页面类型
export const ACT_VERSION_TYPES = {
    V_1_0: '1.0', // 支付宝小程序
    V_2_0: '2_0', // 高德小程序
};
// 发放对象
export const ACTOBJECTTYPES = {
    GROUP: '01', // 按人群发放
    USER: '02', // 指定用户发放
    CONDITION: '03', // 按条件发放（未开放）
};
// 领取状态 1未领取 2 已领取 3已失效
export const EXCHANGESTATUS = {
    UNTAKE: '1', // 未领取
    TAKED: '2', // 已领取
    OUTDATE: '3', // 已失效
};

// 旧活动状态
export const STATUS_TYPES = {
    ALL: '00',
    NOSTART: '1', // 未开始
    DOING: '2', // 进行中
    END: '3', // 已结束
    STOP: '4', // 已停止
    UNPAY: '5', // 待支付
    DRAFT: '0', // 草稿
};

// 新奖品活动状态
export const PRIZE_STATUS_TYPES = {
    ALL: '00',
    DRAFT: '0', // 草稿
    DOING: '1', // 进行中
    STOP: '2', // 已停止
    END: '3', // 已结束
};

// 合作伙伴分账状态
export const PARTNER_STATUS_TYPES = {
    ALL: '00',
    NOTSELLTE: '01', // 未分账
    SELLTED: '02', // 已分账
    OFFLINE: '03', // 线下分账
};

// 新活动状态
export const NEW_STATUS_TYPES = {
    ALL: '00',
    DRAFT: '01', // 草稿
    NOSTART: '02', // 未开始
    DOING: '03', // 进行中
    END: '04', // 已结束
};

// 旧活动状态
export const PLATFORM_STATUS_TYPES = {
    NOSTART: '1', // 待投放
    DOING: '2', // 投放中
    STOP: '3', // 已停止
    END: '4', // 已关闭
};

// 账单日月类型
export const BILL_TYPES = {
    DAY: '01',
    MONTH: '02',
};
export const BILL_STATUS = {
    ALL: '00',
    UNCONFIRMED: '0', // 未确认
    CONFIRMED: '1', // 确认
};

// 双充类型
export const DOUBLE_TYPES = {
    OPEN: '01', // 已开通
    UNAPPLIED: '02', // 待申请
    UNAUDITED: '03', // 待审核
    REFUSE: '04', // 已拒绝
};

// 企业卡类型
export const E_CARD_TYPES = {
    UNAUDITED: '01', // 待审核
    PASS: '02', // 已通过
    REFUSE: '03', // 已拒绝
};

export const CHART_DEFAULT_COLORS = [
    '#3BA1FF',
    '#4FCB74',
    '#FBD438',
    '#F04864',
    '#F04864',
    '#9860E5',
    '#37CBCB',
];

// 订单渠道
export const ORDER_CHANNEL_TYPES = {
    ALI: '01', // 支付宝小程序
    AMAP: '02', // 高德小程序
    APP: '03', // 新电途APP
    WUXI: '04', // 无锡H5
    JIUDAO: '05', // 就到H5
    WX: '06', // 微信小程序
};

// 订单状态
export const ORDER_STATUS = {
    ALL: '00', // 全部
    UNPAID: '01', // 待支付
    UNASSESS: '02', // 待评价
    DONE: '03', // 已完成
    CANCEL: '04', // 已取消
};

// 开票状态
export const INVIOCE_STATUS = {
    ALL: '00', // 全部
    NOTISSUED: '01', // 未出具
    OPENING: '04', // 开具中
    SUCCESS: '02', // 已开具
    FAIL: '03', // 开具失败
    CANCEL: '05', // 已作废
    REDING: '06', //红冲中
    RED_FAIL: '07', // 红冲失败
};

// 开票方式
export const INVIOCE_MODE = {
    PLAT_DO: '01', // 平台代开
    PLAT_JOINT: '02', // 平台对接
    OFFLINE: '03', // 线下开票
    XDT: '04', // 新电途开票
    SAFE_CHARGE: '05', // 安心充开票
};

// 企业状态
export const COMPANY_STATUS = {
    ALL: '00',
    DOING: '01', // 使用中
    SIGN: '02', // 签约中
    END: '03', // 已停用
    DEL: '04', // 删除
};

// 权益卡月卡状态
export const WEEK_CARD_STATUS = {
    ALL: '00',
    USED: '1', // 待开始
    USEING: '2', // 使用中
    OUT: '3', // 已超额
    END: '4', // 已结束
    REFUND: '5', // 已退卡
};

// 权益卡类型
export const WEEK_CARD_TYPES = {
    ALL: '00',
    COMMON: '01', // 普通卡
    AGENT: '02', // 分销代理卡
    GROUP: '03', // 团购卡
};

// 代理申请状态
export const AGENT_STATUS = {
    ALL: '000',
    APPLY: '00', // 未申请
    REVIEW: '01', // 审核中
    SUCCESS: '02', // 审核通过
    REFUND: '03', // 已拒绝
    CLOSE: '04', // 已关闭
    OVER: '05', // 过期
};
// 分润规则类型
export const RULE_TYPES = {
    COMMON: '01', // 通用
    STATION: '02', // 场站
    // TEMPLATE: '03', // 模板
    CITY: '03', // 城市
};

// 券类型
export const COUPON_TYPES = {
    XDT: '03', // 新电途平台
    ALI: '01', // 支付宝
    OTHER: '02', // 第三方
    BUY: '04', // 购卡券
};

// 券类型
export const NEW_COUPON_TYPES = {
    XDT: '21', // 新电途平台
    ALI: '22', // 支付宝
    OTHER: '24', // 非充电券
    BANGDAO: '23', // 邦道智术券
    WEIXIN: '25', //微信商家券
};

// 礼包券类型
export const GIFT_TYPES = {
    COUPON: '01', //优惠券
    RIGHT: '02', //权益
    EXP_MEMBER: '03', //体验会员
    DRAW: '04', //抽奖次数
    ENERGY: '05', //能量
    INTEGRAL: '06', //积分
};

// 活动范围
export const RANGE_TYPES = {
    ALL: '1', // 全部
    OTHER: '0', //  部分
};

// 选项卡类型
export const SELECT_TYPES = {
    ALL: '00', // 默认全都允许选
    EXCEPTBUY: '01', // 除了购电模式，其他都可选
    ONLYBUY: '02', // 仅购电模式可选
};

// 选项卡类型
export const MATCH_TYPES = {
    UNMATCHED: '0', // 未匹配
    MATCHED: '1', // 已匹配
    DOUBLEMACHED: '2', // 重复匹配
    ABANDON: '99', // 场站废弃
};

export const LOGIN_ERROR_CODE = {
    ENM_security_1: '用户修改密码操作成功！',
    ENM_security_2: '原密码错误,请重新输入！',
    ENM_security_3: '用户修改密码操作失败！',
    ENM_security_4: '用户修改密码操作失败！',
    ENM_security_5: '用户修改密码操作失败！',
    ENM_security_6: '此账号密码禁止修改！',
    ENM_security_7: '密码与确认密码不一致！',
    ENM_security_8: '密码与确认密码不能为空！',
    ENM_security_00: '登录过程中发生未知异常！',
    ENM_security_01: '密码太短，请修改！',
    ENM_security_02: '密码必须包含字母大小写',
    ENM_security_03: '密码必须包含数字字母大小写',
    ENM_security_04: '密码必须包含数字和字母',
    ENM_security_05: '密码必须包含数字字母大小写符号',
    ENM_security_06: '密码必须包含符号',
    ENM_security_07: '密码不能和历史密码重复',
    ENM_security_08: '初始密码未修改，请修改密码！',
    ENM_security_09: '系统当前用户 密码过期，请修改！',
    ENM_security_10: '系统当前用户禁止在当前客户端地址访问！',
    ENM_security_11: '系统当前用户非工作时间不允许登陆！',
    ENM_security_12: '无效的用户名/密码',
    ENM_security_13: '验证码输入错误！',
    ENM_security_14: '请求头被篡改，登录失败！',
    ENM_security_15: '客户端地址被篡改，登录失败！',
    ENM_security_16: '密码错误次数太多，账号自动锁定！',
    ENM_security_17: '验证码错误次数太多，账号自动锁定！',
    ENM_security_18: '用户账号已被锁定',
    ENM_security_19: '用户账号已被禁用或删除',
    ENM_security_20: '用户密码不能与用户名相同',
    ENM_security_21: '用户名密码格式不符合校验规则，请修改。',
    ENM_security_22: '当前用户密码被篡改，存在风险。请联系系统管理员！',
    ENM_security_23: '当前用户存在风险，请联系管理员重置密码再尝试登录！',
};

// 定时任务类型
export const TIMERTASK_STATUS = {
    ALL: '', // 全部
    WAIT: 0, // 待执行
    DOING: 1, // 执行中
    STOP: 2, // 已停止
    DONE: 3, // 已执行/已完成
};

// 告警管理-设备异常-接单类型
export const EQUIPAB_STATUS = {
    ALL: '3', // 全部
    WAIT: '1', // 待接单
    DONE: '2', // 已接单
    MY: '0', // 我的
};

// 福利-状态
export const WELFARE_STATUS = {
    ALL: '', // 全部
    WAIT: '1', // 待开始
    DOING: '2', // 进行中
    DONE: '3', // 已结束
    STOP: '4', // 已终止
    DRAFT: '0', // 草稿
};
// 福利类型
export const WELFARE_TYPES = {
    TASK: '10', // 任务专区
};
// 福利任务类型
export const WELFARE_ACTSUBTYPES = {
    MAIN: '3101', // 主访充电任务
    ZHI: '3102', // 支车主认证
};

//奖品类型
export const PRIZE_TYPES = {
    COUPON: '03', //优惠券1.0
    NEW_COUPON: '01', //优惠券2.0
    GIFT_BAG: '02', //礼包
    SIGNIN_CARD: '04', // 补签卡
};

//充电活动类型
export const CHARGE_COUPON_TYPES = {
    OLD: '1', //1.0
    NEW: '2', //2.0
};

//场站配置页面类型
export const STATION_CONFIG_PAGE_TYPES = {
    COUPON_1: '01', // # 优惠券1.0
    MEMBER_PRICE_STATION: '02', // # 会员权益特价站点
    TURN: '03', // # 抽奖活动
    BUSINESS_1: '04', // # 营销活动1.0
    BUSINESS_2: '05', // # 营销活动2.0
    GROUP: '06', // # 组团
    TEAM: '07', // # 组队
    CARD_GROUP: '08', // # 权益卡团购
    CARD_PLAN: '09', // # 权益卡方案配置
    WORKER: '10', // # 工单
    MEMBER_DAY: '11', //会员日
    RECRUIT_MERCHANTS: '12', //招商套餐
};

export const LARGE_ACT_TYPES = {
    //大促子活动
    TASK_ACT: '35', //任务活动
    TASK_SHARE_ACT: '3501', //3501-分享活动
    TASK_CHARGE: '3502', //3502-完成充电
    TASK_OPEN_VIP: '3503', //3503-开通会员
    TASK_BUY_VIP_CPN: '3504', //3504-购买加量包
    TURNTABLE_ACT: '36', //转盘活动
    FLIP_ACT: '15', //翻牌
    PROMOTION_ACT: '17', //推广活动
    COUPON_ACT: '14', //优惠券营销活动
    POPUP_ACT: '1405', //活动礼品
    PAGE_PICKUP: '1401', //页面领取
    SECKILL: '49', //限时秒杀
    DISCOUNT_STATION: 'QY0005', //特价站
    //会场场景
    LARGE_DAY: '3801', //大促会场
    MEMBER_DAY: '3802', //会员日会场

    INVITE: '28', //邀请有礼
};

// 抽奖活动用途
export const ACT_CONFIG_TYPES = {
    DACU: '02', // 大促专场
    DAILY: '01', // 日常翻牌
    INVOTE: '03', // 邀请有礼
    VIP_DAY: '04', // 会员日
};

// 分销活动类型
export const ACT_SUB_TYPE = {
    NEW: '4101', // 拉新活动
    VIP: '4102', // 拉会员
    VIPMONTH: '410201', // 拉会员 月卡
    VIPQUARTER: '410202', // 拉会员 季卡
    VIPYEAR: '410203', // 拉会员 年卡
};

// 分销\兼职活动状态
export const ACT_STATE_LIST = {
    DRAFT: '0', // 草稿
    WAIT: '1', // 未开始
    DOING: '2', // 进行中
    DONE: '3', // 已结束
    STOP: '4', // 已终止
    PENDING: '6', // 待接单
};

// 分销判断
export const ACT_SUB_STATUS = {
    DOING: '1', // 进行中
    DIS: '2', // 未达标
    IS: '3', // 达标
    FAIL: '4', // 失败
};

//活动平台类型
export const COOPERATION_PLATFORM_TYPES = {
    XDT: 'xdt',
    XDT_X: 'xdt_x',
};

//人群画像应用类型
export const APPLY_TYPE = {
    APPLETGIFT: '11',
    MESSAGE: '10',
};
//人群画像
export const CROWD_TYPES = {
    USER: '01', //用户分群
    VIRTUALLY: '02', //虚拟画像
    REAL_TAG: '07', // 实体标签
    CDP_CROWD: '08', // 人群计算
};

//营销价格基准类型
export const ADJUST_PRICE_TYPES = {
    DEFAULT: '01', //默认
    OPER_ORIGIN: '02', //商家原价
    PUSH: '03', //推送结算价
    SETTLE: '04', //配置结算价
};
