export const BASE_URL = '/everest/webapi';
export const REQUEST_TIMEOUT = 1000 * 60 * 3;
export const MNG_BIL_URL = '/mng-bil/webapi';
export const MNG_DEF_URL = '/mng-def/webapi';
export const MNG_DEFRAY_URL = '/mng-defray/webapi';
export const MNG_AST_URL = '/mng-ast/webapi';
export const MNG_DCR_URL = '/mng-dcr/webapi';
export const MNG_PUB = '/mng-pub';
export const MNG_PUB_URL = '/mng-pub/webapi';
export const MNG_CUST = '/mng-cust/webapi';
export const MNG_SSO_URL = '/mng-ssoapi/webapi';
export const MNG_SSO_OPEN = '/mng-ssoapi/openapi';
export const MNG_CODE = '/mng-code';
export const MNG_ALY = '/mng-aly/webapi';
export const MNG_ORDER_URL = '/mng-order/webapi';
export const MNG_EVENT_URL = '/mng-event/webapi';
//定价系统
export const MNG_PLATPRICE_URL = '/mng-platprice/webapi';
export const MNG_EVENT = '/mng-event/webapi';
const img_base = '/aliMini/xdt';
export const API_HOST =
    process.env.NODE_ENV === 'development' ? 'https://dev.evshine.net' : window.location.origin;
export const SCAN_LOGIN_PAGES = [
    '/screen/xdt',
    '/newscreen/xdt',
    '/largescreen/xdt',
    '/largescreen/store',
    '/newscreen/newxdt',
    '/largescreen/newxdt',
    '/largescreen/wuxixdt',
    '/largescreen/wuxixdtlong',
    '/largescreen/wuxixdtlongtwo',
];

const isPro =
    window.location.origin === 'https://www.evshine.cn' ||
    window.location.origin === 'https://www.xdtev.com';

let cdnStaticUrl = 'https://cdn-static.evshine.net';
let ysfAppId = '077ed6ee99923b18';
if (isPro) {
    cdnStaticUrl = 'https://cdn-static.xdtev.com';
    ysfAppId = '53e61b27f7b85a49';
}
export const YSF_APPID = ysfAppId;
export const CDN_STATIC_URL = cdnStaticUrl;
export const IMG_URL = `${cdnStaticUrl}${img_base}`;
