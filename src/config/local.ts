import { store_zh, store_en } from './local/store';
export const zhLanguage = {
    ...store_zh,
    'page.screen.title': '新电途大屏',

    /**************unit********** */

    'page.screen.unit': '单位：',
    'page.screen.unit.tenThousand': '万',
    'page.screen.unit.one': '个',
    'page.screen.unit.disEnOne': '个',
    'page.screen.unit.tenThousandInd': '万个',
    'page.screen.unit.disEnCount': '万个',
    'page.screen.unit.oneSeat': '座',
    'page.screen.unit.tenThousandSeat': '万座',
    'page.screen.unit.degrees': '度',
    'page.screen.unit.tenThousandDegrees': '万度',
    'page.screen.unit.order': '笔',
    'page.screen.unit.tenThousandOrder': '万笔',

    'page.screen.unit.lasterYear': '去年',
    'page.screen.unit.thisYear': '本年',
    'page.screen.unit.month': '月',

    'page.screen.unit.ratio': '占比',
    'page.screen.unit.yoy': '同比',
    'page.screen.unit.qoq': '环比',
    'page.screen.unit.city': '市',

    /**************left********** */

    'page.screen.left.stationType': '充电站类别',
    'page.screen.left.deviceType': '充电站类别',

    'page.screen.left.pileNum': '充电桩数量',
    'page.screen.left.oper': '运营商',
    'page.screen.left.chargeStation': '充电站',
    'page.screen.left.station': '充电站',
    'page.screen.left.pile': '充电桩',
    'page.screen.left.gun': '充电枪',

    'page.screen.left.overview.station': '充电站 (座)',
    'page.screen.left.overview.device': '充电设备',
    'page.screen.left.overview.facilities': '充电设施',
    'page.screen.left.overview.oper': '运营商（个）',
    'page.screen.left.overview.title': '充电桩接入概览',
    'page.screen.left.overview.cover': '覆盖',
    'page.screen.left.overview.city': '城市',
    'page.screen.left.overview.cities': '城市',
    'page.screen.left.overview.county': '区县',

    'page.screen.left.pileNumRank.title': '充电桩数量排名',
    'page.screen.left.pileNumRank.tip': '运营商充电桩数量',
    'page.screen.left.pileNumRank.tipRank': '运营商排名',

    'page.screen.left.totalOrderNum': '充电订单',

    /**************middle********** */
    'page.screen.middle.nationwide': '全国',
    'page.screen.middle.chargeMap': '充电地图',
    'page.screen.middle.coverCity': '已覆盖城市',
    'page.screen.middle.useXdt': '用户通过新电途',
    'page.screen.middle.startCharge': '启动充电',
    'page.screen.middle.endCharge': '结束充电',
    'page.screen.middle.back': '返回',
    'page.screen.middle.currentCity': '当前地区',
    'page.screen.middle.totalChargeNum': '累计充电量',
    'page.screen.middle.totalOrderNum': '累计订单数量',
    'page.screen.middle.registerNum': '注册用户数',
    'page.screen.middle.chargeview': '充电量情况',
    'page.screen.middle.userview': '用户情况',
    'page.screen.middle.forDay': '按日',
    'page.screen.middle.forMonth': '按月',
    'page.screen.middle.forYear': '按年',
    'page.screen.middle.totalRegisterNum': '累计注册用户数',
    'page.screen.middle.newUserYOY': '新用户YOY',
    'page.screen.middle.userGrowthTrend': '用户增长趋势',

    'page.screen.middle.forecast': '预测',

    'page.screen.middle.map.stations': '站点',

    /**************right********** */
    'page.screen.right.dayCharge': '今日充电',
    'page.screen.right.yesterdayCharge': '昨日充电',
    'page.screen.right.monthCharge': '本月充电',
    'page.screen.right.lastMonthCharge': '上月充电',
    'page.screen.right.activeCity': '活跃城市',
    'page.screen.right.activeArea': '活跃区域',

    'page.screen.right.operateview': '运营情况',
    'page.screen.right.dayEle': '今日电量',
    'page.screen.right.monthEle': '本月电量',
    'page.screen.right.yearEle': '本年电量',
    'page.screen.right.charging': '充电中',
    'page.screen.right.operateoverview': '运营概览',
    'page.screen.right.pileNumRank.title': '充电桩城市排名',
    'page.screen.right.pileNumAreaRank.title': '充电桩区域排名',
    'page.screen.right.rateAreaRank.title': '充电电量占比区域排名',

    'page.screen.right.curCity': '当前城市',
};

export const enLanguage = {
    ...store_en,
    'page.screen.title': 'New Electricity Approach Big Screen',

    /**************unit********** */

    'page.screen.unit': 'Unit：',
    'page.screen.unit.tenThousand': ' ',
    'page.screen.unit.one': 'Count',
    'page.screen.unit.disEnOne': '',
    'page.screen.unit.tenThousandInd': 'Count',
    'page.screen.unit.disEnCount': '',
    'page.screen.unit.oneSeat': 'Station',
    'page.screen.unit.tenThousandSeat': 'Stations',
    'page.screen.unit.degrees': 'kWh',
    'page.screen.unit.tenThousandDegrees': 'kWh',
    'page.screen.unit.order': 'Count',
    'page.screen.unit.tenThousandOrder': 'Count',

    'page.screen.unit.lasterYear': 'Last year',
    'page.screen.unit.thisYear': 'This year',
    'page.screen.unit.month': 'Month',

    'page.screen.unit.ratio': 'Ratio',
    'page.screen.unit.yoy': 'YOY',
    'page.screen.unit.qoq': 'QOQ',
    'page.screen.unit.city': 'City',

    /**************left********** */

    'page.screen.left.stationType': 'Station Type',
    'page.screen.left.deviceType': 'Device Type',

    'page.screen.left.pileNum': 'Number of charging piles',
    'page.screen.left.oper': 'Operator',
    'page.screen.left.chargeStation': 'Charge Stations',
    'page.screen.left.station': 'Station',
    'page.screen.left.pile': 'Pile',
    'page.screen.left.gun': 'Gun',

    'page.screen.left.overview.station': 'Charge Stations',
    'page.screen.left.overview.device': 'Charge Facilities',
    'page.screen.left.overview.facilities': 'Charge Facilities',
    'page.screen.left.overview.oper': 'Operators',
    'page.screen.left.overview.title': 'Overview of charging pile access',
    'page.screen.left.overview.cover': 'Cover',
    'page.screen.left.overview.city': 'City',
    'page.screen.left.overview.cities': 'Cities',
    'page.screen.left.overview.county': 'Country',

    'page.screen.left.pileNumRank.title': 'Ranking of the number of charging piles',
    'page.screen.left.pileNumRank.tip': 'Number of charging piles for operators',
    'page.screen.left.pileNumRank.tipRank': 'Operator Ranking',

    'page.screen.left.totalOrderNum': 'Charge Orders',

    /**************middle********** */
    'page.screen.middle.nationwide': 'Nationwide',
    'page.screen.middle.chargeMap': 'Charging map',
    'page.screen.middle.coverCity': 'City covered',
    'page.screen.middle.useXdt': 'Users via NEA routes',
    'page.screen.middle.startCharge': 'Start charging',
    'page.screen.middle.endCharge': 'End charging',
    'page.screen.middle.back': 'Back',
    'page.screen.middle.currentCity': 'Current City',
    'page.screen.middle.totalChargeNum': 'ACC ',
    'page.screen.middle.totalOrderNum': 'ACC order No.',
    'page.screen.middle.registerNum': 'No. of reg. users',
    'page.screen.middle.chargeview': 'Charging Overview',
    'page.screen.middle.userview': 'User Overview',
    'page.screen.middle.forDay': 'Day',
    'page.screen.middle.forMonth': 'Month',
    'page.screen.middle.forYear': 'Year',
    'page.screen.middle.totalRegisterNum': 'ACC No. of reg. users',
    'page.screen.middle.newUserYOY': 'New User YOY',
    'page.screen.middle.userGrowthTrend': 'User growth trend',

    'page.screen.middle.forecast': 'forecast',

    'page.screen.middle.map.stations': 'Stations',

    /**************right********** */
    'page.screen.right.dayCharge': 'Today',
    'page.screen.right.yesterdayCharge': 'Yesterday',
    'page.screen.right.monthCharge': 'This Month',
    'page.screen.right.lastMonthCharge': 'Last Month',
    'page.screen.right.activeCity': 'Active Cities',
    'page.screen.right.activeArea': 'Active Areas',

    'page.screen.right.operateview': 'Op. Situation',
    'page.screen.right.dayEle': 'Electricity today',
    'page.screen.right.monthEle': 'Electricity this month',
    'page.screen.right.yearEle': 'Electricity this year',
    'page.screen.right.charging': 'Charging in progress',
    'page.screen.right.operateoverview': 'Op. Overview',
    'page.screen.right.pileNumRank.title': 'Ranking of the number of charging city',
    'page.screen.right.pileNumAreaRank.title': 'Ranking of the number of charging county',
    'page.screen.right.rateAreaRank.title': 'Ranking of the rate of charging county',
    'page.screen.right.curCity': 'Current City',
};
