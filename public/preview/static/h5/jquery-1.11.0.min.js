/*! jQuery v1.11.0 | (c) 2005, 2014 jQuery Foundation, Inc. | jquery.org/license */
!(function (e, t) {
    'object' == typeof module && 'object' == typeof module.exports
        ? (module.exports = e.document
              ? t(e, !0)
              : function (e) {
                    if (!e.document) throw new Error('jQuery requires a window with a document');
                    return t(e);
                })
        : t(e);
})('undefined' != typeof window ? window : this, function (e, t) {
    var n = [],
        i = n.slice,
        r = n.concat,
        o = n.push,
        a = n.indexOf,
        s = {},
        l = s.toString,
        u = s.hasOwnProperty,
        c = ''.trim,
        d = {},
        f = '1.11.0',
        p = function (e, t) {
            return new p.fn.init(e, t);
        },
        h = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,
        m = /^-ms-/,
        g = /-([\da-z])/gi,
        v = function (e, t) {
            return t.toUpperCase();
        };
    function y(e) {
        var t = e.length,
            n = p.type(e);
        return (
            'function' !== n &&
            !p.isWindow(e) &&
            (!(1 !== e.nodeType || !t) ||
                'array' === n ||
                0 === t ||
                ('number' == typeof t && t > 0 && t - 1 in e))
        );
    }
    (p.fn = p.prototype =
        {
            jquery: f,
            constructor: p,
            selector: '',
            length: 0,
            toArray: function () {
                return i.call(this);
            },
            get: function (e) {
                return null != e ? (0 > e ? this[e + this.length] : this[e]) : i.call(this);
            },
            pushStack: function (e) {
                var t = p.merge(this.constructor(), e);
                return (t.prevObject = this), (t.context = this.context), t;
            },
            each: function (e, t) {
                return p.each(this, e, t);
            },
            map: function (e) {
                return this.pushStack(
                    p.map(this, function (t, n) {
                        return e.call(t, n, t);
                    }),
                );
            },
            slice: function () {
                return this.pushStack(i.apply(this, arguments));
            },
            first: function () {
                return this.eq(0);
            },
            last: function () {
                return this.eq(-1);
            },
            eq: function (e) {
                var t = this.length,
                    n = +e + (0 > e ? t : 0);
                return this.pushStack(n >= 0 && t > n ? [this[n]] : []);
            },
            end: function () {
                return this.prevObject || this.constructor(null);
            },
            push: o,
            sort: n.sort,
            splice: n.splice,
        }),
        (p.extend = p.fn.extend =
            function () {
                var e,
                    t,
                    n,
                    i,
                    r,
                    o,
                    a = arguments[0] || {},
                    s = 1,
                    l = arguments.length,
                    u = !1;
                for (
                    'boolean' == typeof a && ((u = a), (a = arguments[s] || {}), s++),
                        'object' == typeof a || p.isFunction(a) || (a = {}),
                        s === l && ((a = this), s--);
                    l > s;
                    s++
                )
                    if (null != (r = arguments[s]))
                        for (i in r)
                            (e = a[i]),
                                (n = r[i]),
                                a !== n &&
                                    (u && n && (p.isPlainObject(n) || (t = p.isArray(n)))
                                        ? (t
                                              ? ((t = !1), (o = e && p.isArray(e) ? e : []))
                                              : (o = e && p.isPlainObject(e) ? e : {}),
                                          (a[i] = p.extend(u, o, n)))
                                        : void 0 !== n && (a[i] = n));
                return a;
            }),
        p.extend({
            expando: 'jQuery' + (f + Math.random()).replace(/\D/g, ''),
            isReady: !0,
            error: function (e) {
                throw new Error(e);
            },
            noop: function () {},
            isFunction: function (e) {
                return 'function' === p.type(e);
            },
            isArray:
                Array.isArray ||
                function (e) {
                    return 'array' === p.type(e);
                },
            isWindow: function (e) {
                return null != e && e == e.window;
            },
            isNumeric: function (e) {
                return e - parseFloat(e) >= 0;
            },
            isEmptyObject: function (e) {
                var t;
                for (t in e) return !1;
                return !0;
            },
            isPlainObject: function (e) {
                var t;
                if (!e || 'object' !== p.type(e) || e.nodeType || p.isWindow(e)) return !1;
                try {
                    if (
                        e.constructor &&
                        !u.call(e, 'constructor') &&
                        !u.call(e.constructor.prototype, 'isPrototypeOf')
                    )
                        return !1;
                } catch (n) {
                    return !1;
                }
                if (d.ownLast) for (t in e) return u.call(e, t);
                for (t in e);
                return void 0 === t || u.call(e, t);
            },
            type: function (e) {
                return null == e
                    ? e + ''
                    : 'object' == typeof e || 'function' == typeof e
                    ? s[l.call(e)] || 'object'
                    : typeof e;
            },
            globalEval: function (t) {
                t &&
                    p.trim(t) &&
                    (
                        e.execScript ||
                        function (t) {
                            e.eval.call(e, t);
                        }
                    )(t);
            },
            camelCase: function (e) {
                return e.replace(m, 'ms-').replace(g, v);
            },
            nodeName: function (e, t) {
                return e.nodeName && e.nodeName.toLowerCase() === t.toLowerCase();
            },
            each: function (e, t, n) {
                var i,
                    r = 0,
                    o = e.length,
                    a = y(e);
                if (n) {
                    if (a) {
                        for (; o > r; r++) if (((i = t.apply(e[r], n)), !1 === i)) break;
                    } else for (r in e) if (((i = t.apply(e[r], n)), !1 === i)) break;
                } else if (a) {
                    for (; o > r; r++) if (((i = t.call(e[r], r, e[r])), !1 === i)) break;
                } else for (r in e) if (((i = t.call(e[r], r, e[r])), !1 === i)) break;
                return e;
            },
            trim:
                c && !c.call('\ufeff ')
                    ? function (e) {
                          return null == e ? '' : c.call(e);
                      }
                    : function (e) {
                          return null == e ? '' : (e + '').replace(h, '');
                      },
            makeArray: function (e, t) {
                var n = t || [];
                return (
                    null != e &&
                        (y(Object(e)) ? p.merge(n, 'string' == typeof e ? [e] : e) : o.call(n, e)),
                    n
                );
            },
            inArray: function (e, t, n) {
                var i;
                if (t) {
                    if (a) return a.call(t, e, n);
                    for (i = t.length, n = n ? (0 > n ? Math.max(0, i + n) : n) : 0; i > n; n++)
                        if (n in t && t[n] === e) return n;
                }
                return -1;
            },
            merge: function (e, t) {
                var n = +t.length,
                    i = 0,
                    r = e.length;
                while (n > i) e[r++] = t[i++];
                if (n !== n) while (void 0 !== t[i]) e[r++] = t[i++];
                return (e.length = r), e;
            },
            grep: function (e, t, n) {
                for (var i, r = [], o = 0, a = e.length, s = !n; a > o; o++)
                    (i = !t(e[o], o)), i !== s && r.push(e[o]);
                return r;
            },
            map: function (e, t, n) {
                var i,
                    o = 0,
                    a = e.length,
                    s = y(e),
                    l = [];
                if (s) for (; a > o; o++) (i = t(e[o], o, n)), null != i && l.push(i);
                else for (o in e) (i = t(e[o], o, n)), null != i && l.push(i);
                return r.apply([], l);
            },
            guid: 1,
            proxy: function (e, t) {
                var n, r, o;
                return (
                    'string' == typeof t && ((o = e[t]), (t = e), (e = o)),
                    p.isFunction(e)
                        ? ((n = i.call(arguments, 2)),
                          (r = function () {
                              return e.apply(t || this, n.concat(i.call(arguments)));
                          }),
                          (r.guid = e.guid = e.guid || p.guid++),
                          r)
                        : void 0
                );
            },
            now: function () {
                return +new Date();
            },
            support: d,
        }),
        p.each(
            'Boolean Number String Function Array Date RegExp Object Error'.split(' '),
            function (e, t) {
                s['[object ' + t + ']'] = t.toLowerCase();
            },
        );
    var b = (function (e) {
        var t,
            n,
            i,
            r,
            o,
            a,
            s,
            l,
            u,
            c,
            d,
            f,
            p,
            h,
            m,
            g,
            v,
            y = 'sizzle' + -new Date(),
            b = e.document,
            x = 0,
            w = 0,
            T = re(),
            C = re(),
            N = re(),
            E = function (e, t) {
                return e === t && (u = !0), 0;
            },
            k = 'undefined',
            S = 1 << 31,
            A = {}.hasOwnProperty,
            D = [],
            j = D.pop,
            L = D.push,
            H = D.push,
            q = D.slice,
            _ =
                D.indexOf ||
                function (e) {
                    for (var t = 0, n = this.length; n > t; t++) if (this[t] === e) return t;
                    return -1;
                },
            M =
                'checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped',
            F = '[\\x20\\t\\r\\n\\f]',
            O = '(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+',
            B = O.replace('w', 'w#'),
            P =
                '\\[' +
                F +
                '*(' +
                O +
                ')' +
                F +
                '*(?:([*^$|!~]?=)' +
                F +
                '*(?:([\'"])((?:\\\\.|[^\\\\])*?)\\3|(' +
                B +
                ')|)|)' +
                F +
                '*\\]',
            R =
                ':(' +
                O +
                ')(?:\\((([\'"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|' +
                P.replace(3, 8) +
                ')*)|.*)\\)|)',
            W = new RegExp('^' + F + '+|((?:^|[^\\\\])(?:\\\\.)*)' + F + '+$', 'g'),
            $ = new RegExp('^' + F + '*,' + F + '*'),
            z = new RegExp('^' + F + '*([>+~]|' + F + ')' + F + '*'),
            I = new RegExp('=' + F + '*([^\\]\'"]*?)' + F + '*\\]', 'g'),
            X = new RegExp(R),
            U = new RegExp('^' + B + '$'),
            V = {
                ID: new RegExp('^#(' + O + ')'),
                CLASS: new RegExp('^\\.(' + O + ')'),
                TAG: new RegExp('^(' + O.replace('w', 'w*') + ')'),
                ATTR: new RegExp('^' + P),
                PSEUDO: new RegExp('^' + R),
                CHILD: new RegExp(
                    '^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(' +
                        F +
                        '*(even|odd|(([+-]|)(\\d*)n|)' +
                        F +
                        '*(?:([+-]|)' +
                        F +
                        '*(\\d+)|))' +
                        F +
                        '*\\)|)',
                    'i',
                ),
                bool: new RegExp('^(?:' + M + ')$', 'i'),
                needsContext: new RegExp(
                    '^' +
                        F +
                        '*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(' +
                        F +
                        '*((?:-\\d)?\\d*)' +
                        F +
                        '*\\)|)(?=[^-]|$)',
                    'i',
                ),
            },
            J = /^(?:input|select|textarea|button)$/i,
            Y = /^h\d$/i,
            G = /^[^{]+\{\s*\[native \w/,
            Q = /^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,
            K = /[+~]/,
            Z = /'|\\/g,
            ee = new RegExp('\\\\([\\da-f]{1,6}' + F + '?|(' + F + ')|.)', 'ig'),
            te = function (e, t, n) {
                var i = '0x' + t - 65536;
                return i !== i || n
                    ? t
                    : 0 > i
                    ? String.fromCharCode(i + 65536)
                    : String.fromCharCode((i >> 10) | 55296, (1023 & i) | 56320);
            };
        try {
            H.apply((D = q.call(b.childNodes)), b.childNodes), D[b.childNodes.length].nodeType;
        } catch (ne) {
            H = {
                apply: D.length
                    ? function (e, t) {
                          L.apply(e, q.call(t));
                      }
                    : function (e, t) {
                          var n = e.length,
                              i = 0;
                          while ((e[n++] = t[i++]));
                          e.length = n - 1;
                      },
            };
        }
        function ie(e, t, i, r) {
            var o, a, s, l, u, f, m, g, x, w;
            if (
                ((t ? t.ownerDocument || t : b) !== d && c(t),
                (t = t || d),
                (i = i || []),
                !e || 'string' != typeof e)
            )
                return i;
            if (1 !== (l = t.nodeType) && 9 !== l) return [];
            if (p && !r) {
                if ((o = Q.exec(e)))
                    if ((s = o[1])) {
                        if (9 === l) {
                            if (((a = t.getElementById(s)), !a || !a.parentNode)) return i;
                            if (a.id === s) return i.push(a), i;
                        } else if (
                            t.ownerDocument &&
                            (a = t.ownerDocument.getElementById(s)) &&
                            v(t, a) &&
                            a.id === s
                        )
                            return i.push(a), i;
                    } else {
                        if (o[2]) return H.apply(i, t.getElementsByTagName(e)), i;
                        if ((s = o[3]) && n.getElementsByClassName && t.getElementsByClassName)
                            return H.apply(i, t.getElementsByClassName(s)), i;
                    }
                if (n.qsa && (!h || !h.test(e))) {
                    if (
                        ((g = m = y),
                        (x = t),
                        (w = 9 === l && e),
                        1 === l && 'object' !== t.nodeName.toLowerCase())
                    ) {
                        (f = he(e)),
                            (m = t.getAttribute('id'))
                                ? (g = m.replace(Z, '\\$&'))
                                : t.setAttribute('id', g),
                            (g = "[id='" + g + "'] "),
                            (u = f.length);
                        while (u--) f[u] = g + me(f[u]);
                        (x = (K.test(e) && fe(t.parentNode)) || t), (w = f.join(','));
                    }
                    if (w)
                        try {
                            return H.apply(i, x.querySelectorAll(w)), i;
                        } catch (T) {
                        } finally {
                            m || t.removeAttribute('id');
                        }
                }
            }
            return Ce(e.replace(W, '$1'), t, i, r);
        }
        function re() {
            var e = [];
            function t(n, r) {
                return e.push(n + ' ') > i.cacheLength && delete t[e.shift()], (t[n + ' '] = r);
            }
            return t;
        }
        function oe(e) {
            return (e[y] = !0), e;
        }
        function ae(e) {
            var t = d.createElement('div');
            try {
                return !!e(t);
            } catch (n) {
                return !1;
            } finally {
                t.parentNode && t.parentNode.removeChild(t), (t = null);
            }
        }
        function se(e, t) {
            var n = e.split('|'),
                r = e.length;
            while (r--) i.attrHandle[n[r]] = t;
        }
        function le(e, t) {
            var n = t && e,
                i =
                    n &&
                    1 === e.nodeType &&
                    1 === t.nodeType &&
                    (~t.sourceIndex || S) - (~e.sourceIndex || S);
            if (i) return i;
            if (n) while ((n = n.nextSibling)) if (n === t) return -1;
            return e ? 1 : -1;
        }
        function ue(e) {
            return function (t) {
                var n = t.nodeName.toLowerCase();
                return 'input' === n && t.type === e;
            };
        }
        function ce(e) {
            return function (t) {
                var n = t.nodeName.toLowerCase();
                return ('input' === n || 'button' === n) && t.type === e;
            };
        }
        function de(e) {
            return oe(function (t) {
                return (
                    (t = +t),
                    oe(function (n, i) {
                        var r,
                            o = e([], n.length, t),
                            a = o.length;
                        while (a--) n[(r = o[a])] && (n[r] = !(i[r] = n[r]));
                    })
                );
            });
        }
        function fe(e) {
            return e && typeof e.getElementsByTagName !== k && e;
        }
        for (t in ((n = ie.support = {}),
        (o = ie.isXML =
            function (e) {
                var t = e && (e.ownerDocument || e).documentElement;
                return !!t && 'HTML' !== t.nodeName;
            }),
        (c = ie.setDocument =
            function (e) {
                var t,
                    r = e ? e.ownerDocument || e : b,
                    a = r.defaultView;
                return r !== d && 9 === r.nodeType && r.documentElement
                    ? ((d = r),
                      (f = r.documentElement),
                      (p = !o(r)),
                      a &&
                          a !== a.top &&
                          (a.addEventListener
                              ? a.addEventListener(
                                    'unload',
                                    function () {
                                        c();
                                    },
                                    !1,
                                )
                              : a.attachEvent &&
                                a.attachEvent('onunload', function () {
                                    c();
                                })),
                      (n.attributes = ae(function (e) {
                          return (e.className = 'i'), !e.getAttribute('className');
                      })),
                      (n.getElementsByTagName = ae(function (e) {
                          return (
                              e.appendChild(r.createComment('')),
                              !e.getElementsByTagName('*').length
                          );
                      })),
                      (n.getElementsByClassName =
                          G.test(r.getElementsByClassName) &&
                          ae(function (e) {
                              return (
                                  (e.innerHTML = "<div class='a'></div><div class='a i'></div>"),
                                  (e.firstChild.className = 'i'),
                                  2 === e.getElementsByClassName('i').length
                              );
                          })),
                      (n.getById = ae(function (e) {
                          return (
                              (f.appendChild(e).id = y),
                              !r.getElementsByName || !r.getElementsByName(y).length
                          );
                      })),
                      n.getById
                          ? ((i.find.ID = function (e, t) {
                                if (typeof t.getElementById !== k && p) {
                                    var n = t.getElementById(e);
                                    return n && n.parentNode ? [n] : [];
                                }
                            }),
                            (i.filter.ID = function (e) {
                                var t = e.replace(ee, te);
                                return function (e) {
                                    return e.getAttribute('id') === t;
                                };
                            }))
                          : (delete i.find.ID,
                            (i.filter.ID = function (e) {
                                var t = e.replace(ee, te);
                                return function (e) {
                                    var n =
                                        typeof e.getAttributeNode !== k && e.getAttributeNode('id');
                                    return n && n.value === t;
                                };
                            })),
                      (i.find.TAG = n.getElementsByTagName
                          ? function (e, t) {
                                return typeof t.getElementsByTagName !== k
                                    ? t.getElementsByTagName(e)
                                    : void 0;
                            }
                          : function (e, t) {
                                var n,
                                    i = [],
                                    r = 0,
                                    o = t.getElementsByTagName(e);
                                if ('*' === e) {
                                    while ((n = o[r++])) 1 === n.nodeType && i.push(n);
                                    return i;
                                }
                                return o;
                            }),
                      (i.find.CLASS =
                          n.getElementsByClassName &&
                          function (e, t) {
                              return typeof t.getElementsByClassName !== k && p
                                  ? t.getElementsByClassName(e)
                                  : void 0;
                          }),
                      (m = []),
                      (h = []),
                      (n.qsa = G.test(r.querySelectorAll)) &&
                          (ae(function (e) {
                              (e.innerHTML = "<select t=''><option selected=''></option></select>"),
                                  e.querySelectorAll("[t^='']").length &&
                                      h.push('[*^$]=' + F + '*(?:\'\'|"")'),
                                  e.querySelectorAll('[selected]').length ||
                                      h.push('\\[' + F + '*(?:value|' + M + ')'),
                                  e.querySelectorAll(':checked').length || h.push(':checked');
                          }),
                          ae(function (e) {
                              var t = r.createElement('input');
                              t.setAttribute('type', 'hidden'),
                                  e.appendChild(t).setAttribute('name', 'D'),
                                  e.querySelectorAll('[name=d]').length &&
                                      h.push('name' + F + '*[*^$|!~]?='),
                                  e.querySelectorAll(':enabled').length ||
                                      h.push(':enabled', ':disabled'),
                                  e.querySelectorAll('*,:x'),
                                  h.push(',.*:');
                          })),
                      (n.matchesSelector = G.test(
                          (g =
                              f.webkitMatchesSelector ||
                              f.mozMatchesSelector ||
                              f.oMatchesSelector ||
                              f.msMatchesSelector),
                      )) &&
                          ae(function (e) {
                              (n.disconnectedMatch = g.call(e, 'div')),
                                  g.call(e, "[s!='']:x"),
                                  m.push('!=', R);
                          }),
                      (h = h.length && new RegExp(h.join('|'))),
                      (m = m.length && new RegExp(m.join('|'))),
                      (t = G.test(f.compareDocumentPosition)),
                      (v =
                          t || G.test(f.contains)
                              ? function (e, t) {
                                    var n = 9 === e.nodeType ? e.documentElement : e,
                                        i = t && t.parentNode;
                                    return (
                                        e === i ||
                                        !(
                                            !i ||
                                            1 !== i.nodeType ||
                                            !(n.contains
                                                ? n.contains(i)
                                                : e.compareDocumentPosition &&
                                                  16 & e.compareDocumentPosition(i))
                                        )
                                    );
                                }
                              : function (e, t) {
                                    if (t) while ((t = t.parentNode)) if (t === e) return !0;
                                    return !1;
                                }),
                      (E = t
                          ? function (e, t) {
                                if (e === t) return (u = !0), 0;
                                var i = !e.compareDocumentPosition - !t.compareDocumentPosition;
                                return (
                                    i ||
                                    ((i =
                                        (e.ownerDocument || e) === (t.ownerDocument || t)
                                            ? e.compareDocumentPosition(t)
                                            : 1),
                                    1 & i || (!n.sortDetached && t.compareDocumentPosition(e) === i)
                                        ? e === r || (e.ownerDocument === b && v(b, e))
                                            ? -1
                                            : t === r || (t.ownerDocument === b && v(b, t))
                                            ? 1
                                            : l
                                            ? _.call(l, e) - _.call(l, t)
                                            : 0
                                        : 4 & i
                                        ? -1
                                        : 1)
                                );
                            }
                          : function (e, t) {
                                if (e === t) return (u = !0), 0;
                                var n,
                                    i = 0,
                                    o = e.parentNode,
                                    a = t.parentNode,
                                    s = [e],
                                    c = [t];
                                if (!o || !a)
                                    return e === r
                                        ? -1
                                        : t === r
                                        ? 1
                                        : o
                                        ? -1
                                        : a
                                        ? 1
                                        : l
                                        ? _.call(l, e) - _.call(l, t)
                                        : 0;
                                if (o === a) return le(e, t);
                                n = e;
                                while ((n = n.parentNode)) s.unshift(n);
                                n = t;
                                while ((n = n.parentNode)) c.unshift(n);
                                while (s[i] === c[i]) i++;
                                return i ? le(s[i], c[i]) : s[i] === b ? -1 : c[i] === b ? 1 : 0;
                            }),
                      r)
                    : d;
            }),
        (ie.matches = function (e, t) {
            return ie(e, null, null, t);
        }),
        (ie.matchesSelector = function (e, t) {
            if (
                ((e.ownerDocument || e) !== d && c(e),
                (t = t.replace(I, "='$1']")),
                !(!n.matchesSelector || !p || (m && m.test(t)) || (h && h.test(t))))
            )
                try {
                    var i = g.call(e, t);
                    if (i || n.disconnectedMatch || (e.document && 11 !== e.document.nodeType))
                        return i;
                } catch (r) {}
            return ie(t, d, null, [e]).length > 0;
        }),
        (ie.contains = function (e, t) {
            return (e.ownerDocument || e) !== d && c(e), v(e, t);
        }),
        (ie.attr = function (e, t) {
            (e.ownerDocument || e) !== d && c(e);
            var r = i.attrHandle[t.toLowerCase()],
                o = r && A.call(i.attrHandle, t.toLowerCase()) ? r(e, t, !p) : void 0;
            return void 0 !== o
                ? o
                : n.attributes || !p
                ? e.getAttribute(t)
                : (o = e.getAttributeNode(t)) && o.specified
                ? o.value
                : null;
        }),
        (ie.error = function (e) {
            throw new Error('Syntax error, unrecognized expression: ' + e);
        }),
        (ie.uniqueSort = function (e) {
            var t,
                i = [],
                r = 0,
                o = 0;
            if (((u = !n.detectDuplicates), (l = !n.sortStable && e.slice(0)), e.sort(E), u)) {
                while ((t = e[o++])) t === e[o] && (r = i.push(o));
                while (r--) e.splice(i[r], 1);
            }
            return (l = null), e;
        }),
        (r = ie.getText =
            function (e) {
                var t,
                    n = '',
                    i = 0,
                    o = e.nodeType;
                if (o) {
                    if (1 === o || 9 === o || 11 === o) {
                        if ('string' == typeof e.textContent) return e.textContent;
                        for (e = e.firstChild; e; e = e.nextSibling) n += r(e);
                    } else if (3 === o || 4 === o) return e.nodeValue;
                } else while ((t = e[i++])) n += r(t);
                return n;
            }),
        (i = ie.selectors =
            {
                cacheLength: 50,
                createPseudo: oe,
                match: V,
                attrHandle: {},
                find: {},
                relative: {
                    '>': { dir: 'parentNode', first: !0 },
                    ' ': { dir: 'parentNode' },
                    '+': { dir: 'previousSibling', first: !0 },
                    '~': { dir: 'previousSibling' },
                },
                preFilter: {
                    ATTR: function (e) {
                        return (
                            (e[1] = e[1].replace(ee, te)),
                            (e[3] = (e[4] || e[5] || '').replace(ee, te)),
                            '~=' === e[2] && (e[3] = ' ' + e[3] + ' '),
                            e.slice(0, 4)
                        );
                    },
                    CHILD: function (e) {
                        return (
                            (e[1] = e[1].toLowerCase()),
                            'nth' === e[1].slice(0, 3)
                                ? (e[3] || ie.error(e[0]),
                                  (e[4] = +(e[4]
                                      ? e[5] + (e[6] || 1)
                                      : 2 * ('even' === e[3] || 'odd' === e[3]))),
                                  (e[5] = +(e[7] + e[8] || 'odd' === e[3])))
                                : e[3] && ie.error(e[0]),
                            e
                        );
                    },
                    PSEUDO: function (e) {
                        var t,
                            n = !e[5] && e[2];
                        return V.CHILD.test(e[0])
                            ? null
                            : (e[3] && void 0 !== e[4]
                                  ? (e[2] = e[4])
                                  : n &&
                                    X.test(n) &&
                                    (t = he(n, !0)) &&
                                    (t = n.indexOf(')', n.length - t) - n.length) &&
                                    ((e[0] = e[0].slice(0, t)), (e[2] = n.slice(0, t))),
                              e.slice(0, 3));
                    },
                },
                filter: {
                    TAG: function (e) {
                        var t = e.replace(ee, te).toLowerCase();
                        return '*' === e
                            ? function () {
                                  return !0;
                              }
                            : function (e) {
                                  return e.nodeName && e.nodeName.toLowerCase() === t;
                              };
                    },
                    CLASS: function (e) {
                        var t = T[e + ' '];
                        return (
                            t ||
                            ((t = new RegExp('(^|' + F + ')' + e + '(' + F + '|$)')) &&
                                T(e, function (e) {
                                    return t.test(
                                        ('string' == typeof e.className && e.className) ||
                                            (typeof e.getAttribute !== k &&
                                                e.getAttribute('class')) ||
                                            '',
                                    );
                                }))
                        );
                    },
                    ATTR: function (e, t, n) {
                        return function (i) {
                            var r = ie.attr(i, e);
                            return null == r
                                ? '!=' === t
                                : !t ||
                                      ((r += ''),
                                      '=' === t
                                          ? r === n
                                          : '!=' === t
                                          ? r !== n
                                          : '^=' === t
                                          ? n && 0 === r.indexOf(n)
                                          : '*=' === t
                                          ? n && r.indexOf(n) > -1
                                          : '$=' === t
                                          ? n && r.slice(-n.length) === n
                                          : '~=' === t
                                          ? (' ' + r + ' ').indexOf(n) > -1
                                          : '|=' === t &&
                                            (r === n || r.slice(0, n.length + 1) === n + '-'));
                        };
                    },
                    CHILD: function (e, t, n, i, r) {
                        var o = 'nth' !== e.slice(0, 3),
                            a = 'last' !== e.slice(-4),
                            s = 'of-type' === t;
                        return 1 === i && 0 === r
                            ? function (e) {
                                  return !!e.parentNode;
                              }
                            : function (t, n, l) {
                                  var u,
                                      c,
                                      d,
                                      f,
                                      p,
                                      h,
                                      m = o !== a ? 'nextSibling' : 'previousSibling',
                                      g = t.parentNode,
                                      v = s && t.nodeName.toLowerCase(),
                                      b = !l && !s;
                                  if (g) {
                                      if (o) {
                                          while (m) {
                                              d = t;
                                              while ((d = d[m]))
                                                  if (
                                                      s
                                                          ? d.nodeName.toLowerCase() === v
                                                          : 1 === d.nodeType
                                                  )
                                                      return !1;
                                              h = m = 'only' === e && !h && 'nextSibling';
                                          }
                                          return !0;
                                      }
                                      if (((h = [a ? g.firstChild : g.lastChild]), a && b)) {
                                          (c = g[y] || (g[y] = {})),
                                              (u = c[e] || []),
                                              (p = u[0] === x && u[1]),
                                              (f = u[0] === x && u[2]),
                                              (d = p && g.childNodes[p]);
                                          while ((d = (++p && d && d[m]) || (f = p = 0) || h.pop()))
                                              if (1 === d.nodeType && ++f && d === t) {
                                                  c[e] = [x, p, f];
                                                  break;
                                              }
                                      } else if (b && (u = (t[y] || (t[y] = {}))[e]) && u[0] === x)
                                          f = u[1];
                                      else
                                          while ((d = (++p && d && d[m]) || (f = p = 0) || h.pop()))
                                              if (
                                                  (s
                                                      ? d.nodeName.toLowerCase() === v
                                                      : 1 === d.nodeType) &&
                                                  ++f &&
                                                  (b && ((d[y] || (d[y] = {}))[e] = [x, f]),
                                                  d === t)
                                              )
                                                  break;
                                      return (f -= r), f === i || (f % i === 0 && f / i >= 0);
                                  }
                              };
                    },
                    PSEUDO: function (e, t) {
                        var n,
                            r =
                                i.pseudos[e] ||
                                i.setFilters[e.toLowerCase()] ||
                                ie.error('unsupported pseudo: ' + e);
                        return r[y]
                            ? r(t)
                            : r.length > 1
                            ? ((n = [e, e, '', t]),
                              i.setFilters.hasOwnProperty(e.toLowerCase())
                                  ? oe(function (e, n) {
                                        var i,
                                            o = r(e, t),
                                            a = o.length;
                                        while (a--) (i = _.call(e, o[a])), (e[i] = !(n[i] = o[a]));
                                    })
                                  : function (e) {
                                        return r(e, 0, n);
                                    })
                            : r;
                    },
                },
                pseudos: {
                    not: oe(function (e) {
                        var t = [],
                            n = [],
                            i = a(e.replace(W, '$1'));
                        return i[y]
                            ? oe(function (e, t, n, r) {
                                  var o,
                                      a = i(e, null, r, []),
                                      s = e.length;
                                  while (s--) (o = a[s]) && (e[s] = !(t[s] = o));
                              })
                            : function (e, r, o) {
                                  return (t[0] = e), i(t, null, o, n), !n.pop();
                              };
                    }),
                    has: oe(function (e) {
                        return function (t) {
                            return ie(e, t).length > 0;
                        };
                    }),
                    contains: oe(function (e) {
                        return function (t) {
                            return (t.textContent || t.innerText || r(t)).indexOf(e) > -1;
                        };
                    }),
                    lang: oe(function (e) {
                        return (
                            U.test(e || '') || ie.error('unsupported lang: ' + e),
                            (e = e.replace(ee, te).toLowerCase()),
                            function (t) {
                                var n;
                                do {
                                    if (
                                        (n = p
                                            ? t.lang
                                            : t.getAttribute('xml:lang') || t.getAttribute('lang'))
                                    )
                                        return (
                                            (n = n.toLowerCase()),
                                            n === e || 0 === n.indexOf(e + '-')
                                        );
                                } while ((t = t.parentNode) && 1 === t.nodeType);
                                return !1;
                            }
                        );
                    }),
                    target: function (t) {
                        var n = e.location && e.location.hash;
                        return n && n.slice(1) === t.id;
                    },
                    root: function (e) {
                        return e === f;
                    },
                    focus: function (e) {
                        return (
                            e === d.activeElement &&
                            (!d.hasFocus || d.hasFocus()) &&
                            !!(e.type || e.href || ~e.tabIndex)
                        );
                    },
                    enabled: function (e) {
                        return !1 === e.disabled;
                    },
                    disabled: function (e) {
                        return !0 === e.disabled;
                    },
                    checked: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return ('input' === t && !!e.checked) || ('option' === t && !!e.selected);
                    },
                    selected: function (e) {
                        return e.parentNode && e.parentNode.selectedIndex, !0 === e.selected;
                    },
                    empty: function (e) {
                        for (e = e.firstChild; e; e = e.nextSibling) if (e.nodeType < 6) return !1;
                        return !0;
                    },
                    parent: function (e) {
                        return !i.pseudos.empty(e);
                    },
                    header: function (e) {
                        return Y.test(e.nodeName);
                    },
                    input: function (e) {
                        return J.test(e.nodeName);
                    },
                    button: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return ('input' === t && 'button' === e.type) || 'button' === t;
                    },
                    text: function (e) {
                        var t;
                        return (
                            'input' === e.nodeName.toLowerCase() &&
                            'text' === e.type &&
                            (null == (t = e.getAttribute('type')) || 'text' === t.toLowerCase())
                        );
                    },
                    first: de(function () {
                        return [0];
                    }),
                    last: de(function (e, t) {
                        return [t - 1];
                    }),
                    eq: de(function (e, t, n) {
                        return [0 > n ? n + t : n];
                    }),
                    even: de(function (e, t) {
                        for (var n = 0; t > n; n += 2) e.push(n);
                        return e;
                    }),
                    odd: de(function (e, t) {
                        for (var n = 1; t > n; n += 2) e.push(n);
                        return e;
                    }),
                    lt: de(function (e, t, n) {
                        for (var i = 0 > n ? n + t : n; --i >= 0; ) e.push(i);
                        return e;
                    }),
                    gt: de(function (e, t, n) {
                        for (var i = 0 > n ? n + t : n; ++i < t; ) e.push(i);
                        return e;
                    }),
                },
            }),
        (i.pseudos.nth = i.pseudos.eq),
        { radio: !0, checkbox: !0, file: !0, password: !0, image: !0 }))
            i.pseudos[t] = ue(t);
        for (t in { submit: !0, reset: !0 }) i.pseudos[t] = ce(t);
        function pe() {}
        function he(e, t) {
            var n,
                r,
                o,
                a,
                s,
                l,
                u,
                c = C[e + ' '];
            if (c) return t ? 0 : c.slice(0);
            (s = e), (l = []), (u = i.preFilter);
            while (s) {
                for (a in ((!n || (r = $.exec(s))) &&
                    (r && (s = s.slice(r[0].length) || s), l.push((o = []))),
                (n = !1),
                (r = z.exec(s)) &&
                    ((n = r.shift()),
                    o.push({ value: n, type: r[0].replace(W, ' ') }),
                    (s = s.slice(n.length))),
                i.filter))
                    !(r = V[a].exec(s)) ||
                        (u[a] && !(r = u[a](r))) ||
                        ((n = r.shift()),
                        o.push({ value: n, type: a, matches: r }),
                        (s = s.slice(n.length)));
                if (!n) break;
            }
            return t ? s.length : s ? ie.error(e) : C(e, l).slice(0);
        }
        function me(e) {
            for (var t = 0, n = e.length, i = ''; n > t; t++) i += e[t].value;
            return i;
        }
        function ge(e, t, n) {
            var i = t.dir,
                r = n && 'parentNode' === i,
                o = w++;
            return t.first
                ? function (t, n, o) {
                      while ((t = t[i])) if (1 === t.nodeType || r) return e(t, n, o);
                  }
                : function (t, n, a) {
                      var s,
                          l,
                          u = [x, o];
                      if (a) {
                          while ((t = t[i])) if ((1 === t.nodeType || r) && e(t, n, a)) return !0;
                      } else
                          while ((t = t[i]))
                              if (1 === t.nodeType || r) {
                                  if (
                                      ((l = t[y] || (t[y] = {})),
                                      (s = l[i]) && s[0] === x && s[1] === o)
                                  )
                                      return (u[2] = s[2]);
                                  if (((l[i] = u), (u[2] = e(t, n, a)))) return !0;
                              }
                  };
        }
        function ve(e) {
            return e.length > 1
                ? function (t, n, i) {
                      var r = e.length;
                      while (r--) if (!e[r](t, n, i)) return !1;
                      return !0;
                  }
                : e[0];
        }
        function ye(e, t, n, i, r) {
            for (var o, a = [], s = 0, l = e.length, u = null != t; l > s; s++)
                (o = e[s]) && (!n || n(o, i, r)) && (a.push(o), u && t.push(s));
            return a;
        }
        function be(e, t, n, i, r, o) {
            return (
                i && !i[y] && (i = be(i)),
                r && !r[y] && (r = be(r, o)),
                oe(function (o, a, s, l) {
                    var u,
                        c,
                        d,
                        f = [],
                        p = [],
                        h = a.length,
                        m = o || Te(t || '*', s.nodeType ? [s] : s, []),
                        g = !e || (!o && t) ? m : ye(m, f, e, s, l),
                        v = n ? (r || (o ? e : h || i) ? [] : a) : g;
                    if ((n && n(g, v, s, l), i)) {
                        (u = ye(v, p)), i(u, [], s, l), (c = u.length);
                        while (c--) (d = u[c]) && (v[p[c]] = !(g[p[c]] = d));
                    }
                    if (o) {
                        if (r || e) {
                            if (r) {
                                (u = []), (c = v.length);
                                while (c--) (d = v[c]) && u.push((g[c] = d));
                                r(null, (v = []), u, l);
                            }
                            c = v.length;
                            while (c--)
                                (d = v[c]) &&
                                    (u = r ? _.call(o, d) : f[c]) > -1 &&
                                    (o[u] = !(a[u] = d));
                        }
                    } else (v = ye(v === a ? v.splice(h, v.length) : v)), r ? r(null, a, v, l) : H.apply(a, v);
                })
            );
        }
        function xe(e) {
            for (
                var t,
                    n,
                    r,
                    o = e.length,
                    a = i.relative[e[0].type],
                    l = a || i.relative[' '],
                    u = a ? 1 : 0,
                    c = ge(
                        function (e) {
                            return e === t;
                        },
                        l,
                        !0,
                    ),
                    d = ge(
                        function (e) {
                            return _.call(t, e) > -1;
                        },
                        l,
                        !0,
                    ),
                    f = [
                        function (e, n, i) {
                            return (
                                (!a && (i || n !== s)) ||
                                ((t = n).nodeType ? c(e, n, i) : d(e, n, i))
                            );
                        },
                    ];
                o > u;
                u++
            )
                if ((n = i.relative[e[u].type])) f = [ge(ve(f), n)];
                else {
                    if (((n = i.filter[e[u].type].apply(null, e[u].matches)), n[y])) {
                        for (r = ++u; o > r; r++) if (i.relative[e[r].type]) break;
                        return be(
                            u > 1 && ve(f),
                            u > 1 &&
                                me(
                                    e
                                        .slice(0, u - 1)
                                        .concat({ value: ' ' === e[u - 2].type ? '*' : '' }),
                                ).replace(W, '$1'),
                            n,
                            r > u && xe(e.slice(u, r)),
                            o > r && xe((e = e.slice(r))),
                            o > r && me(e),
                        );
                    }
                    f.push(n);
                }
            return ve(f);
        }
        function we(e, t) {
            var n = t.length > 0,
                r = e.length > 0,
                o = function (o, a, l, u, c) {
                    var f,
                        p,
                        h,
                        m = 0,
                        g = '0',
                        v = o && [],
                        y = [],
                        b = s,
                        w = o || (r && i.find.TAG('*', c)),
                        T = (x += null == b ? 1 : Math.random() || 0.1),
                        C = w.length;
                    for (c && (s = a !== d && a); g !== C && null != (f = w[g]); g++) {
                        if (r && f) {
                            p = 0;
                            while ((h = e[p++]))
                                if (h(f, a, l)) {
                                    u.push(f);
                                    break;
                                }
                            c && (x = T);
                        }
                        n && ((f = !h && f) && m--, o && v.push(f));
                    }
                    if (((m += g), n && g !== m)) {
                        p = 0;
                        while ((h = t[p++])) h(v, y, a, l);
                        if (o) {
                            if (m > 0) while (g--) v[g] || y[g] || (y[g] = j.call(u));
                            y = ye(y);
                        }
                        H.apply(u, y),
                            c && !o && y.length > 0 && m + t.length > 1 && ie.uniqueSort(u);
                    }
                    return c && ((x = T), (s = b)), v;
                };
            return n ? oe(o) : o;
        }
        function Te(e, t, n) {
            for (var i = 0, r = t.length; r > i; i++) ie(e, t[i], n);
            return n;
        }
        function Ce(e, t, r, o) {
            var s,
                l,
                u,
                c,
                d,
                f = he(e);
            if (!o && 1 === f.length) {
                if (
                    ((l = f[0] = f[0].slice(0)),
                    l.length > 2 &&
                        'ID' === (u = l[0]).type &&
                        n.getById &&
                        9 === t.nodeType &&
                        p &&
                        i.relative[l[1].type])
                ) {
                    if (((t = (i.find.ID(u.matches[0].replace(ee, te), t) || [])[0]), !t)) return r;
                    e = e.slice(l.shift().value.length);
                }
                s = V.needsContext.test(e) ? 0 : l.length;
                while (s--) {
                    if (((u = l[s]), i.relative[(c = u.type)])) break;
                    if (
                        (d = i.find[c]) &&
                        (o = d(
                            u.matches[0].replace(ee, te),
                            (K.test(l[0].type) && fe(t.parentNode)) || t,
                        ))
                    ) {
                        if ((l.splice(s, 1), (e = o.length && me(l)), !e)) return H.apply(r, o), r;
                        break;
                    }
                }
            }
            return a(e, f)(o, t, !p, r, (K.test(e) && fe(t.parentNode)) || t), r;
        }
        return (
            (pe.prototype = i.filters = i.pseudos),
            (i.setFilters = new pe()),
            (a = ie.compile =
                function (e, t) {
                    var n,
                        i = [],
                        r = [],
                        o = N[e + ' '];
                    if (!o) {
                        t || (t = he(e)), (n = t.length);
                        while (n--) (o = xe(t[n])), o[y] ? i.push(o) : r.push(o);
                        o = N(e, we(r, i));
                    }
                    return o;
                }),
            (n.sortStable = y.split('').sort(E).join('') === y),
            (n.detectDuplicates = !!u),
            c(),
            (n.sortDetached = ae(function (e) {
                return 1 & e.compareDocumentPosition(d.createElement('div'));
            })),
            ae(function (e) {
                return (
                    (e.innerHTML = "<a href='#'></a>"), '#' === e.firstChild.getAttribute('href')
                );
            }) ||
                se('type|href|height|width', function (e, t, n) {
                    return n ? void 0 : e.getAttribute(t, 'type' === t.toLowerCase() ? 1 : 2);
                }),
            (n.attributes &&
                ae(function (e) {
                    return (
                        (e.innerHTML = '<input/>'),
                        e.firstChild.setAttribute('value', ''),
                        '' === e.firstChild.getAttribute('value')
                    );
                })) ||
                se('value', function (e, t, n) {
                    return n || 'input' !== e.nodeName.toLowerCase() ? void 0 : e.defaultValue;
                }),
            ae(function (e) {
                return null == e.getAttribute('disabled');
            }) ||
                se(M, function (e, t, n) {
                    var i;
                    return n
                        ? void 0
                        : !0 === e[t]
                        ? t.toLowerCase()
                        : (i = e.getAttributeNode(t)) && i.specified
                        ? i.value
                        : null;
                }),
            ie
        );
    })(e);
    (p.find = b),
        (p.expr = b.selectors),
        (p.expr[':'] = p.expr.pseudos),
        (p.unique = b.uniqueSort),
        (p.text = b.getText),
        (p.isXMLDoc = b.isXML),
        (p.contains = b.contains);
    var x = p.expr.match.needsContext,
        w = /^<(\w+)\s*\/?>(?:<\/\1>|)$/,
        T = /^.[^:#\[\.,]*$/;
    function C(e, t, n) {
        if (p.isFunction(t))
            return p.grep(e, function (e, i) {
                return !!t.call(e, i, e) !== n;
            });
        if (t.nodeType)
            return p.grep(e, function (e) {
                return (e === t) !== n;
            });
        if ('string' == typeof t) {
            if (T.test(t)) return p.filter(t, e, n);
            t = p.filter(t, e);
        }
        return p.grep(e, function (e) {
            return p.inArray(e, t) >= 0 !== n;
        });
    }
    (p.filter = function (e, t, n) {
        var i = t[0];
        return (
            n && (e = ':not(' + e + ')'),
            1 === t.length && 1 === i.nodeType
                ? p.find.matchesSelector(i, e)
                    ? [i]
                    : []
                : p.find.matches(
                      e,
                      p.grep(t, function (e) {
                          return 1 === e.nodeType;
                      }),
                  )
        );
    }),
        p.fn.extend({
            find: function (e) {
                var t,
                    n = [],
                    i = this,
                    r = i.length;
                if ('string' != typeof e)
                    return this.pushStack(
                        p(e).filter(function () {
                            for (t = 0; r > t; t++) if (p.contains(i[t], this)) return !0;
                        }),
                    );
                for (t = 0; r > t; t++) p.find(e, i[t], n);
                return (
                    (n = this.pushStack(r > 1 ? p.unique(n) : n)),
                    (n.selector = this.selector ? this.selector + ' ' + e : e),
                    n
                );
            },
            filter: function (e) {
                return this.pushStack(C(this, e || [], !1));
            },
            not: function (e) {
                return this.pushStack(C(this, e || [], !0));
            },
            is: function (e) {
                return !!C(this, 'string' == typeof e && x.test(e) ? p(e) : e || [], !1).length;
            },
        });
    var N,
        E = e.document,
        k = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,
        S = (p.fn.init = function (e, t) {
            var n, i;
            if (!e) return this;
            if ('string' == typeof e) {
                if (
                    ((n =
                        '<' === e.charAt(0) && '>' === e.charAt(e.length - 1) && e.length >= 3
                            ? [null, e, null]
                            : k.exec(e)),
                    !n || (!n[1] && t))
                )
                    return !t || t.jquery ? (t || N).find(e) : this.constructor(t).find(e);
                if (n[1]) {
                    if (
                        ((t = t instanceof p ? t[0] : t),
                        p.merge(
                            this,
                            p.parseHTML(n[1], t && t.nodeType ? t.ownerDocument || t : E, !0),
                        ),
                        w.test(n[1]) && p.isPlainObject(t))
                    )
                        for (n in t) p.isFunction(this[n]) ? this[n](t[n]) : this.attr(n, t[n]);
                    return this;
                }
                if (((i = E.getElementById(n[2])), i && i.parentNode)) {
                    if (i.id !== n[2]) return N.find(e);
                    (this.length = 1), (this[0] = i);
                }
                return (this.context = E), (this.selector = e), this;
            }
            return e.nodeType
                ? ((this.context = this[0] = e), (this.length = 1), this)
                : p.isFunction(e)
                ? 'undefined' != typeof N.ready
                    ? N.ready(e)
                    : e(p)
                : (void 0 !== e.selector &&
                      ((this.selector = e.selector), (this.context = e.context)),
                  p.makeArray(e, this));
        });
    (S.prototype = p.fn), (N = p(E));
    var A = /^(?:parents|prev(?:Until|All))/,
        D = { children: !0, contents: !0, next: !0, prev: !0 };
    function j(e, t) {
        do {
            e = e[t];
        } while (e && 1 !== e.nodeType);
        return e;
    }
    p.extend({
        dir: function (e, t, n) {
            var i = [],
                r = e[t];
            while (r && 9 !== r.nodeType && (void 0 === n || 1 !== r.nodeType || !p(r).is(n)))
                1 === r.nodeType && i.push(r), (r = r[t]);
            return i;
        },
        sibling: function (e, t) {
            for (var n = []; e; e = e.nextSibling) 1 === e.nodeType && e !== t && n.push(e);
            return n;
        },
    }),
        p.fn.extend({
            has: function (e) {
                var t,
                    n = p(e, this),
                    i = n.length;
                return this.filter(function () {
                    for (t = 0; i > t; t++) if (p.contains(this, n[t])) return !0;
                });
            },
            closest: function (e, t) {
                for (
                    var n,
                        i = 0,
                        r = this.length,
                        o = [],
                        a = x.test(e) || 'string' != typeof e ? p(e, t || this.context) : 0;
                    r > i;
                    i++
                )
                    for (n = this[i]; n && n !== t; n = n.parentNode)
                        if (
                            n.nodeType < 11 &&
                            (a ? a.index(n) > -1 : 1 === n.nodeType && p.find.matchesSelector(n, e))
                        ) {
                            o.push(n);
                            break;
                        }
                return this.pushStack(o.length > 1 ? p.unique(o) : o);
            },
            index: function (e) {
                return e
                    ? 'string' == typeof e
                        ? p.inArray(this[0], p(e))
                        : p.inArray(e.jquery ? e[0] : e, this)
                    : this[0] && this[0].parentNode
                    ? this.first().prevAll().length
                    : -1;
            },
            add: function (e, t) {
                return this.pushStack(p.unique(p.merge(this.get(), p(e, t))));
            },
            addBack: function (e) {
                return this.add(null == e ? this.prevObject : this.prevObject.filter(e));
            },
        }),
        p.each(
            {
                parent: function (e) {
                    var t = e.parentNode;
                    return t && 11 !== t.nodeType ? t : null;
                },
                parents: function (e) {
                    return p.dir(e, 'parentNode');
                },
                parentsUntil: function (e, t, n) {
                    return p.dir(e, 'parentNode', n);
                },
                next: function (e) {
                    return j(e, 'nextSibling');
                },
                prev: function (e) {
                    return j(e, 'previousSibling');
                },
                nextAll: function (e) {
                    return p.dir(e, 'nextSibling');
                },
                prevAll: function (e) {
                    return p.dir(e, 'previousSibling');
                },
                nextUntil: function (e, t, n) {
                    return p.dir(e, 'nextSibling', n);
                },
                prevUntil: function (e, t, n) {
                    return p.dir(e, 'previousSibling', n);
                },
                siblings: function (e) {
                    return p.sibling((e.parentNode || {}).firstChild, e);
                },
                children: function (e) {
                    return p.sibling(e.firstChild);
                },
                contents: function (e) {
                    return p.nodeName(e, 'iframe')
                        ? e.contentDocument || e.contentWindow.document
                        : p.merge([], e.childNodes);
                },
            },
            function (e, t) {
                p.fn[e] = function (n, i) {
                    var r = p.map(this, t, n);
                    return (
                        'Until' !== e.slice(-5) && (i = n),
                        i && 'string' == typeof i && (r = p.filter(i, r)),
                        this.length > 1 &&
                            (D[e] || (r = p.unique(r)), A.test(e) && (r = r.reverse())),
                        this.pushStack(r)
                    );
                };
            },
        );
    var L,
        H = /\S+/g,
        q = {};
    function _(e) {
        var t = (q[e] = {});
        return (
            p.each(e.match(H) || [], function (e, n) {
                t[n] = !0;
            }),
            t
        );
    }
    function M() {
        E.addEventListener
            ? (E.removeEventListener('DOMContentLoaded', F, !1),
              e.removeEventListener('load', F, !1))
            : (E.detachEvent('onreadystatechange', F), e.detachEvent('onload', F));
    }
    function F() {
        (E.addEventListener || 'load' === event.type || 'complete' === E.readyState) &&
            (M(), p.ready());
    }
    (p.Callbacks = function (e) {
        e = 'string' == typeof e ? q[e] || _(e) : p.extend({}, e);
        var t,
            n,
            i,
            r,
            o,
            a,
            s = [],
            l = !e.once && [],
            u = function (d) {
                for (
                    n = e.memory && d, i = !0, o = a || 0, a = 0, r = s.length, t = !0;
                    s && r > o;
                    o++
                )
                    if (!1 === s[o].apply(d[0], d[1]) && e.stopOnFalse) {
                        n = !1;
                        break;
                    }
                (t = !1), s && (l ? l.length && u(l.shift()) : n ? (s = []) : c.disable());
            },
            c = {
                add: function () {
                    if (s) {
                        var i = s.length;
                        !(function t(n) {
                            p.each(n, function (n, i) {
                                var r = p.type(i);
                                'function' === r
                                    ? (e.unique && c.has(i)) || s.push(i)
                                    : i && i.length && 'string' !== r && t(i);
                            });
                        })(arguments),
                            t ? (r = s.length) : n && ((a = i), u(n));
                    }
                    return this;
                },
                remove: function () {
                    return (
                        s &&
                            p.each(arguments, function (e, n) {
                                var i;
                                while ((i = p.inArray(n, s, i)) > -1)
                                    s.splice(i, 1), t && (r >= i && r--, o >= i && o--);
                            }),
                        this
                    );
                },
                has: function (e) {
                    return e ? p.inArray(e, s) > -1 : !(!s || !s.length);
                },
                empty: function () {
                    return (s = []), (r = 0), this;
                },
                disable: function () {
                    return (s = l = n = void 0), this;
                },
                disabled: function () {
                    return !s;
                },
                lock: function () {
                    return (l = void 0), n || c.disable(), this;
                },
                locked: function () {
                    return !l;
                },
                fireWith: function (e, n) {
                    return (
                        !s ||
                            (i && !l) ||
                            ((n = n || []),
                            (n = [e, n.slice ? n.slice() : n]),
                            t ? l.push(n) : u(n)),
                        this
                    );
                },
                fire: function () {
                    return c.fireWith(this, arguments), this;
                },
                fired: function () {
                    return !!i;
                },
            };
        return c;
    }),
        p.extend({
            Deferred: function (e) {
                var t = [
                        ['resolve', 'done', p.Callbacks('once memory'), 'resolved'],
                        ['reject', 'fail', p.Callbacks('once memory'), 'rejected'],
                        ['notify', 'progress', p.Callbacks('memory')],
                    ],
                    n = 'pending',
                    i = {
                        state: function () {
                            return n;
                        },
                        always: function () {
                            return r.done(arguments).fail(arguments), this;
                        },
                        then: function () {
                            var e = arguments;
                            return p
                                .Deferred(function (n) {
                                    p.each(t, function (t, o) {
                                        var a = p.isFunction(e[t]) && e[t];
                                        r[o[1]](function () {
                                            var e = a && a.apply(this, arguments);
                                            e && p.isFunction(e.promise)
                                                ? e
                                                      .promise()
                                                      .done(n.resolve)
                                                      .fail(n.reject)
                                                      .progress(n.notify)
                                                : n[o[0] + 'With'](
                                                      this === i ? n.promise() : this,
                                                      a ? [e] : arguments,
                                                  );
                                        });
                                    }),
                                        (e = null);
                                })
                                .promise();
                        },
                        promise: function (e) {
                            return null != e ? p.extend(e, i) : i;
                        },
                    },
                    r = {};
                return (
                    (i.pipe = i.then),
                    p.each(t, function (e, o) {
                        var a = o[2],
                            s = o[3];
                        (i[o[1]] = a.add),
                            s &&
                                a.add(
                                    function () {
                                        n = s;
                                    },
                                    t[1 ^ e][2].disable,
                                    t[2][2].lock,
                                ),
                            (r[o[0]] = function () {
                                return r[o[0] + 'With'](this === r ? i : this, arguments), this;
                            }),
                            (r[o[0] + 'With'] = a.fireWith);
                    }),
                    i.promise(r),
                    e && e.call(r, r),
                    r
                );
            },
            when: function (e) {
                var t,
                    n,
                    r,
                    o = 0,
                    a = i.call(arguments),
                    s = a.length,
                    l = 1 !== s || (e && p.isFunction(e.promise)) ? s : 0,
                    u = 1 === l ? e : p.Deferred(),
                    c = function (e, n, r) {
                        return function (o) {
                            (n[e] = this),
                                (r[e] = arguments.length > 1 ? i.call(arguments) : o),
                                r === t ? u.notifyWith(n, r) : --l || u.resolveWith(n, r);
                        };
                    };
                if (s > 1)
                    for (t = new Array(s), n = new Array(s), r = new Array(s); s > o; o++)
                        a[o] && p.isFunction(a[o].promise)
                            ? a[o].promise().done(c(o, r, a)).fail(u.reject).progress(c(o, n, t))
                            : --l;
                return l || u.resolveWith(r, a), u.promise();
            },
        }),
        (p.fn.ready = function (e) {
            return p.ready.promise().done(e), this;
        }),
        p.extend({
            isReady: !1,
            readyWait: 1,
            holdReady: function (e) {
                e ? p.readyWait++ : p.ready(!0);
            },
            ready: function (e) {
                if (!0 === e ? !--p.readyWait : !p.isReady) {
                    if (!E.body) return setTimeout(p.ready);
                    (p.isReady = !0),
                        (!0 !== e && --p.readyWait > 0) ||
                            (L.resolveWith(E, [p]),
                            p.fn.trigger && p(E).trigger('ready').off('ready'));
                }
            },
        }),
        (p.ready.promise = function (t) {
            if (!L)
                if (((L = p.Deferred()), 'complete' === E.readyState)) setTimeout(p.ready);
                else if (E.addEventListener)
                    E.addEventListener('DOMContentLoaded', F, !1),
                        e.addEventListener('load', F, !1);
                else {
                    E.attachEvent('onreadystatechange', F), e.attachEvent('onload', F);
                    var n = !1;
                    try {
                        n = null == e.frameElement && E.documentElement;
                    } catch (i) {}
                    n &&
                        n.doScroll &&
                        (function t() {
                            if (!p.isReady) {
                                try {
                                    n.doScroll('left');
                                } catch (e) {
                                    return setTimeout(t, 50);
                                }
                                M(), p.ready();
                            }
                        })();
                }
            return L.promise(t);
        });
    var O,
        B = 'undefined';
    for (O in p(d)) break;
    (d.ownLast = '0' !== O),
        (d.inlineBlockNeedsLayout = !1),
        p(function () {
            var e,
                t,
                n = E.getElementsByTagName('body')[0];
            n &&
                ((e = E.createElement('div')),
                (e.style.cssText =
                    'border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px'),
                (t = E.createElement('div')),
                n.appendChild(e).appendChild(t),
                typeof t.style.zoom !== B &&
                    ((t.style.cssText =
                        'border:0;margin:0;width:1px;padding:1px;display:inline;zoom:1'),
                    (d.inlineBlockNeedsLayout = 3 === t.offsetWidth) && (n.style.zoom = 1)),
                n.removeChild(e),
                (e = t = null));
        }),
        (function () {
            var e = E.createElement('div');
            if (null == d.deleteExpando) {
                d.deleteExpando = !0;
                try {
                    delete e.test;
                } catch (t) {
                    d.deleteExpando = !1;
                }
            }
            e = null;
        })(),
        (p.acceptData = function (e) {
            var t = p.noData[(e.nodeName + ' ').toLowerCase()],
                n = +e.nodeType || 1;
            return (1 === n || 9 === n) && (!t || (!0 !== t && e.getAttribute('classid') === t));
        });
    var P = /^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,
        R = /([A-Z])/g;
    function W(e, t, n) {
        if (void 0 === n && 1 === e.nodeType) {
            var i = 'data-' + t.replace(R, '-$1').toLowerCase();
            if (((n = e.getAttribute(i)), 'string' == typeof n)) {
                try {
                    n =
                        'true' === n ||
                        ('false' !== n &&
                            ('null' === n
                                ? null
                                : +n + '' === n
                                ? +n
                                : P.test(n)
                                ? p.parseJSON(n)
                                : n));
                } catch (r) {}
                p.data(e, t, n);
            } else n = void 0;
        }
        return n;
    }
    function $(e) {
        var t;
        for (t in e) if (('data' !== t || !p.isEmptyObject(e[t])) && 'toJSON' !== t) return !1;
        return !0;
    }
    function z(e, t, i, r) {
        if (p.acceptData(e)) {
            var o,
                a,
                s = p.expando,
                l = e.nodeType,
                u = l ? p.cache : e,
                c = l ? e[s] : e[s] && s;
            if ((c && u[c] && (r || u[c].data)) || void 0 !== i || 'string' != typeof t)
                return (
                    c || (c = l ? (e[s] = n.pop() || p.guid++) : s),
                    u[c] || (u[c] = l ? {} : { toJSON: p.noop }),
                    ('object' == typeof t || 'function' == typeof t) &&
                        (r ? (u[c] = p.extend(u[c], t)) : (u[c].data = p.extend(u[c].data, t))),
                    (a = u[c]),
                    r || (a.data || (a.data = {}), (a = a.data)),
                    void 0 !== i && (a[p.camelCase(t)] = i),
                    'string' == typeof t
                        ? ((o = a[t]), null == o && (o = a[p.camelCase(t)]))
                        : (o = a),
                    o
                );
        }
    }
    function I(e, t, n) {
        if (p.acceptData(e)) {
            var i,
                r,
                o = e.nodeType,
                a = o ? p.cache : e,
                s = o ? e[p.expando] : p.expando;
            if (a[s]) {
                if (t && (i = n ? a[s] : a[s].data)) {
                    p.isArray(t)
                        ? (t = t.concat(p.map(t, p.camelCase)))
                        : t in i
                        ? (t = [t])
                        : ((t = p.camelCase(t)), (t = t in i ? [t] : t.split(' '))),
                        (r = t.length);
                    while (r--) delete i[t[r]];
                    if (n ? !$(i) : !p.isEmptyObject(i)) return;
                }
                (n || (delete a[s].data, $(a[s]))) &&
                    (o
                        ? p.cleanData([e], !0)
                        : d.deleteExpando || a != a.window
                        ? delete a[s]
                        : (a[s] = null));
            }
        }
    }
    p.extend({
        cache: {},
        noData: {
            'applet ': !0,
            'embed ': !0,
            'object ': 'clsid:D27CDB6E-AE6D-11cf-96B8-444553540000',
        },
        hasData: function (e) {
            return (e = e.nodeType ? p.cache[e[p.expando]] : e[p.expando]), !!e && !$(e);
        },
        data: function (e, t, n) {
            return z(e, t, n);
        },
        removeData: function (e, t) {
            return I(e, t);
        },
        _data: function (e, t, n) {
            return z(e, t, n, !0);
        },
        _removeData: function (e, t) {
            return I(e, t, !0);
        },
    }),
        p.fn.extend({
            data: function (e, t) {
                var n,
                    i,
                    r,
                    o = this[0],
                    a = o && o.attributes;
                if (void 0 === e) {
                    if (
                        this.length &&
                        ((r = p.data(o)), 1 === o.nodeType && !p._data(o, 'parsedAttrs'))
                    ) {
                        n = a.length;
                        while (n--)
                            (i = a[n].name),
                                0 === i.indexOf('data-') &&
                                    ((i = p.camelCase(i.slice(5))), W(o, i, r[i]));
                        p._data(o, 'parsedAttrs', !0);
                    }
                    return r;
                }
                return 'object' == typeof e
                    ? this.each(function () {
                          p.data(this, e);
                      })
                    : arguments.length > 1
                    ? this.each(function () {
                          p.data(this, e, t);
                      })
                    : o
                    ? W(o, e, p.data(o, e))
                    : void 0;
            },
            removeData: function (e) {
                return this.each(function () {
                    p.removeData(this, e);
                });
            },
        }),
        p.extend({
            queue: function (e, t, n) {
                var i;
                return e
                    ? ((t = (t || 'fx') + 'queue'),
                      (i = p._data(e, t)),
                      n && (!i || p.isArray(n) ? (i = p._data(e, t, p.makeArray(n))) : i.push(n)),
                      i || [])
                    : void 0;
            },
            dequeue: function (e, t) {
                t = t || 'fx';
                var n = p.queue(e, t),
                    i = n.length,
                    r = n.shift(),
                    o = p._queueHooks(e, t),
                    a = function () {
                        p.dequeue(e, t);
                    };
                'inprogress' === r && ((r = n.shift()), i--),
                    r && ('fx' === t && n.unshift('inprogress'), delete o.stop, r.call(e, a, o)),
                    !i && o && o.empty.fire();
            },
            _queueHooks: function (e, t) {
                var n = t + 'queueHooks';
                return (
                    p._data(e, n) ||
                    p._data(e, n, {
                        empty: p.Callbacks('once memory').add(function () {
                            p._removeData(e, t + 'queue'), p._removeData(e, n);
                        }),
                    })
                );
            },
        }),
        p.fn.extend({
            queue: function (e, t) {
                var n = 2;
                return (
                    'string' != typeof e && ((t = e), (e = 'fx'), n--),
                    arguments.length < n
                        ? p.queue(this[0], e)
                        : void 0 === t
                        ? this
                        : this.each(function () {
                              var n = p.queue(this, e, t);
                              p._queueHooks(this, e),
                                  'fx' === e && 'inprogress' !== n[0] && p.dequeue(this, e);
                          })
                );
            },
            dequeue: function (e) {
                return this.each(function () {
                    p.dequeue(this, e);
                });
            },
            clearQueue: function (e) {
                return this.queue(e || 'fx', []);
            },
            promise: function (e, t) {
                var n,
                    i = 1,
                    r = p.Deferred(),
                    o = this,
                    a = this.length,
                    s = function () {
                        --i || r.resolveWith(o, [o]);
                    };
                'string' != typeof e && ((t = e), (e = void 0)), (e = e || 'fx');
                while (a--)
                    (n = p._data(o[a], e + 'queueHooks')), n && n.empty && (i++, n.empty.add(s));
                return s(), r.promise(t);
            },
        });
    var X = /[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,
        U = ['Top', 'Right', 'Bottom', 'Left'],
        V = function (e, t) {
            return (e = t || e), 'none' === p.css(e, 'display') || !p.contains(e.ownerDocument, e);
        },
        J = (p.access = function (e, t, n, i, r, o, a) {
            var s = 0,
                l = e.length,
                u = null == n;
            if ('object' === p.type(n)) for (s in ((r = !0), n)) p.access(e, t, s, n[s], !0, o, a);
            else if (
                void 0 !== i &&
                ((r = !0),
                p.isFunction(i) || (a = !0),
                u &&
                    (a
                        ? (t.call(e, i), (t = null))
                        : ((u = t),
                          (t = function (e, t, n) {
                              return u.call(p(e), n);
                          }))),
                t)
            )
                for (; l > s; s++) t(e[s], n, a ? i : i.call(e[s], s, t(e[s], n)));
            return r ? e : u ? t.call(e) : l ? t(e[0], n) : o;
        }),
        Y = /^(?:checkbox|radio)$/i;
    !(function () {
        var e = E.createDocumentFragment(),
            t = E.createElement('div'),
            n = E.createElement('input');
        if (
            (t.setAttribute('className', 't'),
            (t.innerHTML = "  <link/><table></table><a href='/a'>a</a>"),
            (d.leadingWhitespace = 3 === t.firstChild.nodeType),
            (d.tbody = !t.getElementsByTagName('tbody').length),
            (d.htmlSerialize = !!t.getElementsByTagName('link').length),
            (d.html5Clone = '<:nav></:nav>' !== E.createElement('nav').cloneNode(!0).outerHTML),
            (n.type = 'checkbox'),
            (n.checked = !0),
            e.appendChild(n),
            (d.appendChecked = n.checked),
            (t.innerHTML = '<textarea>x</textarea>'),
            (d.noCloneChecked = !!t.cloneNode(!0).lastChild.defaultValue),
            e.appendChild(t),
            (t.innerHTML = "<input type='radio' checked='checked' name='t'/>"),
            (d.checkClone = t.cloneNode(!0).cloneNode(!0).lastChild.checked),
            (d.noCloneEvent = !0),
            t.attachEvent &&
                (t.attachEvent('onclick', function () {
                    d.noCloneEvent = !1;
                }),
                t.cloneNode(!0).click()),
            null == d.deleteExpando)
        ) {
            d.deleteExpando = !0;
            try {
                delete t.test;
            } catch (i) {
                d.deleteExpando = !1;
            }
        }
        e = t = n = null;
    })(),
        (function () {
            var t,
                n,
                i = E.createElement('div');
            for (t in { submit: !0, change: !0, focusin: !0 })
                (n = 'on' + t),
                    (d[t + 'Bubbles'] = n in e) ||
                        (i.setAttribute(n, 't'),
                        (d[t + 'Bubbles'] = !1 === i.attributes[n].expando));
            i = null;
        })();
    var G = /^(?:input|select|textarea)$/i,
        Q = /^key/,
        K = /^(?:mouse|contextmenu)|click/,
        Z = /^(?:focusinfocus|focusoutblur)$/,
        ee = /^([^.]*)(?:\.(.+)|)$/;
    function te() {
        return !0;
    }
    function ne() {
        return !1;
    }
    function ie() {
        try {
            return E.activeElement;
        } catch (e) {}
    }
    function re(e) {
        var t = oe.split('|'),
            n = e.createDocumentFragment();
        if (n.createElement) while (t.length) n.createElement(t.pop());
        return n;
    }
    (p.event = {
        global: {},
        add: function (e, t, n, i, r) {
            var o,
                a,
                s,
                l,
                u,
                c,
                d,
                f,
                h,
                m,
                g,
                v = p._data(e);
            if (v) {
                n.handler && ((l = n), (n = l.handler), (r = l.selector)),
                    n.guid || (n.guid = p.guid++),
                    (a = v.events) || (a = v.events = {}),
                    (c = v.handle) ||
                        ((c = v.handle =
                            function (e) {
                                return typeof p === B || (e && p.event.triggered === e.type)
                                    ? void 0
                                    : p.event.dispatch.apply(c.elem, arguments);
                            }),
                        (c.elem = e)),
                    (t = (t || '').match(H) || ['']),
                    (s = t.length);
                while (s--)
                    (o = ee.exec(t[s]) || []),
                        (h = g = o[1]),
                        (m = (o[2] || '').split('.').sort()),
                        h &&
                            ((u = p.event.special[h] || {}),
                            (h = (r ? u.delegateType : u.bindType) || h),
                            (u = p.event.special[h] || {}),
                            (d = p.extend(
                                {
                                    type: h,
                                    origType: g,
                                    data: i,
                                    handler: n,
                                    guid: n.guid,
                                    selector: r,
                                    needsContext: r && p.expr.match.needsContext.test(r),
                                    namespace: m.join('.'),
                                },
                                l,
                            )),
                            (f = a[h]) ||
                                ((f = a[h] = []),
                                (f.delegateCount = 0),
                                (u.setup && !1 !== u.setup.call(e, i, m, c)) ||
                                    (e.addEventListener
                                        ? e.addEventListener(h, c, !1)
                                        : e.attachEvent && e.attachEvent('on' + h, c))),
                            u.add &&
                                (u.add.call(e, d), d.handler.guid || (d.handler.guid = n.guid)),
                            r ? f.splice(f.delegateCount++, 0, d) : f.push(d),
                            (p.event.global[h] = !0));
                e = null;
            }
        },
        remove: function (e, t, n, i, r) {
            var o,
                a,
                s,
                l,
                u,
                c,
                d,
                f,
                h,
                m,
                g,
                v = p.hasData(e) && p._data(e);
            if (v && (c = v.events)) {
                (t = (t || '').match(H) || ['']), (u = t.length);
                while (u--)
                    if (
                        ((s = ee.exec(t[u]) || []),
                        (h = g = s[1]),
                        (m = (s[2] || '').split('.').sort()),
                        h)
                    ) {
                        (d = p.event.special[h] || {}),
                            (h = (i ? d.delegateType : d.bindType) || h),
                            (f = c[h] || []),
                            (s =
                                s[2] &&
                                new RegExp('(^|\\.)' + m.join('\\.(?:.*\\.|)') + '(\\.|$)')),
                            (l = o = f.length);
                        while (o--)
                            (a = f[o]),
                                (!r && g !== a.origType) ||
                                    (n && n.guid !== a.guid) ||
                                    (s && !s.test(a.namespace)) ||
                                    (i && i !== a.selector && ('**' !== i || !a.selector)) ||
                                    (f.splice(o, 1),
                                    a.selector && f.delegateCount--,
                                    d.remove && d.remove.call(e, a));
                        l &&
                            !f.length &&
                            ((d.teardown && !1 !== d.teardown.call(e, m, v.handle)) ||
                                p.removeEvent(e, h, v.handle),
                            delete c[h]);
                    } else for (h in c) p.event.remove(e, h + t[u], n, i, !0);
                p.isEmptyObject(c) && (delete v.handle, p._removeData(e, 'events'));
            }
        },
        trigger: function (t, n, i, r) {
            var o,
                a,
                s,
                l,
                c,
                d,
                f,
                h = [i || E],
                m = u.call(t, 'type') ? t.type : t,
                g = u.call(t, 'namespace') ? t.namespace.split('.') : [];
            if (
                ((s = d = i = i || E),
                3 !== i.nodeType &&
                    8 !== i.nodeType &&
                    !Z.test(m + p.event.triggered) &&
                    (m.indexOf('.') >= 0 && ((g = m.split('.')), (m = g.shift()), g.sort()),
                    (a = m.indexOf(':') < 0 && 'on' + m),
                    (t = t[p.expando] ? t : new p.Event(m, 'object' == typeof t && t)),
                    (t.isTrigger = r ? 2 : 3),
                    (t.namespace = g.join('.')),
                    (t.namespace_re = t.namespace
                        ? new RegExp('(^|\\.)' + g.join('\\.(?:.*\\.|)') + '(\\.|$)')
                        : null),
                    (t.result = void 0),
                    t.target || (t.target = i),
                    (n = null == n ? [t] : p.makeArray(n, [t])),
                    (c = p.event.special[m] || {}),
                    r || !c.trigger || !1 !== c.trigger.apply(i, n)))
            ) {
                if (!r && !c.noBubble && !p.isWindow(i)) {
                    for (
                        l = c.delegateType || m, Z.test(l + m) || (s = s.parentNode);
                        s;
                        s = s.parentNode
                    )
                        h.push(s), (d = s);
                    d === (i.ownerDocument || E) && h.push(d.defaultView || d.parentWindow || e);
                }
                f = 0;
                while ((s = h[f++]) && !t.isPropagationStopped())
                    (t.type = f > 1 ? l : c.bindType || m),
                        (o = (p._data(s, 'events') || {})[t.type] && p._data(s, 'handle')),
                        o && o.apply(s, n),
                        (o = a && s[a]),
                        o &&
                            o.apply &&
                            p.acceptData(s) &&
                            ((t.result = o.apply(s, n)), !1 === t.result && t.preventDefault());
                if (
                    ((t.type = m),
                    !r &&
                        !t.isDefaultPrevented() &&
                        (!c._default || !1 === c._default.apply(h.pop(), n)) &&
                        p.acceptData(i) &&
                        a &&
                        i[m] &&
                        !p.isWindow(i))
                ) {
                    (d = i[a]), d && (i[a] = null), (p.event.triggered = m);
                    try {
                        i[m]();
                    } catch (v) {}
                    (p.event.triggered = void 0), d && (i[a] = d);
                }
                return t.result;
            }
        },
        dispatch: function (e) {
            e = p.event.fix(e);
            var t,
                n,
                r,
                o,
                a,
                s = [],
                l = i.call(arguments),
                u = (p._data(this, 'events') || {})[e.type] || [],
                c = p.event.special[e.type] || {};
            if (
                ((l[0] = e),
                (e.delegateTarget = this),
                !c.preDispatch || !1 !== c.preDispatch.call(this, e))
            ) {
                (s = p.event.handlers.call(this, e, u)), (t = 0);
                while ((o = s[t++]) && !e.isPropagationStopped()) {
                    (e.currentTarget = o.elem), (a = 0);
                    while ((r = o.handlers[a++]) && !e.isImmediatePropagationStopped())
                        (!e.namespace_re || e.namespace_re.test(r.namespace)) &&
                            ((e.handleObj = r),
                            (e.data = r.data),
                            (n = ((p.event.special[r.origType] || {}).handle || r.handler).apply(
                                o.elem,
                                l,
                            )),
                            void 0 !== n &&
                                !1 === (e.result = n) &&
                                (e.preventDefault(), e.stopPropagation()));
                }
                return c.postDispatch && c.postDispatch.call(this, e), e.result;
            }
        },
        handlers: function (e, t) {
            var n,
                i,
                r,
                o,
                a = [],
                s = t.delegateCount,
                l = e.target;
            if (s && l.nodeType && (!e.button || 'click' !== e.type))
                for (; l != this; l = l.parentNode || this)
                    if (1 === l.nodeType && (!0 !== l.disabled || 'click' !== e.type)) {
                        for (r = [], o = 0; s > o; o++)
                            (i = t[o]),
                                (n = i.selector + ' '),
                                void 0 === r[n] &&
                                    (r[n] = i.needsContext
                                        ? p(n, this).index(l) >= 0
                                        : p.find(n, this, null, [l]).length),
                                r[n] && r.push(i);
                        r.length && a.push({ elem: l, handlers: r });
                    }
            return s < t.length && a.push({ elem: this, handlers: t.slice(s) }), a;
        },
        fix: function (e) {
            if (e[p.expando]) return e;
            var t,
                n,
                i,
                r = e.type,
                o = e,
                a = this.fixHooks[r];
            a ||
                (this.fixHooks[r] = a =
                    K.test(r) ? this.mouseHooks : Q.test(r) ? this.keyHooks : {}),
                (i = a.props ? this.props.concat(a.props) : this.props),
                (e = new p.Event(o)),
                (t = i.length);
            while (t--) (n = i[t]), (e[n] = o[n]);
            return (
                e.target || (e.target = o.srcElement || E),
                3 === e.target.nodeType && (e.target = e.target.parentNode),
                (e.metaKey = !!e.metaKey),
                a.filter ? a.filter(e, o) : e
            );
        },
        props: 'altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which'.split(
            ' ',
        ),
        fixHooks: {},
        keyHooks: {
            props: 'char charCode key keyCode'.split(' '),
            filter: function (e, t) {
                return (
                    null == e.which && (e.which = null != t.charCode ? t.charCode : t.keyCode), e
                );
            },
        },
        mouseHooks: {
            props: 'button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement'.split(
                ' ',
            ),
            filter: function (e, t) {
                var n,
                    i,
                    r,
                    o = t.button,
                    a = t.fromElement;
                return (
                    null == e.pageX &&
                        null != t.clientX &&
                        ((i = e.target.ownerDocument || E),
                        (r = i.documentElement),
                        (n = i.body),
                        (e.pageX =
                            t.clientX +
                            ((r && r.scrollLeft) || (n && n.scrollLeft) || 0) -
                            ((r && r.clientLeft) || (n && n.clientLeft) || 0)),
                        (e.pageY =
                            t.clientY +
                            ((r && r.scrollTop) || (n && n.scrollTop) || 0) -
                            ((r && r.clientTop) || (n && n.clientTop) || 0))),
                    !e.relatedTarget && a && (e.relatedTarget = a === e.target ? t.toElement : a),
                    e.which || void 0 === o || (e.which = 1 & o ? 1 : 2 & o ? 3 : 4 & o ? 2 : 0),
                    e
                );
            },
        },
        special: {
            load: { noBubble: !0 },
            focus: {
                trigger: function () {
                    if (this !== ie() && this.focus)
                        try {
                            return this.focus(), !1;
                        } catch (e) {}
                },
                delegateType: 'focusin',
            },
            blur: {
                trigger: function () {
                    return this === ie() && this.blur ? (this.blur(), !1) : void 0;
                },
                delegateType: 'focusout',
            },
            click: {
                trigger: function () {
                    return p.nodeName(this, 'input') && 'checkbox' === this.type && this.click
                        ? (this.click(), !1)
                        : void 0;
                },
                _default: function (e) {
                    return p.nodeName(e.target, 'a');
                },
            },
            beforeunload: {
                postDispatch: function (e) {
                    void 0 !== e.result && (e.originalEvent.returnValue = e.result);
                },
            },
        },
        simulate: function (e, t, n, i) {
            var r = p.extend(new p.Event(), n, { type: e, isSimulated: !0, originalEvent: {} });
            i ? p.event.trigger(r, null, t) : p.event.dispatch.call(t, r),
                r.isDefaultPrevented() && n.preventDefault();
        },
    }),
        (p.removeEvent = E.removeEventListener
            ? function (e, t, n) {
                  e.removeEventListener && e.removeEventListener(t, n, !1);
              }
            : function (e, t, n) {
                  var i = 'on' + t;
                  e.detachEvent && (typeof e[i] === B && (e[i] = null), e.detachEvent(i, n));
              }),
        (p.Event = function (e, t) {
            return this instanceof p.Event
                ? (e && e.type
                      ? ((this.originalEvent = e),
                        (this.type = e.type),
                        (this.isDefaultPrevented =
                            e.defaultPrevented ||
                            (void 0 === e.defaultPrevented &&
                                (!1 === e.returnValue ||
                                    (e.getPreventDefault && e.getPreventDefault())))
                                ? te
                                : ne))
                      : (this.type = e),
                  t && p.extend(this, t),
                  (this.timeStamp = (e && e.timeStamp) || p.now()),
                  void (this[p.expando] = !0))
                : new p.Event(e, t);
        }),
        (p.Event.prototype = {
            isDefaultPrevented: ne,
            isPropagationStopped: ne,
            isImmediatePropagationStopped: ne,
            preventDefault: function () {
                var e = this.originalEvent;
                (this.isDefaultPrevented = te),
                    e && (e.preventDefault ? e.preventDefault() : (e.returnValue = !1));
            },
            stopPropagation: function () {
                var e = this.originalEvent;
                (this.isPropagationStopped = te),
                    e && (e.stopPropagation && e.stopPropagation(), (e.cancelBubble = !0));
            },
            stopImmediatePropagation: function () {
                (this.isImmediatePropagationStopped = te), this.stopPropagation();
            },
        }),
        p.each({ mouseenter: 'mouseover', mouseleave: 'mouseout' }, function (e, t) {
            p.event.special[e] = {
                delegateType: t,
                bindType: t,
                handle: function (e) {
                    var n,
                        i = this,
                        r = e.relatedTarget,
                        o = e.handleObj;
                    return (
                        (!r || (r !== i && !p.contains(i, r))) &&
                            ((e.type = o.origType),
                            (n = o.handler.apply(this, arguments)),
                            (e.type = t)),
                        n
                    );
                },
            };
        }),
        d.submitBubbles ||
            (p.event.special.submit = {
                setup: function () {
                    return (
                        !p.nodeName(this, 'form') &&
                        void p.event.add(this, 'click._submit keypress._submit', function (e) {
                            var t = e.target,
                                n =
                                    p.nodeName(t, 'input') || p.nodeName(t, 'button')
                                        ? t.form
                                        : void 0;
                            n &&
                                !p._data(n, 'submitBubbles') &&
                                (p.event.add(n, 'submit._submit', function (e) {
                                    e._submit_bubble = !0;
                                }),
                                p._data(n, 'submitBubbles', !0));
                        })
                    );
                },
                postDispatch: function (e) {
                    e._submit_bubble &&
                        (delete e._submit_bubble,
                        this.parentNode &&
                            !e.isTrigger &&
                            p.event.simulate('submit', this.parentNode, e, !0));
                },
                teardown: function () {
                    return !p.nodeName(this, 'form') && void p.event.remove(this, '._submit');
                },
            }),
        d.changeBubbles ||
            (p.event.special.change = {
                setup: function () {
                    return G.test(this.nodeName)
                        ? (('checkbox' === this.type || 'radio' === this.type) &&
                              (p.event.add(this, 'propertychange._change', function (e) {
                                  'checked' === e.originalEvent.propertyName &&
                                      (this._just_changed = !0);
                              }),
                              p.event.add(this, 'click._change', function (e) {
                                  this._just_changed && !e.isTrigger && (this._just_changed = !1),
                                      p.event.simulate('change', this, e, !0);
                              })),
                          !1)
                        : void p.event.add(this, 'beforeactivate._change', function (e) {
                              var t = e.target;
                              G.test(t.nodeName) &&
                                  !p._data(t, 'changeBubbles') &&
                                  (p.event.add(t, 'change._change', function (e) {
                                      !this.parentNode ||
                                          e.isSimulated ||
                                          e.isTrigger ||
                                          p.event.simulate('change', this.parentNode, e, !0);
                                  }),
                                  p._data(t, 'changeBubbles', !0));
                          });
                },
                handle: function (e) {
                    var t = e.target;
                    return this !== t ||
                        e.isSimulated ||
                        e.isTrigger ||
                        ('radio' !== t.type && 'checkbox' !== t.type)
                        ? e.handleObj.handler.apply(this, arguments)
                        : void 0;
                },
                teardown: function () {
                    return p.event.remove(this, '._change'), !G.test(this.nodeName);
                },
            }),
        d.focusinBubbles ||
            p.each({ focus: 'focusin', blur: 'focusout' }, function (e, t) {
                var n = function (e) {
                    p.event.simulate(t, e.target, p.event.fix(e), !0);
                };
                p.event.special[t] = {
                    setup: function () {
                        var i = this.ownerDocument || this,
                            r = p._data(i, t);
                        r || i.addEventListener(e, n, !0), p._data(i, t, (r || 0) + 1);
                    },
                    teardown: function () {
                        var i = this.ownerDocument || this,
                            r = p._data(i, t) - 1;
                        r
                            ? p._data(i, t, r)
                            : (i.removeEventListener(e, n, !0), p._removeData(i, t));
                    },
                };
            }),
        p.fn.extend({
            on: function (e, t, n, i, r) {
                var o, a;
                if ('object' == typeof e) {
                    for (o in ('string' != typeof t && ((n = n || t), (t = void 0)), e))
                        this.on(o, t, n, e[o], r);
                    return this;
                }
                if (
                    (null == n && null == i
                        ? ((i = t), (n = t = void 0))
                        : null == i &&
                          ('string' == typeof t
                              ? ((i = n), (n = void 0))
                              : ((i = n), (n = t), (t = void 0))),
                    !1 === i)
                )
                    i = ne;
                else if (!i) return this;
                return (
                    1 === r &&
                        ((a = i),
                        (i = function (e) {
                            return p().off(e), a.apply(this, arguments);
                        }),
                        (i.guid = a.guid || (a.guid = p.guid++))),
                    this.each(function () {
                        p.event.add(this, e, i, n, t);
                    })
                );
            },
            one: function (e, t, n, i) {
                return this.on(e, t, n, i, 1);
            },
            off: function (e, t, n) {
                var i, r;
                if (e && e.preventDefault && e.handleObj)
                    return (
                        (i = e.handleObj),
                        p(e.delegateTarget).off(
                            i.namespace ? i.origType + '.' + i.namespace : i.origType,
                            i.selector,
                            i.handler,
                        ),
                        this
                    );
                if ('object' == typeof e) {
                    for (r in e) this.off(r, t, e[r]);
                    return this;
                }
                return (
                    (!1 === t || 'function' == typeof t) && ((n = t), (t = void 0)),
                    !1 === n && (n = ne),
                    this.each(function () {
                        p.event.remove(this, e, n, t);
                    })
                );
            },
            trigger: function (e, t) {
                return this.each(function () {
                    p.event.trigger(e, t, this);
                });
            },
            triggerHandler: function (e, t) {
                var n = this[0];
                return n ? p.event.trigger(e, t, n, !0) : void 0;
            },
        });
    var oe =
            'abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video',
        ae = / jQuery\d+="(?:null|\d+)"/g,
        se = new RegExp('<(?:' + oe + ')[\\s/>]', 'i'),
        le = /^\s+/,
        ue = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,
        ce = /<([\w:]+)/,
        de = /<tbody/i,
        fe = /<|&#?\w+;/,
        pe = /<(?:script|style|link)/i,
        he = /checked\s*(?:[^=]|=\s*.checked.)/i,
        me = /^$|\/(?:java|ecma)script/i,
        ge = /^true\/(.*)/,
        ve = /^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,
        ye = {
            option: [1, "<select multiple='multiple'>", '</select>'],
            legend: [1, '<fieldset>', '</fieldset>'],
            area: [1, '<map>', '</map>'],
            param: [1, '<object>', '</object>'],
            thead: [1, '<table>', '</table>'],
            tr: [2, '<table><tbody>', '</tbody></table>'],
            col: [2, '<table><tbody></tbody><colgroup>', '</colgroup></table>'],
            td: [3, '<table><tbody><tr>', '</tr></tbody></table>'],
            _default: d.htmlSerialize ? [0, '', ''] : [1, 'X<div>', '</div>'],
        },
        be = re(E),
        xe = be.appendChild(E.createElement('div'));
    function we(e, t) {
        var n,
            i,
            r = 0,
            o =
                typeof e.getElementsByTagName !== B
                    ? e.getElementsByTagName(t || '*')
                    : typeof e.querySelectorAll !== B
                    ? e.querySelectorAll(t || '*')
                    : void 0;
        if (!o)
            for (o = [], n = e.childNodes || e; null != (i = n[r]); r++)
                !t || p.nodeName(i, t) ? o.push(i) : p.merge(o, we(i, t));
        return void 0 === t || (t && p.nodeName(e, t)) ? p.merge([e], o) : o;
    }
    function Te(e) {
        Y.test(e.type) && (e.defaultChecked = e.checked);
    }
    function Ce(e, t) {
        return p.nodeName(e, 'table') && p.nodeName(11 !== t.nodeType ? t : t.firstChild, 'tr')
            ? e.getElementsByTagName('tbody')[0] ||
                  e.appendChild(e.ownerDocument.createElement('tbody'))
            : e;
    }
    function Ne(e) {
        return (e.type = (null !== p.find.attr(e, 'type')) + '/' + e.type), e;
    }
    function Ee(e) {
        var t = ge.exec(e.type);
        return t ? (e.type = t[1]) : e.removeAttribute('type'), e;
    }
    function ke(e, t) {
        for (var n, i = 0; null != (n = e[i]); i++)
            p._data(n, 'globalEval', !t || p._data(t[i], 'globalEval'));
    }
    function Se(e, t) {
        if (1 === t.nodeType && p.hasData(e)) {
            var n,
                i,
                r,
                o = p._data(e),
                a = p._data(t, o),
                s = o.events;
            if (s)
                for (n in (delete a.handle, (a.events = {}), s))
                    for (i = 0, r = s[n].length; r > i; i++) p.event.add(t, n, s[n][i]);
            a.data && (a.data = p.extend({}, a.data));
        }
    }
    function Ae(e, t) {
        var n, i, r;
        if (1 === t.nodeType) {
            if (((n = t.nodeName.toLowerCase()), !d.noCloneEvent && t[p.expando])) {
                for (i in ((r = p._data(t)), r.events)) p.removeEvent(t, i, r.handle);
                t.removeAttribute(p.expando);
            }
            'script' === n && t.text !== e.text
                ? ((Ne(t).text = e.text), Ee(t))
                : 'object' === n
                ? (t.parentNode && (t.outerHTML = e.outerHTML),
                  d.html5Clone &&
                      e.innerHTML &&
                      !p.trim(t.innerHTML) &&
                      (t.innerHTML = e.innerHTML))
                : 'input' === n && Y.test(e.type)
                ? ((t.defaultChecked = t.checked = e.checked),
                  t.value !== e.value && (t.value = e.value))
                : 'option' === n
                ? (t.defaultSelected = t.selected = e.defaultSelected)
                : ('input' === n || 'textarea' === n) && (t.defaultValue = e.defaultValue);
        }
    }
    (ye.optgroup = ye.option),
        (ye.tbody = ye.tfoot = ye.colgroup = ye.caption = ye.thead),
        (ye.th = ye.td),
        p.extend({
            clone: function (e, t, n) {
                var i,
                    r,
                    o,
                    a,
                    s,
                    l = p.contains(e.ownerDocument, e);
                if (
                    (d.html5Clone || p.isXMLDoc(e) || !se.test('<' + e.nodeName + '>')
                        ? (o = e.cloneNode(!0))
                        : ((xe.innerHTML = e.outerHTML), xe.removeChild((o = xe.firstChild))),
                    !(
                        (d.noCloneEvent && d.noCloneChecked) ||
                        (1 !== e.nodeType && 11 !== e.nodeType) ||
                        p.isXMLDoc(e)
                    ))
                )
                    for (i = we(o), s = we(e), a = 0; null != (r = s[a]); ++a) i[a] && Ae(r, i[a]);
                if (t)
                    if (n)
                        for (s = s || we(e), i = i || we(o), a = 0; null != (r = s[a]); a++)
                            Se(r, i[a]);
                    else Se(e, o);
                return (
                    (i = we(o, 'script')),
                    i.length > 0 && ke(i, !l && we(e, 'script')),
                    (i = s = r = null),
                    o
                );
            },
            buildFragment: function (e, t, n, i) {
                for (var r, o, a, s, l, u, c, f = e.length, h = re(t), m = [], g = 0; f > g; g++)
                    if (((o = e[g]), o || 0 === o))
                        if ('object' === p.type(o)) p.merge(m, o.nodeType ? [o] : o);
                        else if (fe.test(o)) {
                            (s = s || h.appendChild(t.createElement('div'))),
                                (l = (ce.exec(o) || ['', ''])[1].toLowerCase()),
                                (c = ye[l] || ye._default),
                                (s.innerHTML = c[1] + o.replace(ue, '<$1></$2>') + c[2]),
                                (r = c[0]);
                            while (r--) s = s.lastChild;
                            if (
                                (!d.leadingWhitespace &&
                                    le.test(o) &&
                                    m.push(t.createTextNode(le.exec(o)[0])),
                                !d.tbody)
                            ) {
                                (o =
                                    'table' !== l || de.test(o)
                                        ? '<table>' !== c[1] || de.test(o)
                                            ? 0
                                            : s
                                        : s.firstChild),
                                    (r = o && o.childNodes.length);
                                while (r--)
                                    p.nodeName((u = o.childNodes[r]), 'tbody') &&
                                        !u.childNodes.length &&
                                        o.removeChild(u);
                            }
                            p.merge(m, s.childNodes), (s.textContent = '');
                            while (s.firstChild) s.removeChild(s.firstChild);
                            s = h.lastChild;
                        } else m.push(t.createTextNode(o));
                s && h.removeChild(s), d.appendChecked || p.grep(we(m, 'input'), Te), (g = 0);
                while ((o = m[g++]))
                    if (
                        (!i || -1 === p.inArray(o, i)) &&
                        ((a = p.contains(o.ownerDocument, o)),
                        (s = we(h.appendChild(o), 'script')),
                        a && ke(s),
                        n)
                    ) {
                        r = 0;
                        while ((o = s[r++])) me.test(o.type || '') && n.push(o);
                    }
                return (s = null), h;
            },
            cleanData: function (e, t) {
                for (
                    var i,
                        r,
                        o,
                        a,
                        s = 0,
                        l = p.expando,
                        u = p.cache,
                        c = d.deleteExpando,
                        f = p.event.special;
                    null != (i = e[s]);
                    s++
                )
                    if ((t || p.acceptData(i)) && ((o = i[l]), (a = o && u[o]))) {
                        if (a.events)
                            for (r in a.events)
                                f[r] ? p.event.remove(i, r) : p.removeEvent(i, r, a.handle);
                        u[o] &&
                            (delete u[o],
                            c
                                ? delete i[l]
                                : typeof i.removeAttribute !== B
                                ? i.removeAttribute(l)
                                : (i[l] = null),
                            n.push(o));
                    }
            },
        }),
        p.fn.extend({
            text: function (e) {
                return J(
                    this,
                    function (e) {
                        return void 0 === e
                            ? p.text(this)
                            : this.empty().append(
                                  ((this[0] && this[0].ownerDocument) || E).createTextNode(e),
                              );
                    },
                    null,
                    e,
                    arguments.length,
                );
            },
            append: function () {
                return this.domManip(arguments, function (e) {
                    if (1 === this.nodeType || 11 === this.nodeType || 9 === this.nodeType) {
                        var t = Ce(this, e);
                        t.appendChild(e);
                    }
                });
            },
            prepend: function () {
                return this.domManip(arguments, function (e) {
                    if (1 === this.nodeType || 11 === this.nodeType || 9 === this.nodeType) {
                        var t = Ce(this, e);
                        t.insertBefore(e, t.firstChild);
                    }
                });
            },
            before: function () {
                return this.domManip(arguments, function (e) {
                    this.parentNode && this.parentNode.insertBefore(e, this);
                });
            },
            after: function () {
                return this.domManip(arguments, function (e) {
                    this.parentNode && this.parentNode.insertBefore(e, this.nextSibling);
                });
            },
            remove: function (e, t) {
                for (var n, i = e ? p.filter(e, this) : this, r = 0; null != (n = i[r]); r++)
                    t || 1 !== n.nodeType || p.cleanData(we(n)),
                        n.parentNode &&
                            (t && p.contains(n.ownerDocument, n) && ke(we(n, 'script')),
                            n.parentNode.removeChild(n));
                return this;
            },
            empty: function () {
                for (var e, t = 0; null != (e = this[t]); t++) {
                    1 === e.nodeType && p.cleanData(we(e, !1));
                    while (e.firstChild) e.removeChild(e.firstChild);
                    e.options && p.nodeName(e, 'select') && (e.options.length = 0);
                }
                return this;
            },
            clone: function (e, t) {
                return (
                    (e = null != e && e),
                    (t = null == t ? e : t),
                    this.map(function () {
                        return p.clone(this, e, t);
                    })
                );
            },
            html: function (e) {
                return J(
                    this,
                    function (e) {
                        var t = this[0] || {},
                            n = 0,
                            i = this.length;
                        if (void 0 === e)
                            return 1 === t.nodeType ? t.innerHTML.replace(ae, '') : void 0;
                        if (
                            !(
                                'string' != typeof e ||
                                pe.test(e) ||
                                (!d.htmlSerialize && se.test(e)) ||
                                (!d.leadingWhitespace && le.test(e)) ||
                                ye[(ce.exec(e) || ['', ''])[1].toLowerCase()]
                            )
                        ) {
                            e = e.replace(ue, '<$1></$2>');
                            try {
                                for (; i > n; n++)
                                    (t = this[n] || {}),
                                        1 === t.nodeType &&
                                            (p.cleanData(we(t, !1)), (t.innerHTML = e));
                                t = 0;
                            } catch (r) {}
                        }
                        t && this.empty().append(e);
                    },
                    null,
                    e,
                    arguments.length,
                );
            },
            replaceWith: function () {
                var e = arguments[0];
                return (
                    this.domManip(arguments, function (t) {
                        (e = this.parentNode), p.cleanData(we(this)), e && e.replaceChild(t, this);
                    }),
                    e && (e.length || e.nodeType) ? this : this.remove()
                );
            },
            detach: function (e) {
                return this.remove(e, !0);
            },
            domManip: function (e, t) {
                e = r.apply([], e);
                var n,
                    i,
                    o,
                    a,
                    s,
                    l,
                    u = 0,
                    c = this.length,
                    f = this,
                    h = c - 1,
                    m = e[0],
                    g = p.isFunction(m);
                if (g || (c > 1 && 'string' == typeof m && !d.checkClone && he.test(m)))
                    return this.each(function (n) {
                        var i = f.eq(n);
                        g && (e[0] = m.call(this, n, i.html())), i.domManip(e, t);
                    });
                if (
                    c &&
                    ((l = p.buildFragment(e, this[0].ownerDocument, !1, this)),
                    (n = l.firstChild),
                    1 === l.childNodes.length && (l = n),
                    n)
                ) {
                    for (a = p.map(we(l, 'script'), Ne), o = a.length; c > u; u++)
                        (i = l),
                            u !== h && ((i = p.clone(i, !0, !0)), o && p.merge(a, we(i, 'script'))),
                            t.call(this[u], i, u);
                    if (o)
                        for (s = a[a.length - 1].ownerDocument, p.map(a, Ee), u = 0; o > u; u++)
                            (i = a[u]),
                                me.test(i.type || '') &&
                                    !p._data(i, 'globalEval') &&
                                    p.contains(s, i) &&
                                    (i.src
                                        ? p._evalUrl && p._evalUrl(i.src)
                                        : p.globalEval(
                                              (
                                                  i.text ||
                                                  i.textContent ||
                                                  i.innerHTML ||
                                                  ''
                                              ).replace(ve, ''),
                                          ));
                    l = n = null;
                }
                return this;
            },
        }),
        p.each(
            {
                appendTo: 'append',
                prependTo: 'prepend',
                insertBefore: 'before',
                insertAfter: 'after',
                replaceAll: 'replaceWith',
            },
            function (e, t) {
                p.fn[e] = function (e) {
                    for (var n, i = 0, r = [], a = p(e), s = a.length - 1; s >= i; i++)
                        (n = i === s ? this : this.clone(!0)), p(a[i])[t](n), o.apply(r, n.get());
                    return this.pushStack(r);
                };
            },
        );
    var De,
        je = {};
    function Le(t, n) {
        var i = p(n.createElement(t)).appendTo(n.body),
            r = e.getDefaultComputedStyle
                ? e.getDefaultComputedStyle(i[0]).display
                : p.css(i[0], 'display');
        return i.detach(), r;
    }
    function He(e) {
        var t = E,
            n = je[e];
        return (
            n ||
                ((n = Le(e, t)),
                ('none' !== n && n) ||
                    ((De = (De || p("<iframe frameborder='0' width='0' height='0'/>")).appendTo(
                        t.documentElement,
                    )),
                    (t = (De[0].contentWindow || De[0].contentDocument).document),
                    t.write(),
                    t.close(),
                    (n = Le(e, t)),
                    De.detach()),
                (je[e] = n)),
            n
        );
    }
    !(function () {
        var e,
            t,
            n = E.createElement('div'),
            i =
                '-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;padding:0;margin:0;border:0';
        (n.innerHTML = "  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>"),
            (e = n.getElementsByTagName('a')[0]),
            (e.style.cssText = 'float:left;opacity:.5'),
            (d.opacity = /^0.5/.test(e.style.opacity)),
            (d.cssFloat = !!e.style.cssFloat),
            (n.style.backgroundClip = 'content-box'),
            (n.cloneNode(!0).style.backgroundClip = ''),
            (d.clearCloneStyle = 'content-box' === n.style.backgroundClip),
            (e = n = null),
            (d.shrinkWrapBlocks = function () {
                var e, n, r;
                if (null == t) {
                    if (((e = E.getElementsByTagName('body')[0]), !e)) return;
                    (n = E.createElement('div')),
                        (r = E.createElement('div')),
                        e.appendChild(n).appendChild(r),
                        (t = !1),
                        typeof r.style.zoom !== B &&
                            ((r.style.cssText = i + ';width:1px;padding:1px;zoom:1'),
                            (r.innerHTML = '<div></div>'),
                            (r.firstChild.style.width = '5px'),
                            (t = 3 !== r.offsetWidth)),
                        e.removeChild(n),
                        (e = n = r = null);
                }
                return t;
            });
    })();
    var qe,
        _e,
        Me = /^margin/,
        Fe = new RegExp('^(' + X + ')(?!px)[a-z%]+$', 'i'),
        Oe = /^(top|right|bottom|left)$/;
    function Be(e, t) {
        return {
            get: function () {
                var n = e();
                if (null != n)
                    return n ? void delete this.get : (this.get = t).apply(this, arguments);
            },
        };
    }
    e.getComputedStyle
        ? ((qe = function (e) {
              return e.ownerDocument.defaultView.getComputedStyle(e, null);
          }),
          (_e = function (e, t, n) {
              var i,
                  r,
                  o,
                  a,
                  s = e.style;
              return (
                  (n = n || qe(e)),
                  (a = n ? n.getPropertyValue(t) || n[t] : void 0),
                  n &&
                      ('' !== a || p.contains(e.ownerDocument, e) || (a = p.style(e, t)),
                      Fe.test(a) &&
                          Me.test(t) &&
                          ((i = s.width),
                          (r = s.minWidth),
                          (o = s.maxWidth),
                          (s.minWidth = s.maxWidth = s.width = a),
                          (a = n.width),
                          (s.width = i),
                          (s.minWidth = r),
                          (s.maxWidth = o))),
                  void 0 === a ? a : a + ''
              );
          }))
        : E.documentElement.currentStyle &&
          ((qe = function (e) {
              return e.currentStyle;
          }),
          (_e = function (e, t, n) {
              var i,
                  r,
                  o,
                  a,
                  s = e.style;
              return (
                  (n = n || qe(e)),
                  (a = n ? n[t] : void 0),
                  null == a && s && s[t] && (a = s[t]),
                  Fe.test(a) &&
                      !Oe.test(t) &&
                      ((i = s.left),
                      (r = e.runtimeStyle),
                      (o = r && r.left),
                      o && (r.left = e.currentStyle.left),
                      (s.left = 'fontSize' === t ? '1em' : a),
                      (a = s.pixelLeft + 'px'),
                      (s.left = i),
                      o && (r.left = o)),
                  void 0 === a ? a : a + '' || 'auto'
              );
          })),
        (function () {
            var t,
                n,
                i,
                r,
                o,
                a,
                s = E.createElement('div'),
                l = 'border:0;width:0;height:0;position:absolute;top:0;left:-9999px',
                u =
                    '-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;padding:0;margin:0;border:0';
            function c() {
                var t,
                    n,
                    s = E.getElementsByTagName('body')[0];
                s &&
                    ((t = E.createElement('div')),
                    (n = E.createElement('div')),
                    (t.style.cssText = l),
                    s.appendChild(t).appendChild(n),
                    (n.style.cssText =
                        '-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:absolute;display:block;padding:1px;border:1px;width:4px;margin-top:1%;top:1%'),
                    p.swap(s, null != s.style.zoom ? { zoom: 1 } : {}, function () {
                        i = 4 === n.offsetWidth;
                    }),
                    (r = !0),
                    (o = !1),
                    (a = !0),
                    e.getComputedStyle &&
                        ((o = '1%' !== (e.getComputedStyle(n, null) || {}).top),
                        (r = '4px' === (e.getComputedStyle(n, null) || { width: '4px' }).width)),
                    s.removeChild(t),
                    (n = s = null));
            }
            (s.innerHTML = "  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>"),
                (t = s.getElementsByTagName('a')[0]),
                (t.style.cssText = 'float:left;opacity:.5'),
                (d.opacity = /^0.5/.test(t.style.opacity)),
                (d.cssFloat = !!t.style.cssFloat),
                (s.style.backgroundClip = 'content-box'),
                (s.cloneNode(!0).style.backgroundClip = ''),
                (d.clearCloneStyle = 'content-box' === s.style.backgroundClip),
                (t = s = null),
                p.extend(d, {
                    reliableHiddenOffsets: function () {
                        if (null != n) return n;
                        var e,
                            t,
                            i,
                            r = E.createElement('div'),
                            o = E.getElementsByTagName('body')[0];
                        return o
                            ? (r.setAttribute('className', 't'),
                              (r.innerHTML =
                                  "  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>"),
                              (e = E.createElement('div')),
                              (e.style.cssText = l),
                              o.appendChild(e).appendChild(r),
                              (r.innerHTML = '<table><tr><td></td><td>t</td></tr></table>'),
                              (t = r.getElementsByTagName('td')),
                              (t[0].style.cssText = 'padding:0;margin:0;border:0;display:none'),
                              (i = 0 === t[0].offsetHeight),
                              (t[0].style.display = ''),
                              (t[1].style.display = 'none'),
                              (n = i && 0 === t[0].offsetHeight),
                              o.removeChild(e),
                              (r = o = null),
                              n)
                            : void 0;
                    },
                    boxSizing: function () {
                        return null == i && c(), i;
                    },
                    boxSizingReliable: function () {
                        return null == r && c(), r;
                    },
                    pixelPosition: function () {
                        return null == o && c(), o;
                    },
                    reliableMarginRight: function () {
                        var t, n, i, r;
                        if (null == a && e.getComputedStyle) {
                            if (((t = E.getElementsByTagName('body')[0]), !t)) return;
                            (n = E.createElement('div')),
                                (i = E.createElement('div')),
                                (n.style.cssText = l),
                                t.appendChild(n).appendChild(i),
                                (r = i.appendChild(E.createElement('div'))),
                                (r.style.cssText = i.style.cssText = u),
                                (r.style.marginRight = r.style.width = '0'),
                                (i.style.width = '1px'),
                                (a = !parseFloat((e.getComputedStyle(r, null) || {}).marginRight)),
                                t.removeChild(n);
                        }
                        return a;
                    },
                });
        })(),
        (p.swap = function (e, t, n, i) {
            var r,
                o,
                a = {};
            for (o in t) (a[o] = e.style[o]), (e.style[o] = t[o]);
            for (o in ((r = n.apply(e, i || [])), t)) e.style[o] = a[o];
            return r;
        });
    var Pe = /alpha\([^)]*\)/i,
        Re = /opacity\s*=\s*([^)]*)/,
        We = /^(none|table(?!-c[ea]).+)/,
        $e = new RegExp('^(' + X + ')(.*)$', 'i'),
        ze = new RegExp('^([+-])=(' + X + ')', 'i'),
        Ie = { position: 'absolute', visibility: 'hidden', display: 'block' },
        Xe = { letterSpacing: 0, fontWeight: 400 },
        Ue = ['Webkit', 'O', 'Moz', 'ms'];
    function Ve(e, t) {
        if (t in e) return t;
        var n = t.charAt(0).toUpperCase() + t.slice(1),
            i = t,
            r = Ue.length;
        while (r--) if (((t = Ue[r] + n), t in e)) return t;
        return i;
    }
    function Je(e, t) {
        for (var n, i, r, o = [], a = 0, s = e.length; s > a; a++)
            (i = e[a]),
                i.style &&
                    ((o[a] = p._data(i, 'olddisplay')),
                    (n = i.style.display),
                    t
                        ? (o[a] || 'none' !== n || (i.style.display = ''),
                          '' === i.style.display &&
                              V(i) &&
                              (o[a] = p._data(i, 'olddisplay', He(i.nodeName))))
                        : o[a] ||
                          ((r = V(i)),
                          ((n && 'none' !== n) || !r) &&
                              p._data(i, 'olddisplay', r ? n : p.css(i, 'display'))));
        for (a = 0; s > a; a++)
            (i = e[a]),
                i.style &&
                    ((t && 'none' !== i.style.display && '' !== i.style.display) ||
                        (i.style.display = t ? o[a] || '' : 'none'));
        return e;
    }
    function Ye(e, t, n) {
        var i = $e.exec(t);
        return i ? Math.max(0, i[1] - (n || 0)) + (i[2] || 'px') : t;
    }
    function Ge(e, t, n, i, r) {
        for (
            var o = n === (i ? 'border' : 'content') ? 4 : 'width' === t ? 1 : 0, a = 0;
            4 > o;
            o += 2
        )
            'margin' === n && (a += p.css(e, n + U[o], !0, r)),
                i
                    ? ('content' === n && (a -= p.css(e, 'padding' + U[o], !0, r)),
                      'margin' !== n && (a -= p.css(e, 'border' + U[o] + 'Width', !0, r)))
                    : ((a += p.css(e, 'padding' + U[o], !0, r)),
                      'padding' !== n && (a += p.css(e, 'border' + U[o] + 'Width', !0, r)));
        return a;
    }
    function Qe(e, t, n) {
        var i = !0,
            r = 'width' === t ? e.offsetWidth : e.offsetHeight,
            o = qe(e),
            a = d.boxSizing() && 'border-box' === p.css(e, 'boxSizing', !1, o);
        if (0 >= r || null == r) {
            if (((r = _e(e, t, o)), (0 > r || null == r) && (r = e.style[t]), Fe.test(r))) return r;
            (i = a && (d.boxSizingReliable() || r === e.style[t])), (r = parseFloat(r) || 0);
        }
        return r + Ge(e, t, n || (a ? 'border' : 'content'), i, o) + 'px';
    }
    function Ke(e, t, n, i, r) {
        return new Ke.prototype.init(e, t, n, i, r);
    }
    p.extend({
        cssHooks: {
            opacity: {
                get: function (e, t) {
                    if (t) {
                        var n = _e(e, 'opacity');
                        return '' === n ? '1' : n;
                    }
                },
            },
        },
        cssNumber: {
            columnCount: !0,
            fillOpacity: !0,
            fontWeight: !0,
            lineHeight: !0,
            opacity: !0,
            order: !0,
            orphans: !0,
            widows: !0,
            zIndex: !0,
            zoom: !0,
        },
        cssProps: { float: d.cssFloat ? 'cssFloat' : 'styleFloat' },
        style: function (e, t, n, i) {
            if (e && 3 !== e.nodeType && 8 !== e.nodeType && e.style) {
                var r,
                    o,
                    a,
                    s = p.camelCase(t),
                    l = e.style;
                if (
                    ((t = p.cssProps[s] || (p.cssProps[s] = Ve(l, s))),
                    (a = p.cssHooks[t] || p.cssHooks[s]),
                    void 0 === n)
                )
                    return a && 'get' in a && void 0 !== (r = a.get(e, !1, i)) ? r : l[t];
                if (
                    ((o = typeof n),
                    'string' === o &&
                        (r = ze.exec(n)) &&
                        ((n = (r[1] + 1) * r[2] + parseFloat(p.css(e, t))), (o = 'number')),
                    null != n &&
                        n === n &&
                        ('number' !== o || p.cssNumber[s] || (n += 'px'),
                        d.clearCloneStyle ||
                            '' !== n ||
                            0 !== t.indexOf('background') ||
                            (l[t] = 'inherit'),
                        !a || !('set' in a) || void 0 !== (n = a.set(e, n, i))))
                )
                    try {
                        (l[t] = ''), (l[t] = n);
                    } catch (u) {}
            }
        },
        css: function (e, t, n, i) {
            var r,
                o,
                a,
                s = p.camelCase(t);
            return (
                (t = p.cssProps[s] || (p.cssProps[s] = Ve(e.style, s))),
                (a = p.cssHooks[t] || p.cssHooks[s]),
                a && 'get' in a && (o = a.get(e, !0, n)),
                void 0 === o && (o = _e(e, t, i)),
                'normal' === o && t in Xe && (o = Xe[t]),
                '' === n || n ? ((r = parseFloat(o)), !0 === n || p.isNumeric(r) ? r || 0 : o) : o
            );
        },
    }),
        p.each(['height', 'width'], function (e, t) {
            p.cssHooks[t] = {
                get: function (e, n, i) {
                    return n
                        ? 0 === e.offsetWidth && We.test(p.css(e, 'display'))
                            ? p.swap(e, Ie, function () {
                                  return Qe(e, t, i);
                              })
                            : Qe(e, t, i)
                        : void 0;
                },
                set: function (e, n, i) {
                    var r = i && qe(e);
                    return Ye(
                        e,
                        n,
                        i
                            ? Ge(
                                  e,
                                  t,
                                  i,
                                  d.boxSizing() && 'border-box' === p.css(e, 'boxSizing', !1, r),
                                  r,
                              )
                            : 0,
                    );
                },
            };
        }),
        d.opacity ||
            (p.cssHooks.opacity = {
                get: function (e, t) {
                    return Re.test(
                        (t && e.currentStyle ? e.currentStyle.filter : e.style.filter) || '',
                    )
                        ? 0.01 * parseFloat(RegExp.$1) + ''
                        : t
                        ? '1'
                        : '';
                },
                set: function (e, t) {
                    var n = e.style,
                        i = e.currentStyle,
                        r = p.isNumeric(t) ? 'alpha(opacity=' + 100 * t + ')' : '',
                        o = (i && i.filter) || n.filter || '';
                    (n.zoom = 1),
                        ((t >= 1 || '' === t) &&
                            '' === p.trim(o.replace(Pe, '')) &&
                            n.removeAttribute &&
                            (n.removeAttribute('filter'), '' === t || (i && !i.filter))) ||
                            (n.filter = Pe.test(o) ? o.replace(Pe, r) : o + ' ' + r);
                },
            }),
        (p.cssHooks.marginRight = Be(d.reliableMarginRight, function (e, t) {
            return t ? p.swap(e, { display: 'inline-block' }, _e, [e, 'marginRight']) : void 0;
        })),
        p.each({ margin: '', padding: '', border: 'Width' }, function (e, t) {
            (p.cssHooks[e + t] = {
                expand: function (n) {
                    for (
                        var i = 0, r = {}, o = 'string' == typeof n ? n.split(' ') : [n];
                        4 > i;
                        i++
                    )
                        r[e + U[i] + t] = o[i] || o[i - 2] || o[0];
                    return r;
                },
            }),
                Me.test(e) || (p.cssHooks[e + t].set = Ye);
        }),
        p.fn.extend({
            css: function (e, t) {
                return J(
                    this,
                    function (e, t, n) {
                        var i,
                            r,
                            o = {},
                            a = 0;
                        if (p.isArray(t)) {
                            for (i = qe(e), r = t.length; r > a; a++)
                                o[t[a]] = p.css(e, t[a], !1, i);
                            return o;
                        }
                        return void 0 !== n ? p.style(e, t, n) : p.css(e, t);
                    },
                    e,
                    t,
                    arguments.length > 1,
                );
            },
            show: function () {
                return Je(this, !0);
            },
            hide: function () {
                return Je(this);
            },
            toggle: function (e) {
                return 'boolean' == typeof e
                    ? e
                        ? this.show()
                        : this.hide()
                    : this.each(function () {
                          V(this) ? p(this).show() : p(this).hide();
                      });
            },
        }),
        (p.Tween = Ke),
        (Ke.prototype = {
            constructor: Ke,
            init: function (e, t, n, i, r, o) {
                (this.elem = e),
                    (this.prop = n),
                    (this.easing = r || 'swing'),
                    (this.options = t),
                    (this.start = this.now = this.cur()),
                    (this.end = i),
                    (this.unit = o || (p.cssNumber[n] ? '' : 'px'));
            },
            cur: function () {
                var e = Ke.propHooks[this.prop];
                return e && e.get ? e.get(this) : Ke.propHooks._default.get(this);
            },
            run: function (e) {
                var t,
                    n = Ke.propHooks[this.prop];
                return (
                    (this.pos = t =
                        this.options.duration
                            ? p.easing[this.easing](
                                  e,
                                  this.options.duration * e,
                                  0,
                                  1,
                                  this.options.duration,
                              )
                            : e),
                    (this.now = (this.end - this.start) * t + this.start),
                    this.options.step && this.options.step.call(this.elem, this.now, this),
                    n && n.set ? n.set(this) : Ke.propHooks._default.set(this),
                    this
                );
            },
        }),
        (Ke.prototype.init.prototype = Ke.prototype),
        (Ke.propHooks = {
            _default: {
                get: function (e) {
                    var t;
                    return null == e.elem[e.prop] || (e.elem.style && null != e.elem.style[e.prop])
                        ? ((t = p.css(e.elem, e.prop, '')), t && 'auto' !== t ? t : 0)
                        : e.elem[e.prop];
                },
                set: function (e) {
                    p.fx.step[e.prop]
                        ? p.fx.step[e.prop](e)
                        : e.elem.style &&
                          (null != e.elem.style[p.cssProps[e.prop]] || p.cssHooks[e.prop])
                        ? p.style(e.elem, e.prop, e.now + e.unit)
                        : (e.elem[e.prop] = e.now);
                },
            },
        }),
        (Ke.propHooks.scrollTop = Ke.propHooks.scrollLeft =
            {
                set: function (e) {
                    e.elem.nodeType && e.elem.parentNode && (e.elem[e.prop] = e.now);
                },
            }),
        (p.easing = {
            linear: function (e) {
                return e;
            },
            swing: function (e) {
                return 0.5 - Math.cos(e * Math.PI) / 2;
            },
        }),
        (p.fx = Ke.prototype.init),
        (p.fx.step = {});
    var Ze,
        et,
        tt = /^(?:toggle|show|hide)$/,
        nt = new RegExp('^(?:([+-])=|)(' + X + ')([a-z%]*)$', 'i'),
        it = /queueHooks$/,
        rt = [ut],
        ot = {
            '*': [
                function (e, t) {
                    var n = this.createTween(e, t),
                        i = n.cur(),
                        r = nt.exec(t),
                        o = (r && r[3]) || (p.cssNumber[e] ? '' : 'px'),
                        a = (p.cssNumber[e] || ('px' !== o && +i)) && nt.exec(p.css(n.elem, e)),
                        s = 1,
                        l = 20;
                    if (a && a[3] !== o) {
                        (o = o || a[3]), (r = r || []), (a = +i || 1);
                        do {
                            (s = s || '.5'), (a /= s), p.style(n.elem, e, a + o);
                        } while (s !== (s = n.cur() / i) && 1 !== s && --l);
                    }
                    return (
                        r &&
                            ((a = n.start = +a || +i || 0),
                            (n.unit = o),
                            (n.end = r[1] ? a + (r[1] + 1) * r[2] : +r[2])),
                        n
                    );
                },
            ],
        };
    function at() {
        return (
            setTimeout(function () {
                Ze = void 0;
            }),
            (Ze = p.now())
        );
    }
    function st(e, t) {
        var n,
            i = { height: e },
            r = 0;
        for (t = t ? 1 : 0; 4 > r; r += 2 - t) (n = U[r]), (i['margin' + n] = i['padding' + n] = e);
        return t && (i.opacity = i.width = e), i;
    }
    function lt(e, t, n) {
        for (var i, r = (ot[t] || []).concat(ot['*']), o = 0, a = r.length; a > o; o++)
            if ((i = r[o].call(n, t, e))) return i;
    }
    function ut(e, t, n) {
        var i,
            r,
            o,
            a,
            s,
            l,
            u,
            c,
            f = this,
            h = {},
            m = e.style,
            g = e.nodeType && V(e),
            v = p._data(e, 'fxshow');
        for (i in (n.queue ||
            ((s = p._queueHooks(e, 'fx')),
            null == s.unqueued &&
                ((s.unqueued = 0),
                (l = s.empty.fire),
                (s.empty.fire = function () {
                    s.unqueued || l();
                })),
            s.unqueued++,
            f.always(function () {
                f.always(function () {
                    s.unqueued--, p.queue(e, 'fx').length || s.empty.fire();
                });
            })),
        1 === e.nodeType &&
            ('height' in t || 'width' in t) &&
            ((n.overflow = [m.overflow, m.overflowX, m.overflowY]),
            (u = p.css(e, 'display')),
            (c = He(e.nodeName)),
            'none' === u && (u = c),
            'inline' === u &&
                'none' === p.css(e, 'float') &&
                (d.inlineBlockNeedsLayout && 'inline' !== c
                    ? (m.zoom = 1)
                    : (m.display = 'inline-block'))),
        n.overflow &&
            ((m.overflow = 'hidden'),
            d.shrinkWrapBlocks() ||
                f.always(function () {
                    (m.overflow = n.overflow[0]),
                        (m.overflowX = n.overflow[1]),
                        (m.overflowY = n.overflow[2]);
                })),
        t))
            if (((r = t[i]), tt.exec(r))) {
                if ((delete t[i], (o = o || 'toggle' === r), r === (g ? 'hide' : 'show'))) {
                    if ('show' !== r || !v || void 0 === v[i]) continue;
                    g = !0;
                }
                h[i] = (v && v[i]) || p.style(e, i);
            }
        if (!p.isEmptyObject(h))
            for (i in (v ? 'hidden' in v && (g = v.hidden) : (v = p._data(e, 'fxshow', {})),
            o && (v.hidden = !g),
            g
                ? p(e).show()
                : f.done(function () {
                      p(e).hide();
                  }),
            f.done(function () {
                var t;
                for (t in (p._removeData(e, 'fxshow'), h)) p.style(e, t, h[t]);
            }),
            h))
                (a = lt(g ? v[i] : 0, i, f)),
                    i in v ||
                        ((v[i] = a.start),
                        g &&
                            ((a.end = a.start),
                            (a.start = 'width' === i || 'height' === i ? 1 : 0)));
    }
    function ct(e, t) {
        var n, i, r, o, a;
        for (n in e)
            if (
                ((i = p.camelCase(n)),
                (r = t[i]),
                (o = e[n]),
                p.isArray(o) && ((r = o[1]), (o = e[n] = o[0])),
                n !== i && ((e[i] = o), delete e[n]),
                (a = p.cssHooks[i]),
                a && 'expand' in a)
            )
                for (n in ((o = a.expand(o)), delete e[i], o))
                    n in e || ((e[n] = o[n]), (t[n] = r));
            else t[i] = r;
    }
    function dt(e, t, n) {
        var i,
            r,
            o = 0,
            a = rt.length,
            s = p.Deferred().always(function () {
                delete l.elem;
            }),
            l = function () {
                if (r) return !1;
                for (
                    var t = Ze || at(),
                        n = Math.max(0, u.startTime + u.duration - t),
                        i = n / u.duration || 0,
                        o = 1 - i,
                        a = 0,
                        l = u.tweens.length;
                    l > a;
                    a++
                )
                    u.tweens[a].run(o);
                return s.notifyWith(e, [u, o, n]), 1 > o && l ? n : (s.resolveWith(e, [u]), !1);
            },
            u = s.promise({
                elem: e,
                props: p.extend({}, t),
                opts: p.extend(!0, { specialEasing: {} }, n),
                originalProperties: t,
                originalOptions: n,
                startTime: Ze || at(),
                duration: n.duration,
                tweens: [],
                createTween: function (t, n) {
                    var i = p.Tween(e, u.opts, t, n, u.opts.specialEasing[t] || u.opts.easing);
                    return u.tweens.push(i), i;
                },
                stop: function (t) {
                    var n = 0,
                        i = t ? u.tweens.length : 0;
                    if (r) return this;
                    for (r = !0; i > n; n++) u.tweens[n].run(1);
                    return t ? s.resolveWith(e, [u, t]) : s.rejectWith(e, [u, t]), this;
                },
            }),
            c = u.props;
        for (ct(c, u.opts.specialEasing); a > o; o++)
            if ((i = rt[o].call(u, e, c, u.opts))) return i;
        return (
            p.map(c, lt, u),
            p.isFunction(u.opts.start) && u.opts.start.call(e, u),
            p.fx.timer(p.extend(l, { elem: e, anim: u, queue: u.opts.queue })),
            u
                .progress(u.opts.progress)
                .done(u.opts.done, u.opts.complete)
                .fail(u.opts.fail)
                .always(u.opts.always)
        );
    }
    (p.Animation = p.extend(dt, {
        tweener: function (e, t) {
            p.isFunction(e) ? ((t = e), (e = ['*'])) : (e = e.split(' '));
            for (var n, i = 0, r = e.length; r > i; i++)
                (n = e[i]), (ot[n] = ot[n] || []), ot[n].unshift(t);
        },
        prefilter: function (e, t) {
            t ? rt.unshift(e) : rt.push(e);
        },
    })),
        (p.speed = function (e, t, n) {
            var i =
                e && 'object' == typeof e
                    ? p.extend({}, e)
                    : {
                          complete: n || (!n && t) || (p.isFunction(e) && e),
                          duration: e,
                          easing: (n && t) || (t && !p.isFunction(t) && t),
                      };
            return (
                (i.duration = p.fx.off
                    ? 0
                    : 'number' == typeof i.duration
                    ? i.duration
                    : i.duration in p.fx.speeds
                    ? p.fx.speeds[i.duration]
                    : p.fx.speeds._default),
                (null == i.queue || !0 === i.queue) && (i.queue = 'fx'),
                (i.old = i.complete),
                (i.complete = function () {
                    p.isFunction(i.old) && i.old.call(this), i.queue && p.dequeue(this, i.queue);
                }),
                i
            );
        }),
        p.fn.extend({
            fadeTo: function (e, t, n, i) {
                return this.filter(V)
                    .css('opacity', 0)
                    .show()
                    .end()
                    .animate({ opacity: t }, e, n, i);
            },
            animate: function (e, t, n, i) {
                var r = p.isEmptyObject(e),
                    o = p.speed(t, n, i),
                    a = function () {
                        var t = dt(this, p.extend({}, e), o);
                        (r || p._data(this, 'finish')) && t.stop(!0);
                    };
                return (a.finish = a), r || !1 === o.queue ? this.each(a) : this.queue(o.queue, a);
            },
            stop: function (e, t, n) {
                var i = function (e) {
                    var t = e.stop;
                    delete e.stop, t(n);
                };
                return (
                    'string' != typeof e && ((n = t), (t = e), (e = void 0)),
                    t && !1 !== e && this.queue(e || 'fx', []),
                    this.each(function () {
                        var t = !0,
                            r = null != e && e + 'queueHooks',
                            o = p.timers,
                            a = p._data(this);
                        if (r) a[r] && a[r].stop && i(a[r]);
                        else for (r in a) a[r] && a[r].stop && it.test(r) && i(a[r]);
                        for (r = o.length; r--; )
                            o[r].elem !== this ||
                                (null != e && o[r].queue !== e) ||
                                (o[r].anim.stop(n), (t = !1), o.splice(r, 1));
                        (t || !n) && p.dequeue(this, e);
                    })
                );
            },
            finish: function (e) {
                return (
                    !1 !== e && (e = e || 'fx'),
                    this.each(function () {
                        var t,
                            n = p._data(this),
                            i = n[e + 'queue'],
                            r = n[e + 'queueHooks'],
                            o = p.timers,
                            a = i ? i.length : 0;
                        for (
                            n.finish = !0,
                                p.queue(this, e, []),
                                r && r.stop && r.stop.call(this, !0),
                                t = o.length;
                            t--;

                        )
                            o[t].elem === this &&
                                o[t].queue === e &&
                                (o[t].anim.stop(!0), o.splice(t, 1));
                        for (t = 0; a > t; t++) i[t] && i[t].finish && i[t].finish.call(this);
                        delete n.finish;
                    })
                );
            },
        }),
        p.each(['toggle', 'show', 'hide'], function (e, t) {
            var n = p.fn[t];
            p.fn[t] = function (e, i, r) {
                return null == e || 'boolean' == typeof e
                    ? n.apply(this, arguments)
                    : this.animate(st(t, !0), e, i, r);
            };
        }),
        p.each(
            {
                slideDown: st('show'),
                slideUp: st('hide'),
                slideToggle: st('toggle'),
                fadeIn: { opacity: 'show' },
                fadeOut: { opacity: 'hide' },
                fadeToggle: { opacity: 'toggle' },
            },
            function (e, t) {
                p.fn[e] = function (e, n, i) {
                    return this.animate(t, e, n, i);
                };
            },
        ),
        (p.timers = []),
        (p.fx.tick = function () {
            var e,
                t = p.timers,
                n = 0;
            for (Ze = p.now(); n < t.length; n++) (e = t[n]), e() || t[n] !== e || t.splice(n--, 1);
            t.length || p.fx.stop(), (Ze = void 0);
        }),
        (p.fx.timer = function (e) {
            p.timers.push(e), e() ? p.fx.start() : p.timers.pop();
        }),
        (p.fx.interval = 13),
        (p.fx.start = function () {
            et || (et = setInterval(p.fx.tick, p.fx.interval));
        }),
        (p.fx.stop = function () {
            clearInterval(et), (et = null);
        }),
        (p.fx.speeds = { slow: 600, fast: 200, _default: 400 }),
        (p.fn.delay = function (e, t) {
            return (
                (e = (p.fx && p.fx.speeds[e]) || e),
                (t = t || 'fx'),
                this.queue(t, function (t, n) {
                    var i = setTimeout(t, e);
                    n.stop = function () {
                        clearTimeout(i);
                    };
                })
            );
        }),
        (function () {
            var e,
                t,
                n,
                i,
                r = E.createElement('div');
            r.setAttribute('className', 't'),
                (r.innerHTML =
                    "  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>"),
                (e = r.getElementsByTagName('a')[0]),
                (n = E.createElement('select')),
                (i = n.appendChild(E.createElement('option'))),
                (t = r.getElementsByTagName('input')[0]),
                (e.style.cssText = 'top:1px'),
                (d.getSetAttribute = 't' !== r.className),
                (d.style = /top/.test(e.getAttribute('style'))),
                (d.hrefNormalized = '/a' === e.getAttribute('href')),
                (d.checkOn = !!t.value),
                (d.optSelected = i.selected),
                (d.enctype = !!E.createElement('form').enctype),
                (n.disabled = !0),
                (d.optDisabled = !i.disabled),
                (t = E.createElement('input')),
                t.setAttribute('value', ''),
                (d.input = '' === t.getAttribute('value')),
                (t.value = 't'),
                t.setAttribute('type', 'radio'),
                (d.radioValue = 't' === t.value),
                (e = t = n = i = r = null);
        })();
    var ft = /\r/g;
    p.fn.extend({
        val: function (e) {
            var t,
                n,
                i,
                r = this[0];
            return arguments.length
                ? ((i = p.isFunction(e)),
                  this.each(function (n) {
                      var r;
                      1 === this.nodeType &&
                          ((r = i ? e.call(this, n, p(this).val()) : e),
                          null == r
                              ? (r = '')
                              : 'number' == typeof r
                              ? (r += '')
                              : p.isArray(r) &&
                                (r = p.map(r, function (e) {
                                    return null == e ? '' : e + '';
                                })),
                          (t = p.valHooks[this.type] || p.valHooks[this.nodeName.toLowerCase()]),
                          (t && 'set' in t && void 0 !== t.set(this, r, 'value')) ||
                              (this.value = r));
                  }))
                : r
                ? ((t = p.valHooks[r.type] || p.valHooks[r.nodeName.toLowerCase()]),
                  t && 'get' in t && void 0 !== (n = t.get(r, 'value'))
                      ? n
                      : ((n = r.value),
                        'string' == typeof n ? n.replace(ft, '') : null == n ? '' : n))
                : void 0;
        },
    }),
        p.extend({
            valHooks: {
                option: {
                    get: function (e) {
                        var t = p.find.attr(e, 'value');
                        return null != t ? t : p.text(e);
                    },
                },
                select: {
                    get: function (e) {
                        for (
                            var t,
                                n,
                                i = e.options,
                                r = e.selectedIndex,
                                o = 'select-one' === e.type || 0 > r,
                                a = o ? null : [],
                                s = o ? r + 1 : i.length,
                                l = 0 > r ? s : o ? r : 0;
                            s > l;
                            l++
                        )
                            if (
                                ((n = i[l]),
                                !(
                                    (!n.selected && l !== r) ||
                                    (d.optDisabled
                                        ? n.disabled
                                        : null !== n.getAttribute('disabled')) ||
                                    (n.parentNode.disabled && p.nodeName(n.parentNode, 'optgroup'))
                                ))
                            ) {
                                if (((t = p(n).val()), o)) return t;
                                a.push(t);
                            }
                        return a;
                    },
                    set: function (e, t) {
                        var n,
                            i,
                            r = e.options,
                            o = p.makeArray(t),
                            a = r.length;
                        while (a--)
                            if (((i = r[a]), p.inArray(p.valHooks.option.get(i), o) >= 0))
                                try {
                                    i.selected = n = !0;
                                } catch (s) {
                                    i.scrollHeight;
                                }
                            else i.selected = !1;
                        return n || (e.selectedIndex = -1), r;
                    },
                },
            },
        }),
        p.each(['radio', 'checkbox'], function () {
            (p.valHooks[this] = {
                set: function (e, t) {
                    return p.isArray(t) ? (e.checked = p.inArray(p(e).val(), t) >= 0) : void 0;
                },
            }),
                d.checkOn ||
                    (p.valHooks[this].get = function (e) {
                        return null === e.getAttribute('value') ? 'on' : e.value;
                    });
        });
    var pt,
        ht,
        mt = p.expr.attrHandle,
        gt = /^(?:checked|selected)$/i,
        vt = d.getSetAttribute,
        yt = d.input;
    p.fn.extend({
        attr: function (e, t) {
            return J(this, p.attr, e, t, arguments.length > 1);
        },
        removeAttr: function (e) {
            return this.each(function () {
                p.removeAttr(this, e);
            });
        },
    }),
        p.extend({
            attr: function (e, t, n) {
                var i,
                    r,
                    o = e.nodeType;
                if (e && 3 !== o && 8 !== o && 2 !== o)
                    return typeof e.getAttribute === B
                        ? p.prop(e, t, n)
                        : ((1 === o && p.isXMLDoc(e)) ||
                              ((t = t.toLowerCase()),
                              (i = p.attrHooks[t] || (p.expr.match.bool.test(t) ? ht : pt))),
                          void 0 === n
                              ? i && 'get' in i && null !== (r = i.get(e, t))
                                  ? r
                                  : ((r = p.find.attr(e, t)), null == r ? void 0 : r)
                              : null !== n
                              ? i && 'set' in i && void 0 !== (r = i.set(e, n, t))
                                  ? r
                                  : (e.setAttribute(t, n + ''), n)
                              : void p.removeAttr(e, t));
            },
            removeAttr: function (e, t) {
                var n,
                    i,
                    r = 0,
                    o = t && t.match(H);
                if (o && 1 === e.nodeType)
                    while ((n = o[r++]))
                        (i = p.propFix[n] || n),
                            p.expr.match.bool.test(n)
                                ? (yt && vt) || !gt.test(n)
                                    ? (e[i] = !1)
                                    : (e[p.camelCase('default-' + n)] = e[i] = !1)
                                : p.attr(e, n, ''),
                            e.removeAttribute(vt ? n : i);
            },
            attrHooks: {
                type: {
                    set: function (e, t) {
                        if (!d.radioValue && 'radio' === t && p.nodeName(e, 'input')) {
                            var n = e.value;
                            return e.setAttribute('type', t), n && (e.value = n), t;
                        }
                    },
                },
            },
        }),
        (ht = {
            set: function (e, t, n) {
                return (
                    !1 === t
                        ? p.removeAttr(e, n)
                        : (yt && vt) || !gt.test(n)
                        ? e.setAttribute((!vt && p.propFix[n]) || n, n)
                        : (e[p.camelCase('default-' + n)] = e[n] = !0),
                    n
                );
            },
        }),
        p.each(p.expr.match.bool.source.match(/\w+/g), function (e, t) {
            var n = mt[t] || p.find.attr;
            mt[t] =
                (yt && vt) || !gt.test(t)
                    ? function (e, t, i) {
                          var r, o;
                          return (
                              i ||
                                  ((o = mt[t]),
                                  (mt[t] = r),
                                  (r = null != n(e, t, i) ? t.toLowerCase() : null),
                                  (mt[t] = o)),
                              r
                          );
                      }
                    : function (e, t, n) {
                          return n
                              ? void 0
                              : e[p.camelCase('default-' + t)]
                              ? t.toLowerCase()
                              : null;
                      };
        }),
        (yt && vt) ||
            (p.attrHooks.value = {
                set: function (e, t, n) {
                    return p.nodeName(e, 'input')
                        ? void (e.defaultValue = t)
                        : pt && pt.set(e, t, n);
                },
            }),
        vt ||
            ((pt = {
                set: function (e, t, n) {
                    var i = e.getAttributeNode(n);
                    return (
                        i || e.setAttributeNode((i = e.ownerDocument.createAttribute(n))),
                        (i.value = t += ''),
                        'value' === n || t === e.getAttribute(n) ? t : void 0
                    );
                },
            }),
            (mt.id =
                mt.name =
                mt.coords =
                    function (e, t, n) {
                        var i;
                        return n
                            ? void 0
                            : (i = e.getAttributeNode(t)) && '' !== i.value
                            ? i.value
                            : null;
                    }),
            (p.valHooks.button = {
                get: function (e, t) {
                    var n = e.getAttributeNode(t);
                    return n && n.specified ? n.value : void 0;
                },
                set: pt.set,
            }),
            (p.attrHooks.contenteditable = {
                set: function (e, t, n) {
                    pt.set(e, '' !== t && t, n);
                },
            }),
            p.each(['width', 'height'], function (e, t) {
                p.attrHooks[t] = {
                    set: function (e, n) {
                        return '' === n ? (e.setAttribute(t, 'auto'), n) : void 0;
                    },
                };
            })),
        d.style ||
            (p.attrHooks.style = {
                get: function (e) {
                    return e.style.cssText || void 0;
                },
                set: function (e, t) {
                    return (e.style.cssText = t + '');
                },
            });
    var bt = /^(?:input|select|textarea|button|object)$/i,
        xt = /^(?:a|area)$/i;
    p.fn.extend({
        prop: function (e, t) {
            return J(this, p.prop, e, t, arguments.length > 1);
        },
        removeProp: function (e) {
            return (
                (e = p.propFix[e] || e),
                this.each(function () {
                    try {
                        (this[e] = void 0), delete this[e];
                    } catch (t) {}
                })
            );
        },
    }),
        p.extend({
            propFix: { for: 'htmlFor', class: 'className' },
            prop: function (e, t, n) {
                var i,
                    r,
                    o,
                    a = e.nodeType;
                if (e && 3 !== a && 8 !== a && 2 !== a)
                    return (
                        (o = 1 !== a || !p.isXMLDoc(e)),
                        o && ((t = p.propFix[t] || t), (r = p.propHooks[t])),
                        void 0 !== n
                            ? r && 'set' in r && void 0 !== (i = r.set(e, n, t))
                                ? i
                                : (e[t] = n)
                            : r && 'get' in r && null !== (i = r.get(e, t))
                            ? i
                            : e[t]
                    );
            },
            propHooks: {
                tabIndex: {
                    get: function (e) {
                        var t = p.find.attr(e, 'tabindex');
                        return t
                            ? parseInt(t, 10)
                            : bt.test(e.nodeName) || (xt.test(e.nodeName) && e.href)
                            ? 0
                            : -1;
                    },
                },
            },
        }),
        d.hrefNormalized ||
            p.each(['href', 'src'], function (e, t) {
                p.propHooks[t] = {
                    get: function (e) {
                        return e.getAttribute(t, 4);
                    },
                };
            }),
        d.optSelected ||
            (p.propHooks.selected = {
                get: function (e) {
                    var t = e.parentNode;
                    return t && (t.selectedIndex, t.parentNode && t.parentNode.selectedIndex), null;
                },
            }),
        p.each(
            [
                'tabIndex',
                'readOnly',
                'maxLength',
                'cellSpacing',
                'cellPadding',
                'rowSpan',
                'colSpan',
                'useMap',
                'frameBorder',
                'contentEditable',
            ],
            function () {
                p.propFix[this.toLowerCase()] = this;
            },
        ),
        d.enctype || (p.propFix.enctype = 'encoding');
    var wt = /[\t\r\n\f]/g;
    p.fn.extend({
        addClass: function (e) {
            var t,
                n,
                i,
                r,
                o,
                a,
                s = 0,
                l = this.length,
                u = 'string' == typeof e && e;
            if (p.isFunction(e))
                return this.each(function (t) {
                    p(this).addClass(e.call(this, t, this.className));
                });
            if (u)
                for (t = (e || '').match(H) || []; l > s; s++)
                    if (
                        ((n = this[s]),
                        (i =
                            1 === n.nodeType &&
                            (n.className ? (' ' + n.className + ' ').replace(wt, ' ') : ' ')))
                    ) {
                        o = 0;
                        while ((r = t[o++])) i.indexOf(' ' + r + ' ') < 0 && (i += r + ' ');
                        (a = p.trim(i)), n.className !== a && (n.className = a);
                    }
            return this;
        },
        removeClass: function (e) {
            var t,
                n,
                i,
                r,
                o,
                a,
                s = 0,
                l = this.length,
                u = 0 === arguments.length || ('string' == typeof e && e);
            if (p.isFunction(e))
                return this.each(function (t) {
                    p(this).removeClass(e.call(this, t, this.className));
                });
            if (u)
                for (t = (e || '').match(H) || []; l > s; s++)
                    if (
                        ((n = this[s]),
                        (i =
                            1 === n.nodeType &&
                            (n.className ? (' ' + n.className + ' ').replace(wt, ' ') : '')))
                    ) {
                        o = 0;
                        while ((r = t[o++]))
                            while (i.indexOf(' ' + r + ' ') >= 0) i = i.replace(' ' + r + ' ', ' ');
                        (a = e ? p.trim(i) : ''), n.className !== a && (n.className = a);
                    }
            return this;
        },
        toggleClass: function (e, t) {
            var n = typeof e;
            return 'boolean' == typeof t && 'string' === n
                ? t
                    ? this.addClass(e)
                    : this.removeClass(e)
                : this.each(
                      p.isFunction(e)
                          ? function (n) {
                                p(this).toggleClass(e.call(this, n, this.className, t), t);
                            }
                          : function () {
                                if ('string' === n) {
                                    var t,
                                        i = 0,
                                        r = p(this),
                                        o = e.match(H) || [];
                                    while ((t = o[i++]))
                                        r.hasClass(t) ? r.removeClass(t) : r.addClass(t);
                                } else
                                    (n === B || 'boolean' === n) &&
                                        (this.className &&
                                            p._data(this, '__className__', this.className),
                                        (this.className =
                                            this.className || !1 === e
                                                ? ''
                                                : p._data(this, '__className__') || ''));
                            },
                  );
        },
        hasClass: function (e) {
            for (var t = ' ' + e + ' ', n = 0, i = this.length; i > n; n++)
                if (
                    1 === this[n].nodeType &&
                    (' ' + this[n].className + ' ').replace(wt, ' ').indexOf(t) >= 0
                )
                    return !0;
            return !1;
        },
    }),
        p.each(
            'blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu'.split(
                ' ',
            ),
            function (e, t) {
                p.fn[t] = function (e, n) {
                    return arguments.length > 0 ? this.on(t, null, e, n) : this.trigger(t);
                };
            },
        ),
        p.fn.extend({
            hover: function (e, t) {
                return this.mouseenter(e).mouseleave(t || e);
            },
            bind: function (e, t, n) {
                return this.on(e, null, t, n);
            },
            unbind: function (e, t) {
                return this.off(e, null, t);
            },
            delegate: function (e, t, n, i) {
                return this.on(t, e, n, i);
            },
            undelegate: function (e, t, n) {
                return 1 === arguments.length ? this.off(e, '**') : this.off(t, e || '**', n);
            },
        });
    var Tt = p.now(),
        Ct = /\?/,
        Nt =
            /(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;
    (p.parseJSON = function (t) {
        if (e.JSON && e.JSON.parse) return e.JSON.parse(t + '');
        var n,
            i = null,
            r = p.trim(t + '');
        return r &&
            !p.trim(
                r.replace(Nt, function (e, t, r, o) {
                    return n && t && (i = 0), 0 === i ? e : ((n = r || t), (i += !o - !r), '');
                }),
            )
            ? Function('return ' + r)()
            : p.error('Invalid JSON: ' + t);
    }),
        (p.parseXML = function (t) {
            var n, i;
            if (!t || 'string' != typeof t) return null;
            try {
                e.DOMParser
                    ? ((i = new DOMParser()), (n = i.parseFromString(t, 'text/xml')))
                    : ((n = new ActiveXObject('Microsoft.XMLDOM')),
                      (n.async = 'false'),
                      n.loadXML(t));
            } catch (r) {
                n = void 0;
            }
            return (
                (n && n.documentElement && !n.getElementsByTagName('parsererror').length) ||
                    p.error('Invalid XML: ' + t),
                n
            );
        });
    var Et,
        kt,
        St = /#.*$/,
        At = /([?&])_=[^&]*/,
        Dt = /^(.*?):[ \t]*([^\r\n]*)\r?$/gm,
        jt = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/,
        Lt = /^(?:GET|HEAD)$/,
        Ht = /^\/\//,
        qt = /^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,
        _t = {},
        Mt = {},
        Ft = '*/'.concat('*');
    try {
        kt = location.href;
    } catch (sn) {
        (kt = E.createElement('a')), (kt.href = ''), (kt = kt.href);
    }
    function Ot(e) {
        return function (t, n) {
            'string' != typeof t && ((n = t), (t = '*'));
            var i,
                r = 0,
                o = t.toLowerCase().match(H) || [];
            if (p.isFunction(n))
                while ((i = o[r++]))
                    '+' === i.charAt(0)
                        ? ((i = i.slice(1) || '*'), (e[i] = e[i] || []).unshift(n))
                        : (e[i] = e[i] || []).push(n);
        };
    }
    function Bt(e, t, n, i) {
        var r = {},
            o = e === Mt;
        function a(s) {
            var l;
            return (
                (r[s] = !0),
                p.each(e[s] || [], function (e, s) {
                    var u = s(t, n, i);
                    return 'string' != typeof u || o || r[u]
                        ? o
                            ? !(l = u)
                            : void 0
                        : (t.dataTypes.unshift(u), a(u), !1);
                }),
                l
            );
        }
        return a(t.dataTypes[0]) || (!r['*'] && a('*'));
    }
    function Pt(e, t) {
        var n,
            i,
            r = p.ajaxSettings.flatOptions || {};
        for (i in t) void 0 !== t[i] && ((r[i] ? e : n || (n = {}))[i] = t[i]);
        return n && p.extend(!0, e, n), e;
    }
    function Rt(e, t, n) {
        var i,
            r,
            o,
            a,
            s = e.contents,
            l = e.dataTypes;
        while ('*' === l[0])
            l.shift(), void 0 === r && (r = e.mimeType || t.getResponseHeader('Content-Type'));
        if (r)
            for (a in s)
                if (s[a] && s[a].test(r)) {
                    l.unshift(a);
                    break;
                }
        if (l[0] in n) o = l[0];
        else {
            for (a in n) {
                if (!l[0] || e.converters[a + ' ' + l[0]]) {
                    o = a;
                    break;
                }
                i || (i = a);
            }
            o = o || i;
        }
        return o ? (o !== l[0] && l.unshift(o), n[o]) : void 0;
    }
    function Wt(e, t, n, i) {
        var r,
            o,
            a,
            s,
            l,
            u = {},
            c = e.dataTypes.slice();
        if (c[1]) for (a in e.converters) u[a.toLowerCase()] = e.converters[a];
        o = c.shift();
        while (o)
            if (
                (e.responseFields[o] && (n[e.responseFields[o]] = t),
                !l && i && e.dataFilter && (t = e.dataFilter(t, e.dataType)),
                (l = o),
                (o = c.shift()))
            )
                if ('*' === o) o = l;
                else if ('*' !== l && l !== o) {
                    if (((a = u[l + ' ' + o] || u['* ' + o]), !a))
                        for (r in u)
                            if (
                                ((s = r.split(' ')),
                                s[1] === o && (a = u[l + ' ' + s[0]] || u['* ' + s[0]]))
                            ) {
                                !0 === a
                                    ? (a = u[r])
                                    : !0 !== u[r] && ((o = s[0]), c.unshift(s[1]));
                                break;
                            }
                    if (!0 !== a)
                        if (a && e['throws']) t = a(t);
                        else
                            try {
                                t = a(t);
                            } catch (d) {
                                return {
                                    state: 'parsererror',
                                    error: a ? d : 'No conversion from ' + l + ' to ' + o,
                                };
                            }
                }
        return { state: 'success', data: t };
    }
    (Et = qt.exec(kt.toLowerCase()) || []),
        p.extend({
            active: 0,
            lastModified: {},
            etag: {},
            ajaxSettings: {
                url: kt,
                type: 'GET',
                isLocal: jt.test(Et[1]),
                global: !0,
                processData: !0,
                async: !0,
                contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                accepts: {
                    '*': Ft,
                    text: 'text/plain',
                    html: 'text/html',
                    xml: 'application/xml, text/xml',
                    json: 'application/json, text/javascript',
                },
                contents: { xml: /xml/, html: /html/, json: /json/ },
                responseFields: { xml: 'responseXML', text: 'responseText', json: 'responseJSON' },
                converters: {
                    '* text': String,
                    'text html': !0,
                    'text json': p.parseJSON,
                    'text xml': p.parseXML,
                },
                flatOptions: { url: !0, context: !0 },
            },
            ajaxSetup: function (e, t) {
                return t ? Pt(Pt(e, p.ajaxSettings), t) : Pt(p.ajaxSettings, e);
            },
            ajaxPrefilter: Ot(_t),
            ajaxTransport: Ot(Mt),
            ajax: function (e, t) {
                'object' == typeof e && ((t = e), (e = void 0)), (t = t || {});
                var n,
                    i,
                    r,
                    o,
                    a,
                    s,
                    l,
                    u,
                    c = p.ajaxSetup({}, t),
                    d = c.context || c,
                    f = c.context && (d.nodeType || d.jquery) ? p(d) : p.event,
                    h = p.Deferred(),
                    m = p.Callbacks('once memory'),
                    g = c.statusCode || {},
                    v = {},
                    y = {},
                    b = 0,
                    x = 'canceled',
                    w = {
                        readyState: 0,
                        getResponseHeader: function (e) {
                            var t;
                            if (2 === b) {
                                if (!u) {
                                    u = {};
                                    while ((t = Dt.exec(o))) u[t[1].toLowerCase()] = t[2];
                                }
                                t = u[e.toLowerCase()];
                            }
                            return null == t ? null : t;
                        },
                        getAllResponseHeaders: function () {
                            return 2 === b ? o : null;
                        },
                        setRequestHeader: function (e, t) {
                            var n = e.toLowerCase();
                            return b || ((e = y[n] = y[n] || e), (v[e] = t)), this;
                        },
                        overrideMimeType: function (e) {
                            return b || (c.mimeType = e), this;
                        },
                        statusCode: function (e) {
                            var t;
                            if (e)
                                if (2 > b) for (t in e) g[t] = [g[t], e[t]];
                                else w.always(e[w.status]);
                            return this;
                        },
                        abort: function (e) {
                            var t = e || x;
                            return l && l.abort(t), C(0, t), this;
                        },
                    };
                if (
                    ((h.promise(w).complete = m.add),
                    (w.success = w.done),
                    (w.error = w.fail),
                    (c.url = ((e || c.url || kt) + '').replace(St, '').replace(Ht, Et[1] + '//')),
                    (c.type = t.method || t.type || c.method || c.type),
                    (c.dataTypes = p
                        .trim(c.dataType || '*')
                        .toLowerCase()
                        .match(H) || ['']),
                    null == c.crossDomain &&
                        ((n = qt.exec(c.url.toLowerCase())),
                        (c.crossDomain = !(
                            !n ||
                            (n[1] === Et[1] &&
                                n[2] === Et[2] &&
                                (n[3] || ('http:' === n[1] ? '80' : '443')) ===
                                    (Et[3] || ('http:' === Et[1] ? '80' : '443')))
                        ))),
                    c.data &&
                        c.processData &&
                        'string' != typeof c.data &&
                        (c.data = p.param(c.data, c.traditional)),
                    Bt(_t, c, t, w),
                    2 === b)
                )
                    return w;
                for (i in ((s = c.global),
                s && 0 === p.active++ && p.event.trigger('ajaxStart'),
                (c.type = c.type.toUpperCase()),
                (c.hasContent = !Lt.test(c.type)),
                (r = c.url),
                c.hasContent ||
                    (c.data && ((r = c.url += (Ct.test(r) ? '&' : '?') + c.data), delete c.data),
                    !1 === c.cache &&
                        (c.url = At.test(r)
                            ? r.replace(At, '$1_=' + Tt++)
                            : r + (Ct.test(r) ? '&' : '?') + '_=' + Tt++)),
                c.ifModified &&
                    (p.lastModified[r] &&
                        w.setRequestHeader('If-Modified-Since', p.lastModified[r]),
                    p.etag[r] && w.setRequestHeader('If-None-Match', p.etag[r])),
                ((c.data && c.hasContent && !1 !== c.contentType) || t.contentType) &&
                    w.setRequestHeader('Content-Type', c.contentType),
                w.setRequestHeader(
                    'Accept',
                    c.dataTypes[0] && c.accepts[c.dataTypes[0]]
                        ? c.accepts[c.dataTypes[0]] +
                              ('*' !== c.dataTypes[0] ? ', ' + Ft + '; q=0.01' : '')
                        : c.accepts['*'],
                ),
                c.headers))
                    w.setRequestHeader(i, c.headers[i]);
                if (c.beforeSend && (!1 === c.beforeSend.call(d, w, c) || 2 === b))
                    return w.abort();
                for (i in ((x = 'abort'), { success: 1, error: 1, complete: 1 })) w[i](c[i]);
                if ((l = Bt(Mt, c, t, w))) {
                    (w.readyState = 1),
                        s && f.trigger('ajaxSend', [w, c]),
                        c.async &&
                            c.timeout > 0 &&
                            (a = setTimeout(function () {
                                w.abort('timeout');
                            }, c.timeout));
                    try {
                        (b = 1), l.send(v, C);
                    } catch (T) {
                        if (!(2 > b)) throw T;
                        C(-1, T);
                    }
                } else C(-1, 'No Transport');
                function C(e, t, n, i) {
                    var u,
                        v,
                        y,
                        x,
                        T,
                        C = t;
                    2 !== b &&
                        ((b = 2),
                        a && clearTimeout(a),
                        (l = void 0),
                        (o = i || ''),
                        (w.readyState = e > 0 ? 4 : 0),
                        (u = (e >= 200 && 300 > e) || 304 === e),
                        n && (x = Rt(c, w, n)),
                        (x = Wt(c, x, w, u)),
                        u
                            ? (c.ifModified &&
                                  ((T = w.getResponseHeader('Last-Modified')),
                                  T && (p.lastModified[r] = T),
                                  (T = w.getResponseHeader('etag')),
                                  T && (p.etag[r] = T)),
                              204 === e || 'HEAD' === c.type
                                  ? (C = 'nocontent')
                                  : 304 === e
                                  ? (C = 'notmodified')
                                  : ((C = x.state), (v = x.data), (y = x.error), (u = !y)))
                            : ((y = C), (e || !C) && ((C = 'error'), 0 > e && (e = 0))),
                        (w.status = e),
                        (w.statusText = (t || C) + ''),
                        u ? h.resolveWith(d, [v, C, w]) : h.rejectWith(d, [w, C, y]),
                        w.statusCode(g),
                        (g = void 0),
                        s && f.trigger(u ? 'ajaxSuccess' : 'ajaxError', [w, c, u ? v : y]),
                        m.fireWith(d, [w, C]),
                        s &&
                            (f.trigger('ajaxComplete', [w, c]),
                            --p.active || p.event.trigger('ajaxStop')));
                }
                return w;
            },
            getJSON: function (e, t, n) {
                return p.get(e, t, n, 'json');
            },
            getScript: function (e, t) {
                return p.get(e, void 0, t, 'script');
            },
        }),
        p.each(['get', 'post'], function (e, t) {
            p[t] = function (e, n, i, r) {
                return (
                    p.isFunction(n) && ((r = r || i), (i = n), (n = void 0)),
                    p.ajax({ url: e, type: t, dataType: r, data: n, success: i })
                );
            };
        }),
        p.each(
            ['ajaxStart', 'ajaxStop', 'ajaxComplete', 'ajaxError', 'ajaxSuccess', 'ajaxSend'],
            function (e, t) {
                p.fn[t] = function (e) {
                    return this.on(t, e);
                };
            },
        ),
        (p._evalUrl = function (e) {
            return p.ajax({
                url: e,
                type: 'GET',
                dataType: 'script',
                async: !1,
                global: !1,
                throws: !0,
            });
        }),
        p.fn.extend({
            wrapAll: function (e) {
                if (p.isFunction(e))
                    return this.each(function (t) {
                        p(this).wrapAll(e.call(this, t));
                    });
                if (this[0]) {
                    var t = p(e, this[0].ownerDocument).eq(0).clone(!0);
                    this[0].parentNode && t.insertBefore(this[0]),
                        t
                            .map(function () {
                                var e = this;
                                while (e.firstChild && 1 === e.firstChild.nodeType)
                                    e = e.firstChild;
                                return e;
                            })
                            .append(this);
                }
                return this;
            },
            wrapInner: function (e) {
                return this.each(
                    p.isFunction(e)
                        ? function (t) {
                              p(this).wrapInner(e.call(this, t));
                          }
                        : function () {
                              var t = p(this),
                                  n = t.contents();
                              n.length ? n.wrapAll(e) : t.append(e);
                          },
                );
            },
            wrap: function (e) {
                var t = p.isFunction(e);
                return this.each(function (n) {
                    p(this).wrapAll(t ? e.call(this, n) : e);
                });
            },
            unwrap: function () {
                return this.parent()
                    .each(function () {
                        p.nodeName(this, 'body') || p(this).replaceWith(this.childNodes);
                    })
                    .end();
            },
        }),
        (p.expr.filters.hidden = function (e) {
            return (
                (e.offsetWidth <= 0 && e.offsetHeight <= 0) ||
                (!d.reliableHiddenOffsets() &&
                    'none' === ((e.style && e.style.display) || p.css(e, 'display')))
            );
        }),
        (p.expr.filters.visible = function (e) {
            return !p.expr.filters.hidden(e);
        });
    var $t = /%20/g,
        zt = /\[\]$/,
        It = /\r?\n/g,
        Xt = /^(?:submit|button|image|reset|file)$/i,
        Ut = /^(?:input|select|textarea|keygen)/i;
    function Vt(e, t, n, i) {
        var r;
        if (p.isArray(t))
            p.each(t, function (t, r) {
                n || zt.test(e)
                    ? i(e, r)
                    : Vt(e + '[' + ('object' == typeof r ? t : '') + ']', r, n, i);
            });
        else if (n || 'object' !== p.type(t)) i(e, t);
        else for (r in t) Vt(e + '[' + r + ']', t[r], n, i);
    }
    (p.param = function (e, t) {
        var n,
            i = [],
            r = function (e, t) {
                (t = p.isFunction(t) ? t() : null == t ? '' : t),
                    (i[i.length] = encodeURIComponent(e) + '=' + encodeURIComponent(t));
            };
        if (
            (void 0 === t && (t = p.ajaxSettings && p.ajaxSettings.traditional),
            p.isArray(e) || (e.jquery && !p.isPlainObject(e)))
        )
            p.each(e, function () {
                r(this.name, this.value);
            });
        else for (n in e) Vt(n, e[n], t, r);
        return i.join('&').replace($t, '+');
    }),
        p.fn.extend({
            serialize: function () {
                return p.param(this.serializeArray());
            },
            serializeArray: function () {
                return this.map(function () {
                    var e = p.prop(this, 'elements');
                    return e ? p.makeArray(e) : this;
                })
                    .filter(function () {
                        var e = this.type;
                        return (
                            this.name &&
                            !p(this).is(':disabled') &&
                            Ut.test(this.nodeName) &&
                            !Xt.test(e) &&
                            (this.checked || !Y.test(e))
                        );
                    })
                    .map(function (e, t) {
                        var n = p(this).val();
                        return null == n
                            ? null
                            : p.isArray(n)
                            ? p.map(n, function (e) {
                                  return { name: t.name, value: e.replace(It, '\r\n') };
                              })
                            : { name: t.name, value: n.replace(It, '\r\n') };
                    })
                    .get();
            },
        }),
        (p.ajaxSettings.xhr =
            void 0 !== e.ActiveXObject
                ? function () {
                      return (
                          (!this.isLocal &&
                              /^(get|post|head|put|delete|options)$/i.test(this.type) &&
                              Qt()) ||
                          Kt()
                      );
                  }
                : Qt);
    var Jt = 0,
        Yt = {},
        Gt = p.ajaxSettings.xhr();
    function Qt() {
        try {
            return new e.XMLHttpRequest();
        } catch (t) {}
    }
    function Kt() {
        try {
            return new e.ActiveXObject('Microsoft.XMLHTTP');
        } catch (t) {}
    }
    e.ActiveXObject &&
        p(e).on('unload', function () {
            for (var e in Yt) Yt[e](void 0, !0);
        }),
        (d.cors = !!Gt && 'withCredentials' in Gt),
        (Gt = d.ajax = !!Gt),
        Gt &&
            p.ajaxTransport(function (e) {
                var t;
                if (!e.crossDomain || d.cors)
                    return {
                        send: function (n, i) {
                            var r,
                                o = e.xhr(),
                                a = ++Jt;
                            if (
                                (o.open(e.type, e.url, e.async, e.username, e.password),
                                e.xhrFields)
                            )
                                for (r in e.xhrFields) o[r] = e.xhrFields[r];
                            for (r in (e.mimeType &&
                                o.overrideMimeType &&
                                o.overrideMimeType(e.mimeType),
                            e.crossDomain ||
                                n['X-Requested-With'] ||
                                (n['X-Requested-With'] = 'XMLHttpRequest'),
                            n))
                                void 0 !== n[r] && o.setRequestHeader(r, n[r] + '');
                            o.send((e.hasContent && e.data) || null),
                                (t = function (n, r) {
                                    var s, l, u;
                                    if (t && (r || 4 === o.readyState))
                                        if (
                                            (delete Yt[a],
                                            (t = void 0),
                                            (o.onreadystatechange = p.noop),
                                            r)
                                        )
                                            4 !== o.readyState && o.abort();
                                        else {
                                            (u = {}),
                                                (s = o.status),
                                                'string' == typeof o.responseText &&
                                                    (u.text = o.responseText);
                                            try {
                                                l = o.statusText;
                                            } catch (c) {
                                                l = '';
                                            }
                                            s || !e.isLocal || e.crossDomain
                                                ? 1223 === s && (s = 204)
                                                : (s = u.text ? 200 : 404);
                                        }
                                    u && i(s, l, u, o.getAllResponseHeaders());
                                }),
                                e.async
                                    ? 4 === o.readyState
                                        ? setTimeout(t)
                                        : (o.onreadystatechange = Yt[a] = t)
                                    : t();
                        },
                        abort: function () {
                            t && t(void 0, !0);
                        },
                    };
            }),
        p.ajaxSetup({
            accepts: {
                script: 'text/javascript, application/javascript, application/ecmascript, application/x-ecmascript',
            },
            contents: { script: /(?:java|ecma)script/ },
            converters: {
                'text script': function (e) {
                    return p.globalEval(e), e;
                },
            },
        }),
        p.ajaxPrefilter('script', function (e) {
            void 0 === e.cache && (e.cache = !1),
                e.crossDomain && ((e.type = 'GET'), (e.global = !1));
        }),
        p.ajaxTransport('script', function (e) {
            if (e.crossDomain) {
                var t,
                    n = E.head || p('head')[0] || E.documentElement;
                return {
                    send: function (i, r) {
                        (t = E.createElement('script')),
                            (t.async = !0),
                            e.scriptCharset && (t.charset = e.scriptCharset),
                            (t.src = e.url),
                            (t.onload = t.onreadystatechange =
                                function (e, n) {
                                    (n || !t.readyState || /loaded|complete/.test(t.readyState)) &&
                                        ((t.onload = t.onreadystatechange = null),
                                        t.parentNode && t.parentNode.removeChild(t),
                                        (t = null),
                                        n || r(200, 'success'));
                                }),
                            n.insertBefore(t, n.firstChild);
                    },
                    abort: function () {
                        t && t.onload(void 0, !0);
                    },
                };
            }
        });
    var Zt = [],
        en = /(=)\?(?=&|$)|\?\?/;
    p.ajaxSetup({
        jsonp: 'callback',
        jsonpCallback: function () {
            var e = Zt.pop() || p.expando + '_' + Tt++;
            return (this[e] = !0), e;
        },
    }),
        p.ajaxPrefilter('json jsonp', function (t, n, i) {
            var r,
                o,
                a,
                s =
                    !1 !== t.jsonp &&
                    (en.test(t.url)
                        ? 'url'
                        : 'string' == typeof t.data &&
                          !(t.contentType || '').indexOf('application/x-www-form-urlencoded') &&
                          en.test(t.data) &&
                          'data');
            return s || 'jsonp' === t.dataTypes[0]
                ? ((r = t.jsonpCallback =
                      p.isFunction(t.jsonpCallback) ? t.jsonpCallback() : t.jsonpCallback),
                  s
                      ? (t[s] = t[s].replace(en, '$1' + r))
                      : !1 !== t.jsonp &&
                        (t.url += (Ct.test(t.url) ? '&' : '?') + t.jsonp + '=' + r),
                  (t.converters['script json'] = function () {
                      return a || p.error(r + ' was not called'), a[0];
                  }),
                  (t.dataTypes[0] = 'json'),
                  (o = e[r]),
                  (e[r] = function () {
                      a = arguments;
                  }),
                  i.always(function () {
                      (e[r] = o),
                          t[r] && ((t.jsonpCallback = n.jsonpCallback), Zt.push(r)),
                          a && p.isFunction(o) && o(a[0]),
                          (a = o = void 0);
                  }),
                  'script')
                : void 0;
        }),
        (p.parseHTML = function (e, t, n) {
            if (!e || 'string' != typeof e) return null;
            'boolean' == typeof t && ((n = t), (t = !1)), (t = t || E);
            var i = w.exec(e),
                r = !n && [];
            return i
                ? [t.createElement(i[1])]
                : ((i = p.buildFragment([e], t, r)),
                  r && r.length && p(r).remove(),
                  p.merge([], i.childNodes));
        });
    var tn = p.fn.load;
    (p.fn.load = function (e, t, n) {
        if ('string' != typeof e && tn) return tn.apply(this, arguments);
        var i,
            r,
            o,
            a = this,
            s = e.indexOf(' ');
        return (
            s >= 0 && ((i = e.slice(s, e.length)), (e = e.slice(0, s))),
            p.isFunction(t) ? ((n = t), (t = void 0)) : t && 'object' == typeof t && (o = 'POST'),
            a.length > 0 &&
                p
                    .ajax({ url: e, type: o, dataType: 'html', data: t })
                    .done(function (e) {
                        (r = arguments), a.html(i ? p('<div>').append(p.parseHTML(e)).find(i) : e);
                    })
                    .complete(
                        n &&
                            function (e, t) {
                                a.each(n, r || [e.responseText, t, e]);
                            },
                    ),
            this
        );
    }),
        (p.expr.filters.animated = function (e) {
            return p.grep(p.timers, function (t) {
                return e === t.elem;
            }).length;
        });
    var nn = e.document.documentElement;
    function rn(e) {
        return p.isWindow(e) ? e : 9 === e.nodeType && (e.defaultView || e.parentWindow);
    }
    (p.offset = {
        setOffset: function (e, t, n) {
            var i,
                r,
                o,
                a,
                s,
                l,
                u,
                c = p.css(e, 'position'),
                d = p(e),
                f = {};
            'static' === c && (e.style.position = 'relative'),
                (s = d.offset()),
                (o = p.css(e, 'top')),
                (l = p.css(e, 'left')),
                (u = ('absolute' === c || 'fixed' === c) && p.inArray('auto', [o, l]) > -1),
                u
                    ? ((i = d.position()), (a = i.top), (r = i.left))
                    : ((a = parseFloat(o) || 0), (r = parseFloat(l) || 0)),
                p.isFunction(t) && (t = t.call(e, n, s)),
                null != t.top && (f.top = t.top - s.top + a),
                null != t.left && (f.left = t.left - s.left + r),
                'using' in t ? t.using.call(e, f) : d.css(f);
        },
    }),
        p.fn.extend({
            offset: function (e) {
                if (arguments.length)
                    return void 0 === e
                        ? this
                        : this.each(function (t) {
                              p.offset.setOffset(this, e, t);
                          });
                var t,
                    n,
                    i = { top: 0, left: 0 },
                    r = this[0],
                    o = r && r.ownerDocument;
                return o
                    ? ((t = o.documentElement),
                      p.contains(t, r)
                          ? (typeof r.getBoundingClientRect !== B &&
                                (i = r.getBoundingClientRect()),
                            (n = rn(o)),
                            {
                                top: i.top + (n.pageYOffset || t.scrollTop) - (t.clientTop || 0),
                                left:
                                    i.left + (n.pageXOffset || t.scrollLeft) - (t.clientLeft || 0),
                            })
                          : i)
                    : void 0;
            },
            position: function () {
                if (this[0]) {
                    var e,
                        t,
                        n = { top: 0, left: 0 },
                        i = this[0];
                    return (
                        'fixed' === p.css(i, 'position')
                            ? (t = i.getBoundingClientRect())
                            : ((e = this.offsetParent()),
                              (t = this.offset()),
                              p.nodeName(e[0], 'html') || (n = e.offset()),
                              (n.top += p.css(e[0], 'borderTopWidth', !0)),
                              (n.left += p.css(e[0], 'borderLeftWidth', !0))),
                        {
                            top: t.top - n.top - p.css(i, 'marginTop', !0),
                            left: t.left - n.left - p.css(i, 'marginLeft', !0),
                        }
                    );
                }
            },
            offsetParent: function () {
                return this.map(function () {
                    var e = this.offsetParent || nn;
                    while (e && !p.nodeName(e, 'html') && 'static' === p.css(e, 'position'))
                        e = e.offsetParent;
                    return e || nn;
                });
            },
        }),
        p.each({ scrollLeft: 'pageXOffset', scrollTop: 'pageYOffset' }, function (e, t) {
            var n = /Y/.test(t);
            p.fn[e] = function (i) {
                return J(
                    this,
                    function (e, i, r) {
                        var o = rn(e);
                        return void 0 === r
                            ? o
                                ? t in o
                                    ? o[t]
                                    : o.document.documentElement[i]
                                : e[i]
                            : void (o
                                  ? o.scrollTo(n ? p(o).scrollLeft() : r, n ? r : p(o).scrollTop())
                                  : (e[i] = r));
                    },
                    e,
                    i,
                    arguments.length,
                    null,
                );
            };
        }),
        p.each(['top', 'left'], function (e, t) {
            p.cssHooks[t] = Be(d.pixelPosition, function (e, n) {
                return n ? ((n = _e(e, t)), Fe.test(n) ? p(e).position()[t] + 'px' : n) : void 0;
            });
        }),
        p.each({ Height: 'height', Width: 'width' }, function (e, t) {
            p.each({ padding: 'inner' + e, content: t, '': 'outer' + e }, function (n, i) {
                p.fn[i] = function (i, r) {
                    var o = arguments.length && (n || 'boolean' != typeof i),
                        a = n || (!0 === i || !0 === r ? 'margin' : 'border');
                    return J(
                        this,
                        function (t, n, i) {
                            var r;
                            return p.isWindow(t)
                                ? t.document.documentElement['client' + e]
                                : 9 === t.nodeType
                                ? ((r = t.documentElement),
                                  Math.max(
                                      t.body['scroll' + e],
                                      r['scroll' + e],
                                      t.body['offset' + e],
                                      r['offset' + e],
                                      r['client' + e],
                                  ))
                                : void 0 === i
                                ? p.css(t, n, a)
                                : p.style(t, n, i, a);
                        },
                        t,
                        o ? i : void 0,
                        o,
                        null,
                    );
                };
            });
        }),
        (p.fn.size = function () {
            return this.length;
        }),
        (p.fn.andSelf = p.fn.addBack),
        'function' == typeof define &&
            define.amd &&
            define('jquery', [], function () {
                return p;
            });
    var on = e.jQuery,
        an = e.$;
    return (
        (p.noConflict = function (t) {
            return e.$ === p && (e.$ = an), t && e.jQuery === p && (e.jQuery = on), p;
        }),
        typeof t === B && (e.jQuery = e.$ = p),
        p
    );
});
