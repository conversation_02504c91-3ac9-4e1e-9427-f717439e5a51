module.exports = (function (t) {
    var e = {};
    function n(r) {
        if (e[r]) return e[r].exports;
        var o = (e[r] = { i: r, l: !1, exports: {} });
        return t[r].call(o.exports, o, o.exports, n), (o.l = !0), o.exports;
    }
    return (
        (n.m = t),
        (n.c = e),
        (n.d = function (t, e, r) {
            n.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: r });
        }),
        (n.r = function (t) {
            'undefined' !== typeof Symbol &&
                Symbol.toStringTag &&
                Object.defineProperty(t, Symbol.toStringTag, { value: 'Module' }),
                Object.defineProperty(t, '__esModule', { value: !0 });
        }),
        (n.t = function (t, e) {
            if ((1 & e && (t = n(t)), 8 & e)) return t;
            if (4 & e && 'object' === typeof t && t && t.__esModule) return t;
            var r = Object.create(null);
            if (
                (n.r(r),
                Object.defineProperty(r, 'default', { enumerable: !0, value: t }),
                2 & e && 'string' != typeof t)
            )
                for (var o in t)
                    n.d(
                        r,
                        o,
                        function (e) {
                            return t[e];
                        }.bind(null, o),
                    );
            return r;
        }),
        (n.n = function (t) {
            var e =
                t && t.__esModule
                    ? function () {
                          return t['default'];
                      }
                    : function () {
                          return t;
                      };
            return n.d(e, 'a', e), e;
        }),
        (n.o = function (t, e) {
            return Object.prototype.hasOwnProperty.call(t, e);
        }),
        (n.p = ''),
        n((n.s = 'fb15'))
    );
})({
    '1b8a': function (t, e, n) {},
    4529: function (t, e, n) {},
    8875: function (t, e, n) {
        var r, o, i;
        (function (n, c) {
            (o = []),
                (r = c),
                (i = 'function' === typeof r ? r.apply(e, o) : r),
                void 0 === i || (t.exports = i);
        })('undefined' !== typeof self && self, function () {
            function t() {
                var e = Object.getOwnPropertyDescriptor(document, 'currentScript');
                if (!e && 'currentScript' in document && document.currentScript)
                    return document.currentScript;
                if (e && e.get !== t && document.currentScript) return document.currentScript;
                try {
                    throw new Error();
                } catch (p) {
                    var n,
                        r,
                        o,
                        i = /.*at [^(]*\((.*):(.+):(.+)\)$/gi,
                        c = /@([^@]*):(\d+):(\d+)\s*$/gi,
                        u = i.exec(p.stack) || c.exec(p.stack),
                        a = (u && u[1]) || !1,
                        s = (u && u[2]) || !1,
                        l = document.location.href.replace(document.location.hash, ''),
                        d = document.getElementsByTagName('script');
                    a === l &&
                        ((n = document.documentElement.outerHTML),
                        (r = new RegExp(
                            '(?:[^\\n]+?\\n){0,' +
                                (s - 2) +
                                '}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*',
                            'i',
                        )),
                        (o = n.replace(r, '$1').trim()));
                    for (var f = 0; f < d.length; f++) {
                        if ('interactive' === d[f].readyState) return d[f];
                        if (d[f].src === a) return d[f];
                        if (a === l && d[f].innerHTML && d[f].innerHTML.trim() === o) return d[f];
                    }
                    return null;
                }
            }
            return t;
        });
    },
    e3a7: function (t, e, n) {
        'use strict';
        var r = n('1b8a'),
            o = n.n(r);
        o.a;
    },
    fb15: function (t, e, n) {
        'use strict';
        if ((n.r(e), 'undefined' !== typeof window)) {
            var r = window.document.currentScript,
                o = n('8875');
            (r = o()),
                'currentScript' in document ||
                    Object.defineProperty(document, 'currentScript', { get: o });
            var i = r && r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);
            i && (n.p = i[1]);
        }
        n('4529');
        var c = function () {
                var t = this,
                    e = t.$createElement,
                    n = t._self._c || e;
                return n(
                    'button',
                    { staticClass: 'up-button', on: { click: t.handleClick } },
                    [t._t('default')],
                    2,
                );
            },
            u = [];
        const a = navigator.userAgent.toLowerCase();
        function s(t) {
            return '[object Function]' === Object.prototype.toString.call(t);
        }
        function l(t) {
            return t
                ? t.resultString
                    ? t.resultString
                    : t.resultParams
                    ? t.resultParams
                    : void 0
                : t;
        }
        const d = /\(securitywebcache\s([\d\.]+)\)/g.test(a),
            f = /\(cordova\s([\d\.]+)\)/g.test(a);
        function p(t, e, n, r, o) {
            let i = !1;
            return (
                f && window.cordova
                    ? ((i = !0), window.cordova.exec(t, e, n, r, o))
                    : d &&
                      window.WebViewJavascriptBridge &&
                      ((i = !0),
                      window.WebViewJavascriptBridge.callHandler(
                          n,
                          r,
                          o,
                          function (e) {
                              s(t) && t(l(e));
                          },
                          function (t) {
                              s(e) && e(l(t));
                          },
                      )),
                i
            );
        }
        var m = {
                name: 'UPButton',
                props: { scope: String, timeout: Number },
                methods: {
                    appletExplicitAuth(t, e) {
                        let n = p(
                            function (e) {
                                'function' === typeof t && t(e);
                            },
                            function (t) {
                                if ('function' === typeof e) {
                                    let n;
                                    if (window.cordova)
                                        switch (window.cordova.errorRetStatus) {
                                            case window.cordova.callbackStatus.INVALID_ACTION:
                                                n = {
                                                    errcode: 'c03',
                                                    errmsg: 'INVALID_ACTION_EXCEPTION: 插件里面没有此方法！',
                                                };
                                                break;
                                            case window.cordova.callbackStatus
                                                .CLASS_NOT_FOUND_EXCEPTION:
                                                n = {
                                                    errcode: 'c04',
                                                    errmsg: 'CLASS_NOT_FOUND_EXCEPTION: 此插件没有实现！',
                                                };
                                                break;
                                            case window.cordova.callbackStatus
                                                .ILLEGAL_ACCESS_EXCEPTION:
                                                n = {
                                                    errcode: 'c02',
                                                    errmsg: 'ILLEGAL_ACCESS_EXCEPTION: 无权限访问此插件！',
                                                };
                                                break;
                                            default:
                                                break;
                                        }
                                    e(n || t);
                                }
                            },
                            'UPWebSdk',
                            'appletExplicitAuth',
                            [{ scope: this.scope }],
                        );
                        n ||
                            setTimeout(() => {
                                this._count++,
                                    this._count > this._timeout / 20
                                        ? (console.warn(
                                              '请确定是否运行在云闪付APP中,且成功加载了upsdk.js',
                                          ),
                                          e({
                                              errcode: '__ENV__10001',
                                              errmsg: '检测到未在云闪付APP中运行或未成功加载upsdk.js',
                                          }))
                                        : this.appletExplicitAuth(t, e);
                            }, 20);
                    },
                    handleClick(t) {
                        this.btnDisable ||
                            (this.timeout && isNaN(parseInt(this.timeout))
                                ? this.$emit('click', t, {
                                      errcode: '__ENV__10002',
                                      errmsg: '检测到timeout值非法',
                                  })
                                : ((this._count = 0),
                                  (this._timeout = this.timeout || 2e3),
                                  (this.btnDisable = !0),
                                  this.appletExplicitAuth(
                                      (e) => {
                                          console.log('获取授权成功', t, e),
                                              (this.btnDisable = !1),
                                              this.$emit('click', null, e);
                                      },
                                      (e) => {
                                          console.log('获取授权失败', t, e),
                                              (this.btnDisable = !1),
                                              this.$emit('click', e);
                                      },
                                  )));
                    },
                },
            },
            _ = m;
        n('e3a7');
        function h(t, e, n, r, o, i, c, u) {
            var a,
                s = 'function' === typeof t ? t.options : t;
            if (
                (e && ((s.render = e), (s.staticRenderFns = n), (s._compiled = !0)),
                r && (s.functional = !0),
                i && (s._scopeId = 'data-v-' + i),
                c
                    ? ((a = function (t) {
                          (t =
                              t ||
                              (this.$vnode && this.$vnode.ssrContext) ||
                              (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext)),
                              t ||
                                  'undefined' === typeof __VUE_SSR_CONTEXT__ ||
                                  (t = __VUE_SSR_CONTEXT__),
                              o && o.call(this, t),
                              t && t._registeredComponents && t._registeredComponents.add(c);
                      }),
                      (s._ssrRegister = a))
                    : o &&
                      (a = u
                          ? function () {
                                o.call(
                                    this,
                                    (s.functional ? this.parent : this).$root.$options.shadowRoot,
                                );
                            }
                          : o),
                a)
            )
                if (s.functional) {
                    s._injectStyles = a;
                    var l = s.render;
                    s.render = function (t, e) {
                        return a.call(e), l(t, e);
                    };
                } else {
                    var d = s.beforeCreate;
                    s.beforeCreate = d ? [].concat(d, a) : [a];
                }
            return { exports: t, options: s };
        }
        var b = h(_, c, u, !1, null, '78962954', null),
            v = b.exports;
        v.install = function (t) {
            t.component(v.name, v);
        };
        var w = v;
        const S = [w],
            g = function (t, e = {}) {
                S.forEach((e) => {
                    g.installed || t.component(e.name, e);
                });
            };
        'undefined' !== typeof window && window.Vue && g(window.Vue);
        var E = { version: '0.1.0', install: g, UPButton: w };
        e['default'] = E;
    },
});
