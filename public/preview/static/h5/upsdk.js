!(function () {
    function e() {}
    function t(e) {
        return '[object Function]' === Object.prototype.toString.call(e);
    }
    function n(e) {
        return e instanceof Array;
    }
    function o(e) {
        return (
            (n = e),
            '[object Object]' === Object.prototype.toString.call(n) &&
                !(null != (t = e) && t == t.window) &&
                e.__proto__ == Object.prototype
        );
        var t, n;
    }
    function a(e, t, n) {
        return [].indexOf.call(t, e, n);
    }
    function i(e, t) {
        var n, o;
        if ('number' == typeof e.length) {
            for (n = 0; n < e.length; n++) if (!1 === t.call(e[n], n, e[n])) return e;
        } else for (o in e) if (!1 === t.call(e[o], o, e[o])) return e;
        return e;
    }
    function c(e) {
        var t,
            a = [].slice.call(arguments, 1);
        return (
            'boolean' == typeof e && ((t = e), (e = a.shift())),
            a.forEach(function (a) {
                !(function e(t, a, i) {
                    for (var c in a)
                        i && (o(a[c]) || n(a[c]))
                            ? (o(a[c]) && !o(t[c]) && (t[c] = {}),
                              n(a[c]) && !n(t[c]) && (t[c] = []),
                              e(t[c], a[c], i))
                            : void 0 !== a[c] && (t[c] = a[c]);
                })(e, a, t);
            }),
            e
        );
    }
    var r,
        l,
        s,
        u,
        g,
        d,
        p = navigator.userAgent.toLowerCase(),
        f =
            new RegExp(/(com.unionpay.chsp)/).test(p) ||
            new RegExp(/(com.unionpay.mobilepay)/).test(p),
        h = new RegExp(/iphone|ipad|ipod/).test(p),
        P = [],
        v = !1,
        C = !1,
        U = !1,
        S = [],
        B = {},
        m = !1,
        L = /\(version\s(\d+)\)/g.exec(p),
        b = n(L) && 2 <= L.length && L[1],
        A = /\(com.unionpay.upcardcollector\)/.test(p),
        E = {
            ESDK_BAD_PARAMS: { errcode: 'c00', errmsg: '参数错误' },
            ESDK_CONFIG_FAILED: { errcode: 'c01', errmsg: '签名未通过, 不能访问插件' },
            ESDK_PLUGIN_ILLEGAL_ACCESS: {
                errcode: 'c02',
                errmsg: 'ILLEGAL_ACCESS_EXCEPTION: 无权限访问此插件！',
            },
            ESDK_PLUGIN_INVALID_ACTION: {
                errcode: 'c03',
                errmsg: 'INVALID_ACTION_EXCEPTION: 插件里面没有此方法！',
            },
            ESDK_PLUGIN_CLASS_NOT_FOUND: {
                errcode: 'c04',
                errmsg: 'CLASS_NOT_FOUND_EXCEPTION: 此插件没有实现！',
            },
            ESDK_NEED_CONFIG_DONE: { errcode: 'c05', errmsg: 'config执行成功以后才能调用插件方法' },
            ESDK_NOT_IN_WALLET: { errcode: 'c101', errmsg: 'upsdk.js必须被云闪付加载' },
            ESDK_NEED_NEW_VERSION: {
                errcode: 'c102',
                errmsg: '您手机上云闪付版本太低,请安装新版本!',
            },
        };
    (window.upsdk = window.upsdk || {}),
        (window.upsdk.isInsideWallet = f),
        (window.upsdk.checkSdkSupport = !!b && '422' <= b),
        (window.upsdk = window.upsdk || {}),
        i(
            [
                'pay',
                'addBankCard',
                'setNavigationBarTitle',
                'setNavigationBarRightButton',
                'closeWebApp',
                'showFlashInfo',
                'scanQRCode',
                'chooseImage',
                'getLocationCity',
                'getLocationGps',
                'verifyPayPwd',
                'showSharePopup',
                'chooseFileFromAlbum',
                'readAlbumData',
                'startAudioRecording',
                'stopAudioRecording',
                'readAudioRecordingData',
                'startVideoRecording',
                'readVideoRecordingData',
                'readFaceVideoData',
                'readFaceImageData',
                'noteInfoChange',
                'openRNPage',
                'navi',
                'setScreenBrightness',
                'getScreenBrightness',
                'changeScreenShot',
                'monitorScreenShot',
                'removeScreenShot',
                'addCommonApp',
                'logEvent',
                'openBluetoothAdapter',
                'closeBluetoothAdapter',
                'getBluetoothAdapterState',
                'startBluetoothDevicesDiscovery',
                'stopBluetoothDevicesDiscovery',
                'getBluetoothDevices',
                'getConnectedBluetoothDevices',
                'connectBLEDevice',
                'disconnectBLEDevice',
                'writeBLECharacteristicValue',
                'readBLECharacteristicValue',
                'notifyBLECharacteristicValueChange',
                'getBLEDeviceServices',
                'getBLEDeviceCharacteristics',
                'registerBluetoothDeviceFound',
                'cancelBluetoothDeviceFound',
                'registerBLEConnectionStateChange',
                'cancelBLEConnectionStateChange',
                'registerBLECharacteristicValueChange',
                'cancelBLECharacteristicValueChange',
                'registerBluetoothAdapterStateChange',
                'cancelBluetoothAdapterStateChange',
                'openBluetoothSetting',
                'makeBluetoothPair',
                'isBluetoothDevicePaired',
                'setBLEMTU',
                'getBLEMTU',
                'onBLEMTUChange',
                'offBLEMTUChange',
                'getBLEDeviceRSSI',
                'onMtuChanged',
                'getHCEState',
                'startHCE',
                'stopHCE',
                'sendHCEMessage',
                'onHCEMessage',
                'openNFCSetting',
                'scanQRCodeNew',
                'qrCodePay',
                'setTitleStyle',
                'prepareApplet',
                'openApplet',
                'createWebView',
                'saveData',
                'queryData',
                'deleteData',
                'getAllKeys',
                'deleteAllKeys',
                'addBankCardWithSn',
                'showShareMorePanel',
                'shareSinglePlugin',
                'addConsole',
                'recentlyUsedAppletList',
                'deleteRecentlyUsedApplet',
                'collectApplet',
                'cancelCollectApplet',
                'getAppletCollectionList',
                'getScreenParams',
                'qrPay',
                'uPlanQrPay',
                'collectCurrentApplet',
                'setSharedData',
                'getSharedData',
                'navigateTo',
                'navigateBack',
                'redirectTo',
                'reLaunch',
                'switchTab',
                'registerLifecycle',
                'getUserLogoutDate',
                'appletSharePopup',
                'hideShareMenu',
                'setAppletShareInfo',
                'setAppletShareBaseInfo',
                'bioDetect',
                'getBluetoothStatus',
                'registerBLEPeripheralConnectionStateChangedNotification',
                'cancelBLEPeripheralConnectionStateChangedNotification',
                'createBLEPeripheralServer',
                'closeBLEPeripheralServer',
                'addService',
                'removeService',
                'startAdvertising',
                'stopAdvertising',
                'writeCharacteristicValue',
                'registerCharacteristicReadNotification',
                'cancelCharacteristicReadNotification',
                'registerCharacteristicWriteNotification',
                'cancelCharacteristicWriteNotification',
                'registerCharacteristicSubscribedNotification',
                'cancelCharacteristicSubscribedNotification',
                'registerCharacteristicUnsubscribedNotification',
                'cancelCharacteristicUnsubscribedNotification',
                'uniqueIdentifier',
                'getNFCStatus',
                'configApplePay',
                'openBluetooth',
                'vibrate',
                'idCardRecognition',
                'getVersionInfo',
                'openCodeInPage',
                'getSystemInfo',
                'getMenuButtonBundingClientRect',
                'followSelfApplet',
                'cancelFollowSelfApplet',
                'getSelfAppletFollowStatus',
                'collectSelfAppletToHome',
                'cancelCollectSelfAppletToHome',
                'reenterSelfApplet',
                'goSelfAppletAppDetail',
                'setMenuBarStyle',
                'autoGoBack',
                'addSelfAppletToLauncher',
                'openCityPage',
                'readHeadImage',
                'getUserStatus',
                'login',
                'getUserLocation',
                'openAppInfoByIdExt',
                'openPageVerifyFree',
                'checkIsShaking',
                'stopCheckIsShaking',
                'startAccelerometer',
                'stopAccelerometer',
                'onAccelerometerChange',
                'offAccelerometerChange',
                'startGyroscope',
                'stopGyroscope',
                'onGyroscopeChange',
                'offAccelerometerChange',
                'startCompass',
                'stopCompass',
                'onCompassChange',
                'offCompassChange',
                'getZheLiBanPublicKey',
                'configZheLiBanAuthorizeInfo',
                'openZheLiBanPage',
                'getContactsData',
                'getFontSizeLevel',
                'downloadApp',
                'saveImage',
                'openVoiceBroadcast',
                'getVoiceBroadcastState',
                'getNotificationState',
                'getBatteryInfo',
                'getMuteState',
                'getNFCAdapter',
                'openAppSetting',
                'openLocation',
                'getCurrentLocationCity',
                'getLocation',
                'chooseLocation',
                'getBeacons',
                'startBeaconDiscovery',
                'stopBeaconDiscovery',
                'onBeaconUpdate',
                'offBeaconUpdate',
                'onBeaconServiceChange',
                'offBeaconServiceChange',
                'checkStepPermissions',
                'uploadStepData',
                'sendRequest',
            ],
            function (t, n) {
                window.upsdk[n] = e;
            },
        );
    var y = (function () {
        var e = (document.currentScript && document.currentScript.src) || '',
            t = '';
        if (e) {
            var n = e.indexOf('/js/upsdk.js');
            -1 < n && (t = e.substring(0, n));
        } else
            for (var o = document.getElementsByTagName('script'), a = 0; a < o.length; ) {
                var i = o[a],
                    c = i.src.indexOf('/js/upsdk.js');
                if (-1 < c) {
                    t = i.src.substring(0, c);
                    break;
                }
                a++;
            }
        return /\/app$/.test(t) && (t = t.replace(/\/app$/, '/')), t;
    })();
    if (f) {
        var D,
            N = document.createElement('script'),
            W = h ? 'ios' : 'android',
            w = (/\(updebug\s(\d+)\)/g.exec(p)[1], /\(cordova\s([\d\.]+)\)/g.exec(p)),
            I = w && 1 < w.length && w[1],
            k = w && I,
            T = /\(securitywebcache\s([\d\.]+)\)/g.exec(p),
            F = T && 1 < T.length && T[1],
            M = T && F;
        if (((d = y + '/common/upconsole.min.js'), k))
            D = y + '/common/cordova/' + W + '.' + I + '/cordova.js';
        else {
            if (!M) return;
            D = y + '/common/jsbridge/' + W + '.' + F + '/WebViewJavascriptBridge.js';
        }
        ((!window.cordova && k) || M) &&
            (N.setAttribute('type', 'text/javascript'),
            N.setAttribute('src', D),
            document.getElementsByTagName('head')[0].appendChild(N)),
            k ? document.addEventListener('deviceready', Q) : M && (N.onload = Q);
    } else
        (v = !0),
            c(window.upsdk, {
                addBankCardWithSn: function (e) {
                    if ((((e = e || {}).Sn = e.Sn || e.sn || e.SN), A))
                        window.location.href = y.replace(
                            '/s/open',
                            '/upcardcollector/collect?bankSn=' + e.Sn,
                        );
                    else {
                        var t = y + '/outBindCard/html/bindCard.html?bankSn=' + e.Sn;
                        e.upEnv && (t += '&upEnv=' + e.upEnv), (window.location.href = t);
                    }
                },
                closeWebApp: function () {
                    var e = y + '/html/closeWebview.html';
                    window.location.href = e;
                },
            });
    window.upsdk.jsApiList = [];
    var _ = null,
        O = null,
        R = null,
        V = null,
        H = null,
        x = null,
        G = null,
        j = null,
        K = null,
        q = null,
        J = null;
    (callbackForonNfcTagDiscovered = null),
        (callbackForonNdefResultMsg = null),
        (callbackForlistenonBLEMTUChange = null),
        (callbackForlistenBeaconUpdate = null),
        (callbackForlistenBeaconServiceChange = null);
    var Z = {},
        X = {};
    function Q() {
        function e(t, n, o, a, i) {
            C
                ? C
                    ? oe(
                          t,
                          function (e) {
                              z(n, e);
                          },
                          o,
                          a,
                          i,
                      )
                    : U
                    ? z(n, E.ESDK_CONFIG_FAILED)
                    : window.upsdk.ready(function () {
                          e(t, n, o, a, i);
                      })
                : z(n, E.ESDK_NEED_CONFIG_DONE);
        }
        function a(n, a, c, r, l) {
            var s = {};
            o((n = n || {})) &&
                i(n, function (e, n) {
                    t(n) || (s[e] = n);
                }),
                e(
                    function (e) {
                        var o = n.success;
                        t(o) && (t(r) && (e = r(e)), o(e));
                    },
                    function (e) {
                        var o = n.fail || n.cancel;
                        o && (t(l) && (e = l(e)), o(e));
                    },
                    a,
                    c,
                    [s],
                );
        }
        (v = !0),
            void 0 !== r && (window.upsdk.config(r), (r = void 0)),
            void 0 !== l && (window.upsdk.appletConfig(l), (l = void 0)),
            void 0 !== s && (window.upsdk.appletAuth(s), (s = void 0)),
            c(window.upsdk, {
                pay: function (e) {
                    var t = ee('pay', e, ['tn']);
                    if (t) z(e.fail || e.cancel, t);
                    else {
                        delete e.merchantId;
                        var o = /\(updebug\s(\d+)\)/g.exec(p);
                        n(o) && 2 <= o.length && '2' === o[1] ? (e.mode = '02') : (e.mode = '00'),
                            a(e, 'UPWebPay', 'pay', null, function (e) {
                                var t = '';
                                if (e && 'string' == typeof e) {
                                    var n = JSON.parse(e);
                                    n && 'object' == typeof n && (e = n);
                                }
                                return (
                                    'object' == typeof e
                                        ? (t = e.desc || e.msg || e.errmsg)
                                        : 'string' == typeof e && (t = e),
                                    t || (t = '支付插件调用失败'),
                                    { msg: t }
                                );
                            });
                    }
                },
                qrPay: function (e) {
                    a(e, 'UPWebPay', 'qrPay');
                },
                uPlanQrPay: function (e) {
                    a(e, 'UPWebPay', 'qrCodePay');
                },
                addBankCard: function (e) {
                    ((e = e || {}).scene = e.scene || '10007'),
                        a(e, 'UPWebBankCard', 'addBankCard');
                },
                addBankCardWithSn: function (e) {
                    ((e = e || {}).Sn = e.Sn || e.sn || e.SN),
                        a(e, 'UPWebBankCard', 'addBankCardWithSn');
                },
                setNavigationBarTitle: function (t) {
                    e(null, null, 'UPWebBars', 'setNavigationBarTitle', [
                        'string' == typeof t ? t : t && t.title,
                    ]);
                },
                setNavigationBarRightButton: function (e) {
                    a(e, 'UPWebBars', 'setNavigationBarRightButton');
                    var n = e && e.handler;
                    t(n) &&
                        (document.removeEventListener('rightbtnclick', _),
                        (_ = n),
                        document.addEventListener('rightbtnclick', _));
                },
                setTitleStyle: function (e) {
                    a(e, 'UPWebBars', 'setTitleStyle');
                },
                getMenuButtonBundingClientRect: function (e) {
                    a(e, 'UPWebBars', 'getMenuButtonBundingClientRect');
                },
                setMenuBarStyle: function (e) {
                    a(e, 'UPWebBars', 'setMenuBarStyle');
                },
                closeWebApp: function (e) {
                    a(e, 'UPWebClosePage', 'closeWebApp');
                },
                showFlashInfo: function (t) {
                    var n;
                    (n = 'string' == typeof t ? t : t && t.msg) &&
                        e(null, null, 'UPWebUI', 'showFlashInfo', [n]);
                },
                scanQRCode: function (e) {
                    a(e, 'UPWebUI', 'scanQRCode', function (e) {
                        return o(e) && e.value && (e = e.value), e;
                    });
                },
                chooseImage: function (e) {
                    (e.maxWidth && e.maxHeight) || ((e.maxWidth = 500), (e.maxHeight = 1e3)),
                        (e.sourceType = e.sourceType || '3'),
                        a(e, 'UPWebUI', 'chooseImage', function (e) {
                            'string' == typeof e && (e = JSON.parse(e));
                            var t = e.url,
                                n = '';
                            if (t) {
                                var o = t.lastIndexOf('.');
                                n = t.substr(o + 1).trim();
                            } else n = 'jpg';
                            return { base64: e.base64, type: n };
                        });
                },
                saveImage: function (e) {
                    a(e, 'UPWebUI', 'saveImage', function (e) {
                        return e;
                    });
                },
                scanQRCodeNew: function (e) {
                    a(e, 'UPWebUI', 'scanQRCodeNew');
                },
                qrCodePay: function (e) {
                    a(e, 'UPWebUI', 'qrCodePay');
                },
                openCodeInPage: function (e) {
                    a(e, 'UPWebUI', 'openCodeInPage');
                },
                autoGoBack: function (e) {
                    a(e, 'UPWebUI', 'autoGoBack');
                },
                readHeadImage: function (e) {
                    a(e, 'UPWebUserInfo', 'readHeadImage');
                },
                getUserStatus: function (e) {
                    a(e, 'UPWebUserDetail', 'getUserStatus');
                },
                login: function (e) {
                    a(e, 'UPWebUserLogin', 'login');
                },
                showSharePopup: function (e) {
                    function t(t) {
                        var n = {
                            title: e.title,
                            content: e.desc,
                            desc: e.desc,
                            picUrl: e.picUrl,
                            imgUrl: e.picUrl,
                            shareUrl:
                                e.shareUrl +
                                (e.shareUrl.indexOf('?') < 0 ? '?channel=' + t : '&channel=' + t),
                            channel: t,
                        };
                        switch (t) {
                            case 0:
                                h && (n.content = e.content + ' ' + e.shareUrl);
                                break;
                            case 1:
                                n.content = e.content + ' ' + e.shareUrl;
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                break;
                            case 7:
                                h || (n.shareUrl = e.title + ' ' + e.shareUrl);
                        }
                        return n;
                    }
                    e.title || (e.title = ''),
                        e.desc || (e.desc = ''),
                        (e.content = e.desc),
                        e.picUrl ||
                            (-1 < y.indexOf('cup.com.cn')
                                ? (e.picUrl =
                                      'https://base.cup.com.cn/s/wl/web/402/images/common/logo.png')
                                : (e.picUrl =
                                      'https://base.95516.com/s/wl/web/402/images/common/logo.png')),
                        (e.imgUrl = e.picUrl),
                        e.shareUrl || (e.shareUrl = location.href),
                        (window.unionpayWalletShareContent_iOS = function (e) {
                            var n = t(e);
                            return (
                                'function' == typeof shareCallback && (n = shareCallback(e, n)),
                                JSON.stringify(n)
                            );
                        }),
                        (window.unionpayWalletShareContent_Android = function (e) {
                            var n = t(e);
                            'function' == typeof shareCallback && (n = shareCallback(e, n)),
                                share_utils &&
                                    'function' == typeof share_utils.setCommonTemplate &&
                                    share_utils.setCommonTemplate(JSON.stringify(n));
                        }),
                        oe(null, null, 'UPWebBars', 'prefetchImage', [e]),
                        oe(null, null, 'UPWalletPlugin', 'showSharePopup', [e]);
                },
                getLocationCity: function (e) {
                    ((e = e || {}).type = '0'),
                        a(e, 'UPWalletPlugin', 'fetchNativeData', function (e) {
                            return 'string' == typeof e && (e = JSON.parse(e)), e.cityCd;
                        });
                },
                getLocationGps: function (e) {
                    ((e = e || {}).type = '1'), a(e, 'UPWalletPlugin', 'fetchNativeData');
                },
                openLocation: function (e) {
                    ((e = e || {}).scale = e.scale || '15'),
                        (e.data = [
                            {
                                amaplongitude: e.longitude,
                                amaplatitude: e.latitude,
                                mchntNm: e.name,
                                mchntAddr: e.address,
                            },
                        ]),
                        a(e, 'UPWalletPlugin', 'lookMap');
                },
                getCurrentLocationCity: function (e) {
                    a(e, 'UPMapPlugin', 'getCurrentLocationCity');
                },
                getLocation: function (e) {
                    ((e = e || {}).cacheTimeout = e.cacheTimeout || '30'),
                        (e.type = e.type || '0'),
                        a(e, 'UPMapPlugin', 'getLocation');
                },
                chooseLocation: function (e) {
                    a(e, 'UPMapPlugin', 'getSelectLocation', function (e) {
                        var t = {
                            name: e.addr,
                            address: e.snippet,
                            latitude: e.merLat,
                            longitude: e.merLng,
                            provinceName: e.provName,
                        };
                        return e.cityName && (t.cityName = e.cityName), t;
                    });
                },
                verifyPayPwd: function (e) {
                    a(e, 'UPWebAccount', 'verifyPayPwd');
                },
                getUserLogoutDate: function (e) {
                    a(e, 'UPWebAccount', 'getUserLogoutDate');
                },
                chooseFileFromAlbum: function (e) {
                    a(e, 'UPWebNativeInfo', 'chooseFileFromAlbum');
                },
                readAlbumData: function (e) {
                    a(e, 'UPWebNativeInfo', 'readAlbumData');
                },
                startAudioRecording: function (e) {
                    a(e, 'UPWebNativeInfo', 'startAudioRecording');
                },
                stopAudioRecording: function (e) {
                    a(e, 'UPWebNativeInfo', 'stopAudioRecording');
                },
                readAudioRecordingData: function (e) {
                    a(e, 'UPWebNativeInfo', 'readAudioRecordingData');
                },
                startVideoRecording: function (e) {
                    a(e, 'UPWebNativeInfo', 'startVideoRecording');
                },
                readVideoRecordingData: function (e) {
                    a(e, 'UPWebNativeInfo', 'readVideoRecordingData');
                },
                readFaceVideoData: function (e) {
                    a(e, 'UPWebNativeInfo', 'readFaceVideoData');
                },
                readFaceImageData: function (e) {
                    a(e, 'UPWebNativeInfo', 'readFaceImageData');
                },
                uniqueIdentifier: function (e) {
                    a(e, 'UPWebNativeInfo', 'uniqueIdentifier');
                },
                getSystemInfo: function (e) {
                    a(e, 'UPWebNativeInfo', 'getSystemInfo');
                },
                getVersionInfo: function (e) {
                    a(e, 'UPWebNativeInfo', 'getVersionInfo');
                },
                openCityPage: function (e) {
                    a(e, 'UPWebNativeInfo', 'openCityPage');
                },
                getUserLocation: function (e) {
                    a(e, 'UPWebNativeInfo', 'getUserLocation');
                },
                noteInfoChange: function (e) {
                    a(e, 'UPNotesInfoPlugin', 'noteInfoChange');
                },
                openRNPage: function (e) {
                    a(e, 'UPWebNewPage', 'openRNPage');
                },
                prepareApplet: function (e) {
                    a(e, 'UPWebNewPage', 'prepareApplet');
                },
                openApplet: function (e) {
                    a(e, 'UPWebNewPage', 'openApplet');
                },
                createWebView: function (t) {
                    e(null, null, 'UPWebNewPage', 'createWebPage', [
                        JSON.stringify({
                            title: t.title,
                            url: t.url,
                            loading: 'yes',
                            toolbar: 'no',
                            isFinish: t.isFinish || '0',
                        }),
                    ]);
                },
                openAppInfoByIdExt: function (e) {
                    a(e, 'UPWebNewPage', 'openAppInfoByIdExt');
                },
                openPageVerifyFree: function (e) {
                    a(e, 'UPWebSdk', 'openAppInfo');
                },
                navi: function (e) {
                    a(e, 'UPCarCodePlugin', 'navi');
                },
                setScreenBrightness: function (e) {
                    a(e, 'UPCarCodePlugin', 'setScreenBrightness');
                },
                getScreenBrightness: function (e) {
                    a(e, 'UPCarCodePlugin', 'getScreenBrightness');
                },
                changeScreenShot: function (e) {
                    a(e, 'UPWebUI', 'changeScreenShot');
                },
                monitorScreenShot: function (e) {
                    a(e, 'UPCarCodePlugin', 'monitorScreenShot');
                    var n = e && e.success;
                    t(n) &&
                        (document.removeEventListener('screenShotAction', O),
                        (O = n),
                        document.addEventListener('screenShotAction', O));
                },
                removeScreenShot: function (e) {
                    a(e, 'UPCarCodePlugin', 'removeScreenShot'),
                        document.removeEventListener('screenShotAction', O);
                },
                addCommonApp: function (e) {
                    a(e, 'UPAddCommonAppPlugin', 'addCommonApp');
                },
                logEvent: function (e) {
                    a(e, 'UPWebAnalysis', 'logEvent');
                },
                openBluetoothAdapter: function (e) {
                    a(e, 'UPWPluginBluetooth', 'openBluetoothAdapter');
                },
                closeBluetoothAdapter: function (e) {
                    a(e, 'UPWPluginBluetooth', 'closeBluetoothAdapter');
                },
                getBluetoothAdapterState: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBluetoothAdapterState');
                },
                startBluetoothDevicesDiscovery: function (e) {
                    a(e, 'UPWPluginBluetooth', 'startBluetoothDevicesDiscovery');
                },
                stopBluetoothDevicesDiscovery: function (e) {
                    a(e, 'UPWPluginBluetooth', 'stopBluetoothDevicesDiscovery');
                },
                getBluetoothDevices: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBluetoothDevices');
                },
                getConnectedBluetoothDevices: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getConnectedBluetoothDevices');
                },
                connectBLEDevice: function (e) {
                    a(e, 'UPWPluginBluetooth', 'connectBLEDevice');
                },
                disconnectBLEDevice: function (e) {
                    a(e, 'UPWPluginBluetooth', 'disconnectBLEDevice');
                },
                writeBLECharacteristicValue: function (e) {
                    a(e, 'UPWPluginBluetooth', 'writeBLECharacteristicValue');
                },
                readBLECharacteristicValue: function (e) {
                    a(e, 'UPWPluginBluetooth', 'readBLECharacteristicValue');
                },
                notifyBLECharacteristicValueChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'notifyBLECharacteristicValueChange');
                },
                getBLEDeviceServices: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBLEDeviceServices');
                },
                getBLEDeviceCharacteristics: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBLEDeviceCharacteristics');
                },
                registerBluetoothDeviceFound: function (e) {
                    a(e, 'UPWPluginBluetooth', 'registerBluetoothDeviceFound');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BluetoothDeviceFound', R),
                        (R = n),
                        document.addEventListener('BluetoothDeviceFound', R));
                },
                cancelBluetoothDeviceFound: function (e) {
                    a(e, 'UPWPluginBluetooth', 'cancelBluetoothDeviceFound');
                },
                registerBLEConnectionStateChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'registerBLEConnectionStateChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEConnectionStateChange', V),
                        (V = n),
                        document.addEventListener('BLEConnectionStateChange', V));
                },
                cancelBLEConnectionStateChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'cancelBLEConnectionStateChange');
                },
                registerBLECharacteristicValueChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'registerBLECharacteristicValueChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLECharacteristicValueChange', H),
                        (H = n),
                        document.addEventListener('BLECharacteristicValueChange', H));
                },
                cancelBLECharacteristicValueChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'cancelBLECharacteristicValueChange');
                },
                registerBluetoothAdapterStateChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'registerBluetoothAdapterStateChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BluetoothAdapterStateChange', x),
                        (x = n),
                        document.addEventListener('BluetoothAdapterStateChange', x));
                },
                cancelBluetoothAdapterStateChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'cancelBluetoothAdapterStateChange');
                },
                openBluetoothSetting: function (e) {
                    a(e, 'UPWPluginBluetooth', 'openBluetoothSetting');
                },
                makeBluetoothPair: function (e) {
                    a(e, 'UPWPluginBluetooth', 'makeBluetoothPair');
                },
                isBluetoothDevicePaired: function (e) {
                    a(e, 'UPWPluginBluetooth', 'isBluetoothDevicePaired');
                },
                setBLEMTU: function (e) {
                    a(e, 'UPWPluginBluetooth', 'setBLEMTU');
                },
                getBLEMTU: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBLEMTU');
                },
                onBLEMTUChange: function (e) {
                    a(e, 'UPWPluginBluetooth', 'onBLEMTUChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener(
                            'BLEMtuChanged',
                            callbackForlistenonBLEMTUChange,
                        ),
                        (callbackForlistenonBLEMTUChange = n),
                        document.addEventListener(
                            'BLEMtuChanged',
                            callbackForlistenonBLEMTUChange,
                        ));
                },
                offBLEMTUChange: function (e) {
                    document.removeEventListener('BLEMtuChanged', callbackForlistenonBLEMTUChange),
                        a(e, 'UPWPluginBluetooth', 'offBLEMTUChange');
                },
                getBLEDeviceRSSI: function (e) {
                    a(e, 'UPWPluginBluetooth', 'getBLEDeviceRSSI');
                },
                onMtuChanged: function (e) {
                    a(e, 'UPWPluginBluetooth', 'onMtuChanged');
                },
                getBluetoothStatus: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'getBluetoothStatus');
                },
                registerBLEPeripheralConnectionStateChangedNotification: function (e) {
                    a(
                        e,
                        'UPWBluetoothPeripheral',
                        'registerBLEPeripheralConnectionStateChangedNotification',
                    );
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEPeripheralConnectionStateChanged', G),
                        (G = n),
                        document.addEventListener('BLEPeripheralConnectionStateChanged', G));
                },
                cancelBLEPeripheralConnectionStateChangedNotification: function (e) {
                    a(
                        e,
                        'UPWBluetoothPeripheral',
                        'cancelBLEPeripheralConnectionStateChangedNotification',
                    );
                },
                createBLEPeripheralServer: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'createBLEPeripheralServer');
                },
                writeCharacteristicValue: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'writeCharacteristicValue');
                },
                addService: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'addService');
                },
                removeService: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'removeService');
                },
                startAdvertising: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'startAdvertising');
                },
                stopAdvertising: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'stopAdvertising');
                },
                closeBLEPeripheralServer: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'closeBLEPeripheralServer');
                },
                registerCharacteristicReadNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'registerCharacteristicReadNotification');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEPeripheralCharacteristicRead', j),
                        (j = n),
                        document.addEventListener('BLEPeripheralCharacteristicRead', j));
                },
                cancelCharacteristicReadNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'cancelCharacteristicReadNotification');
                },
                registerCharacteristicWriteNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'registerCharacteristicWriteNotification');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEPeripheralCharacteristicWrite', K),
                        (K = n),
                        document.addEventListener('BLEPeripheralCharacteristicWrite', K));
                },
                cancelCharacteristicWriteNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'cancelCharacteristicWriteNotification');
                },
                registerCharacteristicSubscribedNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'registerCharacteristicSubscribedNotification');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEPeripheralCharacteristicSubscribed', q),
                        (q = n),
                        document.addEventListener('BLEPeripheralCharacteristicSubscribed', q));
                },
                cancelCharacteristicSubscribedNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'cancelCharacteristicSubscribedNotification');
                },
                registerCharacteristicUnsubscribedNotification: function (e) {
                    a(
                        e,
                        'UPWBluetoothPeripheral',
                        'registerCharacteristicUnsubscribedNotification',
                    );
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BLEPeripheralCharacteristicUnSubscribed', J),
                        (J = n),
                        document.addEventListener('BLEPeripheralCharacteristicUnSubscribed', J));
                },
                cancelCharacteristicUnsubscribedNotification: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'cancelCharacteristicUnsubscribedNotification');
                },
                openBluetooth: function (e) {
                    a(e, 'UPWBluetoothPeripheral', 'openBluetooth');
                },
                configApplePay: function (e) {
                    a(e, 'UPWNFCManager', 'configApplePay');
                },
                getNFCStatus: function (e) {
                    a(e, 'UPHceManagerPlugin', 'getNFCStatus');
                },
                getHCEState: function (e) {
                    a(e, 'UPHceManagerPlugin', 'getHCEState');
                },
                startHCE: function (e) {
                    a(e, 'UPHceManagerPlugin', 'startHCE');
                },
                stopHCE: function (e) {
                    a(e, 'UPHceManagerPlugin', 'stopHCE');
                },
                sendHCEMessage: function (e) {
                    a(e, 'UPHceManagerPlugin', 'sendHCEMessage');
                },
                onHCEMessage: function (e) {
                    a(e, 'UPHceManagerPlugin', 'onHCEMessage');
                },
                openNFCSetting: function (e) {
                    a(e, 'UPHceManagerPlugin', 'openNFCSetting');
                },
                saveData: function (e) {
                    a(e, 'UPXAppletDataPlugin', 'saveData');
                },
                queryData: function (e) {
                    a(e, 'UPXAppletDataPlugin', 'queryData');
                },
                deleteData: function (e) {
                    a(e, 'UPXAppletDataPlugin', 'deleteData');
                },
                getAllKeys: function (e) {
                    a(e, 'UPXAppletDataPlugin', 'getAllKeys');
                },
                deleteAllKeys: function (e) {
                    a(e, 'UPXAppletDataPlugin', 'deleteAllKeys');
                },
                showShareMorePanel: function (e) {
                    function t(t) {
                        e.shareUrl +=
                            e.shareUrl.indexOf('?') < 0 ? '?channel=' + t : '&channel=' + t;
                        var n = {
                            title: e.title,
                            content: e.desc,
                            desc: e.desc,
                            picUrl: e.picUrl,
                            imgUrl: e.picUrl,
                            shareUrl: e.shareUrl,
                            channel: t,
                        };
                        switch (t) {
                            case 0:
                                h && (n.content = e.content + ' ' + e.shareUrl);
                                break;
                            case 1:
                                n.content = e.content + ' ' + e.shareUrl;
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                break;
                            case 7:
                                h || (n.shareUrl = e.title + ' ' + e.shareUrl);
                        }
                        return n;
                    }
                    e.title || (e.title = ''),
                        e.desc || (e.desc = ''),
                        (e.content = e.desc),
                        e.picUrl ||
                            (-1 < y.indexOf('cup.com.cn')
                                ? (e.picUrl =
                                      'https://base.cup.com.cn/s/wl/web/402/images/common/logo.png')
                                : (e.picUrl =
                                      'https://base.95516.com/s/wl/web/402/images/common/logo.png')),
                        (e.imgUrl = e.picUrl),
                        e.shareUrl || (e.shareUrl = location.href),
                        e.shareList ||
                            (e.shareList = [
                                { shareId: '0', shareType: '1', shareData: {} },
                                { shareId: '1', shareType: '1', shareData: {} },
                                { shareId: '3', shareType: '1', shareData: {} },
                                { shareId: '4', shareType: '1', shareData: {} },
                                { shareId: '5', shareType: '1', shareData: {} },
                                { shareId: '6', shareType: '1', shareData: {} },
                                { shareId: '7', shareType: '1', shareData: {} },
                            ]),
                        (window.unionpayWalletShareContent_iOS = function (n) {
                            var o = t(n);
                            return (
                                'function' == typeof e.shareCallback && (o = e.shareCallback(n, o)),
                                JSON.stringify(o)
                            );
                        }),
                        (window.unionpayWalletShareContent_Android = function (n) {
                            var o = t(n);
                            'function' == typeof e.shareCallback && (o = e.shareCallback(n, o)),
                                share_utils &&
                                    'function' == typeof share_utils.setCommonTemplate &&
                                    share_utils.setCommonTemplate(JSON.stringify(o));
                        });
                    var n = {};
                    (n.shareList = e.shareList),
                        oe(null, null, 'UPWebBars', 'prefetchImage', [e]),
                        oe(e.success, e.fail, 'UPSharePlugin', 'showSharePopupNew', [n]);
                },
                shareSinglePlugin: function (e) {
                    function t(t) {
                        e.shareUrl +=
                            e.shareUrl.indexOf('?') < 0 ? '?channel=' + t : '&channel=' + t;
                        var n = {
                            title: e.title,
                            content: e.desc,
                            desc: e.desc,
                            picUrl: e.picUrl,
                            imgUrl: e.picUrl,
                            shareUrl: e.shareUrl,
                            channel: t,
                        };
                        switch (t) {
                            case 0:
                                h && (n.content = e.content + ' ' + e.shareUrl);
                                break;
                            case 1:
                                n.content = e.content + ' ' + e.shareUrl;
                                break;
                            case 3:
                            case 4:
                            case 5:
                            case 6:
                                break;
                            case 7:
                                h || (n.shareUrl = e.title + ' ' + e.shareUrl);
                        }
                        return n;
                    }
                    e.title || (e.title = ''),
                        e.desc || (e.desc = ''),
                        (e.content = e.desc),
                        e.picUrl ||
                            (-1 < y.indexOf('cup.com.cn')
                                ? (e.picUrl =
                                      'https://base.cup.com.cn/s/wl/web/402/images/common/logo.png')
                                : (e.picUrl =
                                      'https://base.95516.com/s/wl/web/402/images/common/logo.png')),
                        (e.imgUrl = e.picUrl),
                        e.shareUrl || (e.shareUrl = location.href),
                        e.shareId || $.extend(e, { shareId: '3', shareType: '1', shareData: {} }),
                        (window.unionpayWalletShareContent_iOS = function (n) {
                            var o = t(n);
                            return (
                                'function' == typeof e.shareCallback && (o = e.shareCallback(n, o)),
                                JSON.stringify(o)
                            );
                        }),
                        (window.unionpayWalletShareContent_Android = function (n) {
                            var o = t(n);
                            'function' == typeof e.shareCallback && (o = e.shareCallback(n, o)),
                                share_utils &&
                                    'function' == typeof share_utils.setCommonTemplate &&
                                    share_utils.setCommonTemplate(JSON.stringify(o));
                        });
                    var n = {};
                    (n.shareId = e.shareId),
                        (n.shareType = e.shareType),
                        (n.shareData = e.shareData),
                        oe(null, null, 'UPWebBars', 'prefetchImage', [e]),
                        oe(e.success, e.fail, 'UPSharePlugin', 'shareContent', [n]);
                },
                appletSharePopup: function (e) {
                    a(e, 'UPSharePlugin', 'appletSharePopup');
                },
                hideShareMenu: function (e) {
                    a(e, 'UPSharePlugin', 'hideShareMenu');
                },
                setAppletShareInfo: function (e) {
                    a(e, 'UPSharePlugin', 'setAppletShareInfo');
                },
                setAppletShareBaseInfo: function (e) {
                    a(e, 'UPSharePlugin', 'setAppletShareBaseInfo');
                },
                bioDetect: function (e) {
                    a(e, 'UPFacePlugin', 'bioDetect');
                },
                addConsole: function (e) {
                    a(e, 'UPWLogPlugin', 'addConsole');
                },
                recentlyUsedAppletList: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'recentlyUsedAppletList');
                },
                deleteRecentlyUsedApplet: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'deleteRecentlyUsedApplet');
                },
                collectApplet: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'collectApplet');
                },
                cancelCollectApplet: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'cancelCollectApplet');
                },
                getAppletCollectionList: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'getAppletCollectionList');
                },
                collectCurrentApplet: function (e) {
                    a(e, 'UPWAppletCapacityPlugin', 'collectCurrentApplet');
                },
                getScreenParams: function (e) {
                    a(e, 'UPScreenParamsPlugin', 'getScreenParams');
                },
                setSharedData: function (e) {
                    a(e, 'UPWPluginLiteApp', 'setSharedData');
                },
                getSharedData: function (e) {
                    a(e, 'UPWPluginLiteApp', 'getSharedData');
                },
                navigateTo: function (e) {
                    a(e, 'UPWPluginLiteApp', 'navigateTo');
                },
                navigateBack: function (e) {
                    a(e, 'UPWPluginLiteApp', 'navigateBack');
                },
                redirectTo: function (e) {
                    a(e, 'UPWPluginLiteApp', 'redirectTo');
                },
                reLaunch: function (e) {
                    a(e, 'UPWPluginLiteApp', 'reLaunch');
                },
                switchTab: function (e) {
                    a(e, 'UPWPluginLiteApp', 'switchTab');
                },
                registerLifecycle: function (e) {
                    var n = [
                        'rerender',
                        'onAppLoad',
                        'onAppShow',
                        'onAppHide',
                        'onPageShow',
                        'onPageHide',
                        'onPageLoad',
                    ];
                    for (var o in e)
                        e.hasOwnProperty(o) && -1 < n.indexOf(o) && t(e[o]) && ae(o, e[o]);
                    a(e, 'UPWPluginLiteApp', 'ready');
                },
                vibrate: function (e) {
                    a(e, 'UPWDeviceModule', 'vibrate');
                },
                openVoiceBroadcast: function (e) {
                    a(e, 'UPWDeviceModule', 'openVoiceBroadcast');
                },
                getVoiceBroadcastState: function (e) {
                    a(e, 'UPWDeviceModule', 'getVoiceBroadcastState');
                },
                getNotificationState: function (e) {
                    a(e, 'UPWDeviceModule', 'getNotificationState');
                },
                getBatteryInfo: function (e) {
                    a(e, 'UPWDeviceModule', 'getBatteryInfo');
                },
                getMuteState: function (e) {
                    a(e, 'UPWDeviceModule', 'getMuteState');
                },
                idCardRecognition: function (e) {
                    a(e, 'UPAIPlugin', 'idCardRecognition');
                },
                followSelfApplet: function (e) {
                    a(e, 'UPAppletMorePlugin', 'follow');
                },
                cancelFollowSelfApplet: function (e) {
                    a(e, 'UPAppletMorePlugin', 'cancelFollow');
                },
                getSelfAppletFollowStatus: function (e) {
                    a(e, 'UPAppletMorePlugin', 'getFollowStatus');
                },
                collectSelfAppletToHome: function (e) {
                    a(e, 'UPAppletMorePlugin', 'collectToHome');
                },
                cancelCollectSelfAppletToHome: function (e) {
                    a(e, 'UPAppletMorePlugin', 'cancelCollectToHome');
                },
                getSelfAppletCollectStatus: function (e) {
                    a(e, 'UPAppletMorePlugin', 'getCollectStatus');
                },
                reenterSelfApplet: function (e) {
                    a(e, 'UPAppletMorePlugin', 'reenterApplet');
                },
                goSelfAppletAppDetail: function (e) {
                    a(e, 'UPAppletMorePlugin', 'appDetail');
                },
                addSelfAppletToLauncher: function (e) {
                    a(e, 'UPAppletMorePlugin', 'addToLauncher');
                },
                checkIsShaking: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'checkIsShaking');
                },
                stopCheckIsShaking: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'stopCheckIsShaking');
                },
                startAccelerometer: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'startAccelerometer');
                },
                stopAccelerometer: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'stopAccelerometer');
                },
                onAccelerometerChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'onAccelerometerChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('accelerometerChange', X.accelerometerChange),
                        (X.accelerometerChange = n),
                        document.addEventListener('accelerometerChange', X.accelerometerChange));
                },
                offAccelerometerChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'offAccelerometerChange');
                },
                startGyroscope: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'startGyroscope');
                },
                stopGyroscope: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'stopGyroscope');
                },
                onGyroscopeChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'onGyroscopeChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('gyroscopeChange', X.gyroscopeChange),
                        (X.gyroscopeChange = n),
                        document.addEventListener('gyroscopeChange', X.gyroscopeChange));
                },
                offGyroscopeChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'offGyroscopeChange');
                },
                startCompass: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'startCompass');
                },
                stopCompass: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'stopCompass');
                },
                onCompassChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'onCompassChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('compassChange', X.compassChange),
                        (X.compassChange = n),
                        document.addEventListener('compassChange', X.compassChange));
                },
                offCompassChange: function (e) {
                    a(e, 'UPWDeviceSensorPlugin', 'offCompassChange');
                },
                getZheLiBanPublicKey: function (e) {
                    a(e, 'UPZheLiBanPlugin', 'getZheLiBanPublicKey');
                },
                configZheLiBanAuthorizeInfo: function (e) {
                    a(e, 'UPZheLiBanPlugin', 'configZheLiBanAuthorizeInfo');
                },
                openZheLiBanPage: function (e) {
                    a(e, 'UPZheLiBanPlugin', 'openZheLiBanPage');
                },
                getContactsData: function (e) {
                    a(e, 'UPWAddressBook', 'getContactsData');
                },
                getFontSizeLevel: function (e) {
                    a(e, 'UPWebNativeInfo', 'getFontSizeLevel');
                },
                downloadApp: function (e) {
                    a(e, 'UPDownloadPlugin', 'downloadApp');
                },
                getNFCAdapter: function () {
                    return {
                        getUPNFC: function () {
                            return {
                                isSupport: function (e) {
                                    a(e, 'UPNFCPlugin', 'isSupport');
                                },
                                openNfcSetting: function (e) {
                                    a(e, 'UPNFCPlugin', 'openNfcSetting');
                                },
                                startDiscovery: function (e) {
                                    a(e, 'UPNFCPlugin', 'startDiscovery');
                                    var n = e && e.callback;
                                    t(n) &&
                                        (document.removeEventListener(
                                            'onNfcTagDiscovered',
                                            callbackForonNfcTagDiscovered,
                                        ),
                                        (callbackForonNfcTagDiscovered = n),
                                        document.addEventListener(
                                            'onNfcTagDiscovered',
                                            callbackForonNfcTagDiscovered,
                                        ));
                                },
                                stopDiscovery: function (e) {
                                    a(e, 'UPNFCPlugin', 'stopDiscovery');
                                },
                                setAlertMessage: function (e) {
                                    a(e, 'UPNFCPlugin', 'setAlertMessage');
                                },
                                restartPolling: function (e) {
                                    a(e, 'UPNFCPlugin', 'restartPolling');
                                },
                                offDiscovered: function (e) {
                                    document.removeEventListener(
                                        'onNfcTagDiscovered',
                                        callbackForonNfcTagDiscovered,
                                    );
                                },
                            };
                        },
                        isSupport: function (e) {
                            a(e, 'UPNFCPlugin', 'isSupport');
                        },
                        openNfcSetting: function (e) {
                            a(e, 'UPNFCPlugin', 'openNfcSetting');
                        },
                        startDiscovery: function (e) {
                            a(e, 'UPNFCPlugin', 'startDiscovery');
                            var n = e && e.callback;
                            t(n) &&
                                (document.removeEventListener(
                                    'onNfcTagDiscovered',
                                    callbackForonNfcTagDiscovered,
                                ),
                                (callbackForonNfcTagDiscovered = n),
                                document.addEventListener(
                                    'onNfcTagDiscovered',
                                    callbackForonNfcTagDiscovered,
                                ));
                        },
                        stopDiscovery: function (e) {
                            a(e, 'UPNFCPlugin', 'stopDiscovery');
                        },
                        setAlertMessage: function (e) {
                            a(e, 'UPNFCPlugin', 'setAlertMessage');
                        },
                        restartPolling: function (e) {
                            a(e, 'UPNFCPlugin', 'restartPolling');
                        },
                        offDiscovered: function (e) {
                            document.removeEventListener(
                                'onNfcTagDiscovered',
                                callbackForonNfcTagDiscovered,
                            );
                        },
                        getUPIsoDep: function () {
                            return {
                                connect: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'connect');
                                },
                                isConnected: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'isConnected');
                                },
                                close: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'close');
                                },
                                setTimeout: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'setTimeout');
                                },
                                getHistoricalBytes: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'getHistoricalBytes');
                                },
                                getMaxTransceiveLength: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'getMaxTransceiveLength');
                                },
                                transceive: function (e) {
                                    a(e, 'UPIsoDepPlugin', 'transceive');
                                },
                            };
                        },
                        getUPNdef: function () {
                            return {
                                connect: function (e) {
                                    a(e, 'UPNdefPlugin', 'connect');
                                },
                                isConnected: function (e) {
                                    a(e, 'UPNdefPlugin', 'isConnected');
                                },
                                writeNdefMessage: function (e) {
                                    a(e, 'UPNdefPlugin', 'writeNdefMessage');
                                },
                                close: function (e) {
                                    a(e, 'UPNdefPlugin', 'close');
                                },
                                queryNdefStatus: function (e) {
                                    a(e, 'UPNdefPlugin', 'queryNdefStatus');
                                },
                                readNdefMessage: function (e) {
                                    a(e, 'UPNdefPlugin', 'readNdefMessage');
                                },
                            };
                        },
                        getUPIso7816: function () {
                            return {
                                connect: function (e) {
                                    a(e, 'UPIso7816Plugin', 'connect');
                                },
                                isConnected: function (e) {
                                    a(e, 'UPIso7816Plugin', 'isConnected');
                                },
                                transceive: function (e) {
                                    a(e, 'UPIso7816Plugin', 'transceive');
                                },
                                getTagInfo: function (e) {
                                    a(e, 'UPIso7816Plugin', 'getTagInfo');
                                },
                            };
                        },
                        getUPNfcF: function () {
                            return {
                                connect: function (e) {
                                    a(e, 'UPNfcFPlugin', 'connect');
                                },
                                isConnected: function (e) {
                                    a(e, 'UPNfcFPlugin', 'isConnected');
                                },
                                transceive: function (e) {
                                    a(e, 'UPNfcFPlugin', 'transceive');
                                },
                                getTagInfo: function (e) {
                                    a(e, 'UPNfcFPlugin', 'getTagInfo');
                                },
                                close: function (e) {
                                    a(e, 'UPNfcFPlugin', 'close');
                                },
                                setTimeout: function (e) {
                                    a(e, 'UPNfcFPlugin', 'setTimeout');
                                },
                                getMaxTransceiveLength: function (e) {
                                    a(e, 'UPNfcFPlugin', 'getMaxTransceiveLength');
                                },
                            };
                        },
                    };
                },
                openAppSetting: function (e) {
                    a(e, 'UPXMicroStorePlugin', 'showNotificationSetingAlert');
                },
                getBeacons: function (e) {
                    a(e, 'UPWBeacon', 'getBeacons');
                },
                startBeaconDiscovery: function (e) {
                    a(e, 'UPWBeacon', 'startBeaconDiscovery');
                },
                stopBeaconDiscovery: function (e) {
                    a(e, 'UPWBeacon', 'stopBeaconDiscovery');
                },
                onBeaconUpdate: function (e) {
                    a(e, 'UPWBeacon', 'listenBeaconUpdate');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener('BeaconFound', callbackForlistenBeaconUpdate),
                        (callbackForlistenBeaconUpdate = n),
                        document.addEventListener('BeaconFound', callbackForlistenBeaconUpdate));
                },
                offBeaconUpdate: function (e) {
                    a(e, 'UPWBeacon', 'cancelListenBeaconUpdate'),
                        document.removeEventListener('BeaconFound', callbackForlistenBeaconUpdate);
                },
                onBeaconServiceChange: function (e) {
                    a(e, 'UPWBeacon', 'listenBeaconServiceChange');
                    var n = e && e.callback;
                    t(n) &&
                        (document.removeEventListener(
                            'BeaconServiceChange',
                            callbackForlistenBeaconServiceChange,
                        ),
                        (callbackForlistenBeaconServiceChange = n),
                        document.addEventListener(
                            'BeaconServiceChange',
                            callbackForlistenBeaconServiceChange,
                        ));
                },
                offBeaconServiceChange: function (e) {
                    a(e, 'UPWBeacon', 'cancelListenBeaconServiceChange'),
                        document.removeEventListener(
                            'BeaconServiceChange',
                            callbackForlistenBeaconServiceChange,
                        );
                },
                checkStepPermissions: function (e) {
                    a(e, 'UPStepPlugin', 'checkStepPermissions');
                },
                uploadStepData: function (e) {
                    a(e, 'UPStepPlugin', 'uploadStepData');
                },
                sendRequest: function (e) {
                    a(e, 'UPWebNetwork', 'sendRequest', function (t) {
                        var n = t && t.message;
                        if (n) {
                            if (e && 'arraybuffer' == e.dataType) {
                                for (
                                    var o = window.atob(n),
                                        a = o.length,
                                        i = new Uint8Array(a),
                                        c = 0;
                                    c < a;
                                    c++
                                )
                                    i[c] = o.charCodeAt(c);
                                n = i.buffer;
                            }
                            e &&
                                'json' == e.dataType &&
                                'string' == typeof t &&
                                (n = JSON.parse(n)),
                                (t.message = n);
                        }
                        return t;
                    });
                },
            }),
            P.length &&
                ((C = !0),
                i(P, function (e, n) {
                    t(n) && n();
                }));
    }
    function z(e, n) {
        var o;
        if ((n && 'string' == typeof n && (n = { msg: n }), window.cordova))
            switch (window.cordova.errorRetStatus) {
                case window.cordova.callbackStatus.INVALID_ACTION:
                    o = E.ESDK_PLUGIN_INVALID_ACTION;
                    break;
                case window.cordova.callbackStatus.CLASS_NOT_FOUND_EXCEPTION:
                    o = E.ESDK_PLUGIN_CLASS_NOT_FOUND;
                    break;
                case window.cordova.callbackStatus.ILLEGAL_ACCESS_EXCEPTION:
                    o = E.ESDK_PLUGIN_ILLEGAL_ACCESS;
            }
        if ((o && (n = o), t(e))) e(n);
        else if (n) {
            var a = n.errmsg || n.msg || n.desc,
                i = n.errcode || n.code;
            i && (a += ' [' + i + ']'), Y(a);
        }
        window.cordova && (window.cordova.errorRetStatus = window.cordova.callbackStatus.OK);
    }
    function Y(e) {
        m &&
            e &&
            (C && a('showFlashInfo', window.upsdk.jsApiList)
                ? oe(null, null, 'UPWebUI', 'showFlashInfo', [e])
                : alert(e));
    }
    function ee(e, t, n) {
        for (var o, a = 0; a < n.length; ++a)
            if (!t[n[a]]) {
                o = n[a];
                break;
            }
        return o ? e + '调用方法缺少参数' + o : '';
    }
    function te(e) {
        t(u) && ('string' == typeof e && (e = { msg: e }), u(e));
    }
    function ne(e) {
        return e ? (e.resultString ? e.resultString : e.resultParams ? e.resultParams : void 0) : e;
    }
    function oe(e, n, o, a, i) {
        k && window.cordova
            ? window.cordova.exec(e, n, o, a, i)
            : M &&
              window.WebViewJavascriptBridge &&
              window.WebViewJavascriptBridge.callHandler(
                  o,
                  a,
                  i,
                  function (n) {
                      t(e) && e(ne(n));
                  },
                  function (e) {
                      t(n) && n(ne(e));
                  },
              );
    }
    function ae(e, t) {
        h && window.WebViewJavascriptBridge && window.WebViewJavascriptBridge.registerHandler
            ? window.WebViewJavascriptBridge.registerHandler(e, t)
            : (document.removeEventListener(e, Z[e]),
              (Z[e] = t),
              document.addEventListener(e, Z[e]));
    }
    c(window.upsdk, {
        config: function (e) {
            if (!f)
                return (
                    (U = !(C = !0)),
                    void (
                        S.length &&
                        i(S, function (e, n) {
                            t(n) && n();
                        })
                    )
                );
            if (o(e)) {
                e.debug &&
                    ((m = !0),
                    (function () {
                        window.upConsoleShow = !0;
                        var e = document.createElement('script');
                        (e.type = 'text/javascript'),
                            (e.src = d),
                            document.getElementsByTagName('head')[0].appendChild(e);
                    })(),
                    delete e.debug);
                var c = ee('config', e, ['appId', 'nonceStr', 'timestamp', 'signature']);
                if (!c && e.url) {
                    var l = window.location.href.split('#')[0];
                    e.url !== l && (c = '签名因子url取值不正确, 正确的应该是' + l);
                }
                if (c) return z(null, c), void te(c);
                (c = ''),
                    f || (c = E.ESDK_NOT_IN_WALLET),
                    c
                        ? te(c)
                        : v
                        ? oe(
                              function (e) {
                                  var c, r;
                                  (U = !(C = !0)),
                                      'string' == typeof e && (e = JSON.parse(e)),
                                      e.params && (e = e.params),
                                      (B =
                                          (e &&
                                              e.jsApiList &&
                                              ((c = e.jsApiList),
                                              (r = {}),
                                              n(c) &&
                                                  i(c, function (e, t) {
                                                      o(t) && (r[t.plugin] = t.actions);
                                                  }),
                                              r)) ||
                                          {});
                                  var l = [];
                                  i(B, function (e, t) {
                                      n(t) && (l = l.concat(t));
                                  });
                                  var s = a('fetchNativeData', l);
                                  0 <= s && l.splice(s, 1, 'getLocationCity', 'getLocationGps'),
                                      (window.upsdk.jsApiList = l),
                                      m &&
                                          window.uplog &&
                                          window.uplog(
                                              'you can use plugins:' + window.upsdk.jsApiList,
                                          ),
                                      S.length &&
                                          i(S, function (e, n) {
                                              t(n) && n();
                                          }),
                                      Y('config ok');
                              },
                              function (e) {
                                  (C = !(U = !0)), (g = e), t(u) && u(e), Y('config error: ' + e);
                              },
                              'UPWebSdk',
                              'config',
                              [e],
                          )
                        : (r = e);
            } else z(null, E.ESDK_BAD_PARAMS);
        },
        openTicketDetails: function (e) {
            oe(null, null, 'UPWebOpenOther', 'openTicketDetails', [e]);
        },
        ready: function (e) {
            t(e) && (C ? e() : S.push(e));
        },
        error: function (n) {
            (n = (t(n) && n) || e), U ? n(g) : (u = n);
        },
        appletConfig: function (e) {
            if (v) {
                var n = null,
                    o = null;
                t(e.success) && ((n = e.success), delete e.success),
                    t(e.fail) && ((o = e.fail), delete e.fail),
                    oe(
                        function (e) {
                            (C = !0), n(e);
                        },
                        function (e) {
                            z(o, e);
                        },
                        'UPWebSdk',
                        'appletConfig',
                        [e],
                    );
            } else l = e;
        },
        appletAuth: function (e) {
            if (v) {
                var n = null,
                    o = null;
                t(e.success) && ((n = e.success), delete e.success),
                    t(e.fail) && ((o = e.fail), delete e.fail),
                    oe(
                        n,
                        function (e) {
                            z(o, e);
                        },
                        'UPWebSdk',
                        'appletAuth',
                        [e],
                    );
            } else s = e;
        },
        pluginReady: function (e) {
            t(e) && (v ? ((C = !0), e()) : P.push(e));
        },
    }),
        'undefined' != typeof define &&
            define(function () {
                return window.upsdk;
            }),
        (window.backBtnClick_iOS = e);
})();
