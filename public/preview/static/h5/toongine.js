!(function (e) {
    function a(t) {
        if (o[t]) return o[t].exports;
        var n = (o[t] = { exports: {}, id: t, loaded: !1 });
        return e[t].call(n.exports, n, n.exports, a), (n.loaded = !0), n.exports;
    }
    var o = {};
    (a.m = e), (a.c = o), (a.p = ''), a(0);
})([
    function (e, a, o) {
        'use strict';
        function t(e) {
            return e && e.__esModule ? e : { default: e };
        }
        var n = o(1),
            r = t(n),
            i = o(2),
            s = t(i);
        !(function () {
            r.default.setToongineApi(s.default);
        })();
    },
    function (e, a) {
        'use strict';
        Object.defineProperty(a, '__esModule', { value: !0 });
        var o = function () {
                return 'undefined' == typeof toongine_window ? window : toongine_window;
            },
            t = function (e) {
                var a = o();
                a.toongine = e;
            };
        a.default = { getToongineEnv: o, setToongineApi: t };
    },
    function (e, a, o) {
        'use strict';
        function t(e) {
            return e && e.__esModule ? e : { default: e };
        }
        function n(e) {
            return !!e.handlerName || (e.callback({ msg: '参数不合法~', code: -1 }), !1);
        }
        function r(e) {
            n(e) && (h.AEJSBridge ? h.AEJSBridge.dispatch(e) : v.push(e));
        }
        function i(e) {
            return n(e) ? (h.AEJSBridge ? h.AEJSBridge.addEventListener(e) : y.push(e), e) : e;
        }
        function s(e) {
            n(e) && h.AEJSBridge && h.AEJSBridge.removeEventListener(e);
        }
        Object.defineProperty(a, '__esModule', { value: !0 });
        var c = o(1),
            d = t(c),
            u = o(3),
            l = t(u),
            m = o(4),
            p = t(m),
            g = o(8),
            f = t(g),
            v = [],
            y = [],
            h = d.default.getToongineEnv();
        if (!h.AEJSBridge) {
            var N = function e() {
                for (var a = v.length, o = 0; o < a; o++) {
                    var t = v.shift();
                    h.AEJSBridge.dispatch({
                        handlerName: t.handlerName,
                        params: t.params,
                        callback: t.callback,
                    });
                }
                for (var n = y.length, r = 0; r < n; r++) {
                    var i = y.shift();
                    h.AEJSBridge.addEventListener({
                        handlerName:
                            i.params && i.params.eventId && 'string' == typeof i.params.eventId
                                ? i.handlerName + '_' + i.params.eventId
                                : i.handlerName,
                        params: i.params,
                        callback: i.callback,
                    });
                }
                'undefined' != typeof document &&
                    document.removeEventListener &&
                    document.removeEventListener('AEJSBridgeReady', e);
            };
            'undefined' != typeof document &&
                document.addEventListener &&
                document.addEventListener('AEJSBridgeReady', N);
        }
        var K = [];
        K.push.apply(K, p.default), K.push.apply(K, f.default);
        var C = l.default.generateApi(K);
        (C.dispatch = r), (C.addEventListener = i), (C.removeEventListener = s), (a.default = C);
    },
    function (e, a) {
        'use strict';
        Object.defineProperty(a, '__esModule', { value: !0 });
        var o = function (e, a) {
                var o = e.groupName,
                    t = e.moduleName,
                    n = e.actionKeys,
                    r = e.eventKeys,
                    i = e.handlers,
                    s = e.components;
                if (t) {
                    var c = t;
                    o && '' != o && 'core' != o && (c = o + '_' + t);
                    var d = {};
                    if (
                        (n &&
                            n.forEach(function (e) {
                                d[e] = function () {
                                    var o =
                                        arguments.length > 0 && void 0 !== arguments[0]
                                            ? arguments[0]
                                            : {};
                                    a.dispatch({
                                        handlerName: 'action_' + c + '_' + e,
                                        params: o.params,
                                        callback:
                                            void 0 == i || void 0 == i[e]
                                                ? o.callback
                                                : i[e](o.callback),
                                    });
                                };
                            }),
                        r &&
                            r.forEach(function (e) {
                                d[e] = function () {
                                    var o =
                                        arguments.length > 0 && void 0 !== arguments[0]
                                            ? arguments[0]
                                            : {};
                                    return a.addEventListener({
                                        handlerName:
                                            o.params &&
                                            o.params.eventId &&
                                            'string' == typeof o.params.eventId
                                                ? 'event_' + c + '_' + e + '_' + o.params.eventId
                                                : 'event_' + c + '_' + e,
                                        callback:
                                            void 0 == i || void 0 == i[e]
                                                ? o.callback
                                                : i[e](o.callback),
                                    });
                                };
                            }),
                        s)
                    ) {
                        var u = function (e) {
                            d[e] = function () {
                                var a =
                                    arguments.length > 0 && void 0 !== arguments[0]
                                        ? arguments[0]
                                        : {};
                                return s[e](d, a);
                            };
                        };
                        for (var l in s) u(l);
                    }
                    return d;
                }
            },
            t = function (e) {
                var a = {};
                return (
                    e.forEach(function (e) {
                        var t = e.groupName,
                            n = t ? a[t] : {};
                        if ((void 0 == n && (n = {}), void 0 == t || '' == t || 'core' == t)) n = a;
                        else {
                            var r = a[t];
                            r ? (n = r) : (a[t] = n);
                        }
                        n[e.moduleName] = o(e, a);
                    }),
                    a
                );
            };
        a.default = { generate: o, generateApi: t };
    },
    function (e, a, o) {
        'use strict';
        function t(e) {
            return e && e.__esModule ? e : { default: e };
        }
        Object.defineProperty(a, '__esModule', { value: !0 });
        var n = o(5),
            r = t(n);
        a.default = [
            {
                moduleName: 'app',
                actionKeys: ['shutdown', 'setClearStorage'],
                eventKeys: ['onAppLifecycle', 'onGoBack'],
            },
            { moduleName: 'card', actionKeys: ['chooseCard', 'openQrCode'] },
            { moduleName: 'cardList', actionKeys: ['chooseCard'] },
            {
                moduleName: 'chat',
                actionKeys: [
                    'openChat',
                    'createSingleChat',
                    'request',
                    'uploadFile',
                    'uploadAvatar',
                    'chooseFile',
                    'chooseContact',
                ],
            },
            {
                moduleName: 'contact',
                actionKeys: [
                    'openFriends',
                    'openColleagues',
                    'openCard',
                    'openContact',
                    'phoneContacts',
                    'selectPhoneContact',
                ],
            },
            {
                moduleName: 'database',
                actionKeys: ['createTable', 'insert', 'delete', 'update', 'select'],
            },
            {
                moduleName: 'device',
                actionKeys: [
                    'makePhoneCall',
                    'getClipboardData',
                    'setClipboardData',
                    'getNetworkType',
                    'scanCode',
                    'genQrCode',
                    'rotateScreen',
                    'addCalendarEvent',
                    'deleteCalendarEvent',
                    'editCalendarEvent',
                    'getCalendarEvent',
                    'startVibrate',
                    'openGps',
                    'getSystemInfo',
                    'setScreenBrightness',
                    'getScreenBrightness',
                    'setKeepScreenOn',
                    'startBeaconDiscovery',
                    'stopBeaconDiscovery',
                    'getBeacons',
                    'startWifi',
                    'stopWifi',
                    'connectWifi',
                    'getWifiList',
                    'setWifiList',
                    'getConnectedWifi',
                    'getHCEState',
                    'startHCE',
                    'stopHCE',
                    'sendHCEMessage',
                ],
                eventKeys: [
                    'onBeaconUpdate',
                    'onBeaconServiceChange',
                    'onGetWifiList',
                    'onWifiConnected',
                    'onHCEMessage',
                ],
            },
            { moduleName: 'discovery', actionKeys: ['openAround', 'openGroup'] },
            {
                moduleName: 'file',
                actionKeys: [
                    'openDocument',
                    'getFileInfo',
                    'deleteFile',
                    'getFileList',
                    'decompressFile',
                ],
            },
            { moduleName: 'frame', actionKeys: ['open', 'openFrame', 'displayFrame'] },
            { moduleName: 'group', actionKeys: ['setGroup', 'create', 'openQrCode', 'discovery'] },
            { moduleName: 'groupChat', actionKeys: ['create', 'joinGroupChat', 'openGroupChat'] },
            {
                moduleName: 'location',
                actionKeys: ['getLocation', 'openLocation', 'chooseLocation'],
                eventKeys: ['onLocationChange'],
            },
            {
                moduleName: 'media',
                actionKeys: [
                    'chooseImage',
                    'previewImage',
                    'audioPlay',
                    'videoPlay',
                    'audioRecord',
                    'chooseVideo',
                ],
            },
            { moduleName: 'minJianglWallet', actionKeys: ['qrcode', 'tradeList'] },
            {
                moduleName: 'municipalWallet',
                actionKeys: ['authorized', 'qrcode', 'tradeList', 'recharge'],
            },
            {
                moduleName: 'net',
                actionKeys: [
                    'request',
                    'initDownload',
                    'pauseDownload',
                    'cancelDownload',
                    'resumeDownload',
                    'initUpload',
                    'resumeUpload',
                    'cancelUpload',
                    'connectSocket',
                    'sendSocketMessage',
                    'closeSocket',
                    'openUrl',
                ],
                eventKeys: [
                    'onNetworkStatusChange',
                    'onDownloadProgressListener',
                    'onDownloadCompleteListener',
                    'onUploadProgressListener',
                    'onUploadCompleteListener',
                    'onSocketOpen',
                    'onSocketError',
                    'onSocketMessage',
                    'onSocketClose',
                ],
            },
            { moduleName: 'notify', actionKeys: ['openHome', 'openCatalog'] },
            { moduleName: 'oauth', actionKeys: ['personal', 'organizational'] },
            {
                moduleName: 'open',
                actionKeys: [
                    'getCode',
                    'getEnv',
                    'getSystemInfo',
                    'getToken',
                    'getAppParams',
                    'openPageApp',
                    'pageAppSendData',
                ],
                eventKeys: ['onPageAppReceiveDataListener'],
            },
            { moduleName: 'openAppWithAuthLevel', actionKeys: ['openApp'] },
            {
                moduleName: 'page',
                actionKeys: [
                    'setShareInfo',
                    'openShare',
                    'clearShareInfo',
                    'hideLoading',
                    'hideToast',
                    'showActionSheet',
                    'showLoading',
                    'showModal',
                    'showToast',
                    'navigateBack',
                    'navigateTo',
                    'redirectTo',
                    'hideNavigationBarLoading',
                    'setNavigationBarTitle',
                    'showNavigationBarLoading',
                    'disableLoadMore',
                    'disableRefresh',
                    'dismissLoadMore',
                    'enableLoadMore',
                    'enableRefresh',
                    'dismissRefresh',
                ],
                eventKeys: ['onLoadMoreListener', 'onRefreshListener', 'onShareItemClick'],
            },
            {
                moduleName: 'pay',
                actionKeys: [
                    'openCashPay',
                    'openLuckyMoney',
                    'openGathering',
                    'stopGathering',
                    'openRecharge',
                    'checkoutPayCash',
                ],
            },
            { moduleName: 'recommend', actionKeys: ['openHome', 'recommendFriend'] },
            {
                moduleName: 'storage',
                actionKeys: [
                    'getStorageSpace',
                    'getAvailableSpace',
                    'getTotalSpace',
                    'setStorage',
                    'clearStorage',
                    'getStorage',
                ],
            },
            { moduleName: 'toon', actionKeys: ['goHome'] },
            {
                moduleName: 'toonflash',
                actionKeys: ['openHome', 'showUserMainView', 'doorgrandSet'],
            },
            { moduleName: 'topic', actionKeys: ['openArticle'] },
            { moduleName: 'trends', actionKeys: ['openDetail', 'openEditor'] },
            { moduleName: 'user', actionKeys: ['getUserInfo', 'getUserToken'] },
            { moduleName: 'protocol', actionKeys: ['request'] },
            {
                moduleName: 'recorder',
                actionKeys: ['start', 'pause', 'resume', 'stop'],
                eventKeys: [
                    'onFrameRecorded',
                    'onError',
                    'onInterruptionBegin',
                    'onInterruptionEnd',
                ],
                handlers: { onFrameRecorded: r.default.onFrameRecorded },
            },
            {
                groupName: 'tcTemail',
                moduleName: 'assistant',
                actionKeys: ['update', 'loadAfter', 'loadBefore', 'invoke'],
                eventKeys: [],
            },
            { groupName: 'tcpage', moduleName: 'pages', actionKeys: ['display'], eventKeys: [] },
            {
                groupName: 'tcCertEngin',
                moduleName: 'housekeeping',
                actionKeys: ['checkFaceImageEngin'],
                eventKeys: [],
            },
            {
                groupName: 'tcPublic',
                moduleName: 'huairou',
                actionKeys: ['webOpenMap', 'webOpenLivingPay', 'openLoginClass'],
                eventKeys: [],
            },
            {
                groupName: 'tcPublic',
                moduleName: 'group',
                actionKeys: ['openDiscovery', 'openGroupMain'],
                eventKeys: [],
            },
            {
                groupName: 'tcPublic',
                moduleName: 'user',
                actionKeys: ['getUserAvatar', 'getUserInfo'],
                eventKeys: [],
            },
            {
                groupName: 'tcTemail',
                moduleName: 'chat',
                actionKeys: ['createSession'],
                eventKeys: [],
            },
            {
                groupName: 'tcTemail',
                moduleName: 'group',
                actionKeys: ['selectMember'],
                eventKeys: [],
            },
            {
                groupName: 'tcTemail',
                moduleName: 'teamwork',
                actionKeys: ['getTeamworkConfig', 'setTeamworkConfig'],
                eventKeys: [],
            },
        ];
    },
    function (e, a, o) {
        'use strict';
        function t(e) {
            return e && e.__esModule ? e : { default: e };
        }
        Object.defineProperty(a, '__esModule', { value: !0 });
        var n = o(6),
            r = t(n),
            i = o(7),
            s = t(i),
            c = function (e) {
                var a = r.default.createWorker(
                    function (e) {
                        var a = e.data[0];
                        if (void 0 != a && void 0 != a.data && void 0 != a.data.frameBuffer) {
                            var o = toArrayBuffer(a.data.frameBuffer);
                            a.data.frameBuffer = o.buffer;
                        }
                        void 0 != postMessage && postMessage([a]);
                    },
                    { toArrayBuffer: s.default.toArrayBuffer },
                );
                a.onmessage = function (a) {
                    e(a.data[0]);
                };
                var o = function (e) {
                    void 0 != a.postMessage && a.postMessage([e]);
                };
                return o;
            };
        a.default = { onFrameRecorded: c };
    },
    function (e, a) {
        'use strict';
        function o(e, a) {
            var o = new Blob(['var ' + e + ' = ' + a.toString()], { type: 'text/javascript' }),
                t = URL.createObjectURL(o);
            return t;
        }
        Object.defineProperty(a, '__esModule', { value: !0 });
        var t = function (e, a) {
            var t = '';
            if (void 0 != a)
                for (var n in a) {
                    var r = o(n, a[n]);
                    t = t + ';self.importScripts("' + r + '");';
                }
            var i = new Blob([t + 'onmessage=' + e.toString()], { type: 'text/plain' }),
                s = URL.createObjectURL(i);
            return new Worker(s);
        };
        a.default = { createWorker: t };
    },
    function (e, a) {
        'use strict';
        Object.defineProperty(a, '__esModule', { value: !0 });
        var o = function (e, a) {
            for (
                var o,
                    t,
                    n = function (e) {
                        return e > 64 && e < 91
                            ? e - 65
                            : e > 96 && e < 123
                            ? e - 71
                            : e > 47 && e < 58
                            ? e + 4
                            : 43 === e
                            ? 62
                            : 47 === e
                            ? 63
                            : 0;
                    },
                    r = e.replace(/[^A-Za-z0-9\+\/]/g, ''),
                    i = r.length,
                    s = a ? Math.ceil(((3 * i + 1) >>> 2) / a) * a : (3 * i + 1) >>> 2,
                    c = new Uint8Array(s),
                    d = 0,
                    u = 0,
                    l = 0;
                l < i;
                l++
            )
                if (
                    ((t = 3 & l), (d |= n(r.charCodeAt(l)) << (18 - 6 * t)), 3 === t || i - l === 1)
                ) {
                    for (o = 0; o < 3 && u < s; o++, u++) c[u] = (d >>> ((16 >>> o) & 24)) & 255;
                    d = 0;
                }
            return c;
        };
        a.default = { toArrayBuffer: o };
    },
    function (e, a) {
        'use strict';
        Object.defineProperty(a, '__esModule', { value: !0 }),
            (a.default = [
                {
                    groupName: 'tcTemail',
                    moduleName: 'assistant',
                    actionKeys: ['update', 'loadAfter', 'loadBefore', 'invoke'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcpage',
                    moduleName: 'pages',
                    actionKeys: ['display'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcCertEngin',
                    moduleName: 'housekeeping',
                    actionKeys: ['checkFaceImageEngin'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcPublic',
                    moduleName: 'huairou',
                    actionKeys: ['webOpenMap', 'webOpenLivingPay', 'openLoginClass'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcPublic',
                    moduleName: 'group',
                    actionKeys: ['openDiscovery', 'openGroupMain'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcPublic',
                    moduleName: 'user',
                    actionKeys: ['getUserAvatar', 'getUserInfo'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcTemail',
                    moduleName: 'chat',
                    actionKeys: ['createSession'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcTemail',
                    moduleName: 'group',
                    actionKeys: ['selectMember'],
                    eventKeys: [],
                },
                {
                    groupName: 'tcTemail',
                    moduleName: 'teamwork',
                    actionKeys: ['getTeamworkConfig', 'setTeamworkConfig'],
                    eventKeys: [],
                },
            ]);
    },
]);
