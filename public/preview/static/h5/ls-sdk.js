!(function (n) {
    var e = {};
    function t(o) {
        if (e[o]) return e[o].exports;
        var i = (e[o] = { i: o, l: !1, exports: {} });
        return n[o].call(i.exports, i, i.exports, t), (i.l = !0), i.exports;
    }
    (t.m = n),
        (t.c = e),
        (t.d = function (n, e, o) {
            t.o(n, e) || Object.defineProperty(n, e, { enumerable: !0, get: o });
        }),
        (t.r = function (n) {
            'undefined' != typeof Symbol &&
                Symbol.toStringTag &&
                Object.defineProperty(n, Symbol.toStringTag, { value: 'Module' }),
                Object.defineProperty(n, '__esModule', { value: !0 });
        }),
        (t.t = function (n, e) {
            if ((1 & e && (n = t(n)), 8 & e)) return n;
            if (4 & e && 'object' == typeof n && n && n.__esModule) return n;
            var o = Object.create(null);
            if (
                (t.r(o),
                Object.defineProperty(o, 'default', { enumerable: !0, value: n }),
                2 & e && 'string' != typeof n)
            )
                for (var i in n)
                    t.d(
                        o,
                        i,
                        function (e) {
                            return n[e];
                        }.bind(null, i),
                    );
            return o;
        }),
        (t.n = function (n) {
            var e =
                n && n.__esModule
                    ? function () {
                          return n.default;
                      }
                    : function () {
                          return n;
                      };
            return t.d(e, 'a', e), e;
        }),
        (t.o = function (n, e) {
            return Object.prototype.hasOwnProperty.call(n, e);
        }),
        (t.p = ''),
        t((t.s = 0));
})([
    function (n, e) {
        !(function (n) {
            const e = (n, e, t) => {
                    !(function (n) {
                        window.bridge
                            ? n && n()
                            : document.addEventListener('LSJSBridgeReady', n, !1);
                    })(() => {
                        bridge.call(n, e, (n) => {
                            t(n);
                        });
                    });
                },
                t = {},
                o = { _func: {} },
                i = {
                    config: function (n) {
                        (t.appId = n.appId),
                            (t.debug = !!n.hasOwnProperty('debug') && n.debug),
                            e(
                                'ls_check_initCode',
                                { thirdAppId: n.appId, code: n.initCode },
                                function (n) {
                                    200 === (n = JSON.parse(n)).code
                                        ? o._func.ready()
                                        : o._func.error('初始化SDK失败');
                                },
                            );
                    },
                    ready: function (n) {
                        o._func.ready = n;
                    },
                    error: function (n) {
                        o._func.error = n;
                    },
                    userAuth: function (n, t) {
                        var o = this;
                        e('ls_userAuth', { thirdAppId: n.appId }, function (n) {
                            o.debugLogger(n), t(JSON.parse(n));
                        });
                    },
                    qrCode: function (n) {
                        var t = this;
                        e('ls_scan', {}, function (e) {
                            t.debugLogger(e), n(JSON.parse(e));
                        });
                    },
                    share: function (n, t) {
                        var o = this;
                        e('ls_open_share', n, function (n) {
                            o.debugLogger(n), t(JSON.parse(n));
                        });
                    },
                    checkIdentity: function (n) {
                        var t = this;
                        e('ls_check_identity', {}, function (e) {
                            t.debugLogger(e);
                            try {
                                n(JSON.parse(e));
                            } catch (t) {
                                n(e);
                            }
                        });
                    },
                    location: function (n) {
                        var t = this;
                        e('ls_location', {}, function (e) {
                            t.debugLogger(e), n(JSON.parse(e));
                        });
                    },
                    callNo: function (n) {
                        e('ls_call', n);
                    },
                    close: function () {
                        e('ls_close_current_page', {});
                    },
                    goback: function () {
                        e('ls_back', {});
                    },
                    getVersion: function (n) {
                        e('ls_get_version', {}, function (e) {
                            n(JSON.parse(e));
                        });
                    },
                    aliPay: function (n, t) {
                        var o = this;
                        e('ls_native_alipay', n, function (n) {
                            o.debugLogger(n);
                            try {
                                t(JSON.parse(n));
                            } catch (e) {
                                t(n);
                            }
                        });
                    },
                    lsPay: function (n, t) {
                        var o = this;
                        let i = !1;
                        e('ls_get_version', {}, function (r) {
                            (i = !0),
                                e('ls_pay', n, function (n) {
                                    o.debugLogger(n), t(JSON.parse(n));
                                });
                        }),
                            setTimeout(function () {
                                i || t({ code: 9999, msg: '请下载最新版本灵锡APP' });
                            }, 1e3);
                    },
                    previewFile: function (n, t) {
                        e('ls_preview_file', n, function (n) {
                            t();
                        });
                    },
                    openWxMini: function (n, t) {
                        e('ls_open_wx_mini_pro', n, function (n) {
                            t();
                        });
                    },
                    openSystemBroswer: function (n, t) {
                        e('ls_system_web', n, function (n) {
                            t();
                        });
                    },
                    lsStat: function (n, t) {
                        e('ls_uploadStatEvent', n, function (n) {
                            t();
                        });
                    },
                    lsGetVersion: function (n) {
                        var t = this;
                        let o = !1;
                        e('ls_get_version', {}, function (e) {
                            (o = !0), t.debugLogger(e), n(JSON.parse(e));
                        }),
                            setTimeout(function () {
                                o || n({ code: 9999, msg: '请下载最新版本灵锡APP' });
                            }, 1e3);
                    },
                    openScheme: function (n) {
                        e('ls_open_scheme', n);
                    },
                    clipboard: function (n) {
                        var t = this;
                        e('ls_get_copy', {}, function (e) {
                            t.debugLogger(e), n(JSON.parse(e));
                        });
                    },
                    gohome: function () {
                        e('ls_home', {}, function (n) {});
                    },
                    navigationStatus: function (n) {
                        n.navigationBarBackgroundColor &&
                            (n.navigationBarBackgroundColor =
                                n.navigationBarBackgroundColor.replace('#', '#ff')),
                            n.navigationBarTitleColor &&
                                (n.navigationBarTitleColor = n.navigationBarTitleColor.replace(
                                    '#',
                                    '#ff',
                                )),
                            (n.navigationBarBackgroundColor ||
                                n.navigationBarTitleText ||
                                n.navigationBarTitleColor ||
                                n.navigationBarBackText) &&
                                (n.navigationShow = !0),
                            e('ls_navigation_status', n);
                    },
                    lsRouter: function (n, t) {
                        var o = this;
                        let i = !1;
                        e('ls_router', n, function (n) {
                            (i = !0), o.debugLogger(n), t(JSON.parse(n));
                        }),
                            setTimeout(function () {
                                i || t({ code: 9999, msg: '请下载最新版本灵锡APP' });
                            }, 2e3);
                    },
                    debugLogger: function (n) {
                        t.debug && alert(n);
                    },
                };
            n.ls = i;
        })(window);
    },
]);
