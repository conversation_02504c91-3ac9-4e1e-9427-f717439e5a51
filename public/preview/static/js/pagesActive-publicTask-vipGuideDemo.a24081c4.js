(self.webpackChunknew_charge = self.webpackChunknew_charge || []).push([
    [547],
    {
        78733: function (e, n, t) {
            var i = t(78033);
            (i = 'string' == typeof (i = i.__esModule ? i.default : i) ? [[e.id, i, '']] : i)
                .locals && (e.exports = i.locals),
                (0, t(69333).A)('57c12222', i, !0, { sourceMap: !1, shadowMode: !1 });
        },
        55489: function (e, n, t) {
            var i = t(35885);
            (i = 'string' == typeof (i = i.__esModule ? i.default : i) ? [[e.id, i, '']] : i)
                .locals && (e.exports = i.locals),
                (0, t(69333).A)('7327492b', i, !0, { sourceMap: !1, shadowMode: !1 });
        },
        13553: function (e, n, t) {
            function i() {
                var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {},
                    n = e.bdResponse,
                    t = null == n || null == (t = n.puttingList) ? void 0 : t[0];
                return (0, u.A)(
                    (0, u.A)((0, u.A)({}, e), n || {}),
                    {},
                    {
                        putId: null == t ? void 0 : t.id,
                        putName: null == t ? void 0 : t.puttingName,
                        resourceld: null == t ? void 0 : t.resourceld,
                        assemblyld: null == t || null == (n = t.extendCfg) ? void 0 : n.componentId,
                        supParam: (0, a.A)(
                            (0, a.A)(
                                (0, a.A)(
                                    (0, a.A)(
                                        (0, a.A)(
                                            (0, a.A)(
                                                (0, a.A)(
                                                    {},
                                                    '是否命中会员策略',
                                                    e.policyFlag ? '是' : '否',
                                                ),
                                                '命中策略ID',
                                                e.policyId,
                                            ),
                                            '推荐策略类型',
                                            e.policySceneName,
                                        ),
                                        '主文案内容',
                                        e.mainContent,
                                    ),
                                    '副文案内容',
                                    e.subContent,
                                ),
                                '按钮内容',
                                e.btnContent,
                            ),
                            '跳转页面名称',
                            '01' === e.btnPageType ? '开通页' : '中心页',
                        ),
                    },
                );
            }
            t.r(n),
                t.d(n, {
                    default: function () {
                        return m;
                    },
                }),
                t(79432);
            var o = t(23074),
                r = t(31554),
                a = t(60578),
                u = t(81940),
                s = (t(28706), t(26099), t(94084)),
                l = t(14116),
                c = t(19406),
                d = t(62090),
                f = ((n = t(55373)), t.n(n)),
                p = t(59489),
                g =
                    ((n = t(45013)),
                    (n = {
                        props: {
                            standType: { type: String, default: '' },
                            hideBg: { type: Boolean, default: !1 },
                            sourceInfo: { type: Object },
                        },
                        data: function () {
                            return {
                                VIP_GUIDE_ASSEMBLY_TYPES: p.S$,
                                IMG_URL: d.sE,
                                guideInfo: null,
                            };
                        },
                        created: function () {
                            this.sourceInfo && (this.guideInfo = this.sourceInfo);
                        },
                        computed: {
                            guideTitle: function () {
                                return this.guideInfo ? (0, s._Z)(this.guideInfo.mainContent) : [];
                            },
                            paymemberBtnTitle: function () {
                                return (this.guideInfo && this.guideInfo.btnContent) || '';
                            },
                            guideSubTitle: function () {
                                return this.guideInfo ? (0, s._Z)(this.guideInfo.subContent) : [];
                            },
                        },
                        methods: (0, u.A)(
                            (0, u.A)({}, (0, n.mapActions)('common', ['getLocation'])),
                            {},
                            {
                                initData: function () {
                                    var e = arguments,
                                        n = this;
                                    return (0, r.A)(
                                        (0, o.A)().mark(function t() {
                                            var r, a, d, f, p, g;
                                            return (0, o.A)().wrap(
                                                function (t) {
                                                    for (;;)
                                                        switch ((t.prev = t.next)) {
                                                            case 0:
                                                                if (
                                                                    ((r =
                                                                        0 < e.length &&
                                                                        void 0 !== e[0]
                                                                            ? e[0]
                                                                            : {}),
                                                                    (t.prev = 1),
                                                                    n.isShowVipChannelFlag)
                                                                ) {
                                                                    t.next = 4;
                                                                    break;
                                                                }
                                                                return t.abrupt('return');
                                                            case 4:
                                                                return (
                                                                    (t.next = 6),
                                                                    n.getLocation({ city: !0 })
                                                                );
                                                            case 6:
                                                                return (
                                                                    (a = t.sent),
                                                                    (a = (0, u.A)(
                                                                        {
                                                                            standType: n.standType,
                                                                            city: a.city,
                                                                            lat: a.latitude,
                                                                            lon: a.longitude,
                                                                        },
                                                                        r,
                                                                    )),
                                                                    (t.next = 10),
                                                                    (0, c.KX)(a)
                                                                );
                                                            case 10:
                                                                return (
                                                                    (a = t.sent),
                                                                    (d = a.data),
                                                                    (f = (d || {}).bdResponse),
                                                                    (n.guideInfo = d),
                                                                    f &&
                                                                        ((p = (0, l.Ed)(
                                                                            n.$biz,
                                                                            !0,
                                                                        )),
                                                                        (g = i(d)),
                                                                        p(
                                                                            null == f
                                                                                ? void 0
                                                                                : f.puttingList,
                                                                            g,
                                                                        ),
                                                                        (0, s.uk)('StandExpo', g)),
                                                                    t.abrupt('return', d)
                                                                );
                                                            case 18:
                                                                return (
                                                                    (t.prev = 18),
                                                                    (t.t0 = t.catch(1)),
                                                                    t.abrupt(
                                                                        'return',
                                                                        Promise.reject(t.t0),
                                                                    )
                                                                );
                                                            case 21:
                                                            case 'end':
                                                                return t.stop();
                                                        }
                                                },
                                                t,
                                                null,
                                                [[1, 18]],
                                            );
                                        }),
                                    )();
                                },
                                getJumpVipPath: function () {
                                    var e =
                                            0 < arguments.length && void 0 !== arguments[0]
                                                ? arguments[0]
                                                : {},
                                        n = '',
                                        t = (e.actSubType, e.btnPageType),
                                        i = e.policyId,
                                        o = ((i = void 0 === i ? '' : i), '');
                                    switch (this.standType) {
                                        case p.S$.ORDER_DETAIL:
                                            o = '订单详情页';
                                            break;
                                        case p.S$.CHARGE:
                                            o = '充电页';
                                            break;
                                        case p.S$.TRANSACTION:
                                            o = '下单页';
                                    }
                                    return (
                                        (i = {
                                            sourceType: 'vipGuideBar',
                                            policyId: i,
                                            sourceEvent: '{VipActPos_button_Click}|{'
                                                .concat(
                                                    o,
                                                    '会员导购组件点击}|{{策略ID,activ_type},{',
                                                )
                                                .concat(i, ',会员导购组件}}'),
                                            CrowdPortraitId: e.crowdInfo || '',
                                            actId: null == e ? void 0 : e.actId,
                                        }),
                                        (e = f().stringify(i)),
                                        '01' === t
                                            ? (n = '/pagesPaymember/paymember/pay?'.concat(e))
                                            : '02' === t
                                            ? (n = '/pagesPaymember/paymember/details?'.concat(e))
                                            : '03' === t &&
                                              ((i.experience = 1),
                                              (e = f().stringify(i)),
                                              (n = '/pagesPaymember/paymember/pay?'.concat(e))),
                                        n
                                    );
                                },
                                gotoPaymember: function () {
                                    var e = this;
                                    return (0, r.A)(
                                        (0, o.A)().mark(function n() {
                                            var t;
                                            return (0, o.A)().wrap(
                                                function (n) {
                                                    for (;;)
                                                        switch ((n.prev = n.next)) {
                                                            case 0:
                                                                if (((n.prev = 0), e.guideInfo))
                                                                    return (
                                                                        (t = (0, u.A)(
                                                                            {},
                                                                            e.guideInfo,
                                                                        )),
                                                                        (t = e.getJumpVipPath(t)),
                                                                        (0, s.uk)(
                                                                            'PutClick',
                                                                            i(e.guideInfo),
                                                                        ),
                                                                        (n.next = 7),
                                                                        (0, s.SK)(200)
                                                                    );
                                                                n.next = 8;
                                                                break;
                                                            case 7:
                                                                t && (0, s.ZK)(t);
                                                            case 8:
                                                                return n.abrupt('return');
                                                            case 11:
                                                                return (
                                                                    (n.prev = 11),
                                                                    (n.t0 = n.catch(0)),
                                                                    n.abrupt(
                                                                        'return',
                                                                        Promise.reject(n.t0),
                                                                    )
                                                                );
                                                            case 14:
                                                            case 'end':
                                                                return n.stop();
                                                        }
                                                },
                                                n,
                                                null,
                                                [[0, 11]],
                                            );
                                        }),
                                    )();
                                },
                            },
                        ),
                        watch: {
                            sourceInfo: function (e) {
                                (0, s.LK)(e) || (this.guideInfo = e);
                            },
                        },
                    }),
                    t(78733),
                    t(18535)),
                m =
                    ((n = {
                        components: {
                            VipGuideBar: (0, g.A)(
                                n,
                                function () {
                                    var e = this,
                                        n = e.$createElement,
                                        t = e._self._c || n;
                                    return e.isShowVipChannelFlag && e.guideInfo
                                        ? t(
                                              'v-uni-view',
                                              {
                                                  class: ['paymember-info-bar'],
                                                  style: {
                                                      backgroundImage:
                                                          !e.hideBg &&
                                                          e.guideInfo &&
                                                          e.guideInfo.bgImg
                                                              ? 'url(' + e.guideInfo.bgImg + ')'
                                                              : '',
                                                  },
                                              },
                                              [
                                                  e.guideInfo && e.guideInfo.littleIcon
                                                      ? t(
                                                            'v-uni-view',
                                                            { staticClass: 'paymember-info-logo' },
                                                            [
                                                                t('v-uni-image', {
                                                                    staticClass: 'logo-img',
                                                                    attrs: {
                                                                        src: e.guideInfo.littleIcon,
                                                                    },
                                                                }),
                                                            ],
                                                            1,
                                                        )
                                                      : e._e(),
                                                  t(
                                                      'v-uni-view',
                                                      {
                                                          staticClass: 'paymember-info-title',
                                                          style: {
                                                              color:
                                                                  e.guideInfo &&
                                                                  e.guideInfo.contentDefaultColor,
                                                          },
                                                      },
                                                      [
                                                          0 < e.guideTitle.length
                                                              ? t(
                                                                    'v-uni-view',
                                                                    { class: ['info-title'] },
                                                                    [
                                                                        e._l(
                                                                            e.guideTitle,
                                                                            function (n, i) {
                                                                                return [
                                                                                    t(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            key: i,
                                                                                            style: {
                                                                                                color: n.light
                                                                                                    ? e.guideInfo &&
                                                                                                      e
                                                                                                          .guideInfo
                                                                                                          .contentHighColor
                                                                                                    : '',
                                                                                            },
                                                                                        },
                                                                                        [
                                                                                            e._v(
                                                                                                e._s(
                                                                                                    n.result ||
                                                                                                        '',
                                                                                                ),
                                                                                            ),
                                                                                        ],
                                                                                    ),
                                                                                ];
                                                                            },
                                                                        ),
                                                                    ],
                                                                    2,
                                                                )
                                                              : e._e(),
                                                          0 < e.guideSubTitle.length
                                                              ? t(
                                                                    'v-uni-view',
                                                                    {
                                                                        staticClass: 'info-remark',
                                                                        style: {
                                                                            color:
                                                                                e.guideInfo &&
                                                                                e.guideInfo
                                                                                    .subContentDefaultColor,
                                                                        },
                                                                    },
                                                                    [
                                                                        e._l(
                                                                            e.guideSubTitle,
                                                                            function (n, i) {
                                                                                return [
                                                                                    t(
                                                                                        'v-uni-text',
                                                                                        {
                                                                                            key: i,
                                                                                            style: {
                                                                                                color: n.light
                                                                                                    ? e.guideInfo &&
                                                                                                      e
                                                                                                          .guideInfo
                                                                                                          .subContentHighColor
                                                                                                    : '',
                                                                                            },
                                                                                        },
                                                                                        [
                                                                                            e._v(
                                                                                                e._s(
                                                                                                    n.result ||
                                                                                                        '',
                                                                                                ),
                                                                                            ),
                                                                                        ],
                                                                                    ),
                                                                                ];
                                                                            },
                                                                        ),
                                                                    ],
                                                                    2,
                                                                )
                                                              : e._e(),
                                                      ],
                                                      1,
                                                  ),
                                                  t(
                                                      'v-uni-view',
                                                      {
                                                          class: ['paymember-info-btn'],
                                                          style: {
                                                              color:
                                                                  e.guideInfo &&
                                                                  e.guideInfo.btnColor,
                                                              backgroundImage:
                                                                  e.guideInfo && e.guideInfo.btnImg
                                                                      ? 'url(' +
                                                                        e.guideInfo.btnImg +
                                                                        ')'
                                                                      : '',
                                                          },
                                                          on: {
                                                              click: function (n) {
                                                                  n.stopPropagation(),
                                                                      (arguments[0] = n =
                                                                          e.$handleEvent(n)),
                                                                      e.gotoPaymember.apply(
                                                                          void 0,
                                                                          arguments,
                                                                      );
                                                              },
                                                          },
                                                      },
                                                      [e._v(e._s(e.paymemberBtnTitle))],
                                                  ),
                                              ],
                                              1,
                                          )
                                        : e._e();
                                },
                                [],
                                !1,
                                null,
                                '18ba214c',
                                null,
                                !1,
                                void 0,
                                void 0,
                            ).exports,
                        },
                        data: function () {
                            return { guideList: null };
                        },
                        onLoad: function (e) {
                            e = e.guideList;
                            e && (this.guideList = JSON.parse(e)),
                                'public' === d.Kn &&
                                    ((e = window.sessionStorage.getItem('xdt_vipGuide')),
                                    (0, s.g9)(e) || (this.guideList = JSON.parse(e)));
                        },
                    }),
                    t(55489),
                    (0, g.A)(
                        n,
                        function () {
                            var e = this,
                                n = e.$createElement,
                                t = e._self._c || n;
                            return t(
                                'v-uni-view',
                                e._l(e.guideList, function (n, i) {
                                    return t(
                                        'v-uni-view',
                                        { key: i, staticClass: 'mg-t-20' },
                                        [
                                            t('v-uni-view', { staticClass: 'guide-title' }, [
                                                e._v(e._s(n.title || '')),
                                            ]),
                                            t('VipGuideBar', { attrs: { sourceInfo: n } }),
                                        ],
                                        1,
                                    );
                                }),
                                1,
                            );
                        },
                        [],
                        !1,
                        null,
                        '57185284',
                        null,
                        !1,
                        void 0,
                        void 0,
                    ).exports);
        },
        78033: function (e, n, t) {
            t.r(n);
            var i = t(45522),
                o = ((i = t.n(i)), t(67643));
            t = t.n(o)()(i());
            t.push([
                e.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.paymember-info-bar[data-v-18ba214c]{padding:%?18?% %?20?%;display:flex;align-items:center;border-radius:%?22?%;background-size:cover;background-position:50%;background-repeat:no-repeat;overflow:hidden}.paymember-info-bar .paymember-info-logo[data-v-18ba214c]{flex-shrink:0;display:flex;justify-content:center;align-items:center;width:%?50?%;height:%?50?%}.paymember-info-bar .paymember-info-logo .logo-img[data-v-18ba214c]{display:block;width:100%;height:100%}.paymember-info-bar .paymember-info-title[data-v-18ba214c]{margin:0 %?15?%;flex:1}.paymember-info-bar .paymember-info-title .info-title[data-v-18ba214c]{font-size:%?28?%;line-height:1.2;max-width:%?450?%;font-weight:700}.paymember-info-bar .paymember-info-title .info-remark[data-v-18ba214c]{margin-top:%?6?%;font-size:%?22?%;line-height:1.2;color:#555d81;max-width:%?450?%}.paymember-info-bar .paymember-info-btn[data-v-18ba214c]{position:relative;flex-shrink:0;display:flex;justify-content:center;align-items:center;padding:0 %?15?%;min-width:%?130?%;height:%?48?%;background-size:cover;background-position:50%;background-repeat:no-repeat;border-radius:%?30?%;font-size:%?24?%;font-weight:700}.paymember-info-bar .paymember-info-btn .btn-tips[data-v-18ba214c]{position:absolute;padding:%?8?% %?15?%;left:50%;top:0;-webkit-transform:translate(-50%,-90%);transform:translate(-50%,-90%);background-color:#ff2700;color:#f9f000;color:%?24?%;border-radius:%?20?%;border:1px solid #ff2700;white-space:nowrap}.paymember-info-bar .paymember-info-btn .btn-tips .iconfont[data-v-18ba214c]{color:#ff2700;position:absolute;bottom:0;left:50%;-webkit-transform:translate(-50%,60%);transform:translate(-50%,60%);z-index:-1}',
                '',
            ]),
                (n.default = t);
        },
        35885: function (e, n, t) {
            t.r(n);
            var i = t(45522),
                o = ((i = t.n(i)), t(67643));
            t = t.n(o)()(i());
            t.push([
                e.id,
                '@charset "UTF-8";\n/* 引入minxin */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.guide-title[data-v-57185284]{font-size:%?30?%;font-weight:700;line-height:%?40?%}',
                '',
            ]),
                (n.default = t);
        },
        59489: function (e, n, t) {
            t.d(n, {
                $J: function () {
                    return u;
                },
                CC: function () {
                    return i;
                },
                M5: function () {
                    return r;
                },
                Rr: function () {
                    return o;
                },
                S$: function () {
                    return s;
                },
                jr: function () {
                    return a;
                },
            });
            var i = { SINGLE: '01', MERGE: '02' },
                o = {
                    UNSTART: 'unstart',
                    UNRECEIVE: 'unreceive',
                    RECEIVE: 'receive',
                    COMPLETE: 'complete',
                    CLAIM: 'claim',
                },
                r = {
                    TASK: '60',
                    SIGN: '27',
                    AWARD: '14',
                    EQUITY: '61',
                    RECHARGE: '60',
                    STATION_REMEMBER: '66',
                },
                a = { TASK: null, SIGN: null, AWARD: '1410', EQUITY: null, RECHARGE: '6002' },
                u = { DEFAULT: 'default', ACTIVE: 'act' },
                s = { TRANSACTION: '25', CHARGE: '26', ORDER_DETAIL: '27' };
        },
    },
]);
