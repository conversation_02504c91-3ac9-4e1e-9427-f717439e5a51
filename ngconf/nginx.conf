user  root;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;

    include /etc/nginx/conf.d/*.conf;

    absolute_redirect off;

    server {
            listen 8080;
            server_name  _;
            root  /usr/share/nginx/html;
            index index.html index.htm;

            location = /front/user/login {
                # proxy_set_header  Host  $host;
                # proxy_set_header  X-real-ip $remote_addr;
                # proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                # proxy_pass   https//dev.evshine.net/mng-sso/login;

                rewrite ^(.*) $scheme://$http_host/mng-sso/login permanent;
            }

            location /front {
                if ($request_filename ~* .*\.(?:htm|html|json)$)
                {
                   add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
                }
                try_files $uri $uri/ /front/@router;
                index  index.html index.htm;
            }



            location /front/@router {
                 if ($request_filename ~* .*\.(?:htm|html|json)$)
                {
                   add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
                }
                rewrite ^.*$ /front/index.html last;
            }
        }
}
